<?xml version="1.0" standalone="yes"?>
<library-reference><header name="boost/test/data/config.hpp">
<para>common dataset macros </para><macro name="BOOST_TEST_NO_RANDOM_DATASET_AVAILABLE"><purpose>Defined when the random dataset feature is not available. </purpose></macro>
<macro name="BOOST_TEST_NO_GRID_COMPOSITION_AVAILABLE"><purpose>Defined when grid composition of datasets is not available. </purpose></macro>
<macro name="BOOST_TEST_NO_ZIP_COMPOSITION_AVAILABLE"><purpose>Defined when zip composition of datasets is not available. </purpose></macro>
<macro name="BOOST_TEST_DS_ERROR" kind="functionlike"><macro-parameter name="msg"/><purpose>Defined when the initializer_list implementation is buggy, such as for VS2013. </purpose></macro>
<macro name="BOOST_TEST_DS_ASSERT" kind="functionlike"><macro-parameter name="cond"/><macro-parameter name="msg"/></macro>
</header>
<header name="boost/test/data/for_each_sample.hpp">
<para>Defines for_each_sample algorithm. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">









<function name="invoke_action"><type>void</type><template>
          <template-type-parameter name="Action"/>
          <template-type-parameter name="T"/>
        </template><parameter name="action"><paramtype>Action const &amp;</paramtype></parameter><parameter name="arg"><paramtype>T &amp;&amp;</paramtype></parameter><parameter name=""><paramtype>std::false_type</paramtype></parameter></function>
<function name="invoke_action_impl"><type>void</type><template>
          <template-type-parameter name="Action"/>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="I"><type>std::size_t ...</type></template-nontype-parameter>
        </template><parameter name="action"><paramtype>Action const &amp;</paramtype></parameter><parameter name="args"><paramtype>T &amp;&amp;</paramtype></parameter><parameter name=""><paramtype>index_sequence&lt; I... &gt; const &amp;</paramtype></parameter></function>
<function name="invoke_action"><type>void</type><template>
          <template-type-parameter name="Action"/>
          <template-type-parameter name="T"/>
        </template><parameter name="action"><paramtype>Action const &amp;</paramtype></parameter><parameter name="args"><paramtype>T &amp;&amp;</paramtype></parameter><parameter name=""><paramtype>std::true_type</paramtype></parameter></function>
<function name="for_each_sample"><type>std::enable_if&lt; <classname>monomorphic::is_dataset</classname>&lt; DataSet &gt;::value, void &gt;::type</type><template>
          <template-type-parameter name="DataSet"/>
          <template-type-parameter name="Action"/>
        </template><parameter name="samples"><paramtype>DataSet const &amp;</paramtype></parameter><parameter name="act"><paramtype>Action const &amp;</paramtype></parameter><parameter name="number_of_samples"><paramtype><classname>data::size_t</classname></paramtype><default>BOOST_TEST_DS_INFINITE_SIZE</default></parameter></function>
<function name="for_each_sample"><type>std::enable_if&lt;!<classname>monomorphic::is_dataset</classname>&lt; DataSet &gt;::value, void &gt;::type</type><template>
          <template-type-parameter name="DataSet"/>
          <template-type-parameter name="Action"/>
        </template><parameter name="samples"><paramtype>DataSet &amp;&amp;</paramtype></parameter><parameter name="act"><paramtype>Action const &amp;</paramtype></parameter><parameter name="number_of_samples"><paramtype><classname>data::size_t</classname></paramtype><default>BOOST_TEST_DS_INFINITE_SIZE</default></parameter></function>


































</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/monomorphic/array.hpp">
<para>Defines monomorphic dataset based on C type arrays. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
<namespace name="monomorphic">
<class name="array"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Dataset view of a C array. </purpose><typedef name="sample"><type>T</type></typedef>
<typedef name="iterator"><type>T const  *</type></typedef>
<data-member name="arity" specifiers="static"><type>const int</type></data-member>
<method-group name="public member functions">
<method name="size" cv="const"><type><classname>data::size_t</classname></type></method>
<method name="begin" cv="const"><type>iterator</type></method>
</method-group>
<constructor><parameter name="arr_"><paramtype>T const *</paramtype></parameter><parameter name="size_"><paramtype>std::size_t</paramtype></parameter></constructor>
</class><struct-specialization name="is_dataset"><template>
      <template-type-parameter name="T"/>
    </template><specialization><template-arg>array&lt; T &gt;</template-arg></specialization><inherit access="public">true_</inherit><purpose>An array dataset is a dataset. </purpose></struct-specialization>








</namespace>
















































</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/monomorphic/collection.hpp">
<para>Defines monomorphic dataset based on forward iterable sequence. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
<namespace name="monomorphic">
<class name="collection"><template>
      <template-type-parameter name="C"/>
    </template><purpose>Dataset from a forward iterable container (collection) </purpose><description><para>This dataset is applicable to any container implementing a forward iterator. Note that container with one element will be considered as singletons. This dataset is constructible with the boost::unit_test::data::make function. </para></description><typedef name="sample"><type>col_type::value_type</type></typedef>
<typedef name="iterator"><type>col_type::const_iterator</type></typedef>
<data-member name="arity" specifiers="static"><type>const int</type></data-member>
<method-group name="public member functions">
<method name="col" cv="const"><type>C const  &amp;</type><purpose>Returns the underlying collection. </purpose></method>
<method name="size" cv="const"><type><classname>data::size_t</classname></type><purpose>dataset interface </purpose></method>
<method name="begin" cv="const"><type>iterator</type></method>
</method-group>
<constructor specifiers="explicit"><parameter name="col"><paramtype>C &amp;&amp;</paramtype></parameter><purpose>Constructor consumed a temporary collection or stores a reference. </purpose></constructor>
<constructor><parameter name="c"><paramtype><classname>collection</classname> &amp;&amp;</paramtype></parameter><purpose>Move constructor. </purpose></constructor>
</class><struct-specialization name="is_dataset"><template>
      <template-type-parameter name="C"/>
    </template><specialization><template-arg>collection&lt; C &gt;</template-arg></specialization><inherit access="public">true_</inherit><purpose>A collection from a forward iterable container is a dataset. </purpose></struct-specialization>








</namespace>
















































</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/monomorphic/delayed.hpp">
<para>Defines a lazy/delayed dataset store. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
<namespace name="monomorphic">
<class name="delayed_dataset"><template>
      <template-type-parameter name="dataset_t"/>
      <template-nontype-parameter name="Args"><type>class ...</type></template-nontype-parameter>
    </template><purpose>Delayed dataset. </purpose><description><para>This dataset holds another dataset that is instanciated on demand. It is constructed with the <computeroutput>data::make_delayed&lt;dataset_t&gt;</computeroutput>(arg1,....) instead of the <computeroutput>data::make</computeroutput>. </para></description><typedef name="iterator"><type>decltype(std::declval&lt; dataset_t &gt;().begin())</type></typedef>
<data-member name="arity" specifiers="static"><type>const int</type></data-member>
<method-group name="public member functions">
<method name="size" cv="const"><type><classname>boost::unit_test::data::size_t</classname></type></method>
<method name="begin" cv="const"><type>iterator</type></method>
</method-group>
<constructor><parameter name="args"><paramtype>Args...</paramtype></parameter></constructor>
<constructor><parameter name="b"><paramtype><classname>delayed_dataset</classname> &amp;&amp;</paramtype></parameter></constructor>
<method-group name="private member functions">
<method name="get" cv="const"><type>dataset_t &amp;</type></method>
<method name="create" cv="const"><type>std::unique_ptr&lt; dataset_t &gt;</type><template>
          <template-nontype-parameter name="I"><type>std::size_t...</type></template-nontype-parameter>
        </template><parameter name=""><paramtype>boost::unit_test::data::index_sequence&lt; I... &gt;</paramtype></parameter></method>
</method-group>
</class><struct-specialization name="is_dataset"><template>
      <template-type-parameter name="dataset_t"/>
      <template-nontype-parameter name="Args"><type>class ...</type></template-nontype-parameter>
    </template><specialization><template-arg>delayed_dataset&lt; dataset_t</template-arg><template-arg>Args... &gt;</template-arg></specialization><inherit access="public">true_</inherit><purpose>A lazy/delayed dataset is a dataset. </purpose></struct-specialization>








</namespace>
















































</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/monomorphic/fwd.hpp">
<para>Forward declares monomorphic datasets interfaces. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
<namespace name="monomorphic">
<struct name="has_dataset"><template>
      <template-type-parameter name="DataSet"/>
      <template-nontype-parameter name=""><type>class...</type></template-nontype-parameter>
    </template><inherit access="public">boost::unit_test::data::monomorphic::is_dataset&lt; DataSet &gt;</inherit><purpose>Helper to check if a list of types contains a dataset. </purpose></struct><struct-specialization name="has_dataset"><template>
      <template-type-parameter name="DataSet0"/>
      <template-type-parameter name="DataSet1"/>
      <template-nontype-parameter name="DataSetTT"><type>class...</type></template-nontype-parameter>
    </template><specialization><template-arg>DataSet0</template-arg><template-arg>DataSet1</template-arg><template-arg>DataSetTT...</template-arg></specialization><inherit access="public">std::integral_constant&lt; bool, is_dataset&lt; DataSet0 &gt;::value||has_dataset&lt; DataSet1, DataSetTT... &gt;::value &gt;</inherit></struct-specialization><struct name="is_dataset"><template>
      <template-type-parameter name="DataSet"/>
    </template><inherit access="public">false_</inherit><purpose>Helper metafunction indicating if the specified type is a dataset. </purpose></struct><struct-specialization name="is_dataset"><template>
      <template-type-parameter name="DataSet"/>
    </template><specialization><template-arg>DataSet &amp;</template-arg></specialization><inherit access="public">boost::unit_test::data::monomorphic::is_dataset&lt; DataSet &gt;</inherit><purpose>A reference to a dataset is a dataset. </purpose></struct-specialization><struct-specialization name="is_dataset"><template>
      <template-type-parameter name="DataSet"/>
    </template><specialization><template-arg>DataSet &amp;&amp;</template-arg></specialization><inherit access="public">boost::unit_test::data::monomorphic::is_dataset&lt; DataSet &gt;</inherit></struct-specialization><struct-specialization name="is_dataset"><template>
      <template-type-parameter name="DataSet"/>
    </template><specialization><template-arg>DataSet const</template-arg></specialization><inherit access="public">boost::unit_test::data::monomorphic::is_dataset&lt; DataSet &gt;</inherit><purpose>A const dataset is a dataset. </purpose></struct-specialization>








</namespace>
<namespace name="result_of">
<struct name="make"><template>
      <template-type-parameter name="DataSet"/>
    </template><purpose>Result of the make call. </purpose><data-member name="type"><type>decltype(data::make(declval&lt; DataSet &gt;())) typedef</type></data-member>
</struct></namespace>
<overloaded-function name="make"><signature><type>std::enable_if&lt; <classname>monomorphic::is_dataset</classname>&lt; DataSet &gt;::value, DataSet &gt;::type</type><template>
          <template-type-parameter name="DataSet"/>
        </template><parameter name="ds"><paramtype>DataSet &amp;&amp;</paramtype></parameter></signature><signature><type>std::enable_if&lt;!<classname>is_container_forward_iterable</classname>&lt; T &gt;::value &amp;&amp;!<classname>monomorphic::is_dataset</classname>&lt; T &gt;::value &amp;&amp;!is_array&lt; typename remove_reference&lt; T &gt;::type &gt;::value, <classname>monomorphic::singleton</classname>&lt; T &gt; &gt;::type</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="v"><paramtype>T &amp;&amp;</paramtype></parameter></signature><signature><type>std::enable_if&lt; <classname>is_container_forward_iterable</classname>&lt; C &gt;::value, <classname>monomorphic::collection</classname>&lt; C &gt; &gt;::type</type><template>
          <template-type-parameter name="C"/>
        </template><parameter name="c"><paramtype>C &amp;&amp;</paramtype></parameter></signature><signature><type><classname>monomorphic::array</classname>&lt; typename boost::remove_const&lt; T &gt;::type &gt;</type><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="size"><type>std::size_t</type></template-nontype-parameter>
        </template><parameter name="a"><paramtype>T(&amp;)</paramtype></parameter></signature><signature><type><classname>monomorphic::singleton</classname>&lt; char * &gt;</type><parameter name="str"><paramtype>char *</paramtype></parameter></signature><signature><type><classname>monomorphic::singleton</classname>&lt; char const  * &gt;</type><parameter name="str"><paramtype>char const *</paramtype></parameter></signature><signature><type><classname>monomorphic::init_list</classname>&lt; T &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name=""><paramtype>std::initializer_list&lt; T &gt; &amp;&amp;</paramtype></parameter></signature><signature><type>std::enable_if&lt; !<classname>monomorphic::has_dataset</classname>&lt; T, Args... &gt;::value, <classname>monomorphic::init_list</classname>&lt; T &gt;&gt;::type</type><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="Args"><type>class ...</type></template-nontype-parameter>
        </template><parameter name="arg0"><paramtype>T &amp;&amp;</paramtype></parameter><parameter name="args"><paramtype>Args &amp;&amp;...</paramtype></parameter></signature><purpose>Creates a dataset from a value, a collection or an array. </purpose><description><para>This function has several overloads: <programlisting language="c++">// returns ds if ds is already a dataset
template &lt;typename DataSet&gt; DataSet make(DataSet&amp;&amp; ds); 

// creates a singleton dataset, for non forward iterable and non dataset type T
// (a C string is not considered as a sequence).
template &lt;typename T&gt; monomorphic::singleton&lt;T&gt; make(T&amp;&amp; v); 
monomorphic::singleton&lt;char*&gt; make( char* str );
monomorphic::singleton&lt;char const*&gt; make( char const* str );

// creates a collection dataset, for forward iterable and non dataset type C
template &lt;typename C&gt; monomorphic::collection&lt;C&gt; make(C &amp;&amp; c);

// creates an array dataset
template&lt;typename T, std::size_t size&gt; monomorphic::array&lt;T&gt; make( T (&amp;a)[size] );
</programlisting> </para></description></overloaded-function>







<function name="make_delayed"><type>std::enable_if&lt; <classname>monomorphic::is_dataset</classname>&lt; dataset_t &gt;::value, <classname>monomorphic::delayed_dataset</classname>&lt; dataset_t, Args... &gt;&gt;::type</type><template>
          <template-type-parameter name="dataset_t"/>
          <template-nontype-parameter name="Args"><type>class ...</type></template-nontype-parameter>
        </template><parameter name="args"><paramtype>Args...</paramtype></parameter><purpose>Delayed dataset instanciation. </purpose></function>







































</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/monomorphic/generate.hpp">
<para>Defines generic interface for monomorphic dataset based on generator. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
<namespace name="monomorphic">
<class name="generated_by"><template>
      <template-type-parameter name="Generator"/>
    </template><purpose>Generators interface. </purpose><description><para>This class implements the dataset concept over a generator. Examples of generators are:<itemizedlist>
<listitem><para><classname alt="boost::unit_test::data::monomorphic::xrange_t">xrange_t</classname></para>
</listitem><listitem><para><classname alt="boost::unit_test::data::monomorphic::random_t">random_t</classname></para>
</listitem></itemizedlist>
</para><para>The generator concept is the following:<itemizedlist>
<listitem><para>the type of the generated samples is given by field <computeroutput>sample</computeroutput> </para>
</listitem><listitem><para>the member function <computeroutput>capacity</computeroutput> should return the size of the collection being generated (potentially infinite)</para>
</listitem><listitem><para>the member function <computeroutput>next</computeroutput> should change the state of the generator to the next generated value</para>
</listitem><listitem><para>the member function <computeroutput>reset</computeroutput> should put the state of the object in the same state as right after its instanciation </para>
</listitem></itemizedlist>
</para></description><struct name="iterator"><method-group name="public member functions">
<method name="operator *" cv="const"><type>sample const  &amp;</type></method>
<method name="operator++"><type>void</type></method>
</method-group>
<constructor specifiers="explicit"><parameter name="gen"><paramtype>Generator &amp;</paramtype></parameter></constructor>
</struct><typedef name="sample"><type>Generator::sample</type></typedef>
<typedef name="generator_type"><type>Generator</type></typedef>
<data-member name="arity" specifiers="static"><type>const int</type></data-member>
<method-group name="public member functions">
<method name="size" cv="const"><type><classname>data::size_t</classname></type><purpose>Size of the underlying dataset. </purpose></method>
<method name="begin" cv="const"><type><classname>iterator</classname></type><purpose>Iterator on the beginning of the dataset. </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="G"><paramtype>Generator &amp;&amp;</paramtype></parameter></constructor>
<constructor><parameter name="rhs"><paramtype><classname>generated_by</classname> &amp;&amp;</paramtype></parameter></constructor>
</class><struct-specialization name="is_dataset"><template>
      <template-type-parameter name="Generator"/>
    </template><specialization><template-arg>generated_by&lt; Generator &gt;</template-arg></specialization><inherit access="public">true_</inherit><purpose>A generated dataset is a dataset. </purpose></struct-specialization>








</namespace>
















































</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/monomorphic/generators.hpp">
<para>Defines specific generators. </para></header>
<header name="boost/test/data/monomorphic/generators/keywords.hpp">
<para>Keywords used in generator interfaces. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
















































</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/monomorphic/generators/random.hpp">
<para>Random generator. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
<namespace name="monomorphic">
<class name="random_t"><template>
      <template-type-parameter name="SampleType"><default>double</default></template-type-parameter>
      <template-type-parameter name="DistributionType"><default>typename <classname alt="boost::unit_test::data::monomorphic::ds_detail::default_distribution">ds_detail::default_distribution</classname>&lt;SampleType&gt;::type</default></template-type-parameter>
      <template-type-parameter name="EngineType"><default>std::default_random_engine</default></template-type-parameter>
    </template><purpose>Generator for the random sequences. </purpose><description><para>This class implements the generator concept (see <classname alt="boost::unit_test::data::monomorphic::generated_by">boost::unit_test::data::monomorphic::generated_by</classname>) for implementing a random number generator. </para></description><typedef name="sample"><type>SampleType</type></typedef>
<typedef name="distr_type"><type>DistributionType</type></typedef>
<typedef name="engine_type"><type>EngineType</type></typedef>
<method-group name="public member functions">
<method name="capacity" cv="const"><type><classname>data::size_t</classname></type></method>
<method name="next"><type>SampleType</type></method>
<method name="reset"><type>void</type></method>
<method name="seed"><type>void</type><template>
          <template-type-parameter name="SeedType"/>
        </template><parameter name="seed"><paramtype>SeedType &amp;&amp;</paramtype></parameter><purpose>Sets the seed of the pseudo-random number engine. </purpose></method>
</method-group>
<constructor/>
<constructor specifiers="explicit"><parameter name="d"><paramtype>distr_type &amp;&amp;</paramtype></parameter></constructor>
<constructor><parameter name="e"><paramtype>engine_type &amp;&amp;</paramtype></parameter><parameter name="d"><paramtype>distr_type &amp;&amp;</paramtype></parameter></constructor>
</class>








</namespace>








































<overloaded-function name="random"><signature><type><classname>monomorphic::generated_by</classname>&lt; <classname>monomorphic::random_t</classname>&lt;&gt; &gt;</type></signature><signature><type><classname>monomorphic::generated_by</classname>&lt; <classname>monomorphic::random_t</classname>&lt; SampleType &gt; &gt;</type><template>
          <template-type-parameter name="SampleType"/>
        </template><parameter name="begin"><paramtype>SampleType</paramtype></parameter><parameter name="end"><paramtype>SampleType</paramtype></parameter></signature><signature><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="Params"/>
        </template><parameter name="params"><paramtype>Params const &amp;</paramtype></parameter></signature><purpose>Returns an infinite sequence of random numbers. </purpose><description><para>The following overloads are available: <programlisting language="c++">auto d = random();
auto d = random(begin, end);
auto d = random(params);
</programlisting></para><para><itemizedlist>
<listitem><para>The first overload uses the default distribution, which is uniform and which elements are <computeroutput>double</computeroutput> type (the values are in [0, 1) ).</para>
</listitem><listitem><para>The second overload generates numbers in the given interval. The distribution is uniform (in [begin, end) for real numbers, and in [begin, end] for integers). The type of the distribution is deduced from the type of the <computeroutput>begin</computeroutput> and <computeroutput>end</computeroutput> parameters.</para>
</listitem><listitem><para>The third overload generates numbers using the named parameter inside <computeroutput>params</computeroutput> , which are:<itemizedlist>
<listitem><para><computeroutput>distribution:</computeroutput> the distribution used. In this overload, since the type of the samples cannot be deduced, the samples are of type <computeroutput>double</computeroutput> and the distribution is uniform real in [0, 1).</para>
</listitem><listitem><para><computeroutput>seed:</computeroutput> the seed for generating the values</para>
</listitem><listitem><para><computeroutput>engine:</computeroutput> the random number generator engine</para>
</listitem></itemizedlist>
</para>
</listitem></itemizedlist>
</para><para>The function returns an object that implements the dataset API. <note><para>This function is available only for C++11 capable compilers. </para>
</note>
</para></description></overloaded-function>







</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/monomorphic/generators/xrange.hpp">
<para>Defines range generator. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
<namespace name="monomorphic">
<class name="xrange_t"><template>
      <template-type-parameter name="SampleType"/>
      <template-type-parameter name="StepType"><default>SampleType</default></template-type-parameter>
    </template><purpose>Generator for the range sequences. </purpose><description><para>This class implements the generator concept (see <classname alt="boost::unit_test::data::monomorphic::generated_by">boost::unit_test::data::monomorphic::generated_by</classname>) for implementing a range like sequence of numbers. </para></description><typedef name="sample"><type>SampleType</type></typedef>
<method-group name="public member functions">
<method name="capacity" cv="const"><type><classname>data::size_t</classname></type></method>
<method name="next"><type>SampleType</type></method>
<method name="reset"><type>void</type></method>
</method-group>
<constructor><parameter name="begin_"><paramtype>SampleType const &amp;</paramtype></parameter><parameter name="step_"><paramtype>StepType const &amp;</paramtype></parameter><parameter name="size_"><paramtype><classname>data::size_t</classname></paramtype></parameter></constructor>
</class>








</namespace>











































<overloaded-function name="xrange"><signature><type><classname>monomorphic::generated_by</classname>&lt; <classname>monomorphic::xrange_t</classname>&lt; SampleType &gt; &gt;</type><template>
          <template-type-parameter name="SampleType"/>
          <template-type-parameter name="Params"/>
        </template><parameter name="params"><paramtype>Params const &amp;</paramtype></parameter></signature><signature><type><classname>monomorphic::generated_by</classname>&lt; <classname>monomorphic::xrange_t</classname>&lt; SampleType &gt; &gt;</type><template>
          <template-type-parameter name="SampleType"/>
        </template><parameter name="end_val"><paramtype>SampleType const &amp;</paramtype></parameter></signature><signature><type>enable_if_c&lt; <classname>nfp::is_named_param_pack</classname>&lt; Params &gt;::value, <classname>monomorphic::generated_by</classname>&lt; <classname>monomorphic::xrange_t</classname>&lt; SampleType &gt; &gt; &gt;::type</type><template>
          <template-type-parameter name="SampleType"/>
          <template-type-parameter name="Params"/>
        </template><parameter name="end_val"><paramtype>SampleType const &amp;</paramtype></parameter><parameter name="params"><paramtype>Params const &amp;</paramtype></parameter></signature><signature><type><classname>monomorphic::generated_by</classname>&lt; <classname>monomorphic::xrange_t</classname>&lt; SampleType &gt; &gt;</type><template>
          <template-type-parameter name="SampleType"/>
        </template><parameter name="begin_val"><paramtype>SampleType const &amp;</paramtype></parameter><parameter name="end_val"><paramtype>SampleType const &amp;</paramtype></parameter></signature><signature><type><classname>monomorphic::generated_by</classname>&lt; <classname>monomorphic::xrange_t</classname>&lt; SampleType &gt; &gt;</type><template>
          <template-type-parameter name="SampleType"/>
          <template-type-parameter name="StepType"/>
        </template><parameter name="begin_val"><paramtype>SampleType const &amp;</paramtype></parameter><parameter name="end_val"><paramtype>SampleType const &amp;</paramtype></parameter><parameter name="step_val"><paramtype>StepType const &amp;</paramtype></parameter></signature><purpose>Creates a range (sequence) dataset. </purpose><description><para>The following overloads are available: <programlisting language="c++">auto d = xrange();
auto d = xrange(end_val);
auto d = xrange(end_val, param);
auto d = xrange(begin_val, end_val);
auto d = xrange(begin_val, end_val, step_val);
auto d = xrange(param);
</programlisting></para><para><itemizedlist>
<listitem><para><computeroutput>begin_val</computeroutput> indicates the start of the sequence (default to 0).</para>
</listitem><listitem><para><computeroutput>end_val</computeroutput> is the end of the sequence. If ommited, the dataset has infinite size.<sbr/>
</para>
</listitem><listitem><para><computeroutput>step_val</computeroutput> is the step between two consecutive elements of the sequence, and defaults to 1.<sbr/>
</para>
</listitem><listitem><para><computeroutput>param</computeroutput> is the named parameters that describe the sequence. The following parameters are accepted:<itemizedlist>
<listitem><para><computeroutput>begin:</computeroutput> same meaning <computeroutput>begin_val</computeroutput> </para>
</listitem><listitem><para><computeroutput>end:</computeroutput> same meaning as <computeroutput>end_val</computeroutput> </para>
</listitem><listitem><para><computeroutput>step:</computeroutput> same meaning as <computeroutput>step_val</computeroutput> </para>
</listitem></itemizedlist>
</para>
</listitem></itemizedlist>
</para><para>The returned value is an object that implements the dataset API.</para><para><note><para>the step size cannot be null, and it should be positive if <computeroutput>begin_val</computeroutput> &lt; <computeroutput>end_val</computeroutput>, negative otherwise. </para>
</note>
</para></description></overloaded-function>




</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/monomorphic/grid.hpp">
<para>Defines monomorphic dataset n+m dimentional *. Samples in this dataset is grid of elements in DataSet1 and DataSet2. There will be total |DataSet1| * |DataSet2| samples. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
<namespace name="monomorphic">
<class name="grid"><template>
      <template-type-parameter name="DataSet1"/>
      <template-type-parameter name="DataSet2"/>
    </template><purpose>Implements the dataset resulting from a cartesian product/grid operation on datasets. </purpose><description><para>The arity of the resulting dataset is the sum of the arity of its operands. </para></description><struct name="iterator"><typedef name="iterator_sample"><type>decltype(sample_merge(*std::declval&lt; dataset1_iter &gt;(), *std::declval&lt; dataset2_iter &gt;()))</type></typedef>
<method-group name="public member functions">
<method name="operator *" cv="const"><type>auto</type></method>
<method name="operator++"><type>void</type></method>
</method-group>
<constructor specifiers="explicit"><parameter name="iter1"><paramtype>dataset1_iter</paramtype></parameter><parameter name="ds2"><paramtype>DataSet2 const &amp;</paramtype></parameter></constructor>
</struct><data-member name="arity" specifiers="static"><type>const int</type></data-member>
<method-group name="public member functions">
<method name="size" cv="const"><type><classname>data::size_t</classname></type></method>
<method name="begin" cv="const"><type><classname>iterator</classname></type></method>
</method-group>
<constructor><parameter name="ds1"><paramtype>DataSet1 &amp;&amp;</paramtype></parameter><parameter name="ds2"><paramtype>DataSet2 &amp;&amp;</paramtype></parameter><purpose>Constructor. </purpose></constructor>
<constructor><parameter name="j"><paramtype><classname>grid</classname> &amp;&amp;</paramtype></parameter><purpose>Move constructor. </purpose></constructor>
</class><struct-specialization name="is_dataset"><template>
      <template-type-parameter name="DataSet1"/>
      <template-type-parameter name="DataSet2"/>
    </template><specialization><template-arg>grid&lt; DataSet1</template-arg><template-arg>DataSet2 &gt;</template-arg></specialization><inherit access="public">true_</inherit></struct-specialization><namespace name="result_of">
<struct name="grid"><template>
      <template-type-parameter name="DS1Gen"/>
      <template-type-parameter name="DS2Gen"/>
    </template><purpose>Result type of the grid operation on dataset. </purpose><typedef name="type"><type><classname>monomorphic::grid</classname>&lt; typename DS1Gen::type, typename DS2Gen::type &gt;</type></typedef>
</struct></namespace>
<overloaded-function name="operator*"><signature><type>boost::lazy_enable_if_c&lt; <classname>is_dataset</classname>&lt; DataSet1 &gt;::value &amp;&amp;<classname>is_dataset</classname>&lt; DataSet2 &gt;::value, <classname>result_of::grid</classname>&lt; mpl::identity&lt; DataSet1 &gt;, mpl::identity&lt; DataSet2 &gt; &gt;&gt;::type</type><template>
          <template-type-parameter name="DataSet1"/>
          <template-type-parameter name="DataSet2"/>
        </template><parameter name="ds1"><paramtype>DataSet1 &amp;&amp;</paramtype></parameter><parameter name="ds2"><paramtype>DataSet2 &amp;&amp;</paramtype></parameter></signature><signature><type>boost::lazy_enable_if_c&lt; <classname>is_dataset</classname>&lt; DataSet1 &gt;::value &amp;&amp;!<classname>is_dataset</classname>&lt; DataSet2 &gt;::value, <classname>result_of::grid</classname>&lt; mpl::identity&lt; DataSet1 &gt;, <classname>data::result_of::make</classname>&lt; DataSet2 &gt; &gt;&gt;::type</type><template>
          <template-type-parameter name="DataSet1"/>
          <template-type-parameter name="DataSet2"/>
        </template><parameter name="ds1"><paramtype>DataSet1 &amp;&amp;</paramtype></parameter><parameter name="ds2"><paramtype>DataSet2 &amp;&amp;</paramtype></parameter></signature><signature><type>boost::lazy_enable_if_c&lt;!<classname>is_dataset</classname>&lt; DataSet1 &gt;::value &amp;&amp;<classname>is_dataset</classname>&lt; DataSet2 &gt;::value, <classname>result_of::grid</classname>&lt; <classname>data::result_of::make</classname>&lt; DataSet1 &gt;, mpl::identity&lt; DataSet2 &gt; &gt;&gt;::type</type><template>
          <template-type-parameter name="DataSet1"/>
          <template-type-parameter name="DataSet2"/>
        </template><parameter name="ds1"><paramtype>DataSet1 &amp;&amp;</paramtype></parameter><parameter name="ds2"><paramtype>DataSet2 &amp;&amp;</paramtype></parameter></signature><purpose>Grid operation. </purpose></overloaded-function>








</namespace>
















































</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/monomorphic/initializer_list.hpp">
<para>Defines monomorphic dataset based on C++11 initializer_list template. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
<namespace name="monomorphic">
<class name="init_list"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Dataset view from an initializer_list or variadic template arguments. </purpose><description><para>The data should be stored in the dataset, and since the elements are passed by an <computeroutput>std::initializer_list</computeroutput> , it implies a copy of the elements. </para></description><typedef name="iterator"><type>std::vector&lt; T &gt;::const_iterator</type></typedef>
<data-member name="arity" specifiers="static"><type>const int</type></data-member>
<method-group name="public member functions">
<method name="size" cv="const"><type><classname>data::size_t</classname></type><purpose>dataset interface </purpose></method>
<method name="begin" cv="const"><type>iterator</type></method>
</method-group>
<constructor><parameter name="il"><paramtype>std::initializer_list&lt; T &gt;</paramtype></parameter><purpose>Constructor copies content of initializer_list. </purpose></constructor>
<constructor><template>
          <template-nontype-parameter name="Args"><type>class ...</type></template-nontype-parameter>
        </template><parameter name="args"><paramtype>Args &amp;&amp;...</paramtype></parameter><purpose>Variadic template initialization. </purpose></constructor>
</class><class-specialization name="init_list"><template>
    </template><specialization><template-arg>bool</template-arg></specialization><purpose>Specialization of <classname alt="boost::unit_test::data::monomorphic::init_list">init_list</classname> for type bool. </purpose><struct name="non_proxy_iterator"><data-member name="iterator"><type>std::vector&lt; bool &gt;::const_iterator</type></data-member>
<method-group name="public member functions">
<method name="operator *" cv="const"><type>bool</type></method>
<method name="operator++"><type>non_proxy_iterator &amp;</type></method>
</method-group>
<constructor><parameter name="it"><paramtype>std::vector&lt; bool &gt;::const_iterator &amp;&amp;</paramtype></parameter></constructor>
</struct><typedef name="sample"><type>bool</type></typedef>
<typedef name="iterator"><type>non_proxy_iterator</type></typedef>
<data-member name="arity" specifiers="static"><type>const int</type></data-member>
<method-group name="public member functions">
<method name="size" cv="const"><type><classname>data::size_t</classname></type><purpose>dataset interface </purpose></method>
<method name="begin" cv="const"><type>iterator</type></method>
</method-group>
<constructor><parameter name="il"><paramtype>std::initializer_list&lt; bool &gt; &amp;&amp;</paramtype></parameter><purpose>Constructor copies content of initializer_list. </purpose></constructor>
<constructor><template>
          <template-nontype-parameter name="Args"><type>class ...</type></template-nontype-parameter>
        </template><parameter name="args"><paramtype>Args &amp;&amp;...</paramtype></parameter><purpose>Variadic template initialization. </purpose></constructor>
</class-specialization><struct-specialization name="is_dataset"><template>
      <template-type-parameter name="T"/>
    </template><specialization><template-arg>init_list&lt; T &gt;</template-arg></specialization><inherit access="public">true_</inherit><purpose>An array dataset is a dataset. </purpose></struct-specialization>








</namespace>
















































</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/monomorphic/join.hpp">
<para>Defines dataset join operation. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
<namespace name="monomorphic">
<struct-specialization name="is_dataset"><template>
      <template-type-parameter name="DataSet1"/>
      <template-type-parameter name="DataSet2"/>
    </template><specialization><template-arg>join&lt; DataSet1</template-arg><template-arg>DataSet2 &gt;</template-arg></specialization><inherit access="public">true_</inherit></struct-specialization><class name="join"><template>
      <template-type-parameter name="DataSet1"/>
      <template-type-parameter name="DataSet2"/>
    </template><purpose>Defines a new dataset from the concatenation of two datasets. </purpose><description><para>The size of the resulting dataset is the sum of the two underlying datasets. The arity of the datasets should match. </para></description><struct name="iterator"><method-group name="public member functions">
<method name="operator *" cv="const"><type>sample_t</type></method>
<method name="operator++"><type>void</type></method>
</method-group>
<constructor specifiers="explicit"><parameter name="it1"><paramtype>dataset1_iter &amp;&amp;</paramtype></parameter><parameter name="it2"><paramtype>dataset2_iter &amp;&amp;</paramtype></parameter><parameter name="first_size"><paramtype><classname>data::size_t</classname></paramtype></parameter></constructor>
</struct><typedef name="sample_t"><type>typename std::conditional&lt; std::is_reference&lt; iter1_ret &gt;::value &amp;&amp;std::is_reference&lt; iter2_ret &gt;::value &amp;&amp;std::is_same&lt; iter1_ret, iter2_ret &gt;::value, iter1_ret, typename std::remove_reference&lt; iter1_ret &gt;::type &gt;::type</type></typedef>
<data-member name="arity" specifiers="static"><type>const int</type></data-member>
<method-group name="public member functions">
<method name="size" cv="const"><type><classname>data::size_t</classname></type><purpose>dataset interface </purpose></method>
<method name="begin" cv="const"><type><classname>iterator</classname></type></method>
</method-group>
<constructor><parameter name="ds1"><paramtype>DataSet1 &amp;&amp;</paramtype></parameter><parameter name="ds2"><paramtype>DataSet2 &amp;&amp;</paramtype></parameter><purpose>Constructor. </purpose></constructor>
<constructor><parameter name="j"><paramtype><classname>join</classname> &amp;&amp;</paramtype></parameter><purpose>Move constructor. </purpose></constructor>
</class><namespace name="result_of">
<struct name="join"><template>
      <template-type-parameter name="DataSet1Gen"/>
      <template-type-parameter name="DataSet2Gen"/>
    </template><purpose>Result type of the join operation on datasets. </purpose><typedef name="type"><type><classname>monomorphic::join</classname>&lt; typename DataSet1Gen::type, typename DataSet2Gen::type &gt;</type></typedef>
</struct></namespace>



<function name="operator+"><type>boost::lazy_enable_if_c&lt; <classname>is_dataset</classname>&lt; DataSet1 &gt;::value &amp;&amp;<classname>is_dataset</classname>&lt; DataSet2 &gt;::value, <classname>result_of::join</classname>&lt; mpl::identity&lt; DataSet1 &gt;, mpl::identity&lt; DataSet2 &gt; &gt;&gt;::type</type><template>
          <template-type-parameter name="DataSet1"/>
          <template-type-parameter name="DataSet2"/>
        </template><parameter name="ds1"><paramtype>DataSet1 &amp;&amp;</paramtype></parameter><parameter name="ds2"><paramtype>DataSet2 &amp;&amp;</paramtype></parameter></function>
<function name="operator+"><type>boost::lazy_enable_if_c&lt; <classname>is_dataset</classname>&lt; DataSet1 &gt;::value &amp;&amp;!<classname>is_dataset</classname>&lt; DataSet2 &gt;::value, <classname>result_of::join</classname>&lt; mpl::identity&lt; DataSet1 &gt;, <classname>data::result_of::make</classname>&lt; DataSet2 &gt; &gt;&gt;::type</type><template>
          <template-type-parameter name="DataSet1"/>
          <template-type-parameter name="DataSet2"/>
        </template><parameter name="ds1"><paramtype>DataSet1 &amp;&amp;</paramtype></parameter><parameter name="ds2"><paramtype>DataSet2 &amp;&amp;</paramtype></parameter></function>
<function name="operator+"><type>boost::lazy_enable_if_c&lt;!<classname>is_dataset</classname>&lt; DataSet1 &gt;::value &amp;&amp;<classname>is_dataset</classname>&lt; DataSet2 &gt;::value, <classname>result_of::join</classname>&lt; <classname>data::result_of::make</classname>&lt; DataSet1 &gt;, mpl::identity&lt; DataSet2 &gt; &gt;&gt;::type</type><template>
          <template-type-parameter name="DataSet1"/>
          <template-type-parameter name="DataSet2"/>
        </template><parameter name="ds1"><paramtype>DataSet1 &amp;&amp;</paramtype></parameter><parameter name="ds2"><paramtype>DataSet2 &amp;&amp;</paramtype></parameter></function>



</namespace>
















































</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/monomorphic/singleton.hpp">
<para>Defines single element monomorphic dataset. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
<namespace name="monomorphic">
<struct-specialization name="is_dataset"><template>
      <template-type-parameter name="T"/>
    </template><specialization><template-arg>singleton&lt; T &gt;</template-arg></specialization><inherit access="public">true_</inherit></struct-specialization><class name="singleton"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Models a single element data set. </purpose><struct name="iterator"><method-group name="public member functions">
<method name="operator *" cv="const"><type>sample const  &amp;</type></method>
<method name="operator++"><type>void</type></method>
</method-group>
<constructor specifiers="explicit"><parameter name="owner"><paramtype><classname>singleton</classname>&lt; T &gt; const *</paramtype></parameter></constructor>
</struct><data-member name="arity" specifiers="static"><type>const int</type></data-member>
<method-group name="public member functions">
<method name="value" cv="const"><type>T const  &amp;</type><purpose>Value access method. </purpose></method>
<method name="size" cv="const"><type><classname>data::size_t</classname></type><purpose>dataset interface </purpose></method>
<method name="begin" cv="const"><type><classname>iterator</classname></type></method>
</method-group>
<constructor specifiers="explicit"><parameter name="value"><paramtype>T &amp;&amp;</paramtype></parameter><purpose>Constructor. </purpose></constructor>
<constructor><parameter name="s"><paramtype><classname>singleton</classname> &amp;&amp;</paramtype></parameter><purpose>Move constructor. </purpose></constructor>
</class>








</namespace>
















































</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/monomorphic/zip.hpp">
<para>Defines monomorphic dataset based on zipping of 2 other monomorphic datasets. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
<namespace name="monomorphic">
<struct-specialization name="is_dataset"><template>
      <template-type-parameter name="DataSet1"/>
      <template-type-parameter name="DataSet2"/>
    </template><specialization><template-arg>zip&lt; DataSet1</template-arg><template-arg>DataSet2 &gt;</template-arg></specialization><inherit access="public">true_</inherit><purpose>Zipped datasets results in a dataset. </purpose></struct-specialization><class name="zip"><template>
      <template-type-parameter name="DataSet1"/>
      <template-type-parameter name="DataSet2"/>
    </template><purpose>Zip datasets. </purpose><description><para>A zip of two datasets is a dataset whose arity is the sum of the operand datasets arity. The size is given by the function creating the instance (see <computeroutput>operator^</computeroutput> on datasets). </para></description><struct name="iterator"><typedef name="iterator_sample"><type>decltype(sample_merge(*std::declval&lt; dataset1_iter &gt;(), *std::declval&lt; dataset2_iter &gt;()))</type></typedef>
<method-group name="public member functions">
<method name="operator *" cv="const"><type>auto</type></method>
<method name="operator++"><type>void</type></method>
</method-group>
<constructor specifiers="explicit"><parameter name="iter1"><paramtype>dataset1_iter</paramtype></parameter><parameter name="iter2"><paramtype>dataset2_iter</paramtype></parameter></constructor>
</struct><data-member name="arity" specifiers="static"><type>const int</type></data-member>
<method-group name="public member functions">
<method name="size" cv="const"><type><classname>data::size_t</classname></type></method>
<method name="begin" cv="const"><type><classname>iterator</classname></type></method>
</method-group>
<constructor><parameter name="ds1"><paramtype>DataSet1 &amp;&amp;</paramtype></parameter><parameter name="ds2"><paramtype>DataSet2 &amp;&amp;</paramtype></parameter><purpose>Constructor. </purpose><description><para>The datasets are moved and not copied. </para></description></constructor>
<constructor><parameter name="j"><paramtype><classname>zip</classname> &amp;&amp;</paramtype></parameter><purpose>Move constructor. </purpose></constructor>
<method-group name="private member functions">
<method name="zip_size" cv="const"><type><classname>data::size_t</classname></type><purpose>Handles the sise of the resulting zipped dataset. </purpose></method>
</method-group>
</class><namespace name="result_of">
<struct name="zip"><template>
      <template-type-parameter name="DS1Gen"/>
      <template-type-parameter name="DS2Gen"/>
    </template><purpose>Result type of the zip operator. </purpose><typedef name="type"><type><classname>monomorphic::zip</classname>&lt; typename DS1Gen::type, typename DS2Gen::type &gt;</type></typedef>
</struct></namespace>






<overloaded-function name="operator^"><signature><type>boost::lazy_enable_if_c&lt; <classname>is_dataset</classname>&lt; DataSet1 &gt;::value &amp;&amp;<classname>is_dataset</classname>&lt; DataSet2 &gt;::value, <classname>result_of::zip</classname>&lt; mpl::identity&lt; DataSet1 &gt;, mpl::identity&lt; DataSet2 &gt; &gt;&gt;::type</type><template>
          <template-type-parameter name="DataSet1"/>
          <template-type-parameter name="DataSet2"/>
        </template><parameter name="ds1"><paramtype>DataSet1 &amp;&amp;</paramtype></parameter><parameter name="ds2"><paramtype>DataSet2 &amp;&amp;</paramtype></parameter></signature><signature><type>boost::lazy_enable_if_c&lt; <classname>is_dataset</classname>&lt; DataSet1 &gt;::value &amp;&amp;!<classname>is_dataset</classname>&lt; DataSet2 &gt;::value, <classname>result_of::zip</classname>&lt; mpl::identity&lt; DataSet1 &gt;, <classname>data::result_of::make</classname>&lt; DataSet2 &gt; &gt;&gt;::type</type><template>
          <template-type-parameter name="DataSet1"/>
          <template-type-parameter name="DataSet2"/>
        </template><parameter name="ds1"><paramtype>DataSet1 &amp;&amp;</paramtype></parameter><parameter name="ds2"><paramtype>DataSet2 &amp;&amp;</paramtype></parameter></signature><signature><type>boost::lazy_enable_if_c&lt;!<classname>is_dataset</classname>&lt; DataSet1 &gt;::value &amp;&amp;<classname>is_dataset</classname>&lt; DataSet2 &gt;::value, <classname>result_of::zip</classname>&lt; <classname>data::result_of::make</classname>&lt; DataSet1 &gt;, mpl::identity&lt; DataSet2 &gt; &gt;&gt;::type</type><template>
          <template-type-parameter name="DataSet1"/>
          <template-type-parameter name="DataSet2"/>
        </template><parameter name="ds1"><paramtype>DataSet1 &amp;&amp;</paramtype></parameter><parameter name="ds2"><paramtype>DataSet2 &amp;&amp;</paramtype></parameter></signature><purpose>Overload operator for zip support. </purpose></overloaded-function>


</namespace>
















































</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/size.hpp">
<para>simple dataset size abstraction (can be infinite) </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
<class name="size_t"><purpose>Utility for handling the size of a datasets. </purpose><struct name="dummy"><method-group name="public member functions">
<method name="nonnull"><type>void</type></method>
</method-group>
</struct><method-group name="public member functions">
<method name="value" cv="const"><type>std::size_t</type></method>
<method name="is_inf" cv="const"><type>bool</type></method>
<method name="conversion-operator" cv="const"><type>safe_bool</type></method>
<method name="operator--"><type><classname>data::size_t</classname></type></method>
<method name="operator--"><type><classname>data::size_t</classname></type><parameter name=""><paramtype>int</paramtype></parameter></method>
<method name="operator++"><type><classname>data::size_t</classname></type></method>
<method name="operator++"><type><classname>data::size_t</classname></type><parameter name=""><paramtype>int</paramtype></parameter></method>
<method name="operator+="><type><classname>data::size_t</classname> &amp;</type><parameter name="rhs"><paramtype>std::size_t</paramtype></parameter></method>
<method name="operator+="><type><classname>data::size_t</classname> &amp;</type><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></method>
<method name="operator-="><type><classname>data::size_t</classname> &amp;</type><parameter name="rhs"><paramtype>std::size_t</paramtype></parameter></method>
<method name="operator-="><type><classname>data::size_t</classname> &amp;</type><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></method>
</method-group>
<constructor><parameter name="s"><paramtype>std::size_t</paramtype><default>0</default></parameter></constructor>
<constructor specifiers="explicit"><parameter name=""><paramtype>bool</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="T"/>
        </template><parameter name="v"><paramtype>T</paramtype></parameter></constructor>
</class>













<function name="operator&gt;"><type>bool</type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype>std::size_t</paramtype></parameter></function>
<function name="operator&gt;"><type>bool</type><parameter name="lhs"><paramtype>std::size_t</paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator&gt;"><type>bool</type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator&gt;="><type>bool</type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype>std::size_t</paramtype></parameter></function>
<function name="operator&gt;="><type>bool</type><parameter name="lhs"><paramtype>std::size_t</paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator&gt;="><type>bool</type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator&lt;"><type>bool</type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype>std::size_t</paramtype></parameter></function>
<function name="operator&lt;"><type>bool</type><parameter name="lhs"><paramtype>std::size_t</paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator&lt;"><type>bool</type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator&lt;="><type>bool</type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype>std::size_t</paramtype></parameter></function>
<function name="operator&lt;="><type>bool</type><parameter name="lhs"><paramtype>std::size_t</paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator&lt;="><type>bool</type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator=="><type>bool</type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype>std::size_t</paramtype></parameter></function>
<function name="operator=="><type>bool</type><parameter name="lhs"><paramtype>std::size_t</paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator=="><type>bool</type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator!="><type>bool</type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype>std::size_t</paramtype></parameter></function>
<function name="operator!="><type>bool</type><parameter name="lhs"><paramtype>std::size_t</paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator!="><type>bool</type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator+"><type><classname>data::size_t</classname></type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype>std::size_t</paramtype></parameter></function>
<function name="operator+"><type><classname>data::size_t</classname></type><parameter name="lhs"><paramtype>std::size_t</paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator+"><type><classname>data::size_t</classname></type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator*"><type><classname>data::size_t</classname></type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype>std::size_t</paramtype></parameter></function>
<function name="operator*"><type><classname>data::size_t</classname></type><parameter name="lhs"><paramtype>std::size_t</paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator*"><type><classname>data::size_t</classname></type><parameter name="lhs"><paramtype><classname>data::size_t</classname></paramtype></parameter><parameter name="rhs"><paramtype><classname>data::size_t</classname></paramtype></parameter></function>
<function name="operator&lt;&lt;"><type>std::basic_ostream&lt; CharT1, Tr &gt; &amp;</type><template>
          <template-type-parameter name="CharT1"/>
          <template-type-parameter name="Tr"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; CharT1, Tr &gt; &amp;</paramtype></parameter><parameter name="s"><paramtype><classname>data::size_t</classname> const &amp;</paramtype></parameter></function>









</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/data/test_case.hpp">
<para>test case family based on data generator </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="data">
















































</namespace>






</namespace>
</namespace>
<macro name="BOOST_TEST_DATASET_VARIADIC"/>
<macro name="BOOST_DATA_TEST_CASE_PARAM" kind="functionlike"><macro-parameter name="r"/><macro-parameter name="_"/><macro-parameter name="i"/><macro-parameter name="param"/></macro>
<macro name="BOOST_DATA_TEST_CONTEXT" kind="functionlike"><macro-parameter name="r"/><macro-parameter name="_"/><macro-parameter name="param"/></macro>
<macro name="BOOST_DATA_TEST_CASE_PARAMS" kind="functionlike"><macro-parameter name="params"/></macro>
<macro name="BOOST_DATA_TEST_CASE_IMPL" kind="functionlike"><macro-parameter name="arity"/><macro-parameter name="F"/><macro-parameter name="test_name"/><macro-parameter name="dataset"/><macro-parameter name="params"/></macro>
<macro name="BOOST_DATA_TEST_CASE_WITH_PARAMS" kind="functionlike"><macro-parameter name="F"/><macro-parameter name="test_name"/><macro-parameter name="dataset"/><macro-parameter name="..."/></macro>
<macro name="BOOST_DATA_TEST_CASE_NO_PARAMS" kind="functionlike"><macro-parameter name="F"/><macro-parameter name="test_name"/><macro-parameter name="dataset"/></macro>
<macro name="BOOST_DATA_TEST_CASE" kind="functionlike"><macro-parameter name="..."/></macro>
<macro name="BOOST_DATA_TEST_CASE_F" kind="functionlike"><macro-parameter name="F"/><macro-parameter name="..."/></macro>
</header>
<header name="boost/test/debug.hpp">
<para>defines portable debug interfaces </para><para>Intended to standardize interface of programs with debuggers </para><namespace name="boost">
<namespace name="debug">
<struct name="dbg_startup_info"><purpose>Collection of data, which is used by debugger starter routine. </purpose><data-member name="pid"><type>long</type><purpose>pid of a program to attach to </purpose></data-member>
<data-member name="break_or_continue"><type>bool</type><purpose>what to do after debugger is attached </purpose></data-member>
<data-member name="binary_path"><type>unit_test::const_string</type><purpose>path to executable for current process </purpose></data-member>
<data-member name="display"><type>unit_test::const_string</type><purpose>if debugger has a GUI, which display to use (on UNIX) </purpose></data-member>
<data-member name="init_done_lock"><type>unit_test::const_string</type><purpose>path to a uniquely named lock file, which is used to pause current application while debugger is being initialized </purpose></data-member>
</struct><typedef name="dbg_starter"><purpose>Signature of debugger starter routine. Takes an instance of dbg_startup_into as only argument. </purpose><type>boost::function&lt; void(<classname>dbg_startup_info</classname> const  &amp;)&gt;</type></typedef>
<function name="under_debugger"><type>bool</type><purpose>Checks if programs runs under debugger. </purpose><description><para>
</para></description><returns><para>true if current process is under debugger. False otherwise </para>
</returns></function>
<function name="debugger_break"><type>void</type><purpose>Cause program to break execution in debugger at call point. </purpose></function>
<function name="set_debugger"><type>std::string</type><parameter name="dbg_id"><paramtype>unit_test::const_string</paramtype><description><para>Unique id for debugger configuration (for example, gdb) </para></description></parameter><parameter name="s"><paramtype>dbg_starter</paramtype><default>dbg_starter()</default><description><para>Optional starter routine for selected configuration (use only you want to define your own configuration) </para></description></parameter><purpose>Specifies which debugger to use when attaching and optionally what routine to use to start that debugger. </purpose><description><para>There are many different debuggers available for different platforms. Some of them also can be used in a different setups/configuratins. For example, gdb can be used in plain text mode, inside ddd, inside (x)emacs or in a separate xterm window. Boost.Test identifies each configuration with unique string. Also different debuggers configurations require different routines which is specifically tailored to start that debugger configuration. Boost.Test comes with set of predefined configuration names and corresponding routines for these configurations:<itemizedlist>
<listitem><para>TODO</para>
</listitem></itemizedlist>
</para><para>You can use this routine to select which one of the predefined debugger configurations to use in which case you do not need to provide starter routine (the one provided by Boost.Test will be used). You can also use this routine to select your own debugger by providing unique configuration id and starter routine for this configuration.</para><para>

</para></description><returns><para>Id of previously selected debugger configuration </para>
</returns></function>
<function name="attach_debugger"><type>bool</type><parameter name="break_or_continue"><paramtype>bool</paramtype><default>true</default><description><para>tells what we wan to do after the debugger is attached. If true - process execution breaks in the point in invocation of this function. Otherwise execution continues, but now it is under the debugger </para></description></parameter><purpose>Attaches debugger to the current process. </purpose><description><para>Using currently selected debugger, this routine attempts to attach the debugger to this process. 

</para></description><returns><para>true if debugger successfully attached. False otherwise </para>
</returns></function>
<function name="detect_memory_leaks"><type>void</type><parameter name="on_off"><paramtype>bool</paramtype><description><para>boolean switch </para></description></parameter><parameter name="report_file"><paramtype>unit_test::const_string</paramtype><default>unit_test::const_string()</default><description><para>file, where the report should be directed to </para></description></parameter><purpose>Switches on/off memory leaks detection. </purpose><description><para>On platforms where memory leak detection is possible inside of running application (at the moment this is only Windows family) you can switch this feature on and off using this interface. In addition you can specify the name of the file to write a report into. Otherwise the report is going to be generated in standard error stream. 
</para></description></function>
<function name="break_memory_alloc"><type>void</type><parameter name="mem_alloc_order_num"><paramtype>long</paramtype><description><para>Specific memory allocation number </para></description></parameter><purpose>Causes program to break execution in debugger at specific allocation point. </purpose><description><para>On some platforms/memory managers (at the moment only on Windows/Visual Studio) one can tell a C Runtime to break on specific memory allocation. This can be used in combination with memory leak detection (which reports leaked memory allocation number) to locate the place where leak initiated. 
</para></description></function>
</namespace>
</namespace>
</header>
<header name="boost/test/debug_config.hpp">
<para>user's config for Boost.Test debugging support </para><para>This file is intended to be edited by end user to specify varios macros, which configure debugger interface Alterntively you can set these parameters in your own sources/makefiles </para></header>
<header name="boost/test/detail/global_typedef.hpp">
<para>some trivial global typedefs </para><namespace name="boost">
<namespace name="unit_test">
<struct name="static_constant"><template>
      <template-type-parameter name="T"/>
    </template><data-member name="value" specifiers="static"><type>T</type></data-member>
</struct><enum name="report_level"><enumvalue name="INV_REPORT_LEVEL"/><enumvalue name="CONFIRMATION_REPORT"/><enumvalue name="SHORT_REPORT"/><enumvalue name="DETAILED_REPORT"/><enumvalue name="NO_REPORT"/></enum>
<enum name="output_format"><enumvalue name="OF_INVALID"/><enumvalue name="OF_CLF"><purpose>compiler log format </purpose></enumvalue><enumvalue name="OF_XML"><purpose>XML format for report and log,. </purpose></enumvalue><enumvalue name="OF_JUNIT"><purpose>JUNIT format for report and log,. </purpose></enumvalue><enumvalue name="OF_CUSTOM_LOGGER"><purpose>User specified logger. </purpose></enumvalue><enumvalue name="OF_DOT"><purpose>dot format for output content </purpose></enumvalue><purpose>Indicates the output format for the loggers or the test tree printing. </purpose></enum>
<enum name="test_unit_type"><enumvalue name="TUT_CASE"><default>= 0x01</default></enumvalue><enumvalue name="TUT_SUITE"><default>= 0x10</default></enumvalue><enumvalue name="TUT_ANY"><default>= 0x11</default></enumvalue></enum>
<enum name="assertion_result"><enumvalue name="AR_FAILED"/><enumvalue name="AR_PASSED"/><enumvalue name="AR_TRIGGERED"/></enum>
<typedef name="counter_t"><type>unsigned long</type></typedef>
<typedef name="test_unit_id"><type>unsigned long</type></typedef>
<data-member name="INV_TEST_UNIT_ID"><type>const test_unit_id</type></data-member>
<data-member name="MAX_TEST_CASE_ID"><type>const test_unit_id</type></data-member>
<data-member name="MIN_TEST_CASE_ID"><type>const test_unit_id</type></data-member>
<data-member name="MAX_TEST_SUITE_ID"><type>const test_unit_id</type></data-member>
<data-member name="MIN_TEST_SUITE_ID"><type>const test_unit_id</type></data-member>






</namespace>
</namespace>
<macro name="BOOST_TEST_L" kind="functionlike"><macro-parameter name="s"/></macro>
<macro name="BOOST_TEST_STRINGIZE" kind="functionlike"><macro-parameter name="s"/></macro>
<macro name="BOOST_TEST_EMPTY_STRING"/>
<macro name="BOOST_TEST_SINGLETON_CONS_NO_CTOR" kind="functionlike"><macro-parameter name="type"/></macro>
<macro name="BOOST_TEST_SINGLETON_CONS" kind="functionlike"><macro-parameter name="type"/></macro>
<macro name="BOOST_TEST_SINGLETON_CONS_IMPL" kind="functionlike"><macro-parameter name="type"/></macro>
<macro name="BOOST_TEST_SINGLETON_INST" kind="functionlike"><macro-parameter name="inst"/></macro>
</header>
<header name="boost/test/execution_monitor.hpp">
<para>Defines public interface of the Execution Monitor and related classes. </para><namespace name="boost">
<class name="execution_aborted"><purpose>This is a trivial default constructible class. Use it to report graceful abortion of a monitored function execution. </purpose></class><class name="execution_exception"><purpose>This class is used to report any kind of an failure during execution of a monitored function inside of <classname alt="boost::execution_monitor">execution_monitor</classname>. </purpose><description><para>The instance of this class is thrown out of execution_monitor::execute invocation when failure is detected. Regardless of a kind of failure occurred the instance will provide a uniform way to catch and report it.</para><para>One important design rationale for this class is that we should be ready to work after fatal memory corruptions or out of memory conditions. To facilitate this class never allocates any memory and assumes that strings it refers to are either some constants or live in a some kind of persistent (preallocated) memory. </para></description><struct name="location"><purpose>Simple model for the location of failure in a source code. </purpose><data-member name="m_file_name"><type>const_string</type><purpose>File name. </purpose></data-member>
<data-member name="m_line_num"><type>size_t</type><purpose>Line number. </purpose></data-member>
<data-member name="m_function"><type>const_string</type><purpose>Function name. </purpose></data-member>
<method-group name="public member functions">
</method-group>
<constructor specifiers="explicit"><parameter name="file_name"><paramtype>char const *</paramtype><default>0</default></parameter><parameter name="line_num"><paramtype>size_t</paramtype><default>0</default></parameter><parameter name="func"><paramtype>char const *</paramtype><default>0</default></parameter></constructor>
<constructor specifiers="explicit"><parameter name="file_name"><paramtype>const_string</paramtype></parameter><parameter name="line_num"><paramtype>size_t</paramtype><default>0</default></parameter><parameter name="func"><paramtype>char const *</paramtype><default>0</default></parameter></constructor>
</struct><constructor><parameter name="ec"><paramtype>error_code</paramtype><description><para>error code </para></description></parameter><parameter name="what_msg"><paramtype>const_string</paramtype><description><para>error message </para></description></parameter><parameter name="location"><paramtype><classname>location</classname> const &amp;</paramtype><description><para>error location </para></description></parameter><purpose>Constructs instance based on message, location and error code. </purpose><description><para>
</para></description></constructor>
<enum name="error_code"><enumvalue name="no_error"><default>= 0</default><purpose>for completeness only; never returned </purpose></enumvalue><enumvalue name="user_error"><default>= 200</default><purpose>user reported non-fatal error </purpose></enumvalue><enumvalue name="cpp_exception_error"><default>= 205</default><purpose>see note (1) above </purpose></enumvalue><enumvalue name="system_error"><default>= 210</default><purpose>see note (2) above </purpose></enumvalue><enumvalue name="timeout_error"><default>= 215</default><purpose>only detectable on certain platforms </purpose></enumvalue><enumvalue name="user_fatal_error"><default>= 220</default><purpose>user reported fatal error </purpose></enumvalue><enumvalue name="system_fatal_error"><default>= 225</default><purpose>see note (2) above </purpose></enumvalue><purpose>These values are sometimes used as program return codes. The particular values have been chosen to avoid conflicts with commonly used program return codes: values &lt; 100 are often user assigned, values &gt; 255 are sometimes used to report system errors. Gaps in values allow for orderly expansion. </purpose><description><para><note><para>(1) Only uncaught C++ exceptions are treated as errors. If a function catches a C++ exception, it never reaches the <classname alt="boost::execution_monitor">execution_monitor</classname>.</para>
</note>
The implementation decides what is a system_fatal_error and what is just a system_exception. Fatal errors are so likely to have corrupted machine state (like a stack overflow or addressing exception) that it is unreasonable to continue execution.</para><para><note><para>(2) These errors include Unix signals and Windows structured exceptions. They are often initiated by hardware traps. </para>
</note>
</para></description></enum>
</class><class name="execution_monitor"><purpose>Function execution monitor. </purpose><description><para>This class is used to uniformly detect and report an occurrence of several types of signals and exceptions, reducing various errors to a uniform <classname alt="boost::execution_exception">execution_exception</classname> that is returned to a caller.</para><para>The <classname alt="boost::execution_monitor">execution_monitor</classname> behavior can be customized through a set of public parameters (properties) associated with the <classname alt="boost::execution_monitor">execution_monitor</classname> instance. All parameters are implemented as public unit_test::readwrite_property data members of the class <classname alt="boost::execution_monitor">execution_monitor</classname>. </para></description><data-member name="p_catch_system_errors"><type>unit_test::readwrite_property&lt; bool &gt;</type><purpose>Should monitor catch system errors. </purpose><description><para>The <emphasis>p_catch_system_errors</emphasis> property is a boolean flag (default value is true) specifying whether or not <classname alt="boost::execution_monitor">execution_monitor</classname> should trap system errors/system level exceptions/signals, which would cause program to crash in a regular case (without <classname alt="boost::execution_monitor">execution_monitor</classname>). Set this property to false, for example, if you wish to force coredump file creation. The Unit Test Framework provides a runtime parameter <computeroutput>--catch_system_errors=yes</computeroutput> to alter the behavior in monitored test cases. </para></description></data-member>
<data-member name="p_auto_start_dbg"><type>unit_test::readwrite_property&lt; bool &gt;</type><purpose>Should monitor try to attach debugger in case of caught system error. </purpose><description><para>The <emphasis>p_auto_start_dbg</emphasis> property is a boolean flag (default value is false) specifying whether or not <classname alt="boost::execution_monitor">execution_monitor</classname> should try to attach debugger in case system error is caught. </para></description></data-member>
<data-member name="p_timeout"><type>unit_test::readwrite_property&lt; unsigned long int &gt;</type><purpose>Specifies the seconds that elapse before a timer_error occurs. </purpose><description><para>The <emphasis>p_timeout</emphasis> property is an integer timeout (in microseconds) for monitored function execution. Use this parameter to monitor code with possible deadlocks or infinite loops. This feature is only available for some operating systems (not yet Microsoft Windows). </para></description></data-member>
<data-member name="p_use_alt_stack"><type>unit_test::readwrite_property&lt; bool &gt;</type><purpose>Should monitor use alternative stack for the signal catching. </purpose><description><para>The <emphasis>p_use_alt_stack</emphasis> property is a boolean flag (default value is false) specifying whether or not <classname alt="boost::execution_monitor">execution_monitor</classname> should use an alternative stack for the sigaction based signal catching. When enabled the signals are delivered to the <classname alt="boost::execution_monitor">execution_monitor</classname> on a stack different from current execution stack, which is safer in case if it is corrupted by monitored function. For more details on alternative stack handling see appropriate manuals. </para></description></data-member>
<data-member name="p_detect_fp_exceptions"><type>unit_test::readwrite_property&lt; unsigned &gt;</type><purpose>Should monitor try to detect hardware floating point exceptions (!= 0), and which specific exception to catch. </purpose><description><para>The <emphasis>p_detect_fp_exceptions</emphasis> property is a boolean flag (default value is false) specifying whether or not <classname alt="boost::execution_monitor">execution_monitor</classname> should install hardware traps for the floating point exception on platforms where it's supported. </para></description></data-member>
<method-group name="public member functions">
<method name="execute"><type>int</type><parameter name="F"><paramtype>boost::function&lt; int()&gt; const &amp;</paramtype><description><para>Function to monitor </para></description></parameter><purpose>Execution monitor entry point for functions returning integer value. </purpose><description><para>This method executes supplied function F inside a try/catch block and also may include other unspecified platform dependent error detection code.</para><para>This method throws an <classname alt="boost::execution_exception">execution_exception</classname> on an uncaught C++ exception, a hardware or software signal, trap, or other user exception.</para><para><note><para>execute() doesn't consider it an error for F to return a non-zero value. </para>
</note>


<para><emphasis role="bold">See Also:</emphasis><para>vexecute </para>
</para>
</para></description><returns><para>value returned by function call F(). </para>
</returns></method>
<method name="vexecute"><type>void</type><parameter name="F"><paramtype>boost::function&lt; void()&gt; const &amp;</paramtype><description><para>Function to monitor </para></description></parameter><purpose>Execution monitor entry point for functions returning void. </purpose><description><para>This method is semantically identical to execution_monitor::execute, but doesn't produce any result code. 
<para><emphasis role="bold">See Also:</emphasis><para>execute </para>
</para>
</para></description></method>
<method name="register_exception_translator"><type>void</type><template>
          <template-type-parameter name="ExceptionType"><purpose><para>type of the exception we register a translator for </para></purpose></template-type-parameter>
          <template-type-parameter name="ExceptionTranslator"><purpose><para>type of the translator we register for this exception </para></purpose></template-type-parameter>
        </template><parameter name="tr"><paramtype>ExceptionTranslator const &amp;</paramtype><description><para>translator function object with the signature <emphasis> void (ExceptionType const&amp;)</emphasis> </para></description></parameter><parameter name="tag"><paramtype>const_string</paramtype><default>const_string()</default><description><para>tag associated with this translator </para></description></parameter><parameter name=""><paramtype>boost::type&lt; ExceptionType &gt; *</paramtype><default>0</default></parameter><purpose>Registers custom (user supplied) exception translator. </purpose><description><para>This method template registers a translator for an exception type specified as a first template argument. For example <programlisting language="c++">void myExceptTr( MyException const&amp; ex ) { /*do something with the exception here*/}
em.register_exception_translator&lt;MyException&gt;( myExceptTr );
</programlisting> The translator should be any unary function/functor object which accepts MyException const&amp;. This can be free standing function or bound class method. The second argument is an optional string tag you can associate with this translator routine. The only reason to specify the tag is if you plan to erase the translator eventually. This can be useful in scenario when you reuse the same <classname alt="boost::execution_monitor">execution_monitor</classname> instance to monitor different routines and need to register a translator specific to the routine being monitored. While it is possible to erase the translator based on an exception type it was registered for, tag string provides simpler way of doing this. 

</para></description></method>
<method name="erase_exception_translator"><type>void</type><parameter name="tag"><paramtype>const_string</paramtype><description><para>tag associated with translator you wants to erase </para></description></parameter><purpose>Erases custom exception translator based on a tag. </purpose><description><para>Use the same tag as the one used during translator registration 
</para></description></method>
<method name="erase_exception_translator"><type>void</type><template>
          <template-type-parameter name="ExceptionType"/>
        </template><parameter name=""><paramtype>boost::type&lt; ExceptionType &gt; *</paramtype><default>0</default></parameter><purpose>Erases custom exception translator based on an exception type. </purpose><description><para>tparam ExceptionType Exception type for which you want to erase the translator </para></description></method>
</method-group>
<constructor><purpose>Default constructor initializes all execution monitor properties. </purpose></constructor>
<method-group name="private member functions">
<method name="catch_signals"><type>int</type><parameter name="F"><paramtype>boost::function&lt; int()&gt; const &amp;</paramtype></parameter></method>
</method-group>
</class><class name="system_error"><data-member name="p_errno"><type>long const</type></data-member>
<data-member name="p_failed_exp"><type>char const  *const</type></data-member>
<method-group name="public member functions">
</method-group>
<constructor specifiers="explicit"><parameter name="exp"><paramtype>char const *</paramtype></parameter></constructor>
</class><namespace name="fpe">
<enum name="masks"><enumvalue name="BOOST_FPE_OFF"><default>= 0</default></enumvalue><enumvalue name="BOOST_FPE_DIVBYZERO"><default>= BOOST_FPE_OFF</default></enumvalue><enumvalue name="BOOST_FPE_INEXACT"><default>= BOOST_FPE_OFF</default></enumvalue><enumvalue name="BOOST_FPE_INVALID"><default>= BOOST_FPE_OFF</default></enumvalue><enumvalue name="BOOST_FPE_OVERFLOW"><default>= BOOST_FPE_OFF</default></enumvalue><enumvalue name="BOOST_FPE_UNDERFLOW"><default>= BOOST_FPE_OFF</default></enumvalue><enumvalue name="BOOST_FPE_ALL"><default>= BOOST_FPE_OFF</default></enumvalue><enumvalue name="BOOST_FPE_INV"><default>= BOOST_FPE_ALL+1</default></enumvalue></enum>
<function name="enable"><type>unsigned</type><parameter name="mask"><paramtype>unsigned</paramtype></parameter></function>
<function name="disable"><type>unsigned</type><parameter name="mask"><paramtype>unsigned</paramtype></parameter></function>
</namespace>
</namespace>
<macro name="BOOST_TEST_DISABLE_ALT_STACK"><purpose>Disables the support of the alternative stack during the compilation of the Boost.test framework. This is especially useful in case it is not possible to detect the lack of alternative stack support for your compiler (for instance, ESXi). </purpose></macro>
<macro name="BOOST_TEST_SYS_ASSERT" kind="functionlike"><macro-parameter name="cond"/></macro>
</header>
<header name="boost/test/framework.hpp">
<para>Defines Unit Test Framework mono-state interfaces. The framework interfaces are based on Monostate design pattern. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="framework">
<struct name="context_generator"><method-group name="public member functions">
<method name="is_empty" cv="const"><type>bool</type><purpose>Is there any context? </purpose></method>
<method name="next" cv="const"><type>const_string</type><purpose>Give me next frame; empty - last frame. </purpose></method>
</method-group>
<constructor/>
</struct><struct name="internal_error"><inherit access="public">runtime_error</inherit><purpose>This exception type is used to report internal Boost.Test framework errors. </purpose><method-group name="public member functions">
</method-group>
<constructor><parameter name="m"><paramtype>const_string</paramtype></parameter></constructor>
</struct><struct name="nothing_to_test"><data-member name="m_result_code"><type>int</type></data-member>
<method-group name="public member functions">
</method-group>
<constructor specifiers="explicit"><parameter name="rc"><paramtype>int</paramtype></parameter></constructor>
</struct><struct name="setup_error"><inherit access="public">runtime_error</inherit><purpose>This exception type is used to report test module setup errors. </purpose><method-group name="public member functions">
</method-group>
<constructor><parameter name="m"><paramtype>const_string</paramtype></parameter></constructor>
</struct><namespace name="impl">
<struct name="master_test_suite_name_setter"><method-group name="public member functions">
</method-group>
<constructor><parameter name="name"><paramtype>const_string</paramtype></parameter></constructor>
</struct><function name="setup_for_execution"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter></function>
<function name="setup_loggers"><type>void</type></function>
</namespace>
<function name="init"><type>void</type><parameter name="init_func"><paramtype>init_unit_test_func</paramtype><description><para>test module initialization routine </para></description></parameter><parameter name="argc"><paramtype>int</paramtype><description><para>command line arguments collection </para></description></parameter><parameter name="argv"><paramtype>char *</paramtype><description><para>command line arguments collection </para></description></parameter><purpose>This function performs initialization of the framework mono-state. </purpose><description><para>It needs to be called every time before the test is started. 
</para></description></function>
<function name="finalize_setup_phase"><type>void</type><parameter name="tu"><paramtype>test_unit_id</paramtype><default>INV_TEST_UNIT_ID</default><description><para>Optional id of the test unit representing root of test tree. If absent, master test suite is used </para></description></parameter><purpose>This function applies all the decorators and figures out default run status. This argument facilitates an ability of the test cases to prepare some other test units (primarily used internally for self testing). </purpose><description><para>
</para></description></function>
<function name="test_in_progress"><type>bool</type><purpose>This function returns true when testing is in progress (setup is finished). </purpose></function>
<function name="shutdown"><type>void</type><purpose>This function shuts down the framework and clears up its mono-state. </purpose><description><para>It needs to be at the very end of test module execution </para></description></function>
<function name="current_auto_test_suite"><type><classname>test_suite</classname> &amp;</type><parameter name="ts"><paramtype><classname>test_suite</classname> *</paramtype><default>0</default><description><para>test suite to push back to the queue </para></description></parameter><parameter name="push_or_pop"><paramtype>bool</paramtype><default>true</default><description><para>should we push ts to the queue or pop leaf test suite instead </para></description></parameter><purpose>Provides both read and write access to current "leaf" auto test suite during the test unit registration phase. </purpose><description><para>During auto-registration phase the framework maintain a FIFO queue of test units being registered. New test units become children of the current "leaf" test suite and if this is test suite it is pushed back into queue and becomes a new leaf. When test suite registration is completed, a test suite is popped from the back of the queue. Only automatically registered test suites should be added to this queue. Master test suite is always a zero element in this queue, so if no other test suites are registered all test cases are added to master test suite. This function facilitates all three possible actions:<itemizedlist>
<listitem><para>if no argument are provided it returns the current queue leaf test suite</para>
</listitem><listitem><para>if test suite is provided and no second argument are set, test suite is added to the queue</para>
</listitem><listitem><para>if no test suite are provided and last argument is false, the semantic of this function is similar to queue pop: last element is popped from the queue 

</para>
</listitem></itemizedlist>
</para></description></function>
<function name="register_test_unit"><type>void</type><parameter name="tc"><paramtype><classname>test_case</classname> *</paramtype><description><para>test case to register </para></description></parameter><purpose>This function add new test case into the global collection of test units the framework aware of. </purpose><description><para>This function also assignes unique test unit id for every test case. Later on one can use this id to locate the test case if necessary. This is the way for the framework to maintain weak references between test units. 
</para></description></function>
<function name="register_test_unit"><type>void</type><parameter name="ts"><paramtype><classname>test_suite</classname> *</paramtype><description><para>test suite to register </para></description></parameter><purpose>This function add new test suite into the global collection of test units the framework aware of. </purpose><description><para>This function also assignes unique test unit id for every test suite. Later on one can use this id to locate the test case if necessary. This is the way for the framework to maintain weak references between test units. 
</para></description></function>
<function name="deregister_test_unit"><type>void</type><parameter name="tu"><paramtype><classname>test_unit</classname> *</paramtype><description><para>test unit to deregister </para></description></parameter><purpose>This function removes the test unit from the collection of known test units and destroys the test unit object. </purpose><description><para>This function also assigns unique test unit id for every test case. Later on one can use this id to located the test case if necessary. This is the way for the framework to maintain weak references between test units. 
</para></description></function>
<function name="clear"><type>void</type><purpose>After this call the framework can be reinitialized to perform a second test run during the same program lifetime. </purpose></function>
<function name="register_observer"><type>void</type><parameter name="to"><paramtype><classname>test_observer</classname> &amp;</paramtype><description><para>test observer object to add </para></description></parameter><purpose>Observer lifetime should exceed the the testing execution timeframe. </purpose><description><para>
</para></description></function>
<function name="deregister_observer"><type>void</type><parameter name="to"><paramtype><classname>test_observer</classname> &amp;</paramtype><description><para>test observer object to exclude </para></description></parameter><purpose>Excludes the observer object form the framework's list of test observers. </purpose><description><para>
</para></description></function>
<function name="register_global_fixture"><type>void</type><parameter name="tuf"><paramtype>global_fixture &amp;</paramtype><description><para>fixture to add </para></description></parameter><purpose>Adds a new global fixture to be setup before any other tests starts and tore down after any other tests finished. Test unit fixture lifetime should exceed the testing execution timeframe. </purpose><description><para>
</para></description></function>
<function name="deregister_global_fixture"><type>void</type><parameter name="tuf"><paramtype>global_fixture &amp;</paramtype><description><para>fixture to remove </para></description></parameter><purpose>Removes a test global fixture from the framework. </purpose><description><para>Test unit fixture lifetime should exceed the testing execution timeframe 
</para></description></function>
<function name="add_context"><type>int</type><parameter name="context_descr"><paramtype>lazy_ostream const &amp;</paramtype><description><para>context frame message </para></description></parameter><parameter name="sticky"><paramtype>bool</paramtype><description><para>is this sticky frame or not </para></description></parameter><purpose>Records context frame message. </purpose><description><para>Some context frames are sticky - they can only explicitly cleared by specifying context id. Other (non sticky) context frames cleared after every assertion. 

</para></description><returns><para>id of the newly created frame </para>
</returns></function>
<function name="clear_context"><type>void</type><parameter name="context_id"><paramtype>int</paramtype><default>-1</default></parameter><purpose>Erases context frame (when test exits context scope) </purpose><description><para>If context_id is passed clears that specific context frame identified by this id, otherwise clears all non sticky contexts. </para></description></function>
<function name="get_context"><type><classname>context_generator</classname></type><purpose>Produces an instance of small "delegate" object, which facilitates access to collected context. </purpose></function>
<function name="master_test_suite"><type><classname>master_test_suite_t</classname> &amp;</type><purpose>There is only only master test suite per test module. </purpose><description><para>
</para></description><returns><para>a reference the master test suite instance </para>
</returns></function>
<function name="current_test_unit"><type><classname>test_unit</classname> const  &amp;</type><purpose>This function provides an access to the test unit currently being executed. </purpose><description><para>The difference with current_test_case is about the time between a test-suite is being set up or torn down (fixtures) and when the test-cases of that suite start. This function is only valid during test execution phase. <para><emphasis role="bold">See Also:</emphasis><para>current_test_case_id, current_test_case </para>
</para>
</para></description></function>
<function name="current_test_case"><type><classname>test_case</classname> const  &amp;</type><purpose>This function provides an access to the test case currently being executed. </purpose><description><para>This function is only valid during test execution phase. <para><emphasis role="bold">See Also:</emphasis><para>current_test_case_id </para>
</para>
</para></description></function>
<function name="current_test_case_id"><type>test_unit_id</type><purpose>This function provides an access to an id of the test case currently being executed. </purpose><description><para>This function safer than current_test_case, cause if wont throw if no test case is being executed. <para><emphasis role="bold">See Also:</emphasis><para>current_test_case </para>
</para>
</para></description></function>
<function name="get"><type><classname>test_unit</classname> &amp;</type><parameter name="tu_id"><paramtype>test_unit_id</paramtype><description><para>id of a test unit to locate </para></description></parameter><parameter name="tu_type"><paramtype>test_unit_type</paramtype><description><para>type of a test unit to locate </para></description></parameter><purpose>This function provides access to a test unit by id and type combination. It will throw if no test unit located. </purpose><description><para>

</para></description><returns><para>located test unit </para>
</returns></function>
<function name="get"><type>UnitType &amp;</type><template>
          <template-type-parameter name="UnitType"><purpose><para>compile time type of test unit to get (<classname alt="boost::unit_test::test_suite">test_suite</classname> or <classname alt="boost::unit_test::test_case">test_case</classname>) </para></purpose></template-type-parameter>
        </template><parameter name="id"><paramtype>test_unit_id</paramtype><description><para>id of test unit to get </para></description></parameter><purpose>This function template provides access to a typed test unit by id. </purpose><description><para>It will throw if you specify incorrect test unit type 

</para></description></function>
<function name="run"><type>void</type><parameter name="tu"><paramtype>test_unit_id</paramtype><default>INV_TEST_UNIT_ID</default><description><para>Optional id of the test unit or test unit itself from which the test is started. If absent, master test suite is used </para></description></parameter><parameter name="continue_test"><paramtype>bool</paramtype><default>true</default><description><para>true == continue test if it was already started, false == restart the test from scratch regardless </para></description></parameter><purpose>Initiates test execution. </purpose><description><para>This function is used to start the test execution from a specific "root" test unit. If no root provided, test is started from master test suite. This second argument facilitates an ability of the test cases to start some other test units (primarily used internally for self testing). 
</para></description></function>
<function name="run"><type>void</type><parameter name="tu"><paramtype><classname>test_unit</classname> const *</paramtype></parameter><parameter name="continue_test"><paramtype>bool</paramtype><default>true</default></parameter><purpose>Initiates test execution. Same as other overload. </purpose></function>
<function name="assertion_result"><type>void</type><parameter name="ar"><paramtype>unit_test::assertion_result</paramtype></parameter></function>
<function name="exception_caught"><type>void</type><parameter name=""><paramtype><classname>execution_exception</classname> const &amp;</paramtype></parameter><purpose>Reports uncaught exception to all test observers. </purpose></function>
<function name="test_unit_aborted"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Reports aborted test unit to all test observers. </purpose></function>
<function name="test_aborted"><type>void</type><purpose>Reports aborted test module to all test observers. </purpose></function>
</namespace>
<typedef name="init_unit_test_func"><purpose>Test module initialization routine signature. </purpose><description><para>Different depending on whether BOOST_TEST_ALTERNATIVE_INIT_API is defined or not </para></description><type><classname>test_suite</classname> *(*</type></typedef>






</namespace>
</namespace>
<macro name="BOOST_TEST_SETUP_ASSERT" kind="functionlike"><macro-parameter name="cond"/><macro-parameter name="msg"/></macro>
</header>
<header name="boost/test/output/compiler_log_formatter.hpp">
<para>Contains the formatter for the Human Readable Format (HRF) </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="output">
<class name="compiler_log_formatter"><inherit access="public">boost::unit_test::unit_test_log_formatter</inherit><purpose>Log formatter for the Human Readable Format (HRF) log format. </purpose><method-group name="public member functions">
<method name="log_start" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages to </para></description></parameter><parameter name="test_cases_amount"><paramtype>counter_t</paramtype><description><para>total test case amount to be run </para></description></parameter><purpose>Invoked at the beginning of test module execution. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_finish </para>
</para>
</para></description></method>
<method name="log_finish" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><purpose>Invoked at the end of test module execution. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_start </para>
</para>
</para></description></method>
<method name="log_build_info" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="log_build_info"><paramtype>bool</paramtype><description><para>indicates if build info should be logged or not </para></description></parameter><purpose>Invoked when Unit Test Framework build information is requested. </purpose><description><para>
</para></description></method>
<method name="test_unit_start" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="tu"><paramtype><classname>test_unit</classname> const &amp;</paramtype><description><para>test unit being started </para></description></parameter><purpose>Invoked when test unit starts (either test suite or test case) </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>test_unit_finish </para>
</para>
</para></description></method>
<method name="test_unit_finish" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="tu"><paramtype><classname>test_unit</classname> const &amp;</paramtype><description><para>test unit being finished </para></description></parameter><parameter name="elapsed"><paramtype>unsigned long</paramtype><description><para>time in microseconds spend executing this test unit </para></description></parameter><purpose>Invoked when test unit finishes. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>test_unit_start </para>
</para>
</para></description></method>
<method name="test_unit_skipped" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="tu"><paramtype><classname>test_unit</classname> const &amp;</paramtype><description><para>skipped test unit </para></description></parameter><parameter name=""><paramtype>const_string</paramtype></parameter><purpose>Invoked if test unit skipped for any reason. </purpose><description><para>
</para></description></method>
<method name="log_exception_start" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="lcd"><paramtype><classname>log_checkpoint_data</classname> const &amp;</paramtype><description><para>information about the last checkpoint before the exception was triggered </para></description></parameter><parameter name="ex"><paramtype><classname>execution_exception</classname> const &amp;</paramtype><description><para>information about the caught exception </para></description></parameter><purpose>Invoked when Unit Test Framework detects uncaught exception. </purpose><description><para>The framwork calls this function when an uncaught exception it detected. This call is followed by context information:<itemizedlist>
<listitem><para>one call to <computeroutput>entry_context_start</computeroutput>,</para>
</listitem><listitem><para>as many calls to <computeroutput>log_entry_context</computeroutput> as there are context entries</para>
</listitem><listitem><para>one call to <computeroutput>entry_context_finish</computeroutput> </para>
</listitem></itemizedlist>
</para><para>The logging of the exception information is finilized by a call to <computeroutput>log_exception_finish</computeroutput>.</para><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_exception_finish </para>
</para>
</para></description></method>
<method name="log_exception_finish" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><purpose>Invoked when Unit Test Framework detects uncaught exception. </purpose><description><para>Call to this function finishes uncaught exception report. 
<para><emphasis role="bold">See Also:</emphasis><para>log_exception_start </para>
</para>
</para></description></method>
<method name="log_entry_start" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="led"><paramtype><classname>log_entry_data</classname> const &amp;</paramtype><description><para>log entry attributes </para></description></parameter><parameter name="let"><paramtype>log_entry_types</paramtype><description><para>log entry type log_entry_finish </para></description></parameter><purpose>Invoked by Unit Test Framework to start new log entry. </purpose><description><para>Call to this function starts new log entry. It is followed by series of log_entry_value calls and finally call to log_entry_finish. A log entry may consist of one or more values being reported. Some of these values will be plain strings, while others can be complicated expressions in a form of "lazy" expression template lazy_ostream. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_value, log_entry_finish</para>
</para>
<note><para>call to this function may happen before any call to test_unit_start or all calls to test_unit_finish as the framework might log errors raised during global initialization/shutdown. </para>
</note>
</para></description></method>
<method name="log_entry_value" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into. </para></description></parameter><parameter name="value"><paramtype>const_string</paramtype><description><para>log entry string value </para></description></parameter><purpose>Invoked by Unit Test Framework to report a log entry content. </purpose><description><para>This is one of two overloaded methods to report log entry content. This one is used to report plain string value. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, log_entry_finish </para>
</para>
</para></description></method>
<method name="log_entry_value" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="value"><paramtype>lazy_ostream const &amp;</paramtype><description><para>log entry "lazy" value </para></description></parameter><purpose>Invoked by Unit Test Framework to report a log entry content. </purpose><description><para>This is one of two overloaded methods to report log entry content. This one is used to report some complicated expression passed as an expression template lazy_ostream. In most cases default implementation provided by the framework should work as is (it just converts the lazy expression into a string. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, log_entry_finish </para>
</para>
</para></description></method>
<method name="log_entry_finish" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><purpose>Invoked by Unit Test Framework to finish a log entry report. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, log_entry_start </para>
</para>
</para></description></method>
<method name="entry_context_start" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="l"><paramtype>log_level</paramtype><description><para>entry log_level, to be used to fine tune the message </para></description></parameter><purpose>Invoked by Unit Test Framework to start log entry context report. </purpose><description><para>Unit Test Framework logs for failed assertions and uncaught exceptions context if one was defined by a test module. Context consists of multiple "scopes" identified by description messages assigned by the test module using BOOST_TEST_INFO/BOOST_TEST_CONTEXT statements. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_context, entry_context_finish </para>
</para>
</para></description></method>
<method name="log_entry_context" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="l"><paramtype>log_level</paramtype><description><para>entry log_level, to be used to fine tune the message </para></description></parameter><parameter name="value"><paramtype>const_string</paramtype><description><para>context "scope" description </para></description></parameter><purpose>Invoked by Unit Test Framework to report log entry context "scope" description. </purpose><description><para>Each "scope" description is reported by separate call to log_entry_context. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, entry_context_finish </para>
</para>
</para></description></method>
<method name="entry_context_finish" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="l"><paramtype>log_level</paramtype><description><para>entry log_level, to be used to fine tune the message </para></description></parameter><purpose>Invoked by Unit Test Framework to finish log entry context report. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, entry_context_context </para>
</para>
</para></description></method>
<method name="test_unit_skipped" specifiers="virtual"><type>void</type><parameter name=""><paramtype>std::ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Deprecated version of this interface. </purpose><description><para><xrefsect id="a00851_1_deprecated000001"><xreftitle>Deprecated</xreftitle><xrefdescription/></xrefsect></para></description></method>
<method name="test_unit_aborted" specifiers="virtual"><type>void</type><parameter name=""><paramtype>std::ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Invoked when a test unit is aborted. </purpose></method>
<method name="test_unit_timed_out" specifiers="virtual"><type>void</type><parameter name=""><paramtype>std::ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Invoked when a test unit times-out. </purpose></method>
<method name="set_log_level" specifiers="virtual"><type>void</type><parameter name="new_log_level"><paramtype>log_level</paramtype></parameter><purpose>Sets the log level of the logger/formatter. </purpose><description><para>Some loggers need to manage the log level by their own. This member function let the implementation decide of that. <formalpara><title>Since Boost 1.62 </title><para/>
</formalpara>
</para></description></method>
<method name="get_log_level" cv="const" specifiers="virtual"><type>log_level</type><purpose>Returns the log level of the logger/formatter. </purpose><description><para><formalpara><title>Since Boost 1.62 </title><para/>
</formalpara>
</para></description></method>
<method name="get_default_stream_description" cv="const" specifiers="virtual"><type>std::string</type><purpose>Returns a default stream for this logger. </purpose><description><para>The returned string describes the stream as if it was passed from the command line <computeroutput>"--log_sink"</computeroutput> parameter. With that regards, <emphasis role="bold">stdout</emphasis> and <emphasis role="bold">stderr</emphasis> have special meaning indicating the standard output or error stream respectively.</para><para><formalpara><title>Since Boost 1.62 </title><para/>
</formalpara>
</para></description></method>
</method-group>
<constructor/>
<method-group name="protected member functions">
<method name="print_prefix" specifiers="virtual"><type>void</type><parameter name=""><paramtype>std::ostream &amp;</paramtype></parameter><parameter name="file"><paramtype>const_string</paramtype></parameter><parameter name="line"><paramtype>std::size_t</paramtype></parameter></method>
</method-group>
</class></namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/output/plain_report_formatter.hpp">
<namespace name="boost">
<namespace name="unit_test">
<namespace name="output">
<class name="plain_report_formatter"><inherit access="public">boost::unit_test::results_reporter::format</inherit><method-group name="public member functions">
<method name="results_report_start" specifiers="virtual"><type>void</type><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
<method name="results_report_finish" specifiers="virtual"><type>void</type><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
<method name="test_unit_report_start" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
<method name="test_unit_report_finish" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
<method name="do_confirmation_report" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
</method-group>
<constructor/>
</class></namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/output/xml_log_formatter.hpp">
<namespace name="boost">
<namespace name="unit_test">
<namespace name="output">
<class name="xml_log_formatter"><inherit access="public">boost::unit_test::unit_test_log_formatter</inherit><method-group name="public member functions">
<method name="log_start" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages to </para></description></parameter><parameter name="test_cases_amount"><paramtype>counter_t</paramtype><description><para>total test case amount to be run </para></description></parameter><purpose>Invoked at the beginning of test module execution. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_finish </para>
</para>
</para></description></method>
<method name="log_finish" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><purpose>Invoked at the end of test module execution. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_start </para>
</para>
</para></description></method>
<method name="log_build_info" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="log_build_info"><paramtype>bool</paramtype><description><para>indicates if build info should be logged or not </para></description></parameter><purpose>Invoked when Unit Test Framework build information is requested. </purpose><description><para>
</para></description></method>
<method name="test_unit_start" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="tu"><paramtype><classname>test_unit</classname> const &amp;</paramtype><description><para>test unit being started </para></description></parameter><purpose>Invoked when test unit starts (either test suite or test case) </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>test_unit_finish </para>
</para>
</para></description></method>
<method name="test_unit_finish" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="tu"><paramtype><classname>test_unit</classname> const &amp;</paramtype><description><para>test unit being finished </para></description></parameter><parameter name="elapsed"><paramtype>unsigned long</paramtype><description><para>time in microseconds spend executing this test unit </para></description></parameter><purpose>Invoked when test unit finishes. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>test_unit_start </para>
</para>
</para></description></method>
<method name="test_unit_skipped" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="tu"><paramtype><classname>test_unit</classname> const &amp;</paramtype><description><para>skipped test unit </para></description></parameter><parameter name=""><paramtype>const_string</paramtype></parameter><purpose>Invoked if test unit skipped for any reason. </purpose><description><para>
</para></description></method>
<method name="log_exception_start" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="lcd"><paramtype><classname>log_checkpoint_data</classname> const &amp;</paramtype><description><para>information about the last checkpoint before the exception was triggered </para></description></parameter><parameter name="ex"><paramtype><classname>execution_exception</classname> const &amp;</paramtype><description><para>information about the caught exception </para></description></parameter><purpose>Invoked when Unit Test Framework detects uncaught exception. </purpose><description><para>The framwork calls this function when an uncaught exception it detected. This call is followed by context information:<itemizedlist>
<listitem><para>one call to <computeroutput>entry_context_start</computeroutput>,</para>
</listitem><listitem><para>as many calls to <computeroutput>log_entry_context</computeroutput> as there are context entries</para>
</listitem><listitem><para>one call to <computeroutput>entry_context_finish</computeroutput> </para>
</listitem></itemizedlist>
</para><para>The logging of the exception information is finilized by a call to <computeroutput>log_exception_finish</computeroutput>.</para><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_exception_finish </para>
</para>
</para></description></method>
<method name="log_exception_finish" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><purpose>Invoked when Unit Test Framework detects uncaught exception. </purpose><description><para>Call to this function finishes uncaught exception report. 
<para><emphasis role="bold">See Also:</emphasis><para>log_exception_start </para>
</para>
</para></description></method>
<method name="log_entry_start" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="led"><paramtype><classname>log_entry_data</classname> const &amp;</paramtype><description><para>log entry attributes </para></description></parameter><parameter name="let"><paramtype>log_entry_types</paramtype><description><para>log entry type log_entry_finish </para></description></parameter><purpose>Invoked by Unit Test Framework to start new log entry. </purpose><description><para>Call to this function starts new log entry. It is followed by series of log_entry_value calls and finally call to log_entry_finish. A log entry may consist of one or more values being reported. Some of these values will be plain strings, while others can be complicated expressions in a form of "lazy" expression template lazy_ostream. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_value, log_entry_finish</para>
</para>
<note><para>call to this function may happen before any call to test_unit_start or all calls to test_unit_finish as the framework might log errors raised during global initialization/shutdown. </para>
</note>
</para></description></method>
<method name="log_entry_value" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into. </para></description></parameter><parameter name="value"><paramtype>const_string</paramtype><description><para>log entry string value </para></description></parameter><purpose>Invoked by Unit Test Framework to report a log entry content. </purpose><description><para>This is one of two overloaded methods to report log entry content. This one is used to report plain string value. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, log_entry_finish </para>
</para>
</para></description></method>
<method name="log_entry_finish" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><purpose>Invoked by Unit Test Framework to finish a log entry report. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, log_entry_start </para>
</para>
</para></description></method>
<method name="entry_context_start" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="l"><paramtype>log_level</paramtype><description><para>entry log_level, to be used to fine tune the message </para></description></parameter><purpose>Invoked by Unit Test Framework to start log entry context report. </purpose><description><para>Unit Test Framework logs for failed assertions and uncaught exceptions context if one was defined by a test module. Context consists of multiple "scopes" identified by description messages assigned by the test module using BOOST_TEST_INFO/BOOST_TEST_CONTEXT statements. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_context, entry_context_finish </para>
</para>
</para></description></method>
<method name="log_entry_context" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="l"><paramtype>log_level</paramtype><description><para>entry log_level, to be used to fine tune the message </para></description></parameter><parameter name="value"><paramtype>const_string</paramtype><description><para>context "scope" description </para></description></parameter><purpose>Invoked by Unit Test Framework to report log entry context "scope" description. </purpose><description><para>Each "scope" description is reported by separate call to log_entry_context. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, entry_context_finish </para>
</para>
</para></description></method>
<method name="entry_context_finish" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="l"><paramtype>log_level</paramtype><description><para>entry log_level, to be used to fine tune the message </para></description></parameter><purpose>Invoked by Unit Test Framework to finish log entry context report. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, entry_context_context </para>
</para>
</para></description></method>
<method name="log_entry_value"><type>void</type><purpose>Invoked by Unit Test Framework to report a log entry content. </purpose><description><para>This is one of two overloaded methods to report log entry content. This one is used to report plain string value. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, log_entry_finish </para>
</para>
</para></description></method>
<method name="log_entry_value"><type>void</type><purpose>Invoked by Unit Test Framework to report a log entry content. </purpose><description><para>This is one of two overloaded methods to report log entry content. This one is used to report some complicated expression passed as an expression template lazy_ostream. In most cases default implementation provided by the framework should work as is (it just converts the lazy expression into a string. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, log_entry_finish </para>
</para>
</para></description></method>
<method name="test_unit_skipped" specifiers="virtual"><type>void</type><parameter name=""><paramtype>std::ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Deprecated version of this interface. </purpose><description><para><xrefsect id="a00851_1_deprecated000001"><xreftitle>Deprecated</xreftitle><xrefdescription/></xrefsect></para></description></method>
<method name="test_unit_aborted" specifiers="virtual"><type>void</type><parameter name=""><paramtype>std::ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Invoked when a test unit is aborted. </purpose></method>
<method name="test_unit_timed_out" specifiers="virtual"><type>void</type><parameter name=""><paramtype>std::ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Invoked when a test unit times-out. </purpose></method>
<method name="log_entry_value" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="value"><paramtype>lazy_ostream const &amp;</paramtype><description><para>log entry "lazy" value </para></description></parameter><purpose>Invoked by Unit Test Framework to report a log entry content. </purpose><description><para>This is one of two overloaded methods to report log entry content. This one is used to report some complicated expression passed as an expression template lazy_ostream. In most cases default implementation provided by the framework should work as is (it just converts the lazy expression into a string. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, log_entry_finish </para>
</para>
</para></description></method>
<method name="set_log_level" specifiers="virtual"><type>void</type><parameter name="new_log_level"><paramtype>log_level</paramtype></parameter><purpose>Sets the log level of the logger/formatter. </purpose><description><para>Some loggers need to manage the log level by their own. This member function let the implementation decide of that. <formalpara><title>Since Boost 1.62 </title><para/>
</formalpara>
</para></description></method>
<method name="get_log_level" cv="const" specifiers="virtual"><type>log_level</type><purpose>Returns the log level of the logger/formatter. </purpose><description><para><formalpara><title>Since Boost 1.62 </title><para/>
</formalpara>
</para></description></method>
<method name="get_default_stream_description" cv="const" specifiers="virtual"><type>std::string</type><purpose>Returns a default stream for this logger. </purpose><description><para>The returned string describes the stream as if it was passed from the command line <computeroutput>"--log_sink"</computeroutput> parameter. With that regards, <emphasis role="bold">stdout</emphasis> and <emphasis role="bold">stderr</emphasis> have special meaning indicating the standard output or error stream respectively.</para><para><formalpara><title>Since Boost 1.62 </title><para/>
</formalpara>
</para></description></method>
</method-group>
</class></namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/output/xml_report_formatter.hpp">
<namespace name="boost">
<namespace name="unit_test">
<namespace name="output">
<class name="xml_report_formatter"><inherit access="public">boost::unit_test::results_reporter::format</inherit><method-group name="public member functions">
<method name="results_report_start" specifiers="virtual"><type>void</type><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
<method name="results_report_finish" specifiers="virtual"><type>void</type><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
<method name="test_unit_report_start" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
<method name="test_unit_report_finish" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
<method name="do_confirmation_report" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
</method-group>
</class></namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/parameterized_test.hpp">
<para>generators and helper macros for parameterized tests </para><namespace name="boost">
<namespace name="unit_test">



<function name="make_test_case"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="ParamType"/>
          <template-type-parameter name="ParamIter"/>
        </template><parameter name="test_func"><paramtype>boost::function&lt; void(ParamType)&gt; const &amp;</paramtype></parameter><parameter name="tc_name"><paramtype>const_string</paramtype></parameter><parameter name="tc_file"><paramtype>const_string</paramtype></parameter><parameter name="tc_line"><paramtype>std::size_t</paramtype></parameter><parameter name="par_begin"><paramtype>ParamIter</paramtype></parameter><parameter name="par_end"><paramtype>ParamIter</paramtype></parameter></function>
<function name="make_test_case"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="ParamType"/>
          <template-type-parameter name="ParamIter"/>
        </template><parameter name="test_func"><paramtype>void(*)(ParamType)</paramtype></parameter><parameter name="tc_name"><paramtype>const_string</paramtype></parameter><parameter name="tc_file"><paramtype>const_string</paramtype></parameter><parameter name="tc_line"><paramtype>std::size_t</paramtype></parameter><parameter name="par_begin"><paramtype>ParamIter</paramtype></parameter><parameter name="par_end"><paramtype>ParamIter</paramtype></parameter></function>
<function name="make_test_case"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="UserTestCase"/>
          <template-type-parameter name="ParamType"/>
          <template-type-parameter name="ParamIter"/>
        </template><parameter name="test_method"><paramtype>void(UserTestCase::*)(ParamType)</paramtype></parameter><parameter name="tc_name"><paramtype>const_string</paramtype></parameter><parameter name="tc_file"><paramtype>const_string</paramtype></parameter><parameter name="tc_line"><paramtype>std::size_t</paramtype></parameter><parameter name="user_test_case"><paramtype>boost::shared_ptr&lt; UserTestCase &gt; const &amp;</paramtype></parameter><parameter name="par_begin"><paramtype>ParamIter</paramtype></parameter><parameter name="par_end"><paramtype>ParamIter</paramtype></parameter></function>
</namespace>
</namespace>
<macro name="BOOST_PARAM_TEST_CASE" kind="functionlike"><macro-parameter name="function"/><macro-parameter name="begin"/><macro-parameter name="end"/></macro>
<macro name="BOOST_PARAM_CLASS_TEST_CASE" kind="functionlike"><macro-parameter name="function"/><macro-parameter name="tc_instance"/><macro-parameter name="begin"/><macro-parameter name="end"/></macro>
</header>
<header name="boost/test/progress_monitor.hpp">
<para>defines simple text based progress monitor </para><namespace name="boost">
<namespace name="unit_test">
<class name="progress_monitor_t"><inherit access="public">boost::unit_test::test_observer</inherit><purpose>This class implements test observer interface and updates test progress as test units finish or get aborted. </purpose><method-group name="public member functions">
<method name="test_finish" specifiers="virtual"><type>void</type><purpose>Called after the framework ends executing the test cases. </purpose><description><para><note><para>The call is made with a reversed priority order. </para>
</note>
</para></description></method>
<method name="test_unit_start" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Called before the framework starts executing a test unit. </purpose><description><para>
</para></description></method>
<method name="test_unit_skipped" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>backward compatibility </purpose></method>
<method name="test_unit_timed_out" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Called when the test timed out. </purpose><description><para>This function is called to signal that a test unit (case or suite) timed out. A valid test unit is available through boost::unit_test::framework::current_test_unit </para></description></method>
<method name="test_unit_aborted" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Called when a test unit indicates a fatal error. </purpose><description><para>A fatal error happens when<itemizedlist>
<listitem><para>a strong assertion (with <computeroutput>REQUIRE</computeroutput>) fails, which indicates that the test case cannot continue</para>
</listitem><listitem><para>an unexpected exception is caught by the Boost.Test framework </para>
</listitem></itemizedlist>
</para></description></method>
<method name="assertion_result" specifiers="virtual"><type>void</type><parameter name=""><paramtype>unit_test::assertion_result</paramtype></parameter></method>
<method name="exception_caught" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>execution_exception</classname> const &amp;</paramtype></parameter><purpose>Called when an exception is intercepted. </purpose><description><para>In case an exception is intercepted, this call happens before the call to test_unit_aborted in order to log additional data about the exception. </para></description></method>
</method-group>
</class>





</namespace>
</namespace>
</header>
<header name="boost/test/results_collector.hpp">
<para>Defines testing result collector components. </para><para>Defines classes for keeping track (test_results) and collecting (results_collector_t) the states of the test units. </para><namespace name="boost">
<namespace name="unit_test">
<class name="results_collector_t"><inherit access="public">boost::unit_test::test_observer</inherit><purpose>Collects and combines the test results. </purpose><description><para>This class collects and combines the results of the test unit during the execution of the test tree. The results_collector_t::results() function combines the test results on a subtree of the test tree.</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::unit_test::test_observer">boost::unit_test::test_observer</classname> </para>
</para>
</para></description><method-group name="public member functions">
<method name="test_start" specifiers="virtual"><type>void</type><parameter name=""><paramtype>counter_t</paramtype></parameter><parameter name=""><paramtype>test_unit_id</paramtype></parameter><purpose>Called before the framework starts executing the test cases. </purpose><description><para>
</para></description></method>
<method name="test_unit_start" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Called before the framework starts executing a test unit. </purpose><description><para>
</para></description></method>
<method name="test_unit_finish" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned long</paramtype></parameter><purpose>Called at each end of a test unit. </purpose><description><para>
</para></description></method>
<method name="test_unit_skipped" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name=""><paramtype>const_string</paramtype></parameter></method>
<method name="test_unit_aborted" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Called when a test unit indicates a fatal error. </purpose><description><para>A fatal error happens when<itemizedlist>
<listitem><para>a strong assertion (with <computeroutput>REQUIRE</computeroutput>) fails, which indicates that the test case cannot continue</para>
</listitem><listitem><para>an unexpected exception is caught by the Boost.Test framework </para>
</listitem></itemizedlist>
</para></description></method>
<method name="test_unit_timed_out" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Called when the test timed out. </purpose><description><para>This function is called to signal that a test unit (case or suite) timed out. A valid test unit is available through boost::unit_test::framework::current_test_unit </para></description></method>
<method name="assertion_result" specifiers="virtual"><type>void</type><parameter name=""><paramtype>unit_test::assertion_result</paramtype></parameter></method>
<method name="exception_caught" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>execution_exception</classname> const &amp;</paramtype></parameter><purpose>Called when an exception is intercepted. </purpose><description><para>In case an exception is intercepted, this call happens before the call to test_unit_aborted in order to log additional data about the exception. </para></description></method>
<method name="priority" specifiers="virtual"><type>int</type><purpose>The priority indicates the order at which this observer is initialized and tore down in the UTF framework. The order is lowest to highest priority. </purpose></method>
<method name="results" cv="const"><type><classname>test_results</classname> const  &amp;</type><parameter name="tu_id"><paramtype>test_unit_id</paramtype><description><para>id of a test unit </para></description></parameter><purpose>Results access per test unit. </purpose><description><para>
</para></description></method>
<method name="test_finish" specifiers="virtual"><type>void</type><purpose>Called after the framework ends executing the test cases. </purpose><description><para><note><para>The call is made with a reversed priority order. </para>
</note>
</para></description></method>
<method name="test_aborted" specifiers="virtual"><type>void</type><purpose>Called when a critical error is detected. </purpose><description><para>The critical errors are mainly the signals sent by the system and caught by the Boost.Test framework. Since the running binary may be in incoherent/instable state, the test execution is aborted and all remaining tests are discarded.</para><para><note><para>may be called before test_observer::test_unit_finish() </para>
</note>
</para></description></method>
<method name="test_unit_skipped" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>backward compatibility </purpose></method>
</method-group>
</class><class name="test_results"><purpose>Collection of attributes constituting test unit results. </purpose><description><para>This class is a collection of attributes describing a test result.</para><para>The attributes presented as public properties on an instance of the class. In addition summary conclusion methods are presented to generate simple answer to pass/fail question </para></description><data-member name="p_test_suites"><type>counter_prop</type><purpose>Number of test suites. </purpose></data-member>
<data-member name="p_assertions_passed"><type>counter_prop</type><purpose>Number of successful assertions. </purpose></data-member>
<data-member name="p_assertions_failed"><type>counter_prop</type><purpose>Number of failing assertions. </purpose></data-member>
<data-member name="p_warnings_failed"><type>counter_prop</type><purpose>Number of warnings. </purpose></data-member>
<data-member name="p_expected_failures"><type>counter_prop</type></data-member>
<data-member name="p_test_cases_passed"><type>counter_prop</type><purpose>Number of successfull test cases. </purpose></data-member>
<data-member name="p_test_cases_warned"><type>counter_prop</type><purpose>Number of warnings in test cases. </purpose></data-member>
<data-member name="p_test_cases_failed"><type>counter_prop</type><purpose>Number of failing test cases. </purpose></data-member>
<data-member name="p_test_cases_skipped"><type>counter_prop</type><purpose>Number of skipped test cases. </purpose></data-member>
<data-member name="p_test_cases_aborted"><type>counter_prop</type><purpose>Number of aborted test cases. </purpose></data-member>
<data-member name="p_test_cases_timed_out"><type>counter_prop</type><purpose>Number of timed out test cases. </purpose></data-member>
<data-member name="p_test_suites_timed_out"><type>counter_prop</type><purpose>Number of timed out test suites. </purpose></data-member>
<data-member name="p_duration_microseconds"><type>counter_prop</type><purpose>Duration of the test in microseconds. </purpose></data-member>
<data-member name="p_aborted"><type>bool_prop</type><purpose>Indicates that the test unit execution has been aborted. </purpose></data-member>
<data-member name="p_skipped"><type>bool_prop</type><purpose>Indicates that the test unit execution has been skipped. </purpose></data-member>
<data-member name="p_timed_out"><type>bool_prop</type><purpose>Indicates that the test unit has timed out. </purpose></data-member>
<method-group name="public member functions">
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>counter_t</paramtype></parameter><parameter name=""><paramtype>(<classname>results_collector_t</classname>)(<classname>test_results</classname>)(results_collect_helper)</paramtype></parameter><purpose>Type representing counter like public property. </purpose></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>(<classname>results_collector_t</classname>)(<classname>test_results</classname>)(results_collect_helper)</paramtype></parameter><purpose>Type representing boolean like public property. </purpose></method>
<method name="passed" cv="const"><type>bool</type><purpose>Returns true if test unit passed. </purpose></method>
<method name="skipped" cv="const"><type>bool</type><purpose>Returns true if test unit skipped. </purpose><description><para>For test suites, this indicates if the test suite itself has been marked as skipped, and not if the test suite contains any skipped test. </para></description></method>
<method name="aborted" cv="const"><type>bool</type><purpose>Returns true if the test unit was aborted (hard failure) </purpose></method>
<method name="result_code" cv="const"><type>int</type><purpose>Produces result code for the test unit execution. </purpose><description><para>This methhod return one of the result codes defined in <computeroutput>boost/cstdlib.hpp</computeroutput> 
</para></description><returns><para><itemizedlist>
<listitem><para><computeroutput>boost::exit_success</computeroutput> on success,</para>
</listitem><listitem><para><computeroutput>boost::exit_exception_failure</computeroutput> in case test unit was aborted for any reason (including uncaught exception)</para>
</listitem><listitem><para>and <computeroutput>boost::exit_test_failure</computeroutput> otherwise </para>
</listitem></itemizedlist>
</para>
</returns></method>
<method name="operator+="><type>void</type><parameter name=""><paramtype><classname>test_results</classname> const &amp;</paramtype></parameter><purpose>Combines the results of the current instance with another. </purpose><description><para>Only the counters are updated and the <computeroutput>p_aborted</computeroutput> and <computeroutput>p_skipped</computeroutput> are left unchanged. </para></description></method>
<method name="clear"><type>void</type><purpose>Resets the current state of the result. </purpose></method>
</method-group>
<constructor/>
</class>





</namespace>
</namespace>
</header>
<header name="boost/test/results_reporter.hpp">
<para>defines testing result reporter interfaces </para><para>This file defines interfaces that are responsible for results reporting. Interface is presented in a form of free standing function implemented in namespace result_reporter </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="results_reporter">
<class name="format"><purpose>Results report formatter interface. </purpose><description><para>This is abstract interface for the report formatter used by results reporter routines. You can define a custom formatter by implementing this interface and setting the formatter using set_format function. This is usually done during test module initialization </para></description><method-group name="public member functions">
<method name="results_report_start" cv="= 0" specifiers="virtual"><type>void</type><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
<method name="results_report_finish" cv="= 0" specifiers="virtual"><type>void</type><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
<method name="test_unit_report_start" cv="= 0" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
<method name="test_unit_report_finish" cv="= 0" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
<method name="do_confirmation_report" cv="= 0" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name="ostr"><paramtype>std::ostream &amp;</paramtype></parameter></method>
</method-group>
<destructor/>
</class><function name="set_level"><type>void</type><parameter name="l"><paramtype>report_level</paramtype><description><para>report level </para></description></parameter><purpose>Sets reporting level. </purpose><description><para>There are only four possible levels for results report:<itemizedlist>
<listitem><para>confirmation report (boost::unit_test::CONFIRMATION_REPORT). This report level only produces short confirmation message about test module pass/fail status</para>
</listitem><listitem><para>short report (boost::unit_test::SHORT_REPORT). This report level produces short summary report for failed/passed assertions and test units.</para>
</listitem><listitem><para>detailed report (boost::unit_test::DETAILED_REPORT). This report level produces detailed report per test unit for passed/failed assertions and uncaught exceptions</para>
</listitem><listitem><para>no report (boost::unit_test::NO_REPORT). This report level produces no results report. This is used for test modules running as part of some kind of continues integration framework 
</para>
</listitem></itemizedlist>
</para></description></function>
<function name="set_stream"><type>void</type><parameter name=""><paramtype>std::ostream &amp;</paramtype></parameter><purpose>Sets output stream for results reporting. </purpose><description><para>By default std::cerr is used. Use this function to set a different stream. The framework refers to the stream by reference, so you need to make sure the stream object lifetime exceeds the testing main scope. </para></description></function>
<function name="set_format"><type>void</type><parameter name="of"><paramtype>output_format</paramtype><description><para>one of the presefined enumeration values for output formats </para></description></parameter><purpose>Sets one of the predefined formats. </purpose><description><para>The framework implements two results report formats:<itemizedlist>
<listitem><para>plain human readable format (boost::unit_test::OF_CLF)</para>
</listitem><listitem><para>XML format (boost::unit_test::OF_XML) 
</para>
</listitem></itemizedlist>
</para></description></function>
<function name="set_format"><type>void</type><parameter name="f"><paramtype><classname>results_reporter::format</classname> *</paramtype><description><para>pointer to heap allocated instance of custom report formatter class </para></description></parameter><purpose>Sets custom report formatter. </purpose><description><para>The framework takes ownership of the pointer passed as an argument. So this should be a pointer to a heap allocated object 
</para></description></function>
<function name="get_stream"><type>std::ostream &amp;</type><purpose>Access to configured results reporter stream. </purpose><description><para>Use this stream to report additional information abut test module execution </para></description></function>
<function name="make_report"><type>void</type><parameter name="l"><paramtype>report_level</paramtype><default>INV_REPORT_LEVEL</default></parameter><parameter name=""><paramtype>test_unit_id</paramtype><default>INV_TEST_UNIT_ID</default></parameter></function>
<function name="confirmation_report"><type>void</type><parameter name="id"><paramtype>test_unit_id</paramtype><default>INV_TEST_UNIT_ID</default></parameter></function>
<function name="short_report"><type>void</type><parameter name="id"><paramtype>test_unit_id</paramtype><default>INV_TEST_UNIT_ID</default></parameter></function>
<function name="detailed_report"><type>void</type><parameter name="id"><paramtype>test_unit_id</paramtype><default>INV_TEST_UNIT_ID</default></parameter></function>
</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/tools/assertion_result.hpp">
<para>Enhanced result for test predicate that include message explaining failure. </para><namespace name="boost">
<namespace name="test_tools">
<class name="assertion_result"><purpose>Type used for storing the result of an assertion. </purpose><struct name="dummy"><method-group name="public member functions">
<method name="nonnull"><type>void</type></method>
</method-group>
</struct><method-group name="public member functions">
<method name="operator!" cv="const"><type>bool</type></method>
<method name="conversion-operator" cv="const"><type>safe_bool</type></method>
<method name="BOOST_READONLY_PROPERTY"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>(<classname>assertion_result</classname>)</paramtype></parameter></method>
<method name="has_empty_message" cv="const"><type>bool</type></method>
<method name="message"><type>wrap_stringstream &amp;</type></method>
<method name="message" cv="const"><type>const_string</type></method>
</method-group>
<constructor><parameter name="pv_"><paramtype>bool</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="BoolConvertable"/>
        </template><parameter name="pv_"><paramtype>BoolConvertable const &amp;</paramtype></parameter></constructor>
<copy-assignment><type>void</type><parameter name="pv_"><paramtype>bool</paramtype></parameter></copy-assignment>
</class><typedef name="predicate_result"><type><classname>assertion_result</classname></type></typedef>









</namespace>
</namespace>
</header>
<header name="boost/test/tools/detail/bitwise_manip.hpp">
<para>Bitwise comparison manipulator implementation. </para><namespace name="boost">
<namespace name="test_tools">
<struct name="bitwise"><purpose>Bitwise comparison manipulator This is a terminal for the expression. </purpose></struct><function name="operator&lt;&lt;"><type>unit_test::lazy_ostream &amp;</type><parameter name="o"><paramtype>unit_test::lazy_ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>bitwise</classname></paramtype></parameter></function>
<function name="operator&lt;&lt;"><type>std::ostream &amp;</type><parameter name="o"><paramtype>std::ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>bitwise</classname></paramtype></parameter></function>







</namespace>
</namespace>
</header>
<header name="boost/test/tools/detail/lexicographic_manip.hpp">
<para>Lexicographic comparison manipulator implementation. </para><namespace name="boost">
<namespace name="test_tools">
<struct name="lexicographic"><purpose>Lexicographic comparison manipulator, for containers This is a terminal that involves evaluation of the expression. </purpose></struct>

<function name="operator&lt;&lt;"><type>unit_test::lazy_ostream &amp;</type><parameter name="o"><paramtype>unit_test::lazy_ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>lexicographic</classname></paramtype></parameter></function>
<function name="operator&lt;&lt;"><type>std::ostream &amp;</type><parameter name="o"><paramtype>std::ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>lexicographic</classname></paramtype></parameter></function>





</namespace>
</namespace>
</header>
<header name="boost/test/tools/detail/per_element_manip.hpp">
<para>Per element comparison manipulator implementation. </para><namespace name="boost">
<namespace name="test_tools">
<struct name="per_element"><purpose>Per element comparison manipulator, for containers This is a terminal that involves evaluation of the expression. </purpose></struct>



<function name="operator&lt;&lt;"><type>unit_test::lazy_ostream &amp;</type><parameter name="o"><paramtype>unit_test::lazy_ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>per_element</classname></paramtype></parameter></function>
<function name="operator&lt;&lt;"><type>std::ostream &amp;</type><parameter name="o"><paramtype>std::ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>per_element</classname></paramtype></parameter></function>



</namespace>
</namespace>
</header>
<header name="boost/test/tools/detail/tolerance_manip.hpp">
<para>Floating point comparison tolerance manipulators. </para><para>This file defines several manipulators for floating point comparison. These manipulators are intended to be used with BOOST_TEST. </para><namespace name="boost">
<namespace name="test_tools">






<overloaded-function name="tolerance"><signature><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="FPT"/>
        </template><parameter name="v"><paramtype>FPT</paramtype></parameter></signature><signature><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="FPT"/>
        </template><parameter name="v"><paramtype>fpc::percent_tolerance_t&lt; FPT &gt;</paramtype></parameter></signature><signature><type><emphasis>unspecified</emphasis></type></signature><description><para>Tolerance manipulator</para><para>These functions return a manipulator that can be used in conjunction with BOOST_TEST in order to specify the tolerance with which floating point comparisons are made. </para></description></overloaded-function>


</namespace>
</namespace>
</header>
<header name="boost/test/tools/floating_point_comparison.hpp">
<para>algorithms for comparing floating point values </para><namespace name="boost">
<namespace name="math">
<namespace name="fpc">
<class name="close_at_tolerance"><template>
      <template-type-parameter name="FPT"/>
    </template><purpose>Predicate for comparing floating point numbers. </purpose><description><para>This predicate is used to compare floating point numbers. In addition the comparison produces maximum related difference, which can be used to generate detailed error message The methods for comparing floating points are detailed in the documentation. The method is chosen by the boost::math::fpc::strength given at construction.</para><para>This predicate is not suitable for comparing to 0 or to infinity. </para></description><typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="fraction_tolerance" cv="const"><type>FPT</type><purpose>Returns the tolerance. </purpose></method>
<method name="strength" cv="const"><type>fpc::strength</type><purpose>Returns the comparison method. </purpose></method>
<method name="tested_rel_diff" cv="const"><type>FPT</type><purpose>Returns the failing fraction. </purpose></method>
<method name="operator()" cv="const"><type>bool</type><parameter name="left"><paramtype>FPT</paramtype><description><para>first floating point number to be compared </para></description></parameter><parameter name="right"><paramtype>FPT</paramtype><description><para>second floating point number to be compared</para></description></parameter><description><para>Compares two floating point numbers a and b such that their "left" relative difference |a-b|/a and/or "right" relative difference |a-b|/b does not exceed specified relative (fraction) tolerance.</para><para>
What is reported by <computeroutput>tested_rel_diff</computeroutput> in case of failure depends on the comparison method:<itemizedlist>
<listitem><para>for <computeroutput>FPC_STRONG:</computeroutput> the max of the two fractions</para>
</listitem><listitem><para>for <computeroutput>FPC_WEAK:</computeroutput> the min of the two fractions The rationale behind is to report the tolerance to set in order to make a test pass. </para>
</listitem></itemizedlist>
</para></description></method>
</method-group>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="ToleranceType"/>
        </template><parameter name="tolerance"><paramtype>ToleranceType</paramtype></parameter><parameter name="fpc_strength"><paramtype>fpc::strength</paramtype><default>FPC_STRONG</default></parameter></constructor>
</class><class name="is_abstract_class_or_function"><template>
      <template-type-parameter name="T"/>
    </template><data-member name="value" specifiers="static"><type>const bool</type></data-member>
<method-group name="private static functions">
<method name="test" specifiers="static"><type>char</type><template>
          <template-type-parameter name="U"/>
        </template><parameter name=""><paramtype>U(*)</paramtype></parameter></method>
<method name="test" specifiers="static"><type>Two</type><template>
          <template-type-parameter name="U"/>
        </template><parameter name=""><paramtype>...</paramtype></parameter></method>
</method-group>
</class><struct name="percent_tolerance_t"><template>
      <template-type-parameter name="FPT"/>
    </template><data-member name="m_value"><type>FPT</type></data-member>
<method-group name="public member functions">
</method-group>
<constructor specifiers="explicit"><parameter name="v"><paramtype>FPT</paramtype></parameter></constructor>
</struct><class name="small_with_tolerance"><template>
      <template-type-parameter name="FPT"/>
    </template><purpose>Predicate for comparing floating point numbers against 0. </purpose><description><para>Serves the same purpose as <classname alt="boost::math::fpc::close_at_tolerance">boost::math::fpc::close_at_tolerance</classname>, but used when one of the operand is null. </para></description><typedef name="result_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>bool</type><parameter name="fpv"><paramtype>FPT</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit"><parameter name="tolerance"><paramtype>FPT</paramtype></parameter></constructor>
</class><struct name="tolerance_based"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">tolerance_based_delegate::type&lt; T, !is_array&lt; T &gt;::value &amp;&amp;!is_abstract_class_or_function&lt; T &gt;::value &gt;</inherit><purpose>Indicates if a type can be compared using a tolerance scheme. </purpose><description><para>This is a metafunction that should evaluate to <computeroutput>mpl::true_</computeroutput> if the type <computeroutput>T</computeroutput> can be compared using a tolerance based method, typically for floating point types.</para><para>This metafunction can be specialized further to declare user types that are floating point (eg. boost.multiprecision). </para></description></struct><struct name="tolerance_based_delegate"><template>
      <template-type-parameter name="T"/>
      <template-nontype-parameter name="enabled"><type>bool</type></template-nontype-parameter>
    </template></struct><struct-specialization name="tolerance_based_delegate"><template>
      <template-type-parameter name="T"/>
    </template><specialization><template-arg>T</template-arg><template-arg>false</template-arg></specialization><inherit access="public">false_</inherit></struct-specialization><struct-specialization name="tolerance_based_delegate"><template>
      <template-type-parameter name="T"/>
    </template><specialization><template-arg>T</template-arg><template-arg>true</template-arg></specialization><inherit access="public">mpl::bool_&lt; is_floating_point&lt; T &gt;::value||(!std::numeric_limits&lt; T &gt;::is_integer &amp;&amp;std::numeric_limits&lt; T &gt;::is_specialized &amp;&amp;!std::numeric_limits&lt; T &gt;::is_exact)&gt;</inherit></struct-specialization><enum name="strength"><enumvalue name="FPC_STRONG"><purpose>"Very close" - equation 2' in docs, the default </purpose></enumvalue><enumvalue name="FPC_WEAK"><purpose>"Close enough" - equation 3' in docs. </purpose></enumvalue><purpose>Method for comparing floating point numbers. </purpose></enum>
<function name="operator&lt;&lt;"><type>std::ostream &amp;</type><template>
          <template-type-parameter name="FPT"/>
        </template><parameter name="out"><paramtype>std::ostream &amp;</paramtype></parameter><parameter name="t"><paramtype><classname>percent_tolerance_t</classname>&lt; FPT &gt;</paramtype></parameter></function>
<function name="percent_tolerance"><type><classname>percent_tolerance_t</classname>&lt; FPT &gt;</type><template>
          <template-type-parameter name="FPT"/>
        </template><parameter name="v"><paramtype>FPT</paramtype></parameter></function>
<function name="is_small"><type>bool</type><template>
          <template-type-parameter name="FPT"/>
        </template><parameter name="fpv"><paramtype>FPT</paramtype></parameter><parameter name="tolerance"><paramtype>FPT</paramtype></parameter></function>
</namespace>
</namespace>
</namespace>
</header>
<header name="boost/test/tools/output_test_stream.hpp">
<para>output_test_stream class definition </para><namespace name="boost">
<namespace name="test_tools">
<class name="output_test_stream"><inherit access="public">wrapped_stream</inherit><purpose>Class to be used to simplify testing of ostream-based output operations. </purpose><method-group name="public member functions">
<method name="is_empty" specifiers="virtual"><type><classname>assertion_result</classname></type><parameter name="flush_stream"><paramtype>bool</paramtype><default>true</default><description><para>if true, flushes the stream after the call </para></description></parameter><purpose>Checks if the stream is empty. </purpose><description><para>
</para></description></method>
<method name="check_length" specifiers="virtual"><type><classname>assertion_result</classname></type><parameter name="length"><paramtype>std::size_t</paramtype><description><para>target length </para></description></parameter><parameter name="flush_stream"><paramtype>bool</paramtype><default>true</default><description><para>if true, flushes the stream after the call. Set to false to call additional checks on the same content. </para></description></parameter><purpose>Checks the length of the stream. </purpose><description><para>
</para></description></method>
<method name="is_equal" specifiers="virtual"><type><classname>assertion_result</classname></type><parameter name="arg_"><paramtype>const_string</paramtype><description><para>the target stream </para></description></parameter><parameter name="flush_stream"><paramtype>bool</paramtype><default>true</default><description><para>if true, flushes the stream after the call. </para></description></parameter><purpose>Checks the content of the stream against a string. </purpose><description><para>
</para></description></method>
<method name="match_pattern" specifiers="virtual"><type><classname>assertion_result</classname></type><parameter name="flush_stream"><paramtype>bool</paramtype><default>true</default><description><para>if true, flushes/resets the stream after the call. </para></description></parameter><purpose>Checks the content of the stream against a pattern file. </purpose><description><para>
</para></description></method>
<method name="flush"><type>void</type><purpose>Flushes the stream. </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="pattern_file_name"><paramtype>const_string</paramtype><default>const_string()</default><description><para>indicates the name of the file for matching. If the string is empty, the standard input or output streams are used instead (depending on match_or_save) </para></description></parameter><parameter name="match_or_save"><paramtype>bool</paramtype><default>true</default><description><para>if true, the pattern file will be read, otherwise it will be written </para></description></parameter><parameter name="text_or_binary"><paramtype>bool</paramtype><default>true</default><description><para>if false, opens the stream in binary mode. Otherwise the stream is opened with default flags and the carriage returns are ignored. </para></description></parameter><purpose>Constructor. </purpose><description><para>
</para></description></constructor>
<destructor/>
<method-group name="protected member functions">
<method name="get_stream_string_representation" cv="const" specifiers="virtual"><type>std::string</type><purpose>Returns the string representation of the stream. </purpose><description><para>May be overriden in order to mutate the string before the matching operations. </para></description></method>
</method-group>
<method-group name="private member functions">
<method name="length"><type>std::size_t</type><purpose>Length of the stream. </purpose></method>
<method name="sync" specifiers="virtual"><type>void</type><purpose>Synching the stream into an internal string representation. </purpose></method>
</method-group>
</class>








</namespace>
</namespace>
</header>
<header name="boost/test/tree/observer.hpp">
<para>defines abstract interface for test observer </para><namespace name="boost">
<namespace name="unit_test">
<class name="test_observer"><purpose>Generic test observer interface. </purpose><description><para>This interface is used by observers in order to receive notifications from the Boost.Test framework on the current execution state.</para><para>Several observers can be running at the same time, and it is not unusual to have interactions among them. The test_observer::priority member function allows the specification of a particular order among them (lowest priority executed first, except specified otherwise). </para></description><method-group name="public member functions">
<method name="test_start" specifiers="virtual"><type>void</type><parameter name=""><paramtype>counter_t</paramtype></parameter><parameter name=""><paramtype>test_unit_id</paramtype></parameter><purpose>Called before the framework starts executing the test cases. </purpose><description><para>
</para></description></method>
<method name="test_finish" specifiers="virtual"><type>void</type><purpose>Called after the framework ends executing the test cases. </purpose><description><para><note><para>The call is made with a reversed priority order. </para>
</note>
</para></description></method>
<method name="test_aborted" specifiers="virtual"><type>void</type><purpose>Called when a critical error is detected. </purpose><description><para>The critical errors are mainly the signals sent by the system and caught by the Boost.Test framework. Since the running binary may be in incoherent/instable state, the test execution is aborted and all remaining tests are discarded.</para><para><note><para>may be called before test_observer::test_unit_finish() </para>
</note>
</para></description></method>
<method name="test_unit_start" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Called before the framework starts executing a test unit. </purpose><description><para>
</para></description></method>
<method name="test_unit_finish" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned long</paramtype></parameter><purpose>Called at each end of a test unit. </purpose><description><para>
</para></description></method>
<method name="test_unit_skipped" specifiers="virtual"><type>void</type><parameter name="tu"><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name=""><paramtype>const_string</paramtype></parameter></method>
<method name="test_unit_skipped" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>backward compatibility </purpose></method>
<method name="test_unit_timed_out" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Called when the test timed out. </purpose><description><para>This function is called to signal that a test unit (case or suite) timed out. A valid test unit is available through boost::unit_test::framework::current_test_unit </para></description></method>
<method name="test_unit_aborted" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Called when a test unit indicates a fatal error. </purpose><description><para>A fatal error happens when<itemizedlist>
<listitem><para>a strong assertion (with <computeroutput>REQUIRE</computeroutput>) fails, which indicates that the test case cannot continue</para>
</listitem><listitem><para>an unexpected exception is caught by the Boost.Test framework </para>
</listitem></itemizedlist>
</para></description></method>
<method name="assertion_result" specifiers="virtual"><type>void</type><parameter name=""><paramtype>unit_test::assertion_result</paramtype></parameter></method>
<method name="exception_caught" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>execution_exception</classname> const &amp;</paramtype></parameter><purpose>Called when an exception is intercepted. </purpose><description><para>In case an exception is intercepted, this call happens before the call to test_unit_aborted in order to log additional data about the exception. </para></description></method>
<method name="priority" specifiers="virtual"><type>int</type><purpose>The priority indicates the order at which this observer is initialized and tore down in the UTF framework. The order is lowest to highest priority. </purpose></method>
</method-group>
<method-group name="protected member functions">
</method-group>
<destructor/>
</class>





</namespace>
</namespace>
</header>
<header name="boost/test/tree/test_unit.hpp">
<para>Defines <classname alt="boost::unit_test::test_unit">test_unit</classname>, <classname alt="boost::unit_test::test_case">test_case</classname>, <classname alt="boost::unit_test::test_suite">test_suite</classname> and <classname alt="boost::unit_test::master_test_suite_t">master_test_suite_t</classname>. </para><namespace name="boost">
<namespace name="unit_test">
<class name="master_test_suite_t"><inherit access="public">boost::unit_test::test_suite</inherit><data-member name="argc"><type>int</type></data-member>
<data-member name="argv"><type>char **</type></data-member>
<data-member name="p_type"><type>test_unit_type const</type><purpose>type for this test unit </purpose></data-member>
<data-member name="p_type_name"><type>const_string const</type><purpose>"case"/"suite"/"module" </purpose></data-member>
<data-member name="p_file_name"><type>const_string const</type></data-member>
<data-member name="p_line_num"><type>std::size_t const</type></data-member>
<data-member name="p_id"><type>id_t</type><purpose>unique id for this test unit </purpose></data-member>
<data-member name="p_parent_id"><type>parent_id_t</type><purpose>parent test suite id </purpose></data-member>
<data-member name="p_labels"><type>label_list_t</type><purpose>list of labels associated with this test unit </purpose></data-member>
<data-member name="p_dependencies"><type>id_list_t</type><purpose>list of test units this one depends on </purpose></data-member>
<data-member name="p_preconditions"><type>precond_list_t</type><purpose>user supplied preconditions for this test unit; </purpose></data-member>
<data-member name="p_name"><type>readwrite_property&lt; std::string &gt;</type><purpose>name for this test unit </purpose></data-member>
<data-member name="p_description"><type>readwrite_property&lt; std::string &gt;</type><purpose>description for this test unit </purpose></data-member>
<data-member name="p_timeout"><type>readwrite_property&lt; unsigned &gt;</type><purpose>timeout for the test unit execution in seconds </purpose></data-member>
<data-member name="p_expected_failures"><type>readwrite_property&lt; counter_t &gt;</type><purpose>number of expected failures in this test unit </purpose></data-member>
<data-member name="p_default_status"><type>readwrite_property&lt; run_status &gt;</type><purpose>run status obtained by this unit during setup phase </purpose></data-member>
<data-member name="p_run_status"><type>readwrite_property&lt; run_status &gt;</type><purpose>run status assigned to this unit before execution phase after applying all filters </purpose></data-member>
<data-member name="p_sibling_rank"><type>readwrite_property&lt; counter_t &gt;</type><purpose>rank of this test unit amoung siblings of the same parent </purpose></data-member>
<data-member name="p_decorators"><type>readwrite_property&lt; decor_list_t &gt;</type><purpose>automatically assigned decorators; execution is delayed till framework::finalize_setup_phase function </purpose></data-member>
<data-member name="p_fixtures"><type>readwrite_property&lt; fixture_list_t &gt;</type><purpose>fixtures associated with this test unit </purpose></data-member>
<method-group name="private member functions">
</method-group>
<constructor/>
<constructor><parameter name=""><paramtype>const <classname>master_test_suite_t</classname> &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>master_test_suite_t</classname> &amp;</type><parameter name=""><paramtype><classname>master_test_suite_t</classname> const &amp;</paramtype></parameter></copy-assignment>
<enum name="@2"><enumvalue name="type"><default>= TUT_SUITE</default></enumvalue></enum>
<enum name="@0"><enumvalue name="type"><default>= TUT_ANY</default></enumvalue></enum>
<enum name="run_status"><enumvalue name="RS_DISABLED"/><enumvalue name="RS_ENABLED"/><enumvalue name="RS_INHERIT"/><enumvalue name="RS_INVALID"/></enum>
<typedef name="id_list"><type>std::vector&lt; test_unit_id &gt;</type></typedef>
<typedef name="fixture_list_t"><type>std::vector&lt; test_unit_fixture_ptr &gt;</type></typedef>
<typedef name="decor_list_t"><type>std::vector&lt; decorator::base_ptr &gt;</type></typedef>
<typedef name="precondition_t"><type>boost::function&lt; <classname>test_tools::assertion_result</classname>(test_unit_id)&gt;</type></typedef>
<method-group name="public member functions">
<method name="add"><type>void</type><parameter name="tu"><paramtype><classname>test_unit</classname> *</paramtype></parameter><parameter name="expected_failures"><paramtype>counter_t</paramtype><default>0</default></parameter><parameter name="timeout"><paramtype>unsigned</paramtype><default>0</default></parameter><purpose>Adds a test unit to a test suite. </purpose><description><para>It is possible to specify the timeout and the expected failures. </para></description></method>
<method name="add"><type>void</type><parameter name="gen"><paramtype><classname>test_unit_generator</classname> const &amp;</paramtype></parameter><parameter name="timeout"><paramtype>unsigned</paramtype><default>0</default></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="add"><type>void</type><parameter name="gen"><paramtype><classname>test_unit_generator</classname> const &amp;</paramtype></parameter><parameter name="decorators"><paramtype>decorator::collector_t &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="add"><type>void</type><parameter name="gen_ptr"><paramtype>boost::shared_ptr&lt; <classname>test_unit_generator</classname> &gt;</paramtype></parameter><parameter name="decorators"><paramtype>decorator::collector_t &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="remove"><type>void</type><parameter name="id"><paramtype>test_unit_id</paramtype></parameter><purpose>Removes a test from the test suite. </purpose></method>
<method name="generate"><type>void</type><purpose>Generates all the delayed test_units from the generators. </purpose></method>
<method name="check_for_duplicate_test_cases"><type>void</type><purpose>Check for duplicates name in test cases. </purpose><description><para>Raises a setup_error if there are duplicates </para></description></method>
<method name="get" cv="const"><type>test_unit_id</type><parameter name="tu_name"><paramtype>const_string</paramtype></parameter></method>
<method name="size" cv="const"><type>std::size_t</type></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>test_unit_id</paramtype></parameter><parameter name=""><paramtype>(framework::state)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>test_unit_id</paramtype></parameter><parameter name=""><paramtype>(<classname>test_suite</classname>)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>id_list</paramtype></parameter><parameter name=""><paramtype>(<classname>test_unit</classname>)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>std::vector&lt; std::string &gt;</paramtype></parameter><parameter name=""><paramtype>(<classname>test_unit</classname>)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>std::vector&lt; precondition_t &gt;</paramtype></parameter><parameter name=""><paramtype>(<classname>test_unit</classname>)</paramtype></parameter></method>
<method name="depends_on"><type>void</type><parameter name="tu"><paramtype><classname>test_unit</classname> *</paramtype></parameter></method>
<method name="add_precondition"><type>void</type><parameter name=""><paramtype>precondition_t const &amp;</paramtype></parameter></method>
<method name="check_preconditions" cv="const"><type><classname>test_tools::assertion_result</classname></type></method>
<method name="add_label"><type>void</type><parameter name="l"><paramtype>const_string</paramtype></parameter></method>
<method name="has_label" cv="const"><type>bool</type><parameter name="l"><paramtype>const_string</paramtype></parameter></method>
<method name="increase_exp_fail"><type>void</type><parameter name="num"><paramtype>counter_t</paramtype></parameter></method>
<method name="is_enabled" cv="const"><type>bool</type></method>
<method name="full_name" cv="const"><type>std::string</type></method>
</method-group>
</class><class name="test_case"><inherit access="public">boost::unit_test::test_unit</inherit><enum name="@1"><enumvalue name="type"><default>= TUT_CASE</default></enumvalue></enum>
<enum name="@0"><enumvalue name="type"><default>= TUT_ANY</default></enumvalue></enum>
<enum name="run_status"><enumvalue name="RS_DISABLED"/><enumvalue name="RS_ENABLED"/><enumvalue name="RS_INHERIT"/><enumvalue name="RS_INVALID"/></enum>
<typedef name="id_list"><type>std::vector&lt; test_unit_id &gt;</type></typedef>
<typedef name="fixture_list_t"><type>std::vector&lt; test_unit_fixture_ptr &gt;</type></typedef>
<typedef name="decor_list_t"><type>std::vector&lt; decorator::base_ptr &gt;</type></typedef>
<typedef name="precondition_t"><type>boost::function&lt; <classname>test_tools::assertion_result</classname>(test_unit_id)&gt;</type></typedef>
<data-member name="p_test_func"><type>test_func</type></data-member>
<data-member name="p_type"><type>test_unit_type const</type><purpose>type for this test unit </purpose></data-member>
<data-member name="p_type_name"><type>const_string const</type><purpose>"case"/"suite"/"module" </purpose></data-member>
<data-member name="p_file_name"><type>const_string const</type></data-member>
<data-member name="p_line_num"><type>std::size_t const</type></data-member>
<data-member name="p_id"><type>id_t</type><purpose>unique id for this test unit </purpose></data-member>
<data-member name="p_parent_id"><type>parent_id_t</type><purpose>parent test suite id </purpose></data-member>
<data-member name="p_labels"><type>label_list_t</type><purpose>list of labels associated with this test unit </purpose></data-member>
<data-member name="p_dependencies"><type>id_list_t</type><purpose>list of test units this one depends on </purpose></data-member>
<data-member name="p_preconditions"><type>precond_list_t</type><purpose>user supplied preconditions for this test unit; </purpose></data-member>
<data-member name="p_name"><type>readwrite_property&lt; std::string &gt;</type><purpose>name for this test unit </purpose></data-member>
<data-member name="p_description"><type>readwrite_property&lt; std::string &gt;</type><purpose>description for this test unit </purpose></data-member>
<data-member name="p_timeout"><type>readwrite_property&lt; unsigned &gt;</type><purpose>timeout for the test unit execution in seconds </purpose></data-member>
<data-member name="p_expected_failures"><type>readwrite_property&lt; counter_t &gt;</type><purpose>number of expected failures in this test unit </purpose></data-member>
<data-member name="p_default_status"><type>readwrite_property&lt; run_status &gt;</type><purpose>run status obtained by this unit during setup phase </purpose></data-member>
<data-member name="p_run_status"><type>readwrite_property&lt; run_status &gt;</type><purpose>run status assigned to this unit before execution phase after applying all filters </purpose></data-member>
<data-member name="p_sibling_rank"><type>readwrite_property&lt; counter_t &gt;</type><purpose>rank of this test unit amoung siblings of the same parent </purpose></data-member>
<data-member name="p_decorators"><type>readwrite_property&lt; decor_list_t &gt;</type><purpose>automatically assigned decorators; execution is delayed till framework::finalize_setup_phase function </purpose></data-member>
<data-member name="p_fixtures"><type>readwrite_property&lt; fixture_list_t &gt;</type><purpose>fixtures associated with this test unit </purpose></data-member>
<method-group name="public member functions">
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>boost::function&lt; void()&gt;</paramtype></parameter><parameter name=""><paramtype>(<classname>test_case</classname>)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>test_unit_id</paramtype></parameter><parameter name=""><paramtype>(framework::state)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>test_unit_id</paramtype></parameter><parameter name=""><paramtype>(<classname>test_suite</classname>)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>id_list</paramtype></parameter><parameter name=""><paramtype>(<classname>test_unit</classname>)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>std::vector&lt; std::string &gt;</paramtype></parameter><parameter name=""><paramtype>(<classname>test_unit</classname>)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>std::vector&lt; precondition_t &gt;</paramtype></parameter><parameter name=""><paramtype>(<classname>test_unit</classname>)</paramtype></parameter></method>
<method name="depends_on"><type>void</type><parameter name="tu"><paramtype><classname>test_unit</classname> *</paramtype></parameter></method>
<method name="add_precondition"><type>void</type><parameter name=""><paramtype>precondition_t const &amp;</paramtype></parameter></method>
<method name="check_preconditions" cv="const"><type><classname>test_tools::assertion_result</classname></type></method>
<method name="add_label"><type>void</type><parameter name="l"><paramtype>const_string</paramtype></parameter></method>
<method name="has_label" cv="const"><type>bool</type><parameter name="l"><paramtype>const_string</paramtype></parameter></method>
<method name="increase_exp_fail"><type>void</type><parameter name="num"><paramtype>counter_t</paramtype></parameter></method>
<method name="is_enabled" cv="const"><type>bool</type></method>
<method name="full_name" cv="const"><type>std::string</type></method>
</method-group>
<constructor><parameter name="tc_name"><paramtype>const_string</paramtype></parameter><parameter name="test_func"><paramtype>boost::function&lt; void()&gt; const &amp;</paramtype></parameter></constructor>
<constructor><parameter name="tc_name"><paramtype>const_string</paramtype></parameter><parameter name="tc_file"><paramtype>const_string</paramtype></parameter><parameter name="tc_line"><paramtype>std::size_t</paramtype></parameter><parameter name="test_func"><paramtype>boost::function&lt; void()&gt; const &amp;</paramtype></parameter></constructor>
<method-group name="private member functions">
</method-group>
<destructor/>
</class><class name="test_suite"><inherit access="public">boost::unit_test::test_unit</inherit><purpose>Class representing test suites. </purpose><enum name="@2"><enumvalue name="type"><default>= TUT_SUITE</default></enumvalue></enum>
<enum name="@0"><enumvalue name="type"><default>= TUT_ANY</default></enumvalue></enum>
<enum name="run_status"><enumvalue name="RS_DISABLED"/><enumvalue name="RS_ENABLED"/><enumvalue name="RS_INHERIT"/><enumvalue name="RS_INVALID"/></enum>
<typedef name="id_list"><type>std::vector&lt; test_unit_id &gt;</type></typedef>
<typedef name="fixture_list_t"><type>std::vector&lt; test_unit_fixture_ptr &gt;</type></typedef>
<typedef name="decor_list_t"><type>std::vector&lt; decorator::base_ptr &gt;</type></typedef>
<typedef name="precondition_t"><type>boost::function&lt; <classname>test_tools::assertion_result</classname>(test_unit_id)&gt;</type></typedef>
<method-group name="public member functions">
<method name="add"><type>void</type><parameter name="tu"><paramtype><classname>test_unit</classname> *</paramtype></parameter><parameter name="expected_failures"><paramtype>counter_t</paramtype><default>0</default></parameter><parameter name="timeout"><paramtype>unsigned</paramtype><default>0</default></parameter><purpose>Adds a test unit to a test suite. </purpose><description><para>It is possible to specify the timeout and the expected failures. </para></description></method>
<method name="add"><type>void</type><parameter name="gen"><paramtype><classname>test_unit_generator</classname> const &amp;</paramtype></parameter><parameter name="timeout"><paramtype>unsigned</paramtype><default>0</default></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="add"><type>void</type><parameter name="gen"><paramtype><classname>test_unit_generator</classname> const &amp;</paramtype></parameter><parameter name="decorators"><paramtype>decorator::collector_t &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="add"><type>void</type><parameter name="gen_ptr"><paramtype>boost::shared_ptr&lt; <classname>test_unit_generator</classname> &gt;</paramtype></parameter><parameter name="decorators"><paramtype>decorator::collector_t &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="remove"><type>void</type><parameter name="id"><paramtype>test_unit_id</paramtype></parameter><purpose>Removes a test from the test suite. </purpose></method>
<method name="generate"><type>void</type><purpose>Generates all the delayed test_units from the generators. </purpose></method>
<method name="check_for_duplicate_test_cases"><type>void</type><purpose>Check for duplicates name in test cases. </purpose><description><para>Raises a setup_error if there are duplicates </para></description></method>
<method name="get" cv="const"><type>test_unit_id</type><parameter name="tu_name"><paramtype>const_string</paramtype></parameter></method>
<method name="size" cv="const"><type>std::size_t</type></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>test_unit_id</paramtype></parameter><parameter name=""><paramtype>(framework::state)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>test_unit_id</paramtype></parameter><parameter name=""><paramtype>(<classname>test_suite</classname>)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>id_list</paramtype></parameter><parameter name=""><paramtype>(<classname>test_unit</classname>)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>std::vector&lt; std::string &gt;</paramtype></parameter><parameter name=""><paramtype>(<classname>test_unit</classname>)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>std::vector&lt; precondition_t &gt;</paramtype></parameter><parameter name=""><paramtype>(<classname>test_unit</classname>)</paramtype></parameter></method>
<method name="depends_on"><type>void</type><parameter name="tu"><paramtype><classname>test_unit</classname> *</paramtype></parameter></method>
<method name="add_precondition"><type>void</type><parameter name=""><paramtype>precondition_t const &amp;</paramtype></parameter></method>
<method name="check_preconditions" cv="const"><type><classname>test_tools::assertion_result</classname></type></method>
<method name="add_label"><type>void</type><parameter name="l"><paramtype>const_string</paramtype></parameter></method>
<method name="has_label" cv="const"><type>bool</type><parameter name="l"><paramtype>const_string</paramtype></parameter></method>
<method name="increase_exp_fail"><type>void</type><parameter name="num"><paramtype>counter_t</paramtype></parameter></method>
<method name="is_enabled" cv="const"><type>bool</type></method>
<method name="full_name" cv="const"><type>std::string</type></method>
</method-group>
<constructor specifiers="explicit"><parameter name="ts_name"><paramtype>const_string</paramtype></parameter><parameter name="ts_file"><paramtype>const_string</paramtype></parameter><parameter name="ts_line"><paramtype>std::size_t</paramtype></parameter></constructor>
<method-group name="protected member functions">
</method-group>
<constructor specifiers="explicit"><parameter name="module_name"><paramtype>const_string</paramtype></parameter></constructor>
<destructor/>
<data-member name="p_type"><type>test_unit_type const</type><purpose>type for this test unit </purpose></data-member>
<data-member name="p_type_name"><type>const_string const</type><purpose>"case"/"suite"/"module" </purpose></data-member>
<data-member name="p_file_name"><type>const_string const</type></data-member>
<data-member name="p_line_num"><type>std::size_t const</type></data-member>
<data-member name="p_id"><type>id_t</type><purpose>unique id for this test unit </purpose></data-member>
<data-member name="p_parent_id"><type>parent_id_t</type><purpose>parent test suite id </purpose></data-member>
<data-member name="p_labels"><type>label_list_t</type><purpose>list of labels associated with this test unit </purpose></data-member>
<data-member name="p_dependencies"><type>id_list_t</type><purpose>list of test units this one depends on </purpose></data-member>
<data-member name="p_preconditions"><type>precond_list_t</type><purpose>user supplied preconditions for this test unit; </purpose></data-member>
<data-member name="p_name"><type>readwrite_property&lt; std::string &gt;</type><purpose>name for this test unit </purpose></data-member>
<data-member name="p_description"><type>readwrite_property&lt; std::string &gt;</type><purpose>description for this test unit </purpose></data-member>
<data-member name="p_timeout"><type>readwrite_property&lt; unsigned &gt;</type><purpose>timeout for the test unit execution in seconds </purpose></data-member>
<data-member name="p_expected_failures"><type>readwrite_property&lt; counter_t &gt;</type><purpose>number of expected failures in this test unit </purpose></data-member>
<data-member name="p_default_status"><type>readwrite_property&lt; run_status &gt;</type><purpose>run status obtained by this unit during setup phase </purpose></data-member>
<data-member name="p_run_status"><type>readwrite_property&lt; run_status &gt;</type><purpose>run status assigned to this unit before execution phase after applying all filters </purpose></data-member>
<data-member name="p_sibling_rank"><type>readwrite_property&lt; counter_t &gt;</type><purpose>rank of this test unit amoung siblings of the same parent </purpose></data-member>
<data-member name="p_decorators"><type>readwrite_property&lt; decor_list_t &gt;</type><purpose>automatically assigned decorators; execution is delayed till framework::finalize_setup_phase function </purpose></data-member>
<data-member name="p_fixtures"><type>readwrite_property&lt; fixture_list_t &gt;</type><purpose>fixtures associated with this test unit </purpose></data-member>
</class><class name="test_unit"><enum name="@0"><enumvalue name="type"><default>= TUT_ANY</default></enumvalue></enum>
<enum name="run_status"><enumvalue name="RS_DISABLED"/><enumvalue name="RS_ENABLED"/><enumvalue name="RS_INHERIT"/><enumvalue name="RS_INVALID"/></enum>
<typedef name="id_list"><type>std::vector&lt; test_unit_id &gt;</type></typedef>
<typedef name="fixture_list_t"><type>std::vector&lt; test_unit_fixture_ptr &gt;</type></typedef>
<typedef name="decor_list_t"><type>std::vector&lt; decorator::base_ptr &gt;</type></typedef>
<typedef name="precondition_t"><type>boost::function&lt; <classname>test_tools::assertion_result</classname>(test_unit_id)&gt;</type></typedef>
<data-member name="p_type"><type>test_unit_type const</type><purpose>type for this test unit </purpose></data-member>
<data-member name="p_type_name"><type>const_string const</type><purpose>"case"/"suite"/"module" </purpose></data-member>
<data-member name="p_file_name"><type>const_string const</type></data-member>
<data-member name="p_line_num"><type>std::size_t const</type></data-member>
<data-member name="p_id"><type>id_t</type><purpose>unique id for this test unit </purpose></data-member>
<data-member name="p_parent_id"><type>parent_id_t</type><purpose>parent test suite id </purpose></data-member>
<data-member name="p_labels"><type>label_list_t</type><purpose>list of labels associated with this test unit </purpose></data-member>
<data-member name="p_dependencies"><type>id_list_t</type><purpose>list of test units this one depends on </purpose></data-member>
<data-member name="p_preconditions"><type>precond_list_t</type><purpose>user supplied preconditions for this test unit; </purpose></data-member>
<data-member name="p_name"><type>readwrite_property&lt; std::string &gt;</type><purpose>name for this test unit </purpose></data-member>
<data-member name="p_description"><type>readwrite_property&lt; std::string &gt;</type><purpose>description for this test unit </purpose></data-member>
<data-member name="p_timeout"><type>readwrite_property&lt; unsigned &gt;</type><purpose>timeout for the test unit execution in seconds </purpose></data-member>
<data-member name="p_expected_failures"><type>readwrite_property&lt; counter_t &gt;</type><purpose>number of expected failures in this test unit </purpose></data-member>
<data-member name="p_default_status"><type>readwrite_property&lt; run_status &gt;</type><purpose>run status obtained by this unit during setup phase </purpose></data-member>
<data-member name="p_run_status"><type>readwrite_property&lt; run_status &gt;</type><purpose>run status assigned to this unit before execution phase after applying all filters </purpose></data-member>
<data-member name="p_sibling_rank"><type>readwrite_property&lt; counter_t &gt;</type><purpose>rank of this test unit amoung siblings of the same parent </purpose></data-member>
<data-member name="p_decorators"><type>readwrite_property&lt; decor_list_t &gt;</type><purpose>automatically assigned decorators; execution is delayed till framework::finalize_setup_phase function </purpose></data-member>
<data-member name="p_fixtures"><type>readwrite_property&lt; fixture_list_t &gt;</type><purpose>fixtures associated with this test unit </purpose></data-member>
<method-group name="public member functions">
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>test_unit_id</paramtype></parameter><parameter name=""><paramtype>(framework::state)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>test_unit_id</paramtype></parameter><parameter name=""><paramtype>(<classname>test_suite</classname>)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>id_list</paramtype></parameter><parameter name=""><paramtype>(<classname>test_unit</classname>)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>std::vector&lt; std::string &gt;</paramtype></parameter><parameter name=""><paramtype>(<classname>test_unit</classname>)</paramtype></parameter></method>
<method name="BOOST_READONLY_PROPERTY"><type>typedef</type><parameter name=""><paramtype>std::vector&lt; precondition_t &gt;</paramtype></parameter><parameter name=""><paramtype>(<classname>test_unit</classname>)</paramtype></parameter></method>
<method name="depends_on"><type>void</type><parameter name="tu"><paramtype><classname>test_unit</classname> *</paramtype></parameter></method>
<method name="add_precondition"><type>void</type><parameter name=""><paramtype>precondition_t const &amp;</paramtype></parameter></method>
<method name="check_preconditions" cv="const"><type><classname>test_tools::assertion_result</classname></type></method>
<method name="add_label"><type>void</type><parameter name="l"><paramtype>const_string</paramtype></parameter></method>
<method name="has_label" cv="const"><type>bool</type><parameter name="l"><paramtype>const_string</paramtype></parameter></method>
<method name="increase_exp_fail"><type>void</type><parameter name="num"><paramtype>counter_t</paramtype></parameter></method>
<method name="is_enabled" cv="const"><type>bool</type></method>
<method name="full_name" cv="const"><type>std::string</type></method>
</method-group>
<method-group name="protected member functions">
</method-group>
<destructor/>
<constructor><parameter name="tu_name"><paramtype>const_string</paramtype></parameter><parameter name="tc_file"><paramtype>const_string</paramtype></parameter><parameter name="tc_line"><paramtype>std::size_t</paramtype></parameter><parameter name="t"><paramtype>test_unit_type</paramtype></parameter></constructor>
<constructor specifiers="explicit"><parameter name="module_name"><paramtype>const_string</paramtype></parameter></constructor>
</class><class name="test_unit_generator"><method-group name="public member functions">
<method name="next" cv="const = 0" specifiers="virtual"><type><classname>test_unit</classname> *</type></method>
</method-group>
<method-group name="protected member functions">
</method-group>
<destructor/>
</class><namespace name="framework">




























</namespace>
<typedef name="test_unit_id_list"><type>std::vector&lt; test_unit_id &gt;</type></typedef>

<function name="make_test_case"><type><classname>test_case</classname> *</type><parameter name="test_func"><paramtype>boost::function&lt; void()&gt; const &amp;</paramtype></parameter><parameter name="tc_name"><paramtype>const_string</paramtype></parameter><parameter name="tc_file"><paramtype>const_string</paramtype></parameter><parameter name="tc_line"><paramtype>std::size_t</paramtype></parameter></function>
<function name="make_test_case"><type><classname>test_case</classname> *</type><template>
          <template-type-parameter name="UserTestCase"/>
          <template-type-parameter name="InstanceType"/>
        </template><parameter name="test_method"><paramtype>void(UserTestCase::*)()</paramtype></parameter><parameter name="tc_name"><paramtype>const_string</paramtype></parameter><parameter name="tc_file"><paramtype>const_string</paramtype></parameter><parameter name="tc_line"><paramtype>std::size_t</paramtype></parameter><parameter name="user_test_case"><paramtype>boost::shared_ptr&lt; InstanceType &gt;</paramtype></parameter></function>



</namespace>
</namespace>
</header>
<header name="boost/test/unit_test.hpp">
<para>Entry point into the Unit Test Framework. </para><para>This header should be the only header necessary to include to start using the framework </para><namespace name="boost">
<namespace name="unit_test">
<function name="unit_test_main"><type>int</type><parameter name="init_func"><paramtype>init_unit_test_func</paramtype></parameter><parameter name="argc"><paramtype>int</paramtype></parameter><parameter name="argv"><paramtype>char *</paramtype></parameter></function>





</namespace>
</namespace>
</header>
<header name="boost/test/unit_test_log.hpp">
<para>defines singleton class unit_test_log and all manipulators. unit_test_log has output stream like interface. It's implementation is completely hidden with pimple idiom </para><namespace name="boost">
<namespace name="unit_test">
<class name="unit_test_log_t"><inherit access="public">boost::unit_test::test_observer</inherit><purpose>Manages the sets of loggers, their streams and log levels. </purpose><description><para>The Boost.Test framework allows for having several formatters/loggers at the same time, each of which having their own log level and output stream.</para><para>This class serves the purpose of<itemizedlist>
<listitem><para>exposing an interface to the test framework (as a <classname alt="boost::unit_test::test_observer">boost::unit_test::test_observer</classname>)</para>
</listitem><listitem><para>exposing an interface to the testing tools</para>
</listitem><listitem><para>managing several loggers</para>
</listitem></itemizedlist>
</para><para><note><para>Accesses to the functions exposed by this class are made through the singleton <computeroutput>boost::unit_test::unit_test_log</computeroutput>.</para>
</note>
Users/developers willing to implement their own formatter need to:<itemizedlist>
<listitem><para>implement a <classname alt="boost::unit_test::unit_test_log_formatter">boost::unit_test::unit_test_log_formatter</classname> that will output the desired format</para>
</listitem><listitem><para>register the formatter during a eg. global fixture using the method <computeroutput>set_formatter</computeroutput> (though the framework singleton).</para>
</listitem></itemizedlist>
</para><para><warning><para>this observer has a higher priority than the <classname alt="boost::unit_test::results_collector_t">boost::unit_test::results_collector_t</classname>. This means that the various <classname alt="boost::unit_test::test_results">boost::unit_test::test_results</classname> associated to each test unit may not be available at the time the <computeroutput>test_unit_start</computeroutput>, <computeroutput>test_unit_finish</computeroutput> ... are called.</para>
</warning>
<para><emphasis role="bold">See Also:</emphasis><para><itemizedlist>
<listitem><para><classname alt="boost::unit_test::test_observer">boost::unit_test::test_observer</classname></para>
</listitem><listitem><para><classname alt="boost::unit_test::unit_test_log_formatter">boost::unit_test::unit_test_log_formatter</classname> </para>
</listitem></itemizedlist>
</para>
</para>
</para></description><method-group name="public member functions">
<method name="test_start" specifiers="virtual"><type>void</type><parameter name=""><paramtype>counter_t</paramtype></parameter><parameter name=""><paramtype>test_unit_id</paramtype></parameter><purpose>Called before the framework starts executing the test cases. </purpose><description><para>
</para></description></method>
<method name="test_finish" specifiers="virtual"><type>void</type><purpose>Called after the framework ends executing the test cases. </purpose><description><para><note><para>The call is made with a reversed priority order. </para>
</note>
</para></description></method>
<method name="test_aborted" specifiers="virtual"><type>void</type><purpose>Called when a critical error is detected. </purpose><description><para>The critical errors are mainly the signals sent by the system and caught by the Boost.Test framework. Since the running binary may be in incoherent/instable state, the test execution is aborted and all remaining tests are discarded.</para><para><note><para>may be called before test_observer::test_unit_finish() </para>
</note>
</para></description></method>
<method name="test_unit_start" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Called before the framework starts executing a test unit. </purpose><description><para>
</para></description></method>
<method name="test_unit_finish" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned long</paramtype></parameter><purpose>Called at each end of a test unit. </purpose><description><para>
</para></description></method>
<method name="test_unit_skipped" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><parameter name=""><paramtype>const_string</paramtype></parameter></method>
<method name="test_unit_aborted" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Called when a test unit indicates a fatal error. </purpose><description><para>A fatal error happens when<itemizedlist>
<listitem><para>a strong assertion (with <computeroutput>REQUIRE</computeroutput>) fails, which indicates that the test case cannot continue</para>
</listitem><listitem><para>an unexpected exception is caught by the Boost.Test framework </para>
</listitem></itemizedlist>
</para></description></method>
<method name="test_unit_timed_out" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Called when the test timed out. </purpose><description><para>This function is called to signal that a test unit (case or suite) timed out. A valid test unit is available through boost::unit_test::framework::current_test_unit </para></description></method>
<method name="exception_caught" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>execution_exception</classname> const &amp;</paramtype></parameter><purpose>Called when an exception is intercepted. </purpose><description><para>In case an exception is intercepted, this call happens before the call to test_unit_aborted in order to log additional data about the exception. </para></description></method>
<method name="priority" specifiers="virtual"><type>int</type><purpose>The priority indicates the order at which this observer is initialized and tore down in the UTF framework. The order is lowest to highest priority. </purpose></method>
<method name="set_stream"><type>void</type><parameter name=""><paramtype>std::ostream &amp;</paramtype></parameter><purpose>Sets the stream for all loggers. </purpose><description><para>This will override the log sink/stream of all loggers, whether enabled or not. </para></description></method>
<method name="set_stream"><type>void</type><parameter name=""><paramtype>output_format</paramtype></parameter><parameter name=""><paramtype>std::ostream &amp;</paramtype></parameter><purpose>Sets the stream for specific logger. </purpose><description><para><note><para>Has no effect if the specified format is not found </para>
</note>
<formalpara><title>Since Boost 1.62 </title><para/>
</formalpara>
</para></description></method>
<method name="get_stream" cv="const"><type>std::ostream *</type><parameter name=""><paramtype>output_format</paramtype></parameter><purpose>Returns a pointer to the stream associated to specific logger. </purpose><description><para><note><para>Returns a null pointer if the format is not found </para>
</note>
<formalpara><title>Since Boost 1.67 </title><para/>
</formalpara>
</para></description></method>
<method name="set_threshold_level"><type>log_level</type><parameter name=""><paramtype>log_level</paramtype></parameter><purpose>Sets the threshold level for all loggers/formatters. </purpose><description><para>This will override the log level of all loggers, whether enabled or not. 
</para></description><returns><para>the minimum of the previous log level of all formatters (new in Boost 1.73) </para>
</returns></method>
<method name="set_threshold_level"><type>log_level</type><parameter name=""><paramtype>output_format</paramtype></parameter><parameter name=""><paramtype>log_level</paramtype></parameter><purpose>Sets the threshold/log level of a specific format. </purpose><description><para><note><para>Has no effect if the specified format is not found </para>
</note>
<formalpara><title>Since Boost 1.62</title><para/>
</formalpara>

</para></description><returns><para>the previous log level of the corresponding formatter (new in Boost 1.73) </para>
</returns></method>
<method name="add_format"><type>void</type><parameter name=""><paramtype>output_format</paramtype></parameter><purpose>Add a format to the set of loggers. </purpose><description><para>Adding a logger means that the specified logger is enabled. The log level is managed by the formatter itself and specifies what events are forwarded to the underlying formatter. <formalpara><title>Since Boost 1.62 </title><para/>
</formalpara>
</para></description></method>
<method name="set_format"><type>void</type><parameter name=""><paramtype>output_format</paramtype></parameter><purpose>Sets the format of the logger. </purpose><description><para>This will become the only active format of the logs. </para></description></method>
<method name="get_formatter"><type><classname>unit_test_log_formatter</classname> *</type><parameter name=""><paramtype>output_format</paramtype></parameter><purpose>Returns the logger instance for a specific format. </purpose><description><para>
<formalpara><title>Since Boost 1.62 </title><para/>
</formalpara>
</para></description><returns><para>the logger/formatter instance, or <computeroutput/>(unit_test_log_formatter*)0 if the format is not found. </para>
</returns></method>
<method name="set_formatter"><type>void</type><parameter name=""><paramtype><classname>unit_test_log_formatter</classname> *</paramtype></parameter><purpose>Sets the logger instance. </purpose><description><para>The specified logger becomes the unique active one. The custom log formatter has the format <computeroutput>OF_CUSTOM_LOGGER</computeroutput>. If such a format exists already, its formatter gets replaced by the one given in argument.</para><para>The log level and output stream of the new formatter are taken from the currently active logger. In case several loggers are active, the order of priority is CUSTOM, HRF, XML, and JUNIT. If (unit_test_log_formatter*)0 is given as argument, the custom logger (if any) is removed.</para><para><note><para>The ownership of the pointer is transferred to the Boost.Test framework. This call is equivalent to<itemizedlist>
<listitem><para>a call to <computeroutput>add_formatter</computeroutput> </para>
</listitem><listitem><para>a call to <computeroutput>set_format(OF_CUSTOM_LOGGER)</computeroutput> </para>
</listitem><listitem><para>a configuration of the newly added logger with a previously configured stream and log level. </para>
</listitem></itemizedlist>
</para>
</note>
</para></description></method>
<method name="add_formatter"><type>void</type><parameter name="the_formatter"><paramtype><classname>unit_test_log_formatter</classname> *</paramtype></parameter><purpose>Adds a custom log formatter to the set of formatters. </purpose><description><para>The specified logger is added with the format <computeroutput>OF_CUSTOM_LOGGER</computeroutput>, such that it can be futher selected or its stream/log level can be specified. If there is already a custom logger (with <computeroutput>OF_CUSTOM_LOGGER</computeroutput>), then the existing one gets replaced by the one given in argument. The provided logger is added with an enabled state. If (unit_test_log_formatter*)0 is given as argument, the custom logger (if any) is removed and no other action is performed.</para><para><note><para>The ownership of the pointer is transferred to the Boost.Test framework. </para>
</note>
<formalpara><title>Since Boost 1.62 </title><para/>
</formalpara>
</para></description></method>
<method name="set_checkpoint"><type>void</type><parameter name="file"><paramtype>const_string</paramtype></parameter><parameter name="line_num"><paramtype>std::size_t</paramtype></parameter><parameter name="msg"><paramtype>const_string</paramtype><default>const_string()</default></parameter></method>
<method name="operator&lt;&lt;"><type><classname>unit_test_log_t</classname> &amp;</type><parameter name=""><paramtype><classname>log::begin</classname> const &amp;</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>unit_test_log_t</classname> &amp;</type><parameter name=""><paramtype><classname>log::end</classname> const &amp;</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>unit_test_log_t</classname> &amp;</type><parameter name=""><paramtype>log_level</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>unit_test_log_t</classname> &amp;</type><parameter name=""><paramtype>const_string</paramtype></parameter></method>
<method name="operator&lt;&lt;"><type><classname>unit_test_log_t</classname> &amp;</type><parameter name=""><paramtype>lazy_ostream const &amp;</paramtype></parameter></method>
<method name="operator()"><type><emphasis>unspecified</emphasis></type><parameter name=""><paramtype>log_level</paramtype></parameter></method>
<method name="configure"><type>void</type><purpose>Prepares internal states after log levels, streams and format has been set up. </purpose></method>
<method name="test_unit_skipped" specifiers="virtual"><type>void</type><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>backward compatibility </purpose></method>
<method name="assertion_result" specifiers="virtual"><type>void</type><parameter name=""><paramtype>unit_test::assertion_result</paramtype></parameter></method>
</method-group>
</class><namespace name="log">
<struct name="begin"><data-member name="m_file_name"><type>const_string</type></data-member>
<data-member name="m_line_num"><type>std::size_t</type></data-member>
<method-group name="public member functions">
</method-group>
<constructor><parameter name="fn"><paramtype>const_string</paramtype></parameter><parameter name="ln"><paramtype>std::size_t</paramtype></parameter></constructor>
</struct><struct name="end"/></namespace>






</namespace>
</namespace>
<macro name="BOOST_TEST_LOG_ENTRY" kind="functionlike"><macro-parameter name="ll"/></macro>
<macro name="BOOST_TEST_FRAMEWORK_MESSAGE" kind="functionlike"><macro-parameter name="M"/></macro>
<macro name="BOOST_TEST_MESSAGE" kind="functionlike"><macro-parameter name="M"/></macro>
<macro name="BOOST_TEST_PASSPOINT" kind="functionlike"><macro-parameter name=""/></macro>
<macro name="BOOST_TEST_CHECKPOINT" kind="functionlike"><macro-parameter name="M"/></macro>
</header>
<header name="boost/test/unit_test_log_formatter.hpp">
<para>Defines unit test log formatter interface. </para><para>You can define a class with implements this interface and use an instance of it as a Unit Test Framework log formatter </para><namespace name="boost">
<namespace name="unit_test">
<struct name="log_checkpoint_data"><purpose>Collection of log checkpoint attributes. </purpose><data-member name="m_file_name"><type>const_string</type><purpose>log checkpoint file name </purpose></data-member>
<data-member name="m_line_num"><type>std::size_t</type><purpose>log checkpoint file name </purpose></data-member>
<data-member name="m_message"><type>std::string</type><purpose>log checkpoint message </purpose></data-member>
<method-group name="public member functions">
<method name="clear"><type>void</type></method>
</method-group>
</struct><struct name="log_entry_data"><purpose>Collection of log entry attributes. </purpose><data-member name="m_file_name"><type>std::string</type><purpose>log entry file name </purpose></data-member>
<data-member name="m_line_num"><type>std::size_t</type><purpose>log entry line number </purpose></data-member>
<data-member name="m_level"><type>log_level</type><purpose>log entry level </purpose></data-member>
<method-group name="public member functions">
<method name="clear"><type>void</type></method>
</method-group>
<constructor/>
</struct><class name="unit_test_log_formatter"><purpose>Abstract Unit Test Framework log formatter interface. </purpose><description><para>During the test module execution Unit Test Framework can report messages about success or failure of assertions, which test suites are being run and more (specifically which messages are reported depends on log level threshold selected by the user).</para><para>All these messages constitute Unit Test Framework log. There are many ways (formats) to present these messages to the user.</para><para>Boost.Test comes with three formats:<itemizedlist>
<listitem><para>Compiler-like log format: intended for human consumption/diagnostic</para>
</listitem><listitem><para>XML based log format: intended for processing by automated regression test systems.</para>
</listitem><listitem><para>JUNIT based log format: intended for processing by automated regression test systems.</para>
</listitem></itemizedlist>
</para><para>If you want to produce some other format you need to implement class with specific interface and use method <computeroutput>unit_test_log_t::set_formatter</computeroutput> during a test module initialization to set an active formatter. The class <classname alt="boost::unit_test::unit_test_log_formatter">unit_test_log_formatter</classname> defines this interface.</para><para>This interface requires you to format all possible messages being produced in the log. These includes error messages about failed assertions, messages about caught exceptions and information messages about test units being started/ended. All the methods in this interface takes a reference to standard stream as a first argument. This is where final messages needs to be directed to. Also you are given all the information necessary to produce a message.</para><para><formalpara><title>Since Boost 1.62:</title><para><itemizedlist>
<listitem><para>Each formatter may indicate the default output stream. This is convenient for instance for streams intended for automated processing that indicate a file. See <computeroutput>get_default_stream_description</computeroutput> for more details.</para>
</listitem><listitem><para>Each formatter may manage its own log level through the getter/setter <computeroutput>get_log_level</computeroutput> and <computeroutput>set_log_level</computeroutput> .</para>
</listitem></itemizedlist>
</para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><itemizedlist>
<listitem><para><classname alt="boost::unit_test::test_observer">boost::unit_test::test_observer</classname> for an indication of the calls of the test observer interface </para>
</listitem></itemizedlist>
</para>
</para>
</para></description><enum name="log_entry_types"><enumvalue name="BOOST_UTL_ET_INFO"><purpose>Information message from the framework. </purpose></enumvalue><enumvalue name="BOOST_UTL_ET_MESSAGE"><purpose>Information message from the user. </purpose></enumvalue><enumvalue name="BOOST_UTL_ET_WARNING"><purpose>Warning (non error) condition notification message. </purpose></enumvalue><enumvalue name="BOOST_UTL_ET_ERROR"><purpose>Non fatal error notification message. </purpose></enumvalue><enumvalue name="BOOST_UTL_ET_FATAL_ERROR"><purpose>Fatal error notification message. </purpose></enumvalue><purpose>Types of log entries (messages written into a log) </purpose></enum>
<method-group name="public member functions">
<method name="log_start" cv="= 0" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages to </para></description></parameter><parameter name="test_cases_amount"><paramtype>counter_t</paramtype><description><para>total test case amount to be run </para></description></parameter><purpose>Invoked at the beginning of test module execution. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_finish </para>
</para>
</para></description></method>
<method name="log_finish" cv="= 0" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><purpose>Invoked at the end of test module execution. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_start </para>
</para>
</para></description></method>
<method name="log_build_info" cv="= 0" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="log_build_info"><paramtype>bool</paramtype><default>true</default><description><para>indicates if build info should be logged or not </para></description></parameter><purpose>Invoked when Unit Test Framework build information is requested. </purpose><description><para>
</para></description></method>
<method name="test_unit_start" cv="= 0" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="tu"><paramtype><classname>test_unit</classname> const &amp;</paramtype><description><para>test unit being started </para></description></parameter><purpose>Invoked when test unit starts (either test suite or test case) </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>test_unit_finish </para>
</para>
</para></description></method>
<method name="test_unit_finish" cv="= 0" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="tu"><paramtype><classname>test_unit</classname> const &amp;</paramtype><description><para>test unit being finished </para></description></parameter><parameter name="elapsed"><paramtype>unsigned long</paramtype><description><para>time in microseconds spend executing this test unit </para></description></parameter><purpose>Invoked when test unit finishes. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>test_unit_start </para>
</para>
</para></description></method>
<method name="test_unit_skipped" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="tu"><paramtype><classname>test_unit</classname> const &amp;</paramtype><description><para>skipped test unit </para></description></parameter><parameter name=""><paramtype>const_string</paramtype></parameter><purpose>Invoked if test unit skipped for any reason. </purpose><description><para>
</para></description></method>
<method name="test_unit_skipped" specifiers="virtual"><type>void</type><parameter name=""><paramtype>std::ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Deprecated version of this interface. </purpose><description><para><xrefsect id="a00851_1_deprecated000001"><xreftitle>Deprecated</xreftitle><xrefdescription/></xrefsect></para></description></method>
<method name="test_unit_aborted" specifiers="virtual"><type>void</type><parameter name=""><paramtype>std::ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Invoked when a test unit is aborted. </purpose></method>
<method name="test_unit_timed_out" specifiers="virtual"><type>void</type><parameter name=""><paramtype>std::ostream &amp;</paramtype></parameter><parameter name=""><paramtype><classname>test_unit</classname> const &amp;</paramtype></parameter><purpose>Invoked when a test unit times-out. </purpose></method>
<method name="log_exception_start" cv="= 0" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="lcd"><paramtype><classname>log_checkpoint_data</classname> const &amp;</paramtype><description><para>information about the last checkpoint before the exception was triggered </para></description></parameter><parameter name="ex"><paramtype><classname>execution_exception</classname> const &amp;</paramtype><description><para>information about the caught exception </para></description></parameter><purpose>Invoked when Unit Test Framework detects uncaught exception. </purpose><description><para>The framwork calls this function when an uncaught exception it detected. This call is followed by context information:<itemizedlist>
<listitem><para>one call to <computeroutput>entry_context_start</computeroutput>,</para>
</listitem><listitem><para>as many calls to <computeroutput>log_entry_context</computeroutput> as there are context entries</para>
</listitem><listitem><para>one call to <computeroutput>entry_context_finish</computeroutput> </para>
</listitem></itemizedlist>
</para><para>The logging of the exception information is finilized by a call to <computeroutput>log_exception_finish</computeroutput>.</para><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_exception_finish </para>
</para>
</para></description></method>
<method name="log_exception_finish" cv="= 0" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><purpose>Invoked when Unit Test Framework detects uncaught exception. </purpose><description><para>Call to this function finishes uncaught exception report. 
<para><emphasis role="bold">See Also:</emphasis><para>log_exception_start </para>
</para>
</para></description></method>
<method name="log_entry_start" cv="= 0" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="led"><paramtype><classname>log_entry_data</classname> const &amp;</paramtype><description><para>log entry attributes </para></description></parameter><parameter name="let"><paramtype>log_entry_types</paramtype><description><para>log entry type log_entry_finish </para></description></parameter><purpose>Invoked by Unit Test Framework to start new log entry. </purpose><description><para>Call to this function starts new log entry. It is followed by series of log_entry_value calls and finally call to log_entry_finish. A log entry may consist of one or more values being reported. Some of these values will be plain strings, while others can be complicated expressions in a form of "lazy" expression template lazy_ostream. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_value, log_entry_finish</para>
</para>
<note><para>call to this function may happen before any call to test_unit_start or all calls to test_unit_finish as the framework might log errors raised during global initialization/shutdown. </para>
</note>
</para></description></method>
<method name="log_entry_value" cv="= 0" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into. </para></description></parameter><parameter name="value"><paramtype>const_string</paramtype><description><para>log entry string value </para></description></parameter><purpose>Invoked by Unit Test Framework to report a log entry content. </purpose><description><para>This is one of two overloaded methods to report log entry content. This one is used to report plain string value. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, log_entry_finish </para>
</para>
</para></description></method>
<method name="log_entry_value" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="value"><paramtype>lazy_ostream const &amp;</paramtype><description><para>log entry "lazy" value </para></description></parameter><purpose>Invoked by Unit Test Framework to report a log entry content. </purpose><description><para>This is one of two overloaded methods to report log entry content. This one is used to report some complicated expression passed as an expression template lazy_ostream. In most cases default implementation provided by the framework should work as is (it just converts the lazy expression into a string. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, log_entry_finish </para>
</para>
</para></description></method>
<method name="log_entry_finish" cv="= 0" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><purpose>Invoked by Unit Test Framework to finish a log entry report. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, log_entry_start </para>
</para>
</para></description></method>
<method name="entry_context_start" cv="= 0" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="l"><paramtype>log_level</paramtype><description><para>entry log_level, to be used to fine tune the message </para></description></parameter><purpose>Invoked by Unit Test Framework to start log entry context report. </purpose><description><para>Unit Test Framework logs for failed assertions and uncaught exceptions context if one was defined by a test module. Context consists of multiple "scopes" identified by description messages assigned by the test module using BOOST_TEST_INFO/BOOST_TEST_CONTEXT statements. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_context, entry_context_finish </para>
</para>
</para></description></method>
<method name="log_entry_context" cv="= 0" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="l"><paramtype>log_level</paramtype><description><para>entry log_level, to be used to fine tune the message </para></description></parameter><parameter name="value"><paramtype>const_string</paramtype><description><para>context "scope" description </para></description></parameter><purpose>Invoked by Unit Test Framework to report log entry context "scope" description. </purpose><description><para>Each "scope" description is reported by separate call to log_entry_context. 
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, entry_context_finish </para>
</para>
</para></description></method>
<method name="entry_context_finish" cv="= 0" specifiers="virtual"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype><description><para>output stream to write a messages into </para></description></parameter><parameter name="l"><paramtype>log_level</paramtype><description><para>entry log_level, to be used to fine tune the message </para></description></parameter><purpose>Invoked by Unit Test Framework to finish log entry context report. </purpose><description><para>
<para><emphasis role="bold">See Also:</emphasis><para>log_entry_start, entry_context_context </para>
</para>
</para></description></method>
<method name="set_log_level" specifiers="virtual"><type>void</type><parameter name="new_log_level"><paramtype>log_level</paramtype></parameter><purpose>Sets the log level of the logger/formatter. </purpose><description><para>Some loggers need to manage the log level by their own. This member function let the implementation decide of that. <formalpara><title>Since Boost 1.62 </title><para/>
</formalpara>
</para></description></method>
<method name="get_log_level" cv="const" specifiers="virtual"><type>log_level</type><purpose>Returns the log level of the logger/formatter. </purpose><description><para><formalpara><title>Since Boost 1.62 </title><para/>
</formalpara>
</para></description></method>
<method name="get_default_stream_description" cv="const" specifiers="virtual"><type>std::string</type><purpose>Returns a default stream for this logger. </purpose><description><para>The returned string describes the stream as if it was passed from the command line <computeroutput>"--log_sink"</computeroutput> parameter. With that regards, <emphasis role="bold">stdout</emphasis> and <emphasis role="bold">stderr</emphasis> have special meaning indicating the standard output or error stream respectively.</para><para><formalpara><title>Since Boost 1.62 </title><para/>
</formalpara>
</para></description></method>
</method-group>
<constructor><purpose>Constructor. </purpose></constructor>
<destructor/>
</class>





</namespace>
</namespace>
</header>
<header name="boost/test/unit_test_parameters.hpp">
<para>Provides access to various Unit Test Framework runtime parameters. </para><para>Primarily for use by the framework itself </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="runtime_config">
<class name="stream_holder"><struct name="callback_cleaner"><data-member name="m_cleaner_callback"><type>boost::function&lt; void()&gt;</type></data-member>
<data-member name="m_file"><type>std::ofstream</type></data-member>
<method-group name="public member functions">
</method-group>
<constructor><parameter name="cleaner_callback"><paramtype>boost::function&lt; void()&gt;</paramtype></parameter></constructor>
<destructor/>
</struct><method-group name="public member functions">
<method name="setup"><type>void</type><parameter name="stream_name"><paramtype>const const_string &amp;</paramtype></parameter><parameter name="cleaner_callback"><paramtype>boost::function&lt; void()&gt; const &amp;</paramtype><default>boost::function&lt; void()&gt;()</default></parameter></method>
<method name="ref" cv="const"><type>std::ostream &amp;</type></method>
</method-group>
<constructor specifiers="explicit"><parameter name="default_stream"><paramtype>std::ostream &amp;</paramtype><default>std::cout</default></parameter></constructor>
</class><data-member name="btrt_auto_start_dbg"><type>std::string</type></data-member>
<data-member name="btrt_break_exec_path"><type>std::string</type></data-member>
<data-member name="btrt_build_info"><type>std::string</type></data-member>
<data-member name="btrt_catch_sys_errors"><type>std::string</type></data-member>
<data-member name="btrt_color_output"><type>std::string</type></data-member>
<data-member name="btrt_detect_fp_except"><type>std::string</type></data-member>
<data-member name="btrt_detect_mem_leaks"><type>std::string</type></data-member>
<data-member name="btrt_list_content"><type>std::string</type></data-member>
<data-member name="btrt_list_labels"><type>std::string</type></data-member>
<data-member name="btrt_log_format"><type>std::string</type></data-member>
<data-member name="btrt_log_level"><type>std::string</type></data-member>
<data-member name="btrt_log_sink"><type>std::string</type></data-member>
<data-member name="btrt_combined_logger"><type>std::string</type></data-member>
<data-member name="btrt_output_format"><type>std::string</type></data-member>
<data-member name="btrt_random_seed"><type>std::string</type></data-member>
<data-member name="btrt_report_format"><type>std::string</type></data-member>
<data-member name="btrt_report_level"><type>std::string</type></data-member>
<data-member name="btrt_report_mem_leaks"><type>std::string</type></data-member>
<data-member name="btrt_report_sink"><type>std::string</type></data-member>
<data-member name="btrt_result_code"><type>std::string</type></data-member>
<data-member name="btrt_run_filters"><type>std::string</type></data-member>
<data-member name="btrt_save_test_pattern"><type>std::string</type></data-member>
<data-member name="btrt_show_progress"><type>std::string</type></data-member>
<data-member name="btrt_use_alt_stack"><type>std::string</type></data-member>
<data-member name="btrt_wait_for_debugger"><type>std::string</type></data-member>
<data-member name="btrt_help"><type>std::string</type></data-member>
<data-member name="btrt_usage"><type>std::string</type></data-member>
<data-member name="btrt_version"><type>std::string</type></data-member>
<function name="init"><type>void</type><parameter name="argc"><paramtype>int &amp;</paramtype></parameter><parameter name="argv"><paramtype>char **</paramtype></parameter></function>
<function name="argument_store"><type>runtime::arguments_store const  &amp;</type><purpose>Access to arguments. </purpose></function>
<function name="get"><type>T const  &amp;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="parameter_name"><paramtype>runtime::cstring</paramtype></parameter></function>
<function name="has"><type>bool</type><parameter name="parameter_name"><paramtype>runtime::cstring</paramtype></parameter></function>
<function name="save_pattern"><type>bool</type><purpose>For public access. </purpose></function>
</namespace>






</namespace>
</namespace>
</header>
<header name="boost/test/utils/algorithm.hpp">
<para>Addition to STL algorithms. </para><namespace name="boost">
<namespace name="unit_test">
<namespace name="utils">
<function name="mismatch"><type>std::pair&lt; InputIter1, InputIter2 &gt;</type><template>
          <template-type-parameter name="InputIter1"/>
          <template-type-parameter name="InputIter2"/>
        </template><parameter name="first1"><paramtype>InputIter1</paramtype><description><para>- first collection begin iterator </para></description></parameter><parameter name="last1"><paramtype>InputIter1</paramtype><description><para>- first collection end iterator </para></description></parameter><parameter name="first2"><paramtype>InputIter2</paramtype><description><para>- second collection begin iterator </para></description></parameter><parameter name="last2"><paramtype>InputIter2</paramtype><description><para>- second collection end iterator </para></description></parameter><purpose>this algorithm search through two collections for first mismatch position that get returned as a pair of iterators, first pointing to the mismatch position in first collection, second iterator in second one </purpose><description><para>
</para></description></function>
<function name="mismatch"><type>std::pair&lt; InputIter1, InputIter2 &gt;</type><template>
          <template-type-parameter name="InputIter1"/>
          <template-type-parameter name="InputIter2"/>
          <template-type-parameter name="Predicate"/>
        </template><parameter name="first1"><paramtype>InputIter1</paramtype><description><para>- first collection begin iterator </para></description></parameter><parameter name="last1"><paramtype>InputIter1</paramtype><description><para>- first collection end iterator </para></description></parameter><parameter name="first2"><paramtype>InputIter2</paramtype><description><para>- second collection begin iterator </para></description></parameter><parameter name="last2"><paramtype>InputIter2</paramtype><description><para>- second collection end iterator </para></description></parameter><parameter name="pred"><paramtype>Predicate</paramtype><description><para>- predicate to be used for search </para></description></parameter><purpose>this algorithm search through two collections for first mismatch position that get returned as a pair of iterators, first pointing to the mismatch position in first collection, second iterator in second one. This algorithms uses supplied predicate for collection elements comparison </purpose><description><para>
</para></description></function>
<function name="find_first_not_of"><type>ForwardIterator1</type><template>
          <template-type-parameter name="ForwardIterator1"/>
          <template-type-parameter name="ForwardIterator2"/>
        </template><parameter name="first1"><paramtype>ForwardIterator1</paramtype><description><para>- first collection begin iterator </para></description></parameter><parameter name="last1"><paramtype>ForwardIterator1</paramtype><description><para>- first collection end iterator </para></description></parameter><parameter name="first2"><paramtype>ForwardIterator2</paramtype><description><para>- second collection begin iterator </para></description></parameter><parameter name="last2"><paramtype>ForwardIterator2</paramtype><description><para>- second collection end iterator </para></description></parameter><purpose>this algorithm search through first collection for first element that does not belong a second one </purpose><description><para>
</para></description></function>
<function name="find_first_not_of"><type>ForwardIterator1</type><template>
          <template-type-parameter name="ForwardIterator1"/>
          <template-type-parameter name="ForwardIterator2"/>
          <template-type-parameter name="Predicate"/>
        </template><parameter name="first1"><paramtype>ForwardIterator1</paramtype><description><para>- first collection begin iterator </para></description></parameter><parameter name="last1"><paramtype>ForwardIterator1</paramtype><description><para>- first collection end iterator </para></description></parameter><parameter name="first2"><paramtype>ForwardIterator2</paramtype><description><para>- second collection begin iterator </para></description></parameter><parameter name="last2"><paramtype>ForwardIterator2</paramtype><description><para>- second collection end iterator </para></description></parameter><parameter name="pred"><paramtype>Predicate</paramtype><description><para>- predicate to be used for search </para></description></parameter><purpose>this algorithm search through first collection for first element that does not satisfy binary predicate in conjunction will any element in second collection </purpose><description><para>
</para></description></function>
<function name="find_last_of"><type>BidirectionalIterator1</type><template>
          <template-type-parameter name="BidirectionalIterator1"/>
          <template-type-parameter name="ForwardIterator2"/>
        </template><parameter name="first1"><paramtype>BidirectionalIterator1</paramtype><description><para>- first collection begin iterator </para></description></parameter><parameter name="last1"><paramtype>BidirectionalIterator1</paramtype><description><para>- first collection end iterator </para></description></parameter><parameter name="first2"><paramtype>ForwardIterator2</paramtype><description><para>- second collection begin iterator </para></description></parameter><parameter name="last2"><paramtype>ForwardIterator2</paramtype><description><para>- second collection end iterator </para></description></parameter><purpose>this algorithm search through first collection for last element that belongs to a second one </purpose><description><para>
</para></description></function>
<function name="find_last_of"><type>BidirectionalIterator1</type><template>
          <template-type-parameter name="BidirectionalIterator1"/>
          <template-type-parameter name="ForwardIterator2"/>
          <template-type-parameter name="Predicate"/>
        </template><parameter name="first1"><paramtype>BidirectionalIterator1</paramtype><description><para>- first collection begin iterator </para></description></parameter><parameter name="last1"><paramtype>BidirectionalIterator1</paramtype><description><para>- first collection end iterator </para></description></parameter><parameter name="first2"><paramtype>ForwardIterator2</paramtype><description><para>- second collection begin iterator </para></description></parameter><parameter name="last2"><paramtype>ForwardIterator2</paramtype><description><para>- second collection end iterator </para></description></parameter><parameter name="pred"><paramtype>Predicate</paramtype><description><para>- predicate to be used for search </para></description></parameter><purpose>this algorithm search through first collection for last element that satisfy binary predicate in conjunction will at least one element in second collection </purpose><description><para>
</para></description></function>
<function name="find_last_not_of"><type>BidirectionalIterator1</type><template>
          <template-type-parameter name="BidirectionalIterator1"/>
          <template-type-parameter name="ForwardIterator2"/>
        </template><parameter name="first1"><paramtype>BidirectionalIterator1</paramtype><description><para>- first collection begin iterator </para></description></parameter><parameter name="last1"><paramtype>BidirectionalIterator1</paramtype><description><para>- first collection end iterator </para></description></parameter><parameter name="first2"><paramtype>ForwardIterator2</paramtype><description><para>- second collection begin iterator </para></description></parameter><parameter name="last2"><paramtype>ForwardIterator2</paramtype><description><para>- second collection end iterator </para></description></parameter><purpose>this algorithm search through first collection for last element that does not belong to a second one </purpose><description><para>
</para></description></function>
<function name="find_last_not_of"><type>BidirectionalIterator1</type><template>
          <template-type-parameter name="BidirectionalIterator1"/>
          <template-type-parameter name="ForwardIterator2"/>
          <template-type-parameter name="Predicate"/>
        </template><parameter name="first1"><paramtype>BidirectionalIterator1</paramtype><description><para>- first collection begin iterator </para></description></parameter><parameter name="last1"><paramtype>BidirectionalIterator1</paramtype><description><para>- first collection end iterator </para></description></parameter><parameter name="first2"><paramtype>ForwardIterator2</paramtype><description><para>- second collection begin iterator </para></description></parameter><parameter name="last2"><paramtype>ForwardIterator2</paramtype><description><para>- second collection end iterator </para></description></parameter><parameter name="pred"><paramtype>Predicate</paramtype><description><para>- predicate to be used for search </para></description></parameter><purpose>this algorithm search through first collection for last element that does not satisfy binary predicate in conjunction will any element in second collection </purpose><description><para>
</para></description></function>
<function name="replace_all_occurrences_of"><type>StringClass</type><template>
          <template-type-parameter name="StringClass"/>
          <template-type-parameter name="ForwardIterator"/>
        </template><parameter name="str"><paramtype>StringClass</paramtype><description><para>- string of operation </para></description></parameter><parameter name="first1"><paramtype>ForwardIterator</paramtype><description><para>- iterator to the beginning of the substrings to replace </para></description></parameter><parameter name="last1"><paramtype>ForwardIterator</paramtype><description><para>- iterator to the end of the substrings to replace </para></description></parameter><parameter name="first2"><paramtype>ForwardIterator</paramtype><description><para>- iterator to the beginning of the substrings to replace with </para></description></parameter><parameter name="last2"><paramtype>ForwardIterator</paramtype><description><para>- iterator to the end of the substrings to replace with </para></description></parameter><purpose>This algorithm replaces all occurrences of a set of substrings by another substrings. </purpose><description><para>
</para></description></function>
<function name="replace_all_occurrences_with_wildcards"><type>StringClass</type><template>
          <template-type-parameter name="StringClass"/>
          <template-type-parameter name="ForwardIterator"/>
        </template><parameter name="str"><paramtype>StringClass</paramtype><description><para>- string to transform </para></description></parameter><parameter name="it_string_to_find"><paramtype>ForwardIterator</paramtype><description><para>- iterator to the beginning of the substrings to replace </para></description></parameter><parameter name="it_string_to_find_end"><paramtype>ForwardIterator</paramtype><description><para>- iterator to the end of the substrings to replace </para></description></parameter><parameter name="it_string_to_replace"><paramtype>ForwardIterator</paramtype><description><para>- iterator to the beginning of the substrings to replace with </para></description></parameter><parameter name="it_string_to_replace_end"><paramtype>ForwardIterator</paramtype><description><para>- iterator to the end of the substrings to replace with</para></description></parameter><purpose>This algorithm replaces all occurrences of a string with basic wildcards with another (optionally containing wildcards as well). </purpose><description><para>
The wildcard is the symbol '*'. Only a unique wildcard per string is supported. The replacement string may also contain a wildcard, in which case it is considered as a placeholder to the content of the wildcard in the source string. Example:<itemizedlist>
<listitem><para>In order to replace the occurrences of <computeroutput>'time="some-variable-value"'</computeroutput> to a constant string, one may use <computeroutput>'time="*"'</computeroutput> as the string to search for, and 'time="0.0"' as the replacement string.</para>
</listitem><listitem><para>In order to replace the occurrences of 'file.cpp(XX)' per 'file.cpp:XX', where XX is a variable to keep, on may use <computeroutput>'file.cpp(*)'</computeroutput> as the string to search for, and 'file.cpp:*' as the replacement string. </para>
</listitem></itemizedlist>
</para></description></function>
</namespace>






</namespace>
</namespace>
<macro name="BOOST_TEST_BIND1ST" kind="functionlike"><macro-parameter name="F"/><macro-parameter name="A"/></macro>
</header>
<header name="boost/test/utils/is_forward_iterable.hpp">
<para>Defines the is_forward_iterable collection type trait. </para><namespace name="boost">
<namespace name="unit_test">
<struct name="bt_iterator_traits"><template>
      <template-type-parameter name="T"/>
      <template-nontype-parameter name="is_forward_iterable"><type>bool</type><default><classname alt="boost::unit_test::is_forward_iterable">is_forward_iterable</classname>&lt;T&gt;::value</default></template-nontype-parameter>
    </template><purpose>Helper structure for accessing the content of a container or an array. </purpose></struct><struct-specialization name="bt_iterator_traits"><template>
      <template-type-parameter name="T"/>
      <template-nontype-parameter name="N"><type>std::size_t</type></template-nontype-parameter>
    </template><specialization><template-arg>T [N]</template-arg><template-arg>true</template-arg></specialization><typedef name="T_const"><type>boost::add_const&lt; T &gt;::type</type></typedef>
<typedef name="const_iterator"><type>boost::add_pointer&lt; T_const &gt;::type</type></typedef>
<typedef name="value_type"><type>T</type></typedef>
<method-group name="public static functions">
<method name="begin" specifiers="static"><type>const_iterator</type><parameter name="array"><paramtype>T_const(&amp;)</paramtype></parameter></method>
<method name="end" specifiers="static"><type>const_iterator</type><parameter name="array"><paramtype>T_const(&amp;)</paramtype></parameter></method>
<method name="size" specifiers="static"><type>std::size_t</type><parameter name=""><paramtype>T_const(&amp;)</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="bt_iterator_traits"><template>
      <template-type-parameter name="T"/>
    </template><specialization><template-arg>T</template-arg><template-arg>true</template-arg></specialization><typedef name="value_type"><type>std::iterator_traits&lt; const_iterator &gt;::value_type</type></typedef>
<data-member name="const_iterator"><type>decltype(boost::declval&lt; typename boost::add_const&lt; typename boost::remove_reference&lt; T &gt;::type &gt;::type &gt;().begin()) typedef</type></data-member>
<method-group name="public member functions">
<method name="BOOST_STATIC_ASSERT"><type/><parameter name=""><paramtype>(<classname>is_forward_iterable</classname>&lt; T &gt;::value)</paramtype></parameter></method>
</method-group>
<method-group name="public static functions">
<method name="begin" specifiers="static"><type>const_iterator</type><parameter name="container"><paramtype>T const &amp;</paramtype></parameter></method>
<method name="end" specifiers="static"><type>const_iterator</type><parameter name="container"><paramtype>T const &amp;</paramtype></parameter></method>
<method name="size" specifiers="static"><type>std::size_t</type><parameter name="container"><paramtype>T const &amp;</paramtype></parameter></method>
</method-group>
<method-group name="private static functions">
<method name="size" specifiers="static"><type>std::size_t</type><parameter name="container"><paramtype>T const &amp;</paramtype></parameter><parameter name=""><paramtype>std::true_type</paramtype></parameter></method>
<method name="size" specifiers="static"><type>std::size_t</type><parameter name="container"><paramtype>T const &amp;</paramtype></parameter><parameter name=""><paramtype>std::false_type</paramtype></parameter></method>
</method-group>
</struct-specialization><struct name="is_container_forward_iterable"><template>
      <template-type-parameter name="T"/>
    </template><description><para>Indicates that a specific type implements the forward iterable concept. </para></description><enum name="@4"><enumvalue name="value"><default>= is_fwd_it_t::value</default></enumvalue></enum>
<typedef name="T_ref"><type>std::remove_reference&lt; T &gt;::type</type></typedef>
<typedef name="is_fwd_it_t"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="type"><type>mpl::bool_&lt; is_fwd_it_t::value &gt;</type></typedef>
</struct><struct name="is_forward_iterable"><template>
      <template-type-parameter name="T"/>
    </template><description><para>Indicates that a specific type implements the forward iterable concept. </para></description><enum name="@3"><enumvalue name="value"><default>= is_fwd_it_t::value</default></enumvalue></enum>
<typedef name="T_ref"><type>std::remove_reference&lt; T &gt;::type</type></typedef>
<typedef name="is_fwd_it_t"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="type"><type>mpl::bool_&lt; is_fwd_it_t::value &gt;</type></typedef>
</struct>





</namespace>
</namespace>
</header>
<header name="boost/test/utils/named_params.hpp">
<namespace name="boost">
<namespace name="nfp">
<struct name="has_param"><template>
      <template-type-parameter name="NP"/>
      <template-type-parameter name="Keyword"/>
    </template><inherit access="public">is_same&lt; NP::id, Keyword::id &gt;</inherit><purpose>has_param&lt;Params,Keyword&gt;::value is true if Params has parameter corresponding to the Keyword </purpose></struct><struct-specialization name="has_param"><template>
      <template-type-parameter name="NP"/>
      <template-type-parameter name="Rest"/>
      <template-type-parameter name="Keyword"/>
    </template><specialization><template-arg>named_parameter_combine&lt; NP</template-arg><template-arg>Rest &gt;</template-arg><template-arg>Keyword</template-arg></specialization><inherit access="public">mpl::or_&lt; is_same&lt; NP::id, Keyword::id &gt;::type, has_param&lt; Rest, Keyword &gt;::type &gt;</inherit></struct-specialization><struct name="is_named_param_pack"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">false_</inherit><purpose>is_named_param_pack&lt;T&gt;::value is true if T is parameters pack </purpose></struct><struct-specialization name="is_named_param_pack"><template>
      <template-type-parameter name="T"/>
      <template-type-parameter name="unique_id"/>
      <template-type-parameter name="RefType"/>
    </template><specialization><template-arg>named_parameter&lt; T</template-arg><template-arg>unique_id</template-arg><template-arg>RefType &gt;</template-arg></specialization><inherit access="public">true_</inherit></struct-specialization><struct-specialization name="is_named_param_pack"><template>
      <template-type-parameter name="NP"/>
      <template-type-parameter name="Rest"/>
    </template><specialization><template-arg>named_parameter_combine&lt; NP</template-arg><template-arg>Rest &gt;</template-arg></specialization><inherit access="public">true_</inherit></struct-specialization><struct name="keyword"><template>
      <template-type-parameter name="unique_id"/>
      <template-nontype-parameter name="required"><type>bool</type><default>false</default></template-nontype-parameter>
    </template><typedef name="id"><type>unique_id</type></typedef>
<method-group name="public member functions">
</method-group>
<copy-assignment cv="const"><type><classname>named_parameter</classname>&lt; T const, unique_id &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="t"><paramtype>T const &amp;</paramtype></parameter></copy-assignment>
<copy-assignment cv="const"><type><classname>named_parameter</classname>&lt; T, unique_id &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="t"><paramtype>T &amp;</paramtype></parameter></copy-assignment>
<copy-assignment cv="const"><type><classname>named_parameter</classname>&lt; char const  *, unique_id, char const  * &gt;</type><parameter name="t"><paramtype>char const *</paramtype></parameter></copy-assignment>
</struct><struct name="named_parameter"><template>
      <template-type-parameter name="T"/>
      <template-type-parameter name="unique_id"/>
      <template-type-parameter name="RefType"/>
    </template><typedef name="data_type"><type>T</type></typedef>
<typedef name="ref_type"><type>RefType</type></typedef>
<typedef name="id"><type>unique_id</type></typedef>
<method-group name="public member functions">
<method name="operator[]" cv="const"><type>ref_type</type><parameter name=""><paramtype><classname>keyword</classname>&lt; unique_id, true &gt;</paramtype></parameter></method>
<method name="operator[]" cv="const"><type>ref_type</type><parameter name=""><paramtype><classname>keyword</classname>&lt; unique_id, false &gt;</paramtype></parameter></method>
<method name="operator[]" cv="const"><type><classname>nil</classname></type><template>
          <template-type-parameter name="UnknownId"/>
        </template><parameter name=""><paramtype><classname>keyword</classname>&lt; UnknownId, false &gt;</paramtype></parameter></method>
<method name="has" cv="const"><type>bool</type><parameter name=""><paramtype><classname>keyword</classname>&lt; unique_id, false &gt;</paramtype></parameter></method>
<method name="has" cv="const"><type>bool</type><template>
          <template-type-parameter name="UnknownId"/>
        </template><parameter name=""><paramtype><classname>keyword</classname>&lt; UnknownId, false &gt;</paramtype></parameter></method>
<method name="erase" cv="const"><type>void</type><parameter name=""><paramtype><classname>keyword</classname>&lt; unique_id, false &gt;</paramtype></parameter></method>
<method name="erase" cv="const"><type>void</type><template>
          <template-type-parameter name="UnknownId"/>
        </template><parameter name=""><paramtype><classname>keyword</classname>&lt; UnknownId, false &gt;</paramtype></parameter></method>
<method name="apply_to" cv="const"><type>void</type><template>
          <template-type-parameter name="Visitor"/>
        </template><parameter name="V"><paramtype>Visitor &amp;</paramtype></parameter></method>
<method name="operator," cv="const"><type><classname>named_parameter_combine</classname>&lt; NP, <classname>named_parameter</classname>&lt; T, unique_id, RefType &gt; &gt;</type><parameter name="np"><paramtype>NP const &amp;</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit"><parameter name="v"><paramtype>ref_type</paramtype></parameter></constructor>
<constructor><parameter name="np"><paramtype><classname>named_parameter</classname> const &amp;</paramtype></parameter></constructor>
</struct><struct name="named_parameter_combine"><template>
      <template-type-parameter name="NP"/>
      <template-type-parameter name="Rest"><default><classname alt="boost::nfp::nil">nil</classname></default></template-type-parameter>
    </template><inherit access="public">Rest</inherit><typedef name="res_type"><type>NP::ref_type</type></typedef>
<typedef name="self_type"><type><classname>named_parameter_combine</classname>&lt; NP, Rest &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator[]" cv="const"><type>res_type</type><parameter name="kw"><paramtype><classname>keyword</classname>&lt; typename NP::id, true &gt;</paramtype></parameter></method>
<method name="operator[]" cv="const"><type>res_type</type><parameter name="kw"><paramtype><classname>keyword</classname>&lt; typename NP::id, false &gt;</paramtype></parameter></method>
<method name="has" cv="const"><type>bool</type><parameter name="kw"><paramtype><classname>keyword</classname>&lt; typename NP::id, false &gt;</paramtype></parameter></method>
<method name="erase" cv="const"><type>void</type><parameter name="kw"><paramtype><classname>keyword</classname>&lt; typename NP::id, false &gt;</paramtype></parameter></method>
<method name="apply_to" cv="const"><type>void</type><template>
          <template-type-parameter name="Visitor"/>
        </template><parameter name="V"><paramtype>Visitor &amp;</paramtype></parameter></method>
<method name="operator," cv="const"><type><classname>named_parameter_combine</classname>&lt; NP, <classname>named_parameter_combine</classname>&lt; NP, Rest &gt; &gt;</type><parameter name="np"><paramtype>NP const &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="np"><paramtype>NP const &amp;</paramtype></parameter><parameter name="r"><paramtype>Rest const &amp;</paramtype></parameter></constructor>
</struct><struct name="nil"><method-group name="public member functions">
<method name="conversion-operator" cv="const"><type>T const &amp;</type><template>
          <template-type-parameter name="T"/>
        </template></method>
<method name="any_cast" cv="const"><type>T</type><template>
          <template-type-parameter name="T"/>
        </template></method>
<method name="operator()"><type><classname>nil</classname></type><template>
          <template-type-parameter name="Arg1"/>
        </template><parameter name=""><paramtype>Arg1 const &amp;</paramtype></parameter></method>
<method name="operator()"><type><classname>nil</classname></type><template>
          <template-type-parameter name="Arg1"/>
          <template-type-parameter name="Arg2"/>
        </template><parameter name=""><paramtype>Arg1 const &amp;</paramtype></parameter><parameter name=""><paramtype>Arg2 const &amp;</paramtype></parameter></method>
<method name="operator()"><type><classname>nil</classname></type><template>
          <template-type-parameter name="Arg1"/>
          <template-type-parameter name="Arg2"/>
          <template-type-parameter name="Arg3"/>
        </template><parameter name=""><paramtype>Arg1 const &amp;</paramtype></parameter><parameter name=""><paramtype>Arg2 const &amp;</paramtype></parameter><parameter name=""><paramtype>Arg3 const &amp;</paramtype></parameter></method>
<method name="apply_to" cv="const"><type>void</type><template>
          <template-type-parameter name="Visitor"/>
        </template><parameter name=""><paramtype>Visitor &amp;</paramtype></parameter></method>
</method-group>
<method-group name="public static functions">
<method name="inst" specifiers="static"><type><classname>nil</classname> &amp;</type></method>
</method-group>
<method-group name="private member functions">
</method-group>
<constructor/>
</struct><struct name="param_type"><template>
      <template-type-parameter name="NP"/>
      <template-type-parameter name="Keyword"/>
      <template-type-parameter name="DefaultType"><default>void</default></template-type-parameter>
    </template><inherit access="public">mpl::if_&lt; is_same&lt; NP::id, Keyword::id &gt;::type, remove_cv&lt; NP::data_type &gt;::type, DefaultType &gt;</inherit><purpose>param_type&lt;Params,Keyword,Default&gt;::type is the type of the parameter corresponding to the Keyword (if parameter is present) or Default </purpose></struct><struct-specialization name="param_type"><template>
      <template-type-parameter name="NP"/>
      <template-type-parameter name="Rest"/>
      <template-type-parameter name="Keyword"/>
      <template-type-parameter name="DefaultType"/>
    </template><specialization><template-arg>named_parameter_combine&lt; NP</template-arg><template-arg>Rest &gt;</template-arg><template-arg>Keyword</template-arg><template-arg>DefaultType</template-arg></specialization><inherit access="public">mpl::if_&lt; is_same&lt; NP::id, Keyword::id &gt;::type, remove_cv&lt; NP::data_type &gt;::type, param_type&lt; Rest, Keyword, DefaultType &gt;::type &gt;</inherit></struct-specialization><struct name="typed_keyword"><template>
      <template-type-parameter name="T"/>
      <template-type-parameter name="unique_id"/>
      <template-nontype-parameter name="required"><type>bool</type></template-nontype-parameter>
    </template><inherit access="public">boost::nfp::keyword&lt; unique_id, required &gt;</inherit><method-group name="public member functions">
</method-group>
<copy-assignment cv="const"><type><classname>named_parameter</classname>&lt; T const, unique_id &gt;</type><parameter name="t"><paramtype>T const &amp;</paramtype></parameter></copy-assignment>
<copy-assignment cv="const"><type><classname>named_parameter</classname>&lt; T, unique_id &gt;</type><parameter name="t"><paramtype>T &amp;</paramtype></parameter></copy-assignment>
<typedef name="id"><type>unique_id</type></typedef>
</struct><struct-specialization name="typed_keyword"><template>
      <template-type-parameter name="unique_id"/>
      <template-nontype-parameter name="required"><type>bool</type></template-nontype-parameter>
    </template><specialization><template-arg>bool</template-arg><template-arg>unique_id</template-arg><template-arg>required</template-arg></specialization><inherit access="public">boost::nfp::keyword&lt; unique_id, required &gt;</inherit><inherit access="public">boost::nfp::named_parameter&lt; bool, unique_id, bool &gt;</inherit><typedef name="id"><type>unique_id</type></typedef>
<typedef name="data_type"><type>bool</type></typedef>
<typedef name="ref_type"><type>bool</type></typedef>
<method-group name="public member functions">
<method name="operator!" cv="const"><type><classname>named_parameter</classname>&lt; bool, unique_id, bool &gt;</type></method>
<method name="operator[]" cv="const"><type>ref_type</type><parameter name=""><paramtype><classname>keyword</classname>&lt; unique_id, true &gt;</paramtype></parameter></method>
<method name="operator[]" cv="const"><type>ref_type</type><parameter name=""><paramtype><classname>keyword</classname>&lt; unique_id, false &gt;</paramtype></parameter></method>
<method name="operator[]" cv="const"><type><classname>nil</classname></type><parameter name=""><paramtype><classname>keyword</classname>&lt; UnknownId, false &gt;</paramtype></parameter></method>
<method name="has" cv="const"><type>bool</type><parameter name=""><paramtype><classname>keyword</classname>&lt; unique_id, false &gt;</paramtype></parameter></method>
<method name="has" cv="const"><type>bool</type><parameter name=""><paramtype><classname>keyword</classname>&lt; UnknownId, false &gt;</paramtype></parameter></method>
<method name="erase" cv="const"><type>void</type><parameter name=""><paramtype><classname>keyword</classname>&lt; unique_id, false &gt;</paramtype></parameter></method>
<method name="erase" cv="const"><type>void</type><parameter name=""><paramtype><classname>keyword</classname>&lt; UnknownId, false &gt;</paramtype></parameter></method>
<method name="apply_to" cv="const"><type>void</type><parameter name="V"><paramtype>Visitor &amp;</paramtype></parameter></method>
<method name="operator," cv="const"><type><classname>named_parameter_combine</classname>&lt; NP, <classname>named_parameter</classname>&lt; bool, unique_id, bool &gt; &gt;</type><parameter name="np"><paramtype>NP const &amp;</paramtype></parameter></method>
</method-group>
<constructor/>
</struct-specialization><typedef name="no_params_type"><type><classname>named_parameter</classname>&lt; char, struct no_params_type_t, char &gt;</type></typedef>
<function name="opt_assign"><type>enable_if_c&lt;!<classname>has_param</classname>&lt; Params, Keyword &gt;::value, void &gt;::type</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Params"/>
          <template-type-parameter name="Keyword"/>
        </template><parameter name=""><paramtype>T &amp;</paramtype></parameter><parameter name=""><paramtype>Params const &amp;</paramtype></parameter><parameter name=""><paramtype>Keyword</paramtype></parameter></function>
<function name="opt_assign"><type>enable_if_c&lt; <classname>has_param</classname>&lt; Params, Keyword &gt;::value, void &gt;::type</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Params"/>
          <template-type-parameter name="Keyword"/>
        </template><parameter name="target"><paramtype>T &amp;</paramtype></parameter><parameter name="p"><paramtype>Params const &amp;</paramtype></parameter><parameter name="k"><paramtype>Keyword</paramtype></parameter></function>
<function name="opt_get"><type>T</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Params"/>
          <template-type-parameter name="Keyword"/>
        </template><parameter name="p"><paramtype>Params const &amp;</paramtype></parameter><parameter name="k"><paramtype>Keyword</paramtype></parameter><parameter name="default_val"><paramtype>T</paramtype></parameter></function>
<function name="opt_append"><type>enable_if_c&lt;!<classname>has_param</classname>&lt; Params, <classname>keyword</classname>&lt; typename NP::id &gt; &gt;::value, <classname>named_parameter_combine</classname>&lt; NP, Params &gt; &gt;::type</type><template>
          <template-type-parameter name="Params"/>
          <template-type-parameter name="NP"/>
        </template><parameter name="params"><paramtype>Params const &amp;</paramtype></parameter><parameter name="np"><paramtype>NP const &amp;</paramtype></parameter></function>
<function name="opt_append"><type>enable_if_c&lt; <classname>has_param</classname>&lt; Params, <classname>keyword</classname>&lt; typename NP::id &gt; &gt;::value, Params &gt;::type</type><template>
          <template-type-parameter name="Params"/>
          <template-type-parameter name="NP"/>
        </template><parameter name="params"><paramtype>Params const &amp;</paramtype></parameter><parameter name=""><paramtype>NP const &amp;</paramtype></parameter></function>
</namespace>
</namespace>
</header>
</library-reference>