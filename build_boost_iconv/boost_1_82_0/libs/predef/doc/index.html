<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="generator" content="Asciidoctor 2.0.16">
<meta name="author" content="<PERSON>">
<title>Boost.Predef</title>
<style>
@import url(https://fonts.googleapis.com/css2?family=Comfortaa&display=swap);
/*! normalize.css v2.1.2 | MIT License | git.io/normalize */
/* ========================================================================== HTML5 display definitions ========================================================================== */
/** Correct `block` display not defined in IE 8/9. */
@import url(https://fonts.googleapis.com/css2?family=IBM+Plex+Mono&display=swap);
article, aside, details, figcaption, figure, footer, header, hgroup, main, nav, section, summary { display: block; }

/** Correct `inline-block` display not defined in IE 8/9. */
audio, canvas, video { display: inline-block; }

/** Prevent modern browsers from displaying `audio` without controls. Remove excess height in iOS 5 devices. */
audio:not([controls]) { display: none; height: 0; }

/** Address `[hidden]` styling not present in IE 8/9. Hide the `template` element in IE, Safari, and Firefox < 22. */
[hidden], template { display: none; }

script { display: none !important; }

/* ========================================================================== Base ========================================================================== */
/** 1. Set default font family to sans-serif. 2. Prevent iOS text size adjust after orientation change, without disabling user zoom. */
html { font-family: sans-serif; /* 1 */ -ms-text-size-adjust: 100%; /* 2 */ -webkit-text-size-adjust: 100%; /* 2 */ }

/** Remove default margin. */
body { margin: 0; }

/* ========================================================================== Links ========================================================================== */
/** Remove the gray background color from active links in IE 10. */
a { background: transparent; }

/** Address `outline` inconsistency between Chrome and other browsers. */
a:focus { outline: thin dotted; }

/** Improve readability when focused and also mouse hovered in all browsers. */
a:active, a:hover { outline: 0; }

/* ========================================================================== Typography ========================================================================== */
/** Address variable `h1` font-size and margin within `section` and `article` contexts in Firefox 4+, Safari 5, and Chrome. */
h1 { font-size: 2em; margin: 0.67em 0; }

/** Address styling not present in IE 8/9, Safari 5, and Chrome. */
abbr[title] { border-bottom: 1px dotted; }

/** Address style set to `bolder` in Firefox 4+, Safari 5, and Chrome. */
b, strong { font-weight: bold; }

/** Address styling not present in Safari 5 and Chrome. */
dfn { font-style: italic; }

/** Address differences between Firefox and other browsers. */
hr { -moz-box-sizing: content-box; box-sizing: content-box; height: 0; }

/** Address styling not present in IE 8/9. */
mark { background: #ff0; color: #000; }

/** Correct font family set oddly in Safari 5 and Chrome. */
code, kbd, pre, samp { font-family: monospace, serif; font-size: 1em; }

/** Improve readability of pre-formatted text in all browsers. */
pre { white-space: pre-wrap; }

/** Set consistent quote types. */
q { quotes: "\201C" "\201D" "\2018" "\2019"; }

/** Address inconsistent and variable font size in all browsers. */
small { font-size: 80%; }

/** Prevent `sub` and `sup` affecting `line-height` in all browsers. */
sub, sup { font-size: 75%; line-height: 0; position: relative; vertical-align: baseline; }

sup { top: -0.5em; }

sub { bottom: -0.25em; }

/* ========================================================================== Embedded content ========================================================================== */
/** Remove border when inside `a` element in IE 8/9. */
img { border: 0; }

/** Correct overflow displayed oddly in IE 9. */
svg:not(:root) { overflow: hidden; }

/* ========================================================================== Figures ========================================================================== */
/** Address margin not present in IE 8/9 and Safari 5. */
figure { margin: 0; }

/* ========================================================================== Forms ========================================================================== */
/** Define consistent border, margin, and padding. */
fieldset { border: 1px solid #c0c0c0; margin: 0 2px; padding: 0.35em 0.625em 0.75em; }

/** 1. Correct `color` not being inherited in IE 8/9. 2. Remove padding so people aren't caught out if they zero out fieldsets. */
legend { border: 0; /* 1 */ padding: 0; /* 2 */ }

/** 1. Correct font family not being inherited in all browsers. 2. Correct font size not being inherited in all browsers. 3. Address margins set differently in Firefox 4+, Safari 5, and Chrome. */
button, input, select, textarea { font-family: inherit; /* 1 */ font-size: 100%; /* 2 */ margin: 0; /* 3 */ }

/** Address Firefox 4+ setting `line-height` on `input` using `!important` in the UA stylesheet. */
button, input { line-height: normal; }

/** Address inconsistent `text-transform` inheritance for `button` and `select`. All other form control elements do not inherit `text-transform` values. Correct `button` style inheritance in Chrome, Safari 5+, and IE 8+. Correct `select` style inheritance in Firefox 4+ and Opera. */
button, select { text-transform: none; }

/** 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio` and `video` controls. 2. Correct inability to style clickable `input` types in iOS. 3. Improve usability and consistency of cursor style between image-type `input` and others. */
button, html input[type="button"], input[type="reset"], input[type="submit"] { -webkit-appearance: button; /* 2 */ cursor: pointer; /* 3 */ }

/** Re-set default cursor for disabled elements. */
button[disabled], html input[disabled] { cursor: default; }

/** 1. Address box sizing set to `content-box` in IE 8/9. 2. Remove excess padding in IE 8/9. */
input[type="checkbox"], input[type="radio"] { box-sizing: border-box; /* 1 */ padding: 0; /* 2 */ }

/** 1. Address `appearance` set to `searchfield` in Safari 5 and Chrome. 2. Address `box-sizing` set to `border-box` in Safari 5 and Chrome (include `-moz` to future-proof). */
input[type="search"] { -webkit-appearance: textfield; /* 1 */ -moz-box-sizing: content-box; -webkit-box-sizing: content-box; /* 2 */ box-sizing: content-box; }

/** Remove inner padding and search cancel button in Safari 5 and Chrome on OS X. */
input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration { -webkit-appearance: none; }

/** Remove inner padding and border in Firefox 4+. */
button::-moz-focus-inner, input::-moz-focus-inner { border: 0; padding: 0; }

/** 1. Remove default vertical scrollbar in IE 8/9. 2. Improve readability and alignment in all browsers. */
textarea { overflow: auto; /* 1 */ vertical-align: top; /* 2 */ }

/* ========================================================================== Tables ========================================================================== */
/** Remove most spacing between table cells. */
table { border-collapse: collapse; border-spacing: 0; }

meta.foundation-mq-small { font-family: "only screen and (min-width: 768px)"; width: 768px; }

meta.foundation-mq-medium { font-family: "only screen and (min-width:1280px)"; width: 1280px; }

meta.foundation-mq-large { font-family: "only screen and (min-width:1440px)"; width: 1440px; }

*, *:before, *:after { -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; }

html, body { font-size: 100%; }

body { background: #111; color: #aaa; padding: 0; margin: 0; font-family: "Comfortaa", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", "sans-serif", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; font-weight: normal; font-style: normal; line-height: 1; position: relative; cursor: auto; }

a:hover { cursor: pointer; }

img, object, embed { max-width: 100%; height: auto; }

object, embed { height: 100%; }

img { -ms-interpolation-mode: bicubic; }

#map_canvas img, #map_canvas embed, #map_canvas object, .map_canvas img, .map_canvas embed, .map_canvas object { max-width: none !important; }

.left { float: left !important; }

.right { float: right !important; }

.text-left { text-align: left !important; }

.text-right { text-align: right !important; }

.text-center { text-align: center !important; }

.text-justify { text-align: justify !important; }

.hide { display: none; }

.antialiased { -webkit-font-smoothing: antialiased; }

img { display: inline-block; vertical-align: middle; }

textarea { height: auto; min-height: 50px; }

select { width: 100%; }

p.lead { font-size: 1.21875em; line-height: 1.6; }

.subheader, .admonitionblock td.content > .title, .audioblock > .title, .exampleblock > .title, .imageblock > .title, .listingblock > .title, .literalblock > .title, .stemblock > .title, .openblock > .title, .paragraph > .title, .quoteblock > .title, table.tableblock > .title, .verseblock > .title, .videoblock > .title, .dlist > .title, .olist > .title, .ulist > .title, .qlist > .title, .hdlist > .title { line-height: 1.45; color: #e18400; font-weight: normal; margin-top: 0; margin-bottom: 0.25em; }

/* Typography resets */
div, dl, dt, dd, ul, ol, li, h1, h2, h3, #toctitle, .sidebarblock > .content > .title, h4, h5, h6, pre, form, p, blockquote, th, td { margin: 0; padding: 0; direction: ltr; }

/* Default Link Styles */
a { color: #FA9300; text-decoration: underline; line-height: inherit; }
a:hover, a:focus { color: #e18400; }
a img { border: none; }

/* Default paragraph styles */
p { font-family: inherit; font-weight: normal; font-size: 1em; line-height: 1.6; margin-bottom: 1.25em; text-rendering: optimizeLegibility; }
p aside { font-size: 0.875em; line-height: 1.35; font-style: italic; }

/* Default header styles */
h1, h2, h3, #toctitle, .sidebarblock > .content > .title, h4, h5, h6 { font-family: "Comfortaa", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", "sans-serif", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; font-weight: 300; font-style: normal; color: #ffa92e; text-rendering: optimizeLegibility; margin-top: 1em; margin-bottom: 0.5em; line-height: 1.0125em; }
h1 small, h2 small, h3 small, #toctitle small, .sidebarblock > .content > .title small, h4 small, h5 small, h6 small { font-size: 60%; color: #ffe8c7; line-height: 0; }

h1 { font-size: 2.125em; }

h2 { font-size: 1.6875em; }

h3, #toctitle, .sidebarblock > .content > .title { font-size: 1.375em; }

h4 { font-size: 1.125em; }

h5 { font-size: 1.125em; }

h6 { font-size: 1em; }

hr { border: solid #2b2b2b; border-width: 1px 0 0; clear: both; margin: 1.25em 0 1.1875em; height: 0; }

/* Helpful Typography Defaults */
em, i { font-style: italic; line-height: inherit; }

strong, b { font-weight: bold; line-height: inherit; }

small { font-size: 60%; line-height: inherit; }

code { font-family: "IBM Plex Mono", "Droid Sans Mono", "DejaVu Sans Mono", "Monospace", monospace; font-weight: normal; color: #ffe8c7; }

/* Lists */
ul, ol, dl { font-size: 1em; line-height: 1.6; margin-bottom: 1.25em; list-style-position: outside; font-family: inherit; }

ul, ol { margin-left: 1.5em; }
ul.no-bullet, ol.no-bullet { margin-left: 1.5em; }

/* Unordered Lists */
ul li ul, ul li ol { margin-left: 1.25em; margin-bottom: 0; font-size: 1em; /* Override nested font-size change */ }
ul.square li ul, ul.circle li ul, ul.disc li ul { list-style: inherit; }
ul.square { list-style-type: square; }
ul.circle { list-style-type: circle; }
ul.disc { list-style-type: disc; }
ul.no-bullet { list-style: none; }

/* Ordered Lists */
ol li ul, ol li ol { margin-left: 1.25em; margin-bottom: 0; }

/* Definition Lists */
dl dt { margin-bottom: 0.3125em; font-weight: bold; }
dl dd { margin-bottom: 1.25em; }

/* Abbreviations */
abbr, acronym { text-transform: uppercase; font-size: 90%; color: #aaa; border-bottom: 1px dotted #ddd; cursor: help; }

abbr { text-transform: none; }

/* Blockquotes */
blockquote { margin: 0 0 1.25em; padding: 0.5625em 1.25em 0 1.1875em; border-left: 1px solid #ddd; }
blockquote cite { display: block; font-size: 0.9375em; color: #aaa; }
blockquote cite:before { content: "\2014 \0020"; }
blockquote cite a, blockquote cite a:visited { color: #aaa; }

blockquote, blockquote p { line-height: 1.6; color: #ffa92e; }

/* Microformats */
.vcard { display: inline-block; margin: 0 0 1.25em 0; border: 1px solid #ddd; padding: 0.625em 0.75em; }
.vcard li { margin: 0; display: block; }
.vcard .fn { font-weight: bold; font-size: 0.9375em; }

.vevent .summary { font-weight: bold; }
.vevent abbr { cursor: auto; text-decoration: none; font-weight: bold; border: none; padding: 0 0.0625em; }

@media only screen and (min-width: 768px) { h1, h2, h3, #toctitle, .sidebarblock > .content > .title, h4, h5, h6 { line-height: 1.2; }
  h1 { font-size: 2.75em; }
  h2 { font-size: 2.3125em; }
  h3, #toctitle, .sidebarblock > .content > .title { font-size: 1.6875em; }
  h4 { font-size: 1.4375em; } }
/* Tables */
table { background: #111; margin-bottom: 1.25em; border: solid 1px #613900; }
table thead, table tfoot { background: #2e1b00; font-weight: bold; }
table thead tr th, table thead tr td, table tfoot tr th, table tfoot tr td { padding: 0.5em 0.625em 0.625em; font-size: inherit; color: #aaa; text-align: left; }
table tr th, table tr td { padding: 0.5625em 0.625em; font-size: inherit; color: #aaa; }
table tr.even, table tr.alt, table tr:nth-of-type(even) { background: #040404; }
table thead tr th, table tfoot tr th, table tbody tr td, table tr td, table tfoot tr td { display: table-cell; line-height: 1.6; }

body { tab-size: 4; word-wrap: anywhere; -moz-osx-font-smoothing: grayscale; -webkit-font-smoothing: antialiased; }

table { word-wrap: normal; }

h1, h2, h3, #toctitle, .sidebarblock > .content > .title, h4, h5, h6 { line-height: 1.2; word-spacing: -0.05em; }
h1 strong, h2 strong, h3 strong, #toctitle strong, .sidebarblock > .content > .title strong, h4 strong, h5 strong, h6 strong { font-weight: 400; }

object, svg { display: inline-block; vertical-align: middle; }

.center { margin-left: auto; margin-right: auto; }

.stretch { width: 100%; }

.clearfix:before, .clearfix:after, .float-group:before, .float-group:after { content: " "; display: table; }
.clearfix:after, .float-group:after { clear: both; }

:not(pre).nobreak { word-wrap: normal; }
:not(pre).nowrap { white-space: nowrap; }
:not(pre).pre-wrap { white-space: pre-wrap; }

:not(pre):not([class^=L]) > code { font-size: 0.875em; font-style: normal !important; letter-spacing: 0; padding: 0.1em 0.5ex; word-spacing: -0.15em; background-color: black; border: 1px solid #111; -webkit-border-radius: 8px; border-radius: 8px; line-height: 1.45; text-rendering: optimizeSpeed; }

pre { color: #FA9300; font-family: "IBM Plex Mono", "Droid Sans Mono", "DejaVu Sans Mono", "Monospace", monospace; line-height: 1.45; text-rendering: optimizeSpeed; }
pre code, pre pre { color: inherit; font-size: inherit; line-height: inherit; }
pre > code { display: block; }

pre.nowrap, pre.nowrap pre { white-space: pre; word-wrap: normal; }

em em { font-style: normal; }

strong strong { font-weight: normal; }

.keyseq { color: #dddddd; }

kbd { font-family: "IBM Plex Mono", "Droid Sans Mono", "DejaVu Sans Mono", "Monospace", monospace; display: inline-block; color: #aaa; font-size: 0.65em; line-height: 1.45; background-color: #f7f7f7; border: 1px solid #ccc; -webkit-border-radius: 3px; border-radius: 3px; -moz-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2), 0 0 0 0.1em white inset; -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2), 0 0 0 0.1em white inset; box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2), 0 0 0 0.1em white inset; margin: 0 0.15em; padding: 0.2em 0.5em; vertical-align: middle; position: relative; top: -0.1em; white-space: nowrap; }

.keyseq kbd:first-child { margin-left: 0; }

.keyseq kbd:last-child { margin-right: 0; }

.menuseq, .menuref { color: #000; }

.menuseq b:not(.caret), .menuref { font-weight: inherit; }

.menuseq { word-spacing: -0.02em; }
.menuseq b.caret { font-size: 1.25em; line-height: 0.8; }
.menuseq i.caret { font-weight: bold; text-align: center; width: 0.45em; }

b.button:before, b.button:after { position: relative; top: -1px; font-weight: normal; }

b.button:before { content: "["; padding: 0 3px 0 2px; }

b.button:after { content: "]"; padding: 0 2px 0 3px; }

p a > code:hover { color: #ffddae; }

#header, #content, #footnotes, #footer { width: 100%; margin-left: auto; margin-right: auto; margin-top: 0; margin-bottom: 0; max-width: 62.5em; *zoom: 1; position: relative; padding-left: 0.9375em; padding-right: 0.9375em; }
#header:before, #header:after, #content:before, #content:after, #footnotes:before, #footnotes:after, #footer:before, #footer:after { content: " "; display: table; }
#header:after, #content:after, #footnotes:after, #footer:after { clear: both; }

#content { margin-top: 1.25em; }

#content:before { content: none; }

#header > h1:first-child { color: #ffa92e; margin-top: 2.25rem; margin-bottom: 0; }
#header > h1:first-child + #toc { margin-top: 8px; border-top: 1px solid #2b2b2b; }
#header > h1:only-child, body.toc2 #header > h1:nth-last-child(2) { border-bottom: 1px solid #2b2b2b; padding-bottom: 8px; }
#header .details { border-bottom: 1px solid #2b2b2b; line-height: 1.45; padding-top: 0.25em; padding-bottom: 0.25em; padding-left: 0.25em; color: #aaa; display: -ms-flexbox; display: -webkit-flex; display: flex; -ms-flex-flow: row wrap; -webkit-flex-flow: row wrap; flex-flow: row wrap; }
#header .details span:first-child { margin-left: -0.125em; }
#header .details span.email a { color: #ffa92e; }
#header .details br { display: none; }
#header .details br + span:before { content: "\00a0\2013\00a0"; }
#header .details br + span.author:before { content: "\00a0\22c5\00a0"; color: #ffa92e; }
#header .details br + span#revremark:before { content: "\00a0|\00a0"; }
#header #revnumber { text-transform: capitalize; }
#header #revnumber:after { content: "\00a0"; }

#content > h1:first-child:not([class]) { color: #ffa92e; border-bottom: 1px solid #2b2b2b; padding-bottom: 8px; margin-top: 0; padding-top: 1rem; margin-bottom: 1.25rem; }

#toc { border-bottom: 1px solid #1e1e1e; padding-bottom: 0.5em; }
#toc > ul { margin-left: 0.125em; }
#toc ul.sectlevel0 > li > a { font-style: italic; }
#toc ul.sectlevel0 ul.sectlevel1 { margin: 0.5em 0; }
#toc ul { font-family: "Comfortaa", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", "sans-serif", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; list-style-type: none; }
#toc li { line-height: 1.3334; margin-top: 0.3334em; }
#toc a { text-decoration: none; }
#toc a:active { text-decoration: underline; }

#toctitle { color: #e18400; font-size: 1.2em; }

@media only screen and (min-width: 768px) { #toctitle { font-size: 1.375em; }
  body.toc2 { padding-left: 15em; padding-right: 0; }
  #toc.toc2 { margin-top: 0 !important; background: black; position: fixed; width: 15em; left: 0; top: 0; border-right: 1px solid #1e1e1e; border-top-width: 0 !important; border-bottom-width: 0 !important; z-index: 1000; padding: 1.25em 1em; height: 100%; overflow: auto; }
  #toc.toc2 #toctitle { margin-top: 0; margin-bottom: 0.8rem; font-size: 1.2em; }
  #toc.toc2 > ul { font-size: 0.9em; margin-bottom: 0; }
  #toc.toc2 ul ul { margin-left: 0; padding-left: 1em; }
  #toc.toc2 ul.sectlevel0 ul.sectlevel1 { padding-left: 0; margin-top: 0.5em; margin-bottom: 0.5em; }
  body.toc2.toc-right { padding-left: 0; padding-right: 15em; }
  body.toc2.toc-right #toc.toc2 { border-right-width: 0; border-left: 1px solid #1e1e1e; left: auto; right: 0; } }
@media only screen and (min-width: 1280px) { body.toc2 { padding-left: 20em; padding-right: 0; }
  #toc.toc2 { width: 20em; }
  #toc.toc2 #toctitle { font-size: 1.375em; }
  #toc.toc2 > ul { font-size: 0.95em; }
  #toc.toc2 ul ul { padding-left: 1.25em; }
  body.toc2.toc-right { padding-left: 0; padding-right: 20em; } }
#content #toc { border-style: solid; border-width: 1px; border-color: black; margin-bottom: 1.25em; padding: 1.25em; background: black; -webkit-border-radius: 8px; border-radius: 8px; }
#content #toc > :first-child { margin-top: 0; }
#content #toc > :last-child { margin-bottom: 0; }

#footer { max-width: none; background: #aaa; padding: 1.25em; }

#footer-text { color: #555555; line-height: 1.44; }

#content { margin-bottom: 0.625em; }

.sect1 { padding-bottom: 0.625em; }

@media only screen and (min-width: 768px) { #content { margin-bottom: 1.25em; }
  .sect1 { padding-bottom: 1.25em; } }
.sect1:last-child { padding-bottom: 0; }

.sect1 + .sect1 { border-top: 1px solid #1e1e1e; }

#content h1 > a.anchor, h2 > a.anchor, h3 > a.anchor, #toctitle > a.anchor, .sidebarblock > .content > .title > a.anchor, h4 > a.anchor, h5 > a.anchor, h6 > a.anchor { position: absolute; z-index: 1001; width: 1.5ex; margin-left: -1.5ex; display: block; text-decoration: none !important; visibility: hidden; text-align: center; font-weight: normal; }
#content h1 > a.anchor:before, h2 > a.anchor:before, h3 > a.anchor:before, #toctitle > a.anchor:before, .sidebarblock > .content > .title > a.anchor:before, h4 > a.anchor:before, h5 > a.anchor:before, h6 > a.anchor:before { content: "\00A7"; font-size: 0.85em; display: block; padding-top: 0.1em; }
#content h1:hover > a.anchor, #content h1 > a.anchor:hover, h2:hover > a.anchor, h2 > a.anchor:hover, h3:hover > a.anchor, #toctitle:hover > a.anchor, .sidebarblock > .content > .title:hover > a.anchor, h3 > a.anchor:hover, #toctitle > a.anchor:hover, .sidebarblock > .content > .title > a.anchor:hover, h4:hover > a.anchor, h4 > a.anchor:hover, h5:hover > a.anchor, h5 > a.anchor:hover, h6:hover > a.anchor, h6 > a.anchor:hover { visibility: visible; }
#content h1 > a.link, h2 > a.link, h3 > a.link, #toctitle > a.link, .sidebarblock > .content > .title > a.link, h4 > a.link, h5 > a.link, h6 > a.link { color: #ffa92e; text-decoration: none; }
#content h1 > a.link:hover, h2 > a.link:hover, h3 > a.link:hover, #toctitle > a.link:hover, .sidebarblock > .content > .title > a.link:hover, h4 > a.link:hover, h5 > a.link:hover, h6 > a.link:hover { color: #ff9e15; }

details, .audioblock, .imageblock, .literalblock, .listingblock, .stemblock, .videoblock { margin-bottom: 1.25em; }

details > summary:first-of-type { cursor: pointer; display: list-item; outline: none; margin-bottom: 0.75em; }

.admonitionblock td.content > .title, .audioblock > .title, .exampleblock > .title, .imageblock > .title, .listingblock > .title, .literalblock > .title, .stemblock > .title, .openblock > .title, .paragraph > .title, .quoteblock > .title, table.tableblock > .title, .verseblock > .title, .videoblock > .title, .dlist > .title, .olist > .title, .ulist > .title, .qlist > .title, .hdlist > .title { text-rendering: optimizeLegibility; text-align: left; font-family: "Comfortaa", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", "sans-serif", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; font-size: 1rem; font-style: italic; }

table.tableblock.fit-content > caption.title { white-space: nowrap; width: 0; }

.paragraph.lead > p, #preamble > .sectionbody > [class="paragraph"]:first-of-type p { font-size: 1.21875em; line-height: 1.6; color: #ffa92e; }

table.tableblock #preamble > .sectionbody > [class="paragraph"]:first-of-type p { font-size: inherit; }

.admonitionblock > table { border-collapse: separate; border: 0; background: none; width: 100%; }
.admonitionblock > table td.icon { text-align: center; width: 80px; }
.admonitionblock > table td.icon img { max-width: none; }
.admonitionblock > table td.icon .title { font-weight: bold; font-family: "Comfortaa", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", "sans-serif", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; text-transform: uppercase; }
.admonitionblock > table td.content { padding-left: 1.125em; padding-right: 1.25em; border-left: 1px solid #2b2b2b; color: #aaa; word-wrap: anywhere; }
.admonitionblock > table td.content > :last-child > :last-child { margin-bottom: 0; }

.exampleblock > .content { border-style: solid; border-width: 1px; border-color: black; margin-bottom: 1.25em; padding: 1.25em; background: #111; -webkit-border-radius: 8px; border-radius: 8px; }
.exampleblock > .content > :first-child { margin-top: 0; }
.exampleblock > .content > :last-child { margin-bottom: 0; }

.sidebarblock { border-style: solid; border-width: 1px; border-color: black; margin-bottom: 1.25em; padding: 1.25em; background: black; -webkit-border-radius: 8px; border-radius: 8px; }
.sidebarblock > :first-child { margin-top: 0; }
.sidebarblock > :last-child { margin-bottom: 0; }
.sidebarblock > .content > .title { color: #e18400; margin-top: 0; text-align: center; }

.exampleblock > .content > :last-child > :last-child, .exampleblock > .content .olist > ol > li:last-child > :last-child, .exampleblock > .content .ulist > ul > li:last-child > :last-child, .exampleblock > .content .qlist > ol > li:last-child > :last-child, .sidebarblock > .content > :last-child > :last-child, .sidebarblock > .content .olist > ol > li:last-child > :last-child, .sidebarblock > .content .ulist > ul > li:last-child > :last-child, .sidebarblock > .content .qlist > ol > li:last-child > :last-child { margin-bottom: 0; }

.literalblock pre, .listingblock > .content > pre { border: 1px solid #2e1b00; -webkit-border-radius: 8px; border-radius: 8px; overflow-x: auto; padding: 1em; font-size: 0.8125em; }
@media only screen and (min-width: 768px) { .literalblock pre, .listingblock > .content > pre { font-size: 0.90625em; } }
@media only screen and (min-width: 1280px) { .literalblock pre, .listingblock > .content > pre { font-size: 1em; } }

.literalblock pre, .listingblock > .content > pre:not(.highlight), .listingblock > .content > pre[class="highlight"], .listingblock > .content > pre[class^="highlight "] { background: black; }

.literalblock.output pre { color: black; background-color: #FA9300; }

.listingblock > .content { position: relative; }

.listingblock code[data-lang]:before { display: none; content: attr(data-lang); position: absolute; font-size: 0.75em; top: 0.425rem; right: 0.5rem; line-height: 1; text-transform: uppercase; color: inherit; opacity: 0.5; }

.listingblock:hover code[data-lang]:before { display: block; }

.listingblock.terminal pre .command:before { content: attr(data-prompt); padding-right: 0.5em; color: inherit; opacity: 0.5; }

.listingblock.terminal pre .command:not([data-prompt]):before { content: "$"; }

.listingblock pre.highlightjs { padding: 0; }
.listingblock pre.highlightjs > code { padding: 1em; -webkit-border-radius: 8px; border-radius: 8px; }

.prettyprint { background: black; }

pre.prettyprint .linenums { line-height: 1.45; margin-left: 2em; }

pre.prettyprint li { background: none; list-style-type: inherit; padding-left: 0; }

pre.prettyprint li code[data-lang]:before { opacity: 1; }

pre.prettyprint li:not(:first-child) code[data-lang]:before { display: none; }

table.linenotable { border-collapse: separate; border: 0; margin-bottom: 0; background: none; }
table.linenotable td[class] { color: inherit; vertical-align: top; padding: 0; line-height: inherit; white-space: normal; }
table.linenotable td.code { padding-left: 0.75em; }
table.linenotable td.linenos { border-right: 1px solid currentColor; opacity: 0.35; padding-right: 0.5em; }

pre.pygments .lineno { border-right: 1px solid currentColor; opacity: 0.35; display: inline-block; margin-right: 0.75em; }
pre.pygments .lineno:before { content: ""; margin-right: -0.125em; }

.quoteblock { margin: 0 1em 1.25em 1.5em; display: table; }
.quoteblock:not(.excerpt) > .title { margin-left: -1.5em; margin-bottom: 0.75em; }
.quoteblock blockquote, .quoteblock p { color: #ffa92e; font-size: 1.15rem; line-height: 1.75; word-spacing: 0.1em; letter-spacing: 0; font-style: italic; text-align: justify; }
.quoteblock blockquote { margin: 0; padding: 0; border: 0; }
.quoteblock blockquote:before { content: "\201c"; float: left; font-size: 2.75em; font-weight: bold; line-height: 0.6em; margin-left: -0.6em; color: #e18400; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); }
.quoteblock blockquote > .paragraph:last-child p { margin-bottom: 0; }
.quoteblock .attribution { margin-top: 0.75em; margin-right: 0.5ex; text-align: right; }

.verseblock { margin: 0 1em 1.25em 1em; }
.verseblock pre { font-family: "Open Sans", "DejaVu Sans", sans; font-size: 1.15rem; color: #ffa92e; font-weight: 300; text-rendering: optimizeLegibility; }
.verseblock pre strong { font-weight: 400; }
.verseblock .attribution { margin-top: 1.25rem; margin-left: 0.5ex; }

.quoteblock .attribution, .verseblock .attribution { font-size: 0.9375em; line-height: 1.45; font-style: italic; }
.quoteblock .attribution br, .verseblock .attribution br { display: none; }
.quoteblock .attribution cite, .verseblock .attribution cite { display: block; letter-spacing: -0.025em; color: #aaa; }

.quoteblock.abstract blockquote:before, .quoteblock.excerpt blockquote:before, .quoteblock .quoteblock blockquote:before { display: none; }
.quoteblock.abstract blockquote, .quoteblock.abstract p, .quoteblock.excerpt blockquote, .quoteblock.excerpt p, .quoteblock .quoteblock blockquote, .quoteblock .quoteblock p { line-height: 1.6; word-spacing: 0; }
.quoteblock.abstract { margin: 0 1em 1.25em 1em; display: block; }
.quoteblock.abstract > .title { margin: 0 0 0.375em 0; font-size: 1.15em; text-align: center; }
.quoteblock.excerpt > blockquote, .quoteblock .quoteblock { padding: 0 0 0.25em 1em; border-left: 0.25em solid #2b2b2b; }
.quoteblock.excerpt, .quoteblock .quoteblock { margin-left: 0; }
.quoteblock.excerpt blockquote, .quoteblock.excerpt p, .quoteblock .quoteblock blockquote, .quoteblock .quoteblock p { color: inherit; font-size: 1.0625rem; }
.quoteblock.excerpt .attribution, .quoteblock .quoteblock .attribution { color: inherit; text-align: left; margin-right: 0; }

p.tableblock:last-child { margin-bottom: 0; }

td.tableblock > .content { margin-bottom: 1.25em; word-wrap: anywhere; }
td.tableblock > .content > :last-child { margin-bottom: -1.25em; }

table.tableblock, th.tableblock, td.tableblock { border: 0 solid #613900; }

table.grid-all > * > tr > * { border-width: 1px; }

table.grid-cols > * > tr > * { border-width: 0 1px; }

table.grid-rows > * > tr > * { border-width: 1px 0; }

table.frame-all { border-width: 1px; }

table.frame-ends { border-width: 1px 0; }

table.frame-sides { border-width: 0 1px; }

table.frame-none > colgroup + * > :first-child > *, table.frame-sides > colgroup + * > :first-child > * { border-top-width: 0; }

table.frame-none > :last-child > :last-child > *, table.frame-sides > :last-child > :last-child > * { border-bottom-width: 0; }

table.frame-none > * > tr > :first-child, table.frame-ends > * > tr > :first-child { border-left-width: 0; }

table.frame-none > * > tr > :last-child, table.frame-ends > * > tr > :last-child { border-right-width: 0; }

table.stripes-all tr, table.stripes-odd tr:nth-of-type(odd), table.stripes-even tr:nth-of-type(even), table.stripes-hover tr:hover { background: #040404; }

th.halign-left, td.halign-left { text-align: left; }

th.halign-right, td.halign-right { text-align: right; }

th.halign-center, td.halign-center { text-align: center; }

th.valign-top, td.valign-top { vertical-align: top; }

th.valign-bottom, td.valign-bottom { vertical-align: bottom; }

th.valign-middle, td.valign-middle { vertical-align: middle; }

table thead th, table tfoot th { font-weight: bold; }

tbody tr th { display: table-cell; line-height: 1.6; background: #2e1b00; }

tbody tr th, tbody tr th p, tfoot tr th, tfoot tr th p { color: #aaa; font-weight: bold; }

p.tableblock > code:only-child { background: none; padding: 0; }

p.tableblock { font-size: 1em; }

ol { margin-left: 1.75em; }

ul li ol { margin-left: 1.5em; }

dl dd { margin-left: 1.125em; }

dl dd:last-child, dl dd:last-child > :last-child { margin-bottom: 0; }

ol > li p, ul > li p, ul dd, ol dd, .olist .olist, .ulist .ulist, .ulist .olist, .olist .ulist { margin-bottom: 0.625em; }

ul.checklist, ul.none, ol.none, ul.no-bullet, ol.no-bullet, ol.unnumbered, ul.unstyled, ol.unstyled { list-style-type: none; }

ul.no-bullet, ol.no-bullet, ol.unnumbered { margin-left: 0.625em; }

ul.unstyled, ol.unstyled { margin-left: 0; }

ul.checklist { margin-left: 0.625em; }

ul.checklist li > p:first-child > .fa-square-o:first-child, ul.checklist li > p:first-child > .fa-check-square-o:first-child { width: 1.25em; font-size: 0.8em; position: relative; bottom: 0.125em; }

ul.checklist li > p:first-child > input[type="checkbox"]:first-child { margin-right: 0.25em; }

ul.inline { display: -ms-flexbox; display: -webkit-box; display: flex; -ms-flex-flow: row wrap; -webkit-flex-flow: row wrap; flex-flow: row wrap; list-style: none; margin: 0 0 0.625em -1.25em; }

ul.inline > li { margin-left: 1.25em; }

.unstyled dl dt { font-weight: normal; font-style: normal; }

ol.arabic { list-style-type: decimal; }

ol.decimal { list-style-type: decimal-leading-zero; }

ol.loweralpha { list-style-type: lower-alpha; }

ol.upperalpha { list-style-type: upper-alpha; }

ol.lowerroman { list-style-type: lower-roman; }

ol.upperroman { list-style-type: upper-roman; }

ol.lowergreek { list-style-type: lower-greek; }

.hdlist > table, .colist > table { border: 0; background: none; }
.hdlist > table > tbody > tr, .colist > table > tbody > tr { background: none; }

td.hdlist1, td.hdlist2 { vertical-align: top; padding: 0 0.625em; }

td.hdlist1 { font-weight: bold; padding-bottom: 1.25em; }

td.hdlist2 { word-wrap: anywhere; }

.literalblock + .colist, .listingblock + .colist { margin-top: -0.5em; }

.colist td:not([class]):first-child { padding: 0.4em 0.75em 0 0.75em; line-height: 1; vertical-align: top; }
.colist td:not([class]):first-child img { max-width: none; }
.colist td:not([class]):last-child { padding: 0.25em 0; }

.thumb, .th { line-height: 0; display: inline-block; border: solid 4px #fff; -webkit-box-shadow: 0 0 0 1px #ddd; box-shadow: 0 0 0 1px #ddd; }

.imageblock.left { margin: 0.25em 0.625em 1.25em 0; }
.imageblock.right { margin: 0.25em 0 1.25em 0.625em; }
.imageblock > .title { margin-bottom: 0; }
.imageblock.thumb, .imageblock.th { border-width: 6px; }
.imageblock.thumb > .title, .imageblock.th > .title { padding: 0 0.125em; }

.image.left, .image.right { margin-top: 0.25em; margin-bottom: 0.25em; display: inline-block; line-height: 0; }
.image.left { margin-right: 0.625em; }
.image.right { margin-left: 0.625em; }

a.image { text-decoration: none; display: inline-block; }
a.image object { pointer-events: none; }

sup.footnote, sup.footnoteref { font-size: 0.875em; position: static; vertical-align: super; }
sup.footnote a, sup.footnoteref a { text-decoration: none; }
sup.footnote a:active, sup.footnoteref a:active { text-decoration: underline; }

#footnotes { padding-top: 0.75em; padding-bottom: 0.75em; margin-bottom: 0.625em; }
#footnotes hr { width: 20%; min-width: 6.25em; margin: -0.25em 0 0.75em 0; border-width: 1px 0 0 0; }
#footnotes .footnote { padding: 0 0.375em 0 0.225em; line-height: 1.3334; font-size: 0.875em; margin-left: 1.2em; margin-bottom: 0.2em; }
#footnotes .footnote a:first-of-type { font-weight: bold; text-decoration: none; margin-left: -1.05em; }
#footnotes .footnote:last-of-type { margin-bottom: 0; }
#content #footnotes { margin-top: -0.625em; margin-bottom: 0; padding: 0.75em 0; }

.gist .file-data > table { border: 0; background: #fff; width: 100%; margin-bottom: 0; }
.gist .file-data > table td.line-data { width: 99%; }

div.unbreakable { page-break-inside: avoid; }

.big { font-size: larger; }

.small { font-size: smaller; }

.underline { text-decoration: underline; }

.overline { text-decoration: overline; }

.line-through { text-decoration: line-through; }

.aqua { color: #00bfbf; }

.aqua-background { background-color: #00fafa; }

.black { color: black; }

.black-background { background-color: black; }

.blue { color: #0000bf; }

.blue-background { background-color: #0000fa; }

.fuchsia { color: #bf00bf; }

.fuchsia-background { background-color: #fa00fa; }

.gray { color: #606060; }

.gray-background { background-color: #7d7d7d; }

.green { color: #006000; }

.green-background { background-color: #007d00; }

.lime { color: #00bf00; }

.lime-background { background-color: #00fa00; }

.maroon { color: #600000; }

.maroon-background { background-color: #7d0000; }

.navy { color: #000060; }

.navy-background { background-color: #00007d; }

.olive { color: #606000; }

.olive-background { background-color: #7d7d00; }

.purple { color: #600060; }

.purple-background { background-color: #7d007d; }

.red { color: #bf0000; }

.red-background { background-color: #fa0000; }

.silver { color: #909090; }

.silver-background { background-color: #bcbcbc; }

.teal { color: #006060; }

.teal-background { background-color: #007d7d; }

.white { color: #bfbfbf; }

.white-background { background-color: #fafafa; }

.yellow { color: #bfbf00; }

.yellow-background { background-color: #fafa00; }

span.icon > .fa { cursor: default; }
a span.icon > .fa { cursor: inherit; }

.admonitionblock td.icon [class^="fa icon-"] { font-size: 2.5em; text-shadow: 0px 0px 5px #c77500; cursor: default; }
.admonitionblock td.icon .icon-note:before { content: "\f05a"; color: #bc6e00; }
.admonitionblock td.icon .icon-tip:before { content: "\f0eb"; text-shadow: 1px 1px 2px rgba(155, 155, 0, 0.8); color: #111; }
.admonitionblock td.icon .icon-warning:before { content: "\f071"; color: #bf6900; }
.admonitionblock td.icon .icon-caution:before { content: "\f06d"; color: #bf3400; }
.admonitionblock td.icon .icon-important:before { content: "\f06a"; color: #bf0000; }

.conum[data-value] { display: inline-block; color: #fff !important; background-color: #aaa; -webkit-border-radius: 50%; border-radius: 50%; text-align: center; font-size: 0.75em; width: 1.67em; height: 1.67em; line-height: 1.67em; font-family: "Open Sans", "DejaVu Sans", sans-serif; font-style: normal; font-weight: bold; }
.conum[data-value] * { color: #fff !important; }
.conum[data-value] + b { display: none; }
.conum[data-value]:after { content: attr(data-value); }
pre .conum[data-value] { position: relative; top: -0.125em; }

b.conum * { color: inherit !important; }

.conum:not([data-value]):empty { display: none; }

dt, th.tableblock, td.content, div.footnote { text-rendering: optimizeLegibility; }

h1, h2, p, td.content, span.alt { letter-spacing: -0.01em; }

p strong, td.content strong, div.footnote strong { letter-spacing: -0.005em; }

strong { text-shadow: 0px 0px 2px; }

p, blockquote, dt, td.content, span.alt { font-size: 1.0625rem; }

p { margin-bottom: 1.25rem; }

.sidebarblock p, .sidebarblock dt, .sidebarblock td.content, p.tableblock { font-size: 1em; }

.exampleblock > .content { border-color: #2e1b00; -moz-box-shadow: 0 1px 4px #2e1b00; -webkit-box-shadow: 0 1px 4px #2e1b00; box-shadow: 0 1px 4px #2e1b00; }

.admonitionblock .icon .title { font-size: 2.5em; text-shadow: 0px 0px 5px #c77500; }

.caution .icon .title { color: #6610f2; }

.important .icon .title { color: #e74c3c; }

.note .icon .title { color: #3498db; }

.tip .icon .title { color: #00bc8c; }

.warning .icon .title { color: #f39c12; }

pre.pygments { background: #000 !important; }

.literalblock pre, .listingblock > .content > pre { font-size: 0.875em; }
@media only screen and (min-width: 768px) { .literalblock pre, .listingblock > .content > pre { font-size: 0.875em; } }
@media only screen and (min-width: 1280px) { .literalblock pre, .listingblock > .content > pre { font-size: 0.875em; } }

.literalblock pre, .listingblock > .content > pre { background: black !important; }

.sidebarblock { border-color: #2e1b00; }

a { color: inherit; }

#header .details { color: #c77500; }

#toc { scrollbar-width: none; scrollbar-color: #613900 transparent; }

#toc:hover { scrollbar-width: thin; scrollbar-color: #c77500 #2e1b00; }

#toc::-webkit-scrollbar { width: 0px; }

#toc::-webkit-scrollbar-thumb { background: #613900; }

#toc::-webkit-scrollbar-track { background: transparent; }

#toc:hover::-webkit-scrollbar { width: 6px; }

#toc:hover::-webkit-scrollbar-thumb { background: #c77500; }

#toc:hover::-webkit-scrollbar-track { background: #2e1b00; }

.print-only { display: none !important; }

@page { margin: 1.25cm 0.75cm; }
@media print { * { -moz-box-shadow: none !important; -webkit-box-shadow: none !important; box-shadow: none !important; text-shadow: none !important; }
  html { font-size: 80%; }
  a { color: inherit !important; text-decoration: underline !important; }
  a.bare, a[href^="#"], a[href^="mailto:"] { text-decoration: none !important; }
  a[href^="http:"]:not(.bare):after, a[href^="https:"]:not(.bare):after { content: "(" attr(href) ")"; display: inline-block; font-size: 0.875em; padding-left: 0.25em; }
  abbr[title]:after { content: " (" attr(title) ")"; }
  pre, blockquote, tr, img, object, svg { page-break-inside: avoid; }
  thead { display: table-header-group; }
  svg { max-width: 100%; }
  p, blockquote, dt, td.content { font-size: 1em; orphans: 3; widows: 3; }
  h2, h3, #toctitle, .sidebarblock > .content > .title, #toctitle, .sidebarblock > .content > .title { page-break-after: avoid; }
  #header, #content, #footnotes, #footer { max-width: none; }
  #toc, .sidebarblock, .exampleblock > .content { background: none !important; }
  #toc { border-bottom: 1px solid #2b2b2b !important; padding-bottom: 0 !important; }
  body.book #header { text-align: center; }
  body.book #header > h1:first-child { border: 0 !important; margin: 2.5em 0 1em 0; }
  body.book #header .details { border: 0 !important; display: block; padding: 0 !important; }
  body.book #header .details span:first-child { margin-left: 0 !important; }
  body.book #header .details br { display: block; }
  body.book #header .details br + span:before { content: none !important; }
  body.book #toc { border: 0 !important; text-align: left !important; padding: 0 !important; margin: 0 !important; }
  body.book #toc, body.book #preamble, body.book h1.sect0, body.book .sect1 > h2 { page-break-before: always; }
  .literalblock pre, .literalblock pre[class], .listingblock pre, .listingblock pre[class] { border: 0 !important; }
  .listingblock code[data-lang]:before { display: block; }
  #footer { padding: 0 0.9375em; }
  .hide-on-print { display: none !important; }
  .print-only { display: block !important; }
  .hide-for-print { display: none !important; }
  .show-for-print { display: inherit !important; } }
@media print, amzn-kf8 { #header > h1:first-child { margin-top: 1.25rem; }
  .sect1 { padding: 0 !important; }
  .sect1 + .sect1 { border: 0; }
  #footer { background: none; }
  #footer-text { color: #aaa; font-size: 0.9em; } }
@media amzn-kf8 { #header, #content, #footnotes, #footer { padding: 0; } }
.menubar { position: fixed; display: block; top: 0px; left: 0px; right: 0px; z-index: 99; background-color: black; border-bottom: 1px solid #2e1b00; padding: 10px; text-align: center; }

.menubar p { margin: 0px; }

.menubar image { padding-right: 1em; }

.menubar-item { padding-left: 1em; }

@media print { .menubar { display: none; } }

</style>
<style>
pre.rouge table td { padding: 5px; }
pre.rouge table pre { margin: 0; }
pre.rouge, pre.rouge .w {
  color: #586e75;
}
pre.rouge .err {
  color: #002b36;
  background-color: #dc322f;
}
pre.rouge .c, pre.rouge .ch, pre.rouge .cd, pre.rouge .cm, pre.rouge .cpf, pre.rouge .c1, pre.rouge .cs {
  color: #657b83;
}
pre.rouge .cp {
  color: #b58900;
}
pre.rouge .nt {
  color: #b58900;
}
pre.rouge .o, pre.rouge .ow {
  color: #93a1a1;
}
pre.rouge .p, pre.rouge .pi {
  color: #93a1a1;
}
pre.rouge .gi {
  color: #859900;
}
pre.rouge .gd {
  color: #dc322f;
}
pre.rouge .gh {
  color: #268bd2;
  background-color: #002b36;
  font-weight: bold;
}
pre.rouge .k, pre.rouge .kn, pre.rouge .kp, pre.rouge .kr, pre.rouge .kv {
  color: #6c71c4;
}
pre.rouge .kc {
  color: #cb4b16;
}
pre.rouge .kt {
  color: #cb4b16;
}
pre.rouge .kd {
  color: #cb4b16;
}
pre.rouge .s, pre.rouge .sb, pre.rouge .sc, pre.rouge .dl, pre.rouge .sd, pre.rouge .s2, pre.rouge .sh, pre.rouge .sx, pre.rouge .s1 {
  color: #859900;
}
pre.rouge .sa {
  color: #6c71c4;
}
pre.rouge .sr {
  color: #2aa198;
}
pre.rouge .si {
  color: #d33682;
}
pre.rouge .se {
  color: #d33682;
}
pre.rouge .nn {
  color: #b58900;
}
pre.rouge .nc {
  color: #b58900;
}
pre.rouge .no {
  color: #b58900;
}
pre.rouge .na {
  color: #268bd2;
}
pre.rouge .m, pre.rouge .mb, pre.rouge .mf, pre.rouge .mh, pre.rouge .mi, pre.rouge .il, pre.rouge .mo, pre.rouge .mx {
  color: #859900;
}
pre.rouge .ss {
  color: #859900;
}
</style>
</head>
<body class="article toc2 toc-left">
<div id="header">
<h1>Boost.Predef</h1>
<div class="details">
<span id="author" class="author">René Ferdinand Rivera Morell</span><br>
</div>
<div id="toc" class="toc2">
<div id="toctitle">Table of Contents</div>
<ul class="sectlevel1">
<li><a href="#_introduction">1. Introduction</a>
<ul class="sectlevel2">
<li><a href="#_proposal">1.1. Proposal</a></li>
<li><a href="#_current_library">1.2. Current Library</a></li>
<li><a href="#_design_choices">1.3. Design choices</a></li>
<li><a href="#_future_work">1.4. Future work</a></li>
</ul>
</li>
<li><a href="#_using_the_predefs">2. Using the predefs</a>
<ul class="sectlevel2">
<li><a href="#_the_emulated_macros">2.1. The <code>*_EMULATED</code> macros</a></li>
<li><a href="#_using_the_boost_version_number_macro">2.2. Using the <code>BOOST_VERSION_NUMBER</code> macro</a></li>
</ul>
</li>
<li><a href="#_adding_new_predefs">3. Adding new predefs</a>
<ul class="sectlevel2">
<li><a href="#_requirements_of_the_header">3.1. Requirements of the header</a></li>
<li><a href="#_structure_of_the_header">3.2. Structure of the header</a></li>
<li><a href="#_adding_exclusive_predefs">3.3. Adding exclusive predefs</a></li>
<li><a href="#_adding_an_exclusive_but_emulated_predef">3.4. Adding an exclusive but emulated predef</a></li>
<li><a href="#_using_utility_pattern_macros">3.5. Using utility pattern macros</a></li>
</ul>
</li>
<li><a href="#_reference">4. Reference</a>
<ul class="sectlevel2">
<li><a href="#_boost_arch_architecture_macros">4.1. <code>BOOST_ARCH</code> architecture macros</a>
<ul class="sectlevel3">
<li><a href="#_boost_arch_alpha">4.1.1. <code>BOOST_ARCH_ALPHA</code></a></li>
<li><a href="#_boost_arch_arm">4.1.2. <code>BOOST_ARCH_ARM</code></a></li>
<li><a href="#_boost_arch_blackfin">4.1.3. <code>BOOST_ARCH_BLACKFIN</code></a></li>
<li><a href="#_boost_arch_convex">4.1.4. <code>BOOST_ARCH_CONVEX</code></a></li>
<li><a href="#_boost_arch_e2k">4.1.5. <code>BOOST_ARCH_E2K</code></a></li>
<li><a href="#_boost_arch_ia64">4.1.6. <code>BOOST_ARCH_IA64</code></a></li>
<li><a href="#_boost_arch_m68k">4.1.7. <code>BOOST_ARCH_M68K</code></a></li>
<li><a href="#_boost_arch_mips">4.1.8. <code>BOOST_ARCH_MIPS</code></a></li>
<li><a href="#_boost_arch_parisc">4.1.9. <code>BOOST_ARCH_PARISC</code></a></li>
<li><a href="#_boost_arch_ppc">4.1.10. <code>BOOST_ARCH_PPC</code></a></li>
<li><a href="#_boost_arch_ppc_64">4.1.11. <code>BOOST_ARCH_PPC_64</code></a></li>
<li><a href="#_boost_arch_ptx">4.1.12. <code>BOOST_ARCH_PTX</code></a></li>
<li><a href="#_boost_arch_pyramid">4.1.13. <code>BOOST_ARCH_PYRAMID</code></a></li>
<li><a href="#_boost_arch_riscv">4.1.14. <code>BOOST_ARCH_RISCV</code></a></li>
<li><a href="#_boost_arch_rs6000">4.1.15. <code>BOOST_ARCH_RS6000</code></a></li>
<li><a href="#_boost_arch_sparc">4.1.16. <code>BOOST_ARCH_SPARC</code></a></li>
<li><a href="#_boost_arch_sh">4.1.17. <code>BOOST_ARCH_SH</code></a></li>
<li><a href="#_boost_arch_sys370">4.1.18. <code>BOOST_ARCH_SYS370</code></a></li>
<li><a href="#_boost_arch_sys390">4.1.19. <code>BOOST_ARCH_SYS390</code></a></li>
<li><a href="#_boost_arch_x86">4.1.20. <code>BOOST_ARCH_X86</code></a></li>
<li><a href="#_boost_arch_z">4.1.21. <code>BOOST_ARCH_Z</code></a></li>
<li><a href="#_boost_arch_x86_32">4.1.22. <code>BOOST_ARCH_X86_32</code></a></li>
<li><a href="#_boost_arch_x86_64">4.1.23. <code>BOOST_ARCH_X86_64</code></a></li>
</ul>
</li>
<li><a href="#_boost_comp_compiler_macros">4.2. <code>BOOST_COMP</code> compiler macros</a>
<ul class="sectlevel3">
<li><a href="#_boost_comp_borland">4.2.1. <code>BOOST_COMP_BORLAND</code></a></li>
<li><a href="#_boost_comp_clang">4.2.2. <code>BOOST_COMP_CLANG</code></a></li>
<li><a href="#_boost_comp_como">4.2.3. <code>BOOST_COMP_COMO</code></a></li>
<li><a href="#_boost_comp_dec">4.2.4. <code>BOOST_COMP_DEC</code></a></li>
<li><a href="#_boost_comp_diab">4.2.5. <code>BOOST_COMP_DIAB</code></a></li>
<li><a href="#_boost_comp_dmc">4.2.6. <code>BOOST_COMP_DMC</code></a></li>
<li><a href="#_boost_comp_sysc">4.2.7. <code>BOOST_COMP_SYSC</code></a></li>
<li><a href="#_boost_comp_edg">4.2.8. <code>BOOST_COMP_EDG</code></a></li>
<li><a href="#_boost_comp_path">4.2.9. <code>BOOST_COMP_PATH</code></a></li>
<li><a href="#_boost_comp_gnuc">4.2.10. <code>BOOST_COMP_GNUC</code></a></li>
<li><a href="#_boost_comp_gccxml">4.2.11. <code>BOOST_COMP_GCCXML</code></a></li>
<li><a href="#_boost_comp_ghs">4.2.12. <code>BOOST_COMP_GHS</code></a></li>
<li><a href="#_boost_comp_hpacc">4.2.13. <code>BOOST_COMP_HPACC</code></a></li>
<li><a href="#_boost_comp_iar">4.2.14. <code>BOOST_COMP_IAR</code></a></li>
<li><a href="#_boost_comp_ibm">4.2.15. <code>BOOST_COMP_IBM</code></a></li>
<li><a href="#_boost_comp_intel">4.2.16. <code>BOOST_COMP_INTEL</code></a></li>
<li><a href="#_boost_comp_kcc">4.2.17. <code>BOOST_COMP_KCC</code></a></li>
<li><a href="#_boost_comp_llvm">4.2.18. <code>BOOST_COMP_LLVM</code></a></li>
<li><a href="#_boost_comp_highc">4.2.19. <code>BOOST_COMP_HIGHC</code></a></li>
<li><a href="#_boost_comp_mwerks">4.2.20. <code>BOOST_COMP_MWERKS</code></a></li>
<li><a href="#_boost_comp_mri">4.2.21. <code>BOOST_COMP_MRI</code></a></li>
<li><a href="#_boost_comp_mpw">4.2.22. <code>BOOST_COMP_MPW</code></a></li>
<li><a href="#_boost_comp_nvcc">4.2.23. <code>BOOST_COMP_NVCC</code></a></li>
<li><a href="#_boost_comp_palm">4.2.24. <code>BOOST_COMP_PALM</code></a></li>
<li><a href="#_boost_comp_pgi">4.2.25. <code>BOOST_COMP_PGI</code></a></li>
<li><a href="#_boost_comp_sgi">4.2.26. <code>BOOST_COMP_SGI</code></a></li>
<li><a href="#_boost_comp_sunpro">4.2.27. <code>BOOST_COMP_SUNPRO</code></a></li>
<li><a href="#_boost_comp_tendra">4.2.28. <code>BOOST_COMP_TENDRA</code></a></li>
<li><a href="#_boost_comp_msvc">4.2.29. <code>BOOST_COMP_MSVC</code></a></li>
<li><a href="#_boost_comp_watcom">4.2.30. <code>BOOST_COMP_WATCOM</code></a></li>
</ul>
</li>
<li><a href="#_boost_lang_language_standards_macros">4.3. <code>BOOST_LANG</code> language standards macros</a>
<ul class="sectlevel3">
<li><a href="#_boost_lang_cuda">4.3.1. <code>BOOST_LANG_CUDA</code></a></li>
<li><a href="#_boost_lang_objc">4.3.2. <code>BOOST_LANG_OBJC</code></a></li>
<li><a href="#_boost_lang_stdc">4.3.3. <code>BOOST_LANG_STDC</code></a></li>
<li><a href="#_boost_lang_stdcpp">4.3.4. <code>BOOST_LANG_STDCPP</code></a></li>
<li><a href="#_boost_lang_stdcppcli">4.3.5. <code>BOOST_LANG_STDCPPCLI</code></a></li>
<li><a href="#_boost_lang_stdecpp">4.3.6. <code>BOOST_LANG_STDECPP</code></a></li>
</ul>
</li>
<li><a href="#_boost_lib_library_macros">4.4. <code>BOOST_LIB</code> library macros</a>
<ul class="sectlevel3">
<li><a href="#_boost_lib_c_cloudabi">4.4.1. <code>BOOST_LIB_C_CLOUDABI</code></a></li>
<li><a href="#_boost_lib_c_gnu">4.4.2. <code>BOOST_LIB_C_GNU</code></a></li>
<li><a href="#_boost_lib_c_uc">4.4.3. <code>BOOST_LIB_C_UC</code></a></li>
<li><a href="#_boost_lib_c_vms">4.4.4. <code>BOOST_LIB_C_VMS</code></a></li>
<li><a href="#_boost_lib_c_zos">4.4.5. <code>BOOST_LIB_C_ZOS</code></a></li>
<li><a href="#_boost_lib_std_cxx">4.4.6. <code>BOOST_LIB_STD_CXX</code></a></li>
<li><a href="#_boost_lib_std_dinkumware">4.4.7. <code>BOOST_LIB_STD_DINKUMWARE</code></a></li>
<li><a href="#_boost_lib_std_como">4.4.8. <code>BOOST_LIB_STD_COMO</code></a></li>
<li><a href="#_boost_lib_std_msipl">4.4.9. <code>BOOST_LIB_STD_MSIPL</code></a></li>
<li><a href="#_boost_lib_std_msl">4.4.10. <code>BOOST_LIB_STD_MSL</code></a></li>
<li><a href="#_boost_lib_std_rw">4.4.11. <code>BOOST_LIB_STD_RW</code></a></li>
<li><a href="#_boost_lib_std_sgi">4.4.12. <code>BOOST_LIB_STD_SGI</code></a></li>
<li><a href="#_boost_lib_std_gnu">4.4.13. <code>BOOST_LIB_STD_GNU</code></a></li>
<li><a href="#_boost_lib_std_stlport">4.4.14. <code>BOOST_LIB_STD_STLPORT</code></a></li>
<li><a href="#_boost_lib_std_ibm">4.4.15. <code>BOOST_LIB_STD_IBM</code></a></li>
</ul>
</li>
<li><a href="#_boost_os_operating_system_macros">4.5. <code>BOOST_OS</code> operating system macros</a>
<ul class="sectlevel3">
<li><a href="#_boost_os_aix">4.5.1. <code>BOOST_OS_AIX</code></a></li>
<li><a href="#_boost_os_amigaos">4.5.2. <code>BOOST_OS_AMIGAOS</code></a></li>
<li><a href="#_boost_os_beos">4.5.3. <code>BOOST_OS_BEOS</code></a></li>
<li><a href="#_boost_os_bsd">4.5.4. <code>BOOST_OS_BSD</code></a></li>
<li><a href="#_boost_os_cygwin">4.5.5. <code>BOOST_OS_CYGWIN</code></a></li>
<li><a href="#_boost_os_haiku">4.5.6. <code>BOOST_OS_HAIKU</code></a></li>
<li><a href="#_boost_os_hpux">4.5.7. <code>BOOST_OS_HPUX</code></a></li>
<li><a href="#_boost_os_ios">4.5.8. <code>BOOST_OS_IOS</code></a></li>
<li><a href="#_boost_os_irix">4.5.9. <code>BOOST_OS_IRIX</code></a></li>
<li><a href="#_boost_os_linux">4.5.10. <code>BOOST_OS_LINUX</code></a></li>
<li><a href="#_boost_os_macos">4.5.11. <code>BOOST_OS_MACOS</code></a></li>
<li><a href="#_boost_os_os400">4.5.12. <code>BOOST_OS_OS400</code></a></li>
<li><a href="#_boost_os_qnx">4.5.13. <code>BOOST_OS_QNX</code></a></li>
<li><a href="#_boost_os_solaris">4.5.14. <code>BOOST_OS_SOLARIS</code></a></li>
<li><a href="#_boost_os_unix">4.5.15. <code>BOOST_OS_UNIX</code></a></li>
<li><a href="#_boost_os_svr4">4.5.16. <code>BOOST_OS_SVR4</code></a></li>
<li><a href="#_boost_os_vms">4.5.17. <code>BOOST_OS_VMS</code></a></li>
<li><a href="#_boost_os_windows">4.5.18. <code>BOOST_OS_WINDOWS</code></a></li>
<li><a href="#_boost_os_bsd_bsdi">4.5.19. <code>BOOST_OS_BSD_BSDI</code></a></li>
<li><a href="#_boost_os_bsd_dragonfly">4.5.20. <code>BOOST_OS_BSD_DRAGONFLY</code></a></li>
<li><a href="#_boost_os_bsd_free">4.5.21. <code>BOOST_OS_BSD_FREE</code></a></li>
<li><a href="#_boost_os_bsd_net">4.5.22. <code>BOOST_OS_BSD_NET</code></a></li>
<li><a href="#_boost_os_bsd_open">4.5.23. <code>BOOST_OS_BSD_OPEN</code></a></li>
</ul>
</li>
<li><a href="#_boost_plat_platform_macros">4.6. <code>BOOST_PLAT</code> platform macros</a>
<ul class="sectlevel3">
<li><a href="#_boost_plat_android">4.6.1. <code>BOOST_PLAT_ANDROID</code></a></li>
<li><a href="#_boost_plat_cloudabi">4.6.2. <code>BOOST_PLAT_CLOUDABI</code></a></li>
<li><a href="#_boost_plat_ios_device">4.6.3. <code>BOOST_PLAT_IOS_DEVICE</code></a></li>
<li><a href="#_boost_plat_ios_simulator">4.6.4. <code>BOOST_PLAT_IOS_SIMULATOR</code></a></li>
<li><a href="#_boost_plat_mingw">4.6.5. <code>BOOST_PLAT_MINGW</code></a></li>
<li><a href="#_boost_plat_mingw32">4.6.6. <code>BOOST_PLAT_MINGW32</code></a></li>
<li><a href="#_boost_plat_mingw64">4.6.7. <code>BOOST_PLAT_MINGW64</code></a></li>
<li><a href="#_boost_plat_windows_desktop">4.6.8. <code>BOOST_PLAT_WINDOWS_DESKTOP</code></a></li>
<li><a href="#_boost_plat_windows_phone">4.6.9. <code>BOOST_PLAT_WINDOWS_PHONE</code></a></li>
<li><a href="#_boost_plat_windows_runtime">4.6.10. <code>BOOST_PLAT_WINDOWS_RUNTIME</code></a></li>
<li><a href="#_boost_plat_windows_server">4.6.11. <code>BOOST_PLAT_WINDOWS_SERVER</code></a></li>
<li><a href="#_boost_plat_windows_store">4.6.12. <code>BOOST_PLAT_WINDOWS_STORE</code></a></li>
<li><a href="#_boost_plat_windows_system">4.6.13. <code>BOOST_PLAT_WINDOWS_SYSTEM</code></a></li>
<li><a href="#_boost_plat_windows_uwp">4.6.14. <code>BOOST_PLAT_WINDOWS_UWP</code></a></li>
</ul>
</li>
<li><a href="#_boost_hw_hardware_macros">4.7. <code>BOOST_HW</code> hardware macros</a>
<ul class="sectlevel3">
<li><a href="#_using_the_boost_hw_simd_predefs">4.7.1. Using the <code>BOOST_HW_SIMD_*</code> predefs</a></li>
<li><a href="#_boost_hw_simd_arm">4.7.2. <code>BOOST_HW_SIMD_ARM</code></a></li>
<li><a href="#_boost_hw_simd_arm_version">4.7.3. <code>BOOST_HW_SIMD_ARM_*_VERSION</code></a></li>
<li><a href="#_boost_hw_simd_ppc">4.7.4. <code>BOOST_HW_SIMD_PPC</code></a></li>
<li><a href="#_boost_hw_simd_ppc_version">4.7.5. <code>BOOST_HW_SIMD_PPC_*_VERSION</code></a></li>
<li><a href="#_boost_hw_simd_x86">4.7.6. <code>BOOST_HW_SIMD_X86</code></a></li>
<li><a href="#_boost_hw_simd_x86_version">4.7.7. <code>BOOST_HW_SIMD_X86_*_VERSION</code></a></li>
<li><a href="#_boost_hw_simd_x86_amd">4.7.8. <code>BOOST_HW_SIMD_X86_AMD</code></a></li>
<li><a href="#_boost_hw_simd_x86_amd_version">4.7.9. <code>BOOST_HW_SIMD_X86_AMD_*_VERSION</code></a></li>
</ul>
</li>
<li><a href="#_other_macros">4.8. Other macros</a>
<ul class="sectlevel3">
<li><a href="#_boost_endian">4.8.1. <code>BOOST_ENDIAN_*</code></a></li>
<li><a href="#_boost_arch_word_bits">4.8.2. <code>BOOST_ARCH_WORD_BITS</code></a></li>
<li><a href="#_boost_predef_workaround">4.8.3. <code>BOOST_PREDEF_WORKAROUND</code></a></li>
<li><a href="#_boost_predef_tested_at">4.8.4. <code>BOOST_PREDEF_TESTED_AT</code></a></li>
</ul>
</li>
<li><a href="#_version_definition_macros">4.9. Version definition macros</a>
<ul class="sectlevel3">
<li><a href="#_boost_version_number">4.9.1. <code>BOOST_VERSION_NUMBER</code></a></li>
<li><a href="#_boost_predef_make_macros">4.9.2. <code>BOOST_PREDEF_MAKE_..</code> macros</a></li>
<li><a href="#_boost_predef_make_date_macros">4.9.3. <code>BOOST_PREDEF_MAKE_*..</code> date macros</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#_check_utilities">5. Check Utilities</a>
<ul class="sectlevel2">
<li><a href="#_predef_check_programs">5.1. <code>predef_check</code> programs</a></li>
<li><a href="#_using_with_boost_build">5.2. Using with Boost.Build</a></li>
</ul>
</li>
<li><a href="#_history">6. History</a>
<ul class="sectlevel2">
<li><a href="#_1_14_0">6.1. 1.14.0</a></li>
<li><a href="#_1_13_1">6.2. 1.13.1</a></li>
<li><a href="#_1_13">6.3. 1.13</a></li>
<li><a href="#_1_12">6.4. 1.12</a></li>
<li><a href="#_1_11">6.5. 1.11</a></li>
<li><a href="#_1_10">6.6. 1.10</a></li>
<li><a href="#_1_9">6.7. 1.9</a></li>
<li><a href="#_1_8">6.8. 1.8</a></li>
<li><a href="#_1_7">6.9. 1.7</a></li>
<li><a href="#_1_6">6.10. 1.6</a></li>
<li><a href="#_1_5">6.11. 1.5</a></li>
<li><a href="#_1_4_1">6.12. 1.4.1</a></li>
<li><a href="#_1_4">6.13. 1.4</a></li>
<li><a href="#_1_3">6.14. 1.3</a></li>
<li><a href="#_1_2">6.15. 1.2</a></li>
<li><a href="#_1_1">6.16. 1.1</a></li>
</ul>
</li>
<li><a href="#_to_do">7. To Do</a></li>
<li><a href="#_acknowledgements">8. Acknowledgements</a></li>
<li><a href="#_colophon">Colophon</a></li>
</ul>
</div>
</div>
<div id="content">
<div class="sect1">
<h2 id="_introduction"><a class="anchor" href="#_introduction"></a>1. Introduction</h2>
<div class="sectionbody">
<div class="paragraph">
<p>This library defines a set of compiler, architecture, operating system,
library, and other version numbers from the information it can gather of
C, C++, Objective C, and Objective C++ predefined macros or those defined
in generally available headers. The idea for this library grew out of a
proposal to extend the Boost Config library to provide more, and consistent,
information than the feature definitions it supports. What follows is
an edited version of that brief proposal.</p>
</div>
<div class="sect2">
<h3 id="_proposal"><a class="anchor" href="#_proposal"></a>1.1. Proposal</h3>
<div class="paragraph">
<p>The idea is to define a set of macros to identify compilers and
consistently represent their version. This includes:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>A unique BOOST_VERSION_NUMBER(major,minor,patch) macro to specify version
numbers (unfortunately, the name BOOST_VERSION is already taken to designate
the version number of boost itself).</p>
</li>
<li>
<p>A compiler identification macro, suitable for use in <code>#if</code>/<code>#elif</code> directives,
for each of the supported compilers. All macros would be defined, regardless
of the compiler. The one macro corresponding to the compiler being used would
be defined, in terms of BOOST_VERSION_NUMBER, to carry the exact compiler
version. All other macros would expand to an expression evaluating to false
(for instance, the token 0) to indicate that the corresponding compiler is not
present.</p>
</li>
<li>
<p>"Null values" could be set, for all macros, in
boost/config/select_compiler.hpp; then, for each compiler the corresponding
identification macro would be #undef and re-#defined in the corresponding
boost/compiler/(cc).hpp; however in the context of the Boost.Config
infrastructure using a "prefix" header (to be introduced) or
boost/config/suffix.hpp is a better solution.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_current_library"><a class="anchor" href="#_current_library"></a>1.2. Current Library</h3>
<div class="paragraph">
<p>The current Predef library is now, both an independent library, and expanded
in scope. It includes detection and definition of architectures, compilers,
languages, libraries, operating systems, and endianness. The key benefits are:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Version numbers that are always defined so that one doesn&#8217;t have to guard
with <code>#ifdef</code>.</p>
</li>
<li>
<p>Guard macros that can be used for <code>#ifdef</code> checks.</p>
</li>
<li>
<p>All possible definitions are included with the single <code>#include &lt;boost/predef.h&gt;</code>
so that it&#8217;s friendly to pre-compiled header usage.</p>
</li>
<li>
<p>Specific definitions can be included, ex. <code>#include &lt;boost/predef/os/windows.h&gt;</code>
for single checks.</p>
</li>
<li>
<p>Predefs can be directly used in both preprocessor and compiler expressions
for comparison to other similarly defined values.</p>
</li>
<li>
<p>The headers are usable from multiple languages, that support the C preprocessor.
In particular C++, C, Objective C, and Objective C++.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_design_choices"><a class="anchor" href="#_design_choices"></a>1.3. Design choices</h3>
<div class="paragraph">
<p>An important design choice concerns how to represent compiler versions by means
of a single integer number suitable for use in preprocessing directives. Let&#8217;s
do some calculation. The "basic" signed type for preprocessing
constant-expressions is long in C90 (and C++, as of 2006) and intmax_t in C99.
The type long shall at least be able to represent the number <code>+2 147 483 647</code>.
This means the most significant digit can only be 0, 1 or 2; and if we want all
decimal digits to be able to vary between 0 and 9, the largest range we can
consider is <code>[0, 999 999 999\</code>]. Distributing evenly, this means 3 decimal
digits for each version number part.</p>
</div>
<div class="paragraph">
<p>So we can:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>use an uneven distribution or</p>
</li>
<li>
<p>use more bits (a larger type) or</p>
</li>
<li>
<p>use 3/3/3 and have the particular compiler/platform/stdlib deal with setting
the numbers within the 3-digit range.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>It appears relatively safe to go for the first option and set it at 2/2/5. That
covers CodeWarrior and others, which are up to and past 10 for the major number.
Some compilers use the build number in lieu of the patch one; five digits
(which is already reached by VC++ 8) seems a reasonable limit even in this case.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
A 2/2/6 scheme would allow for bigger patch/build numbers at the cost,
for instance, of limiting the major version number to 20 (or, with further
constraints, to 21).
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>It might reassure the reader that this decision is actually encoded in one place
in the code; the definition of <code>BOOST_VERSION_NUMBER</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="_future_work"><a class="anchor" href="#_future_work"></a>1.4. Future work</h3>
<div class="paragraph">
<p>Even though the basics of this library are done, there is much work that can be
done:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Right now we limit the detection of libraries to known built-in predefined
macros, and to guaranteed to exist system and library headers. It might be
interesting to add something like auto-configuration predefs. This way we can
add definitions for user specific libraries and features.</p>
</li>
<li>
<p>Along with the above, it might be good to add some user control as to which
headers are included with the top-level header. Although in the current
form of the library this is less of an issue as one can include the
specific headers one needs.</p>
</li>
<li>
<p>Additionally, even if there is no auto-configure style option.. It would be
good to add optionally included headers so that user can get consistent
version number definitions for libraries they use.</p>
</li>
<li>
<p>And obviously there&#8217;s lots of work to do in reformulating the existing
Boost libraries to use the Predef library.</p>
</li>
<li>
<p>And there&#8217;s the continuing work of adding definitions for present and
future compilers, platforms, architectures, languages, and libraries.</p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_using_the_predefs"><a class="anchor" href="#_using_the_predefs"></a>2. Using the predefs</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To use the automatically defined predefs one needs to only include the
single top-level header:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#include &lt;boost/predef.h&gt;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This defines [*all] the version macros known to the library. For each
macro it will be defined to either a`<em>zero</em>`valued expression for when
the particular item is not detected, and to a`<em>positive</em>`value if it
is detected. The predef macros fall onto five categories each with
macros of a particular prefix:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>BOOST_ARCH_</code> for system/CPU architecture one is compiling for.</p>
</li>
<li>
<p><code>BOOST_COMP_</code> for the compiler one is using.</p>
</li>
<li>
<p><code>BOOST_LANG_</code> for language standards one is compiling against.</p>
</li>
<li>
<p><code>BOOST_LIB_C_</code> and <code>BOOST_LIB_STD_</code> for the C and C++ standard library
in use.</p>
</li>
<li>
<p><code>BOOST_OS_</code> for the operating system we are compiling to.</p>
</li>
<li>
<p><code>BOOST_PLAT_</code> for platforms on top of operating system or compilers.</p>
</li>
<li>
<p><code>BOOST_ENDIAN_</code> for endianness of the os and architecture combination.</p>
</li>
<li>
<p><code>BOOST_HW_</code> for hardware specific features.</p>
</li>
<li>
<p><code>BOOST_HW_SIMD</code> for SIMD (Single Instruction Multiple Data) detection.</p>
</li>
</ul>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
The detected definitions are for the configuration one is targeting
during the compile. In particular in a cross-compile this means the target
system, and not the host system.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>One uses the individual definitions to compare against specific versions
by comparing against the <code>BOOST_VERSION_NUMBER</code> macro. For example, to make
a choice based on the version of the GCC C++ compiler one would:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#include &lt;boost/predef.h&gt;
#include &lt;iostream&gt;
</span>
<span class="kt">int</span> <span class="nf">main</span><span class="p">()</span>
<span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="n">BOOST_COMP_GNUC</span> <span class="o">&gt;=</span> <span class="n">BOOST_VERSION_NUMBER</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">))</span>
    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"GCC compiler is at least version 4.0.0"</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
  <span class="k">else</span>
    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"GCC compiler is at older than version 4.0.0, or not a GCC compiler"</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
  <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>As you might notice above the <code>else</code> clause also covers the case where
the particular compiler is not detected. But one can make the test
also test for the detection. All predef definitions are defined
as a zero (0) expression when not detected. Hence one could use the
detection with a natural single condition. For example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#include &lt;boost/predef.h&gt;
#include &lt;iostream&gt;
</span>
<span class="kt">int</span> <span class="nf">main</span><span class="p">()</span>
<span class="p">{</span>
  <span class="k">if</span> <span class="p">(</span><span class="n">BOOST_COMP_GNUC</span><span class="p">)</span>
    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"This is GNU GCC!"</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
  <span class="k">else</span>
    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"Not GNU GCC."</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
  <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>And since the predef&#8217;s are preprocessor definitions the same is possible
from the preprocessor:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#include &lt;boost/predef.h&gt;
#include &lt;iostream&gt;
</span>
<span class="cp">#if BOOST_COMP_GNUC
</span>  <span class="cp">#if BOOST_COMP_GNUC &gt;= BOOST_VERSION_NUMBER(4,0,0)
</span>    <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">the_compiler</span> <span class="o">=</span> <span class="s">"GNU GCC, of at least version 4."</span>
  <span class="cp">#else
</span>    <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">the_compiler</span> <span class="o">=</span> <span class="s">"GNU GCC, less than version 4."</span>
  <span class="cp">#endif
#else
</span>  <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">the_compiler</span> <span class="o">=</span> <span class="s">"Not GNU GCC."</span>
<span class="cp">#endif
</span>
<span class="kt">int</span> <span class="nf">main</span><span class="p">()</span>
<span class="p">{</span>
  <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="n">the_compiler</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
  <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>In addition, for each version macro defined there is an
<code>*_AVAILABLE</code> macro defined only when the particular aspect is
detected. I.e. a definition equivalent to:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#if BOOST_PREDEF_ABC
</span>  <span class="cp">#define BOOST_PREDEF_ABC_AVAILABLE
#endif</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Also for each aspect there is a macro defined with a descriptive
name of what the detection is.</p>
</div>
<div class="sect2">
<h3 id="_the_emulated_macros"><a class="anchor" href="#_the_emulated_macros"></a>2.1. The <code>*_EMULATED</code> macros</h3>
<div class="paragraph">
<p>Predef definitions are guaranteed to be uniquely detected within one category.
But there are contexts under which multiple underlying detections are possible.
The well known example of this is detection of GCC and MSVC compilers which are
commonly emulated by other compilers by defining the same base macros. To
account for this detection headers are allowed to define <code>*_EMULATED</code> predefs
when this situation is detected. The emulated predefs will be set to the
version number of the detection instead of the regular predef macro for that
detection. For example MSVC will set <code>BOOST_COMP_MSVC_EMULATED</code> but not set <code>BOOST_COMP_MSVC</code>, and it will also set <code>BOOST_COMP_MSVC_AVAILABLE</code>.</p>
</div>
</div>
<div class="sect2">
<h3 id="_using_the_boost_version_number_macro"><a class="anchor" href="#_using_the_boost_version_number_macro"></a>2.2. Using the <code>BOOST_VERSION_NUMBER</code> macro</h3>
<div class="paragraph">
<p>All the predefs are defined to be a use of the <code>BOOST_VERSION_NUMBER</code> macro.
The macro takes individual major, minor, and patch value expressions:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#define BOOST_VERSION_NUMBER( major, minor, patch ) ...</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The arguments are:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Major version number, as a constant value expression in the range [0,99].</p>
</li>
<li>
<p>Minor version number, as a constant value expression in the range [0,99].</p>
</li>
<li>
<p>Patch-level version number, as a constant value expression in the
range [0,99999].</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>The ranges for each are "enforced" by the use of a modulo ("%"), i.e. truncation,
as opposed to a clamp. And hence this means that the limits are enforced only
enough to keep from having out-of-range problems. But not enough to prevent
other kinds of problems. Like exceeding the range and getting false detections,
or non-detections. It is up to the individual predefs to ensure correct
usage beyond the range guarantee.</p>
</div>
<div class="paragraph">
<p>The values for the arguments can be any preprocessor valid constant value expression.
Only constant value arithmetic is used in the definition of the <code>BOOST_VERSION_NUMBER</code>
macro and in any of the other predef macros. This means that any allowed base is
possible, i.e. binary, octal, decimal, and hexadecimal. For example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#define MY_APPLICATION_VERSION_NUMBER BOOST_VERSION_NUMBER(2,0xA,015)</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Is equivalent to:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#define MY_APPLICATION_VERSION_NUMBER BOOST_VERSION_NUMBER(2,10,13)</span></code></pre>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_adding_new_predefs"><a class="anchor" href="#_adding_new_predefs"></a>3. Adding new predefs</h2>
<div class="sectionbody">
<div class="paragraph">
<p>We know that a library like this one will be an eternal work-in-progress. And
as such we expect, and look forward to, others contributing corrections and
additions to the predefs. With that in mind we need to keep a consistent way
of defining the new predefs. Hence all current, and future, predefs follow
the same structure and requirements.</p>
</div>
<div class="sect2">
<h3 id="_requirements_of_the_header"><a class="anchor" href="#_requirements_of_the_header"></a>3.1. Requirements of the header</h3>
<div class="paragraph">
<p>All predefs need to follow a set of requirements:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>The headers must use the Boost Software License.</p>
</li>
<li>
<p>The predef must, by default, be defined as <code>BOOST_VERSION_NUMBER_NOT_AVAILABLE</code>.</p>
</li>
<li>
<p>The predef must be redefined to a non-zero value once detected.</p>
</li>
<li>
<p>The predef must, by default, be defined to <code>BOOST_VERSION_NUMBER_AVAILABLE</code>
when the predef is detected.</p>
</li>
<li>
<p>If possible, the predef will be defined as the version number detected.</p>
</li>
<li>
<p>The predef must define <code>*_AVAILABLE</code> macros as needed.</p>
</li>
<li>
<p>The predef must define a symbolic constant string name macro.</p>
</li>
<li>
<p>The predef must declare itself, after being defined, for the testing
system.</p>
</li>
<li>
<p>The predef must guarantee that it is the only one defined as detected
per category.</p>
</li>
<li>
<p>But a predef can define <code>*_EMULATED</code> macros to indicate that it was
previously detected by another header and is being "emulated" by the
system. Note that the <code>*_AVAILABLE</code> macros must still be defined in this
situation.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>And there are some extra guidelines that predef headers should follow:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>The detection should avoid including extra headers that might otherwise
not be included by default.</p>
</li>
<li>
<p>If the detection must include a header, prefer guarding it within the
detection if possible.</p>
</li>
<li>
<p>If the detection must include headers unconditionally, and has a choice
of headers to include, prefer the ones with the least impact. I.e.
include the one with the minimal set of definitions and other
dependencies.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_structure_of_the_header"><a class="anchor" href="#_structure_of_the_header"></a>3.2. Structure of the header</h3>
<div class="paragraph">
<p>For general consistency it&#8217;s suggested that new predef headers follow the
structure below, as current predef headers do. First we have the copyright
and license statement, followed by the include guard:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cm">/*
Copyright Jane Doe YYYY
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/</span>

<span class="cp">#ifndef BOOST_PREDEF_category_tag_H
#define BOOST_PREDEF_category_tag_H</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>If the detection depends on the detection of another predef you should
include those headers here.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#include &lt;boost/predef/CATEGORY_TAG/DEPENDENCY.h&gt;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Depending on how you are defining the predef you will at minimum have
to include the <code>version_number.h</code> header. But you might also want to
include the <code>make.h</code> header for the version number decomposing utility
macros:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#include &lt;boost/predef/version_number.h&gt;
#include &lt;boost/predef/make.h&gt;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The Predef library uses <a href="https://asciidoctor.org/">Asciidoctor</a> for documentation
and for the individual predefs to appear in the reference section we add
in-code documentation followed by the zero-value default definition of the
predef macro. We strongly recommend this particular placement of the
documentation and default definition because some development
environments automatically interpret this and provide in-line help
for the macro. In particular this works for the popular Eclipse IDE:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cm">/* tag::reference[]

= `BOOST_category_tag`

Documentation about what is detected.

*/</span>

<span class="cp">#define BOOST_category_tag BOOST_VERSION_NUMBER_NOT_AVAILABLE</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Next is the detection and definition of the particular predef. The
structure for this is to do a single overall check (<code>condition_a</code>) and
place further version detection inside this. The first action inside
the overall check is to &#8220;#undef BOOST_category_tag&#8221; which removes
the zero-value default. The rest is up to the you how to do the checks
for defining the version. But at minimum it must
&#8220;#define BOOST_category_tag BOOST_VERSION_NUMBER_AVAILABLE&#8221; as
the fallback to minimally indicate that the predef was detected:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#if (condition_a)
#   undef BOOST_category_tag
#   if (condition_b)
#        define BOOST_category_tag BOOST_VERSION_NUMBER(major,minor,patch)
#    else
#        define BOOST_category_tag BOOST_VERSION_NUMBER_AVAILABLE
#    endif
#endif</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>We also need to provide the <code>*_AVAILABLE</code> versions of the predef.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#if BOOST_category_tag
#   define BOOST_category_tag_AVAILABLE
#endif</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>And for convenience we also want to provide a <code>*_NAME</code> macro:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#define BOOST_category_tag_NAME "Name"</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>We close out the include guard at this point. We do whis before the test
declaration as the testing system includes the headers multiple times
to generate the needed testing code.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#endif</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The testing of the predef macros is automated to generate checks for all
the defined predefs, whether detected or not. To do this we need to
declare the predef to the test system. This declaration is empty for
regular use. And during the test programs they expand out specially
to create informational output:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#include &lt;boost/predef/detail/test.h&gt;
</span><span class="n">BOOST_PREDEF_DECLARE_TEST</span><span class="p">(</span><span class="n">BOOST_category_tag</span><span class="p">,</span><span class="n">BOOST_category_tag_NAME</span><span class="p">)</span></code></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_adding_exclusive_predefs"><a class="anchor" href="#_adding_exclusive_predefs"></a>3.3. Adding exclusive predefs</h3>
<div class="paragraph">
<p>For headers of predefs that need to be mutually exclusive in the detection
we need to add checks and definitions to detect when the predef is
detected by multiple headers.</p>
</div>
<div class="paragraph">
<p>Internally compiler, operating system, and platforms define
<code>BOOST_PREDEF_DETAIL_COMP_DETECTED</code>, <code>BOOST_PREDEF_DEFAIL_OS_DETECTED</code>, and
<code>BOOST_PREDEF_DETAIL_PLAT_DETECTED</code> respectively when the predef is first
detected. This is used to guard against multiple definition of the detection
in later included headers. In those cases the detection would instead be
written as:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#if !BOOST_PREDEF_DETAIL_category_DETECTED &amp;&amp; (condition_a)
#   undef BOOST_category_tag
#   if (condition_b)
#        define BOOST_category_tag BOOST_VERSION_NUMBER(major,minor,patch)
#    else
#        define BOOST_category_tag BOOST_VERSION_NUMBER(0,0,1)
#    endif
#endif</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>And we also include a header that defines the <code>*_DETECTED</code> macro when we have
the detection:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#if BOOST_category_tag
#   define BOOST_category_tag_AVAILABLE
#   include &lt;boost/predef/detail/CATEGORY_detected.h&gt;
#endif</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Everything else about the header is the same as the basic detection header.</p>
</div>
</div>
<div class="sect2">
<h3 id="_adding_an_exclusive_but_emulated_predef"><a class="anchor" href="#_adding_an_exclusive_but_emulated_predef"></a>3.4. Adding an exclusive but emulated predef</h3>
<div class="paragraph">
<p>Because compilers are frequently emulated by other compilers we both want
to have exclusive detection of the compiler and also provide information
that we detected the emulation of the compiler. To accomplish this we define
a local <code>*_DETECTION</code> macro for the compiler detection. And conditionally
define either the base compiler predef <code>BOOST_COMP_compiler</code> or the alternate
<code>BOOST_COMP_compiler_EMULATED</code> predef.</p>
</div>
<div class="paragraph">
<p>The initial detection would look like:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#if (condition_a)
#   if (condition_b)
#        define BOOST_COMP_tag_DETECTION BOOST_VERSION_NUMBER(major,minor,patch)
#    else
#        define BOOST_COMP_tag_DETECTION BOOST_VERSION_NUMBER_AVAILABLE
#    endif
#endif</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>And then we can conditionally define the base or emulated predefs:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#ifdef BOOST_COMP_tag_DETECTION
#   if defined(BOOST_PREDEF_DETAIL_COMP_DETECTED)
#       define BOOST_COMP_tag_EMULATED BOOST_COMP_tag_DETECTION
#   else
#       undef BOOST_COMP_tag
#       define BOOST_COMP_tag BOOST_COMP_tag_DETECTION
#   endif
#   define BOOST_category_tag_AVAILABLE
#   include &lt;boost/predef/detail/comp_detected.h&gt;
#endif</span></code></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_using_utility_pattern_macros"><a class="anchor" href="#_using_utility_pattern_macros"></a>3.5. Using utility pattern macros</h3>
<div class="paragraph">
<p>By including:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#include &lt;boost/predef/make.h&gt;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>One will get a set of utility macros to decompose common version
macros as defined by compilers. For example the EDG compiler
uses a simple 3-digit version macro (M,N,P). It can be decomposed
and defined as:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#define BOOST_COMP_EDG BOOST_PREDEF_MAKE_N_N_N(__EDG_VERSION__)</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The decomposition macros are split into three types: decimal
decomposition, hexadecimal decomposition, and date decomposition.
They follow the format of using "N" for decimal, "F" for hexadecimal,
and "Y", "M", "D" for dates.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_reference"><a class="anchor" href="#_reference"></a>4. Reference</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="_boost_arch_architecture_macros"><a class="anchor" href="#_boost_arch_architecture_macros"></a>4.1. <code>BOOST_ARCH</code> architecture macros</h3>
<div class="sect3">
<h4 id="_boost_arch_alpha"><a class="anchor" href="#_boost_arch_alpha"></a>4.1.1. <code>BOOST_ARCH_ALPHA</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/DEC_Alpha">DEC Alpha</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__alpha__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__alpha</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_ALPHA</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__alpha_ev4__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__alpha_ev5__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__alpha_ev6__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.0.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_arm"><a class="anchor" href="#_boost_arch_arm"></a>4.1.2. <code>BOOST_ARCH_ARM</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/ARM_architecture">ARM</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__TARGET_ARCH_ARM</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__TARGET_ARCH_THUMB</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_ARM</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__arm__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__arm64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__thumb__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_ARM64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__aarch64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__AARCH64EL__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_7__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_7A__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_7R__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_7M__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_6K__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_6Z__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_6KZ__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_6T2__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_5TE__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_5TEJ__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_4T__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_4__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__TARGET_ARCH_ARM</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__TARGET_ARCH_THUMB</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_ARM</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__arm64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_ARM64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__aarch64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__AARCH64EL__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_7__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">7.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_7A__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">7.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_7R__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">7.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_7M__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">7.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_6K__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_6Z__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_6KZ__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_6T2__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_5TE__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_5TEJ__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_4T__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_ARCH_4__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.0.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_blackfin"><a class="anchor" href="#_boost_arch_blackfin"></a>4.1.3. <code>BOOST_ARCH_BLACKFIN</code></h4>
<div class="paragraph">
<p>Blackfin Processors from Analog Devices.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__bfin__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__BFIN__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>bfin</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>BFIN</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_convex"><a class="anchor" href="#_boost_arch_convex"></a>4.1.4. <code>BOOST_ARCH_CONVEX</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Convex_Computer">Convex Computer</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__convex__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__convex_c1__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__convex_c2__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__convex_c32__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.2.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__convex_c34__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.4.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__convex_c38__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.8.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_e2k"><a class="anchor" href="#_boost_arch_e2k"></a>4.1.5. <code>BOOST_ARCH_E2K</code></h4>
<div class="paragraph">
<p><a href="https://en.wikipedia.org/wiki/Elbrus_2000">E2K</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__e2k__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__e2k__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.0.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_ia64"><a class="anchor" href="#_boost_arch_ia64"></a>4.1.6. <code>BOOST_ARCH_IA64</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Ia64">Intel Itanium 64</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ia64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_IA64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__IA64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ia64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_IA64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__itanium__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_m68k"><a class="anchor" href="#_boost_arch_m68k"></a>4.1.7. <code>BOOST_ARCH_M68K</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/M68k">Motorola 68k</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__m68k__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>M68000</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mc68060__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>mc68060</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mc68060</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mc68040__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>mc68040</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mc68040</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mc68030__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>mc68030</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mc68030</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mc68020__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>mc68020</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mc68020</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mc68010__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>mc68010</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mc68010</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mc68000__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0.0.1</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>mc68000</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0.0.1</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mc68000</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0.0.1</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_mips"><a class="anchor" href="#_boost_arch_mips"></a>4.1.8. <code>BOOST_ARCH_MIPS</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/MIPS_architecture">MIPS</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mips__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mips</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MIPS__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__mips</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_MIPS_ISA_MIPS1</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_R3000</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_MIPS_ISA_MIPS2</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MIPS_ISA2__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_R4000</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_MIPS_ISA_MIPS3</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MIPS_ISA3__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_MIPS_ISA_MIPS4</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MIPS_ISA4__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.0.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_parisc"><a class="anchor" href="#_boost_arch_parisc"></a>4.1.9. <code>BOOST_ARCH_PARISC</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/PA-RISC_family">HP/PA RISC</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__hppa__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__hppa</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__HPPA__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_PA_RISC1_0</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_PA_RISC1_1</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.1.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__HPPA11__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.1.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__PA7100__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.1.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_PA_RISC2_0</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__RISC2_0__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__HPPA20__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__PA8000__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.0.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_ppc"><a class="anchor" href="#_boost_arch_ppc"></a>4.1.10. <code>BOOST_ARCH_PPC</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/PowerPC">PowerPC</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__powerpc</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__powerpc__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__powerpc64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__POWERPC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ppc__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ppc64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__PPC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__PPC64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_PPC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_ARCH_PPC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_ARCH_PPC64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__PPCGECKO__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__PPCBROADWAY__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_XENON</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ppc</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ppc601__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.1.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_ARCH_601</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.1.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ppc603__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.3.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_ARCH_603</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.3.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ppc604__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.4.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ppc604__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.4.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_ppc_64"><a class="anchor" href="#_boost_arch_ppc_64"></a>4.1.11. <code>BOOST_ARCH_PPC_64</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/PowerPC">PowerPC</a> 64 bit architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__powerpc64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ppc64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__PPC64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_ARCH_PPC64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_ptx"><a class="anchor" href="#_boost_arch_ptx"></a>4.1.12. <code>BOOST_ARCH_PTX</code></h4>
<div class="paragraph">
<p><a href="https://en.wikipedia.org/wiki/Parallel_Thread_Execution">PTX</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__CUDA_ARCH__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__CUDA_ARCH__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_pyramid"><a class="anchor" href="#_boost_arch_pyramid"></a>4.1.13. <code>BOOST_ARCH_PYRAMID</code></h4>
<div class="paragraph">
<p>Pyramid 9810 architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>pyr</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_riscv"><a class="anchor" href="#_boost_arch_riscv"></a>4.1.14. <code>BOOST_ARCH_RISCV</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/RISC-V">RISC-V</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__riscv</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_rs6000"><a class="anchor" href="#_boost_arch_rs6000"></a>4.1.15. <code>BOOST_ARCH_RS6000</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/RS/6000">RS/6000</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__THW_RS6000</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_IBMR2</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_POWER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_ARCH_PWR</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_ARCH_PWR2</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_sparc"><a class="anchor" href="#_boost_arch_sparc"></a>4.1.16. <code>BOOST_ARCH_SPARC</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/SPARC">SPARC</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sparc__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sparc</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sparcv9</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">9.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sparc_v9__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">9.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sparcv8</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sparc_v8__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8.0.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_sh"><a class="anchor" href="#_boost_arch_sh"></a>4.1.17. <code>BOOST_ARCH_SH</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/SuperH">SuperH</a> architecture:
If available versions [1-5] are specifically detected.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sh__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SH5__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SH4__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sh3__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SH3__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sh2__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sh1__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.0.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_sys370"><a class="anchor" href="#_boost_arch_sys370"></a>4.1.18. <code>BOOST_ARCH_SYS370</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/System/370">System/370</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__370__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__THW_370__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_sys390"><a class="anchor" href="#_boost_arch_sys390"></a>4.1.19. <code>BOOST_ARCH_SYS390</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/System/390">System/390</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__s390__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__s390x__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_x86"><a class="anchor" href="#_boost_arch_x86"></a>4.1.20. <code>BOOST_ARCH_X86</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/X86">Intel x86</a> architecture. This is
a category to indicate that either <code>BOOST_ARCH_X86_32</code> or
<code>BOOST_ARCH_X86_64</code> is detected.</p>
</div>
</div>
<div class="sect3">
<h4 id="_boost_arch_z"><a class="anchor" href="#_boost_arch_z"></a>4.1.21. <code>BOOST_ARCH_Z</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Z/Architecture">z/Architecture</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SYSC_ZARCH__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_x86_32"><a class="anchor" href="#_boost_arch_x86_32"></a>4.1.22. <code>BOOST_ARCH_X86_32</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/X86">Intel x86</a> architecture:
If available versions [3-6] are specifically detected.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>i386</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__i386__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__i486__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__i586__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__i686__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__i386</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_IX86</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_X86_</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__THW_INTEL__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__I86__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__INTEL__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__I86__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_IX86</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__i686__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__i586__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__i486__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__i386__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.0.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_arch_x86_64"><a class="anchor" href="#_boost_arch_x86_64"></a>4.1.23. <code>BOOST_ARCH_X86_64</code></h4>
<div class="paragraph">
<p><a href="https://en.wikipedia.org/wiki/X86-64">X86-64</a> architecture.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__x86_64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__x86_64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__amd64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__amd64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_X64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="sect2">
<h3 id="_boost_comp_compiler_macros"><a class="anchor" href="#_boost_comp_compiler_macros"></a>4.2. <code>BOOST_COMP</code> compiler macros</h3>
<div class="sect3">
<h4 id="_boost_comp_borland"><a class="anchor" href="#_boost_comp_borland"></a>4.2.1. <code>BOOST_COMP_BORLAND</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/C_plus_plus_builder">Borland C++</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__BORLANDC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__CODEGEARC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__BORLANDC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__CODEGEARC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_clang"><a class="anchor" href="#_boost_comp_clang"></a>4.2.2. <code>BOOST_COMP_CLANG</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Clang">Clang</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__clang__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__clang_major__</code>, <code>__clang_minor__</code>, <code>__clang_patchlevel__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_como"><a class="anchor" href="#_boost_comp_como"></a>4.2.3. <code>BOOST_COMP_COMO</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Comeau_C/C%2B%2B">Comeau C++</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__COMO__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__COMO_VERSION__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_dec"><a class="anchor" href="#_boost_comp_dec"></a>4.2.4. <code>BOOST_COMP_DEC</code></h4>
<div class="paragraph">
<p><a href="http://www.openvms.compaq.com/openvms/brochures/deccplus/">Compaq C/C++</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__DECCXX</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__DECC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__DECCXX_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__DECC_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_diab"><a class="anchor" href="#_boost_comp_diab"></a>4.2.5. <code>BOOST_COMP_DIAB</code></h4>
<div class="paragraph">
<p><a href="http://www.windriver.com/products/development_suite/wind_river_compiler/">Diab C/C++</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__DCC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__VERSION_NUMBER__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_dmc"><a class="anchor" href="#_boost_comp_dmc"></a>4.2.6. <code>BOOST_COMP_DMC</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Digital_Mars">Digital Mars</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__DMC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__DMC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_sysc"><a class="anchor" href="#_boost_comp_sysc"></a>4.2.7. <code>BOOST_COMP_SYSC</code></h4>
<div class="paragraph">
<p><a href="http://www.dignus.com/dcxx/">Dignus Systems/C++</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SYSC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SYSC_VER__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_edg"><a class="anchor" href="#_boost_comp_edg"></a>4.2.8. <code>BOOST_COMP_EDG</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Edison_Design_Group">EDG C++ Frontend</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__EDG__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__EDG_VERSION__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_path"><a class="anchor" href="#_boost_comp_path"></a>4.2.9. <code>BOOST_COMP_PATH</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/PathScale">EKOpath</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__PATHCC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__PATHCC__</code>, <code>__PATHCC_MINOR__</code>, <code>__PATHCC_PATCHLEVEL__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_gnuc"><a class="anchor" href="#_boost_comp_gnuc"></a>4.2.10. <code>BOOST_COMP_GNUC</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/GNU_Compiler_Collection">Gnu GCC C/C++</a> compiler.
Version number available as major, minor, and patch (if available).</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__GNUC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__GNUC__</code>, <code>__GNUC_MINOR__</code>, <code>__GNUC_PATCHLEVEL__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__GNUC__</code>, <code>__GNUC_MINOR__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_gccxml"><a class="anchor" href="#_boost_comp_gccxml"></a>4.2.11. <code>BOOST_COMP_GCCXML</code></h4>
<div class="paragraph">
<p><a href="http://www.gccxml.org/">GCC XML</a> compiler.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__GCCXML__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_ghs"><a class="anchor" href="#_boost_comp_ghs"></a>4.2.12. <code>BOOST_COMP_GHS</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Green_Hills_Software">Green Hills C/C++</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ghs</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ghs__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__GHS_VERSION_NUMBER__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ghs</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_hpacc"><a class="anchor" href="#_boost_comp_hpacc"></a>4.2.13. <code>BOOST_COMP_HPACC</code></h4>
<div class="paragraph">
<p>HP aC++ compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__HP_aCC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__HP_aCC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_iar"><a class="anchor" href="#_boost_comp_iar"></a>4.2.14. <code>BOOST_COMP_IAR</code></h4>
<div class="paragraph">
<p>IAR C/C++ compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__IAR_SYSTEMS_ICC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__VER__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_ibm"><a class="anchor" href="#_boost_comp_ibm"></a>4.2.15. <code>BOOST_COMP_IBM</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/VisualAge">IBM XL C/C++</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__IBMCPP__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__xlC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__xlc__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__COMPILER_VER__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__xlC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__xlc__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__IBMCPP__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_intel"><a class="anchor" href="#_boost_comp_intel"></a>4.2.16. <code>BOOST_COMP_INTEL</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Intel_C%2B%2B">Intel C/C++</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__INTEL_COMPILER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ICL</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ICC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ECC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__INTEL_COMPILER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__INTEL_COMPILER</code> and <code>__INTEL_COMPILER_UPDATE</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
Because of an Intel mistake in the release version numbering when
<code>__INTEL_COMPILER</code> is <code>9999</code> it is detected as version 12.1.0.
</td>
</tr>
</table>
</div>
</div>
<div class="sect3">
<h4 id="_boost_comp_kcc"><a class="anchor" href="#_boost_comp_kcc"></a>4.2.17. <code>BOOST_COMP_KCC</code></h4>
<div class="paragraph">
<p>Kai C++ compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__KCC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__KCC_VERSION</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_llvm"><a class="anchor" href="#_boost_comp_llvm"></a>4.2.18. <code>BOOST_COMP_LLVM</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/LLVM">LLVM</a> compiler.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__llvm__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_highc"><a class="anchor" href="#_boost_comp_highc"></a>4.2.19. <code>BOOST_COMP_HIGHC</code></h4>
<div class="paragraph">
<p>MetaWare High C/C++ compiler.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__HIGHC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_mwerks"><a class="anchor" href="#_boost_comp_mwerks"></a>4.2.20. <code>BOOST_COMP_MWERKS</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/CodeWarrior">Metrowerks CodeWarrior</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MWERKS__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__CWCC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__CWCC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MWERKS__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P &gt;= 4.2.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MWERKS__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">9.R.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MWERKS__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">8.R.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_mri"><a class="anchor" href="#_boost_comp_mri"></a>4.2.21. <code>BOOST_COMP_MRI</code></h4>
<div class="paragraph">
<p><a href="http://www.mentor.com/microtec/">Microtec C/C++</a> compiler.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_MRI</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_mpw"><a class="anchor" href="#_boost_comp_mpw"></a>4.2.22. <code>BOOST_COMP_MPW</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Macintosh_Programmer%27s_Workshop">MPW C++</a> compiler.
Version number available as major, and minor.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MRC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>MPW_C</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>MPW_CPLUS</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MRC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_nvcc"><a class="anchor" href="#_boost_comp_nvcc"></a>4.2.23. <code>BOOST_COMP_NVCC</code></h4>
<div class="paragraph">
<p><a href="https://en.wikipedia.org/wiki/NVIDIA_CUDA_Compiler">NVCC</a> compiler.
Version number available as major, minor, and patch beginning with version 7.5.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__NVCC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__CUDACC_VER_MAJOR__</code>, <code>__CUDACC_VER_MINOR__</code>, <code>__CUDACC_VER_BUILD__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_palm"><a class="anchor" href="#_boost_comp_palm"></a>4.2.24. <code>BOOST_COMP_PALM</code></h4>
<div class="paragraph">
<p>Palm C/C++ compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_PACC_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_PACC_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_pgi"><a class="anchor" href="#_boost_comp_pgi"></a>4.2.25. <code>BOOST_COMP_PGI</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/The_Portland_Group">Portland Group C/C++</a> compiler.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__PGI</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__PGIC__</code>, <code>__PGIC_MINOR__</code>, <code>__PGIC_PATCHLEVEL__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_sgi"><a class="anchor" href="#_boost_comp_sgi"></a>4.2.26. <code>BOOST_COMP_SGI</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/MIPSpro">SGI MIPSpro</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sgi</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>sgi</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_SGI_COMPILER_VERSION</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_COMPILER_VERSION</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_sunpro"><a class="anchor" href="#_boost_comp_sunpro"></a>4.2.27. <code>BOOST_COMP_SUNPRO</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Oracle_Solaris_Studio">Oracle Solaris Studio</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SUNPRO_CC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SUNPRO_C</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SUNPRO_CC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SUNPRO_C</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SUNPRO_CC</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">VV.RR.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SUNPRO_C</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">VV.RR.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_tendra"><a class="anchor" href="#_boost_comp_tendra"></a>4.2.28. <code>BOOST_COMP_TENDRA</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/TenDRA_Compiler">TenDRA C/C++</a> compiler.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__TenDRA__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_comp_msvc"><a class="anchor" href="#_boost_comp_msvc"></a>4.2.29. <code>BOOST_COMP_MSVC</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Visual_studio">Microsoft Visual C/C++</a> compiler.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_MSC_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_MSC_FULL_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_MSC_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
</tbody>
</table>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
Release of Visual Studio after 2015 will no longer be identified
by Boost Predef as the marketing version number. Instead we use the
compiler version number directly, i.e. the _MSC_VER number.
</td>
</tr>
</table>
</div>
</div>
<div class="sect3">
<h4 id="_boost_comp_watcom"><a class="anchor" href="#_boost_comp_watcom"></a>4.2.30. <code>BOOST_COMP_WATCOM</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Watcom">Watcom C++</a> compiler.
Version number available as major, and minor.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__WATCOMC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__WATCOMC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="sect2">
<h3 id="_boost_lang_language_standards_macros"><a class="anchor" href="#_boost_lang_language_standards_macros"></a>4.3. <code>BOOST_LANG</code> language standards macros</h3>
<div class="sect3">
<h4 id="_boost_lang_cuda"><a class="anchor" href="#_boost_lang_cuda"></a>4.3.1. <code>BOOST_LANG_CUDA</code></h4>
<div class="paragraph">
<p><a href="https://en.wikipedia.org/wiki/CUDA">CUDA C/C++</a> language.
If available, the version is detected as VV.RR.P.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__CUDACC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__CUDA__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>CUDA_VERSION</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">VV.RR.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lang_objc"><a class="anchor" href="#_boost_lang_objc"></a>4.3.2. <code>BOOST_LANG_OBJC</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Objective-C">Objective-C</a> language.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__OBJC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lang_stdc"><a class="anchor" href="#_boost_lang_stdc"></a>4.3.3. <code>BOOST_LANG_STDC</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/C_(programming_language)">Standard C</a> language.
If available, the year of the standard is detected as YYYY.MM.1 from the Epoch date.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__STDC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__STDC_VERSION__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lang_stdcpp"><a class="anchor" href="#_boost_lang_stdcpp"></a>4.3.4. <code>BOOST_LANG_STDCPP</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/C%2B%2B">Standard C++</a> language.
If available, the year of the standard is detected as YYYY.MM.1 from the Epoch date.
Because of the way the C++ standardization process works the
defined version year will not be the commonly known year of the standard.
Specifically the defined versions are:</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<caption class="title">Table 1. Detected Version Number vs. C++ Standard Year</caption>
<colgroup>
<col style="width: 33.3333%;">
<col style="width: 33.3333%;">
<col style="width: 33.3334%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Detected Version Number</th>
<th class="tableblock halign-left valign-top">Standard Year</th>
<th class="tableblock halign-left valign-top">C++ Standard</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">27.11.1</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1998</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">ISO/IEC 14882:1998</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">41.3.1</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2011</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">ISO/IEC 14882:2011</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">44.2.1</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2014</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">ISO/IEC 14882:2014</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock">47.3.1</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2017</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">ISO/IEC 14882:2017</p></td>
</tr>
</tbody>
</table>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__cplusplus</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__cplusplus</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">YYYY.MM.1</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lang_stdcppcli"><a class="anchor" href="#_boost_lang_stdcppcli"></a>4.3.5. <code>BOOST_LANG_STDCPPCLI</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/C%2B%2B/CLI">Standard C++/CLI</a> language.
If available, the year of the standard is detected as YYYY.MM.1 from the Epoch date.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__cplusplus_cli</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__cplusplus_cli</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">YYYY.MM.1</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lang_stdecpp"><a class="anchor" href="#_boost_lang_stdecpp"></a>4.3.6. <code>BOOST_LANG_STDECPP</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Embedded_C%2B%2B">Standard Embedded C++</a> language.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__embedded_cplusplus</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="sect2">
<h3 id="_boost_lib_library_macros"><a class="anchor" href="#_boost_lib_library_macros"></a>4.4. <code>BOOST_LIB</code> library macros</h3>
<div class="sect3">
<h4 id="_boost_lib_c_cloudabi"><a class="anchor" href="#_boost_lib_c_cloudabi"></a>4.4.1. <code>BOOST_LIB_C_CLOUDABI</code></h4>
<div class="paragraph">
<p><a href="https://github.com/NuxiNL/cloudlibc">cloudlibc</a> - CloudABI&#8217;s standard C library.
Version number available as major, and minor.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__cloudlibc__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__cloudlibc_major__</code>, <code>__cloudlibc_minor__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_c_gnu"><a class="anchor" href="#_boost_lib_c_gnu"></a>4.4.2. <code>BOOST_LIB_C_GNU</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Glibc">GNU glibc</a> Standard C library.
Version number available as major, and minor.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__GLIBC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__GNU_LIBRARY__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__GLIBC__</code>, <code>__GLIBC_MINOR__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__GNU_LIBRARY__</code>, <code>__GNU_LIBRARY_MINOR__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_c_uc"><a class="anchor" href="#_boost_lib_c_uc"></a>4.4.3. <code>BOOST_LIB_C_UC</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Uclibc">uClibc</a> Standard C library.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__UCLIBC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__UCLIBC_MAJOR__</code>, <code>__UCLIBC_MINOR__</code>, <code>__UCLIBC_SUBLEVEL__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_c_vms"><a class="anchor" href="#_boost_lib_c_vms"></a>4.4.4. <code>BOOST_LIB_C_VMS</code></h4>
<div class="paragraph">
<p>VMS libc Standard C library.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__CRTL_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__CRTL_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_c_zos"><a class="anchor" href="#_boost_lib_c_zos"></a>4.4.5. <code>BOOST_LIB_C_ZOS</code></h4>
<div class="paragraph">
<p>z/OS libc Standard C library.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__LIBREL__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__LIBREL__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__TARGET_LIB__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_std_cxx"><a class="anchor" href="#_boost_lib_std_cxx"></a>4.4.6. <code>BOOST_LIB_STD_CXX</code></h4>
<div class="paragraph">
<p><a href="http://libcxx.llvm.org/">libc++</a> C++ Standard Library.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_LIBCPP_VERSION</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_LIBCPP_VERSION</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.0.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_std_dinkumware"><a class="anchor" href="#_boost_lib_std_dinkumware"></a>4.4.7. <code>BOOST_LIB_STD_DINKUMWARE</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Dinkumware">Dinkumware</a> Standard C++ Library.
If available version number as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_YVALS</code>, <code>__IBMCPP__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_CPPLIB_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_CPPLIB_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_std_como"><a class="anchor" href="#_boost_lib_std_como"></a>4.4.8. <code>BOOST_LIB_STD_COMO</code></h4>
<div class="paragraph">
<p><a href="http://www.comeaucomputing.com/libcomo/">Comeau Computing</a> Standard C++ Library.
Version number available as major.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__LIBCOMO__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__LIBCOMO_VERSION__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.0.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_std_msipl"><a class="anchor" href="#_boost_lib_std_msipl"></a>4.4.9. <code>BOOST_LIB_STD_MSIPL</code></h4>
<div class="paragraph">
<p><a href="http://modena.us/">Modena Software Lib++</a> Standard C++ Library.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>MSIPL_COMPILE_H</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MSIPL_COMPILE_H</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_std_msl"><a class="anchor" href="#_boost_lib_std_msl"></a>4.4.10. <code>BOOST_LIB_STD_MSL</code></h4>
<div class="paragraph">
<p><a href="http://www.freescale.com/">Metrowerks</a> Standard C++ Library.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MSL_CPP__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MSL__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MSL_CPP__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MSL__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_std_rw"><a class="anchor" href="#_boost_lib_std_rw"></a>4.4.11. <code>BOOST_LIB_STD_RW</code></h4>
<div class="paragraph">
<p><a href="http://stdcxx.apache.org/">Roguewave</a> Standard C++ library.
If available version number as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__STD_RWCOMPILER_H__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_RWSTD_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_RWSTD_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_std_sgi"><a class="anchor" href="#_boost_lib_std_sgi"></a>4.4.12. <code>BOOST_LIB_STD_SGI</code></h4>
<div class="paragraph">
<p><a href="http://www.sgi.com/tech/stl/">SGI</a> Standard C++ library.
If available version number as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__STL_CONFIG_H</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SGI_STL</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_std_gnu"><a class="anchor" href="#_boost_lib_std_gnu"></a>4.4.13. <code>BOOST_LIB_STD_GNU</code></h4>
<div class="paragraph">
<p><a href="https://gcc.gnu.org/onlinedocs/libstdc%2b%2b/">GNU libstdc++</a> Standard C++ library.
Version number available as year (from 1970), month, and day.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__GLIBCXX__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__GLIBCPP__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__GLIBCXX__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__GLIBCPP__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_std_stlport"><a class="anchor" href="#_boost_lib_std_stlport"></a>4.4.14. <code>BOOST_LIB_STD_STLPORT</code></h4>
<div class="paragraph">
<p><a href="http://sourceforge.net/projects/stlport/">STLport Standard C++</a> library.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SGI_STL_PORT</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_STLPORT_VERSION</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_STLPORT_MAJOR</code>, <code>_STLPORT_MINOR</code>, <code>_STLPORT_PATCHLEVEL</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_STLPORT_VERSION</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SGI_STL_PORT</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_lib_std_ibm"><a class="anchor" href="#_boost_lib_std_ibm"></a>4.4.15. <code>BOOST_LIB_STD_IBM</code></h4>
<div class="paragraph">
<p><a href="http://www.ibm.com/software/awdtools/xlcpp/">IBM VACPP Standard C++</a> library.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__IBMCPP__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="sect2">
<h3 id="_boost_os_operating_system_macros"><a class="anchor" href="#_boost_os_operating_system_macros"></a>4.5. <code>BOOST_OS</code> operating system macros</h3>
<div class="sect3">
<h4 id="_boost_os_aix"><a class="anchor" href="#_boost_os_aix"></a>4.5.1. <code>BOOST_OS_AIX</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/AIX_operating_system">IBM AIX</a> operating system.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_AIX</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__TOS_AIX__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_AIX43</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.3.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_AIX41</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.1.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_AIX32</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.2.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_AIX3</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.0.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_amigaos"><a class="anchor" href="#_boost_os_amigaos"></a>4.5.2. <code>BOOST_OS_AMIGAOS</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/AmigaOS">AmigaOS</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>AMIGA</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__amigaos__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_beos"><a class="anchor" href="#_boost_os_beos"></a>4.5.3. <code>BOOST_OS_BEOS</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/BeOS">BeOS</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__BEOS__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_bsd"><a class="anchor" href="#_boost_os_bsd"></a>4.5.4. <code>BOOST_OS_BSD</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Berkeley_Software_Distribution">BSD</a> operating system.</p>
</div>
<div class="paragraph">
<p>BSD has various branch operating systems possible and each detected
individually. This detects the following variations and sets a specific
version number macro to match:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>BOOST_OS_BSD_DRAGONFLY</code> <a href="http://en.wikipedia.org/wiki/DragonFly_BSD">DragonFly BSD</a></p>
</li>
<li>
<p><code>BOOST_OS_BSD_FREE</code> <a href="http://en.wikipedia.org/wiki/Freebsd">FreeBSD</a></p>
</li>
<li>
<p><code>BOOST_OS_BSD_BSDI</code> <a href="http://en.wikipedia.org/wiki/BSD/OS">BSDi BSD/OS</a></p>
</li>
<li>
<p><code>BOOST_OS_BSD_NET</code> <a href="http://en.wikipedia.org/wiki/Netbsd">NetBSD</a></p>
</li>
<li>
<p><code>BOOST_OS_BSD_OPEN</code> <a href="http://en.wikipedia.org/wiki/Openbsd">OpenBSD</a></p>
</li>
</ul>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
The general <code>BOOST_OS_BSD</code> is set in all cases to indicate some form
of BSD. If the above variants is detected the corresponding macro is also set.
</td>
</tr>
</table>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>BSD</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_SYSTYPE_BSD</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>BSD4_2</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.2.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>BSD4_3</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.3.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>BSD4_4</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.4.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>BSD</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_cygwin"><a class="anchor" href="#_boost_os_cygwin"></a>4.5.5. <code>BOOST_OS_CYGWIN</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Cygwin">Cygwin</a> evironment.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__CYGWIN__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>CYGWIN_VERSION_API_MAJOR</code>, <code>CYGWIN_VERSION_API_MINOR</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_haiku"><a class="anchor" href="#_boost_os_haiku"></a>4.5.6. <code>BOOST_OS_HAIKU</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Haiku_(operating_system)">Haiku</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__HAIKU__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_hpux"><a class="anchor" href="#_boost_os_hpux"></a>4.5.7. <code>BOOST_OS_HPUX</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/HP-UX">HP-UX</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>hpux</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_hpux</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__hpux</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_ios"><a class="anchor" href="#_boost_os_ios"></a>4.5.8. <code>BOOST_OS_IOS</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/iOS">iOS</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__APPLE__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MACH__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ENVIRONMENT_IPHONE_OS_VERSION_MIN_REQUIRED__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ENVIRONMENT_IPHONE_OS_VERSION_MIN_REQUIRED__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">__ENVIRONMENT_IPHONE_OS_VERSION_MIN_REQUIRED__*1000</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_irix"><a class="anchor" href="#_boost_os_irix"></a>4.5.9. <code>BOOST_OS_IRIX</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Irix">IRIX</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>sgi</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sgi</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_linux"><a class="anchor" href="#_boost_os_linux"></a>4.5.10. <code>BOOST_OS_LINUX</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Linux">Linux</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>linux</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__linux</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__linux__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__gnu_linux__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_macos"><a class="anchor" href="#_boost_os_macos"></a>4.5.11. <code>BOOST_OS_MACOS</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Mac_OS">Mac OS</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>macintosh</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>Macintosh</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__APPLE__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MACH__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__APPLE__</code>, <code>__MACH__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">10.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code><em>otherwise</em></code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">9.0.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_os400"><a class="anchor" href="#_boost_os_os400"></a>4.5.12. <code>BOOST_OS_OS400</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/IBM_i">IBM OS/400</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__OS400__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_qnx"><a class="anchor" href="#_boost_os_qnx"></a>4.5.13. <code>BOOST_OS_QNX</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/QNX">QNX</a> operating system.
Version number available as major, and minor if possible. And
version 4 is specifically detected.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__QNX__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__QNXNTO__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_NTO_VERSION</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__QNX__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.0.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_solaris"><a class="anchor" href="#_boost_os_solaris"></a>4.5.14. <code>BOOST_OS_SOLARIS</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Solaris_Operating_Environment">Solaris</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>sun</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sun</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_unix"><a class="anchor" href="#_boost_os_unix"></a>4.5.15. <code>BOOST_OS_UNIX</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Unix">Unix Environment</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>unix</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__unix</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_XOPEN_SOURCE</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_POSIX_SOURCE</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_svr4"><a class="anchor" href="#_boost_os_svr4"></a>4.5.16. <code>BOOST_OS_SVR4</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/UNIX_System_V">SVR4 Environment</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__sysv__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SVR4</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__svr4__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_SYSTYPE_SVR4</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_vms"><a class="anchor" href="#_boost_os_vms"></a>4.5.17. <code>BOOST_OS_VMS</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Vms">VMS</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>VMS</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__VMS</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__VMS_VER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_windows"><a class="anchor" href="#_boost_os_windows"></a>4.5.18. <code>BOOST_OS_WINDOWS</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Category:Microsoft_Windows">Microsoft Windows</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_WIN32</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_WIN64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__WIN32__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__TOS_WIN__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__WINDOWS__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_bsd_bsdi"><a class="anchor" href="#_boost_os_bsd_bsdi"></a>4.5.19. <code>BOOST_OS_BSD_BSDI</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/BSD/OS">BSDi BSD/OS</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__bsdi__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_bsd_dragonfly"><a class="anchor" href="#_boost_os_bsd_dragonfly"></a>4.5.20. <code>BOOST_OS_BSD_DRAGONFLY</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/DragonFly_BSD">DragonFly BSD</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__DragonFly__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_bsd_free"><a class="anchor" href="#_boost_os_bsd_free"></a>4.5.21. <code>BOOST_OS_BSD_FREE</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Freebsd">FreeBSD</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__FreeBSD__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__FreeBSD_version</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_bsd_net"><a class="anchor" href="#_boost_os_bsd_net"></a>4.5.22. <code>BOOST_OS_BSD_NET</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Netbsd">NetBSD</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__NETBSD__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__NetBSD__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__NETBSD_version</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>NetBSD0_8</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0.8.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>NetBSD0_9</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">0.9.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>NetBSD1_0</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">1.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__NetBSD_Version</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.P</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_os_bsd_open"><a class="anchor" href="#_boost_os_bsd_open"></a>4.5.23. <code>BOOST_OS_BSD_OPEN</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Openbsd">OpenBSD</a> operating system.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__OpenBSD__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD2_0</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD2_1</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.1.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD2_2</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.2.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD2_3</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.3.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD2_4</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.4.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD2_5</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.5.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD2_6</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.6.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD2_7</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.7.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD2_8</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.8.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD2_9</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">2.9.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD3_0</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD3_1</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.1.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD3_2</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.2.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD3_3</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.3.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD3_4</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.4.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD3_5</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.5.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD3_6</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.6.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD3_7</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.7.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD3_8</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.8.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD3_9</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">3.9.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD4_0</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD4_1</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.1.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD4_2</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.2.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD4_3</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.3.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD4_4</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.4.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD4_5</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.5.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD4_6</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.6.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD4_7</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.7.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD4_8</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.8.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD4_9</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">4.9.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD5_0</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD5_1</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.1.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD5_2</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.2.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD5_3</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.3.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD5_4</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.4.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD5_5</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.5.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD5_6</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.6.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD5_7</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.7.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD5_8</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.8.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD5_9</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">5.9.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD6_0</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.0.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD6_1</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.1.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD6_2</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.2.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD6_3</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.3.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD6_4</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.4.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD6_5</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.5.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD6_6</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.6.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD6_7</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.7.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD6_8</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.8.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>OpenBSD6_9</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">6.9.0</p></td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="sect2">
<h3 id="_boost_plat_platform_macros"><a class="anchor" href="#_boost_plat_platform_macros"></a>4.6. <code>BOOST_PLAT</code> platform macros</h3>
<div class="sect3">
<h4 id="_boost_plat_android"><a class="anchor" href="#_boost_plat_android"></a>4.6.1. <code>BOOST_PLAT_ANDROID</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/Android_%28operating_system%29">Android</a> platform.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ANDROID__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_plat_cloudabi"><a class="anchor" href="#_boost_plat_cloudabi"></a>4.6.2. <code>BOOST_PLAT_CLOUDABI</code></h4>
<div class="paragraph">
<p><a href="https://github.com/NuxiNL/cloudabi">CloudABI</a> platform.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__CloudABI__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_plat_ios_device"><a class="anchor" href="#_boost_plat_ios_device"></a>4.6.3. <code>BOOST_PLAT_IOS_DEVICE</code></h4>

</div>
<div class="sect3">
<h4 id="_boost_plat_ios_simulator"><a class="anchor" href="#_boost_plat_ios_simulator"></a>4.6.4. <code>BOOST_PLAT_IOS_SIMULATOR</code></h4>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>TARGET_IPHONE_SIMULATOR</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>TARGET_OS_SIMULATOR</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_plat_mingw"><a class="anchor" href="#_boost_plat_mingw"></a>4.6.5. <code>BOOST_PLAT_MINGW</code></h4>
<div class="paragraph">
<p><a href="http://en.wikipedia.org/wiki/MinGW">MinGW</a> platform, either variety.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MINGW32__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MINGW64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MINGW64_VERSION_MAJOR</code>, <code>__MINGW64_VERSION_MINOR</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MINGW32_VERSION_MAJOR</code>, <code>__MINGW32_VERSION_MINOR</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_plat_mingw32"><a class="anchor" href="#_boost_plat_mingw32"></a>4.6.6. <code>BOOST_PLAT_MINGW32</code></h4>
<div class="paragraph">
<p><a href="http://www.mingw.org/">MinGW</a> platform.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MINGW32__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MINGW32_VERSION_MAJOR</code>, <code>__MINGW32_VERSION_MINOR</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_plat_mingw64"><a class="anchor" href="#_boost_plat_mingw64"></a>4.6.7. <code>BOOST_PLAT_MINGW64</code></h4>
<div class="paragraph">
<p><a href="https://mingw-w64.org/">MinGW-w64</a> platform.
Version number available as major, minor, and patch.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MINGW64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MINGW64_VERSION_MAJOR</code>, <code>__MINGW64_VERSION_MINOR</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">V.R.0</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_plat_windows_desktop"><a class="anchor" href="#_boost_plat_windows_desktop"></a>4.6.8. <code>BOOST_PLAT_WINDOWS_DESKTOP</code></h4>
<div class="paragraph">
<p><a href="https://docs.microsoft.com/en-us/windows/uwp/get-started/universal-application-platform-guide">UWP</a>
for Windows Desktop development.  Also available if the Platform SDK is too
old to support UWP.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>WINAPI_FAMILY == WINAPI_FAMILY_DESKTOP_APP</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>!BOOST_PLAT_WINDOWS_UWP</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_plat_windows_phone"><a class="anchor" href="#_boost_plat_windows_phone"></a>4.6.9. <code>BOOST_PLAT_WINDOWS_PHONE</code></h4>
<div class="paragraph">
<p><a href="https://docs.microsoft.com/en-us/windows/uwp/get-started/universal-application-platform-guide">UWP</a>
for Windows Phone development.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>WINAPI_FAMILY == WINAPI_FAMILY_PHONE_APP</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_plat_windows_runtime"><a class="anchor" href="#_boost_plat_windows_runtime"></a>4.6.10. <code>BOOST_PLAT_WINDOWS_RUNTIME</code></h4>
<div class="paragraph">
<p>Deprecated.</p>
</div>
<div class="paragraph">
<p><a href="https://docs.microsoft.com/en-us/windows/uwp/get-started/universal-application-platform-guide">UWP</a>
for Windows Phone or Store development.  This does not align to the existing development model for
UWP and is deprecated.  Use one of the other `BOOST_PLAT_WINDOWS_*`definitions instead.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>BOOST_PLAT_WINDOWS_PHONE</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>BOOST_PLAT_WINDOWS_STORE</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_plat_windows_server"><a class="anchor" href="#_boost_plat_windows_server"></a>4.6.11. <code>BOOST_PLAT_WINDOWS_SERVER</code></h4>
<div class="paragraph">
<p><a href="https://docs.microsoft.com/en-us/windows/uwp/get-started/universal-application-platform-guide">UWP</a>
for Windows Server development.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>WINAPI_FAMILY == WINAPI_FAMILY_SERVER</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_plat_windows_store"><a class="anchor" href="#_boost_plat_windows_store"></a>4.6.12. <code>BOOST_PLAT_WINDOWS_STORE</code></h4>
<div class="paragraph">
<p><a href="https://docs.microsoft.com/en-us/windows/uwp/get-started/universal-application-platform-guide">UWP</a>
for Windows Store development.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>WINAPI_FAMILY == WINAPI_FAMILY_PC_APP</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>WINAPI_FAMILY == WINAPI_FAMILY_APP</code> (deprecated)</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_plat_windows_system"><a class="anchor" href="#_boost_plat_windows_system"></a>4.6.13. <code>BOOST_PLAT_WINDOWS_SYSTEM</code></h4>
<div class="paragraph">
<p><a href="https://docs.microsoft.com/en-us/windows/uwp/get-started/universal-application-platform-guide">UWP</a>
for Windows System development.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>WINAPI_FAMILY == WINAPI_FAMILY_SYSTEM</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_plat_windows_uwp"><a class="anchor" href="#_boost_plat_windows_uwp"></a>4.6.14. <code>BOOST_PLAT_WINDOWS_UWP</code></h4>
<div class="paragraph">
<p><a href="http://docs.microsoft.com/windows/uwp/">Universal Windows Platform</a>
is available if the current development environment is capable of targeting 
UWP development.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__MINGW64_VERSION_MAJOR</code> from <code>_mingw.h</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>&gt;= 3</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>VER_PRODUCTBUILD</code> from <code>ntverp.h</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>&gt;= 9200</code></p></td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="sect2">
<h3 id="_boost_hw_hardware_macros"><a class="anchor" href="#_boost_hw_hardware_macros"></a>4.7. <code>BOOST_HW</code> hardware macros</h3>
<div class="sect3">
<h4 id="_using_the_boost_hw_simd_predefs"><a class="anchor" href="#_using_the_boost_hw_simd_predefs"></a>4.7.1. Using the <code>BOOST_HW_SIMD_*</code> predefs</h4>
<div class="paragraph">
<p>SIMD predefs depend on compiler options. For example, you will have to add the
option <code>-msse3</code> to clang or gcc to enable SSE3. SIMD predefs are also inclusive.
This means that if SSE3 is enabled, then every other extensions with a lower
version number will implicitly be enabled and detected. However, some extensions
are CPU specific, they may not be detected nor enabled when an upper version is
enabled.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
SSE(1) and SSE2 are automatically enabled by default when using x86-64
architecture.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>To check if any SIMD extension has been enabled, you can use:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#include &lt;boost/predef/hardware/simd.h&gt;
#include &lt;iostream&gt;
</span>
<span class="kt">int</span> <span class="nf">main</span><span class="p">()</span>
<span class="p">{</span>
<span class="cp">#if defined(BOOST_HW_SIMD_AVAILABLE)
</span>    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"SIMD detected!"</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
<span class="cp">#endif
</span>    <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>When writing SIMD specific code, you may want to check if a particular extension
has been detected. To do so you have to use the right architecture predef and
compare it. Those predef are of the form <code>BOOST_HW_SIMD_"ARCH"</code> (where <code>"ARCH"</code>
is either <code>ARM</code>, <code>PPC</code>, or <code>X86</code>). For example, if you compile code for x86
architecture, you will have to use <code>BOOST_HW_SIMD_X86</code>. Its value will be the
version number of the most recent SIMD extension detected for the architecture.</p>
</div>
<div class="paragraph">
<p>To check if an extension has been enabled:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#include &lt;boost/predef/hardware/simd.h&gt;
#include &lt;iostream&gt;
</span>
<span class="kt">int</span> <span class="nf">main</span><span class="p">()</span>
<span class="p">{</span>
<span class="cp">#if BOOST_HW_SIMD_X86 &gt;= BOOST_HW_SIMD_X86_SSE3_VERSION
</span>    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"This is SSE3!"</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
<span class="cp">#endif
</span>    <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
The <strong>_VERSION</strong> defines that map version number to actual real
identifiers. This way it is easier to write comparisons without messing up with
version numbers.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>To <strong>"strictly"</strong> check the most recent detected extension:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#include &lt;boost/predef/hardware/simd.h&gt;
#include &lt;iostream&gt;
</span>
<span class="kt">int</span> <span class="nf">main</span><span class="p">()</span>
<span class="p">{</span>
<span class="cp">#if BOOST_HW_SIMD_X86 == BOOST_HW_SIMD_X86_SSE3_VERSION
</span>    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"This is SSE3 and this is the most recent enabled extension!"</span>
        <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
<span class="cp">#endif
</span>    <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Because of the version systems of predefs and of the inclusive property of SIMD
extensions macros, you can easily check for ranges of supported extensions:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#include &lt;boost/predef/hardware/simd.h&gt;
#include &lt;iostream&gt;
</span>
<span class="kt">int</span> <span class="nf">main</span><span class="p">()</span>
<span class="p">{</span>
<span class="cp">#if BOOST_HW_SIMD_X86 &gt;= BOOST_HW_SIMD_X86_SSE2_VERSION &amp;&amp;\
    BOOST_HW_SIMD_X86 &lt;= BOOST_HW_SIMD_X86_SSSE3_VERSION
</span>    <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">"This is SSE2, SSE3 and SSSE3!"</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
<span class="cp">#endif
</span>    <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
Unlike gcc and clang, Visual Studio does not allow you to specify precisely
the SSE variants you want to use, the only detections that will take place are
SSE, SSE2, AVX and AVX2. For more informations,
    see [@https://msdn.microsoft.com/en-us/library/b0084kay.aspx here].
</td>
</tr>
</table>
</div>
</div>
<div class="sect3">
<h4 id="_boost_hw_simd_arm"><a class="anchor" href="#_boost_hw_simd_arm"></a>4.7.2. <code>BOOST_HW_SIMD_ARM</code></h4>
<div class="paragraph">
<p>The SIMD extension for ARM (<strong>if detected</strong>).
Version number depends on the most recent detected extension.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_NEON__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__aarch64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_ARM</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_ARM64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ARM_NEON__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_ARM_NEON_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__aarch64__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_ARM_NEON_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_ARM</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_ARM_NEON_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_ARM64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_ARM_NEON_VERSION</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_hw_simd_arm_version"><a class="anchor" href="#_boost_hw_simd_arm_version"></a>4.7.3. <code>BOOST_HW_SIMD_ARM_*_VERSION</code></h4>
<div class="paragraph">
<p>Those defines represent ARM SIMD extensions versions.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
You <strong>MUST</strong> compare them with the predef <code>BOOST_HW_SIMD_ARM</code>.
= <code>BOOST_HW_SIMD_ARM_NEON_VERSION</code>
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/ARM_architecture#Advanced_SIMD_.28NEON.29">NEON</a>
ARM extension version number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>1.0.0</strong>.</p>
</div>
</div>
<div class="sect3">
<h4 id="_boost_hw_simd_ppc"><a class="anchor" href="#_boost_hw_simd_ppc"></a>4.7.4. <code>BOOST_HW_SIMD_PPC</code></h4>
<div class="paragraph">
<p>The SIMD extension for PowerPC (<strong>if detected</strong>).
Version number depends on the most recent detected extension.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__VECTOR4DOUBLE__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ALTIVEC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__VEC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__VSX__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__VECTOR4DOUBLE__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_PPC_QPX_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__ALTIVEC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_PPC_VMX_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__VEC__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_PPC_VMX_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__VSX__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_PPC_VSX_VERSION</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_hw_simd_ppc_version"><a class="anchor" href="#_boost_hw_simd_ppc_version"></a>4.7.5. <code>BOOST_HW_SIMD_PPC_*_VERSION</code></h4>
<div class="paragraph">
<p>Those defines represent Power PC SIMD extensions versions.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
You <strong>MUST</strong> compare them with the predef <code>BOOST_HW_SIMD_PPC</code>.
= <code>BOOST_HW_SIMD_PPC_VMX_VERSION</code>
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/AltiVec#VMX128">VMX</a> powerpc extension
version number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>1.0.0</strong>.
= <code>BOOST_HW_SIMD_PPC_VSX_VERSION</code></p>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/AltiVec#VSX">VSX</a> powerpc extension version
number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>1.1.0</strong>.
= <code>BOOST_HW_SIMD_PPC_QPX_VERSION</code></p>
</div>
<div class="paragraph">
<p>The QPX powerpc extension version number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>2.0.0</strong>.</p>
</div>
</div>
<div class="sect3">
<h4 id="_boost_hw_simd_x86"><a class="anchor" href="#_boost_hw_simd_x86"></a>4.7.6. <code>BOOST_HW_SIMD_X86</code></h4>
<div class="paragraph">
<p>The SIMD extension for x86 (<strong>if detected</strong>).
Version number depends on the most recent detected extension.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSE__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_X64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_IX86_FP &gt;= 1</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSE2__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_X64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_IX86_FP &gt;= 2</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSE3__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSSE3__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSE4_1__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSE4_2__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__AVX__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__FMA__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__AVX2__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSE__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_SSE_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_X64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_SSE_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_IX86_FP &gt;= 1</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_SSE_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSE2__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_SSE2_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_X64</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_SSE2_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>_M_IX86_FP &gt;= 2</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_SSE2_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSE3__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_SSE3_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSSE3__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_SSSE3_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSE4_1__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_SSE4_1_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSE4_2__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_SSE4_2_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__AVX__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_AVX_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__FMA__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_FMA3_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__AVX2__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_AVX2_VERSION</p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect3">
<h4 id="_boost_hw_simd_x86_version"><a class="anchor" href="#_boost_hw_simd_x86_version"></a>4.7.7. <code>BOOST_HW_SIMD_X86_*_VERSION</code></h4>
<div class="paragraph">
<p>Those defines represent x86 SIMD extensions versions.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
You <strong>MUST</strong> compare them with the predef <code>BOOST_HW_SIMD_X86</code>.
= <code>BOOST_HW_SIMD_X86_MMX_VERSION</code>
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/MMX_(instruction_set)">MMX</a> x86 extension
version number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>0.99.0</strong>.
= <code>BOOST_HW_SIMD_X86_SSE_VERSION</code></p>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/Streaming_SIMD_Extensions">SSE</a> x86 extension
version number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>1.0.0</strong>.
= <code>BOOST_HW_SIMD_X86_SSE2_VERSION</code></p>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/SSE2">SSE2</a> x86 extension version number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>2.0.0</strong>.
= <code>BOOST_HW_SIMD_X86_SSE3_VERSION</code></p>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/SSE3">SSE3</a> x86 extension version number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>3.0.0</strong>.
= <code>BOOST_HW_SIMD_X86_SSSE3_VERSION</code></p>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/SSSE3">SSSE3</a> x86 extension version number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>3.1.0</strong>.
= <code>BOOST_HW_SIMD_X86_SSE4_1_VERSION</code></p>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/SSE4#SSE4.1">SSE4_1</a> x86 extension version
number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>4.1.0</strong>.
= <code>BOOST_HW_SIMD_X86_SSE4_2_VERSION</code></p>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/SSE4##SSE4.2">SSE4_2</a> x86 extension version
number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>4.2.0</strong>.
= <code>BOOST_HW_SIMD_X86_AVX_VERSION</code></p>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/Advanced_Vector_Extensions">AVX</a> x86
extension version number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>5.0.0</strong>.
= <code>BOOST_HW_SIMD_X86_FMA3_VERSION</code></p>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/FMA_instruction_set">FMA3</a> x86 extension
version number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>5.2.0</strong>.
= <code>BOOST_HW_SIMD_X86_AVX2_VERSION</code></p>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/Advanced_Vector_Extensions#Advanced_Vector_Extensions_2">AVX2</a>
x86 extension version number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>5.3.0</strong>.
= <code>BOOST_HW_SIMD_X86_MIC_VERSION</code></p>
</div>
<div class="paragraph">
<p>The <a href="https://en.wikipedia.org/wiki/Xeon_Phi">MIC</a> (Xeon Phi) x86 extension
version number.</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>9.0.0</strong>.</p>
</div>
</div>
<div class="sect3">
<h4 id="_boost_hw_simd_x86_amd"><a class="anchor" href="#_boost_hw_simd_x86_amd"></a>4.7.8. <code>BOOST_HW_SIMD_X86_AMD</code></h4>
<div class="paragraph">
<p>The SIMD extension for x86 (AMD) (<strong>if detected</strong>).
Version number depends on the most recent detected extension.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSE4A__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__FMA4__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__XOP__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>BOOST_HW_SIMD_X86</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><strong>detection</strong></p></td>
</tr>
</tbody>
</table>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Symbol</th>
<th class="tableblock halign-left valign-top">Version</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__SSE4A__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_SSE4A_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__FMA4__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_FMA4_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>__XOP__</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86_XOP_VERSION</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>BOOST_HW_SIMD_X86</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">BOOST_HW_SIMD_X86</p></td>
</tr>
</tbody>
</table>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
This predef includes every other x86 SIMD extensions and also has other
more specific extensions (FMA4, XOP, SSE4a). You should use this predef
instead of <code>BOOST_HW_SIMD_X86</code> to test if those specific extensions have
been detected.
</td>
</tr>
</table>
</div>
</div>
<div class="sect3">
<h4 id="_boost_hw_simd_x86_amd_version"><a class="anchor" href="#_boost_hw_simd_x86_amd_version"></a>4.7.9. <code>BOOST_HW_SIMD_X86_AMD_*_VERSION</code></h4>
<div class="paragraph">
<p>Those defines represent x86 (AMD specific) SIMD extensions versions.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
You <strong>MUST</strong> compare them with the predef <code>BOOST_HW_SIMD_X86_AMD</code>.
= <code>BOOST_HW_SIMD_X86_AMD_SSE4A_VERSION</code>
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p><a href="https://en.wikipedia.org/wiki/SSE4##SSE4A">SSE4A</a> x86 extension (AMD specific).</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>4.0.0</strong>.
= <code>BOOST_HW_SIMD_X86_AMD_FMA4_VERSION</code></p>
</div>
<div class="paragraph">
<p><a href="https://en.wikipedia.org/wiki/FMA_instruction_set#FMA4_instruction_set">FMA4</a> x86 extension (AMD specific).</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>5.1.0</strong>.
= <code>BOOST_HW_SIMD_X86_AMD_XOP_VERSION</code></p>
</div>
<div class="paragraph">
<p><a href="https://en.wikipedia.org/wiki/XOP_instruction_set">XOP</a> x86 extension (AMD specific).</p>
</div>
<div class="paragraph">
<p>Version number is: <strong>5.1.1</strong>.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_other_macros"><a class="anchor" href="#_other_macros"></a>4.8. Other macros</h3>
<div class="sect3">
<h4 id="_boost_endian"><a class="anchor" href="#_boost_endian"></a>4.8.1. <code>BOOST_ENDIAN_*</code></h4>
<div class="paragraph">
<p>Detection of endian memory ordering. There are four defined macros
in this header that define the various generally possible endian
memory orderings:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>BOOST_ENDIAN_BIG_BYTE</code>, byte-swapped big-endian.</p>
</li>
<li>
<p><code>BOOST_ENDIAN_BIG_WORD</code>, word-swapped big-endian.</p>
</li>
<li>
<p><code>BOOST_ENDIAN_LITTLE_BYTE</code>, byte-swapped little-endian.</p>
</li>
<li>
<p><code>BOOST_ENDIAN_LITTLE_WORD</code>, word-swapped little-endian.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>The detection is conservative in that it only identifies endianness
that it knows for certain. In particular bi-endianness is not
indicated as is it not practically possible to determine the
endianness from anything but an operating system provided
header. And the currently known headers do not define that
programatic bi-endianness is available.</p>
</div>
<div class="paragraph">
<p>This implementation is a compilation of various publicly available
information and acquired knowledge:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>The indispensable documentation of "Pre-defined Compiler Macros"
<a href="http://sourceforge.net/p/predef/wiki/Endianness">Endianness</a>.</p>
</li>
<li>
<p>The various endian specifications available in the
<a href="http://wikipedia.org/">Wikipedia</a> computer architecture pages.</p>
</li>
<li>
<p>Generally available searches for headers that define endianness.</p>
</li>
</ol>
</div>
</div>
<div class="sect3">
<h4 id="_boost_arch_word_bits"><a class="anchor" href="#_boost_arch_word_bits"></a>4.8.2. <code>BOOST_ARCH_WORD_BITS</code></h4>
<div class="paragraph">
<p>Detects the native word size, in bits, for the current architecture. There are
two types of macros for this detection:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>BOOST_ARCH_WORD_BITS</code>, gives the number of word size bits
(16, 32, 64).</p>
</li>
<li>
<p><code>BOOST_ARCH_WORD_BITS_16</code>, <code>BOOST_ARCH_WORD_BITS_32</code>, and
<code>BOOST_ARCH_WORD_BITS_64</code>, indicate when the given word size is
detected.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>They allow for both single checks and direct use of the size in code.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">ℹ</div>
</td>
<td class="content">
The word size is determined manually on each architecture. Hence use of
the <code>wordsize.h</code> header will also include all the architecture headers.
</td>
</tr>
</table>
</div>
</div>
<div class="sect3">
<h4 id="_boost_predef_workaround"><a class="anchor" href="#_boost_predef_workaround"></a>4.8.3. <code>BOOST_PREDEF_WORKAROUND</code></h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="n">BOOST_PREDEF_WORKAROUND</span><span class="p">(</span><span class="n">symbol</span><span class="p">,</span><span class="n">comp</span><span class="p">,</span><span class="n">major</span><span class="p">,</span><span class="n">minor</span><span class="p">,</span><span class="n">patch</span><span class="p">)</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Usage:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#if BOOST_PREDEF_WORKAROUND(BOOST_COMP_CLANG,&lt;,3,0,0)
</span>    <span class="c1">// Workaround for old clang compilers..</span>
<span class="cp">#endif</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Defines a comparison against two version numbers that depends on the definion
of <code>BOOST_STRICT_CONFIG</code>. When <code>BOOST_STRICT_CONFIG</code> is defined this will expand
to a value convertible to <code>false</code>. Which has the effect of disabling all code
conditionally guarded by <code>BOOST_PREDEF_WORKAROUND</code>. When <code>BOOST_STRICT_CONFIG</code>
is undefine this expand to test the given <code>symbol</code> version value with the
<code>comp</code> comparison against <code>BOOST_VERSION_NUMBER(major,minor,patch)</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="_boost_predef_tested_at"><a class="anchor" href="#_boost_predef_tested_at"></a>4.8.4. <code>BOOST_PREDEF_TESTED_AT</code></h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="n">BOOST_PREDEF_TESTED_AT</span><span class="p">(</span><span class="n">symbol</span><span class="p">,</span><span class="n">major</span><span class="p">,</span><span class="n">minor</span><span class="p">,</span><span class="n">patch</span><span class="p">)</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Usage:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="cp">#if BOOST_PREDEF_TESTED_AT(BOOST_COMP_CLANG,3,5,0)
</span>    <span class="c1">// Needed for clang, and last checked for 3.5.0.</span>
<span class="cp">#endif</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Defines a comparison against two version numbers that depends on the definion
of <code>BOOST_STRICT_CONFIG</code> and <code>BOOST_DETECT_OUTDATED_WORKAROUNDS</code>.
When <code>BOOST_STRICT_CONFIG</code> is defined this will expand to a value convertible
to <code>false</code>. Which has the effect of disabling all code
conditionally guarded by <code>BOOST_PREDEF_TESTED_AT</code>. When <code>BOOST_STRICT_CONFIG</code>
is undefined this expand to either:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>A value convertible to <code>true</code> when <code>BOOST_DETECT_OUTDATED_WORKAROUNDS</code> is not
defined.</p>
</li>
<li>
<p>A value convertible <code>true</code> when the expansion of
<code>BOOST_PREDEF_WORKAROUND(symbol, &#8656;, major, minor, patch)</code> is <code>true</code> and
<code>BOOST_DETECT_OUTDATED_WORKAROUNDS</code> is defined.</p>
</li>
<li>
<p>A compile error when the expansion of
<code>BOOST_PREDEF_WORKAROUND(symbol, &gt;, major, minor, patch)</code> is true and
<code>BOOST_DETECT_OUTDATED_WORKAROUNDS</code> is defined.</p>
</li>
</ul>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_version_definition_macros"><a class="anchor" href="#_version_definition_macros"></a>4.9. Version definition macros</h3>
<div class="sect3">
<h4 id="_boost_version_number"><a class="anchor" href="#_boost_version_number"></a>4.9.1. <code>BOOST_VERSION_NUMBER</code></h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="n">BOOST_VERSION_NUMBER</span><span class="p">(</span><span class="n">major</span><span class="p">,</span><span class="n">minor</span><span class="p">,</span><span class="n">patch</span><span class="p">)</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Defines standard version numbers, with these properties:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Decimal base whole numbers in the range [0,1000000000).
The number range is designed to allow for a (2,2,5) triplet.
Which fits within a 32 bit value.</p>
</li>
<li>
<p>The <code>major</code> number can be in the [0,99] range.</p>
</li>
<li>
<p>The <code>minor</code> number can be in the [0,99] range.</p>
</li>
<li>
<p>The <code>patch</code> number can be in the [0,99999] range.</p>
</li>
<li>
<p>Values can be specified in any base. As the defined value
is an constant expression.</p>
</li>
<li>
<p>Value can be directly used in both preprocessor and compiler
expressions for comparison to other similarly defined values.</p>
</li>
<li>
<p>The implementation enforces the individual ranges for the
major, minor, and patch numbers. And values over the ranges
are truncated (modulo).</p>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="cpp"><span class="n">BOOST_VERSION_NUMBER_MAJOR</span><span class="p">(</span><span class="n">N</span><span class="p">),</span> <span class="n">BOOST_VERSION_NUMBER_MINOR</span><span class="p">(</span><span class="n">N</span><span class="p">),</span> <span class="n">BOOST_VERSION_NUMBER_PATCH</span><span class="p">(</span><span class="n">N</span><span class="p">)</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The macros extract the major, minor, and patch portion from a well formed
version number resulting in a preprocessor expression in the range of
[0,99] or [0,99999] for the major and minor, or patch numbers
respectively.</p>
</div>
</div>
<div class="sect3">
<h4 id="_boost_predef_make_macros"><a class="anchor" href="#_boost_predef_make_macros"></a>4.9.2. <code>BOOST_PREDEF_MAKE_..</code> macros</h4>
<div class="paragraph">
<p>These set of macros decompose common vendor version number
macros which are composed version, revision, and patch digits.
The naming convention indicates:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>The base of the specified version number. &#8220;BOOST_PREDEF_MAKE_0X&#8221; for
hexadecimal digits, and &#8220;BOOST_PREDEF_MAKE_10&#8221; for decimal digits.</p>
</li>
<li>
<p>The format of the vendor version number. Where &#8220;V&#8221; indicates the version digits,
&#8220;R&#8221; indicates the revision digits, &#8220;P&#8221; indicates the patch digits, and &#8220;0&#8221;
indicates an ignored digit.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Macros are:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>BOOST_PREDEF_MAKE_0X_VRP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_0X_VVRP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_0X_VRPP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_0X_VVRR(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_0X_VRRPPPP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_0X_VVRRP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_0X_VRRPP000(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_0X_VVRRPP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VPPP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VVPPP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VR0(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VRP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VRP000(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VRPPPP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VRPP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VRR(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VRRPP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VRR000(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VV00(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VVRR(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VVRRP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VVRRPP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VVRRPPP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VVRR0PP00(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VVRR0PPPP(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_10_VVRR00PP00(V)</code></p>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="_boost_predef_make_date_macros"><a class="anchor" href="#_boost_predef_make_date_macros"></a>4.9.3. <code>BOOST_PREDEF_MAKE_*..</code> date macros</h4>
<div class="paragraph">
<p>Date decomposition macros return a date in the relative to the 1970
Epoch date. If the month is not available, January 1st is used as the month and day.
If the day is not available, but the month is, the 1st of the month is used as the day.</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>BOOST_PREDEF_MAKE_DATE(Y,M,D)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_YYYYMMDD(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_YYYY(V)</code></p>
</li>
<li>
<p><code>BOOST_PREDEF_MAKE_YYYYMM(V)</code></p>
</li>
</ul>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_check_utilities"><a class="anchor" href="#_check_utilities"></a>5. Check Utilities</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The <code>predef_check</code> utility provides a facility for building a
program that will check a given set of expressions against
the definitions it detected when it was built.</p>
</div>
<div class="sect2">
<h3 id="_predef_check_programs"><a class="anchor" href="#_predef_check_programs"></a>5.1. <code>predef_check</code> programs</h3>
<div class="paragraph">
<p>Even though there is only one <code>predef_check</code> program, there
are variations for each of the languages that are detected
by Predef to match the convention for sources files. For all
of them one invokes with a list of expression arguments. The
expressions are evaluated within the context of the particular
<code>predef_check</code> program and if they all are true zero (0) is returned.
Otherwise the index of the first false expression is returned.</p>
</div>
<div class="paragraph">
<p>The expression syntax is simple:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="jam">predef-definition [ relational-operator version-value ]</code></pre>
</div>
</div>
<div class="paragraph">
<p><em>predef-definition</em> can be any of the Predef definitions. For
example <code>BOOST_COMP_GCC</code>.</p>
</div>
<div class="paragraph">
<p><em>relational-operator</em> can be any of: <code>&gt;</code>, <code>&lt;</code>, <code>&gt;=</code>, <code>&#8656;</code>,
<code>==</code> and <code>!=</code>.</p>
</div>
<div class="paragraph">
<p><em>version-number</em> can be a full or partial version triplet value.
If it&#8217;s a partial version triple it is completed with zeros. That
is <code>x.y</code> is equivalent to <code>x.y.0</code> and <code>x</code> is equivalent to
<code>x.0.0</code>.</p>
</div>
<div class="paragraph">
<p>The <em>relations-operator</em> and <em>version-number</em> can be omitted. In
which case it is equivalent to:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="jam">predef-definition &gt; 0.0.0</code></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_using_with_boost_build"><a class="anchor" href="#_using_with_boost_build"></a>5.2. Using with Boost.Build</h3>
<div class="paragraph">
<p>You can use the <code>predef_check</code> programs directly from Boost Build
to configure target requirements. This is useful for controlling
what gets built as part of your project based on the detailed
version information available in Predef. The basic use is simple:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="jam">import path-to-predef-src/tools/check/predef
    : check require
    : predef-check predef-require ;

exe my_windows_program : windows_source.cpp
    : [ predef-require "BOOST_OS_WINDOWS" ] ;</code></pre>
</div>
</div>
<div class="paragraph">
<p>That simple use case will skip building the <code>my_windows_program</code>
unless one is building for Windows. Like the direct <code>predef_check</code>
you can pass multiple expressions using relational comparisons.
For example:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="jam">import path-to-predef-src/tools/check/predef
    : check require
    : predef-check predef-require ;

lib my_special_lib : source.cpp
    : [ predef-require "BOOST_OS_WINDOWS != 0" "BOOST_OS_VMS != 0"] ;</code></pre>
</div>
</div>
<div class="paragraph">
<p>And in that case the <code>my_special_lib</code> is built only when the OS is
not Windows or VMS. The <code>requires</code> rule is a special case of the
<code>check</code> rule. And is defined in terms of it:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="jam">rule require ( expressions + : language ? )
{
    return [ check $(expressions) : $(language) : : &lt;build&gt;no ] ;
}</code></pre>
</div>
</div>
<div class="paragraph">
<p>The expression can also use explicit "and", "or" logical operators
to for more complex checks:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="jam">import path-to-predef-src/tools/check/predef
    : check require
    : predef-check predef-require ;

lib my_special_lib : source.cpp
    : [ predef-require "BOOST_OS_WINDOWS" or "BOOST_OS_VMS"] ;</code></pre>
</div>
</div>
<div class="paragraph">
<p>You can use the <code>check</code> rule for more control and to implement
something other than control of what gets built. The definition
for the <code>check</code> rule is:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="jam">rule check ( expressions + : language ? : true-properties * : false-properties * )</code></pre>
</div>
</div>
<div class="paragraph">
<p>When invoked as a requirement of a Boost Build target this rule
will add the <code>true-properties</code> to the target if all the <code>expressions</code>
evaluate to true. Otherwise the <code>false-properties</code> get added as
requirements. For example you could use it to enable or disable
features in your programs:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="jam">import path-to-predef-src/tools/check/predef
    : check require
    : predef-check predef-require ;

exe my_special_exe : source.cpp
    : [ predef-check "BOOST_OS_WINDOWS == 0"
        : : &lt;define&gt;ENABLE_WMF=0
        : &lt;define&gt;ENABLE_WMF=1 ] ;</code></pre>
</div>
</div>
<div class="paragraph">
<p>For both <code>check</code> and <code>require</code> the <code>language</code> argument controls
which variant of the <code>predef_check</code> program is used to check the
expressions. It defaults to "c++", but can be any of: "c", "cpp",
"objc", and "objcpp".</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_history"><a class="anchor" href="#_history"></a>6. History</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="_1_14_0"><a class="anchor" href="#_1_14_0"></a>6.1. 1.14.0</h3>
<div class="ulist">
<ul>
<li>
<p>Add detection of LoongArch (from Zhang Na).</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_13_1"><a class="anchor" href="#_1_13_1"></a>6.2. 1.13.1</h3>
<div class="ulist">
<ul>
<li>
<p>Fix spelling of "epoch".</p>
</li>
<li>
<p>Add missing parenthesis in <code>sparc.h</code> (from tkoecker).</p>
</li>
<li>
<p>Update documentation to use Rouge code styling and Amber general style.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_13"><a class="anchor" href="#_1_13"></a>6.3. 1.13</h3>
<div class="ulist">
<ul>
<li>
<p>Add <code>ARCH_PPC_64</code> predef.</p>
</li>
<li>
<p>Fix <code>ARCH_WORD_BITS*</code> redefinition warnings/errors.</p>
</li>
<li>
<p>Add <code>ARCH_E2K</code>, Elbrus 2000, architecture from Konstantin Ivlev.</p>
</li>
<li>
<p>Fix not handling recent C++ version that go above 10.x version.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_12"><a class="anchor" href="#_1_12"></a>6.4. 1.12</h3>
<div class="ulist">
<ul>
<li>
<p>Switch to using the endian.h header on OpenBSD. (Brad Smith)</p>
</li>
<li>
<p>Fix not handling recent versions of stdcxx that go above version 9.</p>
</li>
<li>
<p>Fix including sub-BSD OS headers directly causing redef warnings.</p>
</li>
<li>
<p>Add CI testing of direct inclusion of all headers.</p>
</li>
<li>
<p>Add CI testing on FreeBSD for clang and gcc.</p>
</li>
<li>
<p>Add <code>WORD_BITS</code> set of predefs to detect the architecture word size.
Initial implementation inspired by submission from Mikhail Komarov.</p>
</li>
<li>
<p>Add CI testing for Cygwin 32 and 64.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_11"><a class="anchor" href="#_1_11"></a>6.5. 1.11</h3>
<div class="ulist">
<ul>
<li>
<p>Add <code>BOOST_ARCH_RISCV</code>. (from Andreas Schwab)</p>
</li>
<li>
<p>Add RISC-V endian detection. (from Thomas Petazzoni)</p>
</li>
<li>
<p>Convert documentation to AsciiDoctor format.</p>
</li>
<li>
<p>Document correct versions for C++ standard.</p>
</li>
<li>
<p>Fix compile error from not available header when building in WinCE.</p>
</li>
<li>
<p>Remove deprecated <code>BOOST_OS_ANDROID</code>.</p>
</li>
<li>
<p>Fix compile for Wine. (Kevin Puetz)</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_10"><a class="anchor" href="#_1_10"></a>6.6. 1.10</h3>
<div class="ulist">
<ul>
<li>
<p>Fix bad include of sub-BSD os headers from main BSD header.</p>
</li>
<li>
<p>Fix use of deprecated <code>TARGET_IPHONE_SIMULATOR</code> instead of newer
<code>TARGET_OS_SIMULATOR</code>.</p>
</li>
<li>
<p>Add <code>BOOST_PLAT_ANDROID</code> to resolve conflict between Linux and Android
OS predefs. The <code>BOOST_OS_ANDROID</code> predef is now deprecated and will be
removed in a future release.</p>
</li>
<li>
<p>Add support for consuming Predef as a CMake project.</p>
</li>
<li>
<p>Add support for consuming Predef as a standalone B2 project.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_9"><a class="anchor" href="#_1_9"></a>6.7. 1.9</h3>
<div class="ulist">
<ul>
<li>
<p>Fixes for <code>BOOST_COMP_NVCC*</code> predefs. (from Benjamin Worpitz)</p>
</li>
<li>
<p>Add specific version information for Cygwin OS predef. (from James E. King III)</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_8"><a class="anchor" href="#_1_8"></a>6.8. 1.8</h3>
<div class="ulist">
<ul>
<li>
<p>Add support for __ARM_ARCH macro. (from Tim Blechmann)</p>
</li>
<li>
<p>Add detection for PTX architecture. (from Benjamin Worpitz)</p>
</li>
<li>
<p>Add nvcc compiler detection. (from Benjamin Worpitz)</p>
</li>
<li>
<p>Add support for detecting CUDA. (from Benjamin Worpitz)</p>
</li>
<li>
<p>Remove reference to obsolete BOOST_ARCH_AMD64. (from Peter Kolbus)</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_7"><a class="anchor" href="#_1_7"></a>6.9. 1.7</h3>
<div class="ulist">
<ul>
<li>
<p>Fix BOOST_ARCH_PARISK/BOOST_ARCH_PARISC typo.</p>
</li>
<li>
<p>Improved Windows Universal Platform detection. (from James E. King, III)</p>
</li>
<li>
<p>Add detection for CloudABI with cloudlibc. (from James E. King, III)</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_6"><a class="anchor" href="#_1_6"></a>6.10. 1.6</h3>
<div class="ulist">
<ul>
<li>
<p>Fix Intel C/C++ version 9999 detection to be 12.1.0.</p>
</li>
<li>
<p>Addition of <code>BOOST_PREDEF_WORKAROUND</code> and <code>BOOST_PREDEF_TESTED_AT</code> macros
for defect workarounds and detection.</p>
</li>
<li>
<p>Add ARM64 MSVC SIMD detection. (from Minmin Gong)</p>
</li>
<li>
<p>Add detection of iOS simulator vs device as a platform choice. (from Ruslan
Baratov)</p>
</li>
<li>
<p>Fix MinGW incorrect header guard. (from Ruslan Baratov)</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_5"><a class="anchor" href="#_1_5"></a>6.11. 1.5</h3>
<div class="ulist">
<ul>
<li>
<p>Fix Intel C/C++ compiler version specification.</p>
</li>
<li>
<p>Add <code>BOOST_VERSION_NUMBER_MAJOR</code>, <code>BOOST_VERSION_NUMBER_MINOR</code>,
<code>BOOST_VERSION_NUMBER_PATCH</code> macros to extract components from valid version
numbers.</p>
</li>
<li>
<p>Change VS version numbering. Version after VS2015 will use the compiler
version instead of the varied product versions.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_4_1"><a class="anchor" href="#_1_4_1"></a>6.12. 1.4.1</h3>
<div class="ulist">
<ul>
<li>
<p>Small fixes for some redefinition errors, and mispelled macros.</p>
</li>
<li>
<p>Slightly rearrangement of structure to comply with current library requirements.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_4"><a class="anchor" href="#_1_4"></a>6.13. 1.4</h3>
<div class="ulist">
<ul>
<li>
<p>Add detection of SIMD hardware. With the addition of the <code>BOOST_HW_*</code>
category (from Charly Chevalier).</p>
</li>
<li>
<p>Add compile only version of check utilities to address cross-compile
use cases. And changed the BBv2 check support to use compile only checks.</p>
</li>
<li>
<p>Fix test warnings.</p>
</li>
<li>
<p>Fix typos on <code>AVAILABLE</code> macros for Windows Platform. (from Vemund Handeland)</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_3"><a class="anchor" href="#_1_3"></a>6.14. 1.3</h3>
<div class="ulist">
<ul>
<li>
<p>Fix many problems with <code>predef_check</code> functionality.</p>
</li>
<li>
<p>Update SunPro detection to accommodate latest version of compiler from Oracle.</p>
</li>
<li>
<p>Addition of Travis-CI and Appveyor testing.</p>
</li>
<li>
<p>Add <code>and</code> and <code>or</code> logical operators for <code>predef_check</code> expression on the Boost Build side.</p>
</li>
<li>
<p>Fix <code>BOOST_ARCH_PARISC</code> to correctly spelled name (from Graham Hanson).</p>
</li>
<li>
<p>Fix <code>MAKE_YYYYM</code> macros to correctly limit the month (from rick68).</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_2"><a class="anchor" href="#_1_2"></a>6.15. 1.2</h3>
<div class="ulist">
<ul>
<li>
<p>Account for skip in Visual Studio product version vs. compiler version.
This supports version of VS 2015 an onward.</p>
</li>
<li>
<p>Add detection of Haiku OS (from Jessica Hamilton).</p>
</li>
<li>
<p>Some fixes to endian detection for Android (from mstahl-at-redhat.com).</p>
</li>
<li>
<p>Add missing <code>BOOST_PREDEF_MAKE_0X_VVRRPP</code> macro (from Erik Lindahl).</p>
</li>
<li>
<p>Add <code>predef_check</code> program and BBv2 integration for build configuration
checks.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_1_1"><a class="anchor" href="#_1_1"></a>6.16. 1.1</h3>
<div class="ulist">
<ul>
<li>
<p>Addition of <code>BOOST_PLAT_*</code> platform definitions for MinGW and
Windows platform variants.</p>
</li>
<li>
<p>Detection of ARM architecture for Windows compilers to target
mobile devices of Windows 8.</p>
</li>
<li>
<p>Improved ARM detection for 64 bit ARM.</p>
</li>
<li>
<p>Added detection of iOS an an operating system.</p>
</li>
<li>
<p>Improved detection of endianess on some platforms.</p>
</li>
<li>
<p>Addition of exclusive plus emulated definitions for platform
and compiler detection.</p>
</li>
</ul>
</div>
<div class="admonitionblock warning">
<table>
<tr>
<td class="icon">
<div class="title">⚠</div>
</td>
<td class="content">
The big change for this version is the restructuring of the
definitions to avoid duplicate definitions in one category. That is, only one
<code>BOOST_OS_*</code>, <code>BOOST_COMP_*</code>, and <code>BOOST_PLAT_*</code> variant will be detected
(except for sub-categories).
</td>
</tr>
</table>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_to_do"><a class="anchor" href="#_to_do"></a>7. To Do</h2>
<div class="sectionbody">
<div class="ulist">
<ul>
<li>
<p>Improve reference documentation.</p>
</li>
</ul>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_acknowledgements"><a class="anchor" href="#_acknowledgements"></a>8. Acknowledgements</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The comprehensiveness of this library would not be
possible without the existence of the indispensable
resource that is the
<a href="http://sourceforge.net/p/predef/">Pre-defined C/C++ Compiler Macros</a>
Project. It was, and continues to be, the primary source
of the definitions that make up this library. Thanks
to Bjorn Reese and all the volunteers that make that
resource possible.</p>
</div>
<div class="paragraph">
<p>This library would be an incoherent mess if it weren&#8217;t for
Boost community that provided invaluable feedback for the
eight years that it took to polish into a useable form.
In particular I would like to thank: Mathias Gaunard,
Robert Stewart, Joël Lamotte, Lars Viklund, Nathan Ridge,
Artyom Beilis, Joshua Boyce, Gottlob Frege, Thomas Heller,
Edward Diener, Dave Abrahams, Iain Denniston, Dan Price,
Ioannis Papadopoulos, and Robert Ramey. And thanks to
Joel Falcou for managing the review of this library.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_colophon"><a class="anchor" href="#_colophon"></a>Colophon</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
<a href="https://www.boost.org/LICENSE_1_0.txt" class="bare">https://www.boost.org/LICENSE_1_0.txt</a>)</p>
</div>
<div class="paragraph">
<p>Copyright 2005-2021 René Ferdinand Rivera Morell; Copyright 2015 Charly Chevalier; Copyright 2015 Joel Falcou</p>
</div>
</div>
</div>
</div>
</body>
</html>