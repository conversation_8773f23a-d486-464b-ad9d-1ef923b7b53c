[/ 
  Copyright 2015 <PERSON>.
  Distributed under the Boost Software License, Version 1.0.
  (See accompanying file LICENSE_1_0.txt or copy at
  http://www.boost.org/LICENSE_1_0.txt).
]

[section:is_destructible is_destructible]

   template <class T>
   struct is_destructible : public __tof {};
    
__inherit If `T` does not have its destructor deleted
then inherits from __true_type, otherwise inherits from __false_type.  Type `T`
must be a complete type.

__compat This trait requires the C++11 features `decltype` and SFINAE-expression support for full support.
While there is some fallback code for cases where this is not the case, the trait should really be considered broken in that case.

__header ` #include <boost/type_traits/is_copy_constructible.hpp>` or ` #include <boost/type_traits.hpp>`

[endsect]

