<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>boost::cnv::stream Converter</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Convert 2.0">
<link rel="up" href="../converters_detail.html" title="Converters in Detail">
<link rel="prev" href="../converters_detail.html" title="Converters in Detail">
<link rel="next" href="stream_converter/locale_support.html" title="Locale Support">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../converters_detail.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../converters_detail.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="stream_converter/locale_support.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_convert.converters_detail.stream_converter"></a><a class="link" href="stream_converter.html" title="boost::cnv::stream Converter"><span class="emphasis"><em>boost::cnv::stream</em></span>
      Converter</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support">Formatting
        Support</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support.numeric_base">Numeric
          Base</a></span></dt>
<dt><span class="section"><a href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support.field_width__fill_character_and_adjustment">Field
          Width, Fill Character and Adjustment</a></span></dt>
<dt><span class="section"><a href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support.leading_whitespace_characters">Leading
          Whitespace Characters</a></span></dt>
<dt><span class="section"><a href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support.format_of_boolean_values">Format
          of Boolean Values</a></span></dt>
</dl></dd>
<dt><span class="section"><a href="stream_converter/locale_support.html">Locale
        Support</a></span></dt>
<dt><span class="section"><a href="stream_converter/supported_string_types.html">Supported
        String Types</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="stream_converter/supported_string_types.html#boost_convert.converters_detail.stream_converter.supported_string_types.wide_string">Wide
          String</a></span></dt>
<dt><span class="section"><a href="stream_converter/supported_string_types/custom_string_types.html">Custom
          String Types</a></span></dt>
</dl></dd>
<dt><span class="section"><a href="stream_converter/the___default_constructible__type_requirement.html">The
        <span class="emphasis"><em>Default Constructible</em></span> Type Requirement</a></span></dt>
</dl></div>
<p>
        The purpose of the converter is to provide conversion-related formatting
        and locale support not available with <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">lexical_cast</span></code>.
        Advantages of deploying a <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">stream</span></code>-based
        conversion engine are:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            availability and maturity;
          </li>
<li class="listitem">
            formatting and locale support;
          </li>
<li class="listitem">
            familiar interface and deployment;
          </li>
<li class="listitem">
            instant re-use of available standard manipulators (<code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">hex</span></code>,
            <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">setprecision</span></code>, <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">skipws</span></code>,
            etc.);
          </li>
<li class="listitem">
            extendibility via custom manipulators (see <a href="http://www.cecalc.ula.ve/documentacion/tutoriales/PGICDK/doc/pgC++_lib/stdlibug/str_5412.htm" target="_top">Stream
            Storage for Private Use: iword, pword, and xalloc</a> by Rogue Wave
            Software).
          </li>
</ul></div>
<p>
        The converter might be deployed as follows:
      </p>
<p>
</p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">convert</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">convert</span><span class="special">/</span><span class="identifier">stream</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">detail</span><span class="special">/</span><span class="identifier">lightweight_test</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">convert</span><span class="special">;</span>

<span class="keyword">struct</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">::</span><span class="identifier">by_default</span> <span class="special">:</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">::</span><span class="identifier">cstream</span> <span class="special">{};</span>
</pre>
<p>
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">int</span>    <span class="identifier">i2</span> <span class="special">=</span> <span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="string">"123"</span><span class="special">).</span><span class="identifier">value</span><span class="special">();</span>      <span class="comment">// Throws when fails.</span>
<span class="keyword">int</span>    <span class="identifier">i3</span> <span class="special">=</span> <span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="string">"uhm"</span><span class="special">).</span><span class="identifier">value_or</span><span class="special">(-</span><span class="number">1</span><span class="special">);</span> <span class="comment">// Returns -1 when fails.</span>
<span class="identifier">string</span> <span class="identifier">s2</span> <span class="special">=</span> <span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="number">123</span><span class="special">).</span><span class="identifier">value</span><span class="special">();</span>

<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">i2</span> <span class="special">==</span> <span class="number">123</span><span class="special">);</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">i3</span> <span class="special">==</span> <span class="special">-</span><span class="number">1</span><span class="special">);</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">s2</span> <span class="special">==</span> <span class="string">"123"</span><span class="special">);</span>
</pre>
<p>
      </p>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_convert.converters_detail.stream_converter.formatting_support"></a><a class="link" href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support" title="Formatting Support">Formatting
        Support</a>
</h4></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support.numeric_base">Numeric
          Base</a></span></dt>
<dt><span class="section"><a href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support.field_width__fill_character_and_adjustment">Field
          Width, Fill Character and Adjustment</a></span></dt>
<dt><span class="section"><a href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support.leading_whitespace_characters">Leading
          Whitespace Characters</a></span></dt>
<dt><span class="section"><a href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support.format_of_boolean_values">Format
          of Boolean Values</a></span></dt>
</dl></div>
<p>
          Formatting support is provided by the underlying <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">stringstream</span></code>.
          Consequently, the API heavily borrows formatting metaphors from this underlying
          component. One such metaphor is the <span class="emphasis"><em>manipulator</em></span> represented
          by <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">hex</span></code>, <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">dec</span></code>,
          <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">uppercase</span></code>, <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">scientific</span></code>,
          etc.
        </p>
<p>
          The following code demonstrates how <code class="computeroutput"><span class="keyword">char</span></code>
          and <code class="computeroutput"><span class="keyword">wchar_t</span></code> strings can be
          read in the <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">hex</span></code> or <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">dec</span></code>
          format:
        </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">::</span><span class="identifier">cstream</span> <span class="identifier">ccnv</span><span class="special">;</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">::</span><span class="identifier">wstream</span> <span class="identifier">wcnv</span><span class="special">;</span>

<span class="keyword">int</span> <span class="identifier">v01</span> <span class="special">=</span> <span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="string">"  FF"</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">hex</span><span class="special">)(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">skipws</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="number">0</span><span class="special">);</span>
<span class="keyword">int</span> <span class="identifier">v02</span> <span class="special">=</span> <span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="identifier">L</span><span class="string">"  F"</span><span class="special">,</span> <span class="identifier">wcnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">hex</span><span class="special">)(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">skipws</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="number">0</span><span class="special">);</span>
<span class="keyword">int</span> <span class="identifier">v03</span> <span class="special">=</span> <span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="string">"  FF"</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">dec</span><span class="special">)(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">skipws</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(-</span><span class="number">5</span><span class="special">);</span>
<span class="keyword">int</span> <span class="identifier">v04</span> <span class="special">=</span> <span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="identifier">L</span><span class="string">"  F"</span><span class="special">,</span> <span class="identifier">wcnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">dec</span><span class="special">)(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">skipws</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(-</span><span class="number">5</span><span class="special">);</span>

<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">v01</span> <span class="special">==</span> <span class="number">255</span><span class="special">);</span> <span class="comment">// "FF"</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">v02</span> <span class="special">==</span>  <span class="number">15</span><span class="special">);</span> <span class="comment">// L"F"</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">v03</span> <span class="special">==</span>  <span class="special">-</span><span class="number">5</span><span class="special">);</span> <span class="comment">// Failed to convert "FF" as decimal.</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">v04</span> <span class="special">==</span>  <span class="special">-</span><span class="number">5</span><span class="special">);</span> <span class="comment">// Failed to convert L"F" as decimal.</span>
</pre>
<p>
        </p>
<p>
          For batch-processing it might be more efficient to configure the converter
          once:
        </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">showbase</span><span class="special">)(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">uppercase</span><span class="special">)(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">hex</span><span class="special">);</span>

<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="number">255</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">,</span> <span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span> <span class="string">"0XFF"</span><span class="special">);</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span> <span class="number">15</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">,</span> <span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span>  <span class="string">"0XF"</span><span class="special">);</span>
</pre>
<p>
        </p>
<p>
          An alternative (generic) formatting interface is currently being extended
          and explored:
        </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">cnv</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">;</span>
<span class="keyword">namespace</span> <span class="identifier">arg</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">::</span><span class="identifier">parameter</span><span class="special">;</span>
</pre>
<p>
        </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">arg</span><span class="special">::</span><span class="identifier">base</span> <span class="special">=</span> <span class="identifier">cnv</span><span class="special">::</span><span class="identifier">base</span><span class="special">::</span><span class="identifier">dec</span><span class="special">)</span>
    <span class="special">(</span><span class="identifier">arg</span><span class="special">::</span><span class="identifier">uppercase</span> <span class="special">=</span> <span class="keyword">true</span><span class="special">)</span>
    <span class="special">(</span><span class="identifier">arg</span><span class="special">::</span><span class="identifier">notation</span> <span class="special">=</span> <span class="identifier">cnv</span><span class="special">::</span><span class="identifier">notation</span><span class="special">::</span><span class="identifier">scientific</span><span class="special">);</span>
</pre>
<p>
        </p>
<p>
          is equivalent to the following <span class="emphasis"><em>std::manipulator</em></span>-based
          variant:
        </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">dec</span><span class="special">)(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">uppercase</span><span class="special">)(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">scientific</span><span class="special">);</span>
</pre>
<p>
        </p>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_convert.converters_detail.stream_converter.formatting_support.numeric_base"></a><a class="link" href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support.numeric_base" title="Numeric Base">Numeric
          Base</a>
</h5></div></div></div>
<p>
</p>
<pre class="programlisting"><span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">convert</span><span class="special">;</span>
</pre>
<p>
          </p>
<p>
            The following example demonstrates the deployment of <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">dec</span></code>,
            <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">oct</span></code> <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">hex</span></code>
            manipulators:
          </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">::</span><span class="identifier">cstream</span> <span class="identifier">ccnv</span><span class="special">;</span>

<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span> <span class="string">"11"</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">hex</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="number">0</span><span class="special">)</span> <span class="special">==</span> <span class="number">17</span><span class="special">);</span> <span class="comment">// 11(16) = 17(10)</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span> <span class="string">"11"</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">oct</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="number">0</span><span class="special">)</span> <span class="special">==</span>  <span class="number">9</span><span class="special">);</span> <span class="comment">// 11(8)  = 9(10)</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span> <span class="string">"11"</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">dec</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="number">0</span><span class="special">)</span> <span class="special">==</span> <span class="number">11</span><span class="special">);</span>

<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span> <span class="number">18</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">hex</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span> <span class="string">"12"</span><span class="special">);</span> <span class="comment">// 18(10) = 12(16)</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span> <span class="number">10</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">oct</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span> <span class="string">"12"</span><span class="special">);</span> <span class="comment">// 10(10) = 12(8)</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span> <span class="number">12</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">dec</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span> <span class="string">"12"</span><span class="special">);</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="number">255</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">arg</span><span class="special">::</span><span class="identifier">base</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">::</span><span class="identifier">base</span><span class="special">::</span><span class="identifier">oct</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span> <span class="string">"377"</span><span class="special">);</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="number">255</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">arg</span><span class="special">::</span><span class="identifier">base</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">::</span><span class="identifier">base</span><span class="special">::</span><span class="identifier">hex</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span>  <span class="string">"ff"</span><span class="special">);</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="number">255</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">arg</span><span class="special">::</span><span class="identifier">base</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">::</span><span class="identifier">base</span><span class="special">::</span><span class="identifier">dec</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span> <span class="string">"255"</span><span class="special">);</span>

<span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">showbase</span><span class="special">);</span>

<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="number">18</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">hex</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span> <span class="string">"0x12"</span><span class="special">);</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="number">10</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">oct</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span>  <span class="string">"012"</span><span class="special">);</span>

<span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">uppercase</span><span class="special">);</span>

<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="number">18</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">hex</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span> <span class="string">"0X12"</span><span class="special">);</span>
</pre>
<p>
          </p>
<p>
            A more generic interface is also supported:
          </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">cnv</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">;</span>
<span class="keyword">namespace</span> <span class="identifier">arg</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">::</span><span class="identifier">parameter</span><span class="special">;</span>
</pre>
<p>
          </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="string">"11"</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">arg</span><span class="special">::</span><span class="identifier">base</span> <span class="special">=</span> <span class="identifier">cnv</span><span class="special">::</span><span class="identifier">base</span><span class="special">::</span><span class="identifier">hex</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="number">0</span><span class="special">)</span> <span class="special">==</span> <span class="number">17</span><span class="special">);</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="string">"11"</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">arg</span><span class="special">::</span><span class="identifier">base</span> <span class="special">=</span> <span class="identifier">cnv</span><span class="special">::</span><span class="identifier">base</span><span class="special">::</span><span class="identifier">oct</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="number">0</span><span class="special">)</span> <span class="special">==</span>  <span class="number">9</span><span class="special">);</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="string">"11"</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">arg</span><span class="special">::</span><span class="identifier">base</span> <span class="special">=</span> <span class="identifier">cnv</span><span class="special">::</span><span class="identifier">base</span><span class="special">::</span><span class="identifier">dec</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="number">0</span><span class="special">)</span> <span class="special">==</span> <span class="number">11</span><span class="special">);</span>
</pre>
<p>
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_convert.converters_detail.stream_converter.formatting_support.field_width__fill_character_and_adjustment"></a><a class="link" href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support.field_width__fill_character_and_adjustment" title="Field Width, Fill Character and Adjustment">Field
          Width, Fill Character and Adjustment</a>
</h5></div></div></div>
<p>
</p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">::</span><span class="identifier">cstream</span> <span class="identifier">cnv</span><span class="special">;</span>

<span class="identifier">boost</span><span class="special">::</span><span class="identifier">optional</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;</span> <span class="identifier">s01</span> <span class="special">=</span> <span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="number">12</span><span class="special">,</span> <span class="identifier">cnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">setw</span><span class="special">(</span><span class="number">4</span><span class="special">)));</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">optional</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;</span> <span class="identifier">s02</span> <span class="special">=</span> <span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="number">12</span><span class="special">,</span> <span class="identifier">cnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">setw</span><span class="special">(</span><span class="number">5</span><span class="special">))(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">setfill</span><span class="special">(</span><span class="char">'*'</span><span class="special">)));</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">optional</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;</span> <span class="identifier">s03</span> <span class="special">=</span> <span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="number">12</span><span class="special">,</span> <span class="identifier">cnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">setw</span><span class="special">(</span><span class="number">5</span><span class="special">))(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">setfill</span><span class="special">(</span><span class="char">'*'</span><span class="special">))(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">left</span><span class="special">));</span>

<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">s01</span> <span class="special">&amp;&amp;</span> <span class="identifier">s01</span><span class="special">.</span><span class="identifier">value</span><span class="special">()</span> <span class="special">==</span> <span class="string">"  12"</span><span class="special">);</span>  <span class="comment">// Field width = 4.</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">s02</span> <span class="special">&amp;&amp;</span> <span class="identifier">s02</span><span class="special">.</span><span class="identifier">value</span><span class="special">()</span> <span class="special">==</span> <span class="string">"***12"</span><span class="special">);</span> <span class="comment">// Field width = 5, filler = '*'.</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">s03</span> <span class="special">&amp;&amp;</span> <span class="identifier">s03</span><span class="special">.</span><span class="identifier">value</span><span class="special">()</span> <span class="special">==</span> <span class="string">"12***"</span><span class="special">);</span> <span class="comment">// Field width = 5, filler = '*', left adjustment</span>
</pre>
<p>
          </p>
<p>
            It needs to be remembered that <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">::</span><span class="identifier">stream</span></code>
            converter uses <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">stream</span></code> as its underlying conversion
            engine. Consequently, formatting-related behavior are driven by the
            <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">stream</span></code>. Namely, after every operation
            is performed, the <span class="emphasis"><em>default field width is restored</em></span>.
            The values of the fill character and the adjustment remain unchanged
            until they are modified explicitly.
          </p>
<p>
</p>
<pre class="programlisting"><span class="comment">// The fill and adjustment remain '*' and 'left'.</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">optional</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;</span> <span class="identifier">s11</span> <span class="special">=</span> <span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="number">12</span><span class="special">,</span> <span class="identifier">cnv</span><span class="special">(</span><span class="identifier">arg</span><span class="special">::</span><span class="identifier">width</span> <span class="special">=</span> <span class="number">4</span><span class="special">));</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">optional</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;</span> <span class="identifier">s12</span> <span class="special">=</span> <span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="number">12</span><span class="special">,</span> <span class="identifier">cnv</span><span class="special">(</span><span class="identifier">arg</span><span class="special">::</span><span class="identifier">width</span> <span class="special">=</span> <span class="number">5</span><span class="special">)</span>
                                                     <span class="special">(</span><span class="identifier">arg</span><span class="special">::</span><span class="identifier">fill</span> <span class="special">=</span> <span class="char">' '</span><span class="special">)</span>
                                                     <span class="special">(</span><span class="identifier">arg</span><span class="special">::</span><span class="identifier">adjust</span> <span class="special">=</span> <span class="identifier">cnv</span><span class="special">::</span><span class="identifier">adjust</span><span class="special">::</span><span class="identifier">right</span><span class="special">));</span>

<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">s11</span> <span class="special">&amp;&amp;</span> <span class="identifier">s11</span><span class="special">.</span><span class="identifier">value</span><span class="special">()</span> <span class="special">==</span> <span class="string">"12**"</span><span class="special">);</span>  <span class="comment">// Field width was set to 4.</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">s12</span> <span class="special">&amp;&amp;</span> <span class="identifier">s12</span><span class="special">.</span><span class="identifier">value</span><span class="special">()</span> <span class="special">==</span> <span class="string">"   12"</span><span class="special">);</span> <span class="comment">// Field width was set to 5 with the ' ' filler.</span>
</pre>
<p>
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_convert.converters_detail.stream_converter.formatting_support.leading_whitespace_characters"></a><a class="link" href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support.leading_whitespace_characters" title="Leading Whitespace Characters">Leading
          Whitespace Characters</a>
</h5></div></div></div>
<p>
</p>
<pre class="programlisting"><span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">convert</span><span class="special">;</span>
</pre>
<p>
          </p>
<p>
</p>
<pre class="programlisting">    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">cnv</span><span class="special">::</span><span class="identifier">cstream</span>    <span class="identifier">ccnv</span><span class="special">;</span>
    <span class="keyword">char</span> <span class="keyword">const</span><span class="special">*</span> <span class="keyword">const</span> <span class="identifier">cstr_good</span> <span class="special">=</span> <span class="string">"  123"</span><span class="special">;</span>
    <span class="keyword">char</span> <span class="keyword">const</span><span class="special">*</span> <span class="keyword">const</span>  <span class="identifier">cstr_bad</span> <span class="special">=</span> <span class="string">"  123 "</span><span class="special">;</span> <span class="comment">// std::skipws only affects leading spaces.</span>

    <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">skipws</span><span class="special">);</span>        <span class="comment">// Ignore leading whitespaces</span>
<span class="comment">//  ccnv(arg::skipws = true); // Ignore leading whitespaces. Alternative interface</span>

    <span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="identifier">cstr_good</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">).</span><span class="identifier">value_or</span><span class="special">(</span><span class="number">0</span><span class="special">)</span> <span class="special">==</span> <span class="number">123</span><span class="special">);</span>
    <span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="string">"  123"</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span> <span class="string">"123"</span><span class="special">);</span>

    <span class="identifier">BOOST_TEST</span><span class="special">(!</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="identifier">cstr_bad</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">));</span>

    <span class="identifier">ccnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">noskipws</span><span class="special">);</span>       <span class="comment">// Do not ignore leading whitespaces</span>
<span class="comment">//  ccnv(arg::skipws = false); // Do not ignore leading whitespaces. Alternative interface</span>

    <span class="comment">// All conversions fail.</span>
    <span class="identifier">BOOST_TEST</span><span class="special">(!</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="identifier">cstr_good</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">));</span>
    <span class="identifier">BOOST_TEST</span><span class="special">(!</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span> <span class="identifier">cstr_bad</span><span class="special">,</span> <span class="identifier">ccnv</span><span class="special">));</span>
</pre>
<p>
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_convert.converters_detail.stream_converter.formatting_support.format_of_boolean_values"></a><a class="link" href="stream_converter.html#boost_convert.converters_detail.stream_converter.formatting_support.format_of_boolean_values" title="Format of Boolean Values">Format
          of Boolean Values</a>
</h5></div></div></div>
<p>
</p>
<pre class="programlisting"><span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">convert</span><span class="special">;</span>
</pre>
<p>
          </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span> <span class="keyword">true</span><span class="special">,</span> <span class="identifier">cnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">boolalpha</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span>  <span class="string">"true"</span><span class="special">);</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="keyword">false</span><span class="special">,</span> <span class="identifier">cnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">boolalpha</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span> <span class="string">"false"</span><span class="special">);</span>

<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">bool</span><span class="special">&gt;(</span> <span class="string">"true"</span><span class="special">,</span> <span class="identifier">cnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">boolalpha</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="keyword">false</span><span class="special">)</span> <span class="special">==</span>  <span class="keyword">true</span><span class="special">);</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">bool</span><span class="special">&gt;(</span><span class="string">"false"</span><span class="special">,</span> <span class="identifier">cnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">boolalpha</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span> <span class="keyword">true</span><span class="special">)</span> <span class="special">==</span> <span class="keyword">false</span><span class="special">);</span>

<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span> <span class="keyword">true</span><span class="special">,</span> <span class="identifier">cnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">noboolalpha</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span> <span class="string">"1"</span><span class="special">);</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="identifier">string</span><span class="special">&gt;(</span><span class="keyword">false</span><span class="special">,</span> <span class="identifier">cnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">noboolalpha</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="string">"bad"</span><span class="special">)</span> <span class="special">==</span> <span class="string">"0"</span><span class="special">);</span>

<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">bool</span><span class="special">&gt;(</span><span class="string">"1"</span><span class="special">,</span> <span class="identifier">cnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">noboolalpha</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span><span class="keyword">false</span><span class="special">)</span> <span class="special">==</span>  <span class="keyword">true</span><span class="special">);</span>
<span class="identifier">BOOST_TEST</span><span class="special">(</span><span class="identifier">convert</span><span class="special">&lt;</span><span class="keyword">bool</span><span class="special">&gt;(</span><span class="string">"0"</span><span class="special">,</span> <span class="identifier">cnv</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">noboolalpha</span><span class="special">)).</span><span class="identifier">value_or</span><span class="special">(</span> <span class="keyword">true</span><span class="special">)</span> <span class="special">==</span> <span class="keyword">false</span><span class="special">);</span>
</pre>
<p>
          </p>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2009-2022 Vladimir
      Batov<p>
        Distributed under the Boost Software License, Version 1.0. See copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>.
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../converters_detail.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../converters_detail.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="stream_converter/locale_support.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
