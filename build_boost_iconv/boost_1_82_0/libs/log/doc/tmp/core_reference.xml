<?xml version="1.0" standalone="yes"?>
<library-reference id="core_components"><title>Core components</title><header name="boost/log/core/core.hpp">
<para><para><PERSON><PERSON> </para>

<para>19.04.2007</para>

This header contains logging core class definition. </para><namespace name="boost">
<namespace name="log">
<class name="core"><purpose>Logging library core class. </purpose><description><para>The logging core is used to interconnect log sources and sinks. It also provides a number of basic features, like global filtering and global and thread-specific attribute storage.</para><para>The logging core is a singleton. Users can acquire the core instance by calling the static method <computeroutput>get</computeroutput>. </para></description><typedef name="exception_handler_type"><purpose>Exception handler function type. </purpose><type><emphasis>unspecified</emphasis></type></typedef>
<method-group name="public member functions">
<method name="set_logging_enabled"><type>bool</type><parameter name="enabled"><paramtype>bool</paramtype><default>true</default><description><para>The actual flag of logging activity. </para></description></parameter><description><para>The method enables or disables logging.</para><para>Setting this status to <computeroutput>false</computeroutput> allows you to completely wipe out any logging activity, including filtering and generation of attribute values. It is useful if you want to completely disable logging in a running application. The state of logging does not alter any other properties of the logging library, such as filters or sinks, so you can enable logging with the very same settings that you had when the logging was disabled. This feature may also be useful if you want to perform major changes to logging configuration and don't want your application to block on opening or pushing a log record.</para><para>By default logging is enabled.</para><para>

</para></description><returns><para>The previous value of enabled/disabled logging flag </para>
</returns></method>
<method name="get_logging_enabled" cv="const"><type>bool</type><description><para>The method allows to detect if logging is enabled. See the comment for <computeroutput>set_logging_enabled</computeroutput>. </para></description></method>
<method name="set_filter"><type>void</type><parameter name="filter"><paramtype>filter const &amp;</paramtype><description><para>The filter function object to be installed. </para></description></parameter><description><para>The method sets the global logging filter. The filter is applied to every log record that is processed.</para><para>
</para></description></method>
<method name="reset_filter"><type>void</type><description><para>The method removes the global logging filter. All log records are passed to sinks without global filtering applied. </para></description></method>
<method name="add_sink"><type>void</type><parameter name="s"><paramtype>shared_ptr&lt; sinks::sink &gt; const &amp;</paramtype><description><para>The sink to be registered. </para></description></parameter><description><para>The method adds a new sink. The sink is included into logging process immediately after being added and until being removed. No sink can be added more than once at the same time. If the sink is already registered, the call is ignored.</para><para>
</para></description></method>
<method name="remove_sink"><type>void</type><parameter name="s"><paramtype>shared_ptr&lt; sinks::sink &gt; const &amp;</paramtype><description><para>The sink to be unregistered. </para></description></parameter><description><para>The method removes the sink from the output. The sink will not receive any log records after removal. The call has no effect if the sink is not registered.</para><para>
</para></description></method>
<method name="remove_all_sinks"><type>void</type><description><para>The method removes all registered sinks from the output. The sinks will not receive any log records after removal. </para></description></method>
<method name="flush"><type>void</type><description><para>The method performs flush on all registered sinks.</para><para><note><para>This method may take long time to complete as it may block until all sinks manage to process all buffered log records. The call will also block all logging attempts until the operation completes. </para>
</note>
</para></description></method>
<method name="add_global_attribute"><type>std::pair&lt; attribute_set::iterator, bool &gt;</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype><description><para>The attribute name. </para></description></parameter><parameter name="attr"><paramtype>attribute const &amp;</paramtype><description><para>The attribute factory. </para></description></parameter><description><para>The method adds an attribute to the global attribute set. The attribute will be implicitly added to every log record.</para><para>

</para></description><returns><para>A pair of values. If the second member is <computeroutput>true</computeroutput>, then the attribute is added and the first member points to the attribute. Otherwise the attribute was not added and the first member points to the attribute that prevents addition. </para>
</returns></method>
<method name="remove_global_attribute"><type>void</type><parameter name="it"><paramtype>attribute_set::iterator</paramtype><description><para>Iterator to the previously added attribute. </para></description></parameter><description><para>The method removes an attribute from the global attribute set.</para><para>


</para></description><requires><para>The attribute was added with the <computeroutput>add_global_attribute</computeroutput> call. </para>
</requires><postconditions><para>The attribute is no longer registered as a global attribute. The iterator is invalidated after removal.</para>
</postconditions></method>
<method name="get_global_attributes" cv="const"><type>attribute_set</type><description><para>The method returns a copy of the complete set of currently registered global attributes. </para></description></method>
<method name="set_global_attributes"><type>void</type><parameter name="attrs"><paramtype>attribute_set const &amp;</paramtype><description><para>The set of attributes to be installed. </para></description></parameter><description><para>The method replaces the complete set of currently registered global attributes with the provided set.</para><para><note><para>The method invalidates all iterators and references that may have been returned from the <computeroutput>add_global_attribute</computeroutput> method.</para>
</note>

</para></description></method>
<method name="add_thread_attribute"><type>std::pair&lt; attribute_set::iterator, bool &gt;</type><parameter name="name"><paramtype>attribute_name const &amp;</paramtype><description><para>The attribute name. </para></description></parameter><parameter name="attr"><paramtype>attribute const &amp;</paramtype><description><para>The attribute factory. </para></description></parameter><description><para>The method adds an attribute to the thread-specific attribute set. The attribute will be implicitly added to every log record made in the current thread.</para><para><note><para>In single-threaded build the effect is the same as adding the attribute globally. This, however, does not imply that iterators to thread-specific and global attributes are interchangeable.</para>
</note>


</para></description><returns><para>A pair of values. If the second member is <computeroutput>true</computeroutput>, then the attribute is added and the first member points to the attribute. Otherwise the attribute was not added and the first member points to the attribute that prevents addition. </para>
</returns></method>
<method name="remove_thread_attribute"><type>void</type><parameter name="it"><paramtype>attribute_set::iterator</paramtype><description><para>Iterator to the previously added attribute. </para></description></parameter><description><para>The method removes an attribute from the thread-specific attribute set.</para><para>


</para></description><requires><para>The attribute was added with the <computeroutput>add_thread_attribute</computeroutput> call. </para>
</requires><postconditions><para>The attribute is no longer registered as a thread-specific attribute. The iterator is invalidated after removal.</para>
</postconditions></method>
<method name="get_thread_attributes" cv="const"><type>attribute_set</type><description><para>The method returns a copy of the complete set of currently registered thread-specific attributes. </para></description></method>
<method name="set_thread_attributes"><type>void</type><parameter name="attrs"><paramtype>attribute_set const &amp;</paramtype><description><para>The set of attributes to be installed. </para></description></parameter><description><para>The method replaces the complete set of currently registered thread-specific attributes with the provided set.</para><para><note><para>The method invalidates all iterators and references that may have been returned from the <computeroutput>add_thread_attribute</computeroutput> method.</para>
</note>

</para></description></method>
<method name="set_exception_handler"><type>void</type><parameter name="handler"><paramtype>exception_handler_type const &amp;</paramtype><description><para>Exception handling function</para></description></parameter><description><para>The method sets exception handler function. The function will be called with no arguments in case if an exception occurs during either <computeroutput>open_record</computeroutput> or <computeroutput>push_record</computeroutput> method execution. Since exception handler is called from a <computeroutput>catch</computeroutput> statement, the exception can be rethrown in order to determine its type.</para><para>By default no handler is installed, thus any exception is propagated as usual.</para><para><para><emphasis role="bold">See Also:</emphasis><para>See also: <computeroutput>utility/exception_handler.hpp</computeroutput> </para>
</para>

<note><para>The exception handler can be invoked in several threads concurrently. Thread interruptions are not affected by exception handlers. </para>
</note>
</para></description></method>
<method name="open_record"><type><classname>record</classname></type><parameter name="source_attributes"><paramtype>attribute_set const &amp;</paramtype><description><para>The set of source-specific attributes to be attached to the record to be opened. </para></description></parameter><description><para>The method attempts to open a new record to be written. While attempting to open a log record all filtering is applied. A successfully opened record can be pushed further to sinks by calling the <computeroutput>push_record</computeroutput> method or simply destroyed by destroying the returned object.</para><para>More than one open records are allowed, such records exist independently. All attribute values are acquired during opening the record and do not interact between records.</para><para>The returned records can be copied, however, they must not be passed between different threads.</para><para>

<emphasis role="bold">Throws:</emphasis> If an exception handler is installed, only throws if the handler throws. Otherwise may throw if one of the sinks throws, or some system resource limitation is reached. </para></description><returns><para>A valid log record if the record is opened, an invalid record object if not (e.g. because it didn't pass filtering).</para>
</returns></method>
<method name="open_record"><type><classname>record</classname></type><parameter name="source_attributes"><paramtype>attribute_value_set const &amp;</paramtype><description><para>The set of source-specific attribute values to be attached to the record to be opened. </para></description></parameter><description><para>The method attempts to open a new record to be written. While attempting to open a log record all filtering is applied. A successfully opened record can be pushed further to sinks by calling the <computeroutput>push_record</computeroutput> method or simply destroyed by destroying the returned object.</para><para>More than one open records are allowed, such records exist independently. All attribute values are acquired during opening the record and do not interact between records.</para><para>The returned records can be copied, however, they must not be passed between different threads.</para><para>

<emphasis role="bold">Throws:</emphasis> If an exception handler is installed, only throws if the handler throws. Otherwise may throw if one of the sinks throws, or some system resource limitation is reached. </para></description><returns><para>A valid log record if the record is opened, an invalid record object if not (e.g. because it didn't pass filtering).</para>
</returns></method>
<method name="open_record"><type><classname>record</classname></type><parameter name="source_attributes"><paramtype>attribute_value_set &amp;&amp;</paramtype><description><para>The set of source-specific attribute values to be attached to the record to be opened. The contents of this container are unspecified after this call. </para></description></parameter><description><para>The method attempts to open a new record to be written. While attempting to open a log record all filtering is applied. A successfully opened record can be pushed further to sinks by calling the <computeroutput>push_record</computeroutput> method or simply destroyed by destroying the returned object.</para><para>More than one open records are allowed, such records exist independently. All attribute values are acquired during opening the record and do not interact between records.</para><para>The returned records can be copied, however, they must not be passed between different threads.</para><para>

<emphasis role="bold">Throws:</emphasis> If an exception handler is installed, only throws if the handler throws. Otherwise may throw if one of the sinks throws, or some system resource limitation is reached. </para></description><returns><para>A valid log record if the record is opened, an invalid record object if not (e.g. because it didn't pass filtering).</para>
</returns></method>
<method name="push_record"><type>void</type><parameter name="rec"><paramtype><classname>record</classname> &amp;&amp;</paramtype><description><para>A previously successfully opened log record.</para></description></parameter><description><para>The method pushes the record to sinks. The record is moved from in the process.</para><para>


<emphasis role="bold">Throws:</emphasis> If an exception handler is installed, only throws if the handler throws. Otherwise may throw if one of the sinks throws. </para></description><requires><para><computeroutput>!!rec == true</computeroutput> </para>
</requires><postconditions><para><computeroutput>!rec == true</computeroutput> </para>
</postconditions></method>
</method-group>
<destructor><description><para>Destructor. Destroys the core, releases any sinks and attributes that were registered. </para></description></destructor>
<constructor cv="= delete"><parameter name=""><paramtype><classname>core</classname> const &amp;</paramtype></parameter></constructor>
<copy-assignment cv="= delete"><type><classname>core</classname> &amp;</type><parameter name=""><paramtype><classname>core</classname> const &amp;</paramtype></parameter></copy-assignment>
<method-group name="public static functions">
<method name="get" specifiers="static"><type>core_ptr</type><description><para>
</para></description><returns><para>The method returns a pointer to the logging core singleton instance. </para>
</returns></method>
</method-group>
</class><typedef name="core_ptr"><type>shared_ptr&lt; <classname>core</classname> &gt;</type></typedef>


</namespace>
</namespace>
</header>
<header name="boost/log/core/record.hpp">
<para><para>Andrey Semashev </para>

<para>09.03.2009</para>

This header contains a logging record class definition. </para><namespace name="boost">
<namespace name="log">
<class name="record"><purpose>Logging record class. </purpose><description><para>The logging record encapsulates all information related to a single logging statement, in particular, attribute values view and the log message string. The record can be updated before pushing for further processing to the logging core. </para></description><method-group name="public member functions">
<method name="attribute_values" cv="noexcept"><type>attribute_value_set &amp;</type><description><para>

</para></description><requires><para><computeroutput>!!*this</computeroutput> </para>
</requires><returns><para>A reference to the set of attribute values attached to this record</para>
</returns></method>
<method name="attribute_values" cv="const noexcept"><type>attribute_value_set const  &amp;</type><description><para>

</para></description><requires><para><computeroutput>!!*this</computeroutput> </para>
</requires><returns><para>A reference to the set of attribute values attached to this record</para>
</returns></method>
<method name="conversion-operator" cv="const noexcept" specifiers="explicit"><type>bool</type><description><para>Conversion to an unspecified boolean type</para><para>
</para></description><returns><para><computeroutput>true</computeroutput>, if the <computeroutput>*this</computeroutput> identifies a log record, <computeroutput>false</computeroutput>, if the <computeroutput>*this</computeroutput> is not valid </para>
</returns></method>
<method name="operator!" cv="const noexcept"><type>bool</type><description><para>Inverted conversion to an unspecified boolean type</para><para>
</para></description><returns><para><computeroutput>false</computeroutput>, if the <computeroutput>*this</computeroutput> identifies a log record, <computeroutput>true</computeroutput>, if the <computeroutput>*this</computeroutput> is not valid </para>
</returns></method>
<method name="swap" cv="noexcept"><type>void</type><parameter name="that"><paramtype><classname>record</classname> &amp;</paramtype><description><para>Another record to swap with <emphasis role="bold">Throws:</emphasis> Nothing </para></description></parameter><description><para>Swaps two handles</para><para>
</para></description></method>
<method name="reset" cv="noexcept"><type>void</type><description><para>Resets the log record handle. If there are no other handles left, the log record is closed and all resources referenced by the record are released.</para><para>
</para></description><postconditions><para><computeroutput>!*this == true</computeroutput> </para>
</postconditions></method>
<method name="operator[]" cv="const"><type>attribute_value_set::mapped_type</type><parameter name="name"><paramtype>attribute_value_set::key_type</paramtype><description><para>Attribute name. </para></description></parameter><description><para>Attribute value lookup.</para><para>

</para></description><returns><para>An <computeroutput>attribute_value</computeroutput>, non-empty if it is found, empty otherwise. </para>
</returns></method>
<method name="operator[]" cv="const"><type>result_of::extract&lt; typename expressions::attribute_keyword&lt; DescriptorT, ActorT &gt;::value_type, DescriptorT &gt;::type</type><template>
          <template-type-parameter name="DescriptorT"/>
          <template-nontype-parameter name="ActorT"><type>template&lt; typename &gt; class</type></template-nontype-parameter>
        </template><parameter name="keyword"><paramtype>expressions::attribute_keyword&lt; DescriptorT, ActorT &gt; const &amp;</paramtype><description><para>Attribute keyword. </para></description></parameter><description><para>Attribute value lookup.</para><para>

</para></description><returns><para>A <computeroutput>value_ref</computeroutput> with extracted attribute value if it is found, empty <computeroutput>value_ref</computeroutput> otherwise. </para>
</returns></method>
<method name="lock"><type><classname>record_view</classname></type><description><para>The function ensures that the log record does not depend on any thread-specific data. Then the record contents are used to construct a <computeroutput><classname alt="boost::log::record_view">record_view</classname></computeroutput> which is returned from the function. The record is no longer valid after the call.</para><para>


</para></description><requires><para><computeroutput>!!*this</computeroutput> </para>
</requires><postconditions><para><computeroutput>!*this</computeroutput> </para>
</postconditions><returns><para>The record view that contains all attribute values from the original record. </para>
</returns></method>
</method-group>
<constructor cv="noexcept"><description><para>Default constructor. Creates an empty record that is equivalent to the invalid record handle.</para><para>
</para></description><postconditions><para><computeroutput>!*this == true</computeroutput> </para>
</postconditions></constructor>
<constructor cv="noexcept"><parameter name="that"><paramtype><classname>record</classname> &amp;&amp;</paramtype></parameter><description><para>Move constructor. Source record contents unspecified after the operation. </para></description></constructor>
<destructor><description><para>Destructor. Destroys the record, releases any sinks and attribute values that were involved in processing this record. </para></description></destructor>
<copy-assignment cv="noexcept"><type><classname>record</classname> &amp;</type><parameter name="that"><paramtype><classname>record</classname> &amp;&amp;</paramtype></parameter><description><para>Move assignment. Source record contents unspecified after the operation. </para></description></copy-assignment>
</class>
<function name="swap"><type>void</type><parameter name="left"><paramtype><classname>record</classname> &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>record</classname> &amp;</paramtype></parameter><description><para>A free-standing swap function overload for <computeroutput>record</computeroutput> </para></description></function>
</namespace>
</namespace>
</header>
<header name="boost/log/core/record_view.hpp">
<para><para>Andrey Semashev </para>

<para>09.03.2009</para>

This header contains a logging record view class definition. </para><namespace name="boost">
<namespace name="log">
<class name="record_view"><purpose>Logging record view class. </purpose><description><para>The logging record encapsulates all information related to a single logging statement, in particular, attribute values view and the log message string. The view is immutable, it is implemented as a wrapper around a reference-counted implementation. </para></description><method-group name="public member functions">
<method name="attribute_values" cv="const noexcept"><type>attribute_value_set const  &amp;</type><description><para>

</para></description><requires><para><computeroutput>!!*this</computeroutput> </para>
</requires><returns><para>A reference to the set of attribute values attached to this record</para>
</returns></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype><classname>record_view</classname> const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Equality comparison</para><para>

</para></description><returns><para><computeroutput>true</computeroutput> if both <computeroutput>*this</computeroutput> and <emphasis>that</emphasis> identify the same log record or both do not identify any record, <computeroutput>false</computeroutput> otherwise. </para>
</returns></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><parameter name="that"><paramtype><classname>record_view</classname> const &amp;</paramtype><description><para>Comparand </para></description></parameter><description><para>Inequality comparison</para><para>

</para></description><returns><para><computeroutput>!(*this == that)</computeroutput> </para>
</returns></method>
<method name="conversion-operator" cv="const noexcept" specifiers="explicit"><type>bool</type><description><para>Conversion to an unspecified boolean type</para><para>
</para></description><returns><para><computeroutput>true</computeroutput>, if the <computeroutput>*this</computeroutput> identifies a log record, <computeroutput>false</computeroutput>, if the <computeroutput>*this</computeroutput> is not valid </para>
</returns></method>
<method name="operator!" cv="const noexcept"><type>bool</type><description><para>Inverted conversion to an unspecified boolean type</para><para>
</para></description><returns><para><computeroutput>false</computeroutput>, if the <computeroutput>*this</computeroutput> identifies a log record, <computeroutput>true</computeroutput>, if the <computeroutput>*this</computeroutput> is not valid </para>
</returns></method>
<method name="swap" cv="noexcept"><type>void</type><parameter name="that"><paramtype><classname>record_view</classname> &amp;</paramtype><description><para>Another record to swap with <emphasis role="bold">Throws:</emphasis> Nothing </para></description></parameter><description><para>Swaps two handles</para><para>
</para></description></method>
<method name="reset" cv="noexcept"><type>void</type><description><para>Resets the log record handle. If there are no other handles left, the log record is closed and all resources referenced by the record are released.</para><para>
</para></description><postconditions><para><computeroutput>!*this == true</computeroutput> </para>
</postconditions></method>
<method name="operator[]" cv="const"><type>attribute_value_set::mapped_type</type><parameter name="name"><paramtype>attribute_value_set::key_type</paramtype><description><para>Attribute name. </para></description></parameter><description><para>Attribute value lookup.</para><para>

</para></description><returns><para>An <computeroutput>attribute_value</computeroutput>, non-empty if it is found, empty otherwise. </para>
</returns></method>
<method name="operator[]" cv="const"><type>result_of::extract&lt; typename expressions::attribute_keyword&lt; DescriptorT, ActorT &gt;::value_type, DescriptorT &gt;::type</type><template>
          <template-type-parameter name="DescriptorT"/>
          <template-nontype-parameter name="ActorT"><type>template&lt; typename &gt; class</type></template-nontype-parameter>
        </template><parameter name="keyword"><paramtype>expressions::attribute_keyword&lt; DescriptorT, ActorT &gt; const &amp;</paramtype><description><para>Attribute keyword. </para></description></parameter><description><para>Attribute value lookup.</para><para>

</para></description><returns><para>A <computeroutput>value_ref</computeroutput> with extracted attribute value if it is found, empty <computeroutput>value_ref</computeroutput> otherwise. </para>
</returns></method>
</method-group>
<constructor><description><para>Default constructor. Creates an empty record view that is equivalent to the invalid record handle.</para><para>
</para></description><postconditions><para><computeroutput>!*this == true</computeroutput> </para>
</postconditions></constructor>
<constructor cv="noexcept"><parameter name="that"><paramtype><classname>record_view</classname> const &amp;</paramtype></parameter><description><para>Copy constructor </para></description></constructor>
<constructor cv="noexcept"><parameter name="that"><paramtype><classname>record_view</classname> &amp;&amp;</paramtype></parameter><description><para>Move constructor. Source record contents unspecified after the operation. </para></description></constructor>
<destructor><description><para>Destructor. Destroys the record, releases any sinks and attribute values that were involved in processing this record. </para></description></destructor>
<copy-assignment cv="noexcept"><type><classname>record_view</classname> &amp;</type><parameter name="that"><paramtype><classname>record_view</classname> const &amp;</paramtype></parameter><description><para>Copy assignment </para></description></copy-assignment>
<copy-assignment cv="noexcept"><type><classname>record_view</classname> &amp;</type><parameter name="that"><paramtype><classname>record_view</classname> &amp;&amp;</paramtype></parameter><description><para>Move assignment. Source record contents unspecified after the operation. </para></description></copy-assignment>
</class><function name="swap"><type>void</type><parameter name="left"><paramtype><classname>record_view</classname> &amp;</paramtype></parameter><parameter name="right"><paramtype><classname>record_view</classname> &amp;</paramtype></parameter><description><para>A free-standing swap function overload for <computeroutput><classname alt="boost::log::record_view">record_view</classname></computeroutput> </para></description></function>

</namespace>
</namespace>
</header>
</library-reference>