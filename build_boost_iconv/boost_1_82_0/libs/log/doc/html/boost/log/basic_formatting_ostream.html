<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template basic_formatting_ostream</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Log v2">
<link rel="up" href="../../utilities.html#header.boost.log.utility.formatting_ostream_fwd_hpp" title="Header &lt;boost/log/utility/formatting_ostream_fwd.hpp&gt;">
<link rel="prev" href="operator_idm34936.html" title="Function template operator&lt;&lt;">
<link rel="next" href="as_action_adapter.html" title="Struct template as_action_adapter">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="operator_idm34936.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../utilities.html#header.boost.log.utility.formatting_ostream_fwd_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="as_action_adapter.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.log.basic_formatting_ostream"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template basic_formatting_ostream</span></h2>
<p>boost::log::basic_formatting_ostream — Stream for log records formatting. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../utilities.html#header.boost.log.utility.formatting_ostream_fwd_hpp" title="Header &lt;boost/log/utility/formatting_ostream_fwd.hpp&gt;">boost/log/utility/formatting_ostream_fwd.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">,</span> <span class="keyword">typename</span> AllocatorT<span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// <a class="link" href="basic_formatting_ostream.html#boost.log.basic_formatting_ostreamconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="basic_formatting_ostream.html#idm35309-bb"><span class="identifier">basic_formatting_ostream</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="basic_formatting_ostream.html#idm35316-bb"><span class="identifier">basic_formatting_ostream</span></a><span class="special">(</span><span class="identifier">string_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html#idm35372-bb"><span class="identifier">basic_formatting_ostream</span></a><span class="special">(</span><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> 
  <a class="link" href="basic_formatting_ostream.html#idm35377-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html#idm35327-bb"><span class="special">~</span><span class="identifier">basic_formatting_ostream</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="basic_formatting_ostream.html#idm34986-bb">public member functions</a></span>
  <span class="keyword">void</span> <a class="link" href="basic_formatting_ostream.html#idm34987-bb"><span class="identifier">attach</span></a><span class="special">(</span><span class="identifier">string_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_formatting_ostream.html#idm34996-bb"><span class="identifier">detach</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">string_type</span> <span class="keyword">const</span>  <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35000-bb"><span class="identifier">str</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">ostream_type</span> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35006-bb"><span class="identifier">stream</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">ostream_type</span> <span class="keyword">const</span>  <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35012-bb"><span class="identifier">stream</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">fmtflags</span> <a class="link" href="basic_formatting_ostream.html#idm35018-bb"><span class="identifier">flags</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">fmtflags</span> <a class="link" href="basic_formatting_ostream.html#idm35020-bb"><span class="identifier">flags</span></a><span class="special">(</span><span class="identifier">fmtflags</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">fmtflags</span> <a class="link" href="basic_formatting_ostream.html#idm35024-bb"><span class="identifier">setf</span></a><span class="special">(</span><span class="identifier">fmtflags</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">fmtflags</span> <a class="link" href="basic_formatting_ostream.html#idm35028-bb"><span class="identifier">setf</span></a><span class="special">(</span><span class="identifier">fmtflags</span><span class="special">,</span> <span class="identifier">fmtflags</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_formatting_ostream.html#idm35034-bb"><span class="identifier">unsetf</span></a><span class="special">(</span><span class="identifier">fmtflags</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> <a class="link" href="basic_formatting_ostream.html#idm35038-bb"><span class="identifier">precision</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> <a class="link" href="basic_formatting_ostream.html#idm35040-bb"><span class="identifier">precision</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> <a class="link" href="basic_formatting_ostream.html#idm35044-bb"><span class="identifier">width</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> <a class="link" href="basic_formatting_ostream.html#idm35046-bb"><span class="identifier">width</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span> <a class="link" href="basic_formatting_ostream.html#idm35050-bb"><span class="identifier">getloc</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span> <a class="link" href="basic_formatting_ostream.html#idm35052-bb"><span class="identifier">imbue</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">long</span> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35056-bb"><span class="identifier">iword</span></a><span class="special">(</span><span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <span class="special">*</span><span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35060-bb"><span class="identifier">pword</span></a><span class="special">(</span><span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_formatting_ostream.html#idm35064-bb"><span class="identifier">register_callback</span></a><span class="special">(</span><span class="identifier">event_callback</span><span class="special">,</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="basic_formatting_ostream.html#idm35070-bb"><span class="keyword">operator</span> <span class="keyword">bool</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="basic_formatting_ostream.html#idm35072-bb"><span class="keyword">operator</span><span class="special">!</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iostate</span> <a class="link" href="basic_formatting_ostream.html#idm35074-bb"><span class="identifier">rdstate</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_formatting_ostream.html#idm35076-bb"><span class="identifier">clear</span></a><span class="special">(</span><span class="identifier">iostate</span> <span class="special">=</span> <span class="identifier">goodbit</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_formatting_ostream.html#idm35081-bb"><span class="identifier">setstate</span></a><span class="special">(</span><span class="identifier">iostate</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="basic_formatting_ostream.html#idm35085-bb"><span class="identifier">good</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="basic_formatting_ostream.html#idm35087-bb"><span class="identifier">eof</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="basic_formatting_ostream.html#idm35089-bb"><span class="identifier">fail</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="basic_formatting_ostream.html#idm35091-bb"><span class="identifier">bad</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iostate</span> <a class="link" href="basic_formatting_ostream.html#idm35093-bb"><span class="identifier">exceptions</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_formatting_ostream.html#idm35095-bb"><span class="identifier">exceptions</span></a><span class="special">(</span><span class="identifier">iostate</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">ostream_type</span> <span class="special">*</span> <a class="link" href="basic_formatting_ostream.html#idm35099-bb"><span class="identifier">tie</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">ostream_type</span> <span class="special">*</span> <a class="link" href="basic_formatting_ostream.html#idm35101-bb"><span class="identifier">tie</span></a><span class="special">(</span><span class="identifier">ostream_type</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">streambuf_type</span> <span class="special">*</span> <a class="link" href="basic_formatting_ostream.html#idm35105-bb"><span class="identifier">rdbuf</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> 
  <a class="link" href="basic_formatting_ostream.html#idm35107-bb"><span class="identifier">copyfmt</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ios</span><span class="special">&lt;</span> <span class="identifier">char_type</span><span class="special">,</span> <span class="identifier">traits_type</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35112-bb"><span class="identifier">copyfmt</span></a><span class="special">(</span><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">char_type</span> <a class="link" href="basic_formatting_ostream.html#idm35118-bb"><span class="identifier">fill</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">char_type</span> <a class="link" href="basic_formatting_ostream.html#idm35120-bb"><span class="identifier">fill</span></a><span class="special">(</span><span class="identifier">char_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">char</span> <a class="link" href="basic_formatting_ostream.html#idm35124-bb"><span class="identifier">narrow</span></a><span class="special">(</span><span class="identifier">char_type</span><span class="special">,</span> <span class="keyword">char</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">char_type</span> <a class="link" href="basic_formatting_ostream.html#idm35130-bb"><span class="identifier">widen</span></a><span class="special">(</span><span class="keyword">char</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35134-bb"><span class="identifier">flush</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">pos_type</span> <a class="link" href="basic_formatting_ostream.html#idm35137-bb"><span class="identifier">tellp</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35139-bb"><span class="identifier">seekp</span></a><span class="special">(</span><span class="identifier">pos_type</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35144-bb"><span class="identifier">seekp</span></a><span class="special">(</span><span class="identifier">off_type</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">ios_base</span><span class="special">::</span><span class="identifier">seekdir</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35151-bb"><span class="identifier">put</span></a><span class="special">(</span><span class="identifier">char_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> OtherCharT<span class="special">&gt;</span> <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <a class="link" href="basic_formatting_ostream.html#idm35156-bb"><span class="identifier">put</span></a><span class="special">(</span><span class="identifier">OtherCharT</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35163-bb"><span class="identifier">write</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">char_type</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> OtherCharT<span class="special">&gt;</span> 
    <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <a class="link" href="basic_formatting_ostream.html#idm35170-bb"><span class="identifier">write</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">OtherCharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35179-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="identifier">ios_base_manip</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35184-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="identifier">basic_ios_manip</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35189-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="identifier">stream_manip</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35194-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">char</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35199-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35204-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">wchar_t</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35209-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">const</span> <span class="keyword">wchar_t</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35214-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">char16_t</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35219-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char16_t</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35224-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">char32_t</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35229-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char32_t</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35234-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">bool</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35239-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">signed</span> <span class="keyword">char</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35244-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">unsigned</span> <span class="keyword">char</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35249-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">short</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35254-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">unsigned</span> <span class="keyword">short</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35259-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35264-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35269-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">long</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35274-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">unsigned</span> <span class="keyword">long</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35279-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">long</span> <span class="keyword">long</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35284-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">unsigned</span> <span class="keyword">long</span> <span class="keyword">long</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35289-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">float</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35294-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">double</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a class="link" href="basic_formatting_ostream.html#idm35299-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> 
  <a class="link" href="basic_formatting_ostream.html#idm35304-bb"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_streambuf</span><span class="special">&lt;</span> <span class="identifier">char_type</span><span class="special">,</span> <span class="identifier">traits_type</span> <span class="special">&gt;</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="basic_formatting_ostream.html#idm35330-bb">public static functions</a></span>
  <span class="keyword">static</span> <span class="keyword">int</span> <a class="link" href="basic_formatting_ostream.html#idm35331-bb"><span class="identifier">xalloc</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">bool</span> <a class="link" href="basic_formatting_ostream.html#idm35333-bb"><span class="identifier">sync_with_stdio</span></a><span class="special">(</span><span class="keyword">bool</span> <span class="special">=</span> <span class="keyword">true</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="basic_formatting_ostream.html#idm35338-bb">protected member functions</a></span>
  <span class="keyword">void</span> <a class="link" href="basic_formatting_ostream.html#idm35339-bb"><span class="identifier">init_stream</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="basic_formatting_ostream.html#idm35341-bb">private member functions</a></span>
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> 
  <a class="link" href="basic_formatting_ostream.html#idm35342-bb"><span class="identifier">formatted_write</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">char_type</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> OtherCharT<span class="special">&gt;</span> 
    <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> 
    <a class="link" href="basic_formatting_ostream.html#idm35349-bb"><span class="identifier">formatted_write</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">OtherCharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_formatting_ostream.html#idm35358-bb"><span class="identifier">aligned_write</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">char_type</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> OtherCharT<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="basic_formatting_ostream.html#idm35364-bb"><span class="identifier">aligned_write</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">OtherCharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm74742"></a><h2>Description</h2>
<p>Stream wrapper for log records formatting.</p>
<p>This stream wrapper is used by the library for log record formatting. It implements the standard string stream interface with a few differences:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem"><p>It does not derive from standard types <code class="computeroutput">std::basic_ostream</code>, <code class="computeroutput">std::basic_ios</code> and <code class="computeroutput">std::ios_base</code>, although it tries to implement their interfaces closely. There are a few small differences, mostly regarding <code class="computeroutput">rdbuf</code> and <code class="computeroutput">str</code> signatures, as well as the supported insertion operator overloads. The actual wrapped stream can be accessed through the <code class="computeroutput">stream</code> methods. </p></li>
<li class="listitem"><p>By default, <code class="computeroutput">bool</code> values are formatted using alphabetical representation rather than numeric. </p></li>
<li class="listitem"><p>The stream supports writing strings of character types different from the stream character type. The stream will perform character code conversion as needed using the imbued locale. </p></li>
<li class="listitem"><p>The stream operates on an external string object rather than on the embedded one. The string can be attached or detached from the stream dynamically.</p></li>
</ul></div>
<p>
Although <code class="computeroutput"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a></code> does not derive from <code class="computeroutput">std::basic_ostream</code>, users are not required to add special overloads of <code class="computeroutput">operator&lt;&lt;</code> for it since the stream will by default reuse the operators for <code class="computeroutput">std::basic_ostream</code>. However, one can define special overloads of <code class="computeroutput">operator&lt;&lt;</code> for <code class="computeroutput"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a></code> if a certain type needs special formatting when output to log. </p>
<div class="refsect2">
<a name="idm74771"></a><h3>
<a name="boost.log.basic_formatting_ostreamconstruct-copy-destruct"></a><code class="computeroutput">basic_formatting_ostream</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a name="idm35309-bb"></a><span class="identifier">basic_formatting_ostream</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>Default constructor. Creates an empty record that is equivalent to the invalid record handle. The stream capability is not available after construction.</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">!*this == true</code> </p></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm35316-bb"></a><span class="identifier">basic_formatting_ostream</span><span class="special">(</span><span class="identifier">string_type</span> <span class="special">&amp;</span> str<span class="special">)</span><span class="special">;</span></pre>
<p>Initializing constructor. Attaches the string to the constructed stream. The string will be used to store the formatted characters.</p>
<p>

</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">str</code></span></p></td>
<td><p>The string buffer to attach. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">!*this == false</code> </p></td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm35372-bb"></a><span class="identifier">basic_formatting_ostream</span><span class="special">(</span><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="keyword">const</span> <span class="special">&amp;</span> that<span class="special">)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span></pre>Copy constructor (closed) </li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> 
<a name="idm35377-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="keyword">const</span> <span class="special">&amp;</span> that<span class="special">)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span></pre>Assignment (closed) </li>
<li class="listitem">
<pre class="literallayout"><a name="idm35327-bb"></a><span class="special">~</span><span class="identifier">basic_formatting_ostream</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>Destructor. Destroys the record, releases any sinks and attribute values that were involved in processing this record. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm74859"></a><h3>
<a name="idm34986-bb"></a><code class="computeroutput">basic_formatting_ostream</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm34987-bb"></a><span class="identifier">attach</span><span class="special">(</span><span class="identifier">string_type</span> <span class="special">&amp;</span> str<span class="special">)</span><span class="special">;</span></pre>
<p>Attaches the stream to the string. The string will be used to store the formatted characters.</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">str</code></span></p></td>
<td><p>The string buffer to attach. </p></td>
</tr></tbody>
</table></div></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm34996-bb"></a><span class="identifier">detach</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>Detaches the stream from the string. Any buffered data is flushed to the string. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">string_type</span> <span class="keyword">const</span>  <span class="special">&amp;</span> <a name="idm35000-bb"></a><span class="identifier">str</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Reference to the attached string. The string must be attached before calling this method. </p></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">ostream_type</span> <span class="special">&amp;</span> <a name="idm35006-bb"></a><span class="identifier">stream</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Reference to the wrapped stream </p></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">ostream_type</span> <span class="keyword">const</span>  <span class="special">&amp;</span> <a name="idm35012-bb"></a><span class="identifier">stream</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Reference to the wrapped stream </p></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem"><pre class="literallayout"><span class="identifier">fmtflags</span> <a name="idm35018-bb"></a><span class="identifier">flags</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">fmtflags</span> <a name="idm35020-bb"></a><span class="identifier">flags</span><span class="special">(</span><span class="identifier">fmtflags</span> f<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">fmtflags</span> <a name="idm35024-bb"></a><span class="identifier">setf</span><span class="special">(</span><span class="identifier">fmtflags</span> f<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">fmtflags</span> <a name="idm35028-bb"></a><span class="identifier">setf</span><span class="special">(</span><span class="identifier">fmtflags</span> f<span class="special">,</span> <span class="identifier">fmtflags</span> mask<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm35034-bb"></a><span class="identifier">unsetf</span><span class="special">(</span><span class="identifier">fmtflags</span> f<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> <a name="idm35038-bb"></a><span class="identifier">precision</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> <a name="idm35040-bb"></a><span class="identifier">precision</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> p<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> <a name="idm35044-bb"></a><span class="identifier">width</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> <a name="idm35046-bb"></a><span class="identifier">width</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> w<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span> <a name="idm35050-bb"></a><span class="identifier">getloc</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span> <a name="idm35052-bb"></a><span class="identifier">imbue</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span> <span class="keyword">const</span> <span class="special">&amp;</span> loc<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">long</span> <span class="special">&amp;</span> <a name="idm35056-bb"></a><span class="identifier">iword</span><span class="special">(</span><span class="keyword">int</span> index<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <span class="special">*</span><span class="special">&amp;</span> <a name="idm35060-bb"></a><span class="identifier">pword</span><span class="special">(</span><span class="keyword">int</span> index<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm35064-bb"></a><span class="identifier">register_callback</span><span class="special">(</span><span class="identifier">event_callback</span> fn<span class="special">,</span> <span class="keyword">int</span> index<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">explicit</span> <a name="idm35070-bb"></a><span class="keyword">operator</span> <span class="keyword">bool</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">bool</span> <a name="idm35072-bb"></a><span class="keyword">operator</span><span class="special">!</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">iostate</span> <a name="idm35074-bb"></a><span class="identifier">rdstate</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm35076-bb"></a><span class="identifier">clear</span><span class="special">(</span><span class="identifier">iostate</span> state <span class="special">=</span> <span class="identifier">goodbit</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm35081-bb"></a><span class="identifier">setstate</span><span class="special">(</span><span class="identifier">iostate</span> state<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">bool</span> <a name="idm35085-bb"></a><span class="identifier">good</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">bool</span> <a name="idm35087-bb"></a><span class="identifier">eof</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">bool</span> <a name="idm35089-bb"></a><span class="identifier">fail</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">bool</span> <a name="idm35091-bb"></a><span class="identifier">bad</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">iostate</span> <a name="idm35093-bb"></a><span class="identifier">exceptions</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm35095-bb"></a><span class="identifier">exceptions</span><span class="special">(</span><span class="identifier">iostate</span> s<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">ostream_type</span> <span class="special">*</span> <a name="idm35099-bb"></a><span class="identifier">tie</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">ostream_type</span> <span class="special">*</span> <a name="idm35101-bb"></a><span class="identifier">tie</span><span class="special">(</span><span class="identifier">ostream_type</span> <span class="special">*</span> strm<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">streambuf_type</span> <span class="special">*</span> <a name="idm35105-bb"></a><span class="identifier">rdbuf</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> 
<a name="idm35107-bb"></a><span class="identifier">copyfmt</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ios</span><span class="special">&lt;</span> <span class="identifier">char_type</span><span class="special">,</span> <span class="identifier">traits_type</span> <span class="special">&gt;</span> <span class="special">&amp;</span> rhs<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35112-bb"></a><span class="identifier">copyfmt</span><span class="special">(</span><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> rhs<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">char_type</span> <a name="idm35118-bb"></a><span class="identifier">fill</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">char_type</span> <a name="idm35120-bb"></a><span class="identifier">fill</span><span class="special">(</span><span class="identifier">char_type</span> ch<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">char</span> <a name="idm35124-bb"></a><span class="identifier">narrow</span><span class="special">(</span><span class="identifier">char_type</span> ch<span class="special">,</span> <span class="keyword">char</span> def<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">char_type</span> <a name="idm35130-bb"></a><span class="identifier">widen</span><span class="special">(</span><span class="keyword">char</span> ch<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35134-bb"></a><span class="identifier">flush</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">pos_type</span> <a name="idm35137-bb"></a><span class="identifier">tellp</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35139-bb"></a><span class="identifier">seekp</span><span class="special">(</span><span class="identifier">pos_type</span> pos<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35144-bb"></a><span class="identifier">seekp</span><span class="special">(</span><span class="identifier">off_type</span> off<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">ios_base</span><span class="special">::</span><span class="identifier">seekdir</span> dir<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35151-bb"></a><span class="identifier">put</span><span class="special">(</span><span class="identifier">char_type</span> c<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> OtherCharT<span class="special">&gt;</span> <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <a name="idm35156-bb"></a><span class="identifier">put</span><span class="special">(</span><span class="identifier">OtherCharT</span> c<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35163-bb"></a><span class="identifier">write</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">char_type</span> <span class="special">*</span> p<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> size<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> OtherCharT<span class="special">&gt;</span> 
  <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <a name="idm35170-bb"></a><span class="identifier">write</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">OtherCharT</span> <span class="special">*</span> p<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> size<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35179-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">ios_base_manip</span> manip<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35184-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">basic_ios_manip</span> manip<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35189-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">stream_manip</span> manip<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35194-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">char</span> c<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35199-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span> p<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35204-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">wchar_t</span> c<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35209-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">wchar_t</span> <span class="special">*</span> p<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35214-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">char16_t</span> c<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35219-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char16_t</span> <span class="special">*</span> p<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35224-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">char32_t</span> c<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35229-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char32_t</span> <span class="special">*</span> p<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35234-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">bool</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35239-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">signed</span> <span class="keyword">char</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35244-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="keyword">char</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35249-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">short</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35254-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="keyword">short</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35259-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">int</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35264-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="keyword">int</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35269-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">long</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35274-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="keyword">long</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35279-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">long</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35284-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">unsigned</span> <span class="keyword">long</span> <span class="keyword">long</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35289-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">float</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35294-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">double</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> <a name="idm35299-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="keyword">long</span> <span class="keyword">double</span> value<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> 
<a name="idm35304-bb"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_streambuf</span><span class="special">&lt;</span> <span class="identifier">char_type</span><span class="special">,</span> <span class="identifier">traits_type</span> <span class="special">&gt;</span> <span class="special">*</span> buf<span class="special">)</span><span class="special">;</span></pre></li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm75787"></a><h3>
<a name="idm35330-bb"></a><code class="computeroutput">basic_formatting_ostream</code> public static functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem"><pre class="literallayout"><span class="keyword">static</span> <span class="keyword">int</span> <a name="idm35331-bb"></a><span class="identifier">xalloc</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">static</span> <span class="keyword">bool</span> <a name="idm35333-bb"></a><span class="identifier">sync_with_stdio</span><span class="special">(</span><span class="keyword">bool</span> sync <span class="special">=</span> <span class="keyword">true</span><span class="special">)</span><span class="special">;</span></pre></li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm75815"></a><h3>
<a name="idm35338-bb"></a><code class="computeroutput">basic_formatting_ostream</code> protected member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm35339-bb"></a><span class="identifier">init_stream</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li></ol></div>
</div>
<div class="refsect2">
<a name="idm75829"></a><h3>
<a name="idm35341-bb"></a><code class="computeroutput">basic_formatting_ostream</code> private member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem"><pre class="literallayout"><a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> 
<a name="idm35342-bb"></a><span class="identifier">formatted_write</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">char_type</span> <span class="special">*</span> p<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> size<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> OtherCharT<span class="special">&gt;</span> 
  <a class="link" href="basic_formatting_ostream.html" title="Class template basic_formatting_ostream">basic_formatting_ostream</a> <span class="special">&amp;</span> 
  <a name="idm35349-bb"></a><span class="identifier">formatted_write</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">OtherCharT</span> <span class="special">*</span> p<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> size<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm35358-bb"></a><span class="identifier">aligned_write</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">char_type</span> <span class="special">*</span> p<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> size<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> OtherCharT<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm35364-bb"></a><span class="identifier">aligned_write</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">OtherCharT</span> <span class="special">*</span> p<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">streamsize</span> size<span class="special">)</span><span class="special">;</span></pre></li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2007-2022 Andrey Semashev<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>).
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="operator_idm34936.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../utilities.html#header.boost.log.utility.formatting_ostream_fwd_hpp"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="as_action_adapter.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
