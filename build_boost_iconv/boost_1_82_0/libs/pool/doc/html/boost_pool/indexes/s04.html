<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Index</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Boost.Pool">
<link rel="up" href="../indexes.html" title="Indexes">
<link rel="prev" href="s03.html" title="Typedef Index">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="s03.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../indexes.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="idm7510"></a>Index</h3></div></div></div>
<p><a class="link" href="s04.html#idx_id_63">A</a> <a class="link" href="s04.html#idx_id_64">B</a> <a class="link" href="s04.html#idx_id_65">C</a> <a class="link" href="s04.html#idx_id_66">D</a> <a class="link" href="s04.html#idx_id_67">E</a> <a class="link" href="s04.html#idx_id_68">F</a> <a class="link" href="s04.html#idx_id_69">G</a> <a class="link" href="s04.html#idx_id_70">H</a> <a class="link" href="s04.html#idx_id_71">I</a> <a class="link" href="s04.html#idx_id_72">J</a> <a class="link" href="s04.html#idx_id_73">L</a> <a class="link" href="s04.html#idx_id_74">M</a> <a class="link" href="s04.html#idx_id_75">N</a> <a class="link" href="s04.html#idx_id_76">O</a> <a class="link" href="s04.html#idx_id_77">P</a> <a class="link" href="s04.html#idx_id_78">R</a> <a class="link" href="s04.html#idx_id_79">S</a> <a class="link" href="s04.html#idx_id_80">T</a> <a class="link" href="s04.html#idx_id_81">U</a> <a class="link" href="s04.html#idx_id_82">V</a> <a class="link" href="s04.html#idx_id_83">Z</a></p>
<div class="variablelist"><dl class="variablelist">
<dt>
<a name="idx_id_63"></a><span class="term">A</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">address</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">add_block</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">add_ordered_block</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">alignment</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_hpp.html" title="Header &lt;boost/pool/pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">How Contiguous Chunks are Handled</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">allocate</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">allocation</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">Allocation and Deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/faq.html" title="Appendix B: FAQ"><span class="index-entry-level-1">Appendix B: FAQ</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/implementations.html" title="Appendix F: Other Implementations"><span class="index-entry-level-1">Appendix F: Other Implementations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/references.html" title="Appendix G: References"><span class="index-entry-level-1">Appendix G: References</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/todo.html" title="Appendix H: Future plans"><span class="index-entry-level-1">Appendix H: Future plans</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">Basic ideas behind pooling</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">Boost Pool Interfaces - What interfaces are provided and when to use each one.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/object_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool_alloc.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/simple_segregated_storage.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">How Contiguous Chunks are Handled</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">Simple Segregated Storage</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Allocation and Deallocation</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">ordered</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">size</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Appendix B: FAQ</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/faq.html" title="Appendix B: FAQ"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/faq.html" title="Appendix B: FAQ"><span class="index-entry-level-1">deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/faq.html" title="Appendix B: FAQ"><span class="index-entry-level-1">interface</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/faq.html" title="Appendix B: FAQ"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/faq.html" title="Appendix B: FAQ"><span class="index-entry-level-1">objects</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Appendix F: Other Implementations</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/implementations.html" title="Appendix F: Other Implementations"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/implementations.html" title="Appendix F: Other Implementations"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/implementations.html" title="Appendix F: Other Implementations"><span class="index-entry-level-1">concepts</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/implementations.html" title="Appendix F: Other Implementations"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/implementations.html" title="Appendix F: Other Implementations"><span class="index-entry-level-1">portable</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/implementations.html" title="Appendix F: Other Implementations"><span class="index-entry-level-1">segregated</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Appendix G: References</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/references.html" title="Appendix G: References"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/references.html" title="Appendix G: References"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/references.html" title="Appendix G: References"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/references.html" title="Appendix G: References"><span class="index-entry-level-1">segregated</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Appendix H: Future plans</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/todo.html" title="Appendix H: Future plans"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/todo.html" title="Appendix H: Future plans"><span class="index-entry-level-1">interface</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/todo.html" title="Appendix H: Future plans"><span class="index-entry-level-1">singleton</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/todo.html" title="Appendix H: Future plans"><span class="index-entry-level-1">singleton_pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">automatic</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/object_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_64"></a><span class="term">B</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Basic ideas behind pooling</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">new</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">ordered</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">segregated</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">size</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">block</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">Allocation and Deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/implementations.html" title="Appendix F: Other Implementations"><span class="index-entry-level-1">Appendix F: Other Implementations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">Basic ideas behind pooling</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">Boost Pool Interfaces - What interfaces are provided and when to use each one.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/simple_segregated_storage.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">How Contiguous Chunks are Handled</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Segregation" title="Table 5. Segregation"><span class="index-entry-level-1">Segregation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">Simple Segregated Storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">Struct default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.ss_symbols" title="Table 1. Symbol Table"><span class="index-entry-level-1">Symbol Table</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">The UserAllocator Concept</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator.userallocator_requirements" title="Table 7. UserAllocator Requirements"><span class="index-entry-level-1">UserAllocator Requirements</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Boost Pool Interfaces - What interfaces are provided and when to use each one.</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">concepts</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">interface</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">O</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">ordered</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">singleton</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_POOL_VALIDATE_INTERNALS</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/simple_segregated_storage.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../BOOST_POOL_VALIDATE_INTERNALS.html" title="Macro BOOST_POOL_VALIDATE_INTERNALS"><span class="index-entry-level-1">Macro BOOST_POOL_VALIDATE_INTERNALS</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">build</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/installation.html" title="Installation"><span class="index-entry-level-1">Installation</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Building the Test Programs</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/testing.html" title="Building the Test Programs"><span class="index-entry-level-1">jamfile</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_65"></a><span class="term">C</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">chunk</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">Allocation and Deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">Basic ideas behind pooling</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">Boost Pool Interfaces - What interfaces are provided and when to use each one.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_hpp.html" title="Header &lt;boost/pool/pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/simple_segregated_storage.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">How Contiguous Chunks are Handled</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Segregation" title="Table 5. Segregation"><span class="index-entry-level-1">Segregation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">Simple Segregated Storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.ss_symbols" title="Table 1. Symbol Table"><span class="index-entry-level-1">Symbol Table</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">The UserAllocator Concept</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Class template fast_pool_allocator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">address</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">allocate</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">construct</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">deallocate</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">destroy</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">main</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">max_size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">singleton</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">const_pointer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">other</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">pointer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">rebind</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">value_type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Class template object_pool</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">automatic</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">construct</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">destroy</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">include</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">is_from</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">new</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">nextof</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">next_size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">O</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">ptr</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">segregated</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">set_next_size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">store</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Class template pool</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">alignment</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">is_from</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">malloc_need_resize</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">new</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">nextof</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">O</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">ordered</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">ordered_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">ordered_malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">ordered_malloc_need_resize</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">purge_memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">release_memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">segregated</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">set_max_size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">set_next_size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">store</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Class template pool_allocator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">address</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">allocate</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">construct</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">deallocate</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">destroy</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">initialization</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">main</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">max_size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">singleton</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">const_pointer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">other</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">pointer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">rebind</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">value_type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Class template simple_segregated_storage</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">add_block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">add_ordered_block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">alignment</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">find_prev</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">free_n</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">list</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">malloc_n</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">nextof</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">ordered</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">ordered_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">ordered_free_n</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">ptr</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">segregate</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">segregated</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">size_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">try_malloc_n</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">zero</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Class template singleton_pool</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">difference_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">get_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">interface</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">is_from</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">main</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">mutex</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">normally</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">object_creator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">ordered</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">ordered_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">ordered_malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">purge_memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">release_memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">singleton</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">size_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">tag</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">user_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">concepts</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/implementations.html" title="Appendix F: Other Implementations"><span class="index-entry-level-1">Appendix F: Other Implementations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">Boost Pool Interfaces - What interfaces are provided and when to use each one.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/conventions.html" title="Documentation Naming and Formatting Conventions"><span class="index-entry-level-1">Documentation Naming and Formatting Conventions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">The UserAllocator Concept</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">construct</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Constructors, Destructors, and State</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Constructors" title="Table 4. Constructors, Destructors, and State"><span class="index-entry-level-1">new</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Constructors" title="Table 4. Constructors, Destructors, and State"><span class="index-entry-level-1">ordered</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">const_pointer</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">const_reference</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">conventions</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/conventions.html" title="Documentation Naming and Formatting Conventions"><span class="index-entry-level-1">Documentation Naming and Formatting Conventions</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_66"></a><span class="term">D</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">deallocate</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">deallocation</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">Allocation and Deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/faq.html" title="Appendix B: FAQ"><span class="index-entry-level-1">Appendix B: FAQ</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool_alloc.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/simple_segregated_storage.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">Simple Segregated Storage</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">default_user_allocator_malloc_free</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">Struct default_user_allocator_malloc_free</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">default_user_allocator_new_delete</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">destroy</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">difference_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">Struct default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Documentation Naming and Formatting Conventions</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/conventions.html" title="Documentation Naming and Formatting Conventions"><span class="index-entry-level-1">concepts</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/conventions.html" title="Documentation Naming and Formatting Conventions"><span class="index-entry-level-1">conventions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/conventions.html" title="Documentation Naming and Formatting Conventions"><span class="index-entry-level-1">include</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/conventions.html" title="Documentation Naming and Formatting Conventions"><span class="index-entry-level-1">naming</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/conventions.html" title="Documentation Naming and Formatting Conventions"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/conventions.html" title="Documentation Naming and Formatting Conventions"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_67"></a><span class="term">E</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">elements</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">element_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_68"></a><span class="term">F</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fast_pool_allocator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223/rebind.html" title="Struct template rebind"><span class="index-entry-level-1">Struct template rebind</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">fast_pool_allocator_tag</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_tag.html" title="Struct fast_pool_allocator_tag"><span class="index-entry-level-1">Struct fast_pool_allocator_tag</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">find_prev</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">free</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">Struct default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">free_n</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_69"></a><span class="term">G</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">get_pool</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Guaranteeing Alignment - How we guarantee alignment portably.</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">address</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">alignment</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">automatic</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">concepts</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">elements</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">lcm</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">new</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">overview</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">padding</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">portable</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">segregated</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">sizeof</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_70"></a><span class="term">H</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/pool/object_pool.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">automatic</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/pool/pool.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_hpp.html" title="Header &lt;boost/pool/pool.hpp&gt;"><span class="index-entry-level-1">alignment</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_hpp.html" title="Header &lt;boost/pool/pool.hpp&gt;"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_hpp.html" title="Header &lt;boost/pool/pool.hpp&gt;"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_hpp.html" title="Header &lt;boost/pool/pool.hpp&gt;"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_hpp.html" title="Header &lt;boost/pool/pool.hpp&gt;"><span class="index-entry-level-1">segregated</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/pool/poolfwd.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/poolfwd_hpp.html" title="Header &lt;boost/pool/poolfwd.hpp&gt;"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/poolfwd_hpp.html" title="Header &lt;boost/pool/poolfwd.hpp&gt;"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/poolfwd_hpp.html" title="Header &lt;boost/pool/poolfwd.hpp&gt;"><span class="index-entry-level-1">object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/poolfwd_hpp.html" title="Header &lt;boost/pool/poolfwd.hpp&gt;"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/pool/pool_alloc.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">interface</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">main</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">singleton</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">sizeof</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/pool/simple_segregated_storage.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">BOOST_POOL_VALIDATE_INTERNALS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">segregated</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/pool/singleton_pool.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">interface</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">singleton</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">headers</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">Basic ideas behind pooling</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/object_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_hpp.html" title="Header &lt;boost/pool/pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/poolfwd_hpp.html" title="Header &lt;boost/pool/poolfwd.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/poolfwd.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool_alloc.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/simple_segregated_storage.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/singleton_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/usage.html" title="How do I use Pool?"><span class="index-entry-level-1">How do I use Pool?</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/installation.html" title="Installation"><span class="index-entry-level-1">Installation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../BOOST_POOL_VALIDATE_INTERNALS.html" title="Macro BOOST_POOL_VALIDATE_INTERNALS"><span class="index-entry-level-1">Macro BOOST_POOL_VALIDATE_INTERNALS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">Struct default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_tag.html" title="Struct fast_pool_allocator_tag"><span class="index-entry-level-1">Struct fast_pool_allocator_tag</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool/object_creator.html" title="Struct object_creator"><span class="index-entry-level-1">Struct object_creator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_tag.html" title="Struct pool_allocator_tag"><span class="index-entry-level-1">Struct pool_allocator_tag</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223/rebind.html" title="Struct template rebind"><span class="index-entry-level-1">Struct template rebind</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">How Contiguous Chunks are Handled</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">alignment</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">ordered</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">padding</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">sizeof</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">How do I use Pool?</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/usage.html" title="How do I use Pool?"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/usage.html" title="How do I use Pool?"><span class="index-entry-level-1">include</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/usage.html" title="How do I use Pool?"><span class="index-entry-level-1">interface</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_71"></a><span class="term">I</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">include</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/conventions.html" title="Documentation Naming and Formatting Conventions"><span class="index-entry-level-1">Documentation Naming and Formatting Conventions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/usage.html" title="How do I use Pool?"><span class="index-entry-level-1">How do I use Pool?</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/installation.html" title="Installation"><span class="index-entry-level-1">Installation</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">initialization</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Installation</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/installation.html" title="Installation"><span class="index-entry-level-1">build</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/installation.html" title="Installation"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/installation.html" title="Installation"><span class="index-entry-level-1">include</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../pool/installation.html" title="Installation"><span class="index-entry-level-1">installation</span></a></strong></span></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">interface</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/faq.html" title="Appendix B: FAQ"><span class="index-entry-level-1">Appendix B: FAQ</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/todo.html" title="Appendix H: Future plans"><span class="index-entry-level-1">Appendix H: Future plans</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">Boost Pool Interfaces - What interfaces are provided and when to use each one.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool_alloc.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/singleton_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/usage.html" title="How do I use Pool?"><span class="index-entry-level-1">How do I use Pool?</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces" title="Pool Interfaces"><span class="index-entry-level-1">Pool Interfaces</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">The UserAllocator Concept</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introduction</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">concepts</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">interface</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">segregated</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introduction and Overview</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool.html" title="Introduction and Overview"><span class="index-entry-level-1">overview</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">is_from</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_72"></a><span class="term">J</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">jamfile</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/testing.html" title="Building the Test Programs"><span class="index-entry-level-1">Building the Test Programs</span></a></p></li></ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_73"></a><span class="term">L</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lcm</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">list</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_74"></a><span class="term">M</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_POOL_VALIDATE_INTERNALS</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../BOOST_POOL_VALIDATE_INTERNALS.html" title="Macro BOOST_POOL_VALIDATE_INTERNALS"><span class="index-entry-level-1">BOOST_POOL_VALIDATE_INTERNALS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../BOOST_POOL_VALIDATE_INTERNALS.html" title="Macro BOOST_POOL_VALIDATE_INTERNALS"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../BOOST_POOL_VALIDATE_INTERNALS.html" title="Macro BOOST_POOL_VALIDATE_INTERNALS"><span class="index-entry-level-1">segregated</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">main</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool_alloc.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">malloc</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">Allocation and Deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/references.html" title="Appendix G: References"><span class="index-entry-level-1">Appendix G: References</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">Basic ideas behind pooling</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">Struct default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator.userallocator_requirements" title="Table 7. UserAllocator Requirements"><span class="index-entry-level-1">UserAllocator Requirements</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">malloc_n</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">malloc_need_resize</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">max_size</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">memory</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/faq.html" title="Appendix B: FAQ"><span class="index-entry-level-1">Appendix B: FAQ</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/references.html" title="Appendix G: References"><span class="index-entry-level-1">Appendix G: References</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">Basic ideas behind pooling</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">Boost Pool Interfaces - What interfaces are provided and when to use each one.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/object_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_hpp.html" title="Header &lt;boost/pool/pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool_alloc.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/simple_segregated_storage.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">How Contiguous Chunks are Handled</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Segregation" title="Table 5. Segregation"><span class="index-entry-level-1">Segregation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">Simple Segregated Storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">The UserAllocator Concept</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator.userallocator_requirements" title="Table 7. UserAllocator Requirements"><span class="index-entry-level-1">UserAllocator Requirements</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">mutex</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_75"></a><span class="term">N</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">naming</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/conventions.html" title="Documentation Naming and Formatting Conventions"><span class="index-entry-level-1">Documentation Naming and Formatting Conventions</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">new</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">Basic ideas behind pooling</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Constructors" title="Table 4. Constructors, Destructors, and State"><span class="index-entry-level-1">Constructors, Destructors, and State</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">nextof</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">next_size</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">normally</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_76"></a><span class="term">O</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">O</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">Boost Pool Interfaces - What interfaces are provided and when to use each one.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">Simple Segregated Storage</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">objects</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/faq.html" title="Appendix B: FAQ"><span class="index-entry-level-1">Appendix B: FAQ</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/implementations.html" title="Appendix F: Other Implementations"><span class="index-entry-level-1">Appendix F: Other Implementations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">Basic ideas behind pooling</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">Boost Pool Interfaces - What interfaces are provided and when to use each one.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/conventions.html" title="Documentation Naming and Formatting Conventions"><span class="index-entry-level-1">Documentation Naming and Formatting Conventions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/object_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/poolfwd_hpp.html" title="Header &lt;boost/pool/poolfwd.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/poolfwd.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/simple_segregated_storage.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/singleton_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">How Contiguous Chunks are Handled</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Segregation" title="Table 5. Segregation"><span class="index-entry-level-1">Segregation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">Simple Segregated Storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">Struct default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool/object_creator.html" title="Struct object_creator"><span class="index-entry-level-1">Struct object_creator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">The UserAllocator Concept</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator.userallocator_requirements" title="Table 7. UserAllocator Requirements"><span class="index-entry-level-1">UserAllocator Requirements</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">object_creator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool/object_creator.html" title="Struct object_creator"><span class="index-entry-level-1">Struct object_creator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Object_pool</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">automatic</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">construct</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">destroy</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">difference_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">element_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/object_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/poolfwd_hpp.html" title="Header &lt;boost/pool/poolfwd.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/poolfwd.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">interface</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">is_from</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">object_pool</span></a></strong></span></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">size_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">Struct default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">user_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">object_pool</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></strong></span></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ordered</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">Allocation and Deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">Basic ideas behind pooling</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">Boost Pool Interfaces - What interfaces are provided and when to use each one.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Constructors" title="Table 4. Constructors, Destructors, and State"><span class="index-entry-level-1">Constructors, Destructors, and State</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">How Contiguous Chunks are Handled</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Segregation" title="Table 5. Segregation"><span class="index-entry-level-1">Segregation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ordered_free</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ordered_free_n</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ordered_malloc</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ordered_malloc_need_resize</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">other</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223/rebind.html" title="Struct template rebind"><span class="index-entry-level-1">Struct template rebind</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">overview</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool.html" title="Introduction and Overview"><span class="index-entry-level-1">Introduction and Overview</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_77"></a><span class="term">P</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">p</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">padding</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">How Contiguous Chunks are Handled</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">pointer</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">pool</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">alignment</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">default_user_allocator_new_delete</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">difference_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">interface</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">is_from</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">new</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">ordered</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">ordered_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">ordered_malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">p</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></strong></span></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">purge_memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">release_memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">segregated</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">size_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">user_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Pool Interfaces</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces" title="Pool Interfaces"><span class="index-entry-level-1">interface</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">pool_allocator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">address</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">allocate</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">construct</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">const_pointer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">const_reference</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">deallocate</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">destroy</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">difference_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">fast_pool_allocator_tag</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">interface</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">max_size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">ordered</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">other</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pointer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></strong></span></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator_tag</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">rebind</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">reference</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">singleton</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">size_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263/rebind.html" title="Struct template rebind"><span class="index-entry-level-1">Struct template rebind</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">user_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">value_type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">pool_allocator_tag</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_tag.html" title="Struct pool_allocator_tag"><span class="index-entry-level-1">Struct pool_allocator_tag</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">portable</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/implementations.html" title="Appendix F: Other Implementations"><span class="index-entry-level-1">Appendix F: Other Implementations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">ptr</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">purge_memory</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_78"></a><span class="term">R</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">rebind</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223/rebind.html" title="Struct template rebind"><span class="index-entry-level-1">Struct template rebind</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">reference</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">release_memory</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_79"></a><span class="term">S</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">segregate</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">segregated</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/implementations.html" title="Appendix F: Other Implementations"><span class="index-entry-level-1">Appendix F: Other Implementations</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/references.html" title="Appendix G: References"><span class="index-entry-level-1">Appendix G: References</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">Basic ideas behind pooling</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_hpp.html" title="Header &lt;boost/pool/pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/simple_segregated_storage.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/introduction.html" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../BOOST_POOL_VALIDATE_INTERNALS.html" title="Macro BOOST_POOL_VALIDATE_INTERNALS"><span class="index-entry-level-1">Macro BOOST_POOL_VALIDATE_INTERNALS</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">Simple Segregated Storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.ss_symbols" title="Table 1. Symbol Table"><span class="index-entry-level-1">Symbol Table</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Segregation</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Segregation" title="Table 5. Segregation"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Segregation" title="Table 5. Segregation"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Segregation" title="Table 5. Segregation"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Segregation" title="Table 5. Segregation"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Segregation" title="Table 5. Segregation"><span class="index-entry-level-1">ordered</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Segregation" title="Table 5. Segregation"><span class="index-entry-level-1">size</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">set_max_size</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">set_next_size</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Simple Segregated Storage</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">allocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">O</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">segregated</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">size</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">add_block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">add_ordered_block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">alignment</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">free_n</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">interface</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">malloc_n</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">ordered</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">ordered_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">ordered_free_n</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">segregate</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">segregated</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">size_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">simple_segregated_storage</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">singleton</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/todo.html" title="Appendix H: Future plans"><span class="index-entry-level-1">Appendix H: Future plans</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html" title="Boost Pool Interfaces - What interfaces are provided and when to use each one."><span class="index-entry-level-1">Boost Pool Interfaces - What interfaces are provided and when to use each one.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool_alloc.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/singleton_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_tag.html" title="Struct fast_pool_allocator_tag"><span class="index-entry-level-1">Struct fast_pool_allocator_tag</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool/object_creator.html" title="Struct object_creator"><span class="index-entry-level-1">Struct object_creator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_tag.html" title="Struct pool_allocator_tag"><span class="index-entry-level-1">Struct pool_allocator_tag</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Singleton_pool</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../appendices/todo.html" title="Appendix H: Future plans"><span class="index-entry-level-1">Appendix H: Future plans</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">difference_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/singleton_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">interface</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">is_from</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">main</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">ordered</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">ordered_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">ordered_malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">purge_memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">release_memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">singleton</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">singleton_pool</span></a></strong></span></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">size_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_tag.html" title="Struct fast_pool_allocator_tag"><span class="index-entry-level-1">Struct fast_pool_allocator_tag</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool/object_creator.html" title="Struct object_creator"><span class="index-entry-level-1">Struct object_creator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_tag.html" title="Struct pool_allocator_tag"><span class="index-entry-level-1">Struct pool_allocator_tag</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">tag</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">template</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">user_allocator</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">singleton_pool</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><span class="bold"><strong><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></strong></span></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">size</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.alloc" title="Table 6. Allocation and Deallocation"><span class="index-entry-level-1">Allocation and Deallocation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.concepts" title="Basic ideas behind pooling"><span class="index-entry-level-1">Basic ideas behind pooling</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool_alloc.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/simple_segregated_storage.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/singleton_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">How Contiguous Chunks are Handled</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Segregation" title="Table 5. Segregation"><span class="index-entry-level-1">Segregation</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple" title="Simple Segregated Storage"><span class="index-entry-level-1">Simple Segregated Storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">Struct default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.ss_symbols" title="Table 1. Symbol Table"><span class="index-entry-level-1">Symbol Table</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.templates" title="Table 2. Template Parameters"><span class="index-entry-level-1">Template Parameters</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">The UserAllocator Concept</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Typedefs" title="Table 3. Typedefs"><span class="index-entry-level-1">Typedefs</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator.userallocator_requirements" title="Table 7. UserAllocator Requirements"><span class="index-entry-level-1">UserAllocator Requirements</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">sizeof</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment" title="Guaranteeing Alignment - How we guarantee alignment portably."><span class="index-entry-level-1">Guaranteeing Alignment - How we guarantee alignment portably.</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool_alloc.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.alignment.chunks" title="How Contiguous Chunks are Handled"><span class="index-entry-level-1">How Contiguous Chunks are Handled</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">size_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">Struct default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">store</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Struct default_user_allocator_malloc_free</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">difference_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">size_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Struct default_user_allocator_new_delete</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">default_user_allocator_new_delete</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">difference_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">new</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">size_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Struct fast_pool_allocator_tag</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_tag.html" title="Struct fast_pool_allocator_tag"><span class="index-entry-level-1">fast_pool_allocator_tag</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_tag.html" title="Struct fast_pool_allocator_tag"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_tag.html" title="Struct fast_pool_allocator_tag"><span class="index-entry-level-1">singleton</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_tag.html" title="Struct fast_pool_allocator_tag"><span class="index-entry-level-1">singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_tag.html" title="Struct fast_pool_allocator_tag"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Struct object_creator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool/object_creator.html" title="Struct object_creator"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool/object_creator.html" title="Struct object_creator"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool/object_creator.html" title="Struct object_creator"><span class="index-entry-level-1">object_creator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool/object_creator.html" title="Struct object_creator"><span class="index-entry-level-1">singleton</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool/object_creator.html" title="Struct object_creator"><span class="index-entry-level-1">singleton_pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Struct pool_allocator_tag</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_tag.html" title="Struct pool_allocator_tag"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_tag.html" title="Struct pool_allocator_tag"><span class="index-entry-level-1">pool_allocator_tag</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_tag.html" title="Struct pool_allocator_tag"><span class="index-entry-level-1">singleton</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_tag.html" title="Struct pool_allocator_tag"><span class="index-entry-level-1">singleton_pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Struct template rebind</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223/rebind.html" title="Struct template rebind"><span class="index-entry-level-1">fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223/rebind.html" title="Struct template rebind"><span class="index-entry-level-1">headers</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223/rebind.html" title="Struct template rebind"><span class="index-entry-level-1">other</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263/rebind.html" title="Struct template rebind"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223/rebind.html" title="Struct template rebind"><span class="index-entry-level-1">rebind</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223/rebind.html" title="Struct template rebind"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Symbol Table</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.ss_symbols" title="Table 1. Symbol Table"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.ss_symbols" title="Table 1. Symbol Table"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.ss_symbols" title="Table 1. Symbol Table"><span class="index-entry-level-1">segregated</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.ss_symbols" title="Table 1. Symbol Table"><span class="index-entry-level-1">size</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_80"></a><span class="term">T</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">tag</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">template</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator.html" title="Class template fast_pool_allocator"><span class="index-entry-level-1">Class template fast_pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/object_pool.html" title="Class template object_pool"><span class="index-entry-level-1">Class template object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool.html" title="Class template pool"><span class="index-entry-level-1">Class template pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator.html" title="Class template pool_allocator"><span class="index-entry-level-1">Class template pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/conventions.html" title="Documentation Naming and Formatting Conventions"><span class="index-entry-level-1">Documentation Naming and Formatting Conventions</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/object_pool_hpp.html" title="Header &lt;boost/pool/object_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/object_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/poolfwd_hpp.html" title="Header &lt;boost/pool/poolfwd.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/poolfwd.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/pool_alloc_hpp.html" title="Header &lt;boost/pool/pool_alloc.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/pool_alloc.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/simple_segregated_storage_hpp.html" title="Header &lt;boost/pool/simple_segregated_storage.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/simple_segregated_storage.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../header/boost/pool/singleton_pool_hpp.html" title="Header &lt;boost/pool/singleton_pool.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/pool/singleton_pool.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated" title="Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)"><span class="index-entry-level-1">Simple Segregated Storage (Not for the faint of heart - Embedded programmers only!)</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_malloc_free.html" title="Struct default_user_allocator_malloc_free"><span class="index-entry-level-1">Struct default_user_allocator_malloc_free</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/default_user_allocator_new_delete.html" title="Struct default_user_allocator_new_delete"><span class="index-entry-level-1">Struct default_user_allocator_new_delete</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_tag.html" title="Struct fast_pool_allocator_tag"><span class="index-entry-level-1">Struct fast_pool_allocator_tag</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223/rebind.html" title="Struct template rebind"><span class="index-entry-level-1">Struct template rebind</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.templates" title="Table 2. Template Parameters"><span class="index-entry-level-1">Template Parameters</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">The UserAllocator Concept</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Template Parameters</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.templates" title="Table 2. Template Parameters"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.templates" title="Table 2. Template Parameters"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">try_malloc_n</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Typedefs</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.simple_segregated.Typedefs" title="Table 3. Typedefs"><span class="index-entry-level-1">size</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_81"></a><span class="term">U</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">UserAllocator Concept</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">chunk</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">concepts</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">interface</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">size</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator" title="The UserAllocator Concept"><span class="index-entry-level-1">template</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">UserAllocator Requirements</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator.userallocator_requirements" title="Table 7. UserAllocator Requirements"><span class="index-entry-level-1">block</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator.userallocator_requirements" title="Table 7. UserAllocator Requirements"><span class="index-entry-level-1">malloc</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator.userallocator_requirements" title="Table 7. UserAllocator Requirements"><span class="index-entry-level-1">memory</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator.userallocator_requirements" title="Table 7. UserAllocator Requirements"><span class="index-entry-level-1">objects</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/pooling.html#boost_pool.pool.pooling.user_allocator.userallocator_requirements" title="Table 7. UserAllocator Requirements"><span class="index-entry-level-1">size</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">user_allocator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/singleton_pool.html" title="Class template singleton_pool"><span class="index-entry-level-1">Class template singleton_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.object_pool" title="Object_pool"><span class="index-entry-level-1">Object_pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool" title="pool"><span class="index-entry-level-1">pool</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.singleton_pool" title="Singleton_pool"><span class="index-entry-level-1">Singleton_pool</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_82"></a><span class="term">V</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">value_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/fast_pool_allocator_void__UserAl_idm2223.html" title="Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template fast_pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/pool_allocator_void__UserAllocat_idm2263.html" title="Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;"><span class="index-entry-level-1">Class template pool_allocator&lt;void, UserAllocator, Mutex, NextSize, MaxSize&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../pool/interfaces.html#boost_pool.pool.interfaces.interfaces.pool_allocator" title="pool_allocator"><span class="index-entry-level-1">pool_allocator</span></a></p></li>
</ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_83"></a><span class="term">Z</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">zero</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../../boost/simple_segregated_storage.html" title="Class template simple_segregated_storage"><span class="index-entry-level-1">Class template simple_segregated_storage</span></a></p></li></ul></div>
</li></ul></div></dd>
</dl></div>
</div>
<div class="copyright-footer">Copyright © 2000-2006 Stephen Cleary<br>Copyright © 2011 Paul A. Bristow<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="s03.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../indexes.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a>
</div>
</body>
</html>
