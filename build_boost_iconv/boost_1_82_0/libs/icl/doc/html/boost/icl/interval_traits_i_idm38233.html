<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Struct template interval_traits&lt;icl::left_open_interval&lt; DomainT, Compare &gt;&gt;</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Icl">
<link rel="up" href="../../header/boost/icl/left_open_interval_hpp.html" title="Header &lt;boost/icl/left_open_interval.hpp&gt;">
<link rel="prev" href="interval_bound_ty_idm38215.html" title="Struct template interval_bound_type&lt;left_open_interval&lt; DomainT, Compare &gt;&gt;">
<link rel="next" href="left_open_interval.html" title="Class template left_open_interval">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="interval_bound_ty_idm38215.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../header/boost/icl/left_open_interval_hpp.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="left_open_interval.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.icl.interval_traits_i_idm38233"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Struct template interval_traits&lt;icl::left_open_interval&lt; DomainT, Compare &gt;&gt;</span></h2>
<p>boost::icl::interval_traits&lt;icl::left_open_interval&lt; DomainT, Compare &gt;&gt;</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../header/boost/icl/left_open_interval_hpp.html" title="Header &lt;boost/icl/left_open_interval.hpp&gt;">boost/icl/left_open_interval.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> DomainT<span class="special">,</span> <span class="identifier">ICL_COMPARE</span> Compare<span class="special">&gt;</span> 
<span class="keyword">struct</span> <a class="link" href="interval_traits_i_idm38233.html" title="Struct template interval_traits&lt;icl::left_open_interval&lt; DomainT, Compare &gt;&gt;">interval_traits</a><span class="special">&lt;</span><span class="identifier">icl</span><span class="special">::</span><span class="identifier">left_open_interval</span><span class="special">&lt;</span> <span class="identifier">DomainT</span><span class="special">,</span> <span class="identifier">Compare</span> <span class="special">&gt;</span><span class="special">&gt;</span> <span class="special">{</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">DomainT</span>                                     <a name="boost.icl.interval_traits_i_idm38233.domain_type"></a><span class="identifier">domain_type</span><span class="special">;</span>  
  <span class="keyword">typedef</span> <a class="link" href="left_open_interval.html" title="Class template left_open_interval">icl::left_open_interval</a><span class="special">&lt;</span> <span class="identifier">DomainT</span><span class="special">,</span> <span class="identifier">Compare</span> <span class="special">&gt;</span> <a name="boost.icl.interval_traits_i_idm38233.interval_type"></a><span class="identifier">interval_type</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="interval_traits_i_idm38233.html#idm38246-bb">public member functions</a></span>
  <span class="keyword">typedef</span> <a class="link" href="interval_traits_i_idm38233.html#idm38247-bb"><span class="identifier">ICL_COMPARE_DOMAIN</span></a><span class="special">(</span><span class="identifier">Compare</span><span class="special">,</span> <span class="identifier">DomainT</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="interval_traits_i_idm38233.html#idm38253-bb">public static functions</a></span>
  <span class="keyword">static</span> interval_type <a class="link" href="interval_traits_i_idm38233.html#idm38254-bb"><span class="identifier">construct</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">domain_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">domain_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="identifier">domain_type</span> <a class="link" href="interval_traits_i_idm38233.html#idm38261-bb"><span class="identifier">lower</span></a><span class="special">(</span><span class="keyword">const</span> interval_type <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="identifier">domain_type</span> <a class="link" href="interval_traits_i_idm38233.html#idm38266-bb"><span class="identifier">upper</span></a><span class="special">(</span><span class="keyword">const</span> interval_type <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm54056"></a><h2>Description</h2>
<div class="refsect2">
<a name="idm54058"></a><h3>
<a name="idm38246-bb"></a><code class="computeroutput">interval_traits</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><pre class="literallayout"><span class="keyword">typedef</span> <a name="idm38247-bb"></a><span class="identifier">ICL_COMPARE_DOMAIN</span><span class="special">(</span><span class="identifier">Compare</span><span class="special">,</span> <span class="identifier">DomainT</span><span class="special">)</span><span class="special">;</span></pre></li></ol></div>
</div>
<div class="refsect2">
<a name="idm54075"></a><h3>
<a name="idm38253-bb"></a><code class="computeroutput">interval_traits</code> public static functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem"><pre class="literallayout"><span class="keyword">static</span> interval_type <a name="idm38254-bb"></a><span class="identifier">construct</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">domain_type</span> <span class="special">&amp;</span> lo<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">domain_type</span> <span class="special">&amp;</span> up<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">static</span> <span class="identifier">domain_type</span> <a name="idm38261-bb"></a><span class="identifier">lower</span><span class="special">(</span><span class="keyword">const</span> interval_type <span class="special">&amp;</span> inter_val<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">static</span> <span class="identifier">domain_type</span> <a name="idm38266-bb"></a><span class="identifier">upper</span><span class="special">(</span><span class="keyword">const</span> interval_type <span class="special">&amp;</span> inter_val<span class="special">)</span><span class="special">;</span></pre></li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2007-2010 Joachim
      Faulhaber<br>Copyright © 1999-2006 Cortex Software
      GmbH<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="interval_bound_ty_idm38215.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../header/boost/icl/left_open_interval_hpp.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="left_open_interval.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
