<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Struct template inverse&lt;icl::inter_section&lt; Type &gt;&gt;</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Icl">
<link rel="up" href="../../header/boost/icl/functors_hpp.html" title="Header &lt;boost/icl/functors.hpp&gt;">
<link rel="prev" href="inverse_icl_inpla_idm35335.html" title="Struct template inverse&lt;icl::inplace_star&lt; Type &gt;&gt;">
<link rel="next" href="is_negative.html" title="Struct template is_negative">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="inverse_icl_inpla_idm35335.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../header/boost/icl/functors_hpp.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="is_negative.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.icl.inverse_icl_inter_idm35343"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Struct template inverse&lt;icl::inter_section&lt; Type &gt;&gt;</span></h2>
<p>boost::icl::inverse&lt;icl::inter_section&lt; Type &gt;&gt;</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../header/boost/icl/functors_hpp.html" title="Header &lt;boost/icl/functors.hpp&gt;">boost/icl/functors.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">&gt;</span> 
<span class="keyword">struct</span> <a class="link" href="inverse_icl_inter_idm35343.html" title="Struct template inverse&lt;icl::inter_section&lt; Type &gt;&gt;">inverse</a><span class="special">&lt;</span><span class="identifier">icl</span><span class="special">::</span><span class="identifier">inter_section</span><span class="special">&lt;</span> <span class="identifier">Type</span> <span class="special">&gt;</span><span class="special">&gt;</span> <span class="special">:</span> <span class="keyword">public</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">icl</span><span class="special">::</span><span class="identifier">identity_based_inplace_combine</span><span class="special">&lt;</span> <span class="identifier">Type</span> <span class="special">&gt;</span> <span class="special">{</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">mpl</span><span class="special">::</span><span class="identifier">if_</span><span class="special">&lt;</span> <span class="identifier">has_set_semantics</span><span class="special">&lt;</span> <span class="identifier">Type</span> <span class="special">&gt;</span><span class="special">,</span> <a class="link" href="inplace_caret.html" title="Struct template inplace_caret">icl::inplace_caret</a><span class="special">&lt;</span> <span class="identifier">Type</span> <span class="special">&gt;</span><span class="special">,</span> <a class="link" href="inplace_minus.html" title="Struct template inplace_minus">icl::inplace_minus</a><span class="special">&lt;</span> <span class="identifier">Type</span> <span class="special">&gt;</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <a name="boost.icl.inverse_icl_inter_idm35343.type"></a><span class="identifier">type</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="inverse_icl_inter_idm35343.html#idm35353-bb">public member functions</a></span>
  <span class="keyword">void</span> <a class="link" href="inverse_icl_inter_idm35343.html#idm35354-bb"><span class="keyword">operator</span><span class="special">(</span><span class="special">)</span></a><span class="special">(</span><span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm40457"></a><h2>Description</h2>
<div class="refsect2">
<a name="idm40459"></a><h3>
<a name="idm35353-bb"></a><code class="computeroutput">inverse</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm35354-bb"></a><span class="keyword">operator</span><span class="special">(</span><span class="special">)</span><span class="special">(</span><span class="identifier">Type</span> <span class="special">&amp;</span> object<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Type</span> <span class="special">&amp;</span> operand<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li></ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2007-2010 Joachim
      Faulhaber<br>Copyright © 1999-2006 Cortex Software
      GmbH<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="inverse_icl_inpla_idm35335.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../header/boost/icl/functors_hpp.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="is_negative.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
