<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template interval_base_map</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Icl">
<link rel="up" href="../../header/boost/icl/interval_base_map_hpp.html" title="Header &lt;boost/icl/interval_base_map.hpp&gt;">
<link rel="prev" href="has_inverse_icl_i_idm35758.html" title="Struct template has_inverse&lt;icl::interval_base_map&lt; SubType, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt;&gt;">
<link rel="next" href="interval_base_map/on_codomain_model.html" title="Struct template on_codomain_model">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="has_inverse_icl_i_idm35758.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../header/boost/icl/interval_base_map_hpp.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="interval_base_map/on_codomain_model.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.icl.interval_base_map"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template interval_base_map</span></h2>
<p>boost::icl::interval_base_map — Implements a map as a map of intervals (base class) </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../header/boost/icl/interval_base_map_hpp.html" title="Header &lt;boost/icl/interval_base_map.hpp&gt;">boost/icl/interval_base_map.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> SubType<span class="special">,</span> <span class="keyword">typename</span> DomainT<span class="special">,</span> <span class="keyword">typename</span> CodomainT<span class="special">,</span> 
         <span class="keyword">typename</span> Traits <span class="special">=</span> <a class="link" href="partial_absorber.html" title="Struct partial_absorber">icl::partial_absorber</a><span class="special">,</span> 
         <span class="identifier">ICL_COMPARE</span> Compare <span class="special">=</span> <span class="identifier">ICL_COMPARE_INSTANCE</span><span class="special">(</span><span class="identifier">ICL_COMPARE_DEFAULT</span><span class="special">,</span> <span class="identifier">DomainT</span><span class="special">)</span><span class="special">,</span> 
         <span class="identifier">ICL_COMBINE</span> Combine <span class="special">=</span> <span class="identifier">ICL_COMBINE_INSTANCE</span><span class="special">(</span><a class="link" href="inplace_plus.html" title="Struct template inplace_plus">icl::inplace_plus</a><span class="special">,</span> <span class="identifier">CodomainT</span><span class="special">)</span><span class="special">,</span> 
         <span class="identifier">ICL_SECTION</span> Section <span class="special">=</span> <span class="identifier">ICL_SECTION_INSTANCE</span><span class="special">(</span><a class="link" href="inter_section.html" title="Struct template inter_section">icl::inter_section</a><span class="special">,</span> <span class="identifier">CodomainT</span><span class="special">)</span><span class="special">,</span> 
         <span class="identifier">ICL_INTERVAL</span><span class="special">(</span><span class="identifier">ICL_COMPARE</span><span class="special">)</span> Interval <span class="special">=</span> <span class="identifier">ICL_INTERVAL_INSTANCE</span><span class="special">(</span><span class="identifier">ICL_INTERVAL_DEFAULT</span><span class="special">,</span> <span class="identifier">DomainT</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">)</span><span class="special">,</span> 
         <span class="identifier">ICL_ALLOC</span> Alloc <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator</span><span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="interval_base_map.html" title="Class template interval_base_map">interval_base_map</a> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <a class="link" href="interval_base_map.html" title="Class template interval_base_map">interval_base_map</a><span class="special">&lt;</span> <span class="identifier">SubType</span><span class="special">,</span> <span class="identifier">DomainT</span><span class="special">,</span> <span class="identifier">CodomainT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> <span class="identifier">Combine</span><span class="special">,</span> <span class="identifier">Section</span><span class="special">,</span> <span class="identifier">Interval</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span>             <a name="boost.icl.interval_base_map.type"></a><span class="identifier">type</span><span class="special">;</span>                          
  <span class="keyword">typedef</span> <span class="identifier">SubType</span>                                                                                                          <a name="boost.icl.interval_base_map.sub_type"></a><span class="identifier">sub_type</span><span class="special">;</span>                        <span class="comment">// The designated <span class="emphasis"><em>derived</em></span> or <span class="emphasis"><em>sub_type</em></span> of this base class. </span>
  <span class="keyword">typedef</span> <a class="link" href="interval_base_map.html#boost.icl.interval_base_map.type">type</a>                                                                                                             <a name="boost.icl.interval_base_map.overloadable_type"></a><span class="identifier">overloadable_type</span><span class="special">;</span>               <span class="comment">// Auxilliary type for overloadresolution. </span>
  <span class="keyword">typedef</span> <span class="identifier">Traits</span>                                                                                                           <a name="boost.icl.interval_base_map.traits"></a><span class="identifier">traits</span><span class="special">;</span>                          <span class="comment">// Traits of an itl map. </span>
  <span class="keyword">typedef</span> <a class="link" href="map.html" title="Class template map">icl::map</a><span class="special">&lt;</span> <span class="identifier">DomainT</span><span class="special">,</span> <span class="identifier">CodomainT</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> <span class="identifier">Combine</span><span class="special">,</span> <span class="identifier">Section</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span>                                         <a name="boost.icl.interval_base_map.atomized_type"></a><span class="identifier">atomized_type</span><span class="special">;</span>                   <span class="comment">// The atomized type representing the corresponding container of elements. </span>
  <span class="keyword">typedef</span> <span class="identifier">DomainT</span>                                                                                                          <a name="boost.icl.interval_base_map.domain_type"></a><span class="identifier">domain_type</span><span class="special">;</span>                     <span class="comment">// Domain type (type of the keys) of the map. </span>
  <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">call_traits</span><span class="special">&lt;</span> <span class="identifier">DomainT</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">param_type</span>                                                                        <a name="boost.icl.interval_base_map.domain_param"></a><span class="identifier">domain_param</span><span class="special">;</span>                  
  <span class="keyword">typedef</span> <span class="identifier">CodomainT</span>                                                                                                        <a name="boost.icl.interval_base_map.codomain_type"></a><span class="identifier">codomain_type</span><span class="special">;</span>                   <span class="comment">// Domain type (type of the keys) of the map. </span>
  <span class="keyword">typedef</span> <a class="link" href="mapping_pair.html" title="Struct template mapping_pair">mapping_pair</a><span class="special">&lt;</span> <span class="identifier">domain_type</span><span class="special">,</span> <span class="identifier">codomain_type</span> <span class="special">&gt;</span>                                                                       <a name="boost.icl.interval_base_map.domain_mapping_type"></a><span class="identifier">domain_mapping_type</span><span class="special">;</span>             <span class="comment">// Auxiliary type to help the compiler resolve ambiguities when using std::make_pair. </span>
  <span class="keyword">typedef</span> <span class="identifier">domain_mapping_type</span>                                                                                              <a name="boost.icl.interval_base_map.element_type"></a><span class="identifier">element_type</span><span class="special">;</span>                    <span class="comment">// Conceptual is a map a set of elements of type <code class="computeroutput">element_type</code>. </span>
  <span class="keyword">typedef</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">interval_type</span><span class="special">,</span> <span class="identifier">CodomainT</span> <span class="special">&gt;</span>                                                                            <a name="boost.icl.interval_base_map.interval_mapping_type"></a><span class="identifier">interval_mapping_type</span><span class="special">;</span>           <span class="comment">// Auxiliary type for overload resolution. </span>
  <span class="keyword">typedef</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">interval_type</span><span class="special">,</span> <span class="identifier">CodomainT</span> <span class="special">&gt;</span>                                                                            <a name="boost.icl.interval_base_map.segment_type"></a><span class="identifier">segment_type</span><span class="special">;</span>                    <span class="comment">// Type of an interval containers segment, that is spanned by an interval. </span>
  <span class="keyword">typedef</span> <span class="identifier">difference_type_of</span><span class="special">&lt;</span> <span class="identifier">domain_type</span> <span class="special">&gt;</span><span class="special">::</span><a class="link" href="interval_base_map.html#boost.icl.interval_base_map.type">type</a>                                                                          <a name="boost.icl.interval_base_map.difference_type"></a><span class="identifier">difference_type</span><span class="special">;</span>                 <span class="comment">// The difference type of an interval which is sometimes different form the domain_type. </span>
  <span class="keyword">typedef</span> <span class="identifier">size_type_of</span><span class="special">&lt;</span> <span class="identifier">domain_type</span> <span class="special">&gt;</span><span class="special">::</span><a class="link" href="interval_base_map.html#boost.icl.interval_base_map.type">type</a>                                                                                <a name="boost.icl.interval_base_map.size_type"></a><span class="identifier">size_type</span><span class="special">;</span>                       <span class="comment">// The size type of an interval which is mostly std::size_t. </span>
  <span class="keyword">typedef</span> <a class="link" href="inverse.html" title="Struct template inverse">inverse</a><span class="special">&lt;</span> <span class="identifier">codomain_combine</span> <span class="special">&gt;</span><span class="special">::</span><a class="link" href="interval_base_map.html#boost.icl.interval_base_map.type">type</a>                                                                                <a name="boost.icl.interval_base_map.inverse_codomain_combine"></a><span class="identifier">inverse_codomain_combine</span><span class="special">;</span>        <span class="comment">// Inverse Combine functor for codomain value aggregation. </span>
  <span class="keyword">typedef</span> <span class="identifier">mpl</span><span class="special">::</span><span class="identifier">if_</span><span class="special">&lt;</span> <span class="identifier">has_set_semantics</span><span class="special">&lt;</span> <span class="identifier">codomain_type</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">ICL_SECTION_CODOMAIN</span><span class="special">(</span><span class="identifier">Section</span><span class="special">,</span> <span class="identifier">CodomainT</span><span class="special">)</span><span class="special">,</span> <span class="identifier">codomain_combine</span> <span class="special">&gt;</span><span class="special">::</span><a class="link" href="interval_base_map.html#boost.icl.interval_base_map.type">type</a> <a name="boost.icl.interval_base_map.codomain_intersect"></a><span class="identifier">codomain_intersect</span><span class="special">;</span>              <span class="comment">// Intersection functor for codomain values. </span>
  <span class="keyword">typedef</span> <a class="link" href="inverse.html" title="Struct template inverse">inverse</a><span class="special">&lt;</span> <span class="identifier">codomain_intersect</span> <span class="special">&gt;</span><span class="special">::</span><a class="link" href="interval_base_map.html#boost.icl.interval_base_map.type">type</a>                                                                              <a name="boost.icl.interval_base_map.inverse_codomain_intersect"></a><span class="identifier">inverse_codomain_intersect</span><span class="special">;</span>      <span class="comment">// Inverse Combine functor for codomain value intersection. </span>
  <span class="keyword">typedef</span> <span class="identifier">exclusive_less_than</span><span class="special">&lt;</span> <span class="identifier">interval_type</span> <span class="special">&gt;</span>                                                                             <a name="boost.icl.interval_base_map.interval_compare"></a><span class="identifier">interval_compare</span><span class="special">;</span>                <span class="comment">// Comparison functor for intervals which are keys as well. </span>
  <span class="keyword">typedef</span> <span class="identifier">exclusive_less_than</span><span class="special">&lt;</span> <span class="identifier">interval_type</span> <span class="special">&gt;</span>                                                                             <a name="boost.icl.interval_base_map.key_compare"></a><span class="identifier">key_compare</span><span class="special">;</span>                     <span class="comment">// Comparison functor for keys. </span>
  <span class="keyword">typedef</span> <span class="identifier">Alloc</span><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="keyword">const</span> <span class="identifier">interval_type</span><span class="special">,</span> <span class="identifier">codomain_type</span> <span class="special">&gt;</span> <span class="special">&gt;</span>                                                         <a name="boost.icl.interval_base_map.allocator_type"></a><span class="identifier">allocator_type</span><span class="special">;</span>                  <span class="comment">// The allocator type of the set. </span>
  <span class="keyword">typedef</span> <span class="identifier">ICL_IMPL_SPACE</span><span class="special">::</span><span class="identifier">map</span><span class="special">&lt;</span> <span class="identifier">interval_type</span><span class="special">,</span> <span class="identifier">codomain_type</span><span class="special">,</span> <span class="identifier">key_compare</span><span class="special">,</span> <span class="identifier">allocator_type</span> <span class="special">&gt;</span>                                 <a name="boost.icl.interval_base_map.ImplMapT"></a><span class="identifier">ImplMapT</span><span class="special">;</span>                        <span class="comment">// Container type for the implementation. </span>
  <span class="keyword">typedef</span> <span class="identifier">ImplMapT</span><span class="special">::</span><span class="identifier">key_type</span>                                                                                               <a name="boost.icl.interval_base_map.key_type"></a><span class="identifier">key_type</span><span class="special">;</span>                        <span class="comment">// key type of the implementing container </span>
  <span class="keyword">typedef</span> <span class="identifier">ImplMapT</span><span class="special">::</span><span class="identifier">value_type</span>                                                                                             <a name="boost.icl.interval_base_map.value_type"></a><span class="identifier">value_type</span><span class="special">;</span>                      <span class="comment">// value type of the implementing container </span>
  <span class="keyword">typedef</span> <span class="identifier">ImplMapT</span><span class="special">::</span><span class="identifier">value_type</span><span class="special">::</span><span class="identifier">second_type</span>                                                                                <a name="boost.icl.interval_base_map.data_type"></a><span class="identifier">data_type</span><span class="special">;</span>                       <span class="comment">// data type of the implementing container </span>
  <span class="keyword">typedef</span> <span class="identifier">ImplMapT</span><span class="special">::</span><span class="identifier">pointer</span>                                                                                                <a name="boost.icl.interval_base_map.pointer"></a><span class="identifier">pointer</span><span class="special">;</span>                         <span class="comment">// pointer type </span>
  <span class="keyword">typedef</span> <span class="identifier">ImplMapT</span><span class="special">::</span><span class="identifier">const_pointer</span>                                                                                          <a name="boost.icl.interval_base_map.const_pointer"></a><span class="identifier">const_pointer</span><span class="special">;</span>                   <span class="comment">// const pointer type </span>
  <span class="keyword">typedef</span> <span class="identifier">ImplMapT</span><span class="special">::</span><span class="identifier">reference</span>                                                                                              <a name="boost.icl.interval_base_map.reference"></a><span class="identifier">reference</span><span class="special">;</span>                       <span class="comment">// reference type </span>
  <span class="keyword">typedef</span> <span class="identifier">ImplMapT</span><span class="special">::</span><span class="identifier">const_reference</span>                                                                                        <a name="boost.icl.interval_base_map.const_reference"></a><span class="identifier">const_reference</span><span class="special">;</span>                 <span class="comment">// const reference type </span>
  <span class="keyword">typedef</span> <span class="identifier">ImplMapT</span><span class="special">::</span><span class="identifier">iterator</span>                                                                                               <a name="boost.icl.interval_base_map.iterator"></a><span class="identifier">iterator</span><span class="special">;</span>                        <span class="comment">// iterator for iteration over intervals </span>
  <span class="keyword">typedef</span> <span class="identifier">ImplMapT</span><span class="special">::</span><span class="identifier">const_iterator</span>                                                                                         <a name="boost.icl.interval_base_map.const_iterator"></a><span class="identifier">const_iterator</span><span class="special">;</span>                  <span class="comment">// const_iterator for iteration over intervals </span>
  <span class="keyword">typedef</span> <span class="identifier">ImplMapT</span><span class="special">::</span><span class="identifier">reverse_iterator</span>                                                                                       <a name="boost.icl.interval_base_map.reverse_iterator"></a><span class="identifier">reverse_iterator</span><span class="special">;</span>                <span class="comment">// iterator for reverse iteration over intervals </span>
  <span class="keyword">typedef</span> <span class="identifier">ImplMapT</span><span class="special">::</span><span class="identifier">const_reverse_iterator</span>                                                                                 <a name="boost.icl.interval_base_map.const_reverse_iterator"></a><span class="identifier">const_reverse_iterator</span><span class="special">;</span>          <span class="comment">// const_iterator for iteration over intervals </span>
  <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">icl</span><span class="special">::</span><span class="identifier">element_iterator</span><span class="special">&lt;</span> <span class="identifier">iterator</span> <span class="special">&gt;</span>                                                                         <a name="boost.icl.interval_base_map.element_iterator"></a><span class="identifier">element_iterator</span><span class="special">;</span>                <span class="comment">// element iterator: Depreciated, see documentation. </span>
  <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">icl</span><span class="special">::</span><span class="identifier">element_iterator</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span>                                                                   <a name="boost.icl.interval_base_map.element_const_iterator"></a><span class="identifier">element_const_iterator</span><span class="special">;</span>          <span class="comment">// const element iterator: Depreciated, see documentation. </span>
  <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">icl</span><span class="special">::</span><span class="identifier">element_iterator</span><span class="special">&lt;</span> <span class="identifier">reverse_iterator</span> <span class="special">&gt;</span>                                                                 <a name="boost.icl.interval_base_map.element_reverse_iterator"></a><span class="identifier">element_reverse_iterator</span><span class="special">;</span>        <span class="comment">// element reverse iterator: Depreciated, see documentation. </span>
  <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">icl</span><span class="special">::</span><span class="identifier">element_iterator</span><span class="special">&lt;</span> <span class="identifier">const_reverse_iterator</span> <span class="special">&gt;</span>                                                           <a name="boost.icl.interval_base_map.element_const_rev_idm36138"></a><span class="identifier">element_const_reverse_iterator</span><span class="special">;</span>  <span class="comment">// element const reverse iterator: Depreciated, see documentation. </span>
  <span class="keyword">typedef</span> <span class="identifier">on_absorbtion</span><span class="special">&lt;</span> <a class="link" href="interval_base_map.html#boost.icl.interval_base_map.type">type</a><span class="special">,</span> <span class="identifier">codomain_combine</span><span class="special">,</span> <span class="identifier">Traits</span><span class="special">::</span><span class="identifier">absorbs_identities</span> <span class="special">&gt;</span><span class="special">::</span><a class="link" href="interval_base_map.html#boost.icl.interval_base_map.type">type</a>                                        <a name="boost.icl.interval_base_map.on_codomain_absorbtion"></a><span class="identifier">on_codomain_absorbtion</span><span class="special">;</span>        

  <span class="comment">// member classes/structs/unions</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">,</span> <span class="keyword">bool</span> has_set_semantics<span class="special">&gt;</span> 
  <span class="keyword">struct</span> <a class="link" href="interval_base_map/on_codomain_model.html" title="Struct template on_codomain_model">on_codomain_model</a> <span class="special">{</span>
  <span class="special">}</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">&gt;</span> 
  <span class="keyword">struct</span> <a class="link" href="interval_base_map/on_codomain_model_idm35826.html" title="Struct template on_codomain_model&lt;Type, false&gt;">on_codomain_model</a><span class="special">&lt;</span><span class="identifier">Type</span><span class="special">,</span> <span class="keyword">false</span><span class="special">&gt;</span> <span class="special">{</span>
    <span class="comment">// types</span>
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">interval_type</span>    <a class="link" href="interval_base_map/on_codomain_model_idm35826.html#boost.icl.interval_base_map.on_codomain_model_idm35826.interval_type"><span class="identifier">interval_type</span></a><span class="special">;</span>   
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">codomain_type</span>    <a class="link" href="interval_base_map/on_codomain_model_idm35826.html#boost.icl.interval_base_map.on_codomain_model_idm35826.codomain_type"><span class="identifier">codomain_type</span></a><span class="special">;</span>   
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">segment_type</span>     <a class="link" href="interval_base_map/on_codomain_model_idm35826.html#boost.icl.interval_base_map.on_codomain_model_idm35826.segment_type"><span class="identifier">segment_type</span></a><span class="special">;</span>    
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">codomain_combine</span> <a class="link" href="interval_base_map/on_codomain_model_idm35826.html#boost.icl.interval_base_map.on_codomain_model_idm35826.codomain_combine"><span class="identifier">codomain_combine</span></a><span class="special">;</span>

    <span class="comment">// <a class="link" href="interval_base_map/on_codomain_model_idm35826.html#idm35840-bb">public static functions</a></span>
    <span class="keyword">static</span> <span class="keyword">void</span> <a class="link" href="interval_base_map/on_codomain_model_idm35826.html#idm35841-bb"><span class="identifier">add</span></a><span class="special">(</span><span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">codomain_type</span> <span class="special">&amp;</span><span class="special">,</span> 
                    <span class="keyword">const</span> <span class="identifier">codomain_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">&gt;</span> 
  <span class="keyword">struct</span> <a class="link" href="interval_base_map/on_codomain_model_idm35851.html" title="Struct template on_codomain_model&lt;Type, true&gt;">on_codomain_model</a><span class="special">&lt;</span><span class="identifier">Type</span><span class="special">,</span> <span class="keyword">true</span><span class="special">&gt;</span> <span class="special">{</span>
    <span class="comment">// types</span>
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">interval_type</span>              <a class="link" href="interval_base_map/on_codomain_model_idm35851.html#boost.icl.interval_base_map.on_codomain_model_idm35851.interval_type"><span class="identifier">interval_type</span></a><span class="special">;</span>             
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">codomain_type</span>              <a class="link" href="interval_base_map/on_codomain_model_idm35851.html#boost.icl.interval_base_map.on_codomain_model_idm35851.codomain_type"><span class="identifier">codomain_type</span></a><span class="special">;</span>             
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">segment_type</span>               <a class="link" href="interval_base_map/on_codomain_model_idm35851.html#boost.icl.interval_base_map.on_codomain_model_idm35851.segment_type"><span class="identifier">segment_type</span></a><span class="special">;</span>              
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">codomain_combine</span>           <a class="link" href="interval_base_map/on_codomain_model_idm35851.html#boost.icl.interval_base_map.on_codomain_model_idm35851.codomain_combine"><span class="identifier">codomain_combine</span></a><span class="special">;</span>          
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">inverse_codomain_intersect</span> <a class="link" href="interval_base_map/on_codomain_model_idm35851.html#boost.icl.interval_base_map.on_codomain_model_idm35851.inverse_codomain_intersect"><span class="identifier">inverse_codomain_intersect</span></a><span class="special">;</span>

    <span class="comment">// <a class="link" href="interval_base_map/on_codomain_model_idm35851.html#idm35867-bb">public static functions</a></span>
    <span class="keyword">static</span> <span class="keyword">void</span> <a class="link" href="interval_base_map/on_codomain_model_idm35851.html#idm35868-bb"><span class="identifier">add</span></a><span class="special">(</span><span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">codomain_type</span> <span class="special">&amp;</span><span class="special">,</span> 
                    <span class="keyword">const</span> <span class="identifier">codomain_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">,</span> <span class="keyword">bool</span> is_total<span class="special">&gt;</span> 
  <span class="keyword">struct</span> <a class="link" href="interval_base_map/on_definedness.html" title="Struct template on_definedness">on_definedness</a> <span class="special">{</span>
  <span class="special">}</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">&gt;</span> 
  <span class="keyword">struct</span> <a class="link" href="interval_base_map/on_definedness_Ty_idm35883.html" title="Struct template on_definedness&lt;Type, false&gt;">on_definedness</a><span class="special">&lt;</span><span class="identifier">Type</span><span class="special">,</span> <span class="keyword">false</span><span class="special">&gt;</span> <span class="special">{</span>

    <span class="comment">// <a class="link" href="interval_base_map/on_definedness_Ty_idm35883.html#idm35889-bb">public static functions</a></span>
    <span class="keyword">static</span> <span class="keyword">void</span> <a class="link" href="interval_base_map/on_definedness_Ty_idm35883.html#idm35890-bb"><span class="identifier">add_intersection</span></a><span class="special">(</span><span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">&gt;</span> 
  <span class="keyword">struct</span> <a class="link" href="interval_base_map/on_definedness_Ty_idm35898.html" title="Struct template on_definedness&lt;Type, true&gt;">on_definedness</a><span class="special">&lt;</span><span class="identifier">Type</span><span class="special">,</span> <span class="keyword">true</span><span class="special">&gt;</span> <span class="special">{</span>

    <span class="comment">// <a class="link" href="interval_base_map/on_definedness_Ty_idm35898.html#idm35904-bb">public static functions</a></span>
    <span class="keyword">static</span> <span class="keyword">void</span> <a class="link" href="interval_base_map/on_definedness_Ty_idm35898.html#idm35905-bb"><span class="identifier">add_intersection</span></a><span class="special">(</span><span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">,</span> <span class="keyword">bool</span> is_total_invertible<span class="special">&gt;</span> 
  <span class="keyword">struct</span> <a class="link" href="interval_base_map/on_invertible.html" title="Struct template on_invertible">on_invertible</a> <span class="special">{</span>
  <span class="special">}</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">&gt;</span> 
  <span class="keyword">struct</span> <a class="link" href="interval_base_map/on_invertible_Typ_idm35918.html" title="Struct template on_invertible&lt;Type, false&gt;">on_invertible</a><span class="special">&lt;</span><span class="identifier">Type</span><span class="special">,</span> <span class="keyword">false</span><span class="special">&gt;</span> <span class="special">{</span>
    <span class="comment">// types</span>
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">segment_type</span>             <a class="link" href="interval_base_map/on_invertible_Typ_idm35918.html#boost.icl.interval_base_map.on_invertible_Typ_idm35918.segment_type"><span class="identifier">segment_type</span></a><span class="special">;</span>            
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">inverse_codomain_combine</span> <a class="link" href="interval_base_map/on_invertible_Typ_idm35918.html#boost.icl.interval_base_map.on_invertible_Typ_idm35918.inverse_codomain_combine"><span class="identifier">inverse_codomain_combine</span></a><span class="special">;</span>

    <span class="comment">// <a class="link" href="interval_base_map/on_invertible_Typ_idm35918.html#idm35928-bb">public static functions</a></span>
    <span class="keyword">static</span> <span class="keyword">void</span> <a class="link" href="interval_base_map/on_invertible_Typ_idm35918.html#idm35929-bb"><span class="identifier">subtract</span></a><span class="special">(</span><span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">&gt;</span> 
  <span class="keyword">struct</span> <a class="link" href="interval_base_map/on_invertible_Typ_idm35935.html" title="Struct template on_invertible&lt;Type, true&gt;">on_invertible</a><span class="special">&lt;</span><span class="identifier">Type</span><span class="special">,</span> <span class="keyword">true</span><span class="special">&gt;</span> <span class="special">{</span>
    <span class="comment">// types</span>
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">segment_type</span>             <a class="link" href="interval_base_map/on_invertible_Typ_idm35935.html#boost.icl.interval_base_map.on_invertible_Typ_idm35935.segment_type"><span class="identifier">segment_type</span></a><span class="special">;</span>            
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">inverse_codomain_combine</span> <a class="link" href="interval_base_map/on_invertible_Typ_idm35935.html#boost.icl.interval_base_map.on_invertible_Typ_idm35935.inverse_codomain_combine"><span class="identifier">inverse_codomain_combine</span></a><span class="special">;</span>

    <span class="comment">// <a class="link" href="interval_base_map/on_invertible_Typ_idm35935.html#idm35945-bb">public static functions</a></span>
    <span class="keyword">static</span> <span class="keyword">void</span> <a class="link" href="interval_base_map/on_invertible_Typ_idm35935.html#idm35946-bb"><span class="identifier">subtract</span></a><span class="special">(</span><span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">,</span> <span class="keyword">bool</span> is_total<span class="special">,</span> <span class="keyword">bool</span> absorbs_identities<span class="special">&gt;</span> 
  <span class="keyword">struct</span> <a class="link" href="interval_base_map/on_total_absorbable.html" title="Struct template on_total_absorbable">on_total_absorbable</a> <span class="special">{</span>
  <span class="special">}</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">,</span> <span class="keyword">bool</span> absorbs_identities<span class="special">&gt;</span> 
  <span class="keyword">struct</span> <a class="link" href="interval_base_map/on_total_absorbab_idm35959.html" title="Struct template on_total_absorbable&lt;Type, false, absorbs_identities&gt;">on_total_absorbable</a><span class="special">&lt;</span><span class="identifier">Type</span><span class="special">,</span> <span class="keyword">false</span><span class="special">,</span> <span class="identifier">absorbs_identities</span><span class="special">&gt;</span> <span class="special">{</span>
    <span class="comment">// types</span>
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">segment_type</span>               <a class="link" href="interval_base_map/on_total_absorbab_idm35959.html#boost.icl.interval_base_map.on_total_absorbab_idm35959.segment_type"><span class="identifier">segment_type</span></a><span class="special">;</span>              
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">codomain_type</span>              <a class="link" href="interval_base_map/on_total_absorbab_idm35959.html#boost.icl.interval_base_map.on_total_absorbab_idm35959.codomain_type"><span class="identifier">codomain_type</span></a><span class="special">;</span>             
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">interval_type</span>              <a class="link" href="interval_base_map/on_total_absorbab_idm35959.html#boost.icl.interval_base_map.on_total_absorbab_idm35959.interval_type"><span class="identifier">interval_type</span></a><span class="special">;</span>             
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">value_type</span>                 <a class="link" href="interval_base_map/on_total_absorbab_idm35959.html#boost.icl.interval_base_map.on_total_absorbab_idm35959.value_type"><span class="identifier">value_type</span></a><span class="special">;</span>                
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">const_iterator</span>             <a class="link" href="interval_base_map/on_total_absorbab_idm35959.html#boost.icl.interval_base_map.on_total_absorbab_idm35959.const_iterator"><span class="identifier">const_iterator</span></a><span class="special">;</span>            
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">set_type</span>                   <a class="link" href="interval_base_map/on_total_absorbab_idm35959.html#boost.icl.interval_base_map.on_total_absorbab_idm35959.set_type"><span class="identifier">set_type</span></a><span class="special">;</span>                  
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">inverse_codomain_intersect</span> <a class="link" href="interval_base_map/on_total_absorbab_idm35959.html#boost.icl.interval_base_map.on_total_absorbab_idm35959.inverse_codomain_intersect"><span class="identifier">inverse_codomain_intersect</span></a><span class="special">;</span>

    <span class="comment">// <a class="link" href="interval_base_map/on_total_absorbab_idm35959.html#idm35982-bb">public static functions</a></span>
    <span class="keyword">static</span> <span class="keyword">void</span> <a class="link" href="interval_base_map/on_total_absorbab_idm35959.html#idm35983-bb"><span class="identifier">flip</span></a><span class="special">(</span><span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">&gt;</span> 
  <span class="keyword">struct</span> <a class="link" href="interval_base_map/on_total_absorbab_idm35989.html" title="Struct template on_total_absorbable&lt;Type, true, false&gt;">on_total_absorbable</a><span class="special">&lt;</span><span class="identifier">Type</span><span class="special">,</span> <span class="keyword">true</span><span class="special">,</span> <span class="keyword">false</span><span class="special">&gt;</span> <span class="special">{</span>
    <span class="comment">// types</span>
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">segment_type</span>  <a class="link" href="interval_base_map/on_total_absorbab_idm35989.html#boost.icl.interval_base_map.on_total_absorbab_idm35989.segment_type"><span class="identifier">segment_type</span></a><span class="special">;</span> 
    <span class="keyword">typedef</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">codomain_type</span> <a class="link" href="interval_base_map/on_total_absorbab_idm35989.html#boost.icl.interval_base_map.on_total_absorbab_idm35989.codomain_type"><span class="identifier">codomain_type</span></a><span class="special">;</span>

    <span class="comment">// <a class="link" href="interval_base_map/on_total_absorbab_idm35989.html#idm36000-bb">public static functions</a></span>
    <span class="keyword">static</span> <span class="keyword">void</span> <a class="link" href="interval_base_map/on_total_absorbab_idm35989.html#idm36001-bb"><span class="identifier">flip</span></a><span class="special">(</span><span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Type<span class="special">&gt;</span> 
  <span class="keyword">struct</span> <a class="link" href="interval_base_map/on_total_absorbab_idm36007.html" title="Struct template on_total_absorbable&lt;Type, true, true&gt;">on_total_absorbable</a><span class="special">&lt;</span><span class="identifier">Type</span><span class="special">,</span> <span class="keyword">true</span><span class="special">,</span> <span class="keyword">true</span><span class="special">&gt;</span> <span class="special">{</span>

    <span class="comment">// <a class="link" href="interval_base_map/on_total_absorbab_idm36007.html#idm36014-bb">public static functions</a></span>
    <span class="keyword">static</span> <span class="keyword">void</span> <a class="link" href="interval_base_map/on_total_absorbab_idm36007.html#idm36015-bb"><span class="identifier">flip</span></a><span class="special">(</span><span class="identifier">Type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">typename</span> <span class="identifier">Type</span><span class="special">::</span><span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="interval_base_map.html#boost.icl.interval_base_mapconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="interval_base_map.html#idm36421-bb"><span class="identifier">interval_base_map</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="interval_base_map.html#idm36424-bb"><span class="identifier">interval_base_map</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="interval_base_map.html" title="Class template interval_base_map">interval_base_map</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="interval_base_map.html#idm36430-bb"><span class="identifier">interval_base_map</span></a><span class="special">(</span><a class="link" href="interval_base_map.html" title="Class template interval_base_map">interval_base_map</a> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="interval_base_map.html" title="Class template interval_base_map">interval_base_map</a> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36436-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><a class="link" href="interval_base_map.html" title="Class template interval_base_map">interval_base_map</a><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="interval_base_map.html#idm36145-bb">public member functions</a></span>
  <span class="keyword">typedef</span> <a class="link" href="interval_base_map.html#idm36146-bb"><span class="identifier">ICL_INTERVAL_TYPE</span></a><span class="special">(</span><span class="identifier">Interval</span><span class="special">,</span> <span class="identifier">DomainT</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">typedef</span> <a class="link" href="interval_base_map.html#idm36155-bb"><span class="identifier">ICL_COMPARE_DOMAIN</span></a><span class="special">(</span><span class="identifier">Compare</span><span class="special">,</span> <span class="identifier">DomainT</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">typedef</span> <a class="link" href="interval_base_map.html#idm36162-bb"><span class="identifier">ICL_COMPARE_DOMAIN</span></a><span class="special">(</span><span class="identifier">Compare</span><span class="special">,</span> <span class="identifier">segment_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">typedef</span> <a class="link" href="interval_base_map.html#idm36168-bb"><span class="identifier">ICL_COMBINE_CODOMAIN</span></a><span class="special">(</span><span class="identifier">Combine</span><span class="special">,</span> <span class="identifier">CodomainT</span><span class="special">)</span><span class="special">;</span>
   <a class="link" href="interval_base_map.html#idm36175-bb"><span class="identifier">BOOST_STATIC_CONSTANT</span></a><span class="special">(</span><span class="keyword">bool</span><span class="special">,</span> 
                         <span class="identifier">is_total_invertible</span> <span class="special">=</span> <span class="special">(</span><span class="identifier">Traits</span><span class="special">::</span><span class="identifier">is_total</span> <span class="special">&amp;&amp;</span><span class="identifier">has_inverse</span><span class="special">&lt;</span> <span class="identifier">codomain_type</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
   <a class="link" href="interval_base_map.html#idm36182-bb"><span class="identifier">BOOST_STATIC_CONSTANT</span></a><span class="special">(</span><span class="keyword">int</span><span class="special">,</span> <span class="identifier">fineness</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36189-bb"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="interval_base_map.html" title="Class template interval_base_map">interval_base_map</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36196-bb"><span class="identifier">clear</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="interval_base_map.html#idm36200-bb"><span class="identifier">empty</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="interval_base_map.html#idm36204-bb"><span class="identifier">size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <a class="link" href="interval_base_map.html#idm36208-bb"><span class="identifier">iterative_size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="interval_base_map.html#idm36212-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">domain_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="interval_base_map.html#idm36219-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">codomain_type</span> <a class="link" href="interval_base_map.html#idm36226-bb"><span class="keyword">operator</span><span class="special">(</span><span class="special">)</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">domain_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36232-bb"><span class="identifier">add</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36238-bb"><span class="identifier">add</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="interval_base_map.html#idm36244-bb"><span class="identifier">add</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36255-bb"><span class="identifier">subtract</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36261-bb"><span class="identifier">subtract</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36267-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36274-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="interval_base_map.html#idm36281-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36292-bb"><span class="identifier">set</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36301-bb"><span class="identifier">set</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36310-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36317-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36324-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">domain_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36331-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36338-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36345-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36354-bb"><span class="identifier">add_intersection</span></a><span class="special">(</span><span class="identifier">SubType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36365-bb"><span class="identifier">flip</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">SubType</span> <span class="special">&amp;</span> <a class="link" href="interval_base_map.html#idm36373-bb"><span class="identifier">flip</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="interval_base_map.html#idm36381-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="interval_base_map.html#idm36385-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="interval_base_map.html#idm36389-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="interval_base_map.html#idm36393-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> <a class="link" href="interval_base_map.html#idm36397-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
  <a class="link" href="interval_base_map.html#idm36401-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="interval_base_map.html#idm36405-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="interval_base_map.html#idm36407-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="interval_base_map.html#idm36409-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="interval_base_map.html#idm36411-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="interval_base_map.html#idm36413-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="interval_base_map.html#idm36415-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="interval_base_map.html#idm36417-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="interval_base_map.html#idm36419-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="interval_base_map.html#idm36444-bb">private member functions</a></span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> <span class="identifier">iterator</span> <a class="link" href="interval_base_map.html#idm36445-bb"><span class="identifier">_add</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> <span class="identifier">iterator</span> <a class="link" href="interval_base_map.html#idm36451-bb"><span class="identifier">_add</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36459-bb"><span class="identifier">_subtract</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="interval_base_map.html#idm36465-bb"><span class="identifier">_insert</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="interval_base_map.html#idm36469-bb"><span class="identifier">_insert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36475-bb"><span class="identifier">add_segment</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36485-bb"><span class="identifier">add_main</span></a><span class="special">(</span><span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">,</span> 
                  <span class="keyword">const</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36497-bb"><span class="identifier">add_rear</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36507-bb"><span class="identifier">add_front</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36513-bb"><span class="identifier">subtract_front</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36519-bb"><span class="identifier">subtract_main</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36529-bb"><span class="identifier">subtract_rear</span></a><span class="special">(</span><span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36539-bb"><span class="identifier">insert_main</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">,</span> 
                   <span class="keyword">const</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36549-bb"><span class="identifier">erase_rest</span></a><span class="special">(</span><span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">,</span> 
                  <span class="keyword">const</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> FragmentT<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36559-bb"><span class="identifier">total_add_intersection</span></a><span class="special">(</span><span class="identifier">SubType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">FragmentT</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36567-bb"><span class="identifier">partial_add_intersection</span></a><span class="special">(</span><span class="identifier">SubType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="interval_base_map.html#idm36573-bb"><span class="identifier">partial_add_intersection</span></a><span class="special">(</span><span class="identifier">SubType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="interval_base_map.html#idm36579-bb">protected member functions</a></span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="interval_base_map.html#idm36580-bb"><span class="identifier">gap_insert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> 
                        <span class="keyword">const</span> <span class="identifier">codomain_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
    <a class="link" href="interval_base_map.html#idm36590-bb"><span class="identifier">add_at</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">codomain_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
  <a class="link" href="interval_base_map.html#idm36600-bb"><span class="identifier">insert_at</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">codomain_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">sub_type</span> <span class="special">*</span> <a class="link" href="interval_base_map.html#idm36608-bb"><span class="identifier">that</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">const</span> <span class="identifier">sub_type</span> <span class="special">*</span> <a class="link" href="interval_base_map.html#idm36610-bb"><span class="identifier">that</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm44846"></a><h2>Description</h2>
<div class="refsect2">
<a name="idm44848"></a><h3>
<a name="boost.icl.interval_base_mapconstruct-copy-destruct"></a><code class="computeroutput">interval_base_map</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a name="idm36421-bb"></a><span class="identifier">interval_base_map</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>Default constructor for the empty object </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm36424-bb"></a><span class="identifier">interval_base_map</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="interval_base_map.html" title="Class template interval_base_map">interval_base_map</a> <span class="special">&amp;</span> src<span class="special">)</span><span class="special">;</span></pre>
<p>Copy constructor </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm36430-bb"></a><span class="identifier">interval_base_map</span><span class="special">(</span><a class="link" href="interval_base_map.html" title="Class template interval_base_map">interval_base_map</a> <span class="special">&amp;&amp;</span> src<span class="special">)</span><span class="special">;</span></pre>
<p>Move constructor </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="interval_base_map.html" title="Class template interval_base_map">interval_base_map</a> <span class="special">&amp;</span> <a name="idm36436-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><a class="link" href="interval_base_map.html" title="Class template interval_base_map">interval_base_map</a> src<span class="special">)</span><span class="special">;</span></pre>
<p>Move assignment operator </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm44898"></a><h3>
<a name="idm36145-bb"></a><code class="computeroutput">interval_base_map</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">typedef</span> <a name="idm36146-bb"></a><span class="identifier">ICL_INTERVAL_TYPE</span><span class="special">(</span><span class="identifier">Interval</span><span class="special">,</span> <span class="identifier">DomainT</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">)</span><span class="special">;</span></pre>The interval type of the map. </li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typedef</span> <a name="idm36155-bb"></a><span class="identifier">ICL_COMPARE_DOMAIN</span><span class="special">(</span><span class="identifier">Compare</span><span class="special">,</span> <span class="identifier">DomainT</span><span class="special">)</span><span class="special">;</span></pre>Comparison functor for domain values. </li>
<li class="listitem"><pre class="literallayout"><span class="keyword">typedef</span> <a name="idm36162-bb"></a><span class="identifier">ICL_COMPARE_DOMAIN</span><span class="special">(</span><span class="identifier">Compare</span><span class="special">,</span> <span class="identifier">segment_type</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typedef</span> <a name="idm36168-bb"></a><span class="identifier">ICL_COMBINE_CODOMAIN</span><span class="special">(</span><span class="identifier">Combine</span><span class="special">,</span> <span class="identifier">CodomainT</span><span class="special">)</span><span class="special">;</span></pre>Combine functor for codomain value aggregation. </li>
<li class="listitem"><pre class="literallayout"> <a name="idm36175-bb"></a><span class="identifier">BOOST_STATIC_CONSTANT</span><span class="special">(</span><span class="keyword">bool</span><span class="special">,</span> 
                       <span class="identifier">is_total_invertible</span> <span class="special">=</span> <span class="special">(</span><span class="identifier">Traits</span><span class="special">::</span><span class="identifier">is_total</span> <span class="special">&amp;&amp;</span><span class="identifier">has_inverse</span><span class="special">&lt;</span> <span class="identifier">codomain_type</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"> <a name="idm36182-bb"></a><span class="identifier">BOOST_STATIC_CONSTANT</span><span class="special">(</span><span class="keyword">int</span><span class="special">,</span> <span class="identifier">fineness</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm36189-bb"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="interval_base_map.html" title="Class template interval_base_map">interval_base_map</a> <span class="special">&amp;</span> object<span class="special">)</span><span class="special">;</span></pre>
<p>swap the content of containers </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm36196-bb"></a><span class="identifier">clear</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>clear the map </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm36200-bb"></a><span class="identifier">empty</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>is the map empty? </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm36204-bb"></a><span class="identifier">size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>An interval map's size is it's cardinality </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <a name="idm36208-bb"></a><span class="identifier">iterative_size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Size of the iteration over this container </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm36212-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">domain_type</span> <span class="special">&amp;</span> key_value<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Find the interval value pair, that contains <code class="computeroutput">key</code> </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm36219-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span> key_interval<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Find the first interval value pair, that collides with interval <code class="computeroutput">key_interval</code> </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">codomain_type</span> <a name="idm36226-bb"></a><span class="keyword">operator</span><span class="special">(</span><span class="special">)</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">domain_type</span> <span class="special">&amp;</span> key_value<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Total select function. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36232-bb"></a><span class="identifier">add</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span> key_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>Addition of a key value pair to the map </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36238-bb"></a><span class="identifier">add</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>Addition of an interval value pair to the map. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm36244-bb"></a><span class="identifier">add</span><span class="special">(</span><span class="identifier">iterator</span> prior_<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>Addition of an interval value pair <code class="computeroutput">interval_value_pair</code> to the map. Iterator <code class="computeroutput">prior_</code> is a hint to the position <code class="computeroutput">interval_value_pair</code> can be inserted after. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36255-bb"></a><span class="identifier">subtract</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span> key_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>Subtraction of a key value pair from the map </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36261-bb"></a><span class="identifier">subtract</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>Subtraction of an interval value pair from the map. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36267-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span> key_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>Insertion of a <code class="computeroutput">key_value_pair</code> into the map. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36274-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>Insertion of an <code class="computeroutput">interval_value_pair</code> into the map. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm36281-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">iterator</span> prior<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>Insertion of an <code class="computeroutput">interval_value_pair</code> into the map. Iterator <code class="computeroutput">prior_</code>. serves as a hint to insert after the element <code class="computeroutput">prior</code> point to. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36292-bb"></a><span class="identifier">set</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span> key_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>With <code class="computeroutput">key_value_pair = (k,v)</code> set value <code class="computeroutput">v</code> for key <code class="computeroutput">k</code> </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36301-bb"></a><span class="identifier">set</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>With <code class="computeroutput">interval_value_pair = (I,v)</code> set value <code class="computeroutput">v</code> for all keys in interval <code class="computeroutput">I</code> in the map. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36310-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span> key_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>Erase a <code class="computeroutput">key_value_pair</code> from the map. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36317-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>Erase an <code class="computeroutput">interval_value_pair</code> from the map. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36324-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">domain_type</span> <span class="special">&amp;</span> key<span class="special">)</span><span class="special">;</span></pre>
<p>Erase a key value pair for <code class="computeroutput">key</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36331-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span> inter_val<span class="special">)</span><span class="special">;</span></pre>
<p>Erase all value pairs within the range of the interval <code class="computeroutput">inter_val</code> from the map. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm36338-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">iterator</span> position<span class="special">)</span><span class="special">;</span></pre>
<p>Erase all value pairs within the range of the interval that iterator <code class="computeroutput">position</code> points to. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm36345-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">iterator</span> first<span class="special">,</span> <span class="identifier">iterator</span> past<span class="special">)</span><span class="special">;</span></pre>
<p>Erase all value pairs for a range of iterators <code class="computeroutput">[first,past)</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm36354-bb"></a><span class="identifier">add_intersection</span><span class="special">(</span><span class="identifier">SubType</span> <span class="special">&amp;</span> section<span class="special">,</span> 
                      <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>The intersection of <code class="computeroutput">interval_value_pair</code> and <code class="computeroutput">*this</code> map is added to <code class="computeroutput">section</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36365-bb"></a><span class="identifier">flip</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span> key_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>If <code class="computeroutput">*this</code> map contains <code class="computeroutput">key_value_pair</code> it is erased, otherwise it is added. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">SubType</span> <span class="special">&amp;</span> <a name="idm36373-bb"></a><span class="identifier">flip</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span><span class="special">;</span></pre>
<p>If <code class="computeroutput">*this</code> map contains <code class="computeroutput">interval_value_pair</code> it is erased, otherwise it is added. </p>
</li>
<li class="listitem"><pre class="literallayout"><span class="identifier">iterator</span> <a name="idm36381-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> interval<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">iterator</span> <a name="idm36385-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> interval<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm36389-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> interval<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm36393-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> interval<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> <a name="idm36397-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> interval<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
<a name="idm36401-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> interval<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">iterator</span> <a name="idm36405-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">iterator</span> <a name="idm36407-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm36409-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm36411-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm36413-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm36415-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm36417-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm36419-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm45552"></a><h3>
<a name="idm36444-bb"></a><code class="computeroutput">interval_base_map</code> private member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm36445-bb"></a><span class="identifier">_add</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm36451-bb"></a><span class="identifier">_add</span><span class="special">(</span><span class="identifier">iterator</span> prior_<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm36459-bb"></a><span class="identifier">_subtract</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">iterator</span> <a name="idm36465-bb"></a><span class="identifier">_insert</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">iterator</span> <a name="idm36469-bb"></a><span class="identifier">_insert</span><span class="special">(</span><span class="identifier">iterator</span> prior_<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> interval_value_pair<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm36475-bb"></a><span class="identifier">add_segment</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span> inter_val<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span> co_val<span class="special">,</span> 
                   <span class="identifier">iterator</span> <span class="special">&amp;</span> it_<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm36485-bb"></a><span class="identifier">add_main</span><span class="special">(</span><span class="identifier">interval_type</span> <span class="special">&amp;</span> inter_val<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span> co_val<span class="special">,</span> 
                <span class="identifier">iterator</span> <span class="special">&amp;</span> it_<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">iterator</span> <span class="special">&amp;</span> last_<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm36497-bb"></a><span class="identifier">add_rear</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span> inter_val<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span> co_val<span class="special">,</span> 
                <span class="identifier">iterator</span> <span class="special">&amp;</span> it_<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm36507-bb"></a><span class="identifier">add_front</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span> inter_val<span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span> first_<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm36513-bb"></a><span class="identifier">subtract_front</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span> inter_val<span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span> first_<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm36519-bb"></a><span class="identifier">subtract_main</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span> co_val<span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span> it_<span class="special">,</span> 
                     <span class="keyword">const</span> <span class="identifier">iterator</span> <span class="special">&amp;</span> last_<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm36529-bb"></a><span class="identifier">subtract_rear</span><span class="special">(</span><span class="identifier">interval_type</span> <span class="special">&amp;</span> inter_val<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span> co_val<span class="special">,</span> 
                     <span class="identifier">iterator</span> <span class="special">&amp;</span> it_<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm36539-bb"></a><span class="identifier">insert_main</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm36549-bb"></a><span class="identifier">erase_rest</span><span class="special">(</span><span class="identifier">interval_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CodomainT</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">,</span> 
                <span class="keyword">const</span> <span class="identifier">iterator</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> FragmentT<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm36559-bb"></a><span class="identifier">total_add_intersection</span><span class="special">(</span><span class="identifier">SubType</span> <span class="special">&amp;</span> section<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">FragmentT</span> <span class="special">&amp;</span> fragment<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm36567-bb"></a><span class="identifier">partial_add_intersection</span><span class="special">(</span><span class="identifier">SubType</span> <span class="special">&amp;</span> section<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">segment_type</span> <span class="special">&amp;</span> operand<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">void</span> <a name="idm36573-bb"></a><span class="identifier">partial_add_intersection</span><span class="special">(</span><span class="identifier">SubType</span> <span class="special">&amp;</span> section<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">element_type</span> <span class="special">&amp;</span> operand<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
</ol></div>
</div>
<div class="refsect2">
<a name="idm45877"></a><h3>
<a name="idm36579-bb"></a><code class="computeroutput">interval_base_map</code> protected member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm36580-bb"></a><span class="identifier">gap_insert</span><span class="special">(</span><span class="identifier">iterator</span> prior_<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span> inter_val<span class="special">,</span> 
                      <span class="keyword">const</span> <span class="identifier">codomain_type</span> <span class="special">&amp;</span> co_val<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Combiner<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
  <a name="idm36590-bb"></a><span class="identifier">add_at</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">iterator</span> <span class="special">&amp;</span> prior_<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span> inter_val<span class="special">,</span> 
         <span class="keyword">const</span> <span class="identifier">codomain_type</span> <span class="special">&amp;</span> co_val<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
<a name="idm36600-bb"></a><span class="identifier">insert_at</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">iterator</span> <span class="special">&amp;</span> prior_<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">interval_type</span> <span class="special">&amp;</span> inter_val<span class="special">,</span> 
          <span class="keyword">const</span> <span class="identifier">codomain_type</span> <span class="special">&amp;</span> co_val<span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="identifier">sub_type</span> <span class="special">*</span> <a name="idm36608-bb"></a><span class="identifier">that</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">const</span> <span class="identifier">sub_type</span> <span class="special">*</span> <a name="idm36610-bb"></a><span class="identifier">that</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre></li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2007-2010 Joachim
      Faulhaber<br>Copyright © 1999-2006 Cortex Software
      GmbH<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="has_inverse_icl_i_idm35758.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../header/boost/icl/interval_base_map_hpp.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="interval_base_map/on_codomain_model.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
