<?xml version="1.0" standalone="yes"?>
<library-reference id="interval_container_library_reference"><title>Interval Container Library Reference</title><header name="boost/icl/associative_element_container.hpp">
</header>
<header name="boost/icl/associative_interval_container.hpp">
</header>
<header name="boost/icl/closed_interval.hpp">
<namespace name="boost">
<namespace name="icl">
<class name="closed_interval"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
    </template><typedef name="type"><type><classname>closed_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<typedef name="domain_type"><type>DomainT</type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
<method name="lower" cv="const"><type>DomainT</type></method>
<method name="upper" cv="const"><type>DomainT</type></method>
<method name="first" cv="const"><type>DomainT</type></method>
<method name="last" cv="const"><type>DomainT</type></method>
</method-group>
<constructor><description><para>Default constructor; yields an empty interval <computeroutput>[0,0)</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="val"><paramtype>const DomainT &amp;</paramtype></parameter><description><para>Constructor for a closed singleton interval <computeroutput>[val,val]</computeroutput> </para></description></constructor>
<constructor><parameter name="low"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter><description><para>Interval from <computeroutput>low</computeroutput> to <computeroutput>up</computeroutput> with bounds <computeroutput>bounds</computeroutput> </para></description></constructor>
</class><struct-specialization name="interval_bound_type"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>closed_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="type"><type>interval_bound_type</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>interval_bounds::static_closed</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="interval_traits"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::closed_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="domain_type"><type>DomainT</type></typedef>
<typedef name="interval_type"><type><classname>icl::closed_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
</method-group>
<method-group name="public static functions">
<method name="construct" specifiers="static"><type><classname>interval_type</classname></type><parameter name="lo"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="up"><paramtype>const domain_type &amp;</paramtype></parameter></method>
<method name="lower" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const <classname>interval_type</classname> &amp;</paramtype></parameter></method>
<method name="upper" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const <classname>interval_type</classname> &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="type_to_string"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::closed_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::string</type></method>
</method-group>
</struct-specialization><struct-specialization name="value_size"><template>
      <template-type-parameter name="DomainT"/>
    </template><specialization><template-arg>icl::closed_interval&lt; DomainT &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::size_t</type><parameter name=""><paramtype>const <classname>icl::closed_interval</classname>&lt; DomainT &gt; &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/continuous_interval.hpp">
<namespace name="boost">
<namespace name="icl">
<class name="continuous_interval"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
    </template><typedef name="type"><type><classname>continuous_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<typedef name="domain_type"><type>DomainT</type></typedef>
<typedef name="bounded_domain_type"><type><classname>bounded_value</classname>&lt; DomainT &gt;::<classname>type</classname></type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
<method name="lower" cv="const"><type>domain_type</type></method>
<method name="upper" cv="const"><type>domain_type</type></method>
<method name="bounds" cv="const"><type><classname>interval_bounds</classname></type></method>
</method-group>
<constructor><description><para>Default constructor; yields an empty interval <computeroutput>[0,0)</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="val"><paramtype>const DomainT &amp;</paramtype></parameter><description><para>Constructor for a closed singleton interval <computeroutput>[val,val]</computeroutput> </para></description></constructor>
<constructor><parameter name="low"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="bounds"><paramtype><classname>interval_bounds</classname></paramtype><default>interval_bounds::right_open()</default></parameter><parameter name=""><paramtype><classname>continuous_interval</classname> *</paramtype><default>0</default></parameter><description><para>Interval from <computeroutput>low</computeroutput> to <computeroutput>up</computeroutput> with bounds <computeroutput>bounds</computeroutput> </para></description></constructor>
<method-group name="public static functions">
<method name="open" specifiers="static"><type><classname>continuous_interval</classname></type><parameter name="lo"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter></method>
<method name="right_open" specifiers="static"><type><classname>continuous_interval</classname></type><parameter name="lo"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter></method>
<method name="left_open" specifiers="static"><type><classname>continuous_interval</classname></type><parameter name="lo"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter></method>
<method name="closed" specifiers="static"><type><classname>continuous_interval</classname></type><parameter name="lo"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter></method>
</method-group>
</class><struct-specialization name="dynamic_interval_traits"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>boost::icl::continuous_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="type"><type><classname>dynamic_interval_traits</classname></type></typedef>
<typedef name="interval_type"><type><classname>boost::icl::continuous_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<typedef name="domain_type"><type>DomainT</type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
</method-group>
<method-group name="public static functions">
<method name="construct" specifiers="static"><type><classname>interval_type</classname></type><parameter name="lo"><paramtype>const domain_type</paramtype></parameter><parameter name="up"><paramtype>const domain_type</paramtype></parameter><parameter name="bounds"><paramtype><classname>interval_bounds</classname></paramtype></parameter></method>
<method name="construct_bounded" specifiers="static"><type><classname>interval_type</classname></type><parameter name="lo"><paramtype>const <classname>bounded_value</classname>&lt; DomainT &gt; &amp;</paramtype></parameter><parameter name="up"><paramtype>const <classname>bounded_value</classname>&lt; DomainT &gt; &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="interval_bound_type"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>continuous_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="type"><type>interval_bound_type</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>interval_bounds::dynamic</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="interval_traits"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::continuous_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="type"><type><classname>interval_traits</classname></type></typedef>
<typedef name="domain_type"><type>DomainT</type></typedef>
<typedef name="interval_type"><type><classname>icl::continuous_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
</method-group>
<method-group name="public static functions">
<method name="construct" specifiers="static"><type><classname>interval_type</classname></type><parameter name="lo"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="up"><paramtype>const domain_type &amp;</paramtype></parameter></method>
<method name="lower" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const <classname>interval_type</classname> &amp;</paramtype></parameter></method>
<method name="upper" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const <classname>interval_type</classname> &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_continuous_interval"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>continuous_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="type"><type>is_continuous_interval&lt; <classname>continuous_interval</classname>&lt; DomainT, Compare &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="type_to_string"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::continuous_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::string</type></method>
</method-group>
</struct-specialization><struct-specialization name="value_size"><template>
      <template-type-parameter name="DomainT"/>
    </template><specialization><template-arg>icl::continuous_interval&lt; DomainT &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::size_t</type><parameter name=""><paramtype>const <classname>icl::continuous_interval</classname>&lt; DomainT &gt; &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/discrete_interval.hpp">
<namespace name="boost">
<namespace name="icl">
<class name="discrete_interval"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
    </template><typedef name="type"><type><classname>discrete_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<typedef name="domain_type"><type>DomainT</type></typedef>
<typedef name="bounded_domain_type"><type><classname>bounded_value</classname>&lt; DomainT &gt;::<classname>type</classname></type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
<method name="lower" cv="const"><type>domain_type</type></method>
<method name="upper" cv="const"><type>domain_type</type></method>
<method name="bounds" cv="const"><type><classname>interval_bounds</classname></type></method>
</method-group>
<constructor><description><para>Default constructor; yields an empty interval <computeroutput>[0,0)</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="val"><paramtype>const DomainT &amp;</paramtype></parameter><description><para>Constructor for a closed singleton interval <computeroutput>[val,val]</computeroutput> </para></description></constructor>
<constructor><parameter name="low"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="bounds"><paramtype><classname>interval_bounds</classname></paramtype><default>interval_bounds::right_open()</default></parameter><parameter name=""><paramtype><classname>discrete_interval</classname> *</paramtype><default>0</default></parameter><description><para>Interval from <computeroutput>low</computeroutput> to <computeroutput>up</computeroutput> with bounds <computeroutput>bounds</computeroutput> </para></description></constructor>
<method-group name="public static functions">
<method name="open" specifiers="static"><type><classname>discrete_interval</classname></type><parameter name="lo"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter></method>
<method name="right_open" specifiers="static"><type><classname>discrete_interval</classname></type><parameter name="lo"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter></method>
<method name="left_open" specifiers="static"><type><classname>discrete_interval</classname></type><parameter name="lo"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter></method>
<method name="closed" specifiers="static"><type><classname>discrete_interval</classname></type><parameter name="lo"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter></method>
</method-group>
</class><struct-specialization name="dynamic_interval_traits"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>boost::icl::discrete_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="type"><type><classname>dynamic_interval_traits</classname></type></typedef>
<typedef name="interval_type"><type><classname>boost::icl::discrete_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<typedef name="domain_type"><type>DomainT</type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
</method-group>
<method-group name="public static functions">
<method name="construct" specifiers="static"><type><classname>interval_type</classname></type><parameter name="lo"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="up"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="bounds"><paramtype><classname>interval_bounds</classname></paramtype></parameter></method>
<method name="construct_bounded" specifiers="static"><type><classname>interval_type</classname></type><parameter name="lo"><paramtype>const <classname>bounded_value</classname>&lt; DomainT &gt; &amp;</paramtype></parameter><parameter name="up"><paramtype>const <classname>bounded_value</classname>&lt; DomainT &gt; &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="interval_bound_type"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>discrete_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="type"><type>interval_bound_type</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>interval_bounds::dynamic</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="interval_traits"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::discrete_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="type"><type><classname>interval_traits</classname></type></typedef>
<typedef name="domain_type"><type>DomainT</type></typedef>
<typedef name="interval_type"><type><classname>icl::discrete_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
</method-group>
<method-group name="public static functions">
<method name="construct" specifiers="static"><type><classname>interval_type</classname></type><parameter name="lo"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="up"><paramtype>const domain_type &amp;</paramtype></parameter></method>
<method name="lower" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const <classname>interval_type</classname> &amp;</paramtype></parameter></method>
<method name="upper" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const <classname>interval_type</classname> &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_discrete_interval"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>discrete_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="type"><type>is_discrete_interval&lt; <classname>discrete_interval</classname>&lt; DomainT, Compare &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>is_discrete&lt; DomainT &gt;::value</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="type_to_string"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::discrete_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::string</type></method>
</method-group>
</struct-specialization><struct-specialization name="value_size"><template>
      <template-type-parameter name="DomainT"/>
    </template><specialization><template-arg>icl::discrete_interval&lt; DomainT &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::size_t</type><parameter name=""><paramtype>const <classname>icl::discrete_interval</classname>&lt; DomainT &gt; &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/dynamic_interval_traits.hpp">
<namespace name="boost">
<namespace name="icl">
<struct name="dynamic_interval_traits"><template>
      <template-type-parameter name="Type"/>
    </template><typedef name="domain_type"><type>Type::domain_type</type></typedef>
<typedef name="domain_compare"><type>Type::domain_compare</type></typedef>
<method-group name="public static functions">
<method name="construct" specifiers="static"><type>Type</type><parameter name="lo"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="up"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="bounds"><paramtype><classname>interval_bounds</classname></paramtype></parameter></method>
<method name="construct_bounded" specifiers="static"><type>Type</type><parameter name="lo"><paramtype>const <classname>bounded_value</classname>&lt; domain_type &gt; &amp;</paramtype></parameter><parameter name="up"><paramtype>const <classname>bounded_value</classname>&lt; domain_type &gt; &amp;</paramtype></parameter></method>
</method-group>
</struct>









</namespace>
</namespace>
</header>
<header name="boost/icl/functors.hpp">
<namespace name="boost">
<namespace name="icl">
<struct name="conversion"><template>
      <template-type-parameter name="Combiner"/>
    </template><typedef name="type"><type><classname>conversion</classname>&lt; Combiner &gt;</type></typedef>
<typedef name="argument_type"><type>remove_const&lt; typename remove_reference&lt; typename Combiner::first_argument_type &gt;::<classname>type</classname> &gt;::<classname>type</classname></type></typedef>
<method-group name="public static functions">
<method name="proversion" specifiers="static"><type>argument_type</type><parameter name="value"><paramtype>const argument_type &amp;</paramtype></parameter></method>
<method name="inversion" specifiers="static"><type>argument_type</type><parameter name="value"><paramtype>const argument_type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="identity_based_inplace_combine"><template>
      <template-type-parameter name="Type"/>
    </template><typedef name="first_argument_type"><type>Type &amp;</type></typedef>
<typedef name="second_argument_type"><type>const Type &amp;</type></typedef>
<typedef name="result_type"><type>void</type></typedef>
<method-group name="public static functions">
<method name="identity_element" specifiers="static"><type>Type</type></method>
</method-group>
</struct><struct name="inplace_bit_add"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_bit_add</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
<method-group name="public static functions">
<method name="version" specifiers="static"><type>void</type><parameter name=""><paramtype>Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_bit_and"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_bit_and</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_bit_subtract"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_bit_subtract</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_bit_xor"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_bit_xor</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_caret"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_caret</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_erase"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_erase</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_erasure"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_erasure</classname>&lt; Type &gt;</type></typedef>
<typedef name="base_type"><type><classname>identity_based_inplace_combine</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_et"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_et</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_identity"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_identity</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name=""><paramtype>Type &amp;</paramtype></parameter><parameter name=""><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_insert"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_insert</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_max"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_max</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_min"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_min</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_minus"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_minus</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_plus"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_plus</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
<method-group name="public static functions">
<method name="version" specifiers="static"><type>void</type><parameter name=""><paramtype>Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_slash"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_slash</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inplace_star"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type><classname>inplace_star</classname>&lt; Type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inter_section"><template>
      <template-type-parameter name="Type"/>
    </template><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type>boost::mpl::if_&lt; has_set_semantics&lt; Type &gt;, <classname>icl::inplace_et</classname>&lt; Type &gt;, <classname>icl::inplace_plus</classname>&lt; Type &gt; &gt;::type</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="inverse"><template>
      <template-type-parameter name="Functor"/>
    </template><typedef name="argument_type"><type>remove_reference&lt; typename Functor::first_argument_type &gt;::<classname>type</classname></type></typedef>
<typedef name="type"><type><classname>icl::inplace_erasure</classname>&lt; argument_type &gt;</type></typedef>
</struct><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_bit_add&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>icl::inplace_bit_subtract</classname>&lt; Type &gt;</type></typedef>
</struct-specialization><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_bit_and&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>icl::inplace_bit_xor</classname>&lt; Type &gt;</type></typedef>
</struct-specialization><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_bit_subtract&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>icl::inplace_bit_add</classname>&lt; Type &gt;</type></typedef>
</struct-specialization><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_bit_xor&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>icl::inplace_bit_and</classname>&lt; Type &gt;</type></typedef>
</struct-specialization><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_caret&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>icl::inplace_et</classname>&lt; Type &gt;</type></typedef>
</struct-specialization><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_et&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>icl::inplace_caret</classname>&lt; Type &gt;</type></typedef>
</struct-specialization><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_identity&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>icl::inplace_erasure</classname>&lt; Type &gt;</type></typedef>
</struct-specialization><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_max&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>icl::inplace_min</classname>&lt; Type &gt;</type></typedef>
</struct-specialization><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_min&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>icl::inplace_max</classname>&lt; Type &gt;</type></typedef>
</struct-specialization><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_minus&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>icl::inplace_plus</classname>&lt; Type &gt;</type></typedef>
</struct-specialization><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_plus&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>icl::inplace_minus</classname>&lt; Type &gt;</type></typedef>
</struct-specialization><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_slash&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>icl::inplace_star</classname>&lt; Type &gt;</type></typedef>
</struct-specialization><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_star&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>icl::inplace_slash</classname>&lt; Type &gt;</type></typedef>
</struct-specialization><struct-specialization name="inverse"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inter_section&lt; Type &gt;</template-arg></specialization><inherit access="public">boost::icl::identity_based_inplace_combine&lt; Type &gt;</inherit><typedef name="type"><type>boost::mpl::if_&lt; has_set_semantics&lt; Type &gt;, <classname>icl::inplace_caret</classname>&lt; Type &gt;, <classname>icl::inplace_minus</classname>&lt; Type &gt; &gt;::type</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct name="is_negative"><template>
      <template-type-parameter name="Functor"/>
    </template><typedef name="type"><type><classname>is_negative</classname>&lt; Functor &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>false</default></parameter></method>
</method-group>
</struct><struct-specialization name="is_negative"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_bit_subtract&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>is_negative</classname></type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_negative"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_minus&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>is_negative</classname></type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct name="unit_element_based_inplace_combine"><template>
      <template-type-parameter name="Type"/>
    </template><typedef name="first_argument_type"><type>Type &amp;</type></typedef>
<typedef name="second_argument_type"><type>const Type &amp;</type></typedef>
<typedef name="result_type"><type>void</type></typedef>
<method-group name="public static functions">
<method name="identity_element" specifiers="static"><type>Type</type></method>
</method-group>
</struct><struct name="version"><template>
      <template-type-parameter name="Combiner"/>
    </template><inherit access="public">boost::icl::conversion&lt; Combiner &gt;</inherit><typedef name="type"><type><classname>version</classname>&lt; Combiner &gt;</type></typedef>
<typedef name="base_type"><type><classname>conversion</classname>&lt; Combiner &gt;</type></typedef>
<typedef name="argument_type"><type>base_type::argument_type</type></typedef>
<method-group name="public member functions">
<method name="operator()"><type>argument_type</type><parameter name="value"><paramtype>const argument_type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct-specialization name="version"><template>
    </template><specialization><template-arg>icl::inplace_minus&lt; double &gt;</template-arg></specialization><method-group name="public member functions">
<method name="operator()"><type>double</type><parameter name="val"><paramtype>double</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="version"><template>
    </template><specialization><template-arg>icl::inplace_minus&lt; float &gt;</template-arg></specialization><method-group name="public member functions">
<method name="operator()"><type>float</type><parameter name="val"><paramtype>float</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="version"><template>
    </template><specialization><template-arg>icl::inplace_minus&lt; int &gt;</template-arg></specialization><method-group name="public member functions">
<method name="operator()"><type>int</type><parameter name="val"><paramtype>int</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="version"><template>
    </template><specialization><template-arg>icl::inplace_minus&lt; long &gt;</template-arg></specialization><method-group name="public member functions">
<method name="operator()"><type>long</type><parameter name="val"><paramtype>long</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="version"><template>
    </template><specialization><template-arg>icl::inplace_minus&lt; long double &gt;</template-arg></specialization><method-group name="public member functions">
<method name="operator()"><type>long double</type><parameter name="val"><paramtype>long double</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="version"><template>
    </template><specialization><template-arg>icl::inplace_minus&lt; long long &gt;</template-arg></specialization><method-group name="public member functions">
<method name="operator()"><type>long long</type><parameter name="val"><paramtype>long long</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="version"><template>
    </template><specialization><template-arg>icl::inplace_minus&lt; short &gt;</template-arg></specialization><method-group name="public member functions">
<method name="operator()"><type>short</type><parameter name="val"><paramtype>short</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="version"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>icl::inplace_minus&lt; Type &gt;</template-arg></specialization><inherit access="public">boost::icl::conversion&lt; icl::inplace_minus&lt; Type &gt; &gt;</inherit><typedef name="type"><type><classname>version</classname>&lt; <classname>icl::inplace_minus</classname>&lt; Type &gt; &gt;</type></typedef>
<typedef name="base_type"><type><classname>conversion</classname>&lt; <classname>icl::inplace_minus</classname>&lt; Type &gt; &gt;</type></typedef>
<typedef name="argument_type"><type>base_type::argument_type</type></typedef>
<method-group name="public member functions">
<method name="operator()"><type>Type</type><parameter name="value"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/gregorian.hpp">
<namespace name="boost">
<namespace name="icl">
<struct-specialization name="difference_type_of"><template>
    </template><specialization><template-arg>boost::gregorian::date</template-arg></specialization><typedef name="type"><type>boost::gregorian::date_duration</type></typedef>
</struct-specialization><struct-specialization name="has_difference"><template>
    </template><specialization><template-arg>boost::gregorian::date</template-arg></specialization><typedef name="type"><type>has_difference</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="has_difference"><template>
    </template><specialization><template-arg>boost::gregorian::date_duration</template-arg></specialization><typedef name="type"><type>has_difference</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="identity_element"><template>
    </template><specialization><template-arg>boost::gregorian::date_duration</template-arg></specialization><method-group name="public static functions">
<method name="value" specifiers="static"><type>boost::gregorian::date_duration</type></method>
</method-group>
</struct-specialization><struct-specialization name="is_discrete"><template>
    </template><specialization><template-arg>boost::gregorian::date</template-arg></specialization><typedef name="type"><type>is_discrete</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_discrete"><template>
    </template><specialization><template-arg>boost::gregorian::date_duration</template-arg></specialization><typedef name="type"><type>is_discrete</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="size_type_of"><template>
    </template><specialization><template-arg>boost::gregorian::date</template-arg></specialization><typedef name="type"><type>boost::gregorian::date_duration</type></typedef>
</struct-specialization><struct-specialization name="size_type_of"><template>
    </template><specialization><template-arg>boost::gregorian::date_duration</template-arg></specialization><typedef name="type"><type>boost::gregorian::date_duration</type></typedef>
</struct-specialization>





<function name="operator++"><type>boost::gregorian::date</type><parameter name="x"><paramtype>boost::gregorian::date &amp;</paramtype></parameter></function>
<function name="operator--"><type>boost::gregorian::date</type><parameter name="x"><paramtype>boost::gregorian::date &amp;</paramtype></parameter></function>
<function name="operator++"><type>boost::gregorian::date_duration</type><parameter name="x"><paramtype>boost::gregorian::date_duration &amp;</paramtype></parameter></function>
<function name="operator--"><type>boost::gregorian::date_duration</type><parameter name="x"><paramtype>boost::gregorian::date_duration &amp;</paramtype></parameter></function>
</namespace>
</namespace>
</header>
<header name="boost/icl/impl_config.hpp">
<macro name="ICL_IMPL_SPACE"/>
</header>
<header name="boost/icl/interval.hpp">
<namespace name="boost">
<namespace name="icl">
<struct name="interval"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
    </template><typedef name="interval_type"><type>interval_type_default&lt; DomainT, Compare &gt;::type</type></typedef>
<typedef name="type"><type>interval_type</type></typedef>
<method-group name="public static functions">
<method name="right_open" specifiers="static"><type>interval_type</type><parameter name="low"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter></method>
<method name="left_open" specifiers="static"><type>interval_type</type><parameter name="low"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter></method>
<method name="open" specifiers="static"><type>interval_type</type><parameter name="low"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter></method>
<method name="closed" specifiers="static"><type>interval_type</type><parameter name="low"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter></method>
<method name="construct" specifiers="static"><type>interval_type</type><parameter name="low"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="static_interval"><template>
      <template-type-parameter name="IntervalT"/>
      <template-nontype-parameter name="IsDiscrete"><type>bool</type></template-nontype-parameter>
      <template-nontype-parameter name="PretendedBounds"><type>bound_type</type></template-nontype-parameter>
      <template-nontype-parameter name="RepresentedBounds"><type>bound_type</type></template-nontype-parameter>
    </template></struct><struct-specialization name="static_interval"><template>
      <template-type-parameter name="IntervalT"/>
      <template-nontype-parameter name="PretendedBounds"><type>bound_type</type></template-nontype-parameter>
      <template-nontype-parameter name="RepresentedBounds"><type>bound_type</type></template-nontype-parameter>
    </template><specialization><template-arg>IntervalT</template-arg><template-arg>false</template-arg><template-arg>PretendedBounds</template-arg><template-arg>RepresentedBounds</template-arg></specialization><typedef name="domain_type"><type><classname>interval_traits</classname>&lt; IntervalT &gt;::domain_type</type></typedef>
<method-group name="public static functions">
<method name="construct" specifiers="static"><type>IntervalT</type><parameter name="low"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="up"><paramtype>const domain_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="static_interval"><template>
      <template-type-parameter name="IntervalT"/>
      <template-nontype-parameter name="PretendedBounds"><type>bound_type</type></template-nontype-parameter>
      <template-nontype-parameter name="RepresentedBounds"><type>bound_type</type></template-nontype-parameter>
    </template><specialization><template-arg>IntervalT</template-arg><template-arg>true</template-arg><template-arg>PretendedBounds</template-arg><template-arg>RepresentedBounds</template-arg></specialization><typedef name="domain_type"><type><classname>interval_traits</classname>&lt; IntervalT &gt;::domain_type</type></typedef>
<method-group name="public static functions">
<method name="construct" specifiers="static"><type>IntervalT</type><parameter name="low"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="up"><paramtype>const domain_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/interval_base_map.hpp">
<namespace name="boost">
<namespace name="icl">
<struct-specialization name="absorbs_identities"><template>
      <template-type-parameter name="SubType"/>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_base_map&lt; SubType</template-arg><template-arg>DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>absorbs_identities&lt; <classname>icl::interval_base_map</classname>&lt; SubType, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>(Traits::absorbs_identities)</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="has_inverse"><template>
      <template-type-parameter name="SubType"/>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_base_map&lt; SubType</template-arg><template-arg>DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>has_inverse&lt; <classname>icl::interval_base_map</classname>&lt; SubType, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>(has_inverse&lt; CodomainT &gt;::value)</default></parameter></method>
</method-group>
</struct-specialization><class name="interval_base_map"><template>
      <template-type-parameter name="SubType"/>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"><default><classname alt="boost::icl::partial_absorber">icl::partial_absorber</classname></default></template-type-parameter>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type><default>ICL_COMBINE_INSTANCE(<classname alt="boost::icl::inplace_plus">icl::inplace_plus</classname>, CodomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type><default>ICL_SECTION_INSTANCE(<classname alt="boost::icl::inter_section">icl::inter_section</classname>, CodomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type><default>ICL_INTERVAL_INSTANCE(ICL_INTERVAL_DEFAULT, DomainT, Compare)</default></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type><default>std::allocator</default></template-nontype-parameter>
    </template><purpose>Implements a map as a map of intervals (base class) </purpose><struct name="on_codomain_model"><template>
      <template-type-parameter name="Type"/>
      <template-nontype-parameter name="has_set_semantics"><type>bool</type></template-nontype-parameter>
    </template></struct><struct-specialization name="on_codomain_model"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>false</template-arg></specialization><typedef name="interval_type"><type>Type::interval_type</type></typedef>
<typedef name="codomain_type"><type>Type::codomain_type</type></typedef>
<typedef name="segment_type"><type>Type::segment_type</type></typedef>
<typedef name="codomain_combine"><type>Type::codomain_combine</type></typedef>
<method-group name="public static functions">
<method name="add" specifiers="static"><type>void</type><parameter name="intersection"><paramtype>Type &amp;</paramtype></parameter><parameter name="common_interval"><paramtype>interval_type &amp;</paramtype></parameter><parameter name=""><paramtype>const codomain_type &amp;</paramtype></parameter><parameter name=""><paramtype>const codomain_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="on_codomain_model"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>true</template-arg></specialization><typedef name="interval_type"><type>Type::interval_type</type></typedef>
<typedef name="codomain_type"><type>Type::codomain_type</type></typedef>
<typedef name="segment_type"><type>Type::segment_type</type></typedef>
<typedef name="codomain_combine"><type>Type::codomain_combine</type></typedef>
<typedef name="inverse_codomain_intersect"><type>Type::inverse_codomain_intersect</type></typedef>
<method-group name="public static functions">
<method name="add" specifiers="static"><type>void</type><parameter name="intersection"><paramtype>Type &amp;</paramtype></parameter><parameter name="common_interval"><paramtype>interval_type &amp;</paramtype></parameter><parameter name="flip_value"><paramtype>const codomain_type &amp;</paramtype></parameter><parameter name="co_value"><paramtype>const codomain_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct name="on_definedness"><template>
      <template-type-parameter name="Type"/>
      <template-nontype-parameter name="is_total"><type>bool</type></template-nontype-parameter>
    </template></struct><struct-specialization name="on_definedness"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>false</template-arg></specialization><method-group name="public static functions">
<method name="add_intersection" specifiers="static"><type>void</type><parameter name="section"><paramtype>Type &amp;</paramtype></parameter><parameter name="object"><paramtype>const Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const segment_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="on_definedness"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>true</template-arg></specialization><method-group name="public static functions">
<method name="add_intersection" specifiers="static"><type>void</type><parameter name="section"><paramtype>Type &amp;</paramtype></parameter><parameter name="object"><paramtype>const Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const segment_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct name="on_invertible"><template>
      <template-type-parameter name="Type"/>
      <template-nontype-parameter name="is_total_invertible"><type>bool</type></template-nontype-parameter>
    </template></struct><struct-specialization name="on_invertible"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>false</template-arg></specialization><typedef name="segment_type"><type>Type::segment_type</type></typedef>
<typedef name="inverse_codomain_combine"><type>Type::inverse_codomain_combine</type></typedef>
<method-group name="public static functions">
<method name="subtract" specifiers="static"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const segment_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="on_invertible"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>true</template-arg></specialization><typedef name="segment_type"><type>Type::segment_type</type></typedef>
<typedef name="inverse_codomain_combine"><type>Type::inverse_codomain_combine</type></typedef>
<method-group name="public static functions">
<method name="subtract" specifiers="static"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const segment_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct name="on_total_absorbable"><template>
      <template-type-parameter name="Type"/>
      <template-nontype-parameter name="is_total"><type>bool</type></template-nontype-parameter>
      <template-nontype-parameter name="absorbs_identities"><type>bool</type></template-nontype-parameter>
    </template></struct><struct-specialization name="on_total_absorbable"><template>
      <template-type-parameter name="Type"/>
      <template-nontype-parameter name="absorbs_identities"><type>bool</type></template-nontype-parameter>
    </template><specialization><template-arg>Type</template-arg><template-arg>false</template-arg><template-arg>absorbs_identities</template-arg></specialization><typedef name="segment_type"><type>Type::segment_type</type></typedef>
<typedef name="codomain_type"><type>Type::codomain_type</type></typedef>
<typedef name="interval_type"><type>Type::interval_type</type></typedef>
<typedef name="value_type"><type>Type::value_type</type></typedef>
<typedef name="const_iterator"><type>Type::const_iterator</type></typedef>
<typedef name="set_type"><type>Type::set_type</type></typedef>
<typedef name="inverse_codomain_intersect"><type>Type::inverse_codomain_intersect</type></typedef>
<method-group name="public static functions">
<method name="flip" specifiers="static"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="on_total_absorbable"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>true</template-arg><template-arg>false</template-arg></specialization><typedef name="segment_type"><type>Type::segment_type</type></typedef>
<typedef name="codomain_type"><type>Type::codomain_type</type></typedef>
<method-group name="public static functions">
<method name="flip" specifiers="static"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const segment_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="on_total_absorbable"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>true</template-arg><template-arg>true</template-arg></specialization><method-group name="public static functions">
<method name="flip" specifiers="static"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name=""><paramtype>const typename Type::segment_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><typedef name="type"><type><classname>interval_base_map</classname>&lt; SubType, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt;</type></typedef>
<typedef name="sub_type"><purpose>The designated <emphasis>derived</emphasis> or <emphasis>sub_type</emphasis> of this base class. </purpose><type>SubType</type></typedef>
<typedef name="overloadable_type"><purpose>Auxilliary type for overloadresolution. </purpose><type><classname>type</classname></type></typedef>
<typedef name="traits"><purpose>Traits of an itl map. </purpose><type>Traits</type></typedef>
<typedef name="atomized_type"><purpose>The atomized type representing the corresponding container of elements. </purpose><type><classname>icl::map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Alloc &gt;</type></typedef>
<typedef name="domain_type"><purpose>Domain type (type of the keys) of the map. </purpose><type>DomainT</type></typedef>
<typedef name="domain_param"><type>boost::call_traits&lt; DomainT &gt;::param_type</type></typedef>
<typedef name="codomain_type"><purpose>Domain type (type of the keys) of the map. </purpose><type>CodomainT</type></typedef>
<typedef name="domain_mapping_type"><purpose>Auxiliary type to help the compiler resolve ambiguities when using std::make_pair. </purpose><type><classname>mapping_pair</classname>&lt; domain_type, codomain_type &gt;</type></typedef>
<typedef name="element_type"><purpose>Conceptual is a map a set of elements of type <computeroutput>element_type</computeroutput>. </purpose><type>domain_mapping_type</type></typedef>
<typedef name="interval_mapping_type"><purpose>Auxiliary type for overload resolution. </purpose><type>std::pair&lt; interval_type, CodomainT &gt;</type></typedef>
<typedef name="segment_type"><purpose>Type of an interval containers segment, that is spanned by an interval. </purpose><type>std::pair&lt; interval_type, CodomainT &gt;</type></typedef>
<typedef name="difference_type"><purpose>The difference type of an interval which is sometimes different form the domain_type. </purpose><type>difference_type_of&lt; domain_type &gt;::<classname>type</classname></type></typedef>
<typedef name="size_type"><purpose>The size type of an interval which is mostly std::size_t. </purpose><type>size_type_of&lt; domain_type &gt;::<classname>type</classname></type></typedef>
<typedef name="inverse_codomain_combine"><purpose>Inverse Combine functor for codomain value aggregation. </purpose><type><classname>inverse</classname>&lt; codomain_combine &gt;::<classname>type</classname></type></typedef>
<typedef name="codomain_intersect"><purpose>Intersection functor for codomain values. </purpose><type>mpl::if_&lt; has_set_semantics&lt; codomain_type &gt;, ICL_SECTION_CODOMAIN(Section, CodomainT), codomain_combine &gt;::<classname>type</classname></type></typedef>
<typedef name="inverse_codomain_intersect"><purpose>Inverse Combine functor for codomain value intersection. </purpose><type><classname>inverse</classname>&lt; codomain_intersect &gt;::<classname>type</classname></type></typedef>
<typedef name="interval_compare"><purpose>Comparison functor for intervals which are keys as well. </purpose><type>exclusive_less_than&lt; interval_type &gt;</type></typedef>
<typedef name="key_compare"><purpose>Comparison functor for keys. </purpose><type>exclusive_less_than&lt; interval_type &gt;</type></typedef>
<typedef name="allocator_type"><purpose>The allocator type of the set. </purpose><type>Alloc&lt; std::pair&lt; const interval_type, codomain_type &gt; &gt;</type></typedef>
<typedef name="ImplMapT"><purpose>Container type for the implementation. </purpose><type>ICL_IMPL_SPACE::map&lt; interval_type, codomain_type, key_compare, allocator_type &gt;</type></typedef>
<typedef name="key_type"><purpose>key type of the implementing container </purpose><type>ImplMapT::key_type</type></typedef>
<typedef name="value_type"><purpose>value type of the implementing container </purpose><type>ImplMapT::value_type</type></typedef>
<typedef name="data_type"><purpose>data type of the implementing container </purpose><type>ImplMapT::value_type::second_type</type></typedef>
<typedef name="pointer"><purpose>pointer type </purpose><type>ImplMapT::pointer</type></typedef>
<typedef name="const_pointer"><purpose>const pointer type </purpose><type>ImplMapT::const_pointer</type></typedef>
<typedef name="reference"><purpose>reference type </purpose><type>ImplMapT::reference</type></typedef>
<typedef name="const_reference"><purpose>const reference type </purpose><type>ImplMapT::const_reference</type></typedef>
<typedef name="iterator"><purpose>iterator for iteration over intervals </purpose><type>ImplMapT::iterator</type></typedef>
<typedef name="const_iterator"><purpose>const_iterator for iteration over intervals </purpose><type>ImplMapT::const_iterator</type></typedef>
<typedef name="reverse_iterator"><purpose>iterator for reverse iteration over intervals </purpose><type>ImplMapT::reverse_iterator</type></typedef>
<typedef name="const_reverse_iterator"><purpose>const_iterator for iteration over intervals </purpose><type>ImplMapT::const_reverse_iterator</type></typedef>
<typedef name="element_iterator"><purpose>element iterator: Depreciated, see documentation. </purpose><type>boost::icl::element_iterator&lt; iterator &gt;</type></typedef>
<typedef name="element_const_iterator"><purpose>const element iterator: Depreciated, see documentation. </purpose><type>boost::icl::element_iterator&lt; const_iterator &gt;</type></typedef>
<typedef name="element_reverse_iterator"><purpose>element reverse iterator: Depreciated, see documentation. </purpose><type>boost::icl::element_iterator&lt; reverse_iterator &gt;</type></typedef>
<typedef name="element_const_reverse_iterator"><purpose>element const reverse iterator: Depreciated, see documentation. </purpose><type>boost::icl::element_iterator&lt; const_reverse_iterator &gt;</type></typedef>
<typedef name="on_codomain_absorbtion"><type>on_absorbtion&lt; <classname>type</classname>, codomain_combine, Traits::absorbs_identities &gt;::<classname>type</classname></type></typedef>
<method-group name="public member functions">
<method name="ICL_INTERVAL_TYPE"><type>typedef</type><parameter name=""><paramtype>Interval</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter><parameter name=""><paramtype>Compare</paramtype></parameter><purpose>The interval type of the map. </purpose></method>
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter><purpose>Comparison functor for domain values. </purpose></method>
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>segment_type</paramtype></parameter></method>
<method name="ICL_COMBINE_CODOMAIN"><type>typedef</type><parameter name=""><paramtype>Combine</paramtype></parameter><parameter name=""><paramtype>CodomainT</paramtype></parameter><purpose>Combine functor for codomain value aggregation. </purpose></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>is_total_invertible</paramtype><default>(Traits::is_total &amp;&amp;has_inverse&lt; codomain_type &gt;::value)</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>int</paramtype></parameter><parameter name=""><paramtype>fineness</paramtype><default>0</default></parameter></method>
<method name="swap"><type>void</type><parameter name="object"><paramtype><classname>interval_base_map</classname> &amp;</paramtype></parameter><description><para>swap the content of containers </para></description></method>
<method name="clear"><type>void</type><description><para>clear the map </para></description></method>
<method name="empty" cv="const"><type>bool</type><description><para>is the map empty? </para></description></method>
<method name="size" cv="const"><type>size_type</type><description><para>An interval map's size is it's cardinality </para></description></method>
<method name="iterative_size" cv="const"><type>std::size_t</type><description><para>Size of the iteration over this container </para></description></method>
<method name="find" cv="const"><type>const_iterator</type><parameter name="key_value"><paramtype>const domain_type &amp;</paramtype></parameter><description><para>Find the interval value pair, that contains <computeroutput>key</computeroutput> </para></description></method>
<method name="find" cv="const"><type>const_iterator</type><parameter name="key_interval"><paramtype>const interval_type &amp;</paramtype></parameter><description><para>Find the first interval value pair, that collides with interval <computeroutput>key_interval</computeroutput> </para></description></method>
<method name="operator()" cv="const"><type>codomain_type</type><parameter name="key_value"><paramtype>const domain_type &amp;</paramtype></parameter><description><para>Total select function. </para></description></method>
<method name="add"><type>SubType &amp;</type><parameter name="key_value_pair"><paramtype>const element_type &amp;</paramtype></parameter><description><para>Addition of a key value pair to the map </para></description></method>
<method name="add"><type>SubType &amp;</type><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>Addition of an interval value pair to the map. </para></description></method>
<method name="add"><type>iterator</type><parameter name="prior_"><paramtype>iterator</paramtype></parameter><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>Addition of an interval value pair <computeroutput>interval_value_pair</computeroutput> to the map. Iterator <computeroutput>prior_</computeroutput> is a hint to the position <computeroutput>interval_value_pair</computeroutput> can be inserted after. </para></description></method>
<method name="subtract"><type>SubType &amp;</type><parameter name="key_value_pair"><paramtype>const element_type &amp;</paramtype></parameter><description><para>Subtraction of a key value pair from the map </para></description></method>
<method name="subtract"><type>SubType &amp;</type><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>Subtraction of an interval value pair from the map. </para></description></method>
<method name="insert"><type>SubType &amp;</type><parameter name="key_value_pair"><paramtype>const element_type &amp;</paramtype></parameter><description><para>Insertion of a <computeroutput>key_value_pair</computeroutput> into the map. </para></description></method>
<method name="insert"><type>SubType &amp;</type><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>Insertion of an <computeroutput>interval_value_pair</computeroutput> into the map. </para></description></method>
<method name="insert"><type>iterator</type><parameter name="prior"><paramtype>iterator</paramtype></parameter><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>Insertion of an <computeroutput>interval_value_pair</computeroutput> into the map. Iterator <computeroutput>prior_</computeroutput>. serves as a hint to insert after the element <computeroutput>prior</computeroutput> point to. </para></description></method>
<method name="set"><type>SubType &amp;</type><parameter name="key_value_pair"><paramtype>const element_type &amp;</paramtype></parameter><description><para>With <computeroutput>key_value_pair = (k,v)</computeroutput> set value <computeroutput>v</computeroutput> for key <computeroutput>k</computeroutput> </para></description></method>
<method name="set"><type>SubType &amp;</type><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>With <computeroutput>interval_value_pair = (I,v)</computeroutput> set value <computeroutput>v</computeroutput> for all keys in interval <computeroutput>I</computeroutput> in the map. </para></description></method>
<method name="erase"><type>SubType &amp;</type><parameter name="key_value_pair"><paramtype>const element_type &amp;</paramtype></parameter><description><para>Erase a <computeroutput>key_value_pair</computeroutput> from the map. </para></description></method>
<method name="erase"><type>SubType &amp;</type><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>Erase an <computeroutput>interval_value_pair</computeroutput> from the map. </para></description></method>
<method name="erase"><type>SubType &amp;</type><parameter name="key"><paramtype>const domain_type &amp;</paramtype></parameter><description><para>Erase a key value pair for <computeroutput>key</computeroutput>. </para></description></method>
<method name="erase"><type>SubType &amp;</type><parameter name="inter_val"><paramtype>const interval_type &amp;</paramtype></parameter><description><para>Erase all value pairs within the range of the interval <computeroutput>inter_val</computeroutput> from the map. </para></description></method>
<method name="erase"><type>void</type><parameter name="position"><paramtype>iterator</paramtype></parameter><description><para>Erase all value pairs within the range of the interval that iterator <computeroutput>position</computeroutput> points to. </para></description></method>
<method name="erase"><type>void</type><parameter name="first"><paramtype>iterator</paramtype></parameter><parameter name="past"><paramtype>iterator</paramtype></parameter><description><para>Erase all value pairs for a range of iterators <computeroutput>[first,past)</computeroutput>. </para></description></method>
<method name="add_intersection" cv="const"><type>void</type><parameter name="section"><paramtype>SubType &amp;</paramtype></parameter><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>The intersection of <computeroutput>interval_value_pair</computeroutput> and <computeroutput>*this</computeroutput> map is added to <computeroutput>section</computeroutput>. </para></description></method>
<method name="flip"><type>SubType &amp;</type><parameter name="key_value_pair"><paramtype>const element_type &amp;</paramtype></parameter><description><para>If <computeroutput>*this</computeroutput> map contains <computeroutput>key_value_pair</computeroutput> it is erased, otherwise it is added. </para></description></method>
<method name="flip"><type>SubType &amp;</type><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>If <computeroutput>*this</computeroutput> map contains <computeroutput>interval_value_pair</computeroutput> it is erased, otherwise it is added. </para></description></method>
<method name="lower_bound"><type>iterator</type><parameter name="interval"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="upper_bound"><type>iterator</type><parameter name="interval"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="lower_bound" cv="const"><type>const_iterator</type><parameter name="interval"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="upper_bound" cv="const"><type>const_iterator</type><parameter name="interval"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="equal_range"><type>std::pair&lt; iterator, iterator &gt;</type><parameter name="interval"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="equal_range" cv="const"><type>std::pair&lt; const_iterator, const_iterator &gt;</type><parameter name="interval"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="begin"><type>iterator</type></method>
<method name="end"><type>iterator</type></method>
<method name="begin" cv="const"><type>const_iterator</type></method>
<method name="end" cv="const"><type>const_iterator</type></method>
<method name="rbegin"><type>reverse_iterator</type></method>
<method name="rend"><type>reverse_iterator</type></method>
<method name="rbegin" cv="const"><type>const_reverse_iterator</type></method>
<method name="rend" cv="const"><type>const_reverse_iterator</type></method>
</method-group>
<constructor><description><para>Default constructor for the empty object </para></description></constructor>
<constructor><parameter name="src"><paramtype>const <classname>interval_base_map</classname> &amp;</paramtype></parameter><description><para>Copy constructor </para></description></constructor>
<constructor><parameter name="src"><paramtype><classname>interval_base_map</classname> &amp;&amp;</paramtype></parameter><description><para>Move constructor </para></description></constructor>
<copy-assignment><type><classname>interval_base_map</classname> &amp;</type><parameter name="src"><paramtype><classname>interval_base_map</classname></paramtype></parameter><description><para>Move assignment operator </para></description></copy-assignment>
<method-group name="private member functions">
<method name="_add"><type>iterator</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter></method>
<method name="_add"><type>iterator</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="prior_"><paramtype>iterator</paramtype></parameter><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter></method>
<method name="_subtract"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter></method>
<method name="_insert"><type>iterator</type><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter></method>
<method name="_insert"><type>iterator</type><parameter name="prior_"><paramtype>iterator</paramtype></parameter><parameter name="interval_value_pair"><paramtype>const segment_type &amp;</paramtype></parameter></method>
<method name="add_segment"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="inter_val"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="co_val"><paramtype>const CodomainT &amp;</paramtype></parameter><parameter name="it_"><paramtype>iterator &amp;</paramtype></parameter></method>
<method name="add_main"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="inter_val"><paramtype>interval_type &amp;</paramtype></parameter><parameter name="co_val"><paramtype>const CodomainT &amp;</paramtype></parameter><parameter name="it_"><paramtype>iterator &amp;</paramtype></parameter><parameter name="last_"><paramtype>const iterator &amp;</paramtype></parameter></method>
<method name="add_rear"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="inter_val"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="co_val"><paramtype>const CodomainT &amp;</paramtype></parameter><parameter name="it_"><paramtype>iterator &amp;</paramtype></parameter></method>
<method name="add_front"><type>void</type><parameter name="inter_val"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="first_"><paramtype>iterator &amp;</paramtype></parameter></method>
<method name="subtract_front"><type>void</type><parameter name="inter_val"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="first_"><paramtype>iterator &amp;</paramtype></parameter></method>
<method name="subtract_main"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="co_val"><paramtype>const CodomainT &amp;</paramtype></parameter><parameter name="it_"><paramtype>iterator &amp;</paramtype></parameter><parameter name="last_"><paramtype>const iterator &amp;</paramtype></parameter></method>
<method name="subtract_rear"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="inter_val"><paramtype>interval_type &amp;</paramtype></parameter><parameter name="co_val"><paramtype>const CodomainT &amp;</paramtype></parameter><parameter name="it_"><paramtype>iterator &amp;</paramtype></parameter></method>
<method name="insert_main"><type>void</type><parameter name=""><paramtype>const interval_type &amp;</paramtype></parameter><parameter name=""><paramtype>const CodomainT &amp;</paramtype></parameter><parameter name=""><paramtype>iterator &amp;</paramtype></parameter><parameter name=""><paramtype>const iterator &amp;</paramtype></parameter></method>
<method name="erase_rest"><type>void</type><parameter name=""><paramtype>interval_type &amp;</paramtype></parameter><parameter name=""><paramtype>const CodomainT &amp;</paramtype></parameter><parameter name=""><paramtype>iterator &amp;</paramtype></parameter><parameter name=""><paramtype>const iterator &amp;</paramtype></parameter></method>
<method name="total_add_intersection" cv="const"><type>void</type><template>
          <template-type-parameter name="FragmentT"/>
        </template><parameter name="section"><paramtype>SubType &amp;</paramtype></parameter><parameter name="fragment"><paramtype>const FragmentT &amp;</paramtype></parameter></method>
<method name="partial_add_intersection" cv="const"><type>void</type><parameter name="section"><paramtype>SubType &amp;</paramtype></parameter><parameter name="operand"><paramtype>const segment_type &amp;</paramtype></parameter></method>
<method name="partial_add_intersection" cv="const"><type>void</type><parameter name="section"><paramtype>SubType &amp;</paramtype></parameter><parameter name="operand"><paramtype>const element_type &amp;</paramtype></parameter></method>
</method-group>
<method-group name="protected member functions">
<method name="gap_insert"><type>iterator</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="prior_"><paramtype>iterator</paramtype></parameter><parameter name="inter_val"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="co_val"><paramtype>const codomain_type &amp;</paramtype></parameter></method>
<method name="add_at"><type>std::pair&lt; iterator, bool &gt;</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="prior_"><paramtype>const iterator &amp;</paramtype></parameter><parameter name="inter_val"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="co_val"><paramtype>const codomain_type &amp;</paramtype></parameter></method>
<method name="insert_at"><type>std::pair&lt; iterator, bool &gt;</type><parameter name="prior_"><paramtype>const iterator &amp;</paramtype></parameter><parameter name="inter_val"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="co_val"><paramtype>const codomain_type &amp;</paramtype></parameter></method>
<method name="that"><type>sub_type *</type></method>
<method name="that" cv="const"><type>const sub_type *</type></method>
</method-group>
</class><struct-specialization name="is_interval_container"><template>
      <template-type-parameter name="SubType"/>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_base_map&lt; SubType</template-arg><template-arg>DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_interval_container&lt; <classname>icl::interval_base_map</classname>&lt; SubType, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_map"><template>
      <template-type-parameter name="SubType"/>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_base_map&lt; SubType</template-arg><template-arg>DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_map&lt; <classname>icl::interval_base_map</classname>&lt; SubType, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_total"><template>
      <template-type-parameter name="SubType"/>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_base_map&lt; SubType</template-arg><template-arg>DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_total&lt; <classname>icl::interval_base_map</classname>&lt; SubType, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>(Traits::is_total)</default></parameter></method>
</method-group>
</struct-specialization><struct name="mapping_pair"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
    </template><data-member name="key"><type>DomainT</type></data-member>
<data-member name="data"><type>CodomainT</type></data-member>
<method-group name="public member functions">
</method-group>
<constructor/>
<constructor><parameter name="key_value"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="data_value"><paramtype>const CodomainT &amp;</paramtype></parameter></constructor>
<constructor><parameter name="std_pair"><paramtype>const std::pair&lt; DomainT, CodomainT &gt; &amp;</paramtype></parameter></constructor>
</struct>









</namespace>
</namespace>
</header>
<header name="boost/icl/interval_base_set.hpp">
<namespace name="boost">
<namespace name="icl">
<class name="interval_base_set"><template>
      <template-type-parameter name="SubType"/>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type><default>ICL_INTERVAL_INSTANCE(ICL_INTERVAL_DEFAULT, DomainT, Compare)</default></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type><default>std::allocator</default></template-nontype-parameter>
    </template><purpose>Implements a set as a set of intervals (base class) </purpose><typedef name="type"><type><classname>interval_base_set</classname>&lt; SubType, DomainT, Compare, Interval, Alloc &gt;</type></typedef>
<typedef name="sub_type"><purpose>The designated <emphasis>derived</emphasis> or <emphasis>sub_type</emphasis> of this base class. </purpose><type>SubType</type></typedef>
<typedef name="overloadable_type"><purpose>Auxilliary type for overloadresolution. </purpose><type><classname>type</classname></type></typedef>
<typedef name="domain_type"><purpose>The domain type of the set. </purpose><type>DomainT</type></typedef>
<typedef name="codomain_type"><purpose>The codomaintype is the same as domain_type. </purpose><type>DomainT</type></typedef>
<typedef name="element_type"><purpose>The element type of the set. </purpose><type>DomainT</type></typedef>
<typedef name="segment_type"><purpose>The segment type of the set. </purpose><type>interval_type</type></typedef>
<typedef name="difference_type"><purpose>The difference type of an interval which is sometimes different form the data_type. </purpose><type>difference_type_of&lt; domain_type &gt;::<classname>type</classname></type></typedef>
<typedef name="size_type"><purpose>The size type of an interval which is mostly std::size_t. </purpose><type>size_type_of&lt; domain_type &gt;::<classname>type</classname></type></typedef>
<typedef name="interval_compare"><purpose>Comparison functor for intervals. </purpose><type>exclusive_less_than&lt; interval_type &gt;</type></typedef>
<typedef name="key_compare"><purpose>Comparison functor for keys. </purpose><type>exclusive_less_than&lt; interval_type &gt;</type></typedef>
<typedef name="atomized_type"><purpose>The atomized type representing the corresponding container of elements. </purpose><type>ICL_IMPL_SPACE::set&lt; DomainT, domain_compare, Alloc&lt; DomainT &gt; &gt;</type></typedef>
<typedef name="allocator_type"><purpose>The allocator type of the set. </purpose><type>Alloc&lt; interval_type &gt;</type></typedef>
<typedef name="domain_allocator_type"><purpose>allocator type of the corresponding element set </purpose><type>Alloc&lt; DomainT &gt;</type></typedef>
<typedef name="ImplSetT"><purpose>Container type for the implementation. </purpose><type>ICL_IMPL_SPACE::set&lt; interval_type, key_compare, allocator_type &gt;</type></typedef>
<typedef name="key_type"><purpose>key type of the implementing container </purpose><type>ImplSetT::key_type</type></typedef>
<typedef name="data_type"><purpose>data type of the implementing container </purpose><type>ImplSetT::key_type</type></typedef>
<typedef name="value_type"><purpose>value type of the implementing container </purpose><type>ImplSetT::value_type</type></typedef>
<typedef name="pointer"><purpose>pointer type </purpose><type>ImplSetT::pointer</type></typedef>
<typedef name="const_pointer"><purpose>const pointer type </purpose><type>ImplSetT::const_pointer</type></typedef>
<typedef name="reference"><purpose>reference type </purpose><type>ImplSetT::reference</type></typedef>
<typedef name="const_reference"><purpose>const reference type </purpose><type>ImplSetT::const_reference</type></typedef>
<typedef name="iterator"><purpose>iterator for iteration over intervals </purpose><type>ImplSetT::iterator</type></typedef>
<typedef name="const_iterator"><purpose>const_iterator for iteration over intervals </purpose><type>ImplSetT::const_iterator</type></typedef>
<typedef name="reverse_iterator"><purpose>iterator for reverse iteration over intervals </purpose><type>ImplSetT::reverse_iterator</type></typedef>
<typedef name="const_reverse_iterator"><purpose>const_iterator for iteration over intervals </purpose><type>ImplSetT::const_reverse_iterator</type></typedef>
<typedef name="element_iterator"><purpose>element iterator: Depreciated, see documentation. </purpose><type>boost::icl::element_iterator&lt; iterator &gt;</type></typedef>
<typedef name="element_const_iterator"><purpose>element const iterator: Depreciated, see documentation. </purpose><type>boost::icl::element_iterator&lt; const_iterator &gt;</type></typedef>
<typedef name="element_reverse_iterator"><purpose>element reverse iterator: Depreciated, see documentation. </purpose><type>boost::icl::element_iterator&lt; reverse_iterator &gt;</type></typedef>
<typedef name="element_const_reverse_iterator"><purpose>element const reverse iterator: Depreciated, see documentation. </purpose><type>boost::icl::element_iterator&lt; const_reverse_iterator &gt;</type></typedef>
<method-group name="public member functions">
<method name="ICL_INTERVAL_TYPE"><type>typedef</type><parameter name=""><paramtype>Interval</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter><parameter name=""><paramtype>Compare</paramtype></parameter><purpose>The interval type of the set. </purpose></method>
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter><purpose>Comparison functor for domain values. </purpose></method>
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>segment_type</paramtype></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>int</paramtype></parameter><parameter name=""><paramtype>fineness</paramtype><default>0</default></parameter></method>
<method name="swap"><type>void</type><parameter name="operand"><paramtype><classname>interval_base_set</classname> &amp;</paramtype></parameter><description><para>swap the content of containers </para></description></method>
<method name="clear"><type>void</type><description><para>sets the container empty </para></description></method>
<method name="empty" cv="const"><type>bool</type><description><para>is the container empty? </para></description></method>
<method name="size" cv="const"><type>size_type</type><description><para>An interval set's size is it's cardinality </para></description></method>
<method name="iterative_size" cv="const"><type>std::size_t</type><description><para>Size of the iteration over this container </para></description></method>
<method name="find" cv="const"><type>const_iterator</type><parameter name="key_value"><paramtype>const element_type &amp;</paramtype></parameter><description><para>Find the interval, that contains element <computeroutput>key_value</computeroutput> </para></description></method>
<method name="find" cv="const"><type>const_iterator</type><parameter name="key_interval"><paramtype>const interval_type &amp;</paramtype></parameter><description><para>Find the first interval, that collides with interval <computeroutput>key_interval</computeroutput> </para></description></method>
<method name="add"><type>SubType &amp;</type><parameter name="key"><paramtype>const element_type &amp;</paramtype></parameter><description><para>Add a single element <computeroutput>key</computeroutput> to the set </para></description></method>
<method name="add"><type>SubType &amp;</type><parameter name="inter_val"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>Add an interval of elements <computeroutput>inter_val</computeroutput> to the set </para></description></method>
<method name="add"><type>iterator</type><parameter name="prior_"><paramtype>iterator</paramtype></parameter><parameter name="inter_val"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>Add an interval of elements <computeroutput>inter_val</computeroutput> to the set. Iterator <computeroutput>prior_</computeroutput> is a hint to the position <computeroutput>inter_val</computeroutput> can be inserted after. </para></description></method>
<method name="subtract"><type>SubType &amp;</type><parameter name="key"><paramtype>const element_type &amp;</paramtype></parameter><description><para>Subtract a single element <computeroutput>key</computeroutput> from the set </para></description></method>
<method name="subtract"><type>SubType &amp;</type><parameter name="inter_val"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>Subtract an interval of elements <computeroutput>inter_val</computeroutput> from the set </para></description></method>
<method name="insert"><type>SubType &amp;</type><parameter name="key"><paramtype>const element_type &amp;</paramtype></parameter><description><para>Insert an element <computeroutput>key</computeroutput> into the set </para></description></method>
<method name="insert"><type>SubType &amp;</type><parameter name="inter_val"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>Insert an interval of elements <computeroutput>inter_val</computeroutput> to the set </para></description></method>
<method name="insert"><type>iterator</type><parameter name="prior_"><paramtype>iterator</paramtype></parameter><parameter name="inter_val"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>Insert an interval of elements <computeroutput>inter_val</computeroutput> to the set. Iterator <computeroutput>prior_</computeroutput> is a hint to the position <computeroutput>inter_val</computeroutput> can be inserted after. </para></description></method>
<method name="erase"><type>SubType &amp;</type><parameter name="key"><paramtype>const element_type &amp;</paramtype></parameter><description><para>Erase an element <computeroutput>key</computeroutput> from the set </para></description></method>
<method name="erase"><type>SubType &amp;</type><parameter name="inter_val"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>Erase an interval of elements <computeroutput>inter_val</computeroutput> from the set </para></description></method>
<method name="erase"><type>void</type><parameter name="position"><paramtype>iterator</paramtype></parameter><description><para>Erase the interval that iterator <computeroutput>position</computeroutput> points to. </para></description></method>
<method name="erase"><type>void</type><parameter name="first"><paramtype>iterator</paramtype></parameter><parameter name="past"><paramtype>iterator</paramtype></parameter><description><para>Erase all intervals in the range <computeroutput>[first,past)</computeroutput> of iterators. </para></description></method>
<method name="flip"><type>SubType &amp;</type><parameter name="key"><paramtype>const element_type &amp;</paramtype></parameter><description><para>If <computeroutput>*this</computeroutput> set contains <computeroutput>key</computeroutput> it is erased, otherwise it is added. </para></description></method>
<method name="flip"><type>SubType &amp;</type><parameter name="inter_val"><paramtype>const segment_type &amp;</paramtype></parameter><description><para>If <computeroutput>*this</computeroutput> set contains <computeroutput>inter_val</computeroutput> it is erased, otherwise it is added. </para></description></method>
<method name="begin"><type>iterator</type></method>
<method name="end"><type>iterator</type></method>
<method name="begin" cv="const"><type>const_iterator</type></method>
<method name="end" cv="const"><type>const_iterator</type></method>
<method name="rbegin"><type>reverse_iterator</type></method>
<method name="rend"><type>reverse_iterator</type></method>
<method name="rbegin" cv="const"><type>const_reverse_iterator</type></method>
<method name="rend" cv="const"><type>const_reverse_iterator</type></method>
<method name="lower_bound"><type>iterator</type><parameter name="interval"><paramtype>const value_type &amp;</paramtype></parameter></method>
<method name="upper_bound"><type>iterator</type><parameter name="interval"><paramtype>const value_type &amp;</paramtype></parameter></method>
<method name="lower_bound" cv="const"><type>const_iterator</type><parameter name="interval"><paramtype>const value_type &amp;</paramtype></parameter></method>
<method name="upper_bound" cv="const"><type>const_iterator</type><parameter name="interval"><paramtype>const value_type &amp;</paramtype></parameter></method>
<method name="equal_range"><type>std::pair&lt; iterator, iterator &gt;</type><parameter name="interval"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="equal_range" cv="const"><type>std::pair&lt; const_iterator, const_iterator &gt;</type><parameter name="interval"><paramtype>const key_type &amp;</paramtype></parameter></method>
</method-group>
<constructor><description><para>Default constructor for the empty object </para></description></constructor>
<constructor><parameter name="src"><paramtype>const <classname>interval_base_set</classname> &amp;</paramtype></parameter><description><para>Copy constructor </para></description></constructor>
<constructor><parameter name="src"><paramtype><classname>interval_base_set</classname> &amp;&amp;</paramtype></parameter><description><para>Move constructor </para></description></constructor>
<copy-assignment><type><classname>interval_base_set</classname> &amp;</type><parameter name="src"><paramtype><classname>interval_base_set</classname></paramtype></parameter><description><para>Move assignment operator </para></description></copy-assignment>
<method-group name="private member functions">
<method name="_add"><type>iterator</type><parameter name="addend"><paramtype>const segment_type &amp;</paramtype></parameter></method>
<method name="_add"><type>iterator</type><parameter name="prior"><paramtype>iterator</paramtype></parameter><parameter name="addend"><paramtype>const segment_type &amp;</paramtype></parameter></method>
</method-group>
<method-group name="protected member functions">
<method name="add_front"><type>void</type><parameter name="inter_val"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="first_"><paramtype>iterator &amp;</paramtype></parameter></method>
<method name="add_main"><type>void</type><parameter name="inter_val"><paramtype>interval_type &amp;</paramtype></parameter><parameter name="it_"><paramtype>iterator &amp;</paramtype></parameter><parameter name="last_"><paramtype>const iterator &amp;</paramtype></parameter></method>
<method name="add_segment"><type>void</type><parameter name="inter_val"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="it_"><paramtype>iterator &amp;</paramtype></parameter></method>
<method name="add_rear"><type>void</type><parameter name="inter_val"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="it_"><paramtype>iterator &amp;</paramtype></parameter></method>
<method name="that"><type>sub_type *</type></method>
<method name="that" cv="const"><type>const sub_type *</type></method>
</method-group>
</class><struct-specialization name="is_interval_container"><template>
      <template-type-parameter name="SubType"/>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_base_set&lt; SubType</template-arg><template-arg>DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_interval_container&lt; <classname>icl::interval_base_set</classname>&lt; SubType, DomainT, Compare, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_set"><template>
      <template-type-parameter name="SubType"/>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_base_set&lt; SubType</template-arg><template-arg>DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_set&lt; <classname>icl::interval_base_set</classname>&lt; SubType, DomainT, Compare, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/interval_bounds.hpp">
<namespace name="boost">
<namespace name="icl">
<class name="bounded_value"><template>
      <template-type-parameter name="DomainT"/>
    </template><typedef name="domain_type"><type>DomainT</type></typedef>
<typedef name="type"><type><classname>bounded_value</classname>&lt; DomainT &gt;</type></typedef>
<method-group name="public member functions">
<method name="value" cv="const"><type>domain_type</type></method>
<method name="bound" cv="const"><type><classname>interval_bounds</classname></type></method>
</method-group>
<constructor><parameter name="value"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="bound"><paramtype><classname>interval_bounds</classname></paramtype></parameter></constructor>
</class><class name="interval_bounds"><data-member name="_bits"><type>bound_type</type></data-member>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>static_open</paramtype><default>0</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>static_left_open</paramtype><default>1</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>static_right_open</paramtype><default>2</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>static_closed</paramtype><default>3</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>dynamic</paramtype><default>4</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>undefined</paramtype><default>5</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>_open</paramtype><default>0</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>_left_open</paramtype><default>1</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>_right_open</paramtype><default>2</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>_closed</paramtype><default>3</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>_right</paramtype><default>1</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>_left</paramtype><default>2</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>_all</paramtype><default>3</default></parameter></method>
<method name="all" cv="const"><type><classname>interval_bounds</classname></type></method>
<method name="left" cv="const"><type><classname>interval_bounds</classname></type></method>
<method name="right" cv="const"><type><classname>interval_bounds</classname></type></method>
<method name="reverse_left" cv="const"><type><classname>interval_bounds</classname></type></method>
<method name="reverse_right" cv="const"><type><classname>interval_bounds</classname></type></method>
<method name="bits" cv="const"><type>bound_type</type></method>
</method-group>
<constructor/>
<constructor specifiers="explicit"><parameter name="bounds"><paramtype>bound_type</paramtype></parameter></constructor>
<method-group name="public static functions">
<method name="open" specifiers="static"><type><classname>interval_bounds</classname></type></method>
<method name="left_open" specifiers="static"><type><classname>interval_bounds</classname></type></method>
<method name="right_open" specifiers="static"><type><classname>interval_bounds</classname></type></method>
<method name="closed" specifiers="static"><type><classname>interval_bounds</classname></type></method>
</method-group>
</class><typedef name="bound_type"><type>unsigned char</type></typedef>










</namespace>
</namespace>
</header>
<header name="boost/icl/interval_combining_style.hpp">
<namespace name="boost">
<namespace name="icl">
<namespace name="interval_combine">
<function name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>int</paramtype></parameter><parameter name=""><paramtype>unknown</paramtype><default>0</default></parameter></function>
<function name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>int</paramtype></parameter><parameter name=""><paramtype>joining</paramtype><default>1</default></parameter></function>
<function name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>int</paramtype></parameter><parameter name=""><paramtype>separating</paramtype><default>2</default></parameter></function>
<function name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>int</paramtype></parameter><parameter name=""><paramtype>splitting</paramtype><default>3</default></parameter></function>
<function name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>int</paramtype></parameter><parameter name=""><paramtype>elemental</paramtype><default>4</default></parameter></function>
</namespace>










</namespace>
</namespace>
</header>
<header name="boost/icl/interval_map.hpp">
<namespace name="boost">
<namespace name="icl">
<struct-specialization name="absorbs_identities"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>absorbs_identities&lt; <classname>icl::interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>(Traits::absorbs_identities)</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="has_inverse"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>has_inverse&lt; <classname>icl::interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>(has_inverse&lt; CodomainT &gt;::value)</default></parameter></method>
</method-group>
</struct-specialization><class name="interval_map"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"><default><classname alt="boost::icl::partial_absorber">icl::partial_absorber</classname></default></template-type-parameter>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type><default>ICL_COMBINE_INSTANCE(<classname alt="boost::icl::inplace_plus">icl::inplace_plus</classname>, CodomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type><default>ICL_SECTION_INSTANCE(<classname alt="boost::icl::inter_section">icl::inter_section</classname>, CodomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type><default>ICL_INTERVAL_INSTANCE(ICL_INTERVAL_DEFAULT, DomainT, Compare)</default></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type><default>std::allocator</default></template-nontype-parameter>
    </template><inherit access="public">boost::icl::interval_base_map&lt; interval_map&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt;, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt;</inherit><purpose>implements a map as a map of intervals - on insertion overlapping intervals are split and associated values are combined. </purpose><enum name="@12"><enumvalue name="fineness"><default>= 1</default></enumvalue></enum>
<typedef name="traits"><type>Traits</type></typedef>
<typedef name="type"><type><classname>interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt;</type></typedef>
<typedef name="split_type"><type><classname>split_interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt;</type></typedef>
<typedef name="overloadable_type"><type><classname>type</classname></type></typedef>
<typedef name="joint_type"><type><classname>type</classname></type></typedef>
<typedef name="base_type"><type><classname>interval_base_map</classname>&lt; <classname>type</classname>, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt;</type></typedef>
<typedef name="iterator"><type>base_type::iterator</type></typedef>
<typedef name="value_type"><type>base_type::value_type</type></typedef>
<typedef name="element_type"><type>base_type::element_type</type></typedef>
<typedef name="segment_type"><type>base_type::segment_type</type></typedef>
<typedef name="domain_type"><type>base_type::domain_type</type></typedef>
<typedef name="codomain_type"><type>base_type::codomain_type</type></typedef>
<typedef name="domain_mapping_type"><type>base_type::domain_mapping_type</type></typedef>
<typedef name="interval_mapping_type"><type>base_type::interval_mapping_type</type></typedef>
<typedef name="ImplMapT"><type>base_type::ImplMapT</type></typedef>
<typedef name="size_type"><type>base_type::size_type</type></typedef>
<typedef name="codomain_combine"><type>base_type::codomain_combine</type></typedef>
<typedef name="interval_set_type"><type><classname>interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt;</type></typedef>
<typedef name="set_type"><type><classname>interval_set_type</classname></type></typedef>
<typedef name="key_object_type"><type><classname>set_type</classname></type></typedef>
<method-group name="public member functions">
<method name="ICL_INTERVAL_TYPE"><type>typedef</type><parameter name=""><paramtype>Interval</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter><parameter name=""><paramtype>Compare</paramtype></parameter></method>
<method name="assign"><type>void</type><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_map</classname>&lt; SubType, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Assignment from a base <classname alt="boost::icl::interval_map">interval_map</classname>. </purpose></method>
</method-group>
<constructor><purpose>Default constructor for the empty object. </purpose></constructor>
<constructor><parameter name="src"><paramtype>const <classname>interval_map</classname> &amp;</paramtype></parameter><purpose>Copy constructor. </purpose></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_map</classname>&lt; SubType, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Copy constructor for base_type. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="base_pair"><paramtype>const <classname>domain_mapping_type</classname> &amp;</paramtype></parameter></constructor>
<constructor specifiers="explicit"><parameter name="value_pair"><paramtype>const value_type &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>interval_map</classname> &amp;</type><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_map</classname>&lt; SubType, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Assignment operator for base type. </purpose></copy-assignment>
<constructor><parameter name="src"><paramtype><classname>interval_map</classname> &amp;&amp;</paramtype></parameter><purpose>Move constructor. </purpose></constructor>
<copy-assignment><type><classname>interval_map</classname> &amp;</type><parameter name="src"><paramtype><classname>interval_map</classname></paramtype></parameter><purpose>Move assignment operator. </purpose></copy-assignment>
<method-group name="private member functions">
<method name="handle_inserted"><type>iterator</type><parameter name="it_"><paramtype>iterator</paramtype></parameter></method>
<method name="handle_inserted"><type>void</type><parameter name="prior_"><paramtype>iterator</paramtype></parameter><parameter name="it_"><paramtype>iterator</paramtype></parameter></method>
<method name="handle_left_combined"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="it_"><paramtype>iterator</paramtype></parameter></method>
<method name="handle_combined"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="it_"><paramtype>iterator</paramtype></parameter></method>
<method name="handle_preceeded_combined"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="prior_"><paramtype>iterator</paramtype></parameter><parameter name="it_"><paramtype>iterator &amp;</paramtype></parameter></method>
<method name="handle_succeeded_combined"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="it_"><paramtype>iterator</paramtype></parameter><parameter name="next_"><paramtype>iterator</paramtype></parameter></method>
<method name="handle_reinserted"><type>void</type><parameter name="insertion_"><paramtype>iterator</paramtype></parameter></method>
<method name="gap_insert_at"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="it_"><paramtype>iterator &amp;</paramtype></parameter><parameter name="prior_"><paramtype>iterator</paramtype></parameter><parameter name="end_gap"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="co_val"><paramtype>const codomain_type &amp;</paramtype></parameter></method>
</method-group>
</class><struct-specialization name="is_interval_container"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_interval_container&lt; <classname>icl::interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_map"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_map&lt; <classname>icl::interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_total"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_total&lt; <classname>icl::interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>(Traits::is_total)</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="type_to_string"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::string</type></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/interval_set.hpp">
<namespace name="boost">
<namespace name="icl">
<class name="interval_set"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type><default>ICL_INTERVAL_INSTANCE(ICL_INTERVAL_DEFAULT, DomainT, Compare)</default></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type><default>std::allocator</default></template-nontype-parameter>
    </template><inherit access="public">boost::icl::interval_base_set&lt; interval_set&lt; DomainT, Compare, Interval, Alloc &gt;, DomainT, Compare, Interval, Alloc &gt;</inherit><purpose>Implements a set as a set of intervals - merging adjoining intervals. </purpose><enum name="@11"><enumvalue name="fineness"><default>= 1</default></enumvalue></enum>
<typedef name="type"><type><classname>interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt;</type></typedef>
<typedef name="base_type"><purpose>The base_type of this class. </purpose><type><classname>interval_base_set</classname>&lt; <classname>type</classname>, DomainT, Compare, Interval, Alloc &gt;</type></typedef>
<typedef name="overloadable_type"><type><classname>type</classname></type></typedef>
<typedef name="joint_type"><type><classname>type</classname></type></typedef>
<typedef name="key_object_type"><type><classname>type</classname></type></typedef>
<typedef name="domain_type"><purpose>The domain type of the set. </purpose><type>DomainT</type></typedef>
<typedef name="codomain_type"><purpose>The codomaintype is the same as domain_type. </purpose><type>DomainT</type></typedef>
<typedef name="element_type"><purpose>The element type of the set. </purpose><type>DomainT</type></typedef>
<typedef name="segment_type"><purpose>The segment type of the set. </purpose><type>interval_type</type></typedef>
<typedef name="interval_compare"><purpose>Comparison functor for intervals. </purpose><type>exclusive_less_than&lt; interval_type &gt;</type></typedef>
<typedef name="key_compare"><purpose>Comparison functor for keys. </purpose><type>exclusive_less_than&lt; interval_type &gt;</type></typedef>
<typedef name="allocator_type"><purpose>The allocator type of the set. </purpose><type>Alloc&lt; interval_type &gt;</type></typedef>
<typedef name="domain_allocator_type"><purpose>allocator type of the corresponding element set </purpose><type>Alloc&lt; DomainT &gt;</type></typedef>
<typedef name="atomized_type"><purpose>The corresponding atomized type representing this interval container of elements. </purpose><type>base_type::atomized_type</type></typedef>
<typedef name="ImplSetT"><purpose>Container type for the implementation. </purpose><type>base_type::ImplSetT</type></typedef>
<typedef name="key_type"><purpose>key type of the implementing container </purpose><type>ImplSetT::key_type</type></typedef>
<typedef name="data_type"><purpose>data type of the implementing container </purpose><type>ImplSetT::value_type</type></typedef>
<typedef name="value_type"><purpose>value type of the implementing container </purpose><type>ImplSetT::value_type</type></typedef>
<typedef name="iterator"><purpose>iterator for iteration over intervals </purpose><type>ImplSetT::iterator</type></typedef>
<typedef name="const_iterator"><purpose>const_iterator for iteration over intervals </purpose><type>ImplSetT::const_iterator</type></typedef>
<method-group name="public member functions">
<method name="ICL_INTERVAL_TYPE"><type>typedef</type><parameter name=""><paramtype>Interval</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter><parameter name=""><paramtype>Compare</paramtype></parameter><purpose>The interval type of the set. </purpose></method>
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter><purpose>Comparison functor for domain values. </purpose></method>
<method name="assign"><type>void</type><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_set</classname>&lt; SubType, DomainT, Compare, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Assignment from a base <classname alt="boost::icl::interval_set">interval_set</classname>. </purpose></method>
</method-group>
<constructor><purpose>Default constructor for the empty object. </purpose></constructor>
<constructor><parameter name="src"><paramtype>const <classname>interval_set</classname> &amp;</paramtype></parameter><purpose>Copy constructor. </purpose></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_set</classname>&lt; SubType, DomainT, Compare, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Copy constructor for base_type. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="value"><paramtype>const domain_type &amp;</paramtype></parameter><purpose>Constructor for a single element. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="itv"><paramtype>const interval_type &amp;</paramtype></parameter><purpose>Constructor for a single interval. </purpose></constructor>
<copy-assignment><type><classname>interval_set</classname> &amp;</type><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_set</classname>&lt; SubType, DomainT, Compare, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Assignment operator for base type. </purpose></copy-assignment>
<constructor><parameter name="src"><paramtype><classname>interval_set</classname> &amp;&amp;</paramtype></parameter><purpose>Move constructor. </purpose></constructor>
<copy-assignment><type><classname>interval_set</classname> &amp;</type><parameter name="src"><paramtype><classname>interval_set</classname></paramtype></parameter><purpose>Move assignment operator. </purpose></copy-assignment>
<method-group name="private member functions">
<method name="handle_inserted"><type>iterator</type><parameter name="it_"><paramtype>iterator</paramtype></parameter></method>
<method name="add_over"><type>iterator</type><parameter name="addend"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="last_"><paramtype>iterator</paramtype></parameter></method>
<method name="add_over"><type>iterator</type><parameter name="addend"><paramtype>const interval_type &amp;</paramtype></parameter></method>
</method-group>
</class><struct-specialization name="is_interval_container"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_set&lt; DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_interval_container&lt; <classname>icl::interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_interval_joiner"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_set&lt; DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_interval_joiner&lt; <classname>icl::interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_set"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_set&lt; DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_set&lt; <classname>icl::interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="type_to_string"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::interval_set&lt; DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::string</type></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/interval_traits.hpp">
<namespace name="boost">
<namespace name="icl">
<struct-specialization name="difference_type_of"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>interval_traits&lt; Type &gt;</template-arg></specialization><typedef name="domain_type"><type><classname>interval_traits</classname>&lt; Type &gt;::domain_type</type></typedef>
<typedef name="type"><type>difference_type_of&lt; domain_type &gt;::type</type></typedef>
</struct-specialization><struct-specialization name="domain_type_of"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>interval_traits&lt; Type &gt;</template-arg></specialization><typedef name="type"><type><classname>interval_traits</classname>&lt; Type &gt;::domain_type</type></typedef>
</struct-specialization><struct name="interval_traits"><template>
      <template-type-parameter name="Type"/>
    </template><typedef name="type"><type><classname>interval_traits</classname></type></typedef>
<typedef name="domain_type"><type>domain_type_of&lt; Type &gt;::<classname>type</classname></type></typedef>
<method-group name="public static functions">
<method name="construct" specifiers="static"><type>Type</type><parameter name="lo"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="up"><paramtype>const domain_type &amp;</paramtype></parameter></method>
<method name="upper" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const Type &amp;</paramtype></parameter></method>
<method name="lower" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const Type &amp;</paramtype></parameter></method>
</method-group>
</struct><struct-specialization name="size_type_of"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>interval_traits&lt; Type &gt;</template-arg></specialization><typedef name="domain_type"><type><classname>interval_traits</classname>&lt; Type &gt;::domain_type</type></typedef>
<typedef name="type"><type>size_type_of&lt; domain_type &gt;::type</type></typedef>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/iterator.hpp">
<namespace name="boost">
<namespace name="icl">
<class name="add_iterator"><template>
      <template-type-parameter name="ContainerT"/>
    </template><purpose>Performes an addition using a container's memberfunction add, when operator= is called. </purpose><typedef name="container_type"><purpose>The container's type. </purpose><type>ContainerT</type></typedef>
<typedef name="iterator_category"><type>std::output_iterator_tag</type></typedef>
<typedef name="value_type"><type>void</type></typedef>
<typedef name="difference_type"><type>void</type></typedef>
<typedef name="pointer"><type>void</type></typedef>
<typedef name="reference"><type>void</type></typedef>
<method-group name="public member functions">
<method name="operator *"><type><classname>add_iterator</classname> &amp;</type></method>
<method name="operator++"><type><classname>add_iterator</classname> &amp;</type></method>
<method name="operator++"><type><classname>add_iterator</classname> &amp;</type><parameter name=""><paramtype>int</paramtype></parameter></method>
</method-group>
<constructor><parameter name="cont"><paramtype>ContainerT &amp;</paramtype></parameter><parameter name="iter"><paramtype>typename ContainerT::iterator</paramtype></parameter><description><para>An <classname alt="boost::icl::add_iterator">add_iterator</classname> is constructed with a container and a position that has to be maintained. </para></description></constructor>
<copy-assignment><type><classname>add_iterator</classname> &amp;</type><parameter name="value"><paramtype>typename ContainerT::const_reference</paramtype></parameter><description><para>This assignment operator adds the <computeroutput>value</computeroutput> before the current position. It maintains it's position by incrementing after addition. </para></description></copy-assignment>
</class><class name="insert_iterator"><template>
      <template-type-parameter name="ContainerT"/>
    </template><purpose>Performes an insertion using a container's memberfunction add, when operator= is called. </purpose><typedef name="container_type"><purpose>The container's type. </purpose><type>ContainerT</type></typedef>
<typedef name="iterator_category"><type>std::output_iterator_tag</type></typedef>
<typedef name="value_type"><type>void</type></typedef>
<typedef name="difference_type"><type>void</type></typedef>
<typedef name="pointer"><type>void</type></typedef>
<typedef name="reference"><type>void</type></typedef>
<method-group name="public member functions">
<method name="operator *"><type><classname>insert_iterator</classname> &amp;</type></method>
<method name="operator++"><type><classname>insert_iterator</classname> &amp;</type></method>
<method name="operator++"><type><classname>insert_iterator</classname> &amp;</type><parameter name=""><paramtype>int</paramtype></parameter></method>
</method-group>
<constructor><parameter name="cont"><paramtype>ContainerT &amp;</paramtype></parameter><parameter name="iter"><paramtype>typename ContainerT::iterator</paramtype></parameter><description><para>An <classname alt="boost::icl::insert_iterator">insert_iterator</classname> is constructed with a container and a position that has to be maintained. </para></description></constructor>
<copy-assignment><type><classname>insert_iterator</classname> &amp;</type><parameter name="value"><paramtype>typename ContainerT::const_reference</paramtype></parameter><description><para>This assignment operator adds the <computeroutput>value</computeroutput> before the current position. It maintains it's position by incrementing after addition. </para></description></copy-assignment>
</class>



<function name="adder"><type><classname>add_iterator</classname>&lt; ContainerT &gt;</type><template>
          <template-type-parameter name="ContainerT"/>
          <template-type-parameter name="IteratorT"/>
        </template><parameter name="cont"><paramtype>ContainerT &amp;</paramtype></parameter><parameter name="iter_"><paramtype>IteratorT</paramtype></parameter><description><para>Function adder creates and initializes an <classname alt="boost::icl::add_iterator">add_iterator</classname> </para></description></function>
<function name="inserter"><type><classname>insert_iterator</classname>&lt; ContainerT &gt;</type><template>
          <template-type-parameter name="ContainerT"/>
          <template-type-parameter name="IteratorT"/>
        </template><parameter name="cont"><paramtype>ContainerT &amp;</paramtype></parameter><parameter name="iter_"><paramtype>IteratorT</paramtype></parameter><description><para>Function inserter creates and initializes an <classname alt="boost::icl::insert_iterator">insert_iterator</classname> </para></description></function>




</namespace>
</namespace>
</header>
<header name="boost/icl/left_open_interval.hpp">
<namespace name="boost">
<namespace name="icl">
<struct-specialization name="interval_bound_type"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>left_open_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="type"><type>interval_bound_type</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>interval_bounds::static_left_open</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="interval_traits"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::left_open_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="domain_type"><type>DomainT</type></typedef>
<typedef name="interval_type"><type><classname>icl::left_open_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
</method-group>
<method-group name="public static functions">
<method name="construct" specifiers="static"><type><classname>interval_type</classname></type><parameter name="lo"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="up"><paramtype>const domain_type &amp;</paramtype></parameter></method>
<method name="lower" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const <classname>interval_type</classname> &amp;</paramtype></parameter></method>
<method name="upper" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const <classname>interval_type</classname> &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><class name="left_open_interval"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
    </template><typedef name="type"><type><classname>left_open_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<typedef name="domain_type"><type>DomainT</type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
<method name="lower" cv="const"><type>DomainT</type></method>
<method name="upper" cv="const"><type>DomainT</type></method>
</method-group>
<constructor><description><para>Default constructor; yields an empty interval <computeroutput>(0,0]</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="val"><paramtype>const DomainT &amp;</paramtype></parameter><description><para>Constructor for a left-open singleton interval <computeroutput>(val-1,val]</computeroutput> </para></description></constructor>
<constructor><parameter name="low"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter><description><para>Interval from <computeroutput>low</computeroutput> to <computeroutput>up</computeroutput> with bounds <computeroutput>bounds</computeroutput> </para></description></constructor>
</class><struct-specialization name="type_to_string"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::left_open_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::string</type></method>
</method-group>
</struct-specialization><struct-specialization name="value_size"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::left_open_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::size_t</type><parameter name=""><paramtype>const <classname>icl::left_open_interval</classname>&lt; DomainT &gt; &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/map.hpp">
<namespace name="boost">
<namespace name="icl">
<struct-specialization name="absorbs_identities"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>absorbs_identities</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>int</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>Traits::absorbs_identities</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="has_inverse"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>has_inverse&lt; <classname>icl::map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>(has_inverse&lt; CodomainT &gt;::value)</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_map"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_map&lt; <classname>icl::map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_total"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_total</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>int</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>Traits::is_total</default></parameter></method>
</method-group>
</struct-specialization><class name="map"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"><default><classname alt="boost::icl::partial_absorber">icl::partial_absorber</classname></default></template-type-parameter>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type><default>ICL_COMBINE_INSTANCE(<classname alt="boost::icl::inplace_plus">icl::inplace_plus</classname>, CodomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type><default>ICL_SECTION_INSTANCE(<classname alt="boost::icl::inter_section">icl::inter_section</classname>, CodomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type><default>std::allocator</default></template-nontype-parameter>
    </template><inherit access="private">ICL_IMPL_SPACE::map&lt; DomainT, CodomainT, ICL_COMPARE_DOMAIN(Compare, DomainT), Alloc&lt; std::pair&lt; const DomainT, CodomainT &gt; &gt; &gt;</inherit><purpose>Addable, subractable and intersectable maps. </purpose><struct name="on_codomain_model"><template>
      <template-type-parameter name="Type"/>
      <template-nontype-parameter name="has_set_semantics"><type>bool</type></template-nontype-parameter>
      <template-nontype-parameter name="absorbs_identities"><type>bool</type></template-nontype-parameter>
    </template></struct><struct-specialization name="on_codomain_model"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>false</template-arg><template-arg>false</template-arg></specialization><method-group name="public static functions">
<method name="subtract" specifiers="static"><type>void</type><parameter name=""><paramtype>Type &amp;</paramtype></parameter><parameter name="it_"><paramtype>typename Type::iterator</paramtype></parameter><parameter name=""><paramtype>const typename Type::codomain_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="on_codomain_model"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>false</template-arg><template-arg>true</template-arg></specialization><method-group name="public static functions">
<method name="subtract" specifiers="static"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="it_"><paramtype>typename Type::iterator</paramtype></parameter><parameter name=""><paramtype>const typename Type::codomain_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="on_codomain_model"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>true</template-arg><template-arg>false</template-arg></specialization><typedef name="inverse_codomain_intersect"><type>Type::inverse_codomain_intersect</type></typedef>
<method-group name="public static functions">
<method name="subtract" specifiers="static"><type>void</type><parameter name=""><paramtype>Type &amp;</paramtype></parameter><parameter name="it_"><paramtype>typename Type::iterator</paramtype></parameter><parameter name="co_value"><paramtype>const typename Type::codomain_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="on_codomain_model"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>true</template-arg><template-arg>true</template-arg></specialization><typedef name="inverse_codomain_intersect"><type>Type::inverse_codomain_intersect</type></typedef>
<method-group name="public static functions">
<method name="subtract" specifiers="static"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="it_"><paramtype>typename Type::iterator</paramtype></parameter><parameter name="co_value"><paramtype>const typename Type::codomain_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct name="on_definedness"><template>
      <template-type-parameter name="Type"/>
      <template-nontype-parameter name="is_total"><type>bool</type></template-nontype-parameter>
    </template></struct><struct-specialization name="on_definedness"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>false</template-arg></specialization><method-group name="public static functions">
<method name="add_intersection" specifiers="static"><type>void</type><parameter name="section"><paramtype>Type &amp;</paramtype></parameter><parameter name="object"><paramtype>const Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const element_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="on_definedness"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>true</template-arg></specialization><method-group name="public static functions">
<method name="add_intersection" specifiers="static"><type>void</type><parameter name="section"><paramtype>Type &amp;</paramtype></parameter><parameter name="object"><paramtype>const Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const element_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct name="on_invertible"><template>
      <template-type-parameter name="Type"/>
      <template-nontype-parameter name="is_total_invertible"><type>bool</type></template-nontype-parameter>
    </template></struct><struct-specialization name="on_invertible"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>false</template-arg></specialization><typedef name="element_type"><type>Type::element_type</type></typedef>
<typedef name="inverse_codomain_combine"><type>Type::inverse_codomain_combine</type></typedef>
<method-group name="public static functions">
<method name="subtract" specifiers="static"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const element_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="on_invertible"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>true</template-arg></specialization><typedef name="element_type"><type>Type::element_type</type></typedef>
<typedef name="inverse_codomain_combine"><type>Type::inverse_codomain_combine</type></typedef>
<method-group name="public static functions">
<method name="subtract" specifiers="static"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const element_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct name="on_total_absorbable"><template>
      <template-type-parameter name="Type"/>
      <template-nontype-parameter name="is_total"><type>bool</type></template-nontype-parameter>
      <template-nontype-parameter name="absorbs_identities"><type>bool</type></template-nontype-parameter>
    </template></struct><struct-specialization name="on_total_absorbable"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>false</template-arg><template-arg>false</template-arg></specialization><typedef name="element_type"><type>Type::element_type</type></typedef>
<typedef name="codomain_type"><type>Type::codomain_type</type></typedef>
<typedef name="iterator"><type>Type::iterator</type></typedef>
<typedef name="inverse_codomain_intersect"><type>Type::inverse_codomain_intersect</type></typedef>
<method-group name="public static functions">
<method name="flip" specifiers="static"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const element_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="on_total_absorbable"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>false</template-arg><template-arg>true</template-arg></specialization><typedef name="element_type"><type>Type::element_type</type></typedef>
<typedef name="codomain_type"><type>Type::codomain_type</type></typedef>
<typedef name="iterator"><type>Type::iterator</type></typedef>
<typedef name="inverse_codomain_intersect"><type>Type::inverse_codomain_intersect</type></typedef>
<method-group name="public static functions">
<method name="flip" specifiers="static"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const element_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="on_total_absorbable"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>true</template-arg><template-arg>false</template-arg></specialization><typedef name="element_type"><type>Type::element_type</type></typedef>
<typedef name="codomain_type"><type>Type::codomain_type</type></typedef>
<method-group name="public static functions">
<method name="flip" specifiers="static"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name="operand"><paramtype>const element_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="on_total_absorbable"><template>
      <template-type-parameter name="Type"/>
    </template><specialization><template-arg>Type</template-arg><template-arg>true</template-arg><template-arg>true</template-arg></specialization><typedef name="element_type"><type>Type::element_type</type></typedef>
<method-group name="public static functions">
<method name="flip" specifiers="static"><type>void</type><parameter name="object"><paramtype>Type &amp;</paramtype></parameter><parameter name=""><paramtype>const typename Type::element_type &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><typedef name="allocator_type"><type>Alloc&lt; typename std::pair&lt; const DomainT, CodomainT &gt; &gt;</type></typedef>
<typedef name="type"><type><classname>icl::map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Alloc &gt;</type></typedef>
<typedef name="base_type"><type>ICL_IMPL_SPACE::map&lt; DomainT, CodomainT, ICL_COMPARE_DOMAIN(Compare, DomainT), allocator_type &gt;</type></typedef>
<typedef name="traits"><type>Traits</type></typedef>
<typedef name="domain_type"><type>DomainT</type></typedef>
<typedef name="domain_param"><type>boost::call_traits&lt; DomainT &gt;::param_type</type></typedef>
<typedef name="key_type"><type>DomainT</type></typedef>
<typedef name="codomain_type"><type>CodomainT</type></typedef>
<typedef name="mapped_type"><type>CodomainT</type></typedef>
<typedef name="data_type"><type>CodomainT</type></typedef>
<typedef name="element_type"><type>std::pair&lt; const DomainT, CodomainT &gt;</type></typedef>
<typedef name="value_type"><type>std::pair&lt; const DomainT, CodomainT &gt;</type></typedef>
<typedef name="key_compare"><type>domain_compare</type></typedef>
<typedef name="inverse_codomain_combine"><type><classname>inverse</classname>&lt; codomain_combine &gt;::<classname>type</classname></type></typedef>
<typedef name="codomain_intersect"><type>mpl::if_&lt; has_set_semantics&lt; codomain_type &gt;, ICL_SECTION_CODOMAIN(Section, CodomainT), codomain_combine &gt;::<classname>type</classname></type></typedef>
<typedef name="inverse_codomain_intersect"><type><classname>inverse</classname>&lt; codomain_intersect &gt;::<classname>type</classname></type></typedef>
<typedef name="value_compare"><type>base_type::value_compare</type></typedef>
<typedef name="set_type"><type>ICL_IMPL_SPACE::set&lt; DomainT, domain_compare, Alloc&lt; DomainT &gt; &gt;</type></typedef>
<typedef name="key_object_type"><type>set_type</type></typedef>
<typedef name="on_identity_absorbtion"><type>on_absorbtion&lt; <classname>type</classname>, codomain_combine, Traits::absorbs_identities &gt;</type></typedef>
<typedef name="pointer"><type>base_type::pointer</type></typedef>
<typedef name="const_pointer"><type>base_type::const_pointer</type></typedef>
<typedef name="reference"><type>base_type::reference</type></typedef>
<typedef name="const_reference"><type>base_type::const_reference</type></typedef>
<typedef name="iterator"><type>base_type::iterator</type></typedef>
<typedef name="const_iterator"><type>base_type::const_iterator</type></typedef>
<typedef name="size_type"><type>base_type::size_type</type></typedef>
<typedef name="difference_type"><type>base_type::difference_type</type></typedef>
<typedef name="reverse_iterator"><type>base_type::reverse_iterator</type></typedef>
<typedef name="const_reverse_iterator"><type>base_type::const_reverse_iterator</type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
<method name="ICL_COMBINE_CODOMAIN"><type>typedef</type><parameter name=""><paramtype>Combine</paramtype></parameter><parameter name=""><paramtype>CodomainT</paramtype></parameter></method>
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>element_type</paramtype></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>_total</paramtype><default>(Traits::is_total)</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>_absorbs</paramtype><default>(Traits::absorbs_identities)</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>total_invertible</paramtype><default>(mpl::and_&lt; is_total&lt; <classname alt="boost::icl::map">type</classname> &gt;, has_inverse&lt; codomain_type &gt; &gt;::value)</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>is_total_invertible</paramtype><default>(Traits::is_total &amp;&amp;has_inverse&lt; codomain_type &gt;::value)</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>int</paramtype></parameter><parameter name=""><paramtype>fineness</paramtype><default>4</default></parameter></method>
<method name="swap"><type>void</type><parameter name="src"><paramtype><classname>map</classname> &amp;</paramtype></parameter></method>
<method name="contains" cv="const"><type>bool</type><template>
          <template-type-parameter name="SubObject"/>
        </template><parameter name="sub"><paramtype>const SubObject &amp;</paramtype></parameter></method>
<method name="within" cv="const"><type>bool</type><parameter name="super"><paramtype>const <classname>map</classname> &amp;</paramtype></parameter></method>
<method name="iterative_size" cv="const"><type>std::size_t</type><description><para><computeroutput>iterative_size()</computeroutput> yields the number of elements that is visited throu complete iteration. For interval sets <computeroutput>iterative_size()</computeroutput> is different from <computeroutput>size()</computeroutput>. </para></description></method>
<method name="operator()" cv="const"><type>codomain_type</type><parameter name="key"><paramtype>const domain_type &amp;</paramtype></parameter><description><para>Total select function. </para></description></method>
<method name="add"><type><classname>map</classname> &amp;</type><parameter name="value_pair"><paramtype>const value_type &amp;</paramtype></parameter><description><para><computeroutput>add</computeroutput> inserts <computeroutput>value_pair</computeroutput> into the map if it's key does not exist in the map. If <computeroutput>value_pairs's</computeroutput> key value exists in the map, it's data value is added to the data value already found in the map. </para></description></method>
<method name="add"><type>iterator</type><parameter name="prior"><paramtype>iterator</paramtype></parameter><parameter name="value_pair"><paramtype>const value_type &amp;</paramtype></parameter><description><para><computeroutput>add</computeroutput> add <computeroutput>value_pair</computeroutput> into the map using <computeroutput>prior</computeroutput> as a hint to insert <computeroutput>value_pair</computeroutput> after the position <computeroutput>prior</computeroutput> is pointing to. </para></description></method>
<method name="subtract"><type><classname>map</classname> &amp;</type><parameter name="value_pair"><paramtype>const element_type &amp;</paramtype></parameter><description><para>If the <computeroutput>value_pair's</computeroutput> key value is in the map, it's data value is subtraced from the data value stored in the map. </para></description></method>
<method name="subtract"><type><classname>map</classname> &amp;</type><parameter name="key"><paramtype>const domain_type &amp;</paramtype></parameter></method>
<method name="insert"><type>std::pair&lt; iterator, bool &gt;</type><parameter name="value_pair"><paramtype>const value_type &amp;</paramtype></parameter></method>
<method name="insert"><type>iterator</type><parameter name="prior"><paramtype>iterator</paramtype></parameter><parameter name="value_pair"><paramtype>const value_type &amp;</paramtype></parameter></method>
<method name="insert"><type>iterator</type><template>
          <template-type-parameter name="Iterator"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype></parameter><parameter name="last"><paramtype>Iterator</paramtype></parameter></method>
<method name="set"><type><classname>map</classname> &amp;</type><parameter name="key_value_pair"><paramtype>const element_type &amp;</paramtype></parameter><description><para>With <computeroutput>key_value_pair = (k,v)</computeroutput> set value <computeroutput>v</computeroutput> for key <computeroutput>k</computeroutput> </para></description></method>
<method name="erase"><type>size_type</type><parameter name="key_value_pair"><paramtype>const element_type &amp;</paramtype></parameter><description><para>erase <computeroutput>key_value_pair</computeroutput> from the map. Erase only if, the exact value content <computeroutput>val</computeroutput> is stored for the given key. </para></description></method>
<method name="add_intersection" cv="const"><type>void</type><parameter name="section"><paramtype><classname>map</classname> &amp;</paramtype></parameter><parameter name="key_value_pair"><paramtype>const element_type &amp;</paramtype></parameter><description><para>The intersection of <computeroutput>key_value_pair</computeroutput> and <computeroutput>*this</computeroutput> map is added to <computeroutput>section</computeroutput>. </para></description></method>
<method name="flip"><type><classname>map</classname> &amp;</type><parameter name="operand"><paramtype>const element_type &amp;</paramtype></parameter></method>
<method name="_add"><type><classname>map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Alloc &gt; &amp;</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="addend"><paramtype>const element_type &amp;</paramtype></parameter></method>
<method name="_subtract"><type><classname>map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Alloc &gt; &amp;</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="minuend"><paramtype>const value_type &amp;</paramtype></parameter></method>
</method-group>
<constructor/>
<constructor><parameter name="comp"><paramtype>const key_compare &amp;</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="past"><paramtype>InputIterator</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="past"><paramtype>InputIterator</paramtype></parameter><parameter name="comp"><paramtype>const key_compare &amp;</paramtype></parameter></constructor>
<constructor><parameter name="src"><paramtype>const <classname>map</classname> &amp;</paramtype></parameter></constructor>
<constructor specifiers="explicit"><parameter name="key_value_pair"><paramtype>const element_type &amp;</paramtype></parameter></constructor>
<constructor><parameter name="src"><paramtype><classname>map</classname> &amp;&amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>map</classname> &amp;</type><parameter name="src"><paramtype><classname>map</classname></paramtype></parameter></copy-assignment>
<method-group name="private member functions">
<method name="_add"><type><classname>map</classname> &amp;</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="value_pair"><paramtype>const element_type &amp;</paramtype></parameter></method>
<method name="_add"><type>iterator</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="prior"><paramtype>iterator</paramtype></parameter><parameter name="value_pair"><paramtype>const element_type &amp;</paramtype></parameter></method>
<method name="_subtract"><type><classname>map</classname> &amp;</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="value_pair"><paramtype>const element_type &amp;</paramtype></parameter></method>
<method name="total_add_intersection" cv="const"><type>void</type><template>
          <template-type-parameter name="FragmentT"/>
        </template><parameter name="section"><paramtype><classname>type</classname> &amp;</paramtype></parameter><parameter name="fragment"><paramtype>const FragmentT &amp;</paramtype></parameter></method>
<method name="partial_add_intersection" cv="const"><type>void</type><parameter name="section"><paramtype><classname>type</classname> &amp;</paramtype></parameter><parameter name="operand"><paramtype>const element_type &amp;</paramtype></parameter></method>
</method-group>
</class><struct name="partial_absorber"><enum name="@3"><enumvalue name="absorbs_identities"><default>= true</default></enumvalue></enum>
<enum name="@4"><enumvalue name="is_total"><default>= false</default></enumvalue></enum>
</struct><struct name="partial_enricher"><enum name="@5"><enumvalue name="absorbs_identities"><default>= false</default></enumvalue></enum>
<enum name="@6"><enumvalue name="is_total"><default>= false</default></enumvalue></enum>
</struct><struct name="total_absorber"><enum name="@7"><enumvalue name="absorbs_identities"><default>= true</default></enumvalue></enum>
<enum name="@8"><enumvalue name="is_total"><default>= true</default></enumvalue></enum>
</struct><struct name="total_enricher"><enum name="@9"><enumvalue name="absorbs_identities"><default>= false</default></enumvalue></enum>
<enum name="@10"><enumvalue name="is_total"><default>= true</default></enumvalue></enum>
</struct><struct-specialization name="type_to_string"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Alloc &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::string</type></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/open_interval.hpp">
<namespace name="boost">
<namespace name="icl">
<struct-specialization name="interval_bound_type"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>open_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="type"><type>interval_bound_type</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>interval_bounds::static_open</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="interval_traits"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::open_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="domain_type"><type>DomainT</type></typedef>
<typedef name="interval_type"><type><classname>icl::open_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
</method-group>
<method-group name="public static functions">
<method name="construct" specifiers="static"><type><classname>interval_type</classname></type><parameter name="lo"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="up"><paramtype>const domain_type &amp;</paramtype></parameter></method>
<method name="lower" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const <classname>interval_type</classname> &amp;</paramtype></parameter></method>
<method name="upper" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const <classname>interval_type</classname> &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><class name="open_interval"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
    </template><typedef name="type"><type><classname>open_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<typedef name="domain_type"><type>DomainT</type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
<method name="lower" cv="const"><type>DomainT</type></method>
<method name="upper" cv="const"><type>DomainT</type></method>
</method-group>
<constructor><description><para>Default constructor; yields an empty interval <computeroutput>(0,0)</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="val"><paramtype>const DomainT &amp;</paramtype></parameter><description><para>Constructor for an open singleton interval <computeroutput>(val-1,val+1)</computeroutput> </para></description></constructor>
<constructor><parameter name="low"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter><description><para>Interval from <computeroutput>low</computeroutput> to <computeroutput>up</computeroutput> with bounds <computeroutput>bounds</computeroutput> </para></description></constructor>
</class><struct-specialization name="type_to_string"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::open_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::string</type></method>
</method-group>
</struct-specialization><struct-specialization name="value_size"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::open_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::size_t</type><parameter name=""><paramtype>const <classname>icl::open_interval</classname>&lt; DomainT &gt; &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/ptime.hpp">
<namespace name="boost">
<namespace name="icl">
<struct-specialization name="difference_type_of"><template>
    </template><specialization><template-arg>boost::posix_time::ptime</template-arg></specialization><typedef name="type"><type>boost::posix_time::time_duration</type></typedef>
</struct-specialization><struct-specialization name="has_difference"><template>
    </template><specialization><template-arg>boost::posix_time::ptime</template-arg></specialization><typedef name="type"><type>has_difference</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="has_difference"><template>
    </template><specialization><template-arg>boost::posix_time::time_duration</template-arg></specialization><typedef name="type"><type>has_difference</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_discrete"><template>
    </template><specialization><template-arg>boost::posix_time::ptime</template-arg></specialization><typedef name="type"><type>is_discrete</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_discrete"><template>
    </template><specialization><template-arg>boost::posix_time::time_duration</template-arg></specialization><typedef name="type"><type>is_discrete</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="size_type_of"><template>
    </template><specialization><template-arg>boost::posix_time::ptime</template-arg></specialization><typedef name="type"><type>boost::posix_time::time_duration</type></typedef>
</struct-specialization><struct-specialization name="size_type_of"><template>
    </template><specialization><template-arg>boost::posix_time::time_duration</template-arg></specialization><typedef name="type"><type>boost::posix_time::time_duration</type></typedef>
</struct-specialization><function name="operator++"><type>boost::posix_time::ptime</type><parameter name="x"><paramtype>boost::posix_time::ptime &amp;</paramtype></parameter></function>
<function name="operator--"><type>boost::posix_time::ptime</type><parameter name="x"><paramtype>boost::posix_time::ptime &amp;</paramtype></parameter></function>
<function name="operator++"><type>boost::posix_time::time_duration</type><parameter name="x"><paramtype>boost::posix_time::time_duration &amp;</paramtype></parameter></function>
<function name="operator--"><type>boost::posix_time::time_duration</type><parameter name="x"><paramtype>boost::posix_time::time_duration &amp;</paramtype></parameter></function>






</namespace>
</namespace>
</header>
<header name="boost/icl/rational.hpp">
<namespace name="boost">
<namespace name="icl">
<struct-specialization name="has_inverse"><template>
      <template-type-parameter name="Integral"/>
    </template><specialization><template-arg>boost::rational&lt; Integral &gt;</template-arg></specialization><typedef name="type"><type>has_inverse</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>(boost::is_signed&lt; Integral &gt;::value)</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_continuous"><template>
      <template-type-parameter name="Integral"/>
    </template><specialization><template-arg>boost::rational&lt; Integral &gt;</template-arg></specialization><typedef name="type"><type>is_continuous</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_discrete"><template>
      <template-type-parameter name="Integral"/>
    </template><specialization><template-arg>boost::rational&lt; Integral &gt;</template-arg></specialization><typedef name="type"><type>is_discrete</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>false</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_numeric"><template>
      <template-type-parameter name="Integral"/>
    </template><specialization><template-arg>boost::rational&lt; Integral &gt;</template-arg></specialization><typedef name="type"><type>is_numeric</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/right_open_interval.hpp">
<namespace name="boost">
<namespace name="icl">
<struct-specialization name="interval_bound_type"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>right_open_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="type"><type>interval_bound_type</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bound_type</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>interval_bounds::static_right_open</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="interval_traits"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::right_open_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><typedef name="domain_type"><type>DomainT</type></typedef>
<typedef name="interval_type"><type><classname>icl::right_open_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
</method-group>
<method-group name="public static functions">
<method name="construct" specifiers="static"><type><classname>interval_type</classname></type><parameter name="lo"><paramtype>const domain_type &amp;</paramtype></parameter><parameter name="up"><paramtype>const domain_type &amp;</paramtype></parameter></method>
<method name="lower" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const <classname>interval_type</classname> &amp;</paramtype></parameter></method>
<method name="upper" specifiers="static"><type>domain_type</type><parameter name="inter_val"><paramtype>const <classname>interval_type</classname> &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization><class name="right_open_interval"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
    </template><typedef name="type"><type><classname>right_open_interval</classname>&lt; DomainT, Compare &gt;</type></typedef>
<typedef name="domain_type"><type>DomainT</type></typedef>
<method-group name="public member functions">
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter></method>
<method name="lower" cv="const"><type>domain_type</type></method>
<method name="upper" cv="const"><type>domain_type</type></method>
</method-group>
<constructor><description><para>Default constructor; yields an empty interval <computeroutput>[0,0)</computeroutput>. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="val"><paramtype>const DomainT &amp;</paramtype></parameter><description><para>Constructor for a singleton interval <computeroutput>[val,val+1)</computeroutput> </para></description></constructor>
<constructor><parameter name="low"><paramtype>const DomainT &amp;</paramtype></parameter><parameter name="up"><paramtype>const DomainT &amp;</paramtype></parameter><description><para>Interval from <computeroutput>low</computeroutput> to <computeroutput>up</computeroutput> with bounds <computeroutput>bounds</computeroutput> </para></description></constructor>
</class><struct-specialization name="type_to_string"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::right_open_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::string</type></method>
</method-group>
</struct-specialization><struct-specialization name="value_size"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::right_open_interval&lt; DomainT</template-arg><template-arg>Compare &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::size_t</type><parameter name=""><paramtype>const <classname>icl::right_open_interval</classname>&lt; DomainT &gt; &amp;</paramtype></parameter></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/separate_interval_set.hpp">
<namespace name="boost">
<namespace name="icl">
<struct-specialization name="is_interval_container"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::separate_interval_set&lt; DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_interval_container&lt; <classname>icl::separate_interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_interval_separator"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::separate_interval_set&lt; DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_interval_separator&lt; <classname>icl::separate_interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_set"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::separate_interval_set&lt; DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_set&lt; <classname>icl::separate_interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><class name="separate_interval_set"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type><default>ICL_INTERVAL_INSTANCE(ICL_INTERVAL_DEFAULT, DomainT, Compare)</default></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type><default>std::allocator</default></template-nontype-parameter>
    </template><inherit access="public">boost::icl::interval_base_set&lt; separate_interval_set&lt; DomainT, Compare, Interval, Alloc &gt;, DomainT, Compare, Interval, Alloc &gt;</inherit><purpose>Implements a set as a set of intervals - leaving adjoining intervals separate. </purpose><enum name="@2"><enumvalue name="fineness"><default>= 2</default></enumvalue></enum>
<typedef name="type"><type><classname>separate_interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt;</type></typedef>
<typedef name="base_type"><type><classname>interval_base_set</classname>&lt; <classname>type</classname>, DomainT, Compare, Interval, Alloc &gt;</type></typedef>
<typedef name="overloadable_type"><type><classname>type</classname></type></typedef>
<typedef name="key_object_type"><type><classname>type</classname></type></typedef>
<typedef name="joint_type"><type><classname>interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt;</type></typedef>
<typedef name="domain_type"><purpose>The domain type of the set. </purpose><type>DomainT</type></typedef>
<typedef name="codomain_type"><purpose>The codomaintype is the same as domain_type. </purpose><type>DomainT</type></typedef>
<typedef name="element_type"><purpose>The element type of the set. </purpose><type>DomainT</type></typedef>
<typedef name="segment_type"><purpose>The segment type of the set. </purpose><type>interval_type</type></typedef>
<typedef name="interval_compare"><purpose>Comparison functor for intervals. </purpose><type>exclusive_less_than&lt; interval_type &gt;</type></typedef>
<typedef name="key_compare"><purpose>Comparison functor for keys. </purpose><type>exclusive_less_than&lt; interval_type &gt;</type></typedef>
<typedef name="allocator_type"><purpose>The allocator type of the set. </purpose><type>Alloc&lt; interval_type &gt;</type></typedef>
<typedef name="domain_allocator_type"><purpose>allocator type of the corresponding element set </purpose><type>Alloc&lt; DomainT &gt;</type></typedef>
<typedef name="atomized_type"><purpose>The corresponding atomized type representing this interval container of elements. </purpose><type>base_type::atomized_type</type></typedef>
<typedef name="ImplSetT"><purpose>Container type for the implementation. </purpose><type>base_type::ImplSetT</type></typedef>
<typedef name="key_type"><purpose>key type of the implementing container </purpose><type>ImplSetT::key_type</type></typedef>
<typedef name="data_type"><purpose>data type of the implementing container </purpose><type>ImplSetT::value_type</type></typedef>
<typedef name="value_type"><purpose>value type of the implementing container </purpose><type>ImplSetT::value_type</type></typedef>
<typedef name="iterator"><purpose>iterator for iteration over intervals </purpose><type>ImplSetT::iterator</type></typedef>
<typedef name="const_iterator"><purpose>const_iterator for iteration over intervals </purpose><type>ImplSetT::const_iterator</type></typedef>
<method-group name="public member functions">
<method name="ICL_INTERVAL_TYPE"><type>typedef</type><parameter name=""><paramtype>Interval</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter><parameter name=""><paramtype>Compare</paramtype></parameter><purpose>The interval type of the set. </purpose></method>
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter><purpose>Comparison functor for domain values. </purpose></method>
<method name="assign"><type>void</type><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_set</classname>&lt; SubType, DomainT, Compare, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Assignment from a base <classname alt="boost::icl::interval_set">interval_set</classname>. </purpose></method>
</method-group>
<constructor><purpose>Default constructor for the empty object. </purpose></constructor>
<constructor><parameter name="src"><paramtype>const <classname>separate_interval_set</classname> &amp;</paramtype></parameter><purpose>Copy constructor. </purpose></constructor>
<constructor><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_set</classname>&lt; SubType, DomainT, Compare, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Copy constructor for base_type. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="elem"><paramtype>const domain_type &amp;</paramtype></parameter><purpose>Constructor for a single element. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="itv"><paramtype>const interval_type &amp;</paramtype></parameter><purpose>Constructor for a single interval. </purpose></constructor>
<copy-assignment><type><classname>separate_interval_set</classname> &amp;</type><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_set</classname>&lt; SubType, DomainT, Compare, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Assignment operator for base type. </purpose></copy-assignment>
<constructor><parameter name="src"><paramtype><classname>separate_interval_set</classname> &amp;&amp;</paramtype></parameter><purpose>Move constructor. </purpose></constructor>
<copy-assignment><type><classname>separate_interval_set</classname> &amp;</type><parameter name="src"><paramtype><classname>separate_interval_set</classname></paramtype></parameter><purpose>Move assignment operator. </purpose></copy-assignment>
<method-group name="private member functions">
<method name="handle_inserted"><type>iterator</type><parameter name="inserted_"><paramtype>iterator</paramtype></parameter></method>
<method name="add_over"><type>iterator</type><parameter name="addend"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="last_"><paramtype>iterator</paramtype></parameter></method>
<method name="add_over"><type>iterator</type><parameter name="addend"><paramtype>const interval_type &amp;</paramtype></parameter></method>
</method-group>
</class><struct-specialization name="type_to_string"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::separate_interval_set&lt; DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::string</type></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/set.hpp">
</header>
<header name="boost/icl/split_interval_map.hpp">
<namespace name="boost">
<namespace name="icl">
<struct-specialization name="absorbs_identities"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::split_interval_map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>absorbs_identities&lt; <classname>icl::split_interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>(Traits::absorbs_identities)</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="has_inverse"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::split_interval_map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>has_inverse&lt; <classname>icl::split_interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>(has_inverse&lt; CodomainT &gt;::value)</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_interval_container"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::split_interval_map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_interval_container&lt; <classname>icl::split_interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_interval_splitter"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::split_interval_map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_interval_splitter&lt; <classname>icl::split_interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_map"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::split_interval_map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_map&lt; <classname>icl::split_interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_total"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::split_interval_map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_total&lt; <classname>icl::split_interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>(Traits::is_total)</default></parameter></method>
</method-group>
</struct-specialization><class name="split_interval_map"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"><default><classname alt="boost::icl::partial_absorber">icl::partial_absorber</classname></default></template-type-parameter>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type><default>ICL_COMBINE_INSTANCE(<classname alt="boost::icl::inplace_plus">icl::inplace_plus</classname>, CodomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type><default>ICL_SECTION_INSTANCE(<classname alt="boost::icl::inter_section">icl::inter_section</classname>, CodomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type><default>ICL_INTERVAL_INSTANCE(ICL_INTERVAL_DEFAULT, DomainT, Compare)</default></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type><default>std::allocator</default></template-nontype-parameter>
    </template><inherit access="public">boost::icl::interval_base_map&lt; split_interval_map&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt;, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt;</inherit><purpose>implements a map as a map of intervals - on insertion overlapping intervals are split and associated values are combined. </purpose><enum name="@1"><enumvalue name="fineness"><default>= 3</default></enumvalue></enum>
<typedef name="traits"><type>Traits</type></typedef>
<typedef name="type"><type><classname>split_interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt;</type></typedef>
<typedef name="joint_type"><type><classname>interval_map</classname>&lt; DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt;</type></typedef>
<typedef name="overloadable_type"><type><classname>type</classname></type></typedef>
<typedef name="base_type"><type><classname>interval_base_map</classname>&lt; <classname>type</classname>, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt;</type></typedef>
<typedef name="domain_type"><type>DomainT</type></typedef>
<typedef name="codomain_type"><type>CodomainT</type></typedef>
<typedef name="iterator"><type>base_type::iterator</type></typedef>
<typedef name="value_type"><type>base_type::value_type</type></typedef>
<typedef name="element_type"><type>base_type::element_type</type></typedef>
<typedef name="segment_type"><type>base_type::segment_type</type></typedef>
<typedef name="domain_mapping_type"><type>base_type::domain_mapping_type</type></typedef>
<typedef name="interval_mapping_type"><type>base_type::interval_mapping_type</type></typedef>
<typedef name="ImplMapT"><type>base_type::ImplMapT</type></typedef>
<typedef name="codomain_combine"><type>base_type::codomain_combine</type></typedef>
<typedef name="interval_set_type"><type><classname>interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt;</type></typedef>
<typedef name="set_type"><type><classname>interval_set_type</classname></type></typedef>
<typedef name="key_object_type"><type><classname>set_type</classname></type></typedef>
<method-group name="public member functions">
<method name="ICL_INTERVAL_TYPE"><type>typedef</type><parameter name=""><paramtype>Interval</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter><parameter name=""><paramtype>Compare</paramtype></parameter></method>
<method name="assign"><type>void</type><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_map</classname>&lt; SubType, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Assignment from a base <classname alt="boost::icl::interval_map">interval_map</classname>. </purpose></method>
</method-group>
<constructor><purpose>Default constructor for the empty object. </purpose></constructor>
<constructor><parameter name="src"><paramtype>const <classname>split_interval_map</classname> &amp;</paramtype></parameter><purpose>Copy constructor. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="base_pair"><paramtype>const <classname>domain_mapping_type</classname> &amp;</paramtype></parameter></constructor>
<constructor specifiers="explicit"><parameter name="value_pair"><paramtype>const value_type &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>split_interval_map</classname> &amp;</type><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_map</classname>&lt; SubType, DomainT, CodomainT, Traits, Compare, Combine, Section, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Assignment operator for base type. </purpose></copy-assignment>
<constructor><parameter name="src"><paramtype><classname>split_interval_map</classname> &amp;&amp;</paramtype></parameter><purpose>Move constructor. </purpose></constructor>
<copy-assignment><type><classname>split_interval_map</classname> &amp;</type><parameter name="src"><paramtype><classname>split_interval_map</classname></paramtype></parameter><purpose>Move assignment operator. </purpose></copy-assignment>
<method-group name="private member functions">
<method name="handle_inserted" cv="const"><type>iterator</type><parameter name="it_"><paramtype>iterator</paramtype></parameter></method>
<method name="handle_inserted" cv="const"><type>void</type><parameter name=""><paramtype>iterator</paramtype></parameter><parameter name=""><paramtype>iterator</paramtype></parameter></method>
<method name="handle_left_combined"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="it_"><paramtype>iterator</paramtype></parameter></method>
<method name="handle_combined"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="it_"><paramtype>iterator</paramtype></parameter></method>
<method name="handle_preceeded_combined"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="prior_"><paramtype>iterator</paramtype></parameter><parameter name="it_"><paramtype>iterator &amp;</paramtype></parameter></method>
<method name="handle_succeeded_combined"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="it_"><paramtype>iterator</paramtype></parameter><parameter name=""><paramtype>iterator</paramtype></parameter></method>
<method name="handle_reinserted"><type>void</type><parameter name=""><paramtype>iterator</paramtype></parameter></method>
<method name="gap_insert_at"><type>void</type><template>
          <template-type-parameter name="Combiner"/>
        </template><parameter name="it_"><paramtype>iterator &amp;</paramtype></parameter><parameter name="prior_"><paramtype>iterator</paramtype></parameter><parameter name="end_gap"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="co_val"><paramtype>const codomain_type &amp;</paramtype></parameter></method>
</method-group>
</class><struct-specialization name="type_to_string"><template>
      <template-type-parameter name="DomainT"/>
      <template-type-parameter name="CodomainT"/>
      <template-type-parameter name="Traits"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Combine"><type>ICL_COMBINE</type></template-nontype-parameter>
      <template-nontype-parameter name="Section"><type>ICL_SECTION</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::split_interval_map&lt; DomainT</template-arg><template-arg>CodomainT</template-arg><template-arg>Traits</template-arg><template-arg>Compare</template-arg><template-arg>Combine</template-arg><template-arg>Section</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::string</type></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
<header name="boost/icl/split_interval_set.hpp">
<namespace name="boost">
<namespace name="icl">
<struct-specialization name="is_interval_container"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::split_interval_set&lt; DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_interval_container&lt; <classname>icl::split_interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_interval_splitter"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::split_interval_set&lt; DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_interval_splitter&lt; <classname>icl::split_interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><struct-specialization name="is_set"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::split_interval_set&lt; DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><typedef name="type"><type>is_set&lt; <classname>icl::split_interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>bool</paramtype></parameter><parameter name=""><paramtype>value</paramtype><default>true</default></parameter></method>
</method-group>
</struct-specialization><class name="split_interval_set"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type><default>ICL_COMPARE_INSTANCE(ICL_COMPARE_DEFAULT, DomainT)</default></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type><default>ICL_INTERVAL_INSTANCE(ICL_INTERVAL_DEFAULT, DomainT, Compare)</default></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type><default>std::allocator</default></template-nontype-parameter>
    </template><inherit access="public">boost::icl::interval_base_set&lt; split_interval_set&lt; DomainT, Compare, Interval, Alloc &gt;, DomainT, Compare, Interval, Alloc &gt;</inherit><purpose>implements a set as a set of intervals - on insertion overlapping intervals are split </purpose><enum name="@0"><enumvalue name="fineness"><default>= 3</default></enumvalue></enum>
<typedef name="type"><type><classname>split_interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt;</type></typedef>
<typedef name="base_type"><type><classname>interval_base_set</classname>&lt; <classname>type</classname>, DomainT, Compare, Interval, Alloc &gt;</type></typedef>
<typedef name="joint_type"><type><classname>interval_set</classname>&lt; DomainT, Compare, Interval, Alloc &gt;</type></typedef>
<typedef name="overloadable_type"><type><classname>type</classname></type></typedef>
<typedef name="key_object_type"><type><classname>type</classname></type></typedef>
<typedef name="domain_type"><purpose>The domain type of the set. </purpose><type>DomainT</type></typedef>
<typedef name="codomain_type"><purpose>The codomaintype is the same as domain_type. </purpose><type>DomainT</type></typedef>
<typedef name="element_type"><purpose>The element type of the set. </purpose><type>DomainT</type></typedef>
<typedef name="segment_type"><purpose>The segment type of the set. </purpose><type>interval_type</type></typedef>
<typedef name="interval_compare"><purpose>Comparison functor for intervals. </purpose><type>exclusive_less_than&lt; interval_type &gt;</type></typedef>
<typedef name="key_compare"><purpose>Comparison functor for keys. </purpose><type>exclusive_less_than&lt; interval_type &gt;</type></typedef>
<typedef name="allocator_type"><purpose>The allocator type of the set. </purpose><type>Alloc&lt; interval_type &gt;</type></typedef>
<typedef name="domain_allocator_type"><purpose>allocator type of the corresponding element set </purpose><type>Alloc&lt; DomainT &gt;</type></typedef>
<typedef name="atomized_type"><purpose>The corresponding atomized type representing this interval container of elements. </purpose><type>base_type::atomized_type</type></typedef>
<typedef name="ImplSetT"><purpose>Container type for the implementation. </purpose><type>base_type::ImplSetT</type></typedef>
<typedef name="key_type"><purpose>key type of the implementing container </purpose><type>ImplSetT::key_type</type></typedef>
<typedef name="data_type"><purpose>data type of the implementing container </purpose><type>ImplSetT::value_type</type></typedef>
<typedef name="value_type"><purpose>value type of the implementing container </purpose><type>ImplSetT::value_type</type></typedef>
<typedef name="iterator"><purpose>iterator for iteration over intervals </purpose><type>ImplSetT::iterator</type></typedef>
<typedef name="const_iterator"><purpose>const_iterator for iteration over intervals </purpose><type>ImplSetT::const_iterator</type></typedef>
<method-group name="public member functions">
<method name="ICL_INTERVAL_TYPE"><type>typedef</type><parameter name=""><paramtype>Interval</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter><parameter name=""><paramtype>Compare</paramtype></parameter><purpose>The interval type of the set. </purpose></method>
<method name="ICL_COMPARE_DOMAIN"><type>typedef</type><parameter name=""><paramtype>Compare</paramtype></parameter><parameter name=""><paramtype>DomainT</paramtype></parameter><purpose>Comparison functor for domain values. </purpose></method>
<method name="assign"><type>void</type><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_set</classname>&lt; SubType, DomainT, Compare, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Assignment from a base <classname alt="boost::icl::interval_set">interval_set</classname>. </purpose></method>
</method-group>
<constructor><purpose>Default constructor for the empty object. </purpose></constructor>
<constructor><parameter name="src"><paramtype>const <classname>split_interval_set</classname> &amp;</paramtype></parameter><purpose>Copy constructor. </purpose></constructor>
<constructor><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_set</classname>&lt; SubType, DomainT, Compare, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Copy constructor for base_type. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="elem"><paramtype>const interval_type &amp;</paramtype></parameter><purpose>Constructor for a single element. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="itv"><paramtype>const domain_type &amp;</paramtype></parameter><purpose>Constructor for a single interval. </purpose></constructor>
<copy-assignment><type><classname>split_interval_set</classname> &amp;</type><template>
          <template-type-parameter name="SubType"/>
        </template><parameter name="src"><paramtype>const <classname>interval_base_set</classname>&lt; SubType, DomainT, Compare, Interval, Alloc &gt; &amp;</paramtype></parameter><purpose>Assignment operator for base type. </purpose></copy-assignment>
<constructor><parameter name="src"><paramtype><classname>split_interval_set</classname> &amp;&amp;</paramtype></parameter><purpose>Move constructor. </purpose></constructor>
<copy-assignment><type><classname>split_interval_set</classname> &amp;</type><parameter name="src"><paramtype><classname>split_interval_set</classname></paramtype></parameter><purpose>Move assignment operator. </purpose></copy-assignment>
<method-group name="private member functions">
<method name="handle_inserted"><type>iterator</type><parameter name="inserted_"><paramtype>iterator</paramtype></parameter></method>
<method name="add_over"><type>iterator</type><parameter name="addend"><paramtype>const interval_type &amp;</paramtype></parameter><parameter name="last_"><paramtype>iterator</paramtype></parameter></method>
<method name="add_over"><type>iterator</type><parameter name="addend"><paramtype>const interval_type &amp;</paramtype></parameter></method>
</method-group>
</class><struct-specialization name="type_to_string"><template>
      <template-type-parameter name="DomainT"/>
      <template-nontype-parameter name="Compare"><type>ICL_COMPARE</type></template-nontype-parameter>
      <template-nontype-parameter name="Interval"><type>ICL_INTERVAL(ICL_COMPARE)</type></template-nontype-parameter>
      <template-nontype-parameter name="Alloc"><type>ICL_ALLOC</type></template-nontype-parameter>
    </template><specialization><template-arg>icl::split_interval_set&lt; DomainT</template-arg><template-arg>Compare</template-arg><template-arg>Interval</template-arg><template-arg>Alloc &gt;</template-arg></specialization><method-group name="public static functions">
<method name="apply" specifiers="static"><type>std::string</type></method>
</method-group>
</struct-specialization>









</namespace>
</namespace>
</header>
</library-reference>