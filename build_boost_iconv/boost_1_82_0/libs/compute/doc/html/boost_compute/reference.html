<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Reference</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Chapter 1. Boost.Compute">
<link rel="up" href="../index.html" title="Chapter 1. Boost.Compute">
<link rel="prev" href="platforms_and_compilers.html" title="Platforms and Compilers">
<link rel="next" href="../boost/compute/accumulate.html" title="Function accumulate">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="platforms_and_compilers.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost/compute/accumulate.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_compute.reference"></a><a class="link" href="reference.html" title="Reference">Reference</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#boost_compute.reference.api_overview">API Overview</a></span></dt>
<dt><span class="section"><a href="reference.html#header_reference">Header Reference</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_compute.reference.api_overview"></a><a class="link" href="reference.html#boost_compute.reference.api_overview" title="API Overview">API Overview</a>
</h3></div></div></div>
<h4>
<a name="boost_compute.reference.api_overview.h0"></a>
        <span class="phrase"><a name="boost_compute.reference.api_overview.core_library"></a></span><a class="link" href="reference.html#boost_compute.reference.api_overview.core_library">Core
        Library</a>
      </h4>
<p>
        Header: <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">compute</span><span class="special">/</span><span class="identifier">core</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput">buffer</code>
          </li>
<li class="listitem">
            <code class="computeroutput">command_queue</code>
          </li>
<li class="listitem">
            <code class="computeroutput">context</code>
          </li>
<li class="listitem">
            <code class="computeroutput">device</code>
          </li>
<li class="listitem">
            <code class="computeroutput">event</code>
          </li>
<li class="listitem">
            <code class="computeroutput">kernel</code>
          </li>
<li class="listitem">
            <code class="computeroutput">memory_object</code>
          </li>
<li class="listitem">
            <code class="computeroutput">pipe</code>
          </li>
<li class="listitem">
            <code class="computeroutput">platform</code>
          </li>
<li class="listitem">
            <code class="computeroutput">program</code>
          </li>
<li class="listitem">
            <code class="computeroutput">system</code>
          </li>
<li class="listitem">
            <code class="computeroutput">user_event</code>
          </li>
</ul></div>
<h4>
<a name="boost_compute.reference.api_overview.h1"></a>
        <span class="phrase"><a name="boost_compute.reference.api_overview.utilities"></a></span><a class="link" href="reference.html#boost_compute.reference.api_overview.utilities">Utilities</a>
      </h4>
<p>
        Header: <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">compute</span><span class="special">/</span><span class="identifier">utility</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/dim.html" title="Function template dim">dim()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/extents.html" title="Class template extents">extents&lt;N&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/program_cache.html" title="Class program_cache">program_cache</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a></code>
          </li>
</ul></div>
<h4>
<a name="boost_compute.reference.api_overview.h2"></a>
        <span class="phrase"><a name="boost_compute.reference.api_overview.algorithms"></a></span><a class="link" href="reference.html#boost_compute.reference.api_overview.algorithms">Algorithms</a>
      </h4>
<p>
        Header: <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">compute</span><span class="special">/</span><span class="identifier">algorithm</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/accumulate.html" title="Function accumulate">accumulate()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/adjacent_difference.html" title="Function adjacent_difference">adjacent_difference()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/adjacent_find.html" title="Function adjacent_find">adjacent_find()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/all_of.html" title="Function template all_of">all_of()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/any_of.html" title="Function template any_of">any_of()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/binary_search.html" title="Function template binary_search">binary_search()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/copy.html" title="Function template copy">copy()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/copy_if.html" title="Function template copy_if">copy_if()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/copy_n.html" title="Function template copy_n">copy_n()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/count.html" title="Function template count">count()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/count_if.html" title="Function template count_if">count_if()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/equal.html" title="Function equal">equal()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/equal_range.html" title="Function template equal_range">equal_range()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/exclusive_scan.html" title="Function exclusive_scan">exclusive_scan()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/fill.html" title="Function template fill">fill()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/fill_n.html" title="Function template fill_n">fill_n()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/find.html" title="Function template find">find()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/find_end.html" title="Function template find_end">find_end()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/find_if.html" title="Function template find_if">find_if()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/find_if_not.html" title="Function template find_if_not">find_if_not()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/for_each.html" title="Function template for_each">for_each()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/for_each_n.html" title="Function template for_each_n">for_each_n()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/gather.html" title="Function template gather">gather()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/generate.html" title="Function template generate">generate()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/generate_n.html" title="Function template generate_n">generate_n()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/includes.html" title="Function template includes">includes()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/inclusive_scan.html" title="Function inclusive_scan">inclusive_scan()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/inner_product.html" title="Function inner_product">inner_product()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/inplace_merge.html" title="Function template inplace_merge">inplace_merge()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/iota.html" title="Function template iota">iota()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/is_partitioned.html" title="Function template is_partitioned">is_partitioned()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/is_permutation.html" title="Function template is_permutation">is_permutation()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/is_sorted.html" title="Function is_sorted">is_sorted()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/lower_bound.html" title="Function template lower_bound">lower_bound()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/lexicographical_compare.html" title="Function template lexicographical_compare">lexicographical_compare()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/max_element.html" title="Function max_element">max_element()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/merge.html" title="Function merge">merge()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/min_element.html" title="Function min_element">min_element()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/minmax_element.html" title="Function minmax_element">minmax_element()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/mismatch.html" title="Function mismatch">mismatch()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/next_permutation.html" title="Function template next_permutation">next_permutation()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/none_of.html" title="Function template none_of">none_of()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/nth_element.html" title="Function nth_element">nth_element()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/partial_sum.html" title="Function template partial_sum">partial_sum()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/partition.html" title="Function template partition">partition()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/partition_copy.html" title="Function template partition_copy">partition_copy()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/partition_point.html" title="Function template partition_point">partition_point()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/prev_permutation.html" title="Function template prev_permutation">prev_permutation()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/random_shuffle.html" title="Function template random_shuffle">random_shuffle()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/reduce.html" title="Function reduce">reduce()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/reduce_by_key.html" title="Function reduce_by_key">reduce_by_key()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/remove.html" title="Function template remove">remove()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/remove_if.html" title="Function template remove_if">remove_if()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/replace.html" title="Function template replace">replace()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/replace_copy.html" title="Function template replace_copy">replace_copy()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/reverse.html" title="Function template reverse">reverse()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/reverse_copy.html" title="Function template reverse_copy">reverse_copy()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/rotate.html" title="Function template rotate">rotate()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/rotate_copy.html" title="Function template rotate_copy">rotate_copy()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/scatter.html" title="Function template scatter">scatter()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/search.html" title="Function template search">search()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/search_n.html" title="Function template search_n">search_n()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/set_difference.html" title="Function template set_difference">set_difference()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/set_intersection.html" title="Function template set_intersection">set_intersection()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/set_symmetric_difference.html" title="Function template set_symmetric_difference">set_symmetric_difference()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/set_union.html" title="Function template set_union">set_union()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/sort.html" title="Function sort">sort()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/sort_by_key.html" title="Function sort_by_key">sort_by_key()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/stable_partition.html" title="Function template stable_partition">stable_partition()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/stable_sort.html" title="Function stable_sort">stable_sort()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/stable_sort_by_key.html" title="Function stable_sort_by_key">stable_sort_by_key()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/swap_ranges.html" title="Function template swap_ranges">swap_ranges()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/transform.html" title="Function transform">transform()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/transform_reduce.html" title="Function transform_reduce">transform_reduce()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/unique.html" title="Function unique">unique()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/unique_copy.html" title="Function unique_copy">unique_copy()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/upper_bound.html" title="Function template upper_bound">upper_bound()</a></code>
          </li>
</ul></div>
<h4>
<a name="boost_compute.reference.api_overview.h3"></a>
        <span class="phrase"><a name="boost_compute.reference.api_overview.async"></a></span><a class="link" href="reference.html#boost_compute.reference.api_overview.async">Async</a>
      </h4>
<p>
        Header: <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">compute</span><span class="special">/</span><span class="identifier">async</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/future.html" title="Class template future">future&lt;T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/wait_for_all.html" title="Function template wait_for_all">wait_for_all()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/wait_guard.html" title="Class template wait_guard">wait_guard&lt;Waitable&gt;</a></code>
          </li>
</ul></div>
<h4>
<a name="boost_compute.reference.api_overview.h4"></a>
        <span class="phrase"><a name="boost_compute.reference.api_overview.containers"></a></span><a class="link" href="reference.html#boost_compute.reference.api_overview.containers">Containers</a>
      </h4>
<p>
        Header: <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">compute</span><span class="special">/</span><span class="identifier">container</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/array.html" title="Class template array">array&lt;T, N&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/basic_string.html" title="Class template basic_string">basic_string&lt;CharT&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/dynamic_bitset.html" title="Class template dynamic_bitset">dynamic_bitset&lt;&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/flat_map.html" title="Class template flat_map">flat_map&lt;Key, T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/flat_set.html" title="Class template flat_set">flat_set&lt;T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/mapped_view.html" title="Class template mapped_view">mapped_view&lt;T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/stack.html" title="Class template stack">stack&lt;T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="reference.html#boost.compute.string">string</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/valarray.html" title="Class template valarray">valarray&lt;T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/vector.html" title="Class template vector">vector&lt;T&gt;</a></code>
          </li>
</ul></div>
<h4>
<a name="boost_compute.reference.api_overview.h5"></a>
        <span class="phrase"><a name="boost_compute.reference.api_overview.exceptions"></a></span><a class="link" href="reference.html#boost_compute.reference.api_overview.exceptions">Exceptions</a>
      </h4>
<p>
        Header: <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">compute</span><span class="special">/</span><span class="identifier">exception</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/context_error.html" title="Class context_error">context_error</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/no_device_found.html" title="Class no_device_found">no_device_found</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/opencl_error.html" title="Class opencl_error">opencl_error</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/unsupported_extens_idm8279.html" title="Class unsupported_extension_error">unsupported_extension_error</a></code>
          </li>
</ul></div>
<h4>
<a name="boost_compute.reference.api_overview.h6"></a>
        <span class="phrase"><a name="boost_compute.reference.api_overview.iterators"></a></span><a class="link" href="reference.html#boost_compute.reference.api_overview.iterators">Iterators</a>
      </h4>
<p>
        Header: <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">compute</span><span class="special">/</span><span class="identifier">iterators</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/buffer_iterator.html" title="Class template buffer_iterator">buffer_iterator&lt;T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/constant_buffer_iterator.html" title="Class template constant_buffer_iterator">constant_buffer_iterator&lt;T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/constant_iterator.html" title="Class template constant_iterator">constant_iterator&lt;T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/counting_iterator.html" title="Class template counting_iterator">counting_iterator&lt;T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/discard_iterator.html" title="Class discard_iterator">discard_iterator</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/function_input_iterator.html" title="Class template function_input_iterator">function_input_iterator&lt;Function&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/permutation_iterator.html" title="Class template permutation_iterator">permutation_iterator&lt;ElementIterator,
            IndexIterator&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/strided_iterator.html" title="Class template strided_iterator">strided_iterator&lt;Iterator&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/transform_iterator.html" title="Class template transform_iterator">transform_iterator&lt;InputIterator,
            UnaryFunction&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/zip_iterator.html" title="Class template zip_iterator">zip_iterator&lt;IteratorTuple&gt;</a></code>
          </li>
</ul></div>
<h4>
<a name="boost_compute.reference.api_overview.h7"></a>
        <span class="phrase"><a name="boost_compute.reference.api_overview.images"></a></span><a class="link" href="reference.html#boost_compute.reference.api_overview.images">Images</a>
      </h4>
<p>
        Header: <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">compute</span><span class="special">/</span><span class="identifier">image</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/image1d.html" title="Class image1d">image1d</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/image2d.html" title="Class image2d">image2d</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/image3d.html" title="Class image3d">image3d</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/image_format.html" title="Class image_format">image_format</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/image_object.html" title="Class image_object">image_object</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/image_sampler.html" title="Class image_sampler">image_sampler</a></code>
          </li>
</ul></div>
<h4>
<a name="boost_compute.reference.api_overview.h8"></a>
        <span class="phrase"><a name="boost_compute.reference.api_overview.shared_virtual_memory"></a></span><a class="link" href="reference.html#boost_compute.reference.api_overview.shared_virtual_memory">Shared
        Virtual Memory</a>
      </h4>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/svm_ptr.html" title="Class template svm_ptr">svm_ptr&lt;T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput">svm_alloc&lt;T&gt;()</code>
          </li>
<li class="listitem">
            <code class="computeroutput">svm_free&lt;T&gt;()</code>
          </li>
</ul></div>
<h4>
<a name="boost_compute.reference.api_overview.h9"></a>
        <span class="phrase"><a name="boost_compute.reference.api_overview.macros"></a></span><a class="link" href="reference.html#boost_compute.reference.api_overview.macros">Macros</a>
      </h4>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput">BOOST_COMPUTE_ADAPT_STRUCT</code>
          </li>
<li class="listitem">
            <code class="computeroutput">BOOST_COMPUTE_FUNCTION</code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../BOOST_COMPUTE_STR_idm11779.html" title="Macro BOOST_COMPUTE_STRINGIZE_SOURCE">BOOST_COMPUTE_STRINGIZE_SOURCE()</a></code>
          </li>
</ul></div>
<h4>
<a name="boost_compute.reference.api_overview.h10"></a>
        <span class="phrase"><a name="boost_compute.reference.api_overview.opengl_sharing"></a></span><a class="link" href="reference.html#boost_compute.reference.api_overview.opengl_sharing">OpenGL
        Sharing</a>
      </h4>
<p>
        Header: <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">compute</span><span class="special">/</span><span class="identifier">interop</span><span class="special">/</span><span class="identifier">opengl</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/opengl_buffer.html" title="Class opengl_buffer">opengl_buffer</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/opengl_create_shar_idm9487.html" title="Function opengl_create_shared_context">opengl_create_shared_context()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/opengl_enqueue_acq_idm9450.html" title="Function opengl_enqueue_acquire_buffer">opengl_enqueue_acquire_buffer()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/opengl_enqueue_acq_idm9416.html" title="Function opengl_enqueue_acquire_gl_objects">opengl_enqueue_acquire_gl_objects()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/opengl_enqueue_rel_idm9466.html" title="Function opengl_enqueue_release_buffer">opengl_enqueue_release_buffer()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/opengl_enqueue_rel_idm9433.html" title="Function opengl_enqueue_release_gl_objects">opengl_enqueue_acquire_gl_objects()</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/opengl_renderbuffer.html" title="Class opengl_renderbuffer">opengl_renderbuffer</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/opengl_texture.html" title="Class opengl_texture">opengl_texture</a></code>
          </li>
</ul></div>
<h4>
<a name="boost_compute.reference.api_overview.h11"></a>
        <span class="phrase"><a name="boost_compute.reference.api_overview.random_number_generators"></a></span><a class="link" href="reference.html#boost_compute.reference.api_overview.random_number_generators">Random
        Number Generators</a>
      </h4>
<p>
        Header: <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">compute</span><span class="special">/</span><span class="identifier">random</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/bernoulli_distribution.html" title="Class template bernoulli_distribution">bernoulli_distribution</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="reference.html#boost.compute.default_random_engine">default_random_engine</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/discrete_distribution.html" title="Class template discrete_distribution">discrete_distribution</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/linear_congruential_engine.html" title="Class template linear_congruential_engine">linear_congruential_engine</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/mersenne_twister_engine.html" title="Class template mersenne_twister_engine">mersenne_twister_engine</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/normal_distribution.html" title="Class template normal_distribution">normal_distribution</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/uniform_int_distribution.html" title="Class template uniform_int_distribution">uniform_int_distribution</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/uniform_real_distribution.html" title="Class template uniform_real_distribution">uniform_real_distribution</a></code>
          </li>
</ul></div>
<h4>
<a name="boost_compute.reference.api_overview.h12"></a>
        <span class="phrase"><a name="boost_compute.reference.api_overview.type_traits"></a></span><a class="link" href="reference.html#boost_compute.reference.api_overview.type_traits">Type
        Traits</a>
      </h4>
<p>
        Header: <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">compute</span><span class="special">/</span><span class="identifier">type_traits</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/is_device_iterator.html" title="Struct template is_device_iterator">is_device_iterator&lt;Iterator&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/is_fundamental.html" title="Struct template is_fundamental">is_fundamental&lt;T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/is_vector_type.html" title="Struct template is_vector_type">is_vector_type&lt;T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/make_vector_type.html" title="Struct template make_vector_type">make_vector_type&lt;T,
            N&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/result_of.html" title="Struct template result_of">result_of&lt;F(Args...)&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/scalar_type.html" title="Struct template scalar_type">scalar_type&lt;T&gt;</a></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/compute/type_name.html" title="Function template type_name">type_name&lt;T&gt;()</a></code>
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header_reference"></a>Header Reference</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.accumulate_hpp">Header &lt;boost/compute/algorithm/accumulate.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.adjacent_difference_hpp">Header &lt;boost/compute/algorithm/adjacent_difference.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.adjacent_find_hpp">Header &lt;boost/compute/algorithm/adjacent_find.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.all_of_hpp">Header &lt;boost/compute/algorithm/all_of.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.any_of_hpp">Header &lt;boost/compute/algorithm/any_of.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.binary_search_hpp">Header &lt;boost/compute/algorithm/binary_search.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.copy_hpp">Header &lt;boost/compute/algorithm/copy.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.copy_if_hpp">Header &lt;boost/compute/algorithm/copy_if.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.copy_n_hpp">Header &lt;boost/compute/algorithm/copy_n.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.count_hpp">Header &lt;boost/compute/algorithm/count.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.count_if_hpp">Header &lt;boost/compute/algorithm/count_if.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.equal_hpp">Header &lt;boost/compute/algorithm/equal.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.equal_range_hpp">Header &lt;boost/compute/algorithm/equal_range.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.exclusive_scan_hpp">Header &lt;boost/compute/algorithm/exclusive_scan.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.fill_hpp">Header &lt;boost/compute/algorithm/fill.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.fill_n_hpp">Header &lt;boost/compute/algorithm/fill_n.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.find_hpp">Header &lt;boost/compute/algorithm/find.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.find_end_hpp">Header &lt;boost/compute/algorithm/find_end.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.find_if_hpp">Header &lt;boost/compute/algorithm/find_if.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.find_if_not_hpp">Header &lt;boost/compute/algorithm/find_if_not.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.for_each_hpp">Header &lt;boost/compute/algorithm/for_each.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.for_each_n_hpp">Header &lt;boost/compute/algorithm/for_each_n.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.gather_hpp">Header &lt;boost/compute/algorithm/gather.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.generate_hpp">Header &lt;boost/compute/algorithm/generate.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.generate_n_hpp">Header &lt;boost/compute/algorithm/generate_n.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.includes_hpp">Header &lt;boost/compute/algorithm/includes.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.inclusive_scan_hpp">Header &lt;boost/compute/algorithm/inclusive_scan.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.inner_product_hpp">Header &lt;boost/compute/algorithm/inner_product.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.inplace_merge_hpp">Header &lt;boost/compute/algorithm/inplace_merge.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.iota_hpp">Header &lt;boost/compute/algorithm/iota.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.is_partitioned_hpp">Header &lt;boost/compute/algorithm/is_partitioned.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.is_permutation_hpp">Header &lt;boost/compute/algorithm/is_permutation.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.is_sorted_hpp">Header &lt;boost/compute/algorithm/is_sorted.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.lexicographical_compare_hpp">Header &lt;boost/compute/algorithm/lexicographical_compare.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.lower_bound_hpp">Header &lt;boost/compute/algorithm/lower_bound.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.max_element_hpp">Header &lt;boost/compute/algorithm/max_element.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.merge_hpp">Header &lt;boost/compute/algorithm/merge.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.min_element_hpp">Header &lt;boost/compute/algorithm/min_element.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.minmax_element_hpp">Header &lt;boost/compute/algorithm/minmax_element.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.mismatch_hpp">Header &lt;boost/compute/algorithm/mismatch.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.next_permutation_hpp">Header &lt;boost/compute/algorithm/next_permutation.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.none_of_hpp">Header &lt;boost/compute/algorithm/none_of.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.nth_element_hpp">Header &lt;boost/compute/algorithm/nth_element.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.partial_sum_hpp">Header &lt;boost/compute/algorithm/partial_sum.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.partition_hpp">Header &lt;boost/compute/algorithm/partition.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.partition_copy_hpp">Header &lt;boost/compute/algorithm/partition_copy.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.partition_point_hpp">Header &lt;boost/compute/algorithm/partition_point.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.prev_permutation_hpp">Header &lt;boost/compute/algorithm/prev_permutation.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.random_shuffle_hpp">Header &lt;boost/compute/algorithm/random_shuffle.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.reduce_hpp">Header &lt;boost/compute/algorithm/reduce.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.reduce_by_key_hpp">Header &lt;boost/compute/algorithm/reduce_by_key.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.remove_hpp">Header &lt;boost/compute/algorithm/remove.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.remove_if_hpp">Header &lt;boost/compute/algorithm/remove_if.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.replace_hpp">Header &lt;boost/compute/algorithm/replace.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.replace_copy_hpp">Header &lt;boost/compute/algorithm/replace_copy.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.reverse_hpp">Header &lt;boost/compute/algorithm/reverse.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.reverse_copy_hpp">Header &lt;boost/compute/algorithm/reverse_copy.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.rotate_hpp">Header &lt;boost/compute/algorithm/rotate.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.rotate_copy_hpp">Header &lt;boost/compute/algorithm/rotate_copy.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.scatter_hpp">Header &lt;boost/compute/algorithm/scatter.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.scatter_if_hpp">Header &lt;boost/compute/algorithm/scatter_if.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.search_hpp">Header &lt;boost/compute/algorithm/search.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.search_n_hpp">Header &lt;boost/compute/algorithm/search_n.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.set_difference_hpp">Header &lt;boost/compute/algorithm/set_difference.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.set_intersection_hpp">Header &lt;boost/compute/algorithm/set_intersection.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.set_symmetric_difference_hpp">Header &lt;boost/compute/algorithm/set_symmetric_difference.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.set_union_hpp">Header &lt;boost/compute/algorithm/set_union.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.sort_hpp">Header &lt;boost/compute/algorithm/sort.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.sort_by_key_hpp">Header &lt;boost/compute/algorithm/sort_by_key.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.stable_partition_hpp">Header &lt;boost/compute/algorithm/stable_partition.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.stable_sort_hpp">Header &lt;boost/compute/algorithm/stable_sort.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.stable_sort_by_key_hpp">Header &lt;boost/compute/algorithm/stable_sort_by_key.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.swap_ranges_hpp">Header &lt;boost/compute/algorithm/swap_ranges.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.transform_hpp">Header &lt;boost/compute/algorithm/transform.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.transform_if_hpp">Header &lt;boost/compute/algorithm/transform_if.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.transform_reduce_hpp">Header &lt;boost/compute/algorithm/transform_reduce.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.unique_hpp">Header &lt;boost/compute/algorithm/unique.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.unique_copy_hpp">Header &lt;boost/compute/algorithm/unique_copy.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.algorithm.upper_bound_hpp">Header &lt;boost/compute/algorithm/upper_bound.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.allocator.buffer_allocator_hpp">Header &lt;boost/compute/allocator/buffer_allocator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.allocator.pinned_allocator_hpp">Header &lt;boost/compute/allocator/pinned_allocator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.async.future_hpp">Header &lt;boost/compute/async/future.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.async.wait_hpp">Header &lt;boost/compute/async/wait.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.async.wait_guard_hpp">Header &lt;boost/compute/async/wait_guard.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.container.array_hpp">Header &lt;boost/compute/container/array.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.container.basic_string_hpp">Header &lt;boost/compute/container/basic_string.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.container.dynamic_bitset_hpp">Header &lt;boost/compute/container/dynamic_bitset.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.container.flat_map_hpp">Header &lt;boost/compute/container/flat_map.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.container.flat_set_hpp">Header &lt;boost/compute/container/flat_set.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.container.mapped_view_hpp">Header &lt;boost/compute/container/mapped_view.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.container.stack_hpp">Header &lt;boost/compute/container/stack.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.container.string_hpp">Header &lt;boost/compute/container/string.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.container.valarray_hpp">Header &lt;boost/compute/container/valarray.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.container.vector_hpp">Header &lt;boost/compute/container/vector.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.exception.context_error_hpp">Header &lt;boost/compute/exception/context_error.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.exception.no_device_found_hpp">Header &lt;boost/compute/exception/no_device_found.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.exception.opencl_error_hpp">Header &lt;boost/compute/exception/opencl_error.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.exception.program_build_failure_hpp">Header &lt;boost/compute/exception/program_build_failure.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.exception.unsupported_extension_error_hpp">Header &lt;boost/compute/exception/unsupported_extension_error.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.as_hpp">Header &lt;boost/compute/functional/as.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.atomic_hpp">Header &lt;boost/compute/functional/atomic.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.bind_hpp">Header &lt;boost/compute/functional/bind.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.common_hpp">Header &lt;boost/compute/functional/common.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.convert_hpp">Header &lt;boost/compute/functional/convert.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.field_hpp">Header &lt;boost/compute/functional/field.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.geometry_hpp">Header &lt;boost/compute/functional/geometry.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.get_hpp">Header &lt;boost/compute/functional/get.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.hash_hpp">Header &lt;boost/compute/functional/hash.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.identity_hpp">Header &lt;boost/compute/functional/identity.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.integer_hpp">Header &lt;boost/compute/functional/integer.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.logical_hpp">Header &lt;boost/compute/functional/logical.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.math_hpp">Header &lt;boost/compute/functional/math.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.operator_hpp">Header &lt;boost/compute/functional/operator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.popcount_hpp">Header &lt;boost/compute/functional/popcount.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.functional.relational_hpp">Header &lt;boost/compute/functional/relational.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.image.image1d_hpp">Header &lt;boost/compute/image/image1d.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.image.image2d_hpp">Header &lt;boost/compute/image/image2d.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.image.image3d_hpp">Header &lt;boost/compute/image/image3d.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.image.image_format_hpp">Header &lt;boost/compute/image/image_format.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.image.image_object_hpp">Header &lt;boost/compute/image/image_object.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.image.image_sampler_hpp">Header &lt;boost/compute/image/image_sampler.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.interop.opengl.acquire_hpp">Header &lt;boost/compute/interop/opengl/acquire.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.interop.opengl.context_hpp">Header &lt;boost/compute/interop/opengl/context.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.interop.opengl.opengl_buffer_hpp">Header &lt;boost/compute/interop/opengl/opengl_buffer.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.interop.opengl.opengl_renderbuffer_hpp">Header &lt;boost/compute/interop/opengl/opengl_renderbuffer.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.interop.opengl.opengl_texture_hpp">Header &lt;boost/compute/interop/opengl/opengl_texture.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.iterator.buffer_iterator_hpp">Header &lt;boost/compute/iterator/buffer_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.iterator.constant_buffer_iterator_hpp">Header &lt;boost/compute/iterator/constant_buffer_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.iterator.constant_iterator_hpp">Header &lt;boost/compute/iterator/constant_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.iterator.counting_iterator_hpp">Header &lt;boost/compute/iterator/counting_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.iterator.discard_iterator_hpp">Header &lt;boost/compute/iterator/discard_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.iterator.function_input_iterator_hpp">Header &lt;boost/compute/iterator/function_input_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.iterator.permutation_iterator_hpp">Header &lt;boost/compute/iterator/permutation_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.iterator.strided_iterator_hpp">Header &lt;boost/compute/iterator/strided_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.iterator.transform_iterator_hpp">Header &lt;boost/compute/iterator/transform_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.iterator.zip_iterator_hpp">Header &lt;boost/compute/iterator/zip_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.memory.local_buffer_hpp">Header &lt;boost/compute/memory/local_buffer.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.memory.svm_ptr_hpp">Header &lt;boost/compute/memory/svm_ptr.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.random.bernoulli_distribution_hpp">Header &lt;boost/compute/random/bernoulli_distribution.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.random.default_random_engine_hpp">Header &lt;boost/compute/random/default_random_engine.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.random.discrete_distribution_hpp">Header &lt;boost/compute/random/discrete_distribution.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.random.linear_congruential_engine_hpp">Header &lt;boost/compute/random/linear_congruential_engine.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.random.mersenne_twister_engine_hpp">Header &lt;boost/compute/random/mersenne_twister_engine.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.random.normal_distribution_hpp">Header &lt;boost/compute/random/normal_distribution.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.random.threefry_engine_hpp">Header &lt;boost/compute/random/threefry_engine.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.random.uniform_int_distribution_hpp">Header &lt;boost/compute/random/uniform_int_distribution.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.random.uniform_real_distribution_hpp">Header &lt;boost/compute/random/uniform_real_distribution.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.type_traits.common_type_hpp">Header &lt;boost/compute/type_traits/common_type.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.type_traits.is_device_iterator_hpp">Header &lt;boost/compute/type_traits/is_device_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.type_traits.is_fundamental_hpp">Header &lt;boost/compute/type_traits/is_fundamental.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.type_traits.is_vector_type_hpp">Header &lt;boost/compute/type_traits/is_vector_type.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.type_traits.make_vector_type_hpp">Header &lt;boost/compute/type_traits/make_vector_type.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.type_traits.result_of_hpp">Header &lt;boost/compute/type_traits/result_of.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.type_traits.scalar_type_hpp">Header &lt;boost/compute/type_traits/scalar_type.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.type_traits.type_definition_hpp">Header &lt;boost/compute/type_traits/type_definition.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.type_traits.type_name_hpp">Header &lt;boost/compute/type_traits/type_name.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.type_traits.vector_size_hpp">Header &lt;boost/compute/type_traits/vector_size.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.utility.dim_hpp">Header &lt;boost/compute/utility/dim.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.utility.extents_hpp">Header &lt;boost/compute/utility/extents.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.utility.invoke_hpp">Header &lt;boost/compute/utility/invoke.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.utility.program_cache_hpp">Header &lt;boost/compute/utility/program_cache.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.utility.source_hpp">Header &lt;boost/compute/utility/source.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.compute.utility.wait_list_hpp">Header &lt;boost/compute/utility/wait_list.hpp&gt;</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.accumulate_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/accumulate.hpp" target="_top">boost/compute/algorithm/accumulate.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> BinaryFunction<span class="special">&gt;</span> 
      <span class="identifier">T</span> <a class="link" href="../boost/compute/accumulate.html" title="Function accumulate"><span class="identifier">accumulate</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">BinaryFunction</span><span class="special">,</span> 
                   <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="identifier">T</span> <a class="link" href="../boost/compute/accumulate.html" title="Function accumulate"><span class="identifier">accumulate</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> 
                   <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.adjacent_difference_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/adjacent_difference.hpp" target="_top">boost/compute/algorithm/adjacent_difference.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">,</span> 
             <span class="keyword">typename</span> BinaryFunction<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/adjacent_difference.html" title="Function adjacent_difference"><span class="identifier">adjacent_difference</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                          <span class="identifier">BinaryFunction</span><span class="special">,</span> 
                          <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/adjacent_difference.html" title="Function adjacent_difference"><span class="identifier">adjacent_difference</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                          <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.adjacent_find_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/adjacent_find.hpp" target="_top">boost/compute/algorithm/adjacent_find.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/adjacent_find.html" title="Function adjacent_find"><span class="identifier">adjacent_find</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> 
                    <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/adjacent_find.html" title="Function adjacent_find"><span class="identifier">adjacent_find</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                    <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.all_of_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/all_of.hpp" target="_top">boost/compute/algorithm/all_of.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> UnaryPredicate<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/all_of.html" title="Function template all_of"><span class="identifier">all_of</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">UnaryPredicate</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.any_of_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/any_of.hpp" target="_top">boost/compute/algorithm/any_of.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> UnaryPredicate<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/any_of.html" title="Function template any_of"><span class="identifier">any_of</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">UnaryPredicate</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.binary_search_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/binary_search.hpp" target="_top">boost/compute/algorithm/binary_search.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/binary_search.html" title="Function template binary_search"><span class="identifier">binary_search</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> 
                         <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.copy_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/copy.hpp" target="_top">boost/compute/algorithm/copy.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/copy.html" title="Function template copy"><span class="identifier">copy</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
           <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">,</span> 
           <span class="keyword">const</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/future.html" title="Class template future">future</a><span class="special">&lt;</span> <span class="identifier">OutputIterator</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/copy_async.html" title="Function template copy_async"><span class="identifier">copy_async</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                 <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">,</span> 
                 <span class="keyword">const</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.copy_if_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/copy_if.hpp" target="_top">boost/compute/algorithm/copy_if.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">,</span> 
             <span class="keyword">typename</span> Predicate<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/copy_if.html" title="Function template copy_if"><span class="identifier">copy_if</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> <span class="identifier">Predicate</span><span class="special">,</span> 
              <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.copy_n_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/copy_n.hpp" target="_top">boost/compute/algorithm/copy_n.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Size<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/copy_n.html" title="Function template copy_n"><span class="identifier">copy_n</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Size</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
             <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">,</span> 
             <span class="keyword">const</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.count_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/count.hpp" target="_top">boost/compute/algorithm/count.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="identifier">size_t</span> <a class="link" href="../boost/compute/count.html" title="Function template count"><span class="identifier">count</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> 
                   <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.count_if_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/count_if.hpp" target="_top">boost/compute/algorithm/count_if.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Predicate<span class="special">&gt;</span> 
      <span class="identifier">size_t</span> <a class="link" href="../boost/compute/count_if.html" title="Function template count_if"><span class="identifier">count_if</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Predicate</span><span class="special">,</span> 
                      <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.equal_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/equal.hpp" target="_top">boost/compute/algorithm/equal.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/equal.html" title="Function equal"><span class="identifier">equal</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
                 <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/equal.html" title="Function equal"><span class="identifier">equal</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
                 <span class="identifier">InputIterator2</span><span class="special">,</span> <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.equal_range_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/equal_range.hpp" target="_top">boost/compute/algorithm/equal_range.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/equal_range.html" title="Function template equal_range"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.exclusive_scan_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/exclusive_scan.hpp" target="_top">boost/compute/algorithm/exclusive_scan.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">,</span> 
             <span class="keyword">typename</span> BinaryOperator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/exclusive_scan.html" title="Function exclusive_scan"><span class="identifier">exclusive_scan</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> 
                     <span class="identifier">BinaryOperator</span><span class="special">,</span> 
                     <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/exclusive_scan.html" title="Function exclusive_scan"><span class="identifier">exclusive_scan</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> 
                     <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/exclusive_scan.html" title="Function exclusive_scan"><span class="identifier">exclusive_scan</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                     <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.fill_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/fill.hpp" target="_top">boost/compute/algorithm/fill.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BufferIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/fill.html" title="Function template fill"><span class="identifier">fill</span></a><span class="special">(</span><span class="identifier">BufferIterator</span><span class="special">,</span> <span class="identifier">BufferIterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> 
                <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BufferIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/future.html" title="Class template future">future</a><span class="special">&lt;</span> <span class="keyword">void</span> <span class="special">&gt;</span> 
      <a name="boost.compute.fill_async"></a><span class="identifier">fill_async</span><span class="special">(</span><span class="identifier">BufferIterator</span> first<span class="special">,</span> <span class="identifier">BufferIterator</span> last<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> value<span class="special">,</span> 
                 <span class="identifier">command_queue</span> <span class="special">&amp;</span> queue <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.fill_n_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/fill_n.hpp" target="_top">boost/compute/algorithm/fill_n.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BufferIterator<span class="special">,</span> <span class="keyword">typename</span> Size<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/fill_n.html" title="Function template fill_n"><span class="identifier">fill_n</span></a><span class="special">(</span><span class="identifier">BufferIterator</span><span class="special">,</span> <span class="identifier">Size</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.find_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/find.hpp" target="_top">boost/compute/algorithm/find.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/find.html" title="Function template find"><span class="identifier">find</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> 
           <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.find_end_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/find_end.hpp" target="_top">boost/compute/algorithm/find_end.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> TextIterator<span class="special">,</span> <span class="keyword">typename</span> PatternIterator<span class="special">&gt;</span> 
      <span class="identifier">TextIterator</span> 
      <a class="link" href="../boost/compute/find_end.html" title="Function template find_end"><span class="identifier">find_end</span></a><span class="special">(</span><span class="identifier">TextIterator</span><span class="special">,</span> <span class="identifier">TextIterator</span><span class="special">,</span> <span class="identifier">PatternIterator</span><span class="special">,</span> <span class="identifier">PatternIterator</span><span class="special">,</span> 
               <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.find_if_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/find_if.hpp" target="_top">boost/compute/algorithm/find_if.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> UnaryPredicate<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/find_if.html" title="Function template find_if"><span class="identifier">find_if</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">UnaryPredicate</span><span class="special">,</span> 
              <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.find_if_not_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/find_if_not.hpp" target="_top">boost/compute/algorithm/find_if_not.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> UnaryPredicate<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/find_if_not.html" title="Function template find_if_not"><span class="identifier">find_if_not</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">UnaryPredicate</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.for_each_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/for_each.hpp" target="_top">boost/compute/algorithm/for_each.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> UnaryFunction<span class="special">&gt;</span> 
      <span class="identifier">UnaryFunction</span> 
      <a class="link" href="../boost/compute/for_each.html" title="Function template for_each"><span class="identifier">for_each</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">UnaryFunction</span><span class="special">,</span> 
               <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.for_each_n_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/for_each_n.hpp" target="_top">boost/compute/algorithm/for_each_n.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Size<span class="special">,</span> <span class="keyword">typename</span> UnaryFunction<span class="special">&gt;</span> 
      <span class="identifier">UnaryFunction</span> 
      <a class="link" href="../boost/compute/for_each_n.html" title="Function template for_each_n"><span class="identifier">for_each_n</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Size</span><span class="special">,</span> <span class="identifier">UnaryFunction</span><span class="special">,</span> 
                 <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.gather_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/gather.hpp" target="_top">boost/compute/algorithm/gather.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> MapIterator<span class="special">,</span> 
             <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/gather.html" title="Function template gather"><span class="identifier">gather</span></a><span class="special">(</span><span class="identifier">MapIterator</span><span class="special">,</span> <span class="identifier">MapIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.generate_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/generate.hpp" target="_top">boost/compute/algorithm/generate.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> OutputIterator<span class="special">,</span> <span class="keyword">typename</span> Generator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/generate.html" title="Function template generate"><span class="identifier">generate</span></a><span class="special">(</span><span class="identifier">OutputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> <span class="identifier">Generator</span><span class="special">,</span> 
                    <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.generate_n_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/generate_n.hpp" target="_top">boost/compute/algorithm/generate_n.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> OutputIterator<span class="special">,</span> <span class="keyword">typename</span> Size<span class="special">,</span> <span class="keyword">typename</span> Generator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/generate_n.html" title="Function template generate_n"><span class="identifier">generate_n</span></a><span class="special">(</span><span class="identifier">OutputIterator</span><span class="special">,</span> <span class="identifier">Size</span><span class="special">,</span> <span class="identifier">Generator</span><span class="special">,</span> 
                      <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.includes_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/includes.hpp" target="_top">boost/compute/algorithm/includes.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/includes.html" title="Function template includes"><span class="identifier">includes</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
                    <span class="identifier">InputIterator2</span><span class="special">,</span> 
                    <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.inclusive_scan_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/inclusive_scan.hpp" target="_top">boost/compute/algorithm/inclusive_scan.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">,</span> 
             <span class="keyword">typename</span> BinaryOperator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/inclusive_scan.html" title="Function inclusive_scan"><span class="identifier">inclusive_scan</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                     <span class="identifier">BinaryOperator</span><span class="special">,</span> 
                     <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/inclusive_scan.html" title="Function inclusive_scan"><span class="identifier">inclusive_scan</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                     <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.inner_product_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/inner_product.hpp" target="_top">boost/compute/algorithm/inner_product.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="identifier">T</span> <a class="link" href="../boost/compute/inner_product.html" title="Function inner_product"><span class="identifier">inner_product</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> 
                      <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">,</span> 
             <span class="keyword">typename</span> BinaryAccumulateFunction<span class="special">,</span> 
             <span class="keyword">typename</span> BinaryTransformFunction<span class="special">&gt;</span> 
      <span class="identifier">T</span> <a class="link" href="../boost/compute/inner_product.html" title="Function inner_product"><span class="identifier">inner_product</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> 
                      <span class="identifier">BinaryAccumulateFunction</span><span class="special">,</span> <span class="identifier">BinaryTransformFunction</span><span class="special">,</span> 
                      <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.inplace_merge_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/inplace_merge.hpp" target="_top">boost/compute/algorithm/inplace_merge.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/inplace_merge.html" title="Function template inplace_merge"><span class="identifier">inplace_merge</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> 
                         <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.iota_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/iota.hpp" target="_top">boost/compute/algorithm/iota.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BufferIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/iota.html" title="Function template iota"><span class="identifier">iota</span></a><span class="special">(</span><span class="identifier">BufferIterator</span><span class="special">,</span> <span class="identifier">BufferIterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> 
                <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.is_partitioned_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/is_partitioned.hpp" target="_top">boost/compute/algorithm/is_partitioned.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> UnaryPredicate<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/is_partitioned.html" title="Function template is_partitioned"><span class="identifier">is_partitioned</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">UnaryPredicate</span><span class="special">,</span> 
                          <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.is_permutation_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/is_permutation.hpp" target="_top">boost/compute/algorithm/is_permutation.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/is_permutation.html" title="Function template is_permutation"><span class="identifier">is_permutation</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
                          <span class="identifier">InputIterator2</span><span class="special">,</span> 
                          <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.is_sorted_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/is_sorted.hpp" target="_top">boost/compute/algorithm/is_sorted.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/is_sorted.html" title="Function is_sorted"><span class="identifier">is_sorted</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> 
                     <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/is_sorted.html" title="Function is_sorted"><span class="identifier">is_sorted</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                     <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.lexicographical_compare_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/lexicographical_compare.hpp" target="_top">boost/compute/algorithm/lexicographical_compare.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/lexicographical_compare.html" title="Function template lexicographical_compare"><span class="identifier">lexicographical_compare</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> 
                                   <span class="identifier">InputIterator2</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
                                   <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.lower_bound_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/lower_bound.hpp" target="_top">boost/compute/algorithm/lower_bound.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/lower_bound.html" title="Function template lower_bound"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.max_element_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/max_element.hpp" target="_top">boost/compute/algorithm/max_element.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/max_element.html" title="Function max_element"><span class="identifier">max_element</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/max_element.html" title="Function max_element"><span class="identifier">max_element</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.merge_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/merge.hpp" target="_top">boost/compute/algorithm/merge.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">,</span> 
             <span class="keyword">typename</span> OutputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/merge.html" title="Function merge"><span class="identifier">merge</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
            <span class="identifier">OutputIterator</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> 
            <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">,</span> 
             <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/merge.html" title="Function merge"><span class="identifier">merge</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
            <span class="identifier">OutputIterator</span><span class="special">,</span> <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.min_element_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/min_element.hpp" target="_top">boost/compute/algorithm/min_element.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/min_element.html" title="Function min_element"><span class="identifier">min_element</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/min_element.html" title="Function min_element"><span class="identifier">min_element</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.minmax_element_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/minmax_element.hpp" target="_top">boost/compute/algorithm/minmax_element.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/minmax_element.html" title="Function minmax_element"><span class="identifier">minmax_element</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> 
                     <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/minmax_element.html" title="Function minmax_element"><span class="identifier">minmax_element</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                     <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.mismatch_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/mismatch.hpp" target="_top">boost/compute/algorithm/mismatch.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/mismatch.html" title="Function mismatch"><span class="identifier">mismatch</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
               <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/mismatch.html" title="Function mismatch"><span class="identifier">mismatch</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
               <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.next_permutation_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/next_permutation.hpp" target="_top">boost/compute/algorithm/next_permutation.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/next_permutation.html" title="Function template next_permutation"><span class="identifier">next_permutation</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                            <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.none_of_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/none_of.hpp" target="_top">boost/compute/algorithm/none_of.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> UnaryPredicate<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/none_of.html" title="Function template none_of"><span class="identifier">none_of</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">UnaryPredicate</span><span class="special">,</span> 
                   <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.nth_element_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/nth_element.hpp" target="_top">boost/compute/algorithm/nth_element.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/nth_element.html" title="Function nth_element"><span class="identifier">nth_element</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> 
                       <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/nth_element.html" title="Function nth_element"><span class="identifier">nth_element</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> 
                       <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.partial_sum_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/partial_sum.hpp" target="_top">boost/compute/algorithm/partial_sum.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/partial_sum.html" title="Function template partial_sum"><span class="identifier">partial_sum</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.partition_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/partition.hpp" target="_top">boost/compute/algorithm/partition.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">,</span> <span class="keyword">typename</span> UnaryPredicate<span class="special">&gt;</span> 
      <span class="identifier">Iterator</span> <a class="link" href="../boost/compute/partition.html" title="Function template partition"><span class="identifier">partition</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">UnaryPredicate</span><span class="special">,</span> 
                         <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.partition_copy_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/partition_copy.hpp" target="_top">boost/compute/algorithm/partition_copy.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator1<span class="special">,</span> 
             <span class="keyword">typename</span> OutputIterator2<span class="special">,</span> <span class="keyword">typename</span> UnaryPredicate<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">OutputIterator1</span><span class="special">,</span> <span class="identifier">OutputIterator2</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/partition_copy.html" title="Function template partition_copy"><span class="identifier">partition_copy</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator1</span><span class="special">,</span> 
                     <span class="identifier">OutputIterator2</span><span class="special">,</span> <span class="identifier">UnaryPredicate</span><span class="special">,</span> 
                     <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.partition_point_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/partition_point.hpp" target="_top">boost/compute/algorithm/partition_point.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> UnaryPredicate<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/partition_point.html" title="Function template partition_point"><span class="identifier">partition_point</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">UnaryPredicate</span><span class="special">,</span> 
                      <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.prev_permutation_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/prev_permutation.hpp" target="_top">boost/compute/algorithm/prev_permutation.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a class="link" href="../boost/compute/prev_permutation.html" title="Function template prev_permutation"><span class="identifier">prev_permutation</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                            <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.random_shuffle_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/random_shuffle.hpp" target="_top">boost/compute/algorithm/random_shuffle.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/random_shuffle.html" title="Function template random_shuffle"><span class="identifier">random_shuffle</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> 
                          <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.reduce_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/reduce.hpp" target="_top">boost/compute/algorithm/reduce.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">,</span> 
             <span class="keyword">typename</span> BinaryFunction<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/reduce.html" title="Function reduce"><span class="identifier">reduce</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                  <span class="identifier">BinaryFunction</span><span class="special">,</span> <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/reduce.html" title="Function reduce"><span class="identifier">reduce</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.reduce_by_key_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/reduce_by_key.hpp" target="_top">boost/compute/algorithm/reduce_by_key.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputKeyIterator<span class="special">,</span> <span class="keyword">typename</span> InputValueIterator<span class="special">,</span> 
             <span class="keyword">typename</span> OutputKeyIterator<span class="special">,</span> <span class="keyword">typename</span> OutputValueIterator<span class="special">,</span> 
             <span class="keyword">typename</span> BinaryFunction<span class="special">,</span> <span class="keyword">typename</span> BinaryPredicate<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">OutputKeyIterator</span><span class="special">,</span> <span class="identifier">OutputValueIterator</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/reduce_by_key.html" title="Function reduce_by_key"><span class="identifier">reduce_by_key</span></a><span class="special">(</span><span class="identifier">InputKeyIterator</span><span class="special">,</span> <span class="identifier">InputKeyIterator</span><span class="special">,</span> <span class="identifier">InputValueIterator</span><span class="special">,</span> 
                    <span class="identifier">OutputKeyIterator</span><span class="special">,</span> <span class="identifier">OutputValueIterator</span><span class="special">,</span> <span class="identifier">BinaryFunction</span><span class="special">,</span> 
                    <span class="identifier">BinaryPredicate</span><span class="special">,</span> 
                    <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputKeyIterator<span class="special">,</span> <span class="keyword">typename</span> InputValueIterator<span class="special">,</span> 
             <span class="keyword">typename</span> OutputKeyIterator<span class="special">,</span> <span class="keyword">typename</span> OutputValueIterator<span class="special">,</span> 
             <span class="keyword">typename</span> BinaryFunction<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">OutputKeyIterator</span><span class="special">,</span> <span class="identifier">OutputValueIterator</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/reduce_by_key.html" title="Function reduce_by_key"><span class="identifier">reduce_by_key</span></a><span class="special">(</span><span class="identifier">InputKeyIterator</span><span class="special">,</span> <span class="identifier">InputKeyIterator</span><span class="special">,</span> <span class="identifier">InputValueIterator</span><span class="special">,</span> 
                    <span class="identifier">OutputKeyIterator</span><span class="special">,</span> <span class="identifier">OutputValueIterator</span><span class="special">,</span> <span class="identifier">BinaryFunction</span><span class="special">,</span> 
                    <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputKeyIterator<span class="special">,</span> <span class="keyword">typename</span> InputValueIterator<span class="special">,</span> 
             <span class="keyword">typename</span> OutputKeyIterator<span class="special">,</span> <span class="keyword">typename</span> OutputValueIterator<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">OutputKeyIterator</span><span class="special">,</span> <span class="identifier">OutputValueIterator</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/reduce_by_key.html" title="Function reduce_by_key"><span class="identifier">reduce_by_key</span></a><span class="special">(</span><span class="identifier">InputKeyIterator</span><span class="special">,</span> <span class="identifier">InputKeyIterator</span><span class="special">,</span> <span class="identifier">InputValueIterator</span><span class="special">,</span> 
                    <span class="identifier">OutputKeyIterator</span><span class="special">,</span> <span class="identifier">OutputValueIterator</span><span class="special">,</span> 
                    <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.remove_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/remove.hpp" target="_top">boost/compute/algorithm/remove.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="identifier">Iterator</span> <a class="link" href="../boost/compute/remove.html" title="Function template remove"><span class="identifier">remove</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> 
                      <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.remove_if_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/remove_if.hpp" target="_top">boost/compute/algorithm/remove_if.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">,</span> <span class="keyword">typename</span> Predicate<span class="special">&gt;</span> 
      <span class="identifier">Iterator</span> <a class="link" href="../boost/compute/remove_if.html" title="Function template remove_if"><span class="identifier">remove_if</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Predicate</span><span class="special">,</span> 
                         <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.replace_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/replace.hpp" target="_top">boost/compute/algorithm/replace.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/replace.html" title="Function template replace"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> 
                   <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.replace_copy_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/replace_copy.hpp" target="_top">boost/compute/algorithm/replace_copy.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/replace_copy.html" title="Function template replace_copy"><span class="identifier">replace_copy</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> 
                   <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.reverse_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/reverse.hpp" target="_top">boost/compute/algorithm/reverse.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/reverse.html" title="Function template reverse"><span class="identifier">reverse</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> 
                   <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.reverse_copy_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/reverse_copy.hpp" target="_top">boost/compute/algorithm/reverse_copy.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/reverse_copy.html" title="Function template reverse_copy"><span class="identifier">reverse_copy</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                   <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.rotate_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/rotate.hpp" target="_top">boost/compute/algorithm/rotate.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/rotate.html" title="Function template rotate"><span class="identifier">rotate</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.rotate_copy_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/rotate_copy.hpp" target="_top">boost/compute/algorithm/rotate_copy.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/rotate_copy.html" title="Function template rotate_copy"><span class="identifier">rotate_copy</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                       <span class="identifier">OutputIterator</span><span class="special">,</span> 
                       <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.scatter_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/scatter.hpp" target="_top">boost/compute/algorithm/scatter.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> MapIterator<span class="special">,</span> 
             <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/scatter.html" title="Function template scatter"><span class="identifier">scatter</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">MapIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                   <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.scatter_if_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/scatter_if.hpp" target="_top">boost/compute/algorithm/scatter_if.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> MapIterator<span class="special">,</span> 
             <span class="keyword">typename</span> StencilIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">,</span> 
             <span class="keyword">typename</span> Predicate<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/scatter_if_idm5225.html" title="Function template scatter_if"><span class="identifier">scatter_if</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">MapIterator</span><span class="special">,</span> 
                      <span class="identifier">StencilIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> <span class="identifier">Predicate</span><span class="special">,</span> 
                      <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> MapIterator<span class="special">,</span> 
             <span class="keyword">typename</span> StencilIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.compute.scatter_if_idm5255"></a><span class="identifier">scatter_if</span><span class="special">(</span><span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">,</span> 
                      <span class="identifier">MapIterator</span> map<span class="special">,</span> <span class="identifier">StencilIterator</span> stencil<span class="special">,</span> 
                      <span class="identifier">OutputIterator</span> result<span class="special">,</span> 
                      <span class="identifier">command_queue</span> <span class="special">&amp;</span> queue <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.search_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/search.hpp" target="_top">boost/compute/algorithm/search.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> TextIterator<span class="special">,</span> <span class="keyword">typename</span> PatternIterator<span class="special">&gt;</span> 
      <span class="identifier">TextIterator</span> 
      <a class="link" href="../boost/compute/search.html" title="Function template search"><span class="identifier">search</span></a><span class="special">(</span><span class="identifier">TextIterator</span><span class="special">,</span> <span class="identifier">TextIterator</span><span class="special">,</span> <span class="identifier">PatternIterator</span><span class="special">,</span> <span class="identifier">PatternIterator</span><span class="special">,</span> 
             <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.search_n_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/search_n.hpp" target="_top">boost/compute/algorithm/search_n.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> TextIterator<span class="special">,</span> <span class="keyword">typename</span> ValueType<span class="special">&gt;</span> 
      <span class="identifier">TextIterator</span> 
      <a class="link" href="../boost/compute/search_n.html" title="Function template search_n"><span class="identifier">search_n</span></a><span class="special">(</span><span class="identifier">TextIterator</span><span class="special">,</span> <span class="identifier">TextIterator</span><span class="special">,</span> <span class="identifier">size_t</span><span class="special">,</span> <span class="identifier">ValueType</span><span class="special">,</span> 
               <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.set_difference_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/set_difference.hpp" target="_top">boost/compute/algorithm/set_difference.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">,</span> 
             <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/set_difference.html" title="Function template set_difference"><span class="identifier">set_difference</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
                     <span class="identifier">InputIterator2</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                     <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.set_intersection_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/set_intersection.hpp" target="_top">boost/compute/algorithm/set_intersection.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">,</span> 
             <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/set_intersection.html" title="Function template set_intersection"><span class="identifier">set_intersection</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
                       <span class="identifier">InputIterator2</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                       <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.set_symmetric_difference_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/set_symmetric_difference.hpp" target="_top">boost/compute/algorithm/set_symmetric_difference.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">,</span> 
             <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/set_symmetric_difference.html" title="Function template set_symmetric_difference"><span class="identifier">set_symmetric_difference</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
                               <span class="identifier">InputIterator2</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                               <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.set_union_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/set_union.hpp" target="_top">boost/compute/algorithm/set_union.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">,</span> 
             <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/set_union.html" title="Function template set_union"><span class="identifier">set_union</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
                <span class="identifier">InputIterator2</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.sort_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/sort.hpp" target="_top">boost/compute/algorithm/sort.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/sort.html" title="Function sort"><span class="identifier">sort</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> 
                <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/sort.html" title="Function sort"><span class="identifier">sort</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.sort_by_key_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/sort_by_key.hpp" target="_top">boost/compute/algorithm/sort_by_key.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyIterator<span class="special">,</span> <span class="keyword">typename</span> ValueIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/sort_by_key.html" title="Function sort_by_key"><span class="identifier">sort_by_key</span></a><span class="special">(</span><span class="identifier">KeyIterator</span><span class="special">,</span> <span class="identifier">KeyIterator</span><span class="special">,</span> <span class="identifier">ValueIterator</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> 
                       <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyIterator<span class="special">,</span> <span class="keyword">typename</span> ValueIterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/sort_by_key.html" title="Function sort_by_key"><span class="identifier">sort_by_key</span></a><span class="special">(</span><span class="identifier">KeyIterator</span><span class="special">,</span> <span class="identifier">KeyIterator</span><span class="special">,</span> <span class="identifier">ValueIterator</span><span class="special">,</span> 
                       <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.stable_partition_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/stable_partition.hpp" target="_top">boost/compute/algorithm/stable_partition.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">,</span> <span class="keyword">typename</span> UnaryPredicate<span class="special">&gt;</span> 
      <span class="identifier">Iterator</span> <a class="link" href="../boost/compute/stable_partition.html" title="Function template stable_partition"><span class="identifier">stable_partition</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">UnaryPredicate</span><span class="special">,</span> 
                                <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.stable_sort_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/stable_sort.hpp" target="_top">boost/compute/algorithm/stable_sort.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/stable_sort.html" title="Function stable_sort"><span class="identifier">stable_sort</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Compare</span><span class="special">,</span> 
                       <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/stable_sort.html" title="Function stable_sort"><span class="identifier">stable_sort</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> 
                       <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.stable_sort_by_key_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/stable_sort_by_key.hpp" target="_top">boost/compute/algorithm/stable_sort_by_key.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyIterator<span class="special">,</span> <span class="keyword">typename</span> ValueIterator<span class="special">,</span> <span class="keyword">typename</span> Compare<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/stable_sort_by_key.html" title="Function stable_sort_by_key"><span class="identifier">stable_sort_by_key</span></a><span class="special">(</span><span class="identifier">KeyIterator</span><span class="special">,</span> <span class="identifier">KeyIterator</span><span class="special">,</span> <span class="identifier">ValueIterator</span><span class="special">,</span> 
                              <span class="identifier">Compare</span><span class="special">,</span> 
                              <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyIterator<span class="special">,</span> <span class="keyword">typename</span> ValueIterator<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/stable_sort_by_key.html" title="Function stable_sort_by_key"><span class="identifier">stable_sort_by_key</span></a><span class="special">(</span><span class="identifier">KeyIterator</span><span class="special">,</span> <span class="identifier">KeyIterator</span><span class="special">,</span> <span class="identifier">ValueIterator</span><span class="special">,</span> 
                              <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.swap_ranges_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/swap_ranges.hpp" target="_top">boost/compute/algorithm/swap_ranges.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator1<span class="special">,</span> <span class="keyword">typename</span> Iterator2<span class="special">&gt;</span> 
      <span class="identifier">Iterator2</span> <a class="link" href="../boost/compute/swap_ranges.html" title="Function template swap_ranges"><span class="identifier">swap_ranges</span></a><span class="special">(</span><span class="identifier">Iterator1</span><span class="special">,</span> <span class="identifier">Iterator1</span><span class="special">,</span> <span class="identifier">Iterator2</span><span class="special">,</span> 
                            <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.transform_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/transform.hpp" target="_top">boost/compute/algorithm/transform.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">,</span> 
             <span class="keyword">typename</span> UnaryOperator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/transform.html" title="Function transform"><span class="identifier">transform</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> <span class="identifier">UnaryOperator</span><span class="special">,</span> 
                <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">,</span> 
             <span class="keyword">typename</span> OutputIterator<span class="special">,</span> <span class="keyword">typename</span> BinaryOperator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/transform.html" title="Function transform"><span class="identifier">transform</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
                <span class="identifier">OutputIterator</span><span class="special">,</span> <span class="identifier">BinaryOperator</span><span class="special">,</span> 
                <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.transform_if_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/transform_if.hpp" target="_top">boost/compute/algorithm/transform_if.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">,</span> 
             <span class="keyword">typename</span> UnaryFunction<span class="special">,</span> <span class="keyword">typename</span> Predicate<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/transform_if.html" title="Function template transform_if"><span class="identifier">transform_if</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                   <span class="identifier">UnaryFunction</span><span class="special">,</span> <span class="identifier">Predicate</span><span class="special">,</span> 
                   <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.transform_reduce_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/transform_reduce.hpp" target="_top">boost/compute/algorithm/transform_reduce.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">,</span> 
             <span class="keyword">typename</span> UnaryTransformFunction<span class="special">,</span> <span class="keyword">typename</span> BinaryReduceFunction<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/transform_reduce.html" title="Function transform_reduce"><span class="identifier">transform_reduce</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                            <span class="identifier">UnaryTransformFunction</span><span class="special">,</span> <span class="identifier">BinaryReduceFunction</span><span class="special">,</span> 
                            <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator1<span class="special">,</span> <span class="keyword">typename</span> InputIterator2<span class="special">,</span> 
             <span class="keyword">typename</span> OutputIterator<span class="special">,</span> <span class="keyword">typename</span> BinaryTransformFunction<span class="special">,</span> 
             <span class="keyword">typename</span> BinaryReduceFunction<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/compute/transform_reduce.html" title="Function transform_reduce"><span class="identifier">transform_reduce</span></a><span class="special">(</span><span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator1</span><span class="special">,</span> <span class="identifier">InputIterator2</span><span class="special">,</span> 
                            <span class="identifier">OutputIterator</span><span class="special">,</span> <span class="identifier">BinaryTransformFunction</span><span class="special">,</span> 
                            <span class="identifier">BinaryReduceFunction</span><span class="special">,</span> 
                            <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.unique_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/unique.hpp" target="_top">boost/compute/algorithm/unique.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> BinaryPredicate<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/unique.html" title="Function unique"><span class="identifier">unique</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">BinaryPredicate</span><span class="special">,</span> 
             <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/unique.html" title="Function unique"><span class="identifier">unique</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
             <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.unique_copy_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/unique_copy.hpp" target="_top">boost/compute/algorithm/unique_copy.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">,</span> 
             <span class="keyword">typename</span> BinaryPredicate<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/unique_copy.html" title="Function unique_copy"><span class="identifier">unique_copy</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                  <span class="identifier">BinaryPredicate</span><span class="special">,</span> <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> OutputIterator<span class="special">&gt;</span> 
      <span class="identifier">OutputIterator</span> 
      <a class="link" href="../boost/compute/unique_copy.html" title="Function unique_copy"><span class="identifier">unique_copy</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">OutputIterator</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.algorithm.upper_bound_hpp"></a>Header &lt;<a href="../../../../../boost/compute/algorithm/upper_bound.hpp" target="_top">boost/compute/algorithm/upper_bound.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="identifier">InputIterator</span> 
      <a class="link" href="../boost/compute/upper_bound.html" title="Function template upper_bound"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> 
                  <span class="identifier">command_queue</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">system</span><span class="special">::</span><span class="identifier">default_queue</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.allocator.buffer_allocator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/allocator/buffer_allocator.hpp" target="_top">boost/compute/allocator/buffer_allocator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/buffer_allocator.html" title="Class template buffer_allocator">buffer_allocator</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.allocator.pinned_allocator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/allocator/pinned_allocator.hpp" target="_top">boost/compute/allocator/pinned_allocator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/pinned_allocator.html" title="Class template pinned_allocator">pinned_allocator</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.async.future_hpp"></a>Header &lt;<a href="../../../../../boost/compute/async/future.hpp" target="_top">boost/compute/async/future.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/future.html" title="Class template future">future</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.async.wait_hpp"></a>Header &lt;<a href="../../../../../boost/compute/async/wait.hpp" target="_top">boost/compute/async/wait.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Events<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="../boost/compute/wait_for_all.html" title="Function template wait_for_all"><span class="identifier">wait_for_all</span></a><span class="special">(</span><span class="identifier">Events</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.async.wait_guard_hpp"></a>Header &lt;<a href="../../../../../boost/compute/async/wait_guard.hpp" target="_top">boost/compute/async/wait_guard.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Waitable<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/wait_guard.html" title="Class template wait_guard">wait_guard</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.container.array_hpp"></a>Header &lt;<a href="../../../../../boost/compute/container/array.hpp" target="_top">boost/compute/container/array.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> N<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/array.html" title="Class template array">array</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.container.basic_string_hpp"></a>Header &lt;<a href="../../../../../boost/compute/container/basic_string.hpp" target="_top">boost/compute/container/basic_string.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/compute/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostream</span> <span class="special">&amp;</span> 
      <a name="boost.compute.operator_idm6630"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">ostream</span> <span class="special">&amp;</span> stream<span class="special">,</span> 
                 <a class="link" href="../boost/compute/basic_string.html" title="Class template basic_string">boost::compute::basic_string</a><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="keyword">const</span> <span class="special">&amp;</span> outStr<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.container.dynamic_bitset_hpp"></a>Header &lt;<a href="../../../../../boost/compute/container/dynamic_bitset.hpp" target="_top">boost/compute/container/dynamic_bitset.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Block <span class="special">=</span> <span class="identifier">ulong_</span><span class="special">,</span> 
             <span class="keyword">typename</span> Alloc <span class="special">=</span> <a class="link" href="../boost/compute/buffer_allocator.html" title="Class template buffer_allocator">buffer_allocator</a><span class="special">&lt;</span><span class="identifier">Block</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/compute/dynamic_bitset.html" title="Class template dynamic_bitset">dynamic_bitset</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.container.flat_map_hpp"></a>Header &lt;<a href="../../../../../boost/compute/container/flat_map.hpp" target="_top">boost/compute/container/flat_map.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/flat_map.html" title="Class template flat_map">flat_map</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.container.flat_set_hpp"></a>Header &lt;<a href="../../../../../boost/compute/container/flat_set.hpp" target="_top">boost/compute/container/flat_set.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/flat_set.html" title="Class template flat_set">flat_set</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.container.mapped_view_hpp"></a>Header &lt;<a href="../../../../../boost/compute/container/mapped_view.hpp" target="_top">boost/compute/container/mapped_view.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/mapped_view.html" title="Class template mapped_view">mapped_view</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.container.stack_hpp"></a>Header &lt;<a href="../../../../../boost/compute/container/stack.hpp" target="_top">boost/compute/container/stack.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/stack.html" title="Class template stack">stack</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.container.string_hpp"></a>Header &lt;<a href="../../../../../boost/compute/container/string.hpp" target="_top">boost/compute/container/string.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">typedef</span> <a class="link" href="../boost/compute/basic_string.html" title="Class template basic_string">basic_string</a><span class="special">&lt;</span> <span class="identifier">char_</span> <span class="special">&gt;</span> <a name="boost.compute.string"></a><span class="identifier">string</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.container.valarray_hpp"></a>Header &lt;<a href="../../../../../boost/compute/container/valarray.hpp" target="_top">boost/compute/container/valarray.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis">

<a class="link" href="../BOOST_COMPUTE_DEFI_idm7654.html" title="Macro BOOST_COMPUTE_DEFINE_VALARRAY_BINARY_OPERATOR">BOOST_COMPUTE_DEFINE_VALARRAY_BINARY_OPERATOR</a>(op, op_name, assert)
<a class="link" href="../BOOST_COMPUTE_DEFI_idm7658.html" title="Macro BOOST_COMPUTE_DEFINE_VALARRAY_BINARY_OPERATOR_ANY">BOOST_COMPUTE_DEFINE_VALARRAY_BINARY_OPERATOR_ANY</a>(op, op_name)
<a class="link" href="../BOOST_COMPUTE_DEFI_idm7661.html" title="Macro BOOST_COMPUTE_DEFINE_VALARRAY_BINARY_OPERATOR_NO_FP">BOOST_COMPUTE_DEFINE_VALARRAY_BINARY_OPERATOR_NO_FP</a>(op, op_name)
<a class="link" href="../BOOST_COMPUTE_DEFI_idm7664.html" title="Macro BOOST_COMPUTE_DEFINE_VALARRAY_COMPARISON_OPERATOR">BOOST_COMPUTE_DEFINE_VALARRAY_COMPARISON_OPERATOR</a>(op, op_name)
<a class="link" href="../BOOST_COMPUTE_DEFI_idm7667.html" title="Macro BOOST_COMPUTE_DEFINE_VALARRAY_LOGICAL_OPERATOR">BOOST_COMPUTE_DEFINE_VALARRAY_LOGICAL_OPERATOR</a>(op, op_name)</pre>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/valarray.html" title="Class template valarray">valarray</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.container.vector_hpp"></a>Header &lt;<a href="../../../../../boost/compute/container/vector.hpp" target="_top">boost/compute/container/vector.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Alloc <span class="special">=</span> <a class="link" href="../boost/compute/buffer_allocator.html" title="Class template buffer_allocator">buffer_allocator</a><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/vector.html" title="Class template vector">vector</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.exception.context_error_hpp"></a>Header &lt;<a href="../../../../../boost/compute/exception/context_error.hpp" target="_top">boost/compute/exception/context_error.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/context_error.html" title="Class context_error">context_error</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.exception.no_device_found_hpp"></a>Header &lt;<a href="../../../../../boost/compute/exception/no_device_found.hpp" target="_top">boost/compute/exception/no_device_found.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/no_device_found.html" title="Class no_device_found">no_device_found</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.exception.opencl_error_hpp"></a>Header &lt;<a href="../../../../../boost/compute/exception/opencl_error.hpp" target="_top">boost/compute/exception/opencl_error.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/opencl_error.html" title="Class opencl_error">opencl_error</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.exception.program_build_failure_hpp"></a>Header &lt;<a href="../../../../../boost/compute/exception/program_build_failure.hpp" target="_top">boost/compute/exception/program_build_failure.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/program_build_failure.html" title="Class program_build_failure">program_build_failure</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.exception.unsupported_extension_error_hpp"></a>Header &lt;<a href="../../../../../boost/compute/exception/unsupported_extension_error.hpp" target="_top">boost/compute/exception/unsupported_extension_error.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/unsupported_extens_idm8279.html" title="Class unsupported_extension_error">unsupported_extension_error</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.as_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/as.hpp" target="_top">boost/compute/functional/as.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/as.html" title="Struct template as">as</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.atomic_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/atomic.hpp" target="_top">boost/compute/functional/atomic.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/atomic_add.html" title="Class template atomic_add">atomic_add</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/atomic_and.html" title="Class template atomic_and">atomic_and</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/atomic_cmpxchg.html" title="Class template atomic_cmpxchg">atomic_cmpxchg</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/atomic_dec.html" title="Class template atomic_dec">atomic_dec</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/atomic_inc.html" title="Class template atomic_inc">atomic_inc</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/atomic_max.html" title="Class template atomic_max">atomic_max</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/atomic_min.html" title="Class template atomic_min">atomic_min</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/atomic_or.html" title="Class template atomic_or">atomic_or</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/atomic_sub.html" title="Class template atomic_sub">atomic_sub</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/atomic_xchg.html" title="Class template atomic_xchg">atomic_xchg</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/atomic_xor.html" title="Class template atomic_xor">atomic_xor</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.bind_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/bind.hpp" target="_top">boost/compute/functional/bind.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/is_placeholder.html" title="Struct template is_placeholder">is_placeholder</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> F<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <a class="link" href="../boost/compute/bind.html" title="Function template bind"><span class="identifier">bind</span></a><span class="special">(</span><span class="identifier">F</span><span class="special">,</span> <span class="identifier">Args</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">placeholders</span> <span class="special">{</span>
      placeholder<span class="special">&lt;</span> <span class="number">0</span> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/compute/placeholders/_1.html" title="Global _1">_1</a><span class="special">;</span>
      placeholder<span class="special">&lt;</span> <span class="number">1</span> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/compute/placeholders/_2.html" title="Global _2">_2</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.common_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/common.hpp" target="_top">boost/compute/functional/common.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.convert_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/convert.hpp" target="_top">boost/compute/functional/convert.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/convert.html" title="Struct template convert">convert</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.field_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/field.hpp" target="_top">boost/compute/functional/field.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/field.html" title="Class template field">field</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.geometry_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/geometry.hpp" target="_top">boost/compute/functional/geometry.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.get_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/get.hpp" target="_top">boost/compute/functional/get.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="identifier">size_t</span> N<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/get.html" title="Struct template get">get</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.hash_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/hash.hpp" target="_top">boost/compute/functional/hash.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/hash.html" title="Struct template hash">hash</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.identity_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/identity.hpp" target="_top">boost/compute/functional/identity.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/identity.html" title="Class template identity">identity</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.integer_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/integer.hpp" target="_top">boost/compute/functional/integer.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.logical_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/logical.hpp" target="_top">boost/compute/functional/logical.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Predicate<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/binary_negate.html" title="Class template binary_negate">binary_negate</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/logical_not.html" title="Struct template logical_not">logical_not</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Predicate<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/unary_negate.html" title="Class template unary_negate">unary_negate</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Predicate<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/unary_negate.html" title="Class template unary_negate">unary_negate</a><span class="special">&lt;</span> <span class="identifier">Predicate</span> <span class="special">&gt;</span> <a class="link" href="../boost/compute/not1.html" title="Function template not1"><span class="identifier">not1</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Predicate</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Predicate<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/binary_negate.html" title="Class template binary_negate">binary_negate</a><span class="special">&lt;</span> <span class="identifier">Predicate</span> <span class="special">&gt;</span> <a class="link" href="../boost/compute/not2.html" title="Function template not2"><span class="identifier">not2</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Predicate</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.math_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/math.hpp" target="_top">boost/compute/functional/math.hpp</a>&gt;</h4></div></div></div></div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.operator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/operator.hpp" target="_top">boost/compute/functional/operator.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.popcount_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/popcount.hpp" target="_top">boost/compute/functional/popcount.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/popcount.html" title="Class template popcount">popcount</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.functional.relational_hpp"></a>Header &lt;<a href="../../../../../boost/compute/functional/relational.hpp" target="_top">boost/compute/functional/relational.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.image.image1d_hpp"></a>Header &lt;<a href="../../../../../boost/compute/image/image1d.hpp" target="_top">boost/compute/image/image1d.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/image1d.html" title="Class image1d">image1d</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.image.image2d_hpp"></a>Header &lt;<a href="../../../../../boost/compute/image/image2d.hpp" target="_top">boost/compute/image/image2d.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/image2d.html" title="Class image2d">image2d</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.image.image3d_hpp"></a>Header &lt;<a href="../../../../../boost/compute/image/image3d.hpp" target="_top">boost/compute/image/image3d.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/image3d.html" title="Class image3d">image3d</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.image.image_format_hpp"></a>Header &lt;<a href="../../../../../boost/compute/image/image_format.hpp" target="_top">boost/compute/image/image_format.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/image_format.html" title="Class image_format">image_format</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.image.image_object_hpp"></a>Header &lt;<a href="../../../../../boost/compute/image/image_object.hpp" target="_top">boost/compute/image/image_object.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/image_object.html" title="Class image_object">image_object</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.image.image_sampler_hpp"></a>Header &lt;<a href="../../../../../boost/compute/image/image_sampler.hpp" target="_top">boost/compute/image/image_sampler.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/image_sampler.html" title="Class image_sampler">image_sampler</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.interop.opengl.acquire_hpp"></a>Header &lt;<a href="../../../../../boost/compute/interop/opengl/acquire.hpp" target="_top">boost/compute/interop/opengl/acquire.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="identifier">event</span> <a class="link" href="../boost/compute/opengl_enqueue_acq_idm9416.html" title="Function opengl_enqueue_acquire_gl_objects"><span class="identifier">opengl_enqueue_acquire_gl_objects</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">uint_</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">cl_mem</span> <span class="special">*</span><span class="special">,</span> 
                                            <span class="identifier">command_queue</span> <span class="special">&amp;</span><span class="special">,</span> 
                                            <span class="keyword">const</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="identifier">event</span> <a class="link" href="../boost/compute/opengl_enqueue_rel_idm9433.html" title="Function opengl_enqueue_release_gl_objects"><span class="identifier">opengl_enqueue_release_gl_objects</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">uint_</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">cl_mem</span> <span class="special">*</span><span class="special">,</span> 
                                            <span class="identifier">command_queue</span> <span class="special">&amp;</span><span class="special">,</span> 
                                            <span class="keyword">const</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="identifier">event</span> <a class="link" href="../boost/compute/opengl_enqueue_acq_idm9450.html" title="Function opengl_enqueue_acquire_buffer"><span class="identifier">opengl_enqueue_acquire_buffer</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/compute/opengl_buffer.html" title="Class opengl_buffer">opengl_buffer</a> <span class="special">&amp;</span><span class="special">,</span> 
                                        <span class="identifier">command_queue</span> <span class="special">&amp;</span><span class="special">,</span> 
                                        <span class="keyword">const</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
    <span class="identifier">event</span> <a class="link" href="../boost/compute/opengl_enqueue_rel_idm9466.html" title="Function opengl_enqueue_release_buffer"><span class="identifier">opengl_enqueue_release_buffer</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/compute/opengl_buffer.html" title="Class opengl_buffer">opengl_buffer</a> <span class="special">&amp;</span><span class="special">,</span> 
                                        <span class="identifier">command_queue</span> <span class="special">&amp;</span><span class="special">,</span> 
                                        <span class="keyword">const</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.interop.opengl.context_hpp"></a>Header &lt;<a href="../../../../../boost/compute/interop/opengl/context.hpp" target="_top">boost/compute/interop/opengl/context.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="identifier">context</span> <a class="link" href="../boost/compute/opengl_create_shar_idm9487.html" title="Function opengl_create_shared_context"><span class="identifier">opengl_create_shared_context</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.interop.opengl.opengl_buffer_hpp"></a>Header &lt;<a href="../../../../../boost/compute/interop/opengl/opengl_buffer.hpp" target="_top">boost/compute/interop/opengl/opengl_buffer.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/opengl_buffer.html" title="Class opengl_buffer">opengl_buffer</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.interop.opengl.opengl_renderbuffer_hpp"></a>Header &lt;<a href="../../../../../boost/compute/interop/opengl/opengl_renderbuffer.hpp" target="_top">boost/compute/interop/opengl/opengl_renderbuffer.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/opengl_renderbuffer.html" title="Class opengl_renderbuffer">opengl_renderbuffer</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.interop.opengl.opengl_texture_hpp"></a>Header &lt;<a href="../../../../../boost/compute/interop/opengl/opengl_texture.hpp" target="_top">boost/compute/interop/opengl/opengl_texture.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/opengl_texture.html" title="Class opengl_texture">opengl_texture</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.iterator.buffer_iterator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/iterator/buffer_iterator.hpp" target="_top">boost/compute/iterator/buffer_iterator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/buffer_iterator.html" title="Class template buffer_iterator">buffer_iterator</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/buffer_iterator.html" title="Class template buffer_iterator">buffer_iterator</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <a class="link" href="../boost/compute/make_buffer_iterator.html" title="Function template make_buffer_iterator"><span class="identifier">make_buffer_iterator</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">buffer</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_t</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.iterator.constant_buffer_iterator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/iterator/constant_buffer_iterator.hpp" target="_top">boost/compute/iterator/constant_buffer_iterator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/constant_buffer_iterator.html" title="Class template constant_buffer_iterator">constant_buffer_iterator</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/constant_buffer_iterator.html" title="Class template constant_buffer_iterator">constant_buffer_iterator</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/make_constant_buff_idm9876.html" title="Function template make_constant_buffer_iterator"><span class="identifier">make_constant_buffer_iterator</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">buffer</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_t</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.iterator.constant_iterator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/iterator/constant_iterator.hpp" target="_top">boost/compute/iterator/constant_iterator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/constant_iterator.html" title="Class template constant_iterator">constant_iterator</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/constant_iterator.html" title="Class template constant_iterator">constant_iterator</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <a class="link" href="../boost/compute/make_constant_iterator.html" title="Function template make_constant_iterator"><span class="identifier">make_constant_iterator</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_t</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.iterator.counting_iterator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/iterator/counting_iterator.hpp" target="_top">boost/compute/iterator/counting_iterator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/counting_iterator.html" title="Class template counting_iterator">counting_iterator</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/counting_iterator.html" title="Class template counting_iterator">counting_iterator</a><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <a class="link" href="../boost/compute/make_counting_iterator.html" title="Function template make_counting_iterator"><span class="identifier">make_counting_iterator</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.iterator.discard_iterator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/iterator/discard_iterator.hpp" target="_top">boost/compute/iterator/discard_iterator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/discard_iterator.html" title="Class discard_iterator">discard_iterator</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/is_device_iterato_idm10095.html" title="Struct is_device_iterator&lt;discard_iterator&gt;">is_device_iterator</a><span class="special">&lt;</span><span class="identifier">discard_iterator</span><span class="special">&gt;</span><span class="special">;</span>
    <a class="link" href="../boost/compute/discard_iterator.html" title="Class discard_iterator">discard_iterator</a> <a class="link" href="../boost/compute/make_discard_iterator.html" title="Function make_discard_iterator"><span class="identifier">make_discard_iterator</span></a><span class="special">(</span><span class="identifier">size_t</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.iterator.function_input_iterator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/iterator/function_input_iterator.hpp" target="_top">boost/compute/iterator/function_input_iterator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Function<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/function_input_iterator.html" title="Class template function_input_iterator">function_input_iterator</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Function<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/function_input_iterator.html" title="Class template function_input_iterator">function_input_iterator</a><span class="special">&lt;</span> <span class="identifier">Function</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/make_function_inp_idm10192.html" title="Function template make_function_input_iterator"><span class="identifier">make_function_input_iterator</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Function</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_t</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.iterator.permutation_iterator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/iterator/permutation_iterator.hpp" target="_top">boost/compute/iterator/permutation_iterator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ElementIterator<span class="special">,</span> <span class="keyword">typename</span> IndexIterator<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/compute/permutation_iterator.html" title="Class template permutation_iterator">permutation_iterator</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ElementIterator<span class="special">,</span> <span class="keyword">typename</span> IndexIterator<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/permutation_iterator.html" title="Class template permutation_iterator">permutation_iterator</a><span class="special">&lt;</span> <span class="identifier">ElementIterator</span><span class="special">,</span> <span class="identifier">IndexIterator</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/make_permutation_iterator.html" title="Function template make_permutation_iterator"><span class="identifier">make_permutation_iterator</span></a><span class="special">(</span><span class="identifier">ElementIterator</span><span class="special">,</span> <span class="identifier">IndexIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.iterator.strided_iterator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/iterator/strided_iterator.hpp" target="_top">boost/compute/iterator/strided_iterator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/strided_iterator.html" title="Class template strided_iterator">strided_iterator</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/strided_iterator.html" title="Class template strided_iterator">strided_iterator</a><span class="special">&lt;</span> <span class="identifier">Iterator</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/make_strided_iterator.html" title="Function template make_strided_iterator"><span class="identifier">make_strided_iterator</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> 
                            <span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span> <span class="identifier">Iterator</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">difference_type</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/strided_iterator.html" title="Class template strided_iterator">strided_iterator</a><span class="special">&lt;</span> <span class="identifier">Iterator</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/make_strided_iterator_end.html" title="Function template make_strided_iterator_end"><span class="identifier">make_strided_iterator_end</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> 
                                <span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span> <span class="identifier">Iterator</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">difference_type</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.iterator.transform_iterator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/iterator/transform_iterator.hpp" target="_top">boost/compute/iterator/transform_iterator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> UnaryFunction<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/compute/transform_iterator.html" title="Class template transform_iterator">transform_iterator</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">,</span> <span class="keyword">typename</span> UnaryFunction<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/transform_iterator.html" title="Class template transform_iterator">transform_iterator</a><span class="special">&lt;</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">UnaryFunction</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/make_transform_iterator.html" title="Function template make_transform_iterator"><span class="identifier">make_transform_iterator</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">UnaryFunction</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.iterator.zip_iterator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/iterator/zip_iterator.hpp" target="_top">boost/compute/iterator/zip_iterator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> IteratorTuple<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/zip_iterator.html" title="Class template zip_iterator">zip_iterator</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> IteratorTuple<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/zip_iterator.html" title="Class template zip_iterator">zip_iterator</a><span class="special">&lt;</span> <span class="identifier">IteratorTuple</span> <span class="special">&gt;</span> <a class="link" href="../boost/compute/make_zip_iterator.html" title="Function template make_zip_iterator"><span class="identifier">make_zip_iterator</span></a><span class="special">(</span><span class="identifier">IteratorTuple</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.memory.local_buffer_hpp"></a>Header &lt;<a href="../../../../../boost/compute/memory/local_buffer.hpp" target="_top">boost/compute/memory/local_buffer.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/local_buffer.html" title="Class template local_buffer">local_buffer</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.memory.svm_ptr_hpp"></a>Header &lt;<a href="../../../../../boost/compute/memory/svm_ptr.hpp" target="_top">boost/compute/memory/svm_ptr.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/svm_ptr.html" title="Class template svm_ptr">svm_ptr</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.random.bernoulli_distribution_hpp"></a>Header &lt;<a href="../../../../../boost/compute/random/bernoulli_distribution.hpp" target="_top">boost/compute/random/bernoulli_distribution.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> RealType <span class="special">=</span> <span class="keyword">float</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/bernoulli_distribution.html" title="Class template bernoulli_distribution">bernoulli_distribution</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.random.default_random_engine_hpp"></a>Header &lt;<a href="../../../../../boost/compute/random/default_random_engine.hpp" target="_top">boost/compute/random/default_random_engine.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">typedef</span> <a class="link" href="reference.html#boost.compute.mt19937">mt19937</a> <a name="boost.compute.default_random_engine"></a><span class="identifier">default_random_engine</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.random.discrete_distribution_hpp"></a>Header &lt;<a href="../../../../../boost/compute/random/discrete_distribution.hpp" target="_top">boost/compute/random/discrete_distribution.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> IntType <span class="special">=</span> <span class="identifier">uint_</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/discrete_distribution.html" title="Class template discrete_distribution">discrete_distribution</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.random.linear_congruential_engine_hpp"></a>Header &lt;<a href="../../../../../boost/compute/random/linear_congruential_engine.hpp" target="_top">boost/compute/random/linear_congruential_engine.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T <span class="special">=</span> <span class="identifier">uint_</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/linear_congruential_engine.html" title="Class template linear_congruential_engine">linear_congruential_engine</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.random.mersenne_twister_engine_hpp"></a>Header &lt;<a href="../../../../../boost/compute/random/mersenne_twister_engine.hpp" target="_top">boost/compute/random/mersenne_twister_engine.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/mersenne_twister_engine.html" title="Class template mersenne_twister_engine">mersenne_twister_engine</a><span class="special">;</span>

    <span class="keyword">typedef</span> <a class="link" href="../boost/compute/mersenne_twister_engine.html" title="Class template mersenne_twister_engine">mersenne_twister_engine</a><span class="special">&lt;</span> <span class="identifier">uint_</span> <span class="special">&gt;</span> <a name="boost.compute.mt19937"></a><span class="identifier">mt19937</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.random.normal_distribution_hpp"></a>Header &lt;<a href="../../../../../boost/compute/random/normal_distribution.hpp" target="_top">boost/compute/random/normal_distribution.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> RealType <span class="special">=</span> <span class="keyword">float</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/normal_distribution.html" title="Class template normal_distribution">normal_distribution</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.random.threefry_engine_hpp"></a>Header &lt;<a href="../../../../../boost/compute/random/threefry_engine.hpp" target="_top">boost/compute/random/threefry_engine.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T <span class="special">=</span> <span class="identifier">uint_</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/threefry_engine.html" title="Class template threefry_engine">threefry_engine</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.random.uniform_int_distribution_hpp"></a>Header &lt;<a href="../../../../../boost/compute/random/uniform_int_distribution.hpp" target="_top">boost/compute/random/uniform_int_distribution.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> IntType <span class="special">=</span> <span class="identifier">uint_</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/uniform_int_distribution.html" title="Class template uniform_int_distribution">uniform_int_distribution</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.random.uniform_real_distribution_hpp"></a>Header &lt;<a href="../../../../../boost/compute/random/uniform_real_distribution.hpp" target="_top">boost/compute/random/uniform_real_distribution.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> RealType <span class="special">=</span> <span class="keyword">float</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/uniform_real_distribution.html" title="Class template uniform_real_distribution">uniform_real_distribution</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.type_traits.common_type_hpp"></a>Header &lt;<a href="../../../../../boost/compute/type_traits/common_type.hpp" target="_top">boost/compute/type_traits/common_type.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.type_traits.is_device_iterator_hpp"></a>Header &lt;<a href="../../../../../boost/compute/type_traits/is_device_iterator.hpp" target="_top">boost/compute/type_traits/is_device_iterator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/is_device_iterator.html" title="Struct template is_device_iterator">is_device_iterator</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.type_traits.is_fundamental_hpp"></a>Header &lt;<a href="../../../../../boost/compute/type_traits/is_fundamental.hpp" target="_top">boost/compute/type_traits/is_fundamental.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/is_fundamental.html" title="Struct template is_fundamental">is_fundamental</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.type_traits.is_vector_type_hpp"></a>Header &lt;<a href="../../../../../boost/compute/type_traits/is_vector_type.hpp" target="_top">boost/compute/type_traits/is_vector_type.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/is_vector_type.html" title="Struct template is_vector_type">is_vector_type</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.type_traits.make_vector_type_hpp"></a>Header &lt;<a href="../../../../../boost/compute/type_traits/make_vector_type.hpp" target="_top">boost/compute/type_traits/make_vector_type.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Scalar<span class="special">,</span> <span class="identifier">size_t</span> Size<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/make_vector_type.html" title="Struct template make_vector_type">make_vector_type</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.type_traits.result_of_hpp"></a>Header &lt;<a href="../../../../../boost/compute/type_traits/result_of.hpp" target="_top">boost/compute/type_traits/result_of.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Signature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/result_of.html" title="Struct template result_of">result_of</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.type_traits.scalar_type_hpp"></a>Header &lt;<a href="../../../../../boost/compute/type_traits/scalar_type.hpp" target="_top">boost/compute/type_traits/scalar_type.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Vector<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/scalar_type.html" title="Struct template scalar_type">scalar_type</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.type_traits.type_definition_hpp"></a>Header &lt;<a href="../../../../../boost/compute/type_traits/type_definition.hpp" target="_top">boost/compute/type_traits/type_definition.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a class="link" href="../boost/compute/type_definition.html" title="Function template type_definition"><span class="identifier">type_definition</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.type_traits.type_name_hpp"></a>Header &lt;<a href="../../../../../boost/compute/type_traits/type_name.hpp" target="_top">boost/compute/type_traits/type_name.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis">

<a class="link" href="../BOOST_COMPUTE_TYPE_NAME.html" title="Macro BOOST_COMPUTE_TYPE_NAME">BOOST_COMPUTE_TYPE_NAME</a>(type, name)</pre>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span> <a class="link" href="../boost/compute/type_name.html" title="Function template type_name"><span class="identifier">type_name</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.type_traits.vector_size_hpp"></a>Header &lt;<a href="../../../../../boost/compute/type_traits/vector_size.hpp" target="_top">boost/compute/type_traits/vector_size.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/compute/vector_size.html" title="Struct template vector_size">vector_size</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.utility.dim_hpp"></a>Header &lt;<a href="../../../../../boost/compute/utility/dim.hpp" target="_top">boost/compute/utility/dim.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> <a class="link" href="../boost/compute/extents.html" title="Class template extents">extents</a><span class="special">&lt;</span> <span class="keyword">sizeof</span><span class="special">...</span><span class="special">(</span><span class="identifier">Args</span><span class="special">)</span><span class="special">&gt;</span> <a class="link" href="../boost/compute/dim.html" title="Function template dim"><span class="identifier">dim</span></a><span class="special">(</span><span class="identifier">Args</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.utility.extents_hpp"></a>Header &lt;<a href="../../../../../boost/compute/utility/extents.hpp" target="_top">boost/compute/utility/extents.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="identifier">size_t</span> N<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/compute/extents.html" title="Class template extents">extents</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.utility.invoke_hpp"></a>Header &lt;<a href="../../../../../boost/compute/utility/invoke.hpp" target="_top">boost/compute/utility/invoke.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis">

<a class="link" href="../BOOST_COMPUTE_DET_idm11660.html" title="Macro BOOST_COMPUTE_DETAIL_INVOKE_ARG">BOOST_COMPUTE_DETAIL_INVOKE_ARG</a>(z, n, unused)
<a class="link" href="../BOOST_COMPUTE_DET_idm11664.html" title="Macro BOOST_COMPUTE_DETAIL_INVOKE_ADD_ARG">BOOST_COMPUTE_DETAIL_INVOKE_ADD_ARG</a>(z, n, unused)
<a class="link" href="../BOOST_COMPUTE_DET_idm11668.html" title="Macro BOOST_COMPUTE_DETAIL_DEFINE_INVOKE">BOOST_COMPUTE_DETAIL_DEFINE_INVOKE</a>(z, n, unused)</pre>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Function<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> 
      <a class="link" href="../boost/compute/result_of.html" title="Struct template result_of">result_of</a><span class="special">&lt;</span> <span class="identifier">Function</span><span class="special">(</span><span class="identifier">Args</span><span class="special">...</span><span class="special">)</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> 
      <a class="link" href="../boost/compute/invoke.html" title="Function template invoke"><span class="identifier">invoke</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Function</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">command_queue</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Args</span> <span class="special">&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.utility.program_cache_hpp"></a>Header &lt;<a href="../../../../../boost/compute/utility/program_cache.hpp" target="_top">boost/compute/utility/program_cache.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/program_cache.html" title="Class program_cache">program_cache</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.utility.source_hpp"></a>Header &lt;<a href="../../../../../boost/compute/utility/source.hpp" target="_top">boost/compute/utility/source.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis">

<a class="link" href="../BOOST_COMPUTE_STR_idm11779.html" title="Macro BOOST_COMPUTE_STRINGIZE_SOURCE">BOOST_COMPUTE_STRINGIZE_SOURCE</a>(source)</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.compute.utility.wait_list_hpp"></a>Header &lt;<a href="../../../../../boost/compute/utility/wait_list.hpp" target="_top">boost/compute/utility/wait_list.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">compute</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/compute/wait_list.html" title="Class wait_list">wait_list</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2013, 2014 Kyle Lutz<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="platforms_and_compilers.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost/compute/accumulate.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
