<?xml version="1.0" standalone="yes"?>
<library-reference id="header_reference"><title>Header Reference</title><header name="boost/compute/algorithm/accumulate.hpp">
<namespace name="boost">
<namespace name="compute">










































































































<overloaded-function name="accumulate"><signature><type>T</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="BinaryFunction"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>first element in the input range </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>last element in the input range </para></description></parameter><parameter name="init"><paramtype>T</paramtype><description><para>initial value </para></description></parameter><parameter name="function"><paramtype>BinaryFunction</paramtype><description><para>binary reduction function </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>T</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="init"><paramtype>T</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Returns the result of applying <computeroutput>function</computeroutput> to the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) and <computeroutput>init</computeroutput>.</para><para>If no function is specified, <computeroutput>plus</computeroutput> will be used.</para><para>

In specific situations the call to <computeroutput>accumulate()</computeroutput> can be automatically optimized to a call to the more efficient <computeroutput>reduce()</computeroutput> algorithm. This occurs when the binary reduction function is recognized as associative (such as the <computeroutput>plus&lt;int&gt;</computeroutput> function).</para><para>Note that because floating-point addition is not associative, calling <computeroutput>accumulate()</computeroutput> with <computeroutput>plus&lt;float&gt;</computeroutput> results in a less efficient serial reduction algorithm being executed. If a slight loss in precision is acceptable, the more efficient parallel <computeroutput>reduce()</computeroutput> algorithm should be used instead.</para><para>For example: <programlisting language="c++">// with vec = boost::compute::vector&lt;int&gt;
accumulate(vec.begin(), vec.end(), 0, plus&lt;int&gt;());   // fast
reduce(vec.begin(), vec.end(), &amp;result, plus&lt;int&gt;()); // fast

// with vec = boost::compute::vector&lt;float&gt;
accumulate(vec.begin(), vec.end(), 0, plus&lt;float&gt;());   // slow
reduce(vec.begin(), vec.end(), &amp;result, plus&lt;float&gt;()); // fast
</programlisting></para><para>Space complexity: \Omega(1)<sbr/>
 Space complexity when optimized to <computeroutput>reduce()</computeroutput>: \Omega(n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>reduce() </para>
</para>
</para></description><returns><para>the accumulated result value</para>
</returns></overloaded-function>


























</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/adjacent_difference.hpp">
<namespace name="boost">
<namespace name="compute">








































































































<overloaded-function name="adjacent_difference"><signature><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="BinaryFunction"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>first element in the input range </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>last element in the input range </para></description></parameter><parameter name="result"><paramtype>OutputIterator</paramtype><description><para>first element in the output range </para></description></parameter><parameter name="op"><paramtype>BinaryFunction</paramtype><description><para>binary difference function </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Stores the difference of each pair of consecutive values in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) to the range beginning at <computeroutput>result</computeroutput>. If <computeroutput>op</computeroutput> is not provided, <computeroutput>minus&lt;T&gt;</computeroutput> is used.</para><para>

Space complexity: \Omega(1)<sbr/>
 Space complexity when <computeroutput>result</computeroutput> == <computeroutput>first:</computeroutput> \Omega(n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>adjacent_find() </para>
</para>
</para></description><returns><para><computeroutput>OutputIterator</computeroutput> to the end of the result range</para>
</returns></overloaded-function>




























</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/adjacent_find.hpp">
<namespace name="boost">
<namespace name="compute">






































































































<overloaded-function name="adjacent_find"><signature><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="Compare"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>first element in the range to search </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>last element in the range to search </para></description></parameter><parameter name="compare"><paramtype>Compare</paramtype><description><para>binary comparison function </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Searches the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) for two identical adjacent elements and returns an iterator pointing to the first.</para><para>

Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>find(), adjacent_difference() </para>
</para>
</para></description><returns><para><computeroutput>InputIteratorm</computeroutput> to the first element which compares equal to the following element. If none are equal, returns <computeroutput>last</computeroutput>.</para>
</returns></overloaded-function>






























</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/all_of.hpp">
<namespace name="boost">
<namespace name="compute">





































































































<function name="all_of"><type>bool</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="UnaryPredicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="predicate"><paramtype>UnaryPredicate</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Returns <computeroutput>true</computeroutput> if <computeroutput>predicate</computeroutput> returns <computeroutput>true</computeroutput> for all of the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>any_of(), none_of() </para>
</para>
</para></description></function>































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/any_of.hpp">
<namespace name="boost">
<namespace name="compute">




































































































<function name="any_of"><type>bool</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="UnaryPredicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="predicate"><paramtype>UnaryPredicate</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Returns <computeroutput>true</computeroutput> if <computeroutput>predicate</computeroutput> returns <computeroutput>true</computeroutput> for any of the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>For example, to test if a vector contains any negative values:</para><para><programlisting language="c++"/> Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>all_of(), none_of() </para>
</para>
</para></description></function>
































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/binary_search.hpp">
<namespace name="boost">
<namespace name="compute">



































































































<function name="binary_search"><type>bool</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Returns <computeroutput>true</computeroutput> if <computeroutput>value</computeroutput> is in the sorted range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>Space complexity: \Omega(1) </para></description></function>

































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/copy.hpp">
<namespace name="boost">
<namespace name="compute">

































































































<function name="copy"><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>first element in the range to copy </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>last element in the range to copy </para></description></parameter><parameter name="result"><paramtype>OutputIterator</paramtype><description><para>first element in the result range </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter><parameter name="events"><paramtype>const <classname>wait_list</classname> &amp;</paramtype><default><classname alt="boost::compute::wait_list">wait_list</classname>()</default></parameter><description><para>Copies the values in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) to the range beginning at <computeroutput>result</computeroutput>.</para><para>The generic copy() function can be used for a variety of data transfer tasks and provides a standard interface to the following OpenCL functions:</para><para><itemizedlist>
<listitem><para><computeroutput>clEnqueueReadBuffer()</computeroutput> </para>
</listitem>
<listitem><para><computeroutput>clEnqueueWriteBuffer()</computeroutput> </para>
</listitem>
<listitem><para><computeroutput>clEnqueueCopyBuffer()</computeroutput> </para>
</listitem>
</itemizedlist>
Unlike the aforementioned OpenCL functions, copy() will also work with non-contiguous data-structures (e.g. <computeroutput>std::list&lt;T&gt;</computeroutput>) as well as with "fancy" iterators (e.g. <classname alt="boost::compute::transform_iterator">transform_iterator</classname>).</para><para>

For example, to copy an array of <computeroutput>int</computeroutput> values on the host to a vector on the device: <programlisting language="c++">// array on the host
int data[] = { 1, 2, 3, 4 };

// vector on the device
boost::compute::vector&lt;int&gt; vec(4, context);

// copy values to the device vector
boost::compute::copy(data, data + 4, vec.begin(), queue);
</programlisting></para><para>The copy algorithm can also be used with standard containers such as <computeroutput>std::vector&lt;T&gt;</computeroutput>: <programlisting language="c++">std::vector&lt;int&gt; host_vector = ...
boost::compute::vector&lt;int&gt; device_vector = ...

// copy from the host to the device
boost::compute::copy(
    host_vector.begin(), host_vector.end(), device_vector.begin(), queue
);

// copy from the device to the host
boost::compute::copy(
    device_vector.begin(), device_vector.end(), host_vector.begin(), queue
);
</programlisting></para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>copy_n(), copy_if(), copy_async() </para>
</para>
</para></description><returns><para><computeroutput>OutputIterator</computeroutput> to the end of the result range</para>
</returns></function>
<function name="copy_async"><type><classname>future</classname>&lt; OutputIterator &gt;</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><parameter name="events"><paramtype>const <classname>wait_list</classname> &amp;</paramtype><default><classname alt="boost::compute::wait_list">wait_list</classname>()</default></parameter><description><para>Copies the values in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) to the range beginning at <computeroutput>result</computeroutput>. The copy is performed asynchronously.</para><para><para><emphasis role="bold">See Also:</emphasis><para>copy() </para>
</para>
</para></description></function>


































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/copy_if.hpp">
<namespace name="boost">
<namespace name="compute">
































































































<function name="copy_if"><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="Predicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="predicate"><paramtype>Predicate</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Copies each element in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) for which <computeroutput>predicate</computeroutput> returns <computeroutput>true</computeroutput> to the range beginning at <computeroutput>result</computeroutput>.</para><para>Space complexity: \Omega(2n) </para></description></function>




































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/copy_n.hpp">
<namespace name="boost">
<namespace name="compute">































































































<function name="copy_n"><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="Size"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="count"><paramtype>Size</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><parameter name="events"><paramtype>const <classname>wait_list</classname> &amp;</paramtype><default><classname alt="boost::compute::wait_list">wait_list</classname>()</default></parameter><description><para>Copies <computeroutput>count</computeroutput> elements from <computeroutput>first</computeroutput> to <computeroutput>result</computeroutput>.</para><para>For example, to copy four values from the host to the device: <programlisting language="c++">// values on the host and vector on the device
float values[4] = { 1.f, 2.f, 3.f, 4.f };
boost::compute::vector&lt;float&gt; vec(4, context);

// copy from the host to the device
boost::compute::copy_n(values, 4, vec.begin(), queue);
</programlisting></para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>copy() </para>
</para>
</para></description></function>





































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/count.hpp">
<namespace name="boost">
<namespace name="compute">






























































































<function name="count"><type>size_t</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Returns the number of occurrences of <computeroutput>value</computeroutput> in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>Space complexity on CPUs: \Omega(1)<sbr/>
 Space complexity on GPUs: \Omega(n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>count_if() </para>
</para>
</para></description></function>






































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/count_if.hpp">
<namespace name="boost">
<namespace name="compute">





























































































<function name="count_if"><type>size_t</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="Predicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="predicate"><paramtype>Predicate</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Returns the number of elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) for which <computeroutput>predicate</computeroutput> returns <computeroutput>true</computeroutput>.</para><para>Space complexity on CPUs: \Omega(1)<sbr/>
 Space complexity on GPUs: \Omega(n) </para></description></function>







































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/equal.hpp">
<namespace name="boost">
<namespace name="compute">



























































































<overloaded-function name="equal"><signature><type>bool</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><signature><type>bool</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype></parameter><parameter name="last2"><paramtype>InputIterator2</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Returns <computeroutput>true</computeroutput> if the range [<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>) and the range beginning at <computeroutput>first2</computeroutput> are equal.</para><para>Space complexity: \Omega(1) </para></description></overloaded-function>









































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/equal_range.hpp">
<namespace name="boost">
<namespace name="compute">


























































































<function name="equal_range"><type>std::pair&lt; InputIterator, InputIterator &gt;</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Returns a pair of iterators containing the range of values equal to <computeroutput>value</computeroutput> in the sorted range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>Space complexity: \Omega(1) </para></description></function>










































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/exclusive_scan.hpp">
<namespace name="boost">
<namespace name="compute">























































































<overloaded-function name="exclusive_scan"><signature><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="BinaryOperator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>first element in the range to scan </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>last element in the range to scan </para></description></parameter><parameter name="result"><paramtype>OutputIterator</paramtype><description><para>first element in the result range </para></description></parameter><parameter name="init"><paramtype>T</paramtype><description><para>value used to initialize the scan sequence </para></description></parameter><parameter name="binary_op"><paramtype>BinaryOperator</paramtype><description><para>associative binary operator </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="init"><paramtype>T</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><signature><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Performs an exclusive scan of the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) and stores the results in the range beginning at <computeroutput>result</computeroutput>.</para><para>Each element in the output is assigned to the sum of all the previous values in the input.</para><para>

The default operation is to add the elements up.</para><para><programlisting language="c++"/> But different associative operation can be specified as <computeroutput>binary_op</computeroutput> instead (e.g., multiplication, maximum, minimum). Also value used to initialized the scan sequence can be specified.</para><para><programlisting language="c++"/> Space complexity on GPUs: \Omega(n)<sbr/>
 Space complexity on GPUs when <computeroutput>first</computeroutput> == <computeroutput>result:</computeroutput> \Omega(2n)<sbr/>
 Space complexity on CPUs: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>inclusive_scan() </para>
</para>
</para></description><returns><para><computeroutput>OutputIterator</computeroutput> to the end of the result range</para>
</returns></overloaded-function>













































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/fill.hpp">
<namespace name="boost">
<namespace name="compute">





















































































<function name="fill"><type>void</type><template>
          <template-type-parameter name="BufferIterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>BufferIterator</paramtype><description><para>first element in the range to fill </para></description></parameter><parameter name="last"><paramtype>BufferIterator</paramtype><description><para>last element in the range to fill </para></description></parameter><parameter name="value"><paramtype>const T &amp;</paramtype><description><para>value to copy to each element </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter><description><para>Fills the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) with <computeroutput>value</computeroutput>.</para><para>
For example, to fill a vector on the device with sevens: <programlisting language="c++">// vector on the device
boost::compute::vector&lt;int&gt; vec(10, context);

// fill vector with sevens
boost::compute::fill(vec.begin(), vec.end(), 7, queue);
</programlisting></para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>boost::compute::fill_n() </para>
</para>
</para></description></function>
<function name="fill_async"><type><classname>future</classname>&lt; void &gt;</type><template>
          <template-type-parameter name="BufferIterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>BufferIterator</paramtype></parameter><parameter name="last"><paramtype>BufferIterator</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></function>














































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/fill_n.hpp">
<namespace name="boost">
<namespace name="compute">




















































































<function name="fill_n"><type>void</type><template>
          <template-type-parameter name="BufferIterator"/>
          <template-type-parameter name="Size"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>BufferIterator</paramtype></parameter><parameter name="count"><paramtype>Size</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Fills the range [<computeroutput>first</computeroutput>, <computeroutput>first</computeroutput> + count) with <computeroutput>value</computeroutput>.</para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>fill() </para>
</para>
</para></description></function>
















































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/find.hpp">
<namespace name="boost">
<namespace name="compute">



















































































<function name="find"><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Returns an iterator pointing to the first element in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) that equals <computeroutput>value</computeroutput>.</para><para>Space complexity: \Omega(1) </para></description></function>

















































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/find_end.hpp">
<namespace name="boost">
<namespace name="compute">


















































































<function name="find_end"><type>TextIterator</type><template>
          <template-type-parameter name="TextIterator"/>
          <template-type-parameter name="PatternIterator"/>
        </template><parameter name="t_first"><paramtype>TextIterator</paramtype><description><para>Iterator pointing to start of text </para></description></parameter><parameter name="t_last"><paramtype>TextIterator</paramtype><description><para>Iterator pointing to end of text </para></description></parameter><parameter name="p_first"><paramtype>PatternIterator</paramtype><description><para>Iterator pointing to start of pattern </para></description></parameter><parameter name="p_last"><paramtype>PatternIterator</paramtype><description><para>Iterator pointing to end of pattern </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>Queue on which to execute</para></description></parameter><purpose>Substring matching algorithm. </purpose><description><para>Searches for the last match of the pattern [p_first, p_last) in text [t_first, t_last). 

Space complexity: \Omega(n) </para></description><returns><para>Iterator pointing to beginning of last occurence</para>
</returns></function>


















































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/find_if.hpp">
<namespace name="boost">
<namespace name="compute">

















































































<function name="find_if"><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="UnaryPredicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="predicate"><paramtype>UnaryPredicate</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Returns an iterator pointing to the first element in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) for which <computeroutput>predicate</computeroutput> returns <computeroutput>true</computeroutput>.</para><para>Space complexity: \Omega(1) </para></description></function>



















































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/find_if_not.hpp">
<namespace name="boost">
<namespace name="compute">
















































































<function name="find_if_not"><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="UnaryPredicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="predicate"><paramtype>UnaryPredicate</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Returns an iterator pointing to the first element in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) for which <computeroutput>predicate</computeroutput> returns <computeroutput>false</computeroutput>.</para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>find_if() </para>
</para>
</para></description></function>




















































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/for_each.hpp">
<namespace name="boost">
<namespace name="compute">















































































<function name="for_each"><type>UnaryFunction</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="UnaryFunction"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="function"><paramtype>UnaryFunction</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Calls <computeroutput>function</computeroutput> on each element in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>transform() </para>
</para>
</para></description></function>





















































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/for_each_n.hpp">
<namespace name="boost">
<namespace name="compute">














































































<function name="for_each_n"><type>UnaryFunction</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="Size"/>
          <template-type-parameter name="UnaryFunction"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="count"><paramtype>Size</paramtype></parameter><parameter name="function"><paramtype>UnaryFunction</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Calls <computeroutput>function</computeroutput> on each element in the range [<computeroutput>first</computeroutput>, <computeroutput>first</computeroutput> <computeroutput>+</computeroutput> <computeroutput>count</computeroutput>).</para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>for_each() </para>
</para>
</para></description></function>






















































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/gather.hpp">
<namespace name="boost">
<namespace name="compute">













































































<function name="gather"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="MapIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>MapIterator</paramtype></parameter><parameter name="last"><paramtype>MapIterator</paramtype></parameter><parameter name="input"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Copies the elements using the indices from the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) to the range beginning at <computeroutput>result</computeroutput> using the input values from the range beginning at <computeroutput>input</computeroutput>.</para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>scatter() </para>
</para>
</para></description></function>























































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/generate.hpp">
<namespace name="boost">
<namespace name="compute">












































































<function name="generate"><type>void</type><template>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="Generator"/>
        </template><parameter name="first"><paramtype>OutputIterator</paramtype></parameter><parameter name="last"><paramtype>OutputIterator</paramtype></parameter><parameter name="generator"><paramtype>Generator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Stores the result of <computeroutput>generator</computeroutput> for each element in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>Space complexity: \Omega(1) </para></description></function>
























































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/generate_n.hpp">
<namespace name="boost">
<namespace name="compute">











































































<function name="generate_n"><type>void</type><template>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="Size"/>
          <template-type-parameter name="Generator"/>
        </template><parameter name="first"><paramtype>OutputIterator</paramtype></parameter><parameter name="count"><paramtype>Size</paramtype></parameter><parameter name="generator"><paramtype>Generator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Stores the result of <computeroutput>generator</computeroutput> for each element in the range [<computeroutput>first</computeroutput>, <computeroutput>first</computeroutput> + <computeroutput>count</computeroutput>).</para><para>Space complexity: \Omega(1) </para></description></function>

























































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/includes.hpp">
<namespace name="boost">
<namespace name="compute">










































































<function name="includes"><type>bool</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype><description><para>Iterator pointing to start of first set </para></description></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype><description><para>Iterator pointing to end of first set </para></description></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype><description><para>Iterator pointing to start of second set </para></description></parameter><parameter name="last2"><paramtype>InputIterator2</paramtype><description><para>Iterator pointing to end of second set </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>Queue on which to execute</para></description></parameter><purpose>Includes algorithm. </purpose><description><para>Finds if the sorted range [first1, last1) includes the sorted range [first2, last2). In other words, it checks if [first1, last1) is a superset of [first2, last2).</para><para>

Space complexity: \Omega(distance(<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>) + distance(<computeroutput>first2</computeroutput>, <computeroutput>last2</computeroutput>)) </para></description><returns><para>True, if [first1, last1) includes [first2, last2). False otherwise.</para>
</returns></function>


























































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/inclusive_scan.hpp">
<namespace name="boost">
<namespace name="compute">








































































<overloaded-function name="inclusive_scan"><signature><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="BinaryOperator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>first element in the range to scan </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>last element in the range to scan </para></description></parameter><parameter name="result"><paramtype>OutputIterator</paramtype><description><para>first element in the result range </para></description></parameter><parameter name="binary_op"><paramtype>BinaryOperator</paramtype><description><para>associative binary operator </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Performs an inclusive scan of the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) and stores the results in the range beginning at <computeroutput>result</computeroutput>.</para><para>Each element in the output is assigned to the sum of the current value in the input with the sum of every previous value in the input.</para><para>

The default operation is to add the elements up.</para><para><programlisting language="c++"/> But different associative operation can be specified as <computeroutput>binary_op</computeroutput> instead (e.g., multiplication, maximum, minimum).</para><para><programlisting language="c++"/> Space complexity on GPUs: \Omega(n)<sbr/>
 Space complexity on GPUs when <computeroutput>first</computeroutput> == <computeroutput>result:</computeroutput> \Omega(2n)<sbr/>
 Space complexity on CPUs: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>exclusive_scan() </para>
</para>
</para></description><returns><para><computeroutput>OutputIterator</computeroutput> to the end of the result range</para>
</returns></overloaded-function>




























































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/inner_product.hpp">
<namespace name="boost">
<namespace name="compute">






































































<overloaded-function name="inner_product"><signature><type>T</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype></parameter><parameter name="init"><paramtype>T</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><signature><type>T</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="BinaryAccumulateFunction"/>
          <template-type-parameter name="BinaryTransformFunction"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype></parameter><parameter name="init"><paramtype>T</paramtype></parameter><parameter name="accumulate_function"><paramtype>BinaryAccumulateFunction</paramtype></parameter><parameter name="transform_function"><paramtype>BinaryTransformFunction</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Returns the inner product of the elements in the range [<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>) with the elements in the range beginning at <computeroutput>first2</computeroutput>.</para><para>Space complexity: \Omega(1)<sbr/>
 Space complexity when binary operator is recognized as associative: \Omega(n) </para></description></overloaded-function>






























































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/inplace_merge.hpp">
<namespace name="boost">
<namespace name="compute">





































































<function name="inplace_merge"><type>void</type><template>
          <template-type-parameter name="Iterator"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype></parameter><parameter name="middle"><paramtype>Iterator</paramtype></parameter><parameter name="last"><paramtype>Iterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Merges the sorted values in the range [<computeroutput>first</computeroutput>, <computeroutput>middle</computeroutput>) with the sorted values in the range [<computeroutput>middle</computeroutput>, <computeroutput>last</computeroutput>) in-place.</para><para>Space complexity: \Omega(n) </para></description></function>































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/iota.hpp">
<namespace name="boost">
<namespace name="compute">




































































<function name="iota"><type>void</type><template>
          <template-type-parameter name="BufferIterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>BufferIterator</paramtype></parameter><parameter name="last"><paramtype>BufferIterator</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Fills the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) with sequential values starting at <computeroutput>value</computeroutput>.</para><para>For example, the following code: <programlisting language="c++"/> Will fill <computeroutput>vec</computeroutput> with the values (<computeroutput>0</computeroutput>, <computeroutput>1</computeroutput>, <computeroutput>2</computeroutput>, <computeroutput/>...).</para><para>Space complexity: \Omega(1) </para></description></function>
































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/is_partitioned.hpp">
<namespace name="boost">
<namespace name="compute">



































































<function name="is_partitioned"><type>bool</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="UnaryPredicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="predicate"><paramtype>UnaryPredicate</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Returns <computeroutput>true</computeroutput> if the values in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) are partitioned according to <computeroutput>predicate</computeroutput>.</para><para>Space complexity: \Omega(1) </para></description></function>

































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/is_permutation.hpp">
<namespace name="boost">
<namespace name="compute">


































































<function name="is_permutation"><type>bool</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype><description><para>Iterator pointing to start of first range </para></description></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype><description><para>Iterator pointing to end of first range </para></description></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype><description><para>Iterator pointing to start of second range </para></description></parameter><parameter name="last2"><paramtype>InputIterator2</paramtype><description><para>Iterator pointing to end of second range </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>Queue on which to execute</para></description></parameter><purpose>Permutation checking algorithm. </purpose><description><para>Checks if the range [first1, last1) can be permuted into the range [first2, last2) 

Space complexity: \Omega(distance(<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>) + distance(<computeroutput>first2</computeroutput>, <computeroutput>last2</computeroutput>)) </para></description><returns><para>True, if it can be permuted. False, otherwise.</para>
</returns></function>


































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/is_sorted.hpp">
<namespace name="boost">
<namespace name="compute">
































































<overloaded-function name="is_sorted"><signature><type>bool</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="Compare"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>first element in the range to check </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>last element in the range to check </para></description></parameter><parameter name="compare"><paramtype>Compare</paramtype><description><para>comparison function (by default <computeroutput>less</computeroutput>) </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>bool</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Returns <computeroutput>true</computeroutput> if the values in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) are in sorted order.</para><para>

Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>sort() </para>
</para>
</para></description><returns><para><computeroutput>true</computeroutput> if the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) is sorted</para>
</returns></overloaded-function>




































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/lexicographical_compare.hpp">
<namespace name="boost">
<namespace name="compute">































































<function name="lexicographical_compare"><type>bool</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype></parameter><parameter name="last2"><paramtype>InputIterator2</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Checks if the first range [first1, last1) is lexicographically less than the second range [first2, last2).</para><para>Space complexity: \Omega(max(distance(<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>), distance(<computeroutput>first2</computeroutput>, <computeroutput>last2</computeroutput>))) </para></description></function>





































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/lower_bound.hpp">
<namespace name="boost">
<namespace name="compute">






























































<function name="lower_bound"><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Returns an iterator pointing to the first element in the sorted range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) that is not less than <computeroutput>value</computeroutput>.</para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>upper_bound() </para>
</para>
</para></description></function>






































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/max_element.hpp">
<namespace name="boost">
<namespace name="compute">




























































<overloaded-function name="max_element"><signature><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="Compare"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>first element in the input range </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>last element in the input range </para></description></parameter><parameter name="compare"><paramtype>Compare</paramtype><description><para>comparison function object which returns true if the first argument is less than (i.e. is ordered before) the second. </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Returns an iterator pointing to the element in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) with the maximum value.</para><para>
For example, to find <computeroutput>int2</computeroutput> value with maximum first component in given vector: <programlisting language="c++">// comparison function object
BOOST_COMPUTE_FUNCTION(bool, compare_first, (const int2_ &amp;a, const int2_ &amp;b),
{
    return a.x &lt; b.x;
});

// create vector
boost::compute::vector&lt;uint2_&gt; data = ...

boost::compute::vector&lt;uint2_&gt;::iterator max =
    boost::compute::max_element(data.begin(), data.end(), compare_first, queue);
</programlisting></para><para>Space complexity on CPUs: \Omega(1)<sbr/>
 Space complexity on GPUs: \Omega(N)</para><para><para><emphasis role="bold">See Also:</emphasis><para>min_element() </para>
</para>
</para></description></overloaded-function>








































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/merge.hpp">
<namespace name="boost">
<namespace name="compute">


























































<overloaded-function name="merge"><signature><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="Compare"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype><description><para>first element in the first range to merge </para></description></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype><description><para>last element in the first range to merge </para></description></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype><description><para>first element in the second range to merge </para></description></parameter><parameter name="last2"><paramtype>InputIterator2</paramtype><description><para>last element in the second range to merge </para></description></parameter><parameter name="result"><paramtype>OutputIterator</paramtype><description><para>first element in the result range </para></description></parameter><parameter name="comp"><paramtype>Compare</paramtype><description><para>comparison function (by default <computeroutput>less</computeroutput>) </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype></parameter><parameter name="last2"><paramtype>InputIterator2</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Merges the sorted values in the range [<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>) with the sorted values in the range [<computeroutput>first2</computeroutput>, last2) and stores the result in the range beginning at <computeroutput>result</computeroutput>. Values are compared using the <computeroutput>comp</computeroutput> function. If no comparision function is given, <computeroutput>less</computeroutput> is used.</para><para>

Space complexity: \Omega(distance(<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>) + distance(<computeroutput>first2</computeroutput>, <computeroutput>last2</computeroutput>))</para><para><para><emphasis role="bold">See Also:</emphasis><para>inplace_merge() </para>
</para>
</para></description><returns><para><computeroutput>OutputIterator</computeroutput> to the end of the result range</para>
</returns></overloaded-function>










































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/min_element.hpp">
<namespace name="boost">
<namespace name="compute">
























































<overloaded-function name="min_element"><signature><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="Compare"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>first element in the input range </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>last element in the input range </para></description></parameter><parameter name="compare"><paramtype>Compare</paramtype><description><para>comparison function object which returns true if the first argument is less than (i.e. is ordered before) the second. </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Returns an iterator pointing to the element in range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) with the minimum value.</para><para>
For example, to find <computeroutput>int2</computeroutput> value with minimum first component in given vector: <programlisting language="c++">// comparison function object
BOOST_COMPUTE_FUNCTION(bool, compare_first, (const int2_ &amp;a, const int2_ &amp;b),
{
    return a.x &lt; b.x;
});

// create vector
boost::compute::vector&lt;uint2_&gt; data = ...

boost::compute::vector&lt;uint2_&gt;::iterator min =
    boost::compute::min_element(data.begin(), data.end(), compare_first, queue);
</programlisting></para><para>Space complexity on CPUs: \Omega(1)<sbr/>
 Space complexity on GPUs: \Omega(N)</para><para><para><emphasis role="bold">See Also:</emphasis><para>max_element() </para>
</para>
</para></description></overloaded-function>












































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/minmax_element.hpp">
<namespace name="boost">
<namespace name="compute">






















































<overloaded-function name="minmax_element"><signature><type>std::pair&lt; InputIterator, InputIterator &gt;</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="Compare"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>first element in the input range </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>last element in the input range </para></description></parameter><parameter name="compare"><paramtype>Compare</paramtype><description><para>comparison function object which returns true if the first argument is less than (i.e. is ordered before) the second. </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>std::pair&lt; InputIterator, InputIterator &gt;</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Returns a pair of iterators with the first pointing to the minimum element and the second pointing to the maximum element in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>
Space complexity on CPUs: \Omega(1)<sbr/>
 Space complexity on GPUs: \Omega(N)</para><para><para><emphasis role="bold">See Also:</emphasis><para>max_element(), min_element() </para>
</para>
</para></description></overloaded-function>














































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/mismatch.hpp">
<namespace name="boost">
<namespace name="compute">




















































<overloaded-function name="mismatch"><signature><type>std::pair&lt; InputIterator1, InputIterator2 &gt;</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><signature><type>std::pair&lt; InputIterator1, InputIterator2 &gt;</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype></parameter><parameter name="last2"><paramtype>InputIterator2</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Returns a pair of iterators pointing to the first position where the range [<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>) and the range starting at <computeroutput>first2</computeroutput> differ.</para><para>Space complexity: \Omega(1) </para></description></overloaded-function>
















































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/next_permutation.hpp">
<namespace name="boost">
<namespace name="compute">



















































<function name="next_permutation"><type>bool</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>Iterator pointing to start of range </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>Iterator pointing to end of range </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>Queue on which to execute</para></description></parameter><purpose>Permutation generating algorithm. </purpose><description><para>Transforms the range [first, last) into the next permutation from the set of all permutations arranged in lexicographic order 

Space complexity: \Omega(1) </para></description><returns><para>Boolean value signifying if the last permutation was crossed and the range was reset</para>
</returns></function>

















































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/none_of.hpp">
<namespace name="boost">
<namespace name="compute">


















































<function name="none_of"><type>bool</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="UnaryPredicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="predicate"><paramtype>UnaryPredicate</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Returns <computeroutput>true</computeroutput> if <computeroutput>predicate</computeroutput> returns <computeroutput>true</computeroutput> for none of the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>all_of(), any_of() </para>
</para>
</para></description></function>


















































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/nth_element.hpp">
<namespace name="boost">
<namespace name="compute">
















































<overloaded-function name="nth_element"><signature><type>void</type><template>
          <template-type-parameter name="Iterator"/>
          <template-type-parameter name="Compare"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype></parameter><parameter name="nth"><paramtype>Iterator</paramtype></parameter><parameter name="last"><paramtype>Iterator</paramtype></parameter><parameter name="compare"><paramtype>Compare</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="Iterator"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype></parameter><parameter name="nth"><paramtype>Iterator</paramtype></parameter><parameter name="last"><paramtype>Iterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Rearranges the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) such that the <computeroutput>nth</computeroutput> element would be in that position in a sorted sequence.</para><para>Space complexity: \Omega(3n) </para></description></overloaded-function>




















































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/partial_sum.hpp">
<namespace name="boost">
<namespace name="compute">















































<function name="partial_sum"><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Calculates the cumulative sum of the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) and writes the resulting values to the range beginning at <computeroutput>result</computeroutput>.</para><para>Space complexity on GPUs: \Omega(n)<sbr/>
 Space complexity on GPUs when <computeroutput>first</computeroutput> == <computeroutput>result:</computeroutput> \Omega(2n)<sbr/>
 Space complexity on CPUs: \Omega(1) </para></description></function>





















































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/partition.hpp">
<namespace name="boost">
<namespace name="compute">














































<function name="partition"><type>Iterator</type><template>
          <template-type-parameter name="Iterator"/>
          <template-type-parameter name="UnaryPredicate"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype></parameter><parameter name="last"><paramtype>Iterator</paramtype></parameter><parameter name="predicate"><paramtype>UnaryPredicate</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Partitions the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) according to <computeroutput>predicate</computeroutput>. Order of the elements need not be preserved.</para><para>Space complexity: \Omega(3n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>is_partitioned() and stable_partition() </para>
</para>
</para></description></function>






















































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/partition_copy.hpp">
<namespace name="boost">
<namespace name="compute">













































<function name="partition_copy"><type>std::pair&lt; OutputIterator1, OutputIterator2 &gt;</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator1"/>
          <template-type-parameter name="OutputIterator2"/>
          <template-type-parameter name="UnaryPredicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="first_true"><paramtype>OutputIterator1</paramtype></parameter><parameter name="first_false"><paramtype>OutputIterator2</paramtype></parameter><parameter name="predicate"><paramtype>UnaryPredicate</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Copies all of the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) for which <computeroutput>predicate</computeroutput> returns <computeroutput>true</computeroutput> to the range beginning at <computeroutput>first_true</computeroutput> and all of the elements for which <computeroutput>predicate</computeroutput> returns <computeroutput>false</computeroutput> to the range beginning at <computeroutput>first_false</computeroutput>.</para><para>Space complexity: \Omega(2n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>partition() </para>
</para>
</para></description></function>























































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/partition_point.hpp">
<namespace name="boost">
<namespace name="compute">












































<function name="partition_point"><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="UnaryPredicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>Iterator pointing to start of range </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>Iterator pointing to end of range </para></description></parameter><parameter name="predicate"><paramtype>UnaryPredicate</paramtype><description><para>Unary predicate to be applied on each element </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>Queue on which to execute</para></description></parameter><purpose>Partition point algorithm. </purpose><description><para>Finds the end of true values in the partitioned range [first, last) 

Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>partition() and stable_partition() </para>
</para>
</para></description><returns><para>Iterator pointing to end of true values</para>
</returns></function>
























































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/prev_permutation.hpp">
<namespace name="boost">
<namespace name="compute">











































<function name="prev_permutation"><type>bool</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>Iterator pointing to start of range </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>Iterator pointing to end of range </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>Queue on which to execute</para></description></parameter><purpose>Permutation generating algorithm. </purpose><description><para>Transforms the range [first, last) into the previous permutation from the set of all permutations arranged in lexicographic order 

Space complexity: \Omega(1) </para></description><returns><para>Boolean value signifying if the first permutation was crossed and the range was reset</para>
</returns></function>

























































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/random_shuffle.hpp">
<namespace name="boost">
<namespace name="compute">










































<function name="random_shuffle"><type>void</type><template>
          <template-type-parameter name="Iterator"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype></parameter><parameter name="last"><paramtype>Iterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Randomly shuffles the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>Space complexity: \Omega(2n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>scatter() </para>
</para>
</para></description></function>


























































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/reduce.hpp">
<namespace name="boost">
<namespace name="compute">








































<overloaded-function name="reduce"><signature><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="BinaryFunction"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>first element in the input range </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>last element in the input range </para></description></parameter><parameter name="result"><paramtype>OutputIterator</paramtype><description><para>iterator pointing to the output </para></description></parameter><parameter name="function"><paramtype>BinaryFunction</paramtype><description><para>binary reduction function </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Returns the result of applying <computeroutput>function</computeroutput> to the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>If no function is specified, <computeroutput>plus</computeroutput> will be used.</para><para>
The <computeroutput>reduce()</computeroutput> algorithm assumes that the binary reduction function is associative. When used with non-associative functions the result may be non-deterministic and vary in precision. Notably this affects the <computeroutput>plus&lt;float&gt;()</computeroutput> function as floating-point addition is not associative and may produce slightly different results than a serial algorithm.</para><para>This algorithm supports both host and device iterators for the result argument. This allows for values to be reduced and copied to the host all with a single function call.</para><para>For example, to calculate the sum of the values in a device vector and copy the result to a value on the host:</para><para><programlisting language="c++"/> Note that while the the <computeroutput>reduce()</computeroutput> algorithm is conceptually identical to the <computeroutput>accumulate()</computeroutput> algorithm, its implementation is substantially more efficient on parallel hardware. For more information, see the documentation on the <computeroutput>accumulate()</computeroutput> algorithm.</para><para>Space complexity on GPUs: \Omega(n)<sbr/>
 Space complexity on CPUs: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>accumulate() </para>
</para>
</para></description></overloaded-function>




























































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/reduce_by_key.hpp">
<namespace name="boost">
<namespace name="compute">





































<overloaded-function name="reduce_by_key"><signature><type>std::pair&lt; OutputKeyIterator, OutputValueIterator &gt;</type><template>
          <template-type-parameter name="InputKeyIterator"/>
          <template-type-parameter name="InputValueIterator"/>
          <template-type-parameter name="OutputKeyIterator"/>
          <template-type-parameter name="OutputValueIterator"/>
          <template-type-parameter name="BinaryFunction"/>
          <template-type-parameter name="BinaryPredicate"/>
        </template><parameter name="keys_first"><paramtype>InputKeyIterator</paramtype><description><para>the first key </para></description></parameter><parameter name="keys_last"><paramtype>InputKeyIterator</paramtype><description><para>the last key </para></description></parameter><parameter name="values_first"><paramtype>InputValueIterator</paramtype><description><para>the first input value </para></description></parameter><parameter name="keys_result"><paramtype>OutputKeyIterator</paramtype><description><para>iterator pointing to the key output </para></description></parameter><parameter name="values_result"><paramtype>OutputValueIterator</paramtype><description><para>iterator pointing to the reduced value output </para></description></parameter><parameter name="function"><paramtype>BinaryFunction</paramtype><description><para>binary reduction function </para></description></parameter><parameter name="predicate"><paramtype>BinaryPredicate</paramtype><description><para>binary predicate which returns true only if two keys are equal </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>std::pair&lt; OutputKeyIterator, OutputValueIterator &gt;</type><template>
          <template-type-parameter name="InputKeyIterator"/>
          <template-type-parameter name="InputValueIterator"/>
          <template-type-parameter name="OutputKeyIterator"/>
          <template-type-parameter name="OutputValueIterator"/>
          <template-type-parameter name="BinaryFunction"/>
        </template><parameter name="keys_first"><paramtype>InputKeyIterator</paramtype></parameter><parameter name="keys_last"><paramtype>InputKeyIterator</paramtype></parameter><parameter name="values_first"><paramtype>InputValueIterator</paramtype></parameter><parameter name="keys_result"><paramtype>OutputKeyIterator</paramtype></parameter><parameter name="values_result"><paramtype>OutputValueIterator</paramtype></parameter><parameter name="function"><paramtype>BinaryFunction</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><signature><type>std::pair&lt; OutputKeyIterator, OutputValueIterator &gt;</type><template>
          <template-type-parameter name="InputKeyIterator"/>
          <template-type-parameter name="InputValueIterator"/>
          <template-type-parameter name="OutputKeyIterator"/>
          <template-type-parameter name="OutputValueIterator"/>
        </template><parameter name="keys_first"><paramtype>InputKeyIterator</paramtype></parameter><parameter name="keys_last"><paramtype>InputKeyIterator</paramtype></parameter><parameter name="values_first"><paramtype>InputValueIterator</paramtype></parameter><parameter name="keys_result"><paramtype>OutputKeyIterator</paramtype></parameter><parameter name="values_result"><paramtype>OutputValueIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>The <computeroutput>reduce_by_key()</computeroutput> algorithm performs reduction for each contiguous subsequence of values determinate by equivalent keys.</para><para>Returns a pair of iterators at the end of the ranges [<computeroutput>keys_result</computeroutput>, keys_result_last) and [<computeroutput>values_result</computeroutput>, <computeroutput>values_result_last</computeroutput>).</para><para>If no function is specified, <computeroutput>plus</computeroutput> will be used. If no predicate is specified, <computeroutput>equal_to</computeroutput> will be used.</para><para>
The <computeroutput>reduce_by_key()</computeroutput> algorithm assumes that the binary reduction function is associative. When used with non-associative functions the result may be non-deterministic and vary in precision. Notably this affects the <computeroutput>plus&lt;float&gt;()</computeroutput> function as floating-point addition is not associative and may produce slightly different results than a serial algorithm.</para><para>For example, to calculate the sum of the values for each key:</para><para><programlisting language="c++"/> Space complexity on GPUs: \Omega(2n)<sbr/>
 Space complexity on CPUs: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>reduce() </para>
</para>
</para></description></overloaded-function>































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/remove.hpp">
<namespace name="boost">
<namespace name="compute">




































<function name="remove"><type>Iterator</type><template>
          <template-type-parameter name="Iterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype></parameter><parameter name="last"><paramtype>Iterator</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Removes each element equal to <computeroutput>value</computeroutput> in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>Space complexity: \Omega(3n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>remove_if() </para>
</para>
</para></description></function>
































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/remove_if.hpp">
<namespace name="boost">
<namespace name="compute">



































<function name="remove_if"><type>Iterator</type><template>
          <template-type-parameter name="Iterator"/>
          <template-type-parameter name="Predicate"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype></parameter><parameter name="last"><paramtype>Iterator</paramtype></parameter><parameter name="predicate"><paramtype>Predicate</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Removes each element for which <computeroutput>predicate</computeroutput> returns <computeroutput>true</computeroutput> in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>Space complexity: \Omega(3n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>remove() </para>
</para>
</para></description></function>

































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/replace.hpp">
<namespace name="boost">
<namespace name="compute">


































<function name="replace"><type>void</type><template>
          <template-type-parameter name="Iterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype></parameter><parameter name="last"><paramtype>Iterator</paramtype></parameter><parameter name="old_value"><paramtype>const T &amp;</paramtype></parameter><parameter name="new_value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Replaces each instance of <computeroutput>old_value</computeroutput> in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) with <computeroutput>new_value</computeroutput>.</para><para>Space complexity: \Omega(1) </para></description></function>


































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/replace_copy.hpp">
<namespace name="boost">
<namespace name="compute">

































<function name="replace_copy"><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="old_value"><paramtype>const T &amp;</paramtype></parameter><parameter name="new_value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Copies the value in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) to the range beginning at <computeroutput>result</computeroutput> while replacing each instance of <computeroutput>old_value</computeroutput> with <computeroutput>new_value</computeroutput>.</para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>replace() </para>
</para>
</para></description></function>



































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/reverse.hpp">
<namespace name="boost">
<namespace name="compute">
































<function name="reverse"><type>void</type><template>
          <template-type-parameter name="Iterator"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype></parameter><parameter name="last"><paramtype>Iterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Reverses the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>).</para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>reverse_copy() </para>
</para>
</para></description></function>




































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/reverse_copy.hpp">
<namespace name="boost">
<namespace name="compute">































<function name="reverse_copy"><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Copies the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) in reversed order to the range beginning at <computeroutput>result</computeroutput>.</para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>reverse() </para>
</para>
</para></description></function>





































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/rotate.hpp">
<namespace name="boost">
<namespace name="compute">






























<function name="rotate"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="n_first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Performs left rotation such that element at <computeroutput>n_first</computeroutput> comes to the beginning.</para><para>Space complexity: \Omega(distance(<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>))</para><para><para><emphasis role="bold">See Also:</emphasis><para>rotate_copy() </para>
</para>
</para></description></function>






































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/rotate_copy.hpp">
<namespace name="boost">
<namespace name="compute">





























<function name="rotate_copy"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="n_first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Performs left rotation such that element at n_first comes to the beginning and the output is stored in range starting at result.</para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>rotate() </para>
</para>
</para></description></function>







































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/scatter.hpp">
<namespace name="boost">
<namespace name="compute">




























<function name="scatter"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="MapIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="map"><paramtype>MapIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Copies the elements from the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) to the range beginning at <computeroutput>result</computeroutput> using the output indices from the range beginning at <computeroutput>map</computeroutput>.</para><para>Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>gather() </para>
</para>
</para></description></function>








































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/scatter_if.hpp">
<namespace name="boost">
<namespace name="compute">


























<function name="scatter_if"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="MapIterator"/>
          <template-type-parameter name="StencilIterator"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="Predicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="map"><paramtype>MapIterator</paramtype></parameter><parameter name="stencil"><paramtype>StencilIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="predicate"><paramtype>Predicate</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Copies the elements from the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) to the range beginning at <computeroutput>result</computeroutput> using the output indices from the range beginning at <computeroutput>map</computeroutput> if stencil is resolved to true. By default the predicate is an identity</para><para>Space complexity: \Omega(1) </para></description></function>
<function name="scatter_if"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="MapIterator"/>
          <template-type-parameter name="StencilIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="map"><paramtype>MapIterator</paramtype></parameter><parameter name="stencil"><paramtype>StencilIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></function>









































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/search.hpp">
<namespace name="boost">
<namespace name="compute">

























<function name="search"><type>TextIterator</type><template>
          <template-type-parameter name="TextIterator"/>
          <template-type-parameter name="PatternIterator"/>
        </template><parameter name="t_first"><paramtype>TextIterator</paramtype><description><para>Iterator pointing to start of text </para></description></parameter><parameter name="t_last"><paramtype>TextIterator</paramtype><description><para>Iterator pointing to end of text </para></description></parameter><parameter name="p_first"><paramtype>PatternIterator</paramtype><description><para>Iterator pointing to start of pattern </para></description></parameter><parameter name="p_last"><paramtype>PatternIterator</paramtype><description><para>Iterator pointing to end of pattern </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>Queue on which to execute</para></description></parameter><purpose>Substring matching algorithm. </purpose><description><para>Searches for the first match of the pattern [p_first, p_last) in text [t_first, t_last). 

Space complexity: \Omega(distance(<computeroutput>t_first</computeroutput>, <computeroutput>t_last</computeroutput>)) </para></description><returns><para>Iterator pointing to beginning of first occurrence</para>
</returns></function>











































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/search_n.hpp">
<namespace name="boost">
<namespace name="compute">
























<function name="search_n"><type>TextIterator</type><template>
          <template-type-parameter name="TextIterator"/>
          <template-type-parameter name="ValueType"/>
        </template><parameter name="t_first"><paramtype>TextIterator</paramtype><description><para>Iterator pointing to start of text </para></description></parameter><parameter name="t_last"><paramtype>TextIterator</paramtype><description><para>Iterator pointing to end of text </para></description></parameter><parameter name="n"><paramtype>size_t</paramtype><description><para>Number of times value repeats </para></description></parameter><parameter name="value"><paramtype>ValueType</paramtype><description><para>Value which repeats </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>Queue on which to execute</para></description></parameter><purpose>Substring matching algorithm. </purpose><description><para>Searches for the first occurrence of n consecutive occurrences of value in text [t_first, t_last). 

Space complexity: \Omega(distance(<computeroutput>t_first</computeroutput>, <computeroutput>t_last</computeroutput>)) </para></description><returns><para>Iterator pointing to beginning of first occurrence</para>
</returns></function>












































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/set_difference.hpp">
<namespace name="boost">
<namespace name="compute">























<function name="set_difference"><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype><description><para>Iterator pointing to start of first set </para></description></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype><description><para>Iterator pointing to end of first set </para></description></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype><description><para>Iterator pointing to start of second set </para></description></parameter><parameter name="last2"><paramtype>InputIterator2</paramtype><description><para>Iterator pointing to end of second set </para></description></parameter><parameter name="result"><paramtype>OutputIterator</paramtype><description><para>Iterator pointing to start of range in which the difference will be stored </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>Queue on which to execute</para></description></parameter><purpose>Set difference algorithm. </purpose><description><para>Finds the difference of the sorted range [first2, last2) from the sorted range [first1, last1) and stores it in range starting at result 

Space complexity: \Omega(2(distance(<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>) + distance(<computeroutput>first2</computeroutput>, <computeroutput>last2</computeroutput>))) </para></description><returns><para>Iterator pointing to end of difference</para>
</returns></function>













































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/set_intersection.hpp">
<namespace name="boost">
<namespace name="compute">






















<function name="set_intersection"><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype><description><para>Iterator pointing to start of first set </para></description></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype><description><para>Iterator pointing to end of first set </para></description></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype><description><para>Iterator pointing to start of second set </para></description></parameter><parameter name="last2"><paramtype>InputIterator2</paramtype><description><para>Iterator pointing to end of second set </para></description></parameter><parameter name="result"><paramtype>OutputIterator</paramtype><description><para>Iterator pointing to start of range in which the intersection will be stored </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>Queue on which to execute</para></description></parameter><purpose>Set intersection algorithm. </purpose><description><para>Finds the intersection of the sorted range [first1, last1) with the sorted range [first2, last2) and stores it in range starting at result 

Space complexity: \Omega(2(distance(<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>) + distance(<computeroutput>first2</computeroutput>, <computeroutput>last2</computeroutput>))) </para></description><returns><para>Iterator pointing to end of intersection</para>
</returns></function>














































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/set_symmetric_difference.hpp">
<namespace name="boost">
<namespace name="compute">





















<function name="set_symmetric_difference"><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype><description><para>Iterator pointing to start of first set </para></description></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype><description><para>Iterator pointing to end of first set </para></description></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype><description><para>Iterator pointing to start of second set </para></description></parameter><parameter name="last2"><paramtype>InputIterator2</paramtype><description><para>Iterator pointing to end of second set </para></description></parameter><parameter name="result"><paramtype>OutputIterator</paramtype><description><para>Iterator pointing to start of range in which the symmetric difference will be stored </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>Queue on which to execute</para></description></parameter><purpose>Set symmetric difference algorithm. </purpose><description><para>Finds the symmetric difference of the sorted range [first2, last2) from the sorted range [first1, last1) and stores it in range starting at result 

Space complexity: \Omega(2(distance(<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>) + distance(<computeroutput>first2</computeroutput>, <computeroutput>last2</computeroutput>))) </para></description><returns><para>Iterator pointing to end of symmetric difference</para>
</returns></function>















































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/set_union.hpp">
<namespace name="boost">
<namespace name="compute">




















<function name="set_union"><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype><description><para>Iterator pointing to start of first set </para></description></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype><description><para>Iterator pointing to end of first set </para></description></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype><description><para>Iterator pointing to start of second set </para></description></parameter><parameter name="last2"><paramtype>InputIterator2</paramtype><description><para>Iterator pointing to end of second set </para></description></parameter><parameter name="result"><paramtype>OutputIterator</paramtype><description><para>Iterator pointing to start of range in which the union will be stored </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>Queue on which to execute</para></description></parameter><purpose>Set union algorithm. </purpose><description><para>Finds the union of the sorted range [first1, last1) with the sorted range [first2, last2) and stores it in range starting at result 

Space complexity: \Omega(2(distance(<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>) + distance(<computeroutput>first2</computeroutput>, <computeroutput>last2</computeroutput>))) </para></description><returns><para>Iterator pointing to end of union</para>
</returns></function>
















































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/sort.hpp">
<namespace name="boost">
<namespace name="compute">


















<overloaded-function name="sort"><signature><type>void</type><template>
          <template-type-parameter name="Iterator"/>
          <template-type-parameter name="Compare"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype><description><para>first element in the range to sort </para></description></parameter><parameter name="last"><paramtype>Iterator</paramtype><description><para>last element in the range to sort </para></description></parameter><parameter name="compare"><paramtype>Compare</paramtype><description><para>comparison function (by default <computeroutput>less</computeroutput>) </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="Iterator"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype></parameter><parameter name="last"><paramtype>Iterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Sorts the values in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) according to <computeroutput>compare</computeroutput>.</para><para>
For example, to sort a vector on the device: <programlisting language="c++">// create vector on the device with data
float data[] = { 2.f, 4.f, 1.f, 3.f };
boost::compute::vector&lt;float&gt; vec(data, data + 4, queue);

// sort the vector on the device
boost::compute::sort(vec.begin(), vec.end(), queue);
</programlisting></para><para>The sort() algorithm can also be directly used with host iterators. This example will automatically transfer the data to the device, sort it, and then transfer the data back to the host: <programlisting language="c++">std::vector&lt;int&gt; data = { 9, 3, 2, 5, 1, 4, 6, 7 };

boost::compute::sort(data.begin(), data.end(), queue);
</programlisting></para><para>Space complexity: \Omega(n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>is_sorted() </para>
</para>
</para></description></overloaded-function>


















































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/sort_by_key.hpp">
<namespace name="boost">
<namespace name="compute">
















<overloaded-function name="sort_by_key"><signature><type>void</type><template>
          <template-type-parameter name="KeyIterator"/>
          <template-type-parameter name="ValueIterator"/>
          <template-type-parameter name="Compare"/>
        </template><parameter name="keys_first"><paramtype>KeyIterator</paramtype></parameter><parameter name="keys_last"><paramtype>KeyIterator</paramtype></parameter><parameter name="values_first"><paramtype>ValueIterator</paramtype></parameter><parameter name="compare"><paramtype>Compare</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="KeyIterator"/>
          <template-type-parameter name="ValueIterator"/>
        </template><parameter name="keys_first"><paramtype>KeyIterator</paramtype></parameter><parameter name="keys_last"><paramtype>KeyIterator</paramtype></parameter><parameter name="values_first"><paramtype>ValueIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Performs a key-value sort using the keys in the range [<computeroutput>keys_first</computeroutput>, <computeroutput>keys_last</computeroutput>) on the values in the range [<computeroutput>values_first</computeroutput>, <computeroutput>values_first</computeroutput> <computeroutput>+</computeroutput> (<computeroutput>keys_last</computeroutput> <computeroutput>-</computeroutput> <computeroutput>keys_first</computeroutput>)) using <computeroutput>compare</computeroutput>.</para><para>If no compare function is specified, <computeroutput>less</computeroutput> is used.</para><para>Space complexity: \Omega(2n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>sort() </para>
</para>
</para></description></overloaded-function>




















































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/stable_partition.hpp">
<namespace name="boost">
<namespace name="compute">















<function name="stable_partition"><type>Iterator</type><template>
          <template-type-parameter name="Iterator"/>
          <template-type-parameter name="UnaryPredicate"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype><description><para>Iterator pointing to start of range </para></description></parameter><parameter name="last"><paramtype>Iterator</paramtype><description><para>Iterator pointing to end of range </para></description></parameter><parameter name="predicate"><paramtype>UnaryPredicate</paramtype><description><para>Unary predicate to be applied on each element </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>Queue on which to execute</para></description></parameter><purpose>Partitioning algorithm. </purpose><description><para>Partitions the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) according to <computeroutput>predicate</computeroutput>. The order of the elements is preserved. 

Space complexity: \Omega(3n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>is_partitioned() and partition() </para>
</para>
</para></description><returns><para>Iterator pointing to end of true values</para>
</returns></function>





















































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/stable_sort.hpp">
<namespace name="boost">
<namespace name="compute">













<overloaded-function name="stable_sort"><signature><type>void</type><template>
          <template-type-parameter name="Iterator"/>
          <template-type-parameter name="Compare"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype></parameter><parameter name="last"><paramtype>Iterator</paramtype></parameter><parameter name="compare"><paramtype>Compare</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="Iterator"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype></parameter><parameter name="last"><paramtype>Iterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Sorts the values in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) according to <computeroutput>compare</computeroutput>. The relative order of identical values is preserved.</para><para>Space complexity: \Omega(n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>sort(), is_sorted() </para>
</para>
</para></description></overloaded-function>























































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/stable_sort_by_key.hpp">
<namespace name="boost">
<namespace name="compute">











<overloaded-function name="stable_sort_by_key"><signature><type>void</type><template>
          <template-type-parameter name="KeyIterator"/>
          <template-type-parameter name="ValueIterator"/>
          <template-type-parameter name="Compare"/>
        </template><parameter name="keys_first"><paramtype>KeyIterator</paramtype></parameter><parameter name="keys_last"><paramtype>KeyIterator</paramtype></parameter><parameter name="values_first"><paramtype>ValueIterator</paramtype></parameter><parameter name="compare"><paramtype>Compare</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="KeyIterator"/>
          <template-type-parameter name="ValueIterator"/>
        </template><parameter name="keys_first"><paramtype>KeyIterator</paramtype></parameter><parameter name="keys_last"><paramtype>KeyIterator</paramtype></parameter><parameter name="values_first"><paramtype>ValueIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Performs a key-value stable sort using the keys in the range [<computeroutput>keys_first</computeroutput>, <computeroutput>keys_last</computeroutput>) on the values in the range [<computeroutput>values_first</computeroutput>, <computeroutput>values_first</computeroutput> <computeroutput>+</computeroutput> (<computeroutput>keys_last</computeroutput> <computeroutput>-</computeroutput> <computeroutput>keys_first</computeroutput>)) using <computeroutput>compare</computeroutput>.</para><para>If no compare function is specified, <computeroutput>less</computeroutput> is used.</para><para>Space complexity: \Omega(2n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>sort() </para>
</para>
</para></description></overloaded-function>

























































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/swap_ranges.hpp">
<namespace name="boost">
<namespace name="compute">










<function name="swap_ranges"><type>Iterator2</type><template>
          <template-type-parameter name="Iterator1"/>
          <template-type-parameter name="Iterator2"/>
        </template><parameter name="first1"><paramtype>Iterator1</paramtype></parameter><parameter name="last1"><paramtype>Iterator1</paramtype></parameter><parameter name="first2"><paramtype>Iterator2</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Swaps the elements in the range [<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>) with the elements in the range beginning at <computeroutput>first2</computeroutput>.</para><para>Space complexity: \Omega(distance(<computeroutput>first1</computeroutput>, <computeroutput>last1</computeroutput>)) </para></description></function>


























































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/transform.hpp">
<namespace name="boost">
<namespace name="compute">








<overloaded-function name="transform"><signature><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="UnaryOperator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="op"><paramtype>UnaryOperator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><signature><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="BinaryOperator"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="op"><paramtype>BinaryOperator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Transforms the elements in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) using operator <computeroutput>op</computeroutput> and stores the results in the range beginning at <computeroutput>result</computeroutput>.</para><para>For example, to calculate the absolute value for each element in a vector:</para><para><programlisting language="c++"/> Space complexity: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>copy() </para>
</para>
</para></description></overloaded-function>




























































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/transform_if.hpp">
<namespace name="boost">
<namespace name="compute">







<function name="transform_if"><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="UnaryFunction"/>
          <template-type-parameter name="Predicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="function"><paramtype>UnaryFunction</paramtype></parameter><parameter name="predicate"><paramtype>Predicate</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Copies each element in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) for which <computeroutput>predicate</computeroutput> returns <computeroutput>true</computeroutput> to the range beginning at <computeroutput>result</computeroutput>.</para><para>Space complexity: O(2n) </para></description></function>





























































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/transform_reduce.hpp">
<namespace name="boost">
<namespace name="compute">





<overloaded-function name="transform_reduce"><signature><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="UnaryTransformFunction"/>
          <template-type-parameter name="BinaryReduceFunction"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="transform_function"><paramtype>UnaryTransformFunction</paramtype></parameter><parameter name="reduce_function"><paramtype>BinaryReduceFunction</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="InputIterator1"/>
          <template-type-parameter name="InputIterator2"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="BinaryTransformFunction"/>
          <template-type-parameter name="BinaryReduceFunction"/>
        </template><parameter name="first1"><paramtype>InputIterator1</paramtype></parameter><parameter name="last1"><paramtype>InputIterator1</paramtype></parameter><parameter name="first2"><paramtype>InputIterator2</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="transform_function"><paramtype>BinaryTransformFunction</paramtype></parameter><parameter name="reduce_function"><paramtype>BinaryReduceFunction</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Transforms each value in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) with the unary <computeroutput>transform_function</computeroutput> and then reduces each transformed value with <computeroutput>reduce_function</computeroutput>.</para><para>For example, to calculate the sum of the absolute values of a vector of integers:</para><para><programlisting language="c++"/> Space complexity on GPUs: \Omega(n)<sbr/>
 Space complexity on CPUs: \Omega(1)</para><para><para><emphasis role="bold">See Also:</emphasis><para>reduce(), inner_product() </para>
</para>
</para></description></overloaded-function>































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/unique.hpp">
<namespace name="boost">
<namespace name="compute">



<overloaded-function name="unique"><signature><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="BinaryPredicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>first element in the input range </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>last element in the input range </para></description></parameter><parameter name="op"><paramtype>BinaryPredicate</paramtype><description><para>binary operator used to check for uniqueness </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Removes all consecutive duplicate elements (determined by <computeroutput>op</computeroutput>) from the range [first, last). If <computeroutput>op</computeroutput> is not provided, the equality operator is used.</para><para>

Space complexity: \Omega(4n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>unique_copy() </para>
</para>
</para></description><returns><para><computeroutput>InputIterator</computeroutput> to the new logical end of the range</para>
</returns></overloaded-function>

































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/unique_copy.hpp">
<namespace name="boost">
<namespace name="compute">

<overloaded-function name="unique_copy"><signature><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="BinaryPredicate"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>first element in the input range </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>last element in the input range </para></description></parameter><parameter name="result"><paramtype>OutputIterator</paramtype><description><para>first element in the result range </para></description></parameter><parameter name="op"><paramtype>BinaryPredicate</paramtype><description><para>binary operator used to check for uniqueness </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default><description><para>command queue to perform the operation</para></description></parameter></signature><signature><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="result"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></signature><description><para>Makes a copy of the range [first, last) and removes all consecutive duplicate elements (determined by <computeroutput>op</computeroutput>) from the copy. If <computeroutput>op</computeroutput> is not provided, the equality operator is used.</para><para>

Space complexity: \Omega(4n)</para><para><para><emphasis role="bold">See Also:</emphasis><para>unique() </para>
</para>
</para></description><returns><para><computeroutput>OutputIterator</computeroutput> to the end of the result range</para>
</returns></overloaded-function>



































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/algorithm/upper_bound.hpp">
<namespace name="boost">
<namespace name="compute">
<function name="upper_bound"><type>InputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="T"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Returns an iterator pointing to the first element in the sorted range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) that is not less than or equal to <computeroutput>value</computeroutput>.</para><para>Space complexity: \Omega(1) </para></description></function>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/allocator/buffer_allocator.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="buffer_allocator"><template>
      <template-type-parameter name="T"/>
    </template><purpose>The <classname alt="boost::compute::buffer_allocator">buffer_allocator</classname> class allocates memory with buffer objects. </purpose><description><para><para><emphasis role="bold">See Also:</emphasis><para>buffer </para>
</para>
</para></description><typedef name="value_type"><type>T</type></typedef>
<typedef name="pointer"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="const_pointer"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="size_type"><type>std::size_t</type></typedef>
<typedef name="difference_type"><type>std::ptrdiff_t</type></typedef>
<method-group name="public member functions">
<method name="allocate"><type>pointer</type><parameter name="n"><paramtype>size_type</paramtype></parameter></method>
<method name="deallocate"><type>void</type><parameter name="p"><paramtype>pointer</paramtype></parameter><parameter name="n"><paramtype>size_type</paramtype></parameter></method>
<method name="max_size" cv="const"><type>size_type</type></method>
<method name="get_context" cv="const"><type>context</type></method>
</method-group>
<constructor specifiers="explicit"><parameter name="context"><paramtype>const context &amp;</paramtype></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>buffer_allocator</classname>&lt; T &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>buffer_allocator</classname>&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const <classname>buffer_allocator</classname>&lt; T &gt; &amp;</paramtype></parameter></copy-assignment>
<constructor cv="noexcept"><parameter name="other"><paramtype><classname>buffer_allocator</classname>&lt; T &gt; &amp;&amp;</paramtype></parameter></constructor>
<copy-assignment cv="noexcept"><type><classname>buffer_allocator</classname>&lt; T &gt; &amp;</type><parameter name="other"><paramtype><classname>buffer_allocator</classname>&lt; T &gt; &amp;&amp;</paramtype></parameter></copy-assignment>
<destructor/>
<method-group name="protected member functions">
<method name="set_mem_flags"><type>void</type><parameter name="flags"><paramtype>cl_mem_flags</paramtype></parameter></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/allocator/pinned_allocator.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="pinned_allocator"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">boost::compute::buffer_allocator&lt; T &gt;</inherit><method-group name="public member functions">
</method-group>
<constructor specifiers="explicit"><parameter name="context"><paramtype>const context &amp;</paramtype></parameter></constructor>
<constructor><parameter name="other"><paramtype>const pinned_allocator&lt; T &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type>pinned_allocator&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const pinned_allocator&lt; T &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/async/future.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="future"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Holds the result of an asynchronous computation. </purpose><description><para><para><emphasis role="bold">See Also:</emphasis><para>event, <classname alt="boost::compute::wait_list">wait_list</classname> </para>
</para>
</para></description><method-group name="public member functions">
<method name="get"><type>T</type><description><para>Returns the result of the computation. This will block until the result is ready. </para></description></method>
<method name="valid" cv="const"><type>bool</type><purpose>Returns <computeroutput>true</computeroutput> if the future is valid. </purpose></method>
<method name="wait" cv="const"><type>void</type><purpose>Blocks until the computation is complete. </purpose></method>
<method name="get_event" cv="const"><type>event</type><purpose>Returns the underlying event object. </purpose></method>
<method name="then"><type><classname>future</classname> &amp;</type><template>
          <template-type-parameter name="Function"/>
        </template><parameter name="callback"><paramtype>Function</paramtype></parameter><description><para>Invokes a generic callback function once the future is ready.</para><para>The function specified by callback must be invokable with zero arguments.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clSetEventCallback.html">clSetEventCallback()</ulink> for more information. <warning><para>This method is only available if the OpenCL version is 1.1 or later. </para>
</warning>
</para></description></method>
</method-group>
<constructor/>
<constructor><parameter name="result"><paramtype>const T &amp;</paramtype></parameter><parameter name="event"><paramtype>const event &amp;</paramtype></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>future</classname>&lt; T &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>future</classname> &amp;</type><parameter name="other"><paramtype>const <classname>future</classname>&lt; T &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/async/wait.hpp">
<namespace name="boost">
<namespace name="compute">












































































































<function name="wait_for_all"><type>void</type><template>
          <template-nontype-parameter name="Events"><type>class...</type></template-nontype-parameter>
        </template><parameter name="events"><paramtype>Events &amp;&amp;...</paramtype></parameter><description><para>Blocks until all events have completed. Events can either be event objects or <classname alt="boost::compute::future">future&lt;T&gt;</classname> objects.</para><para><para><emphasis role="bold">See Also:</emphasis><para>event, <classname alt="boost::compute::wait_list">wait_list</classname> </para>
</para>
</para></description></function>
























</namespace>
</namespace>
</header>
<header name="boost/compute/async/wait_guard.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="wait_guard"><template>
      <template-type-parameter name="Waitable"/>
    </template><inherit access="private">noncopyable</inherit><purpose>A guard object for synchronizing an operation on the device. </purpose><description><para>The <classname alt="boost::compute::wait_guard">wait_guard</classname> class stores a waitable object representing an operation on a compute device (e.g. event, <classname alt="boost::compute::future">future&lt;T&gt;</classname>) and calls its <computeroutput>wait()</computeroutput> method when the guard object goes out of scope.</para><para>This is useful for ensuring that an OpenCL operation completes before leaving the current scope and cleaning up any resources.</para><para>For example: <programlisting language="c++">// enqueue a compute kernel for execution
event e = queue.enqueue_nd_range_kernel(...);

// call e.wait() upon exiting the current scope
wait_guard&lt;event&gt; guard(e);
</programlisting></para><para><classname alt="boost::compute::wait_list">wait_list</classname>, wait_for_all() </para></description><method-group name="public member functions">
</method-group>
<constructor><parameter name="waitable"><paramtype>const Waitable &amp;</paramtype></parameter><purpose>Creates a new <classname alt="boost::compute::wait_guard">wait_guard</classname> object for <computeroutput>waitable</computeroutput>. </purpose></constructor>
<destructor><description><para>Destroys the <classname alt="boost::compute::wait_guard">wait_guard</classname> object. The default implementation will call <computeroutput>wait()</computeroutput> on the stored waitable object. </para></description></destructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/container/array.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="array"><template>
      <template-type-parameter name="T"/>
      <template-nontype-parameter name="N"><type>std::size_t</type></template-nontype-parameter>
    </template><purpose>A fixed-size container. </purpose><description><para>The array container is very similar to the <classname alt="boost::compute::vector">vector</classname> container except its size is fixed at compile-time rather than being dynamically resizable at run-time.</para><para>For example, to create a fixed-size array with eight values on the device: <programlisting language="c++">boost::compute::array&lt;int, 8&gt; values(context);
</programlisting></para><para>The Boost.Compute <computeroutput>array</computeroutput> class provides a STL-like API and is modeled after the <computeroutput>std::array</computeroutput> class from the C++ standard library.</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::vector">vector&lt;T&gt;</classname> </para>
</para>
</para></description><enum name="@0"><enumvalue name="static_size"><default>= N</default></enumvalue></enum>
<typedef name="value_type"><type>T</type></typedef>
<typedef name="size_type"><type>std::size_t</type></typedef>
<typedef name="difference_type"><type>ptrdiff_t</type></typedef>
<typedef name="reference"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="const_reference"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="pointer"><type>T *</type></typedef>
<typedef name="const_pointer"><type>const T *</type></typedef>
<typedef name="iterator"><type><classname>buffer_iterator</classname>&lt; T &gt;</type></typedef>
<typedef name="const_iterator"><type><classname>buffer_iterator</classname>&lt; T &gt;</type></typedef>
<typedef name="reverse_iterator"><type>std::reverse_iterator&lt; <classname>iterator</classname> &gt;</type></typedef>
<typedef name="const_reverse_iterator"><type>std::reverse_iterator&lt; <classname>const_iterator</classname> &gt;</type></typedef>
<method-group name="public member functions">
<method name="begin"><type><classname>iterator</classname></type></method>
<method name="begin" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="cbegin" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="end"><type><classname>iterator</classname></type></method>
<method name="end" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="cend" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="rbegin"><type>reverse_iterator</type></method>
<method name="rbegin" cv="const"><type>const_reverse_iterator</type></method>
<method name="crbegin" cv="const"><type>const_reverse_iterator</type></method>
<method name="rend"><type>reverse_iterator</type></method>
<method name="rend" cv="const"><type>const_reverse_iterator</type></method>
<method name="crend" cv="const"><type>const_reverse_iterator</type></method>
<method name="size" cv="const"><type>size_type</type></method>
<method name="empty" cv="const"><type>bool</type></method>
<method name="max_size" cv="const"><type>size_type</type></method>
<method name="operator[]"><type>reference</type><parameter name="index"><paramtype>size_type</paramtype></parameter></method>
<method name="operator[]" cv="const"><type>const_reference</type><parameter name="index"><paramtype>size_type</paramtype></parameter></method>
<method name="at"><type>reference</type><parameter name="index"><paramtype>size_type</paramtype></parameter></method>
<method name="at" cv="const"><type>const_reference</type><parameter name="index"><paramtype>size_type</paramtype></parameter></method>
<method name="front"><type>reference</type></method>
<method name="front" cv="const"><type>const_reference</type></method>
<method name="back"><type>reference</type></method>
<method name="back" cv="const"><type>const_reference</type></method>
<method name="fill"><type>void</type><parameter name="value"><paramtype>const value_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>const command_queue &amp;</paramtype></parameter></method>
<method name="swap"><type>void</type><parameter name="other"><paramtype><classname>array</classname>&lt; T, N &gt; &amp;</paramtype></parameter><parameter name="queue"><paramtype>const command_queue &amp;</paramtype></parameter></method>
<method name="fill"><type>void</type><parameter name="value"><paramtype>const value_type &amp;</paramtype></parameter></method>
<method name="swap"><type>void</type><parameter name="other"><paramtype><classname>array</classname>&lt; T, N &gt; &amp;</paramtype></parameter></method>
<method name="get_buffer" cv="const"><type>const buffer &amp;</type></method>
</method-group>
<constructor specifiers="explicit"><parameter name="context"><paramtype>const context &amp;</paramtype><default>system::default_context()</default></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>array</classname>&lt; T, N &gt; &amp;</paramtype></parameter></constructor>
<constructor><parameter name="array"><paramtype>const boost::array&lt; T, N &gt; &amp;</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype><default>system::default_context()</default></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>array</classname>&lt; T, N &gt; &amp;</paramtype></parameter><parameter name="queue"><paramtype>const command_queue &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>array</classname>&lt; T, N &gt; &amp;</type><parameter name="other"><paramtype>const <classname>array</classname>&lt; T, N &gt; &amp;</paramtype></parameter></copy-assignment>
<copy-assignment><type><classname>array</classname>&lt; T, N &gt; &amp;</type><parameter name="array"><paramtype>const boost::array&lt; T, N &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
<method-group name="private member functions">
<method name="default_queue" cv="const"><type>command_queue</type></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/container/basic_string.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="basic_string"><template>
      <template-type-parameter name="CharT"/>
      <template-type-parameter name="Traits"><default>std::char_traits&lt;CharT&gt;</default></template-type-parameter>
    </template><purpose>A template for a dynamically-sized character sequence. </purpose><description><para>The <computeroutput><classname alt="boost::compute::basic_string">basic_string</classname></computeroutput> class provides a generic template for a dynamically- sized character sequence. This is most commonly used through the <computeroutput>string</computeroutput> typedef (for <computeroutput>basic_string&lt;char&gt;</computeroutput>).</para><para>For example, to create a string on the device with its contents copied from a C-string on the host: <programlisting language="c++">boost::compute::string str("hello, world!");
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::vector">vector&lt;T&gt;</classname> </para>
</para>
</para></description><typedef name="traits_type"><type>Traits</type></typedef>
<typedef name="value_type"><type>Traits::char_type</type></typedef>
<typedef name="size_type"><type>size_t</type></typedef>
<typedef name="reference"><type>::<classname>boost::compute::vector</classname>&lt; CharT &gt;::reference</type></typedef>
<typedef name="const_reference"><type>::<classname>boost::compute::vector</classname>&lt; CharT &gt;::const_reference</type></typedef>
<typedef name="iterator"><type>::<classname>boost::compute::vector</classname>&lt; CharT &gt;::<classname>iterator</classname></type></typedef>
<typedef name="const_iterator"><type>::<classname>boost::compute::vector</classname>&lt; CharT &gt;::<classname>const_iterator</classname></type></typedef>
<typedef name="reverse_iterator"><type>::<classname>boost::compute::vector</classname>&lt; CharT &gt;::reverse_iterator</type></typedef>
<typedef name="const_reverse_iterator"><type>::<classname>boost::compute::vector</classname>&lt; CharT &gt;::const_reverse_iterator</type></typedef>
<data-member name="npos" specifiers="static"><type>const size_type</type></data-member>
<method-group name="public member functions">
<method name="at"><type>reference</type><parameter name="pos"><paramtype>size_type</paramtype></parameter></method>
<method name="at" cv="const"><type>const_reference</type><parameter name="pos"><paramtype>size_type</paramtype></parameter></method>
<method name="operator[]"><type>reference</type><parameter name="pos"><paramtype>size_type</paramtype></parameter></method>
<method name="operator[]" cv="const"><type>const_reference</type><parameter name="pos"><paramtype>size_type</paramtype></parameter></method>
<method name="front"><type>reference</type></method>
<method name="front" cv="const"><type>const_reference</type></method>
<method name="back"><type>reference</type></method>
<method name="back" cv="const"><type>const_reference</type></method>
<method name="begin"><type><classname>iterator</classname></type></method>
<method name="begin" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="cbegin" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="end"><type><classname>iterator</classname></type></method>
<method name="end" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="cend" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="rbegin"><type>reverse_iterator</type></method>
<method name="rbegin" cv="const"><type>const_reverse_iterator</type></method>
<method name="crbegin" cv="const"><type>const_reverse_iterator</type></method>
<method name="rend"><type>reverse_iterator</type></method>
<method name="rend" cv="const"><type>const_reverse_iterator</type></method>
<method name="crend" cv="const"><type>const_reverse_iterator</type></method>
<method name="empty" cv="const"><type>bool</type></method>
<method name="size" cv="const"><type>size_type</type></method>
<method name="length" cv="const"><type>size_type</type></method>
<method name="max_size" cv="const"><type>size_type</type></method>
<method name="reserve"><type>void</type><parameter name="size"><paramtype>size_type</paramtype></parameter></method>
<method name="capacity" cv="const"><type>size_type</type></method>
<method name="shrink_to_fit"><type>void</type></method>
<method name="clear"><type>void</type></method>
<method name="swap"><type>void</type><parameter name="other"><paramtype><classname>basic_string</classname>&lt; CharT, Traits &gt; &amp;</paramtype></parameter></method>
<method name="substr" cv="const"><type><classname>basic_string</classname>&lt; CharT, Traits &gt;</type><parameter name="pos"><paramtype>size_type</paramtype><default>0</default></parameter><parameter name="count"><paramtype>size_type</paramtype><default>npos</default></parameter></method>
<method name="find" cv="const"><type>size_type</type><parameter name="ch"><paramtype>CharT</paramtype></parameter><parameter name="pos"><paramtype>size_type</paramtype><default>0</default></parameter><purpose>Finds the first character <computeroutput>ch</computeroutput>. </purpose></method>
<method name="find" cv="const"><type>size_type</type><parameter name="str"><paramtype><classname>basic_string</classname> &amp;</paramtype></parameter><parameter name="pos"><paramtype>size_type</paramtype><default>0</default></parameter><purpose>Finds the first substring equal to <computeroutput>str</computeroutput>. </purpose></method>
<method name="find" cv="const"><type>size_type</type><parameter name="s"><paramtype>const char *</paramtype></parameter><parameter name="pos"><paramtype>size_type</paramtype><default>0</default></parameter><description><para>Finds the first substring equal to the character string pointed to by <computeroutput>s</computeroutput>. The length of the string is determined by the first null character.</para><para>For example, the following code <programlisting language="c++"/> will return 5 as position. </para></description></method>
</method-group>
<constructor/>
<constructor><parameter name="count"><paramtype>size_type</paramtype></parameter><parameter name="ch"><paramtype>CharT</paramtype></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>basic_string</classname> &amp;</paramtype></parameter><parameter name="pos"><paramtype>size_type</paramtype></parameter><parameter name="count"><paramtype>size_type</paramtype><default>npos</default></parameter></constructor>
<constructor><parameter name="s"><paramtype>const char *</paramtype></parameter><parameter name="count"><paramtype>size_type</paramtype></parameter></constructor>
<constructor><parameter name="s"><paramtype>const char *</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>basic_string</classname>&lt; CharT, Traits &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>basic_string</classname>&lt; CharT, Traits &gt; &amp;</type><parameter name="other"><paramtype>const <classname>basic_string</classname>&lt; CharT, Traits &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
</class>












































































































<function name="operator&lt;&lt;"><type>std::ostream &amp;</type><template>
          <template-type-parameter name="CharT"/>
          <template-type-parameter name="Traits"/>
        </template><parameter name="stream"><paramtype>std::ostream &amp;</paramtype></parameter><parameter name="outStr"><paramtype><classname>boost::compute::basic_string</classname>&lt; CharT, Traits &gt;const &amp;</paramtype></parameter></function>























</namespace>
</namespace>
</header>
<header name="boost/compute/container/dynamic_bitset.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="dynamic_bitset"><template>
      <template-type-parameter name="Block"><default>ulong_</default></template-type-parameter>
      <template-type-parameter name="Alloc"><default><classname alt="boost::compute::buffer_allocator">buffer_allocator</classname>&lt;Block&gt;</default></template-type-parameter>
    </template><purpose>The <classname alt="boost::compute::dynamic_bitset">dynamic_bitset</classname> class contains a resizable bit array. </purpose><description><para>For example, to create a dynamic-bitset with space for 1000 bits on the device: <programlisting language="c++">boost::compute::dynamic_bitset&lt;&gt; bits(1000, queue);
</programlisting></para><para>The Boost.Compute <computeroutput><classname alt="boost::compute::dynamic_bitset">dynamic_bitset</classname></computeroutput> class provides a STL-like API and is modeled after the <computeroutput>boost::dynamic_bitset</computeroutput> class from Boost.</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::vector">vector&lt;T&gt;</classname> </para>
</para>
</para></description><typedef name="block_type"><type>Block</type></typedef>
<typedef name="allocator_type"><type>Alloc</type></typedef>
<typedef name="container_type"><type><classname>vector</classname>&lt; Block, Alloc &gt;</type></typedef>
<typedef name="size_type"><type>container_type::size_type</type></typedef>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>size_type</paramtype></parameter><parameter name=""><paramtype>bits_per_block</paramtype><default>sizeof(block_type) *CHAR_BIT</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>size_type</paramtype></parameter><parameter name=""><paramtype>npos</paramtype><default>static_cast&lt; size_type &gt;(-1)</default></parameter></method>
<method name="size" cv="const"><type>size_type</type><purpose>Returns the size of the dynamic bitset. </purpose></method>
<method name="num_blocks" cv="const"><type>size_type</type><purpose>Returns the number of blocks to store the bits in the dynamic bitset. </purpose></method>
<method name="max_size" cv="const"><type>size_type</type><purpose>Returns the maximum possible size for the dynamic bitset. </purpose></method>
<method name="empty" cv="const"><type>bool</type><purpose>Returns <computeroutput>true</computeroutput> if the dynamic bitset is empty (i.e. <computeroutput>size()</computeroutput> == <computeroutput>0</computeroutput>). </purpose></method>
<method name="count" cv="const"><type>size_type</type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Returns the number of set bits (i.e. '1') in the bitset. </purpose></method>
<method name="resize"><type>void</type><parameter name="num_bits"><paramtype>size_type</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Resizes the bitset to contain <computeroutput>num_bits</computeroutput>. If the new size is greater than the current size the new bits are set to zero. </para></description></method>
<method name="set"><type>void</type><parameter name="n"><paramtype>size_type</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Sets the bit at position <computeroutput>n</computeroutput> to <computeroutput>true</computeroutput>. </purpose></method>
<method name="set"><type>void</type><parameter name="n"><paramtype>size_type</paramtype></parameter><parameter name="value"><paramtype>bool</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Sets the bit at position <computeroutput>n</computeroutput> to <computeroutput>value</computeroutput>. </purpose></method>
<method name="test"><type>bool</type><parameter name="n"><paramtype>size_type</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Returns <computeroutput>true</computeroutput> if the bit at position <computeroutput>n</computeroutput> is set (i.e. '1'). </purpose></method>
<method name="flip"><type>void</type><parameter name="n"><paramtype>size_type</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Flips the value of the bit at position <computeroutput>n</computeroutput>. </purpose></method>
<method name="any" cv="const"><type>bool</type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Returns <computeroutput>true</computeroutput> if any bit in the bitset is set (i.e. '1'). </purpose></method>
<method name="none" cv="const"><type>bool</type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Returns <computeroutput>true</computeroutput> if all of the bits in the bitset are set to zero. </purpose></method>
<method name="reset"><type>void</type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Sets all of the bits in the bitset to zero. </purpose></method>
<method name="reset"><type>void</type><parameter name="n"><paramtype>size_type</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Sets the bit at position <computeroutput>n</computeroutput> to zero. </purpose></method>
<method name="clear"><type>void</type><purpose>Empties the bitset (e.g. <computeroutput>resize(0)</computeroutput>). </purpose></method>
<method name="get_allocator" cv="const"><type>allocator_type</type><purpose>Returns the allocator used to allocate storage for the bitset. </purpose></method>
</method-group>
<constructor><parameter name="size"><paramtype>size_type</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Creates a new dynamic bitset with storage for <computeroutput>size</computeroutput> bits. Initializes all bits to zero. </para></description></constructor>
<constructor><parameter name="other"><paramtype>const <classname>dynamic_bitset</classname> &amp;</paramtype></parameter><purpose>Creates a new dynamic bitset as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>dynamic_bitset</classname> &amp;</type><parameter name="other"><paramtype>const <classname>dynamic_bitset</classname> &amp;</paramtype></parameter><purpose>Copies the data from <computeroutput>other</computeroutput> to <computeroutput>*this</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the dynamic bitset. </purpose></destructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/container/flat_map.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="flat_map"><template>
      <template-type-parameter name="Key"/>
      <template-type-parameter name="T"/>
    </template><typedef name="key_type"><type>Key</type></typedef>
<typedef name="mapped_type"><type>T</type></typedef>
<typedef name="vector_type"><type>::<classname>boost::compute::vector</classname>&lt; std::pair&lt; Key, T &gt; &gt;</type></typedef>
<typedef name="value_type"><type>vector_type::value_type</type></typedef>
<typedef name="size_type"><type>vector_type::size_type</type></typedef>
<typedef name="difference_type"><type>vector_type::difference_type</type></typedef>
<typedef name="reference"><type>vector_type::reference</type></typedef>
<typedef name="const_reference"><type>vector_type::const_reference</type></typedef>
<typedef name="pointer"><type>vector_type::pointer</type></typedef>
<typedef name="const_pointer"><type>vector_type::const_pointer</type></typedef>
<typedef name="iterator"><type><classname>vector_type::iterator</classname></type></typedef>
<typedef name="const_iterator"><type><classname>vector_type::const_iterator</classname></type></typedef>
<typedef name="reverse_iterator"><type>vector_type::reverse_iterator</type></typedef>
<typedef name="const_reverse_iterator"><type>vector_type::const_reverse_iterator</type></typedef>
<method-group name="public member functions">
<method name="begin"><type><classname>iterator</classname></type></method>
<method name="begin" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="cbegin" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="end"><type><classname>iterator</classname></type></method>
<method name="end" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="cend" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="rbegin"><type>reverse_iterator</type></method>
<method name="rbegin" cv="const"><type>const_reverse_iterator</type></method>
<method name="crbegin" cv="const"><type>const_reverse_iterator</type></method>
<method name="rend"><type>reverse_iterator</type></method>
<method name="rend" cv="const"><type>const_reverse_iterator</type></method>
<method name="crend" cv="const"><type>const_reverse_iterator</type></method>
<method name="size" cv="const"><type>size_type</type></method>
<method name="max_size" cv="const"><type>size_type</type></method>
<method name="empty" cv="const"><type>bool</type></method>
<method name="capacity" cv="const"><type>size_type</type></method>
<method name="reserve"><type>void</type><parameter name="size"><paramtype>size_type</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="reserve"><type>void</type><parameter name="size"><paramtype>size_type</paramtype></parameter></method>
<method name="shrink_to_fit"><type>void</type></method>
<method name="clear"><type>void</type></method>
<method name="insert"><type>std::pair&lt; <classname>iterator</classname>, bool &gt;</type><parameter name="value"><paramtype>const value_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="insert"><type>std::pair&lt; <classname>iterator</classname>, bool &gt;</type><parameter name="value"><paramtype>const value_type &amp;</paramtype></parameter></method>
<method name="erase"><type><classname>iterator</classname></type><parameter name="position"><paramtype>const <classname>const_iterator</classname> &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="erase"><type><classname>iterator</classname></type><parameter name="position"><paramtype>const <classname>const_iterator</classname> &amp;</paramtype></parameter></method>
<method name="erase"><type><classname>iterator</classname></type><parameter name="first"><paramtype>const <classname>const_iterator</classname> &amp;</paramtype></parameter><parameter name="last"><paramtype>const <classname>const_iterator</classname> &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="erase"><type><classname>iterator</classname></type><parameter name="first"><paramtype>const <classname>const_iterator</classname> &amp;</paramtype></parameter><parameter name="last"><paramtype>const <classname>const_iterator</classname> &amp;</paramtype></parameter></method>
<method name="erase"><type>size_type</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="find"><type><classname>iterator</classname></type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="find"><type><classname>iterator</classname></type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="find" cv="const"><type><classname>const_iterator</classname></type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="find" cv="const"><type><classname>const_iterator</classname></type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="count" cv="const"><type>size_type</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="count" cv="const"><type>size_type</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="lower_bound"><type><classname>iterator</classname></type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="lower_bound"><type><classname>iterator</classname></type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="lower_bound" cv="const"><type><classname>const_iterator</classname></type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="lower_bound" cv="const"><type><classname>const_iterator</classname></type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="upper_bound"><type><classname>iterator</classname></type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="upper_bound"><type><classname>iterator</classname></type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="upper_bound" cv="const"><type><classname>const_iterator</classname></type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="upper_bound" cv="const"><type><classname>const_iterator</classname></type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="at" cv="const"><type>const mapped_type</type><parameter name="key"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="operator[]"><type><emphasis>unspecified</emphasis></type><parameter name="key"><paramtype>const key_type &amp;</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit"><parameter name="context"><paramtype>const context &amp;</paramtype><default>system::default_context()</default></parameter></constructor>
<constructor><parameter name="other"><paramtype>const flat_map&lt; Key, T &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type>flat_map&lt; Key, T &gt; &amp;</type><parameter name="other"><paramtype>const flat_map&lt; Key, T &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/container/flat_set.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="flat_set"><template>
      <template-type-parameter name="T"/>
    </template><typedef name="key_type"><type>T</type></typedef>
<typedef name="value_type"><type><classname>vector</classname>&lt; T &gt;::value_type</type></typedef>
<typedef name="size_type"><type><classname>vector</classname>&lt; T &gt;::size_type</type></typedef>
<typedef name="difference_type"><type><classname>vector</classname>&lt; T &gt;::difference_type</type></typedef>
<typedef name="reference"><type><classname>vector</classname>&lt; T &gt;::reference</type></typedef>
<typedef name="const_reference"><type><classname>vector</classname>&lt; T &gt;::const_reference</type></typedef>
<typedef name="pointer"><type><classname>vector</classname>&lt; T &gt;::pointer</type></typedef>
<typedef name="const_pointer"><type><classname>vector</classname>&lt; T &gt;::const_pointer</type></typedef>
<typedef name="iterator"><type><classname>vector</classname>&lt; T &gt;::iterator</type></typedef>
<typedef name="const_iterator"><type><classname>vector</classname>&lt; T &gt;::const_iterator</type></typedef>
<typedef name="reverse_iterator"><type><classname>vector</classname>&lt; T &gt;::reverse_iterator</type></typedef>
<typedef name="const_reverse_iterator"><type><classname>vector</classname>&lt; T &gt;::const_reverse_iterator</type></typedef>
<method-group name="public member functions">
<method name="begin"><type>iterator</type></method>
<method name="begin" cv="const"><type>const_iterator</type></method>
<method name="cbegin" cv="const"><type>const_iterator</type></method>
<method name="end"><type>iterator</type></method>
<method name="end" cv="const"><type>const_iterator</type></method>
<method name="cend" cv="const"><type>const_iterator</type></method>
<method name="rbegin"><type>reverse_iterator</type></method>
<method name="rbegin" cv="const"><type>const_reverse_iterator</type></method>
<method name="crbegin" cv="const"><type>const_reverse_iterator</type></method>
<method name="rend"><type>reverse_iterator</type></method>
<method name="rend" cv="const"><type>const_reverse_iterator</type></method>
<method name="crend" cv="const"><type>const_reverse_iterator</type></method>
<method name="size" cv="const"><type>size_type</type></method>
<method name="max_size" cv="const"><type>size_type</type></method>
<method name="empty" cv="const"><type>bool</type></method>
<method name="capacity" cv="const"><type>size_type</type></method>
<method name="reserve"><type>void</type><parameter name="size"><paramtype>size_type</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="reserve"><type>void</type><parameter name="size"><paramtype>size_type</paramtype></parameter></method>
<method name="shrink_to_fit"><type>void</type></method>
<method name="clear"><type>void</type></method>
<method name="insert"><type>std::pair&lt; iterator, bool &gt;</type><parameter name="value"><paramtype>const value_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="insert"><type>std::pair&lt; iterator, bool &gt;</type><parameter name="value"><paramtype>const value_type &amp;</paramtype></parameter></method>
<method name="erase"><type>iterator</type><parameter name="position"><paramtype>const const_iterator &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="erase"><type>iterator</type><parameter name="position"><paramtype>const const_iterator &amp;</paramtype></parameter></method>
<method name="erase"><type>iterator</type><parameter name="first"><paramtype>const const_iterator &amp;</paramtype></parameter><parameter name="last"><paramtype>const const_iterator &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="erase"><type>iterator</type><parameter name="first"><paramtype>const const_iterator &amp;</paramtype></parameter><parameter name="last"><paramtype>const const_iterator &amp;</paramtype></parameter></method>
<method name="erase"><type>size_type</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="erase"><type>size_type</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="find"><type>iterator</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="find"><type>iterator</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="find" cv="const"><type>const_iterator</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="find" cv="const"><type>const_iterator</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="count" cv="const"><type>size_type</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="count" cv="const"><type>size_type</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="lower_bound"><type>iterator</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="lower_bound"><type>iterator</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="lower_bound" cv="const"><type>const_iterator</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="lower_bound" cv="const"><type>const_iterator</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="upper_bound"><type>iterator</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="upper_bound"><type>iterator</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
<method name="upper_bound" cv="const"><type>const_iterator</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="upper_bound" cv="const"><type>const_iterator</type><parameter name="value"><paramtype>const key_type &amp;</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit"><parameter name="context"><paramtype>const context &amp;</paramtype><default>system::default_context()</default></parameter></constructor>
<constructor><parameter name="other"><paramtype>const flat_set&lt; T &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type>flat_set&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const flat_set&lt; T &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/container/mapped_view.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="mapped_view"><template>
      <template-type-parameter name="T"/>
    </template><purpose>A mapped view of host memory. </purpose><description><para>The <classname alt="boost::compute::mapped_view">mapped_view</classname> class simplifies mapping host-memory to a compute device. This allows for host-allocated memory to be used with the Boost.Compute algorithms.</para><para>The following example shows how to map a simple C-array containing data on the host to the device and run the reduce() algorithm to calculate the sum:</para><para><programlisting language="c++"/> <para><emphasis role="bold">See Also:</emphasis><para>buffer </para>
</para>
</para></description><typedef name="value_type"><type>T</type></typedef>
<typedef name="size_type"><type>size_t</type></typedef>
<typedef name="difference_type"><type>ptrdiff_t</type></typedef>
<typedef name="iterator"><type><classname>buffer_iterator</classname>&lt; T &gt;</type></typedef>
<typedef name="const_iterator"><type><classname>buffer_iterator</classname>&lt; T &gt;</type></typedef>
<method-group name="public member functions">
<method name="begin"><type><classname>iterator</classname></type><purpose>Returns an iterator to the first element in the <classname alt="boost::compute::mapped_view">mapped_view</classname>. </purpose></method>
<method name="begin" cv="const"><type><classname>const_iterator</classname></type><purpose>Returns a const_iterator to the first element in the <classname alt="boost::compute::mapped_view">mapped_view</classname>. </purpose></method>
<method name="cbegin" cv="const"><type><classname>const_iterator</classname></type><purpose>Returns a const_iterator to the first element in the <classname alt="boost::compute::mapped_view">mapped_view</classname>. </purpose></method>
<method name="end"><type><classname>iterator</classname></type><purpose>Returns an iterator to one past the last element in the <classname alt="boost::compute::mapped_view">mapped_view</classname>. </purpose></method>
<method name="end" cv="const"><type><classname>const_iterator</classname></type><purpose>Returns a const_iterator to one past the last element in the <classname alt="boost::compute::mapped_view">mapped_view</classname>. </purpose></method>
<method name="cend" cv="const"><type><classname>const_iterator</classname></type><purpose>Returns a const_iterator to one past the last element in the <classname alt="boost::compute::mapped_view">mapped_view</classname>. </purpose></method>
<method name="size" cv="const"><type>size_type</type><purpose>Returns the number of elements in the <classname alt="boost::compute::mapped_view">mapped_view</classname>. </purpose></method>
<method name="get_host_ptr"><type>T *</type><purpose>Returns the host data pointer. </purpose></method>
<method name="get_host_ptr" cv="const"><type>const T *</type><purpose>Returns the host data pointer. </purpose></method>
<method name="resize"><type>void</type><parameter name="size"><paramtype>size_type</paramtype></parameter><purpose>Resizes the <classname alt="boost::compute::mapped_view">mapped_view</classname> to <computeroutput>size</computeroutput> elements. </purpose></method>
<method name="empty" cv="const"><type>bool</type><purpose>Returns <computeroutput>true</computeroutput> if the <classname alt="boost::compute::mapped_view">mapped_view</classname> is empty. </purpose></method>
<method name="get_buffer" cv="const"><type>const buffer &amp;</type><purpose>Returns the mapped buffer. </purpose></method>
<method name="map"><type>void</type><parameter name="flags"><paramtype>cl_map_flags</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Maps the buffer into the host address space.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clEnqueueMapBuffer.html">clEnqueueMapBuffer()</ulink> for more information. </para></description></method>
<method name="map"><type>void</type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Maps the buffer into the host address space for reading and writing.</para><para>Equivalent to: <programlisting language="c++">map(CL_MAP_READ | CL_MAP_WRITE, queue);
</programlisting> </para></description></method>
<method name="unmap"><type>void</type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Unmaps the buffer from the host address space.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clEnqueueUnmapMemObject.html">clEnqueueUnmapMemObject()</ulink> for more information. </para></description></method>
</method-group>
<constructor><purpose>Creates a null <classname alt="boost::compute::mapped_view">mapped_view</classname> object. </purpose></constructor>
<constructor><parameter name="host_ptr"><paramtype>T *</paramtype></parameter><parameter name="n"><paramtype>size_type</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype><default>system::default_context()</default></parameter><description><para>Creates a <classname alt="boost::compute::mapped_view">mapped_view</classname> for <computeroutput>host_ptr</computeroutput> with <computeroutput>n</computeroutput> elements. After constructing a <classname alt="boost::compute::mapped_view">mapped_view</classname> the data is available for use by a compute device. Use the <computeroutput>unmap()</computeroutput> method to make the updated data available to the host. </para></description></constructor>
<constructor><parameter name="host_ptr"><paramtype>const T *</paramtype></parameter><parameter name="n"><paramtype>size_type</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype><default>system::default_context()</default></parameter><description><para>Creates a read-only <classname alt="boost::compute::mapped_view">mapped_view</classname> for <computeroutput>host_ptr</computeroutput> with <computeroutput>n</computeroutput> elements. After constructing a <classname alt="boost::compute::mapped_view">mapped_view</classname> the data is available for use by a compute device. Use the <computeroutput>unmap()</computeroutput> method to make the updated data available to the host. </para></description></constructor>
<constructor><parameter name="other"><paramtype>const <classname>mapped_view</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Creates a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>mapped_view</classname>&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const <classname>mapped_view</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Copies the mapped buffer from <computeroutput>other</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the <classname alt="boost::compute::mapped_view">mapped_view</classname> object. </purpose></destructor>
<method-group name="private static functions">
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/container/stack.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="stack"><template>
      <template-type-parameter name="T"/>
    </template><typedef name="container_type"><type><classname>vector</classname>&lt; T &gt;</type></typedef>
<typedef name="size_type"><type>container_type::size_type</type></typedef>
<typedef name="value_type"><type>container_type::value_type</type></typedef>
<method-group name="public member functions">
<method name="empty" cv="const"><type>bool</type></method>
<method name="size" cv="const"><type>size_type</type></method>
<method name="top" cv="const"><type>value_type</type></method>
<method name="push"><type>void</type><parameter name="value"><paramtype>const T &amp;</paramtype></parameter></method>
<method name="pop"><type>void</type></method>
</method-group>
<constructor/>
<constructor><parameter name="other"><paramtype>const stack&lt; T &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type>stack&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const stack&lt; T &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/container/string.hpp">
<namespace name="boost">
<namespace name="compute">
<typedef name="string"><type><classname>basic_string</classname>&lt; char_ &gt;</type></typedef>





































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/container/valarray.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="valarray"><template>
      <template-type-parameter name="T"/>
    </template><method-group name="public member functions">
<method name="operator *="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const T &amp;</paramtype></parameter></method>
<method name="operator/="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const T &amp;</paramtype></parameter></method>
<method name="operator%="><type>valarray&lt; T &gt; &amp;</type><parameter name="val"><paramtype>const T &amp;</paramtype></parameter></method>
<method name="operator+" cv="const"><type>valarray&lt; T &gt;</type></method>
<method name="operator-" cv="const"><type>valarray&lt; T &gt;</type></method>
<method name="operator~" cv="const"><type>valarray&lt; T &gt;</type></method>
<method name="operator!" cv="const"><type>valarray&lt; char &gt;</type><description><para>In OpenCL there cannot be memory buffer with bool type, for this reason return type is valarray&lt;char&gt; instead of valarray&lt;bool&gt;. 1 means true, 0 means false. </para></description></method>
<method name="operator+="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const T &amp;</paramtype></parameter></method>
<method name="operator-="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const T &amp;</paramtype></parameter></method>
<method name="operator^="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const T &amp;</paramtype></parameter></method>
<method name="operator&amp;="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const T &amp;</paramtype></parameter></method>
<method name="operator|="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const T &amp;</paramtype></parameter></method>
<method name="operator&lt;&lt;="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const T &amp;</paramtype></parameter></method>
<method name="operator&gt;&gt;="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const T &amp;</paramtype></parameter></method>
<method name="operator *="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const valarray&lt; T &gt; &amp;</paramtype></parameter></method>
<method name="operator/="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const valarray&lt; T &gt; &amp;</paramtype></parameter></method>
<method name="operator%="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const valarray&lt; T &gt; &amp;</paramtype></parameter></method>
<method name="operator+="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const valarray&lt; T &gt; &amp;</paramtype></parameter></method>
<method name="operator-="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const valarray&lt; T &gt; &amp;</paramtype></parameter></method>
<method name="operator^="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const valarray&lt; T &gt; &amp;</paramtype></parameter></method>
<method name="operator&amp;="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const valarray&lt; T &gt; &amp;</paramtype></parameter></method>
<method name="operator|="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const valarray&lt; T &gt; &amp;</paramtype></parameter></method>
<method name="operator&lt;&lt;="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const valarray&lt; T &gt; &amp;</paramtype></parameter></method>
<method name="operator&gt;&gt;="><type>valarray&lt; T &gt; &amp;</type><parameter name=""><paramtype>const valarray&lt; T &gt; &amp;</paramtype></parameter></method>
<method name="size" cv="const"><type>size_t</type></method>
<method name="resize"><type>void</type><parameter name="size"><paramtype>size_t</paramtype></parameter><parameter name="value"><paramtype>T</paramtype><default>T()</default></parameter></method>
<method name="operator[]"><type><emphasis>unspecified</emphasis></type><parameter name="index"><paramtype>size_t</paramtype></parameter></method>
<method name="operator[]" cv="const"><type><emphasis>unspecified</emphasis></type><parameter name="index"><paramtype>size_t</paramtype></parameter></method>
<method name="min" cv="const"><type>T()</type></method>
<method name="max" cv="const"><type>T()</type></method>
<method name="sum" cv="const"><type>T</type></method>
<method name="apply" cv="const"><type>valarray&lt; T &gt;</type><template>
          <template-type-parameter name="UnaryFunction"/>
        </template><parameter name="function"><paramtype>UnaryFunction</paramtype></parameter></method>
<method name="get_buffer" cv="const"><type>const buffer &amp;</type></method>
</method-group>
<constructor specifiers="explicit"><parameter name="context"><paramtype>const context &amp;</paramtype><default>system::default_context()</default></parameter></constructor>
<constructor specifiers="explicit"><parameter name="size"><paramtype>size_t</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype><default>system::default_context()</default></parameter></constructor>
<constructor><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="size"><paramtype>size_t</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype><default>system::default_context()</default></parameter></constructor>
<constructor><parameter name="values"><paramtype>const T *</paramtype></parameter><parameter name="size"><paramtype>size_t</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype><default>system::default_context()</default></parameter></constructor>
<constructor><parameter name="other"><paramtype>const valarray&lt; T &gt; &amp;</paramtype></parameter></constructor>
<constructor><parameter name="valarray"><paramtype>const std::valarray&lt; T &gt; &amp;</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype><default>system::default_context()</default></parameter></constructor>
<copy-assignment><type>valarray&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const valarray&lt; T &gt; &amp;</paramtype></parameter></copy-assignment>
<copy-assignment><type>valarray&lt; T &gt; &amp;</type><parameter name="valarray"><paramtype>const std::valarray&lt; T &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
<method-group name="private member functions">
<method name="begin" cv="const"><type><classname>buffer_iterator</classname>&lt; T &gt;</type></method>
<method name="end" cv="const"><type><classname>buffer_iterator</classname>&lt; T &gt;</type></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
<macro name="BOOST_COMPUTE_DEFINE_VALARRAY_BINARY_OPERATOR" kind="functionlike"><macro-parameter name="op"/><macro-parameter name="op_name"/><macro-parameter name="assert"/></macro>
<macro name="BOOST_COMPUTE_DEFINE_VALARRAY_BINARY_OPERATOR_ANY" kind="functionlike"><macro-parameter name="op"/><macro-parameter name="op_name"/></macro>
<macro name="BOOST_COMPUTE_DEFINE_VALARRAY_BINARY_OPERATOR_NO_FP" kind="functionlike"><macro-parameter name="op"/><macro-parameter name="op_name"/></macro>
<macro name="BOOST_COMPUTE_DEFINE_VALARRAY_COMPARISON_OPERATOR" kind="functionlike"><macro-parameter name="op"/><macro-parameter name="op_name"/></macro>
<macro name="BOOST_COMPUTE_DEFINE_VALARRAY_LOGICAL_OPERATOR" kind="functionlike"><macro-parameter name="op"/><macro-parameter name="op_name"/></macro>
</header>
<header name="boost/compute/container/vector.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="vector"><template>
      <template-type-parameter name="T"/>
      <template-type-parameter name="Alloc"><default><classname alt="boost::compute::buffer_allocator">buffer_allocator</classname>&lt;T&gt;</default></template-type-parameter>
    </template><purpose>A resizable array of values. </purpose><description><para>The <classname alt="boost::compute::vector">vector&lt;T&gt;</classname> class stores a dynamic array of values. Internally, the data is stored in an OpenCL buffer object.</para><para>The vector class is the prefered container for storing and accessing data on a compute device. In most cases it should be used instead of directly dealing with buffer objects. If the undelying buffer is needed, it can be accessed with the get_buffer() method.</para><para>The internal storage is allocated in a specific OpenCL context which is passed as an argument to the constructor when the vector is created.</para><para>For example, to create a vector on the device containing space for ten <computeroutput>int</computeroutput> values: <programlisting language="c++">boost::compute::vector&lt;int&gt; vec(10, context);
</programlisting></para><para>Allocation and data transfer can also be performed in a single step: <programlisting language="c++">// values on the host
int data[] = { 1, 2, 3, 4 };

// create a vector of size four and copy the values from data
boost::compute::vector&lt;int&gt; vec(data, data + 4, queue);
</programlisting></para><para>The Boost.Compute <computeroutput>vector</computeroutput> class provides a STL-like API and is modeled after the <computeroutput>std::vector</computeroutput> class from the C++ standard library. It can be used with any of the STL-like algorithms provided by Boost.Compute including <computeroutput>copy()</computeroutput>, <computeroutput>transform()</computeroutput>, and <computeroutput>sort()</computeroutput> (among many others).</para><para>For example: <programlisting language="c++">// a vector on a compute device
boost::compute::vector&lt;float&gt; vec = ...

// copy data to the vector from a host std:vector
boost::compute::copy(host_vec.begin(), host_vec.end(), vec.begin(), queue);

// copy data from the vector to a host std::vector
boost::compute::copy(vec.begin(), vec.end(), host_vec.begin(), queue);

// sort the values in the vector
boost::compute::sort(vec.begin(), vec.end(), queue);

// calculate the sum of the values in the vector (also see reduce())
float sum = boost::compute::accumulate(vec.begin(), vec.end(), 0, queue);

// reverse the values in the vector
boost::compute::reverse(vec.begin(), vec.end(), queue);

// fill the vector with ones
boost::compute::fill(vec.begin(), vec.end(), 1, queue);
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::array">array&lt;T, N&gt;</classname>, buffer </para>
</para>
</para></description><typedef name="value_type"><type>T</type></typedef>
<typedef name="allocator_type"><type>Alloc</type></typedef>
<typedef name="size_type"><type>allocator_type::size_type</type></typedef>
<typedef name="difference_type"><type>allocator_type::difference_type</type></typedef>
<typedef name="reference"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="const_reference"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="pointer"><type>allocator_type::pointer</type></typedef>
<typedef name="const_pointer"><type>allocator_type::const_pointer</type></typedef>
<typedef name="iterator"><type><classname>buffer_iterator</classname>&lt; T &gt;</type></typedef>
<typedef name="const_iterator"><type><classname>buffer_iterator</classname>&lt; T &gt;</type></typedef>
<typedef name="reverse_iterator"><type>std::reverse_iterator&lt; <classname>iterator</classname> &gt;</type></typedef>
<typedef name="const_reverse_iterator"><type>std::reverse_iterator&lt; <classname>const_iterator</classname> &gt;</type></typedef>
<method-group name="public member functions">
<method name="begin"><type><classname>iterator</classname></type></method>
<method name="begin" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="cbegin" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="end"><type><classname>iterator</classname></type></method>
<method name="end" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="cend" cv="const"><type><classname>const_iterator</classname></type></method>
<method name="rbegin"><type>reverse_iterator</type></method>
<method name="rbegin" cv="const"><type>const_reverse_iterator</type></method>
<method name="crbegin" cv="const"><type>const_reverse_iterator</type></method>
<method name="rend"><type>reverse_iterator</type></method>
<method name="rend" cv="const"><type>const_reverse_iterator</type></method>
<method name="crend" cv="const"><type>const_reverse_iterator</type></method>
<method name="size" cv="const"><type>size_type</type><purpose>Returns the number of elements in the vector. </purpose></method>
<method name="max_size" cv="const"><type>size_type</type></method>
<method name="resize"><type>void</type><parameter name="size"><paramtype>size_type</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Resizes the vector to <computeroutput>size</computeroutput>. </purpose></method>
<method name="resize"><type>void</type><parameter name="size"><paramtype>size_type</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="empty" cv="const"><type>bool</type><purpose>Returns <computeroutput>true</computeroutput> if the vector is empty. </purpose></method>
<method name="capacity" cv="const"><type>size_type</type><purpose>Returns the capacity of the vector. </purpose></method>
<method name="reserve"><type>void</type><parameter name="size"><paramtype>size_type</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="reserve"><type>void</type><parameter name="size"><paramtype>size_type</paramtype></parameter></method>
<method name="shrink_to_fit"><type>void</type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="shrink_to_fit"><type>void</type></method>
<method name="operator[]"><type>reference</type><parameter name="index"><paramtype>size_type</paramtype></parameter></method>
<method name="operator[]" cv="const"><type>const_reference</type><parameter name="index"><paramtype>size_type</paramtype></parameter></method>
<method name="at"><type>reference</type><parameter name="index"><paramtype>size_type</paramtype></parameter></method>
<method name="at" cv="const"><type>const_reference</type><parameter name="index"><paramtype>size_type</paramtype></parameter></method>
<method name="front"><type>reference</type></method>
<method name="front" cv="const"><type>const_reference</type></method>
<method name="back"><type>reference</type></method>
<method name="back" cv="const"><type>const_reference</type></method>
<method name="assign"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="assign"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter></method>
<method name="assign"><type>void</type><parameter name="n"><paramtype>size_type</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="assign"><type>void</type><parameter name="n"><paramtype>size_type</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter></method>
<method name="push_back"><type>void</type><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Inserts <computeroutput>value</computeroutput> at the end of the vector (resizing if neccessary).</para><para>Note that calling <computeroutput>push_back()</computeroutput> to insert data values one at a time is inefficient as there is a non-trivial overhead in performing a data transfer to the device. It is usually better to store a set of values on the host (for example, in a <computeroutput>std::vector</computeroutput>) and then transfer them in bulk using the <computeroutput>insert()</computeroutput> method or the copy() algorithm. </para></description></method>
<method name="push_back"><type>void</type><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="pop_back"><type>void</type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="pop_back"><type>void</type></method>
<method name="insert"><type><classname>iterator</classname></type><parameter name="position"><paramtype><classname>iterator</classname></paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="insert"><type><classname>iterator</classname></type><parameter name="position"><paramtype><classname>iterator</classname></paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter></method>
<method name="insert"><type>void</type><parameter name="position"><paramtype><classname>iterator</classname></paramtype></parameter><parameter name="count"><paramtype>size_type</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="insert"><type>void</type><parameter name="position"><paramtype><classname>iterator</classname></paramtype></parameter><parameter name="count"><paramtype>size_type</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter></method>
<method name="insert"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="position"><paramtype><classname>iterator</classname></paramtype></parameter><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Inserts the values in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) into the vector at <computeroutput>position</computeroutput> using <computeroutput>queue</computeroutput>. </para></description></method>
<method name="insert"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="position"><paramtype><classname>iterator</classname></paramtype></parameter><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="erase"><type><classname>iterator</classname></type><parameter name="position"><paramtype><classname>iterator</classname></paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="erase"><type><classname>iterator</classname></type><parameter name="position"><paramtype><classname>iterator</classname></paramtype></parameter></method>
<method name="erase"><type><classname>iterator</classname></type><parameter name="first"><paramtype><classname>iterator</classname></paramtype></parameter><parameter name="last"><paramtype><classname>iterator</classname></paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="erase"><type><classname>iterator</classname></type><parameter name="first"><paramtype><classname>iterator</classname></paramtype></parameter><parameter name="last"><paramtype><classname>iterator</classname></paramtype></parameter></method>
<method name="swap"><type>void</type><parameter name="other"><paramtype><classname>vector</classname> &amp;</paramtype></parameter><purpose>Swaps the contents of <computeroutput>*this</computeroutput> with <computeroutput>other</computeroutput>. </purpose></method>
<method name="clear"><type>void</type><purpose>Removes all elements from the vector. </purpose></method>
<method name="get_allocator" cv="const"><type>allocator_type</type></method>
<method name="get_buffer" cv="const"><type>const buffer &amp;</type><purpose>Returns the underlying buffer. </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="context"><paramtype>const context &amp;</paramtype><default>system::default_context()</default></parameter><purpose>Creates an empty vector in <computeroutput>context</computeroutput>. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="count"><paramtype>size_type</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype><default>system::default_context()</default></parameter><description><para>Creates a vector with space for <computeroutput>count</computeroutput> elements in <computeroutput>context</computeroutput>.</para><para>Note that unlike <computeroutput>std::vector's</computeroutput> constructor, this will not initialize the values in the container. Either call the vector constructor which takes a value to initialize with or use the fill() algorithm to set the initial values.</para><para>For example: <programlisting language="c++">// create a vector on the device with space for ten ints
boost::compute::vector&lt;int&gt; vec(10, context);
</programlisting> </para></description></constructor>
<constructor><parameter name="count"><paramtype>size_type</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Creates a vector with space for <computeroutput>count</computeroutput> elements and sets each equal to <computeroutput>value</computeroutput>.</para><para>For example: <programlisting language="c++">// creates a vector with four values set to nine (e.g. [9, 9, 9, 9]).
boost::compute::vector&lt;int&gt; vec(4, 9, queue);
</programlisting> </para></description></constructor>
<constructor><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><description><para>Creates a vector with space for the values in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>) and copies them into the vector with <computeroutput>queue</computeroutput>.</para><para>For example: <programlisting language="c++">// values on the host
int data[] = { 1, 2, 3, 4 };

// create a vector of size four and copy the values from data
boost::compute::vector&lt;int&gt; vec(data, data + 4, queue);
</programlisting> </para></description></constructor>
<constructor><parameter name="other"><paramtype>const <classname>vector</classname> &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><purpose>Creates a new vector and copies the values from <computeroutput>other</computeroutput>. </purpose></constructor>
<constructor><template>
          <template-type-parameter name="OtherAlloc"/>
        </template><parameter name="other"><paramtype>const <classname>vector</classname>&lt; T, OtherAlloc &gt; &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><purpose>Creates a new vector and copies the values from <computeroutput>other</computeroutput>. </purpose></constructor>
<constructor><template>
          <template-type-parameter name="OtherAlloc"/>
        </template><parameter name="vector"><paramtype>const std::vector&lt; T, OtherAlloc &gt; &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter><purpose>Creates a new vector and copies the values from <computeroutput>vector</computeroutput>. </purpose></constructor>
<constructor><parameter name="list"><paramtype>std::initializer_list&lt; T &gt;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><default>system::default_queue()</default></parameter></constructor>
<copy-assignment><type><classname>vector</classname> &amp;</type><parameter name="other"><paramtype>const <classname>vector</classname> &amp;</paramtype></parameter></copy-assignment>
<copy-assignment><type><classname>vector</classname> &amp;</type><template>
          <template-type-parameter name="OtherAlloc"/>
        </template><parameter name="other"><paramtype>const <classname>vector</classname>&lt; T, OtherAlloc &gt; &amp;</paramtype></parameter></copy-assignment>
<copy-assignment><type><classname>vector</classname> &amp;</type><template>
          <template-type-parameter name="OtherAlloc"/>
        </template><parameter name="vector"><paramtype>const std::vector&lt; T, OtherAlloc &gt; &amp;</paramtype></parameter></copy-assignment>
<constructor><parameter name="other"><paramtype><classname>vector</classname> &amp;&amp;</paramtype></parameter><purpose>Move-constructs a new vector from <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>vector</classname> &amp;</type><parameter name="other"><paramtype><classname>vector</classname> &amp;&amp;</paramtype></parameter><purpose>Move-assigns the data from <computeroutput>other</computeroutput> to <computeroutput>*this</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the vector object. </purpose></destructor>
<method-group name="private member functions">
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/exception/context_error.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="context_error"><inherit access="public">exception</inherit><purpose>A run-time OpenCL context error. </purpose><description><para>The <classname alt="boost::compute::context_error">context_error</classname> exception is thrown when the OpenCL context encounters an error condition. Boost.Compute is notified of these error conditions by registering an error handler when creating context objects (via the <computeroutput>pfn_notify</computeroutput> argument to the <computeroutput>clCreateContext()</computeroutput> function).</para><para>This exception is different than the <classname alt="boost::compute::opencl_error">opencl_error</classname> exception which is thrown as a result of error caused when calling a single OpenCL API function.</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::opencl_error">opencl_error</classname> </para>
</para>
</para></description><method-group name="public member functions">
<method name="what" cv="const"><type>const char *</type><purpose>Returns a string with a description of the error. </purpose></method>
<method name="get_context_ptr" cv="const"><type>const context *</type><description><para>Returns a pointer to the context object which generated the error notification. </para></description></method>
<method name="get_private_info_ptr" cv="const"><type>const void *</type><purpose>Returns a pointer to the private info memory block. </purpose></method>
<method name="get_private_info_size" cv="const"><type>size_t</type><purpose>Returns the size of the private info memory block. </purpose></method>
</method-group>
<constructor><parameter name="context"><paramtype>const context *</paramtype></parameter><parameter name="errinfo"><paramtype>const char *</paramtype></parameter><parameter name="private_info"><paramtype>const void *</paramtype></parameter><parameter name="private_info_size"><paramtype>size_t</paramtype></parameter><purpose>Creates a new context error exception object. </purpose></constructor>
<destructor><purpose>Destroys the context error object. </purpose></destructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/exception/no_device_found.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="no_device_found"><inherit access="public">exception</inherit><purpose>Exception thrown when no OpenCL device is found. </purpose><description><para>This exception is thrown when no valid OpenCL device can be found.</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::opencl_error">opencl_error</classname> </para>
</para>
</para></description><method-group name="public member functions">
<method name="what" cv="const"><type>const char *</type><purpose>Returns a string containing a human-readable error message. </purpose></method>
</method-group>
<constructor><purpose>Creates a new <classname alt="boost::compute::no_device_found">no_device_found</classname> exception object. </purpose></constructor>
<destructor><purpose>Destroys the <classname alt="boost::compute::no_device_found">no_device_found</classname> exception object. </purpose></destructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/exception/opencl_error.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="opencl_error"><inherit access="public">exception</inherit><purpose>A run-time OpenCL error. </purpose><description><para>The <classname alt="boost::compute::opencl_error">opencl_error</classname> class represents an error returned from an OpenCL function.</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::context_error">context_error</classname> </para>
</para>
</para></description><method-group name="public member functions">
<method name="error_code" cv="const"><type>cl_int</type><purpose>Returns the numeric error code. </purpose></method>
<method name="error_string" cv="const"><type>std::string</type><purpose>Returns a string description of the error. </purpose></method>
<method name="what" cv="const"><type>const char *</type><purpose>Returns a C-string description of the error. </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="error"><paramtype>cl_int</paramtype></parameter><purpose>Creates a new <classname alt="boost::compute::opencl_error">opencl_error</classname> exception object for <computeroutput>error</computeroutput>. </purpose></constructor>
<destructor><purpose>Destroys the <classname alt="boost::compute::opencl_error">opencl_error</classname> object. </purpose></destructor>
<method-group name="public static functions">
<method name="to_string" specifiers="static"><type>std::string</type><parameter name="error"><paramtype>cl_int</paramtype></parameter><description><para>Static function which converts the numeric OpenCL error code <computeroutput>error</computeroutput> to a human-readable string.</para><para>For example: <programlisting language="c++">std::cout &lt;&lt; opencl_error::to_string(CL_INVALID_KERNEL_ARGS) &lt;&lt; std::endl;
</programlisting></para><para>Will print "Invalid Kernel Arguments".</para><para>If the error code is unknown (e.g. not a valid OpenCL error), a string containing "Unknown OpenCL Error" along with the error number will be returned. </para></description></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/exception/program_build_failure.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="program_build_failure"><inherit access="public">boost::compute::opencl_error</inherit><purpose>A failure when building OpenCL program. </purpose><description><para>Instances of this class are thrown when OpenCL program build fails. Extends <classname alt="boost::compute::opencl_error">opencl_error</classname> by saving a program build log so it can be used for testing, debugging, or logging purposes.</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::opencl_error">opencl_error</classname> </para>
</para>
</para></description><method-group name="public member functions">
<method name="build_log" cv="const"><type>std::string</type><purpose>Retrieve the log of a failed program build. </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="error"><paramtype>cl_int</paramtype></parameter><parameter name="build_log"><paramtype>const std::string &amp;</paramtype></parameter><description><para>Creates a new <classname alt="boost::compute::program_build_failure">program_build_failure</classname> exception object for <computeroutput>error</computeroutput> and <computeroutput>build_log</computeroutput>. </para></description></constructor>
<destructor><purpose>Destroys the <classname alt="boost::compute::program_build_failure">program_build_failure</classname> object. </purpose></destructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/exception/unsupported_extension_error.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="unsupported_extension_error"><inherit access="public">exception</inherit><purpose>Exception thrown when attempting to use an unsupported OpenCL extension. </purpose><description><para>This exception is thrown when the user attempts to use an OpenCL extension which is not supported on the platform and/or device.</para><para>An example of this is attempting to use CL-GL sharing on a non-GPU device.</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::opencl_error">opencl_error</classname> </para>
</para>
</para></description><method-group name="public member functions">
<method name="extension_name" cv="const"><type>std::string</type><purpose>Returns the name of the unsupported extension. </purpose></method>
<method name="what" cv="const"><type>const char *</type><description><para>Returns a string containing a human-readable error message containing the name of the unsupported exception. </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="extension"><paramtype>const char *</paramtype></parameter><description><para>Creates a new unsupported extension error exception object indicating that <computeroutput>extension</computeroutput> is not supported by the OpenCL platform or device. </para></description></constructor>
<destructor><purpose>Destroys the unsupported extension error object. </purpose></destructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/as.hpp">
<namespace name="boost">
<namespace name="compute">
<struct name="as"><template>
      <template-type-parameter name="T"/>
    </template><description><para>The <classname alt="boost::compute::as">as</classname> function converts its argument to type <computeroutput>T</computeroutput> (similar to reinterpret_cast&lt;T&gt;).</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::convert">convert&lt;T&gt;</classname> </para>
</para>
</para></description><typedef name="result_type"><type>T</type></typedef>
<method-group name="public member functions">
</method-group>
</struct>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/atomic.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="atomic_add"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">function&lt; T(T *, T)&gt;</inherit><method-group name="public member functions">
</method-group>
<constructor/>
</class><class name="atomic_and"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">function&lt; T(T *, T)&gt;</inherit><method-group name="public member functions">
</method-group>
<constructor/>
</class><class name="atomic_cmpxchg"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">function&lt; T(T *, T, T)&gt;</inherit><method-group name="public member functions">
</method-group>
<constructor/>
</class><class name="atomic_dec"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">function&lt; T(T *)&gt;</inherit><method-group name="public member functions">
</method-group>
<constructor/>
</class><class name="atomic_inc"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">function&lt; T(T *)&gt;</inherit><method-group name="public member functions">
</method-group>
<constructor/>
</class><class name="atomic_max"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">function&lt; T(T *, T)&gt;</inherit><method-group name="public member functions">
</method-group>
<constructor/>
</class><class name="atomic_min"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">function&lt; T(T *, T)&gt;</inherit><method-group name="public member functions">
</method-group>
<constructor/>
</class><class name="atomic_or"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">function&lt; T(T *, T)&gt;</inherit><method-group name="public member functions">
</method-group>
<constructor/>
</class><class name="atomic_sub"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">function&lt; T(T *, T)&gt;</inherit><method-group name="public member functions">
</method-group>
<constructor/>
</class><class name="atomic_xchg"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">function&lt; T(T *, T)&gt;</inherit><method-group name="public member functions">
</method-group>
<constructor/>
</class><class name="atomic_xor"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">function&lt; T(T *, T)&gt;</inherit><method-group name="public member functions">
</method-group>
<constructor/>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/bind.hpp">
<namespace name="boost">
<namespace name="compute">
<struct name="is_placeholder"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">false_type</inherit><purpose>Meta-function returning <computeroutput>true</computeroutput> if <computeroutput>T</computeroutput> is a placeholder type. </purpose></struct><namespace name="placeholders">
<data-member name="_1"><type><classname>placeholder</classname>&lt; 0 &gt; const</type></data-member>
<data-member name="_2"><type><classname>placeholder</classname>&lt; 1 &gt; const</type></data-member>
</namespace>
















































































































<function name="bind"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="F"/>
          <template-nontype-parameter name="Args"><type>class...</type></template-nontype-parameter>
        </template><parameter name="f"><paramtype>F</paramtype></parameter><parameter name="args"><paramtype>Args...</paramtype></parameter><description><para>Returns a function wrapper which invokes <computeroutput>f</computeroutput> with <computeroutput>args</computeroutput> when called.</para><para>For example, to generate a unary function object which returns <computeroutput>true</computeroutput> when its argument is less than <computeroutput>7</computeroutput>: <programlisting language="c++">using boost::compute::less;
using boost::compute::placeholders::_1;

auto less_than_seven = boost::compute::bind(less&lt;int&gt;(), _1, 7);
</programlisting> </para></description></function>




















</namespace>
</namespace>
</header>
<header name="boost/compute/functional/common.hpp">
<namespace name="boost">
<namespace name="compute">





































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/convert.hpp">
<namespace name="boost">
<namespace name="compute">
<struct name="convert"><template>
      <template-type-parameter name="T"/>
    </template><description><para>The <classname alt="boost::compute::convert">convert</classname> function converts its argument to type <computeroutput>T</computeroutput> (similar to static_cast&lt;T&gt;).</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::as">as&lt;T&gt;</classname> </para>
</para>
</para></description><typedef name="result_type"><type>T</type></typedef>
<method-group name="public member functions">
</method-group>
</struct>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/field.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="field"><template>
      <template-type-parameter name="T"/>
    </template><description><para>Returns the named field from a value.</para><para>The template-type <computeroutput>T</computeroutput> specifies the field's value type. Note that the value type must match the actual type of the field otherwise runtime compilation or logic errors may occur.</para><para>For example, to access the <computeroutput>second</computeroutput> field in a <computeroutput>std::pair&lt;int, float&gt;</computeroutput> object: <programlisting language="c++">field&lt;float&gt;("second");
</programlisting></para><para>This can also be used with vector types to access individual components as well as perform swizzle operations.</para><para>For example, to access the first and third components of an <computeroutput>int</computeroutput> vector type (e.g. <computeroutput>int4</computeroutput>): <programlisting language="c++">field&lt;int2_&gt;("xz");
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::get">get&lt;N&gt;</classname> </para>
</para>
</para></description><typedef name="result_type"><purpose>Result type. </purpose><type>T</type></typedef>
<method-group name="public member functions">
</method-group>
<constructor><parameter name="field"><paramtype>const std::string &amp;</paramtype></parameter><purpose>Creates a new field functor with <computeroutput>field</computeroutput>. </purpose></constructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/geometry.hpp">
<namespace name="boost">
<namespace name="compute">





































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/get.hpp">
<namespace name="boost">
<namespace name="compute">
<struct name="get"><template>
      <template-nontype-parameter name="N"><type>size_t</type></template-nontype-parameter>
    </template><description><para>Returns the <computeroutput>N'th</computeroutput> element of an aggregate type (e.g. scalarN, pair, tuple, etc.).</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::field">field&lt;T&gt;</classname> </para>
</para>
</para></description><method-group name="public member functions">
<method name="operator()" cv="const"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="Arg"/>
        </template><parameter name="arg"><paramtype>const Arg &amp;</paramtype></parameter></method>
</method-group>
</struct>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/hash.hpp">
<namespace name="boost">
<namespace name="compute">
<struct name="hash"><template>
      <template-type-parameter name="Key"/>
    </template><description><para>The hash function returns a hash value for the input value.</para><para>The return type is <computeroutput>ulong_</computeroutput> (the OpenCL unsigned long type). </para></description></struct>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/identity.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="identity"><template>
      <template-type-parameter name="T"/>
    </template><description><para>Identity function which simply returns its input.</para><para>For example, to directly copy values using the transform() algorithm: <programlisting language="c++">transform(input.begin(), input.end(), output.begin(), identity&lt;int&gt;(), queue);
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::as">as&lt;T&gt;</classname>, <classname alt="boost::compute::convert">convert&lt;T&gt;</classname> </para>
</para>
</para></description><typedef name="result_type"><purpose>Identity function result type. </purpose><type>T</type></typedef>
<method-group name="public member functions">
</method-group>
<constructor><purpose>Creates a new identity function. </purpose></constructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/integer.hpp">
<namespace name="boost">
<namespace name="compute">





































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/logical.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="binary_negate"><template>
      <template-type-parameter name="Predicate"/>
    </template><inherit access="public">boost::compute::binary_function&lt; void, void, int &gt;</inherit><description><para>The binnary_negate function adaptor negates a binary function.</para><para><para><emphasis role="bold">See Also:</emphasis><para>not2() </para>
</para>
</para></description><method-group name="public member functions">
</method-group>
<constructor specifiers="explicit"><parameter name="pred"><paramtype>Predicate</paramtype></parameter></constructor>
</class><struct name="logical_not"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">boost::compute::unary_function&lt; T, int &gt;</inherit><description><para>The <classname alt="boost::compute::logical_not">logical_not</classname> function negates its argument and returns it.</para><para><para><emphasis role="bold">See Also:</emphasis><para>not1(), not2() </para>
</para>
</para></description><method-group name="public member functions">
</method-group>
</struct><class name="unary_negate"><template>
      <template-type-parameter name="Predicate"/>
    </template><inherit access="public">boost::compute::unary_function&lt; void, int &gt;</inherit><description><para>The <classname alt="boost::compute::unary_negate">unary_negate</classname> function adaptor negates a unary function.</para><para><para><emphasis role="bold">See Also:</emphasis><para>not1() </para>
</para>
</para></description><method-group name="public member functions">
</method-group>
<constructor specifiers="explicit"><parameter name="pred"><paramtype>Predicate</paramtype></parameter></constructor>
</class>













































































































<function name="not1"><type><classname>unary_negate</classname>&lt; Predicate &gt;</type><template>
          <template-type-parameter name="Predicate"/>
        </template><parameter name="predicate"><paramtype>const Predicate &amp;</paramtype><description><para>the unary function to wrap</para></description></parameter><description><para>Returns a <classname alt="boost::compute::unary_negate">unary_negate</classname> adaptor around <computeroutput>predicate</computeroutput>.</para><para>

</para></description><returns><para>a <classname alt="boost::compute::unary_negate">unary_negate</classname> wrapper around <computeroutput>predicate</computeroutput> </para>
</returns></function>
<function name="not2"><type><classname>binary_negate</classname>&lt; Predicate &gt;</type><template>
          <template-type-parameter name="Predicate"/>
        </template><parameter name="predicate"><paramtype>const Predicate &amp;</paramtype><description><para>the binary function to wrap</para></description></parameter><description><para>Returns a <classname alt="boost::compute::binary_negate">binary_negate</classname> adaptor around <computeroutput>predicate</computeroutput>.</para><para>

</para></description><returns><para>a <classname alt="boost::compute::binary_negate">binary_negate</classname> wrapper around <computeroutput>predicate</computeroutput> </para>
</returns></function>





















</namespace>
</namespace>
</header>
<header name="boost/compute/functional/math.hpp">
<namespace name="boost">
<namespace name="compute">





































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/operator.hpp">
<namespace name="boost">
<namespace name="compute">





































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/popcount.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="popcount"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">function&lt; T(T)&gt;</inherit><description><para>Returns the number of non-zero bits in <computeroutput>x</computeroutput>.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/popcount.html">popcount()</ulink> for more information. </para></description><method-group name="public member functions">
</method-group>
<constructor/>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/functional/relational.hpp">
<namespace name="boost">
<namespace name="compute">





































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/image/image1d.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="image1d"><inherit access="public">boost::compute::image_object</inherit><purpose>An OpenCL 1D image object. </purpose><description><para><warning><para>This method is only available if the OpenCL version is 1.2 or later.</para>
</warning>
<para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::image_format">image_format</classname>, <classname alt="boost::compute::image2d">image2d</classname> </para>
</para>
</para></description><method-group name="public member functions">
<method name="size" cv="const"><type><classname>extents</classname>&lt; 1 &gt;</type><purpose>Returns the size (width) of the image. </purpose></method>
<method name="origin" cv="const"><type><classname>extents</classname>&lt; 1 &gt;</type><purpose>Returns the origin of the image (<computeroutput>0</computeroutput>). </purpose></method>
<method name="get_info" cv="const"><type>T</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="info"><paramtype>cl_image_info</paramtype></parameter><description><para>Returns information about the image.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetImageInfo.html">clGetImageInfo()</ulink> for more information. </para></description></method>
<method name="get_info" cv="const"><type><emphasis>unspecified</emphasis></type><template>
          <template-nontype-parameter name="Enum"><type>int</type></template-nontype-parameter>
        </template><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="clone" cv="const"><type><classname>image1d</classname></type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Creates a new image with a copy of the data in <computeroutput>*this</computeroutput>. Uses <computeroutput>queue</computeroutput> to perform the copy operation. </para></description></method>
</method-group>
<constructor><purpose>Creates a null <classname alt="boost::compute::image1d">image1d</classname> object. </purpose></constructor>
<constructor><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="image_width"><paramtype>size_t</paramtype></parameter><parameter name="format"><paramtype>const <classname>image_format</classname> &amp;</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><parameter name="host_ptr"><paramtype>void *</paramtype><default>0</default></parameter><description><para>Creates a new <classname alt="boost::compute::image1d">image1d</classname> object.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clCreateImage.html">clCreateImage()</ulink> for more information. </para></description></constructor>
<constructor><parameter name="other"><paramtype>const <classname>image1d</classname> &amp;</paramtype></parameter><purpose>Creates a new <classname alt="boost::compute::image1d">image1d</classname> as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>image1d</classname> &amp;</type><parameter name="other"><paramtype>const <classname>image1d</classname> &amp;</paramtype></parameter><purpose>Copies the <classname alt="boost::compute::image1d">image1d</classname> from <computeroutput>other</computeroutput>. </purpose></copy-assignment>
<constructor cv="noexcept"><parameter name="other"><paramtype><classname>image1d</classname> &amp;&amp;</paramtype></parameter><purpose>Move-constructs a new image object from <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment cv="noexcept"><type><classname>image1d</classname> &amp;</type><parameter name="other"><paramtype><classname>image1d</classname> &amp;&amp;</paramtype></parameter><purpose>Move-assigns the image from <computeroutput>other</computeroutput> to <computeroutput>*this</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the <classname alt="boost::compute::image1d">image1d</classname> object. </purpose></destructor>
<method-group name="public static functions">
<method name="get_supported_formats" specifiers="static"><type>std::vector&lt; <classname>image_format</classname> &gt;</type><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><description><para>Returns the supported image formats for the context.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetSupportedImageFormats.html">clGetSupportedImageFormats()</ulink> for more information. </para></description></method>
<method name="is_supported_format" specifiers="static"><type>bool</type><parameter name="format"><paramtype>const <classname>image_format</classname> &amp;</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><description><para>Returns <computeroutput>true</computeroutput> if <computeroutput>format</computeroutput> is a supported 1D image format for <computeroutput>context</computeroutput>. </para></description></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/image/image2d.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="image2d"><inherit access="public">boost::compute::image_object</inherit><purpose>An OpenCL 2D image object. </purpose><description><para>For example, to create a 640x480 8-bit RGBA image:</para><para><programlisting language="c++"/> <para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::image_format">image_format</classname>, <classname alt="boost::compute::image3d">image3d</classname> </para>
</para>
</para></description><method-group name="public member functions">
<method name="size" cv="const"><type><classname>extents</classname>&lt; 2 &gt;</type><purpose>Returns the size (width, height) of the image. </purpose></method>
<method name="origin" cv="const"><type><classname>extents</classname>&lt; 2 &gt;</type><purpose>Returns the origin of the image (<computeroutput>0</computeroutput>, <computeroutput>0</computeroutput>). </purpose></method>
<method name="get_info" cv="const"><type>T</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="info"><paramtype>cl_image_info</paramtype></parameter><description><para>Returns information about the image.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetImageInfo.html">clGetImageInfo()</ulink> for more information. </para></description></method>
<method name="get_info" cv="const"><type><emphasis>unspecified</emphasis></type><template>
          <template-nontype-parameter name="Enum"><type>int</type></template-nontype-parameter>
        </template><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="clone" cv="const"><type><classname>image2d</classname></type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Creates a new image with a copy of the data in <computeroutput>*this</computeroutput>. Uses <computeroutput>queue</computeroutput> to perform the copy operation. </para></description></method>
</method-group>
<constructor><purpose>Creates a null <classname alt="boost::compute::image2d">image2d</classname> object. </purpose></constructor>
<constructor><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="image_width"><paramtype>size_t</paramtype></parameter><parameter name="image_height"><paramtype>size_t</paramtype></parameter><parameter name="format"><paramtype>const <classname>image_format</classname> &amp;</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><parameter name="host_ptr"><paramtype>void *</paramtype><default>0</default></parameter><parameter name="image_row_pitch"><paramtype>size_t</paramtype><default>0</default></parameter><description><para>Creates a new <classname alt="boost::compute::image2d">image2d</classname> object.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clCreateImage.html">clCreateImage()</ulink> for more information. </para></description></constructor>
<constructor><parameter name="other"><paramtype>const <classname>image2d</classname> &amp;</paramtype></parameter><purpose>Creates a new <classname alt="boost::compute::image2d">image2d</classname> as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>image2d</classname> &amp;</type><parameter name="other"><paramtype>const <classname>image2d</classname> &amp;</paramtype></parameter><purpose>Copies the <classname alt="boost::compute::image2d">image2d</classname> from <computeroutput>other</computeroutput>. </purpose></copy-assignment>
<constructor cv="noexcept"><parameter name="other"><paramtype><classname>image2d</classname> &amp;&amp;</paramtype></parameter><purpose>Move-constructs a new image object from <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment cv="noexcept"><type><classname>image2d</classname> &amp;</type><parameter name="other"><paramtype><classname>image2d</classname> &amp;&amp;</paramtype></parameter><purpose>Move-assigns the image from <computeroutput>other</computeroutput> to <computeroutput>*this</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the <classname alt="boost::compute::image2d">image2d</classname> object. </purpose></destructor>
<method-group name="public static functions">
<method name="get_supported_formats" specifiers="static"><type>std::vector&lt; <classname>image_format</classname> &gt;</type><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><description><para>Returns the supported image formats for the context.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetSupportedImageFormats.html">clGetSupportedImageFormats()</ulink> for more information. </para></description></method>
<method name="is_supported_format" specifiers="static"><type>bool</type><parameter name="format"><paramtype>const <classname>image_format</classname> &amp;</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><description><para>Returns <computeroutput>true</computeroutput> if <computeroutput>format</computeroutput> is a supported 2D image format for <computeroutput>context</computeroutput>. </para></description></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/image/image3d.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="image3d"><inherit access="public">boost::compute::image_object</inherit><purpose>An OpenCL 3D image object. </purpose><description><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::image_format">image_format</classname>, <classname alt="boost::compute::image2d">image2d</classname> </para>
</para>
</para></description><method-group name="public member functions">
<method name="size" cv="const"><type><classname>extents</classname>&lt; 3 &gt;</type><purpose>Returns the size (width, height, depth) of the image. </purpose></method>
<method name="origin" cv="const"><type><classname>extents</classname>&lt; 3 &gt;</type><purpose>Returns the origin of the image (<computeroutput>0</computeroutput>, <computeroutput>0</computeroutput>, <computeroutput>0</computeroutput>). </purpose></method>
<method name="get_info" cv="const"><type>T</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="info"><paramtype>cl_image_info</paramtype></parameter><description><para>Returns information about the image.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetImageInfo.html">clGetImageInfo()</ulink> for more information. </para></description></method>
<method name="get_info" cv="const"><type><emphasis>unspecified</emphasis></type><template>
          <template-nontype-parameter name="Enum"><type>int</type></template-nontype-parameter>
        </template><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="clone" cv="const"><type><classname>image3d</classname></type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Creates a new image with a copy of the data in <computeroutput>*this</computeroutput>. Uses <computeroutput>queue</computeroutput> to perform the copy operation. </para></description></method>
</method-group>
<constructor><purpose>Creates a null <classname alt="boost::compute::image3d">image3d</classname> object. </purpose></constructor>
<constructor><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="image_width"><paramtype>size_t</paramtype></parameter><parameter name="image_height"><paramtype>size_t</paramtype></parameter><parameter name="image_depth"><paramtype>size_t</paramtype></parameter><parameter name="format"><paramtype>const <classname>image_format</classname> &amp;</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><parameter name="host_ptr"><paramtype>void *</paramtype><default>0</default></parameter><parameter name="image_row_pitch"><paramtype>size_t</paramtype><default>0</default></parameter><parameter name="image_slice_pitch"><paramtype>size_t</paramtype><default>0</default></parameter><description><para>Creates a new <classname alt="boost::compute::image3d">image3d</classname> object.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clCreateImage.html">clCreateImage()</ulink> for more information. </para></description></constructor>
<constructor><parameter name="other"><paramtype>const <classname>image3d</classname> &amp;</paramtype></parameter><purpose>Creates a new <classname alt="boost::compute::image3d">image3d</classname> as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>image3d</classname> &amp;</type><parameter name="other"><paramtype>const <classname>image3d</classname> &amp;</paramtype></parameter><purpose>Copies the <classname alt="boost::compute::image3d">image3d</classname> from <computeroutput>other</computeroutput>. </purpose></copy-assignment>
<constructor cv="noexcept"><parameter name="other"><paramtype><classname>image3d</classname> &amp;&amp;</paramtype></parameter><purpose>Move-constructs a new image object from <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment cv="noexcept"><type><classname>image3d</classname> &amp;</type><parameter name="other"><paramtype><classname>image3d</classname> &amp;&amp;</paramtype></parameter><purpose>Move-assigns the image from <computeroutput>other</computeroutput> to <computeroutput>*this</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the <classname alt="boost::compute::image3d">image3d</classname> object. </purpose></destructor>
<method-group name="public static functions">
<method name="get_supported_formats" specifiers="static"><type>std::vector&lt; <classname>image_format</classname> &gt;</type><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><description><para>Returns the supported 3D image formats for the context.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetSupportedImageFormats.html">clGetSupportedImageFormats()</ulink> for more information. </para></description></method>
<method name="is_supported_format" specifiers="static"><type>bool</type><parameter name="format"><paramtype>const <classname>image_format</classname> &amp;</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><description><para>Returns <computeroutput>true</computeroutput> if <computeroutput>format</computeroutput> is a supported 3D image format for <computeroutput>context</computeroutput>. </para></description></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/image/image_format.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="image_format"><purpose>A OpenCL image format. </purpose><description><para>For example, to create a format for a 8-bit RGBA image: <programlisting language="c++">boost::compute::image_format rgba8(CL_RGBA, CL_UNSIGNED_INT8);
</programlisting></para><para>After being constructed, <classname alt="boost::compute::image_format">image_format</classname> objects are usually passed to the constructor of the various image classes (e.g. <classname alt="boost::compute::image2d">image2d</classname>, <classname alt="boost::compute::image3d">image3d</classname>) to create an image object on a compute device.</para><para>Image formats supported by a context can be queried with the static get_supported_formats() in each image class. For example: <programlisting language="c++">std::vector&lt;image_format&gt; formats = image2d::get_supported_formats(ctx);
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::image2d">image2d</classname> </para>
</para>
</para></description><enum name="channel_order"><enumvalue name="r"><default>= CL_R</default></enumvalue><enumvalue name="a"><default>= CL_A</default></enumvalue><enumvalue name="intensity"><default>= CL_INTENSITY</default></enumvalue><enumvalue name="luminance"><default>= CL_LUMINANCE</default></enumvalue><enumvalue name="rg"><default>= CL_RG</default></enumvalue><enumvalue name="ra"><default>= CL_RA</default></enumvalue><enumvalue name="rgb"><default>= CL_RGB</default></enumvalue><enumvalue name="rgba"><default>= CL_RGBA</default></enumvalue><enumvalue name="argb"><default>= CL_ARGB</default></enumvalue><enumvalue name="bgra"><default>= CL_BGRA</default></enumvalue></enum>
<enum name="channel_data_type"><enumvalue name="snorm_int8"><default>= CL_SNORM_INT8</default></enumvalue><enumvalue name="snorm_int16"><default>= CL_SNORM_INT16</default></enumvalue><enumvalue name="unorm_int8"><default>= CL_UNORM_INT8</default></enumvalue><enumvalue name="unorm_int16"><default>= CL_UNORM_INT16</default></enumvalue><enumvalue name="unorm_short_565"><default>= CL_UNORM_SHORT_565</default></enumvalue><enumvalue name="unorm_short_555"><default>= CL_UNORM_SHORT_555</default></enumvalue><enumvalue name="unorm_int_101010"><default>= CL_UNORM_INT_101010</default></enumvalue><enumvalue name="signed_int8"><default>= CL_SIGNED_INT8</default></enumvalue><enumvalue name="signed_int16"><default>= CL_SIGNED_INT16</default></enumvalue><enumvalue name="signed_int32"><default>= CL_SIGNED_INT32</default></enumvalue><enumvalue name="unsigned_int8"><default>= CL_UNSIGNED_INT8</default></enumvalue><enumvalue name="unsigned_int16"><default>= CL_UNSIGNED_INT16</default></enumvalue><enumvalue name="unsigned_int32"><default>= CL_UNSIGNED_INT32</default></enumvalue><enumvalue name="float16"><default>= CL_HALF_FLOAT</default></enumvalue><enumvalue name="float32"><default>= CL_FLOAT</default></enumvalue></enum>
<method-group name="public member functions">
<method name="get_format_ptr" cv="const"><type>const cl_image_format *</type><purpose>Returns a pointer to the <computeroutput>cl_image_format</computeroutput> object. </purpose></method>
<method name="operator==" cv="const"><type>bool</type><parameter name="other"><paramtype>const <classname>image_format</classname> &amp;</paramtype></parameter><purpose>Returns <computeroutput>true</computeroutput> if <computeroutput>*this</computeroutput> is the same as <computeroutput>other</computeroutput>. </purpose></method>
<method name="operator!=" cv="const"><type>bool</type><parameter name="other"><paramtype>const <classname>image_format</classname> &amp;</paramtype></parameter><purpose>Returns <computeroutput>true</computeroutput> if <computeroutput>*this</computeroutput> is not the same as <computeroutput>other</computeroutput>. </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="order"><paramtype>cl_channel_order</paramtype></parameter><parameter name="type"><paramtype>cl_channel_type</paramtype></parameter><purpose>Creates a new image format object with <computeroutput>order</computeroutput> and <computeroutput>type</computeroutput>. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="format"><paramtype>const cl_image_format &amp;</paramtype></parameter><purpose>Creates a new image format object from <computeroutput>format</computeroutput>. </purpose></constructor>
<constructor><parameter name="other"><paramtype>const <classname>image_format</classname> &amp;</paramtype></parameter><purpose>Creates a new image format object as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>image_format</classname> &amp;</type><parameter name="other"><paramtype>const <classname>image_format</classname> &amp;</paramtype></parameter><purpose>Copies the format from <computeroutput>other</computeroutput> to <computeroutput>*this</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the image format object. </purpose></destructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/image/image_object.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="image_object"><inherit access="public">memory_object</inherit><purpose>Base-class for image objects. </purpose><description><para>The <classname alt="boost::compute::image_object">image_object</classname> class is the base-class for image objects on compute devices.</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::image1d">image1d</classname>, <classname alt="boost::compute::image2d">image2d</classname>, <classname alt="boost::compute::image3d">image3d</classname> </para>
</para>
</para></description><method-group name="public member functions">
<method name="get_image_info" cv="const"><type>T</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="info"><paramtype>cl_mem_info</paramtype></parameter><description><para>Returns information about the image object.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetImageInfo.html">clGetImageInfo()</ulink> for more information. </para></description></method>
<method name="format" cv="const"><type><classname>image_format</classname></type><purpose>Returns the format for the image. </purpose></method>
<method name="width" cv="const"><type>size_t</type><purpose>Returns the width of the image. </purpose></method>
<method name="height" cv="const"><type>size_t</type><description><para>Returns the height of the image.</para><para>For 1D images, this function will return <computeroutput>1</computeroutput>. </para></description></method>
<method name="depth" cv="const"><type>size_t</type><description><para>Returns the depth of the image.</para><para>For 1D and 2D images, this function will return <computeroutput>1</computeroutput>. </para></description></method>
</method-group>
<constructor/>
<constructor specifiers="explicit"><parameter name="mem"><paramtype>cl_mem</paramtype></parameter><parameter name="retain"><paramtype>bool</paramtype><default>true</default></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>image_object</classname> &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>image_object</classname> &amp;</type><parameter name="other"><paramtype>const <classname>image_object</classname> &amp;</paramtype></parameter></copy-assignment>
<constructor cv="noexcept"><parameter name="other"><paramtype><classname>image_object</classname> &amp;&amp;</paramtype></parameter></constructor>
<destructor><purpose>Destroys the image object. </purpose></destructor>
<method-group name="public static functions">
<method name="get_supported_formats" specifiers="static"><type>std::vector&lt; <classname>image_format</classname> &gt;</type><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="type"><paramtype>cl_mem_object_type</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><description><para>Returns the supported image formats for the <computeroutput>type</computeroutput> in <computeroutput>context</computeroutput>.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetSupportedImageFormats.html">clGetSupportedImageFormats()</ulink> for more information. </para></description></method>
<method name="is_supported_format" specifiers="static"><type>bool</type><parameter name="format"><paramtype>const <classname>image_format</classname> &amp;</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="type"><paramtype>cl_mem_object_type</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><description><para>Returns <computeroutput>true</computeroutput> if <computeroutput>format</computeroutput> is a supported image format for <computeroutput>type</computeroutput> in <computeroutput>context</computeroutput> with <computeroutput>flags</computeroutput>. </para></description></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/image/image_sampler.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="image_sampler"><purpose>An OpenCL image sampler object. </purpose><description><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::image2d">image2d</classname>, <classname alt="boost::compute::image_format">image_format</classname> </para>
</para>
</para></description><enum name="addressing_mode"><enumvalue name="none"><default>= CL_ADDRESS_NONE</default></enumvalue><enumvalue name="clamp_to_edge"><default>= CL_ADDRESS_CLAMP_TO_EDGE</default></enumvalue><enumvalue name="clamp"><default>= CL_ADDRESS_CLAMP</default></enumvalue><enumvalue name="repeat"><default>= CL_ADDRESS_REPEAT</default></enumvalue></enum>
<enum name="filter_mode"><enumvalue name="nearest"><default>= CL_FILTER_NEAREST</default></enumvalue><enumvalue name="linear"><default>= CL_FILTER_LINEAR</default></enumvalue></enum>
<method-group name="public member functions">
<method name="get" cv="const"><type>cl_sampler &amp;</type><purpose>Returns the underlying <computeroutput>cl_sampler</computeroutput> object. </purpose></method>
<method name="get_context" cv="const"><type>context</type><purpose>Returns the context for the image sampler object. </purpose></method>
<method name="get_info" cv="const"><type>T</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="info"><paramtype>cl_sampler_info</paramtype></parameter><description><para>Returns information about the sampler.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetSamplerInfo.html">clGetSamplerInfo()</ulink> for more information. </para></description></method>
<method name="get_info" cv="const"><type><emphasis>unspecified</emphasis></type><template>
          <template-nontype-parameter name="Enum"><type>int</type></template-nontype-parameter>
        </template><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="operator==" cv="const"><type>bool</type><parameter name="other"><paramtype>const <classname>image_sampler</classname> &amp;</paramtype></parameter><purpose>Returns <computeroutput>true</computeroutput> if the sampler is the same at <computeroutput>other</computeroutput>. </purpose></method>
<method name="operator!=" cv="const"><type>bool</type><parameter name="other"><paramtype>const <classname>image_sampler</classname> &amp;</paramtype></parameter><purpose>Returns <computeroutput>true</computeroutput> if the sampler is different from <computeroutput>other</computeroutput>. </purpose></method>
<method name="conversion-operator" cv="const"><type>cl_sampler</type></method>
</method-group>
<constructor/>
<constructor><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="normalized_coords"><paramtype>bool</paramtype></parameter><parameter name="addressing_mode"><paramtype>cl_addressing_mode</paramtype></parameter><parameter name="filter_mode"><paramtype>cl_filter_mode</paramtype></parameter></constructor>
<constructor specifiers="explicit"><parameter name="sampler"><paramtype>cl_sampler</paramtype></parameter><parameter name="retain"><paramtype>bool</paramtype><default>true</default></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>image_sampler</classname> &amp;</paramtype></parameter><purpose>Creates a new image sampler object as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>image_sampler</classname> &amp;</type><parameter name="other"><paramtype>const <classname>image_sampler</classname> &amp;</paramtype></parameter><purpose>Copies the image sampler object from <computeroutput>other</computeroutput> to <computeroutput>*this</computeroutput>. </purpose></copy-assignment>
<constructor cv="noexcept"><parameter name="other"><paramtype><classname>image_sampler</classname> &amp;&amp;</paramtype></parameter></constructor>
<copy-assignment cv="noexcept"><type><classname>image_sampler</classname> &amp;</type><parameter name="other"><paramtype><classname>image_sampler</classname> &amp;&amp;</paramtype></parameter></copy-assignment>
<destructor><purpose>Destroys the image sampler object. </purpose></destructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/interop/opengl/acquire.hpp">
<namespace name="boost">
<namespace name="compute">


















































































































<function name="opengl_enqueue_acquire_gl_objects"><type>event</type><parameter name="num_objects"><paramtype>const uint_</paramtype></parameter><parameter name="mem_objects"><paramtype>const cl_mem *</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><parameter name="events"><paramtype>const <classname>wait_list</classname> &amp;</paramtype><default><classname alt="boost::compute::wait_list">wait_list</classname>()</default></parameter><description><para>Enqueues a command to acquire the specified OpenGL memory objects.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clEnqueueAcquireGLObjects.html">clEnqueueAcquireGLObjects()</ulink> for more information. </para></description></function>
<function name="opengl_enqueue_release_gl_objects"><type>event</type><parameter name="num_objects"><paramtype>const uint_</paramtype></parameter><parameter name="mem_objects"><paramtype>const cl_mem *</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><parameter name="events"><paramtype>const <classname>wait_list</classname> &amp;</paramtype><default><classname alt="boost::compute::wait_list">wait_list</classname>()</default></parameter><description><para>Enqueues a command to release the specified OpenGL memory objects.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clEnqueueReleaseGLObjects.html">clEnqueueReleaseGLObjects()</ulink> for more information. </para></description></function>
<function name="opengl_enqueue_acquire_buffer"><type>event</type><parameter name="buffer"><paramtype>const <classname>opengl_buffer</classname> &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><parameter name="events"><paramtype>const <classname>wait_list</classname> &amp;</paramtype><default><classname alt="boost::compute::wait_list">wait_list</classname>()</default></parameter><description><para>Enqueues a command to acquire the specified OpenGL buffer.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clEnqueueAcquireGLObjects.html">clEnqueueAcquireGLObjects()</ulink> for more information. </para></description></function>
<function name="opengl_enqueue_release_buffer"><type>event</type><parameter name="buffer"><paramtype>const <classname>opengl_buffer</classname> &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><parameter name="events"><paramtype>const <classname>wait_list</classname> &amp;</paramtype><default><classname alt="boost::compute::wait_list">wait_list</classname>()</default></parameter><description><para>Enqueues a command to release the specified OpenGL buffer.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clEnqueueReleaseGLObjects.html">clEnqueueReleaseGLObjects()</ulink> for more information. </para></description></function>















</namespace>
</namespace>
</header>
<header name="boost/compute/interop/opengl/cl_gl.hpp">
</header>
<header name="boost/compute/interop/opengl/cl_gl_ext.hpp">
</header>
<header name="boost/compute/interop/opengl/context.hpp">
<namespace name="boost">
<namespace name="compute">

















































































































<function name="opengl_create_shared_context"><type>context</type><description><para>Creates a shared OpenCL/OpenGL context for the currently active OpenGL context.</para><para>Once created, the shared context can be used to create OpenCL memory objects which can interact with OpenGL memory objects (e.g. VBOs).</para><para>
</para></description><throws><simpara><classname>unsupported_extension_error</classname> if no CL-GL sharing capable devices are found. </simpara></throws></function>



















</namespace>
</namespace>
</header>
<header name="boost/compute/interop/opengl/gl.hpp">
</header>
<header name="boost/compute/interop/opengl/opengl_buffer.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="opengl_buffer"><inherit access="public">buffer</inherit><description><para>A OpenCL buffer for accessing an OpenGL memory object. </para></description><method-group name="public member functions">
<method name="get_opengl_object" cv="const"><type>GLuint</type><description><para>Returns the OpenGL memory object ID.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetGLObjectInfo.html">clGetGLObjectInfo()</ulink> for more information. </para></description></method>
<method name="get_opengl_type" cv="const"><type>cl_gl_object_type</type><description><para>Returns the OpenGL memory object type.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetGLObjectInfo.html">clGetGLObjectInfo()</ulink> for more information. </para></description></method>
</method-group>
<constructor><purpose>Creates a null OpenGL buffer object. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="mem"><paramtype>cl_mem</paramtype></parameter><parameter name="retain"><paramtype>bool</paramtype><default>true</default></parameter><purpose>Creates a new OpenGL buffer object for <computeroutput>mem</computeroutput>. </purpose></constructor>
<constructor><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="bufobj"><paramtype>GLuint</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><description><para>Creates a new OpenGL buffer object in <computeroutput>context</computeroutput> for <computeroutput>bufobj</computeroutput> with <computeroutput>flags</computeroutput>.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clCreateFromGLBuffer.html">clCreateFromGLBuffer()</ulink> for more information. </para></description></constructor>
<constructor><parameter name="other"><paramtype>const <classname>opengl_buffer</classname> &amp;</paramtype></parameter><purpose>Creates a new OpenGL buffer object as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>opengl_buffer</classname> &amp;</type><parameter name="other"><paramtype>const <classname>opengl_buffer</classname> &amp;</paramtype></parameter><purpose>Copies the OpenGL buffer object from <computeroutput>other</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the OpenGL buffer object. </purpose></destructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/interop/opengl/opengl_renderbuffer.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="opengl_renderbuffer"><inherit access="public">boost::compute::image_object</inherit><description><para>A OpenCL buffer for accessing an OpenGL renderbuffer object. </para></description><method-group name="public member functions">
<method name="size" cv="const"><type><classname>extents</classname>&lt; 2 &gt;</type><purpose>Returns the size (width, height) of the renderbuffer. </purpose></method>
<method name="origin" cv="const"><type><classname>extents</classname>&lt; 2 &gt;</type><purpose>Returns the origin of the renderbuffer (<computeroutput>0</computeroutput>, <computeroutput>0</computeroutput>). </purpose></method>
<method name="get_opengl_object" cv="const"><type>GLuint</type><description><para>Returns the OpenGL memory object ID.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetGLObjectInfo.html">clGetGLObjectInfo()</ulink> for more information. </para></description></method>
<method name="get_opengl_type" cv="const"><type>cl_gl_object_type</type><description><para>Returns the OpenGL memory object type.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetGLObjectInfo.html">clGetGLObjectInfo()</ulink> for more information. </para></description></method>
</method-group>
<constructor><purpose>Creates a null OpenGL renderbuffer object. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="mem"><paramtype>cl_mem</paramtype></parameter><parameter name="retain"><paramtype>bool</paramtype><default>true</default></parameter><purpose>Creates a new OpenGL renderbuffer object for <computeroutput>mem</computeroutput>. </purpose></constructor>
<constructor><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="renderbuffer"><paramtype>GLuint</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><description><para>Creates a new OpenGL renderbuffer object in <computeroutput>context</computeroutput> for <computeroutput>renderbuffer</computeroutput> with <computeroutput>flags</computeroutput>.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clCreateFromGLRenderbuffer.html">clCreateFromGLRenderbuffer()</ulink> for more information. </para></description></constructor>
<constructor><parameter name="other"><paramtype>const <classname>opengl_renderbuffer</classname> &amp;</paramtype></parameter><purpose>Creates a new OpenGL renderbuffer object as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>opengl_renderbuffer</classname> &amp;</type><parameter name="other"><paramtype>const <classname>opengl_renderbuffer</classname> &amp;</paramtype></parameter><purpose>Copies the OpenGL renderbuffer object from <computeroutput>other</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the OpenGL buffer object. </purpose></destructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/interop/opengl/opengl_texture.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="opengl_texture"><inherit access="public">boost::compute::image_object</inherit><description><para>A OpenCL <classname alt="boost::compute::image2d">image2d</classname> for accessing an OpenGL texture object. </para></description><method-group name="public member functions">
<method name="size" cv="const"><type><classname>extents</classname>&lt; 2 &gt;</type><purpose>Returns the size (width, height) of the texture. </purpose></method>
<method name="origin" cv="const"><type><classname>extents</classname>&lt; 2 &gt;</type><purpose>Returns the origin of the texture (<computeroutput>0</computeroutput>, <computeroutput>0</computeroutput>). </purpose></method>
<method name="get_texture_info" cv="const"><type>T</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="info"><paramtype>cl_gl_texture_info</paramtype></parameter><description><para>Returns information about the texture.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clGetGLTextureInfo.html">clGetGLTextureInfo()</ulink> for more information. </para></description></method>
</method-group>
<constructor><purpose>Creates a null OpenGL texture object. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="mem"><paramtype>cl_mem</paramtype></parameter><parameter name="retain"><paramtype>bool</paramtype><default>true</default></parameter><purpose>Creates a new OpenGL texture object for <computeroutput>mem</computeroutput>. </purpose></constructor>
<constructor><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><parameter name="texture_target"><paramtype>GLenum</paramtype></parameter><parameter name="miplevel"><paramtype>GLint</paramtype></parameter><parameter name="texture"><paramtype>GLuint</paramtype></parameter><parameter name="flags"><paramtype>cl_mem_flags</paramtype><default>read_write</default></parameter><description><para>Creates a new OpenGL texture object in <computeroutput>context</computeroutput> for <computeroutput>texture</computeroutput> with <computeroutput>flags</computeroutput>.</para><para>See the documentation for <ulink url="http://www.khronos.org/registry/cl/sdk/1.2/docs/man/xhtml/clCreateFromGLTexture.html">clCreateFromGLTexture()</ulink> for more information. </para></description></constructor>
<constructor><parameter name="other"><paramtype>const <classname>opengl_texture</classname> &amp;</paramtype></parameter><purpose>Creates a new OpenGL texture object as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>opengl_texture</classname> &amp;</type><parameter name="other"><paramtype>const <classname>opengl_texture</classname> &amp;</paramtype></parameter><purpose>Copies the OpenGL texture object from <computeroutput>other</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the texture object. </purpose></destructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/iterator/buffer_iterator.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="buffer_iterator"><template>
      <template-type-parameter name="T"/>
    </template><purpose>An iterator for values in a buffer. </purpose><description><para>The <classname alt="boost::compute::buffer_iterator">buffer_iterator</classname> class iterates over values in a memory buffer on a compute device. It is the most commonly used iterator in Boost.Compute and is used by the <classname alt="boost::compute::vector">vector&lt;T&gt;</classname> and <classname alt="boost::compute::array">array&lt;T, N&gt;</classname> container classes.</para><para>Buffer iterators store a reference to a memory buffer along with an index into that memory buffer.</para><para>The <classname alt="boost::compute::buffer_iterator">buffer_iterator</classname> class allows for arbitrary OpenCL memory objects (including those created outside of Boost.Compute) to be used with the Boost.Compute algorithms (such as transform() and sort()). For example, to reverse the contents of an OpenCL memory buffer containing a set of integers:</para><para><programlisting language="c++"/> <para><emphasis role="bold">See Also:</emphasis><para>buffer, make_buffer_iterator() </para>
</para>
</para></description><typedef name="super_type"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="reference"><type>super_type::reference</type></typedef>
<typedef name="difference_type"><type>super_type::difference_type</type></typedef>
<method-group name="public member functions">
<method name="get_buffer" cv="const"><type>const buffer &amp;</type></method>
<method name="get_index" cv="const"><type>size_t</type></method>
<method name="read" cv="const"><type>T</type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="write"><type>void</type><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
</method-group>
<constructor/>
<constructor><parameter name="buffer"><paramtype>const buffer &amp;</paramtype></parameter><parameter name="index"><paramtype>size_t</paramtype></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>buffer_iterator</classname>&lt; T &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>buffer_iterator</classname>&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const <classname>buffer_iterator</classname>&lt; T &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
<method-group name="private member functions">
</method-group>
</class>































































































































<function name="make_buffer_iterator"><type><classname>buffer_iterator</classname>&lt; T &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="buffer"><paramtype>const buffer &amp;</paramtype><description><para>the buffer object </para></description></parameter><parameter name="index"><paramtype>size_t</paramtype><default>0</default><description><para>the index in the buffer</para></description></parameter><description><para>Creates a new <classname alt="boost::compute::buffer_iterator">buffer_iterator</classname> for <computeroutput>buffer</computeroutput> at <computeroutput>index</computeroutput>.</para><para>

</para></description><returns><para>a <computeroutput><classname alt="boost::compute::buffer_iterator">buffer_iterator</classname></computeroutput> for <computeroutput>buffer</computeroutput> at <computeroutput>index</computeroutput> </para>
</returns></function>




</namespace>
</namespace>
</header>
<header name="boost/compute/iterator/constant_buffer_iterator.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="constant_buffer_iterator"><template>
      <template-type-parameter name="T"/>
    </template><purpose>An iterator for a buffer in the <computeroutput>constant</computeroutput> memory space. </purpose><description><para>The <classname alt="boost::compute::constant_buffer_iterator">constant_buffer_iterator</classname> class provides an iterator for values in a buffer in the <computeroutput>constant</computeroutput> memory space.</para><para>For iterating over values in the <computeroutput>global</computeroutput> memory space (the most common case), use the <classname alt="boost::compute::buffer_iterator">buffer_iterator</classname> class.</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::buffer_iterator">buffer_iterator</classname> </para>
</para>
</para></description><typedef name="super_type"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="reference"><type>super_type::reference</type></typedef>
<typedef name="difference_type"><type>super_type::difference_type</type></typedef>
<method-group name="public member functions">
<method name="get_buffer" cv="const"><type>const buffer &amp;</type></method>
<method name="get_index" cv="const"><type>size_t</type></method>
<method name="read" cv="const"><type>T</type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="write"><type>void</type><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter></method>
<method name="operator[]" cv="const"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="Expr"/>
        </template><parameter name="expr"><paramtype>const Expr &amp;</paramtype></parameter></method>
</method-group>
<constructor/>
<constructor><parameter name="buffer"><paramtype>const buffer &amp;</paramtype></parameter><parameter name="index"><paramtype>size_t</paramtype></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>constant_buffer_iterator</classname>&lt; T &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>constant_buffer_iterator</classname>&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const <classname>constant_buffer_iterator</classname>&lt; T &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
<method-group name="private member functions">
<method name="dereference" cv="const"><type>reference</type></method>
<method name="equal" cv="const"><type>bool</type><parameter name="other"><paramtype>const <classname>constant_buffer_iterator</classname>&lt; T &gt; &amp;</paramtype></parameter></method>
<method name="increment"><type>void</type></method>
<method name="decrement"><type>void</type></method>
<method name="advance"><type>void</type><parameter name="n"><paramtype>difference_type</paramtype></parameter></method>
<method name="distance_to" cv="const"><type>difference_type</type><parameter name="other"><paramtype>const <classname>constant_buffer_iterator</classname>&lt; T &gt; &amp;</paramtype></parameter></method>
</method-group>
</class>






























































































































<function name="make_constant_buffer_iterator"><type><classname>constant_buffer_iterator</classname>&lt; T &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="buffer"><paramtype>const buffer &amp;</paramtype><description><para>the buffer object </para></description></parameter><parameter name="index"><paramtype>size_t</paramtype><default>0</default><description><para>the index in the buffer</para></description></parameter><description><para>Creates a new <classname alt="boost::compute::constant_buffer_iterator">constant_buffer_iterator</classname> for <computeroutput>buffer</computeroutput> at <computeroutput>index</computeroutput>.</para><para>

</para></description><returns><para>a <computeroutput><classname alt="boost::compute::constant_buffer_iterator">constant_buffer_iterator</classname></computeroutput> for <computeroutput>buffer</computeroutput> at <computeroutput>index</computeroutput> </para>
</returns></function>





</namespace>
</namespace>
</header>
<header name="boost/compute/iterator/constant_iterator.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="constant_iterator"><template>
      <template-type-parameter name="T"/>
    </template><purpose>An iterator with a constant value. </purpose><description><para>The <classname alt="boost::compute::constant_iterator">constant_iterator</classname> class provides an iterator which returns a constant value when dereferenced.</para><para>For example, this could be used to implement the fill() algorithm in terms of the copy() algorithm by copying from a range of constant iterators:</para><para><programlisting language="c++"/> <para><emphasis role="bold">See Also:</emphasis><para>make_constant_iterator() </para>
</para>
</para></description><typedef name="super_type"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="reference"><type>super_type::reference</type></typedef>
<typedef name="difference_type"><type>super_type::difference_type</type></typedef>
<method-group name="public member functions">
<method name="get_index" cv="const"><type>size_t</type></method>
</method-group>
<constructor><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="index"><paramtype>size_t</paramtype><default>0</default></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>constant_iterator</classname>&lt; T &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>constant_iterator</classname>&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const <classname>constant_iterator</classname>&lt; T &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
<method-group name="private member functions">
</method-group>
</class>





























































































































<function name="make_constant_iterator"><type><classname>constant_iterator</classname>&lt; T &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="value"><paramtype>const T &amp;</paramtype><description><para>the constant value </para></description></parameter><parameter name="index"><paramtype>size_t</paramtype><default>0</default><description><para>the iterators index</para></description></parameter><description><para>Returns a new <classname alt="boost::compute::constant_iterator">constant_iterator</classname> with <computeroutput>value</computeroutput> at <computeroutput>index</computeroutput>.</para><para>

</para></description><returns><para>a <computeroutput><classname alt="boost::compute::constant_iterator">constant_iterator</classname></computeroutput> with <computeroutput>value</computeroutput> </para>
</returns></function>






</namespace>
</namespace>
</header>
<header name="boost/compute/iterator/counting_iterator.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="counting_iterator"><template>
      <template-type-parameter name="T"/>
    </template><purpose>The <classname alt="boost::compute::counting_iterator">counting_iterator</classname> class implements a counting iterator. </purpose><description><para>A counting iterator returns an internal value (initialized with <computeroutput>init</computeroutput>) which is incremented each time the iterator is incremented.</para><para>For example, this could be used to implement the iota() algorithm in terms of the copy() algorithm by copying from a range of counting iterators:</para><para><programlisting language="c++"/> <para><emphasis role="bold">See Also:</emphasis><para>make_counting_iterator() </para>
</para>
</para></description><typedef name="super_type"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="reference"><type>super_type::reference</type></typedef>
<typedef name="difference_type"><type>super_type::difference_type</type></typedef>
<method-group name="public member functions">
<method name="get_index" cv="const"><type>size_t</type></method>
<method name="operator[]" cv="const"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="Expr"/>
        </template><parameter name="expr"><paramtype>const Expr &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="init"><paramtype>const T &amp;</paramtype></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>counting_iterator</classname>&lt; T &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>counting_iterator</classname>&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const <classname>counting_iterator</classname>&lt; T &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
<method-group name="private member functions">
<method name="dereference" cv="const"><type>reference</type></method>
<method name="equal" cv="const"><type>bool</type><parameter name="other"><paramtype>const <classname>counting_iterator</classname>&lt; T &gt; &amp;</paramtype></parameter></method>
<method name="increment"><type>void</type></method>
<method name="decrement"><type>void</type></method>
<method name="advance"><type>void</type><parameter name="n"><paramtype>difference_type</paramtype></parameter></method>
<method name="distance_to" cv="const"><type>difference_type</type><parameter name="other"><paramtype>const <classname>counting_iterator</classname>&lt; T &gt; &amp;</paramtype></parameter></method>
</method-group>
</class>




























































































































<function name="make_counting_iterator"><type><classname>counting_iterator</classname>&lt; T &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="init"><paramtype>const T &amp;</paramtype><description><para>the initial value</para></description></parameter><description><para>Returns a new <classname alt="boost::compute::counting_iterator">counting_iterator</classname> starting at <computeroutput>init</computeroutput>.</para><para>

For example, to create a counting iterator which returns unsigned integers and increments from one: <programlisting language="c++">auto iter = make_counting_iterator&lt;uint_&gt;(1);
</programlisting> </para></description><returns><para>a <classname alt="boost::compute::counting_iterator">counting_iterator</classname> with <computeroutput>init</computeroutput>.</para>
</returns></function>







</namespace>
</namespace>
</header>
<header name="boost/compute/iterator/discard_iterator.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="discard_iterator"><inherit access="public">type</inherit><purpose>An iterator which discards all values written to it. </purpose><description><para><para><emphasis role="bold">See Also:</emphasis><para>make_discard_iterator(), <classname alt="boost::compute::constant_iterator">constant_iterator</classname> </para>
</para>
</para></description><typedef name="super_type"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="reference"><type>super_type::reference</type></typedef>
<typedef name="difference_type"><type>super_type::difference_type</type></typedef>
<method-group name="public member functions">
</method-group>
<constructor><parameter name="index"><paramtype>size_t</paramtype><default>0</default></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>discard_iterator</classname> &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>discard_iterator</classname> &amp;</type><parameter name="other"><paramtype>const <classname>discard_iterator</classname> &amp;</paramtype></parameter></copy-assignment>
<destructor/>
<method-group name="private member functions">
</method-group>
</class><struct-specialization name="is_device_iterator"><template>
    </template><specialization><template-arg>discard_iterator</template-arg></specialization><inherit access="public">true_type</inherit><purpose>internal_ (<classname alt="boost::compute::is_device_iterator">is_device_iterator</classname> specialization for <classname alt="boost::compute::discard_iterator">discard_iterator</classname>) </purpose></struct-specialization>



























































































































<function name="make_discard_iterator"><type><classname>discard_iterator</classname></type><parameter name="index"><paramtype>size_t</paramtype><default>0</default><description><para>the index of the iterator</para></description></parameter><description><para>Returns a new <classname alt="boost::compute::discard_iterator">discard_iterator</classname> with <computeroutput>index</computeroutput>.</para><para>

</para></description><returns><para>a <computeroutput><classname alt="boost::compute::discard_iterator">discard_iterator</classname></computeroutput> at <computeroutput>index</computeroutput> </para>
</returns></function>








</namespace>
</namespace>
</header>
<header name="boost/compute/iterator/function_input_iterator.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="function_input_iterator"><template>
      <template-type-parameter name="Function"/>
    </template><purpose>Iterator which returns the result of a function when dereferenced. </purpose><description><para>For example:</para><para><programlisting language="c++"/> <para><emphasis role="bold">See Also:</emphasis><para>make_function_input_iterator() </para>
</para>
</para></description><typedef name="super_type"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="reference"><type>super_type::reference</type></typedef>
<typedef name="difference_type"><type>super_type::difference_type</type></typedef>
<typedef name="function"><type>Function</type></typedef>
<method-group name="public member functions">
<method name="get_index" cv="const"><type>size_t</type></method>
<method name="operator[]" cv="const"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="Expr"/>
        </template><parameter name="expr"><paramtype>const Expr &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="function"><paramtype>const Function &amp;</paramtype></parameter><parameter name="index"><paramtype>size_t</paramtype><default>0</default></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>function_input_iterator</classname>&lt; Function &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>function_input_iterator</classname>&lt; Function &gt; &amp;</type><parameter name="other"><paramtype>const <classname>function_input_iterator</classname>&lt; Function &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
<method-group name="private member functions">
<method name="dereference" cv="const"><type>reference</type></method>
<method name="equal" cv="const"><type>bool</type><parameter name="other"><paramtype>const <classname>function_input_iterator</classname>&lt; Function &gt; &amp;</paramtype></parameter></method>
<method name="increment"><type>void</type></method>
<method name="decrement"><type>void</type></method>
<method name="advance"><type>void</type><parameter name="n"><paramtype>difference_type</paramtype></parameter></method>
<method name="distance_to" cv="const"><type>difference_type</type><parameter name="other"><paramtype>const <classname>function_input_iterator</classname>&lt; Function &gt; &amp;</paramtype></parameter></method>
</method-group>
</class>


























































































































<function name="make_function_input_iterator"><type><classname>function_input_iterator</classname>&lt; Function &gt;</type><template>
          <template-type-parameter name="Function"/>
        </template><parameter name="function"><paramtype>const Function &amp;</paramtype><description><para>function to execute when dereferenced </para></description></parameter><parameter name="index"><paramtype>size_t</paramtype><default>0</default><description><para>index of the iterator</para></description></parameter><description><para>Returns a <classname alt="boost::compute::function_input_iterator">function_input_iterator</classname> with <computeroutput>function</computeroutput>.</para><para>

</para></description><returns><para>a <computeroutput><classname alt="boost::compute::function_input_iterator">function_input_iterator</classname></computeroutput> with <computeroutput>function</computeroutput> </para>
</returns></function>









</namespace>
</namespace>
</header>
<header name="boost/compute/iterator/permutation_iterator.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="permutation_iterator"><template>
      <template-type-parameter name="ElementIterator"/>
      <template-type-parameter name="IndexIterator"/>
    </template><purpose>The <classname alt="boost::compute::permutation_iterator">permutation_iterator</classname> class provides a permuation iterator. </purpose><description><para>A permutation iterator iterates over a value range and an index range. When dereferenced, it returns the value from the value range using the current index from the index range.</para><para>For example, to reverse a range using the copy() algorithm and a permutation sequence:</para><para><programlisting language="c++"/> <para><emphasis role="bold">See Also:</emphasis><para>make_permutation_iterator() </para>
</para>
</para></description><typedef name="super_type"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="value_type"><type>super_type::value_type</type></typedef>
<typedef name="reference"><type>super_type::reference</type></typedef>
<typedef name="base_type"><type>super_type::base_type</type></typedef>
<typedef name="difference_type"><type>super_type::difference_type</type></typedef>
<typedef name="index_iterator"><type>IndexIterator</type></typedef>
<method-group name="public member functions">
<method name="get_index" cv="const"><type>size_t</type></method>
<method name="get_buffer" cv="const"><type>const buffer &amp;</type></method>
<method name="operator[]" cv="const"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="IndexExpr"/>
        </template><parameter name="expr"><paramtype>const IndexExpr &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="e"><paramtype>ElementIterator</paramtype></parameter><parameter name="i"><paramtype>IndexIterator</paramtype></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>permutation_iterator</classname>&lt; ElementIterator, IndexIterator &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>permutation_iterator</classname>&lt; ElementIterator, IndexIterator &gt; &amp;</type><parameter name="other"><paramtype>const <classname>permutation_iterator</classname>&lt; ElementIterator, IndexIterator &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
<method-group name="private member functions">
<method name="dereference" cv="const"><type>reference</type></method>
</method-group>
</class>

























































































































<function name="make_permutation_iterator"><type><classname>permutation_iterator</classname>&lt; ElementIterator, IndexIterator &gt;</type><template>
          <template-type-parameter name="ElementIterator"/>
          <template-type-parameter name="IndexIterator"/>
        </template><parameter name="e"><paramtype>ElementIterator</paramtype><description><para>the element range iterator </para></description></parameter><parameter name="i"><paramtype>IndexIterator</paramtype><description><para>the index range iterator</para></description></parameter><description><para>Returns a <classname alt="boost::compute::permutation_iterator">permutation_iterator</classname> for <computeroutput>e</computeroutput> using indices from <computeroutput>i</computeroutput>.</para><para>

</para></description><returns><para>a <computeroutput><classname alt="boost::compute::permutation_iterator">permutation_iterator</classname></computeroutput> for <computeroutput>e</computeroutput> using <computeroutput>i</computeroutput> </para>
</returns></function>










</namespace>
</namespace>
</header>
<header name="boost/compute/iterator/strided_iterator.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="strided_iterator"><template>
      <template-type-parameter name="Iterator"/>
    </template><purpose>An iterator adaptor with adjustable iteration step. </purpose><description><para>The strided iterator adaptor skips over multiple elements each time it is incremented or decremented.</para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::buffer_iterator">buffer_iterator</classname>, make_strided_iterator(), make_strided_iterator_end() </para>
</para>
</para></description><typedef name="super_type"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="value_type"><type>super_type::value_type</type></typedef>
<typedef name="reference"><type>super_type::reference</type></typedef>
<typedef name="base_type"><type>super_type::base_type</type></typedef>
<typedef name="difference_type"><type>super_type::difference_type</type></typedef>
<method-group name="public member functions">
<method name="get_index" cv="const"><type>size_t</type></method>
<method name="get_buffer" cv="const"><type>const buffer &amp;</type></method>
<method name="operator[]" cv="const"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="IndexExpression"/>
        </template><parameter name="expr"><paramtype>const IndexExpression &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="iterator"><paramtype>Iterator</paramtype></parameter><parameter name="stride"><paramtype>difference_type</paramtype></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>strided_iterator</classname>&lt; Iterator &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>strided_iterator</classname>&lt; Iterator &gt; &amp;</type><parameter name="other"><paramtype>const <classname>strided_iterator</classname>&lt; Iterator &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
<method-group name="private member functions">
<method name="dereference" cv="const"><type>reference</type></method>
<method name="equal" cv="const"><type>bool</type><parameter name="other"><paramtype>const <classname>strided_iterator</classname>&lt; Iterator &gt; &amp;</paramtype></parameter></method>
<method name="increment"><type>void</type></method>
<method name="decrement"><type>void</type></method>
<method name="advance"><type>void</type><parameter name="n"><paramtype>typename super_type::difference_type</paramtype></parameter></method>
<method name="distance_to" cv="const"><type>difference_type</type><parameter name="other"><paramtype>const <classname>strided_iterator</classname>&lt; Iterator &gt; &amp;</paramtype></parameter></method>
</method-group>
</class>























































































































<function name="make_strided_iterator"><type><classname>strided_iterator</classname>&lt; Iterator &gt;</type><template>
          <template-type-parameter name="Iterator"/>
        </template><parameter name="iterator"><paramtype>Iterator</paramtype><description><para>the underlying iterator </para></description></parameter><parameter name="stride"><paramtype>typename std::iterator_traits&lt; Iterator &gt;::difference_type</paramtype><description><para>the iteration step for <classname alt="boost::compute::strided_iterator">strided_iterator</classname></para></description></parameter><description><para>Returns a <classname alt="boost::compute::strided_iterator">strided_iterator</classname> for <computeroutput>iterator</computeroutput> with <computeroutput>stride</computeroutput>.</para><para>

For example, to create an iterator which iterates over every other element in a <computeroutput>vector&lt;int&gt;</computeroutput>: <programlisting language="c++">auto strided_iterator = make_strided_iterator(vec.begin(), 2);
</programlisting> </para></description><returns><para>a <computeroutput><classname alt="boost::compute::strided_iterator">strided_iterator</classname></computeroutput> for <computeroutput>iterator</computeroutput> with <computeroutput>stride</computeroutput>.</para>
</returns></function>
<function name="make_strided_iterator_end"><type><classname>strided_iterator</classname>&lt; Iterator &gt;</type><template>
          <template-type-parameter name="Iterator"/>
        </template><parameter name="first"><paramtype>Iterator</paramtype><description><para>the iterator referring to the first element accessible through <classname alt="boost::compute::strided_iterator">strided_iterator</classname> for <computeroutput>first</computeroutput> with <computeroutput>stride</computeroutput> </para></description></parameter><parameter name="last"><paramtype>Iterator</paramtype><description><para>the iterator referring to the last element that may be accessible through <classname alt="boost::compute::strided_iterator">strided_iterator</classname> for <computeroutput>first</computeroutput> with <computeroutput>stride</computeroutput> </para></description></parameter><parameter name="stride"><paramtype>typename std::iterator_traits&lt; Iterator &gt;::difference_type</paramtype><description><para>the iteration step</para></description></parameter><description><para>Returns a <classname alt="boost::compute::strided_iterator">strided_iterator</classname> which refers to element that would follow the last element accessible through <classname alt="boost::compute::strided_iterator">strided_iterator</classname> for <computeroutput>first</computeroutput> iterator with <computeroutput>stride</computeroutput>.</para><para>Parameter <computeroutput>stride</computeroutput> must be greater than zero.</para><para>

It can be helpful when iterating over <classname alt="boost::compute::strided_iterator">strided_iterator</classname>: <programlisting language="c++">// vec.size() may not be divisible by 3
auto strided_iterator_begin = make_strided_iterator(vec.begin(), 3);
auto strided_iterator_end = make_strided_iterator_end(vec.begin(), vec.end(), 3);

// copy every 3rd element to result
boost::compute::copy(
        strided_iterator_begin,
        strided_iterator_end,
        result.begin(),
        queue
);
</programlisting> </para></description><returns><para>a <computeroutput><classname alt="boost::compute::strided_iterator">strided_iterator</classname></computeroutput> referring to element that would follow the last element accessible through <classname alt="boost::compute::strided_iterator">strided_iterator</classname> for <computeroutput>first</computeroutput> iterator with <computeroutput>stride</computeroutput>.</para>
</returns></function>











</namespace>
</namespace>
</header>
<header name="boost/compute/iterator/transform_iterator.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="transform_iterator"><template>
      <template-type-parameter name="InputIterator"/>
      <template-type-parameter name="UnaryFunction"/>
    </template><purpose>A transform iterator adaptor. </purpose><description><para>The <classname alt="boost::compute::transform_iterator">transform_iterator</classname> adaptor applies a unary function to each element produced from the underlying iterator when dereferenced.</para><para>For example, to copy from an input range to an output range while taking the absolute value of each element:</para><para><programlisting language="c++"/> <para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::buffer_iterator">buffer_iterator</classname>, make_transform_iterator() </para>
</para>
</para></description><typedef name="super_type"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="value_type"><type>super_type::value_type</type></typedef>
<typedef name="reference"><type>super_type::reference</type></typedef>
<typedef name="base_type"><type>super_type::base_type</type></typedef>
<typedef name="difference_type"><type>super_type::difference_type</type></typedef>
<typedef name="unary_function"><type>UnaryFunction</type></typedef>
<method-group name="public member functions">
<method name="get_index" cv="const"><type>size_t</type></method>
<method name="get_buffer" cv="const"><type>const buffer &amp;</type></method>
<method name="operator[]" cv="const"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="IndexExpression"/>
        </template><parameter name="expr"><paramtype>const IndexExpression &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="iterator"><paramtype>InputIterator</paramtype></parameter><parameter name="transform"><paramtype>UnaryFunction</paramtype></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>transform_iterator</classname>&lt; InputIterator, UnaryFunction &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>transform_iterator</classname>&lt; InputIterator, UnaryFunction &gt; &amp;</type><parameter name="other"><paramtype>const <classname>transform_iterator</classname>&lt; InputIterator, UnaryFunction &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
<method-group name="private member functions">
<method name="dereference" cv="const"><type>reference</type></method>
</method-group>
</class>






















































































































<function name="make_transform_iterator"><type><classname>transform_iterator</classname>&lt; InputIterator, UnaryFunction &gt;</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="UnaryFunction"/>
        </template><parameter name="iterator"><paramtype>InputIterator</paramtype><description><para>the underlying iterator </para></description></parameter><parameter name="transform"><paramtype>UnaryFunction</paramtype><description><para>the unary transform function</para></description></parameter><description><para>Returns a <classname alt="boost::compute::transform_iterator">transform_iterator</classname> for <computeroutput>iterator</computeroutput> with <computeroutput>transform</computeroutput>.</para><para>

For example, to create an iterator which returns the square-root of each value in a <computeroutput>vector&lt;int&gt;</computeroutput>: <programlisting language="c++">auto sqrt_iterator = make_transform_iterator(vec.begin(), sqrt&lt;int&gt;());
</programlisting> </para></description><returns><para>a <computeroutput><classname alt="boost::compute::transform_iterator">transform_iterator</classname></computeroutput> for <computeroutput>iterator</computeroutput> with <computeroutput>transform</computeroutput> </para>
</returns></function>













</namespace>
</namespace>
</header>
<header name="boost/compute/iterator/zip_iterator.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="zip_iterator"><template>
      <template-type-parameter name="IteratorTuple"/>
    </template><purpose>A zip iterator adaptor. </purpose><description><para>The <classname alt="boost::compute::zip_iterator">zip_iterator</classname> class combines values from multiple input iterators. When dereferenced it returns a tuple containing each value at the current position in each input range.</para><para><para><emphasis role="bold">See Also:</emphasis><para>make_zip_iterator() </para>
</para>
</para></description><typedef name="super_type"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="value_type"><type>super_type::value_type</type></typedef>
<typedef name="reference"><type>super_type::reference</type></typedef>
<typedef name="difference_type"><type>super_type::difference_type</type></typedef>
<typedef name="iterator_tuple"><type>IteratorTuple</type></typedef>
<method-group name="public member functions">
<method name="get_iterator_tuple" cv="const"><type>const IteratorTuple &amp;</type></method>
<method name="operator[]" cv="const"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="IndexExpression"/>
        </template><parameter name="expr"><paramtype>const IndexExpression &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="iterators"><paramtype>IteratorTuple</paramtype></parameter></constructor>
<constructor><parameter name="other"><paramtype>const <classname>zip_iterator</classname>&lt; IteratorTuple &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>zip_iterator</classname>&lt; IteratorTuple &gt; &amp;</type><parameter name="other"><paramtype>const <classname>zip_iterator</classname>&lt; IteratorTuple &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
<method-group name="private member functions">
<method name="dereference" cv="const"><type>reference</type></method>
<method name="equal" cv="const"><type>bool</type><parameter name="other"><paramtype>const <classname>zip_iterator</classname>&lt; IteratorTuple &gt; &amp;</paramtype></parameter></method>
<method name="increment"><type>void</type></method>
<method name="decrement"><type>void</type></method>
<method name="advance"><type>void</type><parameter name="n"><paramtype>difference_type</paramtype></parameter></method>
<method name="distance_to" cv="const"><type>difference_type</type><parameter name="other"><paramtype>const <classname>zip_iterator</classname>&lt; IteratorTuple &gt; &amp;</paramtype></parameter></method>
</method-group>
</class>





















































































































<function name="make_zip_iterator"><type><classname>zip_iterator</classname>&lt; IteratorTuple &gt;</type><template>
          <template-type-parameter name="IteratorTuple"/>
        </template><parameter name="iterators"><paramtype>IteratorTuple</paramtype><description><para>a tuple of input iterators to zip together</para></description></parameter><description><para>Creates a <classname alt="boost::compute::zip_iterator">zip_iterator</classname> for <computeroutput>iterators</computeroutput>.</para><para>

For example, to zip together iterators from three vectors (<computeroutput>a</computeroutput>, <computeroutput>b</computeroutput>, and <computeroutput>c</computeroutput>): <programlisting language="c++">auto zipped = boost::compute::make_zip_iterator(
    boost::make_tuple(a.begin(), b.begin(), c.begin())
);
</programlisting> </para></description><returns><para>a <computeroutput><classname alt="boost::compute::zip_iterator">zip_iterator</classname></computeroutput> for <computeroutput>iterators</computeroutput> </para>
</returns></function>














</namespace>
</namespace>
</header>
<header name="boost/compute/memory/local_buffer.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="local_buffer"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Represents a local memory buffer on the device. </purpose><description><para>The <classname alt="boost::compute::local_buffer">local_buffer</classname> class represents a block of local memory on a compute device.</para><para>This class is most commonly used to set local memory arguments for compute kernels: <programlisting language="c++">// set argument to a local buffer with storage for 32 float's
kernel.set_arg(0, local_buffer&lt;float&gt;(32));
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para>buffer, kernel </para>
</para>
</para></description><method-group name="public member functions">
<method name="size" cv="const"><type>size_t</type><purpose>Returns the number of elements in the local buffer. </purpose></method>
</method-group>
<constructor><parameter name="size"><paramtype>const size_t</paramtype></parameter><purpose>Creates a local buffer object for <computeroutput>size</computeroutput> elements. </purpose></constructor>
<constructor><parameter name="other"><paramtype>const <classname>local_buffer</classname> &amp;</paramtype></parameter><purpose>Creates a local buffer object as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>local_buffer</classname> &amp;</type><parameter name="other"><paramtype>const <classname>local_buffer</classname> &amp;</paramtype></parameter><purpose>Copies <computeroutput>other</computeroutput> to <computeroutput>*this</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the local memory object. </purpose></destructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/memory/svm_ptr.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="svm_ptr"><template>
      <template-type-parameter name="T"/>
    </template><typedef name="value_type"><type>T</type></typedef>
<typedef name="difference_type"><type>std::ptrdiff_t</type></typedef>
<typedef name="pointer"><type>T *</type></typedef>
<typedef name="reference"><type>T &amp;</type></typedef>
<typedef name="iterator_category"><type>std::random_access_iterator_tag</type></typedef>
<method-group name="public member functions">
<method name="get" cv="const"><type>void *</type></method>
<method name="operator+"><type>svm_ptr&lt; T &gt;</type><parameter name="n"><paramtype>difference_type</paramtype></parameter></method>
<method name="operator-"><type>difference_type</type><parameter name="other"><paramtype>svm_ptr&lt; T &gt;</paramtype></parameter></method>
<method name="get_context" cv="const"><type>const context &amp;</type></method>
<method name="operator==" cv="const"><type>bool</type><parameter name="other"><paramtype>const svm_ptr&lt; T &gt; &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const"><type>bool</type><parameter name="other"><paramtype>const svm_ptr&lt; T &gt; &amp;</paramtype></parameter></method>
</method-group>
<constructor/>
<constructor><parameter name="ptr"><paramtype>void *</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype></parameter></constructor>
<constructor><parameter name="other"><paramtype>const svm_ptr&lt; T &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type>svm_ptr&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const svm_ptr&lt; T &gt; &amp;</paramtype></parameter></copy-assignment>
<destructor/>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/random/bernoulli_distribution.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="bernoulli_distribution"><template>
      <template-type-parameter name="RealType"><default>float</default></template-type-parameter>
    </template><purpose>Produces random boolean values according to the following discrete probability function with parameter p : P(true/p) = p and P(false/p) = (1 - p) </purpose><description><para>The following example shows how to setup a bernoulli distribution to produce random boolean values with parameter p = 0.25</para><para><programlisting language="c++"/></para></description><method-group name="public member functions">
<method name="p" cv="const"><type>RealType</type><purpose>Returns the value of the parameter p. </purpose></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="Generator"/>
        </template><parameter name="first"><paramtype>OutputIterator</paramtype></parameter><parameter name="last"><paramtype>OutputIterator</paramtype></parameter><parameter name="generator"><paramtype>Generator &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Generates bernoulli distributed booleans and stores them in the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>). </para></description></method>
</method-group>
<constructor><parameter name="p"><paramtype>RealType</paramtype><default>0.5f</default></parameter><purpose>Creates a new bernoulli distribution. </purpose></constructor>
<destructor><purpose>Destroys the <classname alt="boost::compute::bernoulli_distribution">bernoulli_distribution</classname> object. </purpose></destructor>
<method-group name="private member functions">
<method name="BOOST_STATIC_ASSERT_MSG"><type/><parameter name=""><paramtype>boost::is_floating_point&lt; RealType &gt;::value</paramtype></parameter><parameter name=""><paramtype>"Template argument must be a floating point type"</paramtype></parameter></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/random/default_random_engine.hpp">
<namespace name="boost">
<namespace name="compute">
<typedef name="default_random_engine"><type><classname>mt19937</classname></type></typedef>





































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/random/discrete_distribution.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="discrete_distribution"><template>
      <template-type-parameter name="IntType"><default>uint_</default></template-type-parameter>
    </template><purpose>Produces random integers on the interval [0, n), where probability of each integer is given by the weight of the ith integer divided by the sum of all weights. </purpose><description><para>The following example shows how to setup a discrete distribution to produce 0 and 1 with equal probability</para><para><programlisting language="c++"/></para></description><typedef name="result_type"><type>IntType</type></typedef>
<method-group name="public member functions">
<method name="probabilities" cv="const"><type>::std::vector&lt; double &gt;</type><purpose>Returns the probabilities. </purpose></method>
<method name="BOOST_PREVENT_MACRO_SUBSTITUTION" cv="const"><type>result_type min</type><purpose>Returns the minimum potentially generated value. </purpose></method>
<method name="BOOST_PREVENT_MACRO_SUBSTITUTION" cv="const"><type>result_type max</type><purpose>Returns the maximum potentially generated value. </purpose></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="Generator"/>
        </template><parameter name="first"><paramtype>OutputIterator</paramtype></parameter><parameter name="last"><paramtype>OutputIterator</paramtype></parameter><parameter name="generator"><paramtype>Generator &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Generates uniformly distributed integers and stores them to the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>). </para></description></method>
</method-group>
<constructor><description><para>Creates a new discrete distribution with a single weight p = { 1 }. This distribution produces only zeroes. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><description><para>Creates a new discrete distribution with weights given by the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>). </para></description></constructor>
<destructor><purpose>Destroys the <classname alt="boost::compute::discrete_distribution">discrete_distribution</classname> object. </purpose></destructor>
<method-group name="private member functions">
<method name="BOOST_STATIC_ASSERT_MSG"><type/><parameter name=""><paramtype>boost::is_integral&lt; IntType &gt;::value</paramtype></parameter><parameter name=""><paramtype>"Template argument must be integral"</paramtype></parameter></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/random/linear_congruential_engine.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="linear_congruential_engine"><template>
      <template-type-parameter name="T"><default>uint_</default></template-type-parameter>
    </template><purpose>'Quick and Dirty' linear congruential engine </purpose><description><para>Quick and dirty linear congruential engine to generate low quality random numbers very quickly. For uses in which good quality of random numbers is required(Monte-Carlo Simulations), use other engines like Mersenne Twister instead. </para></description><typedef name="result_type"><type>T</type></typedef>
<data-member name="default_seed" specifiers="static"><type>const T</type></data-member>
<data-member name="a" specifiers="static"><type>const T</type></data-member>
<data-member name="threads" specifiers="static"><type>const size_t</type></data-member>
<method-group name="public member functions">
<method name="seed"><type>void</type><parameter name="value"><paramtype>result_type</paramtype><description><para>seed value for the random-number generator </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><description><para>command queue to perform the operation</para></description></parameter><description><para>Seeds the random number generator with <computeroutput>value</computeroutput>.</para><para>
If no seed value is provided, <computeroutput>default_seed</computeroutput> is used. </para></description></method>
<method name="seed"><type>void</type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>OutputIterator</paramtype></parameter><parameter name="last"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Generates random numbers and stores them to the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>). </purpose></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="Function"/>
        </template><parameter name="first"><paramtype>OutputIterator</paramtype></parameter><parameter name="last"><paramtype>OutputIterator</paramtype></parameter><parameter name="op"><paramtype>Function</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Generates random numbers, transforms them with <computeroutput>op</computeroutput>, and then stores them to the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>). </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>size_t</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Generates <computeroutput>z</computeroutput> random numbers and discards them. </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><parameter name="value"><paramtype>result_type</paramtype><default>default_seed</default></parameter><purpose>Creates a new <classname alt="boost::compute::linear_congruential_engine">linear_congruential_engine</classname> and seeds it with <computeroutput>value</computeroutput>. </purpose></constructor>
<constructor><parameter name="other"><paramtype>const <classname>linear_congruential_engine</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Creates a new <classname alt="boost::compute::linear_congruential_engine">linear_congruential_engine</classname> object as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>linear_congruential_engine</classname>&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const <classname>linear_congruential_engine</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Copies <computeroutput>other</computeroutput> to <computeroutput>*this</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the <classname alt="boost::compute::linear_congruential_engine">linear_congruential_engine</classname> object. </purpose></destructor>
<method-group name="private member functions">
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/random/mersenne_twister_engine.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="mersenne_twister_engine"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Mersenne twister pseudorandom number generator. </purpose><typedef name="result_type"><type>T</type></typedef>
<data-member name="default_seed" specifiers="static"><type>const T</type></data-member>
<data-member name="n" specifiers="static"><type>const T</type></data-member>
<data-member name="m" specifiers="static"><type>const T</type></data-member>
<method-group name="public member functions">
<method name="seed"><type>void</type><parameter name="value"><paramtype>result_type</paramtype><description><para>seed value for the random-number generator </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><description><para>command queue to perform the operation</para></description></parameter><description><para>Seeds the random number generator with <computeroutput>value</computeroutput>.</para><para>
If no seed value is provided, <computeroutput>default_seed</computeroutput> is used. </para></description></method>
<method name="seed"><type>void</type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>OutputIterator</paramtype></parameter><parameter name="last"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Generates random numbers and stores them to the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>). </purpose></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="Function"/>
        </template><parameter name="first"><paramtype>OutputIterator</paramtype></parameter><parameter name="last"><paramtype>OutputIterator</paramtype></parameter><parameter name="op"><paramtype>Function</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Generates random numbers, transforms them with <computeroutput>op</computeroutput>, and then stores them to the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>). </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>size_t</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Generates <computeroutput>z</computeroutput> random numbers and discards them. </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><parameter name="value"><paramtype>result_type</paramtype><default>default_seed</default></parameter><purpose>Creates a new <classname alt="boost::compute::mersenne_twister_engine">mersenne_twister_engine</classname> and seeds it with <computeroutput>value</computeroutput>. </purpose></constructor>
<constructor><parameter name="other"><paramtype>const <classname>mersenne_twister_engine</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Creates a new <classname alt="boost::compute::mersenne_twister_engine">mersenne_twister_engine</classname> object as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>mersenne_twister_engine</classname>&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const <classname>mersenne_twister_engine</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Copies <computeroutput>other</computeroutput> to <computeroutput>*this</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the <classname alt="boost::compute::mersenne_twister_engine">mersenne_twister_engine</classname> object. </purpose></destructor>
<method-group name="private member functions">
</method-group>
</class><typedef name="mt19937"><type><classname>mersenne_twister_engine</classname>&lt; uint_ &gt;</type></typedef>





































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/random/normal_distribution.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="normal_distribution"><template>
      <template-type-parameter name="RealType"><default>float</default></template-type-parameter>
    </template><purpose>Produces random, normally-distributed floating-point numbers. </purpose><description><para>The following example shows how to setup a normal distribution to produce random <computeroutput>float</computeroutput> values centered at <computeroutput>5</computeroutput>:</para><para><programlisting language="c++"/> <para><emphasis role="bold">See Also:</emphasis><para>default_random_engine, <classname alt="boost::compute::uniform_real_distribution">uniform_real_distribution</classname> </para>
</para>
</para></description><typedef name="result_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="mean" cv="const"><type>result_type</type><purpose>Returns the mean value of the distribution. </purpose></method>
<method name="stddev" cv="const"><type>result_type</type><purpose>Returns the standard-deviation of the distribution. </purpose></method>
<method name="BOOST_PREVENT_MACRO_SUBSTITUTION" cv="const"><type>result_type min</type><purpose>Returns the minimum value of the distribution. </purpose></method>
<method name="BOOST_PREVENT_MACRO_SUBSTITUTION" cv="const"><type>result_type max</type><purpose>Returns the maximum value of the distribution. </purpose></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="Generator"/>
        </template><parameter name="first"><paramtype>OutputIterator</paramtype></parameter><parameter name="last"><paramtype>OutputIterator</paramtype></parameter><parameter name="generator"><paramtype>Generator &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Generates normally-distributed floating-point numbers and stores them to the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>). </para></description></method>
</method-group>
<constructor><parameter name="mean"><paramtype>RealType</paramtype><default>0.f</default></parameter><parameter name="stddev"><paramtype>RealType</paramtype><default>1.f</default></parameter><description><para>Creates a new normal distribution producing numbers with the given <computeroutput>mean</computeroutput> and <computeroutput>stddev</computeroutput>. </para></description></constructor>
<destructor><purpose>Destroys the normal distribution object. </purpose></destructor>
<method-group name="private member functions">
<method name="BOOST_STATIC_ASSERT_MSG"><type/><parameter name=""><paramtype>boost::is_floating_point&lt; RealType &gt;::value</paramtype></parameter><parameter name=""><paramtype>"Template argument must be a floating point type"</paramtype></parameter></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/random/threefry_engine.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="threefry_engine"><template>
      <template-type-parameter name="T"><default>uint_</default></template-type-parameter>
    </template><purpose>Threefry pseudorandom number generator. </purpose><typedef name="result_type"><type>T</type></typedef>
<data-member name="default_seed" specifiers="static"><type>const ulong_</type></data-member>
<method-group name="public member functions">
<method name="seed"><type>void</type><parameter name="value"><paramtype>ulong_</paramtype><description><para>seed value for the random-number generator </para></description></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype><description><para>command queue to perform the operation</para></description></parameter><description><para>Seeds the random number generator with <computeroutput>value</computeroutput>.</para><para>
If no seed value is provided, <computeroutput>default_seed</computeroutput> is used. </para></description></method>
<method name="seed"><type>void</type><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>OutputIterator</paramtype></parameter><parameter name="last"><paramtype>OutputIterator</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Generates random numbers and stores them to the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>). </purpose></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="Function"/>
        </template><parameter name="first"><paramtype>OutputIterator</paramtype></parameter><parameter name="last"><paramtype>OutputIterator</paramtype></parameter><parameter name="op"><paramtype>Function</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Generates random numbers, transforms them with <computeroutput>op</computeroutput>, and then stores them to the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>). </para></description></method>
<method name="discard"><type>void</type><parameter name="z"><paramtype>size_t</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><purpose>Generates <computeroutput>z</computeroutput> random numbers and discards them. </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><parameter name="value"><paramtype>ulong_</paramtype><default>default_seed</default></parameter><purpose>Creates a new <classname alt="boost::compute::threefry_engine">threefry_engine</classname> and seeds it with <computeroutput>value</computeroutput>. </purpose></constructor>
<constructor><parameter name="other"><paramtype>const <classname>threefry_engine</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Creates a new <classname alt="boost::compute::threefry_engine">threefry_engine</classname> object as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>threefry_engine</classname>&lt; T &gt; &amp;</type><parameter name="other"><paramtype>const <classname>threefry_engine</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Copies <computeroutput>other</computeroutput> to <computeroutput>*this</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the <classname alt="boost::compute::threefry_engine">threefry_engine</classname> object. </purpose></destructor>
<method-group name="private member functions">
<method name="load_program"><type>void</type></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/random/uniform_int_distribution.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="uniform_int_distribution"><template>
      <template-type-parameter name="IntType"><default>uint_</default></template-type-parameter>
    </template><purpose>Produces uniformily distributed random integers. </purpose><description><para>The following example shows how to setup a uniform int distribution to produce random integers 0 and 1.</para><para><programlisting language="c++"/></para></description><typedef name="result_type"><type>IntType</type></typedef>
<method-group name="public member functions">
<method name="a" cv="const"><type>result_type</type><purpose>Returns the minimum value of the distribution. </purpose></method>
<method name="b" cv="const"><type>result_type</type><purpose>Returns the maximum value of the distribution. </purpose></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="Generator"/>
        </template><parameter name="first"><paramtype>OutputIterator</paramtype></parameter><parameter name="last"><paramtype>OutputIterator</paramtype></parameter><parameter name="generator"><paramtype>Generator &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Generates uniformily distributed integers and stores them to the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>). </para></description></method>
</method-group>
<constructor specifiers="explicit"><parameter name="a"><paramtype>IntType</paramtype><default>0</default></parameter><parameter name="b"><paramtype>IntType</paramtype><default>(std::numeric_limits&lt; IntType &gt;::max)()</default></parameter><description><para>Creates a new uniform distribution producing numbers in the range [<computeroutput>a</computeroutput>, <computeroutput>b</computeroutput>]. </para></description></constructor>
<destructor><purpose>Destroys the <classname alt="boost::compute::uniform_int_distribution">uniform_int_distribution</classname> object. </purpose></destructor>
<method-group name="private member functions">
<method name="BOOST_STATIC_ASSERT_MSG"><type/><parameter name=""><paramtype>boost::is_integral&lt; IntType &gt;::value</paramtype></parameter><parameter name=""><paramtype>"Template argument must be integral"</paramtype></parameter></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/random/uniform_real_distribution.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="uniform_real_distribution"><template>
      <template-type-parameter name="RealType"><default>float</default></template-type-parameter>
    </template><purpose>Produces uniformly distributed random floating-point numbers. </purpose><description><para>The following example shows how to setup a uniform real distribution to produce random <computeroutput>float</computeroutput> values between <computeroutput>1</computeroutput> and <computeroutput>100</computeroutput>.</para><para><programlisting language="c++"/> <para><emphasis role="bold">See Also:</emphasis><para>default_random_engine, <classname alt="boost::compute::normal_distribution">normal_distribution</classname> </para>
</para>
</para></description><typedef name="result_type"><type>RealType</type></typedef>
<method-group name="public member functions">
<method name="a" cv="const"><type>result_type</type><purpose>Returns the minimum value of the distribution. </purpose></method>
<method name="b" cv="const"><type>result_type</type><purpose>Returns the maximum value of the distribution. </purpose></method>
<method name="generate"><type>void</type><template>
          <template-type-parameter name="OutputIterator"/>
          <template-type-parameter name="Generator"/>
        </template><parameter name="first"><paramtype>OutputIterator</paramtype></parameter><parameter name="last"><paramtype>OutputIterator</paramtype></parameter><parameter name="generator"><paramtype>Generator &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><description><para>Generates uniformly distributed floating-point numbers and stores them to the range [<computeroutput>first</computeroutput>, <computeroutput>last</computeroutput>). </para></description></method>
</method-group>
<constructor><parameter name="a"><paramtype>RealType</paramtype><default>0.f</default></parameter><parameter name="b"><paramtype>RealType</paramtype><default>1.f</default></parameter><description><para>Creates a new uniform distribution producing numbers in the range [<computeroutput>a</computeroutput>, <computeroutput>b</computeroutput>). Requires a &lt; b </para></description></constructor>
<destructor><purpose>Destroys the <classname alt="boost::compute::uniform_real_distribution">uniform_real_distribution</classname> object. </purpose></destructor>
<method-group name="private member functions">
<method name="BOOST_STATIC_ASSERT_MSG"><type/><parameter name=""><paramtype>boost::is_floating_point&lt; RealType &gt;::value</paramtype></parameter><parameter name=""><paramtype>"Template argument must be a floating point type"</paramtype></parameter></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/type_traits/common_type.hpp">
<namespace name="boost">
</namespace>
</header>
<header name="boost/compute/type_traits/is_device_iterator.hpp">
<namespace name="boost">
<namespace name="compute">
<struct name="is_device_iterator"><template>
      <template-type-parameter name="Iterator"/>
    </template><inherit access="public">false_type</inherit><description><para>Meta-function returning <computeroutput>true</computeroutput> if <computeroutput>Iterator</computeroutput> is a device-iterator.</para><para>By default, this function returns false. Device iterator types (such as <classname alt="boost::compute::buffer_iterator">buffer_iterator</classname>) should specialize this trait and return <computeroutput>true</computeroutput>.</para><para>For example: <programlisting language="c++">is_device_iterator&lt;buffer_iterator&lt;int&gt;&gt;::value == true
is_device_iterator&lt;std::vector&lt;int&gt;::iterator&gt;::value == false
</programlisting> </para></description></struct>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/type_traits/is_fundamental.hpp">
<namespace name="boost">
<namespace name="compute">
<struct name="is_fundamental"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">false_type</inherit><description><para>Meta-function returning <computeroutput>true</computeroutput> if <computeroutput>T</computeroutput> is a fundamental (i.e. built-in) type.</para><para>For example, <programlisting language="c++">is_fundamental&lt;float&gt;::value == true
is_fundamental&lt;std::pair&lt;int, float&gt;&gt;::value == false
</programlisting> </para></description></struct>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/type_traits/is_vector_type.hpp">
<namespace name="boost">
<namespace name="compute">
<struct name="is_vector_type"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">boost::mpl::bool_&lt; vector_size&lt; T &gt;::value !=1 &gt;</inherit><description><para>Meta-function returning <computeroutput>true</computeroutput> if <computeroutput>T</computeroutput> is a vector type.</para><para>For example, <programlisting language="c++">is_vector_type&lt;int&gt;::value == false
is_vector_type&lt;float4_&gt;::value == true
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::make_vector_type">make_vector_type</classname>, <classname alt="boost::compute::vector_size">vector_size</classname> </para>
</para>
</para></description></struct>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/type_traits/make_vector_type.hpp">
<namespace name="boost">
<namespace name="compute">
<struct name="make_vector_type"><template>
      <template-type-parameter name="Scalar"/>
      <template-nontype-parameter name="Size"><type>size_t</type></template-nontype-parameter>
    </template><description><para>Meta-function which returns a vector type for <computeroutput>Scalar</computeroutput> with <computeroutput>Size</computeroutput>.</para><para>For example, <programlisting language="c++">make_vector_type&lt;int, 2&gt;::type == int2_
make_vector_type&lt;float, 4&gt;::type == float4_
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::is_vector_type">is_vector_type</classname> </para>
</para>
</para></description></struct>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/type_traits/result_of.hpp">
<namespace name="boost">
<namespace name="compute">
<struct name="result_of"><template>
      <template-type-parameter name="Signature"/>
    </template><description><para>Returns the result of <computeroutput>Function</computeroutput> when called with <computeroutput>Args</computeroutput>.</para><para>For example, <programlisting language="c++">// int + int = int
result_of&lt;plus(int, int)&gt;::type == int
</programlisting> </para></description><typedef name="type"><type>::boost::tr1_result_of&lt; Signature &gt;::type</type></typedef>
</struct>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/type_traits/scalar_type.hpp">
<namespace name="boost">
<namespace name="compute">
<struct name="scalar_type"><template>
      <template-type-parameter name="Vector"/>
    </template><description><para>Meta-function returning the scalar type for a vector type.</para><para>For example, <programlisting language="c++">scalar_type&lt;float4_&gt;::type == float
</programlisting> </para></description></struct>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/type_traits/type_definition.hpp">
<namespace name="boost">
<namespace name="compute">


































































































































<function name="type_definition"><type>std::string</type><template>
          <template-type-parameter name="T"/>
        </template><description><para>Returns the OpenCL type definition for <computeroutput>T</computeroutput>.</para><para>
<para><emphasis role="bold">See Also:</emphasis><para>type_name&lt;T&gt;() </para>
</para>
</para></description><returns><para>a string containing the type definition for <computeroutput>T</computeroutput> </para>
</returns></function>


</namespace>
</namespace>
</header>
<header name="boost/compute/type_traits/type_name.hpp">
<namespace name="boost">
<namespace name="compute">

































































































































<function name="type_name"><type>const char *</type><template>
          <template-type-parameter name="T"/>
        </template><description><para>Returns the OpenCL type name for the type <computeroutput>T</computeroutput> as a string.</para><para>
For example: <programlisting language="c++">type_name&lt;float&gt;() == "float"
type_name&lt;float4_&gt;() == "float4"
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para>type_definition&lt;T&gt;() </para>
</para>
</para></description><returns><para>a string containing the type name for <computeroutput>T</computeroutput> </para>
</returns></function>



</namespace>
</namespace>
<macro name="BOOST_COMPUTE_TYPE_NAME" kind="functionlike"><macro-parameter name="type"/><macro-parameter name="name"/><description><para>Registers the OpenCL type for the C++ <computeroutput>type</computeroutput> to <computeroutput>name</computeroutput>.</para><para>For example, the following will allow Eigen's <computeroutput>Vector2f</computeroutput> type to be used with Boost.Compute algorithms and containers as the built-in <computeroutput>float2</computeroutput> type. <programlisting language="c++">BOOST_COMPUTE_TYPE_NAME(Eigen::Vector2f, float2)
</programlisting></para><para>This macro should be invoked in the global namespace.</para><para><para><emphasis role="bold">See Also:</emphasis><para>type_name() </para>
</para>
</para></description></macro>
</header>
<header name="boost/compute/type_traits/vector_size.hpp">
<namespace name="boost">
<namespace name="compute">
<struct name="vector_size"><template>
      <template-type-parameter name="T"/>
    </template><description><para>Meta-function returning the size (number of components) of a vector type <computeroutput>T</computeroutput>. For scalar types this function returns <computeroutput>1</computeroutput>.</para><para>For example, <programlisting language="c++">vector_size&lt;float&gt;::value == 1
vector_size&lt;float4_&gt;::value == 4
</programlisting> </para></description><method-group name="public member functions">
</method-group>
</struct>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/utility/dim.hpp">
<namespace name="boost">
<namespace name="compute">




































































































































<function name="dim"><type><classname>extents</classname>&lt; sizeof...(Args)&gt;</type><template>
          <template-nontype-parameter name="Args"><type>class...</type></template-nontype-parameter>
        </template><parameter name="args"><paramtype>Args...</paramtype></parameter><description><para>The variadic <computeroutput>dim()</computeroutput> function provides a concise syntax for creating <classname alt="boost::compute::extents">extents</classname> objects.</para><para>For example, <programlisting language="c++">extents&lt;2&gt; region = dim(640, 480); // region == (640, 480)
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para><classname alt="boost::compute::extents">extents&lt;N&gt;</classname> </para>
</para>
</para></description></function>
</namespace>
</namespace>
</header>
<header name="boost/compute/utility/extents.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="extents"><template>
      <template-nontype-parameter name="N"><type>size_t</type></template-nontype-parameter>
    </template><description><para>The extents class contains an array of n-dimensional extents.</para><para><para><emphasis role="bold">See Also:</emphasis><para>dim() </para>
</para>
</para></description><typedef name="size_type"><type>size_t</type></typedef>
<typedef name="array_type"><type>boost::array&lt; size_t, N &gt;</type></typedef>
<typedef name="iterator"><type>array_type::iterator</type></typedef>
<typedef name="const_iterator"><type>array_type::const_iterator</type></typedef>
<data-member name="static_size" specifiers="static"><type>const size_type</type></data-member>
<method-group name="public member functions">
<method name="size" cv="const"><type>size_type</type><purpose>Returns the size (i.e. dimensionality) of the extents array. </purpose></method>
<method name="linear" cv="const"><type>size_type</type><description><para>Returns the linear size of the extents. This is equivalent to the product of each extent in each dimension. </para></description></method>
<method name="data"><type>size_t *</type><description><para>Returns a pointer to the extents data array.</para><para>This is useful for passing the extents data to OpenCL APIs which expect an array of <computeroutput>size_t</computeroutput>. </para></description></method>
<method name="data" cv="const"><type>const size_t *</type><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="begin"><type>iterator</type></method>
<method name="begin" cv="const"><type>const_iterator</type></method>
<method name="cbegin" cv="const"><type>const_iterator</type></method>
<method name="end"><type>iterator</type></method>
<method name="end" cv="const"><type>const_iterator</type></method>
<method name="cend" cv="const"><type>const_iterator</type></method>
<method name="operator[]"><type>size_t &amp;</type><parameter name="index"><paramtype>size_t</paramtype></parameter><purpose>Returns a reference to the extent at <computeroutput>index</computeroutput>. </purpose></method>
<method name="operator[]" cv="const"><type>const size_t &amp;</type><parameter name="index"><paramtype>size_t</paramtype></parameter><description><para>This is an overloaded member function, provided for convenience. It differs from the above function only in what argument(s) it accepts. </para></description></method>
<method name="operator==" cv="const"><type>bool</type><parameter name="other"><paramtype>const <classname>extents</classname> &amp;</paramtype></parameter><purpose>Returns <computeroutput>true</computeroutput> if the extents in <computeroutput>*this</computeroutput> are the same as <computeroutput>other</computeroutput>. </purpose></method>
<method name="operator!=" cv="const"><type>bool</type><parameter name="other"><paramtype>const <classname>extents</classname> &amp;</paramtype></parameter><purpose>Returns <computeroutput>true</computeroutput> if the extents in <computeroutput>*this</computeroutput> are not the same as <computeroutput>other</computeroutput>. </purpose></method>
</method-group>
<constructor><description><para>Creates an extents object with each component set to zero.</para><para>For example: <programlisting language="c++">extents&lt;3&gt; exts(); // (0, 0, 0)
</programlisting> </para></description></constructor>
<constructor specifiers="explicit"><parameter name="value"><paramtype>size_t</paramtype></parameter><description><para>Creates an extents object with each component set to <computeroutput>value</computeroutput>.</para><para>For example: <programlisting language="c++">extents&lt;3&gt; exts(1); // (1, 1, 1)
</programlisting> </para></description></constructor>
<constructor><parameter name="values"><paramtype>std::initializer_list&lt; size_t &gt;</paramtype></parameter><purpose>Creates an extents object with <computeroutput>values</computeroutput>. </purpose></constructor>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/utility/invoke.hpp">
<namespace name="boost">
<namespace name="compute">



































































































































<function name="invoke"><type><classname>result_of</classname>&lt; Function(Args...)&gt;::type</type><template>
          <template-type-parameter name="Function"/>
          <template-nontype-parameter name="Args"><type>class...</type></template-nontype-parameter>
        </template><parameter name="function"><paramtype>const Function &amp;</paramtype></parameter><parameter name="queue"><paramtype>command_queue &amp;</paramtype></parameter><parameter name="args"><paramtype>const Args &amp;...</paramtype></parameter><description><para>Invokes <computeroutput>function</computeroutput> with <computeroutput>args</computeroutput> on <computeroutput>queue</computeroutput>.</para><para>For example, to invoke the builtin abs() function: <programlisting language="c++">int result = invoke(abs&lt;int&gt;(), queue, -10); // returns 10
</programlisting> </para></description></function>

</namespace>
</namespace>
<macro name="BOOST_COMPUTE_DETAIL_INVOKE_ARG" kind="functionlike"><macro-parameter name="z"/><macro-parameter name="n"/><macro-parameter name="unused"/></macro>
<macro name="BOOST_COMPUTE_DETAIL_INVOKE_ADD_ARG" kind="functionlike"><macro-parameter name="z"/><macro-parameter name="n"/><macro-parameter name="unused"/></macro>
<macro name="BOOST_COMPUTE_DETAIL_DEFINE_INVOKE" kind="functionlike"><macro-parameter name="z"/><macro-parameter name="n"/><macro-parameter name="unused"/></macro>
</header>
<header name="boost/compute/utility/program_cache.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="program_cache"><inherit access="private">noncopyable</inherit><description><para>The <classname alt="boost::compute::program_cache">program_cache</classname> class stores program objects in a LRU cache.</para><para>This class can be used to help mitigate the overhead of OpenCL's run-time kernel compilation model. Commonly used programs can be stored persistently in the cache and only compiled once on their first use.</para><para>Program objects are stored and retreived based on a user-defined cache key along with the options used to build the program (if any).</para><para>For example, to insert a program into the cache: <programlisting language="c++">cache.insert("foo", foo_program);
</programlisting></para><para>And to retreive the program later: <programlisting language="c++">boost::optional&lt;program&gt; p = cache.get("foo");
if(p){
    // program found in cache
}
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para>program </para>
</para>
</para></description><method-group name="public member functions">
<method name="size" cv="const"><type>size_t</type><purpose>Returns the number of program objects currently stored in the cache. </purpose></method>
<method name="capacity" cv="const"><type>size_t</type><purpose>Returns the total capacity of the cache. </purpose></method>
<method name="clear"><type>void</type><purpose>Clears the program cache. </purpose></method>
<method name="get"><type>boost::optional&lt; program &gt;</type><parameter name="key"><paramtype>const std::string &amp;</paramtype></parameter><description><para>Returns the program object with <computeroutput>key</computeroutput>. Returns a null optional if no program with <computeroutput>key</computeroutput> exists in the cache. </para></description></method>
<method name="get"><type>boost::optional&lt; program &gt;</type><parameter name="key"><paramtype>const std::string &amp;</paramtype></parameter><parameter name="options"><paramtype>const std::string &amp;</paramtype></parameter><description><para>Returns the program object with <computeroutput>key</computeroutput> and <computeroutput>options</computeroutput>. Returns a null optional if no program with <computeroutput>key</computeroutput> and <computeroutput>options</computeroutput> exists in the cache. </para></description></method>
<method name="insert"><type>void</type><parameter name="key"><paramtype>const std::string &amp;</paramtype></parameter><parameter name="program"><paramtype>const program &amp;</paramtype></parameter><purpose>Inserts <computeroutput>program</computeroutput> into the cache with <computeroutput>key</computeroutput>. </purpose></method>
<method name="insert"><type>void</type><parameter name="key"><paramtype>const std::string &amp;</paramtype></parameter><parameter name="options"><paramtype>const std::string &amp;</paramtype></parameter><parameter name="program"><paramtype>const program &amp;</paramtype></parameter><purpose>Inserts <computeroutput>program</computeroutput> into the cache with <computeroutput>key</computeroutput> and <computeroutput>options</computeroutput>. </purpose></method>
<method name="get_or_build"><type>program</type><parameter name="key"><paramtype>const std::string &amp;</paramtype></parameter><parameter name="options"><paramtype>const std::string &amp;</paramtype></parameter><parameter name="source"><paramtype>const std::string &amp;</paramtype></parameter><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><description><para>Loads the program with <computeroutput>key</computeroutput> from the cache if it exists. Otherwise builds a new program with <computeroutput>source</computeroutput> and <computeroutput>options</computeroutput>, stores it in the cache, and returns it.</para><para>This is a convenience function to simplify the common pattern of attempting to load a program from the cache and, if not present, building the program from source and storing it in the cache.</para><para>Equivalent to: <programlisting language="c++">boost::optional&lt;program&gt; p = get(key, options);
if(!p){
    p = program::create_with_source(source, context);
    p-&gt;build(options);
    insert(key, options, *p);
}
return *p;
</programlisting> </para></description></method>
</method-group>
<constructor><parameter name="capacity"><paramtype>size_t</paramtype></parameter><description><para>Creates a new program cache with space for <computeroutput>capacity</computeroutput> number of program objects. </para></description></constructor>
<destructor><purpose>Destroys the program cache. </purpose></destructor>
<method-group name="public static functions">
<method name="get_global_cache" specifiers="static"><type>boost::shared_ptr&lt; <classname>program_cache</classname> &gt;</type><parameter name="context"><paramtype>const context &amp;</paramtype></parameter><description><para>Returns the global program cache for <computeroutput>context</computeroutput>.</para><para>This global cache is used internally by Boost.Compute to store compiled program objects used by its algorithms. All Boost.Compute programs are stored with a cache key beginning with <computeroutput>"__boost"</computeroutput>. User programs should avoid using the same prefix in order to prevent collisions. </para></description></method>
</method-group>
</class>




































































































































</namespace>
</namespace>
</header>
<header name="boost/compute/utility/source.hpp">
<macro name="BOOST_COMPUTE_STRINGIZE_SOURCE" kind="functionlike"><macro-parameter name="source"/><description><para>Stringizes OpenCL source code.</para><para>For example, to create a simple kernel which squares each input value: <programlisting language="c++">const char source[] = BOOST_COMPUTE_STRINGIZE_SOURCE(
    __kernel void square(const float *input, float *output)
    {
        const uint i = get_global_id(0);
        const float x = input[i];
        output[i] = x * x;
    }
);

// create and build square program
program square_program = program::build_with_source(source, context);

// create square kernel
kernel square_kernel(square_program, "square");
</programlisting> </para></description></macro>
</header>
<header name="boost/compute/utility/wait_list.hpp">
<namespace name="boost">
<namespace name="compute">
<class name="wait_list"><purpose>Stores a list of events. </purpose><description><para>The <classname alt="boost::compute::wait_list">wait_list</classname> class stores a set of event objects and can be used to specify dependencies for OpenCL operations or to wait on the host until all of the events have completed.</para><para>This class also provides convenience functions for interacting with OpenCL APIs which typically accept event dependencies as a <computeroutput>cl_event*</computeroutput> pointer and a <computeroutput>cl_uint</computeroutput> size. For example: <programlisting language="c++">wait_list events = ...;

clEnqueueNDRangeKernel(..., events.get_event_ptr(), events.size(), ...);
</programlisting></para><para><para><emphasis role="bold">See Also:</emphasis><para>event, <classname alt="boost::compute::future">future&lt;T&gt;</classname> </para>
</para>
</para></description><typedef name="iterator"><type>std::vector&lt; event &gt;::iterator</type></typedef>
<typedef name="const_iterator"><type>std::vector&lt; event &gt;::const_iterator</type></typedef>
<method-group name="public member functions">
<method name="empty" cv="const"><type>bool</type><purpose>Returns <computeroutput>true</computeroutput> if the wait-list is empty. </purpose></method>
<method name="size" cv="const"><type>uint_</type><purpose>Returns the number of events in the wait-list. </purpose></method>
<method name="clear"><type>void</type><purpose>Removes all of the events from the wait-list. </purpose></method>
<method name="get_event_ptr" cv="const"><type>const cl_event *</type><description><para>Returns a cl_event pointer to the first event in the wait-list. Returns <computeroutput>0</computeroutput> if the wait-list is empty.</para><para>This can be used to pass the wait-list to OpenCL functions which expect a <computeroutput>cl_event</computeroutput> pointer to refer to a list of events. </para></description></method>
<method name="reserve"><type>void</type><parameter name="new_capacity"><paramtype>size_t</paramtype></parameter><purpose>Reserves a minimum length of storage for the wait list object. </purpose></method>
<method name="insert"><type>void</type><parameter name="event"><paramtype>const event &amp;</paramtype></parameter><purpose>Inserts <computeroutput>event</computeroutput> into the wait-list. </purpose></method>
<method name="insert"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="future"><paramtype>const <classname>future</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Inserts the event from <computeroutput>future</computeroutput> into the wait-list. </purpose></method>
<method name="wait" cv="const"><type>void</type><description><para>Blocks until all of the events in the wait-list have completed.</para><para>Does nothing if the wait-list is empty. </para></description></method>
<method name="operator[]" cv="const"><type>const event &amp;</type><parameter name="pos"><paramtype>size_t</paramtype></parameter><purpose>Returns a reference to the event at specified location <computeroutput>pos</computeroutput>. </purpose></method>
<method name="operator[]"><type>event &amp;</type><parameter name="pos"><paramtype>size_t</paramtype></parameter><purpose>Returns a reference to the event at specified location <computeroutput>pos</computeroutput>. </purpose></method>
<method name="begin"><type>iterator</type><purpose>Returns an iterator to the first element of the wait-list. </purpose></method>
<method name="begin" cv="const"><type>const_iterator</type><purpose>Returns an iterator to the first element of the wait-list. </purpose></method>
<method name="cbegin" cv="const"><type>const_iterator</type><purpose>Returns an iterator to the first element of the wait-list. </purpose></method>
<method name="end"><type>iterator</type><purpose>Returns an iterator to the element following the last element of the wait-list. </purpose></method>
<method name="end" cv="const"><type>const_iterator</type><purpose>Returns an iterator to the element following the last element of the wait-list. </purpose></method>
<method name="cend" cv="const"><type>const_iterator</type><purpose>Returns an iterator to the element following the last element of the wait-list. </purpose></method>
</method-group>
<constructor><purpose>Creates an empty wait-list. </purpose></constructor>
<constructor><parameter name="event"><paramtype>const event &amp;</paramtype></parameter><purpose>Creates a wait-list containing <computeroutput>event</computeroutput>. </purpose></constructor>
<constructor><parameter name="other"><paramtype>const <classname>wait_list</classname> &amp;</paramtype></parameter><purpose>Creates a new wait-list as a copy of <computeroutput>other</computeroutput>. </purpose></constructor>
<constructor><parameter name="events"><paramtype>std::initializer_list&lt; event &gt;</paramtype></parameter><purpose>Creates a wait-list from <computeroutput>events</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>wait_list</classname> &amp;</type><parameter name="other"><paramtype>const <classname>wait_list</classname> &amp;</paramtype></parameter><purpose>Copies the events in the wait-list from <computeroutput>other</computeroutput>. </purpose></copy-assignment>
<constructor><parameter name="other"><paramtype><classname>wait_list</classname> &amp;&amp;</paramtype></parameter><purpose>Move-constructs a new wait list object from <computeroutput>other</computeroutput>. </purpose></constructor>
<copy-assignment><type><classname>wait_list</classname> &amp;</type><parameter name="other"><paramtype><classname>wait_list</classname> &amp;&amp;</paramtype></parameter><purpose>Move-assigns the wait list from <computeroutput>other</computeroutput> to <computeroutput>*this</computeroutput>. </purpose></copy-assignment>
<destructor><purpose>Destroys the wait-list. </purpose></destructor>
</class>




































































































































</namespace>
</namespace>
</header>
</library-reference>