<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Function shrink</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Histogram">
<link rel="up" href="../../../histogram/reference.html#header.boost.histogram.algorithm.reduce_hpp" title="Header &lt;boost/histogram/algorithm/reduce.hpp&gt;">
<link rel="prev" href="shrink_idm14017.html" title="Function shrink">
<link rel="next" href="shrink_and_rebin_idm14057.html" title="Function shrink_and_rebin">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="shrink_idm14017.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../histogram/reference.html#header.boost.histogram.algorithm.reduce_hpp"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="shrink_and_rebin_idm14057.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.histogram.algorithm.shrink_idm14039"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Function shrink</span></h2>
<p>boost::histogram::algorithm::shrink — Shrink command to be used in <code class="computeroutput">reduce</code>.</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../histogram/reference.html#header.boost.histogram.algorithm.reduce_hpp" title="Header &lt;boost/histogram/algorithm/reduce.hpp&gt;">boost/histogram/algorithm/reduce.hpp</a>&gt;

</span>
<span class="identifier">reduce_command</span> <span class="identifier">shrink</span><span class="special">(</span><span class="keyword">double</span> lower<span class="special">,</span> <span class="keyword">double</span> upper<span class="special">)</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm23951"></a><h2>Description</h2>
<p>Command is applied to corresponding axis in order of reduce arguments.</p>
<p>Shrinking is based on an inclusive value interval. The bin which contains the first value starts the range of bins to keep. The bin which contains the second value is the last included in that range. When the second value is exactly equal to a lower bin edge, then the previous bin is the last in the range.</p>
<p>The counts in removed bins are added to the corresponding underflow and overflow bins, if they are present. If they are not present, the counts are discarded. Also see <code class="computeroutput">crop</code>, which always discards the counts.</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">lower</code></span></p></td>
<td><p>bin which contains lower is first to be kept. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">upper</code></span></p></td>
<td><p>bin which contains upper is last to be kept, except if upper is equal to the lower edge. </p></td>
</tr>
</tbody>
</table></div></td>
</tr></tbody>
</table></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2016-2019 Hans
      Dembinski<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="shrink_idm14017.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../histogram/reference.html#header.boost.histogram.algorithm.reduce_hpp"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="shrink_and_rebin_idm14057.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
