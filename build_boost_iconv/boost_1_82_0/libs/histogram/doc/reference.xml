<?xml version="1.0" standalone="yes"?>
<library-reference><header name="boost/histogram.hpp">
<para>Includes all standard headers of the Boost.Histogram library. </para><para>Extra headers not automatically included are:<itemizedlist>
<listitem><para><ulink url="histogram/reference.html#header.boost.histogram.ostream_hpp">boost/histogram/ostream.hpp</ulink></para>
</listitem><listitem><para><ulink url="histogram/reference.html#header.boost.histogram.axis.ostream_hpp">boost/histogram/axis/ostream.hpp</ulink></para>
</listitem><listitem><para><ulink url="histogram/reference.html#header.boost.histogram.accumulators.ostream_hpp">boost/histogram/accumulators/ostream.hpp</ulink></para>
</listitem><listitem><para><ulink url="histogram/reference.html#header.boost.histogram.serialization_hpp">boost/histogram/serialization.hpp</ulink> </para>
</listitem></itemizedlist>
</para></header>
<header name="boost/histogram/histogram.hpp">
<namespace name="boost">
<namespace name="histogram">
<class name="histogram"><template>
      <template-type-parameter name="Axes"><purpose><para>std::tuple of axis types OR std::vector of an axis type or <classname alt="boost::histogram::axis::variant">axis::variant</classname> </para></purpose></template-type-parameter>
      <template-type-parameter name="Storage"><purpose><para>class that implements the storage interface </para></purpose></template-type-parameter>
    </template><purpose>Central class of the histogram library. </purpose><description><para>Histogram uses the call operator to insert data, like the <ulink url="https://www.boost.org/doc/libs/develop/doc/html/accumulators.html">Boost.Accumulators</ulink>.</para><para>Use factory functions (see <ulink url="histogram/reference.html#header.boost.histogram.make_histogram_hpp">make_histogram.hpp</ulink> and <ulink url="histogram/reference.html#header.boost.histogram.make_profile_hpp">make_profile.hpp</ulink>) to conveniently create histograms rather than calling the ctors directly.</para><para>Use the <ulink url="boost/histogram/indexed.html">indexed</ulink> range generator to iterate over filled histograms, which is convenient and faster than hand-written loops for multi-dimensional histograms.</para><para>
</para></description><typedef name="axes_type"><type>Axes</type></typedef>
<typedef name="storage_type"><type>Storage</type></typedef>
<typedef name="value_type"><type>typename storage_type::value_type</type></typedef>
<typedef name="iterator"><type>typename storage_type::iterator</type></typedef>
<typedef name="const_iterator"><type>typename storage_type::const_iterator</type></typedef>
<typedef name="multi_index_type"><type><emphasis>unspecified</emphasis></type></typedef>
<method-group name="public member functions">
<method name="rank" cv="const noexcept"><type>constexpr unsigned</type><purpose>Number of axes (dimensions). </purpose></method>
<method name="size" cv="const noexcept"><type>std::size_t</type><purpose>Total number of bins (including underflow/overflow). </purpose></method>
<method name="reset"><type>void</type><purpose>Reset all bins to default initialized values. </purpose></method>
<method name="axis" cv="const"><type>decltype(auto)</type><template>
          <template-nontype-parameter name="N"><type>unsigned</type><default>0</default></template-nontype-parameter>
        </template><parameter name=""><paramtype>std::integral_constant&lt; unsigned, N &gt;</paramtype><default>{}</default></parameter><purpose>Get N-th axis using a compile-time number. </purpose><description><para>This version is more efficient than the one accepting a run-time number. </para></description></method>
<method name="axis" cv="const"><type>decltype(auto)</type><parameter name="i"><paramtype>unsigned</paramtype></parameter><purpose>Get N-th axis with run-time number. </purpose><description><para>Prefer the version that accepts a compile-time number, if you can use it. </para></description></method>
<method name="for_each_axis" cv="const"><type>auto</type><template>
          <template-type-parameter name="Unary"/>
        </template><parameter name="unary"><paramtype>Unary &amp;&amp;</paramtype></parameter><purpose>Apply unary functor/function to each axis. </purpose></method>
<method name="operator()"><type>iterator</type><template>
          <template-type-parameter name="T0"/>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
          <template-type-parameter name=""><default>std::enable_if_t&lt;(detail::is_tuple&lt;T0&gt;::value == false ||                                      sizeof...(Ts) &gt; 0</default></template-type-parameter>
        </template><parameter name="arg0"><paramtype>const T0 &amp;</paramtype></parameter><parameter name="args"><paramtype>const Ts &amp;...</paramtype></parameter><purpose>Fill histogram with values, an optional weight, and/or a sample. </purpose><description><para>Returns iterator to located cell.</para><para>Arguments are passed in order to the axis objects. Passing an argument type that is not convertible to the value type accepted by the axis or passing the wrong number of arguments causes a throw of <computeroutput>std::invalid_argument</computeroutput>.</para><para><emphasis role="bold">Optional weight</emphasis></para><para>An optional weight can be passed as the first or last argument with the <ulink url="boost/histogram/weight.html">weight</ulink> helper function. Compilation fails if the storage elements do not support weights.</para><para><emphasis role="bold">Samples</emphasis></para><para>If the storage elements accept samples, pass them with the sample helper function in addition to the axis arguments, which can be the first or last argument. The <ulink url="boost/histogram/sample.html">sample</ulink> helper function can pass one or more arguments to the storage element. If samples and weights are used together, they can be passed in any order at the beginning or end of the argument list.</para><para><emphasis role="bold">Axis with multiple arguments</emphasis></para><para>If the histogram contains an axis which accepts a <computeroutput>std::tuple</computeroutput> of arguments, the arguments for that axis need to be passed as a <computeroutput>std::tuple</computeroutput>, for example, <computeroutput>std::make_tuple(1.2, 2.3)</computeroutput>. If the histogram contains only this axis and no other, the arguments can be passed directly. </para></description></method>
<method name="operator()"><type>iterator</type><template>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
        </template><parameter name="args"><paramtype>const std::tuple&lt; Ts... &gt; &amp;</paramtype></parameter><purpose>Fill histogram with values, an optional weight, and/or a sample from a <computeroutput>std::tuple</computeroutput>. </purpose></method>
<method name="fill"><type>void</type><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="args"><paramtype>const Iterable &amp;</paramtype><description><para>iterable as explained in the long description. </para></description></parameter><purpose>Fill histogram with several values at once. </purpose><description><para>The argument must be an iterable with a size that matches the rank of the histogram. The element of an iterable may be 1) a value or 2) an iterable over a contiguous sequence of values or 3) a variant of 1) and 2). Sub-iterables must have the same length.</para><para>Warning: <computeroutput>std::vector&lt;bool&gt;</computeroutput> is not a contiguous sequence over boolean values because of the infamous vector specialization for booleans. It cannot be used as an argument, but any truely contiguous sequence of boolean values can (<computeroutput>std::array&lt;bool, N&gt;</computeroutput> or <computeroutput>std::valarray&lt;bool&gt;</computeroutput>, for example).</para><para>Values are passed to the corresponding histogram axis in order. If a single value is passed together with an iterable of values, the single value is treated like an iterable with matching length of copies of this value.</para><para>If the histogram has only one axis, an iterable of values may be passed directly.</para><para>
</para></description></method>
<method name="fill"><type>void</type><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="args"><paramtype>const Iterable &amp;</paramtype><description><para>iterable of values. </para></description></parameter><parameter name="weights"><paramtype>const <classname>weight_type</classname>&lt; T &gt; &amp;</paramtype><description><para>single weight or an iterable of weights. </para></description></parameter><purpose>Fill histogram with several values and weights at once. </purpose><description><para>
</para></description></method>
<method name="fill"><type>void</type><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="weights"><paramtype>const <classname>weight_type</classname>&lt; T &gt; &amp;</paramtype><description><para>single weight or an iterable of weights. </para></description></parameter><parameter name="args"><paramtype>const Iterable &amp;</paramtype><description><para>iterable of values. </para></description></parameter><purpose>Fill histogram with several values and weights at once. </purpose><description><para>
</para></description></method>
<method name="fill"><type>void</type><template>
          <template-type-parameter name="Iterable"/>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="args"><paramtype>const Iterable &amp;</paramtype><description><para>iterable of values. </para></description></parameter><parameter name="samples"><paramtype>const <classname>sample_type</classname>&lt; std::tuple&lt; Ts... &gt;&gt; &amp;</paramtype><description><para>single sample or an iterable of samples. </para></description></parameter><purpose>Fill histogram with several values and samples at once. </purpose><description><para>
</para></description></method>
<method name="fill"><type>void</type><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="samples"><paramtype>const <classname>sample_type</classname>&lt; T &gt; &amp;</paramtype><description><para>single sample or an iterable of samples. </para></description></parameter><parameter name="args"><paramtype>const Iterable &amp;</paramtype><description><para>iterable of values. </para></description></parameter><purpose>Fill histogram with several values and samples at once. </purpose><description><para>
</para></description></method>
<method name="fill"><type>void</type><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="args"><paramtype>const Iterable &amp;</paramtype></parameter><parameter name="weights"><paramtype>const <classname>weight_type</classname>&lt; T &gt; &amp;</paramtype></parameter><parameter name="samples"><paramtype>const <classname>sample_type</classname>&lt; std::tuple&lt; Ts... &gt;&gt; &amp;</paramtype></parameter></method>
<method name="fill"><type>void</type><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="samples"><paramtype>const <classname>sample_type</classname>&lt; T &gt; &amp;</paramtype></parameter><parameter name="weights"><paramtype>const <classname>weight_type</classname>&lt; U &gt; &amp;</paramtype></parameter><parameter name="args"><paramtype>const Iterable &amp;</paramtype></parameter></method>
<method name="fill"><type>void</type><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="weights"><paramtype>const <classname>weight_type</classname>&lt; T &gt; &amp;</paramtype></parameter><parameter name="samples"><paramtype>const <classname>sample_type</classname>&lt; U &gt; &amp;</paramtype></parameter><parameter name="args"><paramtype>const Iterable &amp;</paramtype></parameter></method>
<method name="fill"><type>void</type><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="args"><paramtype>const Iterable &amp;</paramtype></parameter><parameter name="samples"><paramtype>const <classname>sample_type</classname>&lt; T &gt; &amp;</paramtype></parameter><parameter name="weights"><paramtype>const <classname>weight_type</classname>&lt; U &gt; &amp;</paramtype></parameter></method>
<method name="at"><type>decltype(auto)</type><template>
          <template-nontype-parameter name="Is"><type>class...</type></template-nontype-parameter>
        </template><parameter name="i"><paramtype>axis::index_type</paramtype><description><para>index of first axis. </para></description></parameter><parameter name="is"><paramtype>Is...</paramtype><description><para>indices of second, third, ... axes. </para></description></parameter><purpose>Access cell value at integral indices. </purpose><description><para>You can pass indices as individual arguments, as a std::tuple of integers, or as an interable range of integers. Passing the wrong number of arguments causes a throw of std::invalid_argument. Passing an index which is out of bounds causes a throw of std::out_of_range.</para><para>

</para></description><returns><para>reference to cell value. </para>
</returns></method>
<method name="at" cv="const"><type>decltype(auto)</type><template>
          <template-nontype-parameter name="Is"><type>class...</type></template-nontype-parameter>
        </template><parameter name="i"><paramtype>axis::index_type</paramtype></parameter><parameter name="is"><paramtype>Is...</paramtype></parameter><purpose>Access cell value at integral indices (read-only). </purpose></method>
<method name="at"><type>decltype(auto)</type><parameter name="is"><paramtype>const <classname>multi_index_type</classname> &amp;</paramtype></parameter><purpose>Access cell value at integral indices stored in iterable. </purpose></method>
<method name="at" cv="const"><type>decltype(auto)</type><parameter name="is"><paramtype>const <classname>multi_index_type</classname> &amp;</paramtype></parameter><purpose>Access cell value at integral indices stored in iterable (read-only). </purpose></method>
<method name="operator[]"><type>decltype(auto)</type><parameter name="i"><paramtype>axis::index_type</paramtype></parameter><purpose>Access value at index (for rank = 1). </purpose></method>
<method name="operator[]" cv="const"><type>decltype(auto)</type><parameter name="i"><paramtype>axis::index_type</paramtype></parameter><purpose>Access value at index (for rank = 1, read-only). </purpose></method>
<method name="operator[]"><type>decltype(auto)</type><parameter name="is"><paramtype>const <classname>multi_index_type</classname> &amp;</paramtype></parameter><purpose>Access value at index tuple. </purpose></method>
<method name="operator[]" cv="const"><type>decltype(auto)</type><parameter name="is"><paramtype>const <classname>multi_index_type</classname> &amp;</paramtype></parameter><purpose>Access value at index tuple (read-only). </purpose></method>
<method name="operator==" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="rhs"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter><purpose>Equality operator, tests equality for all axes and the storage. </purpose></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="rhs"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter><purpose>Negation of the equality operator. </purpose></method>
<method name="operator+="><type><classname>histogram</classname> &amp;</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="rhs"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter><purpose>Add values of another histogram. </purpose><description><para>This operator is only available if the value_type supports operator+=.</para><para>Both histograms must be compatible to be addable. The histograms are compatible, if the axes are either all identical. If the axes only differ in the states of their discrete growing axis types, then they are also compatible. The discrete growing axes are merged in this case. </para></description></method>
<method name="operator+="><type><classname>histogram</classname> &amp;</type><template>
          <template-type-parameter name="S"/>
        </template><parameter name="rhs"><paramtype>const <classname>histogram</classname>&lt; axes_type, S &gt; &amp;</paramtype></parameter></method>
<method name="operator-="><type><classname>histogram</classname> &amp;</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="rhs"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter><purpose>Subtract values of another histogram. </purpose><description><para>This operator is only available if the value_type supports operator-=. </para></description></method>
<method name="operator *="><type><classname>histogram</classname> &amp;</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="rhs"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter><purpose>Multiply by values of another histogram. </purpose><description><para>This operator is only available if the value_type supports operator*=. </para></description></method>
<method name="operator/="><type><classname>histogram</classname> &amp;</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="rhs"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter><purpose>Divide by values of another histogram. </purpose><description><para>This operator is only available if the value_type supports operator/=. </para></description></method>
<method name="operator *="><type><classname>histogram</classname> &amp;</type><parameter name="x"><paramtype>const double</paramtype></parameter><purpose>Multiply all values with a scalar. </purpose><description><para>This operator is only available if the value_type supports operator*=. </para></description></method>
<method name="operator/="><type><classname>histogram</classname> &amp;</type><parameter name="x"><paramtype>const double</paramtype></parameter><purpose>Divide all values by a scalar. </purpose><description><para>This operator is only available if operator*= is available. </para></description></method>
<method name="begin" cv="noexcept"><type>iterator</type><purpose>Return value iterator to the beginning of the histogram. </purpose></method>
<method name="end" cv="noexcept"><type>iterator</type><purpose>Return value iterator to the end in the histogram. </purpose></method>
<method name="begin" cv="const noexcept"><type>const_iterator</type><purpose>Return value iterator to the beginning of the histogram (read-only). </purpose></method>
<method name="end" cv="const noexcept"><type>const_iterator</type><purpose>Return value iterator to the end in the histogram (read-only). </purpose></method>
<method name="cbegin" cv="const noexcept"><type>const_iterator</type><purpose>Return value iterator to the beginning of the histogram (read-only). </purpose></method>
<method name="cend" cv="const noexcept"><type>const_iterator</type><purpose>Return value iterator to the end in the histogram (read-only). </purpose></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor cv="= default"/>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="rhs"><paramtype><classname>histogram</classname>&lt; A, S &gt; &amp;&amp;</paramtype></parameter></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="rhs"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>histogram</classname> &amp;</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="rhs"><paramtype><classname>histogram</classname>&lt; A, S &gt; &amp;&amp;</paramtype></parameter></copy-assignment>
<copy-assignment><type><classname>histogram</classname> &amp;</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="rhs"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter></copy-assignment>
<constructor><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name=""><default>detail::requires_axes&lt;A&gt;</default></template-type-parameter>
        </template><parameter name="a"><paramtype>A &amp;&amp;</paramtype></parameter><parameter name="s"><paramtype>Storage</paramtype></parameter></constructor>
<constructor specifiers="explicit"><parameter name="axes"><paramtype>Axes</paramtype></parameter></constructor>
<constructor specifiers="explicit"><template>
          <template-nontype-parameter name="As"><type>class...</type></template-nontype-parameter>
          <template-type-parameter name=""><default>detail::requires_axes&lt;std::tuple&lt;std::decay_t&lt;As&gt;...&gt;&gt;</default></template-type-parameter>
        </template><parameter name="as"><paramtype>As &amp;&amp;...</paramtype></parameter></constructor>
</class>


















<function name="operator+"><type>auto</type><template>
          <template-type-parameter name="A1"/>
          <template-type-parameter name="S1"/>
          <template-type-parameter name="A2"/>
          <template-type-parameter name="S2"/>
        </template><parameter name="a"><paramtype>const <classname>histogram</classname>&lt; A1, S1 &gt; &amp;</paramtype></parameter><parameter name="b"><paramtype>const <classname>histogram</classname>&lt; A2, S2 &gt; &amp;</paramtype></parameter><purpose>Pairwise add cells of two histograms and return histogram with the sum. </purpose><description><para>The returned histogram type is the most efficient and safest one constructible from the inputs, if they are not the same type. If one histogram has a tuple axis, the result has a tuple axis. The chosen storage is the one with the larger dynamic range. </para></description></function>
<function name="operator*"><type>auto</type><template>
          <template-type-parameter name="A1"/>
          <template-type-parameter name="S1"/>
          <template-type-parameter name="A2"/>
          <template-type-parameter name="S2"/>
        </template><parameter name="a"><paramtype>const <classname>histogram</classname>&lt; A1, S1 &gt; &amp;</paramtype></parameter><parameter name="b"><paramtype>const <classname>histogram</classname>&lt; A2, S2 &gt; &amp;</paramtype></parameter><purpose>Pairwise multiply cells of two histograms and return histogram with the product. </purpose><description><para>For notes on the returned histogram type, see operator+. </para></description></function>
<function name="operator-"><type>auto</type><template>
          <template-type-parameter name="A1"/>
          <template-type-parameter name="S1"/>
          <template-type-parameter name="A2"/>
          <template-type-parameter name="S2"/>
        </template><parameter name="a"><paramtype>const <classname>histogram</classname>&lt; A1, S1 &gt; &amp;</paramtype></parameter><parameter name="b"><paramtype>const <classname>histogram</classname>&lt; A2, S2 &gt; &amp;</paramtype></parameter><purpose>Pairwise subtract cells of two histograms and return histogram with the difference. </purpose><description><para>For notes on the returned histogram type, see operator+. </para></description></function>
<function name="operator/"><type>auto</type><template>
          <template-type-parameter name="A1"/>
          <template-type-parameter name="S1"/>
          <template-type-parameter name="A2"/>
          <template-type-parameter name="S2"/>
        </template><parameter name="a"><paramtype>const <classname>histogram</classname>&lt; A1, S1 &gt; &amp;</paramtype></parameter><parameter name="b"><paramtype>const <classname>histogram</classname>&lt; A2, S2 &gt; &amp;</paramtype></parameter><purpose>Pairwise divide cells of two histograms and return histogram with the quotient. </purpose><description><para>For notes on the returned histogram type, see operator+. </para></description></function>
<function name="operator*"><type>auto</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="h"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter><parameter name="x"><paramtype>double</paramtype></parameter><purpose>Multiply all cells of the histogram by a number and return a new histogram. </purpose><description><para>If the original histogram has integer cells, the result has double cells. </para></description></function>
<function name="operator*"><type>auto</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="x"><paramtype>double</paramtype></parameter><parameter name="h"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter><purpose>Multiply all cells of the histogram by a number and return a new histogram. </purpose><description><para>If the original histogram has integer cells, the result has double cells. </para></description></function>
<function name="operator/"><type>auto</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="h"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter><parameter name="x"><paramtype>double</paramtype></parameter><purpose>Divide all cells of the histogram by a number and return a new histogram. </purpose><description><para>If the original histogram has integer cells, the result has double cells. </para></description></function>
</namespace>
</namespace>
</header>
<header name="boost/histogram/accumulators.hpp">
<para>Includes all accumulator headers of the Boost.Histogram library. </para><para>Extra header not automatically included:<itemizedlist>
<listitem><para><ulink url="histogram/reference.html#header.boost.histogram.accumulators.ostream_hpp">boost/histogram/accumulators/ostream.hpp</ulink> </para>
</listitem></itemizedlist>
</para></header>
<header name="boost/histogram/accumulators/count.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="accumulators">
<class name="count"><template>
      <template-type-parameter name="ValueType"/>
      <template-nontype-parameter name="ThreadSafe"><type>bool</type><purpose><para>Set to true to make increments and adds thread-safe. </para></purpose></template-nontype-parameter>
    </template><purpose>Wraps a C++ arithmetic type with optionally thread-safe increments and adds. </purpose><description><para>This adaptor optionally uses atomic operations to make concurrent increments and additions thread-safe for the stored arithmetic value, which can be integral or floating point. For small histograms, the performance will still be poor because of False Sharing, see <ulink url="https://en.wikipedia.org/wiki/False_sharing">https://en.wikipedia.org/wiki/False_sharing</ulink> for details.</para><para>Warning: Assignment is not thread-safe in this implementation, so don't assign concurrently.</para><para>This wrapper class can be used as a base class by users to add arbitrary metadata to each bin of a histogram.</para><para>When weighted samples are accumulated and high precision is required, use <computeroutput><classname alt="boost::histogram::accumulators::sum">accumulators::sum</classname></computeroutput> instead (at the cost of lower performance). If a local variance estimate for the weight distribution should be computed as well (generally needed for a detailed statistical analysis), use <computeroutput><classname alt="boost::histogram::accumulators::weighted_sum">accumulators::weighted_sum</classname></computeroutput>.</para><para>
</para></description><typedef name="value_type"><type>ValueType</type></typedef>
<typedef name="const_reference"><type>const value_type &amp;</type></typedef>
<method-group name="public member functions">
<method name="operator++" cv="noexcept"><type><classname>count</classname> &amp;</type><purpose>Increment count by one. </purpose></method>
<method name="operator+=" cv="noexcept"><type><classname>count</classname> &amp;</type><parameter name="value"><paramtype>const_reference</paramtype></parameter><purpose>Increment count by value. </purpose></method>
<method name="operator+=" cv="noexcept"><type><classname>count</classname> &amp;</type><parameter name="s"><paramtype>const <classname>count</classname> &amp;</paramtype></parameter><purpose>Add another count. </purpose></method>
<method name="operator *=" cv="noexcept"><type><classname>count</classname> &amp;</type><parameter name="value"><paramtype>const_reference</paramtype></parameter><purpose>Scale by value. </purpose></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>count</classname> &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>count</classname> &amp;</paramtype></parameter></method>
<method name="value" cv="const noexcept"><type>value_type</type><purpose>Return count. </purpose></method>
<method name="conversion-operator" cv="const noexcept" specifiers="explicit"><type>value_type</type></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
<method name="operator *=" cv="noexcept"><type><classname>count</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>count</classname> &amp;</paramtype></parameter></method>
<method name="operator *" cv="const noexcept"><type><classname>count</classname></type><parameter name="rhs"><paramtype>const <classname>count</classname> &amp;</paramtype></parameter></method>
<method name="operator/=" cv="noexcept"><type><classname>count</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>count</classname> &amp;</paramtype></parameter></method>
<method name="operator/" cv="const noexcept"><type><classname>count</classname></type><parameter name="rhs"><paramtype>const <classname>count</classname> &amp;</paramtype></parameter></method>
<method name="operator&lt;" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>count</classname> &amp;</paramtype></parameter></method>
<method name="operator&gt;" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>count</classname> &amp;</paramtype></parameter></method>
<method name="operator&lt;=" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>count</classname> &amp;</paramtype></parameter></method>
<method name="operator&gt;=" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>count</classname> &amp;</paramtype></parameter></method>
</method-group>
<constructor/>
<constructor cv="noexcept"><parameter name="value"><paramtype>const_reference</paramtype></parameter><purpose>Initialize count to value and allow implicit conversion. </purpose></constructor>
<constructor cv="noexcept"><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="B"><type>bool</type></template-nontype-parameter>
        </template><parameter name="c"><paramtype>const <classname>count</classname>&lt; T, B &gt; &amp;</paramtype></parameter><purpose>Allow implicit conversion from other count. </purpose></constructor>
<method-group name="public static functions">
<method name="thread_safe" cv="noexcept" specifiers="static"><type>constexpr bool</type></method>
</method-group>
</class></namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/accumulators/fraction.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="accumulators">
<class name="fraction"><template>
      <template-type-parameter name="ValueType"/>
    </template><purpose>Accumulate boolean samples and compute the fraction of true samples. </purpose><description><para>This accumulator should be used to calculate the efficiency or success fraction of a random process as a function of process parameters. It returns the fraction of successes, the variance of this fraction, and a two-sided confidence interval with 68.3 % confidence level for this fraction.</para><para>There is no unique way to compute an interval for a success fraction. This class returns the Wilson score interval, because it is widely recommended in the literature for general use. More interval computers can be found in <computeroutput>boost/histogram/utility</computeroutput>, which can be used to compute intervals for other confidence levels. </para></description><typedef name="value_type"><type>ValueType</type></typedef>
<typedef name="const_reference"><type>const value_type &amp;</type></typedef>
<typedef name="real_type"><type>typename std::conditional&lt; std::is_floating_point&lt; value_type &gt;::value, value_type, double &gt;::type</type></typedef>
<typedef name="interval_type"><type>typename <classname>utility::wilson_interval</classname>&lt; real_type &gt;::interval_type</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="noexcept"><type>void</type><parameter name="x"><paramtype>bool</paramtype></parameter><purpose>Insert boolean sample x. </purpose></method>
<method name="operator+=" cv="noexcept"><type><classname>fraction</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>fraction</classname> &amp;</paramtype></parameter><purpose>Add another accumulator. </purpose></method>
<method name="successes" cv="const noexcept"><type>const_reference</type><purpose>Return number of boolean samples that were true. </purpose></method>
<method name="failures" cv="const noexcept"><type>const_reference</type><purpose>Return number of boolean samples that were false. </purpose></method>
<method name="count" cv="const noexcept"><type>value_type</type><purpose>Return total number of boolean samples. </purpose></method>
<method name="value" cv="const noexcept"><type>real_type</type><purpose>Return success fraction of boolean samples. </purpose></method>
<method name="variance" cv="const noexcept"><type>real_type</type><purpose>Return variance of the success fraction. </purpose></method>
<method name="confidence_interval" cv="const noexcept"><type>interval_type</type><purpose>Return standard interval with 68.3 % confidence level (Wilson score interval). </purpose></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>fraction</classname> &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>fraction</classname> &amp;</paramtype></parameter></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor/>
<constructor cv="noexcept"><parameter name="successes"><paramtype>const_reference</paramtype></parameter><parameter name="failures"><paramtype>const_reference</paramtype></parameter><purpose>Initialize to external successes and failures. </purpose></constructor>
<constructor cv="noexcept"><template>
          <template-type-parameter name="T"/>
        </template><parameter name="e"><paramtype>const <classname>fraction</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Allow implicit conversion from fraction with a different value type. </purpose></constructor>
</class></namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/accumulators/is_thread_safe.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="accumulators">
<struct name="is_thread_safe"><template>
      <template-type-parameter name="T"/>
    </template></struct></namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/accumulators/mean.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="accumulators">
<class name="mean"><template>
      <template-type-parameter name="ValueType"/>
    </template><purpose>Calculates mean and variance of sample. </purpose><description><para>Uses Welfords's incremental algorithm to improve the numerical stability of mean and variance computation. </para></description><typedef name="value_type"><type>ValueType</type></typedef>
<typedef name="const_reference"><type>const value_type &amp;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="noexcept"><type>void</type><parameter name="x"><paramtype>const_reference</paramtype></parameter><purpose>Insert sample x. </purpose></method>
<method name="operator()" cv="noexcept"><type>void</type><parameter name="w"><paramtype>const <classname>weight_type</classname>&lt; value_type &gt; &amp;</paramtype></parameter><parameter name="x"><paramtype>const_reference</paramtype></parameter><purpose>Insert sample x with weight w. </purpose></method>
<method name="operator+=" cv="noexcept"><type><classname>mean</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>mean</classname> &amp;</paramtype></parameter><purpose>Add another mean accumulator. </purpose></method>
<method name="operator *=" cv="noexcept"><type><classname>mean</classname> &amp;</type><parameter name="s"><paramtype>const_reference</paramtype></parameter><purpose>Scale by value. </purpose><description><para>This acts as if all samples were scaled by the value. </para></description></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>mean</classname> &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>mean</classname> &amp;</paramtype></parameter></method>
<method name="count" cv="const noexcept"><type>const_reference</type><purpose>Return how many samples were accumulated. </purpose><description><para>count() should be used to check whether value() and variance() are defined, see documentation of value() and variance(). count() can be used to compute the variance of the mean by dividing variance() by count(). </para></description></method>
<method name="value" cv="const noexcept"><type>const_reference</type><purpose>Return mean value of accumulated samples. </purpose><description><para>The result is undefined, if <computeroutput>count() &lt; 1</computeroutput>. </para></description></method>
<method name="variance" cv="const noexcept"><type>value_type</type><purpose>Return variance of accumulated samples. </purpose><description><para>The result is undefined, if <computeroutput>count() &lt; 2</computeroutput>. </para></description></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name="version"><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor cv="= default"/>
<constructor cv="noexcept"><template>
          <template-type-parameter name="T"/>
        </template><parameter name="o"><paramtype>const <classname>mean</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Allow implicit conversion from mean&lt;T&gt;. </purpose></constructor>
<constructor cv="noexcept"><parameter name="n"><paramtype>const_reference</paramtype></parameter><parameter name="mean"><paramtype>const_reference</paramtype></parameter><parameter name="variance"><paramtype>const_reference</paramtype></parameter><purpose>Initialize to external count, mean, and variance. </purpose></constructor>
</class></namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/accumulators/sum.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="accumulators">
<class name="sum"><template>
      <template-type-parameter name="ValueType"/>
    </template><purpose>Uses Neumaier algorithm to compute accurate sums of floats. </purpose><description><para>The algorithm is an improved Kahan algorithm (<ulink url="https://en.wikipedia.org/wiki/Kahan_summation_algorithm">https://en.wikipedia.org/wiki/Kahan_summation_algorithm</ulink>). The algorithm uses memory for two numbers and is three to five times slower compared to using a single number to accumulate a sum, but the relative error of the sum is at the level of the machine precision, independent of the number of samples.</para><para>A. Neumaier, Zeitschrift fuer Angewandte Mathematik und Mechanik 54 (1974) 39-51. </para></description><typedef name="value_type"><type>ValueType</type></typedef>
<typedef name="const_reference"><type>const value_type &amp;</type></typedef>
<method-group name="public member functions">
<method name="operator++" cv="noexcept"><type><classname>sum</classname> &amp;</type><purpose>Increment sum by one. </purpose></method>
<method name="operator+=" cv="noexcept"><type><classname>sum</classname> &amp;</type><parameter name="value"><paramtype>const_reference</paramtype></parameter><purpose>Increment sum by value. </purpose></method>
<method name="operator+=" cv="noexcept"><type><classname>sum</classname> &amp;</type><parameter name="s"><paramtype>const <classname>sum</classname> &amp;</paramtype></parameter><purpose>Add another sum. </purpose></method>
<method name="operator *=" cv="noexcept"><type><classname>sum</classname> &amp;</type><parameter name="value"><paramtype>const_reference</paramtype></parameter><purpose>Scale by value. </purpose></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>sum</classname> &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>sum</classname> &amp;</paramtype></parameter></method>
<method name="value" cv="const noexcept"><type>value_type</type><purpose>Return value of the sum. </purpose></method>
<method name="large_part" cv="const noexcept"><type>const_reference</type><purpose>Return large part of the sum. </purpose></method>
<method name="small_part" cv="const noexcept"><type>const_reference</type><purpose>Return small part of the sum. </purpose></method>
<method name="conversion-operator" cv="const noexcept" specifiers="explicit"><type>value_type</type></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
<method name="operator *=" cv="noexcept"><type><classname>sum</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>sum</classname> &amp;</paramtype></parameter></method>
<method name="operator *" cv="const noexcept"><type><classname>sum</classname></type><parameter name="rhs"><paramtype>const <classname>sum</classname> &amp;</paramtype></parameter></method>
<method name="operator/=" cv="noexcept"><type><classname>sum</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>sum</classname> &amp;</paramtype></parameter></method>
<method name="operator/" cv="const noexcept"><type><classname>sum</classname></type><parameter name="rhs"><paramtype>const <classname>sum</classname> &amp;</paramtype></parameter></method>
<method name="operator&lt;" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>sum</classname> &amp;</paramtype></parameter></method>
<method name="operator&gt;" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>sum</classname> &amp;</paramtype></parameter></method>
<method name="operator&lt;=" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>sum</classname> &amp;</paramtype></parameter></method>
<method name="operator&gt;=" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>sum</classname> &amp;</paramtype></parameter></method>
</method-group>
<constructor cv="= default"/>
<constructor cv="noexcept"><parameter name="value"><paramtype>const_reference</paramtype></parameter><purpose>Initialize sum to value and allow implicit conversion. </purpose></constructor>
<constructor cv="noexcept"><template>
          <template-type-parameter name="T"/>
        </template><parameter name="s"><paramtype>const <classname>sum</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Allow implicit conversion from sum&lt;T&gt; </purpose></constructor>
<constructor cv="noexcept"><parameter name="large_part"><paramtype>const_reference</paramtype></parameter><parameter name="small_part"><paramtype>const_reference</paramtype></parameter><purpose>Initialize sum explicitly with large and small parts. </purpose></constructor>
</class></namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/algorithm/sum.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="algorithm">
<function name="sum"><type>auto</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="hist"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype><description><para>Const reference to the histogram. </para></description></parameter><parameter name="cov"><paramtype>const coverage</paramtype><default>coverage::all</default><description><para>Iterate over all or only inner bins (optional, default: all). </para></description></parameter><purpose>Compute the sum over all histogram cells (underflow/overflow included by default). </purpose><description><para>The implementation favors accuracy and protection against overflow over speed. If the value type of the histogram is an integral or floating point type, accumulators::sum&lt;double&gt; is used to compute the sum, else the original value type is used. Compilation fails, if the value type does not support operator+=. The return type is double if the value type of the histogram is integral or floating point, and the original value type otherwise.</para><para>If you need a different trade-off, you can write your own loop or use <computeroutput>std::accumulate</computeroutput>: <programlisting language="c++">// iterate over all bins
auto sum_all = std::accumulate(hist.begin(), hist.end(), 0.0);

// skip underflow/overflow bins
double sum = 0;
for (auto&amp;&amp; x : indexed(hist))
  sum += *x; // dereference accessor

// or:
// auto ind = boost::histogram::indexed(hist);
// auto sum = std::accumulate(ind.begin(), ind.end(), 0.0);
</programlisting></para><para>

</para></description><returns><para>accumulator type or double</para>
</returns></function>



















</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/accumulators/weighted_mean.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="accumulators">
<class name="weighted_mean"><template>
      <template-type-parameter name="ValueType"/>
    </template><purpose>Calculates mean and variance of weighted sample. </purpose><description><para>Uses West's incremental algorithm to improve numerical stability of mean and variance computation. </para></description><typedef name="value_type"><type>ValueType</type></typedef>
<typedef name="const_reference"><type>const value_type &amp;</type></typedef>
<method-group name="public member functions">
<method name="operator()"><type>void</type><parameter name="x"><paramtype>const_reference</paramtype></parameter><purpose>Insert sample x. </purpose></method>
<method name="operator()"><type>void</type><parameter name="w"><paramtype>const <classname>weight_type</classname>&lt; value_type &gt; &amp;</paramtype></parameter><parameter name="x"><paramtype>const_reference</paramtype></parameter><purpose>Insert sample x with weight w. </purpose></method>
<method name="operator+="><type><classname>weighted_mean</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>weighted_mean</classname> &amp;</paramtype></parameter><purpose>Add another <classname alt="boost::histogram::accumulators::weighted_mean">weighted_mean</classname>. </purpose></method>
<method name="operator *=" cv="noexcept"><type><classname>weighted_mean</classname> &amp;</type><parameter name="s"><paramtype>const_reference</paramtype></parameter><purpose>Scale by value. </purpose><description><para>This acts as if all samples were scaled by the value. </para></description></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>weighted_mean</classname> &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>weighted_mean</classname> &amp;</paramtype></parameter></method>
<method name="sum_of_weights" cv="const noexcept"><type>const_reference</type><purpose>Return sum of weights. </purpose></method>
<method name="sum_of_weights_squared" cv="const noexcept"><type>const_reference</type><purpose>Return sum of weights squared (variance of weight distribution). </purpose></method>
<method name="count" cv="const noexcept"><type>value_type</type><purpose>Return effective counts. </purpose><description><para>This corresponds to the equivalent number of unweighted samples that would have the same variance as this sample. count() should be used to check whether value() and variance() are defined, see documentation of value() and variance(). count() can be used to compute the variance of the mean by dividing variance() by count(). </para></description></method>
<method name="value" cv="const noexcept"><type>const_reference</type><purpose>Return mean value of accumulated weighted samples. </purpose><description><para>The result is undefined, if count() == 0. </para></description></method>
<method name="variance" cv="const"><type>value_type</type><purpose>Return variance of accumulated weighted samples. </purpose><description><para>The result is undefined, if count() == 0 or count() == 1. </para></description></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor cv="= default"/>
<constructor><template>
          <template-type-parameter name="T"/>
        </template><parameter name="o"><paramtype>const <classname>weighted_mean</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Allow implicit conversion from other weighted_means. </purpose></constructor>
<constructor><parameter name="wsum"><paramtype>const_reference</paramtype></parameter><parameter name="wsum2"><paramtype>const_reference</paramtype></parameter><parameter name="mean"><paramtype>const_reference</paramtype></parameter><parameter name="variance"><paramtype>const_reference</paramtype></parameter><purpose>Initialize to external sum of weights, sum of weights squared, mean, and variance. </purpose></constructor>
</class></namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/accumulators/weighted_sum.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="accumulators">
<class name="weighted_sum"><template>
      <template-type-parameter name="ValueType"/>
    </template><purpose>Holds sum of weights and its variance estimate. </purpose><typedef name="value_type"><type>ValueType</type></typedef>
<typedef name="const_reference"><type>const value_type &amp;</type></typedef>
<method-group name="public member functions">
<method name="operator++"><type><classname>weighted_sum</classname> &amp;</type><purpose>Increment by one. </purpose></method>
<method name="operator+="><type><classname>weighted_sum</classname> &amp;</type><parameter name="w"><paramtype>const <classname>weight_type</classname>&lt; value_type &gt; &amp;</paramtype></parameter><purpose>Increment by weight. </purpose></method>
<method name="operator+="><type><classname>weighted_sum</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>weighted_sum</classname> &amp;</paramtype></parameter><purpose>Added another weighted sum. </purpose></method>
<method name="operator/="><type><classname>weighted_sum</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>weighted_sum</classname> &amp;</paramtype></parameter><purpose>Divide by another weighted sum. </purpose></method>
<method name="operator *="><type><classname>weighted_sum</classname> &amp;</type><parameter name="x"><paramtype>const_reference</paramtype></parameter><purpose>Scale by value. </purpose></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>weighted_sum</classname> &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>weighted_sum</classname> &amp;</paramtype></parameter></method>
<method name="value" cv="const noexcept"><type>const_reference</type><purpose>Return value of the sum. </purpose></method>
<method name="variance" cv="const noexcept"><type>const_reference</type><purpose>Return estimated variance of the sum. </purpose></method>
<method name="conversion-operator" cv="const" specifiers="explicit"><type>const_reference</type></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor cv="= default"/>
<constructor cv="noexcept"><parameter name="value"><paramtype>const_reference</paramtype></parameter><purpose>Initialize sum to value and allow implicit conversion. </purpose></constructor>
<constructor cv="noexcept"><template>
          <template-type-parameter name="T"/>
        </template><parameter name="s"><paramtype>const <classname>weighted_sum</classname>&lt; T &gt; &amp;</paramtype></parameter><purpose>Allow implicit conversion from sum&lt;T&gt; </purpose></constructor>
<constructor cv="noexcept"><parameter name="value"><paramtype>const_reference</paramtype></parameter><parameter name="variance"><paramtype>const_reference</paramtype></parameter><purpose>Initialize sum to value and variance. </purpose></constructor>
</class></namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/algorithm.hpp">
<para>Includes all algorithm headers of the Boost.Histogram library. </para></header>
<header name="boost/histogram/algorithm/empty.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="algorithm">



















<function name="empty"><type>auto</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
        </template><parameter name="h"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter><parameter name="cov"><paramtype>coverage</paramtype></parameter><purpose>Check to see if all histogram cells are empty. </purpose><description><para>Use coverage to include or exclude the underflow/overflow bins.</para><para>This algorithm has O(N) complexity, where N is the number of cells.</para><para>Returns true if all cells are empty, and false otherwise. </para></description></function>
</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/algorithm/project.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="algorithm">

















<function name="project"><type>auto</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
          <template-nontype-parameter name="N"><type>unsigned</type></template-nontype-parameter>
          <template-nontype-parameter name="Ns"><type>typename...</type></template-nontype-parameter>
        </template><parameter name="h"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter><parameter name=""><paramtype>std::integral_constant&lt; unsigned, N &gt;</paramtype></parameter><parameter name=""><paramtype>Ns...</paramtype></parameter><purpose>Returns a lower-dimensional histogram, summing over removed axes. </purpose><description><para>Arguments are the source histogram and compile-time numbers, the remaining indices of the axes. Returns a new histogram which only contains the subset of axes. The source histogram is summed over the removed axes. </para></description></function>
<function name="project"><type>auto</type><template>
          <template-type-parameter name="A"/>
          <template-type-parameter name="S"/>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="h"><paramtype>const <classname>histogram</classname>&lt; A, S &gt; &amp;</paramtype></parameter><parameter name="c"><paramtype>const Iterable &amp;</paramtype></parameter><purpose>Returns a lower-dimensional histogram, summing over removed axes. </purpose><description><para>This version accepts a source histogram and an iterable range containing the remaining indices. </para></description></function>

</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/algorithm/reduce.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="algorithm">
<enum name="slice_mode"><enumvalue name="shrink"/><enumvalue name="crop"/><purpose>Whether to behave like <computeroutput>shrink</computeroutput> or <computeroutput>crop</computeroutput> regarding removed bins. </purpose></enum>
<typedef name="reduce_command"><purpose>Holder for a reduce command. </purpose><description><para>Use this type to store reduce commands in a container. The internals of this type are an implementation detail. </para></description><type><emphasis>unspecified</emphasis></type></typedef>

<function name="shrink"><type>reduce_command</type><parameter name="iaxis"><paramtype>unsigned</paramtype><description><para>which axis to operate on. </para></description></parameter><parameter name="lower"><paramtype>double</paramtype><description><para>bin which contains lower is first to be kept. </para></description></parameter><parameter name="upper"><paramtype>double</paramtype><description><para>bin which contains upper is last to be kept, except if upper is equal to the lower edge. </para></description></parameter><purpose>Shrink command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to axis with given index.</para><para>Shrinking is based on an inclusive value interval. The bin which contains the first value starts the range of bins to keep. The bin which contains the second value is the last included in that range. When the second value is exactly equal to a lower bin edge, then the previous bin is the last in the range.</para><para>The counts in removed bins are added to the corresponding underflow and overflow bins, if they are present. If they are not present, the counts are discarded. Also see <computeroutput>crop</computeroutput>, which always discards the counts.</para><para>
</para></description></function>
<function name="shrink"><type>reduce_command</type><parameter name="lower"><paramtype>double</paramtype><description><para>bin which contains lower is first to be kept. </para></description></parameter><parameter name="upper"><paramtype>double</paramtype><description><para>bin which contains upper is last to be kept, except if upper is equal to the lower edge. </para></description></parameter><purpose>Shrink command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to corresponding axis in order of reduce arguments.</para><para>Shrinking is based on an inclusive value interval. The bin which contains the first value starts the range of bins to keep. The bin which contains the second value is the last included in that range. When the second value is exactly equal to a lower bin edge, then the previous bin is the last in the range.</para><para>The counts in removed bins are added to the corresponding underflow and overflow bins, if they are present. If they are not present, the counts are discarded. Also see <computeroutput>crop</computeroutput>, which always discards the counts.</para><para>
</para></description></function>
<function name="crop"><type>reduce_command</type><parameter name="iaxis"><paramtype>unsigned</paramtype><description><para>which axis to operate on. </para></description></parameter><parameter name="lower"><paramtype>double</paramtype><description><para>bin which contains lower is first to be kept. </para></description></parameter><parameter name="upper"><paramtype>double</paramtype><description><para>bin which contains upper is last to be kept, except if upper is equal to the lower edge. </para></description></parameter><purpose>Crop command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to axis with given index.</para><para>Works like <computeroutput>shrink</computeroutput> (see shrink documentation for details), but counts in removed bins are always discarded, whether underflow and overflow bins are present or not.</para><para>
</para></description></function>
<function name="crop"><type>reduce_command</type><parameter name="lower"><paramtype>double</paramtype><description><para>bin which contains lower is first to be kept. </para></description></parameter><parameter name="upper"><paramtype>double</paramtype><description><para>bin which contains upper is last to be kept, except if upper is equal to the lower edge. </para></description></parameter><purpose>Crop command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to corresponding axis in order of reduce arguments.</para><para>Works like <computeroutput>shrink</computeroutput> (see shrink documentation for details), but counts in removed bins are discarded, whether underflow and overflow bins are present or not. If the cropped range goes beyond the axis range, then the content of the underflow or overflow bin which overlaps with the range is kept.</para><para>If the counts in an existing underflow or overflow bin are discared by the crop, the corresponding memory cells are not physically removed. Only their contents are set to zero. This technical limitation may be lifted in the future, then crop may completely remove the cropped memory cells.</para><para>
</para></description></function>
<function name="slice"><type>reduce_command</type><parameter name="iaxis"><paramtype>unsigned</paramtype><description><para>which axis to operate on. </para></description></parameter><parameter name="begin"><paramtype>axis::index_type</paramtype><description><para>first index that should be kept. </para></description></parameter><parameter name="end"><paramtype>axis::index_type</paramtype><description><para>one past the last index that should be kept. </para></description></parameter><parameter name="mode"><paramtype>slice_mode</paramtype><default>slice_mode::shrink</default><description><para>whether to behave like <computeroutput>shrink</computeroutput> or <computeroutput>crop</computeroutput> regarding removed bins. </para></description></parameter><purpose>Slice command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to axis with given index.</para><para>Slicing works like <computeroutput>shrink</computeroutput> or <computeroutput>crop</computeroutput>, but uses bin indices instead of values.</para><para>
</para></description></function>
<function name="slice"><type>reduce_command</type><parameter name="begin"><paramtype>axis::index_type</paramtype><description><para>first index that should be kept. </para></description></parameter><parameter name="end"><paramtype>axis::index_type</paramtype><description><para>one past the last index that should be kept. </para></description></parameter><parameter name="mode"><paramtype>slice_mode</paramtype><default>slice_mode::shrink</default><description><para>whether to behave like <computeroutput>shrink</computeroutput> or <computeroutput>crop</computeroutput> regarding removed bins. </para></description></parameter><purpose>Slice command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to corresponding axis in order of reduce arguments.</para><para>Slicing works like <computeroutput>shrink</computeroutput> or <computeroutput>crop</computeroutput>, but uses bin indices instead of values.</para><para>
</para></description></function>
<function name="rebin"><type>reduce_command</type><parameter name="iaxis"><paramtype>unsigned</paramtype><description><para>which axis to operate on. </para></description></parameter><parameter name="merge"><paramtype>unsigned</paramtype><description><para>how many adjacent bins to merge into one. </para></description></parameter><purpose>Rebin command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to axis with given index.</para><para>The command merges N adjacent bins into one. This makes the axis coarser and the bins wider. The original number of bins is divided by N. If there is a rest to this devision, the axis is implicitly shrunk at the upper end by that rest.</para><para>
</para></description></function>
<function name="rebin"><type>reduce_command</type><parameter name="merge"><paramtype>unsigned</paramtype><description><para>how many adjacent bins to merge into one. </para></description></parameter><purpose>Rebin command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to corresponding axis in order of reduce arguments.</para><para>The command merges N adjacent bins into one. This makes the axis coarser and the bins wider. The original number of bins is divided by N. If there is a rest to this devision, the axis is implicitly shrunk at the upper end by that rest.</para><para>
</para></description></function>
<function name="shrink_and_rebin"><type>reduce_command</type><parameter name="iaxis"><paramtype>unsigned</paramtype><description><para>which axis to operate on. </para></description></parameter><parameter name="lower"><paramtype>double</paramtype><description><para>lowest bound that should be kept. </para></description></parameter><parameter name="upper"><paramtype>double</paramtype><description><para>highest bound that should be kept. If upper is inside bin interval, the whole interval is removed. </para></description></parameter><parameter name="merge"><paramtype>unsigned</paramtype><description><para>how many adjacent bins to merge into one. </para></description></parameter><purpose>Shrink and rebin command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to corresponding axis in order of reduce arguments.</para><para>To shrink(unsigned, double, double) and rebin(unsigned, unsigned) in one command (see the respective commands for more details). Equivalent to passing both commands for the same axis to <computeroutput>reduce</computeroutput>.</para><para>
</para></description></function>
<function name="shrink_and_rebin"><type>reduce_command</type><parameter name="lower"><paramtype>double</paramtype><description><para>lowest bound that should be kept. </para></description></parameter><parameter name="upper"><paramtype>double</paramtype><description><para>highest bound that should be kept. If upper is inside bin interval, the whole interval is removed. </para></description></parameter><parameter name="merge"><paramtype>unsigned</paramtype><description><para>how many adjacent bins to merge into one. </para></description></parameter><purpose>Shrink and rebin command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to corresponding axis in order of reduce arguments.</para><para>To <computeroutput>shrink</computeroutput> and <computeroutput>rebin</computeroutput> in one command (see the respective commands for more details). Equivalent to passing both commands for the same axis to <computeroutput>reduce</computeroutput>.</para><para>
</para></description></function>
<function name="crop_and_rebin"><type>reduce_command</type><parameter name="iaxis"><paramtype>unsigned</paramtype><description><para>which axis to operate on. </para></description></parameter><parameter name="lower"><paramtype>double</paramtype><description><para>lowest bound that should be kept. </para></description></parameter><parameter name="upper"><paramtype>double</paramtype><description><para>highest bound that should be kept. If upper is inside bin interval, the whole interval is removed. </para></description></parameter><parameter name="merge"><paramtype>unsigned</paramtype><description><para>how many adjacent bins to merge into one. </para></description></parameter><purpose>Crop and rebin command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to axis with given index.</para><para>To <computeroutput>crop</computeroutput> and <computeroutput>rebin</computeroutput> in one command (see the respective commands for more details). Equivalent to passing both commands for the same axis to <computeroutput>reduce</computeroutput>.</para><para>
</para></description></function>
<function name="crop_and_rebin"><type>reduce_command</type><parameter name="lower"><paramtype>double</paramtype><description><para>lowest bound that should be kept. </para></description></parameter><parameter name="upper"><paramtype>double</paramtype><description><para>highest bound that should be kept. If upper is inside bin interval, the whole interval is removed. </para></description></parameter><parameter name="merge"><paramtype>unsigned</paramtype><description><para>how many adjacent bins to merge into one. </para></description></parameter><purpose>Crop and rebin command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to corresponding axis in order of reduce arguments.</para><para>To <computeroutput>crop</computeroutput> and <computeroutput>rebin</computeroutput> in one command (see the respective commands for more details). Equivalent to passing both commands for the same axis to <computeroutput>reduce</computeroutput>.</para><para>
</para></description></function>
<function name="slice_and_rebin"><type>reduce_command</type><parameter name="iaxis"><paramtype>unsigned</paramtype><description><para>which axis to operate on. </para></description></parameter><parameter name="begin"><paramtype>axis::index_type</paramtype><description><para>first index that should be kept. </para></description></parameter><parameter name="end"><paramtype>axis::index_type</paramtype><description><para>one past the last index that should be kept. </para></description></parameter><parameter name="merge"><paramtype>unsigned</paramtype><description><para>how many adjacent bins to merge into one. </para></description></parameter><parameter name="mode"><paramtype>slice_mode</paramtype><default>slice_mode::shrink</default><description><para>slice mode, see slice_mode. </para></description></parameter><purpose>Slice and rebin command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to axis with given index.</para><para>To <computeroutput>slice</computeroutput> and <computeroutput>rebin</computeroutput> in one command (see the respective commands for more details). Equivalent to passing both commands for the same axis to <computeroutput>reduce</computeroutput>.</para><para>
</para></description></function>
<function name="slice_and_rebin"><type>reduce_command</type><parameter name="begin"><paramtype>axis::index_type</paramtype><description><para>first index that should be kept. </para></description></parameter><parameter name="end"><paramtype>axis::index_type</paramtype><description><para>one past the last index that should be kept. </para></description></parameter><parameter name="merge"><paramtype>unsigned</paramtype><description><para>how many adjacent bins to merge into one. </para></description></parameter><parameter name="mode"><paramtype>slice_mode</paramtype><default>slice_mode::shrink</default><description><para>slice mode, see slice_mode. </para></description></parameter><purpose>Slice and rebin command to be used in <computeroutput>reduce</computeroutput>. </purpose><description><para>Command is applied to corresponding axis in order of reduce arguments.</para><para>To <computeroutput>slice</computeroutput> and <computeroutput>rebin</computeroutput> in one command (see the respective commands for more details). Equivalent to passing both commands for the same axis to <computeroutput>reduce</computeroutput>.</para><para>
</para></description></function>
<function name="reduce"><type>Histogram</type><template>
          <template-type-parameter name="Histogram"/>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="hist"><paramtype>const Histogram &amp;</paramtype><description><para>original histogram. </para></description></parameter><parameter name="options"><paramtype>const Iterable &amp;</paramtype><description><para>iterable sequence of reduce commands: <computeroutput>shrink</computeroutput>, <computeroutput>slice</computeroutput>, <computeroutput>rebin</computeroutput>, <computeroutput>shrink_and_rebin</computeroutput>, or <computeroutput>slice_and_rebin</computeroutput>. The element type of the iterable should be <computeroutput>reduce_command</computeroutput>. </para></description></parameter><purpose>Shrink, crop, slice, and/or rebin axes of a histogram. </purpose><description><para>Returns a new reduced histogram and leaves the original histogram untouched.</para><para>The commands <computeroutput>rebin</computeroutput> and <computeroutput>shrink</computeroutput> or <computeroutput>slice</computeroutput> for the same axis are automatically combined, this is not an error. Passing a <computeroutput>shrink</computeroutput> and a <computeroutput>slice</computeroutput> command for the same axis or two <computeroutput>rebin</computeroutput> commands triggers an <computeroutput>invalid_argument</computeroutput> exception. Trying to reducing a non-reducible axis triggers an <computeroutput>invalid_argument</computeroutput> exception. Histograms with non-reducible axes can still be reduced along the other axes that are reducible.</para><para>An overload allows one to pass reduce_command as positional arguments.</para><para>
</para></description></function>
<function name="reduce"><type>Histogram</type><template>
          <template-type-parameter name="Histogram"/>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
        </template><parameter name="hist"><paramtype>const Histogram &amp;</paramtype><description><para>original histogram. </para></description></parameter><parameter name="opt"><paramtype>const reduce_command &amp;</paramtype><description><para>first reduce command; one of <computeroutput>shrink</computeroutput>, <computeroutput>slice</computeroutput>, <computeroutput>rebin</computeroutput>, <computeroutput>shrink_and_rebin</computeroutput>, or <computeroutput>slice_or_rebin</computeroutput>. </para></description></parameter><parameter name="opts"><paramtype>const Ts &amp;...</paramtype><description><para>more reduce commands. </para></description></parameter><purpose>Shrink, slice, and/or rebin axes of a histogram. </purpose><description><para>Returns a new reduced histogram and leaves the original histogram untouched.</para><para>The commands <computeroutput>rebin</computeroutput> and <computeroutput>shrink</computeroutput> or <computeroutput>slice</computeroutput> for the same axis are automatically combined, this is not an error. Passing a <computeroutput>shrink</computeroutput> and a <computeroutput>slice</computeroutput> command for the same axis or two <computeroutput>rebin</computeroutput> commands triggers an invalid_argument exception. It is safe to reduce histograms with some axis that are not reducible along the other axes. Trying to reducing a non-reducible axis triggers an invalid_argument exception.</para><para>An overload allows one to pass an iterable of reduce_command.</para><para>
</para></description></function>



</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/axis.hpp">
<para>Includes all axis headers of the Boost.Histogram library. </para><para>Extra header not automatically included:<itemizedlist>
<listitem><para><ulink url="histogram/reference.html#header.boost.histogram.axis.ostream_hpp">boost/histogram/axis/ostream.hpp</ulink> </para>
</listitem></itemizedlist>
</para></header>
<header name="boost/histogram/axis/boolean.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="axis">
<class name="boolean"><template>
      <template-type-parameter name="MetaData"><purpose><para>type to store meta data. </para></purpose></template-type-parameter>
    </template><inherit access="public">boost::histogram::axis::iterator_mixin&lt; boolean&lt; MetaData &gt; &gt;</inherit><inherit access="public">metadata_base_t&lt; MetaData &gt;</inherit><purpose>Discrete axis for boolean data. </purpose><description><para>Binning is a pass-though operation with zero cost, making this the fastest possible axis. The axis has no internal state apart from the optional metadata state. The axis has no under- and overflow bins. It cannot grow and cannot be reduced.</para><para>
</para></description><method-group name="public member functions">
<method name="index" cv="const noexcept"><type>index_type</type><parameter name="x"><paramtype>value_type</paramtype></parameter><purpose>Return index for value argument. </purpose></method>
<method name="value" cv="const noexcept"><type>value_type</type><parameter name="i"><paramtype>index_type</paramtype></parameter><purpose>Return value for index argument. </purpose></method>
<method name="bin" cv="const noexcept"><type>value_type</type><parameter name="i"><paramtype>index_type</paramtype></parameter><purpose>Return bin for index argument. </purpose></method>
<method name="size" cv="const noexcept"><type>index_type</type><purpose>Returns the number of bins, without over- or underflow. </purpose></method>
<method name="operator==" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="M"/>
        </template><parameter name="o"><paramtype>const <classname>boolean</classname>&lt; M &gt; &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="M"/>
        </template><parameter name="o"><paramtype>const <classname>boolean</classname>&lt; M &gt; &amp;</paramtype></parameter></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit" cv="noexcept(std::is_nothrow_move_constructible&lt; metadata_type &gt;::value)"><parameter name="meta"><paramtype>metadata_type</paramtype><default>{}</default><description><para>description of the axis.</para></description></parameter><purpose>Construct a boolean axis. </purpose><description><para>
The constructor is nothrow if meta is nothrow move constructible. </para></description></constructor>
<method-group name="public static functions">
<method name="inclusive" cv="noexcept" specifiers="static"><type>constexpr bool</type><purpose>Whether the axis is inclusive (see <classname alt="boost::histogram::axis::traits::is_inclusive">axis::traits::is_inclusive</classname>). </purpose></method>
<method name="options" cv="noexcept" specifiers="static"><type>constexpr unsigned</type><purpose>Returns the options. </purpose></method>
</method-group>
</class>


















</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/axis/category.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="axis">
<class name="category"><template>
      <template-type-parameter name="Value"><purpose><para>input value type, must be equal-comparable. </para></purpose></template-type-parameter>
      <template-type-parameter name="MetaData"><purpose><para>type to store meta data. </para></purpose></template-type-parameter>
      <template-type-parameter name="Options"><purpose><para>see boost::histogram::axis::option. </para></purpose></template-type-parameter>
      <template-type-parameter name="Allocator"><purpose><para>allocator to use for dynamic memory management.</para></purpose></template-type-parameter>
    </template><inherit access="public">boost::histogram::axis::iterator_mixin&lt; category&lt; Value, MetaData, Options, Allocator &gt; &gt;</inherit><inherit access="public">metadata_base_t&lt; MetaData &gt;</inherit><purpose>Maps at a set of unique values to bin indices. </purpose><description><para>The axis maps a set of values to bins, following the order of arguments in the constructor. The optional overflow bin for this axis counts input values that are not part of the set. Binning has O(N) complexity, but with a very small factor. For small N (the typical use case) it beats other kinds of lookup.</para><para>
The options <computeroutput>underflow</computeroutput> and <computeroutput>circular</computeroutput> are not allowed. The options <computeroutput>growth</computeroutput> and <computeroutput>overflow</computeroutput> are mutually exclusive. </para></description><method-group name="public member functions">
<method name="index" cv="const noexcept"><type>index_type</type><parameter name="x"><paramtype>const value_type &amp;</paramtype></parameter><purpose>Return index for value argument. </purpose></method>
<method name="update"><type>std::pair&lt; index_type, index_type &gt;</type><parameter name="x"><paramtype>const value_type &amp;</paramtype></parameter><purpose>Returns index and shift (if axis has grown) for the passed argument. </purpose></method>
<method name="value" cv="const"><type>auto</type><parameter name="idx"><paramtype>index_type</paramtype></parameter><purpose>Return value for index argument. </purpose><description><para>Throws <computeroutput>std::out_of_range</computeroutput> if the index is out of bounds. </para></description></method>
<method name="bin" cv="const"><type>decltype(auto)</type><parameter name="idx"><paramtype>index_type</paramtype></parameter><purpose>Return value for index argument; alias for value(...). </purpose></method>
<method name="size" cv="const noexcept"><type>index_type</type><purpose>Returns the number of bins, without over- or underflow. </purpose></method>
<method name="operator==" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="V"/>
          <template-type-parameter name="M"/>
          <template-type-parameter name="O"/>
          <template-type-parameter name="A"/>
        </template><parameter name="o"><paramtype>const <classname>category</classname>&lt; V, M, O, A &gt; &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="V"/>
          <template-type-parameter name="M"/>
          <template-type-parameter name="O"/>
          <template-type-parameter name="A"/>
        </template><parameter name="o"><paramtype>const <classname>category</classname>&lt; V, M, O, A &gt; &amp;</paramtype></parameter></method>
<method name="get_allocator" cv="const"><type>allocator_type</type></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor cv="= default"/>
<constructor specifiers="explicit"><parameter name="alloc"><paramtype>allocator_type</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
          <template-type-parameter name=""><default>detail::requires_iterator&lt;It&gt;</default></template-type-parameter>
        </template><parameter name="begin"><paramtype>It</paramtype><description><para>begin of category range of unique values. </para></description></parameter><parameter name="end"><paramtype>It</paramtype><description><para>end of category range of unique values. </para></description></parameter><parameter name="meta"><paramtype>metadata_type</paramtype><default>{}</default><description><para>description of the axis (optional). </para></description></parameter><parameter name="options"><paramtype>options_type</paramtype><default>{}</default><description><para>see boost::histogram::axis::option (optional). </para></description></parameter><parameter name="alloc"><paramtype>allocator_type</paramtype><default>{}</default><description><para>allocator instance to use (optional).</para></description></parameter><purpose>Construct from forward iterator range of unique values. </purpose><description><para>
The constructor throws <computeroutput>std::invalid_argument</computeroutput> if iterator range is invalid. If the range contains duplicated values, the behavior of the axis is undefined.</para><para>The arguments meta and alloc are passed by value. If you move either of them into the axis and the constructor throws, their values are lost. Do not move if you cannot guarantee that the bin description is not valid. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
          <template-type-parameter name="A"/>
          <template-type-parameter name=""><default>detail::requires_iterator&lt;It&gt;</default></template-type-parameter>
          <template-type-parameter name=""><default>detail::requires_allocator&lt;A&gt;</default></template-type-parameter>
        </template><parameter name="begin"><paramtype>It</paramtype></parameter><parameter name="end"><paramtype>It</paramtype></parameter><parameter name="meta"><paramtype>metadata_type</paramtype></parameter><parameter name="alloc"><paramtype>A</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="C"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;C&gt;</default></template-type-parameter>
        </template><parameter name="iterable"><paramtype>const C &amp;</paramtype><description><para>sequence of unique values. </para></description></parameter><parameter name="meta"><paramtype>metadata_type</paramtype><default>{}</default><description><para>description of the axis. </para></description></parameter><parameter name="options"><paramtype>options_type</paramtype><default>{}</default><description><para>see boost::histogram::axis::option (optional). </para></description></parameter><parameter name="alloc"><paramtype>allocator_type</paramtype><default>{}</default><description><para>allocator instance to use. </para></description></parameter><purpose>Construct axis from iterable sequence of unique values. </purpose><description><para>
</para></description></constructor>
<constructor><template>
          <template-type-parameter name="C"/>
          <template-type-parameter name="A"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;C&gt;</default></template-type-parameter>
          <template-type-parameter name=""><default>detail::requires_allocator&lt;A&gt;</default></template-type-parameter>
        </template><parameter name="iterable"><paramtype>const C &amp;</paramtype></parameter><parameter name="meta"><paramtype>metadata_type</paramtype></parameter><parameter name="alloc"><paramtype>A</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="U"/>
        </template><parameter name="list"><paramtype>std::initializer_list&lt; U &gt;</paramtype><description><para><computeroutput>std::initializer_list</computeroutput> of unique values. </para></description></parameter><parameter name="meta"><paramtype>metadata_type</paramtype><default>{}</default><description><para>description of the axis. </para></description></parameter><parameter name="options"><paramtype>options_type</paramtype><default>{}</default><description><para>see boost::histogram::axis::option (optional). </para></description></parameter><parameter name="alloc"><paramtype>allocator_type</paramtype><default>{}</default><description><para>allocator instance to use. </para></description></parameter><purpose>Construct axis from an initializer list of unique values. </purpose><description><para>
</para></description></constructor>
<constructor><template>
          <template-type-parameter name="U"/>
          <template-type-parameter name="A"/>
          <template-type-parameter name=""><default>detail::requires_allocator&lt;A&gt;</default></template-type-parameter>
        </template><parameter name="list"><paramtype>std::initializer_list&lt; U &gt;</paramtype></parameter><parameter name="meta"><paramtype>metadata_type</paramtype></parameter><parameter name="alloc"><paramtype>A</paramtype></parameter></constructor>
<constructor><parameter name="src"><paramtype>const <classname>category</classname> &amp;</paramtype></parameter><parameter name="begin"><paramtype>index_type</paramtype></parameter><parameter name="end"><paramtype>index_type</paramtype></parameter><parameter name="merge"><paramtype>unsigned</paramtype></parameter><purpose>Constructor used by algorithm::reduce to shrink and rebin (not for users). </purpose></constructor>
<method-group name="public static functions">
<method name="options" cv="noexcept" specifiers="static"><type>constexpr unsigned</type><purpose>Returns the options. </purpose></method>
<method name="inclusive" cv="noexcept" specifiers="static"><type>constexpr bool</type><purpose>Whether the axis is inclusive (see <classname alt="boost::histogram::axis::traits::is_inclusive">axis::traits::is_inclusive</classname>). </purpose></method>
<method name="ordered" cv="noexcept" specifiers="static"><type>constexpr bool</type><purpose>Indicate that the axis is not ordered. </purpose></method>
</method-group>
</class>


















</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/axis/integer.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="axis">
<class name="integer"><template>
      <template-type-parameter name="Value"><purpose><para>input value type. Must be integer or floating point. </para></purpose></template-type-parameter>
      <template-type-parameter name="MetaData"><purpose><para>type to store meta data. </para></purpose></template-type-parameter>
      <template-type-parameter name="Options"><purpose><para>see boost::histogram::axis::option. </para></purpose></template-type-parameter>
    </template><inherit access="public">boost::histogram::axis::iterator_mixin&lt; integer&lt; Value, MetaData, Options &gt; &gt;</inherit><inherit access="public">metadata_base_t&lt; MetaData &gt;</inherit><purpose>Axis for an interval of integer values with unit steps. </purpose><description><para>Binning is a O(1) operation. This axis bins even faster than a regular axis.</para><para>The options <computeroutput>growth</computeroutput> and <computeroutput>circular</computeroutput> are mutually exclusive. If the axis uses integers and either <computeroutput>growth</computeroutput> or <computeroutput>circular</computeroutput> are set, the axis cannot have the options <computeroutput>underflow</computeroutput> or <computeroutput>overflow</computeroutput> set.</para><para>
</para></description><method-group name="public member functions">
<method name="index" cv="const noexcept"><type>index_type</type><parameter name="x"><paramtype>value_type</paramtype></parameter><purpose>Return index for value argument. </purpose></method>
<method name="update" cv="noexcept"><type>auto</type><parameter name="x"><paramtype>value_type</paramtype></parameter><purpose>Returns index and shift (if axis has grown) for the passed argument. </purpose></method>
<method name="value" cv="const noexcept"><type>value_type</type><parameter name="i"><paramtype>local_index_type</paramtype></parameter><purpose>Return value for index argument. </purpose></method>
<method name="bin" cv="const noexcept"><type>decltype(auto)</type><parameter name="idx"><paramtype>index_type</paramtype></parameter><purpose>Return bin for index argument. </purpose></method>
<method name="size" cv="const noexcept"><type>index_type</type><purpose>Returns the number of bins, without over- or underflow. </purpose></method>
<method name="operator==" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="V"/>
          <template-type-parameter name="M"/>
          <template-type-parameter name="O"/>
        </template><parameter name="o"><paramtype>const <classname>integer</classname>&lt; V, M, O &gt; &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="V"/>
          <template-type-parameter name="M"/>
          <template-type-parameter name="O"/>
        </template><parameter name="o"><paramtype>const <classname>integer</classname>&lt; V, M, O &gt; &amp;</paramtype></parameter></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor cv="= default"/>
<constructor><parameter name="start"><paramtype>value_type</paramtype><description><para>first integer of covered range. </para></description></parameter><parameter name="stop"><paramtype>value_type</paramtype><description><para>one past last integer of covered range. </para></description></parameter><parameter name="meta"><paramtype>metadata_type</paramtype><default>{}</default><description><para>description of the axis (optional). </para></description></parameter><parameter name="options"><paramtype>options_type</paramtype><default>{}</default><description><para>see boost::histogram::axis::option (optional).</para></description></parameter><purpose>Construct over semi-open integer interval [start, stop). </purpose><description><para>
The constructor throws <computeroutput>std::invalid_argument</computeroutput> if start is not less than stop.</para><para>The arguments meta and alloc are passed by value. If you move either of them into the axis and the constructor throws, their values are lost. Do not move if you cannot guarantee that the bin description is not valid. </para></description></constructor>
<constructor><parameter name="src"><paramtype>const <classname>integer</classname> &amp;</paramtype></parameter><parameter name="begin"><paramtype>index_type</paramtype></parameter><parameter name="end"><paramtype>index_type</paramtype></parameter><parameter name="merge"><paramtype>unsigned</paramtype></parameter><purpose>Constructor used by algorithm::reduce to shrink and rebin. </purpose></constructor>
<method-group name="public static functions">
<method name="options" cv="noexcept" specifiers="static"><type>constexpr unsigned</type><purpose>Returns the options. </purpose></method>
<method name="inclusive" cv="noexcept" specifiers="static"><type>constexpr bool</type><purpose>Whether the axis is inclusive (see <classname alt="boost::histogram::axis::traits::is_inclusive">axis::traits::is_inclusive</classname>). </purpose></method>
</method-group>
<method-group name="private member functions">
<method name="index_impl" cv="const noexcept"><type>index_type</type><template>
          <template-type-parameter name="B"/>
        </template><parameter name=""><paramtype>std::false_type</paramtype></parameter><parameter name=""><paramtype>B</paramtype></parameter><parameter name="z"><paramtype>double</paramtype></parameter></method>
<method name="index_impl" cv="const noexcept"><type>index_type</type><parameter name=""><paramtype>std::true_type</paramtype></parameter><parameter name=""><paramtype>std::false_type</paramtype></parameter><parameter name="z"><paramtype>double</paramtype></parameter></method>
<method name="index_impl" cv="const noexcept"><type>index_type</type><parameter name=""><paramtype>std::true_type</paramtype></parameter><parameter name=""><paramtype>std::true_type</paramtype></parameter><parameter name="z"><paramtype>double</paramtype></parameter></method>
</method-group>
</class>


















</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/axis/interval_view.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="axis">
<class name="interval_view"><template>
      <template-type-parameter name="Axis"/>
    </template><purpose>Lightweight bin view. </purpose><description><para>Represents the current bin interval. </para></description><method-group name="public member functions">
<method name="lower" cv="const noexcept"><type>decltype(auto)</type><purpose>Return lower edge of bin. </purpose></method>
<method name="upper" cv="const noexcept"><type>decltype(auto)</type><purpose>Return upper edge of bin. </purpose></method>
<method name="center" cv="const noexcept"><type>decltype(auto)</type><purpose>Return center of bin. </purpose></method>
<method name="width" cv="const noexcept"><type>decltype(auto)</type><purpose>Return width of bin. </purpose></method>
<method name="operator==" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="BinType"/>
        </template><parameter name="rhs"><paramtype>const BinType &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="BinType"/>
        </template><parameter name="rhs"><paramtype>const BinType &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="axis"><paramtype>const Axis &amp;</paramtype></parameter><parameter name="idx"><paramtype>index_type</paramtype></parameter></constructor>
<constructor cv="= delete"><parameter name="axis"><paramtype>Axis &amp;&amp;</paramtype></parameter><parameter name="idx"><paramtype>index_type</paramtype></parameter></constructor>
</class>


















</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/axis/iterator.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="axis">
<class name="iterator"><template>
      <template-type-parameter name="Axis"/>
    </template><typedef name="reference"><type>typename iterator::iterator_adaptor_::reference</type></typedef>
<method-group name="public member functions">
<method name="operator *" cv="const"><type>reference</type><purpose>Return current bin object. </purpose></method>
</method-group>
<constructor><parameter name="axis"><paramtype>const Axis &amp;</paramtype></parameter><parameter name="idx"><paramtype>index_type</paramtype></parameter><purpose>Make iterator from axis and index. </purpose></constructor>
</class><class name="iterator_mixin"><template>
      <template-type-parameter name="Derived"/>
    </template><purpose>Uses CRTP to inject iterator logic into Derived. </purpose><typedef name="const_iterator"><type>iterator&lt; Derived &gt;</type></typedef>
<typedef name="const_reverse_iterator"><type>std::reverse_iterator&lt; const_iterator &gt;</type></typedef>
<method-group name="public member functions">
<method name="begin" cv="const noexcept"><type>const_iterator</type><purpose>Bin iterator to beginning of the axis (read-only). </purpose></method>
<method name="end" cv="const noexcept"><type>const_iterator</type><purpose>Bin iterator to the end of the axis (read-only). </purpose></method>
<method name="rbegin" cv="const noexcept"><type>const_reverse_iterator</type><purpose>Reverse bin iterator to the last entry of the axis (read-only). </purpose></method>
<method name="rend" cv="const noexcept"><type>const_reverse_iterator</type><purpose>Reverse bin iterator to the end (read-only). </purpose></method>
</method-group>
</class>


















</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/axis/metadata_base.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="axis">
<class name="metadata_base"><template>
      <template-type-parameter name="Metadata"><purpose><para>Wrapped meta data type. </para></purpose></template-type-parameter>
      <template-nontype-parameter name="Detail"><type>bool</type></template-nontype-parameter>
    </template><purpose>Meta data holder with space optimization for empty meta data types. </purpose><description><para>Allows write-access to metadata even if const.</para><para>
</para></description><method-group name="protected member functions">
</method-group>
<constructor cv="= default"/>
<constructor cv="= default"><parameter name=""><paramtype>const <classname>metadata_base</classname> &amp;</paramtype></parameter></constructor>
<copy-assignment cv="= default"><type><classname>metadata_base</classname> &amp;</type><parameter name=""><paramtype>const <classname>metadata_base</classname> &amp;</paramtype></parameter></copy-assignment>
<constructor cv="noexcept"><parameter name="o"><paramtype><classname>metadata_base</classname> &amp;&amp;</paramtype></parameter></constructor>
<constructor cv="noexcept"><parameter name="o"><paramtype>metadata_type &amp;&amp;</paramtype></parameter></constructor>
<copy-assignment cv="noexcept"><type><classname>metadata_base</classname> &amp;</type><parameter name="o"><paramtype><classname>metadata_base</classname> &amp;&amp;</paramtype></parameter></copy-assignment>
<method-group name="public member functions">
<method name="metadata" cv="noexcept"><type>metadata_type &amp;</type><purpose>Returns reference to metadata. </purpose></method>
<method name="metadata" cv="const noexcept"><type>metadata_type &amp;</type><purpose>Returns reference to mutable metadata from const axis. </purpose></method>
</method-group>
</class>


















</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/axis/option.hpp">
<para>Options for builtin axis types. </para><para>Options <computeroutput>circular</computeroutput> and <computeroutput>growth</computeroutput> are mutually exclusive. Options <computeroutput>circular</computeroutput> and <computeroutput>underflow</computeroutput> are mutually exclusive. </para><namespace name="boost">
<namespace name="histogram">
<namespace name="axis">
<namespace name="option">
<struct name="bit"><template>
      <template-nontype-parameter name="Pos"><type>unsigned</type><purpose><para>position of the bit in the set. </para></purpose></template-nontype-parameter>
    </template><purpose>Single option flag. </purpose><description><para>
</para></description></struct><struct name="bitset"><template>
      <template-nontype-parameter name="Bits"><type>unsigned</type></template-nontype-parameter>
    </template><inherit access="public">std::integral_constant&lt; unsigned, Bits &gt;</inherit><purpose>Holder of axis options. </purpose><method-group name="public static functions">
<method name="test" specifiers="static"><type>constexpr auto</type><template>
          <template-nontype-parameter name="B"><type>unsigned</type></template-nontype-parameter>
        </template><parameter name=""><paramtype><classname>bitset</classname>&lt; B &gt;</paramtype></parameter><purpose>Returns true if all option flags in the argument are set and false otherwise. </purpose></method>
</method-group>
</struct><typedef name="none_t"><purpose>All options off. </purpose><type><classname>bitset</classname>&lt; 0 &gt;</type></typedef>
<typedef name="underflow_t"><purpose>Axis has an underflow bin. Mutually exclusive with <computeroutput>circular</computeroutput>. </purpose><type><classname>bit</classname>&lt; 0 &gt;</type></typedef>
<typedef name="overflow_t"><purpose>Axis has overflow bin. </purpose><type><classname>bit</classname>&lt; 1 &gt;</type></typedef>
<typedef name="circular_t"><purpose>Axis is circular. Mutually exclusive with <computeroutput>growth</computeroutput> and <computeroutput>underflow</computeroutput>. </purpose><type><classname>bit</classname>&lt; 2 &gt;</type></typedef>
<typedef name="growth_t"><purpose>Axis can grow. Mutually exclusive with <computeroutput>circular</computeroutput>. </purpose><type><classname>bit</classname>&lt; 3 &gt;</type></typedef>
<data-member name="none"><type>constexpr none_t</type><purpose>Instance of <computeroutput>none_t</computeroutput>. </purpose></data-member>
<data-member name="underflow"><type>constexpr underflow_t</type><purpose>Instance of <computeroutput>underflow_t</computeroutput>. </purpose></data-member>
<data-member name="overflow"><type>constexpr overflow_t</type><purpose>Instance of <computeroutput>overflow_t</computeroutput>. </purpose></data-member>
<data-member name="circular"><type>constexpr circular_t</type><purpose>Instance of <computeroutput>circular_t</computeroutput>. </purpose></data-member>
<data-member name="growth"><type>constexpr growth_t</type><purpose>Instance of <computeroutput>growth_t</computeroutput>. </purpose></data-member>
<function name="operator|"><type>constexpr auto</type><template>
          <template-nontype-parameter name="B1"><type>unsigned</type></template-nontype-parameter>
          <template-nontype-parameter name="B2"><type>unsigned</type></template-nontype-parameter>
        </template><parameter name=""><paramtype><classname>bitset</classname>&lt; B1 &gt;</paramtype></parameter><parameter name=""><paramtype><classname>bitset</classname>&lt; B2 &gt;</paramtype></parameter><purpose>Set union of the axis option arguments. </purpose></function>
<function name="operator&amp;"><type>constexpr auto</type><template>
          <template-nontype-parameter name="B1"><type>unsigned</type></template-nontype-parameter>
          <template-nontype-parameter name="B2"><type>unsigned</type></template-nontype-parameter>
        </template><parameter name=""><paramtype><classname>bitset</classname>&lt; B1 &gt;</paramtype></parameter><parameter name=""><paramtype><classname>bitset</classname>&lt; B2 &gt;</paramtype></parameter><purpose>Set intersection of the option arguments. </purpose></function>
<function name="operator-"><type>constexpr auto</type><template>
          <template-nontype-parameter name="B1"><type>unsigned</type></template-nontype-parameter>
          <template-nontype-parameter name="B2"><type>unsigned</type></template-nontype-parameter>
        </template><parameter name=""><paramtype><classname>bitset</classname>&lt; B1 &gt;</paramtype></parameter><parameter name=""><paramtype><classname>bitset</classname>&lt; B2 &gt;</paramtype></parameter><purpose>Set difference of the option arguments. </purpose></function>
</namespace>



















</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/axis/polymorphic_bin.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="axis">
<class name="polymorphic_bin"><template>
      <template-type-parameter name="RealType"/>
    </template><purpose>Holds the bin data of an <classname alt="boost::histogram::axis::variant">axis::variant</classname>. </purpose><description><para>The interface is a superset of the <classname alt="boost::histogram::axis::interval_view">axis::interval_view</classname> class. In addition, the object is implicitly convertible to the value type, returning the equivalent of a call to lower(). For discrete axes, lower() == upper(), and width() returns zero.</para><para>This is not a view like <classname alt="boost::histogram::axis::interval_view">axis::interval_view</classname> for two reasons.<itemizedlist>
<listitem><para>Sequential calls to lower() and upper() would have to each loop through the variant types. This is likely to be slower than filling all the data in one loop.</para>
</listitem><listitem><para><classname alt="boost::histogram::axis::polymorphic_bin">polymorphic_bin</classname> may be created from a temporary instance of <classname alt="boost::histogram::axis::variant">axis::variant</classname>, like in the call histogram::axis(0). Storing a reference to the axis would result in a dangling reference. Rather than specialing the code to handle this, it seems easier to just use a value instead of a view. </para>
</listitem></itemizedlist>
</para></description><method-group name="public member functions">
<method name="conversion-operator" cv="const noexcept"><type>const value_type &amp;</type><purpose>Implicitly convert to bin value (for axis with discrete values). </purpose></method>
<method name="lower" cv="const noexcept"><type>value_type</type><purpose>Return lower edge of bin. </purpose></method>
<method name="upper" cv="const noexcept"><type>value_type</type><purpose>Return upper edge of bin. </purpose></method>
<method name="center" cv="const noexcept"><type>value_type</type><purpose>Return center of bin. </purpose></method>
<method name="width" cv="const noexcept"><type>value_type</type><purpose>Return width of bin. </purpose></method>
<method name="operator==" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="BinType"/>
        </template><parameter name="rhs"><paramtype>const BinType &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="BinType"/>
        </template><parameter name="rhs"><paramtype>const BinType &amp;</paramtype></parameter></method>
<method name="is_discrete" cv="const noexcept"><type>bool</type><purpose>Return true if bin is discrete. </purpose></method>
</method-group>
<constructor><parameter name="lower"><paramtype>value_type</paramtype></parameter><parameter name="upper"><paramtype>value_type</paramtype></parameter></constructor>
<method-group name="private member functions">
<method name="equal_impl" cv="const noexcept"><type>bool</type><parameter name="rhs"><paramtype>const <classname>polymorphic_bin</classname> &amp;</paramtype></parameter><parameter name=""><paramtype>int</paramtype></parameter></method>
<method name="equal_impl" cv="const noexcept"><type>auto</type><template>
          <template-type-parameter name="BinType"/>
        </template><parameter name="rhs"><paramtype>const BinType &amp;</paramtype></parameter><parameter name=""><paramtype>decltype(rhs.lower(), 0)</paramtype></parameter></method>
<method name="equal_impl" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="BinType"/>
        </template><parameter name="rhs"><paramtype>const BinType &amp;</paramtype></parameter><parameter name=""><paramtype>float</paramtype></parameter></method>
</method-group>
</class>


















</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/axis/regular.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="axis">
<class name="circular"><template>
      <template-type-parameter name="Value"><default>double</default></template-type-parameter>
      <template-type-parameter name="MetaData"><default>use_default</default></template-type-parameter>
      <template-type-parameter name="Options"><default>use_default</default></template-type-parameter>
    </template><purpose>Regular axis with circular option already set. </purpose></class><class name="regular"><template>
      <template-type-parameter name="Value"><purpose><para>input value type, must be floating point. </para></purpose></template-type-parameter>
      <template-type-parameter name="Transform"><purpose><para>builtin or user-defined transform type. </para></purpose></template-type-parameter>
      <template-type-parameter name="MetaData"><purpose><para>type to store meta data. </para></purpose></template-type-parameter>
      <template-type-parameter name="Options"><purpose><para>see boost::histogram::axis::option. </para></purpose></template-type-parameter>
    </template><inherit access="public">boost::histogram::axis::iterator_mixin&lt; regular&lt; Value, Transform, MetaData, Options &gt; &gt;</inherit><inherit access="public">metadata_base_t&lt; MetaData &gt;</inherit><purpose>Axis for equidistant intervals on the real line. </purpose><description><para>The most common binning strategy. Very fast. Binning is a O(1) operation.</para><para>If the axis has an overflow bin (the default), a value on the upper edge of the last bin is put in the overflow bin. The axis range represents a semi-open interval.</para><para>If the overflow bin is deactivated, then a value on the upper edge of the last bin is still counted towards the last bin. The axis range represents a closed interval.</para><para>The options <computeroutput>growth</computeroutput> and <computeroutput>circular</computeroutput> are mutually exclusive.</para><para>
</para></description><method-group name="public member functions">
<method name="transform" cv="const noexcept"><type>const transform_type &amp;</type><purpose>Return instance of the transform type. </purpose></method>
<method name="index" cv="const noexcept"><type>index_type</type><parameter name="x"><paramtype>value_type</paramtype></parameter><purpose>Return index for value argument. </purpose></method>
<method name="update" cv="noexcept"><type>std::pair&lt; index_type, index_type &gt;</type><parameter name="x"><paramtype>value_type</paramtype></parameter><purpose>Returns index and shift (if axis has grown) for the passed argument. </purpose></method>
<method name="value" cv="const noexcept"><type>value_type</type><parameter name="i"><paramtype>real_index_type</paramtype></parameter><purpose>Return value for fractional index argument. </purpose></method>
<method name="bin" cv="const noexcept"><type>decltype(auto)</type><parameter name="idx"><paramtype>index_type</paramtype></parameter><purpose>Return bin for index argument. </purpose></method>
<method name="size" cv="const noexcept"><type>index_type</type><purpose>Returns the number of bins, without over- or underflow. </purpose></method>
<method name="operator==" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="V"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="M"/>
          <template-type-parameter name="O"/>
        </template><parameter name="o"><paramtype>const <classname>regular</classname>&lt; V, T, M, O &gt; &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="V"/>
          <template-type-parameter name="T"/>
          <template-type-parameter name="M"/>
          <template-type-parameter name="O"/>
        </template><parameter name="o"><paramtype>const <classname>regular</classname>&lt; V, T, M, O &gt; &amp;</paramtype></parameter></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor cv="= default"/>
<constructor><parameter name="trans"><paramtype>transform_type</paramtype><description><para>transform instance to use. </para></description></parameter><parameter name="n"><paramtype>unsigned</paramtype><description><para>number of bins. </para></description></parameter><parameter name="start"><paramtype>value_type</paramtype><description><para>low edge of first bin. </para></description></parameter><parameter name="stop"><paramtype>value_type</paramtype><description><para>high edge of last bin. </para></description></parameter><parameter name="meta"><paramtype>metadata_type</paramtype><default>{}</default><description><para>description of the axis (optional). </para></description></parameter><parameter name="options"><paramtype>options_type</paramtype><default>{}</default><description><para>see boost::histogram::axis::option (optional).</para></description></parameter><purpose>Construct n bins over real transformed range [start, stop). </purpose><description><para>
The constructor throws <computeroutput>std::invalid_argument</computeroutput> if n is zero, or if start and stop produce an invalid range after transformation.</para><para>The arguments meta and alloc are passed by value. If you move either of them into the axis and the constructor throws, their values are lost. Do not move if you cannot guarantee that the bin description is not valid. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="n"><paramtype>unsigned</paramtype><description><para>number of bins. </para></description></parameter><parameter name="start"><paramtype>value_type</paramtype><description><para>low edge of first bin. </para></description></parameter><parameter name="stop"><paramtype>value_type</paramtype><description><para>high edge of last bin. </para></description></parameter><parameter name="meta"><paramtype>metadata_type</paramtype><default>{}</default><description><para>description of the axis (optional). </para></description></parameter><parameter name="options"><paramtype>options_type</paramtype><default>{}</default><description><para>see boost::histogram::axis::option (optional). </para></description></parameter><purpose>Construct n bins over real range [start, stop). </purpose><description><para>
</para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="T"/>
        </template><parameter name="trans"><paramtype>transform_type</paramtype><description><para>transform instance to use. </para></description></parameter><parameter name="step"><paramtype>step_type&lt; T &gt;</paramtype><description><para>width of a single bin. </para></description></parameter><parameter name="start"><paramtype>value_type</paramtype><description><para>low edge of first bin. </para></description></parameter><parameter name="stop"><paramtype>value_type</paramtype><description><para>upper limit of high edge of last bin (see below). </para></description></parameter><parameter name="meta"><paramtype>metadata_type</paramtype><default>{}</default><description><para>description of the axis (optional). </para></description></parameter><parameter name="options"><paramtype>options_type</paramtype><default>{}</default><description><para>see boost::histogram::axis::option (optional).</para></description></parameter><purpose>Construct bins with the given step size over real transformed range [start, stop). </purpose><description><para>
The axis computes the number of bins as n = abs(stop - start) / step, rounded down. This means that stop is an upper limit to the actual value (start + n * step). </para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="T"/>
        </template><parameter name="step"><paramtype>step_type&lt; T &gt;</paramtype><description><para>width of a single bin. </para></description></parameter><parameter name="start"><paramtype>value_type</paramtype><description><para>low edge of first bin. </para></description></parameter><parameter name="stop"><paramtype>value_type</paramtype><description><para>upper limit of high edge of last bin (see below). </para></description></parameter><parameter name="meta"><paramtype>metadata_type</paramtype><default>{}</default><description><para>description of the axis (optional). </para></description></parameter><parameter name="options"><paramtype>options_type</paramtype><default>{}</default><description><para>see boost::histogram::axis::option (optional).</para></description></parameter><purpose>Construct bins with the given step size over real range [start, stop). </purpose><description><para>
The axis computes the number of bins as n = abs(stop - start) / step, rounded down. This means that stop is an upper limit to the actual value (start + n * step). </para></description></constructor>
<constructor><parameter name="src"><paramtype>const <classname>regular</classname> &amp;</paramtype></parameter><parameter name="begin"><paramtype>index_type</paramtype></parameter><parameter name="end"><paramtype>index_type</paramtype></parameter><parameter name="merge"><paramtype>unsigned</paramtype></parameter><purpose>Constructor used by algorithm::reduce to shrink and rebin (not for users). </purpose></constructor>
<method-group name="public static functions">
<method name="options" cv="noexcept" specifiers="static"><type>constexpr unsigned</type><purpose>Returns the options. </purpose></method>
</method-group>
</class><namespace name="transform">
<struct name="id"><purpose>Identity transform for equidistant bins. </purpose><method-group name="public static functions">
<method name="forward" cv="noexcept" specifiers="static"><type>T</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T &amp;&amp;</paramtype></parameter><purpose>Pass-through. </purpose></method>
<method name="inverse" cv="noexcept" specifiers="static"><type>T</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T &amp;&amp;</paramtype></parameter><purpose>Pass-through. </purpose></method>
</method-group>
<method-group name="public member functions">
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name=""><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
</struct><struct name="log"><purpose>Log transform for equidistant bins in log-space. </purpose><method-group name="public static functions">
<method name="forward" specifiers="static"><type>T</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T</paramtype></parameter><purpose>Returns log(x) of external value x. </purpose></method>
<method name="inverse" specifiers="static"><type>T</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T</paramtype></parameter><purpose>Returns exp(x) for internal value x. </purpose></method>
</method-group>
<method-group name="public member functions">
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name=""><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
</struct><struct name="pow"><purpose>Pow transform for equidistant bins in pow-space. </purpose><data-member name="power"><type>double</type><purpose>power index </purpose></data-member>
<method-group name="public member functions">
<method name="forward" cv="const"><type>auto</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T</paramtype></parameter><purpose>Returns pow(x, power) of external value x. </purpose></method>
<method name="inverse" cv="const"><type>auto</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T</paramtype></parameter><purpose>Returns pow(x, 1/power) of external value x. </purpose></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="o"><paramtype>const <classname>pow</classname> &amp;</paramtype></parameter></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit"><parameter name="p"><paramtype>double</paramtype></parameter><purpose>Make transform with index p. </purpose></constructor>
<constructor cv="= default"/>
</struct><struct name="sqrt"><purpose>Sqrt transform for equidistant bins in sqrt-space. </purpose><method-group name="public static functions">
<method name="forward" specifiers="static"><type>T</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T</paramtype></parameter><purpose>Returns sqrt(x) of external value x. </purpose></method>
<method name="inverse" specifiers="static"><type>T</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T</paramtype></parameter><purpose>Returns x^2 of internal value x. </purpose></method>
</method-group>
<method-group name="public member functions">
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name=""><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
</struct></namespace>


















<function name="step"><type>step_type&lt; T &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="t"><paramtype>T</paramtype></parameter><purpose>Helper function to mark argument as step size. </purpose></function>
</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/axis/traits.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="axis">
<namespace name="traits">
<struct name="get_options"><template>
      <template-type-parameter name="Axis"><purpose><para>axis type </para></purpose></template-type-parameter>
    </template><purpose>Get axis options for axis type. </purpose><description><para>Doxygen does not render this well. This is a meta-function (template alias), it accepts an axis type and returns the <classname alt="boost::histogram::axis::option::bitset">boost::histogram::axis::option::bitset</classname>.</para><para>If Axis::options() is valid and constexpr, <classname alt="boost::histogram::axis::traits::get_options">get_options</classname> is the corresponding option type. Otherwise, it is boost::histogram::axis::option::growth_t, if the axis has a method <computeroutput>update</computeroutput>, else boost::histogram::axis::option::none_t.</para><para>
</para></description></struct><struct name="is_continuous"><template>
      <template-type-parameter name="Axis"/>
    </template><purpose>Whether axis is continuous or discrete. </purpose><description><para>Doxygen does not render this well. This is a meta-function (template alias), it accepts an axis type and returns a compile-time boolean.</para><para>If the boolean is true, the axis is continuous (covers a continuous range of values). Otherwise it is discrete (covers discrete values). </para></description></struct><struct name="is_inclusive"><template>
      <template-type-parameter name="Axis"><purpose><para>axis type </para></purpose></template-type-parameter>
    </template><purpose>Meta-function to detect whether an axis is inclusive. </purpose><description><para>Doxygen does not render this well. This is a meta-function (template alias), it accepts an axis type and represents compile-time boolean which is true or false, depending on whether the axis is inclusive or not.</para><para>An inclusive axis has a bin for every possible input value. In other words, all possible input values always end up in a valid cell and there is no need to keep track of input tuples that need to be discarded. A histogram which consists entirely of inclusive axes can be filled more efficiently, which can be a factor 2 faster.</para><para>An axis with underflow and overflow bins is always inclusive, but an axis may be inclusive under other conditions. The meta-function checks for the method <computeroutput>constexpr static bool inclusive()</computeroutput>, and uses the result. If this method is not present, it uses get_options&lt;Axis&gt; and checks whether the underflow and overflow bits are present.</para><para>
</para></description></struct><struct name="is_ordered"><template>
      <template-type-parameter name="Axis"><purpose><para>axis type </para></purpose></template-type-parameter>
    </template><purpose>Meta-function to detect whether an axis is ordered. </purpose><description><para>Doxygen does not render this well. This is a meta-function (template alias), it accepts an axis type and returns a compile-time boolean. If the boolean is true, the axis is ordered.</para><para>The meta-function checks for the method <computeroutput>constexpr static bool ordered()</computeroutput>, and uses the result. If this method is not present, it returns true if the value type of the Axis is arithmetic and false otherwise.</para><para>An ordered axis has a value type that is ordered, which means that indices i &lt; j &lt; k implies either value(i) &lt; value(j) &lt; value(k) or value(i) &gt; value(j) &gt; value(k) for all i,j,k. For example, the integer axis is ordered, but the category axis is not. Axis which are not ordered must not have underflow bins, because they only have an "other" category, which is identified with the overflow bin if it is available.</para><para>
</para></description></struct><struct name="is_reducible"><template>
      <template-type-parameter name="Axis"><purpose><para>axis type. </para></purpose></template-type-parameter>
    </template><purpose>Meta-function to detect whether an axis is reducible. </purpose><description><para>Doxygen does not render this well. This is a meta-function (template alias), it accepts an axis type and represents compile-time boolean which is true or false, depending on whether the axis can be reduced with boost::histogram::algorithm::reduce().</para><para>An axis can be made reducible by adding a special constructor, see Axis concept for details.</para><para>
</para></description></struct><struct name="value_type"><template>
      <template-type-parameter name="Axis"><purpose><para>axis type. </para></purpose></template-type-parameter>
    </template><purpose>Value type for axis type. </purpose><description><para>Doxygen does not render this well. This is a meta-function (template alias), it accepts an axis type and returns the value type.</para><para>The value type is deduced from the argument of the <computeroutput>Axis::index</computeroutput> method. Const references are decayed to the their value types, for example, the type deduced for <computeroutput>Axis::index(const int&amp;)</computeroutput> is <computeroutput>int</computeroutput>.</para><para>The deduction always succeeds if the axis type models the Axis concept correctly. Errors come from violations of the concept, in particular, an index method that is templated or overloaded is not allowed.</para><para>
</para></description></struct><function name="options"><type>constexpr unsigned</type><template>
          <template-type-parameter name="Axis"/>
        </template><parameter name="axis"><paramtype>const Axis &amp;</paramtype><description><para>any axis instance </para></description></parameter><purpose>Returns axis options as unsigned integer. </purpose><description><para>See <classname alt="boost::histogram::axis::traits::get_options">get_options</classname> for details.</para><para>
</para></description></function>
<function name="options"><type>unsigned</type><template>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
        </template><parameter name="axis"><paramtype>const <classname>variant</classname>&lt; Ts... &gt; &amp;</paramtype></parameter></function>
<function name="inclusive"><type>constexpr bool</type><template>
          <template-type-parameter name="Axis"/>
        </template><parameter name="axis"><paramtype>const Axis &amp;</paramtype><description><para>any axis instance </para></description></parameter><purpose>Returns true if axis is inclusive or false. </purpose><description><para>See <classname alt="boost::histogram::axis::traits::is_inclusive">is_inclusive</classname> for details.</para><para>
</para></description></function>
<function name="inclusive"><type>bool</type><template>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
        </template><parameter name="axis"><paramtype>const <classname>variant</classname>&lt; Ts... &gt; &amp;</paramtype></parameter></function>
<function name="ordered"><type>constexpr bool</type><template>
          <template-type-parameter name="Axis"/>
        </template><parameter name="axis"><paramtype>const Axis &amp;</paramtype><description><para>any axis instance </para></description></parameter><purpose>Returns true if axis is ordered or false. </purpose><description><para>See <classname alt="boost::histogram::axis::traits::is_ordered">is_ordered</classname> for details.</para><para>
</para></description></function>
<function name="ordered"><type>bool</type><template>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
        </template><parameter name="axis"><paramtype>const <classname>variant</classname>&lt; Ts... &gt; &amp;</paramtype></parameter></function>
<function name="continuous"><type>constexpr bool</type><template>
          <template-type-parameter name="Axis"/>
        </template><parameter name="axis"><paramtype>const Axis &amp;</paramtype><description><para>any axis instance </para></description></parameter><purpose>Returns true if axis is continuous or false. </purpose><description><para>See <classname alt="boost::histogram::axis::traits::is_continuous">is_continuous</classname> for details.</para><para>
</para></description></function>
<function name="continuous"><type>bool</type><template>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
        </template><parameter name="axis"><paramtype>const <classname>variant</classname>&lt; Ts... &gt; &amp;</paramtype></parameter></function>
<function name="extent"><type>index_type</type><template>
          <template-type-parameter name="Axis"/>
        </template><parameter name="axis"><paramtype>const Axis &amp;</paramtype><description><para>any axis instance </para></description></parameter><purpose>Returns axis size plus any extra bins for under- and overflow. </purpose><description><para>
</para></description></function>
<function name="metadata"><type>decltype(auto)</type><template>
          <template-type-parameter name="Axis"/>
        </template><parameter name="axis"><paramtype>Axis &amp;&amp;</paramtype><description><para>any axis instance </para></description></parameter><purpose>Returns reference to metadata of an axis. </purpose><description><para>If the expression x.metadata() for an axis instance <computeroutput>x</computeroutput> (maybe const) is valid, return the result. Otherwise, return a reference to a static instance of <classname alt="boost::histogram::axis::null_type">boost::histogram::axis::null_type</classname>.</para><para>
</para></description></function>
<function name="value"><type>decltype(auto)</type><template>
          <template-type-parameter name="Axis"/>
        </template><parameter name="axis"><paramtype>const Axis &amp;</paramtype><description><para>any axis instance </para></description></parameter><parameter name="index"><paramtype>real_index_type</paramtype><description><para>floating point axis index </para></description></parameter><purpose>Returns axis value for index. </purpose><description><para>If the axis has no <computeroutput>value</computeroutput> method, throw std::runtime_error. If the method exists and accepts a floating point index, pass the index and return the result. If the method exists but accepts only integer indices, cast the floating point index to int, pass this index and return the result.</para><para>
</para></description></function>
<function name="value_as"><type>Result</type><template>
          <template-type-parameter name="Result"><purpose><para>requested return type </para></purpose></template-type-parameter>
          <template-type-parameter name="Axis"><purpose><para>axis type </para></purpose></template-type-parameter>
        </template><parameter name="axis"><paramtype>const Axis &amp;</paramtype><description><para>any axis instance </para></description></parameter><parameter name="index"><paramtype>real_index_type</paramtype><description><para>floating point axis index </para></description></parameter><purpose>Returns axis value for index if it is convertible to target type or throws. </purpose><description><para>Like boost::histogram::axis::traits::value, but converts the result into the requested return type. If the conversion is not possible, throws std::runtime_error.</para><para>

</para></description></function>
<function name="index"><type>axis::index_type</type><template>
          <template-type-parameter name="Axis"/>
          <template-type-parameter name="U"/>
        </template><parameter name="axis"><paramtype>const Axis &amp;</paramtype><description><para>any axis instance </para></description></parameter><parameter name="value"><paramtype>const U &amp;</paramtype><description><para>argument to be passed to <computeroutput>index</computeroutput> method </para></description></parameter><purpose>Returns axis index for value. </purpose><description><para>Throws std::invalid_argument if the value argument is not implicitly convertible.</para><para>
</para></description></function>
<function name="index"><type>axis::index_type</type><template>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
          <template-type-parameter name="U"/>
        </template><parameter name="axis"><paramtype>const <classname>variant</classname>&lt; Ts... &gt; &amp;</paramtype></parameter><parameter name="value"><paramtype>const U &amp;</paramtype></parameter></function>
<function name="rank"><type>constexpr unsigned int</type><template>
          <template-type-parameter name="Axis"/>
        </template><parameter name="axis"><paramtype>const Axis &amp;</paramtype><description><para>any axis instance </para></description></parameter><purpose>Return axis rank (how many arguments it processes). </purpose><description><para>
</para></description></function>
<function name="rank"><type>unsigned int</type><template>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
        </template><parameter name="axis"><paramtype>const <classname>axis::variant</classname>&lt; Ts... &gt; &amp;</paramtype></parameter></function>
<function name="update"><type>std::pair&lt; index_type, index_type &gt;</type><template>
          <template-type-parameter name="Axis"/>
          <template-type-parameter name="U"/>
        </template><parameter name="axis"><paramtype>Axis &amp;</paramtype><description><para>any axis instance </para></description></parameter><parameter name="value"><paramtype>const U &amp;</paramtype><description><para>argument to be passed to <computeroutput>update</computeroutput> or <computeroutput>index</computeroutput> method </para></description></parameter><purpose>Returns pair of axis index and shift for the value argument. </purpose><description><para>Throws <computeroutput>std::invalid_argument</computeroutput> if the value argument is not implicitly convertible to the argument expected by the <computeroutput>index</computeroutput> method. If the result of boost::histogram::axis::traits::get_options&lt;decltype(axis)&gt; has the growth flag set, call <computeroutput>update</computeroutput> method with the argument and return the result. Otherwise, call <computeroutput>index</computeroutput> and return the pair of the result and a zero shift.</para><para>
</para></description></function>
<function name="update"><type>std::pair&lt; index_type, index_type &gt;</type><template>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
          <template-type-parameter name="U"/>
        </template><parameter name="axis"><paramtype><classname>variant</classname>&lt; Ts... &gt; &amp;</paramtype></parameter><parameter name="value"><paramtype>const U &amp;</paramtype></parameter></function>
<function name="width"><type>decltype(auto)</type><template>
          <template-type-parameter name="Axis"/>
        </template><parameter name="axis"><paramtype>const Axis &amp;</paramtype><description><para>any axis instance </para></description></parameter><parameter name="index"><paramtype>index_type</paramtype><description><para>bin index </para></description></parameter><purpose>Returns bin width at axis index. </purpose><description><para>If the axis has no <computeroutput>value</computeroutput> method, throw std::runtime_error. If the method exists and accepts a floating point index, return the result of <computeroutput>axis.value(index + 1) - axis.value(index)</computeroutput>. If the method exists but accepts only integer indices, return 0.</para><para>
</para></description></function>
<function name="width_as"><type>Result</type><template>
          <template-type-parameter name="Result"/>
          <template-type-parameter name="Axis"/>
        </template><parameter name="axis"><paramtype>const Axis &amp;</paramtype><description><para>any axis instance </para></description></parameter><parameter name="index"><paramtype>index_type</paramtype><description><para>bin index </para></description></parameter><purpose>Returns bin width at axis index. </purpose><description><para>Like boost::histogram::axis::traits::width, but converts the result into the requested return type. If the conversion is not possible, throw std::runtime_error.</para><para>
</para></description></function>
</namespace>



















</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/axis/variable.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="axis">
<class name="variable"><template>
      <template-type-parameter name="Value"><purpose><para>input value type, must be floating point. </para></purpose></template-type-parameter>
      <template-type-parameter name="MetaData"><purpose><para>type to store meta data. </para></purpose></template-type-parameter>
      <template-type-parameter name="Options"><purpose><para>see boost::histogram::axis::option. </para></purpose></template-type-parameter>
      <template-type-parameter name="Allocator"><purpose><para>allocator to use for dynamic memory management. </para></purpose></template-type-parameter>
    </template><inherit access="public">boost::histogram::axis::iterator_mixin&lt; variable&lt; Value, MetaData, Options, Allocator &gt; &gt;</inherit><inherit access="public">metadata_base_t&lt; MetaData &gt;</inherit><purpose>Axis for non-equidistant bins on the real line. </purpose><description><para>Binning is a O(log(N)) operation. If speed matters and the problem domain allows it, prefer a regular axis, possibly with a transform.</para><para>If the axis has an overflow bin (the default), a value on the upper edge of the last bin is put in the overflow bin. The axis range represents a semi-open interval.</para><para>If the overflow bin is deactivated, then a value on the upper edge of the last bin is still counted towards the last bin. The axis range represents a closed interval. This is the desired behavior for random numbers drawn from a bounded interval, which is usually closed.</para><para>
</para></description><method-group name="public member functions">
<method name="index" cv="const noexcept"><type>index_type</type><parameter name="x"><paramtype>value_type</paramtype></parameter><purpose>Return index for value argument. </purpose></method>
<method name="update" cv="noexcept"><type>std::pair&lt; index_type, index_type &gt;</type><parameter name="x"><paramtype>value_type</paramtype></parameter></method>
<method name="value" cv="const noexcept"><type>value_type</type><parameter name="i"><paramtype>real_index_type</paramtype></parameter><purpose>Return value for fractional index argument. </purpose></method>
<method name="bin" cv="const noexcept"><type>auto</type><parameter name="idx"><paramtype>index_type</paramtype></parameter><purpose>Return bin for index argument. </purpose></method>
<method name="size" cv="const noexcept"><type>index_type</type><purpose>Returns the number of bins, without over- or underflow. </purpose></method>
<method name="operator==" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="V"/>
          <template-type-parameter name="M"/>
          <template-type-parameter name="O"/>
          <template-type-parameter name="A"/>
        </template><parameter name="o"><paramtype>const <classname>variable</classname>&lt; V, M, O, A &gt; &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="V"/>
          <template-type-parameter name="M"/>
          <template-type-parameter name="O"/>
          <template-type-parameter name="A"/>
        </template><parameter name="o"><paramtype>const <classname>variable</classname>&lt; V, M, O, A &gt; &amp;</paramtype></parameter></method>
<method name="get_allocator" cv="const"><type>auto</type><purpose>Return allocator instance. </purpose></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor cv="= default"/>
<constructor specifiers="explicit"><parameter name="alloc"><paramtype>allocator_type</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
          <template-type-parameter name=""><default>detail::requires_iterator&lt;It&gt;</default></template-type-parameter>
        </template><parameter name="begin"><paramtype>It</paramtype><description><para>begin of edge sequence. </para></description></parameter><parameter name="end"><paramtype>It</paramtype><description><para>end of edge sequence. </para></description></parameter><parameter name="meta"><paramtype>metadata_type</paramtype><default>{}</default><description><para>description of the axis (optional). </para></description></parameter><parameter name="options"><paramtype>options_type</paramtype><default>{}</default><description><para>see boost::histogram::axis::option (optional). </para></description></parameter><parameter name="alloc"><paramtype>allocator_type</paramtype><default>{}</default><description><para>allocator instance to use (optional).</para></description></parameter><purpose>Construct from forward iterator range of bin edges. </purpose><description><para>
The constructor throws <computeroutput>std::invalid_argument</computeroutput> if iterator range is invalid, if less than two edges are provided or if bin edges are not in ascending order.</para><para>The arguments meta and alloc are passed by value. If you move either of them into the axis and the constructor throws, their values are lost. Do not move if you cannot guarantee that the bin description is not valid. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="It"/>
          <template-type-parameter name="A"/>
          <template-type-parameter name=""><default>detail::requires_iterator&lt;It&gt;</default></template-type-parameter>
          <template-type-parameter name=""><default>detail::requires_allocator&lt;A&gt;</default></template-type-parameter>
        </template><parameter name="begin"><paramtype>It</paramtype></parameter><parameter name="end"><paramtype>It</paramtype></parameter><parameter name="meta"><paramtype>metadata_type</paramtype></parameter><parameter name="alloc"><paramtype>A</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="U"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;U&gt;</default></template-type-parameter>
        </template><parameter name="iterable"><paramtype>const U &amp;</paramtype><description><para>iterable range of bin edges. </para></description></parameter><parameter name="meta"><paramtype>metadata_type</paramtype><default>{}</default><description><para>description of the axis (optional). </para></description></parameter><parameter name="options"><paramtype>options_type</paramtype><default>{}</default><description><para>see boost::histogram::axis::option (optional). </para></description></parameter><parameter name="alloc"><paramtype>allocator_type</paramtype><default>{}</default><description><para>allocator instance to use (optional). </para></description></parameter><purpose>Construct variable axis from iterable range of bin edges. </purpose><description><para>
</para></description></constructor>
<constructor><template>
          <template-type-parameter name="U"/>
          <template-type-parameter name="A"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;U&gt;</default></template-type-parameter>
          <template-type-parameter name=""><default>detail::requires_allocator&lt;A&gt;</default></template-type-parameter>
        </template><parameter name="iterable"><paramtype>const U &amp;</paramtype></parameter><parameter name="meta"><paramtype>metadata_type</paramtype></parameter><parameter name="alloc"><paramtype>A</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="U"/>
        </template><parameter name="list"><paramtype>std::initializer_list&lt; U &gt;</paramtype><description><para><computeroutput>std::initializer_list</computeroutput> of bin edges. </para></description></parameter><parameter name="meta"><paramtype>metadata_type</paramtype><default>{}</default><description><para>description of the axis (optional). </para></description></parameter><parameter name="options"><paramtype>options_type</paramtype><default>{}</default><description><para>see boost::histogram::axis::option (optional). </para></description></parameter><parameter name="alloc"><paramtype>allocator_type</paramtype><default>{}</default><description><para>allocator instance to use (optional). </para></description></parameter><purpose>Construct variable axis from initializer list of bin edges. </purpose><description><para>
</para></description></constructor>
<constructor><template>
          <template-type-parameter name="U"/>
          <template-type-parameter name="A"/>
          <template-type-parameter name=""><default>detail::requires_allocator&lt;A&gt;</default></template-type-parameter>
        </template><parameter name="list"><paramtype>std::initializer_list&lt; U &gt;</paramtype></parameter><parameter name="meta"><paramtype>metadata_type</paramtype></parameter><parameter name="alloc"><paramtype>A</paramtype></parameter></constructor>
<constructor><parameter name="src"><paramtype>const <classname>variable</classname> &amp;</paramtype></parameter><parameter name="begin"><paramtype>index_type</paramtype></parameter><parameter name="end"><paramtype>index_type</paramtype></parameter><parameter name="merge"><paramtype>unsigned</paramtype></parameter><purpose>Constructor used by algorithm::reduce to shrink and rebin (not for users). </purpose></constructor>
<method-group name="public static functions">
<method name="options" cv="noexcept" specifiers="static"><type>constexpr unsigned</type><purpose>Returns the options. </purpose></method>
</method-group>
</class>


















</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/axis/variant.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="axis">
<class name="variant"><template>
      <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
    </template><inherit access="public">boost::histogram::axis::iterator_mixin&lt; variant&lt; Ts... &gt; &gt;</inherit><purpose>Polymorphic axis type. </purpose><method-group name="public member functions">
<method name="size" cv="const"><type>index_type</type><purpose>Return size of axis. </purpose></method>
<method name="options" cv="const"><type>unsigned</type><purpose>Return options of axis or option::none_t if axis has no options. </purpose></method>
<method name="inclusive" cv="const"><type>bool</type><purpose>Returns true if the axis is inclusive or false. </purpose></method>
<method name="ordered" cv="const"><type>bool</type><purpose>Returns true if the axis is ordered or false. </purpose></method>
<method name="continuous" cv="const"><type>bool</type><purpose>Returns true if the axis is continuous or false. </purpose></method>
<method name="metadata" cv="const"><type>metadata_type &amp;</type><purpose>Return reference to const metadata or instance of <classname alt="boost::histogram::axis::null_type">null_type</classname> if axis has no metadata. </purpose></method>
<method name="metadata"><type>metadata_type &amp;</type><purpose>Return reference to metadata or instance of <classname alt="boost::histogram::axis::null_type">null_type</classname> if axis has no metadata. </purpose></method>
<method name="index" cv="const"><type>index_type</type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="u"><paramtype>const U &amp;</paramtype></parameter><purpose>Return index for value argument. </purpose><description><para>Throws std::invalid_argument if axis has incompatible call signature. </para></description></method>
<method name="value" cv="const"><type>double</type><parameter name="idx"><paramtype>real_index_type</paramtype></parameter><purpose>Return value for index argument. </purpose><description><para>Only works for axes with value method that returns something convertible to double and will throw a runtime_error otherwise, see axis::traits::value(). </para></description></method>
<method name="bin" cv="const"><type>auto</type><parameter name="idx"><paramtype>index_type</paramtype></parameter><purpose>Return bin for index argument. </purpose><description><para>Only works for axes with value method that returns something convertible to double and will throw a runtime_error otherwise, see axis::traits::value(). </para></description></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor cv="= default"/>
<constructor cv="= default"><parameter name=""><paramtype>const <classname>variant</classname> &amp;</paramtype></parameter></constructor>
<copy-assignment cv="= default"><type><classname>variant</classname> &amp;</type><parameter name=""><paramtype>const <classname>variant</classname> &amp;</paramtype></parameter></copy-assignment>
<constructor cv="= default"><parameter name=""><paramtype><classname>variant</classname> &amp;&amp;</paramtype></parameter></constructor>
<copy-assignment cv="= default"><type><classname>variant</classname> &amp;</type><parameter name=""><paramtype><classname>variant</classname> &amp;&amp;</paramtype></parameter></copy-assignment>
<constructor><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name=""><default>requires_bounded_type&lt;T&gt;</default></template-type-parameter>
        </template><parameter name="t"><paramtype>T &amp;&amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>variant</classname> &amp;</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name=""><default>requires_bounded_type&lt;T&gt;</default></template-type-parameter>
        </template><parameter name="t"><paramtype>T &amp;&amp;</paramtype></parameter></copy-assignment>
<constructor><template>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
        </template><parameter name="u"><paramtype>const <classname>variant</classname>&lt; Us... &gt; &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>variant</classname> &amp;</type><template>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
        </template><parameter name="u"><paramtype>const <classname>variant</classname>&lt; Us... &gt; &amp;</paramtype></parameter></copy-assignment>
</class><class name="variant"><template>
    </template></class><function name="visit"><type>decltype(auto)</type><template>
          <template-type-parameter name="Visitor"/>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
        </template><parameter name="vis"><paramtype>Visitor &amp;&amp;</paramtype></parameter><parameter name="var"><paramtype><classname>variant</classname>&lt; Us... &gt; &amp;</paramtype></parameter><purpose>Apply visitor to variant (reference). </purpose></function>
<function name="visit"><type>decltype(auto)</type><template>
          <template-type-parameter name="Visitor"/>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
        </template><parameter name="vis"><paramtype>Visitor &amp;&amp;</paramtype></parameter><parameter name="var"><paramtype><classname>variant</classname>&lt; Us... &gt; &amp;&amp;</paramtype></parameter><purpose>Apply visitor to variant (movable reference). </purpose></function>
<function name="visit"><type>decltype(auto)</type><template>
          <template-type-parameter name="Visitor"/>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
        </template><parameter name="vis"><paramtype>Visitor &amp;&amp;</paramtype></parameter><parameter name="var"><paramtype>const <classname>variant</classname>&lt; Us... &gt; &amp;</paramtype></parameter><purpose>Apply visitor to variant (const reference). </purpose></function>
<function name="get_if"><type>auto</type><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
        </template><parameter name="v"><paramtype><classname>variant</classname>&lt; Us... &gt; *</paramtype></parameter><purpose>Returns pointer to T in variant or null pointer if type does not match. </purpose></function>
<function name="get_if"><type>auto</type><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
        </template><parameter name="v"><paramtype>const <classname>variant</classname>&lt; Us... &gt; *</paramtype></parameter><purpose>Returns pointer to const T in variant or null pointer if type does not match. </purpose></function>
<function name="get"><type>decltype(auto)</type><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
        </template><parameter name="v"><paramtype><classname>variant</classname>&lt; Us... &gt; &amp;</paramtype></parameter><purpose>Return reference to T, throws std::runtime_error if type does not match. </purpose></function>
<function name="get"><type>decltype(auto)</type><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
        </template><parameter name="v"><paramtype><classname>variant</classname>&lt; Us... &gt; &amp;&amp;</paramtype></parameter><purpose>Return movable reference to T, throws unspecified exception if type does not match. </purpose></function>
<function name="get"><type>decltype(auto)</type><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
        </template><parameter name="v"><paramtype>const <classname>variant</classname>&lt; Us... &gt; &amp;</paramtype></parameter><purpose>Return const reference to T, throws unspecified exception if type does not match. </purpose></function>
<function name="visit"><type>decltype(auto)</type><template>
          <template-type-parameter name="Visitor"/>
          <template-type-parameter name="T"/>
        </template><parameter name="vis"><paramtype>Visitor &amp;&amp;</paramtype></parameter><parameter name="var"><paramtype>T &amp;&amp;</paramtype></parameter></function>
<function name="get"><type>decltype(auto)</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="u"><paramtype>U &amp;&amp;</paramtype></parameter></function>
<function name="get_if"><type>auto</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="u"><paramtype>U *</paramtype></parameter></function>
<function name="get_if"><type>auto</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="u"><paramtype>const U *</paramtype></parameter></function>
<function name="operator=="><type>bool</type><template>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
          <template-nontype-parameter name="Vs"><type>class...</type></template-nontype-parameter>
        </template><parameter name="u"><paramtype>const <classname>variant</classname>&lt; Us... &gt; &amp;</paramtype></parameter><parameter name="v"><paramtype>const <classname>variant</classname>&lt; Vs... &gt; &amp;</paramtype></parameter><purpose>Compare two variants. </purpose><description><para>Return true if the variants point to the same concrete axis type and the types compare equal. Otherwise return false. </para></description></function>
<function name="operator=="><type>bool</type><template>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
          <template-type-parameter name="T"/>
        </template><parameter name="u"><paramtype>const <classname>variant</classname>&lt; Us... &gt; &amp;</paramtype></parameter><parameter name="t"><paramtype>const T &amp;</paramtype></parameter><purpose>Compare variant with a concrete axis type. </purpose><description><para>Return true if the variant point to the same concrete axis type and the types compare equal. Otherwise return false. </para></description></function>
<function name="operator=="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
        </template><parameter name="t"><paramtype>const T &amp;</paramtype></parameter><parameter name="u"><paramtype>const <classname>variant</classname>&lt; Us... &gt; &amp;</paramtype></parameter></function>
<function name="operator!="><type>bool</type><template>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
        </template><parameter name="u"><paramtype>const <classname>variant</classname>&lt; Us... &gt; &amp;</paramtype></parameter><parameter name="t"><paramtype>const <classname>variant</classname>&lt; Ts... &gt; &amp;</paramtype></parameter><purpose>The negation of operator==. </purpose></function>
<function name="operator!="><type>bool</type><template>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
          <template-type-parameter name="T"/>
        </template><parameter name="u"><paramtype>const <classname>variant</classname>&lt; Us... &gt; &amp;</paramtype></parameter><parameter name="t"><paramtype>const T &amp;</paramtype></parameter><purpose>The negation of operator==. </purpose></function>
<function name="operator!="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="Us"><type>class...</type></template-nontype-parameter>
        </template><parameter name="t"><paramtype>const T &amp;</paramtype></parameter><parameter name="u"><paramtype>const <classname>variant</classname>&lt; Us... &gt; &amp;</paramtype></parameter><purpose>The negation of operator==. </purpose></function>

</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/fwd.hpp">
<para>Forward declarations, tag types and type aliases. </para><namespace name="boost">
<namespace name="histogram">
<namespace name="axis">
<struct name="null_type"><purpose>Empty metadata type. </purpose><method-group name="public member functions">
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name=""><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
</struct><typedef name="index_type"><purpose>Integral type for axis indices. </purpose><type>int</type></typedef>
<typedef name="real_index_type"><purpose>Real type for axis indices. </purpose><type>double</type></typedef>
<typedef name="empty_type"><purpose>Another alias for an empty metadata type. </purpose><type><classname>null_type</classname></type></typedef>



















</namespace>
<namespace name="utility">
<class name="clopper_pearson_interval"><template>
      <template-type-parameter name="ValueType"/>
    </template><inherit access="public">boost::histogram::utility::binomial_proportion_interval&lt; ValueType &gt;</inherit><purpose>Clopper-Pearson interval. </purpose><description><para>This is the classic frequentist interval obtained with the Neyman construction. It is therefore often called the 'exact' interval. It is guaranteed to have at least the requested confidence level for all values of the fraction.</para><para>The interval is wider than others that produce coverage closer to the expected confidence level over a random ensemble of factions. The Clopper-Pearson interval essentially always overcovers for such a random ensemble, which is undesirable in practice. The Clopper-Pearson interval is recommended when it is important to be conservative, but the Wilson interval should be preferred in most applications.</para><para>C. Clopper, E.S. Pearson (1934), Biometrika 26 (4): 404-413. doi:10.1093/biomet/26.4.404. </para></description><method-group name="public member functions">
<method name="operator()" cv="const noexcept"><type>interval_type</type><parameter name="successes"><paramtype>value_type</paramtype><description><para>Number of successful trials. </para></description></parameter><parameter name="failures"><paramtype>value_type</paramtype><description><para>Number of failed trials. </para></description></parameter><purpose>Compute interval for given number of successes and failures. </purpose><description><para>
</para></description></method>
</method-group>
<constructor specifiers="explicit" cv="noexcept"><parameter name="cl"><paramtype><classname>confidence_level</classname></paramtype><default><classname alt="boost::histogram::utility::deviation">deviation</classname>{1}</default><description><para>Confidence level for the interval. The default value produces a confidence level of 68 % equivalent to one standard deviation. Both <computeroutput>deviation</computeroutput> and <computeroutput><classname alt="boost::histogram::utility::confidence_level">confidence_level</classname></computeroutput> objects can be used to initialize the interval. </para></description></parameter><purpose>Construct Clopper-Pearson interval computer. </purpose><description><para>
</para></description></constructor>
</class><class name="jeffreys_interval"><template>
      <template-type-parameter name="ValueType"/>
    </template><inherit access="public">boost::histogram::utility::binomial_proportion_interval&lt; ValueType &gt;</inherit><purpose>Jeffreys interval. </purpose><description><para>This is the Bayesian credible interval with a Jeffreys prior. Although it has a Bayesian derivation, it has good coverage. The interval boundaries are close to the Wilson interval. A special property of this interval is that it is equal-tailed; the probability of the true value to be above or below the interval is approximately equal.</para><para>To avoid coverage probability tending to zero when the fraction approaches 0 or 1, this implementation uses a modification described in section 4.1.2 of the paper by L.D. Brown, T.T. Cai, A. DasGupta, Statistical Science 16 (2001) 101-133, doi:10.1214/ss/1009213286. </para></description><method-group name="public member functions">
<method name="operator()" cv="const noexcept"><type>interval_type</type><parameter name="successes"><paramtype>value_type</paramtype><description><para>Number of successful trials. </para></description></parameter><parameter name="failures"><paramtype>value_type</paramtype><description><para>Number of failed trials. </para></description></parameter><purpose>Compute interval for given number of successes and failures. </purpose><description><para>
</para></description></method>
</method-group>
<constructor specifiers="explicit" cv="noexcept"><parameter name="cl"><paramtype><classname>confidence_level</classname></paramtype><default><classname alt="boost::histogram::utility::deviation">deviation</classname>{1}</default><description><para>Confidence level for the interval. The default value produces a confidence level of 68 % equivalent to one standard deviation. Both <computeroutput>deviation</computeroutput> and <computeroutput><classname alt="boost::histogram::utility::confidence_level">confidence_level</classname></computeroutput> objects can be used to initialize the interval. </para></description></parameter><purpose>Construct Jeffreys interval computer. </purpose><description><para>
</para></description></constructor>
</class><class name="wald_interval"><template>
      <template-type-parameter name="ValueType"/>
    </template><inherit access="public">boost::histogram::utility::binomial_proportion_interval&lt; ValueType &gt;</inherit><purpose>Wald interval or normal approximation interval. </purpose><description><para>The Wald interval is a symmetric interval. It is simple to compute, but has poor statistical properties and is universally rejected by statisticians. It should always be replaced by another iternal, for example, the Wilson interval.</para><para>The Wald interval can be derived easily using the plug-in estimate of the variance for the binomial distribution, which is likely a reason for its omnipresence. Without further insight into statistical theory, it is not obvious that this derivation is flawed and that better alternatives exist.</para><para>The Wald interval undercovers on average. It is unsuitable when the sample size is small or when the fraction is close to 0 or 1. e. Its limits are not naturally bounded by 0 or 1. It produces empty intervals if the number of successes or failures is zero.</para><para>For a critique of the Wald interval, see (a selection):</para><para>L.D. Brown, T.T. Cai, A. DasGupta, Statistical Science 16 (2001) 101-133. R. D. Cousins, K. E. Hymes, J. Tucker, Nucl. Instrum. Meth. A 612 (2010) 388-398. </para></description><method-group name="public member functions">
<method name="operator()" cv="const noexcept"><type>interval_type</type><parameter name="successes"><paramtype>value_type</paramtype><description><para>Number of successful trials. </para></description></parameter><parameter name="failures"><paramtype>value_type</paramtype><description><para>Number of failed trials. </para></description></parameter><purpose>Compute interval for given number of successes and failures. </purpose><description><para>
</para></description></method>
</method-group>
<constructor specifiers="explicit" cv="noexcept"><parameter name="d"><paramtype><classname>deviation</classname></paramtype><default><classname alt="boost::histogram::utility::deviation">deviation</classname>{1.0}</default><description><para>Number of standard deviations for the interval. The default value 1 corresponds to a confidence level of 68 %. Both <computeroutput>deviation</computeroutput> and <computeroutput><classname alt="boost::histogram::utility::confidence_level">confidence_level</classname></computeroutput> objects can be used to initialize the interval. </para></description></parameter><purpose>Construct Wald interval computer. </purpose><description><para>
</para></description></constructor>
</class><class name="wilson_interval"><template>
      <template-type-parameter name="ValueType"/>
    </template><inherit access="public">boost::histogram::utility::binomial_proportion_interval&lt; ValueType &gt;</inherit><purpose>Wilson interval. </purpose><description><para>The Wilson score interval is simple to compute, has good coverage. Intervals are automatically bounded between 0 and 1 and never empty. The interval is asymmetric.</para><para>Wilson, E. B. (1927). "Probable inference, the law of succession, and statistical
inference". Journal of the American Statistical Association. 22 (158): 209-212. doi:10.1080/01621459.1927.10502953. JSTOR 2276774.</para><para>The coverage probability for a random ensemble of fractions is close to the nominal value. Unlike the Clopper-Pearson interval, the Wilson score interval is not conservative. For some values of the fractions, the interval undercovers and overcovers for neighboring values. This is a shared property of all alternatives to the Clopper-Pearson interval.</para><para>The Wilson score intervals is widely recommended for general use in the literature. For a review of the literature, see R. D. Cousins, K. E. Hymes, J. Tucker, Nucl. Instrum. Meth. A 612 (2010) 388-398. </para></description><method-group name="public member functions">
<method name="operator()" cv="const noexcept"><type>interval_type</type><parameter name="successes"><paramtype>value_type</paramtype><description><para>Number of successful trials. </para></description></parameter><parameter name="failures"><paramtype>value_type</paramtype><description><para>Number of failed trials. </para></description></parameter><purpose>Compute interval for given number of successes and failures. </purpose><description><para>
</para></description></method>
</method-group>
<constructor specifiers="explicit" cv="noexcept"><parameter name="d"><paramtype><classname>deviation</classname></paramtype><default><classname alt="boost::histogram::utility::deviation">deviation</classname>{1.0}</default><description><para>Number of standard deviations for the interval. The default value 1 corresponds to a confidence level of 68 %. Both <computeroutput>deviation</computeroutput> and <computeroutput><classname alt="boost::histogram::utility::confidence_level">confidence_level</classname></computeroutput> objects can be used to initialize the interval. </para></description></parameter><purpose>Construct Wilson interval computer. </purpose><description><para>
</para></description></constructor>
</class></namespace>
<typedef name="dense_storage"><purpose>Vector-like storage for fast zero-overhead access to cells. </purpose><type><classname>storage_adaptor</classname>&lt; std::vector&lt; T, A &gt; &gt;</type></typedef>
<typedef name="default_storage"><purpose>Default storage, optimized for unweighted histograms. </purpose><type><classname>unlimited_storage</classname>&lt;&gt;</type></typedef>
<typedef name="weight_storage"><purpose>Dense storage which tracks sums of weights and a variance estimate. </purpose><type>dense_storage&lt; <classname>accumulators::weighted_sum</classname>&lt;&gt; &gt;</type></typedef>
<typedef name="profile_storage"><purpose>Dense storage which tracks means of samples in each cell. </purpose><type>dense_storage&lt; <classname>accumulators::mean</classname>&lt;&gt; &gt;</type></typedef>
<typedef name="weighted_profile_storage"><purpose>Dense storage which tracks means of weighted samples in each cell. </purpose><type>dense_storage&lt; <classname>accumulators::weighted_mean</classname>&lt;&gt; &gt;</type></typedef>


























</namespace>
</namespace>
<macro name="BOOST_HISTOGRAM_DETAIL_AXES_LIMIT"/>
</header>
<header name="boost/histogram/indexed.hpp">
<namespace name="boost">
<namespace name="histogram">
<class name="indexed_range"><template>
      <template-type-parameter name="Histogram"/>
    </template><purpose>Input iterator range over histogram bins with multi-dimensional index. </purpose><description><para>The iterator returned by begin() can only be incremented. If several copies of the input iterators exist, the other copies become invalid if one of them is incremented. </para></description><class name="accessor"><purpose>Lightweight view to access value and index of current cell. </purpose><description><para>The methods provide access to the current cell indices and bins. It acts like a pointer to the cell value, and in a limited way also like a reference. To interoperate with the algorithms of the standard library, the accessor is implicitly convertible to a cell value. Assignments and comparisons are passed through to the cell. An accessor is coupled to its parent <classname alt="boost::histogram::indexed_range::iterator">indexed_range::iterator</classname>. Moving the parent iterator forward also updates the linked accessor. Accessors are not copyable. They cannot be stored in containers, but <classname alt="boost::histogram::indexed_range::iterator">indexed_range::iterator</classname> can be stored. </para></description><class name="index_view"><purpose>Array-like view into the current multi-dimensional index. </purpose><class name="const_iterator"><purpose>implementation detail </purpose><method-group name="public member functions">
<method name="operator *" cv="const noexcept"><type>const_reference</type></method>
</method-group>
<method-group name="private member functions">
</method-group>
<constructor specifiers="explicit" cv="noexcept"><parameter name="i"><paramtype>index_pointer</paramtype></parameter></constructor>
</class><typedef name="const_reference"><type>const axis::index_type &amp;</type></typedef>
<method-group name="public member functions">
<method name="begin" cv="const noexcept"><type><classname>const_iterator</classname></type></method>
<method name="end" cv="const noexcept"><type><classname>const_iterator</classname></type></method>
<method name="size" cv="const noexcept"><type>std::size_t</type></method>
<method name="operator[]" cv="const noexcept"><type>const_reference</type><parameter name="d"><paramtype>unsigned</paramtype></parameter></method>
<method name="at" cv="const"><type>const_reference</type><parameter name="d"><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<method-group name="private member functions">
</method-group>
<constructor><parameter name="b"><paramtype>index_pointer</paramtype></parameter><parameter name="e"><paramtype>index_pointer</paramtype></parameter><purpose>implementation detail </purpose></constructor>
</class><method-group name="public member functions">
<method name="get" cv="const noexcept"><type>value_reference</type><purpose>Returns the cell reference. </purpose></method>
<method name="operator *" cv="const noexcept"><type>value_reference</type><purpose>Returns the cell reference. </purpose></method>
<method name="operator-&gt;" cv="const noexcept"><type>value_iterator</type><purpose>Access fields and methods of the cell object. </purpose></method>
<method name="index" cv="const noexcept"><type>axis::index_type</type><parameter name="d"><paramtype>unsigned</paramtype><default>0</default><description><para>axis dimension. </para></description></parameter><purpose>Access current index. </purpose><description><para>
</para></description></method>
<method name="indices" cv="const noexcept"><type><classname>index_view</classname></type><purpose>Access indices as an iterable range. </purpose></method>
<method name="bin" cv="const"><type>decltype(auto)</type><template>
          <template-nontype-parameter name="N"><type>unsigned</type><default>0</default><purpose><para>axis dimension. </para></purpose></template-nontype-parameter>
        </template><parameter name=""><paramtype>std::integral_constant&lt; unsigned, N &gt;</paramtype><default>{}</default></parameter><purpose>Access current bin. </purpose><description><para>
</para></description></method>
<method name="bin" cv="const"><type>decltype(auto)</type><parameter name="d"><paramtype>unsigned</paramtype><description><para>axis dimension. </para></description></parameter><purpose>Access current bin. </purpose><description><para>
</para></description></method>
<method name="density" cv="const"><type>double</type><purpose>Computes density in current cell. </purpose><description><para>The density is computed as the cell value divided by the product of bin widths. Axes without bin widths, like <classname alt="boost::histogram::axis::category">axis::category</classname>, are treated as having unit bin with. </para></description></method>
<method name="operator&lt;" cv="noexcept"><type>bool</type><parameter name="o"><paramtype>const <classname>accessor</classname> &amp;</paramtype></parameter></method>
<method name="operator&gt;" cv="noexcept"><type>bool</type><parameter name="o"><paramtype>const <classname>accessor</classname> &amp;</paramtype></parameter></method>
<method name="operator==" cv="noexcept"><type>bool</type><parameter name="o"><paramtype>const <classname>accessor</classname> &amp;</paramtype></parameter></method>
<method name="operator!=" cv="noexcept"><type>bool</type><parameter name="o"><paramtype>const <classname>accessor</classname> &amp;</paramtype></parameter></method>
<method name="operator&lt;=" cv="noexcept"><type>bool</type><parameter name="o"><paramtype>const <classname>accessor</classname> &amp;</paramtype></parameter></method>
<method name="operator&gt;=" cv="noexcept"><type>bool</type><parameter name="o"><paramtype>const <classname>accessor</classname> &amp;</paramtype></parameter></method>
<method name="operator&lt;" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="o"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="operator&gt;" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="o"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="operator==" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="o"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="o"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="operator&lt;=" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="o"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="operator&gt;=" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="o"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="conversion-operator" cv="const noexcept"><type>value_type</type></method>
</method-group>
<copy-assignment><type><classname>accessor</classname> &amp;</type><parameter name="o"><paramtype>const <classname>accessor</classname> &amp;</paramtype></parameter></copy-assignment>
<copy-assignment><type><classname>accessor</classname> &amp;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>const T &amp;</paramtype></parameter></copy-assignment>
<method-group name="private member functions">
</method-group>
<constructor cv="noexcept"><parameter name="i"><paramtype><classname>iterator</classname> &amp;</paramtype></parameter></constructor>
<constructor cv="= default"><parameter name=""><paramtype>const <classname>accessor</classname> &amp;</paramtype></parameter></constructor>
</class><class name="iterator"><purpose>implementation detail </purpose><struct name="index_data"><data-member name="idx"><type>axis::index_type</type></data-member>
<data-member name="begin"><type>axis::index_type</type></data-member>
<data-member name="end"><type>axis::index_type</type></data-member>
<data-member name="begin_skip"><type>std::size_t</type></data-member>
<data-member name="end_skip"><type>std::size_t</type></data-member>
</struct><struct name="indices_t"><inherit access="private">std::array&lt; index_data, buffer_size &gt;</inherit><typedef name="base_type"><type>std::array&lt; index_data, buffer_size &gt;</type></typedef>
<typedef name="pointer"><type>index_data *</type></typedef>
<typedef name="const_pointer"><type>const index_data *</type></typedef>
<data-member name="hist_"><type>histogram_type *</type></data-member>
<method-group name="public member functions">
<method name="size" cv="const noexcept"><type>unsigned</type></method>
<method name="begin" cv="noexcept"><type>pointer</type></method>
<method name="begin" cv="const noexcept"><type>const_pointer</type></method>
<method name="end" cv="noexcept"><type>pointer</type></method>
<method name="end" cv="const noexcept"><type>const_pointer</type></method>
</method-group>
<constructor cv="noexcept"><parameter name="h"><paramtype>histogram_type *</paramtype></parameter></constructor>
</struct><struct name="pointer_proxy"><data-member name="ref_"><type><classname>reference</classname></type></data-member>
<method-group name="public member functions">
<method name="operator-&gt;" cv="noexcept"><type><classname>reference</classname> *</type></method>
</method-group>
</struct><typedef name="value_type"><type>typename indexed_range::value_type</type></typedef>
<typedef name="reference"><type><classname>accessor</classname></type></typedef>
<typedef name="pointer"><type>pointer_proxy</type></typedef>
<typedef name="difference_type"><type>std::ptrdiff_t</type></typedef>
<typedef name="iterator_category"><type>std::forward_iterator_tag</type></typedef>
<method-group name="public member functions">
<method name="operator *" cv="noexcept"><type><classname>reference</classname></type></method>
<method name="operator-&gt;" cv="noexcept"><type>pointer</type></method>
<method name="operator++"><type><classname>iterator</classname> &amp;</type></method>
<method name="operator++"><type><classname>iterator</classname></type><parameter name=""><paramtype>int</paramtype></parameter></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="x"><paramtype>const <classname>iterator</classname> &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><parameter name="x"><paramtype>const <classname>iterator</classname> &amp;</paramtype></parameter></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="x"><paramtype>const value_iterator &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const noexcept"><type>bool</type><parameter name="x"><paramtype>const value_iterator &amp;</paramtype></parameter></method>
<method name="offset" cv="const noexcept"><type>std::size_t</type></method>
</method-group>
<method-group name="private member functions">
</method-group>
<constructor><parameter name="i"><paramtype>value_iterator</paramtype></parameter><parameter name="h"><paramtype>histogram_type &amp;</paramtype></parameter></constructor>
</class><typedef name="value_iterator"><purpose>implementation detail </purpose><type>std::conditional_t&lt; std::is_const&lt; histogram_type &gt;::value, typename histogram_type::const_iterator, typename histogram_type::iterator &gt;</type></typedef>
<typedef name="value_reference"><purpose>implementation detail </purpose><type>typename std::iterator_traits&lt; value_iterator &gt;::reference</type></typedef>
<typedef name="value_type"><purpose>implementation detail </purpose><type>typename std::iterator_traits&lt; value_iterator &gt;::value_type</type></typedef>
<method-group name="public member functions">
<method name="begin" cv="noexcept"><type><classname>iterator</classname></type></method>
<method name="end" cv="noexcept"><type><classname>iterator</classname></type></method>
</method-group>
<constructor><parameter name="hist"><paramtype>histogram_type &amp;</paramtype></parameter><parameter name="cov"><paramtype>coverage</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="hist"><paramtype>histogram_type &amp;</paramtype></parameter><parameter name="range"><paramtype>Iterable &amp;&amp;</paramtype></parameter></constructor>
<method-group name="private member functions">
<method name="make_range"><type>auto</type><parameter name="hist"><paramtype>histogram_type &amp;</paramtype></parameter><parameter name="cov"><paramtype>coverage</paramtype></parameter></method>
</method-group>
</class><enum name="coverage"><enumvalue name="inner"><description><para>iterate over inner bins, exclude underflow and overflow </para></description></enumvalue><enumvalue name="all"><description><para>iterate over all bins, including underflow and overflow </para></description></enumvalue><purpose>Coverage mode of the indexed range generator. </purpose><description><para>Defines options for the iteration strategy. </para></description></enum>

















<function name="indexed"><type>auto</type><template>
          <template-type-parameter name="Histogram"/>
        </template><parameter name="hist"><paramtype>Histogram &amp;&amp;</paramtype><description><para>Reference to the histogram. </para></description></parameter><parameter name="cov"><paramtype>coverage</paramtype><default>coverage::inner</default><description><para>Iterate over all or only inner bins (optional, default: inner). </para></description></parameter><purpose>Generates an indexed range of <ulink url="https://en.cppreference.com/w/cpp/named_req/ForwardIterator">forward iterators</ulink> over the histogram cells. </purpose><description><para>Use this in a range-based for loop:</para><para><programlisting language="c++">for (auto&amp;&amp; x : indexed(hist)) { ... }
</programlisting></para><para>This generates an optimized loop which is nearly always faster than a hand-written loop over the histogram cells. The iterators dereference to an <classname alt="boost::histogram::indexed_range::accessor">indexed_range::accessor</classname>, which has methods to query the current indices and bins and acts like a pointer to the cell value. The returned iterators are forward iterators. They can be stored in a container, but may not be used after the life-time of the histogram ends.</para><para>

</para></description><returns><para><classname alt="boost::histogram::indexed_range">indexed_range</classname></para>
</returns></function>
<function name="indexed"><type>auto</type><template>
          <template-type-parameter name="Histogram"/>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="hist"><paramtype>Histogram &amp;&amp;</paramtype><description><para>Reference to the histogram. </para></description></parameter><parameter name="range"><paramtype>Iterable &amp;&amp;</paramtype><description><para>Iterable over items with two axis::index_type values, which mark the begin and end index of each axis. The length of the iterable must be equal to the rank of the histogram. The begin index must be smaller than the end index. Index ranges wider than the actual range are reduced to the actual range including underflow and overflow indices. </para></description></parameter><purpose>Generates and indexed range <ulink url="https://en.cppreference.com/w/cpp/named_req/ForwardIterator">forward iterators</ulink> over a rectangular region of histogram cells. </purpose><description><para>Use this in a range-based for loop. Example: <programlisting language="c++">auto hist = make_histogram(axis::integer&lt;&gt;(0, 4), axis::integer&lt;&gt;(2, 6));
axis::index_type range[2] = {{1, 3}, {0, 2}};
for (auto&amp;&amp; x : indexed(hist, range)) { ... }
</programlisting> This skips the first and last index of the first axis, and the last two indices of the second.</para><para>

</para></description><returns><para><classname alt="boost::histogram::indexed_range">indexed_range</classname></para>
</returns></function>







</namespace>
</namespace>
</header>
<header name="boost/histogram/literals.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="literals">
<function name="operator&quot;&quot;_c"><type>auto</type><template>
          <template-nontype-parameter name="digits"><type>char...</type></template-nontype-parameter>
        </template><purpose>Suffix operator to generate literal compile-time numbers, 0_c, 12_c, etc. </purpose></function>
</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/make_histogram.hpp">
<para>Collection of factory functions to conveniently create histograms. </para><namespace name="boost">
<namespace name="histogram">








<function name="make_histogram_with"><type>auto</type><template>
          <template-type-parameter name="Storage"/>
          <template-type-parameter name="Axis"/>
          <template-nontype-parameter name="Axes"><type>class...</type></template-nontype-parameter>
          <template-type-parameter name=""><default>detail::requires_storage_or_adaptible&lt;Storage&gt;</default></template-type-parameter>
          <template-type-parameter name=""><default>detail::requires_axis&lt;Axis&gt;</default></template-type-parameter>
        </template><parameter name="storage"><paramtype>Storage &amp;&amp;</paramtype><description><para>Storage or container with standard interface (any vector, array, or map). </para></description></parameter><parameter name="axis"><paramtype>Axis &amp;&amp;</paramtype><description><para>First axis instance. </para></description></parameter><parameter name="axes"><paramtype>Axes &amp;&amp;...</paramtype><description><para>Other axis instances. </para></description></parameter><purpose>Make histogram from compile-time axis configuration and custom storage. </purpose><description><para>
</para></description></function>
<function name="make_histogram"><type>auto</type><template>
          <template-type-parameter name="Axis"/>
          <template-nontype-parameter name="Axes"><type>class...</type></template-nontype-parameter>
          <template-type-parameter name=""><default>detail::requires_axis&lt;Axis&gt;</default></template-type-parameter>
        </template><parameter name="axis"><paramtype>Axis &amp;&amp;</paramtype><description><para>First axis instance. </para></description></parameter><parameter name="axes"><paramtype>Axes &amp;&amp;...</paramtype><description><para>Other axis instances. </para></description></parameter><purpose>Make histogram from compile-time axis configuration and default storage. </purpose><description><para>
</para></description></function>
<function name="make_weighted_histogram"><type>auto</type><template>
          <template-type-parameter name="Axis"/>
          <template-nontype-parameter name="Axes"><type>class...</type></template-nontype-parameter>
          <template-type-parameter name=""><default>detail::requires_axis&lt;Axis&gt;</default></template-type-parameter>
        </template><parameter name="axis"><paramtype>Axis &amp;&amp;</paramtype><description><para>First axis instance. </para></description></parameter><parameter name="axes"><paramtype>Axes &amp;&amp;...</paramtype><description><para>Other axis instances. </para></description></parameter><purpose>Make histogram from compile-time axis configuration and weight-counting storage. </purpose><description><para>
</para></description></function>
<function name="make_histogram_with"><type>auto</type><template>
          <template-type-parameter name="Storage"/>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_storage_or_adaptible&lt;Storage&gt;</default></template-type-parameter>
          <template-type-parameter name=""><default>detail::requires_sequence_of_any_axis&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="storage"><paramtype>Storage &amp;&amp;</paramtype><description><para>Storage or container with standard interface (any vector, array, or map). </para></description></parameter><parameter name="iterable"><paramtype>Iterable &amp;&amp;</paramtype><description><para>Iterable range of axis objects. </para></description></parameter><purpose>Make histogram from iterable range and custom storage. </purpose><description><para>
</para></description></function>
<function name="make_histogram"><type>auto</type><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_sequence_of_any_axis&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="iterable"><paramtype>Iterable &amp;&amp;</paramtype><description><para>Iterable range of axis objects. </para></description></parameter><purpose>Make histogram from iterable range and default storage. </purpose><description><para>
</para></description></function>
<function name="make_weighted_histogram"><type>auto</type><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_sequence_of_any_axis&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="iterable"><paramtype>Iterable &amp;&amp;</paramtype><description><para>Iterable range of axis objects. </para></description></parameter><purpose>Make histogram from iterable range and weight-counting storage. </purpose><description><para>
</para></description></function>
<function name="make_histogram_with"><type>auto</type><template>
          <template-type-parameter name="Storage"/>
          <template-type-parameter name="Iterator"/>
          <template-type-parameter name=""><default>detail::requires_storage_or_adaptible&lt;Storage&gt;</default></template-type-parameter>
          <template-type-parameter name=""><default>detail::requires_iterator&lt;Iterator&gt;</default></template-type-parameter>
        </template><parameter name="storage"><paramtype>Storage &amp;&amp;</paramtype><description><para>Storage or container with standard interface (any vector, array, or map). </para></description></parameter><parameter name="begin"><paramtype>Iterator</paramtype><description><para>Iterator to range of axis objects. </para></description></parameter><parameter name="end"><paramtype>Iterator</paramtype><description><para>Iterator to range of axis objects. </para></description></parameter><purpose>Make histogram from iterator interval and custom storage. </purpose><description><para>
</para></description></function>
<function name="make_histogram"><type>auto</type><template>
          <template-type-parameter name="Iterator"/>
          <template-type-parameter name=""><default>detail::requires_iterator&lt;Iterator&gt;</default></template-type-parameter>
        </template><parameter name="begin"><paramtype>Iterator</paramtype><description><para>Iterator to range of axis objects. </para></description></parameter><parameter name="end"><paramtype>Iterator</paramtype><description><para>Iterator to range of axis objects. </para></description></parameter><purpose>Make histogram from iterator interval and default storage. </purpose><description><para>
</para></description></function>
<function name="make_weighted_histogram"><type>auto</type><template>
          <template-type-parameter name="Iterator"/>
          <template-type-parameter name=""><default>detail::requires_iterator&lt;Iterator&gt;</default></template-type-parameter>
        </template><parameter name="begin"><paramtype>Iterator</paramtype><description><para>Iterator to range of axis objects. </para></description></parameter><parameter name="end"><paramtype>Iterator</paramtype><description><para>Iterator to range of axis objects. </para></description></parameter><purpose>Make histogram from iterator interval and weight-counting storage. </purpose><description><para>
</para></description></function>









</namespace>
</namespace>
</header>
<header name="boost/histogram/make_profile.hpp">
<para>Collection of factory functions to conveniently create profiles. </para><para>Profiles are histograms which accept an additional sample and compute the mean of the sample in each cell. </para><namespace name="boost">
<namespace name="histogram">


<function name="make_profile"><type>auto</type><template>
          <template-type-parameter name="Axis"/>
          <template-nontype-parameter name="Axes"><type>class...</type></template-nontype-parameter>
          <template-type-parameter name=""><default>detail::requires_axis&lt;Axis&gt;</default></template-type-parameter>
        </template><parameter name="axis"><paramtype>Axis &amp;&amp;</paramtype><description><para>First axis instance. </para></description></parameter><parameter name="axes"><paramtype>Axes &amp;&amp;...</paramtype><description><para>Other axis instances. </para></description></parameter><purpose>Make profle from compile-time axis configuration. </purpose><description><para>
</para></description></function>
<function name="make_weighted_profile"><type>auto</type><template>
          <template-type-parameter name="Axis"/>
          <template-nontype-parameter name="Axes"><type>class...</type></template-nontype-parameter>
          <template-type-parameter name=""><default>detail::requires_axis&lt;Axis&gt;</default></template-type-parameter>
        </template><parameter name="axis"><paramtype>Axis &amp;&amp;</paramtype><description><para>First axis instance. </para></description></parameter><parameter name="axes"><paramtype>Axes &amp;&amp;...</paramtype><description><para>Other axis instances. </para></description></parameter><purpose>Make profle from compile-time axis configuration which accepts weights. </purpose><description><para>
</para></description></function>
<function name="make_profile"><type>auto</type><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_sequence_of_any_axis&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="iterable"><paramtype>Iterable &amp;&amp;</paramtype><description><para>Iterable range of axis objects. </para></description></parameter><purpose>Make profile from iterable range. </purpose><description><para>
</para></description></function>
<function name="make_weighted_profile"><type>auto</type><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_sequence_of_any_axis&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="iterable"><paramtype>Iterable &amp;&amp;</paramtype><description><para>Iterable range of axis objects. </para></description></parameter><purpose>Make profile from iterable range which accepts weights. </purpose><description><para>
</para></description></function>
<function name="make_profile"><type>auto</type><template>
          <template-type-parameter name="Iterator"/>
          <template-type-parameter name=""><default>detail::requires_iterator&lt;Iterator&gt;</default></template-type-parameter>
        </template><parameter name="begin"><paramtype>Iterator</paramtype><description><para>Iterator to range of axis objects. </para></description></parameter><parameter name="end"><paramtype>Iterator</paramtype><description><para>Iterator to range of axis objects. </para></description></parameter><purpose>Make profile from iterator interval. </purpose><description><para>
</para></description></function>
<function name="make_weighted_profile"><type>auto</type><template>
          <template-type-parameter name="Iterator"/>
          <template-type-parameter name=""><default>detail::requires_iterator&lt;Iterator&gt;</default></template-type-parameter>
        </template><parameter name="begin"><paramtype>Iterator</paramtype><description><para>Iterator to range of axis objects. </para></description></parameter><parameter name="end"><paramtype>Iterator</paramtype><description><para>Iterator to range of axis objects. </para></description></parameter><purpose>Make profile from iterator interval which accepts weights. </purpose><description><para>
</para></description></function>


















</namespace>
</namespace>
</header>
<header name="boost/histogram/multi_index.hpp">
<namespace name="boost">
<namespace name="histogram">
<struct name="multi_index"><template>
      <template-nontype-parameter name="Size"><type>std::size_t</type></template-nontype-parameter>
    </template><purpose>Holder for multiple axis indices. </purpose><description><para>Adapts external iterable, tuple, or explicit list of indices to the same representation. </para></description><struct name="priv_tag"/><typedef name="value_type"><type>axis::index_type</type></typedef>
<typedef name="iterator"><type>value_type *</type></typedef>
<typedef name="const_iterator"><type>const value_type *</type></typedef>
<method-group name="public static functions">
<method name="create" specifiers="static"><type><classname>multi_index</classname></type><parameter name="s"><paramtype>std::size_t</paramtype></parameter></method>
<method name="size" cv="noexcept" specifiers="static"><type>constexpr std::size_t</type></method>
</method-group>
<method-group name="public member functions">
<method name="begin" cv="noexcept"><type>iterator</type></method>
<method name="end" cv="noexcept"><type>iterator</type></method>
<method name="begin" cv="const noexcept"><type>const_iterator</type></method>
<method name="end" cv="const noexcept"><type>const_iterator</type></method>
</method-group>
<constructor><template>
          <template-nontype-parameter name="Is"><type>class...</type></template-nontype-parameter>
        </template><parameter name="i"><paramtype>axis::index_type</paramtype></parameter><parameter name="is"><paramtype>Is...</paramtype></parameter></constructor>
<constructor><template>
          <template-nontype-parameter name="Is"><type>class...</type></template-nontype-parameter>
        </template><parameter name="is"><paramtype>const std::tuple&lt; axis::index_type, Is... &gt; &amp;</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="is"><paramtype>const Iterable &amp;</paramtype></parameter></constructor>
<method-group name="private member functions">
</method-group>
<constructor><parameter name=""><paramtype>priv_tag</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="Is"><type>std::size_t...</type></template-nontype-parameter>
        </template><parameter name="is"><paramtype>const T &amp;</paramtype></parameter><parameter name=""><paramtype>mp11::index_sequence&lt; Is... &gt;</paramtype></parameter></constructor>
</struct><struct-specialization name="multi_index"><template>
    </template><specialization><template-arg>static_cast&lt; std::size_t &gt;(-1)</template-arg></specialization><struct name="priv_tag"/><typedef name="value_type"><type>axis::index_type</type></typedef>
<typedef name="iterator"><type>value_type *</type></typedef>
<typedef name="const_iterator"><type>const value_type *</type></typedef>
<method-group name="public static functions">
<method name="create" specifiers="static"><type><classname>multi_index</classname></type><parameter name="s"><paramtype>std::size_t</paramtype></parameter></method>
</method-group>
<method-group name="public member functions">
<method name="begin" cv="noexcept"><type>iterator</type></method>
<method name="end" cv="noexcept"><type>iterator</type></method>
<method name="begin" cv="const noexcept"><type>const_iterator</type></method>
<method name="end" cv="const noexcept"><type>const_iterator</type></method>
<method name="size" cv="const noexcept"><type>std::size_t</type></method>
</method-group>
<constructor><template>
          <template-nontype-parameter name="Is"><type>class...</type></template-nontype-parameter>
        </template><parameter name="i"><paramtype>axis::index_type</paramtype></parameter><parameter name="is"><paramtype>Is...</paramtype></parameter></constructor>
<constructor><template>
          <template-nontype-parameter name="Is"><type>class...</type></template-nontype-parameter>
        </template><parameter name="is"><paramtype>const std::tuple&lt; axis::index_type, Is... &gt; &amp;</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="is"><paramtype>const Iterable &amp;</paramtype></parameter></constructor>
<method-group name="private member functions">
</method-group>
<constructor><parameter name=""><paramtype>priv_tag</paramtype></parameter><parameter name="s"><paramtype>std::size_t</paramtype></parameter></constructor>
<constructor><template>
          <template-type-parameter name="T"/>
          <template-nontype-parameter name="Ns"><type>std::size_t...</type></template-nontype-parameter>
        </template><parameter name="is"><paramtype>const T &amp;</paramtype></parameter><parameter name=""><paramtype>mp11::index_sequence&lt; Ns... &gt;</paramtype></parameter></constructor>
</struct-specialization>

























</namespace>
</namespace>
</header>
<header name="boost/histogram/ostream.hpp">
<para>A simple streaming operator for the histogram type. </para><para>The text representation is rudimentary and not guaranteed to be stable between versions of Boost.Histogram. This header is not included by any other header and must be explicitly included to use the streaming operator.</para><para>To use your own, simply include your own implementation instead of this header. </para><namespace name="boost">
<namespace name="histogram">


























</namespace>
</namespace>
</header>
<header name="boost/histogram/accumulators/ostream.hpp">
<para>Simple streaming operators for the builtin accumulator types. </para><para>The text representation is not guaranteed to be stable between versions of Boost.Histogram. This header is only included by <ulink url="histogram/reference.html#header.boost.histogram.ostream_hpp">boost/histogram/ostream.hpp</ulink>. To use your own, include your own implementation instead of this header and do not include <ulink url="histogram/reference.html#header.boost.histogram.ostream_hpp">boost/histogram/ostream.hpp</ulink>. </para></header>
<header name="boost/histogram/axis/ostream.hpp">
<para>Simple streaming operators for the builtin axis types. </para><para>The text representation is not guaranteed to be stable between versions of Boost.Histogram. This header is only included by <ulink url="histogram/reference.html#header.boost.histogram.ostream_hpp">boost/histogram/ostream.hpp</ulink>. To use your own, include your own implementation instead of this header and do not include <ulink url="histogram/reference.html#header.boost.histogram.ostream_hpp">boost/histogram/ostream.hpp</ulink>. </para></header>
<header name="boost/histogram/sample.hpp">
<namespace name="boost">
<namespace name="histogram">
<struct name="sample_type"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Sample holder and type envelope. </purpose><description><para>You should not construct these directly, use the sample() helper function.</para><para>
</para></description><data-member name="value"><type>T</type></data-member>
</struct>
<function name="sample"><type>auto</type><template>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
        </template><parameter name="ts"><paramtype>Ts &amp;&amp;...</paramtype><description><para>arguments to be forwarded to the accumulator. </para></description></parameter><purpose>Helper function to mark arguments as sample. </purpose><description><para>
</para></description></function>
























</namespace>
</namespace>
</header>
<header name="boost/histogram/serialization.hpp">
<para>Headers from <ulink url="https://www.boost.org/doc/libs/develop/libs/serialization/doc/index.html">Boost.Serialization</ulink> needed to serialize STL types that are used internally by the Boost.Histogram classes. </para></header>
<header name="boost/histogram/storage_adaptor.hpp">
<namespace name="boost">
<namespace name="histogram">
<class name="storage_adaptor"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Turns any vector-like, array-like, and map-like container into a storage type. </purpose><method-group name="public member functions">
<method name="operator==" cv="const"><type>bool</type><template>
          <template-type-parameter name="U"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;U&gt;</default></template-type-parameter>
        </template><parameter name="u"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor cv="= default"><parameter name=""><paramtype><classname>storage_adaptor</classname> &amp;&amp;</paramtype></parameter></constructor>
<constructor cv="= default"><parameter name=""><paramtype>const <classname>storage_adaptor</classname> &amp;</paramtype></parameter></constructor>
<copy-assignment cv="= default"><type><classname>storage_adaptor</classname> &amp;</type><parameter name=""><paramtype><classname>storage_adaptor</classname> &amp;&amp;</paramtype></parameter></copy-assignment>
<copy-assignment cv="= default"><type><classname>storage_adaptor</classname> &amp;</type><parameter name=""><paramtype>const <classname>storage_adaptor</classname> &amp;</paramtype></parameter></copy-assignment>
<constructor><template>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
        </template><parameter name="ts"><paramtype>Ts &amp;&amp;...</paramtype></parameter></constructor>
<copy-assignment><type><classname>storage_adaptor</classname> &amp;</type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="u"><paramtype>U &amp;&amp;</paramtype></parameter></copy-assignment>
</class>

























</namespace>
</namespace>
</header>
<header name="boost/histogram/unlimited_storage.hpp">
<namespace name="boost">
<namespace name="histogram">
<class name="unlimited_storage"><template>
      <template-type-parameter name="Allocator"/>
    </template><purpose>Memory-efficient storage for integral counters which cannot overflow. </purpose><description><para>This storage provides a no-overflow-guarantee if the counters are incremented with integer weights. It maintains a contiguous array of elemental counters, one for each cell. If an operation is requested which would overflow a counter, the array is replaced with another of a wider integral type, then the operation is executed. The storage uses integers of 8, 16, 32, 64 bits, and then switches to a multiprecision integral type, similar to those in <ulink url="https://www.boost.org/doc/libs/develop/libs/multiprecision/doc/html/index.html">Boost.Multiprecision</ulink>.</para><para>A scaling operation or adding a floating point number triggers a conversion of the elemental counters into doubles, which voids the no-overflow-guarantee. </para></description><struct name="adder"><method-group name="public member functions">
<method name="operator()"><type>void</type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="tp"><paramtype>double *</paramtype></parameter><parameter name=""><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter><parameter name="x"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="operator()"><type>void</type><parameter name="tp"><paramtype>large_int *</paramtype></parameter><parameter name=""><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter><parameter name="x"><paramtype>const large_int &amp;</paramtype></parameter></method>
<method name="operator()"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="tp"><paramtype>T *</paramtype></parameter><parameter name="b"><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter><parameter name="x"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="is_x_integral"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name=""><paramtype>std::false_type</paramtype></parameter><parameter name="tp"><paramtype>T *</paramtype></parameter><parameter name="b"><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter><parameter name="x"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="is_x_integral"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name=""><paramtype>std::false_type</paramtype></parameter><parameter name="tp"><paramtype>T *</paramtype></parameter><parameter name="b"><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter><parameter name="x"><paramtype>const large_int &amp;</paramtype></parameter></method>
<method name="is_x_integral"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name=""><paramtype>std::true_type</paramtype></parameter><parameter name="tp"><paramtype>T *</paramtype></parameter><parameter name="b"><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter><parameter name="x"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="is_x_unsigned"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name=""><paramtype>std::false_type</paramtype></parameter><parameter name="tp"><paramtype>T *</paramtype></parameter><parameter name="b"><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter><parameter name="x"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="is_x_unsigned"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name=""><paramtype>std::true_type</paramtype></parameter><parameter name="tp"><paramtype>T *</paramtype></parameter><parameter name="b"><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter><parameter name="x"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="is_x_unsigned"><type>void</type><template>
          <template-type-parameter name="U"/>
        </template><parameter name=""><paramtype>std::true_type</paramtype></parameter><parameter name="tp"><paramtype>large_int *</paramtype></parameter><parameter name=""><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter><parameter name="x"><paramtype>const U &amp;</paramtype></parameter></method>
</method-group>
</struct><struct name="buffer_type"><typedef name="types"><type>mp11::mp_list&lt; U8, U16, U32, U64, large_int, double &gt;</type></typedef>
<data-member name="alloc"><type>allocator_type</type></data-member>
<data-member name="size"><type>std::size_t</type></data-member>
<data-member name="type"><type>unsigned</type></data-member>
<data-member name="ptr" specifiers="mutable"><type>void *</type></data-member>
<method-group name="public static functions">
<method name="type_index" cv="noexcept" specifiers="static"><type>constexpr unsigned</type><template>
          <template-type-parameter name="T"/>
        </template></method>
</method-group>
<method-group name="public member functions">
<method name="visit" cv="const"><type>decltype(auto)</type><template>
          <template-type-parameter name="F"/>
          <template-nontype-parameter name="Ts"><type>class...</type></template-nontype-parameter>
        </template><parameter name="f"><paramtype>F &amp;&amp;</paramtype></parameter><parameter name="ts"><paramtype>Ts &amp;&amp;...</paramtype></parameter></method>
<method name="destroy" cv="noexcept"><type>void</type></method>
<method name="make"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="n"><paramtype>std::size_t</paramtype></parameter></method>
<method name="make"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="U"/>
        </template><parameter name="n"><paramtype>std::size_t</paramtype></parameter><parameter name="iter"><paramtype>U</paramtype></parameter></method>
</method-group>
<constructor><parameter name="a"><paramtype>const allocator_type &amp;</paramtype><default>{}</default></parameter></constructor>
<constructor cv="noexcept"><parameter name="o"><paramtype>buffer_type &amp;&amp;</paramtype></parameter></constructor>
<copy-assignment cv="noexcept"><type>buffer_type &amp;</type><parameter name="o"><paramtype>buffer_type &amp;&amp;</paramtype></parameter></copy-assignment>
<constructor><parameter name="x"><paramtype>const buffer_type &amp;</paramtype></parameter></constructor>
<copy-assignment><type>buffer_type &amp;</type><parameter name="o"><paramtype>const buffer_type &amp;</paramtype></parameter></copy-assignment>
<destructor/>
</struct><class name="const_reference"><purpose>implementation detail </purpose><method-group name="public member functions">
<method name="conversion-operator" cv="const noexcept"><type>double</type></method>
<method name="operator&lt;" cv="const noexcept"><type>bool</type><parameter name="o"><paramtype>const <classname>const_reference</classname> &amp;</paramtype></parameter></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="o"><paramtype>const <classname>const_reference</classname> &amp;</paramtype></parameter></method>
<method name="operator&lt;" cv="const noexcept"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="o"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="operator&gt;" cv="const noexcept"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="o"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="operator==" cv="const noexcept"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="o"><paramtype>const U &amp;</paramtype></parameter></method>
</method-group>
<constructor cv="noexcept"><parameter name="b"><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter></constructor>
<constructor><parameter name=""><paramtype>const <classname>const_reference</classname> &amp;</paramtype></parameter></constructor>
<copy-assignment cv="= delete"><type><classname>const_reference</classname> &amp;</type><parameter name=""><paramtype>const <classname>const_reference</classname> &amp;</paramtype></parameter></copy-assignment>
<copy-assignment cv="= delete"><type><classname>const_reference</classname> &amp;</type><parameter name=""><paramtype><classname>const_reference</classname> &amp;&amp;</paramtype></parameter></copy-assignment>
<method-group name="private member functions">
<method name="apply_binary" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="Binary"/>
        </template><parameter name="x"><paramtype>const <classname>const_reference</classname> &amp;</paramtype></parameter></method>
<method name="apply_binary" cv="const noexcept"><type>bool</type><template>
          <template-type-parameter name="Binary"/>
          <template-type-parameter name="U"/>
        </template><parameter name="x"><paramtype>const U &amp;</paramtype></parameter></method>
</method-group>
</class><struct name="incrementor"><method-group name="public member functions">
<method name="operator()"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="tp"><paramtype>T *</paramtype></parameter><parameter name="b"><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter></method>
<method name="operator()"><type>void</type><parameter name="tp"><paramtype>large_int *</paramtype></parameter><parameter name=""><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter></method>
<method name="operator()"><type>void</type><parameter name="tp"><paramtype>double *</paramtype></parameter><parameter name=""><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter></method>
</method-group>
</struct><class name="iterator_impl"><template>
      <template-type-parameter name="Value"/>
      <template-type-parameter name="Reference"/>
    </template><method-group name="public member functions">
<method name="operator *" cv="const noexcept"><type>Reference</type></method>
</method-group>
<constructor cv="= default"/>
<constructor><template>
          <template-type-parameter name="V"/>
          <template-type-parameter name="R"/>
        </template><parameter name="it"><paramtype>const iterator_impl&lt; V, R &gt; &amp;</paramtype></parameter></constructor>
<constructor cv="noexcept"><parameter name="b"><paramtype>buffer_type *</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter></constructor>
</class><struct name="multiplier"><method-group name="public member functions">
<method name="operator()"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="tp"><paramtype>T *</paramtype></parameter><parameter name="b"><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="x"><paramtype>const double</paramtype></parameter></method>
<method name="operator()"><type>void</type><parameter name="tp"><paramtype>double *</paramtype></parameter><parameter name="b"><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="x"><paramtype>const double</paramtype></parameter></method>
<method name="operator()"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="tp"><paramtype>T *</paramtype></parameter><parameter name="b"><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter><parameter name="x"><paramtype>const double</paramtype></parameter></method>
<method name="operator()"><type>void</type><parameter name="tp"><paramtype>double *</paramtype></parameter><parameter name=""><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter><parameter name="x"><paramtype>const double</paramtype></parameter></method>
</method-group>
</struct><class name="reference"><inherit access="public">boost::histogram::unlimited_storage&lt; Allocator &gt;::const_reference</inherit><purpose>implementation detail </purpose><method-group name="public member functions">
<method name="operator&lt;" cv="const noexcept"><type>bool</type><parameter name="o"><paramtype>const <classname>reference</classname> &amp;</paramtype></parameter></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="o"><paramtype>const <classname>reference</classname> &amp;</paramtype></parameter></method>
<method name="operator&lt;" cv="const noexcept"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="o"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="operator&gt;" cv="const noexcept"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="o"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="operator==" cv="const noexcept"><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="o"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="operator+="><type><classname>reference</classname> &amp;</type><parameter name="x"><paramtype>const <classname>const_reference</classname> &amp;</paramtype></parameter></method>
<method name="operator+="><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="x"><paramtype>const U &amp;</paramtype></parameter></method>
<method name="operator-="><type><classname>reference</classname> &amp;</type><parameter name="x"><paramtype>const double</paramtype></parameter></method>
<method name="operator *="><type><classname>reference</classname> &amp;</type><parameter name="x"><paramtype>const double</paramtype></parameter></method>
<method name="operator/="><type><classname>reference</classname> &amp;</type><parameter name="x"><paramtype>const double</paramtype></parameter></method>
<method name="operator++"><type><classname>reference</classname> &amp;</type></method>
</method-group>
<constructor cv="noexcept"><parameter name="b"><paramtype>buffer_type &amp;</paramtype></parameter><parameter name="i"><paramtype>std::size_t</paramtype></parameter></constructor>
<constructor><parameter name="x"><paramtype>const <classname>reference</classname> &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>reference</classname> &amp;</type><parameter name="x"><paramtype>const <classname>reference</classname> &amp;</paramtype></parameter></copy-assignment>
<copy-assignment><type><classname>reference</classname> &amp;</type><parameter name="x"><paramtype>const <classname>const_reference</classname> &amp;</paramtype></parameter></copy-assignment>
<copy-assignment><type><emphasis>unspecified</emphasis></type><template>
          <template-type-parameter name="U"/>
        </template><parameter name="x"><paramtype>const U &amp;</paramtype></parameter></copy-assignment>
</class><typedef name="allocator_type"><type>Allocator</type></typedef>
<typedef name="value_type"><type>double</type></typedef>
<typedef name="large_int"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="const_iterator"><type>iterator_impl&lt; const value_type, <classname>const_reference</classname> &gt;</type></typedef>
<typedef name="iterator"><type>iterator_impl&lt; value_type, <classname>reference</classname> &gt;</type></typedef>
<data-member name="has_threading_support" specifiers="static"><type>constexpr bool</type></data-member>
<method-group name="public member functions">
<method name="get_allocator" cv="const"><type>allocator_type</type></method>
<method name="reset"><type>void</type><parameter name="n"><paramtype>std::size_t</paramtype></parameter></method>
<method name="size" cv="const noexcept"><type>std::size_t</type></method>
<method name="operator[]" cv="noexcept"><type><classname>reference</classname></type><parameter name="i"><paramtype>std::size_t</paramtype></parameter></method>
<method name="operator[]" cv="const noexcept"><type><classname>const_reference</classname></type><parameter name="i"><paramtype>std::size_t</paramtype></parameter></method>
<method name="operator==" cv="const noexcept"><type>bool</type><parameter name="x"><paramtype>const <classname>unlimited_storage</classname> &amp;</paramtype></parameter></method>
<method name="operator==" cv="const"><type>bool</type><template>
          <template-type-parameter name="Iterable"/>
        </template><parameter name="iterable"><paramtype>const Iterable &amp;</paramtype></parameter></method>
<method name="operator *="><type><classname>unlimited_storage</classname> &amp;</type><parameter name="x"><paramtype>const double</paramtype></parameter></method>
<method name="begin" cv="noexcept"><type>iterator</type></method>
<method name="end" cv="noexcept"><type>iterator</type></method>
<method name="begin" cv="const noexcept"><type>const_iterator</type></method>
<method name="end" cv="const noexcept"><type>const_iterator</type></method>
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name=""><paramtype>unsigned</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit"><parameter name="a"><paramtype>const allocator_type &amp;</paramtype><default>{}</default></parameter></constructor>
<constructor cv="= default"><parameter name=""><paramtype>const <classname>unlimited_storage</classname> &amp;</paramtype></parameter></constructor>
<copy-assignment cv="= default"><type><classname>unlimited_storage</classname> &amp;</type><parameter name=""><paramtype>const <classname>unlimited_storage</classname> &amp;</paramtype></parameter></copy-assignment>
<constructor cv="= default"><parameter name=""><paramtype><classname>unlimited_storage</classname> &amp;&amp;</paramtype></parameter></constructor>
<copy-assignment cv="= default"><type><classname>unlimited_storage</classname> &amp;</type><parameter name=""><paramtype><classname>unlimited_storage</classname> &amp;&amp;</paramtype></parameter></copy-assignment>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="s"><paramtype>const Iterable &amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>unlimited_storage</classname> &amp;</type><template>
          <template-type-parameter name="Iterable"/>
          <template-type-parameter name=""><default>detail::requires_iterable&lt;Iterable&gt;</default></template-type-parameter>
        </template><parameter name="s"><paramtype>const Iterable &amp;</paramtype></parameter></copy-assignment>
<constructor><template>
          <template-type-parameter name="T"/>
        </template><parameter name="s"><paramtype>std::size_t</paramtype></parameter><parameter name="p"><paramtype>const T *</paramtype></parameter><parameter name="a"><paramtype>const allocator_type &amp;</paramtype><default>{}</default></parameter><purpose>implementation detail; used by unit tests, not part of generic storage interface </purpose></constructor>
</class>

























</namespace>
</namespace>
</header>
<header name="boost/histogram/unsafe_access.hpp">
<namespace name="boost">
<namespace name="histogram">
<struct name="unsafe_access"><purpose>Unsafe read/write access to private data that potentially breaks consistency. </purpose><description><para>This struct enables access to private data of some classes. It is intended for library developers who need this to implement algorithms efficiently, for example, serialization. Users should not use this. If you are a user who absolutely needs this to get a specific effect, please submit an issue on Github. Perhaps the public interface is insufficient and should be extended for your use case.</para><para>Unlike the normal interface, the <classname alt="boost::histogram::unsafe_access">unsafe_access</classname> interface may change between versions. If your code relies on <classname alt="boost::histogram::unsafe_access">unsafe_access</classname>, it may or may not break when you update Boost. This is another reason to not use it unless you are ok with these conditions. </para></description><method-group name="public static functions">
<method name="axes" specifiers="static"><type>auto &amp;</type><template>
          <template-type-parameter name="Histogram"/>
        </template><parameter name="hist"><paramtype>Histogram &amp;</paramtype><description><para>histogram. </para></description></parameter><purpose>Get axes. </purpose><description><para>
</para></description></method>
<method name="axes" specifiers="static"><type>const auto &amp;</type><template>
          <template-type-parameter name="Histogram"/>
        </template><parameter name="hist"><paramtype>const Histogram &amp;</paramtype><description><para>histogram. </para></description></parameter><purpose>Get axes. </purpose><description><para>
</para></description></method>
<method name="axis" specifiers="static"><type>decltype(auto)</type><template>
          <template-type-parameter name="Histogram"/>
          <template-nontype-parameter name="I"><type>unsigned</type><default>0</default><purpose><para>axis index (optional, default: 0). </para></purpose></template-nontype-parameter>
        </template><parameter name="hist"><paramtype>Histogram &amp;</paramtype><description><para>histogram. </para></description></parameter><parameter name=""><paramtype>std::integral_constant&lt; unsigned, I &gt;</paramtype><default>{}</default></parameter><purpose>Get mutable axis reference with compile-time number. </purpose><description><para>

</para></description></method>
<method name="axis" specifiers="static"><type>decltype(auto)</type><template>
          <template-type-parameter name="Histogram"/>
        </template><parameter name="hist"><paramtype>Histogram &amp;</paramtype><description><para>histogram. </para></description></parameter><parameter name="i"><paramtype>unsigned</paramtype><description><para>axis index. </para></description></parameter><purpose>Get mutable axis reference with run-time number. </purpose><description><para>
</para></description></method>
<method name="storage" specifiers="static"><type>auto &amp;</type><template>
          <template-type-parameter name="Histogram"/>
        </template><parameter name="hist"><paramtype>Histogram &amp;</paramtype><description><para>histogram. </para></description></parameter><purpose>Get storage. </purpose><description><para>
</para></description></method>
<method name="storage" specifiers="static"><type>const auto &amp;</type><template>
          <template-type-parameter name="Histogram"/>
        </template><parameter name="hist"><paramtype>const Histogram &amp;</paramtype><description><para>histogram. </para></description></parameter><purpose>Get storage. </purpose><description><para>
</para></description></method>
<method name="offset" specifiers="static"><type>auto &amp;</type><template>
          <template-type-parameter name="Histogram"/>
        </template><parameter name="hist"><paramtype>Histogram &amp;</paramtype><description><para>histogram </para></description></parameter><purpose>Get index offset. </purpose><description><para>
</para></description></method>
<method name="offset" specifiers="static"><type>const auto &amp;</type><template>
          <template-type-parameter name="Histogram"/>
        </template><parameter name="hist"><paramtype>const Histogram &amp;</paramtype><description><para>histogram </para></description></parameter><purpose>Get index offset. </purpose><description><para>
</para></description></method>
<method name="unlimited_storage_buffer" specifiers="static"><type>constexpr auto &amp;</type><template>
          <template-type-parameter name="Allocator"/>
        </template><parameter name="storage"><paramtype><classname>unlimited_storage</classname>&lt; Allocator &gt; &amp;</paramtype><description><para>instance of <classname alt="boost::histogram::unlimited_storage">unlimited_storage</classname>. </para></description></parameter><purpose>Get buffer of <classname alt="boost::histogram::unlimited_storage">unlimited_storage</classname>. </purpose><description><para>
</para></description></method>
<method name="storage_adaptor_impl" specifiers="static"><type>constexpr auto &amp;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="storage"><paramtype><classname>storage_adaptor</classname>&lt; T &gt; &amp;</paramtype><description><para>instance of <classname alt="boost::histogram::storage_adaptor">storage_adaptor</classname>. </para></description></parameter><purpose>Get implementation of <classname alt="boost::histogram::storage_adaptor">storage_adaptor</classname>. </purpose><description><para>
</para></description></method>
</method-group>
</struct>

























</namespace>
</namespace>
</header>
<header name="boost/histogram/utility/binomial_proportion_interval.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="utility">
<class name="binomial_proportion_interval"><template>
      <template-type-parameter name="ValueType"/>
    </template><purpose>Common base class for interval calculators. </purpose><typedef name="value_type"><type>ValueType</type></typedef>
<typedef name="interval_type"><type>std::pair&lt; value_type, value_type &gt;</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const = 0" specifiers="virtual"><type>interval_type</type><parameter name="successes"><paramtype>value_type</paramtype><description><para>Number of successful trials. </para></description></parameter><parameter name="failures"><paramtype>value_type</paramtype><description><para>Number of failed trials. </para></description></parameter><purpose>Compute interval for given number of successes and failures. </purpose><description><para>
</para></description></method>
<method name="operator()" cv="const noexcept"><type>interval_type</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="fraction"><paramtype>const <classname>accumulators::fraction</classname>&lt; T &gt; &amp;</paramtype><description><para>Fraction accumulator. </para></description></parameter><purpose>Compute interval for a fraction accumulator. </purpose><description><para>
</para></description></method>
</method-group>
</class><class name="confidence_level"><purpose>Confidence level for intervals. </purpose><description><para>Intervals become wider as the deviation value increases. </para></description><method-group name="public member functions">
<method name="conversion-operator" cv="const noexcept" specifiers="explicit"><type>T</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name=""><default>std::enable_if_t&lt;std::is_floating_point&lt;T&gt;::value&gt;</default></template-type-parameter>
        </template><purpose>explicit conversion to numerical confidence level </purpose></method>
<method name="conversion-operator" cv="const noexcept"><type>deviation</type><purpose>implicit conversion to units of standard deviation </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="cl"><paramtype>double</paramtype></parameter><purpose>constructor from confidence level (a probability) </purpose></constructor>
</class><class name="deviation"><purpose>Confidence level in units of deviations for intervals. </purpose><description><para>Intervals become wider as the deviation value increases. The standard deviation corresponds to a value of 1 and corresponds to 68.3 % confidence level. The conversion between confidence level and deviations is based on a two-sided interval on the normal distribution. </para></description><method-group name="public member functions">
<method name="conversion-operator" cv="const noexcept" specifiers="explicit"><type>T</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name=""><default>std::enable_if_t&lt;std::is_floating_point&lt;T&gt;::value&gt;</default></template-type-parameter>
        </template><purpose>explicit conversion to units of standard deviations </purpose></method>
<method name="conversion-operator" cv="const noexcept"><type>confidence_level</type><purpose>implicit conversion to confidence level </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="d"><paramtype>double</paramtype></parameter><purpose>constructor from units of standard deviations </purpose></constructor>
</class></namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/utility/clopper_pearson_interval.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="utility">
</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/utility/jeffreys_interval.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="utility">
</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/utility/wald_interval.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="utility">
</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/utility/wilson_interval.hpp">
<namespace name="boost">
<namespace name="histogram">
<namespace name="utility">
</namespace>


























</namespace>
</namespace>
</header>
<header name="boost/histogram/weight.hpp">
<namespace name="boost">
<namespace name="histogram">
<struct name="weight_type"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Weight holder and type envelope. </purpose><description><para>You should not construct these directly, use the weight() helper function.</para><para>
</para></description><data-member name="value"><type>T</type><purpose>Access underlying value. </purpose></data-member>
<method-group name="public member functions">
<method name="conversion-operator" cv="const"><type>weight_type&lt; U &gt;</type><template>
          <template-type-parameter name="U"/>
        </template><purpose>Allow implicit conversions of types when the underlying value type allows them. </purpose></method>
</method-group>
</struct><function name="weight"><type>auto</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="t"><paramtype>T &amp;&amp;</paramtype><description><para>argument to be forward to the histogram. </para></description></parameter><purpose>Helper function to mark argument as weight. </purpose><description><para>
</para></description></function>

























</namespace>
</namespace>
</header>
</library-reference>