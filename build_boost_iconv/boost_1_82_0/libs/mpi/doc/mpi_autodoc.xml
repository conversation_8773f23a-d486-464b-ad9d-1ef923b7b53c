<?xml version="1.0" standalone="yes"?>
<library-reference><header name="boost/mpi.hpp">
<para>This file is a top-level convenience header that includes all of the Boost.MPI library headers. Users concerned about compile time may wish to include only specific headers from the Boost.MPI library. </para></header>
<header name="boost/mpi/allocator.hpp">
<para>This header provides an STL-compliant allocator that uses the MPI-2 memory allocation facilities. </para><namespace name="boost">
<namespace name="mpi">
<class name="allocator"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Standard Library-compliant allocator for the MPI-2 memory allocation routines. </purpose><description><para>This allocator provides a standard C++ interface to the <computeroutput>MPI_Alloc_mem</computeroutput> and <computeroutput>MPI_Free_mem</computeroutput> routines of MPI-2. It is intended to be used with the containers in the Standard Library (<computeroutput>vector</computeroutput>, in particular) in cases where the contents of the container will be directly transmitted via MPI. This allocator is also used internally by the library for character buffers that will be used in the transmission of data.</para><para>The <computeroutput>allocator</computeroutput> class template only provides MPI memory allocation when the underlying MPI implementation is either MPI-2 compliant or is known to provide <computeroutput>MPI_Alloc_mem</computeroutput> and <computeroutput>MPI_Free_mem</computeroutput> as extensions. When the MPI memory allocation routines are not available, <computeroutput>allocator</computeroutput> is brought in directly from namespace <computeroutput>std</computeroutput>, so that standard allocators are used throughout. The macro <computeroutput>BOOST_MPI_HAS_MEMORY_ALLOCATION</computeroutput> will be defined when the MPI-2 memory allocation facilities are available. </para></description><struct name="rebind"><template>
      <template-type-parameter name="U"/>
    </template><purpose>Retrieve the type of an allocator similar to this allocator but for a different value type. </purpose><typedef name="other"><type><classname>allocator</classname>&lt; U &gt;</type></typedef>
</struct><typedef name="size_type"><purpose>Holds the size of objects. </purpose><type>std::size_t</type></typedef>
<typedef name="difference_type"><purpose>Holds the number of elements between two pointers. </purpose><type>std::ptrdiff_t</type></typedef>
<typedef name="pointer"><purpose>A pointer to an object of type <computeroutput>T</computeroutput>. </purpose><type>T *</type></typedef>
<typedef name="const_pointer"><purpose>A pointer to a constant object of type <computeroutput>T</computeroutput>. </purpose><type>const T *</type></typedef>
<typedef name="reference"><purpose>A reference to an object of type <computeroutput>T</computeroutput>. </purpose><type>T &amp;</type></typedef>
<typedef name="const_reference"><purpose>A reference to a constant object of type <computeroutput>T</computeroutput>. </purpose><type>const T &amp;</type></typedef>
<typedef name="value_type"><purpose>The type of memory allocated by this allocator. </purpose><type>T</type></typedef>
<method-group name="public member functions">
<method name="address" cv="const"><type>pointer</type><parameter name="x"><paramtype>reference</paramtype></parameter><description><para>Returns the address of object <computeroutput>x</computeroutput>. </para></description></method>
<method name="address" cv="const"><type>const_pointer</type><parameter name="x"><paramtype>const_reference</paramtype></parameter><description><para>Returns the address of object <computeroutput>x</computeroutput>. </para></description></method>
<method name="allocate"><type>pointer</type><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of elements for which memory should be allocated.</para></description></parameter><parameter name=""><paramtype><classname>allocator</classname>&lt; void &gt;::const_pointer</paramtype><default>0</default></parameter><description><para>Allocate enough memory for <computeroutput>n</computeroutput> elements of type <computeroutput>T</computeroutput>.</para><para>

</para></description><returns><para>a pointer to the newly-allocated memory </para>
</returns></method>
<method name="deallocate"><type>void</type><parameter name="p"><paramtype>pointer</paramtype><description><para>The pointer whose memory should be deallocated. This pointer shall have been returned from the <computeroutput>allocate()</computeroutput> function and not have already been freed. </para></description></parameter><parameter name=""><paramtype>size_type</paramtype></parameter><description><para>Deallocate memory referred to by the pointer <computeroutput>p</computeroutput>.</para><para>
</para></description></method>
<method name="max_size" cv="const"><type>size_type</type><description><para>Returns the maximum number of elements that can be allocated with <computeroutput>allocate()</computeroutput>. </para></description></method>
<method name="construct"><type>void</type><parameter name="p"><paramtype>pointer</paramtype></parameter><parameter name="val"><paramtype>const T &amp;</paramtype></parameter><description><para>Construct a copy of <computeroutput>val</computeroutput> at the location referenced by <computeroutput>p</computeroutput>. </para></description></method>
<method name="destroy"><type>void</type><parameter name="p"><paramtype>pointer</paramtype></parameter><description><para>Destroy the object referenced by <computeroutput>p</computeroutput>. </para></description></method>
</method-group>
<constructor><description><para>Default-construct an allocator. </para></description></constructor>
<constructor><parameter name=""><paramtype>const <classname>allocator</classname> &amp;</paramtype></parameter><description><para>Copy-construct an allocator. </para></description></constructor>
<constructor><template>
          <template-type-parameter name="U"/>
        </template><parameter name=""><paramtype>const <classname>allocator</classname>&lt; U &gt; &amp;</paramtype></parameter><description><para>Copy-construct an allocator from another allocator for a different value type. </para></description></constructor>
<destructor><description><para>Destroy an allocator. </para></description></destructor>
</class><class-specialization name="allocator"><template>
    </template><specialization><template-arg>void</template-arg></specialization><purpose>Allocator specialization for <computeroutput>void</computeroutput> value types. </purpose><description><para>The <computeroutput>void</computeroutput> specialization of <computeroutput>allocator</computeroutput> is useful only for rebinding to another, different value type. </para></description><struct name="rebind"><template>
      <template-type-parameter name="U"/>
    </template><typedef name="other"><type><classname>allocator</classname>&lt; U &gt;</type></typedef>
</struct><typedef name="pointer"><type>void *</type></typedef>
<typedef name="const_pointer"><type>const void *</type></typedef>
<typedef name="value_type"><type>void</type></typedef>
</class-specialization><function name="operator=="><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="T2"/>
        </template><parameter name=""><paramtype>const <classname>allocator</classname>&lt; T1 &gt; &amp;</paramtype></parameter><parameter name=""><paramtype>const <classname>allocator</classname>&lt; T2 &gt; &amp;</paramtype></parameter><purpose>Compare two allocators for equality. </purpose><description><para>Since MPI allocators have no state, all MPI allocators are equal.</para><para>
</para></description><returns><para><computeroutput>true</computeroutput> </para>
</returns></function>
<function name="operator!="><type>bool</type><template>
          <template-type-parameter name="T1"/>
          <template-type-parameter name="T2"/>
        </template><parameter name=""><paramtype>const <classname>allocator</classname>&lt; T1 &gt; &amp;</paramtype></parameter><parameter name=""><paramtype>const <classname>allocator</classname>&lt; T2 &gt; &amp;</paramtype></parameter><purpose>Compare two allocators for inequality. </purpose><description><para>Since MPI allocators have no state, all MPI allocators are equal.</para><para>
</para></description><returns><para><computeroutput>false</computeroutput> </para>
</returns></function>
























































































</namespace>
</namespace>
</header>
<header name="boost/mpi/cartesian_communicator.hpp">
<para>This header defines facilities to support MPI communicators with cartesian topologies. If known at compiled time, the dimension of the implied grid can be statically enforced, through the templatized communicator class. Otherwise, a non template, dynamic, base class is provided. </para><namespace name="boost">
<namespace name="mpi">
<class name="cartesian_communicator"><inherit access="public">boost::mpi::communicator</inherit><purpose>An MPI communicator with a cartesian topology. </purpose><description><para>A <computeroutput><classname alt="boost::mpi::cartesian_communicator">cartesian_communicator</classname></computeroutput> is a communicator whose topology is expressed as a grid. Cartesian communicators have the same functionality as (intra)communicators, but also allow one to query the relationships among processes and the properties of the grid. </para></description><method-group name="public member functions">
<method name="ndims" cv="const"><type>int</type><description><para>Retrive the number of dimension of the underlying toppology. </para></description></method>
<method name="rank" cv="const"><type>int</type><parameter name="coords"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype><description><para>the coordinates. the size must match the communicator's topology. </para></description></parameter><description><para>Return the rank of the process at the given coordinates. 
</para></description></method>
<method name="shifted_ranks" cv="const"><type>std::pair&lt; int, int &gt;</type><parameter name="dim"><paramtype>int</paramtype><description><para>the dimension in which the shift takes place. 0 &lt;= dim &lt;= ndim(). </para></description></parameter><parameter name="disp"><paramtype>int</paramtype><description><para>the shift displacement, can be positive (upward) or negative (downward). </para></description></parameter><description><para>Return the rank of the source and target destination process through a shift. 
</para></description></method>
<method name="coordinates" cv="const"><type>std::vector&lt; int &gt;</type><parameter name="rk"><paramtype>int</paramtype><description><para>the ranks in this communicator. </para></description></parameter><description><para>Provides the coordinates of the process with the given rank. 

</para></description><returns><para>the coordinates. </para>
</returns></method>
<method name="topology" cv="const"><type>void</type><parameter name="dims"><paramtype><classname>cartesian_topology</classname> &amp;</paramtype></parameter><parameter name="coords"><paramtype>std::vector&lt; int &gt; &amp;</paramtype></parameter><description><para>Retrieve the topology and coordinates of this process in the grid. </para></description></method>
<method name="topology" cv="const"><type><classname>cartesian_topology</classname></type><description><para>Retrieve the topology of the grid. </para></description></method>
<method name="rank"><type>int</type><purpose>Determine the rank of the executing process in a communicator. </purpose><description><para>This routine is equivalent to <computeroutput>MPI_Comm_rank</computeroutput>.</para><para>
</para></description><returns><para>The rank of the process in the communicator, which will be a value in [0, size()) </para>
</returns></method>
</method-group>
<constructor><parameter name="comm"><paramtype>const MPI_Comm &amp;</paramtype></parameter><parameter name="kind"><paramtype>comm_create_kind</paramtype></parameter><description><para>Build a new Boost.MPI cartesian communicator based on the MPI communicator <computeroutput>comm</computeroutput> with cartesian topology.</para><para><computeroutput>comm</computeroutput> may be any valid MPI communicator. If <computeroutput>comm</computeroutput> is MPI_COMM_NULL, an empty communicator (that cannot be used for communication) is created and the <computeroutput>kind</computeroutput> parameter is ignored. Otherwise, the <computeroutput>kind</computeroutput> parameter determines how the Boost.MPI communicator will be related to <computeroutput>comm:</computeroutput> </para><para><itemizedlist>
<listitem><para>If <computeroutput>kind</computeroutput> is <computeroutput>comm_duplicate</computeroutput>, duplicate <computeroutput>comm</computeroutput> to create a new communicator. This new communicator will be freed when the Boost.MPI communicator (and all copies of it) is destroyed. This option is only permitted if the underlying MPI implementation supports MPI 2.0; duplication of intercommunicators is not available in MPI 1.x.</para>
</listitem><listitem><para>If <computeroutput>kind</computeroutput> is <computeroutput>comm_take_ownership</computeroutput>, take ownership of <computeroutput>comm</computeroutput>. It will be freed automatically when all of the Boost.MPI communicators go out of scope.</para>
</listitem><listitem><para>If <computeroutput>kind</computeroutput> is <computeroutput>comm_attach</computeroutput>, this Boost.MPI communicator will reference the existing MPI communicator <computeroutput>comm</computeroutput> but will not free <computeroutput>comm</computeroutput> when the Boost.MPI communicator goes out of scope. This option should only be used when the communicator is managed by the user. </para>
</listitem></itemizedlist>
</para></description></constructor>
<constructor><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The communicator that the new, cartesian communicator will be based on.</para></description></parameter><parameter name="dims"><paramtype>const <classname>cartesian_topology</classname> &amp;</paramtype><description><para>the cartesian dimension of the new communicator. The size indicate the number of dimension. Some dimensions be set to zero, in which case the corresponding dimension value is left to the system.</para></description></parameter><parameter name="reorder"><paramtype>bool</paramtype><default>false</default><description><para>Whether MPI is permitted to re-order the process ranks within the returned communicator, to better optimize communication. If false, the ranks of each process in the returned process will match precisely the rank of that process within the original communicator. </para></description></parameter><description><para>Create a new communicator whose topology is described by the given cartesian. The indices of the vertices in the cartesian will be assumed to be the ranks of the processes within the communicator. There may be fewer vertices in the cartesian than there are processes in the communicator; in this case, the resulting communicator will be a NULL communicator.</para><para>
</para></description></constructor>
<constructor><parameter name="comm"><paramtype>const <classname>cartesian_communicator</classname> &amp;</paramtype><description><para>the original communicator. </para></description></parameter><parameter name="keep"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype><description><para>and array containiing the dimension to keep from the existing communicator. </para></description></parameter><description><para>Create a new cartesian communicator whose topology is a subset of an existing cartesian cimmunicator. 
</para></description></constructor>
</class><struct name="cartesian_dimension"><purpose>Specify the size and periodicity of the grid in a single dimension. </purpose><description><para>POD lightweight object. </para></description><data-member name="size"><type>int</type><description><para>The size of the grid n this dimension. </para></description></data-member>
<data-member name="periodic"><type>bool</type><description><para>Is the grid periodic in this dimension. </para></description></data-member>
<method-group name="public member functions">
</method-group>
<constructor><parameter name="sz"><paramtype>int</paramtype><default>0</default></parameter><parameter name="p"><paramtype>bool</paramtype><default>false</default></parameter></constructor>
<method-group name="private member functions">
<method name="serialize"><type>void</type><template>
          <template-type-parameter name="Archive"/>
        </template><parameter name="ar"><paramtype>Archive &amp;</paramtype></parameter><parameter name="version"><paramtype>const unsigned int</paramtype></parameter></method>
</method-group>
</struct><class name="cartesian_topology"><inherit access="private">std::vector&lt; cartesian_dimension &gt;</inherit><purpose>Describe the topology of a cartesian grid. </purpose><description><para>Behave mostly like a sequence of <computeroutput><classname alt="boost::mpi::cartesian_dimension">cartesian_dimension</classname></computeroutput> with the notable exception that its size is fixed. This is a lightweight object, so that any constructor that could be considered missing could be replaced with a function (move constructor provided when supported). </para></description><method-group name="public member functions">
<method name="stl"><type>std::vector&lt; <classname>cartesian_dimension</classname> &gt; &amp;</type><description><para>Export as an stl sequence. </para></description></method>
<method name="stl" cv="const"><type>std::vector&lt; <classname>cartesian_dimension</classname> &gt; const  &amp;</type><description><para>Export as an stl sequence. </para></description></method>
<method name="split" cv="const"><type>void</type><parameter name="dims"><paramtype>std::vector&lt; int &gt; &amp;</paramtype></parameter><parameter name="periodics"><paramtype>std::vector&lt; bool &gt; &amp;</paramtype></parameter><description><para>Split the topology in two sequences of sizes and periodicities. </para></description></method>
</method-group>
<constructor cv="= delete"/>
<constructor cv="= default"><parameter name=""><paramtype><classname>cartesian_topology</classname> const &amp;</paramtype></parameter></constructor>
<copy-assignment cv="= default"><type><classname>cartesian_topology</classname> &amp;</type><parameter name=""><paramtype><classname>cartesian_topology</classname> const &amp;</paramtype></parameter></copy-assignment>
<constructor><parameter name="other"><paramtype><classname>cartesian_topology</classname> &amp;&amp;</paramtype></parameter></constructor>
<copy-assignment><type><classname>cartesian_topology</classname> &amp;</type><parameter name="other"><paramtype><classname>cartesian_topology</classname> &amp;&amp;</paramtype></parameter></copy-assignment>
<destructor/>
<constructor><parameter name="ndim"><paramtype>int</paramtype></parameter><purpose>Create a N dimension space. Each dimension is initialized as non periodic of size 0. </purpose></constructor>
<constructor><parameter name="dims"><paramtype>std::vector&lt; <classname>cartesian_dimension</classname> &gt; const &amp;</paramtype></parameter><purpose>Use the provided dimensions specification as initial values. </purpose></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="InitArr"/>
        </template><parameter name="dims"><paramtype>InitArr</paramtype></parameter><purpose>Use dimensions specification provided in the sequence container as initial values. #param dims must be a sequence container. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="dims"><paramtype>std::initializer_list&lt; <classname>cartesian_dimension</classname> &gt;</paramtype></parameter><purpose>Use dimensions specification provided in the initialization list as initial values. #param dims can be of the form { dim_1, false}, .... {dim_n, true}. </purpose></constructor>
<constructor specifiers="explicit"><template>
          <template-nontype-parameter name="NDIM"><type>int</type></template-nontype-parameter>
        </template><parameter name="dims"><paramtype><classname>cartesian_dimension</classname>(&amp;)</paramtype></parameter><purpose>Use dimensions specification provided in the array. #param dims can be of the form { dim_1, false}, .... {dim_n, true}. </purpose></constructor>
<constructor><template>
          <template-type-parameter name="DimRg"/>
          <template-type-parameter name="PerRg"/>
        </template><parameter name="dim_rg"><paramtype>DimRg const &amp;</paramtype><description><para>the dimensions, values must convert to integers. </para></description></parameter><parameter name="period_rg"><paramtype>PerRg const &amp;</paramtype><description><para>the periodicities, values must convert to booleans. #param dims can be of the form { dim_1, false}, .... {dim_n, true} </para></description></parameter><purpose>Use dimensions specification provided in the input ranges The ranges do not need to be the same size. If the sizes are different, the missing values will be complete with zeros of the dim and assumed non periodic. </purpose><description><para>
</para></description></constructor>
<constructor><template>
          <template-type-parameter name="DimIter"/>
          <template-type-parameter name="PerIter"/>
        </template><parameter name="dit"><paramtype>DimIter</paramtype><description><para>dimension iterator, value must convert to integer type. </para></description></parameter><parameter name="pit"><paramtype>PerIter</paramtype><description><para>periodicity iterator, value must convert to booleans.. </para></description></parameter><parameter name="n"><paramtype>int</paramtype></parameter><purpose>Iterator based initializer. Will use the first n iterated values. Both iterators can be single pass. </purpose><description><para>
</para></description></constructor>
</class><struct-specialization name="is_mpi_datatype"><template>
    </template><specialization><template-arg>cartesian_dimension</template-arg></specialization><inherit access="public">true_</inherit></struct-specialization>

<function name="operator=="><type>bool</type><parameter name="d1"><paramtype><classname>cartesian_dimension</classname> const &amp;</paramtype></parameter><parameter name="d2"><paramtype><classname>cartesian_dimension</classname> const &amp;</paramtype></parameter><purpose>Test if the dimensions values are identical. </purpose></function>
<function name="operator!="><type>bool</type><parameter name="d1"><paramtype><classname>cartesian_dimension</classname> const &amp;</paramtype></parameter><parameter name="d2"><paramtype><classname>cartesian_dimension</classname> const &amp;</paramtype></parameter><purpose>Test if the dimension values are different. </purpose></function>
<function name="operator&lt;&lt;"><type>std::ostream &amp;</type><parameter name="out"><paramtype>std::ostream &amp;</paramtype></parameter><parameter name="d"><paramtype><classname>cartesian_dimension</classname> const &amp;</paramtype></parameter><purpose>Pretty printing of a cartesian dimension (size, periodic) </purpose></function>
<function name="operator=="><type>bool</type><parameter name="t1"><paramtype><classname>cartesian_topology</classname> const &amp;</paramtype></parameter><parameter name="t2"><paramtype><classname>cartesian_topology</classname> const &amp;</paramtype></parameter></function>
<function name="operator!="><type>bool</type><parameter name="t1"><paramtype><classname>cartesian_topology</classname> const &amp;</paramtype></parameter><parameter name="t2"><paramtype><classname>cartesian_topology</classname> const &amp;</paramtype></parameter></function>
<function name="operator&lt;&lt;"><type>std::ostream &amp;</type><parameter name="out"><paramtype>std::ostream &amp;</paramtype></parameter><parameter name="t"><paramtype><classname>cartesian_topology</classname> const &amp;</paramtype></parameter><purpose>Pretty printing of a cartesian topology. </purpose></function>
<function name="cartesian_dimensions"><type>std::vector&lt; int &gt; &amp;</type><parameter name="nb_proc"><paramtype>int</paramtype><description><para>the numer of mpi processes.fill a sequence of dimension. </para></description></parameter><parameter name="dims"><paramtype>std::vector&lt; int &gt; &amp;</paramtype><description><para>a sequence of positive or null dimensions. Non zero dimension will be left untouched. </para></description></parameter><description><para>Given en number of processes, and a partially filled sequence of dimension, try to complete the dimension sequence. 
</para></description></function>

















































































</namespace>
</namespace>
</header>
<header name="boost/mpi/collectives.hpp">
<para>This header contains MPI collective operations, which implement various parallel algorithms that require the coordination of all processes within a communicator. The header <computeroutput>collectives_fwd.hpp</computeroutput> provides forward declarations for each of these operations. To include only specific collective algorithms, use the headers <computeroutput>boost/mpi/collectives/algorithm_name.hpp</computeroutput>. </para><namespace name="boost">
<namespace name="mpi">









<overloaded-function name="all_gather"><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The communicator over which the all-gather will occur.</para></description></parameter><parameter name="in_value"><paramtype>const T &amp;</paramtype><description><para>The value to be transmitted by each process. To gather an array of values, <computeroutput>in_values</computeroutput> points to the <computeroutput>n</computeroutput> local values to be transmitted.</para></description></parameter><parameter name="out_values"><paramtype>std::vector&lt; T &gt; &amp;</paramtype><description><para>A vector or pointer to storage that will be populated with the values from each process, indexed by the process ID number. If it is a vector, the vector will be resized accordingly. </para></description></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_value"><paramtype>const T &amp;</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="out_values"><paramtype>std::vector&lt; T &gt; &amp;</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter></signature><purpose>Gather the values stored at every process into vectors of values from each process. </purpose><description><para><computeroutput>all_gather</computeroutput> is a collective algorithm that collects the values stored at each process into a vector of values indexed by the process number they came from. The type <computeroutput>T</computeroutput> of the values may be any type that is serializable or has an associated MPI data type.</para><para>When the type <computeroutput>T</computeroutput> has an associated MPI data type, this routine invokes <computeroutput>MPI_Allgather</computeroutput> to gather the values.</para><para>
</para></description></overloaded-function>



<overloaded-function name="all_reduce"><signature><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Op"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The communicator over which the reduction will occur. </para></description></parameter><parameter name="value"><paramtype>const T *</paramtype><description><para>The local value to be combined with the local values of every other process. For reducing arrays, <computeroutput>in_values</computeroutput> is a pointer to the local values to be reduced and <computeroutput>n</computeroutput> is the number of values to reduce. See <computeroutput>reduce</computeroutput> for more information.</para></description></parameter><parameter name="n"><paramtype>int</paramtype><description><para>Indicated the size of the buffers of array type. </para></description></parameter><parameter name="out_value"><paramtype>T *</paramtype><description><para>Will receive the result of the reduction operation. If this parameter is omitted, the outgoing value will instead be returned.</para></description></parameter><parameter name="op"><paramtype>Op</paramtype><description><para>The binary operation that combines two values of type <computeroutput>T</computeroutput> and returns a third value of type <computeroutput>T</computeroutput>. For types <computeroutput>T</computeroutput> that has ssociated MPI data types, <computeroutput>op</computeroutput> will either be translated into an <computeroutput>MPI_Op</computeroutput> (via <computeroutput>MPI_Op_create</computeroutput>) or, if possible, mapped directly to a built-in MPI operation. See <computeroutput><classname alt="boost::mpi::is_mpi_op">is_mpi_op</classname></computeroutput> in the <computeroutput>operations.hpp</computeroutput> header for more details on this mapping. For any non-built-in operation, commutativity will be determined by the <computeroutput>is_commmutative</computeroutput> trait (also in <computeroutput>operations.hpp</computeroutput>): users are encouraged to mark commutative operations as such, because it gives the implementation additional lattitude to optimize the reduction operation.</para></description></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Op"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="out_value"><paramtype>T &amp;</paramtype></parameter><parameter name="op"><paramtype>Op</paramtype></parameter></signature><signature><type>T</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Op"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="value"><paramtype>const T &amp;</paramtype></parameter><parameter name="op"><paramtype>Op</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Op"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="value"><paramtype><classname>inplace_t</classname>&lt; T * &gt;</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="op"><paramtype>Op</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Op"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="value"><paramtype><classname>inplace_t</classname>&lt; T &gt;</paramtype></parameter><parameter name="op"><paramtype>Op</paramtype></parameter></signature><purpose>Combine the values stored by each process into a single value available to all processes. </purpose><description><para><computeroutput>all_reduce</computeroutput> is a collective algorithm that combines the values stored by each process into a single value available to all processes. The values are combined in a user-defined way, specified via a function object. The type <computeroutput>T</computeroutput> of the values may be any type that is serializable or has an associated MPI data type. One can think of this operation as a <computeroutput>all_gather</computeroutput>, followed by an <computeroutput>std::accumulate()</computeroutput> over the gather values and using the operation <computeroutput>op</computeroutput>.</para><para>When the type <computeroutput>T</computeroutput> has an associated MPI data type, this routine invokes <computeroutput>MPI_Allreduce</computeroutput> to perform the reduction. If possible, built-in MPI operations will be used; otherwise, <computeroutput>all_reduce()</computeroutput> will create a custom MPI_Op for the call to MPI_Allreduce.</para><para>
If wrapped in a <computeroutput><classname alt="boost::mpi::inplace_t">inplace_t</classname></computeroutput> object, combine the usage of both input and $c out_value and the local value will be overwritten (a convenience function <computeroutput>inplace</computeroutput> is provided for the wrapping).</para><para>

</para></description><returns><para>If no <computeroutput>out_value</computeroutput> parameter is supplied, returns the result of the reduction operation. </para>
</returns></overloaded-function>




<overloaded-function name="all_to_all"><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The communicator over which the all-to-all communication will occur.</para></description></parameter><parameter name="in_values"><paramtype>const std::vector&lt; T &gt; &amp;</paramtype><description><para>A vector or pointer to storage that contains the values to send to each process, indexed by the process ID number.</para></description></parameter><parameter name="out_values"><paramtype>std::vector&lt; T &gt; &amp;</paramtype><description><para>A vector or pointer to storage that will be updated to contain the values received from other processes. The jth value in <computeroutput>out_values</computeroutput> will come from the procss with rank j. </para></description></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const std::vector&lt; T &gt; &amp;</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="out_values"><paramtype>std::vector&lt; T &gt; &amp;</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter></signature><purpose>Send data from every process to every other process. </purpose><description><para><computeroutput>all_to_all</computeroutput> is a collective algorithm that transmits <computeroutput>p</computeroutput> values from every process to every other process. On process i, jth value of the <computeroutput>in_values</computeroutput> vector is sent to process j and placed in the ith position of the <computeroutput>out_values</computeroutput> vector in process <computeroutput>j</computeroutput>. The type <computeroutput>T</computeroutput> of the values may be any type that is serializable or has an associated MPI data type. If <computeroutput>n</computeroutput> is provided, then arrays of <computeroutput>n</computeroutput> values will be transferred from one process to another.</para><para>When the type <computeroutput>T</computeroutput> has an associated MPI data type, this routine invokes <computeroutput>MPI_Alltoall</computeroutput> to scatter the values.</para><para>
</para></description></overloaded-function>



<overloaded-function name="broadcast"><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The communicator over which the broadcast will occur.</para></description></parameter><parameter name="value"><paramtype>T &amp;</paramtype><description><para>The value (or values, if <computeroutput>n</computeroutput> is provided) to be transmitted (if the rank of <computeroutput>comm</computeroutput> is equal to <computeroutput>root</computeroutput>) or received (if the rank of <computeroutput>comm</computeroutput> is not equal to <computeroutput>root</computeroutput>). When the <computeroutput>value</computeroutput> is a <computeroutput><classname alt="boost::mpi::skeleton_proxy">skeleton_proxy</classname></computeroutput>, only the skeleton of the object will be broadcast. In this case, the <computeroutput>root</computeroutput> will build a skeleton from the object help in the proxy and all of the non-roots will reshape the objects held in their proxies based on the skeleton sent from the root.</para></description></parameter><parameter name="root"><paramtype>int</paramtype><description><para>The rank/process ID of the process that will be transmitting the value. </para></description></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="values"><paramtype>T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="value"><paramtype><classname>skeleton_proxy</classname>&lt; T &gt; &amp;</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="value"><paramtype>const <classname>skeleton_proxy</classname>&lt; T &gt; &amp;</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><purpose>Broadcast a value from a root process to all other processes. </purpose><description><para><computeroutput>broadcast</computeroutput> is a collective algorithm that transfers a value from an arbitrary <computeroutput>root</computeroutput> process to every other process that is part of the given communicator. The <computeroutput>broadcast</computeroutput> algorithm can transmit any Serializable value, values that have associated MPI data types, packed archives, skeletons, and the content of skeletons; see the <computeroutput>send</computeroutput> primitive for communicators for a complete list. The type <computeroutput>T</computeroutput> shall be the same for all processes that are a part of the communicator <computeroutput>comm</computeroutput>, unless packed archives are being transferred: with packed archives, the root sends a <computeroutput><classname alt="boost::mpi::packed_oarchive">packed_oarchive</classname></computeroutput> or <computeroutput>packed_skeleton_oarchive</computeroutput> whereas the other processes receive a <computeroutput><classname alt="boost::mpi::packed_iarchive">packed_iarchive</classname></computeroutput> or <computeroutput>packed_skeleton_iarchve</computeroutput>, respectively.</para><para>When the type <computeroutput>T</computeroutput> has an associated MPI data type, this routine invokes <computeroutput>MPI_Bcast</computeroutput> to perform the broadcast.</para><para>
</para></description></overloaded-function>



<overloaded-function name="gather"><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The communicator over which the gather will occur.</para></description></parameter><parameter name="in_value"><paramtype>const T &amp;</paramtype><description><para>The value to be transmitted by each process. For gathering arrays of values, <computeroutput>in_values</computeroutput> points to storage for <computeroutput>n*comm</computeroutput>.size() values.</para></description></parameter><parameter name="out_values"><paramtype>std::vector&lt; T &gt; &amp;</paramtype><description><para>A vector or pointer to storage that will be populated with the values from each process, indexed by the process ID number. If it is a vector, it will be resized accordingly. For non-root processes, this parameter may be omitted. If it is still provided, however, it will be unchanged.</para></description></parameter><parameter name="root"><paramtype>int</paramtype><description><para>The process ID number that will collect the values. This value must be the same on all processes. </para></description></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_value"><paramtype>const T &amp;</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_value"><paramtype>const T &amp;</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="out_values"><paramtype>std::vector&lt; T &gt; &amp;</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><purpose>Gather the values stored at every process into a vector at the root process. </purpose><description><para><computeroutput>gather</computeroutput> is a collective algorithm that collects the values stored at each process into a vector of values at the <computeroutput>root</computeroutput> process. This vector is indexed by the process number that the value came from. The type <computeroutput>T</computeroutput> of the values may be any type that is serializable or has an associated MPI data type.</para><para>When the type <computeroutput>T</computeroutput> has an associated MPI data type, this routine invokes <computeroutput>MPI_Gather</computeroutput> to gather the values.</para><para>
</para></description></overloaded-function>





<overloaded-function name="gatherv"><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The communicator over which the gather will occur.</para></description></parameter><parameter name="in_values"><paramtype>const std::vector&lt; T &gt; &amp;</paramtype><description><para>The array of values to be transmitted by each process.</para></description></parameter><parameter name="out_values"><paramtype>T *</paramtype><description><para>A pointer to storage that will be populated with the values from each process. For non-root processes, this parameter may be omitted. If it is still provided, however, it will be unchanged.</para></description></parameter><parameter name="sizes"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype><description><para>A vector containing the number of elements each non-root process will send.</para></description></parameter><parameter name="displs"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype><description><para>A vector such that the i-th entry specifies the displacement (relative to <computeroutput>out_values</computeroutput>) from which to take the ingoing data at the <computeroutput>root</computeroutput> process. Overloaded versions for which <computeroutput>displs</computeroutput> is omitted assume that the data is to be placed contiguously at the root process.</para></description></parameter><parameter name="root"><paramtype>int</paramtype><description><para>The process ID number that will collect the values. This value must be the same on all processes. </para></description></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="in_size"><paramtype>int</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="sizes"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype></parameter><parameter name="displs"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const std::vector&lt; T &gt; &amp;</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="in_size"><paramtype>int</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="in_size"><paramtype>int</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="sizes"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const std::vector&lt; T &gt; &amp;</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="sizes"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><purpose>Similar to boost::mpi::gather with the difference that the number of values to be send by non-root processes can vary. </purpose><description><para>
</para></description></overloaded-function>





<overloaded-function name="scatter"><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The communicator over which the scatter will occur.</para></description></parameter><parameter name="in_values"><paramtype>const std::vector&lt; T &gt; &amp;</paramtype><description><para>A vector or pointer to storage that will contain the values to send to each process, indexed by the process rank. For non-root processes, this parameter may be omitted. If it is still provided, however, it will be unchanged.</para></description></parameter><parameter name="out_value"><paramtype>T &amp;</paramtype><description><para>The value received by each process. When scattering an array of values, <computeroutput>out_values</computeroutput> points to the <computeroutput>n</computeroutput> values that will be received by each process.</para></description></parameter><parameter name="root"><paramtype>int</paramtype><description><para>The process ID number that will scatter the values. This value must be the same on all processes. </para></description></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="out_value"><paramtype>T &amp;</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="out_value"><paramtype>T &amp;</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const std::vector&lt; T &gt; &amp;</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><purpose>Scatter the values stored at the root to all processes within the communicator. </purpose><description><para><computeroutput>scatter</computeroutput> is a collective algorithm that scatters the values stored in the <computeroutput>root</computeroutput> process (inside a vector) to all of the processes in the communicator. The vector <computeroutput>out_values</computeroutput> (only significant at the <computeroutput>root</computeroutput>) is indexed by the process number to which the corresponding value will be sent. The type <computeroutput>T</computeroutput> of the values may be any type that is serializable or has an associated MPI data type.</para><para>When the type <computeroutput>T</computeroutput> has an associated MPI data type, this routine invokes <computeroutput>MPI_Scatter</computeroutput> to scatter the values.</para><para>
</para></description></overloaded-function>





<overloaded-function name="scatterv"><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The communicator over which the scatter will occur.</para></description></parameter><parameter name="in_values"><paramtype>const std::vector&lt; T &gt; &amp;</paramtype><description><para>A vector or pointer to storage that will contain the values to send to each process, indexed by the process rank. For non-root processes, this parameter may be omitted. If it is still provided, however, it will be unchanged.</para></description></parameter><parameter name="sizes"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype><description><para>A vector containing the number of elements each non-root process will receive.</para></description></parameter><parameter name="displs"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype><description><para>A vector such that the i-th entry specifies the displacement (relative to <computeroutput>in_values</computeroutput>) from which to take the outgoing data to process i. Overloaded versions for which <computeroutput>displs</computeroutput> is omitted assume that the data is contiguous at the <computeroutput>root</computeroutput> process.</para></description></parameter><parameter name="out_values"><paramtype>T *</paramtype><description><para>The array of values received by each process.</para></description></parameter><parameter name="out_size"><paramtype>int</paramtype><description><para>For each non-root process this will contain the size of <computeroutput>out_values</computeroutput>.</para></description></parameter><parameter name="root"><paramtype>int</paramtype><description><para>The process ID number that will scatter the values. This value must be the same on all processes. </para></description></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="sizes"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype></parameter><parameter name="displs"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="out_size"><paramtype>int</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="out_size"><paramtype>int</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="sizes"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const std::vector&lt; T &gt; &amp;</paramtype></parameter><parameter name="sizes"><paramtype>const std::vector&lt; int &gt; &amp;</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><purpose>Similar to boost::mpi::scatter with the difference that the number of values stored at the root process does not need to be a multiple of the communicator's size. </purpose><description><para>
</para></description></overloaded-function>




<overloaded-function name="reduce"><signature><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Op"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The communicator over which the reduction will occur.</para></description></parameter><parameter name="in_value"><paramtype>const T &amp;</paramtype><description><para>The local value to be combined with the local values of every other process. For reducing arrays, <computeroutput>in_values</computeroutput> contains a pointer to the local values. In this case, <computeroutput>n</computeroutput> is the number of values that will be reduced. Reduction occurs independently for each of the <computeroutput>n</computeroutput> values referenced by <computeroutput>in_values</computeroutput>, e.g., calling reduce on an array of <computeroutput>n</computeroutput> values is like calling <computeroutput>reduce</computeroutput> <computeroutput>n</computeroutput> separate times, one for each location in <computeroutput>in_values</computeroutput> and <computeroutput>out_values</computeroutput>.</para></description></parameter><parameter name="out_value"><paramtype>T &amp;</paramtype><description><para>Will receive the result of the reduction operation, but only for the <computeroutput>root</computeroutput> process. Non-root processes may omit if parameter; if they choose to supply the parameter, it will be unchanged. For reducing arrays, <computeroutput>out_values</computeroutput> contains a pointer to the storage for the output values.</para></description></parameter><parameter name="op"><paramtype>Op</paramtype><description><para>The binary operation that combines two values of type <computeroutput>T</computeroutput> into a third value of type <computeroutput>T</computeroutput>. For types <computeroutput>T</computeroutput> that has ssociated MPI data types, <computeroutput>op</computeroutput> will either be translated into an <computeroutput>MPI_Op</computeroutput> (via <computeroutput>MPI_Op_create</computeroutput>) or, if possible, mapped directly to a built-in MPI operation. See <computeroutput><classname alt="boost::mpi::is_mpi_op">is_mpi_op</classname></computeroutput> in the <computeroutput>operations.hpp</computeroutput> header for more details on this mapping. For any non-built-in operation, commutativity will be determined by the <computeroutput>is_commmutative</computeroutput> trait (also in <computeroutput>operations.hpp</computeroutput>): users are encouraged to mark commutative operations as such, because it gives the implementation additional lattitude to optimize the reduction operation.</para></description></parameter><parameter name="root"><paramtype>int</paramtype><description><para>The process ID number that will receive the final, combined value. This value must be the same on all processes. </para></description></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Op"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_value"><paramtype>const T &amp;</paramtype></parameter><parameter name="op"><paramtype>Op</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Op"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="op"><paramtype>Op</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Op"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="op"><paramtype>Op</paramtype></parameter><parameter name="root"><paramtype>int</paramtype></parameter></signature><purpose>Combine the values stored by each process into a single value at the root. </purpose><description><para><computeroutput>reduce</computeroutput> is a collective algorithm that combines the values stored by each process into a single value at the <computeroutput>root</computeroutput>. The values can be combined arbitrarily, specified via a function object. The type <computeroutput>T</computeroutput> of the values may be any type that is serializable or has an associated MPI data type. One can think of this operation as a <computeroutput>gather</computeroutput> to the <computeroutput>root</computeroutput>, followed by an <computeroutput>std::accumulate()</computeroutput> over the gathered values and using the operation <computeroutput>op</computeroutput>.</para><para>When the type <computeroutput>T</computeroutput> has an associated MPI data type, this routine invokes <computeroutput>MPI_Reduce</computeroutput> to perform the reduction. If possible, built-in MPI operations will be used; otherwise, <computeroutput>reduce()</computeroutput> will create a custom MPI_Op for the call to MPI_Reduce.</para><para>
</para></description></overloaded-function>



<overloaded-function name="scan"><signature><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Op"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The communicator over which the prefix reduction will occur.</para></description></parameter><parameter name="in_value"><paramtype>const T &amp;</paramtype><description><para>The local value to be combined with the local values of other processes. For the array variant, the <computeroutput>in_values</computeroutput> parameter points to the <computeroutput>n</computeroutput> local values that will be combined.</para></description></parameter><parameter name="out_value"><paramtype>T &amp;</paramtype><description><para>If provided, the ith process will receive the value <computeroutput>op</computeroutput>(in_value[0], op(in_value[1], op(..., in_value[i]) ... )). For the array variant, <computeroutput>out_values</computeroutput> contains a pointer to storage for the <computeroutput>n</computeroutput> output values. The prefix reduction occurs independently for each of the <computeroutput>n</computeroutput> values referenced by <computeroutput>in_values</computeroutput>, e.g., calling scan on an array of <computeroutput>n</computeroutput> values is like calling <computeroutput>scan</computeroutput> <computeroutput>n</computeroutput> separate times, one for each location in <computeroutput>in_values</computeroutput> and <computeroutput>out_values</computeroutput>.</para></description></parameter><parameter name="op"><paramtype>Op</paramtype><description><para>The binary operation that combines two values of type <computeroutput>T</computeroutput> into a third value of type <computeroutput>T</computeroutput>. For types <computeroutput>T</computeroutput> that has ssociated MPI data types, <computeroutput>op</computeroutput> will either be translated into an <computeroutput>MPI_Op</computeroutput> (via <computeroutput>MPI_Op_create</computeroutput>) or, if possible, mapped directly to a built-in MPI operation. See <computeroutput><classname alt="boost::mpi::is_mpi_op">is_mpi_op</classname></computeroutput> in the <computeroutput>operations.hpp</computeroutput> header for more details on this mapping. For any non-built-in operation, commutativity will be determined by the <computeroutput>is_commmutative</computeroutput> trait (also in <computeroutput>operations.hpp</computeroutput>).</para></description></parameter></signature><signature><type>T</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Op"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_value"><paramtype>const T &amp;</paramtype></parameter><parameter name="op"><paramtype>Op</paramtype></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Op"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="in_values"><paramtype>const T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><parameter name="out_values"><paramtype>T *</paramtype></parameter><parameter name="op"><paramtype>Op</paramtype></parameter></signature><purpose>Compute a prefix reduction of values from all processes in the communicator. </purpose><description><para><computeroutput>scan</computeroutput> is a collective algorithm that combines the values stored by each process with the values of all processes with a smaller rank. The values can be arbitrarily combined, specified via a function object <computeroutput>op</computeroutput>. The type <computeroutput>T</computeroutput> of the values may be any type that is serializable or has an associated MPI data type. One can think of this operation as a <computeroutput>gather</computeroutput> to some process, followed by an <computeroutput>std::prefix_sum()</computeroutput> over the gathered values using the operation <computeroutput>op</computeroutput>. The ith process returns the ith value emitted by <computeroutput>std::prefix_sum()</computeroutput>.</para><para>When the type <computeroutput>T</computeroutput> has an associated MPI data type, this routine invokes <computeroutput>MPI_Scan</computeroutput> to perform the reduction. If possible, built-in MPI operations will be used; otherwise, <computeroutput>scan()</computeroutput> will create a custom <computeroutput>MPI_Op</computeroutput> for the call to MPI_Scan.</para><para>

</para></description><returns><para>If no <computeroutput>out_value</computeroutput> parameter is provided, returns the result of prefix reduction. </para>
</returns></overloaded-function>




































</namespace>
</namespace>
</header>
<header name="boost/mpi/collectives_fwd.hpp">
<para>This header provides forward declarations for all of the collective operations contained in the header <computeroutput>collectives.hpp</computeroutput>. </para></header>
<header name="boost/mpi/communicator.hpp">
<para>This header defines the <computeroutput>communicator</computeroutput> class, which is the basis of all communication within Boost.MPI, and provides point-to-point communication operations. </para><namespace name="boost">
<namespace name="mpi">
<class name="communicator"><purpose>A communicator that permits communication and synchronization among a set of processes. </purpose><description><para>The <computeroutput>communicator</computeroutput> class abstracts a set of communicating processes in MPI. All of the processes that belong to a certain communicator can determine the size of the communicator, their rank within the communicator, and communicate with any other processes in the communicator. </para></description><method-group name="public member functions">
<method name="rank" cv="const"><type>int</type><purpose>Determine the rank of the executing process in a communicator. </purpose><description><para>This routine is equivalent to <computeroutput>MPI_Comm_rank</computeroutput>.</para><para>
</para></description><returns><para>The rank of the process in the communicator, which will be a value in [0, size()) </para>
</returns></method>
<method name="size" cv="const"><type>int</type><purpose>Determine the number of processes in a communicator. </purpose><description><para>This routine is equivalent to <computeroutput>MPI_Comm_size</computeroutput>.</para><para>
</para></description><returns><para>The number of processes in the communicator. </para>
</returns></method>
<method name="group" cv="const"><type><classname>boost::mpi::group</classname></type><description><para>This routine constructs a new group whose members are the processes within this communicator. Equivalent to calling <computeroutput>MPI_Comm_group</computeroutput>. </para></description></method>
<method name="send" cv="const"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="dest"><paramtype>int</paramtype><description><para>The rank of the remote process to which the data will be sent.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that will be associated with this message. Tags may be any integer between zero and an implementation-defined upper limit. This limit is accessible via <computeroutput>environment::max_tag()</computeroutput>.</para></description></parameter><parameter name="value"><paramtype>const T &amp;</paramtype><description><para>The value that will be transmitted to the receiver. The type <computeroutput>T</computeroutput> of this value must meet the aforementioned criteria for transmission. </para></description></parameter><purpose>Send data to another process. </purpose><description><para>This routine executes a potentially blocking send with tag <computeroutput>tag</computeroutput> to the process with rank <computeroutput>dest</computeroutput>. It can be received by the destination process with a matching <computeroutput>recv</computeroutput> call.</para><para>The given <computeroutput>value</computeroutput> must be suitable for transmission over MPI. There are several classes of types that meet these requirements:</para><para><itemizedlist>
<listitem><para>Types with mappings to MPI data types: If <computeroutput>is_mpi_datatype&lt;T&gt;</computeroutput> is convertible to <computeroutput>mpl::true_</computeroutput>, then <computeroutput>value</computeroutput> will be transmitted using the MPI data type <computeroutput>get_mpi_datatype&lt;T&gt;()</computeroutput>. All primitive C++ data types that have MPI equivalents, e.g., <computeroutput>int</computeroutput>, <computeroutput>float</computeroutput>, <computeroutput>char</computeroutput>, <computeroutput>double</computeroutput>, etc., have built-in mappings to MPI data types. You may turn a Serializable type with fixed structure into an MPI data type by specializing <computeroutput><classname alt="boost::mpi::is_mpi_datatype">is_mpi_datatype</classname></computeroutput> for your type.</para>
</listitem><listitem><para>Serializable types: Any type that provides the <computeroutput>serialize()</computeroutput> functionality required by the Boost.Serialization library can be transmitted and received.</para>
</listitem><listitem><para>Packed archives and skeletons: Data that has been packed into an <computeroutput><classname alt="boost::mpi::packed_oarchive">mpi::packed_oarchive</classname></computeroutput> or the skeletons of data that have been backed into an <computeroutput>mpi::packed_skeleton_oarchive</computeroutput> can be transmitted, but will be received as <computeroutput><classname alt="boost::mpi::packed_iarchive">mpi::packed_iarchive</classname></computeroutput> and <computeroutput>mpi::packed_skeleton_iarchive</computeroutput>, respectively, to allow the values (or skeletons) to be extracted by the destination process.</para>
</listitem><listitem><para>Content: Content associated with a previously-transmitted skeleton can be transmitted by <computeroutput>send</computeroutput> and received by <computeroutput>recv</computeroutput>. The receiving process may only receive content into the content of a value that has been constructed with the matching skeleton.</para>
</listitem></itemizedlist>
</para><para>For types that have mappings to an MPI data type (including the concent of a type), an invocation of this routine will result in a single MPI_Send call. For variable-length data, e.g., serialized types and packed archives, two messages will be sent via MPI_Send: one containing the length of the data and the second containing the data itself.</para><para>Std::vectors of MPI data type are considered variable size, e.g. their number of elements is unknown and must be transmited (although the serialization process is skipped). You can use the array specialized versions of communication methods is both sender and receiver know the vector size.</para><para>Note that the transmission mode for variable-length data is an implementation detail that is subject to change.</para><para>
</para></description></method>
<method name="send" cv="const"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="value"><paramtype>const std::vector&lt; T, A &gt; &amp;</paramtype></parameter></method>
<method name="send" cv="const"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="dest"><paramtype>int</paramtype><description><para>The rank of the remote process to which the skeleton will be sent.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that will be associated with this message. Tags may be any integer between zero and an implementation-defined upper limit. This limit is accessible via <computeroutput>environment::max_tag()</computeroutput>.</para></description></parameter><parameter name="proxy"><paramtype>const <classname>skeleton_proxy</classname>&lt; T &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::mpi::skeleton_proxy">skeleton_proxy</classname></computeroutput> containing a reference to the object whose skeleton will be transmitted. </para></description></parameter><purpose>Send the skeleton of an object. </purpose><description><para>This routine executes a potentially blocking send with tag <computeroutput>tag</computeroutput> to the process with rank <computeroutput>dest</computeroutput>. It can be received by the destination process with a matching <computeroutput>recv</computeroutput> call. This variation on <computeroutput>send</computeroutput> will be used when a send of a skeleton is explicitly requested via code such as:</para><para><programlisting language="c++">comm.send(dest, tag, skeleton(object));
</programlisting></para><para>The semantics of this routine are equivalent to that of sending a <computeroutput>packed_skeleton_oarchive</computeroutput> storing the skeleton of the <computeroutput>object</computeroutput>.</para><para>
</para></description></method>
<method name="send" cv="const"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="dest"><paramtype>int</paramtype><description><para>The process rank of the remote process to which the data will be sent.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that will be associated with this message. Tags may be any integer between zero and an implementation-defined upper limit. This limit is accessible via <computeroutput>environment::max_tag()</computeroutput>.</para></description></parameter><parameter name="values"><paramtype>const T *</paramtype><description><para>The array of values that will be transmitted to the receiver. The type <computeroutput>T</computeroutput> of these values must be mapped to an MPI data type.</para></description></parameter><parameter name="n"><paramtype>int</paramtype><description><para>The number of values stored in the array. The destination process must call receive with at least this many elements to correctly receive the message. </para></description></parameter><purpose>Send an array of values to another process. </purpose><description><para>This routine executes a potentially blocking send of an array of data with tag <computeroutput>tag</computeroutput> to the process with rank <computeroutput>dest</computeroutput>. It can be received by the destination process with a matching array <computeroutput>recv</computeroutput> call.</para><para>If <computeroutput>T</computeroutput> is an MPI datatype, an invocation of this routine will be mapped to a single call to MPI_Send, using the datatype <computeroutput>get_mpi_datatype&lt;T&gt;()</computeroutput>.</para><para>
</para></description></method>
<method name="send" cv="const"><type>void</type><parameter name="dest"><paramtype>int</paramtype><description><para>The process rank of the remote process to which the message will be sent.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that will be associated with this message. Tags may be any integer between zero and an implementation-defined upper limit. This limit is accessible via <computeroutput>environment::max_tag()</computeroutput>. </para></description></parameter><purpose>Send a message to another process without any data. </purpose><description><para>This routine executes a potentially blocking send of a message to another process. The message contains no extra data, and can therefore only be received by a matching call to <computeroutput>recv()</computeroutput>.</para><para>
</para></description></method>
<method name="recv" cv="const"><type><classname>status</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="source"><paramtype>int</paramtype><description><para>The process that will be sending data. This will either be a process rank within the communicator or the constant <computeroutput>any_source</computeroutput>, indicating that we can receive the message from any process.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that matches a particular kind of message sent by the source process. This may be any tag value permitted by <computeroutput>send</computeroutput>. Alternatively, the argument may be the constant <computeroutput>any_tag</computeroutput>, indicating that this receive matches a message with any tag.</para></description></parameter><parameter name="value"><paramtype>T &amp;</paramtype><description><para>Will contain the value of the message after a successful receive. The type of this value must match the value transmitted by the sender, unless the sender transmitted a packed archive or skeleton: in these cases, the sender transmits a <computeroutput><classname alt="boost::mpi::packed_oarchive">packed_oarchive</classname></computeroutput> or <computeroutput>packed_skeleton_oarchive</computeroutput> and the destination receives a <computeroutput><classname alt="boost::mpi::packed_iarchive">packed_iarchive</classname></computeroutput> or <computeroutput>packed_skeleton_iarchive</computeroutput>, respectively.</para></description></parameter><purpose>Receive data from a remote process. </purpose><description><para>This routine blocks until it receives a message from the process <computeroutput>source</computeroutput> with the given <computeroutput>tag</computeroutput>. The type <computeroutput>T</computeroutput> of the <computeroutput>value</computeroutput> must be suitable for transmission over MPI, which includes serializable types, types that can be mapped to MPI data types (including most built-in C++ types), packed MPI archives, skeletons, and content associated with skeletons; see the documentation of <computeroutput>send</computeroutput> for a complete description.</para><para>

</para></description><returns><para>Information about the received message. </para>
</returns></method>
<method name="recv" cv="const"><type><classname>status</classname></type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="source"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="value"><paramtype>std::vector&lt; T, A &gt; &amp;</paramtype></parameter></method>
<method name="recv" cv="const"><type><classname>status</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="source"><paramtype>int</paramtype><description><para>The process that will be sending data. This will either be a process rank within the communicator or the constant <computeroutput>any_source</computeroutput>, indicating that we can receive the message from any process.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that matches a particular kind of message sent by the source process. This may be any tag value permitted by <computeroutput>send</computeroutput>. Alternatively, the argument may be the constant <computeroutput>any_tag</computeroutput>, indicating that this receive matches a message with any tag.</para></description></parameter><parameter name="proxy"><paramtype>const <classname>skeleton_proxy</classname>&lt; T &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::mpi::skeleton_proxy">skeleton_proxy</classname></computeroutput> containing a reference to the object that will be reshaped to match the received skeleton.</para></description></parameter><purpose>Receive a skeleton from a remote process. </purpose><description><para>This routine blocks until it receives a message from the process <computeroutput>source</computeroutput> with the given <computeroutput>tag</computeroutput> containing a skeleton.</para><para>

</para></description><returns><para>Information about the received message. </para>
</returns></method>
<method name="recv" cv="const"><type><classname>status</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="source"><paramtype>int</paramtype><description><para>The process that will be sending data. This will either be a process rank within the communicator or the constant <computeroutput>any_source</computeroutput>, indicating that we can receive the message from any process.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that matches a particular kind of message sent by the source process. This may be any tag value permitted by <computeroutput>send</computeroutput>. Alternatively, the argument may be the constant <computeroutput>any_tag</computeroutput>, indicating that this receive matches a message with any tag.</para></description></parameter><parameter name="proxy"><paramtype><classname>skeleton_proxy</classname>&lt; T &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::mpi::skeleton_proxy">skeleton_proxy</classname></computeroutput> containing a reference to the object that will be reshaped to match the received skeleton.</para></description></parameter><purpose>Receive a skeleton from a remote process. </purpose><description><para>This routine blocks until it receives a message from the process <computeroutput>source</computeroutput> with the given <computeroutput>tag</computeroutput> containing a skeleton.</para><para>

</para></description><returns><para>Information about the received message. </para>
</returns></method>
<method name="recv" cv="const"><type><classname>status</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="source"><paramtype>int</paramtype><description><para>The process that will be sending data. This will either be a process rank within the communicator or the constant <computeroutput>any_source</computeroutput>, indicating that we can receive the message from any process.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that matches a particular kind of message sent by the source process. This may be any tag value permitted by <computeroutput>send</computeroutput>. Alternatively, the argument may be the constant <computeroutput>any_tag</computeroutput>, indicating that this receive matches a message with any tag.</para></description></parameter><parameter name="values"><paramtype>T *</paramtype><description><para>Will contain the values in the message after a successful receive. The type of these elements must match the type of the elements transmitted by the sender.</para></description></parameter><parameter name="n"><paramtype>int</paramtype><description><para>The number of values that can be stored into the <computeroutput>values</computeroutput> array. This shall not be smaller than the number of elements transmitted by the sender.</para></description></parameter><purpose>Receive an array of values from a remote process. </purpose><description><para>This routine blocks until it receives an array of values from the process <computeroutput>source</computeroutput> with the given <computeroutput>tag</computeroutput>. If the type <computeroutput>T</computeroutput> is</para><para>


</para></description><returns><para>Information about the received message. </para>
</returns><throws><simpara><classname>std::range_error</classname> if the message to be received contains more than <computeroutput>n</computeroutput> values.</simpara></throws></method>
<method name="recv" cv="const"><type><classname>status</classname></type><parameter name="source"><paramtype>int</paramtype><description><para>The process that will be sending the message. This will either be a process rank within the communicator or the constant <computeroutput>any_source</computeroutput>, indicating that we can receive the message from any process.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that matches a particular kind of message sent by the source process. This may be any tag value permitted by <computeroutput>send</computeroutput>. Alternatively, the argument may be the constant <computeroutput>any_tag</computeroutput>, indicating that this receive matches a message with any tag.</para></description></parameter><purpose>Receive a message from a remote process without any data. </purpose><description><para>This routine blocks until it receives a message from the process <computeroutput>source</computeroutput> with the given <computeroutput>tag</computeroutput>.</para><para>

</para></description><returns><para>Information about the received message. </para>
</returns></method>
<method name="sendrecv" cv="const"><type><classname>status</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="stag"><paramtype>int</paramtype></parameter><parameter name="sval"><paramtype>const T &amp;</paramtype></parameter><parameter name="src"><paramtype>int</paramtype></parameter><parameter name="rtag"><paramtype>int</paramtype></parameter><parameter name="rval"><paramtype>T &amp;</paramtype></parameter><purpose>Send a message to remote process and receive another message from another process. </purpose></method>
<method name="isend" cv="const"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="dest"><paramtype>int</paramtype><description><para>The rank of the remote process to which the data will be sent.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that will be associated with this message. Tags may be any integer between zero and an implementation-defined upper limit. This limit is accessible via <computeroutput>environment::max_tag()</computeroutput>.</para></description></parameter><parameter name="value"><paramtype>const T &amp;</paramtype><description><para>The value that will be transmitted to the receiver. The type <computeroutput>T</computeroutput> of this value must meet the aforementioned criteria for transmission. If modified before transmited, the modification may or may not be transmited.</para></description></parameter><purpose>Send a message to a remote process without blocking. </purpose><description><para>The <computeroutput>isend</computeroutput> method is functionality identical to the <computeroutput>send</computeroutput> method and transmits data in the same way, except that <computeroutput>isend</computeroutput> will not block while waiting for the data to be transmitted. Instead, a request object will be immediately returned, allowing one to query the status of the communication or wait until it has completed.</para><para>

</para></description><returns><para>a <computeroutput>request</computeroutput> object that describes this communication. </para>
</returns></method>
<method name="isend" cv="const"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="dest"><paramtype>int</paramtype><description><para>The rank of the remote process to which the skeleton will be sent.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that will be associated with this message. Tags may be any integer between zero and an implementation-defined upper limit. This limit is accessible via <computeroutput>environment::max_tag()</computeroutput>.</para></description></parameter><parameter name="proxy"><paramtype>const <classname>skeleton_proxy</classname>&lt; T &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::mpi::skeleton_proxy">skeleton_proxy</classname></computeroutput> containing a reference to the object whose skeleton will be transmitted.</para></description></parameter><purpose>Send the skeleton of an object without blocking. </purpose><description><para>This routine is functionally identical to the <computeroutput>send</computeroutput> method for <computeroutput><classname alt="boost::mpi::skeleton_proxy">skeleton_proxy</classname></computeroutput> objects except that <computeroutput>isend</computeroutput> will not block while waiting for the data to be transmitted. Instead, a request object will be immediately returned, allowing one to query the status of the communication or wait until it has completed.</para><para>The semantics of this routine are equivalent to a non-blocking send of a <computeroutput>packed_skeleton_oarchive</computeroutput> storing the skeleton of the <computeroutput>object</computeroutput>.</para><para>

</para></description><returns><para>a <computeroutput>request</computeroutput> object that describes this communication. </para>
</returns></method>
<method name="isend" cv="const"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="dest"><paramtype>int</paramtype><description><para>The process rank of the remote process to which the data will be sent.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that will be associated with this message. Tags may be any integer between zero and an implementation-defined upper limit. This limit is accessible via <computeroutput>environment::max_tag()</computeroutput>.</para></description></parameter><parameter name="values"><paramtype>const T *</paramtype><description><para>The array of values that will be transmitted to the receiver. The type <computeroutput>T</computeroutput> of these values must be mapped to an MPI data type.</para></description></parameter><parameter name="n"><paramtype>int</paramtype><description><para>The number of values stored in the array. The destination process must call receive with at least this many elements to correctly receive the message.</para></description></parameter><purpose>Send an array of values to another process without blocking. </purpose><description><para>This routine is functionally identical to the <computeroutput>send</computeroutput> method for arrays except that <computeroutput>isend</computeroutput> will not block while waiting for the data to be transmitted. Instead, a request object will be immediately returned, allowing one to query the status of the communication or wait until it has completed.</para><para>

</para></description><returns><para>a <computeroutput>request</computeroutput> object that describes this communication. </para>
</returns></method>
<method name="isend" cv="const"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="values"><paramtype>const std::vector&lt; T, A &gt; &amp;</paramtype></parameter></method>
<method name="isend" cv="const"><type><classname>request</classname></type><parameter name="dest"><paramtype>int</paramtype><description><para>The process rank of the remote process to which the message will be sent.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that will be associated with this message. Tags may be any integer between zero and an implementation-defined upper limit. This limit is accessible via <computeroutput>environment::max_tag()</computeroutput>.</para></description></parameter><purpose>Send a message to another process without any data without blocking. </purpose><description><para>This routine is functionally identical to the <computeroutput>send</computeroutput> method for sends with no data, except that <computeroutput>isend</computeroutput> will not block while waiting for the message to be transmitted. Instead, a request object will be immediately returned, allowing one to query the status of the communication or wait until it has completed.</para><para>

</para></description><returns><para>a <computeroutput>request</computeroutput> object that describes this communication. </para>
</returns></method>
<method name="irecv" cv="const"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="source"><paramtype>int</paramtype><description><para>The process that will be sending data. This will either be a process rank within the communicator or the constant <computeroutput>any_source</computeroutput>, indicating that we can receive the message from any process.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that matches a particular kind of message sent by the source process. This may be any tag value permitted by <computeroutput>send</computeroutput>. Alternatively, the argument may be the constant <computeroutput>any_tag</computeroutput>, indicating that this receive matches a message with any tag.</para></description></parameter><parameter name="value"><paramtype>T &amp;</paramtype><description><para>Will contain the value of the message after a successful receive. The type of this value must match the value transmitted by the sender, unless the sender transmitted a packed archive or skeleton: in these cases, the sender transmits a <computeroutput><classname alt="boost::mpi::packed_oarchive">packed_oarchive</classname></computeroutput> or <computeroutput>packed_skeleton_oarchive</computeroutput> and the destination receives a <computeroutput><classname alt="boost::mpi::packed_iarchive">packed_iarchive</classname></computeroutput> or <computeroutput>packed_skeleton_iarchive</computeroutput>, respectively.</para></description></parameter><purpose>Prepare to receive a message from a remote process. </purpose><description><para>The <computeroutput>irecv</computeroutput> method is functionally identical to the <computeroutput>recv</computeroutput> method and receive data in the same way, except that <computeroutput>irecv</computeroutput> will not block while waiting for data to be transmitted. Instead, it immediately returns a request object that allows one to query the status of the receive or wait until it has completed.</para><para>

</para></description><returns><para>a <computeroutput>request</computeroutput> object that describes this communication. </para>
</returns></method>
<method name="irecv" cv="const"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="source"><paramtype>int</paramtype><description><para>The process that will be sending data. This will either be a process rank within the communicator or the constant <computeroutput>any_source</computeroutput>, indicating that we can receive the message from any process.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that matches a particular kind of message sent by the source process. This may be any tag value permitted by <computeroutput>send</computeroutput>. Alternatively, the argument may be the constant <computeroutput>any_tag</computeroutput>, indicating that this receive matches a message with any tag.</para></description></parameter><parameter name="values"><paramtype>T *</paramtype><description><para>Will contain the values in the message after a successful receive. The type of these elements must match the type of the elements transmitted by the sender.</para></description></parameter><parameter name="n"><paramtype>int</paramtype><description><para>The number of values that can be stored into the <computeroutput>values</computeroutput> array. This shall not be smaller than the number of elements transmitted by the sender.</para></description></parameter><purpose>Initiate receipt of an array of values from a remote process. </purpose><description><para>This routine initiates a receive operation for an array of values transmitted by process <computeroutput>source</computeroutput> with the given <computeroutput>tag</computeroutput>.</para><para>

</para></description><returns><para>a <computeroutput>request</computeroutput> object that describes this communication. </para>
</returns></method>
<method name="irecv" cv="const"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="source"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="values"><paramtype>std::vector&lt; T, A &gt; &amp;</paramtype></parameter></method>
<method name="irecv" cv="const"><type><classname>request</classname></type><parameter name="source"><paramtype>int</paramtype><description><para>The process that will be sending the message. This will either be a process rank within the communicator or the constant <computeroutput>any_source</computeroutput>, indicating that we can receive the message from any process.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><description><para>The tag that matches a particular kind of message sent by the source process. This may be any tag value permitted by <computeroutput>send</computeroutput>. Alternatively, the argument may be the constant <computeroutput>any_tag</computeroutput>, indicating that this receive matches a message with any tag.</para></description></parameter><purpose>Initiate receipt of a message from a remote process that carries no data. </purpose><description><para>This routine initiates a receive operation for a message from process <computeroutput>source</computeroutput> with the given <computeroutput>tag</computeroutput> that carries no data.</para><para>

</para></description><returns><para>a <computeroutput>request</computeroutput> object that describes this communication. </para>
</returns></method>
<method name="probe" cv="const"><type><classname>status</classname></type><parameter name="source"><paramtype>int</paramtype><default>any_source</default><description><para>Determine if there is a message available from this rank. If <computeroutput>any_source</computeroutput>, then the message returned may come from any source.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><default>any_tag</default><description><para>Determine if there is a message available with the given tag. If <computeroutput>any_tag</computeroutput>, then the message returned may have any tag.</para></description></parameter><purpose>Waits until a message is available to be received. </purpose><description><para>This operation waits until a message matching (<computeroutput>source</computeroutput>, <computeroutput>tag</computeroutput>) is available to be received. It then returns information about that message. The functionality is equivalent to <computeroutput>MPI_Probe</computeroutput>. To check if a message is available without blocking, use <computeroutput>iprobe</computeroutput>.</para><para>

</para></description><returns><para>Returns information about the first message that matches the given criteria. </para>
</returns></method>
<method name="iprobe" cv="const"><type>optional&lt; <classname>status</classname> &gt;</type><parameter name="source"><paramtype>int</paramtype><default>any_source</default><description><para>Determine if there is a message available from this rank. If <computeroutput>any_source</computeroutput>, then the message returned may come from any source.</para></description></parameter><parameter name="tag"><paramtype>int</paramtype><default>any_tag</default><description><para>Determine if there is a message available with the given tag. If <computeroutput>any_tag</computeroutput>, then the message returned may have any tag.</para></description></parameter><purpose>Determine if a message is available to be received. </purpose><description><para>This operation determines if a message matching (<computeroutput>source</computeroutput>, <computeroutput>tag</computeroutput>) is available to be received. If so, it returns information about that message; otherwise, it returns immediately with an empty optional. The functionality is equivalent to <computeroutput>MPI_Iprobe</computeroutput>. To wait until a message is available, use <computeroutput>wait</computeroutput>.</para><para>

</para></description><returns><para>If a matching message is available, returns information about that message. Otherwise, returns an empty <computeroutput>boost::optional</computeroutput>. </para>
</returns></method>
<method name="barrier" cv="const"><type>void</type><purpose>Wait for all processes within a communicator to reach the barrier. </purpose><description><para>This routine is a collective operation that blocks each process until all processes have entered it, then releases all of the processes "simultaneously". It is equivalent to <computeroutput>MPI_Barrier</computeroutput>. </para></description></method>
<method name="conversion-operator" cv="const"><type>bool</type><purpose>Determine if this communicator is valid for communication. </purpose><description><para>Evaluates <computeroutput>true</computeroutput> in a boolean context if this communicator is valid for communication, i.e., does not represent MPI_COMM_NULL. Otherwise, evaluates <computeroutput>false</computeroutput>. </para></description></method>
<method name="conversion-operator" cv="const"><type>MPI_Comm</type><purpose>Access the MPI communicator associated with a Boost.MPI communicator. </purpose><description><para>This routine permits the implicit conversion from a Boost.MPI communicator to an MPI communicator.</para><para>
</para></description><returns><para>The associated MPI communicator. </para>
</returns></method>
<method name="split" cv="const"><type><classname>communicator</classname></type><parameter name="color"><paramtype>int</paramtype><description><para>The color of this process. All processes with the same <computeroutput>color</computeroutput> value will be placed into the same group.</para></description></parameter><parameter name="key"><paramtype>int</paramtype><description><para>A key value that will be used to determine the ordering of processes with the same color in the resulting communicator. If omitted, the rank of the processes in <computeroutput>this</computeroutput> will determine the ordering of processes in the resulting group.</para></description></parameter><description><para>Split the communicator into multiple, disjoint communicators each of which is based on a particular color. This is a collective operation that returns a new communicator that is a subgroup of <computeroutput>this</computeroutput>.</para><para>

</para></description><returns><para>A new communicator containing all of the processes in <computeroutput>this</computeroutput> that have the same <computeroutput>color</computeroutput>. </para>
</returns></method>
<method name="split" cv="const"><type><classname>communicator</classname></type><parameter name="color"><paramtype>int</paramtype></parameter></method>
<method name="as_intercommunicator" cv="const"><type>optional&lt; <classname>intercommunicator</classname> &gt;</type><description><para>Determine if the communicator is in fact an intercommunicator and, if so, return that intercommunicator.</para><para>
</para></description><returns><para>an <computeroutput>optional</computeroutput> containing the intercommunicator, if this communicator is in fact an intercommunicator. Otherwise, returns an empty <computeroutput>optional</computeroutput>. </para>
</returns></method>
<method name="as_graph_communicator" cv="const"><type>optional&lt; <classname>graph_communicator</classname> &gt;</type><description><para>Determine if the communicator has a graph topology and, if so, return that <computeroutput><classname alt="boost::mpi::graph_communicator">graph_communicator</classname></computeroutput>. Even though the communicators have different types, they refer to the same underlying communication space and can be used interchangeably for communication.</para><para>
</para></description><returns><para>an <computeroutput>optional</computeroutput> containing the graph communicator, if this communicator does in fact have a graph topology. Otherwise, returns an empty <computeroutput>optional</computeroutput>. </para>
</returns></method>
<method name="has_graph_topology" cv="const"><type>bool</type><description><para>Determines whether this communicator has a Graph topology. </para></description></method>
<method name="as_cartesian_communicator" cv="const"><type>optional&lt; <classname>cartesian_communicator</classname> &gt;</type><description><para>Determine if the communicator has a cartesian topology and, if so, return that <computeroutput><classname alt="boost::mpi::cartesian_communicator">cartesian_communicator</classname></computeroutput>. Even though the communicators have different types, they refer to the same underlying communication space and can be used interchangeably for communication.</para><para>
</para></description><returns><para>an <computeroutput>optional</computeroutput> containing the cartesian communicator, if this communicator does in fact have a cartesian topology. Otherwise, returns an empty <computeroutput>optional</computeroutput>. </para>
</returns></method>
<method name="has_cartesian_topology" cv="const"><type>bool</type><description><para>Determines whether this communicator has a Cartesian topology. </para></description></method>
<method name="abort" cv="const"><type>void</type><parameter name="errcode"><paramtype>int</paramtype><description><para>The error code to return from aborted processes. </para></description></parameter><description><para>Abort all tasks in the group of this communicator.</para><para>Makes a "best attempt" to abort all of the tasks in the group of this communicator. Depending on the underlying MPI implementation, this may either abort the entire program (and possibly return <computeroutput>errcode</computeroutput> to the environment) or only abort some processes, allowing the others to continue. Consult the documentation for your MPI implementation. This is equivalent to a call to <computeroutput>MPI_Abort</computeroutput> </para><para>

</para></description><returns><para>Will not return. </para>
</returns></method>
</method-group>
<constructor><description><para>Build a new Boost.MPI communicator for <computeroutput>MPI_COMM_WORLD</computeroutput>.</para><para>Constructs a Boost.MPI communicator that attaches to <computeroutput>MPI_COMM_WORLD</computeroutput>. This is the equivalent of constructing with <computeroutput/>(MPI_COMM_WORLD, comm_attach). </para></description></constructor>
<constructor><parameter name="comm"><paramtype>const MPI_Comm &amp;</paramtype></parameter><parameter name="kind"><paramtype>comm_create_kind</paramtype></parameter><description><para>Build a new Boost.MPI communicator based on the MPI communicator <computeroutput>comm</computeroutput>.</para><para><computeroutput>comm</computeroutput> may be any valid MPI communicator. If <computeroutput>comm</computeroutput> is MPI_COMM_NULL, an empty communicator (that cannot be used for communication) is created and the <computeroutput>kind</computeroutput> parameter is ignored. Otherwise, the <computeroutput>kind</computeroutput> parameters determines how the Boost.MPI communicator will be related to <computeroutput>comm:</computeroutput> </para><para><itemizedlist>
<listitem><para>If <computeroutput>kind</computeroutput> is <computeroutput>comm_duplicate</computeroutput>, duplicate <computeroutput>comm</computeroutput> to create a new communicator. This new communicator will be freed when the Boost.MPI communicator (and all copies of it) is destroyed. This option is only permitted if <computeroutput>comm</computeroutput> is a valid MPI intracommunicator or if the underlying MPI implementation supports MPI 2.0 (which supports duplication of intercommunicators).</para>
</listitem><listitem><para>If <computeroutput>kind</computeroutput> is <computeroutput>comm_take_ownership</computeroutput>, take ownership of <computeroutput>comm</computeroutput>. It will be freed automatically when all of the Boost.MPI communicators go out of scope. This option must not be used when <computeroutput>comm</computeroutput> is MPI_COMM_WORLD.</para>
</listitem><listitem><para>If <computeroutput>kind</computeroutput> is <computeroutput>comm_attach</computeroutput>, this Boost.MPI communicator will reference the existing MPI communicator <computeroutput>comm</computeroutput> but will not free <computeroutput>comm</computeroutput> when the Boost.MPI communicator goes out of scope. This option should only be used when the communicator is managed by the user or MPI library (e.g., MPI_COMM_WORLD). </para>
</listitem></itemizedlist>
</para></description></constructor>
<constructor><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>An MPI communicator.</para></description></parameter><parameter name="subgroup"><paramtype>const <classname>boost::mpi::group</classname> &amp;</paramtype><description><para>A subgroup of the MPI communicator, <computeroutput>comm</computeroutput>, for which we will construct a new communicator. </para></description></parameter><description><para>Build a new Boost.MPI communicator based on a subgroup of another MPI communicator.</para><para>This routine will construct a new communicator containing all of the processes from communicator <computeroutput>comm</computeroutput> that are listed within the group <computeroutput>subgroup</computeroutput>. Equivalent to <computeroutput>MPI_Comm_create</computeroutput>.</para><para>
</para></description></constructor>
<method-group name="protected member functions">
<method name="irecv_vector" cv="const"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="source"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="values"><paramtype>std::vector&lt; T, A &gt; &amp;</paramtype></parameter><parameter name=""><paramtype>mpl::true_</paramtype></parameter></method>
<method name="isend_vector" cv="const"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="values"><paramtype>const std::vector&lt; T, A &gt; &amp;</paramtype></parameter><parameter name=""><paramtype>mpl::true_</paramtype></parameter></method>
<method name="send_vector" cv="const"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="value"><paramtype>const std::vector&lt; T, A &gt; &amp;</paramtype></parameter><parameter name=""><paramtype>mpl::true_</paramtype></parameter></method>
<method name="recv_vector" cv="const"><type><classname>status</classname></type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="source"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="value"><paramtype>std::vector&lt; T, A &gt; &amp;</paramtype></parameter><parameter name=""><paramtype>mpl::true_</paramtype></parameter></method>
<method name="irecv_vector" cv="const"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="source"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="values"><paramtype>std::vector&lt; T, A &gt; &amp;</paramtype></parameter><parameter name=""><paramtype>mpl::false_</paramtype></parameter></method>
<method name="isend_vector" cv="const"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="values"><paramtype>const std::vector&lt; T, A &gt; &amp;</paramtype></parameter><parameter name=""><paramtype>mpl::false_</paramtype></parameter></method>
<method name="send_vector" cv="const"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="value"><paramtype>const std::vector&lt; T, A &gt; &amp;</paramtype></parameter><parameter name=""><paramtype>mpl::false_</paramtype></parameter></method>
<method name="recv_vector" cv="const"><type><classname>status</classname></type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="source"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="value"><paramtype>std::vector&lt; T, A &gt; &amp;</paramtype></parameter><parameter name=""><paramtype>mpl::false_</paramtype></parameter></method>
</method-group>
</class><enum name="comm_create_kind"><enumvalue name="comm_duplicate"/><enumvalue name="comm_take_ownership"/><enumvalue name="comm_attach"/><purpose>Enumeration used to describe how to adopt a C <computeroutput>MPI_Comm</computeroutput> into a Boost.MPI communicator. </purpose><description><para>The values for this enumeration determine how a Boost.MPI communicator will behave when constructed with an MPI communicator. The options are:</para><para><itemizedlist>
<listitem><para><computeroutput>comm_duplicate:</computeroutput> Duplicate the MPI_Comm communicator to create a new communicator (e.g., with MPI_Comm_dup). This new MPI_Comm communicator will be automatically freed when the Boost.MPI communicator (and all copies of it) is destroyed.</para>
</listitem><listitem><para><computeroutput>comm_take_ownership:</computeroutput> Take ownership of the communicator. It will be freed automatically when all of the Boost.MPI communicators go out of scope. This option must not be used with MPI_COMM_WORLD.</para>
</listitem><listitem><para><computeroutput>comm_attach:</computeroutput> The Boost.MPI communicator will reference the existing MPI communicator but will not free it when the Boost.MPI communicator goes out of scope. This option should only be used when the communicator is managed by the user or MPI library (e.g., MPI_COMM_WORLD). </para>
</listitem></itemizedlist>
</para></description></enum>
<data-member name="any_source"><type>const int</type><purpose>A constant representing "any process.". </purpose><description><para>This constant may be used for the <computeroutput>source</computeroutput> parameter of <computeroutput>receive</computeroutput> operations to indicate that a message may be received from any source. </para></description></data-member>
<data-member name="any_tag"><type>const int</type><purpose>A constant representing "any tag.". </purpose><description><para>This constant may be used for the <computeroutput>tag</computeroutput> parameter of <computeroutput>receive</computeroutput> operations to indicate that a <computeroutput>send</computeroutput> with any tag will be matched by the receive. </para></description></data-member>
























































<function name="operator=="><type>BOOST_MPI_DECL bool</type><parameter name="comm1"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="comm2"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><purpose>Determines whether two communicators are identical. </purpose><description><para>Equivalent to calling <computeroutput>MPI_Comm_compare</computeroutput> and checking whether the result is <computeroutput>MPI_IDENT</computeroutput>.</para><para>
</para></description><returns><para>True when the two communicators refer to the same underlying MPI communicator. </para>
</returns></function>
<function name="operator!="><type>bool</type><parameter name="comm1"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><parameter name="comm2"><paramtype>const <classname>communicator</classname> &amp;</paramtype></parameter><purpose>Determines whether two communicators are different. </purpose><description><para>
</para></description><returns><para><computeroutput>!</computeroutput>(comm1 == comm2) </para>
</returns></function>
































</namespace>
</namespace>
</header>
<header name="boost/mpi/config.hpp">
<para>This header provides MPI configuration details that expose the capabilities of the underlying MPI implementation, and provides auto-linking support on Windows. </para><macro name="MPICH_IGNORE_CXX_SEEK"/>
<macro name="OMPI_SKIP_MPICXX"/>
<macro name="BOOST_MPI_HOMOGENEOUS"><purpose>Comment this macro is you are running in an heterogeneous environment. </purpose><description><para>When this flag is enabled, we assume some simple, POD-like, type can be transmitted without paying the cost of portable serialization.</para><para>Comment this if your platform is not homogeneous and that portable serialization/deserialization must be performed.</para><para>It you do so, check that your MPI implementation supports thats kind of environment. </para></description></macro>
<macro name="BOOST_MPI_VERSION"><purpose>Major version of the underlying MPI implementation supproted standard. </purpose><description><para>If, for some reason, MPI_VERSION is not supported, you should probably set that according to your MPI documentation </para></description></macro>
<macro name="BOOST_MPI_SUBVERSION"/>
<macro name="BOOST_MPI_HAS_MEMORY_ALLOCATION"><purpose>Determine if the MPI implementation has support for memory allocation. </purpose><description><para>This macro will be defined when the underlying MPI implementation has support for the MPI-2 memory allocation routines <computeroutput>MPI_Alloc_mem</computeroutput> and <computeroutput>MPI_Free_mem</computeroutput>. When defined, the <computeroutput>allocator</computeroutput> class template will provide Standard Library-compliant access to these memory-allocation routines. </para></description></macro>
<macro name="BOOST_MPI_HAS_NOARG_INITIALIZATION"><purpose>Determine if the MPI implementation has supports initialization without command-line arguments. </purpose><description><para>This macro will be defined when the underlying implementation supports initialization of MPI without passing along command-line arguments, e.g., <computeroutput>MPI_Init(NULL, NULL)</computeroutput>. When defined, the <computeroutput>environment</computeroutput> class will provide a default constructor. This macro is always defined for MPI-2 implementations. </para></description></macro>
<macro name="BOOST_MPI_CALLING_CONVENTION"><purpose>Specifies the calling convention that will be used for callbacks from the underlying C MPI. </purpose><description><para>This is a Windows-specific macro, which will be used internally to state the calling convention of any function that is to be used as a callback from MPI. For example, the internally-defined functions that are used in a call to <computeroutput>MPI_Op_create</computeroutput>. This macro is likely only to be useful to users that wish to bypass Boost.MPI, registering their own callbacks in certain cases, e.g., through <computeroutput>MPI_Op_create</computeroutput>. </para></description></macro>
<macro name="BOOST_MPI_BCAST_BOTTOM_WORKS_FINE"><purpose>Indicates that MPI_Bcast supports MPI_BOTTOM. </purpose><description><para>Some implementations have a broken MPI_Bcast wrt to MPI_BOTTOM. BullX MPI and LAM seems to be among them, at least for some versions. The <computeroutput>broacast_test.cpp</computeroutput> test <computeroutput>test_skeleton_and_content</computeroutput> can be used to detect that. </para></description></macro>
<macro name="BOOST_MPI_DECL"/>
</header>
<header name="boost/mpi/datatype.hpp">
<para>This header provides the mapping from C++ types to MPI data types. </para><namespace name="boost">
<namespace name="mpi">
<struct name="is_mpi_builtin_datatype"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">boost::mpl::or_&lt; is_mpi_integer_datatype&lt; T &gt;, is_mpi_floating_point_datatype&lt; T &gt;, is_mpi_logical_datatype&lt; T &gt;, is_mpi_complex_datatype&lt; T &gt;, is_mpi_byte_datatype&lt; T &gt; &gt;</inherit><purpose>Type trait that determines if there exists a built-in MPI data type for a given C++ type. </purpose><description><para>This type trait determines when there is a direct mapping from a C++ type to an MPI type. For instance, the C++ <computeroutput>int</computeroutput> type maps directly to the MPI type <computeroutput>MPI_INT</computeroutput>. When there is a direct mapping from the type <computeroutput>T</computeroutput> to an MPI type, <computeroutput><classname alt="boost::mpi::is_mpi_builtin_datatype">is_mpi_builtin_datatype</classname></computeroutput> will derive from <computeroutput>mpl::true_</computeroutput> and the MPI data type will be accessible via <computeroutput>get_mpi_datatype</computeroutput>.</para><para>In general, users should not need to specialize this trait. However, if you have an additional C++ type that can map directly to only of MPI's built-in types, specialize either this trait or one of the traits corresponding to categories of MPI data types (<computeroutput><classname alt="boost::mpi::is_mpi_integer_datatype">is_mpi_integer_datatype</classname></computeroutput>, <computeroutput><classname alt="boost::mpi::is_mpi_floating_point_datatype">is_mpi_floating_point_datatype</classname></computeroutput>, <computeroutput><classname alt="boost::mpi::is_mpi_logical_datatype">is_mpi_logical_datatype</classname></computeroutput>, <computeroutput><classname alt="boost::mpi::is_mpi_complex_datatype">is_mpi_complex_datatype</classname></computeroutput>, or <computeroutput><classname alt="boost::mpi::is_mpi_builtin_datatype">is_mpi_builtin_datatype</classname></computeroutput>). <computeroutput><classname alt="boost::mpi::is_mpi_builtin_datatype">is_mpi_builtin_datatype</classname></computeroutput> derives <computeroutput>mpl::true_</computeroutput> if any of the traits corresponding to MPI data type categories derived <computeroutput>mpl::true_</computeroutput>. </para></description></struct><struct name="is_mpi_byte_datatype"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">false_</inherit><purpose>Type trait that determines if there exists a built-in byte MPI data type for a given C++ type. </purpose><description><para>This type trait determines when there is a direct mapping from a C++ type to an MPI data type that is classified as an byte data type. See <computeroutput><classname alt="boost::mpi::is_mpi_builtin_datatype">is_mpi_builtin_datatype</classname></computeroutput> for general information about built-in MPI data types. </para></description></struct><struct name="is_mpi_complex_datatype"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">false_</inherit><purpose>Type trait that determines if there exists a built-in complex MPI data type for a given C++ type. </purpose><description><para>This type trait determines when there is a direct mapping from a C++ type to an MPI data type that is classified as a complex data type. See <computeroutput><classname alt="boost::mpi::is_mpi_builtin_datatype">is_mpi_builtin_datatype</classname></computeroutput> for general information about built-in MPI data types. </para></description></struct><struct name="is_mpi_datatype"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">boost::mpi::is_mpi_builtin_datatype&lt; T &gt;</inherit><purpose>Type trait that determines if a C++ type can be mapped to an MPI data type. </purpose><description><para>This type trait determines if it is possible to build an MPI data type that represents a C++ data type. When this is the case, <computeroutput><classname alt="boost::mpi::is_mpi_datatype">is_mpi_datatype</classname></computeroutput> derives <computeroutput>mpl::true_</computeroutput> and the MPI data type will be accessible via <computeroutput>get_mpi_datatype</computeroutput>.</para><para>For any C++ type that maps to a built-in MPI data type (see <computeroutput><classname alt="boost::mpi::is_mpi_builtin_datatype">is_mpi_builtin_datatype</classname></computeroutput>), <computeroutput>is_mpi_data_type</computeroutput> is trivially true. However, any POD ("Plain Old Data") type containing types that themselves can be represented by MPI data types can itself be represented as an MPI data type. For instance, a <computeroutput>point3d</computeroutput> class containing three <computeroutput>double</computeroutput> values can be represented as an MPI data type. To do so, first make the data type Serializable (using the Boost.Serialization library); then, specialize the <computeroutput><classname alt="boost::mpi::is_mpi_datatype">is_mpi_datatype</classname></computeroutput> trait for the point type so that it will derive <computeroutput>mpl::true_:</computeroutput> </para><para><programlisting language="c++">namespace boost { namespace mpi {
  template&lt;&gt; struct is_mpi_datatype&lt;point&gt;
    : public mpl::true_ { };
} }
</programlisting> </para></description></struct><struct name="is_mpi_floating_point_datatype"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">false_</inherit><purpose>Type trait that determines if there exists a built-in floating point MPI data type for a given C++ type. </purpose><description><para>This type trait determines when there is a direct mapping from a C++ type to an MPI data type that is classified as a floating point data type. See <computeroutput><classname alt="boost::mpi::is_mpi_builtin_datatype">is_mpi_builtin_datatype</classname></computeroutput> for general information about built-in MPI data types. </para></description></struct><struct name="is_mpi_integer_datatype"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">false_</inherit><purpose>Type trait that determines if there exists a built-in integer MPI data type for a given C++ type. </purpose><description><para>This type trait determines when there is a direct mapping from a C++ type to an MPI data type that is classified as an integer data type. See <computeroutput><classname alt="boost::mpi::is_mpi_builtin_datatype">is_mpi_builtin_datatype</classname></computeroutput> for general information about built-in MPI data types. </para></description></struct><struct name="is_mpi_logical_datatype"><template>
      <template-type-parameter name="T"/>
    </template><inherit access="public">false_</inherit><purpose>Type trait that determines if there exists a built-in logical MPI data type for a given C++ type. </purpose><description><para>This type trait determines when there is a direct mapping from a C++ type to an MPI data type that is classified as an logical data type. See <computeroutput><classname alt="boost::mpi::is_mpi_builtin_datatype">is_mpi_builtin_datatype</classname></computeroutput> for general information about built-in MPI data types. </para></description></struct>

























































<function name="get_mpi_datatype"><type>MPI_Datatype</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>const T &amp;</paramtype><description><para>for an optimized call, a constructed object of the type should be passed; otherwise, an object will be default-constructed.</para></description></parameter><purpose>Returns an MPI data type for a C++ type. </purpose><description><para>The function creates an MPI data type for the given object <computeroutput>x</computeroutput>. The first time it is called for a class <computeroutput>T</computeroutput>, the MPI data type is created and cached. Subsequent calls for objects of the same type <computeroutput>T</computeroutput> return the cached MPI data type. The type <computeroutput>T</computeroutput> must allow creation of an MPI data type. That is, it must be Serializable and <computeroutput>is_mpi_datatype&lt;T&gt;</computeroutput> must derive <computeroutput>mpl::true_</computeroutput>.</para><para>For fundamental MPI types, a copy of the MPI data type of the MPI library is returned.</para><para>Note that since the data types are cached, the caller should never call <computeroutput>MPI_Type_free()</computeroutput> for the MPI data type returned by this call.</para><para>

</para></description><returns><para>The MPI data type corresponding to type <computeroutput>T</computeroutput>. </para>
</returns></function>































</namespace>
</namespace>
<macro name="BOOST_IS_MPI_DATATYPE" kind="functionlike"><macro-parameter name="T"/></macro>
</header>
<header name="boost/mpi/datatype_fwd.hpp">
<para>This header provides forward declarations for the contents of the header <computeroutput>datatype.hpp</computeroutput>. It is expected to be used primarily by user-defined C++ classes that need to specialize <computeroutput>is_mpi_datatype</computeroutput>. </para><namespace name="boost">
<namespace name="mpi">
<struct name="packed"><purpose>a dummy data type giving MPI_PACKED as its MPI_Datatype </purpose></struct>


























































<function name="get_mpi_datatype"><type>MPI_Datatype</type><template>
          <template-type-parameter name="T"/>
        </template></function>






























</namespace>
</namespace>
</header>
<header name="boost/mpi/environment.hpp">
<para>This header provides the <computeroutput>environment</computeroutput> class, which provides routines to initialize, finalization, and query the status of the Boost MPI environment. </para><namespace name="boost">
<namespace name="mpi">
<class name="environment"><inherit access="private">noncopyable</inherit><purpose>Initialize, finalize, and query the MPI environment. </purpose><description><para>The <computeroutput>environment</computeroutput> class is used to initialize, finalize, and query the MPI environment. It will typically be used in the <computeroutput>main()</computeroutput> function of a program, which will create a single instance of <computeroutput>environment</computeroutput> initialized with the arguments passed to the program:</para><para><programlisting language="c++">int main(int argc, char* argv[])
{
  mpi::environment env(argc, argv);
}
</programlisting></para><para>The instance of <computeroutput>environment</computeroutput> will initialize MPI (by calling <computeroutput>MPI_Init</computeroutput>) in its constructor and finalize MPI (by calling <computeroutput>MPI_Finalize</computeroutput> for normal termination or <computeroutput>MPI_Abort</computeroutput> for an uncaught exception) in its destructor.</para><para>The use of <computeroutput>environment</computeroutput> is not mandatory. Users may choose to invoke <computeroutput>MPI_Init</computeroutput> and <computeroutput>MPI_Finalize</computeroutput> manually. In this case, no <computeroutput>environment</computeroutput> object is needed. If one is created, however, it will do nothing on either construction or destruction. </para></description><method-group name="public member functions">
</method-group>
<constructor specifiers="explicit"><parameter name="abort_on_exception"><paramtype>bool</paramtype><default>true</default><description><para>When true, this object will abort the program if it is destructed due to an uncaught exception. </para></description></parameter><description><para>Initialize the MPI environment.</para><para>If the MPI environment has not already been initialized, initializes MPI with a call to <computeroutput>MPI_Init</computeroutput>. Since this constructor does not take command-line arguments (<computeroutput>argc</computeroutput> and <computeroutput>argv</computeroutput>), it is only available when the underlying MPI implementation supports calling <computeroutput>MPI_Init</computeroutput> with <computeroutput>NULL</computeroutput> arguments, indicated by the macro <computeroutput>BOOST_MPI_HAS_NOARG_INITIALIZATION</computeroutput>.</para><para>
</para></description></constructor>
<constructor specifiers="explicit"><parameter name="mt_level"><paramtype>threading::level</paramtype><description><para>the required level of threading support.</para></description></parameter><parameter name="abort_on_exception"><paramtype>bool</paramtype><default>true</default><description><para>When true, this object will abort the program if it is destructed due to an uncaught exception. </para></description></parameter><description><para>Initialize the MPI environment.</para><para>If the MPI environment has not already been initialized, initializes MPI with a call to <computeroutput>MPI_Init_thread</computeroutput>. Since this constructor does not take command-line arguments (<computeroutput>argc</computeroutput> and <computeroutput>argv</computeroutput>), it is only available when the underlying MPI implementation supports calling <computeroutput>MPI_Init</computeroutput> with <computeroutput>NULL</computeroutput> arguments, indicated by the macro <computeroutput>BOOST_MPI_HAS_NOARG_INITIALIZATION</computeroutput>.</para><para>
</para></description></constructor>
<constructor><parameter name="argc"><paramtype>int &amp;</paramtype><description><para>The number of arguments provided in <computeroutput>argv</computeroutput>, as passed into the program's <computeroutput>main</computeroutput> function.</para></description></parameter><parameter name="argv"><paramtype>char **&amp;</paramtype><description><para>The array of argument strings passed to the program via <computeroutput>main</computeroutput>.</para></description></parameter><parameter name="abort_on_exception"><paramtype>bool</paramtype><default>true</default><description><para>When true, this object will abort the program if it is destructed due to an uncaught exception. </para></description></parameter><description><para>Initialize the MPI environment.</para><para>If the MPI environment has not already been initialized, initializes MPI with a call to <computeroutput>MPI_Init</computeroutput>.</para><para>
</para></description></constructor>
<constructor><parameter name="argc"><paramtype>int &amp;</paramtype><description><para>The number of arguments provided in <computeroutput>argv</computeroutput>, as passed into the program's <computeroutput>main</computeroutput> function.</para></description></parameter><parameter name="argv"><paramtype>char **&amp;</paramtype><description><para>The array of argument strings passed to the program via <computeroutput>main</computeroutput>.</para></description></parameter><parameter name="mt_level"><paramtype>threading::level</paramtype><description><para>the required level of threading support</para></description></parameter><parameter name="abort_on_exception"><paramtype>bool</paramtype><default>true</default><description><para>When true, this object will abort the program if it is destructed due to an uncaught exception. </para></description></parameter><description><para>Initialize the MPI environment.</para><para>If the MPI environment has not already been initialized, initializes MPI with a call to <computeroutput>MPI_Init_thread</computeroutput>.</para><para>
</para></description></constructor>
<destructor><description><para>Shuts down the MPI environment.</para><para>If this <computeroutput>environment</computeroutput> object was used to initialize the MPI environment, and the MPI environment has not already been shut down (finalized), this destructor will shut down the MPI environment. Under normal circumstances, this only involves invoking <computeroutput>MPI_Finalize</computeroutput>. However, if destruction is the result of an uncaught exception and the <computeroutput>abort_on_exception</computeroutput> parameter of the constructor had the value <computeroutput>true</computeroutput>, this destructor will invoke <computeroutput>MPI_Abort</computeroutput> with <computeroutput>MPI_COMM_WORLD</computeroutput> to abort the entire MPI program with a result code of -1. </para></description></destructor>
<method-group name="public static functions">
<method name="abort" specifiers="static"><type>void</type><parameter name="errcode"><paramtype>int</paramtype><description><para>The error code to return to the environment. </para></description></parameter><description><para>Abort all MPI processes.</para><para>Aborts all MPI processes and returns to the environment. The precise behavior will be defined by the underlying MPI implementation. This is equivalent to a call to <computeroutput>MPI_Abort</computeroutput> with <computeroutput>MPI_COMM_WORLD</computeroutput>.</para><para>

</para></description><returns><para>Will not return. </para>
</returns></method>
<method name="initialized" specifiers="static"><type>bool</type><description><para>Determine if the MPI environment has already been initialized.</para><para>This routine is equivalent to a call to <computeroutput>MPI_Initialized</computeroutput>.</para><para>
</para></description><returns><para><computeroutput>true</computeroutput> if the MPI environment has been initialized. </para>
</returns></method>
<method name="finalized" specifiers="static"><type>bool</type><description><para>Determine if the MPI environment has already been finalized.</para><para>The routine is equivalent to a call to <computeroutput>MPI_Finalized</computeroutput>.</para><para>
</para></description><returns><para><computeroutput>true</computeroutput> if the MPI environment has been finalized. </para>
</returns></method>
<method name="max_tag" specifiers="static"><type>int</type><description><para>Retrieves the maximum tag value.</para><para>Returns the maximum value that may be used for the <computeroutput>tag</computeroutput> parameter of send/receive operations. This value will be somewhat smaller than the value of <computeroutput>MPI_TAG_UB</computeroutput>, because the Boost.MPI implementation reserves some tags for collective operations.</para><para>
</para></description><returns><para>the maximum tag value. </para>
</returns></method>
<method name="collectives_tag" specifiers="static"><type>int</type><description><para>The tag value used for collective operations.</para><para>Returns the reserved tag value used by the Boost.MPI implementation for collective operations. Although users are not permitted to use this tag to send or receive messages, it may be useful when monitoring communication patterns.</para><para>
</para></description><returns><para>the tag value used for collective operations. </para>
</returns></method>
<method name="host_rank" specifiers="static"><type>optional&lt; int &gt;</type><description><para>Retrieves the rank of the host process, if one exists.</para><para>If there is a host process, this routine returns the rank of that process. Otherwise, it returns an empty <computeroutput>optional&lt;int&gt;</computeroutput>. MPI does not define the meaning of a "host" process: consult the documentation for the MPI implementation. This routine examines the <computeroutput>MPI_HOST</computeroutput> attribute of <computeroutput>MPI_COMM_WORLD</computeroutput>.</para><para>
</para></description><returns><para>The rank of the host process, if one exists. </para>
</returns></method>
<method name="io_rank" specifiers="static"><type>optional&lt; int &gt;</type><description><para>Retrieves the rank of a process that can perform input/output.</para><para>This routine returns the rank of a process that can perform input/output via the standard C and C++ I/O facilities. If every process can perform I/O using the standard facilities, this routine will return <computeroutput>any_source</computeroutput>; if no process can perform I/O, this routine will return no value (an empty <computeroutput>optional</computeroutput>). This routine examines the <computeroutput>MPI_IO</computeroutput> attribute of <computeroutput>MPI_COMM_WORLD</computeroutput>.</para><para>
</para></description><returns><para>the rank of the process that can perform I/O, <computeroutput>any_source</computeroutput> if every process can perform I/O, or no value if no process can perform I/O. </para>
</returns></method>
<method name="processor_name" specifiers="static"><type>std::string</type><description><para>Retrieve the name of this processor.</para><para>This routine returns the name of this processor. The actual form of the name is unspecified, but may be documented by the underlying MPI implementation. This routine is implemented as a call to <computeroutput>MPI_Get_processor_name</computeroutput>.</para><para>
</para></description><returns><para>the name of this processor. </para>
</returns></method>
<method name="thread_level" specifiers="static"><type>threading::level</type><description><para>Query the current level of thread support. </para></description></method>
<method name="is_main_thread" specifiers="static"><type>bool</type><description><para>Are we in the main thread? </para></description></method>
<method name="version" specifiers="static"><type>std::pair&lt; int, int &gt;</type><purpose>MPI version. </purpose><description><para>Returns a pair with the version and sub-version number. </para></description></method>
</method-group>
</class><namespace name="threading">
<enum name="level"><enumvalue name="single"><default>= MPI_THREAD_SINGLE</default><description><para>Only one thread will execute. </para></description></enumvalue><enumvalue name="funneled"><default>= MPI_THREAD_FUNNELED</default><description><para>Only main thread will do MPI calls.</para><para>The process may be multi-threaded, but only the main thread will make MPI calls (all MPI calls are `<lsquo/>funneled<rsquo/>' to the main thread). </para></description></enumvalue><enumvalue name="serialized"><default>= MPI_THREAD_SERIALIZED</default><description><para>Only one thread at the time do MPI calls.</para><para>The process may be multi-threaded, and multiple threads may make MPI calls, but only one at a time: MPI calls are not made concurrently from two distinct threads (all MPI calls are `<lsquo/>serialized<rsquo/>'). </para></description></enumvalue><enumvalue name="multiple"><default>= MPI_THREAD_MULTIPLE</default><description><para>Multiple thread may do MPI calls.</para><para>Multiple threads may call MPI, with no restrictions. </para></description></enumvalue><purpose>specify the supported threading level. </purpose><description><para>Based on MPI 2 standard/8.7.3 </para></description></enum>
<function name="operator&lt;&lt;"><type>std::ostream &amp;</type><parameter name="out"><paramtype>std::ostream &amp;</paramtype></parameter><parameter name="l"><paramtype>level</paramtype></parameter><description><para>Formated output for threading level. </para></description></function>
<function name="operator&gt;&gt;"><type>std::istream &amp;</type><parameter name="in"><paramtype>std::istream &amp;</paramtype></parameter><parameter name="l"><paramtype>level &amp;</paramtype></parameter><description><para>Formated input for threading level. </para></description></function>
</namespace>


























































































</namespace>
</namespace>
</header>
<header name="boost/mpi/exception.hpp">
<para>This header provides exception classes that report MPI errors to the user and macros that translate MPI error codes into Boost.MPI exceptions. </para><namespace name="boost">
<namespace name="mpi">
<class name="exception"><inherit access="public">exception</inherit><purpose>Catch-all exception class for MPI errors. </purpose><description><para>Instances of this class will be thrown when an MPI error occurs. MPI failures that trigger these exceptions may or may not be recoverable, depending on the underlying MPI implementation. Consult the documentation for your MPI implementation to determine the effect of MPI errors. </para></description><method-group name="public member functions">
<method name="what" cv="const" specifiers="virtual"><type>const char *</type><description><para>A description of the error that occurred. </para></description></method>
<method name="routine" cv="const"><type>const char *</type><description><para>Retrieve the name of the MPI routine that reported the error. </para></description></method>
<method name="result_code" cv="const"><type>int</type><purpose>Retrieve the result code returned from the MPI routine that reported the error. </purpose></method>
<method name="error_class" cv="const"><type>int</type><purpose>Returns the MPI error class associated with the error that triggered this exception. </purpose></method>
</method-group>
<constructor><parameter name="routine"><paramtype>const char *</paramtype><description><para>The MPI routine in which the error occurred. This should be a pointer to a string constant: it will not be copied.</para></description></parameter><parameter name="result_code"><paramtype>int</paramtype><description><para>The result code returned from the MPI routine that aborted with an error. </para></description></parameter><description><para>Build a new <computeroutput>exception</computeroutput> exception.</para><para>
</para></description></constructor>
<destructor/>
</class>

























































































</namespace>
</namespace>
<macro name="BOOST_MPI_CHECK_RESULT" kind="functionlike"><macro-parameter name="MPIFunc"/><macro-parameter name="Args"/><description><para>Call the MPI routine MPIFunc with arguments Args (surrounded by parentheses). If the result is not MPI_SUCCESS, use boost::throw_exception to throw an exception or abort, depending on BOOST_NO_EXCEPTIONS. </para></description></macro>
</header>
<header name="boost/mpi/graph_communicator.hpp">
<para>This header defines facilities to support MPI communicators with graph topologies, using the graph interface defined by the Boost Graph Library. One can construct a communicator whose topology is described by any graph meeting the requirements of the Boost Graph Library's graph concepts. Likewise, any communicator that has a graph topology can be viewed as a graph by the Boost Graph Library, permitting one to use the BGL's graph algorithms on the process topology. </para><namespace name="boost">
<struct-specialization name="graph_traits"><template>
    </template><specialization><template-arg>mpi::graph_communicator</template-arg></specialization><purpose>Traits structure that allows a communicator with graph topology to be view as a graph by the Boost Graph Library. </purpose><description><para>The specialization of <computeroutput>graph_traits</computeroutput> for an MPI communicator allows a communicator with graph topology to be viewed as a graph. An MPI communicator with graph topology meets the requirements of the Graph, Incidence Graph, Adjacency Graph, Vertex List Graph, and Edge List Graph concepts from the Boost Graph Library. </para></description><typedef name="vertex_descriptor"><type>int</type></typedef>
<typedef name="edge_descriptor"><type>std::pair&lt; int, int &gt;</type></typedef>
<typedef name="directed_category"><type>directed_tag</type></typedef>
<typedef name="edge_parallel_category"><type>disallow_parallel_edge_tag</type></typedef>
<typedef name="out_edge_iterator"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="degree_size_type"><type>int</type></typedef>
<typedef name="adjacency_iterator"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="vertex_iterator"><type>counting_iterator&lt; int &gt;</type></typedef>
<typedef name="vertices_size_type"><type>int</type></typedef>
<typedef name="edge_iterator"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="edges_size_type"><type>int</type></typedef>
<method-group name="public static functions">
<method name="null_vertex" specifiers="static"><type>vertex_descriptor</type><purpose>Returns a vertex descriptor that can never refer to any valid vertex. </purpose></method>
</method-group>
</struct-specialization><namespace name="mpi">
<class name="graph_communicator"><inherit access="public">boost::mpi::communicator</inherit><purpose>An MPI communicator with a graph topology. </purpose><description><para>A <computeroutput><classname alt="boost::mpi::graph_communicator">graph_communicator</classname></computeroutput> is a communicator whose topology is expressed as a graph. Graph communicators have the same functionality as (intra)communicators, but also allow one to query the relationships among processes. Those relationships are expressed via a graph, using the interface defined by the Boost Graph Library. The <computeroutput><classname alt="boost::mpi::graph_communicator">graph_communicator</classname></computeroutput> class meets the requirements of the BGL Graph, Incidence Graph, Adjacency Graph, Vertex List Graph, and Edge List Graph concepts. </para></description><method-group name="public member functions">
</method-group>
<constructor><parameter name="comm"><paramtype>const MPI_Comm &amp;</paramtype></parameter><parameter name="kind"><paramtype>comm_create_kind</paramtype></parameter><description><para>Build a new Boost.MPI graph communicator based on the MPI communicator <computeroutput>comm</computeroutput> with graph topology.</para><para><computeroutput>comm</computeroutput> may be any valid MPI communicator. If <computeroutput>comm</computeroutput> is MPI_COMM_NULL, an empty communicator (that cannot be used for communication) is created and the <computeroutput>kind</computeroutput> parameter is ignored. Otherwise, the <computeroutput>kind</computeroutput> parameter determines how the Boost.MPI communicator will be related to <computeroutput>comm:</computeroutput> </para><para><itemizedlist>
<listitem><para>If <computeroutput>kind</computeroutput> is <computeroutput>comm_duplicate</computeroutput>, duplicate <computeroutput>comm</computeroutput> to create a new communicator. This new communicator will be freed when the Boost.MPI communicator (and all copies of it) is destroyed. This option is only permitted if the underlying MPI implementation supports MPI 2.0; duplication of intercommunicators is not available in MPI 1.x.</para>
</listitem><listitem><para>If <computeroutput>kind</computeroutput> is <computeroutput>comm_take_ownership</computeroutput>, take ownership of <computeroutput>comm</computeroutput>. It will be freed automatically when all of the Boost.MPI communicators go out of scope.</para>
</listitem><listitem><para>If <computeroutput>kind</computeroutput> is <computeroutput>comm_attach</computeroutput>, this Boost.MPI communicator will reference the existing MPI communicator <computeroutput>comm</computeroutput> but will not free <computeroutput>comm</computeroutput> when the Boost.MPI communicator goes out of scope. This option should only be used when the communicator is managed by the user. </para>
</listitem></itemizedlist>
</para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="Graph"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The communicator that the new, graph communicator will be based on.</para></description></parameter><parameter name="graph"><paramtype>const Graph &amp;</paramtype><description><para>Any type that meets the requirements of the Incidence Graph and Vertex List Graph concepts from the Boost Graph Library. This structure of this graph will become the topology of the communicator that is returned.</para></description></parameter><parameter name="reorder"><paramtype>bool</paramtype><default>false</default><description><para>Whether MPI is permitted to re-order the process ranks within the returned communicator, to better optimize communication. If false, the ranks of each process in the returned process will match precisely the rank of that process within the original communicator. </para></description></parameter><description><para>Create a new communicator whose topology is described by the given graph. The indices of the vertices in the graph will be assumed to be the ranks of the processes within the communicator. There may be fewer vertices in the graph than there are processes in the communicator; in this case, the resulting communicator will be a NULL communicator.</para><para>
</para></description></constructor>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="Graph"/>
          <template-type-parameter name="RankMap"/>
        </template><parameter name="comm"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The communicator that the new, graph communicator will be based on. The ranks in <computeroutput>rank</computeroutput> refer to the processes in this communicator.</para></description></parameter><parameter name="graph"><paramtype>const Graph &amp;</paramtype><description><para>Any type that meets the requirements of the Incidence Graph and Vertex List Graph concepts from the Boost Graph Library. This structure of this graph will become the topology of the communicator that is returned.</para></description></parameter><parameter name="rank"><paramtype>RankMap</paramtype><description><para>This map translates vertices in the <computeroutput>graph</computeroutput> into ranks within the current communicator. It must be a Readable Property Map (see the Boost Property Map library) whose key type is the vertex type of the <computeroutput>graph</computeroutput> and whose value type is <computeroutput>int</computeroutput>.</para></description></parameter><parameter name="reorder"><paramtype>bool</paramtype><default>false</default><description><para>Whether MPI is permitted to re-order the process ranks within the returned communicator, to better optimize communication. If false, the ranks of each process in the returned process will match precisely the rank of that process within the original communicator. </para></description></parameter><description><para>Create a new communicator whose topology is described by the given graph. The rank map (<computeroutput>rank</computeroutput>) gives the mapping from vertices in the graph to ranks within the communicator. There may be fewer vertices in the graph than there are processes in the communicator; in this case, the resulting communicator will be a NULL communicator.</para><para>
</para></description></constructor>
<method-group name="protected member functions">
</method-group>
</class>



























































<function name="source"><type>int</type><parameter name="edge"><paramtype>const std::pair&lt; int, int &gt; &amp;</paramtype></parameter><parameter name=""><paramtype>const <classname>graph_communicator</classname> &amp;</paramtype></parameter><purpose>Returns the source vertex from an edge in the graph topology of a communicator. </purpose></function>
<function name="target"><type>int</type><parameter name="edge"><paramtype>const std::pair&lt; int, int &gt; &amp;</paramtype></parameter><parameter name=""><paramtype>const <classname>graph_communicator</classname> &amp;</paramtype></parameter><purpose>Returns the target vertex from an edge in the graph topology of a communicator. </purpose></function>
<function name="out_edges"><type><emphasis>unspecified</emphasis></type><parameter name="vertex"><paramtype>int</paramtype></parameter><parameter name="comm"><paramtype>const <classname>graph_communicator</classname> &amp;</paramtype></parameter><purpose>Returns an iterator range containing all of the edges outgoing from the given vertex in a graph topology of a communicator. </purpose></function>
<function name="out_degree"><type>int</type><parameter name="vertex"><paramtype>int</paramtype></parameter><parameter name="comm"><paramtype>const <classname>graph_communicator</classname> &amp;</paramtype></parameter><purpose>Returns the out-degree of a vertex in the graph topology of a communicator. </purpose></function>
<function name="adjacent_vertices"><type><emphasis>unspecified</emphasis></type><parameter name="vertex"><paramtype>int</paramtype></parameter><parameter name="comm"><paramtype>const <classname>graph_communicator</classname> &amp;</paramtype></parameter><purpose>Returns an iterator range containing all of the neighbors of the given vertex in the communicator's graph topology. </purpose></function>
<function name="vertices"><type>std::pair&lt; counting_iterator&lt; int &gt;, counting_iterator&lt; int &gt; &gt;</type><parameter name="comm"><paramtype>const <classname>graph_communicator</classname> &amp;</paramtype></parameter><purpose>Returns an iterator range that contains all of the vertices with the communicator's graph topology, i.e., all of the process ranks in the communicator. </purpose></function>
<function name="num_vertices"><type>int</type><parameter name="comm"><paramtype>const <classname>graph_communicator</classname> &amp;</paramtype></parameter><purpose>Returns the number of vertices within the graph topology of the communicator, i.e., the number of processes in the communicator. </purpose></function>
<function name="edges"><type><emphasis>unspecified</emphasis></type><parameter name="comm"><paramtype>const <classname>graph_communicator</classname> &amp;</paramtype></parameter><purpose>Returns an iterator range that contains all of the edges with the communicator's graph topology. </purpose></function>
<function name="num_edges"><type>int</type><parameter name="comm"><paramtype>const <classname>graph_communicator</classname> &amp;</paramtype></parameter><purpose>Returns the number of edges in the communicator's graph topology. </purpose></function>
<function name="get"><type>identity_property_map</type><parameter name=""><paramtype>vertex_index_t</paramtype></parameter><parameter name=""><paramtype>const <classname>graph_communicator</classname> &amp;</paramtype></parameter><purpose>Returns a property map that maps from vertices in a communicator's graph topology to their index values. </purpose><description><para>Since the vertices are ranks in the communicator, the returned property map is the identity property map. </para></description></function>
<function name="get"><type>int</type><parameter name=""><paramtype>vertex_index_t</paramtype></parameter><parameter name=""><paramtype>const <classname>graph_communicator</classname> &amp;</paramtype></parameter><parameter name="vertex"><paramtype>int</paramtype></parameter><purpose>Returns the index of a vertex in the communicator's graph topology. </purpose><description><para>Since the vertices are ranks in the communicator, this is the identity function. </para></description></function>



















</namespace>
</namespace>
</header>
<header name="boost/mpi/group.hpp">
<para>This header defines the <computeroutput>group</computeroutput> class, which allows one to manipulate and query groups of processes. </para><namespace name="boost">
<namespace name="mpi">
<class name="group"><purpose>A <computeroutput>group</computeroutput> is a representation of a subset of the processes within a <computeroutput>communicator</computeroutput>. </purpose><description><para>The <computeroutput>group</computeroutput> class allows one to create arbitrary subsets of the processes within a communicator. One can compute the union, intersection, or difference of two groups, or create new groups by specifically including or excluding certain processes. Given a group, one can create a new communicator containing only the processes in that group. </para></description><method-group name="public member functions">
<method name="rank" cv="const"><type>optional&lt; int &gt;</type><purpose>Determine the rank of the calling process in the group. </purpose><description><para>This routine is equivalent to <computeroutput>MPI_Group_rank</computeroutput>.</para><para>
</para></description><returns><para>The rank of the calling process in the group, which will be a value in [0, size()). If the calling process is not in the group, returns an empty value. </para>
</returns></method>
<method name="size" cv="const"><type>int</type><purpose>Determine the number of processes in the group. </purpose><description><para>This routine is equivalent to <computeroutput>MPI_Group_size</computeroutput>.</para><para>
</para></description><returns><para>The number of processes in the group. </para>
</returns></method>
<method name="translate_ranks"><type>OutputIterator</type><template>
          <template-type-parameter name="InputIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>Beginning of the iterator range of ranks in the current group.</para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>Past the end of the iterator range of ranks in the current group.</para></description></parameter><parameter name="to_group"><paramtype>const <classname>group</classname> &amp;</paramtype><description><para>The group that we are translating ranks to.</para></description></parameter><parameter name="out"><paramtype>OutputIterator</paramtype><description><para>The output iterator to which the translated ranks will be written.</para></description></parameter><purpose>Translates the ranks from one group into the ranks of the same processes in another group. </purpose><description><para>This routine translates each of the integer rank values in the iterator range <computeroutput/>[first, last) from the current group into rank values of the corresponding processes in <computeroutput>to_group</computeroutput>. The corresponding rank values are written via the output iterator <computeroutput>out</computeroutput>. When there is no correspondence between a rank in the current group and a rank in <computeroutput>to_group</computeroutput>, the value <computeroutput>MPI_UNDEFINED</computeroutput> is written to the output iterator.</para><para>

</para></description><returns><para>the output iterator, which points one step past the last rank written. </para>
</returns></method>
<method name="conversion-operator" cv="const"><type>bool</type><purpose>Determines whether the group is non-empty. </purpose><description><para>
</para></description><returns><para>True if the group is not empty, false if it is empty. </para>
</returns></method>
<method name="conversion-operator" cv="const"><type>MPI_Group</type><purpose>Retrieves the underlying <computeroutput>MPI_Group</computeroutput> associated with this group. </purpose><description><para>
</para></description><returns><para>The <computeroutput>MPI_Group</computeroutput> handle manipulated by this object. If this object represents the empty group, returns <computeroutput>MPI_GROUP_EMPTY</computeroutput>. </para>
</returns></method>
<method name="include"><type><classname>group</classname></type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><purpose>Creates a new group including a subset of the processes in the current group. </purpose><description><para>This routine creates a new <computeroutput>group</computeroutput> which includes only those processes in the current group that are listed in the integer iterator range <computeroutput/>[first, last). Equivalent to <computeroutput>MPI_Group_incl</computeroutput>.</para><para><computeroutput>first</computeroutput> The beginning of the iterator range of ranks to include.</para><para><computeroutput>last</computeroutput> Past the end of the iterator range of ranks to include.</para><para>
</para></description><returns><para>A new group containing those processes with ranks <computeroutput/>[first, last) in the current group. </para>
</returns></method>
<method name="exclude"><type><classname>group</classname></type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype></parameter><parameter name="last"><paramtype>InputIterator</paramtype></parameter><purpose>Creates a new group from all of the processes in the current group, exluding a specific subset of the processes. </purpose><description><para>This routine creates a new <computeroutput>group</computeroutput> which includes all of the processes in the current group except those whose ranks are listed in the integer iterator range <computeroutput/>[first, last). Equivalent to <computeroutput>MPI_Group_excl</computeroutput>.</para><para><computeroutput>first</computeroutput> The beginning of the iterator range of ranks to exclude.</para><para><computeroutput>last</computeroutput> Past the end of the iterator range of ranks to exclude.</para><para>
</para></description><returns><para>A new group containing all of the processes in the current group except those processes with ranks <computeroutput/>[first, last) in the current group. </para>
</returns></method>
</method-group>
<constructor><purpose>Constructs an empty group. </purpose></constructor>
<constructor><parameter name="in_group"><paramtype>const MPI_Group &amp;</paramtype><description><para>The <computeroutput>MPI_Group</computeroutput> used to construct this <computeroutput>group</computeroutput>.</para></description></parameter><parameter name="adopt"><paramtype>bool</paramtype><description><para>Whether the <computeroutput>group</computeroutput> should adopt the <computeroutput>MPI_Group</computeroutput>. When true, the <computeroutput>group</computeroutput> object (or one of its copies) will free the group (via <computeroutput>MPI_Comm_free</computeroutput>) when the last copy is destroyed. Otherwise, the user is responsible for calling <computeroutput>MPI_Group_free</computeroutput>. </para></description></parameter><purpose>Constructs a group from an <computeroutput>MPI_Group</computeroutput>. </purpose><description><para>This routine allows one to construct a Boost.MPI <computeroutput>group</computeroutput> from a C <computeroutput>MPI_Group</computeroutput>. The <computeroutput>group</computeroutput> object can (optionally) adopt the <computeroutput>MPI_Group</computeroutput>, after which point the <computeroutput>group</computeroutput> object becomes responsible for freeing the <computeroutput>MPI_Group</computeroutput> when the last copy of <computeroutput>group</computeroutput> disappears.</para><para>
</para></description></constructor>
</class>






































































<function name="operator=="><type>BOOST_MPI_DECL bool</type><parameter name="g1"><paramtype>const <classname>group</classname> &amp;</paramtype></parameter><parameter name="g2"><paramtype>const <classname>group</classname> &amp;</paramtype></parameter><purpose>Determines whether two process groups are identical. </purpose><description><para>Equivalent to calling <computeroutput>MPI_Group_compare</computeroutput> and checking whether the result is <computeroutput>MPI_IDENT</computeroutput>.</para><para>
</para></description><returns><para>True when the two process groups contain the same processes in the same order. </para>
</returns></function>
<function name="operator!="><type>bool</type><parameter name="g1"><paramtype>const <classname>group</classname> &amp;</paramtype></parameter><parameter name="g2"><paramtype>const <classname>group</classname> &amp;</paramtype></parameter><purpose>Determines whether two process groups are not identical. </purpose><description><para>Equivalent to calling <computeroutput>MPI_Group_compare</computeroutput> and checking whether the result is not <computeroutput>MPI_IDENT</computeroutput>.</para><para>
</para></description><returns><para>False when the two process groups contain the same processes in the same order. </para>
</returns></function>
<function name="operator|"><type>BOOST_MPI_DECL <classname>group</classname></type><parameter name="g1"><paramtype>const <classname>group</classname> &amp;</paramtype></parameter><parameter name="g2"><paramtype>const <classname>group</classname> &amp;</paramtype></parameter><purpose>Computes the union of two process groups. </purpose><description><para>This routine returns a new <computeroutput>group</computeroutput> that contains all processes that are either in group <computeroutput>g1</computeroutput> or in group <computeroutput>g2</computeroutput> (or both). The processes that are in <computeroutput>g1</computeroutput> will be first in the resulting group, followed by the processes from <computeroutput>g2</computeroutput> (but not also in <computeroutput>g1</computeroutput>). Equivalent to <computeroutput>MPI_Group_union</computeroutput>. </para></description></function>
<function name="operator&amp;"><type>BOOST_MPI_DECL <classname>group</classname></type><parameter name="g1"><paramtype>const <classname>group</classname> &amp;</paramtype></parameter><parameter name="g2"><paramtype>const <classname>group</classname> &amp;</paramtype></parameter><purpose>Computes the intersection of two process groups. </purpose><description><para>This routine returns a new <computeroutput>group</computeroutput> that contains all processes that are in group <computeroutput>g1</computeroutput> and in group <computeroutput>g2</computeroutput>, ordered in the same way as <computeroutput>g1</computeroutput>. Equivalent to <computeroutput>MPI_Group_intersection</computeroutput>. </para></description></function>
<function name="operator-"><type>BOOST_MPI_DECL <classname>group</classname></type><parameter name="g1"><paramtype>const <classname>group</classname> &amp;</paramtype></parameter><parameter name="g2"><paramtype>const <classname>group</classname> &amp;</paramtype></parameter><purpose>Computes the difference between two process groups. </purpose><description><para>This routine returns a new <computeroutput>group</computeroutput> that contains all processes that are in group <computeroutput>g1</computeroutput> but not in group <computeroutput>g2</computeroutput>, ordered in the same way as <computeroutput>g1</computeroutput>. Equivalent to <computeroutput>MPI_Group_difference</computeroutput>. </para></description></function>














</namespace>
</namespace>
</header>
<header name="boost/mpi/inplace.hpp">
<para>This header provides helpers to indicate to MPI collective operation that a buffer can be use both as an input and output. </para><namespace name="boost">
<namespace name="mpi">
<struct name="inplace_t"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Wrapper type to explicitly indicate that a input data can be overriden with an output value. </purpose><data-member name="buffer"><type>T &amp;</type></data-member>
<method-group name="public member functions">
</method-group>
<constructor><parameter name="inout"><paramtype>T &amp;</paramtype></parameter></constructor>
</struct><struct-specialization name="inplace_t"><template>
      <template-type-parameter name="T"/>
    </template><specialization><template-arg>T *</template-arg></specialization><data-member name="buffer"><type>T *</type></data-member>
<method-group name="public member functions">
</method-group>
<constructor><parameter name="inout"><paramtype>T *</paramtype></parameter></constructor>
</struct-specialization>























































































<overloaded-function name="inplace"><signature><type><classname>inplace_t</classname>&lt; T &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="inout"><paramtype>T &amp;</paramtype><description><para>the contributing input value, it will be overriden with the output value where one is expected. If it is a pointer, the number of elements will be provided separatly. </para></description></parameter></signature><signature><type><classname>inplace_t</classname>&lt; T * &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="inout"><paramtype>T *</paramtype></parameter></signature><purpose>Wrapp a input data to indicate that it can be overriden with an ouput value. </purpose><description><para>

</para></description><returns><para>The wrapped value or pointer. </para>
</returns></overloaded-function>

</namespace>
</namespace>
</header>
<header name="boost/mpi/intercommunicator.hpp">
<para>This header defines the <computeroutput>intercommunicator</computeroutput> class, which permits communication between different process groups. </para><namespace name="boost">
<namespace name="mpi">
<class name="intercommunicator"><inherit access="public">boost::mpi::communicator</inherit><purpose>Communication facilities among processes in different groups. </purpose><description><para>The <computeroutput>intercommunicator</computeroutput> class provides communication facilities among processes from different groups. An intercommunicator is always associated with two process groups: one "local" process group, containing the process that initiates an MPI operation (e.g., the sender in a <computeroutput>send</computeroutput> operation), and one "remote" process group, containing the process that is the target of the MPI operation.</para><para>While intercommunicators have essentially the same point-to-point operations as intracommunicators (the latter communicate only within a single process group), all communication with intercommunicators occurs between the processes in the local group and the processes in the remote group; communication within a group must use a different (intra-)communicator. </para></description><method-group name="public member functions">
<method name="local_size" cv="const"><type>int</type><description><para>Returns the size of the local group, i.e., the number of local processes that are part of the group. </para></description></method>
<method name="local_group" cv="const"><type><classname>boost::mpi::group</classname></type><description><para>Returns the local group, containing all of the local processes in this intercommunicator. </para></description></method>
<method name="local_rank" cv="const"><type>int</type><description><para>Returns the rank of this process within the local group. </para></description></method>
<method name="remote_size" cv="const"><type>int</type><description><para>Returns the size of the remote group, i.e., the number of processes that are part of the remote group. </para></description></method>
<method name="remote_group" cv="const"><type><classname>boost::mpi::group</classname></type><description><para>Returns the remote group, containing all of the remote processes in this intercommunicator. </para></description></method>
<method name="merge" cv="const"><type><classname>communicator</classname></type><parameter name="high"><paramtype>bool</paramtype><description><para>Whether the processes in this group should have the higher rank numbers than the processes in the other group. Each of the processes within a particular group shall have the same "high" value.</para></description></parameter><description><para>Merge the local and remote groups in this intercommunicator into a new intracommunicator containing the union of the processes in both groups. This method is equivalent to <computeroutput>MPI_Intercomm_merge</computeroutput>.</para><para>

</para></description><returns><para>the new, merged intracommunicator </para>
</returns></method>
</method-group>
<constructor><parameter name="comm"><paramtype>const MPI_Comm &amp;</paramtype></parameter><parameter name="kind"><paramtype>comm_create_kind</paramtype></parameter><description><para>Build a new Boost.MPI intercommunicator based on the MPI intercommunicator <computeroutput>comm</computeroutput>.</para><para><computeroutput>comm</computeroutput> may be any valid MPI intercommunicator. If <computeroutput>comm</computeroutput> is MPI_COMM_NULL, an empty communicator (that cannot be used for communication) is created and the <computeroutput>kind</computeroutput> parameter is ignored. Otherwise, the <computeroutput>kind</computeroutput> parameter determines how the Boost.MPI communicator will be related to <computeroutput>comm:</computeroutput> </para><para><itemizedlist>
<listitem><para>If <computeroutput>kind</computeroutput> is <computeroutput>comm_duplicate</computeroutput>, duplicate <computeroutput>comm</computeroutput> to create a new communicator. This new communicator will be freed when the Boost.MPI communicator (and all copies of it) is destroyed. This option is only permitted if the underlying MPI implementation supports MPI 2.0; duplication of intercommunicators is not available in MPI 1.x.</para>
</listitem><listitem><para>If <computeroutput>kind</computeroutput> is <computeroutput>comm_take_ownership</computeroutput>, take ownership of <computeroutput>comm</computeroutput>. It will be freed automatically when all of the Boost.MPI communicators go out of scope.</para>
</listitem><listitem><para>If <computeroutput>kind</computeroutput> is <computeroutput>comm_attach</computeroutput>, this Boost.MPI communicator will reference the existing MPI communicator <computeroutput>comm</computeroutput> but will not free <computeroutput>comm</computeroutput> when the Boost.MPI communicator goes out of scope. This option should only be used when the communicator is managed by the user. </para>
</listitem></itemizedlist>
</para></description></constructor>
<constructor><parameter name="local"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The intracommunicator containing all of the processes that will go into the local group.</para></description></parameter><parameter name="local_leader"><paramtype>int</paramtype><description><para>The rank within the <computeroutput>local</computeroutput> intracommunicator that will serve as its leader.</para></description></parameter><parameter name="peer"><paramtype>const <classname>communicator</classname> &amp;</paramtype><description><para>The intracommunicator containing all of the processes that will go into the remote group.</para></description></parameter><parameter name="remote_leader"><paramtype>int</paramtype><description><para>The rank within the <computeroutput>peer</computeroutput> group that will serve as its leader. </para></description></parameter><description><para>Constructs a new intercommunicator whose local group is <computeroutput>local</computeroutput> and whose remote group is <computeroutput>peer</computeroutput>. The intercommunicator can then be used to communicate between processes in the two groups. This constructor is equivalent to a call to <computeroutput>MPI_Intercomm_create</computeroutput>.</para><para>
</para></description></constructor>
</class>

























































































</namespace>
</namespace>
</header>
<header name="boost/mpi/nonblocking.hpp">
<para>This header defines operations for completing non-blocking communication requests. </para><namespace name="boost">
<namespace name="mpi">












































































<function name="wait_any"><type>std::pair&lt; <classname>status</classname>, ForwardIterator &gt;</type><template>
          <template-type-parameter name="ForwardIterator"/>
        </template><parameter name="first"><paramtype>ForwardIterator</paramtype><description><para>The iterator that denotes the beginning of the sequence of request objects.</para></description></parameter><parameter name="last"><paramtype>ForwardIterator</paramtype><description><para>The iterator that denotes the end of the sequence of request objects. This may not be equal to <computeroutput>first</computeroutput>.</para></description></parameter><purpose>Wait until any non-blocking request has completed. </purpose><description><para>This routine takes in a set of requests stored in the iterator range <computeroutput/>[first,last) and waits until any of these requests has been completed. It provides functionality equivalent to <computeroutput>MPI_Waitany</computeroutput>.</para><para>

</para></description><returns><para>A pair containing the status object that corresponds to the completed operation and the iterator referencing the completed request. </para>
</returns></function>
<function name="test_any"><type>optional&lt; std::pair&lt; <classname>status</classname>, ForwardIterator &gt; &gt;</type><template>
          <template-type-parameter name="ForwardIterator"/>
        </template><parameter name="first"><paramtype>ForwardIterator</paramtype><description><para>The iterator that denotes the beginning of the sequence of request objects.</para></description></parameter><parameter name="last"><paramtype>ForwardIterator</paramtype><description><para>The iterator that denotes the end of the sequence of request objects.</para></description></parameter><purpose>Test whether any non-blocking request has completed. </purpose><description><para>This routine takes in a set of requests stored in the iterator range <computeroutput/>[first,last) and tests whether any of these requests has been completed. This routine is similar to <computeroutput>wait_any</computeroutput>, but will not block waiting for requests to completed. It provides functionality equivalent to <computeroutput>MPI_Testany</computeroutput>.</para><para>

</para></description><returns><para>If any outstanding requests have completed, a pair containing the status object that corresponds to the completed operation and the iterator referencing the completed request. Otherwise, an empty <computeroutput>optional&lt;&gt;</computeroutput>. </para>
</returns></function>
<overloaded-function name="wait_all"><signature><type>OutputIterator</type><template>
          <template-type-parameter name="ForwardIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>ForwardIterator</paramtype><description><para>The iterator that denotes the beginning of the sequence of request objects.</para></description></parameter><parameter name="last"><paramtype>ForwardIterator</paramtype><description><para>The iterator that denotes the end of the sequence of request objects.</para></description></parameter><parameter name="out"><paramtype>OutputIterator</paramtype><description><para>If provided, an output iterator through which the status of each request will be emitted. The <computeroutput>status</computeroutput> objects are emitted in the same order as the requests are retrieved from <computeroutput/>[first,last).</para></description></parameter></signature><signature><type>void</type><template>
          <template-type-parameter name="ForwardIterator"/>
        </template><parameter name="first"><paramtype>ForwardIterator</paramtype></parameter><parameter name="last"><paramtype>ForwardIterator</paramtype></parameter></signature><purpose>Wait until all non-blocking requests have completed. </purpose><description><para>This routine takes in a set of requests stored in the iterator range <computeroutput/>[first,last) and waits until all of these requests have been completed. It provides functionality equivalent to <computeroutput>MPI_Waitall</computeroutput>.</para><para>

</para></description><returns><para>If an <computeroutput>out</computeroutput> parameter was provided, the value <computeroutput>out</computeroutput> after all of the <computeroutput>status</computeroutput> objects have been emitted. </para>
</returns></overloaded-function>

<overloaded-function name="test_all"><signature><type>optional&lt; OutputIterator &gt;</type><template>
          <template-type-parameter name="ForwardIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>ForwardIterator</paramtype><description><para>The iterator that denotes the beginning of the sequence of request objects.</para></description></parameter><parameter name="last"><paramtype>ForwardIterator</paramtype><description><para>The iterator that denotes the end of the sequence of request objects.</para></description></parameter><parameter name="out"><paramtype>OutputIterator</paramtype><description><para>If provided and all requests hav been completed, an output iterator through which the status of each request will be emitted. The <computeroutput>status</computeroutput> objects are emitted in the same order as the requests are retrieved from <computeroutput/>[first,last).</para></description></parameter></signature><signature><type>bool</type><template>
          <template-type-parameter name="ForwardIterator"/>
        </template><parameter name="first"><paramtype>ForwardIterator</paramtype></parameter><parameter name="last"><paramtype>ForwardIterator</paramtype></parameter></signature><purpose>Tests whether all non-blocking requests have completed. </purpose><description><para>This routine takes in a set of requests stored in the iterator range <computeroutput/>[first,last) and determines whether all of these requests have been completed. However, due to limitations of the underlying MPI implementation, if any of the requests refers to a non-blocking send or receive of a serialized data type, <computeroutput>test_all</computeroutput> will always return the equivalent of <computeroutput>false</computeroutput> (i.e., the requests cannot all be finished at this time). This routine performs the same functionality as <computeroutput>wait_all</computeroutput>, except that this routine will not block. This routine provides functionality equivalent to <computeroutput>MPI_Testall</computeroutput>.</para><para>

</para></description><returns><para>If an <computeroutput>out</computeroutput> parameter was provided, the value <computeroutput>out</computeroutput> after all of the <computeroutput>status</computeroutput> objects have been emitted (if all requests were completed) or an empty <computeroutput>optional&lt;&gt;</computeroutput>. If no <computeroutput>out</computeroutput> parameter was provided, returns <computeroutput>true</computeroutput> if all requests have completed or <computeroutput>false</computeroutput> otherwise. </para>
</returns></overloaded-function>

<overloaded-function name="wait_some"><signature><type>std::pair&lt; OutputIterator, BidirectionalIterator &gt;</type><template>
          <template-type-parameter name="BidirectionalIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>BidirectionalIterator</paramtype><description><para>The iterator that denotes the beginning of the sequence of request objects.</para></description></parameter><parameter name="last"><paramtype>BidirectionalIterator</paramtype><description><para>The iterator that denotes the end of the sequence of request objects. This may not be equal to <computeroutput>first</computeroutput>.</para></description></parameter><parameter name="out"><paramtype>OutputIterator</paramtype><description><para>If provided, the <computeroutput>status</computeroutput> objects corresponding to completed requests will be emitted through this output iterator.</para></description></parameter></signature><signature><type>BidirectionalIterator</type><template>
          <template-type-parameter name="BidirectionalIterator"/>
        </template><parameter name="first"><paramtype>BidirectionalIterator</paramtype></parameter><parameter name="last"><paramtype>BidirectionalIterator</paramtype></parameter></signature><purpose>Wait until some non-blocking requests have completed. </purpose><description><para>This routine takes in a set of requests stored in the iterator range <computeroutput/>[first,last) and waits until at least one of the requests has completed. It then completes all of the requests it can, partitioning the input sequence into pending requests followed by completed requests. If an output iterator is provided, <computeroutput>status</computeroutput> objects will be emitted for each of the completed requests. This routine provides functionality equivalent to <computeroutput>MPI_Waitsome</computeroutput>.</para><para>

</para></description><returns><para>If the <computeroutput>out</computeroutput> parameter was provided, a pair containing the output iterator <computeroutput>out</computeroutput> after all of the <computeroutput>status</computeroutput> objects have been written through it and an iterator referencing the first completed request. If no <computeroutput>out</computeroutput> parameter was provided, only the iterator referencing the first completed request will be emitted. </para>
</returns></overloaded-function>

<overloaded-function name="test_some"><signature><type>std::pair&lt; OutputIterator, BidirectionalIterator &gt;</type><template>
          <template-type-parameter name="BidirectionalIterator"/>
          <template-type-parameter name="OutputIterator"/>
        </template><parameter name="first"><paramtype>BidirectionalIterator</paramtype><description><para>The iterator that denotes the beginning of the sequence of request objects.</para></description></parameter><parameter name="last"><paramtype>BidirectionalIterator</paramtype><description><para>The iterator that denotes the end of the sequence of request objects. This may not be equal to <computeroutput>first</computeroutput>.</para></description></parameter><parameter name="out"><paramtype>OutputIterator</paramtype><description><para>If provided, the <computeroutput>status</computeroutput> objects corresponding to completed requests will be emitted through this output iterator.</para></description></parameter></signature><signature><type>BidirectionalIterator</type><template>
          <template-type-parameter name="BidirectionalIterator"/>
        </template><parameter name="first"><paramtype>BidirectionalIterator</paramtype></parameter><parameter name="last"><paramtype>BidirectionalIterator</paramtype></parameter></signature><purpose>Test whether some non-blocking requests have completed. </purpose><description><para>This routine takes in a set of requests stored in the iterator range <computeroutput/>[first,last) and tests to see if any of the requests has completed. It completes all of the requests it can, partitioning the input sequence into pending requests followed by completed requests. If an output iterator is provided, <computeroutput>status</computeroutput> objects will be emitted for each of the completed requests. This routine is similar to <computeroutput>wait_some</computeroutput>, but does not wait until any requests have completed. This routine provides functionality equivalent to <computeroutput>MPI_Testsome</computeroutput>.</para><para>

</para></description><returns><para>If the <computeroutput>out</computeroutput> parameter was provided, a pair containing the output iterator <computeroutput>out</computeroutput> after all of the <computeroutput>status</computeroutput> objects have been written through it and an iterator referencing the first completed request. If no <computeroutput>out</computeroutput> parameter was provided, only the iterator referencing the first completed request will be emitted. </para>
</returns></overloaded-function>





</namespace>
</namespace>
</header>
<header name="boost/mpi/operations.hpp">
<para>This header provides a mapping from function objects to <computeroutput>MPI_Op</computeroutput> constants used in MPI collective operations. It also provides several new function object types not present in the standard <computeroutput/> &lt;functional&gt; header that have direct mappings to <computeroutput>MPI_Op</computeroutput>. </para><namespace name="boost">
<namespace name="mpi">
<struct name="bitwise_and"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Compute the bitwise AND of two integral values. </purpose><description><para>This binary function object computes the bitwise AND of the two values it is given. When used with MPI and a type <computeroutput>T</computeroutput> that has an associated, built-in MPI data type, translates to <computeroutput>MPI_BAND</computeroutput>. </para></description><typedef name="first_argument_type"><type>T</type></typedef>
<typedef name="second_argument_type"><type>T</type></typedef>
<typedef name="result_type"><type>T</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>T</type><parameter name="x"><paramtype>const T &amp;</paramtype></parameter><parameter name="y"><paramtype>const T &amp;</paramtype></parameter><description><para>
</para></description><returns><para><computeroutput>x</computeroutput> &amp; y. </para>
</returns></method>
</method-group>
</struct><struct name="bitwise_or"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Compute the bitwise OR of two integral values. </purpose><description><para>This binary function object computes the bitwise OR of the two values it is given. When used with MPI and a type <computeroutput>T</computeroutput> that has an associated, built-in MPI data type, translates to <computeroutput>MPI_BOR</computeroutput>. </para></description><typedef name="first_argument_type"><type>T</type></typedef>
<typedef name="second_argument_type"><type>T</type></typedef>
<typedef name="result_type"><type>T</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>T</type><parameter name="x"><paramtype>const T &amp;</paramtype></parameter><parameter name="y"><paramtype>const T &amp;</paramtype></parameter><description><para>
</para></description><returns><para>the <computeroutput>x</computeroutput> | y. </para>
</returns></method>
</method-group>
</struct><struct name="bitwise_xor"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Compute the bitwise exclusive OR of two integral values. </purpose><description><para>This binary function object computes the bitwise exclusive OR of the two values it is given. When used with MPI and a type <computeroutput>T</computeroutput> that has an associated, built-in MPI data type, translates to <computeroutput>MPI_BXOR</computeroutput>. </para></description><typedef name="first_argument_type"><type>T</type></typedef>
<typedef name="second_argument_type"><type>T</type></typedef>
<typedef name="result_type"><type>T</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>T</type><parameter name="x"><paramtype>const T &amp;</paramtype></parameter><parameter name="y"><paramtype>const T &amp;</paramtype></parameter><description><para>
</para></description><returns><para><computeroutput>x</computeroutput> ^ y. </para>
</returns></method>
</method-group>
</struct><struct name="is_commutative"><template>
      <template-type-parameter name="Op"/>
      <template-type-parameter name="T"/>
    </template><inherit access="public">false_</inherit><purpose>Determine if a function object type is commutative. </purpose><description><para>This trait determines if an operation <computeroutput>Op</computeroutput> is commutative when applied to values of type <computeroutput>T</computeroutput>. Parallel operations such as <computeroutput>reduce</computeroutput> and <computeroutput>prefix_sum</computeroutput> can be implemented more efficiently with commutative operations. To mark an operation as commutative, users should specialize <computeroutput><classname alt="boost::mpi::is_commutative">is_commutative</classname></computeroutput> and derive from the class <computeroutput>mpl::true_</computeroutput>. </para></description></struct><struct name="is_mpi_op"><template>
      <template-type-parameter name="Op"/>
      <template-type-parameter name="T"/>
    </template><inherit access="public">false_</inherit><purpose>Determine if a function object has an associated <computeroutput>MPI_Op</computeroutput>. </purpose><description><para>This trait determines if a function object type <computeroutput>Op</computeroutput>, when used with argument type <computeroutput>T</computeroutput>, has an associated <computeroutput>MPI_Op</computeroutput>. If so, <computeroutput>is_mpi_op&lt;Op,T&gt;</computeroutput> will derive from <computeroutput>mpl::false_</computeroutput> and will contain a static member function <computeroutput>op</computeroutput> that takes no arguments but returns the associated <computeroutput>MPI_Op</computeroutput> value. For instance, <computeroutput><classname alt="boost::mpi::is_mpi_op">is_mpi_op</classname>&lt;std::plus&lt;int&gt;</computeroutput>,int&gt;::op() returns <computeroutput>MPI_SUM</computeroutput>.</para><para>Users may specialize <computeroutput><classname alt="boost::mpi::is_mpi_op">is_mpi_op</classname></computeroutput> for any other class templates that map onto operations that have <computeroutput>MPI_Op</computeroutput> equivalences, such as bitwise OR, logical and, or maximum. However, users are encouraged to use the standard function objects in the <computeroutput>functional</computeroutput> and <computeroutput>boost/mpi/operations.hpp</computeroutput> headers whenever possible. For function objects that are class templates with a single template parameter, it may be easier to specialize <computeroutput>is_builtin_mpi_op</computeroutput>. </para></description></struct><struct name="logical_xor"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Compute the logical exclusive OR of two integral values. </purpose><description><para>This binary function object computes the logical exclusive of the two values it is given. When used with MPI and a type <computeroutput>T</computeroutput> that has an associated, built-in MPI data type, translates to <computeroutput>MPI_LXOR</computeroutput>. </para></description><typedef name="first_argument_type"><type>T</type></typedef>
<typedef name="second_argument_type"><type>T</type></typedef>
<typedef name="result_type"><type>T</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>T</type><parameter name="x"><paramtype>const T &amp;</paramtype></parameter><parameter name="y"><paramtype>const T &amp;</paramtype></parameter><description><para>
</para></description><returns><para>the logical exclusive OR of x and y. </para>
</returns></method>
</method-group>
</struct><struct name="maximum"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Compute the maximum of two values. </purpose><description><para>This binary function object computes the maximum of the two values it is given. When used with MPI and a type <computeroutput>T</computeroutput> that has an associated, built-in MPI data type, translates to <computeroutput>MPI_MAX</computeroutput>. </para></description><typedef name="first_argument_type"><type>T</type></typedef>
<typedef name="second_argument_type"><type>T</type></typedef>
<typedef name="result_type"><type>T</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>const T &amp;</type><parameter name="x"><paramtype>const T &amp;</paramtype></parameter><parameter name="y"><paramtype>const T &amp;</paramtype></parameter><description><para>
</para></description><returns><para>the maximum of x and y. </para>
</returns></method>
</method-group>
</struct><struct name="minimum"><template>
      <template-type-parameter name="T"/>
    </template><purpose>Compute the minimum of two values. </purpose><description><para>This binary function object computes the minimum of the two values it is given. When used with MPI and a type <computeroutput>T</computeroutput> that has an associated, built-in MPI data type, translates to <computeroutput>MPI_MIN</computeroutput>. </para></description><typedef name="first_argument_type"><type>T</type></typedef>
<typedef name="second_argument_type"><type>T</type></typedef>
<typedef name="result_type"><type>T</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>const T &amp;</type><parameter name="x"><paramtype>const T &amp;</paramtype></parameter><parameter name="y"><paramtype>const T &amp;</paramtype></parameter><description><para>
</para></description><returns><para>the minimum of x and y. </para>
</returns></method>
</method-group>
</struct>

























































































</namespace>
</namespace>
</header>
<header name="boost/mpi/packed_iarchive.hpp">
<para>This header provides the facilities for packing Serializable data types into a buffer using <computeroutput>MPI_Pack</computeroutput>. The buffers can then be transmitted via MPI and then be unpacked either via the facilities in <computeroutput>packed_oarchive.hpp</computeroutput> or <computeroutput>MPI_Unpack</computeroutput>. </para><namespace name="boost">
<namespace name="mpi">
<class name="packed_iarchive"><inherit access="public">iprimitive</inherit><purpose>An archive that unpacks binary data from an MPI buffer. </purpose><description><para>The <computeroutput><classname alt="boost::mpi::packed_oarchive">packed_oarchive</classname></computeroutput> class is an Archiver (as in the Boost.Serialization library) that unpacks binary data from a buffer received via MPI. It can operate on any Serializable data type and will use the <computeroutput>MPI_Unpack</computeroutput> function of the underlying MPI implementation to perform deserialization. </para></description><method-group name="public member functions">
<method name="load_override"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T &amp;</paramtype></parameter><parameter name=""><paramtype>mpl::false_</paramtype></parameter></method>
<method name="load_override"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T &amp;</paramtype></parameter><parameter name=""><paramtype>mpl::true_</paramtype></parameter></method>
<method name="load_override"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T &amp;</paramtype></parameter></method>
<method name="load_override"><type>void</type><parameter name=""><paramtype>archive::class_id_optional_type &amp;</paramtype></parameter></method>
<method name="load_override"><type>void</type><parameter name="t"><paramtype>archive::class_id_type &amp;</paramtype></parameter></method>
<method name="load_override"><type>void</type><parameter name="t"><paramtype>archive::version_type &amp;</paramtype></parameter></method>
<method name="load_override"><type>void</type><parameter name="t"><paramtype>archive::class_id_reference_type &amp;</paramtype></parameter></method>
<method name="load_override"><type>void</type><parameter name="t"><paramtype>archive::class_name_type &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="comm"><paramtype>MPI_Comm const &amp;</paramtype><description><para>The communicator over which this archive will be received.</para></description></parameter><parameter name="b"><paramtype>buffer_type &amp;</paramtype><description><para>A user-defined buffer that contains the binary representation of serialized objects.</para></description></parameter><parameter name="flags"><paramtype>unsigned int</paramtype><default>boost::archive::no_header</default><description><para>Control the serialization of the data types. Refer to the Boost.Serialization documentation before changing the default flags. </para></description></parameter><parameter name="position"><paramtype>int</paramtype><default>0</default></parameter><description><para>Construct a <computeroutput><classname alt="boost::mpi::packed_iarchive">packed_iarchive</classname></computeroutput> to receive data over the given MPI communicator and with an initial buffer.</para><para>
</para></description></constructor>
<constructor><parameter name="comm"><paramtype>MPI_Comm const &amp;</paramtype><description><para>The communicator over which this archive will be received.</para></description></parameter><parameter name="s"><paramtype>std::size_t</paramtype><default>0</default></parameter><parameter name="flags"><paramtype>unsigned int</paramtype><default>boost::archive::no_header</default><description><para>Control the serialization of the data types. Refer to the Boost.Serialization documentation before changing the default flags. </para></description></parameter><description><para>Construct a <computeroutput><classname alt="boost::mpi::packed_iarchive">packed_iarchive</classname></computeroutput> to receive data over the given MPI communicator.</para><para>
</para></description></constructor>
</class><typedef name="iprimitive"><type>packed_iprimitive</type></typedef>


























































































</namespace>
</namespace>
</header>
<header name="boost/mpi/packed_oarchive.hpp">
<para>This header provides the facilities for unpacking Serializable data types from a buffer using <computeroutput>MPI_Unpack</computeroutput>. The buffers are typically received via MPI and have been packed either by via the facilities in <computeroutput>packed_iarchive.hpp</computeroutput> or <computeroutput>MPI_Pack</computeroutput>. </para><namespace name="boost">
<namespace name="mpi">
<class name="packed_oarchive"><inherit access="public">oprimitive</inherit><purpose>An archive that packs binary data into an MPI buffer. </purpose><description><para>The <computeroutput><classname alt="boost::mpi::packed_iarchive">packed_iarchive</classname></computeroutput> class is an Archiver (as in the Boost.Serialization library) that packs binary data into a buffer for transmission via MPI. It can operate on any Serializable data type and will use the <computeroutput>MPI_Pack</computeroutput> function of the underlying MPI implementation to perform serialization. </para></description><method-group name="public member functions">
<method name="save_override"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T const &amp;</paramtype></parameter><parameter name=""><paramtype>mpl::false_</paramtype></parameter></method>
<method name="save_override"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T const &amp;</paramtype></parameter><parameter name=""><paramtype>mpl::true_</paramtype></parameter></method>
<method name="save_override"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T const &amp;</paramtype></parameter></method>
<method name="save_override"><type>void</type><parameter name=""><paramtype>const archive::class_id_optional_type &amp;</paramtype></parameter></method>
<method name="save_override"><type>void</type><parameter name="t"><paramtype>const archive::class_name_type &amp;</paramtype></parameter></method>
<method name="save_override"><type>void</type><parameter name="t"><paramtype>const archive::class_id_type &amp;</paramtype></parameter></method>
<method name="save_override"><type>void</type><parameter name="t"><paramtype>const archive::version_type &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="comm"><paramtype>MPI_Comm const &amp;</paramtype><description><para>The communicator over which this archive will be sent.</para></description></parameter><parameter name="b"><paramtype>buffer_type &amp;</paramtype><description><para>A user-defined buffer that will be filled with the binary representation of serialized objects.</para></description></parameter><parameter name="flags"><paramtype>unsigned int</paramtype><default>boost::archive::no_header</default><description><para>Control the serialization of the data types. Refer to the Boost.Serialization documentation before changing the default flags.</para></description></parameter><description><para>Construct a <computeroutput><classname alt="boost::mpi::packed_oarchive">packed_oarchive</classname></computeroutput> for transmission over the given MPI communicator and with an initial buffer.</para><para>
</para></description></constructor>
<constructor><parameter name="comm"><paramtype>MPI_Comm const &amp;</paramtype><description><para>The communicator over which this archive will be sent.</para></description></parameter><parameter name="flags"><paramtype>unsigned int</paramtype><default>boost::archive::no_header</default><description><para>Control the serialization of the data types. Refer to the Boost.Serialization documentation before changing the default flags. </para></description></parameter><description><para>Construct a <computeroutput><classname alt="boost::mpi::packed_oarchive">packed_oarchive</classname></computeroutput> for transmission over the given MPI communicator.</para><para>
</para></description></constructor>
</class><typedef name="oprimitive"><type>packed_oprimitive</type></typedef>


























































































</namespace>
</namespace>
</header>
<header name="boost/mpi/python.hpp">
<para>This header interacts with the Python bindings for Boost.MPI. The routines in this header can be used to register user-defined and library-defined data types with Boost.MPI for efficient (de-)serialization and separate transmission of skeletons and content. </para><namespace name="boost">
<namespace name="mpi">
<namespace name="python">
<function name="register_serialized"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="value"><paramtype>const T &amp;</paramtype><default>T()</default><description><para>A sample value of the type <computeroutput>T</computeroutput>. This may be used to compute the Python type associated with the C++ type <computeroutput>T</computeroutput>.</para></description></parameter><parameter name="type"><paramtype>PyTypeObject *</paramtype><default>0</default><description><para>The Python type associated with the C++ type <computeroutput>T</computeroutput>. If not provided, it will be computed from the same value <computeroutput>value</computeroutput>. </para></description></parameter><purpose>Register the type T for direct serialization within Boost.MPI. </purpose><description><para>The <computeroutput>register_serialized</computeroutput> function registers a C++ type for direct serialization within Boost.MPI. Direct serialization elides the use of the Python <computeroutput>pickle</computeroutput> package when serializing Python objects that represent C++ values. Direct serialization can be beneficial both to improve serialization performance (Python pickling can be very inefficient) and to permit serialization for Python-wrapped C++ objects that do not support pickling.</para><para>
</para></description></function>
<function name="register_skeleton_and_content"><type>void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="value"><paramtype>const T &amp;</paramtype><default>T()</default><description><para>A sample object of type T that will be used to determine the Python type associated with T, if <computeroutput>type</computeroutput> is not specified.</para></description></parameter><parameter name="type"><paramtype>PyTypeObject *</paramtype><default>0</default><description><para>The Python type associated with the C++ type <computeroutput>T</computeroutput>. If not provided, it will be computed from the same value <computeroutput>value</computeroutput>. </para></description></parameter><purpose>Registers a type for use with the skeleton/content mechanism in Python. </purpose><description><para>The skeleton/content mechanism can only be used from Python with C++ types that have previously been registered via a call to this function. Both the sender and the transmitter must register the type. It is permitted to call this function multiple times for the same type <computeroutput>T</computeroutput>, but only one call per process per type is required. The type <computeroutput>T</computeroutput> must be Serializable.</para><para>
</para></description></function>
</namespace>


























































































</namespace>
</namespace>
</header>
<header name="boost/mpi/request.hpp">
<para>This header defines the class <computeroutput>request</computeroutput>, which contains a request for non-blocking communication. </para><namespace name="boost">
<namespace name="mpi">
<class name="request"><purpose>A request for a non-blocking send or receive. </purpose><description><para>This structure contains information about a non-blocking send or receive and will be returned from <computeroutput>isend</computeroutput> or <computeroutput>irecv</computeroutput>, respectively. </para></description><class name="handler"><method-group name="public member functions">
<method name="wait" cv="= 0" specifiers="virtual"><type><classname>status</classname></type></method>
<method name="test" cv="= 0" specifiers="virtual"><type>optional&lt; <classname>status</classname> &gt;</type></method>
<method name="cancel" cv="= 0" specifiers="virtual"><type>void</type></method>
<method name="active" cv="const = 0" specifiers="virtual"><type>bool</type></method>
<method name="trivial" cv="= 0" specifiers="virtual"><type>optional&lt; MPI_Request &amp; &gt;</type></method>
</method-group>
<destructor/>
</class><class name="legacy_dynamic_primitive_array_handler"><template>
      <template-type-parameter name="T"/>
      <template-type-parameter name="A"/>
    </template></class><class name="legacy_serialized_array_handler"><template>
      <template-type-parameter name="T"/>
    </template></class><class name="legacy_serialized_handler"><template>
      <template-type-parameter name="T"/>
    </template></class><method-group name="public member functions">
<method name="wait"><type><classname>status</classname></type><description><para>Wait until the communication associated with this request has completed, then return a <computeroutput>status</computeroutput> object describing the communication. </para></description></method>
<method name="test"><type>optional&lt; <classname>status</classname> &gt;</type><description><para>Determine whether the communication associated with this request has completed successfully. If so, returns the <computeroutput>status</computeroutput> object describing the communication. Otherwise, returns an empty <computeroutput>optional&lt;&gt;</computeroutput> to indicate that the communication has not completed yet. Note that once <computeroutput>test()</computeroutput> returns a <computeroutput>status</computeroutput> object, the request has completed and <computeroutput>wait()</computeroutput> should not be called. </para></description></method>
<method name="cancel"><type>void</type><description><para>Cancel a pending communication, assuming it has not already been completed. </para></description></method>
<method name="trivial"><type>optional&lt; MPI_Request &amp; &gt;</type><description><para>The trivial MPI requet implenting this request, provided it's trivial. Probably irrelevant to most users. </para></description></method>
<method name="active" cv="const"><type>bool</type><description><para>Is this request potentialy pending ? </para></description></method>
<method name="preserve"><type>void</type><parameter name="d"><paramtype>boost::shared_ptr&lt; void &gt;</paramtype></parameter></method>
</method-group>
<constructor><description><para>Constructs a NULL request. </para></description></constructor>
<method-group name="public static functions">
<method name="make_trivial_send" specifiers="static"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype><classname>communicator</classname> const &amp;</paramtype></parameter><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="value"><paramtype>T const &amp;</paramtype></parameter><description><para>Send a known number of primitive objects in one MPI request. </para></description></method>
<method name="make_trivial_send" specifiers="static"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype><classname>communicator</classname> const &amp;</paramtype></parameter><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="values"><paramtype>T const *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter></method>
<method name="make_packed_send" specifiers="static"><type><classname>request</classname></type><parameter name="comm"><paramtype><classname>communicator</classname> const &amp;</paramtype></parameter><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="values"><paramtype>void const *</paramtype></parameter><parameter name="n"><paramtype>std::size_t</paramtype></parameter></method>
<method name="make_bottom_send" specifiers="static"><type><classname>request</classname></type><parameter name="comm"><paramtype><classname>communicator</classname> const &amp;</paramtype></parameter><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="tp"><paramtype>MPI_Datatype</paramtype></parameter></method>
<method name="make_empty_send" specifiers="static"><type><classname>request</classname></type><parameter name="comm"><paramtype><classname>communicator</classname> const &amp;</paramtype></parameter><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter></method>
<method name="make_trivial_recv" specifiers="static"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype><classname>communicator</classname> const &amp;</paramtype></parameter><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="value"><paramtype>T &amp;</paramtype></parameter><description><para>Receive a known number of primitive objects in one MPI request. </para></description></method>
<method name="make_trivial_recv" specifiers="static"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype><classname>communicator</classname> const &amp;</paramtype></parameter><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="values"><paramtype>T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter></method>
<method name="make_bottom_recv" specifiers="static"><type><classname>request</classname></type><parameter name="comm"><paramtype><classname>communicator</classname> const &amp;</paramtype></parameter><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="tp"><paramtype>MPI_Datatype</paramtype></parameter></method>
<method name="make_empty_recv" specifiers="static"><type><classname>request</classname></type><parameter name="comm"><paramtype><classname>communicator</classname> const &amp;</paramtype></parameter><parameter name="dest"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter></method>
<method name="make_dynamic" specifiers="static"><type><classname>request</classname></type><description><para>Construct request for simple data of unknown size. </para></description></method>
<method name="make_serialized" specifiers="static"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype><classname>communicator</classname> const &amp;</paramtype></parameter><parameter name="source"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="value"><paramtype>T &amp;</paramtype></parameter><description><para>Constructs request for serialized data. </para></description></method>
<method name="make_serialized_array" specifiers="static"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="comm"><paramtype><classname>communicator</classname> const &amp;</paramtype></parameter><parameter name="source"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="values"><paramtype>T *</paramtype></parameter><parameter name="n"><paramtype>int</paramtype></parameter><description><para>Constructs request for array of complex data. </para></description></method>
<method name="make_dynamic_primitive_array_recv" specifiers="static"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="comm"><paramtype><classname>communicator</classname> const &amp;</paramtype></parameter><parameter name="source"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="values"><paramtype>std::vector&lt; T, A &gt; &amp;</paramtype></parameter><description><para>Request to recv array of primitive data. </para></description></method>
<method name="make_dynamic_primitive_array_send" specifiers="static"><type><classname>request</classname></type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="A"/>
        </template><parameter name="comm"><paramtype><classname>communicator</classname> const &amp;</paramtype></parameter><parameter name="source"><paramtype>int</paramtype></parameter><parameter name="tag"><paramtype>int</paramtype></parameter><parameter name="values"><paramtype>std::vector&lt; T, A &gt; const &amp;</paramtype></parameter><description><para>Request to send array of primitive data. </para></description></method>
</method-group>
<method-group name="private member functions">
</method-group>
<constructor><parameter name="h"><paramtype><classname>handler</classname> *</paramtype></parameter></constructor>
</class>

























































































</namespace>
</namespace>
</header>
<header name="boost/mpi/skeleton_and_content.hpp">
<para>This header provides facilities that allow the structure of data types (called the "skeleton") to be transmitted and received separately from the content stored in those data types. These facilities are useful when the data in a stable data structure (e.g., a mesh or a graph) will need to be transmitted repeatedly. In this case, transmitting the skeleton only once saves both communication effort (it need not be sent again) and local computation (serialization need only be performed once for the content). </para><namespace name="boost">
<namespace name="mpi">


























































































</namespace>
</namespace>
</header>
<header name="boost/mpi/skeleton_and_content_fwd.hpp">
<para>This header contains all of the forward declarations required to use transmit skeletons of data structures and the content of data structures separately. To actually transmit skeletons or content, include the header <computeroutput>boost/mpi/skeleton_and_content.hpp</computeroutput>. </para><namespace name="boost">
<namespace name="mpi">
<struct name="skeleton_proxy"><template>
      <template-type-parameter name="T"/>
    </template></struct>





















































































<function name="skeleton"><type>const <classname>skeleton_proxy</classname>&lt; T &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T &amp;</paramtype></parameter></function>
<function name="get_content"><type>const content</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>const T &amp;</paramtype></parameter></function>


</namespace>
</namespace>
</header>
<header name="boost/mpi/status.hpp">
<para>This header defines the class <computeroutput>status</computeroutput>, which reports on the results of point-to-point communication. </para><namespace name="boost">
<namespace name="mpi">
<class name="status"><purpose>Contains information about a message that has been or can be received. </purpose><description><para>This structure contains status information about messages that have been received (with <computeroutput>communicator::recv</computeroutput>) or can be received (returned from <computeroutput>communicator::probe</computeroutput> or <computeroutput>communicator::iprobe</computeroutput>). It permits access to the source of the message, message tag, error code (rarely used), or the number of elements that have been transmitted. </para></description><data-member name="m_count" specifiers="mutable"><type>int</type></data-member>
<method-group name="public member functions">
<method name="source" cv="const"><type>int</type><description><para>Retrieve the source of the message. </para></description></method>
<method name="tag" cv="const"><type>int</type><description><para>Retrieve the message tag. </para></description></method>
<method name="error" cv="const"><type>int</type><description><para>Retrieve the error code. </para></description></method>
<method name="cancelled" cv="const"><type>bool</type><description><para>Determine whether the communication associated with this object has been successfully cancelled. </para></description></method>
<method name="count" cv="const"><type>optional&lt; int &gt;</type><template>
          <template-type-parameter name="T"/>
        </template><description><para>Determines the number of elements of type <computeroutput>T</computeroutput> contained in the message. The type <computeroutput>T</computeroutput> must have an associated data type, i.e., <computeroutput>is_mpi_datatype&lt;T&gt;</computeroutput> must derive <computeroutput>mpl::true_</computeroutput>. In cases where the type <computeroutput>T</computeroutput> does not match the transmitted type, this routine will return an empty <computeroutput>optional&lt;int&gt;</computeroutput>.</para><para>
</para></description><returns><para>the number of <computeroutput>T</computeroutput> elements in the message, if it can be determined. </para>
</returns></method>
<method name="conversion-operator"><type>MPI_Status &amp;</type><description><para>References the underlying <computeroutput>MPI_Status</computeroutput> </para></description></method>
<method name="conversion-operator" cv="const"><type>const MPI_Status &amp;</type><description><para>References the underlying <computeroutput>MPI_Status</computeroutput> </para></description></method>
</method-group>
<constructor/>
<constructor><parameter name="s"><paramtype>MPI_Status const &amp;</paramtype></parameter></constructor>
</class>

























































































</namespace>
</namespace>
</header>
<header name="boost/mpi/timer.hpp">
<para>This header provides the <computeroutput>timer</computeroutput> class, which provides access to the MPI timers. </para><namespace name="boost">
<namespace name="mpi">
<class name="timer"><purpose>A simple timer that provides access to the MPI timing facilities. </purpose><description><para>The <computeroutput>timer</computeroutput> class is a simple wrapper around the MPI timing facilities that mimics the interface of the Boost Timer library. </para></description><method-group name="public member functions">
<method name="restart"><type>void</type><description><para>Restart the timer.</para><para>
</para></description><postconditions><para><computeroutput>elapsed()</computeroutput> == 0 </para>
</postconditions></method>
<method name="elapsed" cv="const"><type>double</type><description><para>Return the amount of time that has elapsed since the last construction or reset, in seconds. </para></description></method>
<method name="elapsed_max" cv="const"><type>double</type><description><para>Return an estimate of the maximum possible value of elapsed(). Note that this routine may return too high a value on some systems. </para></description></method>
<method name="elapsed_min" cv="const"><type>double</type><description><para>Returns the minimum non-zero value that <computeroutput>elapsed()</computeroutput> may return. This is the resolution of the timer. </para></description></method>
</method-group>
<constructor><description><para>Initializes the timer</para><para>
</para></description><postconditions><para><computeroutput>elapsed()</computeroutput> == 0 </para>
</postconditions></constructor>
<method-group name="public static functions">
<method name="time_is_global" specifiers="static"><type>bool</type><description><para>Determines whether the elapsed time values are global times or local processor times. </para></description></method>
</method-group>
</class>

























































































</namespace>
</namespace>
</header>
</library-reference>