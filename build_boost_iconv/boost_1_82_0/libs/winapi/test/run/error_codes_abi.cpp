/*
 *             Copyright <PERSON><PERSON> 2018.
 * Distributed under the Boost Software License, Version 1.0.
 *    (See accompanying file LICENSE_1_0.txt or copy at
 *          http://www.boost.org/LICENSE_1_0.txt)
 */
/*!
 * \file   error_codes_abi.cpp
 * \author <PERSON><PERSON>
 * \date   09.03.2018
 *
 * \brief  This file contains ABI test for error_codes.hpp
 */

#include <boost/winapi/error_codes.hpp>
#include <windows.h>
#include <winerror.h>
#include <boost/predef/platform/windows_uwp.h>
#include "abi_test_tools.hpp"

int main()
{
#if defined(FACILITY_NULL)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_NULL);
#endif
#if defined(FACILITY_RPC)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_RPC);
#endif
#if defined(FACILITY_DISPATCH)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DISPATCH);
#endif
#if defined(FACILITY_STORAGE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_STORAGE);
#endif
#if defined(FACILITY_ITF)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_ITF);
#endif
#if defined(FACILITY_WIN32)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WIN32);
#endif
#if defined(FACILITY_WINDOWS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WINDOWS);
#endif
#if defined(FACILITY_SSPI)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_SSPI);
#endif
#if defined(FACILITY_SECURITY)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_SECURITY);
#endif
#if defined(FACILITY_CONTROL)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_CONTROL);
#endif
#if defined(FACILITY_CERT)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_CERT);
#endif
#if defined(FACILITY_INTERNET)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_INTERNET);
#endif
#if defined(FACILITY_MEDIASERVER)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_MEDIASERVER);
#endif
#if defined(FACILITY_MSMQ)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_MSMQ);
#endif
#if defined(FACILITY_SETUPAPI)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_SETUPAPI);
#endif
#if defined(FACILITY_SCARD)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_SCARD);
#endif
#if defined(FACILITY_COMPLUS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_COMPLUS);
#endif
#if defined(FACILITY_AAF)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_AAF);
#endif
#if defined(FACILITY_URT)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_URT);
#endif
#if defined(FACILITY_ACS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_ACS);
#endif
#if defined(FACILITY_DPLAY)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DPLAY);
#endif
#if defined(FACILITY_UMI)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_UMI);
#endif
#if defined(FACILITY_SXS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_SXS);
#endif
#if defined(FACILITY_WINDOWS_CE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WINDOWS_CE);
#endif
#if defined(FACILITY_HTTP)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_HTTP);
#endif
#if defined(FACILITY_USERMODE_COMMONLOG)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_USERMODE_COMMONLOG);
#endif
#if defined(FACILITY_WER)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WER);
#endif
#if defined(FACILITY_USERMODE_FILTER_MANAGER)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_USERMODE_FILTER_MANAGER);
#endif
#if defined(FACILITY_BACKGROUNDCOPY)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_BACKGROUNDCOPY);
#endif
#if defined(FACILITY_CONFIGURATION)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_CONFIGURATION);
#endif
#if defined(FACILITY_WIA)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WIA);
#endif
#if defined(FACILITY_STATE_MANAGEMENT)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_STATE_MANAGEMENT);
#endif
#if defined(FACILITY_METADIRECTORY)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_METADIRECTORY);
#endif
#if defined(FACILITY_WINDOWSUPDATE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WINDOWSUPDATE);
#endif
#if defined(FACILITY_DIRECTORYSERVICE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DIRECTORYSERVICE);
#endif
#if defined(FACILITY_GRAPHICS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_GRAPHICS);
#endif
#if defined(FACILITY_SHELL)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_SHELL);
#endif
#if defined(FACILITY_NAP)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_NAP);
#endif
#if defined(FACILITY_TPM_SERVICES)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_TPM_SERVICES);
#endif
#if defined(FACILITY_TPM_SOFTWARE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_TPM_SOFTWARE);
#endif
#if defined(FACILITY_UI)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_UI);
#endif
#if defined(FACILITY_XAML)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_XAML);
#endif
#if defined(FACILITY_ACTION_QUEUE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_ACTION_QUEUE);
#endif
#if defined(FACILITY_PLA)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_PLA);
#endif
#if defined(FACILITY_WINDOWS_SETUP)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WINDOWS_SETUP);
#endif
#if defined(FACILITY_FVE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_FVE);
#endif
#if defined(FACILITY_FWP)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_FWP);
#endif
#if defined(FACILITY_WINRM)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WINRM);
#endif
#if defined(FACILITY_NDIS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_NDIS);
#endif
#if defined(FACILITY_USERMODE_HYPERVISOR)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_USERMODE_HYPERVISOR);
#endif
#if defined(FACILITY_CMI)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_CMI);
#endif
#if defined(FACILITY_USERMODE_VIRTUALIZATION)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_USERMODE_VIRTUALIZATION);
#endif
#if defined(FACILITY_USERMODE_VOLMGR)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_USERMODE_VOLMGR);
#endif
#if defined(FACILITY_BCD)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_BCD);
#endif
#if defined(FACILITY_USERMODE_VHD)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_USERMODE_VHD);
#endif
#if defined(FACILITY_USERMODE_HNS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_USERMODE_HNS);
#endif
#if defined(FACILITY_SDIAG)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_SDIAG);
#endif
#if defined(FACILITY_WEBSERVICES)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WEBSERVICES);
#endif
#if defined(FACILITY_WINPE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WINPE);
#endif
#if defined(FACILITY_WPN)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WPN);
#endif
#if defined(FACILITY_WINDOWS_STORE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WINDOWS_STORE);
#endif
#if defined(FACILITY_INPUT)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_INPUT);
#endif
#if defined(FACILITY_EAP)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_EAP);
#endif
#if defined(FACILITY_WINDOWS_DEFENDER)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WINDOWS_DEFENDER);
#endif
#if defined(FACILITY_OPC)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_OPC);
#endif
#if defined(FACILITY_XPS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_XPS);
#endif
#if defined(FACILITY_MBN)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_MBN);
#endif
#if defined(FACILITY_POWERSHELL)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_POWERSHELL);
#endif
#if defined(FACILITY_RAS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_RAS);
#endif
#if defined(FACILITY_P2P_INT)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_P2P_INT);
#endif
#if defined(FACILITY_P2P)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_P2P);
#endif
#if defined(FACILITY_DAF)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DAF);
#endif
#if defined(FACILITY_BLUETOOTH_ATT)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_BLUETOOTH_ATT);
#endif
#if defined(FACILITY_AUDIO)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_AUDIO);
#endif
#if defined(FACILITY_STATEREPOSITORY)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_STATEREPOSITORY);
#endif
#if defined(FACILITY_VISUALCPP)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_VISUALCPP);
#endif
#if defined(FACILITY_SCRIPT)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_SCRIPT);
#endif
#if defined(FACILITY_PARSE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_PARSE);
#endif
#if defined(FACILITY_BLB)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_BLB);
#endif
#if defined(FACILITY_BLB_CLI)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_BLB_CLI);
#endif
#if defined(FACILITY_WSBAPP)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WSBAPP);
#endif
#if defined(FACILITY_BLBUI)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_BLBUI);
#endif
#if defined(FACILITY_USN)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_USN);
#endif
#if defined(FACILITY_USERMODE_VOLSNAP)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_USERMODE_VOLSNAP);
#endif
#if defined(FACILITY_TIERING)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_TIERING);
#endif
#if defined(FACILITY_WSB_ONLINE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WSB_ONLINE);
#endif
#if defined(FACILITY_ONLINE_ID)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_ONLINE_ID);
#endif
#if defined(FACILITY_DEVICE_UPDATE_AGENT)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEVICE_UPDATE_AGENT);
#endif
#if defined(FACILITY_DLS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DLS);
#endif
#if defined(FACILITY_DELIVERY_OPTIMIZATION)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DELIVERY_OPTIMIZATION);
#endif
#if defined(FACILITY_USERMODE_SPACES)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_USERMODE_SPACES);
#endif
#if defined(FACILITY_USER_MODE_SECURITY_CORE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_USER_MODE_SECURITY_CORE);
#endif
#if defined(FACILITY_USERMODE_LICENSING)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_USERMODE_LICENSING);
#endif
#if defined(FACILITY_SOS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_SOS);
#endif
#if defined(FACILITY_DEBUGGERS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEBUGGERS);
#endif
#if defined(FACILITY_SPP)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_SPP);
#endif
#if defined(FACILITY_RESTORE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_RESTORE);
#endif
#if defined(FACILITY_DMSERVER)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DMSERVER);
#endif
#if defined(FACILITY_DEPLOYMENT_SERVICES_SERVER)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEPLOYMENT_SERVICES_SERVER);
#endif
#if defined(FACILITY_DEPLOYMENT_SERVICES_IMAGING)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEPLOYMENT_SERVICES_IMAGING);
#endif
#if defined(FACILITY_DEPLOYMENT_SERVICES_MANAGEMENT)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEPLOYMENT_SERVICES_MANAGEMENT);
#endif
#if defined(FACILITY_DEPLOYMENT_SERVICES_UTIL)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEPLOYMENT_SERVICES_UTIL);
#endif
#if defined(FACILITY_DEPLOYMENT_SERVICES_BINLSVC)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEPLOYMENT_SERVICES_BINLSVC);
#endif
#if defined(FACILITY_DEPLOYMENT_SERVICES_PXE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEPLOYMENT_SERVICES_PXE);
#endif
#if defined(FACILITY_DEPLOYMENT_SERVICES_TFTP)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEPLOYMENT_SERVICES_TFTP);
#endif
#if defined(FACILITY_DEPLOYMENT_SERVICES_TRANSPORT_MANAGEMENT)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEPLOYMENT_SERVICES_TRANSPORT_MANAGEMENT);
#endif
#if defined(FACILITY_DEPLOYMENT_SERVICES_DRIVER_PROVISIONING)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEPLOYMENT_SERVICES_DRIVER_PROVISIONING);
#endif
#if defined(FACILITY_DEPLOYMENT_SERVICES_MULTICAST_SERVER)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEPLOYMENT_SERVICES_MULTICAST_SERVER);
#endif
#if defined(FACILITY_DEPLOYMENT_SERVICES_MULTICAST_CLIENT)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEPLOYMENT_SERVICES_MULTICAST_CLIENT);
#endif
#if defined(FACILITY_DEPLOYMENT_SERVICES_CONTENT_PROVIDER)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEPLOYMENT_SERVICES_CONTENT_PROVIDER);
#endif
#if defined(FACILITY_LINGUISTIC_SERVICES)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_LINGUISTIC_SERVICES);
#endif
#if defined(FACILITY_AUDIOSTREAMING)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_AUDIOSTREAMING);
#endif
#if defined(FACILITY_ACCELERATOR)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_ACCELERATOR);
#endif
#if defined(FACILITY_WMAAECMA)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WMAAECMA);
#endif
#if defined(FACILITY_DIRECTMUSIC)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DIRECTMUSIC);
#endif
#if defined(FACILITY_DIRECT3D10)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DIRECT3D10);
#endif
#if defined(FACILITY_DXGI)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DXGI);
#endif
#if defined(FACILITY_DXGI_DDI)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DXGI_DDI);
#endif
#if defined(FACILITY_DIRECT3D11)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DIRECT3D11);
#endif
#if defined(FACILITY_DIRECT3D11_DEBUG)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DIRECT3D11_DEBUG);
#endif
#if defined(FACILITY_DIRECT3D12)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DIRECT3D12);
#endif
#if defined(FACILITY_DIRECT3D12_DEBUG)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DIRECT3D12_DEBUG);
#endif
#if defined(FACILITY_LEAP)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_LEAP);
#endif
#if defined(FACILITY_AUDCLNT)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_AUDCLNT);
#endif
#if defined(FACILITY_WINCODEC_DWRITE_DWM)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WINCODEC_DWRITE_DWM);
#endif
#if defined(FACILITY_DIRECT2D)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DIRECT2D);
#endif
#if defined(FACILITY_DEFRAG)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_DEFRAG);
#endif
#if defined(FACILITY_USERMODE_SDBUS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_USERMODE_SDBUS);
#endif
#if defined(FACILITY_JSCRIPT)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_JSCRIPT);
#endif
#if defined(FACILITY_PIDGENX)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_PIDGENX);
#endif
#if defined(FACILITY_EAS)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_EAS);
#endif
#if defined(FACILITY_WEB)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WEB);
#endif
#if defined(FACILITY_WEB_SOCKET)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WEB_SOCKET);
#endif
#if defined(FACILITY_MOBILE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_MOBILE);
#endif
#if defined(FACILITY_SQLITE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_SQLITE);
#endif
#if defined(FACILITY_UTC)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_UTC);
#endif
#if defined(FACILITY_WEP)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_WEP);
#endif
#if defined(FACILITY_SYNCENGINE)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_SYNCENGINE);
#endif
#if defined(FACILITY_XBOX)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_XBOX);
#endif
#if defined(FACILITY_PIX)
    BOOST_WINAPI_TEST_CONSTANT(FACILITY_PIX);
#endif
#if defined(ERROR_SUCCESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SUCCESS);
#endif
#if defined(NO_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(NO_ERROR);
#endif
#if defined(ERROR_INVALID_FUNCTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_FUNCTION);
#endif
#if defined(ERROR_FILE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_NOT_FOUND);
#endif
#if defined(ERROR_PATH_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PATH_NOT_FOUND);
#endif
#if defined(ERROR_TOO_MANY_OPEN_FILES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_OPEN_FILES);
#endif
#if defined(ERROR_ACCESS_DENIED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ACCESS_DENIED);
#endif
#if defined(ERROR_INVALID_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_HANDLE);
#endif
#if defined(ERROR_ARENA_TRASHED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ARENA_TRASHED);
#endif
#if defined(ERROR_NOT_ENOUGH_MEMORY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_ENOUGH_MEMORY);
#endif
#if defined(ERROR_INVALID_BLOCK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_BLOCK);
#endif
#if defined(ERROR_BAD_ENVIRONMENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_ENVIRONMENT);
#endif
#if defined(ERROR_BAD_FORMAT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_FORMAT);
#endif
#if defined(ERROR_INVALID_ACCESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_ACCESS);
#endif
#if defined(ERROR_INVALID_DATA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_DATA);
#endif
#if defined(ERROR_OUTOFMEMORY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OUTOFMEMORY);
#endif
#if defined(ERROR_INVALID_DRIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_DRIVE);
#endif
#if defined(ERROR_CURRENT_DIRECTORY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CURRENT_DIRECTORY);
#endif
#if defined(ERROR_NOT_SAME_DEVICE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_SAME_DEVICE);
#endif
#if defined(ERROR_NO_MORE_FILES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_MORE_FILES);
#endif
#if defined(ERROR_WRITE_PROTECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WRITE_PROTECT);
#endif
#if defined(ERROR_BAD_UNIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_UNIT);
#endif
#if defined(ERROR_NOT_READY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_READY);
#endif
#if defined(ERROR_BAD_COMMAND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_COMMAND);
#endif
#if defined(ERROR_CRC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CRC);
#endif
#if defined(ERROR_BAD_LENGTH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_LENGTH);
#endif
#if defined(ERROR_SEEK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SEEK);
#endif
#if defined(ERROR_NOT_DOS_DISK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_DOS_DISK);
#endif
#if defined(ERROR_SECTOR_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECTOR_NOT_FOUND);
#endif
#if defined(ERROR_OUT_OF_PAPER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OUT_OF_PAPER);
#endif
#if defined(ERROR_WRITE_FAULT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WRITE_FAULT);
#endif
#if defined(ERROR_READ_FAULT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_READ_FAULT);
#endif
#if defined(ERROR_GEN_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GEN_FAILURE);
#endif
#if defined(ERROR_SHARING_VIOLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SHARING_VIOLATION);
#endif
#if defined(ERROR_LOCK_VIOLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOCK_VIOLATION);
#endif
#if defined(ERROR_WRONG_DISK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WRONG_DISK);
#endif
#if defined(ERROR_SHARING_BUFFER_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SHARING_BUFFER_EXCEEDED);
#endif
#if defined(ERROR_HANDLE_EOF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HANDLE_EOF);
#endif
#if defined(ERROR_HANDLE_DISK_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HANDLE_DISK_FULL);
#endif
#if defined(ERROR_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_SUPPORTED);
#endif
#if defined(ERROR_REM_NOT_LIST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REM_NOT_LIST);
#endif
#if defined(ERROR_DUP_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DUP_NAME);
#endif
#if defined(ERROR_BAD_NETPATH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_NETPATH);
#endif
#if defined(ERROR_NETWORK_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NETWORK_BUSY);
#endif
#if defined(ERROR_DEV_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEV_NOT_EXIST);
#endif
#if defined(ERROR_TOO_MANY_CMDS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_CMDS);
#endif
#if defined(ERROR_ADAP_HDW_ERR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ADAP_HDW_ERR);
#endif
#if defined(ERROR_BAD_NET_RESP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_NET_RESP);
#endif
#if defined(ERROR_UNEXP_NET_ERR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNEXP_NET_ERR);
#endif
#if defined(ERROR_BAD_REM_ADAP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_REM_ADAP);
#endif
#if defined(ERROR_PRINTQ_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINTQ_FULL);
#endif
#if defined(ERROR_NO_SPOOL_SPACE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SPOOL_SPACE);
#endif
#if defined(ERROR_PRINT_CANCELLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINT_CANCELLED);
#endif
#if defined(ERROR_NETNAME_DELETED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NETNAME_DELETED);
#endif
#if defined(ERROR_NETWORK_ACCESS_DENIED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NETWORK_ACCESS_DENIED);
#endif
#if defined(ERROR_BAD_DEV_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_DEV_TYPE);
#endif
#if defined(ERROR_BAD_NET_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_NET_NAME);
#endif
#if defined(ERROR_TOO_MANY_NAMES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_NAMES);
#endif
#if defined(ERROR_TOO_MANY_SESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_SESS);
#endif
#if defined(ERROR_SHARING_PAUSED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SHARING_PAUSED);
#endif
#if defined(ERROR_REQ_NOT_ACCEP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REQ_NOT_ACCEP);
#endif
#if defined(ERROR_REDIR_PAUSED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REDIR_PAUSED);
#endif
#if defined(ERROR_FILE_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_EXISTS);
#endif
#if defined(ERROR_CANNOT_MAKE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_MAKE);
#endif
#if defined(ERROR_FAIL_I24)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FAIL_I24);
#endif
#if defined(ERROR_OUT_OF_STRUCTURES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OUT_OF_STRUCTURES);
#endif
#if defined(ERROR_ALREADY_ASSIGNED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALREADY_ASSIGNED);
#endif
#if defined(ERROR_INVALID_PASSWORD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PASSWORD);
#endif
#if defined(ERROR_INVALID_PARAMETER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PARAMETER);
#endif
#if defined(ERROR_NET_WRITE_FAULT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NET_WRITE_FAULT);
#endif
#if defined(ERROR_NO_PROC_SLOTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_PROC_SLOTS);
#endif
#if defined(ERROR_TOO_MANY_SEMAPHORES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_SEMAPHORES);
#endif
#if defined(ERROR_EXCL_SEM_ALREADY_OWNED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EXCL_SEM_ALREADY_OWNED);
#endif
#if defined(ERROR_SEM_IS_SET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SEM_IS_SET);
#endif
#if defined(ERROR_TOO_MANY_SEM_REQUESTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_SEM_REQUESTS);
#endif
#if defined(ERROR_INVALID_AT_INTERRUPT_TIME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_AT_INTERRUPT_TIME);
#endif
#if defined(ERROR_SEM_OWNER_DIED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SEM_OWNER_DIED);
#endif
#if defined(ERROR_SEM_USER_LIMIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SEM_USER_LIMIT);
#endif
#if defined(ERROR_DISK_CHANGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISK_CHANGE);
#endif
#if defined(ERROR_DRIVE_LOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DRIVE_LOCKED);
#endif
#if defined(ERROR_BROKEN_PIPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BROKEN_PIPE);
#endif
#if defined(ERROR_OPEN_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OPEN_FAILED);
#endif
#if defined(ERROR_BUFFER_OVERFLOW)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BUFFER_OVERFLOW);
#endif
#if defined(ERROR_DISK_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISK_FULL);
#endif
#if defined(ERROR_NO_MORE_SEARCH_HANDLES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_MORE_SEARCH_HANDLES);
#endif
#if defined(ERROR_INVALID_TARGET_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_TARGET_HANDLE);
#endif
#if defined(ERROR_INVALID_CATEGORY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_CATEGORY);
#endif
#if defined(ERROR_INVALID_VERIFY_SWITCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_VERIFY_SWITCH);
#endif
#if defined(ERROR_BAD_DRIVER_LEVEL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_DRIVER_LEVEL);
#endif
#if defined(ERROR_CALL_NOT_IMPLEMENTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CALL_NOT_IMPLEMENTED);
#endif
#if defined(ERROR_SEM_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SEM_TIMEOUT);
#endif
#if defined(ERROR_INSUFFICIENT_BUFFER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSUFFICIENT_BUFFER);
#endif
#if defined(ERROR_INVALID_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_NAME);
#endif
#if defined(ERROR_INVALID_LEVEL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_LEVEL);
#endif
#if defined(ERROR_NO_VOLUME_LABEL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_VOLUME_LABEL);
#endif
#if defined(ERROR_MOD_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MOD_NOT_FOUND);
#endif
#if defined(ERROR_PROC_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROC_NOT_FOUND);
#endif
#if defined(ERROR_WAIT_NO_CHILDREN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WAIT_NO_CHILDREN);
#endif
#if defined(ERROR_CHILD_NOT_COMPLETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CHILD_NOT_COMPLETE);
#endif
#if defined(ERROR_DIRECT_ACCESS_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DIRECT_ACCESS_HANDLE);
#endif
#if defined(ERROR_NEGATIVE_SEEK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NEGATIVE_SEEK);
#endif
#if defined(ERROR_SEEK_ON_DEVICE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SEEK_ON_DEVICE);
#endif
#if defined(ERROR_IS_JOIN_TARGET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IS_JOIN_TARGET);
#endif
#if defined(ERROR_IS_JOINED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IS_JOINED);
#endif
#if defined(ERROR_IS_SUBSTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IS_SUBSTED);
#endif
#if defined(ERROR_NOT_JOINED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_JOINED);
#endif
#if defined(ERROR_NOT_SUBSTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_SUBSTED);
#endif
#if defined(ERROR_JOIN_TO_JOIN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_JOIN_TO_JOIN);
#endif
#if defined(ERROR_SUBST_TO_SUBST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SUBST_TO_SUBST);
#endif
#if defined(ERROR_JOIN_TO_SUBST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_JOIN_TO_SUBST);
#endif
#if defined(ERROR_SUBST_TO_JOIN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SUBST_TO_JOIN);
#endif
#if defined(ERROR_BUSY_DRIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BUSY_DRIVE);
#endif
#if defined(ERROR_SAME_DRIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SAME_DRIVE);
#endif
#if defined(ERROR_DIR_NOT_ROOT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DIR_NOT_ROOT);
#endif
#if defined(ERROR_DIR_NOT_EMPTY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DIR_NOT_EMPTY);
#endif
#if defined(ERROR_IS_SUBST_PATH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IS_SUBST_PATH);
#endif
#if defined(ERROR_IS_JOIN_PATH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IS_JOIN_PATH);
#endif
#if defined(ERROR_PATH_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PATH_BUSY);
#endif
#if defined(ERROR_IS_SUBST_TARGET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IS_SUBST_TARGET);
#endif
#if defined(ERROR_SYSTEM_TRACE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYSTEM_TRACE);
#endif
#if defined(ERROR_INVALID_EVENT_COUNT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_EVENT_COUNT);
#endif
#if defined(ERROR_TOO_MANY_MUXWAITERS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_MUXWAITERS);
#endif
#if defined(ERROR_INVALID_LIST_FORMAT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_LIST_FORMAT);
#endif
#if defined(ERROR_LABEL_TOO_LONG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LABEL_TOO_LONG);
#endif
#if defined(ERROR_TOO_MANY_TCBS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_TCBS);
#endif
#if defined(ERROR_SIGNAL_REFUSED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SIGNAL_REFUSED);
#endif
#if defined(ERROR_DISCARDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISCARDED);
#endif
#if defined(ERROR_NOT_LOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_LOCKED);
#endif
#if defined(ERROR_BAD_THREADID_ADDR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_THREADID_ADDR);
#endif
#if defined(ERROR_BAD_ARGUMENTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_ARGUMENTS);
#endif
#if defined(ERROR_BAD_PATHNAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_PATHNAME);
#endif
#if defined(ERROR_SIGNAL_PENDING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SIGNAL_PENDING);
#endif
#if defined(ERROR_MAX_THRDS_REACHED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MAX_THRDS_REACHED);
#endif
#if defined(ERROR_LOCK_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOCK_FAILED);
#endif
#if defined(ERROR_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BUSY);
#endif
#if defined(ERROR_DEVICE_SUPPORT_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_SUPPORT_IN_PROGRESS);
#endif
#if defined(ERROR_CANCEL_VIOLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANCEL_VIOLATION);
#endif
#if defined(ERROR_ATOMIC_LOCKS_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ATOMIC_LOCKS_NOT_SUPPORTED);
#endif
#if defined(ERROR_INVALID_SEGMENT_NUMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SEGMENT_NUMBER);
#endif
#if defined(ERROR_INVALID_ORDINAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_ORDINAL);
#endif
#if defined(ERROR_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALREADY_EXISTS);
#endif
#if defined(ERROR_INVALID_FLAG_NUMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_FLAG_NUMBER);
#endif
#if defined(ERROR_SEM_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SEM_NOT_FOUND);
#endif
#if defined(ERROR_INVALID_STARTING_CODESEG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_STARTING_CODESEG);
#endif
#if defined(ERROR_INVALID_STACKSEG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_STACKSEG);
#endif
#if defined(ERROR_INVALID_MODULETYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_MODULETYPE);
#endif
#if defined(ERROR_INVALID_EXE_SIGNATURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_EXE_SIGNATURE);
#endif
#if defined(ERROR_EXE_MARKED_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EXE_MARKED_INVALID);
#endif
#if defined(ERROR_BAD_EXE_FORMAT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_EXE_FORMAT);
#endif
#if defined(ERROR_ITERATED_DATA_EXCEEDS_64k)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ITERATED_DATA_EXCEEDS_64k);
#endif
#if defined(ERROR_INVALID_MINALLOCSIZE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_MINALLOCSIZE);
#endif
#if defined(ERROR_DYNLINK_FROM_INVALID_RING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DYNLINK_FROM_INVALID_RING);
#endif
#if defined(ERROR_IOPL_NOT_ENABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IOPL_NOT_ENABLED);
#endif
#if defined(ERROR_INVALID_SEGDPL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SEGDPL);
#endif
#if defined(ERROR_AUTODATASEG_EXCEEDS_64k)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_AUTODATASEG_EXCEEDS_64k);
#endif
#if defined(ERROR_RING2SEG_MUST_BE_MOVABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RING2SEG_MUST_BE_MOVABLE);
#endif
#if defined(ERROR_RELOC_CHAIN_XEEDS_SEGLIM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RELOC_CHAIN_XEEDS_SEGLIM);
#endif
#if defined(ERROR_INFLOOP_IN_RELOC_CHAIN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INFLOOP_IN_RELOC_CHAIN);
#endif
#if defined(ERROR_ENVVAR_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ENVVAR_NOT_FOUND);
#endif
#if defined(ERROR_NO_SIGNAL_SENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SIGNAL_SENT);
#endif
#if defined(ERROR_FILENAME_EXCED_RANGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILENAME_EXCED_RANGE);
#endif
#if defined(ERROR_RING2_STACK_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RING2_STACK_IN_USE);
#endif
#if defined(ERROR_META_EXPANSION_TOO_LONG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_META_EXPANSION_TOO_LONG);
#endif
#if defined(ERROR_INVALID_SIGNAL_NUMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SIGNAL_NUMBER);
#endif
#if defined(ERROR_THREAD_1_INACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_THREAD_1_INACTIVE);
#endif
#if defined(ERROR_LOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOCKED);
#endif
#if defined(ERROR_TOO_MANY_MODULES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_MODULES);
#endif
#if defined(ERROR_NESTING_NOT_ALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NESTING_NOT_ALLOWED);
#endif
#if defined(ERROR_EXE_MACHINE_TYPE_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EXE_MACHINE_TYPE_MISMATCH);
#endif
#if defined(ERROR_EXE_CANNOT_MODIFY_SIGNED_BINARY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EXE_CANNOT_MODIFY_SIGNED_BINARY);
#endif
#if defined(ERROR_EXE_CANNOT_MODIFY_STRONG_SIGNED_BINARY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EXE_CANNOT_MODIFY_STRONG_SIGNED_BINARY);
#endif
#if defined(ERROR_FILE_CHECKED_OUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_CHECKED_OUT);
#endif
#if defined(ERROR_CHECKOUT_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CHECKOUT_REQUIRED);
#endif
#if defined(ERROR_BAD_FILE_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_FILE_TYPE);
#endif
#if defined(ERROR_FILE_TOO_LARGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_TOO_LARGE);
#endif
#if defined(ERROR_FORMS_AUTH_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FORMS_AUTH_REQUIRED);
#endif
#if defined(ERROR_VIRUS_INFECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VIRUS_INFECTED);
#endif
#if defined(ERROR_VIRUS_DELETED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VIRUS_DELETED);
#endif
#if defined(ERROR_PIPE_LOCAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PIPE_LOCAL);
#endif
#if defined(ERROR_BAD_PIPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_PIPE);
#endif
#if defined(ERROR_PIPE_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PIPE_BUSY);
#endif
#if defined(ERROR_NO_DATA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_DATA);
#endif
#if defined(ERROR_PIPE_NOT_CONNECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PIPE_NOT_CONNECTED);
#endif
#if defined(ERROR_MORE_DATA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MORE_DATA);
#endif
#if defined(ERROR_NO_WORK_DONE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_WORK_DONE);
#endif
#if defined(ERROR_VC_DISCONNECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VC_DISCONNECTED);
#endif
#if defined(ERROR_INVALID_EA_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_EA_NAME);
#endif
#if defined(ERROR_EA_LIST_INCONSISTENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EA_LIST_INCONSISTENT);
#endif
#if defined(ERROR_NO_MORE_ITEMS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_MORE_ITEMS);
#endif
#if defined(ERROR_CANNOT_COPY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_COPY);
#endif
#if defined(ERROR_DIRECTORY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DIRECTORY);
#endif
#if defined(ERROR_EAS_DIDNT_FIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EAS_DIDNT_FIT);
#endif
#if defined(ERROR_EA_FILE_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EA_FILE_CORRUPT);
#endif
#if defined(ERROR_EA_TABLE_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EA_TABLE_FULL);
#endif
#if defined(ERROR_INVALID_EA_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_EA_HANDLE);
#endif
#if defined(ERROR_EAS_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EAS_NOT_SUPPORTED);
#endif
#if defined(ERROR_NOT_OWNER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_OWNER);
#endif
#if defined(ERROR_TOO_MANY_POSTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_POSTS);
#endif
#if defined(ERROR_PARTIAL_COPY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PARTIAL_COPY);
#endif
#if defined(ERROR_OPLOCK_NOT_GRANTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OPLOCK_NOT_GRANTED);
#endif
#if defined(ERROR_INVALID_OPLOCK_PROTOCOL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_OPLOCK_PROTOCOL);
#endif
#if defined(ERROR_DISK_TOO_FRAGMENTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISK_TOO_FRAGMENTED);
#endif
#if defined(ERROR_DELETE_PENDING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DELETE_PENDING);
#endif
#if defined(ERROR_INCOMPATIBLE_WITH_GLOBAL_SHORT_NAME_REGISTRY_SETTING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INCOMPATIBLE_WITH_GLOBAL_SHORT_NAME_REGISTRY_SETTING);
#endif
#if defined(ERROR_SHORT_NAMES_NOT_ENABLED_ON_VOLUME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SHORT_NAMES_NOT_ENABLED_ON_VOLUME);
#endif
#if defined(ERROR_SECURITY_STREAM_IS_INCONSISTENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECURITY_STREAM_IS_INCONSISTENT);
#endif
#if defined(ERROR_INVALID_LOCK_RANGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_LOCK_RANGE);
#endif
#if defined(ERROR_IMAGE_SUBSYSTEM_NOT_PRESENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IMAGE_SUBSYSTEM_NOT_PRESENT);
#endif
#if defined(ERROR_NOTIFICATION_GUID_ALREADY_DEFINED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOTIFICATION_GUID_ALREADY_DEFINED);
#endif
#if defined(ERROR_INVALID_EXCEPTION_HANDLER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_EXCEPTION_HANDLER);
#endif
#if defined(ERROR_DUPLICATE_PRIVILEGES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DUPLICATE_PRIVILEGES);
#endif
#if defined(ERROR_NO_RANGES_PROCESSED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_RANGES_PROCESSED);
#endif
#if defined(ERROR_NOT_ALLOWED_ON_SYSTEM_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_ALLOWED_ON_SYSTEM_FILE);
#endif
#if defined(ERROR_DISK_RESOURCES_EXHAUSTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISK_RESOURCES_EXHAUSTED);
#endif
#if defined(ERROR_INVALID_TOKEN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_TOKEN);
#endif
#if defined(ERROR_DEVICE_FEATURE_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_FEATURE_NOT_SUPPORTED);
#endif
#if defined(ERROR_MR_MID_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MR_MID_NOT_FOUND);
#endif
#if defined(ERROR_SCOPE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SCOPE_NOT_FOUND);
#endif
#if defined(ERROR_UNDEFINED_SCOPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNDEFINED_SCOPE);
#endif
#if defined(ERROR_INVALID_CAP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_CAP);
#endif
#if defined(ERROR_DEVICE_UNREACHABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_UNREACHABLE);
#endif
#if defined(ERROR_DEVICE_NO_RESOURCES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_NO_RESOURCES);
#endif
#if defined(ERROR_DATA_CHECKSUM_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DATA_CHECKSUM_ERROR);
#endif
#if defined(ERROR_INTERMIXED_KERNEL_EA_OPERATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INTERMIXED_KERNEL_EA_OPERATION);
#endif
#if defined(ERROR_FILE_LEVEL_TRIM_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_LEVEL_TRIM_NOT_SUPPORTED);
#endif
#if defined(ERROR_OFFSET_ALIGNMENT_VIOLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OFFSET_ALIGNMENT_VIOLATION);
#endif
#if defined(ERROR_INVALID_FIELD_IN_PARAMETER_LIST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_FIELD_IN_PARAMETER_LIST);
#endif
#if defined(ERROR_OPERATION_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OPERATION_IN_PROGRESS);
#endif
#if defined(ERROR_BAD_DEVICE_PATH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_DEVICE_PATH);
#endif
#if defined(ERROR_TOO_MANY_DESCRIPTORS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_DESCRIPTORS);
#endif
#if defined(ERROR_SCRUB_DATA_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SCRUB_DATA_DISABLED);
#endif
#if defined(ERROR_NOT_REDUNDANT_STORAGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_REDUNDANT_STORAGE);
#endif
#if defined(ERROR_RESIDENT_FILE_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESIDENT_FILE_NOT_SUPPORTED);
#endif
#if defined(ERROR_COMPRESSED_FILE_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_COMPRESSED_FILE_NOT_SUPPORTED);
#endif
#if defined(ERROR_DIRECTORY_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DIRECTORY_NOT_SUPPORTED);
#endif
#if defined(ERROR_NOT_READ_FROM_COPY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_READ_FROM_COPY);
#endif
#if defined(ERROR_FT_WRITE_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FT_WRITE_FAILURE);
#endif
#if defined(ERROR_FT_DI_SCAN_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FT_DI_SCAN_REQUIRED);
#endif
#if defined(ERROR_INVALID_KERNEL_INFO_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_KERNEL_INFO_VERSION);
#endif
#if defined(ERROR_INVALID_PEP_INFO_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PEP_INFO_VERSION);
#endif
#if defined(ERROR_OBJECT_NOT_EXTERNALLY_BACKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OBJECT_NOT_EXTERNALLY_BACKED);
#endif
#if defined(ERROR_EXTERNAL_BACKING_PROVIDER_UNKNOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EXTERNAL_BACKING_PROVIDER_UNKNOWN);
#endif
#if defined(ERROR_COMPRESSION_NOT_BENEFICIAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_COMPRESSION_NOT_BENEFICIAL);
#endif
#if defined(ERROR_STORAGE_TOPOLOGY_ID_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STORAGE_TOPOLOGY_ID_MISMATCH);
#endif
#if defined(ERROR_BLOCKED_BY_PARENTAL_CONTROLS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BLOCKED_BY_PARENTAL_CONTROLS);
#endif
#if defined(ERROR_BLOCK_TOO_MANY_REFERENCES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BLOCK_TOO_MANY_REFERENCES);
#endif
#if defined(ERROR_MARKED_TO_DISALLOW_WRITES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MARKED_TO_DISALLOW_WRITES);
#endif
#if defined(ERROR_ENCLAVE_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ENCLAVE_FAILURE);
#endif
#if defined(ERROR_FAIL_NOACTION_REBOOT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FAIL_NOACTION_REBOOT);
#endif
#if defined(ERROR_FAIL_SHUTDOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FAIL_SHUTDOWN);
#endif
#if defined(ERROR_FAIL_RESTART)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FAIL_RESTART);
#endif
#if defined(ERROR_MAX_SESSIONS_REACHED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MAX_SESSIONS_REACHED);
#endif
#if defined(ERROR_NETWORK_ACCESS_DENIED_EDP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NETWORK_ACCESS_DENIED_EDP);
#endif
#if defined(ERROR_DEVICE_HINT_NAME_BUFFER_TOO_SMALL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_HINT_NAME_BUFFER_TOO_SMALL);
#endif
#if defined(ERROR_EDP_POLICY_DENIES_OPERATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EDP_POLICY_DENIES_OPERATION);
#endif
#if defined(ERROR_EDP_DPL_POLICY_CANT_BE_SATISFIED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EDP_DPL_POLICY_CANT_BE_SATISFIED);
#endif
#if defined(ERROR_CLOUD_FILE_SYNC_ROOT_METADATA_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_SYNC_ROOT_METADATA_CORRUPT);
#endif
#if defined(ERROR_DEVICE_IN_MAINTENANCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_IN_MAINTENANCE);
#endif
#if defined(ERROR_NOT_SUPPORTED_ON_DAX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_SUPPORTED_ON_DAX);
#endif
#if defined(ERROR_DAX_MAPPING_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DAX_MAPPING_EXISTS);
#endif
#if defined(ERROR_CLOUD_FILE_PROVIDER_NOT_RUNNING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_PROVIDER_NOT_RUNNING);
#endif
#if defined(ERROR_CLOUD_FILE_METADATA_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_METADATA_CORRUPT);
#endif
#if defined(ERROR_CLOUD_FILE_METADATA_TOO_LARGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_METADATA_TOO_LARGE);
#endif
#if defined(ERROR_CLOUD_FILE_PROPERTY_BLOB_TOO_LARGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_PROPERTY_BLOB_TOO_LARGE);
#endif
#if defined(ERROR_CLOUD_FILE_PROPERTY_BLOB_CHECKSUM_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_PROPERTY_BLOB_CHECKSUM_MISMATCH);
#endif
#if defined(ERROR_CHILD_PROCESS_BLOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CHILD_PROCESS_BLOCKED);
#endif
#if defined(ERROR_STORAGE_LOST_DATA_PERSISTENCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STORAGE_LOST_DATA_PERSISTENCE);
#endif
#if defined(ERROR_FILE_SYSTEM_VIRTUALIZATION_UNAVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_SYSTEM_VIRTUALIZATION_UNAVAILABLE);
#endif
#if defined(ERROR_FILE_SYSTEM_VIRTUALIZATION_METADATA_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_SYSTEM_VIRTUALIZATION_METADATA_CORRUPT);
#endif
#if defined(ERROR_FILE_SYSTEM_VIRTUALIZATION_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_SYSTEM_VIRTUALIZATION_BUSY);
#endif
#if defined(ERROR_FILE_SYSTEM_VIRTUALIZATION_PROVIDER_UNKNOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_SYSTEM_VIRTUALIZATION_PROVIDER_UNKNOWN);
#endif
#if defined(ERROR_GDI_HANDLE_LEAK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GDI_HANDLE_LEAK);
#endif
#if defined(ERROR_CLOUD_FILE_TOO_MANY_PROPERTY_BLOBS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_TOO_MANY_PROPERTY_BLOBS);
#endif
#if defined(ERROR_CLOUD_FILE_PROPERTY_VERSION_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_PROPERTY_VERSION_NOT_SUPPORTED);
#endif
#if defined(ERROR_NOT_A_CLOUD_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_A_CLOUD_FILE);
#endif
#if defined(ERROR_CLOUD_FILE_NOT_IN_SYNC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_NOT_IN_SYNC);
#endif
#if defined(ERROR_CLOUD_FILE_ALREADY_CONNECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_ALREADY_CONNECTED);
#endif
#if defined(ERROR_CLOUD_FILE_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_NOT_SUPPORTED);
#endif
#if defined(ERROR_CLOUD_FILE_INVALID_REQUEST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_INVALID_REQUEST);
#endif
#if defined(ERROR_CLOUD_FILE_READ_ONLY_VOLUME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_READ_ONLY_VOLUME);
#endif
#if defined(ERROR_CLOUD_FILE_CONNECTED_PROVIDER_ONLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_CONNECTED_PROVIDER_ONLY);
#endif
#if defined(ERROR_CLOUD_FILE_VALIDATION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_VALIDATION_FAILED);
#endif
#if defined(ERROR_SMB1_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SMB1_NOT_AVAILABLE);
#endif
#if defined(ERROR_FILE_SYSTEM_VIRTUALIZATION_INVALID_OPERATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_SYSTEM_VIRTUALIZATION_INVALID_OPERATION);
#endif
#if defined(ERROR_CLOUD_FILE_AUTHENTICATION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_AUTHENTICATION_FAILED);
#endif
#if defined(ERROR_CLOUD_FILE_INSUFFICIENT_RESOURCES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_INSUFFICIENT_RESOURCES);
#endif
#if defined(ERROR_CLOUD_FILE_NETWORK_UNAVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_NETWORK_UNAVAILABLE);
#endif
#if defined(ERROR_CLOUD_FILE_UNSUCCESSFUL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_UNSUCCESSFUL);
#endif
#if defined(ERROR_CLOUD_FILE_NOT_UNDER_SYNC_ROOT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_NOT_UNDER_SYNC_ROOT);
#endif
#if defined(ERROR_CLOUD_FILE_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_IN_USE);
#endif
#if defined(ERROR_CLOUD_FILE_PINNED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_PINNED);
#endif
#if defined(ERROR_CLOUD_FILE_REQUEST_ABORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_REQUEST_ABORTED);
#endif
#if defined(ERROR_CLOUD_FILE_PROPERTY_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_PROPERTY_CORRUPT);
#endif
#if defined(ERROR_CLOUD_FILE_ACCESS_DENIED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_ACCESS_DENIED);
#endif
#if defined(ERROR_CLOUD_FILE_INCOMPATIBLE_HARDLINKS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_INCOMPATIBLE_HARDLINKS);
#endif
#if defined(ERROR_CLOUD_FILE_PROPERTY_LOCK_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_PROPERTY_LOCK_CONFLICT);
#endif
#if defined(ERROR_CLOUD_FILE_REQUEST_CANCELED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLOUD_FILE_REQUEST_CANCELED);
#endif
#if defined(ERROR_EXTERNAL_SYSKEY_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EXTERNAL_SYSKEY_NOT_SUPPORTED);
#endif
#if defined(ERROR_THREAD_MODE_ALREADY_BACKGROUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_THREAD_MODE_ALREADY_BACKGROUND);
#endif
#if defined(ERROR_THREAD_MODE_NOT_BACKGROUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_THREAD_MODE_NOT_BACKGROUND);
#endif
#if defined(ERROR_PROCESS_MODE_ALREADY_BACKGROUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROCESS_MODE_ALREADY_BACKGROUND);
#endif
#if defined(ERROR_PROCESS_MODE_NOT_BACKGROUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROCESS_MODE_NOT_BACKGROUND);
#endif
#if defined(ERROR_CAPAUTHZ_NOT_DEVUNLOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CAPAUTHZ_NOT_DEVUNLOCKED);
#endif
#if defined(ERROR_CAPAUTHZ_CHANGE_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CAPAUTHZ_CHANGE_TYPE);
#endif
#if defined(ERROR_CAPAUTHZ_NOT_PROVISIONED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CAPAUTHZ_NOT_PROVISIONED);
#endif
#if defined(ERROR_CAPAUTHZ_NOT_AUTHORIZED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CAPAUTHZ_NOT_AUTHORIZED);
#endif
#if defined(ERROR_CAPAUTHZ_NO_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CAPAUTHZ_NO_POLICY);
#endif
#if defined(ERROR_CAPAUTHZ_DB_CORRUPTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CAPAUTHZ_DB_CORRUPTED);
#endif
#if defined(ERROR_CAPAUTHZ_SCCD_INVALID_CATALOG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CAPAUTHZ_SCCD_INVALID_CATALOG);
#endif
#if defined(ERROR_CAPAUTHZ_SCCD_NO_AUTH_ENTITY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CAPAUTHZ_SCCD_NO_AUTH_ENTITY);
#endif
#if defined(ERROR_CAPAUTHZ_SCCD_PARSE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CAPAUTHZ_SCCD_PARSE_ERROR);
#endif
#if defined(ERROR_CAPAUTHZ_SCCD_DEV_MODE_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CAPAUTHZ_SCCD_DEV_MODE_REQUIRED);
#endif
#if defined(ERROR_CAPAUTHZ_SCCD_NO_CAPABILITY_MATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CAPAUTHZ_SCCD_NO_CAPABILITY_MATCH);
#endif
#if defined(ERROR_PNP_QUERY_REMOVE_DEVICE_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PNP_QUERY_REMOVE_DEVICE_TIMEOUT);
#endif
#if defined(ERROR_PNP_QUERY_REMOVE_RELATED_DEVICE_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PNP_QUERY_REMOVE_RELATED_DEVICE_TIMEOUT);
#endif
#if defined(ERROR_PNP_QUERY_REMOVE_UNRELATED_DEVICE_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PNP_QUERY_REMOVE_UNRELATED_DEVICE_TIMEOUT);
#endif
#if defined(ERROR_DEVICE_HARDWARE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_HARDWARE_ERROR);
#endif
#if defined(ERROR_INVALID_ADDRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_ADDRESS);
#endif
#if defined(ERROR_VRF_CFG_ENABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VRF_CFG_ENABLED);
#endif
#if defined(ERROR_PARTITION_TERMINATING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PARTITION_TERMINATING);
#endif
#if defined(ERROR_USER_PROFILE_LOAD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_USER_PROFILE_LOAD);
#endif
#if defined(ERROR_ARITHMETIC_OVERFLOW)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ARITHMETIC_OVERFLOW);
#endif
#if defined(ERROR_PIPE_CONNECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PIPE_CONNECTED);
#endif
#if defined(ERROR_PIPE_LISTENING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PIPE_LISTENING);
#endif
#if defined(ERROR_VERIFIER_STOP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VERIFIER_STOP);
#endif
#if defined(ERROR_ABIOS_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ABIOS_ERROR);
#endif
#if defined(ERROR_WX86_WARNING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WX86_WARNING);
#endif
#if defined(ERROR_WX86_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WX86_ERROR);
#endif
#if defined(ERROR_TIMER_NOT_CANCELED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TIMER_NOT_CANCELED);
#endif
#if defined(ERROR_UNWIND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNWIND);
#endif
#if defined(ERROR_BAD_STACK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_STACK);
#endif
#if defined(ERROR_INVALID_UNWIND_TARGET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_UNWIND_TARGET);
#endif
#if defined(ERROR_INVALID_PORT_ATTRIBUTES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PORT_ATTRIBUTES);
#endif
#if defined(ERROR_PORT_MESSAGE_TOO_LONG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PORT_MESSAGE_TOO_LONG);
#endif
#if defined(ERROR_INVALID_QUOTA_LOWER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_QUOTA_LOWER);
#endif
#if defined(ERROR_DEVICE_ALREADY_ATTACHED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_ALREADY_ATTACHED);
#endif
#if defined(ERROR_INSTRUCTION_MISALIGNMENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTRUCTION_MISALIGNMENT);
#endif
#if defined(ERROR_PROFILING_NOT_STARTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROFILING_NOT_STARTED);
#endif
#if defined(ERROR_PROFILING_NOT_STOPPED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROFILING_NOT_STOPPED);
#endif
#if defined(ERROR_COULD_NOT_INTERPRET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_COULD_NOT_INTERPRET);
#endif
#if defined(ERROR_PROFILING_AT_LIMIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROFILING_AT_LIMIT);
#endif
#if defined(ERROR_CANT_WAIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_WAIT);
#endif
#if defined(ERROR_CANT_TERMINATE_SELF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_TERMINATE_SELF);
#endif
#if defined(ERROR_UNEXPECTED_MM_CREATE_ERR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNEXPECTED_MM_CREATE_ERR);
#endif
#if defined(ERROR_UNEXPECTED_MM_MAP_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNEXPECTED_MM_MAP_ERROR);
#endif
#if defined(ERROR_UNEXPECTED_MM_EXTEND_ERR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNEXPECTED_MM_EXTEND_ERR);
#endif
#if defined(ERROR_BAD_FUNCTION_TABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_FUNCTION_TABLE);
#endif
#if defined(ERROR_NO_GUID_TRANSLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_GUID_TRANSLATION);
#endif
#if defined(ERROR_INVALID_LDT_SIZE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_LDT_SIZE);
#endif
#if defined(ERROR_INVALID_LDT_OFFSET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_LDT_OFFSET);
#endif
#if defined(ERROR_INVALID_LDT_DESCRIPTOR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_LDT_DESCRIPTOR);
#endif
#if defined(ERROR_TOO_MANY_THREADS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_THREADS);
#endif
#if defined(ERROR_THREAD_NOT_IN_PROCESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_THREAD_NOT_IN_PROCESS);
#endif
#if defined(ERROR_PAGEFILE_QUOTA_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PAGEFILE_QUOTA_EXCEEDED);
#endif
#if defined(ERROR_LOGON_SERVER_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOGON_SERVER_CONFLICT);
#endif
#if defined(ERROR_SYNCHRONIZATION_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYNCHRONIZATION_REQUIRED);
#endif
#if defined(ERROR_NET_OPEN_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NET_OPEN_FAILED);
#endif
#if defined(ERROR_IO_PRIVILEGE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IO_PRIVILEGE_FAILED);
#endif
#if defined(ERROR_CONTROL_C_EXIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONTROL_C_EXIT);
#endif
#if defined(ERROR_MISSING_SYSTEMFILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MISSING_SYSTEMFILE);
#endif
#if defined(ERROR_UNHANDLED_EXCEPTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNHANDLED_EXCEPTION);
#endif
#if defined(ERROR_APP_INIT_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_APP_INIT_FAILURE);
#endif
#if defined(ERROR_PAGEFILE_CREATE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PAGEFILE_CREATE_FAILED);
#endif
#if defined(ERROR_INVALID_IMAGE_HASH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_IMAGE_HASH);
#endif
#if defined(ERROR_NO_PAGEFILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_PAGEFILE);
#endif
#if defined(ERROR_ILLEGAL_FLOAT_CONTEXT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ILLEGAL_FLOAT_CONTEXT);
#endif
#if defined(ERROR_NO_EVENT_PAIR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_EVENT_PAIR);
#endif
#if defined(ERROR_DOMAIN_CTRLR_CONFIG_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DOMAIN_CTRLR_CONFIG_ERROR);
#endif
#if defined(ERROR_ILLEGAL_CHARACTER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ILLEGAL_CHARACTER);
#endif
#if defined(ERROR_UNDEFINED_CHARACTER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNDEFINED_CHARACTER);
#endif
#if defined(ERROR_FLOPPY_VOLUME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FLOPPY_VOLUME);
#endif
#if defined(ERROR_BIOS_FAILED_TO_CONNECT_INTERRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BIOS_FAILED_TO_CONNECT_INTERRUPT);
#endif
#if defined(ERROR_BACKUP_CONTROLLER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BACKUP_CONTROLLER);
#endif
#if defined(ERROR_MUTANT_LIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MUTANT_LIMIT_EXCEEDED);
#endif
#if defined(ERROR_FS_DRIVER_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FS_DRIVER_REQUIRED);
#endif
#if defined(ERROR_CANNOT_LOAD_REGISTRY_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_LOAD_REGISTRY_FILE);
#endif
#if defined(ERROR_DEBUG_ATTACH_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEBUG_ATTACH_FAILED);
#endif
#if defined(ERROR_SYSTEM_PROCESS_TERMINATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYSTEM_PROCESS_TERMINATED);
#endif
#if defined(ERROR_DATA_NOT_ACCEPTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DATA_NOT_ACCEPTED);
#endif
#if defined(ERROR_VDM_HARD_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VDM_HARD_ERROR);
#endif
#if defined(ERROR_DRIVER_CANCEL_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DRIVER_CANCEL_TIMEOUT);
#endif
#if defined(ERROR_REPLY_MESSAGE_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REPLY_MESSAGE_MISMATCH);
#endif
#if defined(ERROR_LOST_WRITEBEHIND_DATA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOST_WRITEBEHIND_DATA);
#endif
#if defined(ERROR_CLIENT_SERVER_PARAMETERS_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLIENT_SERVER_PARAMETERS_INVALID);
#endif
#if defined(ERROR_NOT_TINY_STREAM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_TINY_STREAM);
#endif
#if defined(ERROR_STACK_OVERFLOW_READ)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STACK_OVERFLOW_READ);
#endif
#if defined(ERROR_CONVERT_TO_LARGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONVERT_TO_LARGE);
#endif
#if defined(ERROR_FOUND_OUT_OF_SCOPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FOUND_OUT_OF_SCOPE);
#endif
#if defined(ERROR_ALLOCATE_BUCKET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALLOCATE_BUCKET);
#endif
#if defined(ERROR_MARSHALL_OVERFLOW)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MARSHALL_OVERFLOW);
#endif
#if defined(ERROR_INVALID_VARIANT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_VARIANT);
#endif
#if defined(ERROR_BAD_COMPRESSION_BUFFER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_COMPRESSION_BUFFER);
#endif
#if defined(ERROR_AUDIT_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_AUDIT_FAILED);
#endif
#if defined(ERROR_TIMER_RESOLUTION_NOT_SET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TIMER_RESOLUTION_NOT_SET);
#endif
#if defined(ERROR_INSUFFICIENT_LOGON_INFO)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSUFFICIENT_LOGON_INFO);
#endif
#if defined(ERROR_BAD_DLL_ENTRYPOINT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_DLL_ENTRYPOINT);
#endif
#if defined(ERROR_BAD_SERVICE_ENTRYPOINT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_SERVICE_ENTRYPOINT);
#endif
#if defined(ERROR_IP_ADDRESS_CONFLICT1)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IP_ADDRESS_CONFLICT1);
#endif
#if defined(ERROR_IP_ADDRESS_CONFLICT2)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IP_ADDRESS_CONFLICT2);
#endif
#if defined(ERROR_REGISTRY_QUOTA_LIMIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REGISTRY_QUOTA_LIMIT);
#endif
#if defined(ERROR_NO_CALLBACK_ACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_CALLBACK_ACTIVE);
#endif
#if defined(ERROR_PWD_TOO_SHORT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PWD_TOO_SHORT);
#endif
#if defined(ERROR_PWD_TOO_RECENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PWD_TOO_RECENT);
#endif
#if defined(ERROR_PWD_HISTORY_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PWD_HISTORY_CONFLICT);
#endif
#if defined(ERROR_UNSUPPORTED_COMPRESSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNSUPPORTED_COMPRESSION);
#endif
#if defined(ERROR_INVALID_HW_PROFILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_HW_PROFILE);
#endif
#if defined(ERROR_INVALID_PLUGPLAY_DEVICE_PATH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PLUGPLAY_DEVICE_PATH);
#endif
#if defined(ERROR_QUOTA_LIST_INCONSISTENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_QUOTA_LIST_INCONSISTENT);
#endif
#if defined(ERROR_EVALUATION_EXPIRATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVALUATION_EXPIRATION);
#endif
#if defined(ERROR_ILLEGAL_DLL_RELOCATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ILLEGAL_DLL_RELOCATION);
#endif
#if defined(ERROR_DLL_INIT_FAILED_LOGOFF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DLL_INIT_FAILED_LOGOFF);
#endif
#if defined(ERROR_VALIDATE_CONTINUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VALIDATE_CONTINUE);
#endif
#if defined(ERROR_NO_MORE_MATCHES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_MORE_MATCHES);
#endif
#if defined(ERROR_RANGE_LIST_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RANGE_LIST_CONFLICT);
#endif
#if defined(ERROR_SERVER_SID_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVER_SID_MISMATCH);
#endif
#if defined(ERROR_CANT_ENABLE_DENY_ONLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_ENABLE_DENY_ONLY);
#endif
#if defined(ERROR_FLOAT_MULTIPLE_FAULTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FLOAT_MULTIPLE_FAULTS);
#endif
#if defined(ERROR_FLOAT_MULTIPLE_TRAPS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FLOAT_MULTIPLE_TRAPS);
#endif
#if defined(ERROR_NOINTERFACE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOINTERFACE);
#endif
#if defined(ERROR_DRIVER_FAILED_SLEEP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DRIVER_FAILED_SLEEP);
#endif
#if defined(ERROR_CORRUPT_SYSTEM_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CORRUPT_SYSTEM_FILE);
#endif
#if defined(ERROR_COMMITMENT_MINIMUM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_COMMITMENT_MINIMUM);
#endif
#if defined(ERROR_PNP_RESTART_ENUMERATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PNP_RESTART_ENUMERATION);
#endif
#if defined(ERROR_SYSTEM_IMAGE_BAD_SIGNATURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYSTEM_IMAGE_BAD_SIGNATURE);
#endif
#if defined(ERROR_PNP_REBOOT_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PNP_REBOOT_REQUIRED);
#endif
#if defined(ERROR_INSUFFICIENT_POWER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSUFFICIENT_POWER);
#endif
#if defined(ERROR_MULTIPLE_FAULT_VIOLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MULTIPLE_FAULT_VIOLATION);
#endif
#if defined(ERROR_SYSTEM_SHUTDOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYSTEM_SHUTDOWN);
#endif
#if defined(ERROR_PORT_NOT_SET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PORT_NOT_SET);
#endif
#if defined(ERROR_DS_VERSION_CHECK_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_VERSION_CHECK_FAILURE);
#endif
#if defined(ERROR_RANGE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RANGE_NOT_FOUND);
#endif
#if defined(ERROR_NOT_SAFE_MODE_DRIVER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_SAFE_MODE_DRIVER);
#endif
#if defined(ERROR_FAILED_DRIVER_ENTRY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FAILED_DRIVER_ENTRY);
#endif
#if defined(ERROR_DEVICE_ENUMERATION_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_ENUMERATION_ERROR);
#endif
#if defined(ERROR_MOUNT_POINT_NOT_RESOLVED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MOUNT_POINT_NOT_RESOLVED);
#endif
#if defined(ERROR_INVALID_DEVICE_OBJECT_PARAMETER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_DEVICE_OBJECT_PARAMETER);
#endif
#if defined(ERROR_MCA_OCCURED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MCA_OCCURED);
#endif
#if defined(ERROR_DRIVER_DATABASE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DRIVER_DATABASE_ERROR);
#endif
#if defined(ERROR_SYSTEM_HIVE_TOO_LARGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYSTEM_HIVE_TOO_LARGE);
#endif
#if defined(ERROR_DRIVER_FAILED_PRIOR_UNLOAD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DRIVER_FAILED_PRIOR_UNLOAD);
#endif
#if defined(ERROR_VOLSNAP_PREPARE_HIBERNATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VOLSNAP_PREPARE_HIBERNATE);
#endif
#if defined(ERROR_HIBERNATION_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HIBERNATION_FAILURE);
#endif
#if defined(ERROR_PWD_TOO_LONG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PWD_TOO_LONG);
#endif
#if defined(ERROR_FILE_SYSTEM_LIMITATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_SYSTEM_LIMITATION);
#endif
#if defined(ERROR_ASSERTION_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ASSERTION_FAILURE);
#endif
#if defined(ERROR_ACPI_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ACPI_ERROR);
#endif
#if defined(ERROR_WOW_ASSERTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WOW_ASSERTION);
#endif
#if defined(ERROR_PNP_BAD_MPS_TABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PNP_BAD_MPS_TABLE);
#endif
#if defined(ERROR_PNP_TRANSLATION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PNP_TRANSLATION_FAILED);
#endif
#if defined(ERROR_PNP_IRQ_TRANSLATION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PNP_IRQ_TRANSLATION_FAILED);
#endif
#if defined(ERROR_PNP_INVALID_ID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PNP_INVALID_ID);
#endif
#if defined(ERROR_WAKE_SYSTEM_DEBUGGER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WAKE_SYSTEM_DEBUGGER);
#endif
#if defined(ERROR_HANDLES_CLOSED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HANDLES_CLOSED);
#endif
#if defined(ERROR_EXTRANEOUS_INFORMATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EXTRANEOUS_INFORMATION);
#endif
#if defined(ERROR_RXACT_COMMIT_NECESSARY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RXACT_COMMIT_NECESSARY);
#endif
#if defined(ERROR_MEDIA_CHECK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MEDIA_CHECK);
#endif
#if defined(ERROR_GUID_SUBSTITUTION_MADE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GUID_SUBSTITUTION_MADE);
#endif
#if defined(ERROR_STOPPED_ON_SYMLINK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STOPPED_ON_SYMLINK);
#endif
#if defined(ERROR_LONGJUMP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LONGJUMP);
#endif
#if defined(ERROR_PLUGPLAY_QUERY_VETOED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PLUGPLAY_QUERY_VETOED);
#endif
#if defined(ERROR_UNWIND_CONSOLIDATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNWIND_CONSOLIDATE);
#endif
#if defined(ERROR_REGISTRY_HIVE_RECOVERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REGISTRY_HIVE_RECOVERED);
#endif
#if defined(ERROR_DLL_MIGHT_BE_INSECURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DLL_MIGHT_BE_INSECURE);
#endif
#if defined(ERROR_DLL_MIGHT_BE_INCOMPATIBLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DLL_MIGHT_BE_INCOMPATIBLE);
#endif
#if defined(ERROR_DBG_EXCEPTION_NOT_HANDLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DBG_EXCEPTION_NOT_HANDLED);
#endif
#if defined(ERROR_DBG_REPLY_LATER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DBG_REPLY_LATER);
#endif
#if defined(ERROR_DBG_UNABLE_TO_PROVIDE_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DBG_UNABLE_TO_PROVIDE_HANDLE);
#endif
#if defined(ERROR_DBG_TERMINATE_THREAD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DBG_TERMINATE_THREAD);
#endif
#if defined(ERROR_DBG_TERMINATE_PROCESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DBG_TERMINATE_PROCESS);
#endif
#if defined(ERROR_DBG_CONTROL_C)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DBG_CONTROL_C);
#endif
#if defined(ERROR_DBG_PRINTEXCEPTION_C)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DBG_PRINTEXCEPTION_C);
#endif
#if defined(ERROR_DBG_RIPEXCEPTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DBG_RIPEXCEPTION);
#endif
#if defined(ERROR_DBG_CONTROL_BREAK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DBG_CONTROL_BREAK);
#endif
#if defined(ERROR_DBG_COMMAND_EXCEPTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DBG_COMMAND_EXCEPTION);
#endif
#if defined(ERROR_OBJECT_NAME_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OBJECT_NAME_EXISTS);
#endif
#if defined(ERROR_THREAD_WAS_SUSPENDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_THREAD_WAS_SUSPENDED);
#endif
#if defined(ERROR_IMAGE_NOT_AT_BASE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IMAGE_NOT_AT_BASE);
#endif
#if defined(ERROR_RXACT_STATE_CREATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RXACT_STATE_CREATED);
#endif
#if defined(ERROR_SEGMENT_NOTIFICATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SEGMENT_NOTIFICATION);
#endif
#if defined(ERROR_BAD_CURRENT_DIRECTORY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_CURRENT_DIRECTORY);
#endif
#if defined(ERROR_FT_READ_RECOVERY_FROM_BACKUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FT_READ_RECOVERY_FROM_BACKUP);
#endif
#if defined(ERROR_FT_WRITE_RECOVERY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FT_WRITE_RECOVERY);
#endif
#if defined(ERROR_IMAGE_MACHINE_TYPE_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IMAGE_MACHINE_TYPE_MISMATCH);
#endif
#if defined(ERROR_RECEIVE_PARTIAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RECEIVE_PARTIAL);
#endif
#if defined(ERROR_RECEIVE_EXPEDITED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RECEIVE_EXPEDITED);
#endif
#if defined(ERROR_RECEIVE_PARTIAL_EXPEDITED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RECEIVE_PARTIAL_EXPEDITED);
#endif
#if defined(ERROR_EVENT_DONE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVENT_DONE);
#endif
#if defined(ERROR_EVENT_PENDING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVENT_PENDING);
#endif
#if defined(ERROR_CHECKING_FILE_SYSTEM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CHECKING_FILE_SYSTEM);
#endif
#if defined(ERROR_FATAL_APP_EXIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FATAL_APP_EXIT);
#endif
#if defined(ERROR_PREDEFINED_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PREDEFINED_HANDLE);
#endif
#if defined(ERROR_WAS_UNLOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WAS_UNLOCKED);
#endif
#if defined(ERROR_SERVICE_NOTIFICATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_NOTIFICATION);
#endif
#if defined(ERROR_WAS_LOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WAS_LOCKED);
#endif
#if defined(ERROR_LOG_HARD_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_HARD_ERROR);
#endif
#if defined(ERROR_ALREADY_WIN32)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALREADY_WIN32);
#endif
#if defined(ERROR_IMAGE_MACHINE_TYPE_MISMATCH_EXE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IMAGE_MACHINE_TYPE_MISMATCH_EXE);
#endif
#if defined(ERROR_NO_YIELD_PERFORMED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_YIELD_PERFORMED);
#endif
#if defined(ERROR_TIMER_RESUME_IGNORED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TIMER_RESUME_IGNORED);
#endif
#if defined(ERROR_ARBITRATION_UNHANDLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ARBITRATION_UNHANDLED);
#endif
#if defined(ERROR_CARDBUS_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CARDBUS_NOT_SUPPORTED);
#endif
#if defined(ERROR_MP_PROCESSOR_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MP_PROCESSOR_MISMATCH);
#endif
#if defined(ERROR_HIBERNATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HIBERNATED);
#endif
#if defined(ERROR_RESUME_HIBERNATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESUME_HIBERNATION);
#endif
#if defined(ERROR_FIRMWARE_UPDATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FIRMWARE_UPDATED);
#endif
#if defined(ERROR_DRIVERS_LEAKING_LOCKED_PAGES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DRIVERS_LEAKING_LOCKED_PAGES);
#endif
#if defined(ERROR_WAKE_SYSTEM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WAKE_SYSTEM);
#endif
#if defined(ERROR_WAIT_1)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WAIT_1);
#endif
#if defined(ERROR_WAIT_2)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WAIT_2);
#endif
#if defined(ERROR_WAIT_3)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WAIT_3);
#endif
#if defined(ERROR_WAIT_63)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WAIT_63);
#endif
#if defined(ERROR_ABANDONED_WAIT_0)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ABANDONED_WAIT_0);
#endif
#if defined(ERROR_ABANDONED_WAIT_63)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ABANDONED_WAIT_63);
#endif
#if defined(ERROR_USER_APC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_USER_APC);
#endif
#if defined(ERROR_KERNEL_APC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_KERNEL_APC);
#endif
#if defined(ERROR_ALERTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALERTED);
#endif
#if defined(ERROR_ELEVATION_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ELEVATION_REQUIRED);
#endif
#if defined(ERROR_REPARSE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REPARSE);
#endif
#if defined(ERROR_OPLOCK_BREAK_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OPLOCK_BREAK_IN_PROGRESS);
#endif
#if defined(ERROR_VOLUME_MOUNTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VOLUME_MOUNTED);
#endif
#if defined(ERROR_RXACT_COMMITTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RXACT_COMMITTED);
#endif
#if defined(ERROR_NOTIFY_CLEANUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOTIFY_CLEANUP);
#endif
#if defined(ERROR_PRIMARY_TRANSPORT_CONNECT_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRIMARY_TRANSPORT_CONNECT_FAILED);
#endif
#if defined(ERROR_PAGE_FAULT_TRANSITION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PAGE_FAULT_TRANSITION);
#endif
#if defined(ERROR_PAGE_FAULT_DEMAND_ZERO)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PAGE_FAULT_DEMAND_ZERO);
#endif
#if defined(ERROR_PAGE_FAULT_COPY_ON_WRITE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PAGE_FAULT_COPY_ON_WRITE);
#endif
#if defined(ERROR_PAGE_FAULT_GUARD_PAGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PAGE_FAULT_GUARD_PAGE);
#endif
#if defined(ERROR_PAGE_FAULT_PAGING_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PAGE_FAULT_PAGING_FILE);
#endif
#if defined(ERROR_CACHE_PAGE_LOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CACHE_PAGE_LOCKED);
#endif
#if defined(ERROR_CRASH_DUMP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CRASH_DUMP);
#endif
#if defined(ERROR_BUFFER_ALL_ZEROS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BUFFER_ALL_ZEROS);
#endif
#if defined(ERROR_REPARSE_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REPARSE_OBJECT);
#endif
#if defined(ERROR_RESOURCE_REQUIREMENTS_CHANGED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_REQUIREMENTS_CHANGED);
#endif
#if defined(ERROR_TRANSLATION_COMPLETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSLATION_COMPLETE);
#endif
#if defined(ERROR_NOTHING_TO_TERMINATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOTHING_TO_TERMINATE);
#endif
#if defined(ERROR_PROCESS_NOT_IN_JOB)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROCESS_NOT_IN_JOB);
#endif
#if defined(ERROR_PROCESS_IN_JOB)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROCESS_IN_JOB);
#endif
#if defined(ERROR_VOLSNAP_HIBERNATE_READY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VOLSNAP_HIBERNATE_READY);
#endif
#if defined(ERROR_FSFILTER_OP_COMPLETED_SUCCESSFULLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FSFILTER_OP_COMPLETED_SUCCESSFULLY);
#endif
#if defined(ERROR_INTERRUPT_VECTOR_ALREADY_CONNECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INTERRUPT_VECTOR_ALREADY_CONNECTED);
#endif
#if defined(ERROR_INTERRUPT_STILL_CONNECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INTERRUPT_STILL_CONNECTED);
#endif
#if defined(ERROR_WAIT_FOR_OPLOCK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WAIT_FOR_OPLOCK);
#endif
#if defined(ERROR_DBG_EXCEPTION_HANDLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DBG_EXCEPTION_HANDLED);
#endif
#if defined(ERROR_DBG_CONTINUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DBG_CONTINUE);
#endif
#if defined(ERROR_CALLBACK_POP_STACK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CALLBACK_POP_STACK);
#endif
#if defined(ERROR_COMPRESSION_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_COMPRESSION_DISABLED);
#endif
#if defined(ERROR_CANTFETCHBACKWARDS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANTFETCHBACKWARDS);
#endif
#if defined(ERROR_CANTSCROLLBACKWARDS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANTSCROLLBACKWARDS);
#endif
#if defined(ERROR_ROWSNOTRELEASED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ROWSNOTRELEASED);
#endif
#if defined(ERROR_BAD_ACCESSOR_FLAGS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_ACCESSOR_FLAGS);
#endif
#if defined(ERROR_ERRORS_ENCOUNTERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ERRORS_ENCOUNTERED);
#endif
#if defined(ERROR_NOT_CAPABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_CAPABLE);
#endif
#if defined(ERROR_REQUEST_OUT_OF_SEQUENCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REQUEST_OUT_OF_SEQUENCE);
#endif
#if defined(ERROR_VERSION_PARSE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VERSION_PARSE_ERROR);
#endif
#if defined(ERROR_BADSTARTPOSITION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BADSTARTPOSITION);
#endif
#if defined(ERROR_MEMORY_HARDWARE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MEMORY_HARDWARE);
#endif
#if defined(ERROR_DISK_REPAIR_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISK_REPAIR_DISABLED);
#endif
#if defined(ERROR_INSUFFICIENT_RESOURCE_FOR_SPECIFIED_SHARED_SECTION_SIZE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSUFFICIENT_RESOURCE_FOR_SPECIFIED_SHARED_SECTION_SIZE);
#endif
#if defined(ERROR_SYSTEM_POWERSTATE_TRANSITION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYSTEM_POWERSTATE_TRANSITION);
#endif
#if defined(ERROR_SYSTEM_POWERSTATE_COMPLEX_TRANSITION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYSTEM_POWERSTATE_COMPLEX_TRANSITION);
#endif
#if defined(ERROR_MCA_EXCEPTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MCA_EXCEPTION);
#endif
#if defined(ERROR_ACCESS_AUDIT_BY_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ACCESS_AUDIT_BY_POLICY);
#endif
#if defined(ERROR_ACCESS_DISABLED_NO_SAFER_UI_BY_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ACCESS_DISABLED_NO_SAFER_UI_BY_POLICY);
#endif
#if defined(ERROR_ABANDON_HIBERFILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ABANDON_HIBERFILE);
#endif
#if defined(ERROR_LOST_WRITEBEHIND_DATA_NETWORK_DISCONNECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOST_WRITEBEHIND_DATA_NETWORK_DISCONNECTED);
#endif
#if defined(ERROR_LOST_WRITEBEHIND_DATA_NETWORK_SERVER_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOST_WRITEBEHIND_DATA_NETWORK_SERVER_ERROR);
#endif
#if defined(ERROR_LOST_WRITEBEHIND_DATA_LOCAL_DISK_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOST_WRITEBEHIND_DATA_LOCAL_DISK_ERROR);
#endif
#if defined(ERROR_BAD_MCFG_TABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_MCFG_TABLE);
#endif
#if defined(ERROR_DISK_REPAIR_REDIRECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISK_REPAIR_REDIRECTED);
#endif
#if defined(ERROR_DISK_REPAIR_UNSUCCESSFUL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISK_REPAIR_UNSUCCESSFUL);
#endif
#if defined(ERROR_CORRUPT_LOG_OVERFULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CORRUPT_LOG_OVERFULL);
#endif
#if defined(ERROR_CORRUPT_LOG_CORRUPTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CORRUPT_LOG_CORRUPTED);
#endif
#if defined(ERROR_CORRUPT_LOG_UNAVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CORRUPT_LOG_UNAVAILABLE);
#endif
#if defined(ERROR_CORRUPT_LOG_DELETED_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CORRUPT_LOG_DELETED_FULL);
#endif
#if defined(ERROR_CORRUPT_LOG_CLEARED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CORRUPT_LOG_CLEARED);
#endif
#if defined(ERROR_ORPHAN_NAME_EXHAUSTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ORPHAN_NAME_EXHAUSTED);
#endif
#if defined(ERROR_OPLOCK_SWITCHED_TO_NEW_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OPLOCK_SWITCHED_TO_NEW_HANDLE);
#endif
#if defined(ERROR_CANNOT_GRANT_REQUESTED_OPLOCK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_GRANT_REQUESTED_OPLOCK);
#endif
#if defined(ERROR_CANNOT_BREAK_OPLOCK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_BREAK_OPLOCK);
#endif
#if defined(ERROR_OPLOCK_HANDLE_CLOSED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OPLOCK_HANDLE_CLOSED);
#endif
#if defined(ERROR_NO_ACE_CONDITION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_ACE_CONDITION);
#endif
#if defined(ERROR_INVALID_ACE_CONDITION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_ACE_CONDITION);
#endif
#if defined(ERROR_FILE_HANDLE_REVOKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_HANDLE_REVOKED);
#endif
#if defined(ERROR_IMAGE_AT_DIFFERENT_BASE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IMAGE_AT_DIFFERENT_BASE);
#endif
#if defined(ERROR_ENCRYPTED_IO_NOT_POSSIBLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ENCRYPTED_IO_NOT_POSSIBLE);
#endif
#if defined(ERROR_FILE_METADATA_OPTIMIZATION_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_METADATA_OPTIMIZATION_IN_PROGRESS);
#endif
#if defined(ERROR_QUOTA_ACTIVITY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_QUOTA_ACTIVITY);
#endif
#if defined(ERROR_HANDLE_REVOKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HANDLE_REVOKED);
#endif
#if defined(ERROR_CALLBACK_INVOKE_INLINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CALLBACK_INVOKE_INLINE);
#endif
#if defined(ERROR_CPU_SET_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CPU_SET_INVALID);
#endif
#if defined(ERROR_ENCLAVE_NOT_TERMINATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ENCLAVE_NOT_TERMINATED);
#endif
#if defined(ERROR_EA_ACCESS_DENIED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EA_ACCESS_DENIED);
#endif
#if defined(ERROR_OPERATION_ABORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OPERATION_ABORTED);
#endif
#if defined(ERROR_IO_INCOMPLETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IO_INCOMPLETE);
#endif
#if defined(ERROR_IO_PENDING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IO_PENDING);
#endif
#if defined(ERROR_NOACCESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOACCESS);
#endif
#if defined(ERROR_SWAPERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SWAPERROR);
#endif
#if defined(ERROR_STACK_OVERFLOW)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STACK_OVERFLOW);
#endif
#if defined(ERROR_INVALID_MESSAGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_MESSAGE);
#endif
#if defined(ERROR_CAN_NOT_COMPLETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CAN_NOT_COMPLETE);
#endif
#if defined(ERROR_INVALID_FLAGS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_FLAGS);
#endif
#if defined(ERROR_UNRECOGNIZED_VOLUME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNRECOGNIZED_VOLUME);
#endif
#if defined(ERROR_FILE_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_INVALID);
#endif
#if defined(ERROR_FULLSCREEN_MODE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FULLSCREEN_MODE);
#endif
#if defined(ERROR_NO_TOKEN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_TOKEN);
#endif
#if defined(ERROR_BADDB)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BADDB);
#endif
#if defined(ERROR_BADKEY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BADKEY);
#endif
#if defined(ERROR_CANTOPEN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANTOPEN);
#endif
#if defined(ERROR_CANTREAD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANTREAD);
#endif
#if defined(ERROR_CANTWRITE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANTWRITE);
#endif
#if defined(ERROR_REGISTRY_RECOVERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REGISTRY_RECOVERED);
#endif
#if defined(ERROR_REGISTRY_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REGISTRY_CORRUPT);
#endif
#if defined(ERROR_REGISTRY_IO_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REGISTRY_IO_FAILED);
#endif
#if defined(ERROR_NOT_REGISTRY_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_REGISTRY_FILE);
#endif
#if defined(ERROR_KEY_DELETED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_KEY_DELETED);
#endif
#if defined(ERROR_NO_LOG_SPACE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_LOG_SPACE);
#endif
#if defined(ERROR_KEY_HAS_CHILDREN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_KEY_HAS_CHILDREN);
#endif
#if defined(ERROR_CHILD_MUST_BE_VOLATILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CHILD_MUST_BE_VOLATILE);
#endif
#if defined(ERROR_NOTIFY_ENUM_DIR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOTIFY_ENUM_DIR);
#endif
#if defined(ERROR_DEPENDENT_SERVICES_RUNNING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEPENDENT_SERVICES_RUNNING);
#endif
#if defined(ERROR_INVALID_SERVICE_CONTROL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SERVICE_CONTROL);
#endif
#if defined(ERROR_SERVICE_REQUEST_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_REQUEST_TIMEOUT);
#endif
#if defined(ERROR_SERVICE_NO_THREAD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_NO_THREAD);
#endif
#if defined(ERROR_SERVICE_DATABASE_LOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_DATABASE_LOCKED);
#endif
#if defined(ERROR_SERVICE_ALREADY_RUNNING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_ALREADY_RUNNING);
#endif
#if defined(ERROR_INVALID_SERVICE_ACCOUNT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SERVICE_ACCOUNT);
#endif
#if defined(ERROR_SERVICE_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_DISABLED);
#endif
#if defined(ERROR_CIRCULAR_DEPENDENCY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CIRCULAR_DEPENDENCY);
#endif
#if defined(ERROR_SERVICE_DOES_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_DOES_NOT_EXIST);
#endif
#if defined(ERROR_SERVICE_CANNOT_ACCEPT_CTRL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_CANNOT_ACCEPT_CTRL);
#endif
#if defined(ERROR_SERVICE_NOT_ACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_NOT_ACTIVE);
#endif
#if defined(ERROR_FAILED_SERVICE_CONTROLLER_CONNECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FAILED_SERVICE_CONTROLLER_CONNECT);
#endif
#if defined(ERROR_EXCEPTION_IN_SERVICE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EXCEPTION_IN_SERVICE);
#endif
#if defined(ERROR_DATABASE_DOES_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DATABASE_DOES_NOT_EXIST);
#endif
#if defined(ERROR_SERVICE_SPECIFIC_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_SPECIFIC_ERROR);
#endif
#if defined(ERROR_PROCESS_ABORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROCESS_ABORTED);
#endif
#if defined(ERROR_SERVICE_DEPENDENCY_FAIL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_DEPENDENCY_FAIL);
#endif
#if defined(ERROR_SERVICE_LOGON_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_LOGON_FAILED);
#endif
#if defined(ERROR_SERVICE_START_HANG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_START_HANG);
#endif
#if defined(ERROR_INVALID_SERVICE_LOCK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SERVICE_LOCK);
#endif
#if defined(ERROR_SERVICE_MARKED_FOR_DELETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_MARKED_FOR_DELETE);
#endif
#if defined(ERROR_SERVICE_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_EXISTS);
#endif
#if defined(ERROR_ALREADY_RUNNING_LKG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALREADY_RUNNING_LKG);
#endif
#if defined(ERROR_SERVICE_DEPENDENCY_DELETED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_DEPENDENCY_DELETED);
#endif
#if defined(ERROR_BOOT_ALREADY_ACCEPTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BOOT_ALREADY_ACCEPTED);
#endif
#if defined(ERROR_SERVICE_NEVER_STARTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_NEVER_STARTED);
#endif
#if defined(ERROR_DUPLICATE_SERVICE_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DUPLICATE_SERVICE_NAME);
#endif
#if defined(ERROR_DIFFERENT_SERVICE_ACCOUNT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DIFFERENT_SERVICE_ACCOUNT);
#endif
#if defined(ERROR_CANNOT_DETECT_DRIVER_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_DETECT_DRIVER_FAILURE);
#endif
#if defined(ERROR_CANNOT_DETECT_PROCESS_ABORT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_DETECT_PROCESS_ABORT);
#endif
#if defined(ERROR_NO_RECOVERY_PROGRAM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_RECOVERY_PROGRAM);
#endif
#if defined(ERROR_SERVICE_NOT_IN_EXE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_NOT_IN_EXE);
#endif
#if defined(ERROR_NOT_SAFEBOOT_SERVICE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_SAFEBOOT_SERVICE);
#endif
#if defined(ERROR_END_OF_MEDIA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_END_OF_MEDIA);
#endif
#if defined(ERROR_FILEMARK_DETECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILEMARK_DETECTED);
#endif
#if defined(ERROR_BEGINNING_OF_MEDIA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BEGINNING_OF_MEDIA);
#endif
#if defined(ERROR_SETMARK_DETECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SETMARK_DETECTED);
#endif
#if defined(ERROR_NO_DATA_DETECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_DATA_DETECTED);
#endif
#if defined(ERROR_PARTITION_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PARTITION_FAILURE);
#endif
#if defined(ERROR_INVALID_BLOCK_LENGTH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_BLOCK_LENGTH);
#endif
#if defined(ERROR_DEVICE_NOT_PARTITIONED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_NOT_PARTITIONED);
#endif
#if defined(ERROR_UNABLE_TO_LOCK_MEDIA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNABLE_TO_LOCK_MEDIA);
#endif
#if defined(ERROR_UNABLE_TO_UNLOAD_MEDIA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNABLE_TO_UNLOAD_MEDIA);
#endif
#if defined(ERROR_MEDIA_CHANGED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MEDIA_CHANGED);
#endif
#if defined(ERROR_BUS_RESET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BUS_RESET);
#endif
#if defined(ERROR_NO_MEDIA_IN_DRIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_MEDIA_IN_DRIVE);
#endif
#if defined(ERROR_NO_UNICODE_TRANSLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_UNICODE_TRANSLATION);
#endif
#if defined(ERROR_DLL_INIT_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DLL_INIT_FAILED);
#endif
#if defined(ERROR_SHUTDOWN_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SHUTDOWN_IN_PROGRESS);
#endif
#if defined(ERROR_NO_SHUTDOWN_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SHUTDOWN_IN_PROGRESS);
#endif
#if defined(ERROR_IO_DEVICE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IO_DEVICE);
#endif
#if defined(ERROR_SERIAL_NO_DEVICE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERIAL_NO_DEVICE);
#endif
#if defined(ERROR_IRQ_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IRQ_BUSY);
#endif
#if defined(ERROR_MORE_WRITES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MORE_WRITES);
#endif
#if defined(ERROR_COUNTER_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_COUNTER_TIMEOUT);
#endif
#if defined(ERROR_FLOPPY_ID_MARK_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FLOPPY_ID_MARK_NOT_FOUND);
#endif
#if defined(ERROR_FLOPPY_WRONG_CYLINDER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FLOPPY_WRONG_CYLINDER);
#endif
#if defined(ERROR_FLOPPY_UNKNOWN_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FLOPPY_UNKNOWN_ERROR);
#endif
#if defined(ERROR_FLOPPY_BAD_REGISTERS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FLOPPY_BAD_REGISTERS);
#endif
#if defined(ERROR_DISK_RECALIBRATE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISK_RECALIBRATE_FAILED);
#endif
#if defined(ERROR_DISK_OPERATION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISK_OPERATION_FAILED);
#endif
#if defined(ERROR_DISK_RESET_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISK_RESET_FAILED);
#endif
#if defined(ERROR_EOM_OVERFLOW)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EOM_OVERFLOW);
#endif
#if defined(ERROR_NOT_ENOUGH_SERVER_MEMORY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_ENOUGH_SERVER_MEMORY);
#endif
#if defined(ERROR_POSSIBLE_DEADLOCK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_POSSIBLE_DEADLOCK);
#endif
#if defined(ERROR_MAPPED_ALIGNMENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MAPPED_ALIGNMENT);
#endif
#if defined(ERROR_SET_POWER_STATE_VETOED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SET_POWER_STATE_VETOED);
#endif
#if defined(ERROR_SET_POWER_STATE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SET_POWER_STATE_FAILED);
#endif
#if defined(ERROR_TOO_MANY_LINKS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_LINKS);
#endif
#if defined(ERROR_OLD_WIN_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OLD_WIN_VERSION);
#endif
#if defined(ERROR_APP_WRONG_OS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_APP_WRONG_OS);
#endif
#if defined(ERROR_SINGLE_INSTANCE_APP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SINGLE_INSTANCE_APP);
#endif
#if defined(ERROR_RMODE_APP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RMODE_APP);
#endif
#if defined(ERROR_INVALID_DLL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_DLL);
#endif
#if defined(ERROR_NO_ASSOCIATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_ASSOCIATION);
#endif
#if defined(ERROR_DDE_FAIL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DDE_FAIL);
#endif
#if defined(ERROR_DLL_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DLL_NOT_FOUND);
#endif
#if defined(ERROR_NO_MORE_USER_HANDLES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_MORE_USER_HANDLES);
#endif
#if defined(ERROR_MESSAGE_SYNC_ONLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MESSAGE_SYNC_ONLY);
#endif
#if defined(ERROR_SOURCE_ELEMENT_EMPTY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SOURCE_ELEMENT_EMPTY);
#endif
#if defined(ERROR_DESTINATION_ELEMENT_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DESTINATION_ELEMENT_FULL);
#endif
#if defined(ERROR_ILLEGAL_ELEMENT_ADDRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ILLEGAL_ELEMENT_ADDRESS);
#endif
#if defined(ERROR_MAGAZINE_NOT_PRESENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MAGAZINE_NOT_PRESENT);
#endif
#if defined(ERROR_DEVICE_REINITIALIZATION_NEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_REINITIALIZATION_NEEDED);
#endif
#if defined(ERROR_DEVICE_REQUIRES_CLEANING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_REQUIRES_CLEANING);
#endif
#if defined(ERROR_DEVICE_DOOR_OPEN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_DOOR_OPEN);
#endif
#if defined(ERROR_DEVICE_NOT_CONNECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_NOT_CONNECTED);
#endif
#if defined(ERROR_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_FOUND);
#endif
#if defined(ERROR_NO_MATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_MATCH);
#endif
#if defined(ERROR_SET_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SET_NOT_FOUND);
#endif
#if defined(ERROR_POINT_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_POINT_NOT_FOUND);
#endif
#if defined(ERROR_NO_TRACKING_SERVICE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_TRACKING_SERVICE);
#endif
#if defined(ERROR_NO_VOLUME_ID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_VOLUME_ID);
#endif
#if defined(ERROR_UNABLE_TO_REMOVE_REPLACED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNABLE_TO_REMOVE_REPLACED);
#endif
#if defined(ERROR_UNABLE_TO_MOVE_REPLACEMENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNABLE_TO_MOVE_REPLACEMENT);
#endif
#if defined(ERROR_UNABLE_TO_MOVE_REPLACEMENT_2)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNABLE_TO_MOVE_REPLACEMENT_2);
#endif
#if defined(ERROR_JOURNAL_DELETE_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_JOURNAL_DELETE_IN_PROGRESS);
#endif
#if defined(ERROR_JOURNAL_NOT_ACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_JOURNAL_NOT_ACTIVE);
#endif
#if defined(ERROR_POTENTIAL_FILE_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_POTENTIAL_FILE_FOUND);
#endif
#if defined(ERROR_JOURNAL_ENTRY_DELETED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_JOURNAL_ENTRY_DELETED);
#endif
#if defined(ERROR_SHUTDOWN_IS_SCHEDULED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SHUTDOWN_IS_SCHEDULED);
#endif
#if defined(ERROR_SHUTDOWN_USERS_LOGGED_ON)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SHUTDOWN_USERS_LOGGED_ON);
#endif
#if defined(ERROR_BAD_DEVICE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_DEVICE);
#endif
#if defined(ERROR_CONNECTION_UNAVAIL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONNECTION_UNAVAIL);
#endif
#if defined(ERROR_DEVICE_ALREADY_REMEMBERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_ALREADY_REMEMBERED);
#endif
#if defined(ERROR_NO_NET_OR_BAD_PATH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_NET_OR_BAD_PATH);
#endif
#if defined(ERROR_BAD_PROVIDER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_PROVIDER);
#endif
#if defined(ERROR_CANNOT_OPEN_PROFILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_OPEN_PROFILE);
#endif
#if defined(ERROR_BAD_PROFILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_PROFILE);
#endif
#if defined(ERROR_NOT_CONTAINER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_CONTAINER);
#endif
#if defined(ERROR_EXTENDED_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EXTENDED_ERROR);
#endif
#if defined(ERROR_INVALID_GROUPNAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_GROUPNAME);
#endif
#if defined(ERROR_INVALID_COMPUTERNAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_COMPUTERNAME);
#endif
#if defined(ERROR_INVALID_EVENTNAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_EVENTNAME);
#endif
#if defined(ERROR_INVALID_DOMAINNAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_DOMAINNAME);
#endif
#if defined(ERROR_INVALID_SERVICENAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SERVICENAME);
#endif
#if defined(ERROR_INVALID_NETNAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_NETNAME);
#endif
#if defined(ERROR_INVALID_SHARENAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SHARENAME);
#endif
#if defined(ERROR_INVALID_PASSWORDNAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PASSWORDNAME);
#endif
#if defined(ERROR_INVALID_MESSAGENAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_MESSAGENAME);
#endif
#if defined(ERROR_INVALID_MESSAGEDEST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_MESSAGEDEST);
#endif
#if defined(ERROR_SESSION_CREDENTIAL_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SESSION_CREDENTIAL_CONFLICT);
#endif
#if defined(ERROR_REMOTE_SESSION_LIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REMOTE_SESSION_LIMIT_EXCEEDED);
#endif
#if defined(ERROR_DUP_DOMAINNAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DUP_DOMAINNAME);
#endif
#if defined(ERROR_NO_NETWORK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_NETWORK);
#endif
#if defined(ERROR_CANCELLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANCELLED);
#endif
#if defined(ERROR_USER_MAPPED_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_USER_MAPPED_FILE);
#endif
#if defined(ERROR_CONNECTION_REFUSED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONNECTION_REFUSED);
#endif
#if defined(ERROR_GRACEFUL_DISCONNECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GRACEFUL_DISCONNECT);
#endif
#if defined(ERROR_ADDRESS_ALREADY_ASSOCIATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ADDRESS_ALREADY_ASSOCIATED);
#endif
#if defined(ERROR_ADDRESS_NOT_ASSOCIATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ADDRESS_NOT_ASSOCIATED);
#endif
#if defined(ERROR_CONNECTION_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONNECTION_INVALID);
#endif
#if defined(ERROR_CONNECTION_ACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONNECTION_ACTIVE);
#endif
#if defined(ERROR_NETWORK_UNREACHABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NETWORK_UNREACHABLE);
#endif
#if defined(ERROR_HOST_UNREACHABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HOST_UNREACHABLE);
#endif
#if defined(ERROR_PROTOCOL_UNREACHABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROTOCOL_UNREACHABLE);
#endif
#if defined(ERROR_PORT_UNREACHABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PORT_UNREACHABLE);
#endif
#if defined(ERROR_REQUEST_ABORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REQUEST_ABORTED);
#endif
#if defined(ERROR_CONNECTION_ABORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONNECTION_ABORTED);
#endif
#if defined(ERROR_RETRY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RETRY);
#endif
#if defined(ERROR_CONNECTION_COUNT_LIMIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONNECTION_COUNT_LIMIT);
#endif
#if defined(ERROR_LOGIN_TIME_RESTRICTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOGIN_TIME_RESTRICTION);
#endif
#if defined(ERROR_LOGIN_WKSTA_RESTRICTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOGIN_WKSTA_RESTRICTION);
#endif
#if defined(ERROR_INCORRECT_ADDRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INCORRECT_ADDRESS);
#endif
#if defined(ERROR_ALREADY_REGISTERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALREADY_REGISTERED);
#endif
#if defined(ERROR_SERVICE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_NOT_FOUND);
#endif
#if defined(ERROR_NOT_AUTHENTICATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_AUTHENTICATED);
#endif
#if defined(ERROR_NOT_LOGGED_ON)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_LOGGED_ON);
#endif
#if defined(ERROR_CONTINUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONTINUE);
#endif
#if defined(ERROR_ALREADY_INITIALIZED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALREADY_INITIALIZED);
#endif
#if defined(ERROR_NO_MORE_DEVICES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_MORE_DEVICES);
#endif
#if defined(ERROR_NO_SUCH_SITE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SUCH_SITE);
#endif
#if defined(ERROR_DOMAIN_CONTROLLER_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DOMAIN_CONTROLLER_EXISTS);
#endif
#if defined(ERROR_ONLY_IF_CONNECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ONLY_IF_CONNECTED);
#endif
#if defined(ERROR_OVERRIDE_NOCHANGES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OVERRIDE_NOCHANGES);
#endif
#if defined(ERROR_BAD_USER_PROFILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_USER_PROFILE);
#endif
#if defined(ERROR_NOT_SUPPORTED_ON_SBS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_SUPPORTED_ON_SBS);
#endif
#if defined(ERROR_SERVER_SHUTDOWN_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVER_SHUTDOWN_IN_PROGRESS);
#endif
#if defined(ERROR_HOST_DOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HOST_DOWN);
#endif
#if defined(ERROR_NON_ACCOUNT_SID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NON_ACCOUNT_SID);
#endif
#if defined(ERROR_NON_DOMAIN_SID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NON_DOMAIN_SID);
#endif
#if defined(ERROR_APPHELP_BLOCK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_APPHELP_BLOCK);
#endif
#if defined(ERROR_ACCESS_DISABLED_BY_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ACCESS_DISABLED_BY_POLICY);
#endif
#if defined(ERROR_REG_NAT_CONSUMPTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REG_NAT_CONSUMPTION);
#endif
#if defined(ERROR_CSCSHARE_OFFLINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CSCSHARE_OFFLINE);
#endif
#if defined(ERROR_PKINIT_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PKINIT_FAILURE);
#endif
#if defined(ERROR_SMARTCARD_SUBSYSTEM_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SMARTCARD_SUBSYSTEM_FAILURE);
#endif
#if defined(ERROR_DOWNGRADE_DETECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DOWNGRADE_DETECTED);
#endif
#if defined(ERROR_MACHINE_LOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MACHINE_LOCKED);
#endif
#if defined(ERROR_SMB_GUEST_LOGON_BLOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SMB_GUEST_LOGON_BLOCKED);
#endif
#if defined(ERROR_CALLBACK_SUPPLIED_INVALID_DATA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CALLBACK_SUPPLIED_INVALID_DATA);
#endif
#if defined(ERROR_SYNC_FOREGROUND_REFRESH_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYNC_FOREGROUND_REFRESH_REQUIRED);
#endif
#if defined(ERROR_DRIVER_BLOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DRIVER_BLOCKED);
#endif
#if defined(ERROR_INVALID_IMPORT_OF_NON_DLL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_IMPORT_OF_NON_DLL);
#endif
#if defined(ERROR_ACCESS_DISABLED_WEBBLADE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ACCESS_DISABLED_WEBBLADE);
#endif
#if defined(ERROR_ACCESS_DISABLED_WEBBLADE_TAMPER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ACCESS_DISABLED_WEBBLADE_TAMPER);
#endif
#if defined(ERROR_RECOVERY_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RECOVERY_FAILURE);
#endif
#if defined(ERROR_ALREADY_FIBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALREADY_FIBER);
#endif
#if defined(ERROR_ALREADY_THREAD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALREADY_THREAD);
#endif
#if defined(ERROR_STACK_BUFFER_OVERRUN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STACK_BUFFER_OVERRUN);
#endif
#if defined(ERROR_PARAMETER_QUOTA_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PARAMETER_QUOTA_EXCEEDED);
#endif
#if defined(ERROR_DEBUGGER_INACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEBUGGER_INACTIVE);
#endif
#if defined(ERROR_DELAY_LOAD_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DELAY_LOAD_FAILED);
#endif
#if defined(ERROR_VDM_DISALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VDM_DISALLOWED);
#endif
#if defined(ERROR_UNIDENTIFIED_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNIDENTIFIED_ERROR);
#endif
#if defined(ERROR_INVALID_CRUNTIME_PARAMETER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_CRUNTIME_PARAMETER);
#endif
#if defined(ERROR_BEYOND_VDL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BEYOND_VDL);
#endif
#if defined(ERROR_INCOMPATIBLE_SERVICE_SID_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INCOMPATIBLE_SERVICE_SID_TYPE);
#endif
#if defined(ERROR_DRIVER_PROCESS_TERMINATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DRIVER_PROCESS_TERMINATED);
#endif
#if defined(ERROR_IMPLEMENTATION_LIMIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IMPLEMENTATION_LIMIT);
#endif
#if defined(ERROR_PROCESS_IS_PROTECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROCESS_IS_PROTECTED);
#endif
#if defined(ERROR_SERVICE_NOTIFY_CLIENT_LAGGING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICE_NOTIFY_CLIENT_LAGGING);
#endif
#if defined(ERROR_DISK_QUOTA_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISK_QUOTA_EXCEEDED);
#endif
#if defined(ERROR_CONTENT_BLOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONTENT_BLOCKED);
#endif
#if defined(ERROR_INCOMPATIBLE_SERVICE_PRIVILEGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INCOMPATIBLE_SERVICE_PRIVILEGE);
#endif
#if defined(ERROR_APP_HANG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_APP_HANG);
#endif
#if defined(ERROR_INVALID_LABEL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_LABEL);
#endif
#if defined(ERROR_NOT_ALL_ASSIGNED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_ALL_ASSIGNED);
#endif
#if defined(ERROR_SOME_NOT_MAPPED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SOME_NOT_MAPPED);
#endif
#if defined(ERROR_NO_QUOTAS_FOR_ACCOUNT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_QUOTAS_FOR_ACCOUNT);
#endif
#if defined(ERROR_LOCAL_USER_SESSION_KEY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOCAL_USER_SESSION_KEY);
#endif
#if defined(ERROR_NULL_LM_PASSWORD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NULL_LM_PASSWORD);
#endif
#if defined(ERROR_UNKNOWN_REVISION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNKNOWN_REVISION);
#endif
#if defined(ERROR_REVISION_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REVISION_MISMATCH);
#endif
#if defined(ERROR_INVALID_OWNER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_OWNER);
#endif
#if defined(ERROR_INVALID_PRIMARY_GROUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PRIMARY_GROUP);
#endif
#if defined(ERROR_NO_IMPERSONATION_TOKEN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_IMPERSONATION_TOKEN);
#endif
#if defined(ERROR_CANT_DISABLE_MANDATORY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_DISABLE_MANDATORY);
#endif
#if defined(ERROR_NO_LOGON_SERVERS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_LOGON_SERVERS);
#endif
#if defined(ERROR_NO_SUCH_LOGON_SESSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SUCH_LOGON_SESSION);
#endif
#if defined(ERROR_NO_SUCH_PRIVILEGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SUCH_PRIVILEGE);
#endif
#if defined(ERROR_PRIVILEGE_NOT_HELD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRIVILEGE_NOT_HELD);
#endif
#if defined(ERROR_INVALID_ACCOUNT_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_ACCOUNT_NAME);
#endif
#if defined(ERROR_USER_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_USER_EXISTS);
#endif
#if defined(ERROR_NO_SUCH_USER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SUCH_USER);
#endif
#if defined(ERROR_GROUP_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GROUP_EXISTS);
#endif
#if defined(ERROR_NO_SUCH_GROUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SUCH_GROUP);
#endif
#if defined(ERROR_MEMBER_IN_GROUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MEMBER_IN_GROUP);
#endif
#if defined(ERROR_MEMBER_NOT_IN_GROUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MEMBER_NOT_IN_GROUP);
#endif
#if defined(ERROR_LAST_ADMIN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LAST_ADMIN);
#endif
#if defined(ERROR_WRONG_PASSWORD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WRONG_PASSWORD);
#endif
#if defined(ERROR_ILL_FORMED_PASSWORD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ILL_FORMED_PASSWORD);
#endif
#if defined(ERROR_PASSWORD_RESTRICTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PASSWORD_RESTRICTION);
#endif
#if defined(ERROR_LOGON_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOGON_FAILURE);
#endif
#if defined(ERROR_ACCOUNT_RESTRICTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ACCOUNT_RESTRICTION);
#endif
#if defined(ERROR_INVALID_LOGON_HOURS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_LOGON_HOURS);
#endif
#if defined(ERROR_INVALID_WORKSTATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_WORKSTATION);
#endif
#if defined(ERROR_PASSWORD_EXPIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PASSWORD_EXPIRED);
#endif
#if defined(ERROR_ACCOUNT_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ACCOUNT_DISABLED);
#endif
#if defined(ERROR_NONE_MAPPED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NONE_MAPPED);
#endif
#if defined(ERROR_TOO_MANY_LUIDS_REQUESTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_LUIDS_REQUESTED);
#endif
#if defined(ERROR_LUIDS_EXHAUSTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LUIDS_EXHAUSTED);
#endif
#if defined(ERROR_INVALID_SUB_AUTHORITY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SUB_AUTHORITY);
#endif
#if defined(ERROR_INVALID_ACL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_ACL);
#endif
#if defined(ERROR_INVALID_SID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SID);
#endif
#if defined(ERROR_INVALID_SECURITY_DESCR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SECURITY_DESCR);
#endif
#if defined(ERROR_BAD_INHERITANCE_ACL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_INHERITANCE_ACL);
#endif
#if defined(ERROR_SERVER_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVER_DISABLED);
#endif
#if defined(ERROR_SERVER_NOT_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVER_NOT_DISABLED);
#endif
#if defined(ERROR_INVALID_ID_AUTHORITY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_ID_AUTHORITY);
#endif
#if defined(ERROR_ALLOTTED_SPACE_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALLOTTED_SPACE_EXCEEDED);
#endif
#if defined(ERROR_INVALID_GROUP_ATTRIBUTES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_GROUP_ATTRIBUTES);
#endif
#if defined(ERROR_BAD_IMPERSONATION_LEVEL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_IMPERSONATION_LEVEL);
#endif
#if defined(ERROR_CANT_OPEN_ANONYMOUS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_OPEN_ANONYMOUS);
#endif
#if defined(ERROR_BAD_VALIDATION_CLASS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_VALIDATION_CLASS);
#endif
#if defined(ERROR_BAD_TOKEN_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_TOKEN_TYPE);
#endif
#if defined(ERROR_NO_SECURITY_ON_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SECURITY_ON_OBJECT);
#endif
#if defined(ERROR_CANT_ACCESS_DOMAIN_INFO)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_ACCESS_DOMAIN_INFO);
#endif
#if defined(ERROR_INVALID_SERVER_STATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SERVER_STATE);
#endif
#if defined(ERROR_INVALID_DOMAIN_STATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_DOMAIN_STATE);
#endif
#if defined(ERROR_INVALID_DOMAIN_ROLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_DOMAIN_ROLE);
#endif
#if defined(ERROR_NO_SUCH_DOMAIN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SUCH_DOMAIN);
#endif
#if defined(ERROR_DOMAIN_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DOMAIN_EXISTS);
#endif
#if defined(ERROR_DOMAIN_LIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DOMAIN_LIMIT_EXCEEDED);
#endif
#if defined(ERROR_INTERNAL_DB_CORRUPTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INTERNAL_DB_CORRUPTION);
#endif
#if defined(ERROR_INTERNAL_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INTERNAL_ERROR);
#endif
#if defined(ERROR_GENERIC_NOT_MAPPED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GENERIC_NOT_MAPPED);
#endif
#if defined(ERROR_BAD_DESCRIPTOR_FORMAT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_DESCRIPTOR_FORMAT);
#endif
#if defined(ERROR_NOT_LOGON_PROCESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_LOGON_PROCESS);
#endif
#if defined(ERROR_LOGON_SESSION_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOGON_SESSION_EXISTS);
#endif
#if defined(ERROR_NO_SUCH_PACKAGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SUCH_PACKAGE);
#endif
#if defined(ERROR_BAD_LOGON_SESSION_STATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_LOGON_SESSION_STATE);
#endif
#if defined(ERROR_LOGON_SESSION_COLLISION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOGON_SESSION_COLLISION);
#endif
#if defined(ERROR_INVALID_LOGON_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_LOGON_TYPE);
#endif
#if defined(ERROR_CANNOT_IMPERSONATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_IMPERSONATE);
#endif
#if defined(ERROR_RXACT_INVALID_STATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RXACT_INVALID_STATE);
#endif
#if defined(ERROR_RXACT_COMMIT_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RXACT_COMMIT_FAILURE);
#endif
#if defined(ERROR_SPECIAL_ACCOUNT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SPECIAL_ACCOUNT);
#endif
#if defined(ERROR_SPECIAL_GROUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SPECIAL_GROUP);
#endif
#if defined(ERROR_SPECIAL_USER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SPECIAL_USER);
#endif
#if defined(ERROR_MEMBERS_PRIMARY_GROUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MEMBERS_PRIMARY_GROUP);
#endif
#if defined(ERROR_TOKEN_ALREADY_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOKEN_ALREADY_IN_USE);
#endif
#if defined(ERROR_NO_SUCH_ALIAS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SUCH_ALIAS);
#endif
#if defined(ERROR_MEMBER_NOT_IN_ALIAS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MEMBER_NOT_IN_ALIAS);
#endif
#if defined(ERROR_MEMBER_IN_ALIAS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MEMBER_IN_ALIAS);
#endif
#if defined(ERROR_ALIAS_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALIAS_EXISTS);
#endif
#if defined(ERROR_LOGON_NOT_GRANTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOGON_NOT_GRANTED);
#endif
#if defined(ERROR_TOO_MANY_SECRETS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_SECRETS);
#endif
#if defined(ERROR_SECRET_TOO_LONG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECRET_TOO_LONG);
#endif
#if defined(ERROR_INTERNAL_DB_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INTERNAL_DB_ERROR);
#endif
#if defined(ERROR_TOO_MANY_CONTEXT_IDS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_CONTEXT_IDS);
#endif
#if defined(ERROR_LOGON_TYPE_NOT_GRANTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOGON_TYPE_NOT_GRANTED);
#endif
#if defined(ERROR_NT_CROSS_ENCRYPTION_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NT_CROSS_ENCRYPTION_REQUIRED);
#endif
#if defined(ERROR_NO_SUCH_MEMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SUCH_MEMBER);
#endif
#if defined(ERROR_INVALID_MEMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_MEMBER);
#endif
#if defined(ERROR_TOO_MANY_SIDS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TOO_MANY_SIDS);
#endif
#if defined(ERROR_LM_CROSS_ENCRYPTION_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LM_CROSS_ENCRYPTION_REQUIRED);
#endif
#if defined(ERROR_NO_INHERITANCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_INHERITANCE);
#endif
#if defined(ERROR_FILE_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_CORRUPT);
#endif
#if defined(ERROR_DISK_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISK_CORRUPT);
#endif
#if defined(ERROR_NO_USER_SESSION_KEY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_USER_SESSION_KEY);
#endif
#if defined(ERROR_LICENSE_QUOTA_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LICENSE_QUOTA_EXCEEDED);
#endif
#if defined(ERROR_WRONG_TARGET_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WRONG_TARGET_NAME);
#endif
#if defined(ERROR_MUTUAL_AUTH_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MUTUAL_AUTH_FAILED);
#endif
#if defined(ERROR_TIME_SKEW)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TIME_SKEW);
#endif
#if defined(ERROR_CURRENT_DOMAIN_NOT_ALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CURRENT_DOMAIN_NOT_ALLOWED);
#endif
#if defined(ERROR_INVALID_WINDOW_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_WINDOW_HANDLE);
#endif
#if defined(ERROR_INVALID_MENU_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_MENU_HANDLE);
#endif
#if defined(ERROR_INVALID_CURSOR_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_CURSOR_HANDLE);
#endif
#if defined(ERROR_INVALID_ACCEL_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_ACCEL_HANDLE);
#endif
#if defined(ERROR_INVALID_HOOK_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_HOOK_HANDLE);
#endif
#if defined(ERROR_INVALID_DWP_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_DWP_HANDLE);
#endif
#if defined(ERROR_TLW_WITH_WSCHILD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TLW_WITH_WSCHILD);
#endif
#if defined(ERROR_CANNOT_FIND_WND_CLASS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_FIND_WND_CLASS);
#endif
#if defined(ERROR_WINDOW_OF_OTHER_THREAD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WINDOW_OF_OTHER_THREAD);
#endif
#if defined(ERROR_HOTKEY_ALREADY_REGISTERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HOTKEY_ALREADY_REGISTERED);
#endif
#if defined(ERROR_CLASS_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLASS_ALREADY_EXISTS);
#endif
#if defined(ERROR_CLASS_DOES_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLASS_DOES_NOT_EXIST);
#endif
#if defined(ERROR_CLASS_HAS_WINDOWS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLASS_HAS_WINDOWS);
#endif
#if defined(ERROR_INVALID_INDEX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_INDEX);
#endif
#if defined(ERROR_INVALID_ICON_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_ICON_HANDLE);
#endif
#if defined(ERROR_PRIVATE_DIALOG_INDEX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRIVATE_DIALOG_INDEX);
#endif
#if defined(ERROR_LISTBOX_ID_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LISTBOX_ID_NOT_FOUND);
#endif
#if defined(ERROR_NO_WILDCARD_CHARACTERS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_WILDCARD_CHARACTERS);
#endif
#if defined(ERROR_CLIPBOARD_NOT_OPEN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLIPBOARD_NOT_OPEN);
#endif
#if defined(ERROR_HOTKEY_NOT_REGISTERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HOTKEY_NOT_REGISTERED);
#endif
#if defined(ERROR_WINDOW_NOT_DIALOG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WINDOW_NOT_DIALOG);
#endif
#if defined(ERROR_CONTROL_ID_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONTROL_ID_NOT_FOUND);
#endif
#if defined(ERROR_INVALID_COMBOBOX_MESSAGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_COMBOBOX_MESSAGE);
#endif
#if defined(ERROR_WINDOW_NOT_COMBOBOX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WINDOW_NOT_COMBOBOX);
#endif
#if defined(ERROR_INVALID_EDIT_HEIGHT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_EDIT_HEIGHT);
#endif
#if defined(ERROR_DC_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DC_NOT_FOUND);
#endif
#if defined(ERROR_INVALID_HOOK_FILTER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_HOOK_FILTER);
#endif
#if defined(ERROR_INVALID_FILTER_PROC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_FILTER_PROC);
#endif
#if defined(ERROR_HOOK_NEEDS_HMOD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HOOK_NEEDS_HMOD);
#endif
#if defined(ERROR_GLOBAL_ONLY_HOOK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GLOBAL_ONLY_HOOK);
#endif
#if defined(ERROR_JOURNAL_HOOK_SET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_JOURNAL_HOOK_SET);
#endif
#if defined(ERROR_HOOK_NOT_INSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HOOK_NOT_INSTALLED);
#endif
#if defined(ERROR_INVALID_LB_MESSAGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_LB_MESSAGE);
#endif
#if defined(ERROR_SETCOUNT_ON_BAD_LB)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SETCOUNT_ON_BAD_LB);
#endif
#if defined(ERROR_LB_WITHOUT_TABSTOPS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LB_WITHOUT_TABSTOPS);
#endif
#if defined(ERROR_DESTROY_OBJECT_OF_OTHER_THREAD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DESTROY_OBJECT_OF_OTHER_THREAD);
#endif
#if defined(ERROR_CHILD_WINDOW_MENU)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CHILD_WINDOW_MENU);
#endif
#if defined(ERROR_NO_SYSTEM_MENU)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SYSTEM_MENU);
#endif
#if defined(ERROR_INVALID_MSGBOX_STYLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_MSGBOX_STYLE);
#endif
#if defined(ERROR_INVALID_SPI_VALUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SPI_VALUE);
#endif
#if defined(ERROR_SCREEN_ALREADY_LOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SCREEN_ALREADY_LOCKED);
#endif
#if defined(ERROR_HWNDS_HAVE_DIFF_PARENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HWNDS_HAVE_DIFF_PARENT);
#endif
#if defined(ERROR_NOT_CHILD_WINDOW)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_CHILD_WINDOW);
#endif
#if defined(ERROR_INVALID_GW_COMMAND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_GW_COMMAND);
#endif
#if defined(ERROR_INVALID_THREAD_ID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_THREAD_ID);
#endif
#if defined(ERROR_NON_MDICHILD_WINDOW)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NON_MDICHILD_WINDOW);
#endif
#if defined(ERROR_POPUP_ALREADY_ACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_POPUP_ALREADY_ACTIVE);
#endif
#if defined(ERROR_NO_SCROLLBARS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SCROLLBARS);
#endif
#if defined(ERROR_INVALID_SCROLLBAR_RANGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SCROLLBAR_RANGE);
#endif
#if defined(ERROR_INVALID_SHOWWIN_COMMAND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SHOWWIN_COMMAND);
#endif
#if defined(ERROR_NO_SYSTEM_RESOURCES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SYSTEM_RESOURCES);
#endif
#if defined(ERROR_NONPAGED_SYSTEM_RESOURCES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NONPAGED_SYSTEM_RESOURCES);
#endif
#if defined(ERROR_PAGED_SYSTEM_RESOURCES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PAGED_SYSTEM_RESOURCES);
#endif
#if defined(ERROR_WORKING_SET_QUOTA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WORKING_SET_QUOTA);
#endif
#if defined(ERROR_PAGEFILE_QUOTA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PAGEFILE_QUOTA);
#endif
#if defined(ERROR_COMMITMENT_LIMIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_COMMITMENT_LIMIT);
#endif
#if defined(ERROR_MENU_ITEM_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MENU_ITEM_NOT_FOUND);
#endif
#if defined(ERROR_INVALID_KEYBOARD_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_KEYBOARD_HANDLE);
#endif
#if defined(ERROR_HOOK_TYPE_NOT_ALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HOOK_TYPE_NOT_ALLOWED);
#endif
#if defined(ERROR_REQUIRES_INTERACTIVE_WINDOWSTATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REQUIRES_INTERACTIVE_WINDOWSTATION);
#endif
#if defined(ERROR_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TIMEOUT);
#endif
#if defined(ERROR_INVALID_MONITOR_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_MONITOR_HANDLE);
#endif
#if defined(ERROR_INCORRECT_SIZE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INCORRECT_SIZE);
#endif
#if defined(ERROR_SYMLINK_CLASS_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYMLINK_CLASS_DISABLED);
#endif
#if defined(ERROR_SYMLINK_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYMLINK_NOT_SUPPORTED);
#endif
#if defined(ERROR_XML_PARSE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_XML_PARSE_ERROR);
#endif
#if defined(ERROR_XMLDSIG_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_XMLDSIG_ERROR);
#endif
#if defined(ERROR_RESTART_APPLICATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESTART_APPLICATION);
#endif
#if defined(ERROR_WRONG_COMPARTMENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WRONG_COMPARTMENT);
#endif
#if defined(ERROR_AUTHIP_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_AUTHIP_FAILURE);
#endif
#if defined(ERROR_NO_NVRAM_RESOURCES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_NVRAM_RESOURCES);
#endif
#if defined(ERROR_NOT_GUI_PROCESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_GUI_PROCESS);
#endif
#if defined(ERROR_EVENTLOG_FILE_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVENTLOG_FILE_CORRUPT);
#endif
#if defined(ERROR_EVENTLOG_CANT_START)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVENTLOG_CANT_START);
#endif
#if defined(ERROR_LOG_FILE_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_FILE_FULL);
#endif
#if defined(ERROR_EVENTLOG_FILE_CHANGED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVENTLOG_FILE_CHANGED);
#endif
#if defined(ERROR_CONTAINER_ASSIGNED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONTAINER_ASSIGNED);
#endif
#if defined(ERROR_JOB_NO_CONTAINER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_JOB_NO_CONTAINER);
#endif
#if defined(ERROR_INVALID_TASK_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_TASK_NAME);
#endif
#if defined(ERROR_INVALID_TASK_INDEX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_TASK_INDEX);
#endif
#if defined(ERROR_THREAD_ALREADY_IN_TASK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_THREAD_ALREADY_IN_TASK);
#endif
#if defined(ERROR_INSTALL_SERVICE_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_SERVICE_FAILURE);
#endif
#if defined(ERROR_INSTALL_USEREXIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_USEREXIT);
#endif
#if defined(ERROR_INSTALL_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_FAILURE);
#endif
#if defined(ERROR_INSTALL_SUSPEND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_SUSPEND);
#endif
#if defined(ERROR_UNKNOWN_PRODUCT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNKNOWN_PRODUCT);
#endif
#if defined(ERROR_UNKNOWN_FEATURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNKNOWN_FEATURE);
#endif
#if defined(ERROR_UNKNOWN_COMPONENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNKNOWN_COMPONENT);
#endif
#if defined(ERROR_UNKNOWN_PROPERTY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNKNOWN_PROPERTY);
#endif
#if defined(ERROR_INVALID_HANDLE_STATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_HANDLE_STATE);
#endif
#if defined(ERROR_BAD_CONFIGURATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_CONFIGURATION);
#endif
#if defined(ERROR_INDEX_ABSENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INDEX_ABSENT);
#endif
#if defined(ERROR_INSTALL_SOURCE_ABSENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_SOURCE_ABSENT);
#endif
#if defined(ERROR_INSTALL_PACKAGE_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_PACKAGE_VERSION);
#endif
#if defined(ERROR_PRODUCT_UNINSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRODUCT_UNINSTALLED);
#endif
#if defined(ERROR_BAD_QUERY_SYNTAX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_QUERY_SYNTAX);
#endif
#if defined(ERROR_INVALID_FIELD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_FIELD);
#endif
#if defined(ERROR_DEVICE_REMOVED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_REMOVED);
#endif
#if defined(ERROR_INSTALL_ALREADY_RUNNING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_ALREADY_RUNNING);
#endif
#if defined(ERROR_INSTALL_PACKAGE_OPEN_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_PACKAGE_OPEN_FAILED);
#endif
#if defined(ERROR_INSTALL_PACKAGE_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_PACKAGE_INVALID);
#endif
#if defined(ERROR_INSTALL_UI_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_UI_FAILURE);
#endif
#if defined(ERROR_INSTALL_LOG_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_LOG_FAILURE);
#endif
#if defined(ERROR_INSTALL_LANGUAGE_UNSUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_LANGUAGE_UNSUPPORTED);
#endif
#if defined(ERROR_INSTALL_TRANSFORM_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_TRANSFORM_FAILURE);
#endif
#if defined(ERROR_INSTALL_PACKAGE_REJECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_PACKAGE_REJECTED);
#endif
#if defined(ERROR_FUNCTION_NOT_CALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FUNCTION_NOT_CALLED);
#endif
#if defined(ERROR_FUNCTION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FUNCTION_FAILED);
#endif
#if defined(ERROR_INVALID_TABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_TABLE);
#endif
#if defined(ERROR_DATATYPE_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DATATYPE_MISMATCH);
#endif
#if defined(ERROR_UNSUPPORTED_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNSUPPORTED_TYPE);
#endif
#if defined(ERROR_CREATE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CREATE_FAILED);
#endif
#if defined(ERROR_INSTALL_TEMP_UNWRITABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_TEMP_UNWRITABLE);
#endif
#if defined(ERROR_INSTALL_PLATFORM_UNSUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_PLATFORM_UNSUPPORTED);
#endif
#if defined(ERROR_INSTALL_NOTUSED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_NOTUSED);
#endif
#if defined(ERROR_PATCH_PACKAGE_OPEN_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PATCH_PACKAGE_OPEN_FAILED);
#endif
#if defined(ERROR_PATCH_PACKAGE_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PATCH_PACKAGE_INVALID);
#endif
#if defined(ERROR_PATCH_PACKAGE_UNSUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PATCH_PACKAGE_UNSUPPORTED);
#endif
#if defined(ERROR_PRODUCT_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRODUCT_VERSION);
#endif
#if defined(ERROR_INVALID_COMMAND_LINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_COMMAND_LINE);
#endif
#if defined(ERROR_INSTALL_REMOTE_DISALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_REMOTE_DISALLOWED);
#endif
#if defined(ERROR_SUCCESS_REBOOT_INITIATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SUCCESS_REBOOT_INITIATED);
#endif
#if defined(ERROR_PATCH_TARGET_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PATCH_TARGET_NOT_FOUND);
#endif
#if defined(ERROR_PATCH_PACKAGE_REJECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PATCH_PACKAGE_REJECTED);
#endif
#if defined(ERROR_INSTALL_TRANSFORM_REJECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_TRANSFORM_REJECTED);
#endif
#if defined(ERROR_INSTALL_REMOTE_PROHIBITED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_REMOTE_PROHIBITED);
#endif
#if defined(ERROR_PATCH_REMOVAL_UNSUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PATCH_REMOVAL_UNSUPPORTED);
#endif
#if defined(ERROR_UNKNOWN_PATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNKNOWN_PATCH);
#endif
#if defined(ERROR_PATCH_NO_SEQUENCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PATCH_NO_SEQUENCE);
#endif
#if defined(ERROR_PATCH_REMOVAL_DISALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PATCH_REMOVAL_DISALLOWED);
#endif
#if defined(ERROR_INVALID_PATCH_XML)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PATCH_XML);
#endif
#if defined(ERROR_PATCH_MANAGED_ADVERTISED_PRODUCT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PATCH_MANAGED_ADVERTISED_PRODUCT);
#endif
#if defined(ERROR_INSTALL_SERVICE_SAFEBOOT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_SERVICE_SAFEBOOT);
#endif
#if defined(ERROR_FAIL_FAST_EXCEPTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FAIL_FAST_EXCEPTION);
#endif
#if defined(ERROR_INSTALL_REJECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_REJECTED);
#endif
#if defined(ERROR_DYNAMIC_CODE_BLOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DYNAMIC_CODE_BLOCKED);
#endif
#if defined(ERROR_NOT_SAME_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_SAME_OBJECT);
#endif
#if defined(ERROR_STRICT_CFG_VIOLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STRICT_CFG_VIOLATION);
#endif
#if defined(ERROR_SET_CONTEXT_DENIED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SET_CONTEXT_DENIED);
#endif
#if defined(ERROR_CROSS_PARTITION_VIOLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CROSS_PARTITION_VIOLATION);
#endif
#if defined(RPC_S_INVALID_STRING_BINDING)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_STRING_BINDING);
#endif
#if defined(RPC_S_WRONG_KIND_OF_BINDING)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_WRONG_KIND_OF_BINDING);
#endif
#if defined(RPC_S_INVALID_BINDING)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_BINDING);
#endif
#if defined(RPC_S_PROTSEQ_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_PROTSEQ_NOT_SUPPORTED);
#endif
#if defined(RPC_S_INVALID_RPC_PROTSEQ)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_RPC_PROTSEQ);
#endif
#if defined(RPC_S_INVALID_STRING_UUID)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_STRING_UUID);
#endif
#if defined(RPC_S_INVALID_ENDPOINT_FORMAT)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_ENDPOINT_FORMAT);
#endif
#if defined(RPC_S_INVALID_NET_ADDR)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_NET_ADDR);
#endif
#if defined(RPC_S_NO_ENDPOINT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NO_ENDPOINT_FOUND);
#endif
#if defined(RPC_S_INVALID_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_TIMEOUT);
#endif
#if defined(RPC_S_OBJECT_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_OBJECT_NOT_FOUND);
#endif
#if defined(RPC_S_ALREADY_REGISTERED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_ALREADY_REGISTERED);
#endif
#if defined(RPC_S_TYPE_ALREADY_REGISTERED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_TYPE_ALREADY_REGISTERED);
#endif
#if defined(RPC_S_ALREADY_LISTENING)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_ALREADY_LISTENING);
#endif
#if defined(RPC_S_NO_PROTSEQS_REGISTERED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NO_PROTSEQS_REGISTERED);
#endif
#if defined(RPC_S_NOT_LISTENING)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NOT_LISTENING);
#endif
#if defined(RPC_S_UNKNOWN_MGR_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_UNKNOWN_MGR_TYPE);
#endif
#if defined(RPC_S_UNKNOWN_IF)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_UNKNOWN_IF);
#endif
#if defined(RPC_S_NO_BINDINGS)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NO_BINDINGS);
#endif
#if defined(RPC_S_NO_PROTSEQS)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NO_PROTSEQS);
#endif
#if defined(RPC_S_CANT_CREATE_ENDPOINT)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_CANT_CREATE_ENDPOINT);
#endif
#if defined(RPC_S_OUT_OF_RESOURCES)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_OUT_OF_RESOURCES);
#endif
#if defined(RPC_S_SERVER_UNAVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_SERVER_UNAVAILABLE);
#endif
#if defined(RPC_S_SERVER_TOO_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_SERVER_TOO_BUSY);
#endif
#if defined(RPC_S_INVALID_NETWORK_OPTIONS)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_NETWORK_OPTIONS);
#endif
#if defined(RPC_S_NO_CALL_ACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NO_CALL_ACTIVE);
#endif
#if defined(RPC_S_CALL_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_CALL_FAILED);
#endif
#if defined(RPC_S_CALL_FAILED_DNE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_CALL_FAILED_DNE);
#endif
#if defined(RPC_S_PROTOCOL_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_PROTOCOL_ERROR);
#endif
#if defined(RPC_S_PROXY_ACCESS_DENIED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_PROXY_ACCESS_DENIED);
#endif
#if defined(RPC_S_UNSUPPORTED_TRANS_SYN)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_UNSUPPORTED_TRANS_SYN);
#endif
#if defined(RPC_S_UNSUPPORTED_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_UNSUPPORTED_TYPE);
#endif
#if defined(RPC_S_INVALID_TAG)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_TAG);
#endif
#if defined(RPC_S_INVALID_BOUND)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_BOUND);
#endif
#if defined(RPC_S_NO_ENTRY_NAME)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NO_ENTRY_NAME);
#endif
#if defined(RPC_S_INVALID_NAME_SYNTAX)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_NAME_SYNTAX);
#endif
#if defined(RPC_S_UNSUPPORTED_NAME_SYNTAX)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_UNSUPPORTED_NAME_SYNTAX);
#endif
#if defined(RPC_S_UUID_NO_ADDRESS)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_UUID_NO_ADDRESS);
#endif
#if defined(RPC_S_DUPLICATE_ENDPOINT)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_DUPLICATE_ENDPOINT);
#endif
#if defined(RPC_S_UNKNOWN_AUTHN_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_UNKNOWN_AUTHN_TYPE);
#endif
#if defined(RPC_S_MAX_CALLS_TOO_SMALL)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_MAX_CALLS_TOO_SMALL);
#endif
#if defined(RPC_S_STRING_TOO_LONG)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_STRING_TOO_LONG);
#endif
#if defined(RPC_S_PROTSEQ_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_PROTSEQ_NOT_FOUND);
#endif
#if defined(RPC_S_PROCNUM_OUT_OF_RANGE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_PROCNUM_OUT_OF_RANGE);
#endif
#if defined(RPC_S_BINDING_HAS_NO_AUTH)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_BINDING_HAS_NO_AUTH);
#endif
#if defined(RPC_S_UNKNOWN_AUTHN_SERVICE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_UNKNOWN_AUTHN_SERVICE);
#endif
#if defined(RPC_S_UNKNOWN_AUTHN_LEVEL)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_UNKNOWN_AUTHN_LEVEL);
#endif
#if defined(RPC_S_INVALID_AUTH_IDENTITY)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_AUTH_IDENTITY);
#endif
#if defined(RPC_S_UNKNOWN_AUTHZ_SERVICE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_UNKNOWN_AUTHZ_SERVICE);
#endif
#if defined(EPT_S_INVALID_ENTRY)
    BOOST_WINAPI_TEST_CONSTANT(EPT_S_INVALID_ENTRY);
#endif
#if defined(EPT_S_CANT_PERFORM_OP)
    BOOST_WINAPI_TEST_CONSTANT(EPT_S_CANT_PERFORM_OP);
#endif
#if defined(EPT_S_NOT_REGISTERED)
    BOOST_WINAPI_TEST_CONSTANT(EPT_S_NOT_REGISTERED);
#endif
#if defined(RPC_S_NOTHING_TO_EXPORT)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NOTHING_TO_EXPORT);
#endif
#if defined(RPC_S_INCOMPLETE_NAME)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INCOMPLETE_NAME);
#endif
#if defined(RPC_S_INVALID_VERS_OPTION)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_VERS_OPTION);
#endif
#if defined(RPC_S_NO_MORE_MEMBERS)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NO_MORE_MEMBERS);
#endif
#if defined(RPC_S_NOT_ALL_OBJS_UNEXPORTED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NOT_ALL_OBJS_UNEXPORTED);
#endif
#if defined(RPC_S_INTERFACE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INTERFACE_NOT_FOUND);
#endif
#if defined(RPC_S_ENTRY_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_ENTRY_ALREADY_EXISTS);
#endif
#if defined(RPC_S_ENTRY_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_ENTRY_NOT_FOUND);
#endif
#if defined(RPC_S_NAME_SERVICE_UNAVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NAME_SERVICE_UNAVAILABLE);
#endif
#if defined(RPC_S_INVALID_NAF_ID)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_NAF_ID);
#endif
#if defined(RPC_S_CANNOT_SUPPORT)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_CANNOT_SUPPORT);
#endif
#if defined(RPC_S_NO_CONTEXT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NO_CONTEXT_AVAILABLE);
#endif
#if defined(RPC_S_INTERNAL_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INTERNAL_ERROR);
#endif
#if defined(RPC_S_ZERO_DIVIDE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_ZERO_DIVIDE);
#endif
#if defined(RPC_S_ADDRESS_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_ADDRESS_ERROR);
#endif
#if defined(RPC_S_FP_DIV_ZERO)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_FP_DIV_ZERO);
#endif
#if defined(RPC_S_FP_UNDERFLOW)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_FP_UNDERFLOW);
#endif
#if defined(RPC_S_FP_OVERFLOW)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_FP_OVERFLOW);
#endif
#if defined(RPC_X_NO_MORE_ENTRIES)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_NO_MORE_ENTRIES);
#endif
#if defined(RPC_X_SS_CHAR_TRANS_OPEN_FAIL)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_SS_CHAR_TRANS_OPEN_FAIL);
#endif
#if defined(RPC_X_SS_CHAR_TRANS_SHORT_FILE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_SS_CHAR_TRANS_SHORT_FILE);
#endif
#if defined(RPC_X_SS_IN_NULL_CONTEXT)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_SS_IN_NULL_CONTEXT);
#endif
#if defined(RPC_X_SS_CONTEXT_DAMAGED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_SS_CONTEXT_DAMAGED);
#endif
#if defined(RPC_X_SS_HANDLES_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_SS_HANDLES_MISMATCH);
#endif
#if defined(RPC_X_SS_CANNOT_GET_CALL_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_SS_CANNOT_GET_CALL_HANDLE);
#endif
#if defined(RPC_X_NULL_REF_POINTER)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_NULL_REF_POINTER);
#endif
#if defined(RPC_X_ENUM_VALUE_OUT_OF_RANGE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_ENUM_VALUE_OUT_OF_RANGE);
#endif
#if defined(RPC_X_BYTE_COUNT_TOO_SMALL)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_BYTE_COUNT_TOO_SMALL);
#endif
#if defined(RPC_X_BAD_STUB_DATA)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_BAD_STUB_DATA);
#endif
#if defined(ERROR_INVALID_USER_BUFFER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_USER_BUFFER);
#endif
#if defined(ERROR_UNRECOGNIZED_MEDIA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNRECOGNIZED_MEDIA);
#endif
#if defined(ERROR_NO_TRUST_LSA_SECRET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_TRUST_LSA_SECRET);
#endif
#if defined(ERROR_NO_TRUST_SAM_ACCOUNT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_TRUST_SAM_ACCOUNT);
#endif
#if defined(ERROR_TRUSTED_DOMAIN_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRUSTED_DOMAIN_FAILURE);
#endif
#if defined(ERROR_TRUSTED_RELATIONSHIP_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRUSTED_RELATIONSHIP_FAILURE);
#endif
#if defined(ERROR_TRUST_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRUST_FAILURE);
#endif
#if defined(RPC_S_CALL_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_CALL_IN_PROGRESS);
#endif
#if defined(ERROR_NETLOGON_NOT_STARTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NETLOGON_NOT_STARTED);
#endif
#if defined(ERROR_ACCOUNT_EXPIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ACCOUNT_EXPIRED);
#endif
#if defined(ERROR_REDIRECTOR_HAS_OPEN_HANDLES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REDIRECTOR_HAS_OPEN_HANDLES);
#endif
#if defined(ERROR_PRINTER_DRIVER_ALREADY_INSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINTER_DRIVER_ALREADY_INSTALLED);
#endif
#if defined(ERROR_UNKNOWN_PORT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNKNOWN_PORT);
#endif
#if defined(ERROR_UNKNOWN_PRINTER_DRIVER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNKNOWN_PRINTER_DRIVER);
#endif
#if defined(ERROR_UNKNOWN_PRINTPROCESSOR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNKNOWN_PRINTPROCESSOR);
#endif
#if defined(ERROR_INVALID_SEPARATOR_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_SEPARATOR_FILE);
#endif
#if defined(ERROR_INVALID_PRIORITY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PRIORITY);
#endif
#if defined(ERROR_INVALID_PRINTER_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PRINTER_NAME);
#endif
#if defined(ERROR_PRINTER_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINTER_ALREADY_EXISTS);
#endif
#if defined(ERROR_INVALID_PRINTER_COMMAND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PRINTER_COMMAND);
#endif
#if defined(ERROR_INVALID_DATATYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_DATATYPE);
#endif
#if defined(ERROR_INVALID_ENVIRONMENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_ENVIRONMENT);
#endif
#if defined(RPC_S_NO_MORE_BINDINGS)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NO_MORE_BINDINGS);
#endif
#if defined(ERROR_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT);
#endif
#if defined(ERROR_NOLOGON_WORKSTATION_TRUST_ACCOUNT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOLOGON_WORKSTATION_TRUST_ACCOUNT);
#endif
#if defined(ERROR_NOLOGON_SERVER_TRUST_ACCOUNT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOLOGON_SERVER_TRUST_ACCOUNT);
#endif
#if defined(ERROR_DOMAIN_TRUST_INCONSISTENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DOMAIN_TRUST_INCONSISTENT);
#endif
#if defined(ERROR_SERVER_HAS_OPEN_HANDLES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVER_HAS_OPEN_HANDLES);
#endif
#if defined(ERROR_RESOURCE_DATA_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_DATA_NOT_FOUND);
#endif
#if defined(ERROR_RESOURCE_TYPE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_TYPE_NOT_FOUND);
#endif
#if defined(ERROR_RESOURCE_NAME_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_NAME_NOT_FOUND);
#endif
#if defined(ERROR_RESOURCE_LANG_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_LANG_NOT_FOUND);
#endif
#if defined(ERROR_NOT_ENOUGH_QUOTA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_ENOUGH_QUOTA);
#endif
#if defined(RPC_S_NO_INTERFACES)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NO_INTERFACES);
#endif
#if defined(RPC_S_CALL_CANCELLED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_CALL_CANCELLED);
#endif
#if defined(RPC_S_BINDING_INCOMPLETE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_BINDING_INCOMPLETE);
#endif
#if defined(RPC_S_COMM_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_COMM_FAILURE);
#endif
#if defined(RPC_S_UNSUPPORTED_AUTHN_LEVEL)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_UNSUPPORTED_AUTHN_LEVEL);
#endif
#if defined(RPC_S_NO_PRINC_NAME)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NO_PRINC_NAME);
#endif
#if defined(RPC_S_NOT_RPC_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NOT_RPC_ERROR);
#endif
#if defined(RPC_S_UUID_LOCAL_ONLY)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_UUID_LOCAL_ONLY);
#endif
#if defined(RPC_S_SEC_PKG_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_SEC_PKG_ERROR);
#endif
#if defined(RPC_S_NOT_CANCELLED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NOT_CANCELLED);
#endif
#if defined(RPC_X_INVALID_ES_ACTION)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_INVALID_ES_ACTION);
#endif
#if defined(RPC_X_WRONG_ES_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_WRONG_ES_VERSION);
#endif
#if defined(RPC_X_WRONG_STUB_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_WRONG_STUB_VERSION);
#endif
#if defined(RPC_X_INVALID_PIPE_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_INVALID_PIPE_OBJECT);
#endif
#if defined(RPC_X_WRONG_PIPE_ORDER)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_WRONG_PIPE_ORDER);
#endif
#if defined(RPC_X_WRONG_PIPE_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_WRONG_PIPE_VERSION);
#endif
#if defined(RPC_S_COOKIE_AUTH_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_COOKIE_AUTH_FAILED);
#endif
#if defined(RPC_S_DO_NOT_DISTURB)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_DO_NOT_DISTURB);
#endif
#if defined(RPC_S_SYSTEM_HANDLE_COUNT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_SYSTEM_HANDLE_COUNT_EXCEEDED);
#endif
#if defined(RPC_S_SYSTEM_HANDLE_TYPE_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_SYSTEM_HANDLE_TYPE_MISMATCH);
#endif
#if defined(RPC_S_GROUP_MEMBER_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_GROUP_MEMBER_NOT_FOUND);
#endif
#if defined(EPT_S_CANT_CREATE)
    BOOST_WINAPI_TEST_CONSTANT(EPT_S_CANT_CREATE);
#endif
#if defined(RPC_S_INVALID_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_OBJECT);
#endif
#if defined(ERROR_INVALID_TIME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_TIME);
#endif
#if defined(ERROR_INVALID_FORM_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_FORM_NAME);
#endif
#if defined(ERROR_INVALID_FORM_SIZE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_FORM_SIZE);
#endif
#if defined(ERROR_ALREADY_WAITING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALREADY_WAITING);
#endif
#if defined(ERROR_PRINTER_DELETED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINTER_DELETED);
#endif
#if defined(ERROR_INVALID_PRINTER_STATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PRINTER_STATE);
#endif
#if defined(ERROR_PASSWORD_MUST_CHANGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PASSWORD_MUST_CHANGE);
#endif
#if defined(ERROR_DOMAIN_CONTROLLER_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DOMAIN_CONTROLLER_NOT_FOUND);
#endif
#if defined(ERROR_ACCOUNT_LOCKED_OUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ACCOUNT_LOCKED_OUT);
#endif
#if defined(OR_INVALID_OXID)
    BOOST_WINAPI_TEST_CONSTANT(OR_INVALID_OXID);
#endif
#if defined(OR_INVALID_OID)
    BOOST_WINAPI_TEST_CONSTANT(OR_INVALID_OID);
#endif
#if defined(OR_INVALID_SET)
    BOOST_WINAPI_TEST_CONSTANT(OR_INVALID_SET);
#endif
#if defined(RPC_S_SEND_INCOMPLETE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_SEND_INCOMPLETE);
#endif
#if defined(RPC_S_INVALID_ASYNC_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_ASYNC_HANDLE);
#endif
#if defined(RPC_S_INVALID_ASYNC_CALL)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INVALID_ASYNC_CALL);
#endif
#if defined(RPC_X_PIPE_CLOSED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_PIPE_CLOSED);
#endif
#if defined(RPC_X_PIPE_DISCIPLINE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_PIPE_DISCIPLINE_ERROR);
#endif
#if defined(RPC_X_PIPE_EMPTY)
    BOOST_WINAPI_TEST_CONSTANT(RPC_X_PIPE_EMPTY);
#endif
#if defined(ERROR_NO_SITENAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SITENAME);
#endif
#if defined(ERROR_CANT_ACCESS_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_ACCESS_FILE);
#endif
#if defined(ERROR_CANT_RESOLVE_FILENAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_RESOLVE_FILENAME);
#endif
#if defined(RPC_S_ENTRY_TYPE_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_ENTRY_TYPE_MISMATCH);
#endif
#if defined(RPC_S_NOT_ALL_OBJS_EXPORTED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_NOT_ALL_OBJS_EXPORTED);
#endif
#if defined(RPC_S_INTERFACE_NOT_EXPORTED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_INTERFACE_NOT_EXPORTED);
#endif
#if defined(RPC_S_PROFILE_NOT_ADDED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_PROFILE_NOT_ADDED);
#endif
#if defined(RPC_S_PRF_ELT_NOT_ADDED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_PRF_ELT_NOT_ADDED);
#endif
#if defined(RPC_S_PRF_ELT_NOT_REMOVED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_PRF_ELT_NOT_REMOVED);
#endif
#if defined(RPC_S_GRP_ELT_NOT_ADDED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_GRP_ELT_NOT_ADDED);
#endif
#if defined(RPC_S_GRP_ELT_NOT_REMOVED)
    BOOST_WINAPI_TEST_CONSTANT(RPC_S_GRP_ELT_NOT_REMOVED);
#endif
#if defined(ERROR_KM_DRIVER_BLOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_KM_DRIVER_BLOCKED);
#endif
#if defined(ERROR_CONTEXT_EXPIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONTEXT_EXPIRED);
#endif
#if defined(ERROR_PER_USER_TRUST_QUOTA_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PER_USER_TRUST_QUOTA_EXCEEDED);
#endif
#if defined(ERROR_ALL_USER_TRUST_QUOTA_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALL_USER_TRUST_QUOTA_EXCEEDED);
#endif
#if defined(ERROR_USER_DELETE_TRUST_QUOTA_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_USER_DELETE_TRUST_QUOTA_EXCEEDED);
#endif
#if defined(ERROR_AUTHENTICATION_FIREWALL_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_AUTHENTICATION_FIREWALL_FAILED);
#endif
#if defined(ERROR_REMOTE_PRINT_CONNECTIONS_BLOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REMOTE_PRINT_CONNECTIONS_BLOCKED);
#endif
#if defined(ERROR_NTLM_BLOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NTLM_BLOCKED);
#endif
#if defined(ERROR_PASSWORD_CHANGE_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PASSWORD_CHANGE_REQUIRED);
#endif
#if defined(ERROR_LOST_MODE_LOGON_RESTRICTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOST_MODE_LOGON_RESTRICTION);
#endif
#if defined(ERROR_INVALID_PIXEL_FORMAT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PIXEL_FORMAT);
#endif
#if defined(ERROR_BAD_DRIVER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_DRIVER);
#endif
#if defined(ERROR_INVALID_WINDOW_STYLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_WINDOW_STYLE);
#endif
#if defined(ERROR_METAFILE_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_METAFILE_NOT_SUPPORTED);
#endif
#if defined(ERROR_TRANSFORM_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSFORM_NOT_SUPPORTED);
#endif
#if defined(ERROR_CLIPPING_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLIPPING_NOT_SUPPORTED);
#endif
#if defined(ERROR_INVALID_CMM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_CMM);
#endif
#if defined(ERROR_INVALID_PROFILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PROFILE);
#endif
#if defined(ERROR_TAG_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TAG_NOT_FOUND);
#endif
#if defined(ERROR_TAG_NOT_PRESENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TAG_NOT_PRESENT);
#endif
#if defined(ERROR_DUPLICATE_TAG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DUPLICATE_TAG);
#endif
#if defined(ERROR_PROFILE_NOT_ASSOCIATED_WITH_DEVICE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROFILE_NOT_ASSOCIATED_WITH_DEVICE);
#endif
#if defined(ERROR_PROFILE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROFILE_NOT_FOUND);
#endif
#if defined(ERROR_INVALID_COLORSPACE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_COLORSPACE);
#endif
#if defined(ERROR_ICM_NOT_ENABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ICM_NOT_ENABLED);
#endif
#if defined(ERROR_DELETING_ICM_XFORM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DELETING_ICM_XFORM);
#endif
#if defined(ERROR_INVALID_TRANSFORM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_TRANSFORM);
#endif
#if defined(ERROR_COLORSPACE_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_COLORSPACE_MISMATCH);
#endif
#if defined(ERROR_INVALID_COLORINDEX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_COLORINDEX);
#endif
#if defined(ERROR_PROFILE_DOES_NOT_MATCH_DEVICE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROFILE_DOES_NOT_MATCH_DEVICE);
#endif
#if defined(ERROR_CONNECTED_OTHER_PASSWORD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONNECTED_OTHER_PASSWORD);
#endif
#if defined(ERROR_CONNECTED_OTHER_PASSWORD_DEFAULT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONNECTED_OTHER_PASSWORD_DEFAULT);
#endif
#if defined(ERROR_BAD_USERNAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_USERNAME);
#endif
#if defined(ERROR_NOT_CONNECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_CONNECTED);
#endif
#if defined(ERROR_OPEN_FILES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OPEN_FILES);
#endif
#if defined(ERROR_ACTIVE_CONNECTIONS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ACTIVE_CONNECTIONS);
#endif
#if defined(ERROR_DEVICE_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_IN_USE);
#endif
#if defined(ERROR_UNKNOWN_PRINT_MONITOR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNKNOWN_PRINT_MONITOR);
#endif
#if defined(ERROR_PRINTER_DRIVER_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINTER_DRIVER_IN_USE);
#endif
#if defined(ERROR_SPOOL_FILE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SPOOL_FILE_NOT_FOUND);
#endif
#if defined(ERROR_SPL_NO_STARTDOC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SPL_NO_STARTDOC);
#endif
#if defined(ERROR_SPL_NO_ADDJOB)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SPL_NO_ADDJOB);
#endif
#if defined(ERROR_PRINT_PROCESSOR_ALREADY_INSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINT_PROCESSOR_ALREADY_INSTALLED);
#endif
#if defined(ERROR_PRINT_MONITOR_ALREADY_INSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINT_MONITOR_ALREADY_INSTALLED);
#endif
#if defined(ERROR_INVALID_PRINT_MONITOR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PRINT_MONITOR);
#endif
#if defined(ERROR_PRINT_MONITOR_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINT_MONITOR_IN_USE);
#endif
#if defined(ERROR_PRINTER_HAS_JOBS_QUEUED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINTER_HAS_JOBS_QUEUED);
#endif
#if defined(ERROR_SUCCESS_REBOOT_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SUCCESS_REBOOT_REQUIRED);
#endif
#if defined(ERROR_SUCCESS_RESTART_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SUCCESS_RESTART_REQUIRED);
#endif
#if defined(ERROR_PRINTER_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINTER_NOT_FOUND);
#endif
#if defined(ERROR_PRINTER_DRIVER_WARNED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINTER_DRIVER_WARNED);
#endif
#if defined(ERROR_PRINTER_DRIVER_BLOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINTER_DRIVER_BLOCKED);
#endif
#if defined(ERROR_PRINTER_DRIVER_PACKAGE_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINTER_DRIVER_PACKAGE_IN_USE);
#endif
#if defined(ERROR_CORE_DRIVER_PACKAGE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CORE_DRIVER_PACKAGE_NOT_FOUND);
#endif
#if defined(ERROR_FAIL_REBOOT_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FAIL_REBOOT_REQUIRED);
#endif
#if defined(ERROR_FAIL_REBOOT_INITIATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FAIL_REBOOT_INITIATED);
#endif
#if defined(ERROR_PRINTER_DRIVER_DOWNLOAD_NEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINTER_DRIVER_DOWNLOAD_NEEDED);
#endif
#if defined(ERROR_PRINT_JOB_RESTART_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINT_JOB_RESTART_REQUIRED);
#endif
#if defined(ERROR_INVALID_PRINTER_DRIVER_MANIFEST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PRINTER_DRIVER_MANIFEST);
#endif
#if defined(ERROR_PRINTER_NOT_SHAREABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRINTER_NOT_SHAREABLE);
#endif
#if defined(ERROR_REQUEST_PAUSED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REQUEST_PAUSED);
#endif
#if defined(ERROR_IO_REISSUE_AS_CACHED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IO_REISSUE_AS_CACHED);
#endif
#if defined(ERROR_WINS_INTERNAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WINS_INTERNAL);
#endif
#if defined(ERROR_CAN_NOT_DEL_LOCAL_WINS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CAN_NOT_DEL_LOCAL_WINS);
#endif
#if defined(ERROR_STATIC_INIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATIC_INIT);
#endif
#if defined(ERROR_INC_BACKUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INC_BACKUP);
#endif
#if defined(ERROR_FULL_BACKUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FULL_BACKUP);
#endif
#if defined(ERROR_REC_NON_EXISTENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REC_NON_EXISTENT);
#endif
#if defined(ERROR_RPL_NOT_ALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RPL_NOT_ALLOWED);
#endif
#if defined(PEERDIST_ERROR_CONTENTINFO_VERSION_UNSUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_CONTENTINFO_VERSION_UNSUPPORTED);
#endif
#if defined(PEERDIST_ERROR_CANNOT_PARSE_CONTENTINFO)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_CANNOT_PARSE_CONTENTINFO);
#endif
#if defined(PEERDIST_ERROR_MISSING_DATA)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_MISSING_DATA);
#endif
#if defined(PEERDIST_ERROR_NO_MORE)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_NO_MORE);
#endif
#if defined(PEERDIST_ERROR_NOT_INITIALIZED)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_NOT_INITIALIZED);
#endif
#if defined(PEERDIST_ERROR_ALREADY_INITIALIZED)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_ALREADY_INITIALIZED);
#endif
#if defined(PEERDIST_ERROR_SHUTDOWN_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_SHUTDOWN_IN_PROGRESS);
#endif
#if defined(PEERDIST_ERROR_INVALIDATED)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_INVALIDATED);
#endif
#if defined(PEERDIST_ERROR_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_ALREADY_EXISTS);
#endif
#if defined(PEERDIST_ERROR_OPERATION_NOTFOUND)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_OPERATION_NOTFOUND);
#endif
#if defined(PEERDIST_ERROR_ALREADY_COMPLETED)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_ALREADY_COMPLETED);
#endif
#if defined(PEERDIST_ERROR_OUT_OF_BOUNDS)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_OUT_OF_BOUNDS);
#endif
#if defined(PEERDIST_ERROR_VERSION_UNSUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_VERSION_UNSUPPORTED);
#endif
#if defined(PEERDIST_ERROR_INVALID_CONFIGURATION)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_INVALID_CONFIGURATION);
#endif
#if defined(PEERDIST_ERROR_NOT_LICENSED)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_NOT_LICENSED);
#endif
#if defined(PEERDIST_ERROR_SERVICE_UNAVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_SERVICE_UNAVAILABLE);
#endif
#if defined(PEERDIST_ERROR_TRUST_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(PEERDIST_ERROR_TRUST_FAILURE);
#endif
#if defined(ERROR_DHCP_ADDRESS_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DHCP_ADDRESS_CONFLICT);
#endif
#if defined(ERROR_WMI_GUID_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_GUID_NOT_FOUND);
#endif
#if defined(ERROR_WMI_INSTANCE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_INSTANCE_NOT_FOUND);
#endif
#if defined(ERROR_WMI_ITEMID_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_ITEMID_NOT_FOUND);
#endif
#if defined(ERROR_WMI_TRY_AGAIN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_TRY_AGAIN);
#endif
#if defined(ERROR_WMI_DP_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_DP_NOT_FOUND);
#endif
#if defined(ERROR_WMI_UNRESOLVED_INSTANCE_REF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_UNRESOLVED_INSTANCE_REF);
#endif
#if defined(ERROR_WMI_ALREADY_ENABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_ALREADY_ENABLED);
#endif
#if defined(ERROR_WMI_GUID_DISCONNECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_GUID_DISCONNECTED);
#endif
#if defined(ERROR_WMI_SERVER_UNAVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_SERVER_UNAVAILABLE);
#endif
#if defined(ERROR_WMI_DP_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_DP_FAILED);
#endif
#if defined(ERROR_WMI_INVALID_MOF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_INVALID_MOF);
#endif
#if defined(ERROR_WMI_INVALID_REGINFO)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_INVALID_REGINFO);
#endif
#if defined(ERROR_WMI_ALREADY_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_ALREADY_DISABLED);
#endif
#if defined(ERROR_WMI_READ_ONLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_READ_ONLY);
#endif
#if defined(ERROR_WMI_SET_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WMI_SET_FAILURE);
#endif
#if defined(ERROR_NOT_APPCONTAINER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_APPCONTAINER);
#endif
#if defined(ERROR_APPCONTAINER_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_APPCONTAINER_REQUIRED);
#endif
#if defined(ERROR_NOT_SUPPORTED_IN_APPCONTAINER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_SUPPORTED_IN_APPCONTAINER);
#endif
#if defined(ERROR_INVALID_PACKAGE_SID_LENGTH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_PACKAGE_SID_LENGTH);
#endif
#if defined(ERROR_INVALID_MEDIA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_MEDIA);
#endif
#if defined(ERROR_INVALID_LIBRARY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_LIBRARY);
#endif
#if defined(ERROR_INVALID_MEDIA_POOL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_MEDIA_POOL);
#endif
#if defined(ERROR_DRIVE_MEDIA_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DRIVE_MEDIA_MISMATCH);
#endif
#if defined(ERROR_MEDIA_OFFLINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MEDIA_OFFLINE);
#endif
#if defined(ERROR_LIBRARY_OFFLINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LIBRARY_OFFLINE);
#endif
#if defined(ERROR_EMPTY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EMPTY);
#endif
#if defined(ERROR_NOT_EMPTY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_EMPTY);
#endif
#if defined(ERROR_MEDIA_UNAVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MEDIA_UNAVAILABLE);
#endif
#if defined(ERROR_RESOURCE_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_DISABLED);
#endif
#if defined(ERROR_INVALID_CLEANER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_CLEANER);
#endif
#if defined(ERROR_UNABLE_TO_CLEAN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNABLE_TO_CLEAN);
#endif
#if defined(ERROR_OBJECT_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OBJECT_NOT_FOUND);
#endif
#if defined(ERROR_DATABASE_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DATABASE_FAILURE);
#endif
#if defined(ERROR_DATABASE_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DATABASE_FULL);
#endif
#if defined(ERROR_MEDIA_INCOMPATIBLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MEDIA_INCOMPATIBLE);
#endif
#if defined(ERROR_RESOURCE_NOT_PRESENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_NOT_PRESENT);
#endif
#if defined(ERROR_INVALID_OPERATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_OPERATION);
#endif
#if defined(ERROR_MEDIA_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MEDIA_NOT_AVAILABLE);
#endif
#if defined(ERROR_DEVICE_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEVICE_NOT_AVAILABLE);
#endif
#if defined(ERROR_REQUEST_REFUSED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REQUEST_REFUSED);
#endif
#if defined(ERROR_INVALID_DRIVE_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_DRIVE_OBJECT);
#endif
#if defined(ERROR_LIBRARY_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LIBRARY_FULL);
#endif
#if defined(ERROR_MEDIUM_NOT_ACCESSIBLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MEDIUM_NOT_ACCESSIBLE);
#endif
#if defined(ERROR_UNABLE_TO_LOAD_MEDIUM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNABLE_TO_LOAD_MEDIUM);
#endif
#if defined(ERROR_UNABLE_TO_INVENTORY_DRIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNABLE_TO_INVENTORY_DRIVE);
#endif
#if defined(ERROR_UNABLE_TO_INVENTORY_SLOT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNABLE_TO_INVENTORY_SLOT);
#endif
#if defined(ERROR_UNABLE_TO_INVENTORY_TRANSPORT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNABLE_TO_INVENTORY_TRANSPORT);
#endif
#if defined(ERROR_TRANSPORT_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSPORT_FULL);
#endif
#if defined(ERROR_CONTROLLING_IEPORT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CONTROLLING_IEPORT);
#endif
#if defined(ERROR_UNABLE_TO_EJECT_MOUNTED_MEDIA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNABLE_TO_EJECT_MOUNTED_MEDIA);
#endif
#if defined(ERROR_CLEANER_SLOT_SET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLEANER_SLOT_SET);
#endif
#if defined(ERROR_CLEANER_SLOT_NOT_SET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLEANER_SLOT_NOT_SET);
#endif
#if defined(ERROR_CLEANER_CARTRIDGE_SPENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLEANER_CARTRIDGE_SPENT);
#endif
#if defined(ERROR_UNEXPECTED_OMID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNEXPECTED_OMID);
#endif
#if defined(ERROR_CANT_DELETE_LAST_ITEM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_DELETE_LAST_ITEM);
#endif
#if defined(ERROR_MESSAGE_EXCEEDS_MAX_SIZE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MESSAGE_EXCEEDS_MAX_SIZE);
#endif
#if defined(ERROR_VOLUME_CONTAINS_SYS_FILES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VOLUME_CONTAINS_SYS_FILES);
#endif
#if defined(ERROR_INDIGENOUS_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INDIGENOUS_TYPE);
#endif
#if defined(ERROR_NO_SUPPORTING_DRIVES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SUPPORTING_DRIVES);
#endif
#if defined(ERROR_CLEANER_CARTRIDGE_INSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLEANER_CARTRIDGE_INSTALLED);
#endif
#if defined(ERROR_IEPORT_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IEPORT_FULL);
#endif
#if defined(ERROR_FILE_OFFLINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_OFFLINE);
#endif
#if defined(ERROR_REMOTE_STORAGE_NOT_ACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REMOTE_STORAGE_NOT_ACTIVE);
#endif
#if defined(ERROR_REMOTE_STORAGE_MEDIA_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REMOTE_STORAGE_MEDIA_ERROR);
#endif
#if defined(ERROR_NOT_A_REPARSE_POINT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_A_REPARSE_POINT);
#endif
#if defined(ERROR_REPARSE_ATTRIBUTE_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REPARSE_ATTRIBUTE_CONFLICT);
#endif
#if defined(ERROR_INVALID_REPARSE_DATA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_REPARSE_DATA);
#endif
#if defined(ERROR_REPARSE_TAG_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REPARSE_TAG_INVALID);
#endif
#if defined(ERROR_REPARSE_TAG_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REPARSE_TAG_MISMATCH);
#endif
#if defined(ERROR_REPARSE_POINT_ENCOUNTERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REPARSE_POINT_ENCOUNTERED);
#endif
#if defined(ERROR_APP_DATA_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_APP_DATA_NOT_FOUND);
#endif
#if defined(ERROR_APP_DATA_EXPIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_APP_DATA_EXPIRED);
#endif
#if defined(ERROR_APP_DATA_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_APP_DATA_CORRUPT);
#endif
#if defined(ERROR_APP_DATA_LIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_APP_DATA_LIMIT_EXCEEDED);
#endif
#if defined(ERROR_APP_DATA_REBOOT_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_APP_DATA_REBOOT_REQUIRED);
#endif
#if defined(ERROR_SECUREBOOT_ROLLBACK_DETECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_ROLLBACK_DETECTED);
#endif
#if defined(ERROR_SECUREBOOT_POLICY_VIOLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_POLICY_VIOLATION);
#endif
#if defined(ERROR_SECUREBOOT_INVALID_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_INVALID_POLICY);
#endif
#if defined(ERROR_SECUREBOOT_POLICY_PUBLISHER_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_POLICY_PUBLISHER_NOT_FOUND);
#endif
#if defined(ERROR_SECUREBOOT_POLICY_NOT_SIGNED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_POLICY_NOT_SIGNED);
#endif
#if defined(ERROR_SECUREBOOT_NOT_ENABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_NOT_ENABLED);
#endif
#if defined(ERROR_SECUREBOOT_FILE_REPLACED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_FILE_REPLACED);
#endif
#if defined(ERROR_SECUREBOOT_POLICY_NOT_AUTHORIZED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_POLICY_NOT_AUTHORIZED);
#endif
#if defined(ERROR_SECUREBOOT_POLICY_UNKNOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_POLICY_UNKNOWN);
#endif
#if defined(ERROR_SECUREBOOT_POLICY_MISSING_ANTIROLLBACKVERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_POLICY_MISSING_ANTIROLLBACKVERSION);
#endif
#if defined(ERROR_SECUREBOOT_PLATFORM_ID_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_PLATFORM_ID_MISMATCH);
#endif
#if defined(ERROR_SECUREBOOT_POLICY_ROLLBACK_DETECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_POLICY_ROLLBACK_DETECTED);
#endif
#if defined(ERROR_SECUREBOOT_POLICY_UPGRADE_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_POLICY_UPGRADE_MISMATCH);
#endif
#if defined(ERROR_SECUREBOOT_REQUIRED_POLICY_FILE_MISSING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_REQUIRED_POLICY_FILE_MISSING);
#endif
#if defined(ERROR_SECUREBOOT_NOT_BASE_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_NOT_BASE_POLICY);
#endif
#if defined(ERROR_SECUREBOOT_NOT_SUPPLEMENTAL_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECUREBOOT_NOT_SUPPLEMENTAL_POLICY);
#endif
#if defined(ERROR_OFFLOAD_READ_FLT_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OFFLOAD_READ_FLT_NOT_SUPPORTED);
#endif
#if defined(ERROR_OFFLOAD_WRITE_FLT_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OFFLOAD_WRITE_FLT_NOT_SUPPORTED);
#endif
#if defined(ERROR_OFFLOAD_READ_FILE_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OFFLOAD_READ_FILE_NOT_SUPPORTED);
#endif
#if defined(ERROR_OFFLOAD_WRITE_FILE_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OFFLOAD_WRITE_FILE_NOT_SUPPORTED);
#endif
#if defined(ERROR_ALREADY_HAS_STREAM_ID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALREADY_HAS_STREAM_ID);
#endif
#if defined(ERROR_SMR_GARBAGE_COLLECTION_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SMR_GARBAGE_COLLECTION_REQUIRED);
#endif
#if defined(ERROR_VOLUME_NOT_SIS_ENABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VOLUME_NOT_SIS_ENABLED);
#endif
#if defined(ERROR_SYSTEM_INTEGRITY_ROLLBACK_DETECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYSTEM_INTEGRITY_ROLLBACK_DETECTED);
#endif
#if defined(ERROR_SYSTEM_INTEGRITY_POLICY_VIOLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYSTEM_INTEGRITY_POLICY_VIOLATION);
#endif
#if defined(ERROR_SYSTEM_INTEGRITY_INVALID_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYSTEM_INTEGRITY_INVALID_POLICY);
#endif
#if defined(ERROR_SYSTEM_INTEGRITY_POLICY_NOT_SIGNED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYSTEM_INTEGRITY_POLICY_NOT_SIGNED);
#endif
#if defined(ERROR_VSM_NOT_INITIALIZED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VSM_NOT_INITIALIZED);
#endif
#if defined(ERROR_VSM_DMA_PROTECTION_NOT_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VSM_DMA_PROTECTION_NOT_IN_USE);
#endif
#if defined(ERROR_PLATFORM_MANIFEST_NOT_AUTHORIZED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PLATFORM_MANIFEST_NOT_AUTHORIZED);
#endif
#if defined(ERROR_PLATFORM_MANIFEST_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PLATFORM_MANIFEST_INVALID);
#endif
#if defined(ERROR_PLATFORM_MANIFEST_FILE_NOT_AUTHORIZED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PLATFORM_MANIFEST_FILE_NOT_AUTHORIZED);
#endif
#if defined(ERROR_PLATFORM_MANIFEST_CATALOG_NOT_AUTHORIZED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PLATFORM_MANIFEST_CATALOG_NOT_AUTHORIZED);
#endif
#if defined(ERROR_PLATFORM_MANIFEST_BINARY_ID_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PLATFORM_MANIFEST_BINARY_ID_NOT_FOUND);
#endif
#if defined(ERROR_PLATFORM_MANIFEST_NOT_ACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PLATFORM_MANIFEST_NOT_ACTIVE);
#endif
#if defined(ERROR_PLATFORM_MANIFEST_NOT_SIGNED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PLATFORM_MANIFEST_NOT_SIGNED);
#endif
#if defined(ERROR_DEPENDENT_RESOURCE_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEPENDENT_RESOURCE_EXISTS);
#endif
#if defined(ERROR_DEPENDENCY_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEPENDENCY_NOT_FOUND);
#endif
#if defined(ERROR_DEPENDENCY_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEPENDENCY_ALREADY_EXISTS);
#endif
#if defined(ERROR_RESOURCE_NOT_ONLINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_NOT_ONLINE);
#endif
#if defined(ERROR_HOST_NODE_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HOST_NODE_NOT_AVAILABLE);
#endif
#if defined(ERROR_RESOURCE_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_NOT_AVAILABLE);
#endif
#if defined(ERROR_RESOURCE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_NOT_FOUND);
#endif
#if defined(ERROR_SHUTDOWN_CLUSTER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SHUTDOWN_CLUSTER);
#endif
#if defined(ERROR_CANT_EVICT_ACTIVE_NODE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_EVICT_ACTIVE_NODE);
#endif
#if defined(ERROR_OBJECT_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OBJECT_ALREADY_EXISTS);
#endif
#if defined(ERROR_OBJECT_IN_LIST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OBJECT_IN_LIST);
#endif
#if defined(ERROR_GROUP_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GROUP_NOT_AVAILABLE);
#endif
#if defined(ERROR_GROUP_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GROUP_NOT_FOUND);
#endif
#if defined(ERROR_GROUP_NOT_ONLINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GROUP_NOT_ONLINE);
#endif
#if defined(ERROR_HOST_NODE_NOT_RESOURCE_OWNER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HOST_NODE_NOT_RESOURCE_OWNER);
#endif
#if defined(ERROR_HOST_NODE_NOT_GROUP_OWNER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HOST_NODE_NOT_GROUP_OWNER);
#endif
#if defined(ERROR_RESMON_CREATE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESMON_CREATE_FAILED);
#endif
#if defined(ERROR_RESMON_ONLINE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESMON_ONLINE_FAILED);
#endif
#if defined(ERROR_RESOURCE_ONLINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_ONLINE);
#endif
#if defined(ERROR_QUORUM_RESOURCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_QUORUM_RESOURCE);
#endif
#if defined(ERROR_NOT_QUORUM_CAPABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_QUORUM_CAPABLE);
#endif
#if defined(ERROR_CLUSTER_SHUTTING_DOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_SHUTTING_DOWN);
#endif
#if defined(ERROR_INVALID_STATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_STATE);
#endif
#if defined(ERROR_RESOURCE_PROPERTIES_STORED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_PROPERTIES_STORED);
#endif
#if defined(ERROR_NOT_QUORUM_CLASS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_QUORUM_CLASS);
#endif
#if defined(ERROR_CORE_RESOURCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CORE_RESOURCE);
#endif
#if defined(ERROR_QUORUM_RESOURCE_ONLINE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_QUORUM_RESOURCE_ONLINE_FAILED);
#endif
#if defined(ERROR_QUORUMLOG_OPEN_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_QUORUMLOG_OPEN_FAILED);
#endif
#if defined(ERROR_CLUSTERLOG_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTERLOG_CORRUPT);
#endif
#if defined(ERROR_CLUSTERLOG_RECORD_EXCEEDS_MAXSIZE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTERLOG_RECORD_EXCEEDS_MAXSIZE);
#endif
#if defined(ERROR_CLUSTERLOG_EXCEEDS_MAXSIZE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTERLOG_EXCEEDS_MAXSIZE);
#endif
#if defined(ERROR_CLUSTERLOG_CHKPOINT_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTERLOG_CHKPOINT_NOT_FOUND);
#endif
#if defined(ERROR_CLUSTERLOG_NOT_ENOUGH_SPACE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTERLOG_NOT_ENOUGH_SPACE);
#endif
#if defined(ERROR_QUORUM_OWNER_ALIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_QUORUM_OWNER_ALIVE);
#endif
#if defined(ERROR_NETWORK_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NETWORK_NOT_AVAILABLE);
#endif
#if defined(ERROR_NODE_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NODE_NOT_AVAILABLE);
#endif
#if defined(ERROR_ALL_NODES_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ALL_NODES_NOT_AVAILABLE);
#endif
#if defined(ERROR_RESOURCE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_FAILED);
#endif
#if defined(ERROR_CLUSTER_INVALID_NODE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INVALID_NODE);
#endif
#if defined(ERROR_CLUSTER_NODE_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_EXISTS);
#endif
#if defined(ERROR_CLUSTER_JOIN_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_JOIN_IN_PROGRESS);
#endif
#if defined(ERROR_CLUSTER_NODE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_NOT_FOUND);
#endif
#if defined(ERROR_CLUSTER_LOCAL_NODE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_LOCAL_NODE_NOT_FOUND);
#endif
#if defined(ERROR_CLUSTER_NETWORK_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NETWORK_EXISTS);
#endif
#if defined(ERROR_CLUSTER_NETWORK_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NETWORK_NOT_FOUND);
#endif
#if defined(ERROR_CLUSTER_NETINTERFACE_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NETINTERFACE_EXISTS);
#endif
#if defined(ERROR_CLUSTER_NETINTERFACE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NETINTERFACE_NOT_FOUND);
#endif
#if defined(ERROR_CLUSTER_INVALID_REQUEST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INVALID_REQUEST);
#endif
#if defined(ERROR_CLUSTER_INVALID_NETWORK_PROVIDER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INVALID_NETWORK_PROVIDER);
#endif
#if defined(ERROR_CLUSTER_NODE_DOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_DOWN);
#endif
#if defined(ERROR_CLUSTER_NODE_UNREACHABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_UNREACHABLE);
#endif
#if defined(ERROR_CLUSTER_NODE_NOT_MEMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_NOT_MEMBER);
#endif
#if defined(ERROR_CLUSTER_JOIN_NOT_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_JOIN_NOT_IN_PROGRESS);
#endif
#if defined(ERROR_CLUSTER_INVALID_NETWORK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INVALID_NETWORK);
#endif
#if defined(ERROR_CLUSTER_NODE_UP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_UP);
#endif
#if defined(ERROR_CLUSTER_IPADDR_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_IPADDR_IN_USE);
#endif
#if defined(ERROR_CLUSTER_NODE_NOT_PAUSED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_NOT_PAUSED);
#endif
#if defined(ERROR_CLUSTER_NO_SECURITY_CONTEXT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NO_SECURITY_CONTEXT);
#endif
#if defined(ERROR_CLUSTER_NETWORK_NOT_INTERNAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NETWORK_NOT_INTERNAL);
#endif
#if defined(ERROR_CLUSTER_NODE_ALREADY_UP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_ALREADY_UP);
#endif
#if defined(ERROR_CLUSTER_NODE_ALREADY_DOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_ALREADY_DOWN);
#endif
#if defined(ERROR_CLUSTER_NETWORK_ALREADY_ONLINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NETWORK_ALREADY_ONLINE);
#endif
#if defined(ERROR_CLUSTER_NETWORK_ALREADY_OFFLINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NETWORK_ALREADY_OFFLINE);
#endif
#if defined(ERROR_CLUSTER_NODE_ALREADY_MEMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_ALREADY_MEMBER);
#endif
#if defined(ERROR_CLUSTER_LAST_INTERNAL_NETWORK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_LAST_INTERNAL_NETWORK);
#endif
#if defined(ERROR_CLUSTER_NETWORK_HAS_DEPENDENTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NETWORK_HAS_DEPENDENTS);
#endif
#if defined(ERROR_INVALID_OPERATION_ON_QUORUM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_OPERATION_ON_QUORUM);
#endif
#if defined(ERROR_DEPENDENCY_NOT_ALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEPENDENCY_NOT_ALLOWED);
#endif
#if defined(ERROR_CLUSTER_NODE_PAUSED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_PAUSED);
#endif
#if defined(ERROR_NODE_CANT_HOST_RESOURCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NODE_CANT_HOST_RESOURCE);
#endif
#if defined(ERROR_CLUSTER_NODE_NOT_READY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_NOT_READY);
#endif
#if defined(ERROR_CLUSTER_NODE_SHUTTING_DOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_SHUTTING_DOWN);
#endif
#if defined(ERROR_CLUSTER_JOIN_ABORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_JOIN_ABORTED);
#endif
#if defined(ERROR_CLUSTER_INCOMPATIBLE_VERSIONS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INCOMPATIBLE_VERSIONS);
#endif
#if defined(ERROR_CLUSTER_MAXNUM_OF_RESOURCES_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_MAXNUM_OF_RESOURCES_EXCEEDED);
#endif
#if defined(ERROR_CLUSTER_SYSTEM_CONFIG_CHANGED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_SYSTEM_CONFIG_CHANGED);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_TYPE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_TYPE_NOT_FOUND);
#endif
#if defined(ERROR_CLUSTER_RESTYPE_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESTYPE_NOT_SUPPORTED);
#endif
#if defined(ERROR_CLUSTER_RESNAME_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESNAME_NOT_FOUND);
#endif
#if defined(ERROR_CLUSTER_NO_RPC_PACKAGES_REGISTERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NO_RPC_PACKAGES_REGISTERED);
#endif
#if defined(ERROR_CLUSTER_OWNER_NOT_IN_PREFLIST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_OWNER_NOT_IN_PREFLIST);
#endif
#if defined(ERROR_CLUSTER_DATABASE_SEQMISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_DATABASE_SEQMISMATCH);
#endif
#if defined(ERROR_RESMON_INVALID_STATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESMON_INVALID_STATE);
#endif
#if defined(ERROR_CLUSTER_GUM_NOT_LOCKER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_GUM_NOT_LOCKER);
#endif
#if defined(ERROR_QUORUM_DISK_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_QUORUM_DISK_NOT_FOUND);
#endif
#if defined(ERROR_DATABASE_BACKUP_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DATABASE_BACKUP_CORRUPT);
#endif
#if defined(ERROR_CLUSTER_NODE_ALREADY_HAS_DFS_ROOT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_ALREADY_HAS_DFS_ROOT);
#endif
#if defined(ERROR_RESOURCE_PROPERTY_UNCHANGEABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_PROPERTY_UNCHANGEABLE);
#endif
#if defined(ERROR_NO_ADMIN_ACCESS_POINT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_ADMIN_ACCESS_POINT);
#endif
#if defined(ERROR_CLUSTER_MEMBERSHIP_INVALID_STATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_MEMBERSHIP_INVALID_STATE);
#endif
#if defined(ERROR_CLUSTER_QUORUMLOG_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_QUORUMLOG_NOT_FOUND);
#endif
#if defined(ERROR_CLUSTER_MEMBERSHIP_HALT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_MEMBERSHIP_HALT);
#endif
#if defined(ERROR_CLUSTER_INSTANCE_ID_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INSTANCE_ID_MISMATCH);
#endif
#if defined(ERROR_CLUSTER_NETWORK_NOT_FOUND_FOR_IP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NETWORK_NOT_FOUND_FOR_IP);
#endif
#if defined(ERROR_CLUSTER_PROPERTY_DATA_TYPE_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_PROPERTY_DATA_TYPE_MISMATCH);
#endif
#if defined(ERROR_CLUSTER_EVICT_WITHOUT_CLEANUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_EVICT_WITHOUT_CLEANUP);
#endif
#if defined(ERROR_CLUSTER_PARAMETER_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_PARAMETER_MISMATCH);
#endif
#if defined(ERROR_NODE_CANNOT_BE_CLUSTERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NODE_CANNOT_BE_CLUSTERED);
#endif
#if defined(ERROR_CLUSTER_WRONG_OS_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_WRONG_OS_VERSION);
#endif
#if defined(ERROR_CLUSTER_CANT_CREATE_DUP_CLUSTER_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_CANT_CREATE_DUP_CLUSTER_NAME);
#endif
#if defined(ERROR_CLUSCFG_ALREADY_COMMITTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSCFG_ALREADY_COMMITTED);
#endif
#if defined(ERROR_CLUSCFG_ROLLBACK_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSCFG_ROLLBACK_FAILED);
#endif
#if defined(ERROR_CLUSCFG_SYSTEM_DISK_DRIVE_LETTER_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSCFG_SYSTEM_DISK_DRIVE_LETTER_CONFLICT);
#endif
#if defined(ERROR_CLUSTER_OLD_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_OLD_VERSION);
#endif
#if defined(ERROR_CLUSTER_MISMATCHED_COMPUTER_ACCT_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_MISMATCHED_COMPUTER_ACCT_NAME);
#endif
#if defined(ERROR_CLUSTER_NO_NET_ADAPTERS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NO_NET_ADAPTERS);
#endif
#if defined(ERROR_CLUSTER_POISONED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_POISONED);
#endif
#if defined(ERROR_CLUSTER_GROUP_MOVING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_GROUP_MOVING);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_TYPE_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_TYPE_BUSY);
#endif
#if defined(ERROR_RESOURCE_CALL_TIMED_OUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_CALL_TIMED_OUT);
#endif
#if defined(ERROR_INVALID_CLUSTER_IPV6_ADDRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_CLUSTER_IPV6_ADDRESS);
#endif
#if defined(ERROR_CLUSTER_INTERNAL_INVALID_FUNCTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INTERNAL_INVALID_FUNCTION);
#endif
#if defined(ERROR_CLUSTER_PARAMETER_OUT_OF_BOUNDS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_PARAMETER_OUT_OF_BOUNDS);
#endif
#if defined(ERROR_CLUSTER_PARTIAL_SEND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_PARTIAL_SEND);
#endif
#if defined(ERROR_CLUSTER_REGISTRY_INVALID_FUNCTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_REGISTRY_INVALID_FUNCTION);
#endif
#if defined(ERROR_CLUSTER_INVALID_STRING_TERMINATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INVALID_STRING_TERMINATION);
#endif
#if defined(ERROR_CLUSTER_INVALID_STRING_FORMAT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INVALID_STRING_FORMAT);
#endif
#if defined(ERROR_CLUSTER_DATABASE_TRANSACTION_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_DATABASE_TRANSACTION_IN_PROGRESS);
#endif
#if defined(ERROR_CLUSTER_DATABASE_TRANSACTION_NOT_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_DATABASE_TRANSACTION_NOT_IN_PROGRESS);
#endif
#if defined(ERROR_CLUSTER_NULL_DATA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NULL_DATA);
#endif
#if defined(ERROR_CLUSTER_PARTIAL_READ)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_PARTIAL_READ);
#endif
#if defined(ERROR_CLUSTER_PARTIAL_WRITE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_PARTIAL_WRITE);
#endif
#if defined(ERROR_CLUSTER_CANT_DESERIALIZE_DATA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_CANT_DESERIALIZE_DATA);
#endif
#if defined(ERROR_DEPENDENT_RESOURCE_PROPERTY_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEPENDENT_RESOURCE_PROPERTY_CONFLICT);
#endif
#if defined(ERROR_CLUSTER_NO_QUORUM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NO_QUORUM);
#endif
#if defined(ERROR_CLUSTER_INVALID_IPV6_NETWORK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INVALID_IPV6_NETWORK);
#endif
#if defined(ERROR_CLUSTER_INVALID_IPV6_TUNNEL_NETWORK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INVALID_IPV6_TUNNEL_NETWORK);
#endif
#if defined(ERROR_QUORUM_NOT_ALLOWED_IN_THIS_GROUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_QUORUM_NOT_ALLOWED_IN_THIS_GROUP);
#endif
#if defined(ERROR_DEPENDENCY_TREE_TOO_COMPLEX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEPENDENCY_TREE_TOO_COMPLEX);
#endif
#if defined(ERROR_EXCEPTION_IN_RESOURCE_CALL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EXCEPTION_IN_RESOURCE_CALL);
#endif
#if defined(ERROR_CLUSTER_RHS_FAILED_INITIALIZATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RHS_FAILED_INITIALIZATION);
#endif
#if defined(ERROR_CLUSTER_NOT_INSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NOT_INSTALLED);
#endif
#if defined(ERROR_CLUSTER_RESOURCES_MUST_BE_ONLINE_ON_THE_SAME_NODE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCES_MUST_BE_ONLINE_ON_THE_SAME_NODE);
#endif
#if defined(ERROR_CLUSTER_MAX_NODES_IN_CLUSTER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_MAX_NODES_IN_CLUSTER);
#endif
#if defined(ERROR_CLUSTER_TOO_MANY_NODES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_TOO_MANY_NODES);
#endif
#if defined(ERROR_CLUSTER_OBJECT_ALREADY_USED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_OBJECT_ALREADY_USED);
#endif
#if defined(ERROR_NONCORE_GROUPS_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NONCORE_GROUPS_FOUND);
#endif
#if defined(ERROR_FILE_SHARE_RESOURCE_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_SHARE_RESOURCE_CONFLICT);
#endif
#if defined(ERROR_CLUSTER_EVICT_INVALID_REQUEST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_EVICT_INVALID_REQUEST);
#endif
#if defined(ERROR_CLUSTER_SINGLETON_RESOURCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_SINGLETON_RESOURCE);
#endif
#if defined(ERROR_CLUSTER_GROUP_SINGLETON_RESOURCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_GROUP_SINGLETON_RESOURCE);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_PROVIDER_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_PROVIDER_FAILED);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_CONFIGURATION_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_CONFIGURATION_ERROR);
#endif
#if defined(ERROR_CLUSTER_GROUP_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_GROUP_BUSY);
#endif
#if defined(ERROR_CLUSTER_NOT_SHARED_VOLUME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NOT_SHARED_VOLUME);
#endif
#if defined(ERROR_CLUSTER_INVALID_SECURITY_DESCRIPTOR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INVALID_SECURITY_DESCRIPTOR);
#endif
#if defined(ERROR_CLUSTER_SHARED_VOLUMES_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_SHARED_VOLUMES_IN_USE);
#endif
#if defined(ERROR_CLUSTER_USE_SHARED_VOLUMES_API)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_USE_SHARED_VOLUMES_API);
#endif
#if defined(ERROR_CLUSTER_BACKUP_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_BACKUP_IN_PROGRESS);
#endif
#if defined(ERROR_NON_CSV_PATH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NON_CSV_PATH);
#endif
#if defined(ERROR_CSV_VOLUME_NOT_LOCAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CSV_VOLUME_NOT_LOCAL);
#endif
#if defined(ERROR_CLUSTER_WATCHDOG_TERMINATING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_WATCHDOG_TERMINATING);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_VETOED_MOVE_INCOMPATIBLE_NODES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_VETOED_MOVE_INCOMPATIBLE_NODES);
#endif
#if defined(ERROR_CLUSTER_INVALID_NODE_WEIGHT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INVALID_NODE_WEIGHT);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_VETOED_CALL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_VETOED_CALL);
#endif
#if defined(ERROR_RESMON_SYSTEM_RESOURCES_LACKING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESMON_SYSTEM_RESOURCES_LACKING);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_DESTINATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_DESTINATION);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_SOURCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_SOURCE);
#endif
#if defined(ERROR_CLUSTER_GROUP_QUEUED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_GROUP_QUEUED);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_LOCKED_STATUS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_LOCKED_STATUS);
#endif
#if defined(ERROR_CLUSTER_SHARED_VOLUME_FAILOVER_NOT_ALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_SHARED_VOLUME_FAILOVER_NOT_ALLOWED);
#endif
#if defined(ERROR_CLUSTER_NODE_DRAIN_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_DRAIN_IN_PROGRESS);
#endif
#if defined(ERROR_CLUSTER_DISK_NOT_CONNECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_DISK_NOT_CONNECTED);
#endif
#if defined(ERROR_DISK_NOT_CSV_CAPABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DISK_NOT_CSV_CAPABLE);
#endif
#if defined(ERROR_RESOURCE_NOT_IN_AVAILABLE_STORAGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_NOT_IN_AVAILABLE_STORAGE);
#endif
#if defined(ERROR_CLUSTER_SHARED_VOLUME_REDIRECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_SHARED_VOLUME_REDIRECTED);
#endif
#if defined(ERROR_CLUSTER_SHARED_VOLUME_NOT_REDIRECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_SHARED_VOLUME_NOT_REDIRECTED);
#endif
#if defined(ERROR_CLUSTER_CANNOT_RETURN_PROPERTIES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_CANNOT_RETURN_PROPERTIES);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_CONTAINS_UNSUPPORTED_DIFF_AREA_FOR_SHARED_VOLUMES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_CONTAINS_UNSUPPORTED_DIFF_AREA_FOR_SHARED_VOLUMES);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_IS_IN_MAINTENANCE_MODE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_IS_IN_MAINTENANCE_MODE);
#endif
#if defined(ERROR_CLUSTER_AFFINITY_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_AFFINITY_CONFLICT);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_IS_REPLICA_VIRTUAL_MACHINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_IS_REPLICA_VIRTUAL_MACHINE);
#endif
#if defined(ERROR_CLUSTER_UPGRADE_INCOMPATIBLE_VERSIONS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_UPGRADE_INCOMPATIBLE_VERSIONS);
#endif
#if defined(ERROR_CLUSTER_UPGRADE_FIX_QUORUM_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_UPGRADE_FIX_QUORUM_NOT_SUPPORTED);
#endif
#if defined(ERROR_CLUSTER_UPGRADE_RESTART_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_UPGRADE_RESTART_REQUIRED);
#endif
#if defined(ERROR_CLUSTER_UPGRADE_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_UPGRADE_IN_PROGRESS);
#endif
#if defined(ERROR_CLUSTER_UPGRADE_INCOMPLETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_UPGRADE_INCOMPLETE);
#endif
#if defined(ERROR_CLUSTER_NODE_IN_GRACE_PERIOD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_IN_GRACE_PERIOD);
#endif
#if defined(ERROR_CLUSTER_CSV_IO_PAUSE_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_CSV_IO_PAUSE_TIMEOUT);
#endif
#if defined(ERROR_NODE_NOT_ACTIVE_CLUSTER_MEMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NODE_NOT_ACTIVE_CLUSTER_MEMBER);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_NOT_MONITORED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_NOT_MONITORED);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_DOES_NOT_SUPPORT_UNMONITORED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_DOES_NOT_SUPPORT_UNMONITORED);
#endif
#if defined(ERROR_CLUSTER_RESOURCE_IS_REPLICATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_RESOURCE_IS_REPLICATED);
#endif
#if defined(ERROR_CLUSTER_NODE_ISOLATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_ISOLATED);
#endif
#if defined(ERROR_CLUSTER_NODE_QUARANTINED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_NODE_QUARANTINED);
#endif
#if defined(ERROR_CLUSTER_DATABASE_UPDATE_CONDITION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_DATABASE_UPDATE_CONDITION_FAILED);
#endif
#if defined(ERROR_CLUSTER_SPACE_DEGRADED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_SPACE_DEGRADED);
#endif
#if defined(ERROR_CLUSTER_TOKEN_DELEGATION_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_TOKEN_DELEGATION_NOT_SUPPORTED);
#endif
#if defined(ERROR_CLUSTER_CSV_INVALID_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_CSV_INVALID_HANDLE);
#endif
#if defined(ERROR_CLUSTER_CSV_SUPPORTED_ONLY_ON_COORDINATOR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_CSV_SUPPORTED_ONLY_ON_COORDINATOR);
#endif
#if defined(ERROR_GROUPSET_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GROUPSET_NOT_AVAILABLE);
#endif
#if defined(ERROR_GROUPSET_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GROUPSET_NOT_FOUND);
#endif
#if defined(ERROR_GROUPSET_CANT_PROVIDE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GROUPSET_CANT_PROVIDE);
#endif
#if defined(ERROR_CLUSTER_FAULT_DOMAIN_PARENT_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_FAULT_DOMAIN_PARENT_NOT_FOUND);
#endif
#if defined(ERROR_CLUSTER_FAULT_DOMAIN_INVALID_HIERARCHY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_FAULT_DOMAIN_INVALID_HIERARCHY);
#endif
#if defined(ERROR_CLUSTER_FAULT_DOMAIN_FAILED_S2D_VALIDATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_FAULT_DOMAIN_FAILED_S2D_VALIDATION);
#endif
#if defined(ERROR_CLUSTER_FAULT_DOMAIN_S2D_CONNECTIVITY_LOSS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_FAULT_DOMAIN_S2D_CONNECTIVITY_LOSS);
#endif
#if defined(ERROR_CLUSTER_INVALID_INFRASTRUCTURE_FILESERVER_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTER_INVALID_INFRASTRUCTURE_FILESERVER_NAME);
#endif
#if defined(ERROR_CLUSTERSET_MANAGEMENT_CLUSTER_UNREACHABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CLUSTERSET_MANAGEMENT_CLUSTER_UNREACHABLE);
#endif
#if defined(ERROR_ENCRYPTION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ENCRYPTION_FAILED);
#endif
#if defined(ERROR_DECRYPTION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DECRYPTION_FAILED);
#endif
#if defined(ERROR_FILE_ENCRYPTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_ENCRYPTED);
#endif
#if defined(ERROR_NO_RECOVERY_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_RECOVERY_POLICY);
#endif
#if defined(ERROR_NO_EFS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_EFS);
#endif
#if defined(ERROR_WRONG_EFS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_WRONG_EFS);
#endif
#if defined(ERROR_NO_USER_KEYS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_USER_KEYS);
#endif
#if defined(ERROR_FILE_NOT_ENCRYPTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_NOT_ENCRYPTED);
#endif
#if defined(ERROR_NOT_EXPORT_FORMAT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_EXPORT_FORMAT);
#endif
#if defined(ERROR_FILE_READ_ONLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_READ_ONLY);
#endif
#if defined(ERROR_DIR_EFS_DISALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DIR_EFS_DISALLOWED);
#endif
#if defined(ERROR_EFS_SERVER_NOT_TRUSTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EFS_SERVER_NOT_TRUSTED);
#endif
#if defined(ERROR_BAD_RECOVERY_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_RECOVERY_POLICY);
#endif
#if defined(ERROR_EFS_ALG_BLOB_TOO_BIG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EFS_ALG_BLOB_TOO_BIG);
#endif
#if defined(ERROR_VOLUME_NOT_SUPPORT_EFS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VOLUME_NOT_SUPPORT_EFS);
#endif
#if defined(ERROR_EFS_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EFS_DISABLED);
#endif
#if defined(ERROR_EFS_VERSION_NOT_SUPPORT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EFS_VERSION_NOT_SUPPORT);
#endif
#if defined(ERROR_CS_ENCRYPTION_INVALID_SERVER_RESPONSE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CS_ENCRYPTION_INVALID_SERVER_RESPONSE);
#endif
#if defined(ERROR_CS_ENCRYPTION_UNSUPPORTED_SERVER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CS_ENCRYPTION_UNSUPPORTED_SERVER);
#endif
#if defined(ERROR_CS_ENCRYPTION_EXISTING_ENCRYPTED_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CS_ENCRYPTION_EXISTING_ENCRYPTED_FILE);
#endif
#if defined(ERROR_CS_ENCRYPTION_NEW_ENCRYPTED_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CS_ENCRYPTION_NEW_ENCRYPTED_FILE);
#endif
#if defined(ERROR_CS_ENCRYPTION_FILE_NOT_CSE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CS_ENCRYPTION_FILE_NOT_CSE);
#endif
#if defined(ERROR_ENCRYPTION_POLICY_DENIES_OPERATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ENCRYPTION_POLICY_DENIES_OPERATION);
#endif
#if defined(ERROR_NO_BROWSER_SERVERS_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_BROWSER_SERVERS_FOUND);
#endif
#if defined(SCHED_E_SERVICE_NOT_LOCALSYSTEM)
    BOOST_WINAPI_TEST_CONSTANT(SCHED_E_SERVICE_NOT_LOCALSYSTEM);
#endif
#if defined(ERROR_LOG_SECTOR_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_SECTOR_INVALID);
#endif
#if defined(ERROR_LOG_SECTOR_PARITY_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_SECTOR_PARITY_INVALID);
#endif
#if defined(ERROR_LOG_SECTOR_REMAPPED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_SECTOR_REMAPPED);
#endif
#if defined(ERROR_LOG_BLOCK_INCOMPLETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_BLOCK_INCOMPLETE);
#endif
#if defined(ERROR_LOG_INVALID_RANGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_INVALID_RANGE);
#endif
#if defined(ERROR_LOG_BLOCKS_EXHAUSTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_BLOCKS_EXHAUSTED);
#endif
#if defined(ERROR_LOG_READ_CONTEXT_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_READ_CONTEXT_INVALID);
#endif
#if defined(ERROR_LOG_RESTART_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_RESTART_INVALID);
#endif
#if defined(ERROR_LOG_BLOCK_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_BLOCK_VERSION);
#endif
#if defined(ERROR_LOG_BLOCK_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_BLOCK_INVALID);
#endif
#if defined(ERROR_LOG_READ_MODE_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_READ_MODE_INVALID);
#endif
#if defined(ERROR_LOG_NO_RESTART)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_NO_RESTART);
#endif
#if defined(ERROR_LOG_METADATA_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_METADATA_CORRUPT);
#endif
#if defined(ERROR_LOG_METADATA_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_METADATA_INVALID);
#endif
#if defined(ERROR_LOG_METADATA_INCONSISTENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_METADATA_INCONSISTENT);
#endif
#if defined(ERROR_LOG_RESERVATION_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_RESERVATION_INVALID);
#endif
#if defined(ERROR_LOG_CANT_DELETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_CANT_DELETE);
#endif
#if defined(ERROR_LOG_CONTAINER_LIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_CONTAINER_LIMIT_EXCEEDED);
#endif
#if defined(ERROR_LOG_START_OF_LOG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_START_OF_LOG);
#endif
#if defined(ERROR_LOG_POLICY_ALREADY_INSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_POLICY_ALREADY_INSTALLED);
#endif
#if defined(ERROR_LOG_POLICY_NOT_INSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_POLICY_NOT_INSTALLED);
#endif
#if defined(ERROR_LOG_POLICY_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_POLICY_INVALID);
#endif
#if defined(ERROR_LOG_POLICY_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_POLICY_CONFLICT);
#endif
#if defined(ERROR_LOG_PINNED_ARCHIVE_TAIL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_PINNED_ARCHIVE_TAIL);
#endif
#if defined(ERROR_LOG_RECORD_NONEXISTENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_RECORD_NONEXISTENT);
#endif
#if defined(ERROR_LOG_RECORDS_RESERVED_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_RECORDS_RESERVED_INVALID);
#endif
#if defined(ERROR_LOG_SPACE_RESERVED_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_SPACE_RESERVED_INVALID);
#endif
#if defined(ERROR_LOG_TAIL_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_TAIL_INVALID);
#endif
#if defined(ERROR_LOG_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_FULL);
#endif
#if defined(ERROR_COULD_NOT_RESIZE_LOG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_COULD_NOT_RESIZE_LOG);
#endif
#if defined(ERROR_LOG_MULTIPLEXED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_MULTIPLEXED);
#endif
#if defined(ERROR_LOG_DEDICATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_DEDICATED);
#endif
#if defined(ERROR_LOG_ARCHIVE_NOT_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_ARCHIVE_NOT_IN_PROGRESS);
#endif
#if defined(ERROR_LOG_ARCHIVE_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_ARCHIVE_IN_PROGRESS);
#endif
#if defined(ERROR_LOG_EPHEMERAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_EPHEMERAL);
#endif
#if defined(ERROR_LOG_NOT_ENOUGH_CONTAINERS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_NOT_ENOUGH_CONTAINERS);
#endif
#if defined(ERROR_LOG_CLIENT_ALREADY_REGISTERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_CLIENT_ALREADY_REGISTERED);
#endif
#if defined(ERROR_LOG_CLIENT_NOT_REGISTERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_CLIENT_NOT_REGISTERED);
#endif
#if defined(ERROR_LOG_FULL_HANDLER_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_FULL_HANDLER_IN_PROGRESS);
#endif
#if defined(ERROR_LOG_CONTAINER_READ_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_CONTAINER_READ_FAILED);
#endif
#if defined(ERROR_LOG_CONTAINER_WRITE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_CONTAINER_WRITE_FAILED);
#endif
#if defined(ERROR_LOG_CONTAINER_OPEN_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_CONTAINER_OPEN_FAILED);
#endif
#if defined(ERROR_LOG_CONTAINER_STATE_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_CONTAINER_STATE_INVALID);
#endif
#if defined(ERROR_LOG_STATE_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_STATE_INVALID);
#endif
#if defined(ERROR_LOG_PINNED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_PINNED);
#endif
#if defined(ERROR_LOG_METADATA_FLUSH_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_METADATA_FLUSH_FAILED);
#endif
#if defined(ERROR_LOG_INCONSISTENT_SECURITY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_INCONSISTENT_SECURITY);
#endif
#if defined(ERROR_LOG_APPENDED_FLUSH_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_APPENDED_FLUSH_FAILED);
#endif
#if defined(ERROR_LOG_PINNED_RESERVATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_PINNED_RESERVATION);
#endif
#if defined(ERROR_INVALID_TRANSACTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_TRANSACTION);
#endif
#if defined(ERROR_TRANSACTION_NOT_ACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_NOT_ACTIVE);
#endif
#if defined(ERROR_TRANSACTION_REQUEST_NOT_VALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_REQUEST_NOT_VALID);
#endif
#if defined(ERROR_TRANSACTION_NOT_REQUESTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_NOT_REQUESTED);
#endif
#if defined(ERROR_TRANSACTION_ALREADY_ABORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_ALREADY_ABORTED);
#endif
#if defined(ERROR_TRANSACTION_ALREADY_COMMITTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_ALREADY_COMMITTED);
#endif
#if defined(ERROR_TM_INITIALIZATION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TM_INITIALIZATION_FAILED);
#endif
#if defined(ERROR_RESOURCEMANAGER_READ_ONLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCEMANAGER_READ_ONLY);
#endif
#if defined(ERROR_TRANSACTION_NOT_JOINED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_NOT_JOINED);
#endif
#if defined(ERROR_TRANSACTION_SUPERIOR_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_SUPERIOR_EXISTS);
#endif
#if defined(ERROR_CRM_PROTOCOL_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CRM_PROTOCOL_ALREADY_EXISTS);
#endif
#if defined(ERROR_TRANSACTION_PROPAGATION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_PROPAGATION_FAILED);
#endif
#if defined(ERROR_CRM_PROTOCOL_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CRM_PROTOCOL_NOT_FOUND);
#endif
#if defined(ERROR_TRANSACTION_INVALID_MARSHALL_BUFFER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_INVALID_MARSHALL_BUFFER);
#endif
#if defined(ERROR_CURRENT_TRANSACTION_NOT_VALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CURRENT_TRANSACTION_NOT_VALID);
#endif
#if defined(ERROR_TRANSACTION_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_NOT_FOUND);
#endif
#if defined(ERROR_RESOURCEMANAGER_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCEMANAGER_NOT_FOUND);
#endif
#if defined(ERROR_ENLISTMENT_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ENLISTMENT_NOT_FOUND);
#endif
#if defined(ERROR_TRANSACTIONMANAGER_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTIONMANAGER_NOT_FOUND);
#endif
#if defined(ERROR_TRANSACTIONMANAGER_NOT_ONLINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTIONMANAGER_NOT_ONLINE);
#endif
#if defined(ERROR_TRANSACTIONMANAGER_RECOVERY_NAME_COLLISION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTIONMANAGER_RECOVERY_NAME_COLLISION);
#endif
#if defined(ERROR_TRANSACTION_NOT_ROOT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_NOT_ROOT);
#endif
#if defined(ERROR_TRANSACTION_OBJECT_EXPIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_OBJECT_EXPIRED);
#endif
#if defined(ERROR_TRANSACTION_RESPONSE_NOT_ENLISTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_RESPONSE_NOT_ENLISTED);
#endif
#if defined(ERROR_TRANSACTION_RECORD_TOO_LONG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_RECORD_TOO_LONG);
#endif
#if defined(ERROR_IMPLICIT_TRANSACTION_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IMPLICIT_TRANSACTION_NOT_SUPPORTED);
#endif
#if defined(ERROR_TRANSACTION_INTEGRITY_VIOLATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_INTEGRITY_VIOLATED);
#endif
#if defined(ERROR_TRANSACTIONMANAGER_IDENTITY_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTIONMANAGER_IDENTITY_MISMATCH);
#endif
#if defined(ERROR_RM_CANNOT_BE_FROZEN_FOR_SNAPSHOT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RM_CANNOT_BE_FROZEN_FOR_SNAPSHOT);
#endif
#if defined(ERROR_TRANSACTION_MUST_WRITETHROUGH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_MUST_WRITETHROUGH);
#endif
#if defined(ERROR_TRANSACTION_NO_SUPERIOR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_NO_SUPERIOR);
#endif
#if defined(ERROR_HEURISTIC_DAMAGE_POSSIBLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HEURISTIC_DAMAGE_POSSIBLE);
#endif
#if defined(ERROR_TRANSACTIONAL_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTIONAL_CONFLICT);
#endif
#if defined(ERROR_RM_NOT_ACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RM_NOT_ACTIVE);
#endif
#if defined(ERROR_RM_METADATA_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RM_METADATA_CORRUPT);
#endif
#if defined(ERROR_DIRECTORY_NOT_RM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DIRECTORY_NOT_RM);
#endif
#if defined(ERROR_TRANSACTIONS_UNSUPPORTED_REMOTE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTIONS_UNSUPPORTED_REMOTE);
#endif
#if defined(ERROR_LOG_RESIZE_INVALID_SIZE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_RESIZE_INVALID_SIZE);
#endif
#if defined(ERROR_OBJECT_NO_LONGER_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OBJECT_NO_LONGER_EXISTS);
#endif
#if defined(ERROR_STREAM_MINIVERSION_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STREAM_MINIVERSION_NOT_FOUND);
#endif
#if defined(ERROR_STREAM_MINIVERSION_NOT_VALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STREAM_MINIVERSION_NOT_VALID);
#endif
#if defined(ERROR_MINIVERSION_INACCESSIBLE_FROM_SPECIFIED_TRANSACTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MINIVERSION_INACCESSIBLE_FROM_SPECIFIED_TRANSACTION);
#endif
#if defined(ERROR_CANT_OPEN_MINIVERSION_WITH_MODIFY_INTENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_OPEN_MINIVERSION_WITH_MODIFY_INTENT);
#endif
#if defined(ERROR_CANT_CREATE_MORE_STREAM_MINIVERSIONS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_CREATE_MORE_STREAM_MINIVERSIONS);
#endif
#if defined(ERROR_REMOTE_FILE_VERSION_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REMOTE_FILE_VERSION_MISMATCH);
#endif
#if defined(ERROR_HANDLE_NO_LONGER_VALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HANDLE_NO_LONGER_VALID);
#endif
#if defined(ERROR_NO_TXF_METADATA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_TXF_METADATA);
#endif
#if defined(ERROR_LOG_CORRUPTION_DETECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_CORRUPTION_DETECTED);
#endif
#if defined(ERROR_CANT_RECOVER_WITH_HANDLE_OPEN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_RECOVER_WITH_HANDLE_OPEN);
#endif
#if defined(ERROR_RM_DISCONNECTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RM_DISCONNECTED);
#endif
#if defined(ERROR_ENLISTMENT_NOT_SUPERIOR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ENLISTMENT_NOT_SUPERIOR);
#endif
#if defined(ERROR_RECOVERY_NOT_NEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RECOVERY_NOT_NEEDED);
#endif
#if defined(ERROR_RM_ALREADY_STARTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RM_ALREADY_STARTED);
#endif
#if defined(ERROR_FILE_IDENTITY_NOT_PERSISTENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FILE_IDENTITY_NOT_PERSISTENT);
#endif
#if defined(ERROR_CANT_BREAK_TRANSACTIONAL_DEPENDENCY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_BREAK_TRANSACTIONAL_DEPENDENCY);
#endif
#if defined(ERROR_CANT_CROSS_RM_BOUNDARY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANT_CROSS_RM_BOUNDARY);
#endif
#if defined(ERROR_TXF_DIR_NOT_EMPTY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TXF_DIR_NOT_EMPTY);
#endif
#if defined(ERROR_INDOUBT_TRANSACTIONS_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INDOUBT_TRANSACTIONS_EXIST);
#endif
#if defined(ERROR_TM_VOLATILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TM_VOLATILE);
#endif
#if defined(ERROR_ROLLBACK_TIMER_EXPIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ROLLBACK_TIMER_EXPIRED);
#endif
#if defined(ERROR_TXF_ATTRIBUTE_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TXF_ATTRIBUTE_CORRUPT);
#endif
#if defined(ERROR_EFS_NOT_ALLOWED_IN_TRANSACTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EFS_NOT_ALLOWED_IN_TRANSACTION);
#endif
#if defined(ERROR_TRANSACTIONAL_OPEN_NOT_ALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTIONAL_OPEN_NOT_ALLOWED);
#endif
#if defined(ERROR_LOG_GROWTH_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_LOG_GROWTH_FAILED);
#endif
#if defined(ERROR_TRANSACTED_MAPPING_UNSUPPORTED_REMOTE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTED_MAPPING_UNSUPPORTED_REMOTE);
#endif
#if defined(ERROR_TXF_METADATA_ALREADY_PRESENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TXF_METADATA_ALREADY_PRESENT);
#endif
#if defined(ERROR_TRANSACTION_SCOPE_CALLBACKS_NOT_SET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_SCOPE_CALLBACKS_NOT_SET);
#endif
#if defined(ERROR_TRANSACTION_REQUIRED_PROMOTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_REQUIRED_PROMOTION);
#endif
#if defined(ERROR_CANNOT_EXECUTE_FILE_IN_TRANSACTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_EXECUTE_FILE_IN_TRANSACTION);
#endif
#if defined(ERROR_TRANSACTIONS_NOT_FROZEN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTIONS_NOT_FROZEN);
#endif
#if defined(ERROR_TRANSACTION_FREEZE_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_FREEZE_IN_PROGRESS);
#endif
#if defined(ERROR_NOT_SNAPSHOT_VOLUME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_SNAPSHOT_VOLUME);
#endif
#if defined(ERROR_NO_SAVEPOINT_WITH_OPEN_FILES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SAVEPOINT_WITH_OPEN_FILES);
#endif
#if defined(ERROR_DATA_LOST_REPAIR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DATA_LOST_REPAIR);
#endif
#if defined(ERROR_SPARSE_NOT_ALLOWED_IN_TRANSACTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SPARSE_NOT_ALLOWED_IN_TRANSACTION);
#endif
#if defined(ERROR_TM_IDENTITY_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TM_IDENTITY_MISMATCH);
#endif
#if defined(ERROR_FLOATED_SECTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_FLOATED_SECTION);
#endif
#if defined(ERROR_CANNOT_ACCEPT_TRANSACTED_WORK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_ACCEPT_TRANSACTED_WORK);
#endif
#if defined(ERROR_CANNOT_ABORT_TRANSACTIONS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_ABORT_TRANSACTIONS);
#endif
#if defined(ERROR_BAD_CLUSTERS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_BAD_CLUSTERS);
#endif
#if defined(ERROR_COMPRESSION_NOT_ALLOWED_IN_TRANSACTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_COMPRESSION_NOT_ALLOWED_IN_TRANSACTION);
#endif
#if defined(ERROR_VOLUME_DIRTY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_VOLUME_DIRTY);
#endif
#if defined(ERROR_NO_LINK_TRACKING_IN_TRANSACTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_LINK_TRACKING_IN_TRANSACTION);
#endif
#if defined(ERROR_OPERATION_NOT_SUPPORTED_IN_TRANSACTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OPERATION_NOT_SUPPORTED_IN_TRANSACTION);
#endif
#if defined(ERROR_EXPIRED_HANDLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EXPIRED_HANDLE);
#endif
#if defined(ERROR_TRANSACTION_NOT_ENLISTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TRANSACTION_NOT_ENLISTED);
#endif
#if defined(ERROR_CTX_WINSTATION_NAME_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_WINSTATION_NAME_INVALID);
#endif
#if defined(ERROR_CTX_INVALID_PD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_INVALID_PD);
#endif
#if defined(ERROR_CTX_PD_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_PD_NOT_FOUND);
#endif
#if defined(ERROR_CTX_WD_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_WD_NOT_FOUND);
#endif
#if defined(ERROR_CTX_CANNOT_MAKE_EVENTLOG_ENTRY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_CANNOT_MAKE_EVENTLOG_ENTRY);
#endif
#if defined(ERROR_CTX_SERVICE_NAME_COLLISION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_SERVICE_NAME_COLLISION);
#endif
#if defined(ERROR_CTX_CLOSE_PENDING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_CLOSE_PENDING);
#endif
#if defined(ERROR_CTX_NO_OUTBUF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_NO_OUTBUF);
#endif
#if defined(ERROR_CTX_MODEM_INF_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_MODEM_INF_NOT_FOUND);
#endif
#if defined(ERROR_CTX_INVALID_MODEMNAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_INVALID_MODEMNAME);
#endif
#if defined(ERROR_CTX_MODEM_RESPONSE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_MODEM_RESPONSE_ERROR);
#endif
#if defined(ERROR_CTX_MODEM_RESPONSE_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_MODEM_RESPONSE_TIMEOUT);
#endif
#if defined(ERROR_CTX_MODEM_RESPONSE_NO_CARRIER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_MODEM_RESPONSE_NO_CARRIER);
#endif
#if defined(ERROR_CTX_MODEM_RESPONSE_NO_DIALTONE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_MODEM_RESPONSE_NO_DIALTONE);
#endif
#if defined(ERROR_CTX_MODEM_RESPONSE_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_MODEM_RESPONSE_BUSY);
#endif
#if defined(ERROR_CTX_MODEM_RESPONSE_VOICE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_MODEM_RESPONSE_VOICE);
#endif
#if defined(ERROR_CTX_TD_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_TD_ERROR);
#endif
#if defined(ERROR_CTX_WINSTATION_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_WINSTATION_NOT_FOUND);
#endif
#if defined(ERROR_CTX_WINSTATION_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_WINSTATION_ALREADY_EXISTS);
#endif
#if defined(ERROR_CTX_WINSTATION_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_WINSTATION_BUSY);
#endif
#if defined(ERROR_CTX_BAD_VIDEO_MODE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_BAD_VIDEO_MODE);
#endif
#if defined(ERROR_CTX_GRAPHICS_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_GRAPHICS_INVALID);
#endif
#if defined(ERROR_CTX_LOGON_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_LOGON_DISABLED);
#endif
#if defined(ERROR_CTX_NOT_CONSOLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_NOT_CONSOLE);
#endif
#if defined(ERROR_CTX_CLIENT_QUERY_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_CLIENT_QUERY_TIMEOUT);
#endif
#if defined(ERROR_CTX_CONSOLE_DISCONNECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_CONSOLE_DISCONNECT);
#endif
#if defined(ERROR_CTX_CONSOLE_CONNECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_CONSOLE_CONNECT);
#endif
#if defined(ERROR_CTX_SHADOW_DENIED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_SHADOW_DENIED);
#endif
#if defined(ERROR_CTX_WINSTATION_ACCESS_DENIED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_WINSTATION_ACCESS_DENIED);
#endif
#if defined(ERROR_CTX_INVALID_WD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_INVALID_WD);
#endif
#if defined(ERROR_CTX_SHADOW_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_SHADOW_INVALID);
#endif
#if defined(ERROR_CTX_SHADOW_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_SHADOW_DISABLED);
#endif
#if defined(ERROR_CTX_CLIENT_LICENSE_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_CLIENT_LICENSE_IN_USE);
#endif
#if defined(ERROR_CTX_CLIENT_LICENSE_NOT_SET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_CLIENT_LICENSE_NOT_SET);
#endif
#if defined(ERROR_CTX_LICENSE_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_LICENSE_NOT_AVAILABLE);
#endif
#if defined(ERROR_CTX_LICENSE_CLIENT_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_LICENSE_CLIENT_INVALID);
#endif
#if defined(ERROR_CTX_LICENSE_EXPIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_LICENSE_EXPIRED);
#endif
#if defined(ERROR_CTX_SHADOW_NOT_RUNNING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_SHADOW_NOT_RUNNING);
#endif
#if defined(ERROR_CTX_SHADOW_ENDED_BY_MODE_CHANGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_SHADOW_ENDED_BY_MODE_CHANGE);
#endif
#if defined(ERROR_ACTIVATION_COUNT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ACTIVATION_COUNT_EXCEEDED);
#endif
#if defined(ERROR_CTX_WINSTATIONS_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_WINSTATIONS_DISABLED);
#endif
#if defined(ERROR_CTX_ENCRYPTION_LEVEL_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_ENCRYPTION_LEVEL_REQUIRED);
#endif
#if defined(ERROR_CTX_SESSION_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_SESSION_IN_USE);
#endif
#if defined(ERROR_CTX_NO_FORCE_LOGOFF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_NO_FORCE_LOGOFF);
#endif
#if defined(ERROR_CTX_ACCOUNT_RESTRICTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_ACCOUNT_RESTRICTION);
#endif
#if defined(ERROR_RDP_PROTOCOL_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RDP_PROTOCOL_ERROR);
#endif
#if defined(ERROR_CTX_CDM_CONNECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_CDM_CONNECT);
#endif
#if defined(ERROR_CTX_CDM_DISCONNECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_CDM_DISCONNECT);
#endif
#if defined(ERROR_CTX_SECURITY_LAYER_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CTX_SECURITY_LAYER_ERROR);
#endif
#if defined(ERROR_TS_INCOMPATIBLE_SESSIONS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TS_INCOMPATIBLE_SESSIONS);
#endif
#if defined(ERROR_TS_VIDEO_SUBSYSTEM_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_TS_VIDEO_SUBSYSTEM_ERROR);
#endif
#if defined(FRS_ERR_INVALID_API_SEQUENCE)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_INVALID_API_SEQUENCE);
#endif
#if defined(FRS_ERR_STARTING_SERVICE)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_STARTING_SERVICE);
#endif
#if defined(FRS_ERR_STOPPING_SERVICE)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_STOPPING_SERVICE);
#endif
#if defined(FRS_ERR_INTERNAL_API)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_INTERNAL_API);
#endif
#if defined(FRS_ERR_INTERNAL)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_INTERNAL);
#endif
#if defined(FRS_ERR_SERVICE_COMM)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_SERVICE_COMM);
#endif
#if defined(FRS_ERR_INSUFFICIENT_PRIV)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_INSUFFICIENT_PRIV);
#endif
#if defined(FRS_ERR_AUTHENTICATION)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_AUTHENTICATION);
#endif
#if defined(FRS_ERR_PARENT_INSUFFICIENT_PRIV)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_PARENT_INSUFFICIENT_PRIV);
#endif
#if defined(FRS_ERR_PARENT_AUTHENTICATION)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_PARENT_AUTHENTICATION);
#endif
#if defined(FRS_ERR_CHILD_TO_PARENT_COMM)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_CHILD_TO_PARENT_COMM);
#endif
#if defined(FRS_ERR_PARENT_TO_CHILD_COMM)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_PARENT_TO_CHILD_COMM);
#endif
#if defined(FRS_ERR_SYSVOL_POPULATE)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_SYSVOL_POPULATE);
#endif
#if defined(FRS_ERR_SYSVOL_POPULATE_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_SYSVOL_POPULATE_TIMEOUT);
#endif
#if defined(FRS_ERR_SYSVOL_IS_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_SYSVOL_IS_BUSY);
#endif
#if defined(FRS_ERR_SYSVOL_DEMOTE)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_SYSVOL_DEMOTE);
#endif
#if defined(FRS_ERR_INVALID_SERVICE_PARAMETER)
    BOOST_WINAPI_TEST_CONSTANT(FRS_ERR_INVALID_SERVICE_PARAMETER);
#endif
#if defined(DS_S_SUCCESS)
    BOOST_WINAPI_TEST_CONSTANT(DS_S_SUCCESS);
#endif
#if defined(ERROR_DS_NOT_INSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NOT_INSTALLED);
#endif
#if defined(ERROR_DS_MEMBERSHIP_EVALUATED_LOCALLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MEMBERSHIP_EVALUATED_LOCALLY);
#endif
#if defined(ERROR_DS_NO_ATTRIBUTE_OR_VALUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_ATTRIBUTE_OR_VALUE);
#endif
#if defined(ERROR_DS_INVALID_ATTRIBUTE_SYNTAX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INVALID_ATTRIBUTE_SYNTAX);
#endif
#if defined(ERROR_DS_ATTRIBUTE_TYPE_UNDEFINED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ATTRIBUTE_TYPE_UNDEFINED);
#endif
#if defined(ERROR_DS_ATTRIBUTE_OR_VALUE_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ATTRIBUTE_OR_VALUE_EXISTS);
#endif
#if defined(ERROR_DS_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_BUSY);
#endif
#if defined(ERROR_DS_UNAVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_UNAVAILABLE);
#endif
#if defined(ERROR_DS_NO_RIDS_ALLOCATED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_RIDS_ALLOCATED);
#endif
#if defined(ERROR_DS_NO_MORE_RIDS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_MORE_RIDS);
#endif
#if defined(ERROR_DS_INCORRECT_ROLE_OWNER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INCORRECT_ROLE_OWNER);
#endif
#if defined(ERROR_DS_RIDMGR_INIT_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_RIDMGR_INIT_ERROR);
#endif
#if defined(ERROR_DS_OBJ_CLASS_VIOLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OBJ_CLASS_VIOLATION);
#endif
#if defined(ERROR_DS_CANT_ON_NON_LEAF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_ON_NON_LEAF);
#endif
#if defined(ERROR_DS_CANT_ON_RDN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_ON_RDN);
#endif
#if defined(ERROR_DS_CANT_MOD_OBJ_CLASS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_MOD_OBJ_CLASS);
#endif
#if defined(ERROR_DS_CROSS_DOM_MOVE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CROSS_DOM_MOVE_ERROR);
#endif
#if defined(ERROR_DS_GC_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_GC_NOT_AVAILABLE);
#endif
#if defined(ERROR_SHARED_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SHARED_POLICY);
#endif
#if defined(ERROR_POLICY_OBJECT_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_POLICY_OBJECT_NOT_FOUND);
#endif
#if defined(ERROR_POLICY_ONLY_IN_DS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_POLICY_ONLY_IN_DS);
#endif
#if defined(ERROR_PROMOTION_ACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROMOTION_ACTIVE);
#endif
#if defined(ERROR_NO_PROMOTION_ACTIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_PROMOTION_ACTIVE);
#endif
#if defined(ERROR_DS_OPERATIONS_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OPERATIONS_ERROR);
#endif
#if defined(ERROR_DS_PROTOCOL_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_PROTOCOL_ERROR);
#endif
#if defined(ERROR_DS_TIMELIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_TIMELIMIT_EXCEEDED);
#endif
#if defined(ERROR_DS_SIZELIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SIZELIMIT_EXCEEDED);
#endif
#if defined(ERROR_DS_ADMIN_LIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ADMIN_LIMIT_EXCEEDED);
#endif
#if defined(ERROR_DS_COMPARE_FALSE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_COMPARE_FALSE);
#endif
#if defined(ERROR_DS_COMPARE_TRUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_COMPARE_TRUE);
#endif
#if defined(ERROR_DS_AUTH_METHOD_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_AUTH_METHOD_NOT_SUPPORTED);
#endif
#if defined(ERROR_DS_STRONG_AUTH_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_STRONG_AUTH_REQUIRED);
#endif
#if defined(ERROR_DS_INAPPROPRIATE_AUTH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INAPPROPRIATE_AUTH);
#endif
#if defined(ERROR_DS_AUTH_UNKNOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_AUTH_UNKNOWN);
#endif
#if defined(ERROR_DS_REFERRAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_REFERRAL);
#endif
#if defined(ERROR_DS_UNAVAILABLE_CRIT_EXTENSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_UNAVAILABLE_CRIT_EXTENSION);
#endif
#if defined(ERROR_DS_CONFIDENTIALITY_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CONFIDENTIALITY_REQUIRED);
#endif
#if defined(ERROR_DS_INAPPROPRIATE_MATCHING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INAPPROPRIATE_MATCHING);
#endif
#if defined(ERROR_DS_CONSTRAINT_VIOLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CONSTRAINT_VIOLATION);
#endif
#if defined(ERROR_DS_NO_SUCH_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_SUCH_OBJECT);
#endif
#if defined(ERROR_DS_ALIAS_PROBLEM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ALIAS_PROBLEM);
#endif
#if defined(ERROR_DS_INVALID_DN_SYNTAX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INVALID_DN_SYNTAX);
#endif
#if defined(ERROR_DS_IS_LEAF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_IS_LEAF);
#endif
#if defined(ERROR_DS_ALIAS_DEREF_PROBLEM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ALIAS_DEREF_PROBLEM);
#endif
#if defined(ERROR_DS_UNWILLING_TO_PERFORM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_UNWILLING_TO_PERFORM);
#endif
#if defined(ERROR_DS_LOOP_DETECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_LOOP_DETECT);
#endif
#if defined(ERROR_DS_NAMING_VIOLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAMING_VIOLATION);
#endif
#if defined(ERROR_DS_OBJECT_RESULTS_TOO_LARGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OBJECT_RESULTS_TOO_LARGE);
#endif
#if defined(ERROR_DS_AFFECTS_MULTIPLE_DSAS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_AFFECTS_MULTIPLE_DSAS);
#endif
#if defined(ERROR_DS_SERVER_DOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SERVER_DOWN);
#endif
#if defined(ERROR_DS_LOCAL_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_LOCAL_ERROR);
#endif
#if defined(ERROR_DS_ENCODING_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ENCODING_ERROR);
#endif
#if defined(ERROR_DS_DECODING_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DECODING_ERROR);
#endif
#if defined(ERROR_DS_FILTER_UNKNOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_FILTER_UNKNOWN);
#endif
#if defined(ERROR_DS_PARAM_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_PARAM_ERROR);
#endif
#if defined(ERROR_DS_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NOT_SUPPORTED);
#endif
#if defined(ERROR_DS_NO_RESULTS_RETURNED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_RESULTS_RETURNED);
#endif
#if defined(ERROR_DS_CONTROL_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CONTROL_NOT_FOUND);
#endif
#if defined(ERROR_DS_CLIENT_LOOP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CLIENT_LOOP);
#endif
#if defined(ERROR_DS_REFERRAL_LIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_REFERRAL_LIMIT_EXCEEDED);
#endif
#if defined(ERROR_DS_SORT_CONTROL_MISSING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SORT_CONTROL_MISSING);
#endif
#if defined(ERROR_DS_OFFSET_RANGE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OFFSET_RANGE_ERROR);
#endif
#if defined(ERROR_DS_RIDMGR_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_RIDMGR_DISABLED);
#endif
#if defined(ERROR_DS_ROOT_MUST_BE_NC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ROOT_MUST_BE_NC);
#endif
#if defined(ERROR_DS_ADD_REPLICA_INHIBITED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ADD_REPLICA_INHIBITED);
#endif
#if defined(ERROR_DS_ATT_NOT_DEF_IN_SCHEMA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ATT_NOT_DEF_IN_SCHEMA);
#endif
#if defined(ERROR_DS_MAX_OBJ_SIZE_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MAX_OBJ_SIZE_EXCEEDED);
#endif
#if defined(ERROR_DS_OBJ_STRING_NAME_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OBJ_STRING_NAME_EXISTS);
#endif
#if defined(ERROR_DS_NO_RDN_DEFINED_IN_SCHEMA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_RDN_DEFINED_IN_SCHEMA);
#endif
#if defined(ERROR_DS_RDN_DOESNT_MATCH_SCHEMA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_RDN_DOESNT_MATCH_SCHEMA);
#endif
#if defined(ERROR_DS_NO_REQUESTED_ATTS_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_REQUESTED_ATTS_FOUND);
#endif
#if defined(ERROR_DS_USER_BUFFER_TO_SMALL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_USER_BUFFER_TO_SMALL);
#endif
#if defined(ERROR_DS_ATT_IS_NOT_ON_OBJ)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ATT_IS_NOT_ON_OBJ);
#endif
#if defined(ERROR_DS_ILLEGAL_MOD_OPERATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ILLEGAL_MOD_OPERATION);
#endif
#if defined(ERROR_DS_OBJ_TOO_LARGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OBJ_TOO_LARGE);
#endif
#if defined(ERROR_DS_BAD_INSTANCE_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_BAD_INSTANCE_TYPE);
#endif
#if defined(ERROR_DS_MASTERDSA_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MASTERDSA_REQUIRED);
#endif
#if defined(ERROR_DS_OBJECT_CLASS_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OBJECT_CLASS_REQUIRED);
#endif
#if defined(ERROR_DS_MISSING_REQUIRED_ATT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MISSING_REQUIRED_ATT);
#endif
#if defined(ERROR_DS_ATT_NOT_DEF_FOR_CLASS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ATT_NOT_DEF_FOR_CLASS);
#endif
#if defined(ERROR_DS_ATT_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ATT_ALREADY_EXISTS);
#endif
#if defined(ERROR_DS_CANT_ADD_ATT_VALUES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_ADD_ATT_VALUES);
#endif
#if defined(ERROR_DS_SINGLE_VALUE_CONSTRAINT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SINGLE_VALUE_CONSTRAINT);
#endif
#if defined(ERROR_DS_RANGE_CONSTRAINT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_RANGE_CONSTRAINT);
#endif
#if defined(ERROR_DS_ATT_VAL_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ATT_VAL_ALREADY_EXISTS);
#endif
#if defined(ERROR_DS_CANT_REM_MISSING_ATT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_REM_MISSING_ATT);
#endif
#if defined(ERROR_DS_CANT_REM_MISSING_ATT_VAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_REM_MISSING_ATT_VAL);
#endif
#if defined(ERROR_DS_ROOT_CANT_BE_SUBREF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ROOT_CANT_BE_SUBREF);
#endif
#if defined(ERROR_DS_NO_CHAINING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_CHAINING);
#endif
#if defined(ERROR_DS_NO_CHAINED_EVAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_CHAINED_EVAL);
#endif
#if defined(ERROR_DS_NO_PARENT_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_PARENT_OBJECT);
#endif
#if defined(ERROR_DS_PARENT_IS_AN_ALIAS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_PARENT_IS_AN_ALIAS);
#endif
#if defined(ERROR_DS_CANT_MIX_MASTER_AND_REPS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_MIX_MASTER_AND_REPS);
#endif
#if defined(ERROR_DS_CHILDREN_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CHILDREN_EXIST);
#endif
#if defined(ERROR_DS_OBJ_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OBJ_NOT_FOUND);
#endif
#if defined(ERROR_DS_ALIASED_OBJ_MISSING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ALIASED_OBJ_MISSING);
#endif
#if defined(ERROR_DS_BAD_NAME_SYNTAX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_BAD_NAME_SYNTAX);
#endif
#if defined(ERROR_DS_ALIAS_POINTS_TO_ALIAS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ALIAS_POINTS_TO_ALIAS);
#endif
#if defined(ERROR_DS_CANT_DEREF_ALIAS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_DEREF_ALIAS);
#endif
#if defined(ERROR_DS_OUT_OF_SCOPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OUT_OF_SCOPE);
#endif
#if defined(ERROR_DS_OBJECT_BEING_REMOVED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OBJECT_BEING_REMOVED);
#endif
#if defined(ERROR_DS_CANT_DELETE_DSA_OBJ)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_DELETE_DSA_OBJ);
#endif
#if defined(ERROR_DS_GENERIC_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_GENERIC_ERROR);
#endif
#if defined(ERROR_DS_DSA_MUST_BE_INT_MASTER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DSA_MUST_BE_INT_MASTER);
#endif
#if defined(ERROR_DS_CLASS_NOT_DSA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CLASS_NOT_DSA);
#endif
#if defined(ERROR_DS_INSUFF_ACCESS_RIGHTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INSUFF_ACCESS_RIGHTS);
#endif
#if defined(ERROR_DS_ILLEGAL_SUPERIOR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ILLEGAL_SUPERIOR);
#endif
#if defined(ERROR_DS_ATTRIBUTE_OWNED_BY_SAM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ATTRIBUTE_OWNED_BY_SAM);
#endif
#if defined(ERROR_DS_NAME_TOO_MANY_PARTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_TOO_MANY_PARTS);
#endif
#if defined(ERROR_DS_NAME_TOO_LONG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_TOO_LONG);
#endif
#if defined(ERROR_DS_NAME_VALUE_TOO_LONG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_VALUE_TOO_LONG);
#endif
#if defined(ERROR_DS_NAME_UNPARSEABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_UNPARSEABLE);
#endif
#if defined(ERROR_DS_NAME_TYPE_UNKNOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_TYPE_UNKNOWN);
#endif
#if defined(ERROR_DS_NOT_AN_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NOT_AN_OBJECT);
#endif
#if defined(ERROR_DS_SEC_DESC_TOO_SHORT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SEC_DESC_TOO_SHORT);
#endif
#if defined(ERROR_DS_SEC_DESC_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SEC_DESC_INVALID);
#endif
#if defined(ERROR_DS_NO_DELETED_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_DELETED_NAME);
#endif
#if defined(ERROR_DS_SUBREF_MUST_HAVE_PARENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SUBREF_MUST_HAVE_PARENT);
#endif
#if defined(ERROR_DS_NCNAME_MUST_BE_NC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NCNAME_MUST_BE_NC);
#endif
#if defined(ERROR_DS_CANT_ADD_SYSTEM_ONLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_ADD_SYSTEM_ONLY);
#endif
#if defined(ERROR_DS_CLASS_MUST_BE_CONCRETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CLASS_MUST_BE_CONCRETE);
#endif
#if defined(ERROR_DS_INVALID_DMD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INVALID_DMD);
#endif
#if defined(ERROR_DS_OBJ_GUID_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OBJ_GUID_EXISTS);
#endif
#if defined(ERROR_DS_NOT_ON_BACKLINK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NOT_ON_BACKLINK);
#endif
#if defined(ERROR_DS_NO_CROSSREF_FOR_NC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_CROSSREF_FOR_NC);
#endif
#if defined(ERROR_DS_SHUTTING_DOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SHUTTING_DOWN);
#endif
#if defined(ERROR_DS_UNKNOWN_OPERATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_UNKNOWN_OPERATION);
#endif
#if defined(ERROR_DS_INVALID_ROLE_OWNER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INVALID_ROLE_OWNER);
#endif
#if defined(ERROR_DS_COULDNT_CONTACT_FSMO)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_COULDNT_CONTACT_FSMO);
#endif
#if defined(ERROR_DS_CROSS_NC_DN_RENAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CROSS_NC_DN_RENAME);
#endif
#if defined(ERROR_DS_CANT_MOD_SYSTEM_ONLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_MOD_SYSTEM_ONLY);
#endif
#if defined(ERROR_DS_REPLICATOR_ONLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_REPLICATOR_ONLY);
#endif
#if defined(ERROR_DS_OBJ_CLASS_NOT_DEFINED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OBJ_CLASS_NOT_DEFINED);
#endif
#if defined(ERROR_DS_OBJ_CLASS_NOT_SUBCLASS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OBJ_CLASS_NOT_SUBCLASS);
#endif
#if defined(ERROR_DS_NAME_REFERENCE_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_REFERENCE_INVALID);
#endif
#if defined(ERROR_DS_CROSS_REF_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CROSS_REF_EXISTS);
#endif
#if defined(ERROR_DS_CANT_DEL_MASTER_CROSSREF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_DEL_MASTER_CROSSREF);
#endif
#if defined(ERROR_DS_SUBTREE_NOTIFY_NOT_NC_HEAD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SUBTREE_NOTIFY_NOT_NC_HEAD);
#endif
#if defined(ERROR_DS_NOTIFY_FILTER_TOO_COMPLEX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NOTIFY_FILTER_TOO_COMPLEX);
#endif
#if defined(ERROR_DS_DUP_RDN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DUP_RDN);
#endif
#if defined(ERROR_DS_DUP_OID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DUP_OID);
#endif
#if defined(ERROR_DS_DUP_MAPI_ID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DUP_MAPI_ID);
#endif
#if defined(ERROR_DS_DUP_SCHEMA_ID_GUID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DUP_SCHEMA_ID_GUID);
#endif
#if defined(ERROR_DS_DUP_LDAP_DISPLAY_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DUP_LDAP_DISPLAY_NAME);
#endif
#if defined(ERROR_DS_SEMANTIC_ATT_TEST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SEMANTIC_ATT_TEST);
#endif
#if defined(ERROR_DS_SYNTAX_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SYNTAX_MISMATCH);
#endif
#if defined(ERROR_DS_EXISTS_IN_MUST_HAVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_EXISTS_IN_MUST_HAVE);
#endif
#if defined(ERROR_DS_EXISTS_IN_MAY_HAVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_EXISTS_IN_MAY_HAVE);
#endif
#if defined(ERROR_DS_NONEXISTENT_MAY_HAVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NONEXISTENT_MAY_HAVE);
#endif
#if defined(ERROR_DS_NONEXISTENT_MUST_HAVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NONEXISTENT_MUST_HAVE);
#endif
#if defined(ERROR_DS_AUX_CLS_TEST_FAIL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_AUX_CLS_TEST_FAIL);
#endif
#if defined(ERROR_DS_NONEXISTENT_POSS_SUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NONEXISTENT_POSS_SUP);
#endif
#if defined(ERROR_DS_SUB_CLS_TEST_FAIL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SUB_CLS_TEST_FAIL);
#endif
#if defined(ERROR_DS_BAD_RDN_ATT_ID_SYNTAX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_BAD_RDN_ATT_ID_SYNTAX);
#endif
#if defined(ERROR_DS_EXISTS_IN_AUX_CLS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_EXISTS_IN_AUX_CLS);
#endif
#if defined(ERROR_DS_EXISTS_IN_SUB_CLS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_EXISTS_IN_SUB_CLS);
#endif
#if defined(ERROR_DS_EXISTS_IN_POSS_SUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_EXISTS_IN_POSS_SUP);
#endif
#if defined(ERROR_DS_RECALCSCHEMA_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_RECALCSCHEMA_FAILED);
#endif
#if defined(ERROR_DS_TREE_DELETE_NOT_FINISHED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_TREE_DELETE_NOT_FINISHED);
#endif
#if defined(ERROR_DS_CANT_DELETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_DELETE);
#endif
#if defined(ERROR_DS_ATT_SCHEMA_REQ_ID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ATT_SCHEMA_REQ_ID);
#endif
#if defined(ERROR_DS_BAD_ATT_SCHEMA_SYNTAX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_BAD_ATT_SCHEMA_SYNTAX);
#endif
#if defined(ERROR_DS_CANT_CACHE_ATT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_CACHE_ATT);
#endif
#if defined(ERROR_DS_CANT_CACHE_CLASS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_CACHE_CLASS);
#endif
#if defined(ERROR_DS_CANT_REMOVE_ATT_CACHE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_REMOVE_ATT_CACHE);
#endif
#if defined(ERROR_DS_CANT_REMOVE_CLASS_CACHE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_REMOVE_CLASS_CACHE);
#endif
#if defined(ERROR_DS_CANT_RETRIEVE_DN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_RETRIEVE_DN);
#endif
#if defined(ERROR_DS_MISSING_SUPREF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MISSING_SUPREF);
#endif
#if defined(ERROR_DS_CANT_RETRIEVE_INSTANCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_RETRIEVE_INSTANCE);
#endif
#if defined(ERROR_DS_CODE_INCONSISTENCY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CODE_INCONSISTENCY);
#endif
#if defined(ERROR_DS_DATABASE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DATABASE_ERROR);
#endif
#if defined(ERROR_DS_GOVERNSID_MISSING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_GOVERNSID_MISSING);
#endif
#if defined(ERROR_DS_MISSING_EXPECTED_ATT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MISSING_EXPECTED_ATT);
#endif
#if defined(ERROR_DS_NCNAME_MISSING_CR_REF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NCNAME_MISSING_CR_REF);
#endif
#if defined(ERROR_DS_SECURITY_CHECKING_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SECURITY_CHECKING_ERROR);
#endif
#if defined(ERROR_DS_SCHEMA_NOT_LOADED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SCHEMA_NOT_LOADED);
#endif
#if defined(ERROR_DS_SCHEMA_ALLOC_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SCHEMA_ALLOC_FAILED);
#endif
#if defined(ERROR_DS_ATT_SCHEMA_REQ_SYNTAX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ATT_SCHEMA_REQ_SYNTAX);
#endif
#if defined(ERROR_DS_GCVERIFY_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_GCVERIFY_ERROR);
#endif
#if defined(ERROR_DS_DRA_SCHEMA_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_SCHEMA_MISMATCH);
#endif
#if defined(ERROR_DS_CANT_FIND_DSA_OBJ)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_FIND_DSA_OBJ);
#endif
#if defined(ERROR_DS_CANT_FIND_EXPECTED_NC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_FIND_EXPECTED_NC);
#endif
#if defined(ERROR_DS_CANT_FIND_NC_IN_CACHE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_FIND_NC_IN_CACHE);
#endif
#if defined(ERROR_DS_CANT_RETRIEVE_CHILD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_RETRIEVE_CHILD);
#endif
#if defined(ERROR_DS_SECURITY_ILLEGAL_MODIFY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SECURITY_ILLEGAL_MODIFY);
#endif
#if defined(ERROR_DS_CANT_REPLACE_HIDDEN_REC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_REPLACE_HIDDEN_REC);
#endif
#if defined(ERROR_DS_BAD_HIERARCHY_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_BAD_HIERARCHY_FILE);
#endif
#if defined(ERROR_DS_BUILD_HIERARCHY_TABLE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_BUILD_HIERARCHY_TABLE_FAILED);
#endif
#if defined(ERROR_DS_CONFIG_PARAM_MISSING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CONFIG_PARAM_MISSING);
#endif
#if defined(ERROR_DS_COUNTING_AB_INDICES_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_COUNTING_AB_INDICES_FAILED);
#endif
#if defined(ERROR_DS_HIERARCHY_TABLE_MALLOC_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_HIERARCHY_TABLE_MALLOC_FAILED);
#endif
#if defined(ERROR_DS_INTERNAL_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INTERNAL_FAILURE);
#endif
#if defined(ERROR_DS_UNKNOWN_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_UNKNOWN_ERROR);
#endif
#if defined(ERROR_DS_ROOT_REQUIRES_CLASS_TOP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ROOT_REQUIRES_CLASS_TOP);
#endif
#if defined(ERROR_DS_REFUSING_FSMO_ROLES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_REFUSING_FSMO_ROLES);
#endif
#if defined(ERROR_DS_MISSING_FSMO_SETTINGS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MISSING_FSMO_SETTINGS);
#endif
#if defined(ERROR_DS_UNABLE_TO_SURRENDER_ROLES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_UNABLE_TO_SURRENDER_ROLES);
#endif
#if defined(ERROR_DS_DRA_GENERIC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_GENERIC);
#endif
#if defined(ERROR_DS_DRA_INVALID_PARAMETER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_INVALID_PARAMETER);
#endif
#if defined(ERROR_DS_DRA_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_BUSY);
#endif
#if defined(ERROR_DS_DRA_BAD_DN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_BAD_DN);
#endif
#if defined(ERROR_DS_DRA_BAD_NC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_BAD_NC);
#endif
#if defined(ERROR_DS_DRA_DN_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_DN_EXISTS);
#endif
#if defined(ERROR_DS_DRA_INTERNAL_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_INTERNAL_ERROR);
#endif
#if defined(ERROR_DS_DRA_INCONSISTENT_DIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_INCONSISTENT_DIT);
#endif
#if defined(ERROR_DS_DRA_CONNECTION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_CONNECTION_FAILED);
#endif
#if defined(ERROR_DS_DRA_BAD_INSTANCE_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_BAD_INSTANCE_TYPE);
#endif
#if defined(ERROR_DS_DRA_OUT_OF_MEM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_OUT_OF_MEM);
#endif
#if defined(ERROR_DS_DRA_MAIL_PROBLEM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_MAIL_PROBLEM);
#endif
#if defined(ERROR_DS_DRA_REF_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_REF_ALREADY_EXISTS);
#endif
#if defined(ERROR_DS_DRA_REF_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_REF_NOT_FOUND);
#endif
#if defined(ERROR_DS_DRA_OBJ_IS_REP_SOURCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_OBJ_IS_REP_SOURCE);
#endif
#if defined(ERROR_DS_DRA_DB_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_DB_ERROR);
#endif
#if defined(ERROR_DS_DRA_NO_REPLICA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_NO_REPLICA);
#endif
#if defined(ERROR_DS_DRA_ACCESS_DENIED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_ACCESS_DENIED);
#endif
#if defined(ERROR_DS_DRA_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_NOT_SUPPORTED);
#endif
#if defined(ERROR_DS_DRA_RPC_CANCELLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_RPC_CANCELLED);
#endif
#if defined(ERROR_DS_DRA_SOURCE_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_SOURCE_DISABLED);
#endif
#if defined(ERROR_DS_DRA_SINK_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_SINK_DISABLED);
#endif
#if defined(ERROR_DS_DRA_NAME_COLLISION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_NAME_COLLISION);
#endif
#if defined(ERROR_DS_DRA_SOURCE_REINSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_SOURCE_REINSTALLED);
#endif
#if defined(ERROR_DS_DRA_MISSING_PARENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_MISSING_PARENT);
#endif
#if defined(ERROR_DS_DRA_PREEMPTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_PREEMPTED);
#endif
#if defined(ERROR_DS_DRA_ABANDON_SYNC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_ABANDON_SYNC);
#endif
#if defined(ERROR_DS_DRA_SHUTDOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_SHUTDOWN);
#endif
#if defined(ERROR_DS_DRA_INCOMPATIBLE_PARTIAL_SET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_INCOMPATIBLE_PARTIAL_SET);
#endif
#if defined(ERROR_DS_DRA_SOURCE_IS_PARTIAL_REPLICA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_SOURCE_IS_PARTIAL_REPLICA);
#endif
#if defined(ERROR_DS_DRA_EXTN_CONNECTION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_EXTN_CONNECTION_FAILED);
#endif
#if defined(ERROR_DS_INSTALL_SCHEMA_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INSTALL_SCHEMA_MISMATCH);
#endif
#if defined(ERROR_DS_DUP_LINK_ID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DUP_LINK_ID);
#endif
#if defined(ERROR_DS_NAME_ERROR_RESOLVING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_ERROR_RESOLVING);
#endif
#if defined(ERROR_DS_NAME_ERROR_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_ERROR_NOT_FOUND);
#endif
#if defined(ERROR_DS_NAME_ERROR_NOT_UNIQUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_ERROR_NOT_UNIQUE);
#endif
#if defined(ERROR_DS_NAME_ERROR_NO_MAPPING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_ERROR_NO_MAPPING);
#endif
#if defined(ERROR_DS_NAME_ERROR_DOMAIN_ONLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_ERROR_DOMAIN_ONLY);
#endif
#if defined(ERROR_DS_NAME_ERROR_NO_SYNTACTICAL_MAPPING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_ERROR_NO_SYNTACTICAL_MAPPING);
#endif
#if defined(ERROR_DS_CONSTRUCTED_ATT_MOD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CONSTRUCTED_ATT_MOD);
#endif
#if defined(ERROR_DS_WRONG_OM_OBJ_CLASS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_WRONG_OM_OBJ_CLASS);
#endif
#if defined(ERROR_DS_DRA_REPL_PENDING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_REPL_PENDING);
#endif
#if defined(ERROR_DS_DS_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DS_REQUIRED);
#endif
#if defined(ERROR_DS_INVALID_LDAP_DISPLAY_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INVALID_LDAP_DISPLAY_NAME);
#endif
#if defined(ERROR_DS_NON_BASE_SEARCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NON_BASE_SEARCH);
#endif
#if defined(ERROR_DS_CANT_RETRIEVE_ATTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_RETRIEVE_ATTS);
#endif
#if defined(ERROR_DS_BACKLINK_WITHOUT_LINK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_BACKLINK_WITHOUT_LINK);
#endif
#if defined(ERROR_DS_EPOCH_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_EPOCH_MISMATCH);
#endif
#if defined(ERROR_DS_SRC_NAME_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SRC_NAME_MISMATCH);
#endif
#if defined(ERROR_DS_SRC_AND_DST_NC_IDENTICAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SRC_AND_DST_NC_IDENTICAL);
#endif
#if defined(ERROR_DS_DST_NC_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DST_NC_MISMATCH);
#endif
#if defined(ERROR_DS_NOT_AUTHORITIVE_FOR_DST_NC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NOT_AUTHORITIVE_FOR_DST_NC);
#endif
#if defined(ERROR_DS_SRC_GUID_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SRC_GUID_MISMATCH);
#endif
#if defined(ERROR_DS_CANT_MOVE_DELETED_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_MOVE_DELETED_OBJECT);
#endif
#if defined(ERROR_DS_PDC_OPERATION_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_PDC_OPERATION_IN_PROGRESS);
#endif
#if defined(ERROR_DS_CROSS_DOMAIN_CLEANUP_REQD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CROSS_DOMAIN_CLEANUP_REQD);
#endif
#if defined(ERROR_DS_ILLEGAL_XDOM_MOVE_OPERATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ILLEGAL_XDOM_MOVE_OPERATION);
#endif
#if defined(ERROR_DS_CANT_WITH_ACCT_GROUP_MEMBERSHPS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_WITH_ACCT_GROUP_MEMBERSHPS);
#endif
#if defined(ERROR_DS_NC_MUST_HAVE_NC_PARENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NC_MUST_HAVE_NC_PARENT);
#endif
#if defined(ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE);
#endif
#if defined(ERROR_DS_DST_DOMAIN_NOT_NATIVE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DST_DOMAIN_NOT_NATIVE);
#endif
#if defined(ERROR_DS_MISSING_INFRASTRUCTURE_CONTAINER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MISSING_INFRASTRUCTURE_CONTAINER);
#endif
#if defined(ERROR_DS_CANT_MOVE_ACCOUNT_GROUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_MOVE_ACCOUNT_GROUP);
#endif
#if defined(ERROR_DS_CANT_MOVE_RESOURCE_GROUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_MOVE_RESOURCE_GROUP);
#endif
#if defined(ERROR_DS_INVALID_SEARCH_FLAG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INVALID_SEARCH_FLAG);
#endif
#if defined(ERROR_DS_NO_TREE_DELETE_ABOVE_NC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_TREE_DELETE_ABOVE_NC);
#endif
#if defined(ERROR_DS_COULDNT_LOCK_TREE_FOR_DELETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_COULDNT_LOCK_TREE_FOR_DELETE);
#endif
#if defined(ERROR_DS_COULDNT_IDENTIFY_OBJECTS_FOR_TREE_DELETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_COULDNT_IDENTIFY_OBJECTS_FOR_TREE_DELETE);
#endif
#if defined(ERROR_DS_SAM_INIT_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SAM_INIT_FAILURE);
#endif
#if defined(ERROR_DS_SENSITIVE_GROUP_VIOLATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SENSITIVE_GROUP_VIOLATION);
#endif
#if defined(ERROR_DS_CANT_MOD_PRIMARYGROUPID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_MOD_PRIMARYGROUPID);
#endif
#if defined(ERROR_DS_ILLEGAL_BASE_SCHEMA_MOD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ILLEGAL_BASE_SCHEMA_MOD);
#endif
#if defined(ERROR_DS_NONSAFE_SCHEMA_CHANGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NONSAFE_SCHEMA_CHANGE);
#endif
#if defined(ERROR_DS_SCHEMA_UPDATE_DISALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SCHEMA_UPDATE_DISALLOWED);
#endif
#if defined(ERROR_DS_CANT_CREATE_UNDER_SCHEMA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_CREATE_UNDER_SCHEMA);
#endif
#if defined(ERROR_DS_INSTALL_NO_SRC_SCH_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INSTALL_NO_SRC_SCH_VERSION);
#endif
#if defined(ERROR_DS_INSTALL_NO_SCH_VERSION_IN_INIFILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INSTALL_NO_SCH_VERSION_IN_INIFILE);
#endif
#if defined(ERROR_DS_INVALID_GROUP_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INVALID_GROUP_TYPE);
#endif
#if defined(ERROR_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN);
#endif
#if defined(ERROR_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN);
#endif
#if defined(ERROR_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER);
#endif
#if defined(ERROR_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER);
#endif
#if defined(ERROR_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER);
#endif
#if defined(ERROR_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER);
#endif
#if defined(ERROR_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER);
#endif
#if defined(ERROR_DS_HAVE_PRIMARY_MEMBERS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_HAVE_PRIMARY_MEMBERS);
#endif
#if defined(ERROR_DS_STRING_SD_CONVERSION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_STRING_SD_CONVERSION_FAILED);
#endif
#if defined(ERROR_DS_NAMING_MASTER_GC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAMING_MASTER_GC);
#endif
#if defined(ERROR_DS_DNS_LOOKUP_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DNS_LOOKUP_FAILURE);
#endif
#if defined(ERROR_DS_COULDNT_UPDATE_SPNS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_COULDNT_UPDATE_SPNS);
#endif
#if defined(ERROR_DS_CANT_RETRIEVE_SD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_RETRIEVE_SD);
#endif
#if defined(ERROR_DS_KEY_NOT_UNIQUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_KEY_NOT_UNIQUE);
#endif
#if defined(ERROR_DS_WRONG_LINKED_ATT_SYNTAX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_WRONG_LINKED_ATT_SYNTAX);
#endif
#if defined(ERROR_DS_SAM_NEED_BOOTKEY_PASSWORD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SAM_NEED_BOOTKEY_PASSWORD);
#endif
#if defined(ERROR_DS_SAM_NEED_BOOTKEY_FLOPPY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SAM_NEED_BOOTKEY_FLOPPY);
#endif
#if defined(ERROR_DS_CANT_START)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_START);
#endif
#if defined(ERROR_DS_INIT_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INIT_FAILURE);
#endif
#if defined(ERROR_DS_NO_PKT_PRIVACY_ON_CONNECTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_PKT_PRIVACY_ON_CONNECTION);
#endif
#if defined(ERROR_DS_SOURCE_DOMAIN_IN_FOREST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SOURCE_DOMAIN_IN_FOREST);
#endif
#if defined(ERROR_DS_DESTINATION_DOMAIN_NOT_IN_FOREST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DESTINATION_DOMAIN_NOT_IN_FOREST);
#endif
#if defined(ERROR_DS_DESTINATION_AUDITING_NOT_ENABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DESTINATION_AUDITING_NOT_ENABLED);
#endif
#if defined(ERROR_DS_CANT_FIND_DC_FOR_SRC_DOMAIN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_FIND_DC_FOR_SRC_DOMAIN);
#endif
#if defined(ERROR_DS_SRC_OBJ_NOT_GROUP_OR_USER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SRC_OBJ_NOT_GROUP_OR_USER);
#endif
#if defined(ERROR_DS_SRC_SID_EXISTS_IN_FOREST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SRC_SID_EXISTS_IN_FOREST);
#endif
#if defined(ERROR_DS_SRC_AND_DST_OBJECT_CLASS_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SRC_AND_DST_OBJECT_CLASS_MISMATCH);
#endif
#if defined(ERROR_SAM_INIT_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SAM_INIT_FAILURE);
#endif
#if defined(ERROR_DS_DRA_SCHEMA_INFO_SHIP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_SCHEMA_INFO_SHIP);
#endif
#if defined(ERROR_DS_DRA_SCHEMA_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_SCHEMA_CONFLICT);
#endif
#if defined(ERROR_DS_DRA_EARLIER_SCHEMA_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_EARLIER_SCHEMA_CONFLICT);
#endif
#if defined(ERROR_DS_DRA_OBJ_NC_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_OBJ_NC_MISMATCH);
#endif
#if defined(ERROR_DS_NC_STILL_HAS_DSAS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NC_STILL_HAS_DSAS);
#endif
#if defined(ERROR_DS_GC_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_GC_REQUIRED);
#endif
#if defined(ERROR_DS_LOCAL_MEMBER_OF_LOCAL_ONLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_LOCAL_MEMBER_OF_LOCAL_ONLY);
#endif
#if defined(ERROR_DS_NO_FPO_IN_UNIVERSAL_GROUPS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_FPO_IN_UNIVERSAL_GROUPS);
#endif
#if defined(ERROR_DS_CANT_ADD_TO_GC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_ADD_TO_GC);
#endif
#if defined(ERROR_DS_NO_CHECKPOINT_WITH_PDC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_CHECKPOINT_WITH_PDC);
#endif
#if defined(ERROR_DS_SOURCE_AUDITING_NOT_ENABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SOURCE_AUDITING_NOT_ENABLED);
#endif
#if defined(ERROR_DS_CANT_CREATE_IN_NONDOMAIN_NC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_CREATE_IN_NONDOMAIN_NC);
#endif
#if defined(ERROR_DS_INVALID_NAME_FOR_SPN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INVALID_NAME_FOR_SPN);
#endif
#if defined(ERROR_DS_FILTER_USES_CONTRUCTED_ATTRS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_FILTER_USES_CONTRUCTED_ATTRS);
#endif
#if defined(ERROR_DS_UNICODEPWD_NOT_IN_QUOTES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_UNICODEPWD_NOT_IN_QUOTES);
#endif
#if defined(ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED);
#endif
#if defined(ERROR_DS_MUST_BE_RUN_ON_DST_DC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MUST_BE_RUN_ON_DST_DC);
#endif
#if defined(ERROR_DS_SRC_DC_MUST_BE_SP4_OR_GREATER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SRC_DC_MUST_BE_SP4_OR_GREATER);
#endif
#if defined(ERROR_DS_CANT_TREE_DELETE_CRITICAL_OBJ)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_TREE_DELETE_CRITICAL_OBJ);
#endif
#if defined(ERROR_DS_INIT_FAILURE_CONSOLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INIT_FAILURE_CONSOLE);
#endif
#if defined(ERROR_DS_SAM_INIT_FAILURE_CONSOLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SAM_INIT_FAILURE_CONSOLE);
#endif
#if defined(ERROR_DS_FOREST_VERSION_TOO_HIGH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_FOREST_VERSION_TOO_HIGH);
#endif
#if defined(ERROR_DS_DOMAIN_VERSION_TOO_HIGH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DOMAIN_VERSION_TOO_HIGH);
#endif
#if defined(ERROR_DS_FOREST_VERSION_TOO_LOW)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_FOREST_VERSION_TOO_LOW);
#endif
#if defined(ERROR_DS_DOMAIN_VERSION_TOO_LOW)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DOMAIN_VERSION_TOO_LOW);
#endif
#if defined(ERROR_DS_INCOMPATIBLE_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INCOMPATIBLE_VERSION);
#endif
#if defined(ERROR_DS_LOW_DSA_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_LOW_DSA_VERSION);
#endif
#if defined(ERROR_DS_NO_BEHAVIOR_VERSION_IN_MIXEDDOMAIN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_BEHAVIOR_VERSION_IN_MIXEDDOMAIN);
#endif
#if defined(ERROR_DS_NOT_SUPPORTED_SORT_ORDER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NOT_SUPPORTED_SORT_ORDER);
#endif
#if defined(ERROR_DS_NAME_NOT_UNIQUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_NOT_UNIQUE);
#endif
#if defined(ERROR_DS_MACHINE_ACCOUNT_CREATED_PRENT4)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MACHINE_ACCOUNT_CREATED_PRENT4);
#endif
#if defined(ERROR_DS_OUT_OF_VERSION_STORE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OUT_OF_VERSION_STORE);
#endif
#if defined(ERROR_DS_INCOMPATIBLE_CONTROLS_USED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INCOMPATIBLE_CONTROLS_USED);
#endif
#if defined(ERROR_DS_NO_REF_DOMAIN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_REF_DOMAIN);
#endif
#if defined(ERROR_DS_RESERVED_LINK_ID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_RESERVED_LINK_ID);
#endif
#if defined(ERROR_DS_LINK_ID_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_LINK_ID_NOT_AVAILABLE);
#endif
#if defined(ERROR_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER);
#endif
#if defined(ERROR_DS_MODIFYDN_DISALLOWED_BY_INSTANCE_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MODIFYDN_DISALLOWED_BY_INSTANCE_TYPE);
#endif
#if defined(ERROR_DS_NO_OBJECT_MOVE_IN_SCHEMA_NC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_OBJECT_MOVE_IN_SCHEMA_NC);
#endif
#if defined(ERROR_DS_MODIFYDN_DISALLOWED_BY_FLAG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MODIFYDN_DISALLOWED_BY_FLAG);
#endif
#if defined(ERROR_DS_MODIFYDN_WRONG_GRANDPARENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MODIFYDN_WRONG_GRANDPARENT);
#endif
#if defined(ERROR_DS_NAME_ERROR_TRUST_REFERRAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NAME_ERROR_TRUST_REFERRAL);
#endif
#if defined(ERROR_NOT_SUPPORTED_ON_STANDARD_SERVER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NOT_SUPPORTED_ON_STANDARD_SERVER);
#endif
#if defined(ERROR_DS_CANT_ACCESS_REMOTE_PART_OF_AD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_ACCESS_REMOTE_PART_OF_AD);
#endif
#if defined(ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE_V2)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE_V2);
#endif
#if defined(ERROR_DS_THREAD_LIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_THREAD_LIMIT_EXCEEDED);
#endif
#if defined(ERROR_DS_NOT_CLOSEST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NOT_CLOSEST);
#endif
#if defined(ERROR_DS_CANT_DERIVE_SPN_WITHOUT_SERVER_REF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_DERIVE_SPN_WITHOUT_SERVER_REF);
#endif
#if defined(ERROR_DS_SINGLE_USER_MODE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SINGLE_USER_MODE_FAILED);
#endif
#if defined(ERROR_DS_NTDSCRIPT_SYNTAX_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NTDSCRIPT_SYNTAX_ERROR);
#endif
#if defined(ERROR_DS_NTDSCRIPT_PROCESS_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NTDSCRIPT_PROCESS_ERROR);
#endif
#if defined(ERROR_DS_DIFFERENT_REPL_EPOCHS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DIFFERENT_REPL_EPOCHS);
#endif
#if defined(ERROR_DS_DRS_EXTENSIONS_CHANGED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRS_EXTENSIONS_CHANGED);
#endif
#if defined(ERROR_DS_REPLICA_SET_CHANGE_NOT_ALLOWED_ON_DISABLED_CR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_REPLICA_SET_CHANGE_NOT_ALLOWED_ON_DISABLED_CR);
#endif
#if defined(ERROR_DS_NO_MSDS_INTID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_MSDS_INTID);
#endif
#if defined(ERROR_DS_DUP_MSDS_INTID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DUP_MSDS_INTID);
#endif
#if defined(ERROR_DS_EXISTS_IN_RDNATTID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_EXISTS_IN_RDNATTID);
#endif
#if defined(ERROR_DS_AUTHORIZATION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_AUTHORIZATION_FAILED);
#endif
#if defined(ERROR_DS_INVALID_SCRIPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INVALID_SCRIPT);
#endif
#if defined(ERROR_DS_REMOTE_CROSSREF_OP_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_REMOTE_CROSSREF_OP_FAILED);
#endif
#if defined(ERROR_DS_CROSS_REF_BUSY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CROSS_REF_BUSY);
#endif
#if defined(ERROR_DS_CANT_DERIVE_SPN_FOR_DELETED_DOMAIN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_DERIVE_SPN_FOR_DELETED_DOMAIN);
#endif
#if defined(ERROR_DS_CANT_DEMOTE_WITH_WRITEABLE_NC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_DEMOTE_WITH_WRITEABLE_NC);
#endif
#if defined(ERROR_DS_DUPLICATE_ID_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DUPLICATE_ID_FOUND);
#endif
#if defined(ERROR_DS_INSUFFICIENT_ATTR_TO_CREATE_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INSUFFICIENT_ATTR_TO_CREATE_OBJECT);
#endif
#if defined(ERROR_DS_GROUP_CONVERSION_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_GROUP_CONVERSION_ERROR);
#endif
#if defined(ERROR_DS_CANT_MOVE_APP_BASIC_GROUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_MOVE_APP_BASIC_GROUP);
#endif
#if defined(ERROR_DS_CANT_MOVE_APP_QUERY_GROUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_CANT_MOVE_APP_QUERY_GROUP);
#endif
#if defined(ERROR_DS_ROLE_NOT_VERIFIED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_ROLE_NOT_VERIFIED);
#endif
#if defined(ERROR_DS_WKO_CONTAINER_CANNOT_BE_SPECIAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_WKO_CONTAINER_CANNOT_BE_SPECIAL);
#endif
#if defined(ERROR_DS_DOMAIN_RENAME_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DOMAIN_RENAME_IN_PROGRESS);
#endif
#if defined(ERROR_DS_EXISTING_AD_CHILD_NC)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_EXISTING_AD_CHILD_NC);
#endif
#if defined(ERROR_DS_REPL_LIFETIME_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_REPL_LIFETIME_EXCEEDED);
#endif
#if defined(ERROR_DS_DISALLOWED_IN_SYSTEM_CONTAINER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DISALLOWED_IN_SYSTEM_CONTAINER);
#endif
#if defined(ERROR_DS_LDAP_SEND_QUEUE_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_LDAP_SEND_QUEUE_FULL);
#endif
#if defined(ERROR_DS_DRA_OUT_SCHEDULE_WINDOW)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_OUT_SCHEDULE_WINDOW);
#endif
#if defined(ERROR_DS_POLICY_NOT_KNOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_POLICY_NOT_KNOWN);
#endif
#if defined(ERROR_NO_SITE_SETTINGS_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SITE_SETTINGS_OBJECT);
#endif
#if defined(ERROR_NO_SECRETS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_SECRETS);
#endif
#if defined(ERROR_NO_WRITABLE_DC_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NO_WRITABLE_DC_FOUND);
#endif
#if defined(ERROR_DS_NO_SERVER_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_SERVER_OBJECT);
#endif
#if defined(ERROR_DS_NO_NTDSA_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NO_NTDSA_OBJECT);
#endif
#if defined(ERROR_DS_NON_ASQ_SEARCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_NON_ASQ_SEARCH);
#endif
#if defined(ERROR_DS_AUDIT_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_AUDIT_FAILURE);
#endif
#if defined(ERROR_DS_INVALID_SEARCH_FLAG_SUBTREE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INVALID_SEARCH_FLAG_SUBTREE);
#endif
#if defined(ERROR_DS_INVALID_SEARCH_FLAG_TUPLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_INVALID_SEARCH_FLAG_TUPLE);
#endif
#if defined(ERROR_DS_HIERARCHY_TABLE_TOO_DEEP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_HIERARCHY_TABLE_TOO_DEEP);
#endif
#if defined(ERROR_DS_DRA_CORRUPT_UTD_VECTOR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_CORRUPT_UTD_VECTOR);
#endif
#if defined(ERROR_DS_DRA_SECRETS_DENIED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_SECRETS_DENIED);
#endif
#if defined(ERROR_DS_RESERVED_MAPI_ID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_RESERVED_MAPI_ID);
#endif
#if defined(ERROR_DS_MAPI_ID_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MAPI_ID_NOT_AVAILABLE);
#endif
#if defined(ERROR_DS_DRA_MISSING_KRBTGT_SECRET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_MISSING_KRBTGT_SECRET);
#endif
#if defined(ERROR_DS_DOMAIN_NAME_EXISTS_IN_FOREST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DOMAIN_NAME_EXISTS_IN_FOREST);
#endif
#if defined(ERROR_DS_FLAT_NAME_EXISTS_IN_FOREST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_FLAT_NAME_EXISTS_IN_FOREST);
#endif
#if defined(ERROR_INVALID_USER_PRINCIPAL_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_USER_PRINCIPAL_NAME);
#endif
#if defined(ERROR_DS_OID_MAPPED_GROUP_CANT_HAVE_MEMBERS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OID_MAPPED_GROUP_CANT_HAVE_MEMBERS);
#endif
#if defined(ERROR_DS_OID_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_OID_NOT_FOUND);
#endif
#if defined(ERROR_DS_DRA_RECYCLED_TARGET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DRA_RECYCLED_TARGET);
#endif
#if defined(ERROR_DS_DISALLOWED_NC_REDIRECT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_DISALLOWED_NC_REDIRECT);
#endif
#if defined(ERROR_DS_HIGH_ADLDS_FFL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_HIGH_ADLDS_FFL);
#endif
#if defined(ERROR_DS_HIGH_DSA_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_HIGH_DSA_VERSION);
#endif
#if defined(ERROR_DS_LOW_ADLDS_FFL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_LOW_ADLDS_FFL);
#endif
#if defined(ERROR_DOMAIN_SID_SAME_AS_LOCAL_WORKSTATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DOMAIN_SID_SAME_AS_LOCAL_WORKSTATION);
#endif
#if defined(ERROR_DS_UNDELETE_SAM_VALIDATION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_UNDELETE_SAM_VALIDATION_FAILED);
#endif
#if defined(ERROR_INCORRECT_ACCOUNT_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INCORRECT_ACCOUNT_TYPE);
#endif
#if defined(ERROR_DS_SPN_VALUE_NOT_UNIQUE_IN_FOREST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_SPN_VALUE_NOT_UNIQUE_IN_FOREST);
#endif
#if defined(ERROR_DS_UPN_VALUE_NOT_UNIQUE_IN_FOREST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_UPN_VALUE_NOT_UNIQUE_IN_FOREST);
#endif
#if defined(ERROR_DS_MISSING_FOREST_TRUST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_MISSING_FOREST_TRUST);
#endif
#if defined(ERROR_DS_VALUE_KEY_NOT_UNIQUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DS_VALUE_KEY_NOT_UNIQUE);
#endif
#if defined(DNS_ERROR_RESPONSE_CODES_BASE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RESPONSE_CODES_BASE);
#endif
#if defined(DNS_ERROR_RCODE_NO_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_NO_ERROR);
#endif
#if defined(DNS_ERROR_MASK)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_MASK);
#endif
#if defined(DNS_ERROR_RCODE_FORMAT_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_FORMAT_ERROR);
#endif
#if defined(DNS_ERROR_RCODE_SERVER_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_SERVER_FAILURE);
#endif
#if defined(DNS_ERROR_RCODE_NAME_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_NAME_ERROR);
#endif
#if defined(DNS_ERROR_RCODE_NOT_IMPLEMENTED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_NOT_IMPLEMENTED);
#endif
#if defined(DNS_ERROR_RCODE_REFUSED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_REFUSED);
#endif
#if defined(DNS_ERROR_RCODE_YXDOMAIN)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_YXDOMAIN);
#endif
#if defined(DNS_ERROR_RCODE_YXRRSET)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_YXRRSET);
#endif
#if defined(DNS_ERROR_RCODE_NXRRSET)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_NXRRSET);
#endif
#if defined(DNS_ERROR_RCODE_NOTAUTH)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_NOTAUTH);
#endif
#if defined(DNS_ERROR_RCODE_NOTZONE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_NOTZONE);
#endif
#if defined(DNS_ERROR_RCODE_BADSIG)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_BADSIG);
#endif
#if defined(DNS_ERROR_RCODE_BADKEY)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_BADKEY);
#endif
#if defined(DNS_ERROR_RCODE_BADTIME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_BADTIME);
#endif
#if defined(DNS_ERROR_RCODE_LAST)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE_LAST);
#endif
#if defined(DNS_ERROR_DNSSEC_BASE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DNSSEC_BASE);
#endif
#if defined(DNS_ERROR_KEYMASTER_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_KEYMASTER_REQUIRED);
#endif
#if defined(DNS_ERROR_NOT_ALLOWED_ON_SIGNED_ZONE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NOT_ALLOWED_ON_SIGNED_ZONE);
#endif
#if defined(DNS_ERROR_NSEC3_INCOMPATIBLE_WITH_RSA_SHA1)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NSEC3_INCOMPATIBLE_WITH_RSA_SHA1);
#endif
#if defined(DNS_ERROR_NOT_ENOUGH_SIGNING_KEY_DESCRIPTORS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NOT_ENOUGH_SIGNING_KEY_DESCRIPTORS);
#endif
#if defined(DNS_ERROR_UNSUPPORTED_ALGORITHM)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_UNSUPPORTED_ALGORITHM);
#endif
#if defined(DNS_ERROR_INVALID_KEY_SIZE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_KEY_SIZE);
#endif
#if defined(DNS_ERROR_SIGNING_KEY_NOT_ACCESSIBLE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_SIGNING_KEY_NOT_ACCESSIBLE);
#endif
#if defined(DNS_ERROR_KSP_DOES_NOT_SUPPORT_PROTECTION)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_KSP_DOES_NOT_SUPPORT_PROTECTION);
#endif
#if defined(DNS_ERROR_UNEXPECTED_DATA_PROTECTION_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_UNEXPECTED_DATA_PROTECTION_ERROR);
#endif
#if defined(DNS_ERROR_UNEXPECTED_CNG_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_UNEXPECTED_CNG_ERROR);
#endif
#if defined(DNS_ERROR_UNKNOWN_SIGNING_PARAMETER_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_UNKNOWN_SIGNING_PARAMETER_VERSION);
#endif
#if defined(DNS_ERROR_KSP_NOT_ACCESSIBLE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_KSP_NOT_ACCESSIBLE);
#endif
#if defined(DNS_ERROR_TOO_MANY_SKDS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_TOO_MANY_SKDS);
#endif
#if defined(DNS_ERROR_INVALID_ROLLOVER_PERIOD)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_ROLLOVER_PERIOD);
#endif
#if defined(DNS_ERROR_INVALID_INITIAL_ROLLOVER_OFFSET)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_INITIAL_ROLLOVER_OFFSET);
#endif
#if defined(DNS_ERROR_ROLLOVER_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ROLLOVER_IN_PROGRESS);
#endif
#if defined(DNS_ERROR_STANDBY_KEY_NOT_PRESENT)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_STANDBY_KEY_NOT_PRESENT);
#endif
#if defined(DNS_ERROR_NOT_ALLOWED_ON_ZSK)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NOT_ALLOWED_ON_ZSK);
#endif
#if defined(DNS_ERROR_NOT_ALLOWED_ON_ACTIVE_SKD)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NOT_ALLOWED_ON_ACTIVE_SKD);
#endif
#if defined(DNS_ERROR_ROLLOVER_ALREADY_QUEUED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ROLLOVER_ALREADY_QUEUED);
#endif
#if defined(DNS_ERROR_NOT_ALLOWED_ON_UNSIGNED_ZONE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NOT_ALLOWED_ON_UNSIGNED_ZONE);
#endif
#if defined(DNS_ERROR_BAD_KEYMASTER)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_BAD_KEYMASTER);
#endif
#if defined(DNS_ERROR_INVALID_SIGNATURE_VALIDITY_PERIOD)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_SIGNATURE_VALIDITY_PERIOD);
#endif
#if defined(DNS_ERROR_INVALID_NSEC3_ITERATION_COUNT)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_NSEC3_ITERATION_COUNT);
#endif
#if defined(DNS_ERROR_DNSSEC_IS_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DNSSEC_IS_DISABLED);
#endif
#if defined(DNS_ERROR_INVALID_XML)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_XML);
#endif
#if defined(DNS_ERROR_NO_VALID_TRUST_ANCHORS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NO_VALID_TRUST_ANCHORS);
#endif
#if defined(DNS_ERROR_ROLLOVER_NOT_POKEABLE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ROLLOVER_NOT_POKEABLE);
#endif
#if defined(DNS_ERROR_NSEC3_NAME_COLLISION)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NSEC3_NAME_COLLISION);
#endif
#if defined(DNS_ERROR_NSEC_INCOMPATIBLE_WITH_NSEC3_RSA_SHA1)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NSEC_INCOMPATIBLE_WITH_NSEC3_RSA_SHA1);
#endif
#if defined(DNS_ERROR_PACKET_FMT_BASE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_PACKET_FMT_BASE);
#endif
#if defined(DNS_INFO_NO_RECORDS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_INFO_NO_RECORDS);
#endif
#if defined(DNS_ERROR_BAD_PACKET)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_BAD_PACKET);
#endif
#if defined(DNS_ERROR_NO_PACKET)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NO_PACKET);
#endif
#if defined(DNS_ERROR_RCODE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RCODE);
#endif
#if defined(DNS_ERROR_UNSECURE_PACKET)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_UNSECURE_PACKET);
#endif
#if defined(DNS_STATUS_PACKET_UNSECURE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_STATUS_PACKET_UNSECURE);
#endif
#if defined(DNS_REQUEST_PENDING)
    BOOST_WINAPI_TEST_CONSTANT(DNS_REQUEST_PENDING);
#endif
#if defined(DNS_ERROR_NO_MEMORY)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NO_MEMORY);
#endif
#if defined(DNS_ERROR_INVALID_NAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_NAME);
#endif
#if defined(DNS_ERROR_INVALID_DATA)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_DATA);
#endif
#if defined(DNS_ERROR_GENERAL_API_BASE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_GENERAL_API_BASE);
#endif
#if defined(DNS_ERROR_INVALID_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_TYPE);
#endif
#if defined(DNS_ERROR_INVALID_IP_ADDRESS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_IP_ADDRESS);
#endif
#if defined(DNS_ERROR_INVALID_PROPERTY)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_PROPERTY);
#endif
#if defined(DNS_ERROR_TRY_AGAIN_LATER)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_TRY_AGAIN_LATER);
#endif
#if defined(DNS_ERROR_NOT_UNIQUE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NOT_UNIQUE);
#endif
#if defined(DNS_ERROR_NON_RFC_NAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NON_RFC_NAME);
#endif
#if defined(DNS_STATUS_FQDN)
    BOOST_WINAPI_TEST_CONSTANT(DNS_STATUS_FQDN);
#endif
#if defined(DNS_STATUS_DOTTED_NAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_STATUS_DOTTED_NAME);
#endif
#if defined(DNS_STATUS_SINGLE_PART_NAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_STATUS_SINGLE_PART_NAME);
#endif
#if defined(DNS_ERROR_INVALID_NAME_CHAR)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_NAME_CHAR);
#endif
#if defined(DNS_ERROR_NUMERIC_NAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NUMERIC_NAME);
#endif
#if defined(DNS_ERROR_NOT_ALLOWED_ON_ROOT_SERVER)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NOT_ALLOWED_ON_ROOT_SERVER);
#endif
#if defined(DNS_ERROR_NOT_ALLOWED_UNDER_DELEGATION)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NOT_ALLOWED_UNDER_DELEGATION);
#endif
#if defined(DNS_ERROR_CANNOT_FIND_ROOT_HINTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_CANNOT_FIND_ROOT_HINTS);
#endif
#if defined(DNS_ERROR_INCONSISTENT_ROOT_HINTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INCONSISTENT_ROOT_HINTS);
#endif
#if defined(DNS_ERROR_DWORD_VALUE_TOO_SMALL)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DWORD_VALUE_TOO_SMALL);
#endif
#if defined(DNS_ERROR_DWORD_VALUE_TOO_LARGE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DWORD_VALUE_TOO_LARGE);
#endif
#if defined(DNS_ERROR_BACKGROUND_LOADING)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_BACKGROUND_LOADING);
#endif
#if defined(DNS_ERROR_NOT_ALLOWED_ON_RODC)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NOT_ALLOWED_ON_RODC);
#endif
#if defined(DNS_ERROR_NOT_ALLOWED_UNDER_DNAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NOT_ALLOWED_UNDER_DNAME);
#endif
#if defined(DNS_ERROR_DELEGATION_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DELEGATION_REQUIRED);
#endif
#if defined(DNS_ERROR_INVALID_POLICY_TABLE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_POLICY_TABLE);
#endif
#if defined(DNS_ERROR_ADDRESS_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ADDRESS_REQUIRED);
#endif
#if defined(DNS_ERROR_ZONE_BASE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONE_BASE);
#endif
#if defined(DNS_ERROR_ZONE_DOES_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONE_DOES_NOT_EXIST);
#endif
#if defined(DNS_ERROR_NO_ZONE_INFO)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NO_ZONE_INFO);
#endif
#if defined(DNS_ERROR_INVALID_ZONE_OPERATION)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_ZONE_OPERATION);
#endif
#if defined(DNS_ERROR_ZONE_CONFIGURATION_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONE_CONFIGURATION_ERROR);
#endif
#if defined(DNS_ERROR_ZONE_HAS_NO_SOA_RECORD)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONE_HAS_NO_SOA_RECORD);
#endif
#if defined(DNS_ERROR_ZONE_HAS_NO_NS_RECORDS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONE_HAS_NO_NS_RECORDS);
#endif
#if defined(DNS_ERROR_ZONE_LOCKED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONE_LOCKED);
#endif
#if defined(DNS_ERROR_ZONE_CREATION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONE_CREATION_FAILED);
#endif
#if defined(DNS_ERROR_ZONE_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONE_ALREADY_EXISTS);
#endif
#if defined(DNS_ERROR_AUTOZONE_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_AUTOZONE_ALREADY_EXISTS);
#endif
#if defined(DNS_ERROR_INVALID_ZONE_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_ZONE_TYPE);
#endif
#if defined(DNS_ERROR_SECONDARY_REQUIRES_MASTER_IP)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_SECONDARY_REQUIRES_MASTER_IP);
#endif
#if defined(DNS_ERROR_ZONE_NOT_SECONDARY)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONE_NOT_SECONDARY);
#endif
#if defined(DNS_ERROR_NEED_SECONDARY_ADDRESSES)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NEED_SECONDARY_ADDRESSES);
#endif
#if defined(DNS_ERROR_WINS_INIT_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_WINS_INIT_FAILED);
#endif
#if defined(DNS_ERROR_NEED_WINS_SERVERS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NEED_WINS_SERVERS);
#endif
#if defined(DNS_ERROR_NBSTAT_INIT_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NBSTAT_INIT_FAILED);
#endif
#if defined(DNS_ERROR_SOA_DELETE_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_SOA_DELETE_INVALID);
#endif
#if defined(DNS_ERROR_FORWARDER_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_FORWARDER_ALREADY_EXISTS);
#endif
#if defined(DNS_ERROR_ZONE_REQUIRES_MASTER_IP)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONE_REQUIRES_MASTER_IP);
#endif
#if defined(DNS_ERROR_ZONE_IS_SHUTDOWN)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONE_IS_SHUTDOWN);
#endif
#if defined(DNS_ERROR_ZONE_LOCKED_FOR_SIGNING)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONE_LOCKED_FOR_SIGNING);
#endif
#if defined(DNS_ERROR_DATAFILE_BASE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DATAFILE_BASE);
#endif
#if defined(DNS_ERROR_PRIMARY_REQUIRES_DATAFILE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_PRIMARY_REQUIRES_DATAFILE);
#endif
#if defined(DNS_ERROR_INVALID_DATAFILE_NAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_DATAFILE_NAME);
#endif
#if defined(DNS_ERROR_DATAFILE_OPEN_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DATAFILE_OPEN_FAILURE);
#endif
#if defined(DNS_ERROR_FILE_WRITEBACK_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_FILE_WRITEBACK_FAILED);
#endif
#if defined(DNS_ERROR_DATAFILE_PARSING)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DATAFILE_PARSING);
#endif
#if defined(DNS_ERROR_DATABASE_BASE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DATABASE_BASE);
#endif
#if defined(DNS_ERROR_RECORD_DOES_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RECORD_DOES_NOT_EXIST);
#endif
#if defined(DNS_ERROR_RECORD_FORMAT)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RECORD_FORMAT);
#endif
#if defined(DNS_ERROR_NODE_CREATION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NODE_CREATION_FAILED);
#endif
#if defined(DNS_ERROR_UNKNOWN_RECORD_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_UNKNOWN_RECORD_TYPE);
#endif
#if defined(DNS_ERROR_RECORD_TIMED_OUT)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RECORD_TIMED_OUT);
#endif
#if defined(DNS_ERROR_NAME_NOT_IN_ZONE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NAME_NOT_IN_ZONE);
#endif
#if defined(DNS_ERROR_CNAME_LOOP)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_CNAME_LOOP);
#endif
#if defined(DNS_ERROR_NODE_IS_CNAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NODE_IS_CNAME);
#endif
#if defined(DNS_ERROR_CNAME_COLLISION)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_CNAME_COLLISION);
#endif
#if defined(DNS_ERROR_RECORD_ONLY_AT_ZONE_ROOT)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RECORD_ONLY_AT_ZONE_ROOT);
#endif
#if defined(DNS_ERROR_RECORD_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RECORD_ALREADY_EXISTS);
#endif
#if defined(DNS_ERROR_SECONDARY_DATA)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_SECONDARY_DATA);
#endif
#if defined(DNS_ERROR_NO_CREATE_CACHE_DATA)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NO_CREATE_CACHE_DATA);
#endif
#if defined(DNS_ERROR_NAME_DOES_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NAME_DOES_NOT_EXIST);
#endif
#if defined(DNS_WARNING_PTR_CREATE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_WARNING_PTR_CREATE_FAILED);
#endif
#if defined(DNS_WARNING_DOMAIN_UNDELETED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_WARNING_DOMAIN_UNDELETED);
#endif
#if defined(DNS_ERROR_DS_UNAVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DS_UNAVAILABLE);
#endif
#if defined(DNS_ERROR_DS_ZONE_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DS_ZONE_ALREADY_EXISTS);
#endif
#if defined(DNS_ERROR_NO_BOOTFILE_IF_DS_ZONE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NO_BOOTFILE_IF_DS_ZONE);
#endif
#if defined(DNS_ERROR_NODE_IS_DNAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NODE_IS_DNAME);
#endif
#if defined(DNS_ERROR_DNAME_COLLISION)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DNAME_COLLISION);
#endif
#if defined(DNS_ERROR_ALIAS_LOOP)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ALIAS_LOOP);
#endif
#if defined(DNS_ERROR_OPERATION_BASE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_OPERATION_BASE);
#endif
#if defined(DNS_INFO_AXFR_COMPLETE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_INFO_AXFR_COMPLETE);
#endif
#if defined(DNS_ERROR_AXFR)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_AXFR);
#endif
#if defined(DNS_INFO_ADDED_LOCAL_WINS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_INFO_ADDED_LOCAL_WINS);
#endif
#if defined(DNS_ERROR_SECURE_BASE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_SECURE_BASE);
#endif
#if defined(DNS_STATUS_CONTINUE_NEEDED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_STATUS_CONTINUE_NEEDED);
#endif
#if defined(DNS_ERROR_SETUP_BASE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_SETUP_BASE);
#endif
#if defined(DNS_ERROR_NO_TCPIP)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NO_TCPIP);
#endif
#if defined(DNS_ERROR_NO_DNS_SERVERS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NO_DNS_SERVERS);
#endif
#if defined(DNS_ERROR_DP_BASE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DP_BASE);
#endif
#if defined(DNS_ERROR_DP_DOES_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DP_DOES_NOT_EXIST);
#endif
#if defined(DNS_ERROR_DP_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DP_ALREADY_EXISTS);
#endif
#if defined(DNS_ERROR_DP_NOT_ENLISTED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DP_NOT_ENLISTED);
#endif
#if defined(DNS_ERROR_DP_ALREADY_ENLISTED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DP_ALREADY_ENLISTED);
#endif
#if defined(DNS_ERROR_DP_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DP_NOT_AVAILABLE);
#endif
#if defined(DNS_ERROR_DP_FSMO_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DP_FSMO_ERROR);
#endif
#if defined(DNS_ERROR_RRL_NOT_ENABLED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RRL_NOT_ENABLED);
#endif
#if defined(DNS_ERROR_RRL_INVALID_WINDOW_SIZE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RRL_INVALID_WINDOW_SIZE);
#endif
#if defined(DNS_ERROR_RRL_INVALID_IPV4_PREFIX)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RRL_INVALID_IPV4_PREFIX);
#endif
#if defined(DNS_ERROR_RRL_INVALID_IPV6_PREFIX)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RRL_INVALID_IPV6_PREFIX);
#endif
#if defined(DNS_ERROR_RRL_INVALID_TC_RATE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RRL_INVALID_TC_RATE);
#endif
#if defined(DNS_ERROR_RRL_INVALID_LEAK_RATE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RRL_INVALID_LEAK_RATE);
#endif
#if defined(DNS_ERROR_RRL_LEAK_RATE_LESSTHAN_TC_RATE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_RRL_LEAK_RATE_LESSTHAN_TC_RATE);
#endif
#if defined(DNS_ERROR_VIRTUALIZATION_INSTANCE_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_VIRTUALIZATION_INSTANCE_ALREADY_EXISTS);
#endif
#if defined(DNS_ERROR_VIRTUALIZATION_INSTANCE_DOES_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_VIRTUALIZATION_INSTANCE_DOES_NOT_EXIST);
#endif
#if defined(DNS_ERROR_VIRTUALIZATION_TREE_LOCKED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_VIRTUALIZATION_TREE_LOCKED);
#endif
#if defined(DNS_ERROR_INVAILD_VIRTUALIZATION_INSTANCE_NAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVAILD_VIRTUALIZATION_INSTANCE_NAME);
#endif
#if defined(DNS_ERROR_DEFAULT_VIRTUALIZATION_INSTANCE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DEFAULT_VIRTUALIZATION_INSTANCE);
#endif
#if defined(DNS_ERROR_ZONESCOPE_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONESCOPE_ALREADY_EXISTS);
#endif
#if defined(DNS_ERROR_ZONESCOPE_DOES_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONESCOPE_DOES_NOT_EXIST);
#endif
#if defined(DNS_ERROR_DEFAULT_ZONESCOPE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DEFAULT_ZONESCOPE);
#endif
#if defined(DNS_ERROR_INVALID_ZONESCOPE_NAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_ZONESCOPE_NAME);
#endif
#if defined(DNS_ERROR_NOT_ALLOWED_WITH_ZONESCOPES)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_NOT_ALLOWED_WITH_ZONESCOPES);
#endif
#if defined(DNS_ERROR_LOAD_ZONESCOPE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_LOAD_ZONESCOPE_FAILED);
#endif
#if defined(DNS_ERROR_ZONESCOPE_FILE_WRITEBACK_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONESCOPE_FILE_WRITEBACK_FAILED);
#endif
#if defined(DNS_ERROR_INVALID_SCOPE_NAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_SCOPE_NAME);
#endif
#if defined(DNS_ERROR_SCOPE_DOES_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_SCOPE_DOES_NOT_EXIST);
#endif
#if defined(DNS_ERROR_DEFAULT_SCOPE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_DEFAULT_SCOPE);
#endif
#if defined(DNS_ERROR_INVALID_SCOPE_OPERATION)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_SCOPE_OPERATION);
#endif
#if defined(DNS_ERROR_SCOPE_LOCKED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_SCOPE_LOCKED);
#endif
#if defined(DNS_ERROR_SCOPE_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_SCOPE_ALREADY_EXISTS);
#endif
#if defined(DNS_ERROR_POLICY_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_ALREADY_EXISTS);
#endif
#if defined(DNS_ERROR_POLICY_DOES_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_DOES_NOT_EXIST);
#endif
#if defined(DNS_ERROR_POLICY_INVALID_CRITERIA)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_INVALID_CRITERIA);
#endif
#if defined(DNS_ERROR_POLICY_INVALID_SETTINGS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_INVALID_SETTINGS);
#endif
#if defined(DNS_ERROR_CLIENT_SUBNET_IS_ACCESSED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_CLIENT_SUBNET_IS_ACCESSED);
#endif
#if defined(DNS_ERROR_CLIENT_SUBNET_DOES_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_CLIENT_SUBNET_DOES_NOT_EXIST);
#endif
#if defined(DNS_ERROR_CLIENT_SUBNET_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_CLIENT_SUBNET_ALREADY_EXISTS);
#endif
#if defined(DNS_ERROR_SUBNET_DOES_NOT_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_SUBNET_DOES_NOT_EXIST);
#endif
#if defined(DNS_ERROR_SUBNET_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_SUBNET_ALREADY_EXISTS);
#endif
#if defined(DNS_ERROR_POLICY_LOCKED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_LOCKED);
#endif
#if defined(DNS_ERROR_POLICY_INVALID_WEIGHT)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_INVALID_WEIGHT);
#endif
#if defined(DNS_ERROR_POLICY_INVALID_NAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_INVALID_NAME);
#endif
#if defined(DNS_ERROR_POLICY_MISSING_CRITERIA)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_MISSING_CRITERIA);
#endif
#if defined(DNS_ERROR_INVALID_CLIENT_SUBNET_NAME)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_INVALID_CLIENT_SUBNET_NAME);
#endif
#if defined(DNS_ERROR_POLICY_PROCESSING_ORDER_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_PROCESSING_ORDER_INVALID);
#endif
#if defined(DNS_ERROR_POLICY_SCOPE_MISSING)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_SCOPE_MISSING);
#endif
#if defined(DNS_ERROR_POLICY_SCOPE_NOT_ALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_SCOPE_NOT_ALLOWED);
#endif
#if defined(DNS_ERROR_SERVERSCOPE_IS_REFERENCED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_SERVERSCOPE_IS_REFERENCED);
#endif
#if defined(DNS_ERROR_ZONESCOPE_IS_REFERENCED)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_ZONESCOPE_IS_REFERENCED);
#endif
#if defined(DNS_ERROR_POLICY_INVALID_CRITERIA_CLIENT_SUBNET)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_INVALID_CRITERIA_CLIENT_SUBNET);
#endif
#if defined(DNS_ERROR_POLICY_INVALID_CRITERIA_TRANSPORT_PROTOCOL)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_INVALID_CRITERIA_TRANSPORT_PROTOCOL);
#endif
#if defined(DNS_ERROR_POLICY_INVALID_CRITERIA_NETWORK_PROTOCOL)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_INVALID_CRITERIA_NETWORK_PROTOCOL);
#endif
#if defined(DNS_ERROR_POLICY_INVALID_CRITERIA_INTERFACE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_INVALID_CRITERIA_INTERFACE);
#endif
#if defined(DNS_ERROR_POLICY_INVALID_CRITERIA_FQDN)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_INVALID_CRITERIA_FQDN);
#endif
#if defined(DNS_ERROR_POLICY_INVALID_CRITERIA_QUERY_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_INVALID_CRITERIA_QUERY_TYPE);
#endif
#if defined(DNS_ERROR_POLICY_INVALID_CRITERIA_TIME_OF_DAY)
    BOOST_WINAPI_TEST_CONSTANT(DNS_ERROR_POLICY_INVALID_CRITERIA_TIME_OF_DAY);
#endif
#if defined(WSABASEERR)
    BOOST_WINAPI_TEST_CONSTANT(WSABASEERR);
#endif
#if defined(WSAEINTR)
    BOOST_WINAPI_TEST_CONSTANT(WSAEINTR);
#endif
#if defined(WSAEBADF)
    BOOST_WINAPI_TEST_CONSTANT(WSAEBADF);
#endif
#if defined(WSAEACCES)
    BOOST_WINAPI_TEST_CONSTANT(WSAEACCES);
#endif
#if defined(WSAEFAULT)
    BOOST_WINAPI_TEST_CONSTANT(WSAEFAULT);
#endif
#if defined(WSAEINVAL)
    BOOST_WINAPI_TEST_CONSTANT(WSAEINVAL);
#endif
#if defined(WSAEMFILE)
    BOOST_WINAPI_TEST_CONSTANT(WSAEMFILE);
#endif
#if defined(WSAEWOULDBLOCK)
    BOOST_WINAPI_TEST_CONSTANT(WSAEWOULDBLOCK);
#endif
#if defined(WSAEINPROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(WSAEINPROGRESS);
#endif
#if defined(WSAEALREADY)
    BOOST_WINAPI_TEST_CONSTANT(WSAEALREADY);
#endif
#if defined(WSAENOTSOCK)
    BOOST_WINAPI_TEST_CONSTANT(WSAENOTSOCK);
#endif
#if defined(WSAEDESTADDRREQ)
    BOOST_WINAPI_TEST_CONSTANT(WSAEDESTADDRREQ);
#endif
#if defined(WSAEMSGSIZE)
    BOOST_WINAPI_TEST_CONSTANT(WSAEMSGSIZE);
#endif
#if defined(WSAEPROTOTYPE)
    BOOST_WINAPI_TEST_CONSTANT(WSAEPROTOTYPE);
#endif
#if defined(WSAENOPROTOOPT)
    BOOST_WINAPI_TEST_CONSTANT(WSAENOPROTOOPT);
#endif
#if defined(WSAEPROTONOSUPPORT)
    BOOST_WINAPI_TEST_CONSTANT(WSAEPROTONOSUPPORT);
#endif
#if defined(WSAESOCKTNOSUPPORT)
    BOOST_WINAPI_TEST_CONSTANT(WSAESOCKTNOSUPPORT);
#endif
#if defined(WSAEOPNOTSUPP)
    BOOST_WINAPI_TEST_CONSTANT(WSAEOPNOTSUPP);
#endif
#if defined(WSAEPFNOSUPPORT)
    BOOST_WINAPI_TEST_CONSTANT(WSAEPFNOSUPPORT);
#endif
#if defined(WSAEAFNOSUPPORT)
    BOOST_WINAPI_TEST_CONSTANT(WSAEAFNOSUPPORT);
#endif
#if defined(WSAEADDRINUSE)
    BOOST_WINAPI_TEST_CONSTANT(WSAEADDRINUSE);
#endif
#if defined(WSAEADDRNOTAVAIL)
    BOOST_WINAPI_TEST_CONSTANT(WSAEADDRNOTAVAIL);
#endif
#if defined(WSAENETDOWN)
    BOOST_WINAPI_TEST_CONSTANT(WSAENETDOWN);
#endif
#if defined(WSAENETUNREACH)
    BOOST_WINAPI_TEST_CONSTANT(WSAENETUNREACH);
#endif
#if defined(WSAENETRESET)
    BOOST_WINAPI_TEST_CONSTANT(WSAENETRESET);
#endif
#if defined(WSAECONNABORTED)
    BOOST_WINAPI_TEST_CONSTANT(WSAECONNABORTED);
#endif
#if defined(WSAECONNRESET)
    BOOST_WINAPI_TEST_CONSTANT(WSAECONNRESET);
#endif
#if defined(WSAENOBUFS)
    BOOST_WINAPI_TEST_CONSTANT(WSAENOBUFS);
#endif
#if defined(WSAEISCONN)
    BOOST_WINAPI_TEST_CONSTANT(WSAEISCONN);
#endif
#if defined(WSAENOTCONN)
    BOOST_WINAPI_TEST_CONSTANT(WSAENOTCONN);
#endif
#if defined(WSAESHUTDOWN)
    BOOST_WINAPI_TEST_CONSTANT(WSAESHUTDOWN);
#endif
#if defined(WSAETOOMANYREFS)
    BOOST_WINAPI_TEST_CONSTANT(WSAETOOMANYREFS);
#endif
#if defined(WSAETIMEDOUT)
    BOOST_WINAPI_TEST_CONSTANT(WSAETIMEDOUT);
#endif
#if defined(WSAECONNREFUSED)
    BOOST_WINAPI_TEST_CONSTANT(WSAECONNREFUSED);
#endif
#if defined(WSAELOOP)
    BOOST_WINAPI_TEST_CONSTANT(WSAELOOP);
#endif
#if defined(WSAENAMETOOLONG)
    BOOST_WINAPI_TEST_CONSTANT(WSAENAMETOOLONG);
#endif
#if defined(WSAEHOSTDOWN)
    BOOST_WINAPI_TEST_CONSTANT(WSAEHOSTDOWN);
#endif
#if defined(WSAEHOSTUNREACH)
    BOOST_WINAPI_TEST_CONSTANT(WSAEHOSTUNREACH);
#endif
#if defined(WSAENOTEMPTY)
    BOOST_WINAPI_TEST_CONSTANT(WSAENOTEMPTY);
#endif
#if defined(WSAEPROCLIM)
    BOOST_WINAPI_TEST_CONSTANT(WSAEPROCLIM);
#endif
#if defined(WSAEUSERS)
    BOOST_WINAPI_TEST_CONSTANT(WSAEUSERS);
#endif
#if defined(WSAEDQUOT)
    BOOST_WINAPI_TEST_CONSTANT(WSAEDQUOT);
#endif
#if defined(WSAESTALE)
    BOOST_WINAPI_TEST_CONSTANT(WSAESTALE);
#endif
#if defined(WSAEREMOTE)
    BOOST_WINAPI_TEST_CONSTANT(WSAEREMOTE);
#endif
#if defined(WSASYSNOTREADY)
    BOOST_WINAPI_TEST_CONSTANT(WSASYSNOTREADY);
#endif
#if defined(WSAVERNOTSUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(WSAVERNOTSUPPORTED);
#endif
#if defined(WSANOTINITIALISED)
    BOOST_WINAPI_TEST_CONSTANT(WSANOTINITIALISED);
#endif
#if defined(WSAEDISCON)
    BOOST_WINAPI_TEST_CONSTANT(WSAEDISCON);
#endif
#if defined(WSAENOMORE)
    BOOST_WINAPI_TEST_CONSTANT(WSAENOMORE);
#endif
#if defined(WSAECANCELLED)
    BOOST_WINAPI_TEST_CONSTANT(WSAECANCELLED);
#endif
#if defined(WSAEINVALIDPROCTABLE)
    BOOST_WINAPI_TEST_CONSTANT(WSAEINVALIDPROCTABLE);
#endif
#if defined(WSAEINVALIDPROVIDER)
    BOOST_WINAPI_TEST_CONSTANT(WSAEINVALIDPROVIDER);
#endif
#if defined(WSAEPROVIDERFAILEDINIT)
    BOOST_WINAPI_TEST_CONSTANT(WSAEPROVIDERFAILEDINIT);
#endif
#if defined(WSASYSCALLFAILURE)
    BOOST_WINAPI_TEST_CONSTANT(WSASYSCALLFAILURE);
#endif
#if defined(WSASERVICE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(WSASERVICE_NOT_FOUND);
#endif
#if defined(WSATYPE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(WSATYPE_NOT_FOUND);
#endif
#if defined(WSA_E_NO_MORE)
    BOOST_WINAPI_TEST_CONSTANT(WSA_E_NO_MORE);
#endif
#if defined(WSA_E_CANCELLED)
    BOOST_WINAPI_TEST_CONSTANT(WSA_E_CANCELLED);
#endif
#if defined(WSAEREFUSED)
    BOOST_WINAPI_TEST_CONSTANT(WSAEREFUSED);
#endif
#if defined(WSAHOST_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(WSAHOST_NOT_FOUND);
#endif
#if defined(WSATRY_AGAIN)
    BOOST_WINAPI_TEST_CONSTANT(WSATRY_AGAIN);
#endif
#if defined(WSANO_RECOVERY)
    BOOST_WINAPI_TEST_CONSTANT(WSANO_RECOVERY);
#endif
#if defined(WSANO_DATA)
    BOOST_WINAPI_TEST_CONSTANT(WSANO_DATA);
#endif
#if defined(WSA_QOS_RECEIVERS)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_RECEIVERS);
#endif
#if defined(WSA_QOS_SENDERS)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_SENDERS);
#endif
#if defined(WSA_QOS_NO_SENDERS)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_NO_SENDERS);
#endif
#if defined(WSA_QOS_NO_RECEIVERS)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_NO_RECEIVERS);
#endif
#if defined(WSA_QOS_REQUEST_CONFIRMED)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_REQUEST_CONFIRMED);
#endif
#if defined(WSA_QOS_ADMISSION_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_ADMISSION_FAILURE);
#endif
#if defined(WSA_QOS_POLICY_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_POLICY_FAILURE);
#endif
#if defined(WSA_QOS_BAD_STYLE)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_BAD_STYLE);
#endif
#if defined(WSA_QOS_BAD_OBJECT)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_BAD_OBJECT);
#endif
#if defined(WSA_QOS_TRAFFIC_CTRL_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_TRAFFIC_CTRL_ERROR);
#endif
#if defined(WSA_QOS_GENERIC_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_GENERIC_ERROR);
#endif
#if defined(WSA_QOS_ESERVICETYPE)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_ESERVICETYPE);
#endif
#if defined(WSA_QOS_EFLOWSPEC)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_EFLOWSPEC);
#endif
#if defined(WSA_QOS_EPROVSPECBUF)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_EPROVSPECBUF);
#endif
#if defined(WSA_QOS_EFILTERSTYLE)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_EFILTERSTYLE);
#endif
#if defined(WSA_QOS_EFILTERTYPE)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_EFILTERTYPE);
#endif
#if defined(WSA_QOS_EFILTERCOUNT)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_EFILTERCOUNT);
#endif
#if defined(WSA_QOS_EOBJLENGTH)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_EOBJLENGTH);
#endif
#if defined(WSA_QOS_EFLOWCOUNT)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_EFLOWCOUNT);
#endif
#if defined(WSA_QOS_EUNKOWNPSOBJ)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_EUNKOWNPSOBJ);
#endif
#if defined(WSA_QOS_EPOLICYOBJ)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_EPOLICYOBJ);
#endif
#if defined(WSA_QOS_EFLOWDESC)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_EFLOWDESC);
#endif
#if defined(WSA_QOS_EPSFLOWSPEC)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_EPSFLOWSPEC);
#endif
#if defined(WSA_QOS_EPSFILTERSPEC)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_EPSFILTERSPEC);
#endif
#if defined(WSA_QOS_ESDMODEOBJ)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_ESDMODEOBJ);
#endif
#if defined(WSA_QOS_ESHAPERATEOBJ)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_ESHAPERATEOBJ);
#endif
#if defined(WSA_QOS_RESERVED_PETYPE)
    BOOST_WINAPI_TEST_CONSTANT(WSA_QOS_RESERVED_PETYPE);
#endif
#if defined(WSA_SECURE_HOST_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(WSA_SECURE_HOST_NOT_FOUND);
#endif
#if defined(WSA_IPSEC_NAME_POLICY_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(WSA_IPSEC_NAME_POLICY_ERROR);
#endif
#if defined(ERROR_IPSEC_QM_POLICY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_QM_POLICY_EXISTS);
#endif
#if defined(ERROR_IPSEC_QM_POLICY_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_QM_POLICY_NOT_FOUND);
#endif
#if defined(ERROR_IPSEC_QM_POLICY_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_QM_POLICY_IN_USE);
#endif
#if defined(ERROR_IPSEC_MM_POLICY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_MM_POLICY_EXISTS);
#endif
#if defined(ERROR_IPSEC_MM_POLICY_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_MM_POLICY_NOT_FOUND);
#endif
#if defined(ERROR_IPSEC_MM_POLICY_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_MM_POLICY_IN_USE);
#endif
#if defined(ERROR_IPSEC_MM_FILTER_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_MM_FILTER_EXISTS);
#endif
#if defined(ERROR_IPSEC_MM_FILTER_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_MM_FILTER_NOT_FOUND);
#endif
#if defined(ERROR_IPSEC_TRANSPORT_FILTER_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_TRANSPORT_FILTER_EXISTS);
#endif
#if defined(ERROR_IPSEC_TRANSPORT_FILTER_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_TRANSPORT_FILTER_NOT_FOUND);
#endif
#if defined(ERROR_IPSEC_MM_AUTH_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_MM_AUTH_EXISTS);
#endif
#if defined(ERROR_IPSEC_MM_AUTH_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_MM_AUTH_NOT_FOUND);
#endif
#if defined(ERROR_IPSEC_MM_AUTH_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_MM_AUTH_IN_USE);
#endif
#if defined(ERROR_IPSEC_DEFAULT_MM_POLICY_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_DEFAULT_MM_POLICY_NOT_FOUND);
#endif
#if defined(ERROR_IPSEC_DEFAULT_MM_AUTH_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_DEFAULT_MM_AUTH_NOT_FOUND);
#endif
#if defined(ERROR_IPSEC_DEFAULT_QM_POLICY_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_DEFAULT_QM_POLICY_NOT_FOUND);
#endif
#if defined(ERROR_IPSEC_TUNNEL_FILTER_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_TUNNEL_FILTER_EXISTS);
#endif
#if defined(ERROR_IPSEC_TUNNEL_FILTER_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_TUNNEL_FILTER_NOT_FOUND);
#endif
#if defined(ERROR_IPSEC_MM_FILTER_PENDING_DELETION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_MM_FILTER_PENDING_DELETION);
#endif
#if defined(ERROR_IPSEC_TRANSPORT_FILTER_PENDING_DELETION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_TRANSPORT_FILTER_PENDING_DELETION);
#endif
#if defined(ERROR_IPSEC_TUNNEL_FILTER_PENDING_DELETION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_TUNNEL_FILTER_PENDING_DELETION);
#endif
#if defined(ERROR_IPSEC_MM_POLICY_PENDING_DELETION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_MM_POLICY_PENDING_DELETION);
#endif
#if defined(ERROR_IPSEC_MM_AUTH_PENDING_DELETION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_MM_AUTH_PENDING_DELETION);
#endif
#if defined(ERROR_IPSEC_QM_POLICY_PENDING_DELETION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_QM_POLICY_PENDING_DELETION);
#endif
#if defined(WARNING_IPSEC_MM_POLICY_PRUNED)
    BOOST_WINAPI_TEST_CONSTANT(WARNING_IPSEC_MM_POLICY_PRUNED);
#endif
#if defined(WARNING_IPSEC_QM_POLICY_PRUNED)
    BOOST_WINAPI_TEST_CONSTANT(WARNING_IPSEC_QM_POLICY_PRUNED);
#endif
#if defined(ERROR_IPSEC_IKE_NEG_STATUS_BEGIN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_NEG_STATUS_BEGIN);
#endif
#if defined(ERROR_IPSEC_IKE_AUTH_FAIL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_AUTH_FAIL);
#endif
#if defined(ERROR_IPSEC_IKE_ATTRIB_FAIL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_ATTRIB_FAIL);
#endif
#if defined(ERROR_IPSEC_IKE_NEGOTIATION_PENDING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_NEGOTIATION_PENDING);
#endif
#if defined(ERROR_IPSEC_IKE_GENERAL_PROCESSING_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_GENERAL_PROCESSING_ERROR);
#endif
#if defined(ERROR_IPSEC_IKE_TIMED_OUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_TIMED_OUT);
#endif
#if defined(ERROR_IPSEC_IKE_NO_CERT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_NO_CERT);
#endif
#if defined(ERROR_IPSEC_IKE_SA_DELETED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_SA_DELETED);
#endif
#if defined(ERROR_IPSEC_IKE_SA_REAPED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_SA_REAPED);
#endif
#if defined(ERROR_IPSEC_IKE_MM_ACQUIRE_DROP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_MM_ACQUIRE_DROP);
#endif
#if defined(ERROR_IPSEC_IKE_QM_ACQUIRE_DROP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_QM_ACQUIRE_DROP);
#endif
#if defined(ERROR_IPSEC_IKE_QUEUE_DROP_MM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_QUEUE_DROP_MM);
#endif
#if defined(ERROR_IPSEC_IKE_QUEUE_DROP_NO_MM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_QUEUE_DROP_NO_MM);
#endif
#if defined(ERROR_IPSEC_IKE_DROP_NO_RESPONSE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_DROP_NO_RESPONSE);
#endif
#if defined(ERROR_IPSEC_IKE_MM_DELAY_DROP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_MM_DELAY_DROP);
#endif
#if defined(ERROR_IPSEC_IKE_QM_DELAY_DROP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_QM_DELAY_DROP);
#endif
#if defined(ERROR_IPSEC_IKE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_ERROR);
#endif
#if defined(ERROR_IPSEC_IKE_CRL_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_CRL_FAILED);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_KEY_USAGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_KEY_USAGE);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_CERT_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_CERT_TYPE);
#endif
#if defined(ERROR_IPSEC_IKE_NO_PRIVATE_KEY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_NO_PRIVATE_KEY);
#endif
#if defined(ERROR_IPSEC_IKE_SIMULTANEOUS_REKEY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_SIMULTANEOUS_REKEY);
#endif
#if defined(ERROR_IPSEC_IKE_DH_FAIL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_DH_FAIL);
#endif
#if defined(ERROR_IPSEC_IKE_CRITICAL_PAYLOAD_NOT_RECOGNIZED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_CRITICAL_PAYLOAD_NOT_RECOGNIZED);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_HEADER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_HEADER);
#endif
#if defined(ERROR_IPSEC_IKE_NO_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_NO_POLICY);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_SIGNATURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_SIGNATURE);
#endif
#if defined(ERROR_IPSEC_IKE_KERBEROS_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_KERBEROS_ERROR);
#endif
#if defined(ERROR_IPSEC_IKE_NO_PUBLIC_KEY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_NO_PUBLIC_KEY);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_SA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_SA);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_PROP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_PROP);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_TRANS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_TRANS);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_KE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_KE);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_ID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_ID);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_CERT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_CERT);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_CERT_REQ)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_CERT_REQ);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_HASH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_HASH);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_SIG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_SIG);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_NONCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_NONCE);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_NOTIFY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_NOTIFY);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_DELETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_DELETE);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_VENDOR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_VENDOR);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_PAYLOAD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_PAYLOAD);
#endif
#if defined(ERROR_IPSEC_IKE_LOAD_SOFT_SA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_LOAD_SOFT_SA);
#endif
#if defined(ERROR_IPSEC_IKE_SOFT_SA_TORN_DOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_SOFT_SA_TORN_DOWN);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_COOKIE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_COOKIE);
#endif
#if defined(ERROR_IPSEC_IKE_NO_PEER_CERT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_NO_PEER_CERT);
#endif
#if defined(ERROR_IPSEC_IKE_PEER_CRL_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PEER_CRL_FAILED);
#endif
#if defined(ERROR_IPSEC_IKE_POLICY_CHANGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_POLICY_CHANGE);
#endif
#if defined(ERROR_IPSEC_IKE_NO_MM_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_NO_MM_POLICY);
#endif
#if defined(ERROR_IPSEC_IKE_NOTCBPRIV)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_NOTCBPRIV);
#endif
#if defined(ERROR_IPSEC_IKE_SECLOADFAIL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_SECLOADFAIL);
#endif
#if defined(ERROR_IPSEC_IKE_FAILSSPINIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_FAILSSPINIT);
#endif
#if defined(ERROR_IPSEC_IKE_FAILQUERYSSP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_FAILQUERYSSP);
#endif
#if defined(ERROR_IPSEC_IKE_SRVACQFAIL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_SRVACQFAIL);
#endif
#if defined(ERROR_IPSEC_IKE_SRVQUERYCRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_SRVQUERYCRED);
#endif
#if defined(ERROR_IPSEC_IKE_GETSPIFAIL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_GETSPIFAIL);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_FILTER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_FILTER);
#endif
#if defined(ERROR_IPSEC_IKE_OUT_OF_MEMORY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_OUT_OF_MEMORY);
#endif
#if defined(ERROR_IPSEC_IKE_ADD_UPDATE_KEY_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_ADD_UPDATE_KEY_FAILED);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_POLICY);
#endif
#if defined(ERROR_IPSEC_IKE_UNKNOWN_DOI)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_UNKNOWN_DOI);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_SITUATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_SITUATION);
#endif
#if defined(ERROR_IPSEC_IKE_DH_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_DH_FAILURE);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_GROUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_GROUP);
#endif
#if defined(ERROR_IPSEC_IKE_ENCRYPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_ENCRYPT);
#endif
#if defined(ERROR_IPSEC_IKE_DECRYPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_DECRYPT);
#endif
#if defined(ERROR_IPSEC_IKE_POLICY_MATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_POLICY_MATCH);
#endif
#if defined(ERROR_IPSEC_IKE_UNSUPPORTED_ID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_UNSUPPORTED_ID);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_HASH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_HASH);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_HASH_ALG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_HASH_ALG);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_HASH_SIZE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_HASH_SIZE);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_ENCRYPT_ALG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_ENCRYPT_ALG);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_AUTH_ALG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_AUTH_ALG);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_SIG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_SIG);
#endif
#if defined(ERROR_IPSEC_IKE_LOAD_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_LOAD_FAILED);
#endif
#if defined(ERROR_IPSEC_IKE_RPC_DELETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_RPC_DELETE);
#endif
#if defined(ERROR_IPSEC_IKE_BENIGN_REINIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_BENIGN_REINIT);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_RESPONDER_LIFETIME_NOTIFY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_RESPONDER_LIFETIME_NOTIFY);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_MAJOR_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_MAJOR_VERSION);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_CERT_KEYLEN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_CERT_KEYLEN);
#endif
#if defined(ERROR_IPSEC_IKE_MM_LIMIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_MM_LIMIT);
#endif
#if defined(ERROR_IPSEC_IKE_NEGOTIATION_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_NEGOTIATION_DISABLED);
#endif
#if defined(ERROR_IPSEC_IKE_QM_LIMIT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_QM_LIMIT);
#endif
#if defined(ERROR_IPSEC_IKE_MM_EXPIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_MM_EXPIRED);
#endif
#if defined(ERROR_IPSEC_IKE_PEER_MM_ASSUMED_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PEER_MM_ASSUMED_INVALID);
#endif
#if defined(ERROR_IPSEC_IKE_CERT_CHAIN_POLICY_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_CERT_CHAIN_POLICY_MISMATCH);
#endif
#if defined(ERROR_IPSEC_IKE_UNEXPECTED_MESSAGE_ID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_UNEXPECTED_MESSAGE_ID);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_AUTH_PAYLOAD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_AUTH_PAYLOAD);
#endif
#if defined(ERROR_IPSEC_IKE_DOS_COOKIE_SENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_DOS_COOKIE_SENT);
#endif
#if defined(ERROR_IPSEC_IKE_SHUTTING_DOWN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_SHUTTING_DOWN);
#endif
#if defined(ERROR_IPSEC_IKE_CGA_AUTH_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_CGA_AUTH_FAILED);
#endif
#if defined(ERROR_IPSEC_IKE_PROCESS_ERR_NATOA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PROCESS_ERR_NATOA);
#endif
#if defined(ERROR_IPSEC_IKE_INVALID_MM_FOR_QM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INVALID_MM_FOR_QM);
#endif
#if defined(ERROR_IPSEC_IKE_QM_EXPIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_QM_EXPIRED);
#endif
#if defined(ERROR_IPSEC_IKE_TOO_MANY_FILTERS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_TOO_MANY_FILTERS);
#endif
#if BOOST_PLAT_WINDOWS_SDK_VERSION >= BOOST_WINAPI_WINDOWS_SDK_6_0 && defined(ERROR_IPSEC_IKE_NEG_STATUS_END)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_NEG_STATUS_END);
#endif
#if defined(ERROR_IPSEC_IKE_KILL_DUMMY_NAP_TUNNEL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_KILL_DUMMY_NAP_TUNNEL);
#endif
#if defined(ERROR_IPSEC_IKE_INNER_IP_ASSIGNMENT_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_INNER_IP_ASSIGNMENT_FAILURE);
#endif
#if defined(ERROR_IPSEC_IKE_REQUIRE_CP_PAYLOAD_MISSING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_REQUIRE_CP_PAYLOAD_MISSING);
#endif
#if defined(ERROR_IPSEC_KEY_MODULE_IMPERSONATION_NEGOTIATION_PENDING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_KEY_MODULE_IMPERSONATION_NEGOTIATION_PENDING);
#endif
#if defined(ERROR_IPSEC_IKE_COEXISTENCE_SUPPRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_COEXISTENCE_SUPPRESS);
#endif
#if defined(ERROR_IPSEC_IKE_RATELIMIT_DROP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_RATELIMIT_DROP);
#endif
#if defined(ERROR_IPSEC_IKE_PEER_DOESNT_SUPPORT_MOBIKE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_PEER_DOESNT_SUPPORT_MOBIKE);
#endif
#if defined(ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE);
#endif
#if defined(ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_FAILURE);
#endif
#if defined(ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE_WITH_OPTIONAL_RETRY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE_WITH_OPTIONAL_RETRY);
#endif
#if defined(ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_AND_CERTMAP_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_AND_CERTMAP_FAILURE);
#endif
#if defined(ERROR_IPSEC_IKE_NEG_STATUS_EXTENDED_END)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_IKE_NEG_STATUS_EXTENDED_END);
#endif
#if defined(ERROR_IPSEC_BAD_SPI)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_BAD_SPI);
#endif
#if defined(ERROR_IPSEC_SA_LIFETIME_EXPIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_SA_LIFETIME_EXPIRED);
#endif
#if defined(ERROR_IPSEC_WRONG_SA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_WRONG_SA);
#endif
#if defined(ERROR_IPSEC_REPLAY_CHECK_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_REPLAY_CHECK_FAILED);
#endif
#if defined(ERROR_IPSEC_INVALID_PACKET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_INVALID_PACKET);
#endif
#if defined(ERROR_IPSEC_INTEGRITY_CHECK_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_INTEGRITY_CHECK_FAILED);
#endif
#if defined(ERROR_IPSEC_CLEAR_TEXT_DROP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_CLEAR_TEXT_DROP);
#endif
#if defined(ERROR_IPSEC_AUTH_FIREWALL_DROP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_AUTH_FIREWALL_DROP);
#endif
#if defined(ERROR_IPSEC_THROTTLE_DROP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_THROTTLE_DROP);
#endif
#if defined(ERROR_IPSEC_DOSP_BLOCK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_DOSP_BLOCK);
#endif
#if defined(ERROR_IPSEC_DOSP_RECEIVED_MULTICAST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_DOSP_RECEIVED_MULTICAST);
#endif
#if defined(ERROR_IPSEC_DOSP_INVALID_PACKET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_DOSP_INVALID_PACKET);
#endif
#if defined(ERROR_IPSEC_DOSP_STATE_LOOKUP_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_DOSP_STATE_LOOKUP_FAILED);
#endif
#if defined(ERROR_IPSEC_DOSP_MAX_ENTRIES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_DOSP_MAX_ENTRIES);
#endif
#if defined(ERROR_IPSEC_DOSP_KEYMOD_NOT_ALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_DOSP_KEYMOD_NOT_ALLOWED);
#endif
#if defined(ERROR_IPSEC_DOSP_NOT_INSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_DOSP_NOT_INSTALLED);
#endif
#if defined(ERROR_IPSEC_DOSP_MAX_PER_IP_RATELIMIT_QUEUES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_IPSEC_DOSP_MAX_PER_IP_RATELIMIT_QUEUES);
#endif
#if defined(ERROR_SXS_SECTION_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_SECTION_NOT_FOUND);
#endif
#if defined(ERROR_SXS_CANT_GEN_ACTCTX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_CANT_GEN_ACTCTX);
#endif
#if defined(ERROR_SXS_INVALID_ACTCTXDATA_FORMAT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_INVALID_ACTCTXDATA_FORMAT);
#endif
#if defined(ERROR_SXS_ASSEMBLY_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_ASSEMBLY_NOT_FOUND);
#endif
#if defined(ERROR_SXS_MANIFEST_FORMAT_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_MANIFEST_FORMAT_ERROR);
#endif
#if defined(ERROR_SXS_MANIFEST_PARSE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_MANIFEST_PARSE_ERROR);
#endif
#if defined(ERROR_SXS_ACTIVATION_CONTEXT_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_ACTIVATION_CONTEXT_DISABLED);
#endif
#if defined(ERROR_SXS_KEY_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_KEY_NOT_FOUND);
#endif
#if defined(ERROR_SXS_VERSION_CONFLICT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_VERSION_CONFLICT);
#endif
#if defined(ERROR_SXS_WRONG_SECTION_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_WRONG_SECTION_TYPE);
#endif
#if defined(ERROR_SXS_THREAD_QUERIES_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_THREAD_QUERIES_DISABLED);
#endif
#if defined(ERROR_SXS_PROCESS_DEFAULT_ALREADY_SET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_PROCESS_DEFAULT_ALREADY_SET);
#endif
#if defined(ERROR_SXS_UNKNOWN_ENCODING_GROUP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_UNKNOWN_ENCODING_GROUP);
#endif
#if defined(ERROR_SXS_UNKNOWN_ENCODING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_UNKNOWN_ENCODING);
#endif
#if defined(ERROR_SXS_INVALID_XML_NAMESPACE_URI)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_INVALID_XML_NAMESPACE_URI);
#endif
#if defined(ERROR_SXS_ROOT_MANIFEST_DEPENDENCY_NOT_INSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_ROOT_MANIFEST_DEPENDENCY_NOT_INSTALLED);
#endif
#if defined(ERROR_SXS_LEAF_MANIFEST_DEPENDENCY_NOT_INSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_LEAF_MANIFEST_DEPENDENCY_NOT_INSTALLED);
#endif
#if defined(ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE);
#endif
#if defined(ERROR_SXS_MANIFEST_MISSING_REQUIRED_DEFAULT_NAMESPACE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_MANIFEST_MISSING_REQUIRED_DEFAULT_NAMESPACE);
#endif
#if defined(ERROR_SXS_MANIFEST_INVALID_REQUIRED_DEFAULT_NAMESPACE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_MANIFEST_INVALID_REQUIRED_DEFAULT_NAMESPACE);
#endif
#if defined(ERROR_SXS_PRIVATE_MANIFEST_CROSS_PATH_WITH_REPARSE_POINT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_PRIVATE_MANIFEST_CROSS_PATH_WITH_REPARSE_POINT);
#endif
#if defined(ERROR_SXS_DUPLICATE_DLL_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_DUPLICATE_DLL_NAME);
#endif
#if defined(ERROR_SXS_DUPLICATE_WINDOWCLASS_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_DUPLICATE_WINDOWCLASS_NAME);
#endif
#if defined(ERROR_SXS_DUPLICATE_CLSID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_DUPLICATE_CLSID);
#endif
#if defined(ERROR_SXS_DUPLICATE_IID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_DUPLICATE_IID);
#endif
#if defined(ERROR_SXS_DUPLICATE_TLBID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_DUPLICATE_TLBID);
#endif
#if defined(ERROR_SXS_DUPLICATE_PROGID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_DUPLICATE_PROGID);
#endif
#if defined(ERROR_SXS_DUPLICATE_ASSEMBLY_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_DUPLICATE_ASSEMBLY_NAME);
#endif
#if defined(ERROR_SXS_FILE_HASH_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_FILE_HASH_MISMATCH);
#endif
#if defined(ERROR_SXS_POLICY_PARSE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_POLICY_PARSE_ERROR);
#endif
#if defined(ERROR_SXS_XML_E_MISSINGQUOTE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_MISSINGQUOTE);
#endif
#if defined(ERROR_SXS_XML_E_COMMENTSYNTAX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_COMMENTSYNTAX);
#endif
#if defined(ERROR_SXS_XML_E_BADSTARTNAMECHAR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_BADSTARTNAMECHAR);
#endif
#if defined(ERROR_SXS_XML_E_BADNAMECHAR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_BADNAMECHAR);
#endif
#if defined(ERROR_SXS_XML_E_BADCHARINSTRING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_BADCHARINSTRING);
#endif
#if defined(ERROR_SXS_XML_E_XMLDECLSYNTAX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_XMLDECLSYNTAX);
#endif
#if defined(ERROR_SXS_XML_E_BADCHARDATA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_BADCHARDATA);
#endif
#if defined(ERROR_SXS_XML_E_MISSINGWHITESPACE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_MISSINGWHITESPACE);
#endif
#if defined(ERROR_SXS_XML_E_EXPECTINGTAGEND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_EXPECTINGTAGEND);
#endif
#if defined(ERROR_SXS_XML_E_MISSINGSEMICOLON)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_MISSINGSEMICOLON);
#endif
#if defined(ERROR_SXS_XML_E_UNBALANCEDPAREN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_UNBALANCEDPAREN);
#endif
#if defined(ERROR_SXS_XML_E_INTERNALERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_INTERNALERROR);
#endif
#if defined(ERROR_SXS_XML_E_UNEXPECTED_WHITESPACE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_UNEXPECTED_WHITESPACE);
#endif
#if defined(ERROR_SXS_XML_E_INCOMPLETE_ENCODING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_INCOMPLETE_ENCODING);
#endif
#if defined(ERROR_SXS_XML_E_MISSING_PAREN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_MISSING_PAREN);
#endif
#if defined(ERROR_SXS_XML_E_EXPECTINGCLOSEQUOTE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_EXPECTINGCLOSEQUOTE);
#endif
#if defined(ERROR_SXS_XML_E_MULTIPLE_COLONS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_MULTIPLE_COLONS);
#endif
#if defined(ERROR_SXS_XML_E_INVALID_DECIMAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_INVALID_DECIMAL);
#endif
#if defined(ERROR_SXS_XML_E_INVALID_HEXIDECIMAL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_INVALID_HEXIDECIMAL);
#endif
#if defined(ERROR_SXS_XML_E_INVALID_UNICODE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_INVALID_UNICODE);
#endif
#if defined(ERROR_SXS_XML_E_WHITESPACEORQUESTIONMARK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_WHITESPACEORQUESTIONMARK);
#endif
#if defined(ERROR_SXS_XML_E_UNEXPECTEDENDTAG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_UNEXPECTEDENDTAG);
#endif
#if defined(ERROR_SXS_XML_E_UNCLOSEDTAG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_UNCLOSEDTAG);
#endif
#if defined(ERROR_SXS_XML_E_DUPLICATEATTRIBUTE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_DUPLICATEATTRIBUTE);
#endif
#if defined(ERROR_SXS_XML_E_MULTIPLEROOTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_MULTIPLEROOTS);
#endif
#if defined(ERROR_SXS_XML_E_INVALIDATROOTLEVEL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_INVALIDATROOTLEVEL);
#endif
#if defined(ERROR_SXS_XML_E_BADXMLDECL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_BADXMLDECL);
#endif
#if defined(ERROR_SXS_XML_E_MISSINGROOT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_MISSINGROOT);
#endif
#if defined(ERROR_SXS_XML_E_UNEXPECTEDEOF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_UNEXPECTEDEOF);
#endif
#if defined(ERROR_SXS_XML_E_BADPEREFINSUBSET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_BADPEREFINSUBSET);
#endif
#if defined(ERROR_SXS_XML_E_UNCLOSEDSTARTTAG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_UNCLOSEDSTARTTAG);
#endif
#if defined(ERROR_SXS_XML_E_UNCLOSEDENDTAG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_UNCLOSEDENDTAG);
#endif
#if defined(ERROR_SXS_XML_E_UNCLOSEDSTRING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_UNCLOSEDSTRING);
#endif
#if defined(ERROR_SXS_XML_E_UNCLOSEDCOMMENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_UNCLOSEDCOMMENT);
#endif
#if defined(ERROR_SXS_XML_E_UNCLOSEDDECL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_UNCLOSEDDECL);
#endif
#if defined(ERROR_SXS_XML_E_UNCLOSEDCDATA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_UNCLOSEDCDATA);
#endif
#if defined(ERROR_SXS_XML_E_RESERVEDNAMESPACE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_RESERVEDNAMESPACE);
#endif
#if defined(ERROR_SXS_XML_E_INVALIDENCODING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_INVALIDENCODING);
#endif
#if defined(ERROR_SXS_XML_E_INVALIDSWITCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_INVALIDSWITCH);
#endif
#if defined(ERROR_SXS_XML_E_BADXMLCASE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_BADXMLCASE);
#endif
#if defined(ERROR_SXS_XML_E_INVALID_STANDALONE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_INVALID_STANDALONE);
#endif
#if defined(ERROR_SXS_XML_E_UNEXPECTED_STANDALONE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_UNEXPECTED_STANDALONE);
#endif
#if defined(ERROR_SXS_XML_E_INVALID_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_INVALID_VERSION);
#endif
#if defined(ERROR_SXS_XML_E_MISSINGEQUALS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_XML_E_MISSINGEQUALS);
#endif
#if defined(ERROR_SXS_PROTECTION_RECOVERY_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_PROTECTION_RECOVERY_FAILED);
#endif
#if defined(ERROR_SXS_PROTECTION_PUBLIC_KEY_TOO_SHORT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_PROTECTION_PUBLIC_KEY_TOO_SHORT);
#endif
#if defined(ERROR_SXS_PROTECTION_CATALOG_NOT_VALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_PROTECTION_CATALOG_NOT_VALID);
#endif
#if defined(ERROR_SXS_UNTRANSLATABLE_HRESULT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_UNTRANSLATABLE_HRESULT);
#endif
#if defined(ERROR_SXS_PROTECTION_CATALOG_FILE_MISSING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_PROTECTION_CATALOG_FILE_MISSING);
#endif
#if defined(ERROR_SXS_MISSING_ASSEMBLY_IDENTITY_ATTRIBUTE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_MISSING_ASSEMBLY_IDENTITY_ATTRIBUTE);
#endif
#if defined(ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE_NAME);
#endif
#if defined(ERROR_SXS_ASSEMBLY_MISSING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_ASSEMBLY_MISSING);
#endif
#if defined(ERROR_SXS_CORRUPT_ACTIVATION_STACK)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_CORRUPT_ACTIVATION_STACK);
#endif
#if defined(ERROR_SXS_CORRUPTION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_CORRUPTION);
#endif
#if defined(ERROR_SXS_EARLY_DEACTIVATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_EARLY_DEACTIVATION);
#endif
#if defined(ERROR_SXS_INVALID_DEACTIVATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_INVALID_DEACTIVATION);
#endif
#if defined(ERROR_SXS_MULTIPLE_DEACTIVATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_MULTIPLE_DEACTIVATION);
#endif
#if defined(ERROR_SXS_PROCESS_TERMINATION_REQUESTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_PROCESS_TERMINATION_REQUESTED);
#endif
#if defined(ERROR_SXS_RELEASE_ACTIVATION_CONTEXT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_RELEASE_ACTIVATION_CONTEXT);
#endif
#if defined(ERROR_SXS_SYSTEM_DEFAULT_ACTIVATION_CONTEXT_EMPTY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_SYSTEM_DEFAULT_ACTIVATION_CONTEXT_EMPTY);
#endif
#if defined(ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_VALUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_VALUE);
#endif
#if defined(ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_NAME);
#endif
#if defined(ERROR_SXS_IDENTITY_DUPLICATE_ATTRIBUTE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_IDENTITY_DUPLICATE_ATTRIBUTE);
#endif
#if defined(ERROR_SXS_IDENTITY_PARSE_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_IDENTITY_PARSE_ERROR);
#endif
#if defined(ERROR_MALFORMED_SUBSTITUTION_STRING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MALFORMED_SUBSTITUTION_STRING);
#endif
#if defined(ERROR_SXS_INCORRECT_PUBLIC_KEY_TOKEN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_INCORRECT_PUBLIC_KEY_TOKEN);
#endif
#if defined(ERROR_UNMAPPED_SUBSTITUTION_STRING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_UNMAPPED_SUBSTITUTION_STRING);
#endif
#if defined(ERROR_SXS_ASSEMBLY_NOT_LOCKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_ASSEMBLY_NOT_LOCKED);
#endif
#if defined(ERROR_SXS_COMPONENT_STORE_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_COMPONENT_STORE_CORRUPT);
#endif
#if defined(ERROR_ADVANCED_INSTALLER_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_ADVANCED_INSTALLER_FAILED);
#endif
#if defined(ERROR_XML_ENCODING_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_XML_ENCODING_MISMATCH);
#endif
#if defined(ERROR_SXS_MANIFEST_IDENTITY_SAME_BUT_CONTENTS_DIFFERENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_MANIFEST_IDENTITY_SAME_BUT_CONTENTS_DIFFERENT);
#endif
#if defined(ERROR_SXS_IDENTITIES_DIFFERENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_IDENTITIES_DIFFERENT);
#endif
#if defined(ERROR_SXS_ASSEMBLY_IS_NOT_A_DEPLOYMENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_ASSEMBLY_IS_NOT_A_DEPLOYMENT);
#endif
#if defined(ERROR_SXS_FILE_NOT_PART_OF_ASSEMBLY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_FILE_NOT_PART_OF_ASSEMBLY);
#endif
#if defined(ERROR_SXS_MANIFEST_TOO_BIG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_MANIFEST_TOO_BIG);
#endif
#if defined(ERROR_SXS_SETTING_NOT_REGISTERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_SETTING_NOT_REGISTERED);
#endif
#if defined(ERROR_SXS_TRANSACTION_CLOSURE_INCOMPLETE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_TRANSACTION_CLOSURE_INCOMPLETE);
#endif
#if defined(ERROR_SMI_PRIMITIVE_INSTALLER_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SMI_PRIMITIVE_INSTALLER_FAILED);
#endif
#if defined(ERROR_GENERIC_COMMAND_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GENERIC_COMMAND_FAILED);
#endif
#if defined(ERROR_SXS_FILE_HASH_MISSING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SXS_FILE_HASH_MISSING);
#endif
#if defined(ERROR_EVT_INVALID_CHANNEL_PATH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_INVALID_CHANNEL_PATH);
#endif
#if defined(ERROR_EVT_INVALID_QUERY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_INVALID_QUERY);
#endif
#if defined(ERROR_EVT_PUBLISHER_METADATA_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_PUBLISHER_METADATA_NOT_FOUND);
#endif
#if defined(ERROR_EVT_EVENT_TEMPLATE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_EVENT_TEMPLATE_NOT_FOUND);
#endif
#if defined(ERROR_EVT_INVALID_PUBLISHER_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_INVALID_PUBLISHER_NAME);
#endif
#if defined(ERROR_EVT_INVALID_EVENT_DATA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_INVALID_EVENT_DATA);
#endif
#if defined(ERROR_EVT_CHANNEL_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_CHANNEL_NOT_FOUND);
#endif
#if defined(ERROR_EVT_MALFORMED_XML_TEXT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_MALFORMED_XML_TEXT);
#endif
#if defined(ERROR_EVT_SUBSCRIPTION_TO_DIRECT_CHANNEL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_SUBSCRIPTION_TO_DIRECT_CHANNEL);
#endif
#if defined(ERROR_EVT_CONFIGURATION_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_CONFIGURATION_ERROR);
#endif
#if defined(ERROR_EVT_QUERY_RESULT_STALE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_QUERY_RESULT_STALE);
#endif
#if defined(ERROR_EVT_QUERY_RESULT_INVALID_POSITION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_QUERY_RESULT_INVALID_POSITION);
#endif
#if defined(ERROR_EVT_NON_VALIDATING_MSXML)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_NON_VALIDATING_MSXML);
#endif
#if defined(ERROR_EVT_FILTER_ALREADYSCOPED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_FILTER_ALREADYSCOPED);
#endif
#if defined(ERROR_EVT_FILTER_NOTELTSET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_FILTER_NOTELTSET);
#endif
#if defined(ERROR_EVT_FILTER_INVARG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_FILTER_INVARG);
#endif
#if defined(ERROR_EVT_FILTER_INVTEST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_FILTER_INVTEST);
#endif
#if defined(ERROR_EVT_FILTER_INVTYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_FILTER_INVTYPE);
#endif
#if defined(ERROR_EVT_FILTER_PARSEERR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_FILTER_PARSEERR);
#endif
#if defined(ERROR_EVT_FILTER_UNSUPPORTEDOP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_FILTER_UNSUPPORTEDOP);
#endif
#if defined(ERROR_EVT_FILTER_UNEXPECTEDTOKEN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_FILTER_UNEXPECTEDTOKEN);
#endif
#if defined(ERROR_EVT_INVALID_OPERATION_OVER_ENABLED_DIRECT_CHANNEL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_INVALID_OPERATION_OVER_ENABLED_DIRECT_CHANNEL);
#endif
#if defined(ERROR_EVT_INVALID_CHANNEL_PROPERTY_VALUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_INVALID_CHANNEL_PROPERTY_VALUE);
#endif
#if defined(ERROR_EVT_INVALID_PUBLISHER_PROPERTY_VALUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_INVALID_PUBLISHER_PROPERTY_VALUE);
#endif
#if defined(ERROR_EVT_CHANNEL_CANNOT_ACTIVATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_CHANNEL_CANNOT_ACTIVATE);
#endif
#if defined(ERROR_EVT_FILTER_TOO_COMPLEX)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_FILTER_TOO_COMPLEX);
#endif
#if defined(ERROR_EVT_MESSAGE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_MESSAGE_NOT_FOUND);
#endif
#if defined(ERROR_EVT_MESSAGE_ID_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_MESSAGE_ID_NOT_FOUND);
#endif
#if defined(ERROR_EVT_UNRESOLVED_VALUE_INSERT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_UNRESOLVED_VALUE_INSERT);
#endif
#if defined(ERROR_EVT_UNRESOLVED_PARAMETER_INSERT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_UNRESOLVED_PARAMETER_INSERT);
#endif
#if defined(ERROR_EVT_MAX_INSERTS_REACHED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_MAX_INSERTS_REACHED);
#endif
#if defined(ERROR_EVT_EVENT_DEFINITION_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_EVENT_DEFINITION_NOT_FOUND);
#endif
#if defined(ERROR_EVT_MESSAGE_LOCALE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_MESSAGE_LOCALE_NOT_FOUND);
#endif
#if defined(ERROR_EVT_VERSION_TOO_OLD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_VERSION_TOO_OLD);
#endif
#if defined(ERROR_EVT_VERSION_TOO_NEW)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_VERSION_TOO_NEW);
#endif
#if defined(ERROR_EVT_CANNOT_OPEN_CHANNEL_OF_QUERY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_CANNOT_OPEN_CHANNEL_OF_QUERY);
#endif
#if defined(ERROR_EVT_PUBLISHER_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_PUBLISHER_DISABLED);
#endif
#if defined(ERROR_EVT_FILTER_OUT_OF_RANGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EVT_FILTER_OUT_OF_RANGE);
#endif
#if defined(ERROR_EC_SUBSCRIPTION_CANNOT_ACTIVATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EC_SUBSCRIPTION_CANNOT_ACTIVATE);
#endif
#if defined(ERROR_EC_LOG_DISABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EC_LOG_DISABLED);
#endif
#if defined(ERROR_EC_CIRCULAR_FORWARDING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EC_CIRCULAR_FORWARDING);
#endif
#if defined(ERROR_EC_CREDSTORE_FULL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EC_CREDSTORE_FULL);
#endif
#if defined(ERROR_EC_CRED_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EC_CRED_NOT_FOUND);
#endif
#if defined(ERROR_EC_NO_ACTIVE_CHANNEL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_EC_NO_ACTIVE_CHANNEL);
#endif
#if defined(ERROR_MUI_FILE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MUI_FILE_NOT_FOUND);
#endif
#if defined(ERROR_MUI_INVALID_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MUI_INVALID_FILE);
#endif
#if defined(ERROR_MUI_INVALID_RC_CONFIG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MUI_INVALID_RC_CONFIG);
#endif
#if defined(ERROR_MUI_INVALID_LOCALE_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MUI_INVALID_LOCALE_NAME);
#endif
#if defined(ERROR_MUI_INVALID_ULTIMATEFALLBACK_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MUI_INVALID_ULTIMATEFALLBACK_NAME);
#endif
#if defined(ERROR_MUI_FILE_NOT_LOADED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MUI_FILE_NOT_LOADED);
#endif
#if defined(ERROR_RESOURCE_ENUM_USER_STOP)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESOURCE_ENUM_USER_STOP);
#endif
#if defined(ERROR_MUI_INTLSETTINGS_UILANG_NOT_INSTALLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MUI_INTLSETTINGS_UILANG_NOT_INSTALLED);
#endif
#if defined(ERROR_MUI_INTLSETTINGS_INVALID_LOCALE_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MUI_INTLSETTINGS_INVALID_LOCALE_NAME);
#endif
#if defined(ERROR_MRM_RUNTIME_NO_DEFAULT_OR_NEUTRAL_RESOURCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_RUNTIME_NO_DEFAULT_OR_NEUTRAL_RESOURCE);
#endif
#if defined(ERROR_MRM_INVALID_PRICONFIG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_INVALID_PRICONFIG);
#endif
#if defined(ERROR_MRM_INVALID_FILE_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_INVALID_FILE_TYPE);
#endif
#if defined(ERROR_MRM_UNKNOWN_QUALIFIER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_UNKNOWN_QUALIFIER);
#endif
#if defined(ERROR_MRM_INVALID_QUALIFIER_VALUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_INVALID_QUALIFIER_VALUE);
#endif
#if defined(ERROR_MRM_NO_CANDIDATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_NO_CANDIDATE);
#endif
#if defined(ERROR_MRM_NO_MATCH_OR_DEFAULT_CANDIDATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_NO_MATCH_OR_DEFAULT_CANDIDATE);
#endif
#if defined(ERROR_MRM_RESOURCE_TYPE_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_RESOURCE_TYPE_MISMATCH);
#endif
#if defined(ERROR_MRM_DUPLICATE_MAP_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_DUPLICATE_MAP_NAME);
#endif
#if defined(ERROR_MRM_DUPLICATE_ENTRY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_DUPLICATE_ENTRY);
#endif
#if defined(ERROR_MRM_INVALID_RESOURCE_IDENTIFIER)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_INVALID_RESOURCE_IDENTIFIER);
#endif
#if defined(ERROR_MRM_FILEPATH_TOO_LONG)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_FILEPATH_TOO_LONG);
#endif
#if defined(ERROR_MRM_UNSUPPORTED_DIRECTORY_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_UNSUPPORTED_DIRECTORY_TYPE);
#endif
#if defined(ERROR_MRM_INVALID_PRI_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_INVALID_PRI_FILE);
#endif
#if defined(ERROR_MRM_NAMED_RESOURCE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_NAMED_RESOURCE_NOT_FOUND);
#endif
#if defined(ERROR_MRM_MAP_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_MAP_NOT_FOUND);
#endif
#if defined(ERROR_MRM_UNSUPPORTED_PROFILE_TYPE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_UNSUPPORTED_PROFILE_TYPE);
#endif
#if defined(ERROR_MRM_INVALID_QUALIFIER_OPERATOR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_INVALID_QUALIFIER_OPERATOR);
#endif
#if defined(ERROR_MRM_INDETERMINATE_QUALIFIER_VALUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_INDETERMINATE_QUALIFIER_VALUE);
#endif
#if defined(ERROR_MRM_AUTOMERGE_ENABLED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_AUTOMERGE_ENABLED);
#endif
#if defined(ERROR_MRM_TOO_MANY_RESOURCES)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_TOO_MANY_RESOURCES);
#endif
#if defined(ERROR_MRM_UNSUPPORTED_FILE_TYPE_FOR_MERGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_UNSUPPORTED_FILE_TYPE_FOR_MERGE);
#endif
#if defined(ERROR_MRM_UNSUPPORTED_FILE_TYPE_FOR_LOAD_UNLOAD_PRI_FILE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_UNSUPPORTED_FILE_TYPE_FOR_LOAD_UNLOAD_PRI_FILE);
#endif
#if defined(ERROR_MRM_NO_CURRENT_VIEW_ON_THREAD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_NO_CURRENT_VIEW_ON_THREAD);
#endif
#if defined(ERROR_DIFFERENT_PROFILE_RESOURCE_MANAGER_EXIST)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DIFFERENT_PROFILE_RESOURCE_MANAGER_EXIST);
#endif
#if defined(ERROR_OPERATION_NOT_ALLOWED_FROM_SYSTEM_COMPONENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_OPERATION_NOT_ALLOWED_FROM_SYSTEM_COMPONENT);
#endif
#if defined(ERROR_MRM_DIRECT_REF_TO_NON_DEFAULT_RESOURCE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_DIRECT_REF_TO_NON_DEFAULT_RESOURCE);
#endif
#if defined(ERROR_MRM_GENERATION_COUNT_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MRM_GENERATION_COUNT_MISMATCH);
#endif
#if defined(ERROR_PRI_MERGE_VERSION_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRI_MERGE_VERSION_MISMATCH);
#endif
#if defined(ERROR_PRI_MERGE_MISSING_SCHEMA)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRI_MERGE_MISSING_SCHEMA);
#endif
#if defined(ERROR_PRI_MERGE_LOAD_FILE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRI_MERGE_LOAD_FILE_FAILED);
#endif
#if defined(ERROR_PRI_MERGE_ADD_FILE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRI_MERGE_ADD_FILE_FAILED);
#endif
#if defined(ERROR_PRI_MERGE_WRITE_FILE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRI_MERGE_WRITE_FILE_FAILED);
#endif
#if defined(ERROR_PRI_MERGE_MULTIPLE_PACKAGE_FAMILIES_NOT_ALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRI_MERGE_MULTIPLE_PACKAGE_FAMILIES_NOT_ALLOWED);
#endif
#if defined(ERROR_PRI_MERGE_MULTIPLE_MAIN_PACKAGES_NOT_ALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRI_MERGE_MULTIPLE_MAIN_PACKAGES_NOT_ALLOWED);
#endif
#if defined(ERROR_PRI_MERGE_BUNDLE_PACKAGES_NOT_ALLOWED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRI_MERGE_BUNDLE_PACKAGES_NOT_ALLOWED);
#endif
#if defined(ERROR_PRI_MERGE_MAIN_PACKAGE_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRI_MERGE_MAIN_PACKAGE_REQUIRED);
#endif
#if defined(ERROR_PRI_MERGE_RESOURCE_PACKAGE_REQUIRED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRI_MERGE_RESOURCE_PACKAGE_REQUIRED);
#endif
#if defined(ERROR_PRI_MERGE_INVALID_FILE_NAME)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PRI_MERGE_INVALID_FILE_NAME);
#endif
#if defined(ERROR_MCA_INVALID_CAPABILITIES_STRING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MCA_INVALID_CAPABILITIES_STRING);
#endif
#if defined(ERROR_MCA_INVALID_VCP_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MCA_INVALID_VCP_VERSION);
#endif
#if defined(ERROR_MCA_MONITOR_VIOLATES_MCCS_SPECIFICATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MCA_MONITOR_VIOLATES_MCCS_SPECIFICATION);
#endif
#if defined(ERROR_MCA_MCCS_VERSION_MISMATCH)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MCA_MCCS_VERSION_MISMATCH);
#endif
#if defined(ERROR_MCA_UNSUPPORTED_MCCS_VERSION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MCA_UNSUPPORTED_MCCS_VERSION);
#endif
#if defined(ERROR_MCA_INTERNAL_ERROR)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MCA_INTERNAL_ERROR);
#endif
#if defined(ERROR_MCA_INVALID_TECHNOLOGY_TYPE_RETURNED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MCA_INVALID_TECHNOLOGY_TYPE_RETURNED);
#endif
#if defined(ERROR_MCA_UNSUPPORTED_COLOR_TEMPERATURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_MCA_UNSUPPORTED_COLOR_TEMPERATURE);
#endif
#if defined(ERROR_AMBIGUOUS_SYSTEM_DEVICE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_AMBIGUOUS_SYSTEM_DEVICE);
#endif
#if defined(ERROR_SYSTEM_DEVICE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYSTEM_DEVICE_NOT_FOUND);
#endif
#if defined(ERROR_HASH_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HASH_NOT_SUPPORTED);
#endif
#if defined(ERROR_HASH_NOT_PRESENT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_HASH_NOT_PRESENT);
#endif
#if defined(ERROR_SECONDARY_IC_PROVIDER_NOT_REGISTERED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SECONDARY_IC_PROVIDER_NOT_REGISTERED);
#endif
#if defined(ERROR_GPIO_CLIENT_INFORMATION_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GPIO_CLIENT_INFORMATION_INVALID);
#endif
#if defined(ERROR_GPIO_VERSION_NOT_SUPPORTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GPIO_VERSION_NOT_SUPPORTED);
#endif
#if defined(ERROR_GPIO_INVALID_REGISTRATION_PACKET)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GPIO_INVALID_REGISTRATION_PACKET);
#endif
#if defined(ERROR_GPIO_OPERATION_DENIED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GPIO_OPERATION_DENIED);
#endif
#if defined(ERROR_GPIO_INCOMPATIBLE_CONNECT_MODE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GPIO_INCOMPATIBLE_CONNECT_MODE);
#endif
#if defined(ERROR_GPIO_INTERRUPT_ALREADY_UNMASKED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_GPIO_INTERRUPT_ALREADY_UNMASKED);
#endif
#if defined(ERROR_CANNOT_SWITCH_RUNLEVEL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_CANNOT_SWITCH_RUNLEVEL);
#endif
#if defined(ERROR_INVALID_RUNLEVEL_SETTING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_RUNLEVEL_SETTING);
#endif
#if defined(ERROR_RUNLEVEL_SWITCH_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RUNLEVEL_SWITCH_TIMEOUT);
#endif
#if defined(ERROR_RUNLEVEL_SWITCH_AGENT_TIMEOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RUNLEVEL_SWITCH_AGENT_TIMEOUT);
#endif
#if defined(ERROR_RUNLEVEL_SWITCH_IN_PROGRESS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RUNLEVEL_SWITCH_IN_PROGRESS);
#endif
#if defined(ERROR_SERVICES_FAILED_AUTOSTART)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SERVICES_FAILED_AUTOSTART);
#endif
#if defined(ERROR_COM_TASK_STOP_PENDING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_COM_TASK_STOP_PENDING);
#endif
#if defined(ERROR_INSTALL_OPEN_PACKAGE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_OPEN_PACKAGE_FAILED);
#endif
#if defined(ERROR_INSTALL_PACKAGE_NOT_FOUND)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_PACKAGE_NOT_FOUND);
#endif
#if defined(ERROR_INSTALL_INVALID_PACKAGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_INVALID_PACKAGE);
#endif
#if defined(ERROR_INSTALL_RESOLVE_DEPENDENCY_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_RESOLVE_DEPENDENCY_FAILED);
#endif
#if defined(ERROR_INSTALL_OUT_OF_DISK_SPACE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_OUT_OF_DISK_SPACE);
#endif
#if defined(ERROR_INSTALL_NETWORK_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_NETWORK_FAILURE);
#endif
#if defined(ERROR_INSTALL_REGISTRATION_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_REGISTRATION_FAILURE);
#endif
#if defined(ERROR_INSTALL_DEREGISTRATION_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_DEREGISTRATION_FAILURE);
#endif
#if defined(ERROR_INSTALL_CANCEL)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_CANCEL);
#endif
#if defined(ERROR_INSTALL_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_FAILED);
#endif
#if defined(ERROR_REMOVE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_REMOVE_FAILED);
#endif
#if defined(ERROR_PACKAGE_ALREADY_EXISTS)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PACKAGE_ALREADY_EXISTS);
#endif
#if defined(ERROR_NEEDS_REMEDIATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NEEDS_REMEDIATION);
#endif
#if defined(ERROR_INSTALL_PREREQUISITE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_PREREQUISITE_FAILED);
#endif
#if defined(ERROR_PACKAGE_REPOSITORY_CORRUPTED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PACKAGE_REPOSITORY_CORRUPTED);
#endif
#if defined(ERROR_INSTALL_POLICY_FAILURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_POLICY_FAILURE);
#endif
#if defined(ERROR_PACKAGE_UPDATING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PACKAGE_UPDATING);
#endif
#if defined(ERROR_DEPLOYMENT_BLOCKED_BY_POLICY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEPLOYMENT_BLOCKED_BY_POLICY);
#endif
#if defined(ERROR_PACKAGES_IN_USE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PACKAGES_IN_USE);
#endif
#if defined(ERROR_RECOVERY_FILE_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RECOVERY_FILE_CORRUPT);
#endif
#if defined(ERROR_INVALID_STAGED_SIGNATURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INVALID_STAGED_SIGNATURE);
#endif
#if defined(ERROR_DELETING_EXISTING_APPLICATIONDATA_STORE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DELETING_EXISTING_APPLICATIONDATA_STORE_FAILED);
#endif
#if defined(ERROR_INSTALL_PACKAGE_DOWNGRADE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_PACKAGE_DOWNGRADE);
#endif
#if defined(ERROR_SYSTEM_NEEDS_REMEDIATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_SYSTEM_NEEDS_REMEDIATION);
#endif
#if defined(ERROR_APPX_INTEGRITY_FAILURE_CLR_NGEN)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_APPX_INTEGRITY_FAILURE_CLR_NGEN);
#endif
#if defined(ERROR_RESILIENCY_FILE_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_RESILIENCY_FILE_CORRUPT);
#endif
#if defined(ERROR_INSTALL_FIREWALL_SERVICE_NOT_RUNNING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_FIREWALL_SERVICE_NOT_RUNNING);
#endif
#if defined(ERROR_PACKAGE_MOVE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PACKAGE_MOVE_FAILED);
#endif
#if defined(ERROR_INSTALL_VOLUME_NOT_EMPTY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_VOLUME_NOT_EMPTY);
#endif
#if defined(ERROR_INSTALL_VOLUME_OFFLINE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_VOLUME_OFFLINE);
#endif
#if defined(ERROR_INSTALL_VOLUME_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_VOLUME_CORRUPT);
#endif
#if defined(ERROR_NEEDS_REGISTRATION)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_NEEDS_REGISTRATION);
#endif
#if defined(ERROR_INSTALL_WRONG_PROCESSOR_ARCHITECTURE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_WRONG_PROCESSOR_ARCHITECTURE);
#endif
#if defined(ERROR_DEV_SIDELOAD_LIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEV_SIDELOAD_LIMIT_EXCEEDED);
#endif
#if defined(ERROR_INSTALL_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE);
#endif
#if defined(ERROR_PACKAGE_NOT_SUPPORTED_ON_FILESYSTEM)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PACKAGE_NOT_SUPPORTED_ON_FILESYSTEM);
#endif
#if defined(ERROR_PACKAGE_MOVE_BLOCKED_BY_STREAMING)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PACKAGE_MOVE_BLOCKED_BY_STREAMING);
#endif
#if defined(ERROR_INSTALL_OPTIONAL_PACKAGE_APPLICATIONID_NOT_UNIQUE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_OPTIONAL_PACKAGE_APPLICATIONID_NOT_UNIQUE);
#endif
#if defined(ERROR_PACKAGE_STAGING_ONHOLD)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PACKAGE_STAGING_ONHOLD);
#endif
#if defined(ERROR_INSTALL_INVALID_RELATED_SET_UPDATE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_INVALID_RELATED_SET_UPDATE);
#endif
#if defined(ERROR_INSTALL_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE_FULLTRUST_CAPABILITY)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_INSTALL_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE_FULLTRUST_CAPABILITY);
#endif
#if defined(ERROR_DEPLOYMENT_BLOCKED_BY_USER_LOG_OFF)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_DEPLOYMENT_BLOCKED_BY_USER_LOG_OFF);
#endif
#if defined(ERROR_PROVISION_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE_PROVISIONED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PROVISION_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE_PROVISIONED);
#endif
#if defined(ERROR_PACKAGES_REPUTATION_CHECK_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PACKAGES_REPUTATION_CHECK_FAILED);
#endif
#if defined(ERROR_PACKAGES_REPUTATION_CHECK_TIMEDOUT)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_PACKAGES_REPUTATION_CHECK_TIMEDOUT);
#endif
#if defined(APPMODEL_ERROR_NO_PACKAGE)
    BOOST_WINAPI_TEST_CONSTANT(APPMODEL_ERROR_NO_PACKAGE);
#endif
#if defined(APPMODEL_ERROR_PACKAGE_RUNTIME_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(APPMODEL_ERROR_PACKAGE_RUNTIME_CORRUPT);
#endif
#if defined(APPMODEL_ERROR_PACKAGE_IDENTITY_CORRUPT)
    BOOST_WINAPI_TEST_CONSTANT(APPMODEL_ERROR_PACKAGE_IDENTITY_CORRUPT);
#endif
#if defined(APPMODEL_ERROR_NO_APPLICATION)
    BOOST_WINAPI_TEST_CONSTANT(APPMODEL_ERROR_NO_APPLICATION);
#endif
#if defined(APPMODEL_ERROR_DYNAMIC_PROPERTY_READ_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(APPMODEL_ERROR_DYNAMIC_PROPERTY_READ_FAILED);
#endif
#if defined(APPMODEL_ERROR_DYNAMIC_PROPERTY_INVALID)
    BOOST_WINAPI_TEST_CONSTANT(APPMODEL_ERROR_DYNAMIC_PROPERTY_INVALID);
#endif
#if defined(APPMODEL_ERROR_PACKAGE_NOT_AVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(APPMODEL_ERROR_PACKAGE_NOT_AVAILABLE);
#endif
#if defined(ERROR_STATE_LOAD_STORE_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_LOAD_STORE_FAILED);
#endif
#if defined(ERROR_STATE_GET_VERSION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_GET_VERSION_FAILED);
#endif
#if defined(ERROR_STATE_SET_VERSION_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_SET_VERSION_FAILED);
#endif
#if defined(ERROR_STATE_STRUCTURED_RESET_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_STRUCTURED_RESET_FAILED);
#endif
#if defined(ERROR_STATE_OPEN_CONTAINER_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_OPEN_CONTAINER_FAILED);
#endif
#if defined(ERROR_STATE_CREATE_CONTAINER_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_CREATE_CONTAINER_FAILED);
#endif
#if defined(ERROR_STATE_DELETE_CONTAINER_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_DELETE_CONTAINER_FAILED);
#endif
#if defined(ERROR_STATE_READ_SETTING_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_READ_SETTING_FAILED);
#endif
#if defined(ERROR_STATE_WRITE_SETTING_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_WRITE_SETTING_FAILED);
#endif
#if defined(ERROR_STATE_DELETE_SETTING_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_DELETE_SETTING_FAILED);
#endif
#if defined(ERROR_STATE_QUERY_SETTING_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_QUERY_SETTING_FAILED);
#endif
#if defined(ERROR_STATE_READ_COMPOSITE_SETTING_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_READ_COMPOSITE_SETTING_FAILED);
#endif
#if defined(ERROR_STATE_WRITE_COMPOSITE_SETTING_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_WRITE_COMPOSITE_SETTING_FAILED);
#endif
#if defined(ERROR_STATE_ENUMERATE_CONTAINER_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_ENUMERATE_CONTAINER_FAILED);
#endif
#if defined(ERROR_STATE_ENUMERATE_SETTINGS_FAILED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_ENUMERATE_SETTINGS_FAILED);
#endif
#if defined(ERROR_STATE_COMPOSITE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_COMPOSITE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED);
#endif
#if defined(ERROR_STATE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED);
#endif
#if defined(ERROR_STATE_SETTING_NAME_SIZE_LIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_SETTING_NAME_SIZE_LIMIT_EXCEEDED);
#endif
#if defined(ERROR_STATE_CONTAINER_NAME_SIZE_LIMIT_EXCEEDED)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_STATE_CONTAINER_NAME_SIZE_LIMIT_EXCEEDED);
#endif
#if defined(ERROR_API_UNAVAILABLE)
    BOOST_WINAPI_TEST_CONSTANT(ERROR_API_UNAVAILABLE);
#endif
#if defined(STORE_ERROR_UNLICENSED)
    BOOST_WINAPI_TEST_CONSTANT(STORE_ERROR_UNLICENSED);
#endif
#if defined(STORE_ERROR_UNLICENSED_USER)
    BOOST_WINAPI_TEST_CONSTANT(STORE_ERROR_UNLICENSED_USER);
#endif
#if defined(STORE_ERROR_PENDING_COM_TRANSACTION)
    BOOST_WINAPI_TEST_CONSTANT(STORE_ERROR_PENDING_COM_TRANSACTION);
#endif
#if defined(STORE_ERROR_LICENSE_REVOKED)
    BOOST_WINAPI_TEST_CONSTANT(STORE_ERROR_LICENSE_REVOKED);
#endif

    return boost::report_errors();
}
