<!-- HTML header for doxygen 1.8.13-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=9"/>
    <meta name="generator" content="Doxygen 1.8.15"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Generic Image Library: planar_pixel_reference&lt; ChannelReference, ColorSpace &gt; Struct Template Reference</title>
    <link href="tabs.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-boost.css" rel="stylesheet" type="text/css"/>
  </head>
  <body>
    <div class="boost-header">
      <table border="0" cellpadding="7" cellspacing="0" width="100%" summary="header">
	<tr>
	  <td valign="top" width="300">
            <h3><a href="../index.html"><img alt="Boost GIL" src="../_static/gil.png" border="0"></a></h3>
	  </td>
	  <td ><h1 align="center"><a href="../index.html"></a></h1></td>
	  <td></td>
	</tr>
      </table>
    </div>
    <hr/>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceboost.html">boost</a></li><li class="navelem"><b>gil</b></li><li class="navelem"><a class="el" href="structboost_1_1gil_1_1planar__pixel__reference.html">planar_pixel_reference</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structboost_1_1gil_1_1planar__pixel__reference-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">planar_pixel_reference&lt; ChannelReference, ColorSpace &gt; Struct Template Reference<div class="ingroups"><a class="el" href="group___pixel.html">Pixel</a> &raquo; <a class="el" href="group___pixel_model.html">Models</a> &raquo; <a class="el" href="group___pixel_model_planar_ref.html">planar_pixel_reference</a><a class="el" href="group___color_base.html">ColorBase</a> &raquo; <a class="el" href="group___color_base_model.html">Models</a> &raquo;  &#124; <a class="el" href="group___color_base_model_planar_ref.html">planar_pixel_reference</a><a class="el" href="group___pixel_based.html">PixelBased</a> &raquo;  &#124; <a class="el" href="group___pixel_based_model.html">Models</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>A reference proxy to a planar pixel.  
 <a href="structboost_1_1gil_1_1planar__pixel__reference.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="planar__pixel__reference_8hpp_source.html">planar_pixel_reference.hpp</a>&gt;</code></p>

<p>Inherits homogeneous_color_base&lt; Element, Layout, K &gt;.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:aa58b25ebd200242c9ac69d6277535a0f"><td class="memItemLeft" align="right" valign="top"><a id="aa58b25ebd200242c9ac69d6277535a0f"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>parent_t</b> = detail::homogeneous_color_base&lt; ChannelReference, <a class="el" href="structboost_1_1gil_1_1layout.html">layout</a>&lt; ColorSpace &gt;, mp11::mp_size&lt; ColorSpace &gt;::value &gt;</td></tr>
<tr class="separator:aa58b25ebd200242c9ac69d6277535a0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a555a9845ae172f4bba280ee40e5bac00"><td class="memItemLeft" align="right" valign="top"><a id="a555a9845ae172f4bba280ee40e5bac00"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>value_type</b> = <a class="el" href="structboost_1_1gil_1_1pixel.html">pixel</a>&lt; channel_t, <a class="el" href="structboost_1_1gil_1_1layout.html">layout</a>&lt; ColorSpace &gt; &gt;</td></tr>
<tr class="separator:a555a9845ae172f4bba280ee40e5bac00"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a85cb80ed1553967ad2c639a01f5886dd"><td class="memItemLeft" align="right" valign="top"><a id="a85cb80ed1553967ad2c639a01f5886dd"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>reference</b> = <a class="el" href="structboost_1_1gil_1_1planar__pixel__reference.html">planar_pixel_reference</a>&lt; ChannelReference, ColorSpace &gt;</td></tr>
<tr class="separator:a85cb80ed1553967ad2c639a01f5886dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7402622b02382ce4ca74caa76db55e47"><td class="memItemLeft" align="right" valign="top"><a id="a7402622b02382ce4ca74caa76db55e47"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>const_reference</b> = <a class="el" href="structboost_1_1gil_1_1planar__pixel__reference.html">planar_pixel_reference</a>&lt; channel_const_reference, ColorSpace &gt;</td></tr>
<tr class="separator:a7402622b02382ce4ca74caa76db55e47"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ab85a6656613145874834ed272fed7f76"><td class="memItemLeft" align="right" valign="top"><a id="ab85a6656613145874834ed272fed7f76"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>planar_pixel_reference</b> (ChannelReference v0, ChannelReference v1)</td></tr>
<tr class="separator:ab85a6656613145874834ed272fed7f76"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac748457647ef60e3058dd9a777a659f3"><td class="memItemLeft" align="right" valign="top"><a id="ac748457647ef60e3058dd9a777a659f3"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>planar_pixel_reference</b> (ChannelReference v0, ChannelReference v1, ChannelReference v2)</td></tr>
<tr class="separator:ac748457647ef60e3058dd9a777a659f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1150133adc3a4f2edc3d3bd9dde477b1"><td class="memItemLeft" align="right" valign="top"><a id="a1150133adc3a4f2edc3d3bd9dde477b1"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>planar_pixel_reference</b> (ChannelReference v0, ChannelReference v1, ChannelReference v2, ChannelReference v3)</td></tr>
<tr class="separator:a1150133adc3a4f2edc3d3bd9dde477b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6074ff79afc3bc5206fa2008ff959886"><td class="memItemLeft" align="right" valign="top"><a id="a6074ff79afc3bc5206fa2008ff959886"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>planar_pixel_reference</b> (ChannelReference v0, ChannelReference v1, ChannelReference v2, ChannelReference v3, ChannelReference v4)</td></tr>
<tr class="separator:a6074ff79afc3bc5206fa2008ff959886"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a326177c672b834ebae1f606d092db69d"><td class="memItemLeft" align="right" valign="top"><a id="a326177c672b834ebae1f606d092db69d"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>planar_pixel_reference</b> (ChannelReference v0, ChannelReference v1, ChannelReference v2, ChannelReference v3, ChannelReference v4, ChannelReference v5)</td></tr>
<tr class="separator:a326177c672b834ebae1f606d092db69d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7ac169b22308d88e887c184884b7503"><td class="memItemLeft" align="right" valign="top"><a id="ac7ac169b22308d88e887c184884b7503"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>planar_pixel_reference</b> (<a class="el" href="structboost_1_1gil_1_1planar__pixel__reference.html">planar_pixel_reference</a> const &amp;p)</td></tr>
<tr class="separator:ac7ac169b22308d88e887c184884b7503"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba8c88de90b8e2dc7756789f39803eba"><td class="memItemLeft" align="right" valign="top"><a id="aba8c88de90b8e2dc7756789f39803eba"></a>
auto&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (<a class="el" href="structboost_1_1gil_1_1planar__pixel__reference.html">planar_pixel_reference</a> const &amp;p) const -&gt; <a class="el" href="structboost_1_1gil_1_1planar__pixel__reference.html">planar_pixel_reference</a> const &amp;</td></tr>
<tr class="separator:aba8c88de90b8e2dc7756789f39803eba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af3f63ccd3ad9904cc7df630d342f17c5"><td class="memTemplParams" colspan="2"><a id="af3f63ccd3ad9904cc7df630d342f17c5"></a>
template&lt;typename Pixel &gt; </td></tr>
<tr class="memitem:af3f63ccd3ad9904cc7df630d342f17c5"><td class="memTemplItemLeft" align="right" valign="top">&#160;</td><td class="memTemplItemRight" valign="bottom"><b>planar_pixel_reference</b> (Pixel const &amp;p)</td></tr>
<tr class="separator:af3f63ccd3ad9904cc7df630d342f17c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6683e7afb9f3d8e1e44a9e95d76fe2fa"><td class="memTemplParams" colspan="2"><a id="a6683e7afb9f3d8e1e44a9e95d76fe2fa"></a>
template&lt;typename Pixel &gt; </td></tr>
<tr class="memitem:a6683e7afb9f3d8e1e44a9e95d76fe2fa"><td class="memTemplItemLeft" align="right" valign="top">auto&#160;</td><td class="memTemplItemRight" valign="bottom"><b>operator=</b> (Pixel const &amp;p) const -&gt; <a class="el" href="structboost_1_1gil_1_1planar__pixel__reference.html">planar_pixel_reference</a> const &amp;</td></tr>
<tr class="separator:a6683e7afb9f3d8e1e44a9e95d76fe2fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9236ce4ebec269c382f8d9d7681fa0d8"><td class="memTemplParams" colspan="2"><a id="a9236ce4ebec269c382f8d9d7681fa0d8"></a>
template&lt;typename ChannelV , typename Mapping &gt; </td></tr>
<tr class="memitem:a9236ce4ebec269c382f8d9d7681fa0d8"><td class="memTemplItemLeft" align="right" valign="top">&#160;</td><td class="memTemplItemRight" valign="bottom"><b>planar_pixel_reference</b> (<a class="el" href="structboost_1_1gil_1_1pixel.html">pixel</a>&lt; ChannelV, <a class="el" href="structboost_1_1gil_1_1layout.html">layout</a>&lt; ColorSpace, Mapping &gt;&gt; &amp;p)</td></tr>
<tr class="separator:a9236ce4ebec269c382f8d9d7681fa0d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a72ac5f7f127cd6d6bd0c5d38e8647bcd"><td class="memTemplParams" colspan="2"><a id="a72ac5f7f127cd6d6bd0c5d38e8647bcd"></a>
template&lt;typename ChannelPtr &gt; </td></tr>
<tr class="memitem:a72ac5f7f127cd6d6bd0c5d38e8647bcd"><td class="memTemplItemLeft" align="right" valign="top">&#160;</td><td class="memTemplItemRight" valign="bottom"><b>planar_pixel_reference</b> (<a class="el" href="structboost_1_1gil_1_1planar__pixel__iterator.html">planar_pixel_iterator</a>&lt; ChannelPtr, ColorSpace &gt; const &amp;p, std::ptrdiff_t diff)</td></tr>
<tr class="separator:a72ac5f7f127cd6d6bd0c5d38e8647bcd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ee168419936700a6db322af16b6fa56"><td class="memTemplParams" colspan="2"><a id="a2ee168419936700a6db322af16b6fa56"></a>
template&lt;typename Pixel &gt; </td></tr>
<tr class="memitem:a2ee168419936700a6db322af16b6fa56"><td class="memTemplItemLeft" align="right" valign="top">bool&#160;</td><td class="memTemplItemRight" valign="bottom"><b>operator==</b> (Pixel const &amp;p) const</td></tr>
<tr class="separator:a2ee168419936700a6db322af16b6fa56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e84b33a61c27a8ca0d4cfd1d17a3ce7"><td class="memTemplParams" colspan="2"><a id="a7e84b33a61c27a8ca0d4cfd1d17a3ce7"></a>
template&lt;typename Pixel &gt; </td></tr>
<tr class="memitem:a7e84b33a61c27a8ca0d4cfd1d17a3ce7"><td class="memTemplItemLeft" align="right" valign="top">bool&#160;</td><td class="memTemplItemRight" valign="bottom"><b>operator!=</b> (Pixel const &amp;p) const</td></tr>
<tr class="separator:a7e84b33a61c27a8ca0d4cfd1d17a3ce7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a43fcae62467ed761b142a423a3c8fbf6"><td class="memItemLeft" align="right" valign="top"><a id="a43fcae62467ed761b142a423a3c8fbf6"></a>
auto&#160;</td><td class="memItemRight" valign="bottom"><b>operator[]</b> (std::size_t i) const -&gt; ChannelReference</td></tr>
<tr class="separator:a43fcae62467ed761b142a423a3c8fbf6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3717bd7d088f0e11bb897704be7ee4e4"><td class="memItemLeft" align="right" valign="top"><a id="a3717bd7d088f0e11bb897704be7ee4e4"></a>
auto&#160;</td><td class="memItemRight" valign="bottom"><b>operator-&gt;</b> () const -&gt; <a class="el" href="structboost_1_1gil_1_1planar__pixel__reference.html">planar_pixel_reference</a> const *</td></tr>
<tr class="separator:a3717bd7d088f0e11bb897704be7ee4e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:ada04709db53ded2c170444a3f1c4e5d4"><td class="memItemLeft" align="right" valign="top"><a id="ada04709db53ded2c170444a3f1c4e5d4"></a>
static constexpr bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_mutable</b> = channel_traits&lt;ChannelReference&gt;::is_mutable</td></tr>
<tr class="separator:ada04709db53ded2c170444a3f1c4e5d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;typename ChannelReference, typename ColorSpace&gt;<br />
struct boost::gil::planar_pixel_reference&lt; ChannelReference, ColorSpace &gt;</h3>

<p>A reference proxy to a planar pixel. </p>
<p>A reference to a planar pixel is a proxy class containing references to each of the corresponding channels. Models: <a class="el" href="structboost_1_1gil_1_1_homogeneous_color_base_concept.html" title="Color base whose elements all have the same type.">HomogeneousColorBaseConcept</a>, <a class="el" href="structboost_1_1gil_1_1_homogeneous_pixel_concept.html" title="Homogeneous pixel concept.">HomogeneousPixelConcept</a></p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">ChannelReference</td><td>A channel reference, either const or mutable </td></tr>
    <tr><td class="paramname">ColorSpace</td><td></td></tr>
  </table>
  </dd>
</dl>
</div><hr/>The documentation for this struct was generated from the following files:<ul>
<li><a class="el" href="metafunctions_8hpp_source.html">metafunctions.hpp</a></li>
<li><a class="el" href="planar__pixel__reference_8hpp_source.html">planar_pixel_reference.hpp</a></li>
</ul>
</div><!-- contents -->
     <!-- HTML footer for doxygen 1.8.13-->
     <!-- start footer part -->
     <hr class="footer"/>
     <address class="footer">
      <small>
        Generated by &#160;<a href="http://www.doxygen.org/index.html">doxygen</a> 1.8.15
      </small>
    </address>
  </body>
</html>
