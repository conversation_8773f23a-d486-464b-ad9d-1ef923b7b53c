<!-- HTML header for doxygen 1.8.13-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=9"/>
    <meta name="generator" content="Doxygen 1.8.15"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Generic Image Library: base.hpp Source File</title>
    <link href="tabs.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-boost.css" rel="stylesheet" type="text/css"/>
  </head>
  <body>
    <div class="boost-header">
      <table border="0" cellpadding="7" cellspacing="0" width="100%" summary="header">
	<tr>
	  <td valign="top" width="300">
            <h3><a href="../index.html"><img alt="Boost GIL" src="../_static/gil.png" border="0"></a></h3>
	  </td>
	  <td ><h1 align="center"><a href="../index.html"></a></h1></td>
	  <td></td>
	</tr>
      </table>
    </div>
    <hr/>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_1878a3f4746a95c6aad317458cc7ef80.html">boost</a></li><li class="navelem"><a class="el" href="dir_df4750f408086f9b9c1b5ee4251365ff.html">gil</a></li><li class="navelem"><a class="el" href="dir_80930c1173f2c0438c68e99be5d8d1e3.html">io</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">base.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">// Copyright 2007-2008 Christian Henning, Andreas Pokorny, Lubomir Bourdev</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment">// Distributed under the Boost Software License, Version 1.0</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">// See accompanying file LICENSE_1_0.txt or copy at</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment">// http://www.boost.org/LICENSE_1_0.txt</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#ifndef BOOST_GIL_IO_BASE_HPP</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#define BOOST_GIL_IO_BASE_HPP</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;</div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#include &lt;boost/gil/extension/toolbox/toolbox.hpp&gt;</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;</div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#include &lt;boost/gil/bit_aligned_pixel_reference.hpp&gt;</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#include &lt;boost/gil/bit_aligned_pixel_iterator.hpp&gt;</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#include &lt;boost/gil/color_convert.hpp&gt;</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &lt;boost/gil/utilities.hpp&gt;</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &lt;boost/gil/io/error.hpp&gt;</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &lt;boost/gil/io/typedefs.hpp&gt;</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &lt;istream&gt;</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &lt;ostream&gt;</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &lt;type_traits&gt;</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &lt;vector&gt;</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceboost.html">boost</a> { <span class="keyword">namespace </span>gil {</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="keyword">struct </span>format_tag {};</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="keyword">template</span>&lt; <span class="keyword">typename</span> Property &gt;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">struct </span>property_base</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;{</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;    <span class="keyword">using</span> type = Property;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;};</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="keyword">template</span>&lt;<span class="keyword">typename</span> FormatTag&gt;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="keyword">struct </span>is_format_tag : std::is_base_of&lt;format_tag, FormatTag&gt; {};</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">struct </span>image_read_settings_base</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;{</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="keyword">protected</span>:</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;    image_read_settings_base()</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;    : _top_left( 0, 0 )</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;    , _dim     ( 0, 0 )</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;    {}</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;    image_read_settings_base( point_t <span class="keyword">const</span>&amp; top_left</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;                            , point_t <span class="keyword">const</span>&amp; dim</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;                            )</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;    : _top_left( top_left )</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;    , _dim     ( dim      )</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;    {}</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;    <span class="keywordtype">void</span> set( point_t <span class="keyword">const</span>&amp; top_left</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;            , point_t <span class="keyword">const</span>&amp; dim</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;            )</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    {</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;        _top_left = top_left;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;        _dim      = dim;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;    }</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;    point_t _top_left;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;    point_t _dim;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;};</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;<span class="comment">// Depending on image type the parameter Pixel can be a reference type</span></div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;<span class="comment">// for bit_aligned images or a pixel for byte images.</span></div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1is__read__supported.html">   78</a></span>&#160;<span class="keyword">template</span>&lt; <span class="keyword">typename</span> Pixel, <span class="keyword">typename</span> FormatTag &gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1is__read__supported.html">is_read_supported</a> {};</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;<span class="keyword">template</span>&lt; <span class="keyword">typename</span> Pixel, <span class="keyword">typename</span> FormatTag &gt; <span class="keyword">struct </span>is_write_supported {};</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;<span class="keyword">namespace </span>detail {</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;<span class="keyword">template</span>&lt; <span class="keyword">typename</span> Property &gt;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;<span class="keyword">struct </span>property_base</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;{</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    <span class="keyword">using</span> type = Property;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;};</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;} <span class="comment">// namespace detail</span></div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;<span class="keyword">struct </span>read_support_true  { <span class="keyword">static</span> constexpr <span class="keywordtype">bool</span> is_supported = <span class="keyword">true</span>; };</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;<span class="keyword">struct </span>read_support_false { <span class="keyword">static</span> constexpr <span class="keywordtype">bool</span> is_supported = <span class="keyword">false</span>; };</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;<span class="keyword">struct </span>write_support_true { <span class="keyword">static</span> constexpr <span class="keywordtype">bool</span> is_supported = <span class="keyword">true</span>; };</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;<span class="keyword">struct </span>write_support_false{ <span class="keyword">static</span> constexpr <span class="keywordtype">bool</span> is_supported = <span class="keyword">false</span>; };</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;<span class="keyword">class </span>no_log {};</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="keyword">template</span>&lt; <span class="keyword">typename</span> Device, <span class="keyword">typename</span> FormatTag &gt; <span class="keyword">struct </span>reader_backend;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;<span class="keyword">template</span>&lt; <span class="keyword">typename</span> Device, <span class="keyword">typename</span> FormatTag &gt; <span class="keyword">struct </span>writer_backend;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;<span class="keyword">template</span>&lt; <span class="keyword">typename</span> FormatTag &gt; <span class="keyword">struct </span>image_read_info;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="keyword">template</span>&lt; <span class="keyword">typename</span> FormatTag &gt; <span class="keyword">struct </span>image_read_settings;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;<span class="keyword">template</span>&lt; <span class="keyword">typename</span> FormatTag, <span class="keyword">typename</span> Log = no_log &gt; <span class="keyword">struct </span>image_write_info;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;} <span class="comment">// namespace gil</span></div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;} <span class="comment">// namespace boost</span></div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;<span class="preprocessor">#endif</span></div><div class="ttc" id="namespaceboost_html"><div class="ttname"><a href="namespaceboost.html">boost</a></div><div class="ttdoc">defined(BOOST_NO_CXX17_HDR_MEMORY_RESOURCE)</div><div class="ttdef"><b>Definition:</b> algorithm.hpp:36</div></div>
<div class="ttc" id="structboost_1_1gil_1_1is__read__supported_html"><div class="ttname"><a href="structboost_1_1gil_1_1is__read__supported.html">boost::gil::is_read_supported</a></div><div class="ttdef"><b>Definition:</b> base.hpp:78</div></div>
</div><!-- fragment --></div><!-- contents -->
     <!-- HTML footer for doxygen 1.8.13-->
     <!-- start footer part -->
     <hr class="footer"/>
     <address class="footer">
      <small>
        Generated by &#160;<a href="http://www.doxygen.org/index.html">doxygen</a> 1.8.15
      </small>
    </address>
  </body>
</html>
