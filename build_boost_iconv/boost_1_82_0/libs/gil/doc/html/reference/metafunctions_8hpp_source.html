<!-- HTML header for doxygen 1.8.13-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=9"/>
    <meta name="generator" content="Doxygen 1.8.15"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Generic Image Library: metafunctions.hpp Source File</title>
    <link href="tabs.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-boost.css" rel="stylesheet" type="text/css"/>
  </head>
  <body>
    <div class="boost-header">
      <table border="0" cellpadding="7" cellspacing="0" width="100%" summary="header">
	<tr>
	  <td valign="top" width="300">
            <h3><a href="../index.html"><img alt="Boost GIL" src="../_static/gil.png" border="0"></a></h3>
	  </td>
	  <td ><h1 align="center"><a href="../index.html"></a></h1></td>
	  <td></td>
	</tr>
      </table>
    </div>
    <hr/>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_1878a3f4746a95c6aad317458cc7ef80.html">boost</a></li><li class="navelem"><a class="el" href="dir_df4750f408086f9b9c1b5ee4251365ff.html">gil</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">metafunctions.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">// Copyright 2005-2007 Adobe Systems Incorporated</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">// Copyright 2021 Pranam Lashkari &lt;<EMAIL>&gt;</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">// Distributed under the Boost Software License, Version 1.0</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment">// See accompanying file LICENSE_1_0.txt or copy at</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment">// http://www.boost.org/LICENSE_1_0.txt</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#ifndef BOOST_GIL_METAFUNCTIONS_HPP</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#define BOOST_GIL_METAFUNCTIONS_HPP</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;</div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#include &lt;boost/gil/channel.hpp&gt;</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#include &lt;boost/gil/dynamic_step.hpp&gt;</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#include &lt;boost/gil/concepts.hpp&gt;</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#include &lt;boost/gil/concepts/detail/type_traits.hpp&gt;</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &lt;boost/gil/detail/mp11.hpp&gt;</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &lt;iterator&gt;</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &lt;type_traits&gt;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceboost.html">boost</a> { <span class="keyword">namespace </span>gil {</div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment">// forward declarations</span></div><div class="line"><a name="l00024"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1pixel.html">   24</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1pixel.html">pixel</a>;</div><div class="line"><a name="l00025"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1packed__pixel.html">   25</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField,<span class="keyword">typename</span> ChannelRefs,<span class="keyword">typename</span> Layout&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1packed__pixel.html">packed_pixel</a>;</div><div class="line"><a name="l00026"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1planar__pixel__reference.html">   26</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> C&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1planar__pixel__reference.html">planar_pixel_reference</a>;</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> IC, <span class="keyword">typename</span> C&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1planar__pixel__iterator.html">planar_pixel_iterator</a>;</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> I&gt; <span class="keyword">class </span><a class="code" href="classboost_1_1gil_1_1memory__based__step__iterator.html">memory_based_step_iterator</a>;</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> I&gt; <span class="keyword">class </span><a class="code" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> L&gt; <span class="keyword">class </span><a class="code" href="classboost_1_1gil_1_1image__view.html">image_view</a>;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Pixel, <span class="keywordtype">bool</span> IsPlanar = false, <span class="keyword">typename</span> Alloc=std::allocator&lt;<span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>&gt; &gt; <span class="keyword">class </span><a class="code" href="classboost_1_1gil_1_1image.html">image</a>;</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1channel__type.html">channel_type</a>;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt; <span class="keyword">struct </span>color_space_type;</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt; <span class="keyword">struct </span>channel_mapping_type;</div><div class="line"><a name="l00035"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1is__iterator__adaptor.html">   35</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> It&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1is__iterator__adaptor.html">is_iterator_adaptor</a>;</div><div class="line"><a name="l00036"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1iterator__adaptor__get__base.html">   36</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> It&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__adaptor__get__base.html">iterator_adaptor_get_base</a>;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keyword">typename</span> ChannelBitSizes, <span class="keyword">typename</span> Layout, <span class="keywordtype">bool</span> IsMutable&gt; <span class="keyword">struct </span>bit_aligned_pixel_reference;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> PixelRef&gt;</div><div class="line"><a name="l00058"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1pixel__reference__is__basic.html">   58</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1pixel__reference__is__basic.html">pixel_reference_is_basic</a> : <span class="keyword">public</span> std::false_type {};</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1pixel__reference__is__basic.html">pixel_reference_is_basic</a>&lt;<a class="code" href="structboost_1_1gil_1_1pixel.html">pixel</a>&lt;T, L&gt;&amp;&gt; : std::true_type {};</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;<span class="keyword">struct </span>pixel_reference_is_basic&lt;const pixel&lt;T, L&gt;&amp;&gt; : std::true_type {};</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> TR, <span class="keyword">typename</span> CS&gt;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;<span class="keyword">struct </span>pixel_reference_is_basic&lt;planar_pixel_reference&lt;TR, CS&gt;&gt; : std::true_type {};</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> TR, <span class="keyword">typename</span> CS&gt;</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;<span class="keyword">struct </span>pixel_reference_is_basic&lt;const planar_pixel_reference&lt;TR, CS&gt;&gt; : std::true_type {};</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Iterator&gt;</div><div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1iterator__is__basic.html">   76</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__is__basic.html">iterator_is_basic</a> : std::false_type {};</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt;</div><div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1iterator__is__basic_3_01pixel_3_01_t_00_01_l_01_4_01_5_01_4.html">   80</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__is__basic.html">iterator_is_basic</a>&lt;<a class="code" href="structboost_1_1gil_1_1pixel.html">pixel</a>&lt;T, L&gt;*&gt; : std::true_type {};</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt;</div><div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1iterator__is__basic_3_01pixel_3_01_t_00_01_l_01_4_01const_01_5_01_4.html">   84</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__is__basic.html">iterator_is_basic</a>&lt;<a class="code" href="structboost_1_1gil_1_1pixel.html">pixel</a>&lt;T, L&gt; const*&gt; : std::true_type {};</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> CS&gt;</div><div class="line"><a name="l00088"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1iterator__is__basic_3_01planar__pixel__iterator_3_01_t_01_5_00_01_c_s_01_4_01_4.html">   88</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__is__basic.html">iterator_is_basic</a>&lt;<a class="code" href="structboost_1_1gil_1_1planar__pixel__iterator.html">planar_pixel_iterator</a>&lt;T*, CS&gt;&gt; : std::true_type {};</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> CS&gt;</div><div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1iterator__is__basic_3_01planar__pixel__iterator_3_01_t_01const_01_5_00_01_c_s_01_4_01_4.html">   92</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__is__basic.html">iterator_is_basic</a>&lt;<a class="code" href="structboost_1_1gil_1_1planar__pixel__iterator.html">planar_pixel_iterator</a>&lt;T const*, CS&gt;&gt; : std::true_type {};</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt;</div><div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1iterator__is__basic_3_01memory__based__step__iterator_3_01pixel_3_01_t_00_01_l_01_4_01_5_01_4_01_4.html">   96</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__is__basic.html">iterator_is_basic</a>&lt;<a class="code" href="classboost_1_1gil_1_1memory__based__step__iterator.html">memory_based_step_iterator</a>&lt;<a class="code" href="structboost_1_1gil_1_1pixel.html">pixel</a>&lt;T, L&gt;*&gt;&gt; : std::true_type {};</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt;</div><div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1iterator__is__basic_3_01memory__based__step__iterator_3_01pixel_3_01_t_00_49e6005b45e6301328ca4342b3cea079.html">  100</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__is__basic.html">iterator_is_basic</a>&lt;<a class="code" href="classboost_1_1gil_1_1memory__based__step__iterator.html">memory_based_step_iterator</a>&lt;<a class="code" href="structboost_1_1gil_1_1pixel.html">pixel</a>&lt;T, L&gt; const*&gt;&gt; : std::true_type {};</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> CS&gt;</div><div class="line"><a name="l00104"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1iterator__is__basic_3_01memory__based__step__iterator_3_01planar__pixel__i11756f255913088facaf56838bba8ad0.html">  104</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__is__basic.html">iterator_is_basic</a>&lt;<a class="code" href="classboost_1_1gil_1_1memory__based__step__iterator.html">memory_based_step_iterator</a>&lt;<a class="code" href="structboost_1_1gil_1_1planar__pixel__iterator.html">planar_pixel_iterator</a>&lt;T*, CS&gt;&gt;&gt;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;    : std::true_type</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;{};</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> CS&gt;</div><div class="line"><a name="l00110"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1iterator__is__basic_3_01memory__based__step__iterator_3_01planar__pixel__i1e167244adb96adf7850fda415bdbfe9.html">  110</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__is__basic.html">iterator_is_basic</a>&lt;<a class="code" href="classboost_1_1gil_1_1memory__based__step__iterator.html">memory_based_step_iterator</a>&lt;<a class="code" href="structboost_1_1gil_1_1planar__pixel__iterator.html">planar_pixel_iterator</a>&lt;T const*, CS&gt;&gt;&gt;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;    : std::true_type</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;{};</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Loc&gt;</div><div class="line"><a name="l00118"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1locator__is__basic.html">  118</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1locator__is__basic.html">locator_is_basic</a> : std::false_type {};</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Iterator&gt;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1locator__is__basic.html">locator_is_basic</a></div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;    &lt;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;        <a class="code" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>&lt;<a class="code" href="classboost_1_1gil_1_1memory__based__step__iterator.html">memory_based_step_iterator</a>&lt;Iterator&gt;&gt;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;    &gt; : <a class="code" href="structboost_1_1gil_1_1iterator__is__basic.html">iterator_is_basic</a>&lt;Iterator&gt;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;{};</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> View&gt;</div><div class="line"><a name="l00130"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1view__is__basic.html">  130</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1view__is__basic.html">view_is_basic</a> : std::false_type {};</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Loc&gt;</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1view__is__basic.html">view_is_basic</a>&lt;<a class="code" href="classboost_1_1gil_1_1image__view.html">image_view</a>&lt;Loc&gt;&gt; : <a class="code" href="structboost_1_1gil_1_1locator__is__basic.html">locator_is_basic</a>&lt;Loc&gt; {};</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Img&gt;</div><div class="line"><a name="l00138"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1image__is__basic.html">  138</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1image__is__basic.html">image_is_basic</a> : std::false_type {};</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Pixel, <span class="keywordtype">bool</span> IsPlanar, <span class="keyword">typename</span> Alloc&gt;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1image__is__basic.html">image_is_basic</a>&lt;<a class="code" href="classboost_1_1gil_1_1image.html">image</a>&lt;Pixel, IsPlanar, Alloc&gt;&gt; : std::true_type {};</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> I&gt;</div><div class="line"><a name="l00149"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1iterator__is__step.html">  149</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__is__step.html">iterator_is_step</a>;</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;<span class="keyword">namespace </span>detail {</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> It, <span class="keywordtype">bool</span> IsBase, <span class="keywordtype">bool</span> EqualsStepType&gt;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;<span class="keyword">struct </span>iterator_is_step_impl;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;<span class="comment">// iterator that has the same type as its dynamic_x_step_type must be a step iterator</span></div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> It, <span class="keywordtype">bool</span> IsBase&gt;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;<span class="keyword">struct </span>iterator_is_step_impl&lt;It, IsBase, true&gt; : std::true_type {};</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;<span class="comment">// base iterator can never be a step iterator</span></div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> It&gt;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;<span class="keyword">struct </span>iterator_is_step_impl&lt;It, true, false&gt; : std::false_type {};</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;<span class="comment">// for an iterator adaptor, see if its base is step</span></div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> It&gt;</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;<span class="keyword">struct </span>iterator_is_step_impl&lt;It, false, false&gt;</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;    : <span class="keyword">public</span> iterator_is_step&lt;typename iterator_adaptor_get_base&lt;It&gt;::type&gt; {};</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;} <span class="comment">// namespace detail</span></div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> I&gt;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;<span class="keyword">struct </span>iterator_is_step</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;    : detail::iterator_is_step_impl</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;    &lt;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;        I,</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;        !is_iterator_adaptor&lt;I&gt;::value,</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;        std::is_same&lt;I, typename dynamic_x_step_type&lt;I&gt;::type</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;    &gt;::value</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;&gt;</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;{};</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;</div><div class="line"><a name="l00186"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1locator__is__step__in__x.html">  186</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> L&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1locator__is__step__in__x.html">locator_is_step_in_x</a> : <span class="keyword">public</span> <a class="code" href="structboost_1_1gil_1_1iterator__is__step.html">iterator_is_step</a>&lt;typename L::x_iterator&gt; {};</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;</div><div class="line"><a name="l00190"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1locator__is__step__in__y.html">  190</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> L&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1locator__is__step__in__y.html">locator_is_step_in_y</a> : <span class="keyword">public</span> <a class="code" href="structboost_1_1gil_1_1iterator__is__step.html">iterator_is_step</a>&lt;typename L::y_iterator&gt; {};</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;</div><div class="line"><a name="l00194"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1view__is__step__in__x.html">  194</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> V&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1view__is__step__in__x.html">view_is_step_in_x</a> : <span class="keyword">public</span> <a class="code" href="structboost_1_1gil_1_1locator__is__step__in__x.html">locator_is_step_in_x</a>&lt;typename V::xy_locator&gt; {};</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;</div><div class="line"><a name="l00198"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1view__is__step__in__y.html">  198</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> V&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1view__is__step__in__y.html">view_is_step_in_y</a> : <span class="keyword">public</span> <a class="code" href="structboost_1_1gil_1_1locator__is__step__in__y.html">locator_is_step_in_y</a>&lt;typename V::xy_locator&gt; {};</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> PixelReference&gt;</div><div class="line"><a name="l00203"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1pixel__reference__is__proxy.html">  203</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1pixel__reference__is__proxy.html">pixel_reference_is_proxy</a></div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;    : mp11::mp_not</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;    &lt;</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;        std::is_same</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;        &lt;</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;            typename detail::remove_const_and_reference&lt;PixelReference&gt;::type,</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;            typename detail::remove_const_and_reference&lt;PixelReference&gt;::type::value_type</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;        &gt;</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;    &gt;</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;{};</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Pixel&gt;</div><div class="line"><a name="l00217"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1pixel__is__reference.html">  217</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1pixel__is__reference.html">pixel_is_reference</a></div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;    : mp11::mp_or&lt;is_reference&lt;Pixel&gt;, pixel_reference_is_proxy&lt;Pixel&gt;&gt; {};</div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> R&gt;</div><div class="line"><a name="l00229"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1pixel__reference__is__mutable.html">  229</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1pixel__reference__is__mutable.html">pixel_reference_is_mutable</a></div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;    : std::integral_constant&lt;bool, std::remove_reference&lt;R&gt;::type::is_mutable&gt;</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;{};</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> R&gt;</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1pixel__reference__is__mutable.html">pixel_reference_is_mutable</a>&lt;R const&amp;&gt;</div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;    : mp11::mp_and&lt;pixel_reference_is_proxy&lt;R&gt;, <a class="code" href="structboost_1_1gil_1_1pixel__reference__is__mutable.html">pixel_reference_is_mutable&lt;R&gt;</a>&gt;</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;{};</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;</div><div class="line"><a name="l00240"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1locator__is__mutable.html">  240</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> L&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1locator__is__mutable.html">locator_is_mutable</a> : <span class="keyword">public</span> <a class="code" href="structboost_1_1gil_1_1iterator__is__mutable.html">iterator_is_mutable</a>&lt;typename L::x_iterator&gt; {};</div><div class="line"><a name="l00243"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1view__is__mutable.html">  243</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> V&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1view__is__mutable.html">view_is_mutable</a> : <span class="keyword">public</span> <a class="code" href="structboost_1_1gil_1_1iterator__is__mutable.html">iterator_is_mutable</a>&lt;typename V::x_iterator&gt; {};</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div><div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;</div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;</div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;</div><div class="line"><a name="l00267"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1pixel__reference__type.html">  267</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L, <span class="keywordtype">bool</span> IsPlanar=false, <span class="keywordtype">bool</span> IsMutable=true&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1pixel__reference__type.html">pixel_reference_type</a>{};</div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1pixel__reference__type.html">pixel_reference_type</a>&lt;T,L,false,true &gt; { <span class="keyword">using</span> type = <a class="code" href="structboost_1_1gil_1_1pixel.html">pixel&lt;T,L&gt;</a>&amp;; };</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt; <span class="keyword">struct </span>pixel_reference_type&lt;T,L,false,false&gt; { <span class="keyword">using</span> type = pixel&lt;T,L&gt; <span class="keyword">const</span>&amp;; };</div><div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt; <span class="keyword">struct </span>pixel_reference_type&lt;T,L,true,true&gt; { <span class="keyword">using</span> type = planar_pixel_reference&lt;typename channel_traits&lt;T&gt;::reference,<span class="keyword">typename</span> color_space_type&lt;L&gt;::type&gt; <span class="keyword">const</span>; };       <span class="comment">// TODO: Assert M=identity</span></div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt; <span class="keyword">struct </span>pixel_reference_type&lt;T,L,true,false&gt; { <span class="keyword">using</span> type = planar_pixel_reference&lt;typename channel_traits&lt;T&gt;::const_reference,<span class="keyword">typename</span> color_space_type&lt;L&gt;::type&gt; <span class="keyword">const</span>; };<span class="comment">// TODO: Assert M=identity</span></div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;</div><div class="line"><a name="l00275"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1iterator__type__from__pixel.html">  275</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Pixel, <span class="keywordtype">bool</span> IsPlanar=false, <span class="keywordtype">bool</span> IsStep=false, <span class="keywordtype">bool</span> IsMutable=true&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__type__from__pixel.html">iterator_type_from_pixel</a>{};</div><div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Pixel&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__type__from__pixel.html">iterator_type_from_pixel</a>&lt;Pixel,false,false,true &gt; { <span class="keyword">using</span> type = Pixel *; };</div><div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Pixel&gt; <span class="keyword">struct </span>iterator_type_from_pixel&lt;Pixel,false,false,false&gt; { <span class="keyword">using</span> type = <span class="keyword">const</span> Pixel *; };</div><div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Pixel&gt; <span class="keyword">struct </span>iterator_type_from_pixel&lt;Pixel,true,false,true&gt; {</div><div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;    <span class="keyword">using</span> type = planar_pixel_iterator&lt;typename channel_traits&lt;typename channel_type&lt;Pixel&gt;::type&gt;::pointer,<span class="keyword">typename</span> color_space_type&lt;Pixel&gt;::type&gt;;</div><div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;};</div><div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Pixel&gt; <span class="keyword">struct </span>iterator_type_from_pixel&lt;Pixel,true,false,false&gt; {</div><div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;    <span class="keyword">using</span> type = planar_pixel_iterator&lt;typename channel_traits&lt;typename channel_type&lt;Pixel&gt;::type&gt;::const_pointer,<span class="keyword">typename</span> color_space_type&lt;Pixel&gt;::type&gt;;</div><div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;};</div><div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Pixel, <span class="keywordtype">bool</span> IsPlanar, <span class="keywordtype">bool</span> IsMutable&gt; <span class="keyword">struct </span>iterator_type_from_pixel&lt;Pixel,IsPlanar,true,IsMutable&gt; {</div><div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;    <span class="keyword">using</span> type = memory_based_step_iterator&lt;typename iterator_type_from_pixel&lt;Pixel,IsPlanar,false,IsMutable&gt;::type&gt;;</div><div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;};</div><div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;</div><div class="line"><a name="l00290"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1iterator__type.html">  290</a></span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L, <span class="keywordtype">bool</span> IsPlanar=false, <span class="keywordtype">bool</span> IsStep=false, <span class="keywordtype">bool</span> IsMutable=true&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__type.html">iterator_type</a>{};</div><div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt; <span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1iterator__type.html">iterator_type</a>&lt;T,L,false,false,true &gt; { <span class="keyword">using</span> type = <a class="code" href="structboost_1_1gil_1_1pixel.html">pixel&lt;T,L&gt;</a>*; };</div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt; <span class="keyword">struct </span>iterator_type&lt;T,L,false,false,false&gt; { <span class="keyword">using</span> type = pixel&lt;T,L&gt; <span class="keyword">const</span>*; };</div><div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt; <span class="keyword">struct </span>iterator_type&lt;T,L,true,false,true&gt; { <span class="keyword">using</span> type = planar_pixel_iterator&lt;T*,typename L::color_space_t&gt;; };               <span class="comment">// TODO: Assert M=identity</span></div><div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L&gt; <span class="keyword">struct </span>iterator_type&lt;T,L,true,false,false&gt; { <span class="keyword">using</span> type = planar_pixel_iterator&lt;const T*,typename L::color_space_t&gt;; };        <span class="comment">// TODO: Assert M=identity</span></div><div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L, <span class="keywordtype">bool</span> IsPlanar, <span class="keywordtype">bool</span> IsMutable&gt; <span class="keyword">struct </span>iterator_type&lt;T,L,IsPlanar,true,IsMutable&gt; {</div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;    <span class="keyword">using</span> type = memory_based_step_iterator&lt;typename iterator_type&lt;T,L,IsPlanar,false,IsMutable&gt;::type&gt;;</div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;};</div><div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;</div><div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> XIterator&gt;</div><div class="line"><a name="l00302"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1type__from__x__iterator.html">  302</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1type__from__x__iterator.html">type_from_x_iterator</a></div><div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;{</div><div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;    <span class="keyword">using</span> <a class="code" href="classboost_1_1gil_1_1memory__based__step__iterator.html">step_iterator_t</a> = <a class="code" href="classboost_1_1gil_1_1memory__based__step__iterator.html">memory_based_step_iterator&lt;XIterator&gt;</a>;</div><div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;    <span class="keyword">using</span> <a class="code" href="classboost_1_1gil_1_1memory__based__2d__locator.html">xy_locator_t</a> = <a class="code" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator&lt;step_iterator_t&gt;</a>;</div><div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;    <span class="keyword">using</span> <a class="code" href="classboost_1_1gil_1_1image__view.html">view_t</a> = <a class="code" href="classboost_1_1gil_1_1image__view.html">image_view&lt;xy_locator_t&gt;</a>;</div><div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;};</div><div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;</div><div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;<span class="keyword">namespace </span>detail {</div><div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;</div><div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keyword">typename</span> FirstBit, <span class="keyword">typename</span> NumBits&gt;</div><div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;<span class="keyword">struct </span>packed_channel_reference_type</div><div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;{</div><div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;    <span class="keyword">using</span> type = packed_channel_reference</div><div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;        &lt;</div><div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;            BitField, FirstBit::value, NumBits::value, <span class="keyword">true</span></div><div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;        &gt; <span class="keyword">const</span>;</div><div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;};</div><div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;</div><div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keyword">typename</span> ChannelBitSizes&gt;</div><div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;<span class="keyword">class </span>packed_channel_references_vector_type</div><div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;{</div><div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;    <span class="keyword">template</span> &lt;<span class="keyword">typename</span> FirstBit, <span class="keyword">typename</span> NumBits&gt;</div><div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;    <span class="keyword">using</span> reference_type = <span class="keyword">typename</span> packed_channel_reference_type&lt;BitField, FirstBit, NumBits&gt;::type;</div><div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;</div><div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;    <span class="comment">// If ChannelBitSizesVector is mp11::mp_list_c&lt;int,7,7,2&gt;</span></div><div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;    <span class="comment">// Then first_bits_vector will be mp11::mp_list_c&lt;int,0,7,14,16&gt;</span></div><div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;    <span class="keyword">using</span> first_bit_list = mp11::mp_fold_q</div><div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;        &lt;</div><div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;            ChannelBitSizes,</div><div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;            mp11::mp_list&lt;std::integral_constant&lt;int, 0&gt;&gt;,</div><div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;            mp11::mp_bind</div><div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;            &lt;</div><div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;                mp11::mp_push_back,</div><div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;                mp11::_1,</div><div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;                mp11::mp_bind</div><div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;                &lt;</div><div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;                    mp11::mp_plus,</div><div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;                    mp11::mp_bind&lt;mp_back, mp11::_1&gt;,</div><div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;                    mp11::_2</div><div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;                &gt;</div><div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;            &gt;</div><div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;        &gt;;</div><div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;</div><div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;    static_assert(mp11::mp_at_c&lt;first_bit_list, 0&gt;::value == 0, <span class="stringliteral">&quot;packed channel first bit must be 0&quot;</span>);</div><div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;</div><div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;    <span class="keyword">using</span> type = mp11::mp_transform</div><div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;        &lt;</div><div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;            reference_type,</div><div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;            mp_pop_back&lt;first_bit_list&gt;,</div><div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;            ChannelBitSizes</div><div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;        &gt;;</div><div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;};</div><div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;</div><div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;} <span class="comment">// namespace detail</span></div><div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;</div><div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keyword">typename</span> ChannelBitSizes, <span class="keyword">typename</span> Layout&gt;</div><div class="line"><a name="l00367"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1packed__pixel__type.html">  367</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1packed__pixel__type.html">packed_pixel_type</a></div><div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;{</div><div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;    <span class="keyword">using</span> <a class="code" href="structboost_1_1gil_1_1packed__pixel.html">type</a> = <a class="code" href="structboost_1_1gil_1_1packed__pixel.html">packed_pixel</a></div><div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;        &lt;</div><div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;            BitField,</div><div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;            <span class="keyword">typename</span> detail::packed_channel_references_vector_type</div><div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;            &lt;</div><div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;                BitField,</div><div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;                ChannelBitSizes</div><div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;            &gt;<a class="code" href="structboost_1_1gil_1_1packed__pixel.html">::type</a>,</div><div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;        Layout&gt;;</div><div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;};</div><div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;</div><div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;</div><div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keyword">typename</span> ChannelBitSizes, <span class="keyword">typename</span> Layout, <span class="keyword">typename</span> Alloc=std::allocator&lt;<span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>&gt;&gt;</div><div class="line"><a name="l00392"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1packed__image__type.html">  392</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1packed__image__type.html">packed_image_type</a></div><div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;{</div><div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;    <span class="keyword">using</span> <a class="code" href="classboost_1_1gil_1_1image.html">type</a> = <a class="code" href="classboost_1_1gil_1_1image.html">image&lt;typename packed_pixel_type&lt;BitField,ChannelBitSizes,Layout&gt;::type</a>,<span class="keyword">false</span>,Alloc&gt;;</div><div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;};</div><div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;</div><div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keywordtype">unsigned</span> Size1, <span class="keyword">typename</span> Layout, <span class="keyword">typename</span> Alloc = std::allocator&lt;<span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>&gt;&gt;</div><div class="line"><a name="l00400"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1packed__image1__type.html">  400</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1packed__image1__type.html">packed_image1_type</a></div><div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;    : <a class="code" href="structboost_1_1gil_1_1packed__image__type.html">packed_image_type</a>&lt;BitField, mp11::mp_list_c&lt;unsigned, Size1&gt;, Layout, Alloc&gt;</div><div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;{};</div><div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;</div><div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keywordtype">unsigned</span> Size1, <span class="keywordtype">unsigned</span> Size2, <span class="keyword">typename</span> Layout, <span class="keyword">typename</span> Alloc = std::allocator&lt;<span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>&gt;&gt;</div><div class="line"><a name="l00407"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1packed__image2__type.html">  407</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1packed__image2__type.html">packed_image2_type</a></div><div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;    : <a class="code" href="structboost_1_1gil_1_1packed__image__type.html">packed_image_type</a>&lt;BitField, mp11::mp_list_c&lt;unsigned, Size1, Size2&gt;, Layout, Alloc&gt;</div><div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;{};</div><div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;</div><div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keywordtype">unsigned</span> Size1, <span class="keywordtype">unsigned</span> Size2, <span class="keywordtype">unsigned</span> Size3, <span class="keyword">typename</span> Layout, <span class="keyword">typename</span> Alloc = std::allocator&lt;<span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>&gt;&gt;</div><div class="line"><a name="l00414"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1packed__image3__type.html">  414</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1packed__image3__type.html">packed_image3_type</a></div><div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;    : <a class="code" href="structboost_1_1gil_1_1packed__image__type.html">packed_image_type</a>&lt;BitField, mp11::mp_list_c&lt;unsigned, Size1, Size2, Size3&gt;, Layout, Alloc&gt;</div><div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;{};</div><div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;</div><div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keywordtype">unsigned</span> Size1, <span class="keywordtype">unsigned</span> Size2, <span class="keywordtype">unsigned</span> Size3, <span class="keywordtype">unsigned</span> Size4, <span class="keyword">typename</span> Layout, <span class="keyword">typename</span> Alloc = std::allocator&lt;<span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>&gt;&gt;</div><div class="line"><a name="l00421"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1packed__image4__type.html">  421</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1packed__image4__type.html">packed_image4_type</a></div><div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;    : <a class="code" href="structboost_1_1gil_1_1packed__image__type.html">packed_image_type</a>&lt;BitField, mp11::mp_list_c&lt;unsigned, Size1, Size2, Size3, Size4&gt;, Layout, Alloc&gt;</div><div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;{};</div><div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;</div><div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keywordtype">unsigned</span> Size1, <span class="keywordtype">unsigned</span> Size2, <span class="keywordtype">unsigned</span> Size3, <span class="keywordtype">unsigned</span> Size4, <span class="keywordtype">unsigned</span> Size5, <span class="keyword">typename</span> Layout, <span class="keyword">typename</span> Alloc = std::allocator&lt;<span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>&gt;&gt;</div><div class="line"><a name="l00428"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1packed__image5__type.html">  428</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1packed__image5__type.html">packed_image5_type</a></div><div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;    : <a class="code" href="structboost_1_1gil_1_1packed__image__type.html">packed_image_type</a>&lt;BitField, mp11::mp_list_c&lt;unsigned, Size1, Size2, Size3, Size4, Size5&gt;, Layout, Alloc&gt; {};</div><div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;</div><div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;</div><div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;<span class="keyword">template</span></div><div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;&lt;</div><div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;    <span class="keyword">typename</span> ChannelBitSizes,</div><div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;    <span class="keyword">typename</span> Layout,</div><div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;    <span class="keyword">typename</span> Alloc = std::allocator&lt;unsigned char&gt;</div><div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160;&gt;</div><div class="line"><a name="l00444"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1bit__aligned__image__type.html">  444</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1bit__aligned__image__type.html">bit_aligned_image_type</a></div><div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;{</div><div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;</div><div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;    <span class="keyword">static</span> constexpr <span class="keywordtype">int</span> bit_size =</div><div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;        mp11::mp_fold</div><div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;        &lt;</div><div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;            ChannelBitSizes,</div><div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;            std::integral_constant&lt;int, 0&gt;,</div><div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;            mp11::mp_plus</div><div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;        &gt;::value;</div><div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;</div><div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;    <span class="keyword">using</span> bitfield_t = <span class="keyword">typename</span> detail::min_fast_uint&lt;bit_size + 7&gt;::type;</div><div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;    <span class="keyword">using</span> bit_alignedref_t = bit_aligned_pixel_reference&lt;bitfield_t, ChannelBitSizes, Layout, true&gt; <span class="keyword">const</span>;</div><div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;</div><div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;    <span class="keyword">using</span> <a class="code" href="classboost_1_1gil_1_1image.html">type</a> = <a class="code" href="classboost_1_1gil_1_1image.html">image&lt;bit_alignedref_t,false,Alloc&gt;</a>;</div><div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;};</div><div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;</div><div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">unsigned</span> Size1, <span class="keyword">typename</span> Layout, <span class="keyword">typename</span> Alloc = std::allocator&lt;<span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>&gt;&gt;</div><div class="line"><a name="l00466"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1bit__aligned__image1__type.html">  466</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1bit__aligned__image1__type.html">bit_aligned_image1_type</a> : <a class="code" href="structboost_1_1gil_1_1bit__aligned__image__type.html">bit_aligned_image_type</a>&lt;mp11::mp_list_c&lt;unsigned, Size1&gt;, Layout, Alloc&gt; {};</div><div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;</div><div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">unsigned</span> Size1, <span class="keywordtype">unsigned</span> Size2, <span class="keyword">typename</span> Layout, <span class="keyword">typename</span> Alloc = std::allocator&lt;<span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>&gt;&gt;</div><div class="line"><a name="l00471"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1bit__aligned__image2__type.html">  471</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1bit__aligned__image2__type.html">bit_aligned_image2_type</a> : <a class="code" href="structboost_1_1gil_1_1bit__aligned__image__type.html">bit_aligned_image_type</a>&lt;mp11::mp_list_c&lt;unsigned, Size1, Size2&gt;, Layout, Alloc&gt; {};</div><div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;</div><div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">unsigned</span> Size1, <span class="keywordtype">unsigned</span> Size2, <span class="keywordtype">unsigned</span> Size3, <span class="keyword">typename</span> Layout, <span class="keyword">typename</span> Alloc = std::allocator&lt;<span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>&gt;&gt;</div><div class="line"><a name="l00476"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1bit__aligned__image3__type.html">  476</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1bit__aligned__image3__type.html">bit_aligned_image3_type</a> : <a class="code" href="structboost_1_1gil_1_1bit__aligned__image__type.html">bit_aligned_image_type</a>&lt;mp11::mp_list_c&lt;unsigned, Size1, Size2, Size3&gt;, Layout, Alloc&gt; {};</div><div class="line"><a name="l00477"></a><span class="lineno">  477</span>&#160;</div><div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">unsigned</span> Size1, <span class="keywordtype">unsigned</span> Size2, <span class="keywordtype">unsigned</span> Size3, <span class="keywordtype">unsigned</span> Size4, <span class="keyword">typename</span> Layout, <span class="keyword">typename</span> Alloc = std::allocator&lt;<span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>&gt;&gt;</div><div class="line"><a name="l00481"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1bit__aligned__image4__type.html">  481</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1bit__aligned__image4__type.html">bit_aligned_image4_type</a> : <a class="code" href="structboost_1_1gil_1_1bit__aligned__image__type.html">bit_aligned_image_type</a>&lt;mp11::mp_list_c&lt;unsigned, Size1, Size2, Size3, Size4&gt;, Layout, Alloc&gt; {};</div><div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;</div><div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">unsigned</span> Size1, <span class="keywordtype">unsigned</span> Size2, <span class="keywordtype">unsigned</span> Size3, <span class="keywordtype">unsigned</span> Size4, <span class="keywordtype">unsigned</span> Size5, <span class="keyword">typename</span> Layout, <span class="keyword">typename</span> Alloc = std::allocator&lt;<span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>&gt;&gt;</div><div class="line"><a name="l00486"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1bit__aligned__image5__type.html">  486</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1bit__aligned__image5__type.html">bit_aligned_image5_type</a> : <a class="code" href="structboost_1_1gil_1_1bit__aligned__image__type.html">bit_aligned_image_type</a>&lt;mp11::mp_list_c&lt;unsigned, Size1, Size2, Size3, Size4, Size5&gt;, Layout, Alloc&gt; {};</div><div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;</div><div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;</div><div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Channel, <span class="keyword">typename</span> Layout&gt;</div><div class="line"><a name="l00492"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1pixel__value__type.html">  492</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1pixel__value__type.html">pixel_value_type</a></div><div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;{</div><div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;    <span class="comment">// by default use gil::pixel. Specializations are provided for</span></div><div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;    <span class="keyword">using</span> <a class="code" href="structboost_1_1gil_1_1pixel.html">type</a> = <a class="code" href="structboost_1_1gil_1_1pixel.html">pixel&lt;Channel, Layout&gt;</a>;</div><div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;};</div><div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;</div><div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;<span class="comment">// Specializations for packed channels</span></div><div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keywordtype">int</span> NumBits, <span class="keywordtype">bool</span> IsMutable, <span class="keyword">typename</span> Layout&gt;</div><div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1pixel__value__type.html">pixel_value_type</a>&lt;packed_dynamic_channel_reference&lt;BitField, NumBits, IsMutable&gt;, Layout&gt;</div><div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;    : <a class="code" href="structboost_1_1gil_1_1packed__pixel__type.html">packed_pixel_type</a>&lt;BitField, mp11::mp_list_c&lt;unsigned, NumBits&gt;, Layout&gt;</div><div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;{};</div><div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;</div><div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keywordtype">int</span> NumBits, <span class="keywordtype">bool</span> IsMutable, <span class="keyword">typename</span> Layout&gt;</div><div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;<span class="keyword">struct </span>pixel_value_type&lt;packed_dynamic_channel_reference&lt;BitField, NumBits, IsMutable&gt; const, Layout&gt;</div><div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;    : packed_pixel_type&lt;BitField, mp11::mp_list_c&lt;unsigned, NumBits&gt;, Layout&gt;</div><div class="line"><a name="l00507"></a><span class="lineno">  507</span>&#160;{};</div><div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160;</div><div class="line"><a name="l00509"></a><span class="lineno">  509</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keywordtype">int</span> FirstBit, <span class="keywordtype">int</span> NumBits, <span class="keywordtype">bool</span> IsMutable, <span class="keyword">typename</span> Layout&gt;</div><div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;<span class="keyword">struct </span>pixel_value_type&lt;packed_channel_reference&lt;BitField, FirstBit, NumBits, IsMutable&gt;, Layout&gt;</div><div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;    : packed_pixel_type&lt;BitField, mp11::mp_list_c&lt;unsigned, NumBits&gt;, Layout&gt;</div><div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;{};</div><div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;</div><div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BitField, <span class="keywordtype">int</span> FirstBit, <span class="keywordtype">int</span> NumBits, <span class="keywordtype">bool</span> IsMutable, <span class="keyword">typename</span> Layout&gt;</div><div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;<span class="keyword">struct </span>pixel_value_type&lt;packed_channel_reference&lt;BitField, FirstBit, NumBits, IsMutable&gt; const, Layout&gt;</div><div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;    : packed_pixel_type&lt;BitField, mp11::mp_list_c&lt;unsigned, NumBits&gt;, Layout&gt;</div><div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;{};</div><div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;</div><div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> NumBits, <span class="keyword">typename</span> Layout&gt;</div><div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;<span class="keyword">struct </span>pixel_value_type&lt;packed_channel_value&lt;NumBits&gt;, Layout&gt;</div><div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;    : packed_pixel_type&lt;typename detail::min_fast_uint&lt;NumBits&gt;::type, mp11::mp_list_c&lt;unsigned, NumBits&gt;, Layout&gt;</div><div class="line"><a name="l00522"></a><span class="lineno">  522</span>&#160;{};</div><div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;</div><div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L, <span class="keywordtype">bool</span> IsPlanar = false, <span class="keywordtype">bool</span> IsStepX = false, <span class="keywordtype">bool</span> IsMutable = true&gt;</div><div class="line"><a name="l00527"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1locator__type.html">  527</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1locator__type.html">locator_type</a></div><div class="line"><a name="l00528"></a><span class="lineno">  528</span>&#160;{</div><div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;    <span class="keyword">using</span> type = <span class="keyword">typename</span> <a class="code" href="structboost_1_1gil_1_1type__from__x__iterator.html">type_from_x_iterator</a></div><div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;        &lt;</div><div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;            <span class="keyword">typename</span> <a class="code" href="structboost_1_1gil_1_1iterator__type.html">iterator_type&lt;T, L, IsPlanar, IsStepX, IsMutable&gt;::type</a></div><div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;        &gt;::xy_locator_type;</div><div class="line"><a name="l00533"></a><span class="lineno">  533</span>&#160;};</div><div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;</div><div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L, <span class="keywordtype">bool</span> IsPlanar = false, <span class="keywordtype">bool</span> IsStepX = false, <span class="keywordtype">bool</span> IsMutable = true&gt;</div><div class="line"><a name="l00538"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1view__type.html">  538</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1view__type.html">view_type</a></div><div class="line"><a name="l00539"></a><span class="lineno">  539</span>&#160;{</div><div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;    <span class="keyword">using</span> type = <span class="keyword">typename</span> <a class="code" href="structboost_1_1gil_1_1type__from__x__iterator.html">type_from_x_iterator</a></div><div class="line"><a name="l00541"></a><span class="lineno">  541</span>&#160;        &lt;</div><div class="line"><a name="l00542"></a><span class="lineno">  542</span>&#160;            <span class="keyword">typename</span> <a class="code" href="structboost_1_1gil_1_1iterator__type.html">iterator_type&lt;T, L, IsPlanar, IsStepX, IsMutable&gt;::type</a></div><div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160;        &gt;::view_t;</div><div class="line"><a name="l00544"></a><span class="lineno">  544</span>&#160;};</div><div class="line"><a name="l00545"></a><span class="lineno">  545</span>&#160;</div><div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keyword">typename</span> L, <span class="keywordtype">bool</span> IsPlanar = false, <span class="keyword">typename</span> Alloc = std::allocator&lt;<span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>&gt;&gt;</div><div class="line"><a name="l00549"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1image__type.html">  549</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1image__type.html">image_type</a></div><div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160;{</div><div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;    <span class="keyword">using</span> <a class="code" href="classboost_1_1gil_1_1image.html">type</a> = <a class="code" href="classboost_1_1gil_1_1image.html">image&lt;pixel&lt;T, L&gt;</a>, IsPlanar, Alloc&gt;;</div><div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160;};</div><div class="line"><a name="l00553"></a><span class="lineno">  553</span>&#160;</div><div class="line"><a name="l00556"></a><span class="lineno">  556</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Pixel, <span class="keywordtype">bool</span> IsPlanar=false, <span class="keywordtype">bool</span> IsStepX=false, <span class="keywordtype">bool</span> IsMutable=true&gt;</div><div class="line"><a name="l00557"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1view__type__from__pixel.html">  557</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1view__type__from__pixel.html">view_type_from_pixel</a> {</div><div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;    <span class="keyword">using</span> type = <span class="keyword">typename</span> <a class="code" href="structboost_1_1gil_1_1type__from__x__iterator.html">type_from_x_iterator&lt;typename iterator_type_from_pixel&lt;Pixel,IsPlanar,IsStepX,IsMutable&gt;::type</a>&gt;::view_t;</div><div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;};</div><div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;</div><div class="line"><a name="l00561"></a><span class="lineno">  561</span>&#160;</div><div class="line"><a name="l00565"></a><span class="lineno">  565</span>&#160;<span class="keyword">template</span></div><div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160;&lt;</div><div class="line"><a name="l00567"></a><span class="lineno">  567</span>&#160;        <span class="keyword">typename</span> Ref,</div><div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;        <span class="keyword">typename</span> T = use_default,</div><div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160;        <span class="keyword">typename</span> L = use_default,</div><div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;        <span class="keyword">typename</span> IsPlanar = use_default,</div><div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;        <span class="keyword">typename</span> IsMutable = use_default&gt;</div><div class="line"><a name="l00572"></a><span class="lineno"><a class="line" href="classboost_1_1gil_1_1derived__pixel__reference__type.html">  572</a></span>&#160;<span class="keyword">class </span><a class="code" href="classboost_1_1gil_1_1derived__pixel__reference__type.html">derived_pixel_reference_type</a></div><div class="line"><a name="l00573"></a><span class="lineno">  573</span>&#160;{</div><div class="line"><a name="l00574"></a><span class="lineno">  574</span>&#160;    <span class="keyword">using</span> pixel_t = <span class="keyword">typename</span> std::remove_reference&lt;Ref&gt;::type;</div><div class="line"><a name="l00575"></a><span class="lineno">  575</span>&#160;</div><div class="line"><a name="l00576"></a><span class="lineno">  576</span>&#160;    <span class="keyword">using</span> channel_t = <span class="keyword">typename</span> mp11::mp_if</div><div class="line"><a name="l00577"></a><span class="lineno">  577</span>&#160;        &lt;</div><div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;            std::is_same&lt;T, use_default&gt;,</div><div class="line"><a name="l00579"></a><span class="lineno">  579</span>&#160;            <span class="keyword">typename</span> <a class="code" href="structboost_1_1gil_1_1channel__type.html">channel_type&lt;pixel_t&gt;::type</a>,</div><div class="line"><a name="l00580"></a><span class="lineno">  580</span>&#160;            T</div><div class="line"><a name="l00581"></a><span class="lineno">  581</span>&#160;        &gt;::type;</div><div class="line"><a name="l00582"></a><span class="lineno">  582</span>&#160;</div><div class="line"><a name="l00583"></a><span class="lineno">  583</span>&#160;    <span class="keyword">using</span> layout_t = <span class="keyword">typename</span>  mp11::mp_if</div><div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160;        &lt;</div><div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;            std::is_same&lt;L, use_default&gt;,</div><div class="line"><a name="l00586"></a><span class="lineno">  586</span>&#160;            <a class="code" href="structboost_1_1gil_1_1layout.html">layout</a></div><div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;            &lt;</div><div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;                <span class="keyword">typename</span> color_space_type&lt;pixel_t&gt;::type,</div><div class="line"><a name="l00589"></a><span class="lineno">  589</span>&#160;                <span class="keyword">typename</span> channel_mapping_type&lt;pixel_t&gt;::type</div><div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;            &gt;,</div><div class="line"><a name="l00591"></a><span class="lineno">  591</span>&#160;            L</div><div class="line"><a name="l00592"></a><span class="lineno">  592</span>&#160;        &gt;::type;</div><div class="line"><a name="l00593"></a><span class="lineno">  593</span>&#160;</div><div class="line"><a name="l00594"></a><span class="lineno">  594</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> <span class="keyword">const</span> mut = mp11::mp_if</div><div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160;        &lt;</div><div class="line"><a name="l00596"></a><span class="lineno">  596</span>&#160;            std::is_same&lt;IsMutable, use_default&gt;,</div><div class="line"><a name="l00597"></a><span class="lineno">  597</span>&#160;            <a class="code" href="structboost_1_1gil_1_1pixel__reference__is__mutable.html">pixel_reference_is_mutable&lt;Ref&gt;</a>,</div><div class="line"><a name="l00598"></a><span class="lineno">  598</span>&#160;            IsMutable</div><div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;        &gt;::value;</div><div class="line"><a name="l00600"></a><span class="lineno">  600</span>&#160;</div><div class="line"><a name="l00601"></a><span class="lineno">  601</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> <span class="keyword">const</span> planar = mp11::mp_if</div><div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;        &lt;</div><div class="line"><a name="l00603"></a><span class="lineno">  603</span>&#160;            std::is_same&lt;IsPlanar, use_default&gt;,</div><div class="line"><a name="l00604"></a><span class="lineno">  604</span>&#160;            is_planar&lt;pixel_t&gt;,</div><div class="line"><a name="l00605"></a><span class="lineno">  605</span>&#160;            IsPlanar</div><div class="line"><a name="l00606"></a><span class="lineno">  606</span>&#160;        &gt;::value;</div><div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;</div><div class="line"><a name="l00608"></a><span class="lineno">  608</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00609"></a><span class="lineno">  609</span>&#160;    <span class="keyword">using</span> type = <span class="keyword">typename</span> <a class="code" href="structboost_1_1gil_1_1pixel__reference__type.html">pixel_reference_type&lt;channel_t, layout_t, planar, mut&gt;::type</a>;</div><div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;};</div><div class="line"><a name="l00611"></a><span class="lineno">  611</span>&#160;</div><div class="line"><a name="l00615"></a><span class="lineno">  615</span>&#160;<span class="keyword">template</span></div><div class="line"><a name="l00616"></a><span class="lineno">  616</span>&#160;&lt;</div><div class="line"><a name="l00617"></a><span class="lineno">  617</span>&#160;    <span class="keyword">typename</span> Iterator,</div><div class="line"><a name="l00618"></a><span class="lineno">  618</span>&#160;    <span class="keyword">typename</span> T = use_default,</div><div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;    <span class="keyword">typename</span> L = use_default,</div><div class="line"><a name="l00620"></a><span class="lineno">  620</span>&#160;    <span class="keyword">typename</span> IsPlanar = use_default,</div><div class="line"><a name="l00621"></a><span class="lineno">  621</span>&#160;    <span class="keyword">typename</span> IsStep = use_default,</div><div class="line"><a name="l00622"></a><span class="lineno">  622</span>&#160;    <span class="keyword">typename</span> IsMutable = use_default</div><div class="line"><a name="l00623"></a><span class="lineno">  623</span>&#160;&gt;</div><div class="line"><a name="l00624"></a><span class="lineno"><a class="line" href="classboost_1_1gil_1_1derived__iterator__type.html">  624</a></span>&#160;<span class="keyword">class </span><a class="code" href="classboost_1_1gil_1_1derived__iterator__type.html">derived_iterator_type</a></div><div class="line"><a name="l00625"></a><span class="lineno">  625</span>&#160;{</div><div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;    <span class="keyword">using</span> channel_t = <span class="keyword">typename</span> mp11::mp_if</div><div class="line"><a name="l00627"></a><span class="lineno">  627</span>&#160;        &lt;</div><div class="line"><a name="l00628"></a><span class="lineno">  628</span>&#160;            std::is_same&lt;T, use_default&gt;,</div><div class="line"><a name="l00629"></a><span class="lineno">  629</span>&#160;            <span class="keyword">typename</span> <a class="code" href="structboost_1_1gil_1_1channel__type.html">channel_type&lt;Iterator&gt;::type</a>,</div><div class="line"><a name="l00630"></a><span class="lineno">  630</span>&#160;            T</div><div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;        &gt;::type;</div><div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;</div><div class="line"><a name="l00633"></a><span class="lineno">  633</span>&#160;    <span class="keyword">using</span> layout_t = <span class="keyword">typename</span> mp11::mp_if</div><div class="line"><a name="l00634"></a><span class="lineno">  634</span>&#160;        &lt;</div><div class="line"><a name="l00635"></a><span class="lineno">  635</span>&#160;            std::is_same&lt;L, use_default&gt;,</div><div class="line"><a name="l00636"></a><span class="lineno">  636</span>&#160;            <a class="code" href="structboost_1_1gil_1_1layout.html">layout</a></div><div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160;            &lt;</div><div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;                <span class="keyword">typename</span> color_space_type&lt;Iterator&gt;::type,</div><div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160;                <span class="keyword">typename</span> channel_mapping_type&lt;Iterator&gt;::type</div><div class="line"><a name="l00640"></a><span class="lineno">  640</span>&#160;            &gt;,</div><div class="line"><a name="l00641"></a><span class="lineno">  641</span>&#160;            L</div><div class="line"><a name="l00642"></a><span class="lineno">  642</span>&#160;        &gt;::type;</div><div class="line"><a name="l00643"></a><span class="lineno">  643</span>&#160;</div><div class="line"><a name="l00644"></a><span class="lineno">  644</span>&#160;    <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">bool</span> mut = mp11::mp_if</div><div class="line"><a name="l00645"></a><span class="lineno">  645</span>&#160;        &lt;</div><div class="line"><a name="l00646"></a><span class="lineno">  646</span>&#160;            std::is_same&lt;IsMutable, use_default&gt;,</div><div class="line"><a name="l00647"></a><span class="lineno">  647</span>&#160;            <a class="code" href="structboost_1_1gil_1_1iterator__is__mutable.html">iterator_is_mutable&lt;Iterator&gt;</a>,</div><div class="line"><a name="l00648"></a><span class="lineno">  648</span>&#160;            IsMutable</div><div class="line"><a name="l00649"></a><span class="lineno">  649</span>&#160;        &gt;::value;</div><div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160;</div><div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> <span class="keyword">const</span> planar = mp11::mp_if</div><div class="line"><a name="l00652"></a><span class="lineno">  652</span>&#160;        &lt;</div><div class="line"><a name="l00653"></a><span class="lineno">  653</span>&#160;            std::is_same&lt;IsPlanar, use_default&gt;,</div><div class="line"><a name="l00654"></a><span class="lineno">  654</span>&#160;            is_planar&lt;Iterator&gt;,</div><div class="line"><a name="l00655"></a><span class="lineno">  655</span>&#160;            IsPlanar</div><div class="line"><a name="l00656"></a><span class="lineno">  656</span>&#160;        &gt;::value;</div><div class="line"><a name="l00657"></a><span class="lineno">  657</span>&#160;</div><div class="line"><a name="l00658"></a><span class="lineno">  658</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> <span class="keyword">const</span> step = mp11::mp_if</div><div class="line"><a name="l00659"></a><span class="lineno">  659</span>&#160;        &lt;</div><div class="line"><a name="l00660"></a><span class="lineno">  660</span>&#160;            std::is_same&lt;IsStep, use_default&gt;,</div><div class="line"><a name="l00661"></a><span class="lineno">  661</span>&#160;            <a class="code" href="structboost_1_1gil_1_1iterator__is__step.html">iterator_is_step&lt;Iterator&gt;</a>,</div><div class="line"><a name="l00662"></a><span class="lineno">  662</span>&#160;            IsStep</div><div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;        &gt;::type::value;</div><div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160;</div><div class="line"><a name="l00665"></a><span class="lineno">  665</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00666"></a><span class="lineno">  666</span>&#160;    <span class="keyword">using</span> type = <span class="keyword">typename</span> <a class="code" href="structboost_1_1gil_1_1iterator__type.html">iterator_type&lt;channel_t, layout_t, planar, step, mut&gt;::type</a>;</div><div class="line"><a name="l00667"></a><span class="lineno">  667</span>&#160;};</div><div class="line"><a name="l00668"></a><span class="lineno">  668</span>&#160;</div><div class="line"><a name="l00672"></a><span class="lineno">  672</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> View, <span class="keyword">typename</span> T = use_default, <span class="keyword">typename</span> L = use_default, <span class="keyword">typename</span> IsPlanar = use_default, <span class="keyword">typename</span> StepX = use_default, <span class="keyword">typename</span> IsMutable = use_default&gt;</div><div class="line"><a name="l00673"></a><span class="lineno"><a class="line" href="classboost_1_1gil_1_1derived__view__type.html">  673</a></span>&#160;<span class="keyword">class </span><a class="code" href="classboost_1_1gil_1_1derived__view__type.html">derived_view_type</a></div><div class="line"><a name="l00674"></a><span class="lineno">  674</span>&#160;{</div><div class="line"><a name="l00675"></a><span class="lineno">  675</span>&#160;    <span class="keyword">using</span> channel_t = <span class="keyword">typename</span> mp11::mp_if</div><div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;        &lt;</div><div class="line"><a name="l00677"></a><span class="lineno">  677</span>&#160;            std::is_same&lt;T, use_default&gt;,</div><div class="line"><a name="l00678"></a><span class="lineno">  678</span>&#160;            <span class="keyword">typename</span> <a class="code" href="structboost_1_1gil_1_1channel__type.html">channel_type&lt;View&gt;::type</a>,</div><div class="line"><a name="l00679"></a><span class="lineno">  679</span>&#160;            T</div><div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;        &gt;;</div><div class="line"><a name="l00681"></a><span class="lineno">  681</span>&#160;</div><div class="line"><a name="l00682"></a><span class="lineno">  682</span>&#160;    <span class="keyword">using</span> layout_t = <span class="keyword">typename</span> mp11::mp_if</div><div class="line"><a name="l00683"></a><span class="lineno">  683</span>&#160;        &lt;</div><div class="line"><a name="l00684"></a><span class="lineno">  684</span>&#160;            std::is_same&lt;L, use_default&gt;,</div><div class="line"><a name="l00685"></a><span class="lineno">  685</span>&#160;            <a class="code" href="structboost_1_1gil_1_1layout.html">layout</a></div><div class="line"><a name="l00686"></a><span class="lineno">  686</span>&#160;            &lt;</div><div class="line"><a name="l00687"></a><span class="lineno">  687</span>&#160;                <span class="keyword">typename</span> color_space_type&lt;View&gt;::type,</div><div class="line"><a name="l00688"></a><span class="lineno">  688</span>&#160;                <span class="keyword">typename</span> channel_mapping_type&lt;View&gt;::type</div><div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160;            &gt;,</div><div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;            L</div><div class="line"><a name="l00691"></a><span class="lineno">  691</span>&#160;        &gt;;</div><div class="line"><a name="l00692"></a><span class="lineno">  692</span>&#160;</div><div class="line"><a name="l00693"></a><span class="lineno">  693</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> <span class="keyword">const</span> mut = mp11::mp_if</div><div class="line"><a name="l00694"></a><span class="lineno">  694</span>&#160;        &lt;</div><div class="line"><a name="l00695"></a><span class="lineno">  695</span>&#160;            std::is_same&lt;IsMutable, use_default&gt;,</div><div class="line"><a name="l00696"></a><span class="lineno">  696</span>&#160;            <a class="code" href="structboost_1_1gil_1_1view__is__mutable.html">view_is_mutable&lt;View&gt;</a>,</div><div class="line"><a name="l00697"></a><span class="lineno">  697</span>&#160;            IsMutable</div><div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160;        &gt;::value;</div><div class="line"><a name="l00699"></a><span class="lineno">  699</span>&#160;</div><div class="line"><a name="l00700"></a><span class="lineno">  700</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> <span class="keyword">const</span> planar = mp11::mp_if</div><div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;        &lt;</div><div class="line"><a name="l00702"></a><span class="lineno">  702</span>&#160;            std::is_same&lt;IsPlanar, use_default&gt;,</div><div class="line"><a name="l00703"></a><span class="lineno">  703</span>&#160;            is_planar&lt;View&gt;,</div><div class="line"><a name="l00704"></a><span class="lineno">  704</span>&#160;            IsPlanar</div><div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;        &gt;::value;</div><div class="line"><a name="l00706"></a><span class="lineno">  706</span>&#160;</div><div class="line"><a name="l00707"></a><span class="lineno">  707</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> <span class="keyword">const</span> step = mp11::mp_if</div><div class="line"><a name="l00708"></a><span class="lineno">  708</span>&#160;        &lt;</div><div class="line"><a name="l00709"></a><span class="lineno">  709</span>&#160;            std::is_same&lt;StepX, use_default&gt;,</div><div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;            <a class="code" href="structboost_1_1gil_1_1view__is__step__in__x.html">view_is_step_in_x&lt;View&gt;</a>,</div><div class="line"><a name="l00711"></a><span class="lineno">  711</span>&#160;            StepX</div><div class="line"><a name="l00712"></a><span class="lineno">  712</span>&#160;        &gt;::value;</div><div class="line"><a name="l00713"></a><span class="lineno">  713</span>&#160;</div><div class="line"><a name="l00714"></a><span class="lineno">  714</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00715"></a><span class="lineno">  715</span>&#160;    <span class="keyword">using</span> type = <span class="keyword">typename</span> view_type&lt;channel_t, layout_t, planar, step, mut&gt;::type;</div><div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;};</div><div class="line"><a name="l00717"></a><span class="lineno">  717</span>&#160;</div><div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Image, <span class="keyword">typename</span> T = use_default, <span class="keyword">typename</span> L = use_default, <span class="keyword">typename</span> IsPlanar = use_default&gt;</div><div class="line"><a name="l00722"></a><span class="lineno"><a class="line" href="classboost_1_1gil_1_1derived__image__type.html">  722</a></span>&#160;<span class="keyword">class </span><a class="code" href="classboost_1_1gil_1_1derived__image__type.html">derived_image_type</a></div><div class="line"><a name="l00723"></a><span class="lineno">  723</span>&#160;{</div><div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;    <span class="keyword">using</span> channel_t = <span class="keyword">typename</span> mp11::mp_if</div><div class="line"><a name="l00725"></a><span class="lineno">  725</span>&#160;        &lt;</div><div class="line"><a name="l00726"></a><span class="lineno">  726</span>&#160;            std::is_same&lt;T, use_default&gt;,</div><div class="line"><a name="l00727"></a><span class="lineno">  727</span>&#160;            <span class="keyword">typename</span> <a class="code" href="structboost_1_1gil_1_1channel__type.html">channel_type&lt;Image&gt;::type</a>,</div><div class="line"><a name="l00728"></a><span class="lineno">  728</span>&#160;            T</div><div class="line"><a name="l00729"></a><span class="lineno">  729</span>&#160;        &gt;::type;</div><div class="line"><a name="l00730"></a><span class="lineno">  730</span>&#160;</div><div class="line"><a name="l00731"></a><span class="lineno">  731</span>&#160;    <span class="keyword">using</span> layout_t = <span class="keyword">typename</span> mp11::mp_if</div><div class="line"><a name="l00732"></a><span class="lineno">  732</span>&#160;        &lt;</div><div class="line"><a name="l00733"></a><span class="lineno">  733</span>&#160;            std::is_same&lt;L, use_default&gt;,</div><div class="line"><a name="l00734"></a><span class="lineno">  734</span>&#160;            <a class="code" href="structboost_1_1gil_1_1layout.html">layout</a></div><div class="line"><a name="l00735"></a><span class="lineno">  735</span>&#160;            &lt;</div><div class="line"><a name="l00736"></a><span class="lineno">  736</span>&#160;                <span class="keyword">typename</span> color_space_type&lt;Image&gt;::type,</div><div class="line"><a name="l00737"></a><span class="lineno">  737</span>&#160;                <span class="keyword">typename</span> channel_mapping_type&lt;Image&gt;::type&gt;,</div><div class="line"><a name="l00738"></a><span class="lineno">  738</span>&#160;                L</div><div class="line"><a name="l00739"></a><span class="lineno">  739</span>&#160;            &gt;::type;</div><div class="line"><a name="l00740"></a><span class="lineno">  740</span>&#160;</div><div class="line"><a name="l00741"></a><span class="lineno">  741</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> <span class="keyword">const</span> planar = mp11::mp_if</div><div class="line"><a name="l00742"></a><span class="lineno">  742</span>&#160;        &lt;</div><div class="line"><a name="l00743"></a><span class="lineno">  743</span>&#160;            std::is_same&lt;IsPlanar, use_default&gt;,</div><div class="line"><a name="l00744"></a><span class="lineno">  744</span>&#160;            is_planar&lt;Image&gt;,</div><div class="line"><a name="l00745"></a><span class="lineno">  745</span>&#160;            IsPlanar</div><div class="line"><a name="l00746"></a><span class="lineno">  746</span>&#160;        &gt;::value;</div><div class="line"><a name="l00747"></a><span class="lineno">  747</span>&#160;</div><div class="line"><a name="l00748"></a><span class="lineno">  748</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00749"></a><span class="lineno">  749</span>&#160;    <span class="keyword">using</span> type = <span class="keyword">typename</span> <a class="code" href="classboost_1_1gil_1_1image.html">image_type&lt;channel_t, layout_t, planar&gt;::type</a>;</div><div class="line"><a name="l00750"></a><span class="lineno">  750</span>&#160;};</div><div class="line"><a name="l00751"></a><span class="lineno">  751</span>&#160;</div><div class="line"><a name="l00752"></a><span class="lineno">  752</span>&#160;}}  <span class="comment">// namespace boost::gil</span></div><div class="line"><a name="l00753"></a><span class="lineno">  753</span>&#160;</div><div class="line"><a name="l00754"></a><span class="lineno">  754</span>&#160;<span class="preprocessor">#endif</span></div><div class="ttc" id="structboost_1_1gil_1_1is__iterator__adaptor_html"><div class="ttname"><a href="structboost_1_1gil_1_1is__iterator__adaptor.html">boost::gil::is_iterator_adaptor</a></div><div class="ttdoc">metafunction predicate determining whether the given iterator is a plain one or an adaptor over anoth...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:35</div></div>
<div class="ttc" id="structboost_1_1gil_1_1packed__image__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1packed__image__type.html">boost::gil::packed_image_type</a></div><div class="ttdoc">Returns the type of an interleaved packed image: an image whose channels may not be byte-aligned,...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:392</div></div>
<div class="ttc" id="structboost_1_1gil_1_1pixel__reference__is__proxy_html"><div class="ttname"><a href="structboost_1_1gil_1_1pixel__reference__is__proxy.html">boost::gil::pixel_reference_is_proxy</a></div><div class="ttdoc">Determines whether the given pixel reference is a proxy class or a native C++ reference.</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:203</div></div>
<div class="ttc" id="structboost_1_1gil_1_1planar__pixel__reference_html"><div class="ttname"><a href="structboost_1_1gil_1_1planar__pixel__reference.html">boost::gil::planar_pixel_reference</a></div><div class="ttdoc">A reference proxy to a planar pixel.</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:26</div></div>
<div class="ttc" id="structboost_1_1gil_1_1image__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1image__type.html">boost::gil::image_type</a></div><div class="ttdoc">Returns the type of a homogeneous image given the channel type, layout, and whether it operates on pl...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:549</div></div>
<div class="ttc" id="structboost_1_1gil_1_1locator__is__step__in__x_html"><div class="ttname"><a href="structboost_1_1gil_1_1locator__is__step__in__x.html">boost::gil::locator_is_step_in_x</a></div><div class="ttdoc">Determines if the given locator has a horizontal step that could be set dynamically.</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:186</div></div>
<div class="ttc" id="structboost_1_1gil_1_1view__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1view__type.html">boost::gil::view_type</a></div><div class="ttdoc">Returns the type of a homogeneous view given the channel type, layout, whether it operates on planar ...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:538</div></div>
<div class="ttc" id="namespaceboost_html"><div class="ttname"><a href="namespaceboost.html">boost</a></div><div class="ttdoc">defined(BOOST_NO_CXX17_HDR_MEMORY_RESOURCE)</div><div class="ttdef"><b>Definition:</b> algorithm.hpp:36</div></div>
<div class="ttc" id="classboost_1_1gil_1_1image__view_html"><div class="ttname"><a href="classboost_1_1gil_1_1image__view.html">boost::gil::image_view</a></div><div class="ttdoc">A lightweight object that interprets memory as a 2D array of pixels. Models ImageViewConcept,...</div><div class="ttdef"><b>Definition:</b> image_view.hpp:53</div></div>
<div class="ttc" id="structboost_1_1gil_1_1iterator__adaptor__get__base_html"><div class="ttname"><a href="structboost_1_1gil_1_1iterator__adaptor__get__base.html">boost::gil::iterator_adaptor_get_base</a></div><div class="ttdoc">returns the base iterator for a given iterator adaptor. Provide an specialization when introducing ne...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:36</div></div>
<div class="ttc" id="structboost_1_1gil_1_1packed__image1__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1packed__image1__type.html">boost::gil::packed_image1_type</a></div><div class="ttdoc">Returns the type of a single-channel image given its bitfield type, the bit size of its channel and i...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:400</div></div>
<div class="ttc" id="structboost_1_1gil_1_1pixel_html"><div class="ttname"><a href="structboost_1_1gil_1_1pixel.html">boost::gil::pixel</a></div><div class="ttdoc">Represents a pixel value (a container of channels). Models: HomogeneousColorBaseValueConcept,...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:24</div></div>
<div class="ttc" id="structboost_1_1gil_1_1iterator__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1iterator__type.html">boost::gil::iterator_type</a></div><div class="ttdoc">Returns the type of a homogeneous iterator given the channel type, layout, whether it operates on pla...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:290</div></div>
<div class="ttc" id="structboost_1_1gil_1_1packed__image4__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1packed__image4__type.html">boost::gil::packed_image4_type</a></div><div class="ttdoc">Returns the type of a four channel image given its bitfield type, the bit size of its channels and it...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:421</div></div>
<div class="ttc" id="structboost_1_1gil_1_1packed__image2__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1packed__image2__type.html">boost::gil::packed_image2_type</a></div><div class="ttdoc">Returns the type of a two channel image given its bitfield type, the bit size of its channels and its...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:407</div></div>
<div class="ttc" id="structboost_1_1gil_1_1bit__aligned__image5__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1bit__aligned__image5__type.html">boost::gil::bit_aligned_image5_type</a></div><div class="ttdoc">Returns the type of a five channel bit-aligned image given the bit size of its channels and its layou...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:486</div></div>
<div class="ttc" id="classboost_1_1gil_1_1derived__iterator__type_html"><div class="ttname"><a href="classboost_1_1gil_1_1derived__iterator__type.html">boost::gil::derived_iterator_type</a></div><div class="ttdoc">Constructs a pixel iterator type from a source pixel iterator type by changing some of the properties...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:624</div></div>
<div class="ttc" id="structboost_1_1gil_1_1planar__pixel__iterator_html"><div class="ttname"><a href="structboost_1_1gil_1_1planar__pixel__iterator.html">boost::gil::planar_pixel_iterator</a></div><div class="ttdoc">An iterator over planar pixels. Models HomogeneousColorBaseConcept, PixelIteratorConcept,...</div><div class="ttdef"><b>Definition:</b> algorithm.hpp:40</div></div>
<div class="ttc" id="structboost_1_1gil_1_1iterator__is__basic_html"><div class="ttname"><a href="structboost_1_1gil_1_1iterator__is__basic.html">boost::gil::iterator_is_basic</a></div><div class="ttdoc">Determines if a given pixel iterator is basic Basic iterators must use gil::pixel (if interleaved),...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:76</div></div>
<div class="ttc" id="classboost_1_1gil_1_1derived__pixel__reference__type_html"><div class="ttname"><a href="classboost_1_1gil_1_1derived__pixel__reference__type.html">boost::gil::derived_pixel_reference_type</a></div><div class="ttdoc">Constructs a pixel reference type from a source pixel reference type by changing some of the properti...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:572</div></div>
<div class="ttc" id="structboost_1_1gil_1_1bit__aligned__image4__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1bit__aligned__image4__type.html">boost::gil::bit_aligned_image4_type</a></div><div class="ttdoc">Returns the type of a four channel bit-aligned image given the bit size of its channels and its layou...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:481</div></div>
<div class="ttc" id="classboost_1_1gil_1_1memory__based__2d__locator_html"><div class="ttname"><a href="classboost_1_1gil_1_1memory__based__2d__locator.html">boost::gil::memory_based_2d_locator</a></div><div class="ttdoc">Memory-based pixel locator. Models: PixelLocatorConcept,HasDynamicXStepTypeConcept,...</div><div class="ttdef"><b>Definition:</b> algorithm.hpp:44</div></div>
<div class="ttc" id="structboost_1_1gil_1_1type__from__x__iterator_html"><div class="ttname"><a href="structboost_1_1gil_1_1type__from__x__iterator.html">boost::gil::type_from_x_iterator</a></div><div class="ttdoc">Given a pixel iterator defining access to pixels along a row, returns the types of the corresponding ...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:302</div></div>
<div class="ttc" id="structboost_1_1gil_1_1image__is__basic_html"><div class="ttname"><a href="structboost_1_1gil_1_1image__is__basic.html">boost::gil::image_is_basic</a></div><div class="ttdoc">Basic images must use basic views and std::allocator.</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:138</div></div>
<div class="ttc" id="structboost_1_1gil_1_1pixel__reference__is__mutable_html"><div class="ttname"><a href="structboost_1_1gil_1_1pixel__reference__is__mutable.html">boost::gil::pixel_reference_is_mutable</a></div><div class="ttdoc">Determines if the given pixel reference is mutable (i.e. its channels can be changed)</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:229</div></div>
<div class="ttc" id="structboost_1_1gil_1_1locator__is__mutable_html"><div class="ttname"><a href="structboost_1_1gil_1_1locator__is__mutable.html">boost::gil::locator_is_mutable</a></div><div class="ttdoc">Determines if the given locator is mutable (i.e. its pixels can be changed)</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:240</div></div>
<div class="ttc" id="classboost_1_1gil_1_1derived__image__type_html"><div class="ttname"><a href="classboost_1_1gil_1_1derived__image__type.html">boost::gil::derived_image_type</a></div><div class="ttdoc">Constructs a homogeneous image type from a source image type by changing some of the properties....</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:722</div></div>
<div class="ttc" id="structboost_1_1gil_1_1layout_html"><div class="ttname"><a href="structboost_1_1gil_1_1layout.html">boost::gil::layout</a></div><div class="ttdoc">Represents a color space and ordering of channels in memory.</div><div class="ttdef"><b>Definition:</b> utilities.hpp:267</div></div>
<div class="ttc" id="classboost_1_1gil_1_1image_html"><div class="ttname"><a href="classboost_1_1gil_1_1image.html">boost::gil::image</a></div><div class="ttdoc">container interface over image view. Models ImageConcept, PixelBasedConcept</div><div class="ttdef"><b>Definition:</b> image.hpp:42</div></div>
<div class="ttc" id="structboost_1_1gil_1_1locator__is__basic_html"><div class="ttname"><a href="structboost_1_1gil_1_1locator__is__basic.html">boost::gil::locator_is_basic</a></div><div class="ttdoc">Determines if a given locator is basic. A basic locator is memory-based and has basic x_iterator and ...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:118</div></div>
<div class="ttc" id="structboost_1_1gil_1_1pixel__reference__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1pixel__reference__type.html">boost::gil::pixel_reference_type</a></div><div class="ttdoc">Returns the type of a homogeneous pixel reference given the channel type, layout, whether it operates...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:267</div></div>
<div class="ttc" id="structboost_1_1gil_1_1bit__aligned__image__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1bit__aligned__image__type.html">boost::gil::bit_aligned_image_type</a></div><div class="ttdoc">Returns the type of a packed image whose pixels may not be byte aligned. For example,...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:444</div></div>
<div class="ttc" id="structboost_1_1gil_1_1view__is__step__in__y_html"><div class="ttname"><a href="structboost_1_1gil_1_1view__is__step__in__y.html">boost::gil::view_is_step_in_y</a></div><div class="ttdoc">Determines if the given view has a vertical step that could be set dynamically.</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:198</div></div>
<div class="ttc" id="structboost_1_1gil_1_1view__is__mutable_html"><div class="ttname"><a href="structboost_1_1gil_1_1view__is__mutable.html">boost::gil::view_is_mutable</a></div><div class="ttdoc">Determines if the given view is mutable (i.e. its pixels can be changed)</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:243</div></div>
<div class="ttc" id="structboost_1_1gil_1_1channel__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1channel__type.html">boost::gil::channel_type</a></div><div class="ttdef"><b>Definition:</b> color_convert.hpp:31</div></div>
<div class="ttc" id="structboost_1_1gil_1_1pixel__value__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1pixel__value__type.html">boost::gil::pixel_value_type</a></div><div class="ttdoc">Returns the type of a homogeneous pixel given the channel type and layout.</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:492</div></div>
<div class="ttc" id="structboost_1_1gil_1_1locator__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1locator__type.html">boost::gil::locator_type</a></div><div class="ttdoc">Returns the type of a homogeneous locator given the channel type, layout, whether it operates on plan...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:527</div></div>
<div class="ttc" id="structboost_1_1gil_1_1iterator__is__mutable_html"><div class="ttname"><a href="structboost_1_1gil_1_1iterator__is__mutable.html">boost::gil::iterator_is_mutable</a></div><div class="ttdoc">Metafunction predicate returning whether the given iterator allows for changing its values.</div><div class="ttdef"><b>Definition:</b> pixel_iterator.hpp:49</div></div>
<div class="ttc" id="classboost_1_1gil_1_1derived__view__type_html"><div class="ttname"><a href="classboost_1_1gil_1_1derived__view__type.html">boost::gil::derived_view_type</a></div><div class="ttdoc">Constructs an image view type from a source view type by changing some of the properties....</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:673</div></div>
<div class="ttc" id="structboost_1_1gil_1_1packed__pixel__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1packed__pixel__type.html">boost::gil::packed_pixel_type</a></div><div class="ttdoc">Returns the type of a packed pixel given its bitfield type, the bit size of its channels and its layo...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:367</div></div>
<div class="ttc" id="structboost_1_1gil_1_1packed__pixel_html"><div class="ttname"><a href="structboost_1_1gil_1_1packed__pixel.html">boost::gil::packed_pixel</a></div><div class="ttdoc">Heterogeneous pixel value whose channel references can be constructed from the pixel bitfield and the...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:25</div></div>
<div class="ttc" id="structboost_1_1gil_1_1pixel__is__reference_html"><div class="ttname"><a href="structboost_1_1gil_1_1pixel__is__reference.html">boost::gil::pixel_is_reference</a></div><div class="ttdoc">Given a model of a pixel, determines whether the model represents a pixel reference (as opposed to pi...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:217</div></div>
<div class="ttc" id="structboost_1_1gil_1_1bit__aligned__image1__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1bit__aligned__image1__type.html">boost::gil::bit_aligned_image1_type</a></div><div class="ttdoc">Returns the type of a single-channel bit-aligned image given the bit size of its channel and its layo...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:466</div></div>
<div class="ttc" id="structboost_1_1gil_1_1pixel__reference__is__basic_html"><div class="ttname"><a href="structboost_1_1gil_1_1pixel__reference__is__basic.html">boost::gil::pixel_reference_is_basic</a></div><div class="ttdoc">Determines if a given pixel reference is basic Basic references must use gil::pixel&amp; (if interleaved)...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:58</div></div>
<div class="ttc" id="structboost_1_1gil_1_1packed__image3__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1packed__image3__type.html">boost::gil::packed_image3_type</a></div><div class="ttdoc">Returns the type of a three channel image given its bitfield type, the bit size of its channels and i...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:414</div></div>
<div class="ttc" id="structboost_1_1gil_1_1locator__is__step__in__y_html"><div class="ttname"><a href="structboost_1_1gil_1_1locator__is__step__in__y.html">boost::gil::locator_is_step_in_y</a></div><div class="ttdoc">Determines if the given locator has a vertical step that could be set dynamically.</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:190</div></div>
<div class="ttc" id="structboost_1_1gil_1_1view__is__basic_html"><div class="ttname"><a href="structboost_1_1gil_1_1view__is__basic.html">boost::gil::view_is_basic</a></div><div class="ttdoc">Basic views must be over basic locators.</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:130</div></div>
<div class="ttc" id="structboost_1_1gil_1_1iterator__is__step_html"><div class="ttname"><a href="structboost_1_1gil_1_1iterator__is__step.html">boost::gil::iterator_is_step</a></div><div class="ttdoc">Determines if the given iterator has a step that could be set dynamically.</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:149</div></div>
<div class="ttc" id="structboost_1_1gil_1_1bit__aligned__image2__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1bit__aligned__image2__type.html">boost::gil::bit_aligned_image2_type</a></div><div class="ttdoc">Returns the type of a two channel bit-aligned image given the bit size of its channels and its layout...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:471</div></div>
<div class="ttc" id="structboost_1_1gil_1_1iterator__type__from__pixel_html"><div class="ttname"><a href="structboost_1_1gil_1_1iterator__type__from__pixel.html">boost::gil::iterator_type_from_pixel</a></div><div class="ttdoc">Returns the type of a pixel iterator given the pixel type, whether it operates on planar data,...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:275</div></div>
<div class="ttc" id="structboost_1_1gil_1_1view__type__from__pixel_html"><div class="ttname"><a href="structboost_1_1gil_1_1view__type__from__pixel.html">boost::gil::view_type_from_pixel</a></div><div class="ttdoc">Returns the type of a view the pixel type, whether it operates on planar data and whether it has a st...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:557</div></div>
<div class="ttc" id="classboost_1_1gil_1_1memory__based__step__iterator_html"><div class="ttname"><a href="classboost_1_1gil_1_1memory__based__step__iterator.html">boost::gil::memory_based_step_iterator</a></div><div class="ttdoc">MEMORY-BASED STEP ITERATOR.</div><div class="ttdef"><b>Definition:</b> algorithm.hpp:42</div></div>
<div class="ttc" id="structboost_1_1gil_1_1bit__aligned__image3__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1bit__aligned__image3__type.html">boost::gil::bit_aligned_image3_type</a></div><div class="ttdoc">Returns the type of a three channel bit-aligned image given the bit size of its channels and its layo...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:476</div></div>
<div class="ttc" id="structboost_1_1gil_1_1view__is__step__in__x_html"><div class="ttname"><a href="structboost_1_1gil_1_1view__is__step__in__x.html">boost::gil::view_is_step_in_x</a></div><div class="ttdoc">Determines if the given view has a horizontal step that could be set dynamically.</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:194</div></div>
<div class="ttc" id="structboost_1_1gil_1_1packed__image5__type_html"><div class="ttname"><a href="structboost_1_1gil_1_1packed__image5__type.html">boost::gil::packed_image5_type</a></div><div class="ttdoc">Returns the type of a five channel image given its bitfield type, the bit size of its channels and it...</div><div class="ttdef"><b>Definition:</b> metafunctions.hpp:428</div></div>
</div><!-- fragment --></div><!-- contents -->
     <!-- HTML footer for doxygen 1.8.13-->
     <!-- start footer part -->
     <hr class="footer"/>
     <address class="footer">
      <small>
        Generated by &#160;<a href="http://www.doxygen.org/index.html">doxygen</a> 1.8.15
      </small>
    </address>
  </body>
</html>
