<!-- HTML header for doxygen 1.8.13-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=9"/>
    <meta name="generator" content="Doxygen 1.8.15"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Generic Image Library: utilities.hpp Source File</title>
    <link href="tabs.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-boost.css" rel="stylesheet" type="text/css"/>
  </head>
  <body>
    <div class="boost-header">
      <table border="0" cellpadding="7" cellspacing="0" width="100%" summary="header">
	<tr>
	  <td valign="top" width="300">
            <h3><a href="../index.html"><img alt="Boost GIL" src="../_static/gil.png" border="0"></a></h3>
	  </td>
	  <td ><h1 align="center"><a href="../index.html"></a></h1></td>
	  <td></td>
	</tr>
      </table>
    </div>
    <hr/>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_1878a3f4746a95c6aad317458cc7ef80.html">boost</a></li><li class="navelem"><a class="el" href="dir_df4750f408086f9b9c1b5ee4251365ff.html">gil</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">utilities.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">// Copyright 2005-2007 Adobe Systems Incorporated</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment">// Distributed under the Boost Software License, Version 1.0</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">// See accompanying file LICENSE_1_0.txt or copy at</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment">// http://www.boost.org/LICENSE_1_0.txt</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#ifndef BOOST_GIL_UTILITIES_HPP</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#define BOOST_GIL_UTILITIES_HPP</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;</div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#include &lt;boost/gil/detail/mp11.hpp&gt;</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;</div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#include &lt;boost/config.hpp&gt;</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#if defined(BOOST_CLANG)</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#pragma clang diagnostic push</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#pragma clang diagnostic ignored &quot;-Wconversion&quot;</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#if defined(BOOST_GCC) &amp;&amp; (BOOST_GCC &gt;= 40900)</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#pragma GCC diagnostic push</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#pragma GCC diagnostic ignored &quot;-Wconversion&quot;</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &lt;boost/iterator/iterator_adaptor.hpp&gt;</span></div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#include &lt;boost/iterator/iterator_facade.hpp&gt;</span></div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#if defined(BOOST_CLANG)</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma clang diagnostic pop</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#if defined(BOOST_GCC) &amp;&amp; (BOOST_GCC &gt;= 40900)</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#pragma GCC diagnostic pop</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#include &lt;algorithm&gt;</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#include &lt;cmath&gt;</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#include &lt;cstddef&gt;</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &lt;functional&gt;</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &lt;iterator&gt;</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#include &lt;utility&gt;</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#include &lt;type_traits&gt;</span></div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceboost.html">boost</a> { <span class="keyword">namespace </span>gil {</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="keyword">inline</span> std::ptrdiff_t iround(<span class="keywordtype">float</span> x)</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;{</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;    <span class="keywordflow">return</span> static_cast&lt;std::ptrdiff_t&gt;(x + (x &lt; 0.0f ? -0.5f : 0.5f));</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;}</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="keyword">inline</span> std::ptrdiff_t iround(<span class="keywordtype">double</span> x)</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;{</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    <span class="keywordflow">return</span> static_cast&lt;std::ptrdiff_t&gt;(x + (x &lt; 0.0 ? -0.5 : 0.5));</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;}</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="keyword">inline</span> std::ptrdiff_t ifloor(<span class="keywordtype">float</span> x)</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;{</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;    <span class="keywordflow">return</span> static_cast&lt;std::ptrdiff_t&gt;(std::floor(x));</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;}</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;<span class="keyword">inline</span> std::ptrdiff_t ifloor(<span class="keywordtype">double</span> x)</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;{</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;    <span class="keywordflow">return</span> static_cast&lt;std::ptrdiff_t&gt;(std::floor(x));</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;}</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="keyword">inline</span> std::ptrdiff_t iceil(<span class="keywordtype">float</span> x)</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;{</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;    <span class="keywordflow">return</span> static_cast&lt;std::ptrdiff_t&gt;(std::ceil(x));</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;}</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;<span class="keyword">inline</span> std::ptrdiff_t iceil(<span class="keywordtype">double</span> x)</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;{</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;    <span class="keywordflow">return</span> static_cast&lt;std::ptrdiff_t&gt;(std::ceil(x));</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;}</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;<span class="keyword">inline</span> T align(T val, std::size_t alignment)</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;{</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    <span class="keywordflow">return</span> val+(alignment - val%alignment)%alignment;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;}</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;<span class="keyword">template</span></div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;&lt;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;    <span class="keyword">typename</span> ConstT,</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;    <span class="keyword">typename</span> Value,</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;    <span class="keyword">typename</span> Reference,</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;    <span class="keyword">typename</span> ConstReference,</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;    <span class="keyword">typename</span> ArgType,</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;    <span class="keyword">typename</span> ResultType,</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;    <span class="keywordtype">bool</span> IsMutable</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;&gt;</div><div class="line"><a name="l00106"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1deref__base.html">  106</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1deref__base.html">deref_base</a></div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;{</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;    <span class="keyword">using</span> argument_type = ArgType;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;    <span class="keyword">using</span> result_type = ResultType;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;    <span class="keyword">using</span> const_t = ConstT;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;    <span class="keyword">using</span> value_type = Value;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;    <span class="keyword">using</span> reference = Reference;</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;    <span class="keyword">using</span> const_reference = ConstReference;</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;    <span class="keyword">static</span> constexpr <span class="keywordtype">bool</span> is_mutable = IsMutable;</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;};</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> D1, <span class="keyword">typename</span> D2&gt;</div><div class="line"><a name="l00121"></a><span class="lineno"><a class="line" href="classboost_1_1gil_1_1deref__compose.html">  121</a></span>&#160;<span class="keyword">class </span><a class="code" href="classboost_1_1gil_1_1deref__compose.html">deref_compose</a> : <span class="keyword">public</span> <a class="code" href="structboost_1_1gil_1_1deref__base.html">deref_base</a></div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;&lt;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;    deref_compose&lt;typename D1::const_t, typename D2::const_t&gt;,</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;    typename D1::value_type,</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    typename D1::reference,</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;    typename D1::const_reference,</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;    typename D2::argument_type,</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    typename D1::result_type,</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;    D1::is_mutable &amp;&amp; D2::is_mutable</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;&gt;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;{</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;    D1 _fn1;</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;    D2 _fn2;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;    <span class="keyword">using</span> argument_type = <span class="keyword">typename</span> D2::argument_type;</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;    <span class="keyword">using</span> result_type = <span class="keyword">typename</span> D1::result_type;</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;    <a class="code" href="classboost_1_1gil_1_1deref__compose.html">deref_compose</a>() = <span class="keywordflow">default</span>;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;    <a class="code" href="classboost_1_1gil_1_1deref__compose.html">deref_compose</a>(<span class="keyword">const</span> D1&amp; x, <span class="keyword">const</span> D2&amp; y) : _fn1(x), _fn2(y) {}</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    <a class="code" href="classboost_1_1gil_1_1deref__compose.html">deref_compose</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1gil_1_1deref__compose.html">deref_compose</a>&amp; dc)  : _fn1(dc._fn1), _fn2(dc._fn2) {}</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <span class="keyword">template</span> &lt;<span class="keyword">typename</span> _D1, <span class="keyword">typename</span> _D2&gt;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;    <a class="code" href="classboost_1_1gil_1_1deref__compose.html">deref_compose</a>(<span class="keyword">const</span> <a class="code" href="classboost_1_1gil_1_1deref__compose.html">deref_compose&lt;_D1,_D2&gt;</a>&amp; dc)</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        : _fn1(dc._fn1), _fn2(dc._fn2)</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;    {}</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    result_type operator()(argument_type x)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> _fn1(_fn2(x)); }</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;    result_type operator()(argument_type x)       { <span class="keywordflow">return</span> _fn1(_fn2(x)); }</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;};</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;<span class="comment">// reinterpret_cast is implementation-defined. Static cast is not.</span></div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> OutPtr, <span class="keyword">typename</span> In&gt;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;BOOST_FORCEINLINE</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;<span class="keyword">auto</span> gil_reinterpret_cast(In* p) -&gt; OutPtr</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;{</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;    <span class="keywordflow">return</span> static_cast&lt;OutPtr&gt;(static_cast&lt;void*&gt;(p));</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;}</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> OutPtr, <span class="keyword">typename</span> In&gt;</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;BOOST_FORCEINLINE</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;<span class="keyword">auto</span> gil_reinterpret_cast_c(In <span class="keyword">const</span>* p) -&gt; OutPtr <span class="keyword">const</span></div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;{</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;    <span class="keywordflow">return</span> static_cast&lt;OutPtr const&gt;(static_cast&lt;void const*&gt;(p));</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;}</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;<span class="keyword">namespace </span>detail {</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">class</span> InputIter, <span class="keyword">class</span> Size, <span class="keyword">class</span> OutputIter&gt;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;<span class="keyword">auto</span> _copy_n(InputIter first, Size count, OutputIter result, std::input_iterator_tag)</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;    -&gt; std::pair&lt;InputIter, OutputIter&gt;</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;{</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;   <span class="keywordflow">for</span> ( ; count &gt; 0; --count)</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;   {</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;      *result = *first;</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;      ++first;</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;      ++result;</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;   }</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;   <span class="keywordflow">return</span> std::pair&lt;InputIter, OutputIter&gt;(first, result);</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;}</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">class</span> RAIter, <span class="keyword">class</span> Size, <span class="keyword">class</span> OutputIter&gt;</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;<span class="keyword">inline</span> <span class="keyword">auto</span> _copy_n(RAIter first, Size count, OutputIter result, std::random_access_iterator_tag)</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;    -&gt; std::pair&lt;RAIter, OutputIter&gt;</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;{</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;   RAIter last = first + count;</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;   <span class="keywordflow">return</span> std::pair&lt;RAIter, OutputIter&gt;(last, <a class="code" href="group___s_t_l_optimizations.html#ga8e23fe06d31c7cce605e4bf8255e1ee9">std::copy</a>(first, last, result));</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;}</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">class</span> InputIter, <span class="keyword">class</span> Size, <span class="keyword">class</span> OutputIter&gt;</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;<span class="keyword">inline</span> <span class="keyword">auto</span> _copy_n(InputIter first, Size count, OutputIter result)</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;    -&gt; std::pair&lt;InputIter, OutputIter&gt;</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;{</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;   <span class="keywordflow">return</span> _copy_n(first, count, result, <span class="keyword">typename</span> std::iterator_traits&lt;InputIter&gt;::iterator_category());</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;}</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">class</span> InputIter, <span class="keyword">class</span> Size, <span class="keyword">class</span> OutputIter&gt;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;<span class="keyword">inline</span> <span class="keyword">auto</span> copy_n(InputIter first, Size count, OutputIter result)</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;    -&gt; std::pair&lt;InputIter, OutputIter&gt;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;{</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;    <span class="keywordflow">return</span> detail::_copy_n(first, count, result);</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;}</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00210"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1detail_1_1identity.html">  210</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1detail_1_1identity.html">identity</a></div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;{</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;    <span class="keyword">using</span> argument_type = T;</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;    <span class="keyword">using</span> result_type = T;</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;    <span class="keyword">const</span> T&amp; operator()(<span class="keyword">const</span> T&amp; val)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> val; }</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;};</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T1, <span class="keyword">typename</span> T2&gt;</div><div class="line"><a name="l00219"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1detail_1_1plus__asymmetric.html">  219</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1detail_1_1plus__asymmetric.html">plus_asymmetric</a> {</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;    <span class="keyword">using</span> first_argument_type = T1;</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;    <span class="keyword">using</span> second_argument_type = T2;</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;    <span class="keyword">using</span> result_type = T1;</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;    T1 operator()(T1 f1, T2 f2)<span class="keyword"> const</span></div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;<span class="keyword">    </span>{</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;        <span class="keywordflow">return</span> f1+f2;</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;    }</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;};</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00231"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1detail_1_1inc.html">  231</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1detail_1_1inc.html">inc</a></div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;{</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;    <span class="keyword">using</span> argument_type = T;</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;    <span class="keyword">using</span> result_type = T;</div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;    T operator()(T x)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> ++x; }</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;};</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00240"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1detail_1_1dec.html">  240</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1detail_1_1dec.html">dec</a></div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;{</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;    <span class="keyword">using</span> argument_type = T;</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;    <span class="keyword">using</span> result_type = T;</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;    T operator()(T x)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> --x; }</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;};</div><div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;<span class="comment">//         a given Boost.MP11-compatible list (or size if the type is not present)</span></div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Types, <span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00250"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1detail_1_1type__to__index.html">  250</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1detail_1_1type__to__index.html">type_to_index</a> : mp11::mp_find&lt;Types, T&gt;</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;{</div><div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;    static_assert(mp11::mp_contains&lt;Types, T&gt;::value, <span class="stringliteral">&quot;T should be element of Types&quot;</span>);</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;};</div><div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;</div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;} <span class="comment">// namespace detail</span></div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;<span class="keyword">template</span></div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;&lt;</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;    <span class="keyword">typename</span> ColorSpace,</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;    <span class="keyword">typename</span> ChannelMapping = mp11::mp_iota</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;    &lt;</div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;        std::integral_constant&lt;int, mp11::mp_size&lt;ColorSpace&gt;::value&gt;</div><div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;    &gt;</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;&gt;</div><div class="line"><a name="l00267"></a><span class="lineno"><a class="line" href="structboost_1_1gil_1_1layout.html">  267</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structboost_1_1gil_1_1layout.html">layout</a></div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;{</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;    <span class="keyword">using</span> color_space_t = ColorSpace;</div><div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;    <span class="keyword">using</span> channel_mapping_t = ChannelMapping;</div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;</div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;    static_assert(mp11::mp_size&lt;ColorSpace&gt;::value &gt; 0,</div><div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;        <span class="stringliteral">&quot;color space should not be empty sequence&quot;</span>);</div><div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;};</div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;</div><div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Value, <span class="keyword">typename</span> T1, <span class="keyword">typename</span> T2&gt;</div><div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;<span class="keywordtype">void</span> swap_proxy(T1&amp; left, T2&amp; right)</div><div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;{</div><div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;    Value tmp = left;</div><div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;    left = right;</div><div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;    right = tmp;</div><div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;}</div><div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;</div><div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;BOOST_FORCEINLINE <span class="keywordtype">bool</span> little_endian()</div><div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;{</div><div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;    <span class="keywordtype">short</span> tester = 0x0001;</div><div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;    <span class="keywordflow">return</span>  *(<span class="keywordtype">char</span>*)&amp;tester!=0;</div><div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;}</div><div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;BOOST_FORCEINLINE <span class="keywordtype">bool</span> big_endian()</div><div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;{</div><div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;    <span class="keywordflow">return</span> !little_endian();</div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;}</div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;</div><div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;}}  <span class="comment">// namespace boost::gil</span></div><div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;</div><div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;<span class="preprocessor">#endif</span></div><div class="ttc" id="structboost_1_1gil_1_1detail_1_1plus__asymmetric_html"><div class="ttname"><a href="structboost_1_1gil_1_1detail_1_1plus__asymmetric.html">boost::gil::detail::plus_asymmetric</a></div><div class="ttdoc">plus function object whose arguments may be of different type.</div><div class="ttdef"><b>Definition:</b> utilities.hpp:219</div></div>
<div class="ttc" id="namespaceboost_html"><div class="ttname"><a href="namespaceboost.html">boost</a></div><div class="ttdoc">defined(BOOST_NO_CXX17_HDR_MEMORY_RESOURCE)</div><div class="ttdef"><b>Definition:</b> algorithm.hpp:36</div></div>
<div class="ttc" id="structboost_1_1gil_1_1deref__base_html"><div class="ttname"><a href="structboost_1_1gil_1_1deref__base.html">boost::gil::deref_base</a></div><div class="ttdoc">Helper base class for pixel dereference adaptors.</div><div class="ttdef"><b>Definition:</b> utilities.hpp:106</div></div>
<div class="ttc" id="structboost_1_1gil_1_1detail_1_1dec_html"><div class="ttname"><a href="structboost_1_1gil_1_1detail_1_1dec.html">boost::gil::detail::dec</a></div><div class="ttdoc">operator– wrapped in a function object</div><div class="ttdef"><b>Definition:</b> utilities.hpp:240</div></div>
<div class="ttc" id="structboost_1_1gil_1_1detail_1_1identity_html"><div class="ttname"><a href="structboost_1_1gil_1_1detail_1_1identity.html">boost::gil::detail::identity</a></div><div class="ttdoc">identity taken from SGI STL.</div><div class="ttdef"><b>Definition:</b> utilities.hpp:210</div></div>
<div class="ttc" id="structboost_1_1gil_1_1detail_1_1inc_html"><div class="ttname"><a href="structboost_1_1gil_1_1detail_1_1inc.html">boost::gil::detail::inc</a></div><div class="ttdoc">operator++ wrapped in a function object</div><div class="ttdef"><b>Definition:</b> utilities.hpp:231</div></div>
<div class="ttc" id="structboost_1_1gil_1_1layout_html"><div class="ttname"><a href="structboost_1_1gil_1_1layout.html">boost::gil::layout</a></div><div class="ttdoc">Represents a color space and ordering of channels in memory.</div><div class="ttdef"><b>Definition:</b> utilities.hpp:267</div></div>
<div class="ttc" id="classboost_1_1gil_1_1deref__compose_html"><div class="ttname"><a href="classboost_1_1gil_1_1deref__compose.html">boost::gil::deref_compose</a></div><div class="ttdoc">Composes two dereference function objects. Similar to std::unary_compose but needs to pull some alias...</div><div class="ttdef"><b>Definition:</b> utilities.hpp:121</div></div>
<div class="ttc" id="group___s_t_l_optimizations_html_ga8e23fe06d31c7cce605e4bf8255e1ee9"><div class="ttname"><a href="group___s_t_l_optimizations.html#ga8e23fe06d31c7cce605e4bf8255e1ee9">std::copy</a></div><div class="ttdeci">BOOST_FORCEINLINE auto copy(boost::gil::pixel&lt; T, CS &gt; *first, boost::gil::pixel&lt; T, CS &gt; *last, boost::gil::pixel&lt; T, CS &gt; *dst) -&gt; boost::gil::pixel&lt; T, CS &gt; *</div><div class="ttdoc">Copy when both src and dst are interleaved and of the same type can be just memmove.</div><div class="ttdef"><b>Definition:</b> algorithm.hpp:145</div></div>
<div class="ttc" id="structboost_1_1gil_1_1detail_1_1type__to__index_html"><div class="ttname"><a href="structboost_1_1gil_1_1detail_1_1type__to__index.html">boost::gil::detail::type_to_index</a></div><div class="ttdoc">Returns the index corresponding to the first occurrance of a given given type in.</div><div class="ttdef"><b>Definition:</b> utilities.hpp:250</div></div>
</div><!-- fragment --></div><!-- contents -->
     <!-- HTML footer for doxygen 1.8.13-->
     <!-- start footer part -->
     <hr class="footer"/>
     <address class="footer">
      <small>
        Generated by &#160;<a href="http://www.doxygen.org/index.html">doxygen</a> 1.8.15
      </small>
    </address>
  </body>
</html>
