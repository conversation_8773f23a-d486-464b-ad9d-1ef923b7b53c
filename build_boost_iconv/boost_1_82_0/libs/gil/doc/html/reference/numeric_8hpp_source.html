<!-- HTML header for doxygen 1.8.13-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=9"/>
    <meta name="generator" content="Doxygen 1.8.15"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Generic Image Library: numeric.hpp Source File</title>
    <link href="tabs.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-boost.css" rel="stylesheet" type="text/css"/>
  </head>
  <body>
    <div class="boost-header">
      <table border="0" cellpadding="7" cellspacing="0" width="100%" summary="header">
	<tr>
	  <td valign="top" width="300">
            <h3><a href="../index.html"><img alt="Boost GIL" src="../_static/gil.png" border="0"></a></h3>
	  </td>
	  <td ><h1 align="center"><a href="../index.html"></a></h1></td>
	  <td></td>
	</tr>
      </table>
    </div>
    <hr/>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_1878a3f4746a95c6aad317458cc7ef80.html">boost</a></li><li class="navelem"><a class="el" href="dir_df4750f408086f9b9c1b5ee4251365ff.html">gil</a></li><li class="navelem"><a class="el" href="dir_29d7d1879aced8e7726ea9a11f3c7333.html">image_processing</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">numeric.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">// Copyright 2019 Olzhas Zhumabek &lt;<EMAIL>&gt;</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">// Copyright 2021 Pranam Lashkari &lt;<EMAIL>&gt;</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">// Use, modification and distribution are subject to the Boost Software License,</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment">// Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment">// http://www.boost.org/LICENSE_1_0.txt)</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#ifndef BOOST_GIL_IMAGE_PROCESSING_NUMERIC_HPP</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#define BOOST_GIL_IMAGE_PROCESSING_NUMERIC_HPP</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;</div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#include &lt;boost/gil/image_processing/kernel.hpp&gt;</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#include &lt;boost/gil/image_processing/convolve.hpp&gt;</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#include &lt;boost/gil/image_view.hpp&gt;</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#include &lt;boost/gil/typedefs.hpp&gt;</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &lt;boost/gil/detail/math.hpp&gt;</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment">// fixes ambigious call to std::abs, https://stackoverflow.com/a/30084734/4593721</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &lt;cstdlib&gt;</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &lt;cmath&gt;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceboost.html">boost</a> { <span class="keyword">namespace </span>gil {</div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="keyword">inline</span> <span class="keywordtype">double</span> normalized_sinc(<span class="keywordtype">double</span> x)</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;{</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;    <span class="keywordflow">return</span> std::sin(x * boost::gil::detail::pi) / (x * boost::gil::detail::pi);</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;}</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="group___image_processing_math.html#gaaa4583e6d012a8b49ad1738cec762651">   46</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">double</span> <a class="code" href="group___image_processing_math.html#gaaa4583e6d012a8b49ad1738cec762651">lanczos</a>(<span class="keywordtype">double</span> x, std::ptrdiff_t a)</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;{</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;    <span class="comment">// means == but &lt;= avoids compiler warning</span></div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;    <span class="keywordflow">if</span> (0 &lt;= x &amp;&amp; x &lt;= 0)</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        <span class="keywordflow">return</span> 1;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;    <span class="keywordflow">if</span> (static_cast&lt;double&gt;(-a) &lt; x &amp;&amp; x &lt; static_cast&lt;double&gt;(a))</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        <span class="keywordflow">return</span> normalized_sinc(x) / normalized_sinc(x / static_cast&lt;double&gt;(a));</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;    <span class="keywordflow">return</span> 0;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;}</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="preprocessor">#if BOOST_WORKAROUND(BOOST_MSVC, &gt;= 1400)</span></div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="preprocessor">#pragma warning(push)</span></div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="preprocessor">#pragma warning(disable:4244) // &#39;argument&#39;: conversion from &#39;const Channel&#39; to &#39;BaseChannelValue&#39;, possible loss of data</span></div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> compute_tensor_entries(</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;    boost::gil::gray16s_view_t dx,</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;    boost::gil::gray16s_view_t dy,</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;    boost::gil::gray32f_view_t m11,</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;    boost::gil::gray32f_view_t m12_21,</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;    boost::gil::gray32f_view_t m22)</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;{</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;    <span class="keywordflow">for</span> (std::ptrdiff_t y = 0; y &lt; dx.height(); ++y) {</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;        <span class="keywordflow">for</span> (std::ptrdiff_t x = 0; x &lt; dx.width(); ++x) {</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;            <span class="keyword">auto</span> dx_value = dx(x, y);</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;            <span class="keyword">auto</span> dy_value = dy(x, y);</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;            m11(x, y) = dx_value * dx_value;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;            m12_21(x, y) = dx_value * dy_value;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;            m22(x, y) = dy_value * dy_value;</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        }</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;    }</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;}</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;<span class="preprocessor">#if BOOST_WORKAROUND(BOOST_MSVC, &gt;= 1400)</span></div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;<span class="preprocessor">#pragma warning(pop)</span></div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T = <span class="keywordtype">float</span>, <span class="keyword">typename</span> Allocator = std::allocator&lt;T&gt;&gt;</div><div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="group___image_processing_math.html#gadc5ef4af3a887d6f7161e9b67fad5f2f">   92</a></span>&#160;<span class="keyword">inline</span> <span class="keyword">auto</span> <a class="code" href="group___image_processing_math.html#gadc5ef4af3a887d6f7161e9b67fad5f2f">generate_normalized_mean</a>(std::size_t side_length)</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    -&gt; <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a></div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;{</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;    <span class="keywordflow">if</span> (side_length % 2 != 1)</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;        <span class="keywordflow">throw</span> std::invalid_argument(<span class="stringliteral">&quot;kernel dimensions should be odd and equal&quot;</span>);</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;    <span class="keyword">const</span> <span class="keywordtype">float</span> entry = 1.0f / static_cast&lt;float&gt;(side_length * side_length);</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;    <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a> result(side_length, side_length / 2, side_length / 2);</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">auto</span>&amp; cell: result) {</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;        cell = entry;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;    }</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;}</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T = <span class="keywordtype">float</span>, <span class="keyword">typename</span> Allocator = std::allocator&lt;T&gt;&gt;</div><div class="line"><a name="l00112"></a><span class="lineno"><a class="line" href="group___image_processing_math.html#ga4b14b0baaa6100c287c69bd1c9609364">  112</a></span>&#160;<span class="keyword">inline</span> <span class="keyword">auto</span> <a class="code" href="group___image_processing_math.html#ga4b14b0baaa6100c287c69bd1c9609364">generate_unnormalized_mean</a>(std::size_t side_length)</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;    -&gt; <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a></div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;{</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;    <span class="keywordflow">if</span> (side_length % 2 != 1)</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        <span class="keywordflow">throw</span> std::invalid_argument(<span class="stringliteral">&quot;kernel dimensions should be odd and equal&quot;</span>);</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;    <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a> result(side_length, side_length / 2, side_length / 2);</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">auto</span>&amp; cell: result) {</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        cell = 1.0f;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    }</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;}</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T = <span class="keywordtype">float</span>, <span class="keyword">typename</span> Allocator = std::allocator&lt;T&gt;&gt;</div><div class="line"><a name="l00132"></a><span class="lineno"><a class="line" href="group___image_processing_math.html#ga47a665d04688f2d4cb01fe96df76586c">  132</a></span>&#160;<span class="keyword">inline</span> <span class="keyword">auto</span> <a class="code" href="group___image_processing_math.html#ga47a665d04688f2d4cb01fe96df76586c">generate_gaussian_kernel</a>(std::size_t side_length, <span class="keywordtype">double</span> sigma)</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;    -&gt; <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a></div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;{</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;    <span class="keywordflow">if</span> (side_length % 2 != 1)</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;        <span class="keywordflow">throw</span> std::invalid_argument(<span class="stringliteral">&quot;kernel dimensions should be odd and equal&quot;</span>);</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;    <span class="keyword">const</span> <span class="keywordtype">double</span> denominator = 2 * boost::gil::detail::pi * sigma * sigma;</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;    <span class="keyword">auto</span> middle = side_length / 2;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;    std::vector&lt;T, Allocator&gt; values(side_length * side_length);</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    <span class="keywordflow">for</span> (std::size_t y = 0; y &lt; side_length; ++y)</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;    {</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;        <span class="keywordflow">for</span> (std::size_t x = 0; x &lt; side_length; ++x)</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        {</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;            <span class="keyword">const</span> <span class="keyword">auto</span> delta_x = middle &gt; x ? middle - x : x - middle;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;            <span class="keyword">const</span> <span class="keyword">auto</span> delta_y = middle &gt; y ? middle - y : y - middle;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;            <span class="keyword">const</span> <span class="keywordtype">double</span> power = (delta_x * delta_x +  delta_y * delta_y) / (2 * sigma * sigma);</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;            <span class="keyword">const</span> <span class="keywordtype">double</span> nominator = std::exp(-power);</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;            <span class="keyword">const</span> <span class="keywordtype">float</span> value = static_cast&lt;float&gt;(nominator / denominator);</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;            values[y * side_length + x] = value;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;        }</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;    }</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a>(values.begin(), values.size(), middle, middle);</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;}</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T = <span class="keywordtype">float</span>, <span class="keyword">typename</span> Allocator = std::allocator&lt;T&gt;&gt;</div><div class="line"><a name="l00165"></a><span class="lineno"><a class="line" href="group___image_processing_math.html#gabf1b8e4d5a0da875263ee3a9353355ac">  165</a></span>&#160;<span class="keyword">inline</span> <span class="keyword">auto</span> <a class="code" href="group___image_processing_math.html#gabf1b8e4d5a0da875263ee3a9353355ac">generate_dx_sobel</a>(<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> degree = 1)</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;    -&gt; <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a></div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;{</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;    <span class="keywordflow">switch</span> (degree)</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;    {</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;        <span class="keywordflow">case</span> 0:</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;        {</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;            <span class="keywordflow">return</span> detail::get_identity_kernel&lt;T, Allocator&gt;();</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;        }</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;        <span class="keywordflow">case</span> 1:</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;        {</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;            <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a> result(3, 1, 1);</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;            <a class="code" href="group___s_t_l_optimizations.html#ga8e23fe06d31c7cce605e4bf8255e1ee9">std::copy</a>(detail::dx_sobel.begin(), detail::dx_sobel.end(), result.begin());</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;            <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;        }</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;        <span class="keywordflow">default</span>:</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;            <span class="keywordflow">throw</span> std::logic_error(<span class="stringliteral">&quot;not supported yet&quot;</span>);</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;    }</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;    <span class="comment">//to not upset compiler</span></div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;    <span class="keywordflow">throw</span> std::runtime_error(<span class="stringliteral">&quot;unreachable statement&quot;</span>);</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;}</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T = <span class="keywordtype">float</span>, <span class="keyword">typename</span> Allocator = std::allocator&lt;T&gt;&gt;</div><div class="line"><a name="l00196"></a><span class="lineno"><a class="line" href="group___image_processing_math.html#gaf9d35e4abff3c1164917498d67d50a6f">  196</a></span>&#160;<span class="keyword">inline</span> <span class="keyword">auto</span> <a class="code" href="group___image_processing_math.html#gaf9d35e4abff3c1164917498d67d50a6f">generate_dx_scharr</a>(<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> degree = 1)</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;    -&gt; <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a></div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;{</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;    <span class="keywordflow">switch</span> (degree)</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;    {</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;        <span class="keywordflow">case</span> 0:</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;        {</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;            <span class="keywordflow">return</span> detail::get_identity_kernel&lt;T, Allocator&gt;();</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;        }</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;        <span class="keywordflow">case</span> 1:</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;        {</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;            <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a> result(3, 1, 1);</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;            <a class="code" href="group___s_t_l_optimizations.html#ga8e23fe06d31c7cce605e4bf8255e1ee9">std::copy</a>(detail::dx_scharr.begin(), detail::dx_scharr.end(), result.begin());</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;            <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;        }</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;        <span class="keywordflow">default</span>:</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;            <span class="keywordflow">throw</span> std::logic_error(<span class="stringliteral">&quot;not supported yet&quot;</span>);</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;    }</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;    <span class="comment">//to not upset compiler</span></div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;    <span class="keywordflow">throw</span> std::runtime_error(<span class="stringliteral">&quot;unreachable statement&quot;</span>);</div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;}</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T = <span class="keywordtype">float</span>, <span class="keyword">typename</span> Allocator = std::allocator&lt;T&gt;&gt;</div><div class="line"><a name="l00227"></a><span class="lineno"><a class="line" href="group___image_processing_math.html#gaf0e135a1b35302f2b4314be87022154e">  227</a></span>&#160;<span class="keyword">inline</span> <span class="keyword">auto</span> <a class="code" href="group___image_processing_math.html#gaf0e135a1b35302f2b4314be87022154e">generate_dy_sobel</a>(<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> degree = 1)</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;    -&gt; <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a></div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;{</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;    <span class="keywordflow">switch</span> (degree)</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;    {</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;        <span class="keywordflow">case</span> 0:</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;        {</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;            <span class="keywordflow">return</span> detail::get_identity_kernel&lt;T, Allocator&gt;();</div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;        }</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;        <span class="keywordflow">case</span> 1:</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;        {</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;            <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a> result(3, 1, 1);</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;            <a class="code" href="group___s_t_l_optimizations.html#ga8e23fe06d31c7cce605e4bf8255e1ee9">std::copy</a>(detail::dy_sobel.begin(), detail::dy_sobel.end(), result.begin());</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;            <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;        }</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;        <span class="keywordflow">default</span>:</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;            <span class="keywordflow">throw</span> std::logic_error(<span class="stringliteral">&quot;not supported yet&quot;</span>);</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;    }</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div><div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;    <span class="comment">//to not upset compiler</span></div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;    <span class="keywordflow">throw</span> std::runtime_error(<span class="stringliteral">&quot;unreachable statement&quot;</span>);</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;}</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T = <span class="keywordtype">float</span>, <span class="keyword">typename</span> Allocator = std::allocator&lt;T&gt;&gt;</div><div class="line"><a name="l00258"></a><span class="lineno"><a class="line" href="group___image_processing_math.html#ga98edf189b294eb2db50ed957ed84e815">  258</a></span>&#160;<span class="keyword">inline</span> <span class="keyword">auto</span> <a class="code" href="group___image_processing_math.html#ga98edf189b294eb2db50ed957ed84e815">generate_dy_scharr</a>(<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> degree = 1)</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;    -&gt; <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a> </div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;{</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;    <span class="keywordflow">switch</span> (degree)</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;    {</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;        <span class="keywordflow">case</span> 0:</div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;        {</div><div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;            <span class="keywordflow">return</span> detail::get_identity_kernel&lt;T, Allocator&gt;();</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;        }</div><div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;        <span class="keywordflow">case</span> 1:</div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;        {</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;            <a class="code" href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">detail::kernel_2d&lt;T, Allocator&gt;</a> result(3, 1, 1);</div><div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;            <a class="code" href="group___s_t_l_optimizations.html#ga8e23fe06d31c7cce605e4bf8255e1ee9">std::copy</a>(detail::dy_scharr.begin(), detail::dy_scharr.end(), result.begin());</div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;            <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;        }</div><div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;        <span class="keywordflow">default</span>:</div><div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;            <span class="keywordflow">throw</span> std::logic_error(<span class="stringliteral">&quot;not supported yet&quot;</span>);</div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;    }</div><div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;</div><div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;    <span class="comment">//to not upset compiler</span></div><div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;    <span class="keywordflow">throw</span> std::runtime_error(<span class="stringliteral">&quot;unreachable statement&quot;</span>);</div><div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;}</div><div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;</div><div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> GradientView, <span class="keyword">typename</span> OutputView&gt;</div><div class="line"><a name="l00291"></a><span class="lineno"><a class="line" href="group___image_processing_math.html#ga5b662d207dd54aba778c0f8c3697c025">  291</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">void</span> <a class="code" href="group___image_processing_math.html#ga5b662d207dd54aba778c0f8c3697c025">compute_hessian_entries</a>(</div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;    GradientView dx,</div><div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;    GradientView dy,</div><div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;    OutputView ddxx,</div><div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;    OutputView dxdy,</div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;    OutputView ddyy)</div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;{</div><div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;    <span class="keyword">auto</span> sobel_x = <a class="code" href="group___image_processing_math.html#gabf1b8e4d5a0da875263ee3a9353355ac">generate_dx_sobel</a>();</div><div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;    <span class="keyword">auto</span> sobel_y = <a class="code" href="group___image_processing_math.html#gaf0e135a1b35302f2b4314be87022154e">generate_dy_sobel</a>();</div><div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;    detail::convolve_2d(dx, sobel_x, ddxx);</div><div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;    detail::convolve_2d(dx, sobel_y, dxdy);</div><div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;    detail::convolve_2d(dy, sobel_y, ddyy);</div><div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;}</div><div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;</div><div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;}} <span class="comment">// namespace boost::gil</span></div><div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;</div><div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;<span class="preprocessor">#endif</span></div><div class="ttc" id="group___image_processing_math_html_gabf1b8e4d5a0da875263ee3a9353355ac"><div class="ttname"><a href="group___image_processing_math.html#gabf1b8e4d5a0da875263ee3a9353355ac">boost::gil::generate_dx_sobel</a></div><div class="ttdeci">auto generate_dx_sobel(unsigned int degree=1) -&gt; detail::kernel_2d&lt; T, Allocator &gt;</div><div class="ttdoc">Generates Sobel operator in horizontal directionGenerates a kernel which will represent Sobel operato...</div><div class="ttdef"><b>Definition:</b> numeric.hpp:165</div></div>
<div class="ttc" id="namespaceboost_html"><div class="ttname"><a href="namespaceboost.html">boost</a></div><div class="ttdoc">defined(BOOST_NO_CXX17_HDR_MEMORY_RESOURCE)</div><div class="ttdef"><b>Definition:</b> algorithm.hpp:36</div></div>
<div class="ttc" id="group___image_processing_math_html_gadc5ef4af3a887d6f7161e9b67fad5f2f"><div class="ttname"><a href="group___image_processing_math.html#gadc5ef4af3a887d6f7161e9b67fad5f2f">boost::gil::generate_normalized_mean</a></div><div class="ttdeci">auto generate_normalized_mean(std::size_t side_length) -&gt; detail::kernel_2d&lt; T, Allocator &gt;</div><div class="ttdoc">Generate mean kernelFills supplied view with normalized mean in which all entries will be equal to.</div><div class="ttdef"><b>Definition:</b> numeric.hpp:92</div></div>
<div class="ttc" id="group___image_processing_math_html_gaf9d35e4abff3c1164917498d67d50a6f"><div class="ttname"><a href="group___image_processing_math.html#gaf9d35e4abff3c1164917498d67d50a6f">boost::gil::generate_dx_scharr</a></div><div class="ttdeci">auto generate_dx_scharr(unsigned int degree=1) -&gt; detail::kernel_2d&lt; T, Allocator &gt;</div><div class="ttdoc">Generate Scharr operator in horizontal directionGenerates a kernel which will represent Scharr operat...</div><div class="ttdef"><b>Definition:</b> numeric.hpp:196</div></div>
<div class="ttc" id="group___image_processing_math_html_ga5b662d207dd54aba778c0f8c3697c025"><div class="ttname"><a href="group___image_processing_math.html#ga5b662d207dd54aba778c0f8c3697c025">boost::gil::compute_hessian_entries</a></div><div class="ttdeci">void compute_hessian_entries(GradientView dx, GradientView dy, OutputView ddxx, OutputView dxdy, OutputView ddyy)</div><div class="ttdoc">Compute xy gradient, and second order x and y gradientsHessian matrix is defined as a matrix of parti...</div><div class="ttdef"><b>Definition:</b> numeric.hpp:291</div></div>
<div class="ttc" id="group___image_processing_math_html_gaaa4583e6d012a8b49ad1738cec762651"><div class="ttname"><a href="group___image_processing_math.html#gaaa4583e6d012a8b49ad1738cec762651">boost::gil::lanczos</a></div><div class="ttdeci">double lanczos(double x, std::ptrdiff_t a)</div><div class="ttdoc">Lanczos response at point xLanczos response is defined as: x == 0: 1 -a &lt; x &amp;&amp; x &lt; a: 0 otherwise: no...</div><div class="ttdef"><b>Definition:</b> numeric.hpp:46</div></div>
<div class="ttc" id="group___image_processing_math_html_ga4b14b0baaa6100c287c69bd1c9609364"><div class="ttname"><a href="group___image_processing_math.html#ga4b14b0baaa6100c287c69bd1c9609364">boost::gil::generate_unnormalized_mean</a></div><div class="ttdeci">auto generate_unnormalized_mean(std::size_t side_length) -&gt; detail::kernel_2d&lt; T, Allocator &gt;</div><div class="ttdoc">Generate kernel with all 1sFills supplied view with 1s (ones)</div><div class="ttdef"><b>Definition:</b> numeric.hpp:112</div></div>
<div class="ttc" id="group___image_processing_math_html_gaf0e135a1b35302f2b4314be87022154e"><div class="ttname"><a href="group___image_processing_math.html#gaf0e135a1b35302f2b4314be87022154e">boost::gil::generate_dy_sobel</a></div><div class="ttdeci">auto generate_dy_sobel(unsigned int degree=1) -&gt; detail::kernel_2d&lt; T, Allocator &gt;</div><div class="ttdoc">Generates Sobel operator in vertical directionGenerates a kernel which will represent Sobel operator ...</div><div class="ttdef"><b>Definition:</b> numeric.hpp:227</div></div>
<div class="ttc" id="group___image_processing_math_html_ga98edf189b294eb2db50ed957ed84e815"><div class="ttname"><a href="group___image_processing_math.html#ga98edf189b294eb2db50ed957ed84e815">boost::gil::generate_dy_scharr</a></div><div class="ttdeci">auto generate_dy_scharr(unsigned int degree=1) -&gt; detail::kernel_2d&lt; T, Allocator &gt;</div><div class="ttdoc">Generate Scharr operator in vertical directionGenerates a kernel which will represent Scharr operator...</div><div class="ttdef"><b>Definition:</b> numeric.hpp:258</div></div>
<div class="ttc" id="group___image_processing_math_html_ga47a665d04688f2d4cb01fe96df76586c"><div class="ttname"><a href="group___image_processing_math.html#ga47a665d04688f2d4cb01fe96df76586c">boost::gil::generate_gaussian_kernel</a></div><div class="ttdeci">auto generate_gaussian_kernel(std::size_t side_length, double sigma) -&gt; detail::kernel_2d&lt; T, Allocator &gt;</div><div class="ttdoc">Generate Gaussian kernelFills supplied view with values taken from Gaussian distribution....</div><div class="ttdef"><b>Definition:</b> numeric.hpp:132</div></div>
<div class="ttc" id="group___s_t_l_optimizations_html_ga8e23fe06d31c7cce605e4bf8255e1ee9"><div class="ttname"><a href="group___s_t_l_optimizations.html#ga8e23fe06d31c7cce605e4bf8255e1ee9">std::copy</a></div><div class="ttdeci">BOOST_FORCEINLINE auto copy(boost::gil::pixel&lt; T, CS &gt; *first, boost::gil::pixel&lt; T, CS &gt; *last, boost::gil::pixel&lt; T, CS &gt; *dst) -&gt; boost::gil::pixel&lt; T, CS &gt; *</div><div class="ttdoc">Copy when both src and dst are interleaved and of the same type can be just memmove.</div><div class="ttdef"><b>Definition:</b> algorithm.hpp:145</div></div>
<div class="ttc" id="classboost_1_1gil_1_1detail_1_1kernel__2d_html"><div class="ttname"><a href="classboost_1_1gil_1_1detail_1_1kernel__2d.html">boost::gil::detail::kernel_2d</a></div><div class="ttdoc">variable-size kernel</div><div class="ttdef"><b>Definition:</b> kernel.hpp:272</div></div>
</div><!-- fragment --></div><!-- contents -->
     <!-- HTML footer for doxygen 1.8.13-->
     <!-- start footer part -->
     <hr class="footer"/>
     <address class="footer">
      <small>
        Generated by &#160;<a href="http://www.doxygen.org/index.html">doxygen</a> 1.8.15
      </small>
    </address>
  </body>
</html>
