<!-- HTML header for doxygen 1.8.13-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=9"/>
    <meta name="generator" content="Doxygen 1.8.15"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Generic Image Library: memory_based_2d_locator&lt; StepIterator &gt; Class Template Reference</title>
    <link href="tabs.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-boost.css" rel="stylesheet" type="text/css"/>
  </head>
  <body>
    <div class="boost-header">
      <table border="0" cellpadding="7" cellspacing="0" width="100%" summary="header">
	<tr>
	  <td valign="top" width="300">
            <h3><a href="../index.html"><img alt="Boost GIL" src="../_static/gil.png" border="0"></a></h3>
	  </td>
	  <td ><h1 align="center"><a href="../index.html"></a></h1></td>
	  <td></td>
	</tr>
      </table>
    </div>
    <hr/>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceboost.html">boost</a></li><li class="navelem"><b>gil</b></li><li class="navelem"><a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#friends">Friends</a> &#124;
<a href="classboost_1_1gil_1_1memory__based__2d__locator-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">memory_based_2d_locator&lt; StepIterator &gt; Class Template Reference<div class="ingroups"><a class="el" href="group___pixel_locator.html">Pixel Locator</a> &raquo; <a class="el" href="group___pixel_locator_model.html">Models</a><a class="el" href="group___pixel_based.html">PixelBased</a> &raquo;  &#124; <a class="el" href="group___pixel_based_model.html">Models</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Memory-based pixel locator. Models: <a class="el" href="structboost_1_1gil_1_1_pixel_locator_concept.html" title="GIL&#39;s 2-dimensional locator over immutable GIL pixels.">PixelLocatorConcept</a>,<a class="el" href="structboost_1_1gil_1_1_has_dynamic_x_step_type_concept.html" title="Concept for iterators, locators and views that can define a type just like the given iterator,...">HasDynamicXStepTypeConcept</a>,<a class="el" href="structboost_1_1gil_1_1_has_dynamic_y_step_type_concept.html" title="Concept for locators and views that can define a type just like the given locator or view,...">HasDynamicYStepTypeConcept</a>,<a class="el" href="structboost_1_1gil_1_1_has_transposed_type_concept.html" title="Concept for locators and views that can define a type just like the given locator or view,...">HasTransposedTypeConcept</a>The class takes a step iterator as a parameter. The step iterator provides navigation along the vertical axis while its base iterator provides horizontal navigation.  
 <a href="classboost_1_1gil_1_1memory__based__2d__locator.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="locator_8hpp_source.html">locator.hpp</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for memory_based_2d_locator&lt; StepIterator &gt;:</div>
<div class="dyncontent">
 <div class="center">
  <img src="classboost_1_1gil_1_1memory__based__2d__locator.png" usemap="#memory_5Fbased_5F2d_5Flocator_3C_20StepIterator_20_3E_map" alt=""/>
  <map id="memory_5Fbased_5F2d_5Flocator_3C_20StepIterator_20_3E_map" name="memory_5Fbased_5F2d_5Flocator_3C_20StepIterator_20_3E_map">
<area href="classboost_1_1gil_1_1pixel__2d__locator__base.html" alt="pixel_2d_locator_base&lt; memory_based_2d_locator&lt; StepIterator &gt;, iterator_adaptor_get_base&lt; StepIterator &gt;::type, StepIterator &gt;" shape="rect" coords="0,0,754,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a401101d1c66195bf53c23be02e63a9fa"><td class="memItemLeft" align="right" valign="top"><a id="a401101d1c66195bf53c23be02e63a9fa"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>parent_t</b> = <a class="el" href="classboost_1_1gil_1_1pixel__2d__locator__base.html">pixel_2d_locator_base</a>&lt; <a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>&lt; StepIterator &gt;, typename <a class="el" href="structboost_1_1gil_1_1iterator__adaptor__get__base.html">iterator_adaptor_get_base</a>&lt; StepIterator &gt;::type, StepIterator &gt;</td></tr>
<tr class="separator:a401101d1c66195bf53c23be02e63a9fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa1c5de48bf13e08f32553e70e14ac5bf"><td class="memItemLeft" align="right" valign="top"><a id="aa1c5de48bf13e08f32553e70e14ac5bf"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>const_t</b> = <a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>&lt; typename <a class="el" href="structboost_1_1gil_1_1const__iterator__type.html">const_iterator_type</a>&lt; StepIterator &gt;::type &gt;</td></tr>
<tr class="separator:aa1c5de48bf13e08f32553e70e14ac5bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af4751cc075ce9cd5f95f4179630f4db7"><td class="memItemLeft" align="right" valign="top"><a id="af4751cc075ce9cd5f95f4179630f4db7"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>coord_t</b> = typename parent_t::coord_t</td></tr>
<tr class="separator:af4751cc075ce9cd5f95f4179630f4db7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76daae71db955ccfc833b1894dfcda78"><td class="memItemLeft" align="right" valign="top"><a id="a76daae71db955ccfc833b1894dfcda78"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>x_coord_t</b> = typename parent_t::x_coord_t</td></tr>
<tr class="separator:a76daae71db955ccfc833b1894dfcda78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa252cae47c1b16bc27a51f8cce669ad0"><td class="memItemLeft" align="right" valign="top"><a id="aa252cae47c1b16bc27a51f8cce669ad0"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>y_coord_t</b> = typename parent_t::y_coord_t</td></tr>
<tr class="separator:aa252cae47c1b16bc27a51f8cce669ad0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a09cfecd72dadcb60a066bcb70be67b"><td class="memItemLeft" align="right" valign="top"><a id="a8a09cfecd72dadcb60a066bcb70be67b"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>x_iterator</b> = typename parent_t::x_iterator</td></tr>
<tr class="separator:a8a09cfecd72dadcb60a066bcb70be67b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa25c6cebb91b563d0bb729323a8e43a9"><td class="memItemLeft" align="right" valign="top"><a id="aa25c6cebb91b563d0bb729323a8e43a9"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>y_iterator</b> = typename parent_t::y_iterator</td></tr>
<tr class="separator:aa25c6cebb91b563d0bb729323a8e43a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ae9f9bc144b7bd7d8523c345ac34a58"><td class="memItemLeft" align="right" valign="top"><a id="a2ae9f9bc144b7bd7d8523c345ac34a58"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>difference_type</b> = typename <a class="el" href="classboost_1_1gil_1_1point.html">parent_t::difference_type</a></td></tr>
<tr class="separator:a2ae9f9bc144b7bd7d8523c345ac34a58"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd3670b743c9f80b9c2f6b3ccb5fe37c"><td class="memItemLeft" align="right" valign="top"><a id="afd3670b743c9f80b9c2f6b3ccb5fe37c"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>reference</b> = typename parent_t::reference</td></tr>
<tr class="separator:afd3670b743c9f80b9c2f6b3ccb5fe37c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36335102578a291d7b04c3f76bcc5e77"><td class="memItemLeft" align="right" valign="top"><a id="a36335102578a291d7b04c3f76bcc5e77"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>cached_location_t</b> = std::ptrdiff_t</td></tr>
<tr class="separator:a36335102578a291d7b04c3f76bcc5e77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td colspan="2" onclick="javascript:toggleInherit('pub_types_classboost_1_1gil_1_1pixel__2d__locator__base')"><img src="closed.png" alt="-"/>&#160;Public Types inherited from <a class="el" href="classboost_1_1gil_1_1pixel__2d__locator__base.html">pixel_2d_locator_base&lt; memory_based_2d_locator&lt; StepIterator &gt;, iterator_adaptor_get_base&lt; StepIterator &gt;::type, StepIterator &gt;</a></td></tr>
<tr class="memitem:ac8dff97b16cfc859526cbe71bb5e0456 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="ac8dff97b16cfc859526cbe71bb5e0456"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>x_iterator</b> = <a class="el" href="structboost_1_1gil_1_1iterator__adaptor__get__base.html">iterator_adaptor_get_base</a>&lt; StepIterator &gt;::type</td></tr>
<tr class="separator:ac8dff97b16cfc859526cbe71bb5e0456 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7db843d09573b19a4ec6c70cb699df2 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="ac7db843d09573b19a4ec6c70cb699df2"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>y_iterator</b> = StepIterator</td></tr>
<tr class="separator:ac7db843d09573b19a4ec6c70cb699df2 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4cf0b4cc09e331d088cba0918375298a inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a4cf0b4cc09e331d088cba0918375298a"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>value_type</b> = typename std::iterator_traits&lt; x_iterator &gt;::value_type</td></tr>
<tr class="separator:a4cf0b4cc09e331d088cba0918375298a inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae4866d87dad1cbb5ec690604b6d7e4c9 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="ae4866d87dad1cbb5ec690604b6d7e4c9"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>reference</b> = typename std::iterator_traits&lt; x_iterator &gt;::reference</td></tr>
<tr class="separator:ae4866d87dad1cbb5ec690604b6d7e4c9 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b1fc581762647c48e112deb1b043ab5 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a6b1fc581762647c48e112deb1b043ab5"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>coord_t</b> = typename std::iterator_traits&lt; x_iterator &gt;::<a class="el" href="classboost_1_1gil_1_1point.html">difference_type</a></td></tr>
<tr class="separator:a6b1fc581762647c48e112deb1b043ab5 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afb01b687e5fb83d62030e97956f78df8 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="afb01b687e5fb83d62030e97956f78df8"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>difference_type</b> = <a class="el" href="classboost_1_1gil_1_1point.html">point</a>&lt; coord_t &gt;</td></tr>
<tr class="separator:afb01b687e5fb83d62030e97956f78df8 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad3b7e554a3f62b6ae64646320bdf4707 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="ad3b7e554a3f62b6ae64646320bdf4707"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>point_t</b> = <a class="el" href="classboost_1_1gil_1_1point.html">difference_type</a></td></tr>
<tr class="separator:ad3b7e554a3f62b6ae64646320bdf4707 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af57b12c880c8e86ceb3ae23874ca5921 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="af57b12c880c8e86ceb3ae23874ca5921"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>x_coord_t</b> = typename point_t::template axis&lt; 0 &gt;::coord_t</td></tr>
<tr class="separator:af57b12c880c8e86ceb3ae23874ca5921 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1f91fc1334430fa1687684fcc107b480 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a1f91fc1334430fa1687684fcc107b480"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>y_coord_t</b> = typename point_t::template axis&lt; 1 &gt;::coord_t</td></tr>
<tr class="separator:a1f91fc1334430fa1687684fcc107b480 inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7192e60183dc5bbaaa9c9344e2b00ccc inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a7192e60183dc5bbaaa9c9344e2b00ccc"></a>
using&#160;</td><td class="memItemRight" valign="bottom"><b>cached_location_t</b> = <a class="el" href="classboost_1_1gil_1_1point.html">difference_type</a></td></tr>
<tr class="separator:a7192e60183dc5bbaaa9c9344e2b00ccc inherit pub_types_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ac2c9b2764bf9fd23c1992234c112d960"><td class="memItemLeft" align="right" valign="top"><a id="ac2c9b2764bf9fd23c1992234c112d960"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>memory_based_2d_locator</b> (const StepIterator &amp;yit)</td></tr>
<tr class="separator:ac2c9b2764bf9fd23c1992234c112d960"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b752ab53eb9e889fb895655a966cb3b"><td class="memTemplParams" colspan="2"><a id="a0b752ab53eb9e889fb895655a966cb3b"></a>
template&lt;typename SI &gt; </td></tr>
<tr class="memitem:a0b752ab53eb9e889fb895655a966cb3b"><td class="memTemplItemLeft" align="right" valign="top">&#160;</td><td class="memTemplItemRight" valign="bottom"><b>memory_based_2d_locator</b> (const <a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>&lt; SI &gt; &amp;loc, coord_t y_step)</td></tr>
<tr class="separator:a0b752ab53eb9e889fb895655a966cb3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a783be33dde25e847076010bad6d79a6c"><td class="memTemplParams" colspan="2"><a id="a783be33dde25e847076010bad6d79a6c"></a>
template&lt;typename SI &gt; </td></tr>
<tr class="memitem:a783be33dde25e847076010bad6d79a6c"><td class="memTemplItemLeft" align="right" valign="top">&#160;</td><td class="memTemplItemRight" valign="bottom"><b>memory_based_2d_locator</b> (const <a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>&lt; SI &gt; &amp;loc, coord_t x_step, coord_t y_step, bool transpose=false)</td></tr>
<tr class="separator:a783be33dde25e847076010bad6d79a6c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a59b376d31ac977d9c6eeb355233112"><td class="memItemLeft" align="right" valign="top"><a id="a2a59b376d31ac977d9c6eeb355233112"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>memory_based_2d_locator</b> (x_iterator xit, std::ptrdiff_t row_bytes)</td></tr>
<tr class="separator:a2a59b376d31ac977d9c6eeb355233112"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad6c18dc848130253be0092ad120b8a15"><td class="memTemplParams" colspan="2"><a id="ad6c18dc848130253be0092ad120b8a15"></a>
template&lt;typename X &gt; </td></tr>
<tr class="memitem:ad6c18dc848130253be0092ad120b8a15"><td class="memTemplItemLeft" align="right" valign="top">&#160;</td><td class="memTemplItemRight" valign="bottom"><b>memory_based_2d_locator</b> (const <a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>&lt; X &gt; &amp;pl)</td></tr>
<tr class="separator:ad6c18dc848130253be0092ad120b8a15"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a583cb0c7f3715920e0295e8fd4e9ad7c"><td class="memItemLeft" align="right" valign="top"><a id="a583cb0c7f3715920e0295e8fd4e9ad7c"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>memory_based_2d_locator</b> (const <a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a> &amp;pl)</td></tr>
<tr class="separator:a583cb0c7f3715920e0295e8fd4e9ad7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af0c36b48879208b63059ca2506a69c9d"><td class="memItemLeft" align="right" valign="top"><a id="af0c36b48879208b63059ca2506a69c9d"></a>
<a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (<a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a> const &amp;other)=default</td></tr>
<tr class="separator:af0c36b48879208b63059ca2506a69c9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41b0fb9b0107b46f7afc96170835f2a2"><td class="memItemLeft" align="right" valign="top"><a id="a41b0fb9b0107b46f7afc96170835f2a2"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator==</b> (const <a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">this_t</a> &amp;p) const</td></tr>
<tr class="separator:a41b0fb9b0107b46f7afc96170835f2a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c798b7a0d0d64f2eb83064ef4580193"><td class="memItemLeft" align="right" valign="top"><a id="a6c798b7a0d0d64f2eb83064ef4580193"></a>
x_iterator const  &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>x</b> () const</td></tr>
<tr class="separator:a6c798b7a0d0d64f2eb83064ef4580193"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7739793ed82edb77f2672851243daad8"><td class="memItemLeft" align="right" valign="top"><a id="a7739793ed82edb77f2672851243daad8"></a>
y_iterator const  &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>y</b> () const</td></tr>
<tr class="separator:a7739793ed82edb77f2672851243daad8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a72169bdc650ac7eda288398e4ce1d7f8"><td class="memItemLeft" align="right" valign="top"><a id="a72169bdc650ac7eda288398e4ce1d7f8"></a>
x_iterator &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>x</b> ()</td></tr>
<tr class="separator:a72169bdc650ac7eda288398e4ce1d7f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7ca2bdfc5a0be2b150b22dddfd9b10f"><td class="memItemLeft" align="right" valign="top"><a id="ac7ca2bdfc5a0be2b150b22dddfd9b10f"></a>
y_iterator &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>y</b> ()</td></tr>
<tr class="separator:ac7ca2bdfc5a0be2b150b22dddfd9b10f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac677b8d3ec02b5e28f5b485c6688f347"><td class="memItemLeft" align="right" valign="top"><a id="ac677b8d3ec02b5e28f5b485c6688f347"></a>
x_iterator&#160;</td><td class="memItemRight" valign="bottom"><b>x_at</b> (x_coord_t dx, y_coord_t dy) const</td></tr>
<tr class="separator:ac677b8d3ec02b5e28f5b485c6688f347"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9254747c0198383ecf39c10f4a7844c5"><td class="memItemLeft" align="right" valign="top"><a id="a9254747c0198383ecf39c10f4a7844c5"></a>
x_iterator&#160;</td><td class="memItemRight" valign="bottom"><b>x_at</b> (const difference_type &amp;d) const</td></tr>
<tr class="separator:a9254747c0198383ecf39c10f4a7844c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc76e6bdf3e317e1c068cea90a4486cb"><td class="memItemLeft" align="right" valign="top"><a id="adc76e6bdf3e317e1c068cea90a4486cb"></a>
<a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">this_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>xy_at</b> (x_coord_t dx, y_coord_t dy) const</td></tr>
<tr class="separator:adc76e6bdf3e317e1c068cea90a4486cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a90aa2d340b8a035ecc96761ab7368ec6"><td class="memItemLeft" align="right" valign="top"><a id="a90aa2d340b8a035ecc96761ab7368ec6"></a>
<a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">this_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>xy_at</b> (const difference_type &amp;d) const</td></tr>
<tr class="separator:a90aa2d340b8a035ecc96761ab7368ec6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a589233c96e618890b464e6ff4d53f36d"><td class="memItemLeft" align="right" valign="top"><a id="a589233c96e618890b464e6ff4d53f36d"></a>
reference&#160;</td><td class="memItemRight" valign="bottom"><b>operator()</b> (x_coord_t dx, y_coord_t dy) const</td></tr>
<tr class="separator:a589233c96e618890b464e6ff4d53f36d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad6377cfeb4cde7ec3c0d98ca3ee943a"><td class="memItemLeft" align="right" valign="top"><a id="aad6377cfeb4cde7ec3c0d98ca3ee943a"></a>
reference&#160;</td><td class="memItemRight" valign="bottom"><b>operator[]</b> (const difference_type &amp;d) const</td></tr>
<tr class="separator:aad6377cfeb4cde7ec3c0d98ca3ee943a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4aec2ad9cc0351fd9aab6c1abe0d126f"><td class="memItemLeft" align="right" valign="top"><a id="a4aec2ad9cc0351fd9aab6c1abe0d126f"></a>
<a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">this_t</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator+=</b> (const difference_type &amp;d)</td></tr>
<tr class="separator:a4aec2ad9cc0351fd9aab6c1abe0d126f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd08a61d0bf0e45c4b4a7dd0fe4c7f39"><td class="memItemLeft" align="right" valign="top"><a id="abd08a61d0bf0e45c4b4a7dd0fe4c7f39"></a>
<a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">this_t</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator-=</b> (const difference_type &amp;d)</td></tr>
<tr class="separator:abd08a61d0bf0e45c4b4a7dd0fe4c7f39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf9358069730bd5cd114f5cc07ee1f02"><td class="memItemLeft" align="right" valign="top"><a id="abf9358069730bd5cd114f5cc07ee1f02"></a>
cached_location_t&#160;</td><td class="memItemRight" valign="bottom"><b>cache_location</b> (const difference_type &amp;d) const</td></tr>
<tr class="separator:abf9358069730bd5cd114f5cc07ee1f02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e1034acfdb761f1457673463f2b8610"><td class="memItemLeft" align="right" valign="top"><a id="a2e1034acfdb761f1457673463f2b8610"></a>
cached_location_t&#160;</td><td class="memItemRight" valign="bottom"><b>cache_location</b> (x_coord_t dx, y_coord_t dy) const</td></tr>
<tr class="separator:a2e1034acfdb761f1457673463f2b8610"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0ab0119182747d1d7aa0694669f3f98"><td class="memItemLeft" align="right" valign="top"><a id="ab0ab0119182747d1d7aa0694669f3f98"></a>
reference&#160;</td><td class="memItemRight" valign="bottom"><b>operator[]</b> (const cached_location_t &amp;loc) const</td></tr>
<tr class="separator:ab0ab0119182747d1d7aa0694669f3f98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a562fedd363455c7b2ce067c778907b8e"><td class="memItemLeft" align="right" valign="top"><a id="a562fedd363455c7b2ce067c778907b8e"></a>
std::ptrdiff_t&#160;</td><td class="memItemRight" valign="bottom"><b>row_size</b> () const</td></tr>
<tr class="separator:a562fedd363455c7b2ce067c778907b8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1bfcc6f25047a10aebe79f7fe97c3877"><td class="memItemLeft" align="right" valign="top"><a id="a1bfcc6f25047a10aebe79f7fe97c3877"></a>
std::ptrdiff_t&#160;</td><td class="memItemRight" valign="bottom"><b>pixel_size</b> () const</td></tr>
<tr class="separator:a1bfcc6f25047a10aebe79f7fe97c3877"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae9a9166d450a99162128a10e40098d97"><td class="memItemLeft" align="right" valign="top"><a id="ae9a9166d450a99162128a10e40098d97"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_1d_traversable</b> (x_coord_t width) const</td></tr>
<tr class="separator:ae9a9166d450a99162128a10e40098d97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6f5b7c6cc6a3e326ef5d311889028e8"><td class="memItemLeft" align="right" valign="top"><a id="ae6f5b7c6cc6a3e326ef5d311889028e8"></a>
std::ptrdiff_t&#160;</td><td class="memItemRight" valign="bottom"><b>y_distance_to</b> (<a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">this_t</a> const &amp;p2, x_coord_t xDiff) const</td></tr>
<tr class="separator:ae6f5b7c6cc6a3e326ef5d311889028e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classboost_1_1gil_1_1pixel__2d__locator__base.html">pixel_2d_locator_base&lt; memory_based_2d_locator&lt; StepIterator &gt;, iterator_adaptor_get_base&lt; StepIterator &gt;::type, StepIterator &gt;</a></td></tr>
<tr class="memitem:a2fc5cf8788f95bf50cdab2e272fc7708 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a2fc5cf8788f95bf50cdab2e272fc7708"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator!=</b> (const <a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>&lt; StepIterator &gt; &amp;p) const</td></tr>
<tr class="separator:a2fc5cf8788f95bf50cdab2e272fc7708 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac677b8d3ec02b5e28f5b485c6688f347 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="ac677b8d3ec02b5e28f5b485c6688f347"></a>
x_iterator&#160;</td><td class="memItemRight" valign="bottom"><b>x_at</b> (x_coord_t dx, y_coord_t dy) const</td></tr>
<tr class="separator:ac677b8d3ec02b5e28f5b485c6688f347 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9254747c0198383ecf39c10f4a7844c5 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a9254747c0198383ecf39c10f4a7844c5"></a>
x_iterator&#160;</td><td class="memItemRight" valign="bottom"><b>x_at</b> (const <a class="el" href="classboost_1_1gil_1_1point.html">difference_type</a> &amp;d) const</td></tr>
<tr class="separator:a9254747c0198383ecf39c10f4a7844c5 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd3d3c6fcce3ce7c72de0d2059fd015e inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="abd3d3c6fcce3ce7c72de0d2059fd015e"></a>
y_iterator&#160;</td><td class="memItemRight" valign="bottom"><b>y_at</b> (x_coord_t dx, y_coord_t dy) const</td></tr>
<tr class="separator:abd3d3c6fcce3ce7c72de0d2059fd015e inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c22db3e04d46f370dae91de935233fb inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a4c22db3e04d46f370dae91de935233fb"></a>
y_iterator&#160;</td><td class="memItemRight" valign="bottom"><b>y_at</b> (const <a class="el" href="classboost_1_1gil_1_1point.html">difference_type</a> &amp;d) const</td></tr>
<tr class="separator:a4c22db3e04d46f370dae91de935233fb inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a26fb7b21c165511095079ac298816209 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a26fb7b21c165511095079ac298816209"></a>
<a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>&lt; StepIterator &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>xy_at</b> (x_coord_t dx, y_coord_t dy) const</td></tr>
<tr class="separator:a26fb7b21c165511095079ac298816209 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7cd646736e5f090adc2ae141a5f690c9 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a7cd646736e5f090adc2ae141a5f690c9"></a>
<a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>&lt; StepIterator &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>xy_at</b> (const <a class="el" href="classboost_1_1gil_1_1point.html">difference_type</a> &amp;d) const</td></tr>
<tr class="separator:a7cd646736e5f090adc2ae141a5f690c9 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a99149c579785498e03160ecb1a41609d inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a99149c579785498e03160ecb1a41609d"></a>
axis&lt; D &gt;::iterator &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>axis_iterator</b> ()</td></tr>
<tr class="separator:a99149c579785498e03160ecb1a41609d inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afbe31de642fbd6534226beb9eaeeba17 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="afbe31de642fbd6534226beb9eaeeba17"></a>
axis&lt; D &gt;::iterator const &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>axis_iterator</b> () const</td></tr>
<tr class="separator:afbe31de642fbd6534226beb9eaeeba17 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d065d04d2d4ec9fb1d213243330f0c6 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a2d065d04d2d4ec9fb1d213243330f0c6"></a>
axis&lt; D &gt;::iterator&#160;</td><td class="memItemRight" valign="bottom"><b>axis_iterator</b> (<a class="el" href="classboost_1_1gil_1_1point.html">point_t</a> const &amp;p) const</td></tr>
<tr class="separator:a2d065d04d2d4ec9fb1d213243330f0c6 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a589233c96e618890b464e6ff4d53f36d inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a589233c96e618890b464e6ff4d53f36d"></a>
reference&#160;</td><td class="memItemRight" valign="bottom"><b>operator()</b> (x_coord_t dx, y_coord_t dy) const</td></tr>
<tr class="separator:a589233c96e618890b464e6ff4d53f36d inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad6377cfeb4cde7ec3c0d98ca3ee943a inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="aad6377cfeb4cde7ec3c0d98ca3ee943a"></a>
reference&#160;</td><td class="memItemRight" valign="bottom"><b>operator[]</b> (const <a class="el" href="classboost_1_1gil_1_1point.html">difference_type</a> &amp;d) const</td></tr>
<tr class="separator:aad6377cfeb4cde7ec3c0d98ca3ee943a inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5b18bb8c3a765334dd1e3bd2eb8374f3 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a5b18bb8c3a765334dd1e3bd2eb8374f3"></a>
reference&#160;</td><td class="memItemRight" valign="bottom"><b>operator *</b> () const</td></tr>
<tr class="separator:a5b18bb8c3a765334dd1e3bd2eb8374f3 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5183ba70dbe786e8dd0cb258b73ed2d2 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a5183ba70dbe786e8dd0cb258b73ed2d2"></a>
<a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>&lt; StepIterator &gt; &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator+=</b> (const <a class="el" href="classboost_1_1gil_1_1point.html">difference_type</a> &amp;d)</td></tr>
<tr class="separator:a5183ba70dbe786e8dd0cb258b73ed2d2 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae4adb8167f7979861b075323379317de inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="ae4adb8167f7979861b075323379317de"></a>
<a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>&lt; StepIterator &gt; &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator-=</b> (const <a class="el" href="classboost_1_1gil_1_1point.html">difference_type</a> &amp;d)</td></tr>
<tr class="separator:ae4adb8167f7979861b075323379317de inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a889204668e3638b29078a7c51563f8e3 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a889204668e3638b29078a7c51563f8e3"></a>
<a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>&lt; StepIterator &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>operator+</b> (const <a class="el" href="classboost_1_1gil_1_1point.html">difference_type</a> &amp;d) const</td></tr>
<tr class="separator:a889204668e3638b29078a7c51563f8e3 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad00224b883bb26060c9b155e1b6b582d inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="ad00224b883bb26060c9b155e1b6b582d"></a>
<a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html">memory_based_2d_locator</a>&lt; StepIterator &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>operator-</b> (const <a class="el" href="classboost_1_1gil_1_1point.html">difference_type</a> &amp;d) const</td></tr>
<tr class="separator:ad00224b883bb26060c9b155e1b6b582d inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf9358069730bd5cd114f5cc07ee1f02 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="abf9358069730bd5cd114f5cc07ee1f02"></a>
<a class="el" href="classboost_1_1gil_1_1point.html">cached_location_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>cache_location</b> (const <a class="el" href="classboost_1_1gil_1_1point.html">difference_type</a> &amp;d) const</td></tr>
<tr class="separator:abf9358069730bd5cd114f5cc07ee1f02 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e1034acfdb761f1457673463f2b8610 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="a2e1034acfdb761f1457673463f2b8610"></a>
<a class="el" href="classboost_1_1gil_1_1point.html">cached_location_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>cache_location</b> (x_coord_t dx, y_coord_t dy) const</td></tr>
<tr class="separator:a2e1034acfdb761f1457673463f2b8610 inherit pub_methods_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="friends"></a>
Friends</h2></td></tr>
<tr class="memitem:aa3d47826a98177d5ae9c665603ab5f57"><td class="memTemplParams" colspan="2"><a id="aa3d47826a98177d5ae9c665603ab5f57"></a>
template&lt;typename X &gt; </td></tr>
<tr class="memitem:aa3d47826a98177d5ae9c665603ab5f57"><td class="memTemplItemLeft" align="right" valign="top">class&#160;</td><td class="memTemplItemRight" valign="bottom"><b>memory_based_2d_locator</b></td></tr>
<tr class="separator:aa3d47826a98177d5ae9c665603ab5f57"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_static_attribs_classboost_1_1gil_1_1pixel__2d__locator__base"><td colspan="2" onclick="javascript:toggleInherit('pub_static_attribs_classboost_1_1gil_1_1pixel__2d__locator__base')"><img src="closed.png" alt="-"/>&#160;Static Public Attributes inherited from <a class="el" href="classboost_1_1gil_1_1pixel__2d__locator__base.html">pixel_2d_locator_base&lt; memory_based_2d_locator&lt; StepIterator &gt;, iterator_adaptor_get_base&lt; StepIterator &gt;::type, StepIterator &gt;</a></td></tr>
<tr class="memitem:acf81c27b3fffa67d66b47f46979b607e inherit pub_static_attribs_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memItemLeft" align="right" valign="top"><a id="acf81c27b3fffa67d66b47f46979b607e"></a>
static const std::size_t&#160;</td><td class="memItemRight" valign="bottom"><b>num_dimensions</b></td></tr>
<tr class="separator:acf81c27b3fffa67d66b47f46979b607e inherit pub_static_attribs_classboost_1_1gil_1_1pixel__2d__locator__base"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;typename StepIterator&gt;<br />
class boost::gil::memory_based_2d_locator&lt; StepIterator &gt;</h3>

<p>Memory-based pixel locator. Models: <a class="el" href="structboost_1_1gil_1_1_pixel_locator_concept.html" title="GIL&#39;s 2-dimensional locator over immutable GIL pixels.">PixelLocatorConcept</a>,<a class="el" href="structboost_1_1gil_1_1_has_dynamic_x_step_type_concept.html" title="Concept for iterators, locators and views that can define a type just like the given iterator,...">HasDynamicXStepTypeConcept</a>,<a class="el" href="structboost_1_1gil_1_1_has_dynamic_y_step_type_concept.html" title="Concept for locators and views that can define a type just like the given locator or view,...">HasDynamicYStepTypeConcept</a>,<a class="el" href="structboost_1_1gil_1_1_has_transposed_type_concept.html" title="Concept for locators and views that can define a type just like the given locator or view,...">HasTransposedTypeConcept</a></p>
<p>The class takes a step iterator as a parameter. The step iterator provides navigation along the vertical axis while its base iterator provides horizontal navigation. </p>
<p>Each instantiation is optimal in terms of size and efficiency. For example, xy locator over interleaved rgb image results in a step iterator consisting of one std::ptrdiff_t for the row size and one native pointer (8 bytes total). ++locator.x() resolves to pointer increment. At the other extreme, a 2D navigation of the even pixels of a planar CMYK image results in a step iterator consisting of one std::ptrdiff_t for the doubled row size, and one step iterator consisting of one std::ptrdiff_t for the horizontal step of two and a CMYK <a class="el" href="structboost_1_1gil_1_1planar__pixel__iterator.html" title="An iterator over planar pixels. Models HomogeneousColorBaseConcept, PixelIteratorConcept,...">planar_pixel_iterator</a> consisting of 4 pointers (24 bytes). In this case ++locator.x() results in four native pointer additions.</p>
<p>Note also that <code><a class="el" href="classboost_1_1gil_1_1memory__based__2d__locator.html" title="Memory-based pixel locator. Models: PixelLocatorConcept,HasDynamicXStepTypeConcept,...">memory_based_2d_locator</a></code> does not require that its element type be a pixel. It could be instantiated with an iterator whose <code>value_type</code> models only <code><a class="el" href="structboost_1_1gil_1_1_regular.html" title="Concept for type regularity requirement.">Regular</a></code>. In this case the locator models the weaker <a class="el" href="structboost_1_1gil_1_1_random_access2_d_locator_concept.html" title="2-dimensional locator over immutable values">RandomAccess2DLocatorConcept</a>, and does not model <a class="el" href="structboost_1_1gil_1_1_pixel_based_concept.html" title="Concept for all pixel-based GIL constructs.">PixelBasedConcept</a>. Many generic algorithms don't require the elements to be pixels. </p>
</div><hr/>The documentation for this class was generated from the following files:<ul>
<li><a class="el" href="algorithm_8hpp_source.html">algorithm.hpp</a></li>
<li><a class="el" href="locator_8hpp_source.html">locator.hpp</a></li>
</ul>
</div><!-- contents -->
     <!-- HTML footer for doxygen 1.8.13-->
     <!-- start footer part -->
     <hr class="footer"/>
     <address class="footer">
      <small>
        Generated by &#160;<a href="http://www.doxygen.org/index.html">doxygen</a> 1.8.15
      </small>
    </address>
  </body>
</html>
