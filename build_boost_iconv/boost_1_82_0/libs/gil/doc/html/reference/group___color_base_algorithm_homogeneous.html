<!-- HTML header for doxygen 1.8.13-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=9"/>
    <meta name="generator" content="Doxygen 1.8.15"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Generic Image Library: element_type, element_reference_type, element_const_reference_type</title>
    <link href="tabs.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-boost.css" rel="stylesheet" type="text/css"/>
  </head>
  <body>
    <div class="boost-header">
      <table border="0" cellpadding="7" cellspacing="0" width="100%" summary="header">
	<tr>
	  <td valign="top" width="300">
            <h3><a href="../index.html"><img alt="Boost GIL" src="../_static/gil.png" border="0"></a></h3>
	  </td>
	  <td ><h1 align="center"><a href="../index.html"></a></h1></td>
	  <td></td>
	</tr>
      </table>
    </div>
    <hr/>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a>  </div>
  <div class="headertitle">
<div class="title">element_type, element_reference_type, element_const_reference_type<div class="ingroups"><a class="el" href="group___color_base.html">ColorBase</a> &raquo; <a class="el" href="group___color_base_algorithm.html">Algorithms and Utility Functions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Types for homogeneous color bases.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1gil_1_1element__type.html">element_type&lt; ColorBase &gt;</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specifies the element type of a homogeneous color base.  <a href="structboost_1_1gil_1_1element__type.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1gil_1_1element__reference__type.html">element_reference_type&lt; ColorBase &gt;</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specifies the return type of the mutable element accessor at_c of a homogeneous color base.  <a href="structboost_1_1gil_1_1element__reference__type.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1gil_1_1element__const__reference__type.html">element_const_reference_type&lt; ColorBase &gt;</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Specifies the return type of the constant element accessor at_c of a homogeneous color base.  <a href="structboost_1_1gil_1_1element__const__reference__type.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Types for homogeneous color bases. </p>
<p><a class="el" href="structboost_1_1gil_1_1element__type.html" title="Specifies the element type of a homogeneous color base.">element_type</a>, <a class="el" href="structboost_1_1gil_1_1element__reference__type.html" title="Specifies the return type of the mutable element accessor at_c of a homogeneous color base.">element_reference_type</a>, <a class="el" href="structboost_1_1gil_1_1element__const__reference__type.html" title="Specifies the return type of the constant element accessor at_c of a homogeneous color base.">element_const_reference_type</a>: Support for homogeneous color bases</p>
<p>Example: </p><div class="fragment"><div class="line"><span class="keyword">using</span> element_t = element_type&lt;rgb8c_planar_ptr_t&gt;::type;</div><div class="line">static_assert(std::is_same&lt;element_t, const uint8_t*&gt;::value, <span class="stringliteral">&quot;&quot;</span>);</div></div><!-- fragment --> </div><!-- contents -->
     <!-- HTML footer for doxygen 1.8.13-->
     <!-- start footer part -->
     <hr class="footer"/>
     <address class="footer">
      <small>
        Generated by &#160;<a href="http://www.doxygen.org/index.html">doxygen</a> 1.8.15
      </small>
    </address>
  </body>
</html>
