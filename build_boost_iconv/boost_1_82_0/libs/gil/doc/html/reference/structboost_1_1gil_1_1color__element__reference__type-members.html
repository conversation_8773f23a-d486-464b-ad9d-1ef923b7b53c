<!-- HTML header for doxygen 1.8.13-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=9"/>
    <meta name="generator" content="Doxygen 1.8.15"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Generic Image Library: Member List</title>
    <link href="tabs.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-boost.css" rel="stylesheet" type="text/css"/>
  </head>
  <body>
    <div class="boost-header">
      <table border="0" cellpadding="7" cellspacing="0" width="100%" summary="header">
	<tr>
	  <td valign="top" width="300">
            <h3><a href="../index.html"><img alt="Boost GIL" src="../_static/gil.png" border="0"></a></h3>
	  </td>
	  <td ><h1 align="center"><a href="../index.html"></a></h1></td>
	  <td></td>
	</tr>
      </table>
    </div>
    <hr/>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceboost.html">boost</a></li><li class="navelem"><b>gil</b></li><li class="navelem"><a class="el" href="structboost_1_1gil_1_1color__element__reference__type.html">color_element_reference_type</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">color_element_reference_type&lt; ColorBase, Color &gt; Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="structboost_1_1gil_1_1color__element__reference__type.html">color_element_reference_type&lt; ColorBase, Color &gt;</a>, including all inherited members.</p>
<table class="directory">
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>channel_mapping_t</b> typedef (defined in <a class="el" href="structboost_1_1gil_1_1kth__semantic__element__reference__type.html">kth_semantic_element_reference_type&lt; ColorBase, color_index_type&lt; ColorBase, Color &gt;::value &gt;</a>)</td><td class="entry"><a class="el" href="structboost_1_1gil_1_1kth__semantic__element__reference__type.html">kth_semantic_element_reference_type&lt; ColorBase, color_index_type&lt; ColorBase, Color &gt;::value &gt;</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>get</b>(ColorBase &amp;cb) (defined in <a class="el" href="structboost_1_1gil_1_1kth__semantic__element__reference__type.html">kth_semantic_element_reference_type&lt; ColorBase, color_index_type&lt; ColorBase, Color &gt;::value &gt;</a>)</td><td class="entry"><a class="el" href="structboost_1_1gil_1_1kth__semantic__element__reference__type.html">kth_semantic_element_reference_type&lt; ColorBase, color_index_type&lt; ColorBase, Color &gt;::value &gt;</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>semantic_index</b> (defined in <a class="el" href="structboost_1_1gil_1_1kth__semantic__element__reference__type.html">kth_semantic_element_reference_type&lt; ColorBase, color_index_type&lt; ColorBase, Color &gt;::value &gt;</a>)</td><td class="entry"><a class="el" href="structboost_1_1gil_1_1kth__semantic__element__reference__type.html">kth_semantic_element_reference_type&lt; ColorBase, color_index_type&lt; ColorBase, Color &gt;::value &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>type</b> typedef (defined in <a class="el" href="structboost_1_1gil_1_1kth__semantic__element__reference__type.html">kth_semantic_element_reference_type&lt; ColorBase, color_index_type&lt; ColorBase, Color &gt;::value &gt;</a>)</td><td class="entry"><a class="el" href="structboost_1_1gil_1_1kth__semantic__element__reference__type.html">kth_semantic_element_reference_type&lt; ColorBase, color_index_type&lt; ColorBase, Color &gt;::value &gt;</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
     <!-- HTML footer for doxygen 1.8.13-->
     <!-- start footer part -->
     <hr class="footer"/>
     <address class="footer">
      <small>
        Generated by &#160;<a href="http://www.doxygen.org/index.html">doxygen</a> 1.8.15
      </small>
    </address>
  </body>
</html>
