<!-- HTML header for doxygen 1.8.13-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=9"/>
    <meta name="generator" content="Doxygen 1.8.15"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Generic Image Library: ChannelsCompatibleConcept&lt; Channel1, Channel2 &gt; Struct Template Reference</title>
    <link href="tabs.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-boost.css" rel="stylesheet" type="text/css"/>
  </head>
  <body>
    <div class="boost-header">
      <table border="0" cellpadding="7" cellspacing="0" width="100%" summary="header">
	<tr>
	  <td valign="top" width="300">
            <h3><a href="../index.html"><img alt="Boost GIL" src="../_static/gil.png" border="0"></a></h3>
	  </td>
	  <td ><h1 align="center"><a href="../index.html"></a></h1></td>
	  <td></td>
	</tr>
      </table>
    </div>
    <hr/>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceboost.html">boost</a></li><li class="navelem"><b>gil</b></li><li class="navelem"><a class="el" href="structboost_1_1gil_1_1_channels_compatible_concept.html">ChannelsCompatibleConcept</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structboost_1_1gil_1_1_channels_compatible_concept-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">ChannelsCompatibleConcept&lt; Channel1, Channel2 &gt; Struct Template Reference<div class="ingroups"><a class="el" href="group___channel.html">Channel</a> &raquo; <a class="el" href="group___channel_concept.html">Concepts</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Channels are compatible if their associated value types (ignoring constness and references) are the same.  
 <a href="structboost_1_1gil_1_1_channels_compatible_concept.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="concepts_2channel_8hpp_source.html">channel.hpp</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ab0a0dbf6ca9028bbbb2240cad5882537"><td class="memItemLeft" align="right" valign="top"><a id="ab0a0dbf6ca9028bbbb2240cad5882537"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>constraints</b> ()</td></tr>
<tr class="separator:ab0a0dbf6ca9028bbbb2240cad5882537"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;typename Channel1, typename Channel2&gt;<br />
struct boost::gil::ChannelsCompatibleConcept&lt; Channel1, Channel2 &gt;</h3>

<p>Channels are compatible if their associated value types (ignoring constness and references) are the same. </p>
<div class="fragment"><div class="line">concept ChannelsCompatibleConcept&lt;ChannelConcept T1, ChannelConcept T2&gt;</div><div class="line">{</div><div class="line">    where SameType&lt;T1::value_type, T2::value_type&gt;;</div><div class="line">};</div></div><!-- fragment --> </div><hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="concepts_2channel_8hpp_source.html">concepts/channel.hpp</a></li>
</ul>
</div><!-- contents -->
     <!-- HTML footer for doxygen 1.8.13-->
     <!-- start footer part -->
     <hr class="footer"/>
     <address class="footer">
      <small>
        Generated by &#160;<a href="http://www.doxygen.org/index.html">doxygen</a> 1.8.15
      </small>
    </address>
  </body>
</html>
