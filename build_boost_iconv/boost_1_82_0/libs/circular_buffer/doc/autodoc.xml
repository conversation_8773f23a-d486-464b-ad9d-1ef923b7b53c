<?xml version="1.0" standalone="yes"?>
<library-reference id="boost_circular_buffer_c___reference"><title>Boost.Circular_buffer C++ Reference</title><header name="boost/circular_buffer.hpp">
<para>Includes &lt;boost/circular_buffer/base.hpp&gt; </para><macro name="BOOST_CB_ENABLE_DEBUG"><description><para>Debug support control. </para></description></macro>
</header>
<header name="boost/circular_buffer/base.hpp">
<namespace name="boost">
<class name="circular_buffer"><template>
      <template-type-parameter name="T"><purpose><para>The type of the elements stored in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para></purpose></template-type-parameter>
      <template-type-parameter name="Alloc"><purpose><para>The allocator type used for all internal memory management. </para></purpose></template-type-parameter>
    </template><inherit access="private">empty_value&lt; Alloc &gt;</inherit><purpose>Circular buffer - a STL compliant container. </purpose><description><para>
<formalpara><title>Type Requirements T</title><para>The <computeroutput>T</computeroutput> has to be <ulink url="https://www.boost.org/sgi/stl/Assignable.html">SGIAssignable</ulink> (SGI STL defined combination of <ulink url="../../../utility/Assignable.html">Assignable</ulink> and <ulink url="../../../utility/CopyConstructible.html">CopyConstructible</ulink>). Moreover <computeroutput>T</computeroutput> has to be <ulink url="https://www.boost.org/sgi/stl/DefaultConstructible.html">DefaultConstructible</ulink> if supplied as a default parameter when invoking some of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>'s methods e.g. <computeroutput>insert(iterator pos, const value_type&amp; item = value_type())</computeroutput>. And <ulink url="https://www.boost.org/sgi/stl/EqualityComparable.html">EqualityComparable</ulink> and/or <ulink url="../../../utility/LessThanComparable.html">LessThanComparable</ulink> if the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be compared with another container. </para>
</formalpara>

<formalpara><title>Type Requirements Alloc</title><para>The <computeroutput>Alloc</computeroutput> has to meet the allocator requirements imposed by STL. </para>
</formalpara>
<formalpara><title>Default Alloc</title><para>std::allocator&lt;T&gt;</para>
</formalpara>
For detailed documentation of the <classname alt="boost::circular_buffer">circular_buffer</classname> visit: <ulink url="http://www.boost.org/libs/circular_buffer/doc/circular_buffer.html">http://www.boost.org/libs/circular_buffer/doc/circular_buffer.html</ulink> </para></description><typedef name="this_type"><purpose>The type of this <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;</type></typedef>
<typedef name="value_type"><purpose>The type of elements stored in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><type>Alloc::value_type</type></typedef>
<typedef name="pointer"><purpose>A pointer to an element. </purpose><type>allocator_pointer&lt; Alloc &gt;::type</type></typedef>
<typedef name="const_pointer"><purpose>A const pointer to the element. </purpose><type>allocator_const_pointer&lt; Alloc &gt;::type</type></typedef>
<typedef name="reference"><purpose>A reference to an element. </purpose><type>value_type &amp;</type></typedef>
<typedef name="const_reference"><purpose>A const reference to an element. </purpose><type>const value_type &amp;</type></typedef>
<typedef name="difference_type"><purpose>The distance type. </purpose><description><para>(A signed integral type used to represent the distance between two iterators.) </para></description><type>allocator_difference_type&lt; Alloc &gt;::type</type></typedef>
<typedef name="size_type"><purpose>The size type. </purpose><description><para>(An unsigned integral type that can represent any non-negative value of the container's distance type.) </para></description><type>allocator_size_type&lt; Alloc &gt;::type</type></typedef>
<typedef name="allocator_type"><purpose>The type of an allocator used in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><type>Alloc</type></typedef>
<typedef name="const_iterator"><purpose>A const (random access) iterator used to iterate through the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><type>cb_details::iterator&lt; <classname>circular_buffer</classname>&lt; T, Alloc &gt;, cb_details::const_traits&lt; Alloc &gt; &gt;</type></typedef>
<typedef name="iterator"><purpose>A (random access) iterator used to iterate through the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><type>cb_details::iterator&lt; <classname>circular_buffer</classname>&lt; T, Alloc &gt;, cb_details::nonconst_traits&lt; Alloc &gt; &gt;</type></typedef>
<typedef name="const_reverse_iterator"><purpose>A const iterator used to iterate backwards through a <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><type>std::reverse_iterator&lt; const_iterator &gt;</type></typedef>
<typedef name="reverse_iterator"><purpose>An iterator used to iterate backwards through a <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><type>std::reverse_iterator&lt; iterator &gt;</type></typedef>
<typedef name="array_range"><purpose>An array range. </purpose><description><para>(A typedef for the <ulink url="https://www.boost.org/sgi/stl/pair.html"><computeroutput>std::pair</computeroutput></ulink> where its first element is a pointer to a beginning of an array and its second element represents a size of the array.) </para></description><type>std::pair&lt; pointer, size_type &gt;</type></typedef>
<typedef name="const_array_range"><purpose>A range of a const array. </purpose><description><para>(A typedef for the <ulink url="https://www.boost.org/sgi/stl/pair.html"><computeroutput>std::pair</computeroutput></ulink> where its first element is a pointer to a beginning of a const array and its second element represents a size of the const array.) </para></description><type>std::pair&lt; const_pointer, size_type &gt;</type></typedef>
<typedef name="capacity_type"><purpose>The capacity type. </purpose><description><para>(Same as <computeroutput>size_type</computeroutput> - defined for consistency with the __cbso class. </para></description><type>size_type</type></typedef>
<typedef name="param_value_type"><purpose>A type representing the "best" way to pass the value_type to a method. </purpose><type>const value_type &amp;</type></typedef>
<typedef name="rvalue_type"><description><para>A type representing rvalue from param type. On compilers without rvalue references support this type is the Boost.Moves type used for emulation. </para></description><type>value_type &amp;&amp;</type></typedef>
<method-group name="public member functions">
<method name="get_allocator" cv="const noexcept"><type>allocator_type</type><purpose>Get the allocator. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>get_allocator()</computeroutput> for obtaining an allocator reference. </para>
</para>
</para></description><returns><para>The allocator. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="get_allocator" cv="noexcept"><type>allocator_type &amp;</type><purpose>Get the allocator reference. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<note><para>This method was added in order to optimize obtaining of the allocator with a state, although use of stateful allocators in STL is discouraged. </para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>get_allocator() const</computeroutput> </para>
</para>
</para></description><returns><para>A reference to the allocator. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="begin" cv="noexcept"><type>iterator</type><purpose>Get the iterator pointing to the beginning of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>end()</computeroutput>, <computeroutput>rbegin()</computeroutput>, <computeroutput>rend()</computeroutput> </para>
</para>
</para></description><returns><para>A random access iterator pointing to the first element of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is empty it returns an iterator equal to the one returned by <computeroutput>end()</computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="end" cv="noexcept"><type>iterator</type><purpose>Get the iterator pointing to the end of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>begin()</computeroutput>, <computeroutput>rbegin()</computeroutput>, <computeroutput>rend()</computeroutput> </para>
</para>
</para></description><returns><para>A random access iterator pointing to the element "one behind" the last element of the <computeroutput> <classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is empty it returns an iterator equal to the one returned by <computeroutput>begin()</computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="begin" cv="const noexcept"><type>const_iterator</type><purpose>Get the const iterator pointing to the beginning of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>end() const</computeroutput>, <computeroutput>rbegin() const</computeroutput>, <computeroutput>rend() const</computeroutput> </para>
</para>
</para></description><returns><para>A const random access iterator pointing to the first element of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is empty it returns an iterator equal to the one returned by <computeroutput>end() const</computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="cbegin" cv="const noexcept"><type>const_iterator</type></method>
<method name="end" cv="const noexcept"><type>const_iterator</type><purpose>Get the const iterator pointing to the end of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>begin() const</computeroutput>, <computeroutput>rbegin() const</computeroutput>, <computeroutput>rend() const</computeroutput> </para>
</para>
</para></description><returns><para>A const random access iterator pointing to the element "one behind" the last element of the <computeroutput> <classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is empty it returns an iterator equal to the one returned by <computeroutput>begin() const</computeroutput> const. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="cend" cv="const noexcept"><type>const_iterator</type></method>
<method name="rbegin" cv="noexcept"><type>reverse_iterator</type><purpose>Get the iterator pointing to the beginning of the "reversed" <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rend()</computeroutput>, <computeroutput>begin()</computeroutput>, <computeroutput>end()</computeroutput> </para>
</para>
</para></description><returns><para>A reverse random access iterator pointing to the last element of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is empty it returns an iterator equal to the one returned by <computeroutput>rend()</computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="rend" cv="noexcept"><type>reverse_iterator</type><purpose>Get the iterator pointing to the end of the "reversed" <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rbegin()</computeroutput>, <computeroutput>begin()</computeroutput>, <computeroutput>end()</computeroutput> </para>
</para>
</para></description><returns><para>A reverse random access iterator pointing to the element "one before" the first element of the <computeroutput> <classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is empty it returns an iterator equal to the one returned by <computeroutput>rbegin()</computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="rbegin" cv="const noexcept"><type>const_reverse_iterator</type><purpose>Get the const iterator pointing to the beginning of the "reversed" <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rend() const</computeroutput>, <computeroutput>begin() const</computeroutput>, <computeroutput>end() const</computeroutput> </para>
</para>
</para></description><returns><para>A const reverse random access iterator pointing to the last element of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is empty it returns an iterator equal to the one returned by <computeroutput>rend() const</computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="rend" cv="const noexcept"><type>const_reverse_iterator</type><purpose>Get the const iterator pointing to the end of the "reversed" <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rbegin() const</computeroutput>, <computeroutput>begin() const</computeroutput>, <computeroutput>end() const</computeroutput> </para>
</para>
</para></description><returns><para>A const reverse random access iterator pointing to the element "one before" the first element of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is empty it returns an iterator equal to the one returned by <computeroutput>rbegin() const</computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="operator []"><type>reference</type><parameter name="index"><paramtype>size_type</paramtype><description><para>The position of the element. </para></description></parameter><purpose>Get the element at the <computeroutput>index</computeroutput> position. </purpose><description><para>



<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>at()</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>0 &lt;= index &amp;&amp; index &lt; size()</computeroutput> </para>
</requires><returns><para>A reference to the element at the <computeroutput>index</computeroutput> position. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="operator []" cv="const"><type>const_reference</type><parameter name="index"><paramtype>size_type</paramtype><description><para>The position of the element. </para></description></parameter><purpose>Get the element at the <computeroutput>index</computeroutput> position. </purpose><description><para>



<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>at() const </computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>0 &lt;= index &amp;&amp; index &lt; size()</computeroutput> </para>
</requires><returns><para>A const reference to the element at the <computeroutput>index</computeroutput> position. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="at"><type>reference</type><parameter name="index"><paramtype>size_type</paramtype><description><para>The position of the element. </para></description></parameter><purpose>Get the element at the <computeroutput>index</computeroutput> position. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Strong. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator[] </computeroutput> </para>
</para>
</para></description><returns><para>A reference to the element at the <computeroutput>index</computeroutput> position. </para>
</returns><throws><simpara><classname>&lt;code&gt;std::out_of_range&lt;/code&gt;</classname> when the <computeroutput>index</computeroutput> is invalid (when <computeroutput>index &gt;= size()</computeroutput>). </simpara></throws></method>
<method name="at" cv="const"><type>const_reference</type><parameter name="index"><paramtype>size_type</paramtype><description><para>The position of the element. </para></description></parameter><purpose>Get the element at the <computeroutput>index</computeroutput> position. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Strong. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator[] const </computeroutput> </para>
</para>
</para></description><returns><para>A const reference to the element at the <computeroutput>index</computeroutput> position. </para>
</returns><throws><simpara><classname>&lt;code&gt;std::out_of_range&lt;/code&gt;</classname> when the <computeroutput>index</computeroutput> is invalid (when <computeroutput>index &gt;= size()</computeroutput>). </simpara></throws></method>
<method name="front"><type>reference</type><purpose>Get the first element. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>back()</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>!empty()</computeroutput> </para>
</requires><returns><para>A reference to the first element of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="back"><type>reference</type><purpose>Get the last element. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>front()</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>!empty()</computeroutput> </para>
</requires><returns><para>A reference to the last element of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="front" cv="const"><type>const_reference</type><purpose>Get the first element. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>back() const</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>!empty()</computeroutput> </para>
</requires><returns><para>A const reference to the first element of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="back" cv="const"><type>const_reference</type><purpose>Get the last element. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>front() const</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>!empty()</computeroutput> </para>
</requires><returns><para>A const reference to the last element of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="array_one"><type>array_range</type><purpose>Get the first continuous array of the internal buffer. </purpose><description><para>This method in combination with <computeroutput>array_two()</computeroutput> can be useful when passing the stored data into a legacy C API as an array. Suppose there is a <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> of capacity 10, containing 7 characters <computeroutput>'a', 'b', ..., 'g'</computeroutput> where <computeroutput>buff[0] == 'a'</computeroutput>, <computeroutput>buff[1] == 'b'</computeroutput>, ... and <computeroutput>buff[6] == 'g'</computeroutput>:<sbr/>
<sbr/>
 <computeroutput>circular_buffer&lt;char&gt; buff(10);</computeroutput><sbr/>
<sbr/>
 The internal representation is often not linear and the state of the internal buffer may look like this:<sbr/>
 <sbr/>
<computeroutput> |e|f|g| | | |a|b|c|d|<sbr/>
 end ___^<sbr/>
 begin _______^</computeroutput><sbr/>
<sbr/>
</para><para>where <computeroutput>|a|b|c|d|</computeroutput> represents the "array one", <computeroutput>|e|f|g|</computeroutput> represents the "array two" and <computeroutput>| | | |</computeroutput> is a free space.<sbr/>
 Now consider a typical C style function for writing data into a file:<sbr/>
<sbr/>
 <computeroutput>int write(int file_desc, char* buff, int num_bytes);</computeroutput><sbr/>
<sbr/>
 There are two ways how to write the content of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> into a file. Either relying on <computeroutput>array_one()</computeroutput> and <computeroutput>array_two()</computeroutput> methods and calling the write function twice:<sbr/>
<sbr/>
 <computeroutput>array_range ar = buff.array_one();<sbr/>
 write(file_desc, ar.first, ar.second);<sbr/>
 ar = buff.array_two();<sbr/>
 write(file_desc, ar.first, ar.second);</computeroutput><sbr/>
<sbr/>
 Or relying on the <computeroutput>linearize()</computeroutput> method:<sbr/>
<sbr/>
<computeroutput> write(file_desc, buff.linearize(), buff.size());</computeroutput><sbr/>
<sbr/>
 Since the complexity of <computeroutput>array_one()</computeroutput> and <computeroutput>array_two()</computeroutput> methods is constant the first option is suitable when calling the write method is "cheap". On the other hand the second option is more suitable when calling the write method is more "expensive" than calling the <computeroutput>linearize()</computeroutput> method whose complexity is linear. 

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<warning><para>In general invoking any method which modifies the internal state of the <classname alt="boost::circular_buffer">circular_buffer</classname> may delinearize the internal buffer and invalidate the array ranges returned by <computeroutput>array_one()</computeroutput> and <computeroutput>array_two()</computeroutput> (and their const versions). </para>
</warning>
<note><para>In the case the internal buffer is linear e.g. <computeroutput>|a|b|c|d|e|f|g| | | |</computeroutput> the "array one" is represented by <computeroutput>|a|b|c|d|e|f|g|</computeroutput> and the "array two" does not exist (the <computeroutput>array_two()</computeroutput> method returns an array with the size <computeroutput>0</computeroutput>). </para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>array_two()</computeroutput>, <computeroutput>linearize()</computeroutput> </para>
</para>
</para></description><returns><para>The array range of the first continuous array of the internal buffer. In the case the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is empty the size of the returned array is <computeroutput>0</computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="array_two"><type>array_range</type><purpose>Get the second continuous array of the internal buffer. </purpose><description><para>This method in combination with <computeroutput>array_one()</computeroutput> can be useful when passing the stored data into a legacy C API as an array. 

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>array_one()</computeroutput> </para>
</para>
</para></description><returns><para>The array range of the second continuous array of the internal buffer. In the case the internal buffer is linear or the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is empty the size of the returned array is <computeroutput>0</computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="array_one" cv="const"><type>const_array_range</type><purpose>Get the first continuous array of the internal buffer. </purpose><description><para>This method in combination with <computeroutput>array_two() const</computeroutput> can be useful when passing the stored data into a legacy C API as an array. 

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>array_two() const</computeroutput>; <computeroutput>array_one()</computeroutput> for more details how to pass data into a legacy C API. </para>
</para>
</para></description><returns><para>The array range of the first continuous array of the internal buffer. In the case the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is empty the size of the returned array is <computeroutput>0</computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="array_two" cv="const"><type>const_array_range</type><purpose>Get the second continuous array of the internal buffer. </purpose><description><para>This method in combination with <computeroutput>array_one() const</computeroutput> can be useful when passing the stored data into a legacy C API as an array. 

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>array_one() const</computeroutput> </para>
</para>
</para></description><returns><para>The array range of the second continuous array of the internal buffer. In the case the internal buffer is linear or the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is empty the size of the returned array is <computeroutput>0</computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="linearize"><type>pointer</type><purpose>Linearize the internal buffer into a continuous array. </purpose><description><para>This method can be useful when passing the stored data into a legacy C API as an array. 


<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operations in the <emphasis>Throws</emphasis> section do not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>); does not invalidate any iterators if the postcondition (the <emphasis>Effect</emphasis>) is already met prior calling this method. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>); constant if the postcondition (the <emphasis>Effect</emphasis>) is already met. </para>
</formalpara>
<warning><para>In general invoking any method which modifies the internal state of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> may delinearize the internal buffer and invalidate the returned pointer. </para>
</warning>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>array_one()</computeroutput> and <computeroutput>array_two()</computeroutput> for the other option how to pass data into a legacy C API; <computeroutput>is_linearized()</computeroutput>, <computeroutput>rotate(const_iterator)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>&amp;(*this)[0] &lt; &amp;(*this)[1] &lt; ... &lt; &amp;(*this)[size() - 1]</computeroutput> </para>
</postconditions><returns><para>A pointer to the beginning of the array or <computeroutput>0</computeroutput> if empty. </para>
</returns><throws><simpara><classname>&lt;a</classname> href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </simpara></throws></method>
<method name="is_linearized" cv="const noexcept"><type>bool</type><purpose>Is the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> linearized? </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>linearize()</computeroutput>, <computeroutput>array_one()</computeroutput>, <computeroutput>array_two()</computeroutput> </para>
</para>
</para></description><returns><para><computeroutput>true</computeroutput> if the internal buffer is linearized into a continuous array (i.e. the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> meets a condition <computeroutput>&amp;(*this)[0] &lt; &amp;(*this)[1] &lt; ... &lt; &amp;(*this)[size() - 1]</computeroutput>); <computeroutput>false</computeroutput> otherwise. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="rotate"><type>void</type><parameter name="new_begin"><paramtype>const_iterator</paramtype><description><para>The new beginning. </para></description></parameter><purpose>Rotate elements in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>A more effective implementation of <computeroutput><ulink url="https://www.boost.org/sgi/stl/rotate.html">std::rotate</ulink></computeroutput>. 



<formalpara><title>Exception Safety</title><para>Basic; no-throw if the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full or <computeroutput>new_begin</computeroutput> points to <computeroutput>begin()</computeroutput> or if the operations in the <emphasis>Throws</emphasis> section do not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>If <computeroutput>m &lt; n</computeroutput> invalidates iterators pointing to the last <computeroutput>m</computeroutput> elements (<emphasis role="bold">including</emphasis> <computeroutput>new_begin</computeroutput>, but not iterators equal to <computeroutput>end()</computeroutput>) else invalidates iterators pointing to the first <computeroutput>n</computeroutput> elements; does not invalidate any iterators if the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>(std::min)(m, n)</computeroutput>); constant if the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput><ulink url="https://www.boost.org/sgi/stl/rotate.html">std::rotate</ulink></computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>new_begin</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> <emphasis role="bold">except</emphasis> its end. </para>
</requires><postconditions><para>Before calling the method suppose:<sbr/>
<sbr/>
 <computeroutput>m == std::distance(new_begin, end())</computeroutput><sbr/>
<computeroutput>n == std::distance(begin(), new_begin)</computeroutput> <sbr/>
<computeroutput>val_0 == *new_begin, val_1 == *(new_begin + 1), ... val_m == *(new_begin + m)</computeroutput><sbr/>
 <computeroutput>val_r1 == *(new_begin - 1), val_r2 == *(new_begin - 2), ... val_rn == *(new_begin - n)</computeroutput><sbr/>
 <sbr/>
then after call to the method:<sbr/>
<sbr/>
 <computeroutput>val_0 == (*this)[0] &amp;&amp; val_1 == (*this)[1] &amp;&amp; ... &amp;&amp; val_m == (*this)[m - 1] &amp;&amp; val_r1 == (*this)[m + n - 1] &amp;&amp; val_r2 == (*this)[m + n - 2] &amp;&amp; ... &amp;&amp; val_rn == (*this)[m]</computeroutput> </para>
</postconditions><throws><simpara><classname>See</classname> <ulink url="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t">Exceptions of move_if_noexcept(T&amp;)</ulink>. </simpara></throws></method>
<method name="size" cv="const noexcept"><type>size_type</type><purpose>Get the number of elements currently stored in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>capacity()</computeroutput>, <computeroutput>max_size()</computeroutput>, <computeroutput>reserve()</computeroutput>, <computeroutput>resize(size_type, const_reference)</computeroutput> </para>
</para>
</para></description><returns><para>The number of elements stored in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="max_size" cv="const noexcept"><type>size_type</type><purpose>Get the largest possible size or capacity of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. (It depends on allocator's max_size()). </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>size()</computeroutput>, <computeroutput>capacity()</computeroutput>, <computeroutput>reserve()</computeroutput> </para>
</para>
</para></description><returns><para>The maximum size/capacity the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> can be set to. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="empty" cv="const noexcept"><type>bool</type><purpose>Is the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> empty? </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>full()</computeroutput> </para>
</para>
</para></description><returns><para><computeroutput>true</computeroutput> if there are no elements stored in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>; <computeroutput>false</computeroutput> otherwise. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="full" cv="const noexcept"><type>bool</type><purpose>Is the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> full? </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>empty()</computeroutput> </para>
</para>
</para></description><returns><para><computeroutput>true</computeroutput> if the number of elements stored in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> equals the capacity of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>; <computeroutput>false</computeroutput> otherwise. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="reserve" cv="const noexcept"><type>size_type</type><purpose>Get the maximum number of elements which can be inserted into the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> without overwriting any of already stored elements. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>capacity()</computeroutput>, <computeroutput>size()</computeroutput>, <computeroutput>max_size()</computeroutput> </para>
</para>
</para></description><returns><para><computeroutput>capacity() - size()</computeroutput> </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="capacity" cv="const noexcept"><type>capacity_type</type><purpose>Get the capacity of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>reserve()</computeroutput>, <computeroutput>size()</computeroutput>, <computeroutput>max_size()</computeroutput>, <computeroutput>set_capacity(capacity_type)</computeroutput> </para>
</para>
</para></description><returns><para>The maximum number of elements which can be stored in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="set_capacity"><type>void</type><parameter name="new_capacity"><paramtype>capacity_type</paramtype><description><para>The new capacity. </para></description></parameter><purpose>Change the capacity of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>



<formalpara><title>Exception Safety</title><para>Strong. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>) if the new capacity is different from the original. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>min[size(), new_capacity]</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rset_capacity(capacity_type)</computeroutput>, <computeroutput>resize(size_type, const_reference)</computeroutput> </para>
</para>
</para></description><requires><para>If <computeroutput>T</computeroutput> is a move only type, then compiler shall support <computeroutput>noexcept</computeroutput> modifiers and move constructor of <computeroutput>T</computeroutput> must be marked with it (must not throw exceptions). </para>
</requires><postconditions><para><computeroutput>capacity() == new_capacity &amp;&amp; size() &lt;= new_capacity</computeroutput><sbr/>
<sbr/>
 If the current number of elements stored in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is greater than the desired new capacity then number of <computeroutput>[size() - new_capacity]</computeroutput> <emphasis role="bold">last</emphasis> elements will be removed and the new size will be equal to <computeroutput>new_capacity</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted, (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="resize"><type>void</type><parameter name="new_size"><paramtype>size_type</paramtype><description><para>The new size. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><default>value_type()</default><description><para>The element the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be filled with in order to gain the requested size. (See the <emphasis>Effect</emphasis>.) </para></description></parameter><purpose>Change the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>) if the new size is greater than the current capacity. Invalidates iterators pointing to the removed elements if the new size is lower that the original size. Otherwise it does not invalidate any iterator. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the new size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rresize(size_type, const_reference)</computeroutput>, <computeroutput>set_capacity(capacity_type)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>size() == new_size &amp;&amp; capacity() &gt;= new_size</computeroutput><sbr/>
<sbr/>
 If the new size is greater than the current size, copies of <computeroutput>item</computeroutput> will be inserted at the <emphasis role="bold">back</emphasis> of the of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> in order to achieve the desired size. In the case the resulting size exceeds the current capacity the capacity will be set to <computeroutput>new_size</computeroutput>.<sbr/>
 If the current number of elements stored in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is greater than the desired new size then number of <computeroutput>[size() - new_size]</computeroutput> <emphasis role="bold">last</emphasis> elements will be removed. (The capacity will remain unchanged.) </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="rset_capacity"><type>void</type><parameter name="new_capacity"><paramtype>capacity_type</paramtype><description><para>The new capacity. </para></description></parameter><purpose>Change the capacity of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>



<formalpara><title>Exception Safety</title><para>Strong. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>) if the new capacity is different from the original. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>min[size(), new_capacity]</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>set_capacity(capacity_type)</computeroutput>, <computeroutput>rresize(size_type, const_reference)</computeroutput> </para>
</para>
</para></description><requires><para>If <computeroutput>T</computeroutput> is a move only type, then compiler shall support <computeroutput>noexcept</computeroutput> modifiers and move constructor of <computeroutput>T</computeroutput> must be marked with it (must not throw exceptions). </para>
</requires><postconditions><para><computeroutput>capacity() == new_capacity &amp;&amp; size() &lt;= new_capacity</computeroutput><sbr/>
<sbr/>
 If the current number of elements stored in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is greater than the desired new capacity then number of <computeroutput>[size() - new_capacity]</computeroutput> <emphasis role="bold">first</emphasis> elements will be removed and the new size will be equal to <computeroutput>new_capacity</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="rresize"><type>void</type><parameter name="new_size"><paramtype>size_type</paramtype><description><para>The new size. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><default>value_type()</default><description><para>The element the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be filled with in order to gain the requested size. (See the <emphasis>Effect</emphasis>.) </para></description></parameter><purpose>Change the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>) if the new size is greater than the current capacity. Invalidates iterators pointing to the removed elements if the new size is lower that the original size. Otherwise it does not invalidate any iterator. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the new size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>resize(size_type, const_reference)</computeroutput>, <computeroutput>rset_capacity(capacity_type)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>size() == new_size &amp;&amp; capacity() &gt;= new_size</computeroutput><sbr/>
<sbr/>
 If the new size is greater than the current size, copies of <computeroutput>item</computeroutput> will be inserted at the <emphasis role="bold">front</emphasis> of the of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> in order to achieve the desired size. In the case the resulting size exceeds the current capacity the capacity will be set to <computeroutput>new_size</computeroutput>.<sbr/>
 If the current number of elements stored in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is greater than the desired new size then number of <computeroutput>[size() - new_size]</computeroutput> <emphasis role="bold">first</emphasis> elements will be removed. (The capacity will remain unchanged.) </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="assign"><type>void</type><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of elements the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be filled with. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be filled with. </para></description></parameter><purpose>Assign <computeroutput>n</computeroutput> items into the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>The content of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be removed and replaced with <computeroutput>n</computeroutput> copies of the <computeroutput>item</computeroutput>. 


<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the <computeroutput>n</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator=</computeroutput>, <computeroutput>assign(capacity_type, size_type, const_reference)</computeroutput>, <computeroutput>assign(InputIterator, InputIterator)</computeroutput>, <computeroutput>assign(capacity_type, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>capacity() == n &amp;&amp; size() == n &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this) [n - 1] == item</computeroutput> </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="assign"><type>void</type><parameter name="buffer_capacity"><paramtype>capacity_type</paramtype><description><para>The new capacity. </para></description></parameter><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of elements the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be filled with. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be filled with. </para></description></parameter><purpose>Assign <computeroutput>n</computeroutput> items into the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> specifying the capacity. </purpose><description><para>The capacity of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be set to the specified value and the content of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be removed and replaced with <computeroutput>n</computeroutput> copies of the <computeroutput>item</computeroutput>. 



<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the <computeroutput>n</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator=</computeroutput>, <computeroutput>assign(size_type, const_reference)</computeroutput>, <computeroutput>assign(InputIterator, InputIterator)</computeroutput>, <computeroutput>assign(capacity_type, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>capacity &gt;= n</computeroutput> </para>
</requires><postconditions><para><computeroutput>capacity() == buffer_capacity &amp;&amp; size() == n &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this) [n - 1] == item </computeroutput> </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="assign"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>The beginning of the range to be copied. </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>The end of the range to be copied. </para></description></parameter><purpose>Assign a copy of the range into the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>The content of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be removed and replaced with copies of elements from the specified range. 



<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the <computeroutput>std::distance(first, last)</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator=</computeroutput>, <computeroutput>assign(size_type, const_reference)</computeroutput>, <computeroutput>assign(capacity_type, size_type, const_reference)</computeroutput>, <computeroutput>assign(capacity_type, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para>Valid range <computeroutput>[first, last)</computeroutput>.<sbr/>
 <computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> have to meet the requirements of <ulink url="https://www.boost.org/sgi/stl/InputIterator.html">InputIterator</ulink>. </para>
</requires><postconditions><para><computeroutput>capacity() == std::distance(first, last) &amp;&amp; size() == std::distance(first, last) &amp;&amp; (*this)[0]== *first &amp;&amp; (*this)[1] == *(first + 1) &amp;&amp; ... &amp;&amp; (*this)[std::distance(first, last) - 1] == *(last - 1)</computeroutput> </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="assign"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="buffer_capacity"><paramtype>capacity_type</paramtype><description><para>The new capacity. </para></description></parameter><parameter name="first"><paramtype>InputIterator</paramtype><description><para>The beginning of the range to be copied. </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>The end of the range to be copied. </para></description></parameter><purpose>Assign a copy of the range into the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> specifying the capacity. </purpose><description><para>The capacity of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be set to the specified value and the content of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be removed and replaced with copies of elements from the specified range. 



<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(first, last)</computeroutput>; in <computeroutput>min[capacity, std::distance(first, last)]</computeroutput> if the <computeroutput>InputIterator</computeroutput> is a <ulink url="https://www.boost.org/sgi/stl/RandomAccessIterator.html">RandomAccessIterator</ulink>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator=</computeroutput>, <computeroutput>assign(size_type, const_reference)</computeroutput>, <computeroutput>assign(capacity_type, size_type, const_reference)</computeroutput>, <computeroutput>assign(InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para>Valid range <computeroutput>[first, last)</computeroutput>.<sbr/>
 <computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> have to meet the requirements of <ulink url="https://www.boost.org/sgi/stl/InputIterator.html">InputIterator</ulink>. </para>
</requires><postconditions><para><computeroutput>capacity() == buffer_capacity &amp;&amp; size() &lt;= std::distance(first, last) &amp;&amp; (*this)[0]== *(last - buffer_capacity) &amp;&amp; (*this)[1] == *(last - buffer_capacity + 1) &amp;&amp; ... &amp;&amp; (*this)[buffer_capacity - 1] == *(last - 1)</computeroutput><sbr/>
<sbr/>
 If the number of items to be copied from the range <computeroutput>[first, last)</computeroutput> is greater than the specified <computeroutput>buffer_capacity</computeroutput> then only elements from the range <computeroutput>[last - buffer_capacity, last)</computeroutput> will be copied. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="swap" cv="noexcept"><type>void</type><parameter name="cb"><paramtype><classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> whose content will be swapped. </para></description></parameter><purpose>Swap the contents of two <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators of both <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s. (On the other hand the iterators still point to the same elements but within another container. If you want to rely on this feature you have to turn the <ulink url="#debug">Debug Support</ulink> off otherwise an assertion will report an error if such invalidated iterator is used.) </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>swap(circular_buffer&lt;T, Alloc&gt;&amp;, circular_buffer&lt;T, Alloc&gt;&amp;)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>this</computeroutput> contains elements of <computeroutput>cb</computeroutput> and vice versa; the capacity of <computeroutput>this</computeroutput> equals to the capacity of <computeroutput>cb</computeroutput> and vice versa. </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="push_back"><type>void</type><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert a new element at the end of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators with the exception of iterators pointing to the overwritten element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>push_front(const_reference)</computeroutput>, <computeroutput>pop_back()</computeroutput>, <computeroutput>pop_front()</computeroutput> </para>
</para>
</para></description><postconditions><para>if <computeroutput>capacity() &gt; 0</computeroutput> then <computeroutput>back() == item</computeroutput><sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full, the first element will be removed. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted. </para>
</postconditions><throws><simpara><classname>Whatever</classname> <computeroutput>T::T(const T&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="push_back"><type>void</type><parameter name="item"><paramtype>rvalue_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert a new element at the end of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> using rvalue references or rvalues references emulation. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators with the exception of iterators pointing to the overwritten element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>push_front(const_reference)</computeroutput>, <computeroutput>pop_back()</computeroutput>, <computeroutput>pop_front()</computeroutput> </para>
</para>
</para></description><postconditions><para>if <computeroutput>capacity() &gt; 0</computeroutput> then <computeroutput>back() == item</computeroutput><sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full, the first element will be removed. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted. </para>
</postconditions><throws><simpara><classname>Whatever</classname> <computeroutput>T::T(T&amp;&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="push_back"><type>void</type><purpose>Insert a new default-constructed element at the end of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators with the exception of iterators pointing to the overwritten element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>push_front(const_reference)</computeroutput>, <computeroutput>pop_back()</computeroutput>, <computeroutput>pop_front()</computeroutput> </para>
</para>
</para></description><postconditions><para>if <computeroutput>capacity() &gt; 0</computeroutput> then <computeroutput>back() == item</computeroutput><sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full, the first element will be removed. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted. </para>
</postconditions><throws><simpara><classname>Whatever</classname> <computeroutput>T::T()</computeroutput> throws. Whatever <computeroutput>T::T(T&amp;&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="push_front"><type>void</type><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert a new element at the beginning of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators with the exception of iterators pointing to the overwritten element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>push_back(const_reference)</computeroutput>, <computeroutput>pop_back()</computeroutput>, <computeroutput>pop_front()</computeroutput> </para>
</para>
</para></description><postconditions><para>if <computeroutput>capacity() &gt; 0</computeroutput> then <computeroutput>front() == item</computeroutput><sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full, the last element will be removed. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted. </para>
</postconditions><throws><simpara><classname>Whatever</classname> <computeroutput>T::T(const T&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="push_front"><type>void</type><parameter name="item"><paramtype>rvalue_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert a new element at the beginning of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> using rvalue references or rvalues references emulation. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators with the exception of iterators pointing to the overwritten element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>push_back(const_reference)</computeroutput>, <computeroutput>pop_back()</computeroutput>, <computeroutput>pop_front()</computeroutput> </para>
</para>
</para></description><postconditions><para>if <computeroutput>capacity() &gt; 0</computeroutput> then <computeroutput>front() == item</computeroutput><sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full, the last element will be removed. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted. </para>
</postconditions><throws><simpara><classname>Whatever</classname> <computeroutput>T::T(T&amp;&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="push_front"><type>void</type><purpose>Insert a new default-constructed element at the beginning of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators with the exception of iterators pointing to the overwritten element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>push_back(const_reference)</computeroutput>, <computeroutput>pop_back()</computeroutput>, <computeroutput>pop_front()</computeroutput> </para>
</para>
</para></description><postconditions><para>if <computeroutput>capacity() &gt; 0</computeroutput> then <computeroutput>front() == item</computeroutput><sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full, the last element will be removed. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted. </para>
</postconditions><throws><simpara><classname>Whatever</classname> <computeroutput>T::T()</computeroutput> throws. Whatever <computeroutput>T::T(T&amp;&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="pop_back"><type>void</type><purpose>Remove the last element from the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates only iterators pointing to the removed element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>pop_front()</computeroutput>, <computeroutput>push_back(const_reference)</computeroutput>, <computeroutput>push_front(const_reference)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>!empty()</computeroutput> </para>
</requires><postconditions><para>The last element is removed from the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="pop_front"><type>void</type><purpose>Remove the first element from the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates only iterators pointing to the removed element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>pop_back()</computeroutput>, <computeroutput>push_back(const_reference)</computeroutput>, <computeroutput>push_front(const_reference)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>!empty()</computeroutput> </para>
</requires><postconditions><para>The first element is removed from the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="insert"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the <computeroutput>item</computeroutput> will be inserted. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert an element at the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the elements at the insertion point (including <computeroutput>pos</computeroutput>) and iterators behind the insertion point (towards the end; except iterators equal to <computeroutput>end()</computeroutput>). It also invalidates iterators pointing to the overwritten element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(pos, end())</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The <computeroutput>item</computeroutput> will be inserted at the position <computeroutput>pos</computeroutput>.<sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full, the first element will be overwritten. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full and the <computeroutput>pos</computeroutput> points to <computeroutput>begin()</computeroutput>, then the <computeroutput>item</computeroutput> will not be inserted. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted. </para>
</postconditions><returns><para>Iterator to the inserted element or <computeroutput>begin()</computeroutput> if the <computeroutput>item</computeroutput> is not inserted. (See the <emphasis>Effect</emphasis>.) </para>
</returns><throws><simpara><classname>Whatever</classname> <computeroutput>T::T(const T&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws. <ulink url="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t">Exceptions of move_if_noexcept(T&amp;)</ulink>.</simpara></throws></method>
<method name="insert"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the <computeroutput>item</computeroutput> will be inserted. </para></description></parameter><parameter name="item"><paramtype>rvalue_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert an element at the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the elements at the insertion point (including <computeroutput>pos</computeroutput>) and iterators behind the insertion point (towards the end; except iterators equal to <computeroutput>end()</computeroutput>). It also invalidates iterators pointing to the overwritten element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(pos, end())</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The <computeroutput>item</computeroutput> will be inserted at the position <computeroutput>pos</computeroutput>.<sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full, the first element will be overwritten. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full and the <computeroutput>pos</computeroutput> points to <computeroutput>begin()</computeroutput>, then the <computeroutput>item</computeroutput> will not be inserted. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted. </para>
</postconditions><returns><para>Iterator to the inserted element or <computeroutput>begin()</computeroutput> if the <computeroutput>item</computeroutput> is not inserted. (See the <emphasis>Effect</emphasis>.) </para>
</returns><throws><simpara><classname>Whatever</classname> <computeroutput>T::T(T&amp;&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> throws. <ulink url="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t">Exceptions of move_if_noexcept(T&amp;)</ulink>. </simpara></throws></method>
<method name="insert"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the <computeroutput>item</computeroutput> will be inserted. </para></description></parameter><purpose>Insert a default-constructed element at the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the elements at the insertion point (including <computeroutput>pos</computeroutput>) and iterators behind the insertion point (towards the end; except iterators equal to <computeroutput>end()</computeroutput>). It also invalidates iterators pointing to the overwritten element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(pos, end())</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The <computeroutput>item</computeroutput> will be inserted at the position <computeroutput>pos</computeroutput>.<sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full, the first element will be overwritten. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full and the <computeroutput>pos</computeroutput> points to <computeroutput>begin()</computeroutput>, then the <computeroutput>item</computeroutput> will not be inserted. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted. </para>
</postconditions><returns><para>Iterator to the inserted element or <computeroutput>begin()</computeroutput> if the <computeroutput>item</computeroutput> is not inserted. (See the <emphasis>Effect</emphasis>.) </para>
</returns><throws><simpara><classname>Whatever</classname> <computeroutput>T::T()</computeroutput> throws. Whatever <computeroutput>T::T(T&amp;&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> throws. <ulink url="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t">Exceptions of move_if_noexcept(T&amp;)</ulink>. </simpara></throws></method>
<method name="insert"><type>void</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the <computeroutput>item</computeroutput>s will be inserted. </para></description></parameter><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of <computeroutput>item</computeroutput>s the to be inserted. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element whose copies will be inserted. </para></description></parameter><purpose>Insert <computeroutput>n</computeroutput> copies of the <computeroutput>item</computeroutput> at the specified position. </purpose><description><para>



<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operations in the <emphasis>Throws</emphasis> section do not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the elements at the insertion point (including <computeroutput>pos</computeroutput>) and iterators behind the insertion point (towards the end; except iterators equal to <computeroutput>end()</computeroutput>). It also invalidates iterators pointing to the overwritten elements. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>min[capacity(), std::distance(pos, end()) + n]</computeroutput>). </para>
</formalpara>
<formalpara><title>Example</title><para>Consider a <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<sbr/>
<sbr/>
 <computeroutput>|1|2|3|4| | |</computeroutput><sbr/>
 <computeroutput>p ___^</computeroutput><sbr/>
<sbr/>
After inserting 5 elements at the position <computeroutput>p</computeroutput>:<sbr/>
<sbr/>
 <computeroutput>insert(p, (size_t)5, 0);</computeroutput><sbr/>
<sbr/>
actually only 4 elements get inserted and elements <computeroutput>1</computeroutput> and <computeroutput>2</computeroutput> are overwritten. This is due to the fact the insert operation preserves the capacity. After insertion the internal buffer looks like this:<sbr/>
<sbr/>
<computeroutput>|0|0|0|0|3|4|</computeroutput><sbr/>
 <sbr/>
For comparison if the capacity would not be preserved the internal buffer would then result in <computeroutput>|1|2|0|0|0|0|0|3|4|</computeroutput>. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The number of <computeroutput>min[n, (pos - begin()) + reserve()]</computeroutput> elements will be inserted at the position <computeroutput>pos</computeroutput>.<sbr/>
The number of <computeroutput>min[pos - begin(), max[0, n - reserve()]]</computeroutput> elements will be overwritten at the beginning of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>.<sbr/>
(See <emphasis>Example</emphasis> for the explanation.) </para>
</postconditions><throws><simpara><classname>Whatever</classname> <computeroutput>T::T(const T&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws. <ulink url="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t">Exceptions of move_if_noexcept(T&amp;)</ulink>. </simpara></throws></method>
<method name="insert"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the range will be inserted. </para></description></parameter><parameter name="first"><paramtype>InputIterator</paramtype><description><para>The beginning of the range to be inserted. </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>The end of the range to be inserted. </para></description></parameter><purpose>Insert the range <computeroutput>[first, last)</computeroutput> at the specified position. </purpose><description><para>



<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operations in the <emphasis>Throws</emphasis> section do not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the elements at the insertion point (including <computeroutput>pos</computeroutput>) and iterators behind the insertion point (towards the end; except iterators equal to <computeroutput>end()</computeroutput>). It also invalidates iterators pointing to the overwritten elements. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>[std::distance(pos, end()) + std::distance(first, last)]</computeroutput>; in <computeroutput>min[capacity(), std::distance(pos, end()) + std::distance(first, last)]</computeroutput> if the <computeroutput>InputIterator</computeroutput> is a <ulink url="https://www.boost.org/sgi/stl/RandomAccessIterator.html">RandomAccessIterator</ulink>). </para>
</formalpara>
<formalpara><title>Example</title><para>Consider a <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<sbr/>
<sbr/>
 <computeroutput>|1|2|3|4| | |</computeroutput><sbr/>
 <computeroutput>p ___^</computeroutput><sbr/>
<sbr/>
After inserting a range of elements at the position <computeroutput>p</computeroutput>:<sbr/>
<sbr/>
 <computeroutput>int array[] = { 5, 6, 7, 8, 9 };</computeroutput><sbr/>
<computeroutput>insert(p, array, array + 5);</computeroutput><sbr/>
<sbr/>
 actually only elements <computeroutput>6</computeroutput>, <computeroutput>7</computeroutput>, <computeroutput>8</computeroutput> and <computeroutput>9</computeroutput> from the specified range get inserted and elements <computeroutput>1</computeroutput> and <computeroutput>2</computeroutput> are overwritten. This is due to the fact the insert operation preserves the capacity. After insertion the internal buffer looks like this:<sbr/>
<sbr/>
<computeroutput>|6|7|8|9|3|4|</computeroutput><sbr/>
<sbr/>
For comparison if the capacity would not be preserved the internal buffer would then result in <computeroutput>|1|2|5|6|7|8|9|3|4|</computeroutput>. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> or its end.<sbr/>
 Valid range <computeroutput>[first, last)</computeroutput> where <computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> meet the requirements of an <ulink url="https://www.boost.org/sgi/stl/InputIterator.html">InputIterator</ulink>. </para>
</requires><postconditions><para>Elements from the range <computeroutput>[first + max[0, distance(first, last) - (pos - begin()) - reserve()], last)</computeroutput> will be inserted at the position <computeroutput>pos</computeroutput>.<sbr/>
The number of <computeroutput>min[pos - begin(), max[0, distance(first, last) - reserve()]]</computeroutput> elements will be overwritten at the beginning of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>.<sbr/>
(See <emphasis>Example</emphasis> for the explanation.) </para>
</postconditions><throws><simpara><classname>Whatever</classname> <computeroutput>T::T(const T&amp;)</computeroutput> throws if the <computeroutput>InputIterator</computeroutput> is not a move iterator. Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws if the <computeroutput>InputIterator</computeroutput> is not a move iterator. Whatever <computeroutput>T::T(T&amp;&amp;)</computeroutput> throws if the <computeroutput>InputIterator</computeroutput> is a move iterator. Whatever <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> throws if the <computeroutput>InputIterator</computeroutput> is a move iterator. </simpara></throws></method>
<method name="rinsert"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position before which the <computeroutput>item</computeroutput> will be inserted. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert an element before the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operations in the <emphasis>Throws</emphasis> section do not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the elements before the insertion point (towards the beginning and excluding <computeroutput>pos</computeroutput>). It also invalidates iterators pointing to the overwritten element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(begin(), pos)</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The <computeroutput>item</computeroutput> will be inserted before the position <computeroutput>pos</computeroutput>.<sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full, the last element will be overwritten. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full and the <computeroutput>pos</computeroutput> points to <computeroutput>end()</computeroutput>, then the <computeroutput>item</computeroutput> will not be inserted. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted. </para>
</postconditions><returns><para>Iterator to the inserted element or <computeroutput>end()</computeroutput> if the <computeroutput>item</computeroutput> is not inserted. (See the <emphasis>Effect</emphasis>.) </para>
</returns><throws><simpara><classname>Whatever</classname> <computeroutput>T::T(const T&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws. <ulink url="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t">Exceptions of move_if_noexcept(T&amp;)</ulink>. </simpara></throws></method>
<method name="rinsert"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position before which the <computeroutput>item</computeroutput> will be inserted. </para></description></parameter><parameter name="item"><paramtype>rvalue_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert an element before the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operations in the <emphasis>Throws</emphasis> section do not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the elements before the insertion point (towards the beginning and excluding <computeroutput>pos</computeroutput>). It also invalidates iterators pointing to the overwritten element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(begin(), pos)</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The <computeroutput>item</computeroutput> will be inserted before the position <computeroutput>pos</computeroutput>.<sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full, the last element will be overwritten. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full and the <computeroutput>pos</computeroutput> points to <computeroutput>end()</computeroutput>, then the <computeroutput>item</computeroutput> will not be inserted. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted. </para>
</postconditions><returns><para>Iterator to the inserted element or <computeroutput>end()</computeroutput> if the <computeroutput>item</computeroutput> is not inserted. (See the <emphasis>Effect</emphasis>.) </para>
</returns><throws><simpara><classname>Whatever</classname> <computeroutput>T::T(T&amp;&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> throws. <ulink url="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t">Exceptions of move_if_noexcept(T&amp;)</ulink>. </simpara></throws></method>
<method name="rinsert"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position before which the <computeroutput>item</computeroutput> will be inserted. </para></description></parameter><purpose>Insert an element before the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operations in the <emphasis>Throws</emphasis> section do not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the elements before the insertion point (towards the beginning and excluding <computeroutput>pos</computeroutput>). It also invalidates iterators pointing to the overwritten element. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(begin(), pos)</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The <computeroutput>item</computeroutput> will be inserted before the position <computeroutput>pos</computeroutput>.<sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full, the last element will be overwritten. If the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> is full and the <computeroutput>pos</computeroutput> points to <computeroutput>end()</computeroutput>, then the <computeroutput>item</computeroutput> will not be inserted. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted. </para>
</postconditions><returns><para>Iterator to the inserted element or <computeroutput>end()</computeroutput> if the <computeroutput>item</computeroutput> is not inserted. (See the <emphasis>Effect</emphasis>.) </para>
</returns><throws><simpara><classname>Whatever</classname> <computeroutput>T::T()</computeroutput> throws. Whatever <computeroutput>T::T(T&amp;&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> throws. <ulink url="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t">Exceptions of move_if_noexcept(T&amp;)</ulink>. </simpara></throws></method>
<method name="rinsert"><type>void</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the <computeroutput>item</computeroutput>s will be inserted. </para></description></parameter><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of <computeroutput>item</computeroutput>s the to be inserted. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element whose copies will be inserted. </para></description></parameter><purpose>Insert <computeroutput>n</computeroutput> copies of the <computeroutput>item</computeroutput> before the specified position. </purpose><description><para>



<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operations in the <emphasis>Throws</emphasis> section do not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the elements before the insertion point (towards the beginning and excluding <computeroutput>pos</computeroutput>). It also invalidates iterators pointing to the overwritten elements. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>min[capacity(), std::distance(begin(), pos) + n]</computeroutput>). </para>
</formalpara>
<formalpara><title>Example</title><para>Consider a <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<sbr/>
<sbr/>
 <computeroutput>|1|2|3|4| | |</computeroutput><sbr/>
 <computeroutput>p ___^</computeroutput><sbr/>
<sbr/>
After inserting 5 elements before the position <computeroutput>p</computeroutput>:<sbr/>
<sbr/>
 <computeroutput>rinsert(p, (size_t)5, 0);</computeroutput><sbr/>
<sbr/>
actually only 4 elements get inserted and elements <computeroutput>3</computeroutput> and <computeroutput>4</computeroutput> are overwritten. This is due to the fact the rinsert operation preserves the capacity. After insertion the internal buffer looks like this:<sbr/>
<sbr/>
<computeroutput>|1|2|0|0|0|0|</computeroutput><sbr/>
 <sbr/>
For comparison if the capacity would not be preserved the internal buffer would then result in <computeroutput>|1|2|0|0|0|0|0|3|4|</computeroutput>. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The number of <computeroutput>min[n, (end() - pos) + reserve()]</computeroutput> elements will be inserted before the position <computeroutput>pos</computeroutput>.<sbr/>
The number of <computeroutput>min[end() - pos, max[0, n - reserve()]]</computeroutput> elements will be overwritten at the end of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>.<sbr/>
(See <emphasis>Example</emphasis> for the explanation.) </para>
</postconditions><throws><simpara><classname>Whatever</classname> <computeroutput>T::T(const T&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws. <ulink url="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t">Exceptions of move_if_noexcept(T&amp;)</ulink>. </simpara></throws></method>
<method name="rinsert"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the range will be inserted. </para></description></parameter><parameter name="first"><paramtype>InputIterator</paramtype><description><para>The beginning of the range to be inserted. </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>The end of the range to be inserted. </para></description></parameter><purpose>Insert the range <computeroutput>[first, last)</computeroutput> before the specified position. </purpose><description><para>



<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operations in the <emphasis>Throws</emphasis> section do not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the elements before the insertion point (towards the beginning and excluding <computeroutput>pos</computeroutput>). It also invalidates iterators pointing to the overwritten elements. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>[std::distance(begin(), pos) + std::distance(first, last)]</computeroutput>; in <computeroutput>min[capacity(), std::distance(begin(), pos) + std::distance(first, last)]</computeroutput> if the <computeroutput>InputIterator</computeroutput> is a <ulink url="https://www.boost.org/sgi/stl/RandomAccessIterator.html">RandomAccessIterator</ulink>). </para>
</formalpara>
<formalpara><title>Example</title><para>Consider a <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<sbr/>
<sbr/>
 <computeroutput>|1|2|3|4| | |</computeroutput><sbr/>
 <computeroutput>p ___^</computeroutput><sbr/>
<sbr/>
After inserting a range of elements before the position <computeroutput>p</computeroutput>:<sbr/>
<sbr/>
 <computeroutput>int array[] = { 5, 6, 7, 8, 9 };</computeroutput><sbr/>
<computeroutput>insert(p, array, array + 5);</computeroutput><sbr/>
<sbr/>
 actually only elements <computeroutput>5</computeroutput>, <computeroutput>6</computeroutput>, <computeroutput>7</computeroutput> and <computeroutput>8</computeroutput> from the specified range get inserted and elements <computeroutput>3</computeroutput> and <computeroutput>4</computeroutput> are overwritten. This is due to the fact the rinsert operation preserves the capacity. After insertion the internal buffer looks like this:<sbr/>
<sbr/>
<computeroutput>|1|2|5|6|7|8|</computeroutput><sbr/>
<sbr/>
For comparison if the capacity would not be preserved the internal buffer would then result in <computeroutput>|1|2|5|6|7|8|9|3|4|</computeroutput>. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> or its end.<sbr/>
 Valid range <computeroutput>[first, last)</computeroutput> where <computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> meet the requirements of an <ulink url="https://www.boost.org/sgi/stl/InputIterator.html">InputIterator</ulink>. </para>
</requires><postconditions><para>Elements from the range <computeroutput>[first, last - max[0, distance(first, last) - (end() - pos) - reserve()])</computeroutput> will be inserted before the position <computeroutput>pos</computeroutput>.<sbr/>
The number of <computeroutput>min[end() - pos, max[0, distance(first, last) - reserve()]]</computeroutput> elements will be overwritten at the end of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>.<sbr/>
(See <emphasis>Example</emphasis> for the explanation.) </para>
</postconditions><throws><simpara><classname>Whatever</classname> <computeroutput>T::T(const T&amp;)</computeroutput> throws if the <computeroutput>InputIterator</computeroutput> is not a move iterator. Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws if the <computeroutput>InputIterator</computeroutput> is not a move iterator. Whatever <computeroutput>T::T(T&amp;&amp;)</computeroutput> throws if the <computeroutput>InputIterator</computeroutput> is a move iterator. Whatever <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> throws if the <computeroutput>InputIterator</computeroutput> is a move iterator. </simpara></throws></method>
<method name="erase"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator pointing at the element to be removed. </para></description></parameter><purpose>Remove an element at the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the erased element and iterators pointing to the elements behind the erased element (towards the end; except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(pos, end())</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>erase(iterator, iterator)</computeroutput>, <computeroutput>rerase(iterator)</computeroutput>, <computeroutput>rerase(iterator, iterator)</computeroutput>, <computeroutput>erase_begin(size_type)</computeroutput>, <computeroutput>erase_end(size_type)</computeroutput>, <computeroutput>clear()</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (but not an <computeroutput>end()</computeroutput>). </para>
</requires><postconditions><para>The element at the position <computeroutput>pos</computeroutput> is removed. </para>
</postconditions><returns><para>Iterator to the first element remaining beyond the removed element or <computeroutput>end()</computeroutput> if no such element exists. </para>
</returns><throws><simpara><classname>&lt;a</classname> href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </simpara></throws></method>
<method name="erase"><type>iterator</type><parameter name="first"><paramtype>iterator</paramtype><description><para>The beginning of the range to be removed. </para></description></parameter><parameter name="last"><paramtype>iterator</paramtype><description><para>The end of the range to be removed. </para></description></parameter><purpose>Erase the range <computeroutput>[first, last)</computeroutput>. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the erased elements and iterators pointing to the elements behind the erased range (towards the end; except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(first, end())</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>erase(iterator)</computeroutput>, <computeroutput>rerase(iterator)</computeroutput>, <computeroutput>rerase(iterator, iterator)</computeroutput>, <computeroutput>erase_begin(size_type)</computeroutput>, <computeroutput>erase_end(size_type)</computeroutput>, <computeroutput>clear()</computeroutput> </para>
</para>
</para></description><requires><para>Valid range <computeroutput>[first, last)</computeroutput>. </para>
</requires><postconditions><para>The elements from the range <computeroutput>[first, last)</computeroutput> are removed. (If <computeroutput>first == last</computeroutput> nothing is removed.) </para>
</postconditions><returns><para>Iterator to the first element remaining beyond the removed elements or <computeroutput>end()</computeroutput> if no such element exists. </para>
</returns><throws><simpara><classname>&lt;a</classname> href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </simpara></throws></method>
<method name="rerase"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator pointing at the element to be removed. </para></description></parameter><purpose>Remove an element at the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the erased element and iterators pointing to the elements in front of the erased element (towards the beginning). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(begin(), pos)</computeroutput>). </para>
</formalpara>
<note><para>This method is symmetric to the <computeroutput>erase(iterator)</computeroutput> method and is more effective than <computeroutput>erase(iterator)</computeroutput> if the iterator <computeroutput>pos</computeroutput> is close to the beginning of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. (See the <emphasis>Complexity</emphasis>.) </para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>erase(iterator)</computeroutput>, <computeroutput>erase(iterator, iterator)</computeroutput>, <computeroutput>rerase(iterator, iterator)</computeroutput>, <computeroutput>erase_begin(size_type)</computeroutput>, <computeroutput>erase_end(size_type)</computeroutput>, <computeroutput>clear()</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (but not an <computeroutput>end()</computeroutput>). </para>
</requires><postconditions><para>The element at the position <computeroutput>pos</computeroutput> is removed. </para>
</postconditions><returns><para>Iterator to the first element remaining in front of the removed element or <computeroutput>begin()</computeroutput> if no such element exists. </para>
</returns><throws><simpara><classname>&lt;a</classname> href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </simpara></throws></method>
<method name="rerase"><type>iterator</type><parameter name="first"><paramtype>iterator</paramtype><description><para>The beginning of the range to be removed. </para></description></parameter><parameter name="last"><paramtype>iterator</paramtype><description><para>The end of the range to be removed. </para></description></parameter><purpose>Erase the range <computeroutput>[first, last)</computeroutput>. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the erased elements and iterators pointing to the elements in front of the erased range (towards the beginning). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(begin(), last)</computeroutput>). </para>
</formalpara>
<note><para>This method is symmetric to the <computeroutput>erase(iterator, iterator)</computeroutput> method and is more effective than <computeroutput>erase(iterator, iterator)</computeroutput> if <computeroutput>std::distance(begin(), first)</computeroutput> is lower that <computeroutput>std::distance(last, end())</computeroutput>. </para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>erase(iterator)</computeroutput>, <computeroutput>erase(iterator, iterator)</computeroutput>, <computeroutput>rerase(iterator)</computeroutput>, <computeroutput>erase_begin(size_type)</computeroutput>, <computeroutput>erase_end(size_type)</computeroutput>, <computeroutput>clear()</computeroutput> </para>
</para>
</para></description><requires><para>Valid range <computeroutput>[first, last)</computeroutput>. </para>
</requires><postconditions><para>The elements from the range <computeroutput>[first, last)</computeroutput> are removed. (If <computeroutput>first == last</computeroutput> nothing is removed.) </para>
</postconditions><returns><para>Iterator to the first element remaining in front of the removed elements or <computeroutput>begin()</computeroutput> if no such element exists. </para>
</returns><throws><simpara><classname>&lt;a</classname> href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </simpara></throws></method>
<method name="erase_begin"><type>void</type><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of elements to be removed. </para></description></parameter><purpose>Remove first <computeroutput>n</computeroutput> elements (with constant complexity for scalar types). </purpose><description><para>



<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. (I.e. no throw in case of scalars.) </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the first <computeroutput>n</computeroutput> erased elements. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in <computeroutput>n</computeroutput>) for scalar types; linear for other types. </para>
</formalpara>
<note><para>This method has been specially designed for types which do not require an explicit destructruction (e.g. integer, float or a pointer). For these scalar types a call to a destructor is not required which makes it possible to implement the "erase from beginning" operation with a constant complexity. For non-sacalar types the complexity is linear (hence the explicit destruction is needed) and the implementation is actually equivalent to <computeroutput>rerase(begin(), begin() + n)</computeroutput>. </para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>erase(iterator)</computeroutput>, <computeroutput>erase(iterator, iterator)</computeroutput>, <computeroutput>rerase(iterator)</computeroutput>, <computeroutput>rerase(iterator, iterator)</computeroutput>, <computeroutput>erase_end(size_type)</computeroutput>, <computeroutput>clear()</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>n &lt;= size()</computeroutput> </para>
</requires><postconditions><para>The <computeroutput>n</computeroutput> elements at the beginning of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be removed. </para>
</postconditions><throws><simpara><classname>&lt;a</classname> href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </simpara></throws></method>
<method name="erase_end"><type>void</type><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of elements to be removed. </para></description></parameter><purpose>Remove last <computeroutput>n</computeroutput> elements (with constant complexity for scalar types). </purpose><description><para>



<formalpara><title>Exception Safety</title><para>Basic; no-throw if the operation in the <emphasis>Throws</emphasis> section does not throw anything. (I.e. no throw in case of scalars.) </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates iterators pointing to the last <computeroutput>n</computeroutput> erased elements. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in <computeroutput>n</computeroutput>) for scalar types; linear for other types. </para>
</formalpara>
<note><para>This method has been specially designed for types which do not require an explicit destructruction (e.g. integer, float or a pointer). For these scalar types a call to a destructor is not required which makes it possible to implement the "erase from end" operation with a constant complexity. For non-sacalar types the complexity is linear (hence the explicit destruction is needed) and the implementation is actually equivalent to <computeroutput>erase(end() - n, end())</computeroutput>. </para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>erase(iterator)</computeroutput>, <computeroutput>erase(iterator, iterator)</computeroutput>, <computeroutput>rerase(iterator)</computeroutput>, <computeroutput>rerase(iterator, iterator)</computeroutput>, <computeroutput>erase_begin(size_type)</computeroutput>, <computeroutput>clear()</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>n &lt;= size()</computeroutput> </para>
</requires><postconditions><para>The <computeroutput>n</computeroutput> elements at the end of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be removed. </para>
</postconditions><throws><simpara><classname>&lt;a</classname> href="circular_buffer/implementation.html#circular_buffer.implementation.exceptions_of_move_if_noexcept_t"&gt;Exceptions of move_if_noexcept(T&amp;). </simpara></throws></method>
<method name="clear" cv="noexcept"><type>void</type><purpose>Remove all stored elements from the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>) for scalar types; linear for other types. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>~circular_buffer()</computeroutput>, <computeroutput>erase(iterator)</computeroutput>, <computeroutput>erase(iterator, iterator)</computeroutput>, <computeroutput>rerase(iterator)</computeroutput>, <computeroutput>rerase(iterator, iterator)</computeroutput>, <computeroutput>erase_begin(size_type)</computeroutput>, <computeroutput>erase_end(size_type)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>size() == 0</computeroutput> </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
</method-group>
<constructor specifiers="explicit" cv="noexcept"><parameter name="alloc"><paramtype>const allocator_type &amp;</paramtype><default>allocator_type()</default><description><para>The allocator. </para></description></parameter><purpose>Create an empty <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> with zero capacity. </purpose><description><para>


<formalpara><title>Complexity</title><para>Constant. </para>
</formalpara>
<warning><para>Since Boost version 1.36 the behaviour of this constructor has changed. Now the constructor does not allocate any memory and both capacity and size are set to zero. Also note when inserting an element into a <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> with zero capacity (e.g. by <computeroutput>push_back(const_reference)</computeroutput> or <computeroutput>insert(iterator, value_type)</computeroutput>) nothing will be inserted and the size (as well as capacity) remains zero. </para>
</warning>
<note><para>You can explicitly set the capacity by calling the <computeroutput>set_capacity(capacity_type)</computeroutput> method or you can use the other constructor with the capacity specified. </para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>circular_buffer(capacity_type, const allocator_type&amp; alloc)</computeroutput>, <computeroutput>set_capacity(capacity_type)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>capacity() == 0 &amp;&amp; size() == 0</computeroutput> </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></constructor>
<constructor specifiers="explicit"><parameter name="buffer_capacity"><paramtype>capacity_type</paramtype><description><para>The maximum number of elements which can be stored in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para></description></parameter><parameter name="alloc"><paramtype>const allocator_type &amp;</paramtype><default>allocator_type()</default><description><para>The allocator. </para></description></parameter><purpose>Create an empty <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> with the specified capacity. </purpose><description><para>


<formalpara><title>Complexity</title><para>Constant. </para>
</formalpara>
</para></description><postconditions><para><computeroutput>capacity() == buffer_capacity &amp;&amp; size() == 0</computeroutput> </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). </simpara></throws></constructor>
<constructor><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of elements the created <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be filled with. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element the created <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be filled with. </para></description></parameter><parameter name="alloc"><paramtype>const allocator_type &amp;</paramtype><default>allocator_type()</default><description><para>The allocator. </para></description></parameter><purpose>Create a full <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> with the specified capacity and filled with <computeroutput>n</computeroutput> copies of <computeroutput>item</computeroutput>. </purpose><description><para>


<formalpara><title>Complexity</title><para>Linear (in the <computeroutput>n</computeroutput>). </para>
</formalpara>
</para></description><postconditions><para><computeroutput>capacity() == n &amp;&amp; full() &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this)[n - 1] == item </computeroutput> </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></constructor>
<constructor><parameter name="buffer_capacity"><paramtype>capacity_type</paramtype><description><para>The capacity of the created <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para></description></parameter><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of elements the created <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be filled with. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element the created <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> will be filled with. </para></description></parameter><parameter name="alloc"><paramtype>const allocator_type &amp;</paramtype><default>allocator_type()</default><description><para>The allocator. </para></description></parameter><purpose>Create a <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> with the specified capacity and filled with <computeroutput>n</computeroutput> copies of <computeroutput>item</computeroutput>. </purpose><description><para>



<formalpara><title>Complexity</title><para>Linear (in the <computeroutput>n</computeroutput>). </para>
</formalpara>
</para></description><requires><para><computeroutput>buffer_capacity &gt;= n</computeroutput> </para>
</requires><postconditions><para><computeroutput>capacity() == buffer_capacity &amp;&amp; size() == n &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this)[n - 1] == item</computeroutput> </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></constructor>
<constructor><parameter name="cb"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to be copied. </para></description></parameter><purpose>The copy constructor. </purpose><description><para>Creates a copy of the specified <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. 


<formalpara><title>Complexity</title><para>Linear (in the size of <computeroutput>cb</computeroutput>). </para>
</formalpara>
</para></description><postconditions><para><computeroutput>*this == cb</computeroutput> </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></constructor>
<constructor cv="noexcept"><parameter name="cb"><paramtype><classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;&amp;</paramtype><description><para><computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to 'steal' value from. </para></description></parameter><purpose>The move constructor. </purpose><description><para>Move constructs a <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> from <computeroutput>cb</computeroutput>, leaving <computeroutput>cb</computeroutput> empty. 



<formalpara><title>Constant.</title><para/>
</formalpara>
</para></description><requires><para>C++ compiler with rvalue references support. </para>
</requires><postconditions><para><computeroutput>cb.empty()</computeroutput> </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></constructor>
<constructor><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>The beginning of the range to be copied. </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>The end of the range to be copied. </para></description></parameter><parameter name="alloc"><paramtype>const allocator_type &amp;</paramtype><default>allocator_type()</default><description><para>The allocator. </para></description></parameter><purpose>Create a full <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> filled with a copy of the range. </purpose><description><para>



<formalpara><title>Complexity</title><para>Linear (in the <computeroutput>std::distance(first, last)</computeroutput>). </para>
</formalpara>
</para></description><requires><para>Valid range <computeroutput>[first, last)</computeroutput>.<sbr/>
 <computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> have to meet the requirements of <ulink url="https://www.boost.org/sgi/stl/InputIterator.html">InputIterator</ulink>. </para>
</requires><postconditions><para><computeroutput>capacity() == std::distance(first, last) &amp;&amp; full() &amp;&amp; (*this)[0]== *first &amp;&amp; (*this)[1] == *(first + 1) &amp;&amp; ... &amp;&amp; (*this)[std::distance(first, last) - 1] == *(last - 1)</computeroutput> </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></constructor>
<constructor><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="buffer_capacity"><paramtype>capacity_type</paramtype><description><para>The capacity of the created <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para></description></parameter><parameter name="first"><paramtype>InputIterator</paramtype><description><para>The beginning of the range to be copied. </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>The end of the range to be copied. </para></description></parameter><parameter name="alloc"><paramtype>const allocator_type &amp;</paramtype><default>allocator_type()</default><description><para>The allocator. </para></description></parameter><purpose>Create a <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> with the specified capacity and filled with a copy of the range. </purpose><description><para>



<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(first, last)</computeroutput>; in <computeroutput>min[capacity, std::distance(first, last)]</computeroutput> if the <computeroutput>InputIterator</computeroutput> is a <ulink url="https://www.boost.org/sgi/stl/RandomAccessIterator.html">RandomAccessIterator</ulink>). </para>
</formalpara>
</para></description><requires><para>Valid range <computeroutput>[first, last)</computeroutput>.<sbr/>
 <computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> have to meet the requirements of <ulink url="https://www.boost.org/sgi/stl/InputIterator.html">InputIterator</ulink>. </para>
</requires><postconditions><para><computeroutput>capacity() == buffer_capacity &amp;&amp; size() &lt;= std::distance(first, last) &amp;&amp; (*this)[0]== *(last - buffer_capacity) &amp;&amp; (*this)[1] == *(last - buffer_capacity + 1) &amp;&amp; ... &amp;&amp; (*this)[buffer_capacity - 1] == *(last - 1)</computeroutput><sbr/>
<sbr/>
 If the number of items to be copied from the range <computeroutput>[first, last)</computeroutput> is greater than the specified <computeroutput>buffer_capacity</computeroutput> then only elements from the range <computeroutput>[last - buffer_capacity, last)</computeroutput> will be copied. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></constructor>
<destructor><purpose>The destructor. </purpose><description><para>Destroys the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. 
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (including iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>) for scalar types; linear for other types. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>clear()</computeroutput> </para>
</para>
</para></description><throws><simpara><classname>Nothing.</classname> </simpara></throws></destructor>
<copy-assignment><type><classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</type><parameter name="cb"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to be copied. </para></description></parameter><purpose>The assign operator. </purpose><description><para>Makes this <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to become a copy of the specified <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. 


<formalpara><title>Exception Safety</title><para>Strong. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to this <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of <computeroutput>cb</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>assign(size_type, const_reference)</computeroutput>, <computeroutput>assign(capacity_type, size_type, const_reference)</computeroutput>, <computeroutput>assign(InputIterator, InputIterator)</computeroutput>, <computeroutput>assign(capacity_type, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>*this == cb</computeroutput> </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></copy-assignment>
<copy-assignment cv="noexcept"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</type><parameter name="cb"><paramtype><classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;&amp;</paramtype><description><para><computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to 'steal' value from. </para></description></parameter><purpose>Move assigns content of <computeroutput>cb</computeroutput> to <computeroutput>*this</computeroutput>, leaving <computeroutput>cb</computeroutput> empty. </purpose><description><para>



<formalpara><title>Complexity</title><para>Constant. </para>
</formalpara>
</para></description><requires><para>C++ compiler with rvalue references support. </para>
</requires><postconditions><para><computeroutput>cb.empty()</computeroutput> </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></copy-assignment>
</class><function name="operator=="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to compare. </para></description></parameter><parameter name="rhs"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to compare. </para></description></parameter><purpose>Compare two <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s element-by-element to determine if they are equal. </purpose><description><para>


<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s). </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
</para></description><returns><para><computeroutput>lhs.size() == rhs.size() &amp;&amp; <ulink url="https://www.boost.org/sgi/stl/equal.html">std::equal</ulink>(lhs.begin(), lhs.end(), rhs.begin())</computeroutput> </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></function>
<function name="operator&lt;"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to compare. </para></description></parameter><parameter name="rhs"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to compare. </para></description></parameter><purpose>Compare two <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s element-by-element to determine if the left one is lesser than the right one. </purpose><description><para>


<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s). </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
</para></description><returns><para><computeroutput><ulink url="https://www.boost.org/sgi/stl/lexicographical_compare.html">std::lexicographical_compare</ulink>(lhs.begin(), lhs.end(), rhs.begin(), rhs.end())</computeroutput> </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></function>
<function name="operator!="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to compare. </para></description></parameter><parameter name="rhs"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to compare. </para></description></parameter><purpose>Compare two <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s element-by-element to determine if they are non-equal. </purpose><description><para>


<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s). </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator==(const circular_buffer&lt;T,Alloc&gt;&amp;, const circular_buffer&lt;T,Alloc&gt;&amp;)</computeroutput> </para>
</para>
</para></description><returns><para><computeroutput>!(lhs == rhs)</computeroutput> </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></function>
<function name="operator&gt;"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to compare. </para></description></parameter><parameter name="rhs"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to compare. </para></description></parameter><purpose>Compare two <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s element-by-element to determine if the left one is greater than the right one. </purpose><description><para>


<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s). </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator&lt;(const circular_buffer&lt;T,Alloc&gt;&amp;, const circular_buffer&lt;T,Alloc&gt;&amp;)</computeroutput> </para>
</para>
</para></description><returns><para><computeroutput>rhs &lt; lhs</computeroutput> </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></function>
<function name="operator&lt;="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to compare. </para></description></parameter><parameter name="rhs"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to compare. </para></description></parameter><purpose>Compare two <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s element-by-element to determine if the left one is lesser or equal to the right one. </purpose><description><para>


<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s). </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator&lt;(const circular_buffer&lt;T,Alloc&gt;&amp;, const circular_buffer&lt;T,Alloc&gt;&amp;)</computeroutput> </para>
</para>
</para></description><returns><para><computeroutput>!(rhs &lt; lhs)</computeroutput> </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></function>
<function name="operator&gt;="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to compare. </para></description></parameter><parameter name="rhs"><paramtype>const <classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to compare. </para></description></parameter><purpose>Compare two <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s element-by-element to determine if the left one is greater or equal to the right one. </purpose><description><para>


<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s). </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator&lt;(const circular_buffer&lt;T,Alloc&gt;&amp;, const circular_buffer&lt;T,Alloc&gt;&amp;)</computeroutput> </para>
</para>
</para></description><returns><para><computeroutput>!(lhs &lt; rhs)</computeroutput> </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></function>
<function name="swap"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype><classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> whose content will be swapped with <computeroutput>rhs</computeroutput>. </para></description></parameter><parameter name="rhs"><paramtype><classname>circular_buffer</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> whose content will be swapped with <computeroutput>lhs</computeroutput>. </para></description></parameter><purpose>Swap the contents of two <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s. </purpose><description><para>


<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s). </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators of both <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>s. (On the other hand the iterators still point to the same elements but within another container. If you want to rely on this feature you have to turn the <ulink url="#debug">Debug Support</ulink> off otherwise an assertion will report an error if such invalidated iterator is used.) </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>swap(circular_buffer&lt;T, Alloc&gt;&amp;)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>lhs</computeroutput> contains elements of <computeroutput>rhs</computeroutput> and vice versa. </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></function>







</namespace>
</header>
<header name="boost/circular_buffer/space_optimized.hpp">
<namespace name="boost">
<class name="circular_buffer_space_optimized"><template>
      <template-type-parameter name="T"/>
      <template-type-parameter name="Alloc"/>
    </template><inherit access="private">boost::circular_buffer&lt; T, Alloc &gt;</inherit><purpose>Space optimized circular buffer container adaptor. <computeroutput>T</computeroutput> must be a copyable class or must have an noexcept move constructor and move assignment operator. </purpose><typedef name="value_type"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::value_type</type></typedef>
<typedef name="pointer"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::pointer</type></typedef>
<typedef name="const_pointer"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::const_pointer</type></typedef>
<typedef name="reference"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::reference</type></typedef>
<typedef name="const_reference"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::const_reference</type></typedef>
<typedef name="size_type"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::size_type</type></typedef>
<typedef name="difference_type"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::difference_type</type></typedef>
<typedef name="allocator_type"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::allocator_type</type></typedef>
<typedef name="const_iterator"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::const_iterator</type></typedef>
<typedef name="iterator"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::iterator</type></typedef>
<typedef name="const_reverse_iterator"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::const_reverse_iterator</type></typedef>
<typedef name="reverse_iterator"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::reverse_iterator</type></typedef>
<typedef name="array_range"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::array_range</type></typedef>
<typedef name="const_array_range"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::const_array_range</type></typedef>
<typedef name="param_value_type"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::param_value_type</type></typedef>
<typedef name="rvalue_type"><type><classname>circular_buffer</classname>&lt; T, Alloc &gt;::rvalue_type</type></typedef>
<typedef name="capacity_type"><description><para>Capacity controller of the space optimized circular buffer.</para><para><para><emphasis role="bold">See Also:</emphasis><para>capacity_control in details.hpp. </para>
</para>
<computeroutput> class capacity_control<sbr/>
 {<sbr/>
 size_type m_capacity; // Available capacity.<sbr/>
 size_type m_min_capacity; // Minimum capacity.<sbr/>
 public:<sbr/>
 capacity_control(size_type capacity, size_type min_capacity = 0)<sbr/>
 : m_capacity(capacity), m_min_capacity(min_capacity)<sbr/>
 {};<sbr/>
 size_type capacity() const { return m_capacity; }<sbr/>
 size_type min_capacity() const { return m_min_capacity; }<sbr/>
 operator size_type() const { return m_capacity; }<sbr/>
 };<sbr/>
 </computeroutput> </para><para>Always <computeroutput>capacity &gt;= min_capacity</computeroutput>. </para><para>The <computeroutput>capacity()</computeroutput> represents the capacity of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> and the <computeroutput>min_capacity()</computeroutput> determines the minimal allocated size of its internal buffer. </para><para>The converting constructor of the <computeroutput>capacity_control</computeroutput> allows implicit conversion from <computeroutput>size_type</computeroutput>-like types which ensures compatibility of creating an instance of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> with other STL containers.</para><para>On the other hand the operator <computeroutput>size_type()</computeroutput> provides implicit conversion to the <computeroutput>size_type</computeroutput> which allows to treat the capacity of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> the same way as in the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para></description><type>cb_details::capacity_control&lt; size_type &gt;</type></typedef>
<method-group name="public member functions">
<method name="full" cv="const noexcept"><type>bool</type><purpose>Is the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> full? </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>empty()</computeroutput> </para>
</para>
</para></description><returns><para><computeroutput>true</computeroutput> if the number of elements stored in the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> equals the capacity of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>; <computeroutput>false</computeroutput> otherwise. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="reserve" cv="const noexcept"><type>size_type</type><purpose>Get the maximum number of elements which can be inserted into the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> without overwriting any of already stored elements. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>capacity()</computeroutput>, <computeroutput>size()</computeroutput>, <computeroutput>max_size()</computeroutput> </para>
</para>
</para></description><returns><para><computeroutput>capacity().capacity() - size()</computeroutput> </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="capacity" cv="const noexcept"><type>const capacity_type &amp;</type><purpose>Get the capacity of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Does not invalidate any iterators. </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>reserve()</computeroutput>, <computeroutput>size()</computeroutput>, <computeroutput>max_size()</computeroutput>, <computeroutput>set_capacity(const capacity_type&amp;)</computeroutput> </para>
</para>
</para></description><returns><para>The capacity controller representing the maximum number of elements which can be stored in the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> and the minimal allocated size of the internal buffer. </para>
</returns><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="set_capacity"><type>void</type><parameter name="capacity_ctrl"><paramtype>const capacity_type &amp;</paramtype><description><para>The new capacity controller. </para></description></parameter><purpose>Change the capacity (and the minimal guaranteed amount of allocated memory) of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Strong. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>min[size(), capacity_ctrl.capacity()]</computeroutput>). </para>
</formalpara>
<note><para>To explicitly clear the extra allocated memory use the <emphasis role="bold">shrink-to-fit</emphasis> technique:<sbr/>
<sbr/>
 <computeroutput>boost::circular_buffer_space_optimized&lt;int&gt; cb(1000);<sbr/>
 ...<sbr/>
 boost::circular_buffer_space_optimized&lt;int&gt;(cb).swap(cb);</computeroutput><sbr/>
<sbr/>
 For more information about the shrink-to-fit technique in STL see <ulink url="http://www.gotw.ca/gotw/054.htm">http://www.gotw.ca/gotw/054.htm</ulink>. </para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rset_capacity(const capacity_type&amp;)</computeroutput>, <computeroutput>resize(size_type, const_reference)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>capacity() == capacity_ctrl &amp;&amp; size() &lt;= capacity_ctrl.capacity()</computeroutput><sbr/>
<sbr/>
 If the current number of elements stored in the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is greater than the desired new capacity then number of <computeroutput>[size() - capacity_ctrl.capacity()]</computeroutput> <emphasis role="bold">last</emphasis> elements will be removed and the new size will be equal to <computeroutput>capacity_ctrl.capacity()</computeroutput>.<sbr/>
<sbr/>
 If the current number of elements stored in the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is lower than the new capacity then the amount of allocated memory in the internal buffer may be accommodated as necessary but it will never drop below <computeroutput>capacity_ctrl.min_capacity()</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted, (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="resize"><type>void</type><parameter name="new_size"><paramtype>size_type</paramtype><description><para>The new size. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><default>value_type()</default><description><para>The element the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be filled with in order to gain the requested size. (See the <emphasis>Effect</emphasis>.) </para></description></parameter><purpose>Change the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the new size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rresize(size_type, const_reference)</computeroutput>, <computeroutput>set_capacity(const capacity_type&amp;)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>size() == new_size &amp;&amp; capacity().capacity() &gt;= new_size</computeroutput><sbr/>
<sbr/>
 If the new size is greater than the current size, copies of <computeroutput>item</computeroutput> will be inserted at the <emphasis role="bold">back</emphasis> of the of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> in order to achieve the desired size. In the case the resulting size exceeds the current capacity the capacity will be set to <computeroutput>new_size</computeroutput>.<sbr/>
<sbr/>
 If the current number of elements stored in the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is greater than the desired new size then number of <computeroutput>[size() - new_size]</computeroutput> <emphasis role="bold">last</emphasis> elements will be removed. (The capacity will remain unchanged.)<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be accommodated as necessary. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="rset_capacity"><type>void</type><parameter name="capacity_ctrl"><paramtype>const capacity_type &amp;</paramtype><description><para>The new capacity controller. </para></description></parameter><purpose>Change the capacity (and the minimal guaranteed amount of allocated memory) of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Strong. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>min[size(), capacity_ctrl.capacity()]</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>set_capacity(const capacity_type&amp;)</computeroutput>, <computeroutput>rresize(size_type, const_reference)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>capacity() == capacity_ctrl &amp;&amp; size() &lt;= capacity_ctrl</computeroutput><sbr/>
<sbr/>
 If the current number of elements stored in the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is greater than the desired new capacity then number of <computeroutput>[size() - capacity_ctrl.capacity()]</computeroutput> <emphasis role="bold">first</emphasis> elements will be removed and the new size will be equal to <computeroutput>capacity_ctrl.capacity()</computeroutput>.<sbr/>
<sbr/>
 If the current number of elements stored in the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is lower than the new capacity then the amount of allocated memory in the internal buffer may be accommodated as necessary but it will never drop below <computeroutput>capacity_ctrl.min_capacity()</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="rresize"><type>void</type><parameter name="new_size"><paramtype>size_type</paramtype><description><para>The new size. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><default>value_type()</default><description><para>The element the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be filled with in order to gain the requested size. (See the <emphasis>Effect</emphasis>.) </para></description></parameter><purpose>Change the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the new size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>resize(size_type, const_reference)</computeroutput>, <computeroutput>rset_capacity(const capacity_type&amp;)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>size() == new_size &amp;&amp; capacity().capacity() &gt;= new_size</computeroutput><sbr/>
<sbr/>
 If the new size is greater than the current size, copies of <computeroutput>item</computeroutput> will be inserted at the <emphasis role="bold">front</emphasis> of the of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> in order to achieve the desired size. In the case the resulting size exceeds the current capacity the capacity will be set to <computeroutput>new_size</computeroutput>.<sbr/>
<sbr/>
 If the current number of elements stored in the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is greater than the desired new size then number of <computeroutput>[size() - new_size]</computeroutput> <emphasis role="bold">first</emphasis> elements will be removed. (The capacity will remain unchanged.)<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be accommodated as necessary. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="assign"><type>void</type><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of elements the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be filled with. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be filled with. </para></description></parameter><purpose>Assign <computeroutput>n</computeroutput> items into the space optimized circular buffer. </purpose><description><para>The content of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be removed and replaced with <computeroutput>n</computeroutput> copies of the <computeroutput>item</computeroutput>. 


<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the <computeroutput>n</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator=</computeroutput>, <computeroutput>assign(capacity_type, size_type, const_reference)</computeroutput>, <computeroutput>assign(InputIterator, InputIterator)</computeroutput>, <computeroutput>assign(capacity_type, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>capacity().capacity() == n &amp;&amp; capacity().min_capacity() == 0 &amp;&amp; size() == n &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this) [n - 1] == item</computeroutput><sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer is <computeroutput>n</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="assign"><type>void</type><parameter name="capacity_ctrl"><paramtype>capacity_type</paramtype><description><para>The new capacity controller. </para></description></parameter><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of elements the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be filled with. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be filled with. </para></description></parameter><purpose>Assign <computeroutput>n</computeroutput> items into the space optimized circular buffer specifying the capacity. </purpose><description><para>The capacity of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be set to the specified value and the content of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be removed and replaced with <computeroutput>n</computeroutput> copies of the <computeroutput>item</computeroutput>. 



<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the <computeroutput>n</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator=</computeroutput>, <computeroutput>assign(size_type, const_reference)</computeroutput>, <computeroutput>assign(InputIterator, InputIterator)</computeroutput>, <computeroutput>assign(capacity_type, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>capacity_ctrl.capacity() &gt;= n</computeroutput> </para>
</requires><postconditions><para><computeroutput>capacity() == capacity_ctrl &amp;&amp; size() == n &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this) [n - 1] == item </computeroutput><sbr/>
<sbr/>
 The amount of allocated memory will be <computeroutput>max[n, capacity_ctrl.min_capacity()]</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="assign"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>The beginning of the range to be copied. </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>The end of the range to be copied. </para></description></parameter><purpose>Assign a copy of the range into the space optimized circular buffer. </purpose><description><para>The content of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be removed and replaced with copies of elements from the specified range. 



<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the <computeroutput>std::distance(first, last)</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator=</computeroutput>, <computeroutput>assign(size_type, const_reference)</computeroutput>, <computeroutput>assign(capacity_type, size_type, const_reference)</computeroutput>, <computeroutput>assign(capacity_type, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para>Valid range <computeroutput>[first, last)</computeroutput>.<sbr/>
 <computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> have to meet the requirements of <ulink url="https://www.boost.org/sgi/stl/InputIterator.html">InputIterator</ulink>. </para>
</requires><postconditions><para><computeroutput>capacity().capacity() == std::distance(first, last) &amp;&amp; capacity().min_capacity() == 0 &amp;&amp; size() == std::distance(first, last) &amp;&amp; (*this)[0]== *first &amp;&amp; (*this)[1] == *(first + 1) &amp;&amp; ... &amp;&amp; (*this)[std::distance(first, last) - 1] == *(last - 1)</computeroutput><sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer is <computeroutput>std::distance(first, last)</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept and <computeroutput>InputIterator</computeroutput> is a move iterator. </simpara></throws></method>
<method name="assign"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="capacity_ctrl"><paramtype>capacity_type</paramtype><description><para>The new capacity controller. </para></description></parameter><parameter name="first"><paramtype>InputIterator</paramtype><description><para>The beginning of the range to be copied. </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>The end of the range to be copied. </para></description></parameter><purpose>Assign a copy of the range into the space optimized circular buffer specifying the capacity. </purpose><description><para>The capacity of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be set to the specified value and the content of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be removed and replaced with copies of elements from the specified range. 



<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(first, last)</computeroutput>; in <computeroutput>min[capacity_ctrl.capacity(), std::distance(first, last)]</computeroutput> if the <computeroutput>InputIterator</computeroutput> is a <ulink url="https://www.boost.org/sgi/stl/RandomAccessIterator.html">RandomAccessIterator</ulink>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>operator=</computeroutput>, <computeroutput>assign(size_type, const_reference)</computeroutput>, <computeroutput>assign(capacity_type, size_type, const_reference)</computeroutput>, <computeroutput>assign(InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para>Valid range <computeroutput>[first, last)</computeroutput>.<sbr/>
 <computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> have to meet the requirements of <ulink url="https://www.boost.org/sgi/stl/InputIterator.html">InputIterator</ulink>. </para>
</requires><postconditions><para><computeroutput>capacity() == capacity_ctrl &amp;&amp; size() &lt;= std::distance(first, last) &amp;&amp; (*this)[0]== *(last - capacity) &amp;&amp; (*this)[1] == *(last - capacity + 1) &amp;&amp; ... &amp;&amp; (*this)[capacity - 1] == *(last - 1)</computeroutput><sbr/>
<sbr/>
 If the number of items to be copied from the range <computeroutput>[first, last)</computeroutput> is greater than the specified <computeroutput>capacity</computeroutput> then only elements from the range <computeroutput>[last - capacity, last)</computeroutput> will be copied.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer is <computeroutput>max[std::distance(first, last), capacity_ctrl.min_capacity()]</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept and <computeroutput>InputIterator</computeroutput> is a move iterator. </simpara></throws></method>
<method name="swap" cv="noexcept"><type>void</type><parameter name="cb"><paramtype><classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> whose content will be swapped. </para></description></parameter><purpose>Swap the contents of two space-optimized circular-buffers. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>No-throw. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators of both <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> containers. (On the other hand the iterators still point to the same elements but within another container. If you want to rely on this feature you have to turn the __debug_support off, otherwise an assertion will report an error if such invalidated iterator is used.) </para>
</formalpara>
<formalpara><title>Complexity</title><para>Constant (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>swap(circular_buffer&lt;T, Alloc&gt;&amp;, circular_buffer&lt;T, Alloc&gt;&amp;)</computeroutput>, <computeroutput>swap(circular_buffer_space_optimized&lt;T, Alloc&gt;&amp;, circular_buffer_space_optimized&lt;T, Alloc&gt;&amp;)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>this</computeroutput> contains elements of <computeroutput>cb</computeroutput> and vice versa; the capacity and the amount of allocated memory in the internal buffer of <computeroutput>this</computeroutput> equal to the capacity and the amount of allocated memory of <computeroutput>cb</computeroutput> and vice versa. </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></method>
<method name="push_back"><type>void</type><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert a new element at the end of the space optimized circular buffer. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>push_front(const_reference)</computeroutput>, <computeroutput>pop_back()</computeroutput>, <computeroutput>pop_front()</computeroutput> </para>
</para>
</para></description><postconditions><para>if <computeroutput>capacity().capacity() &gt; 0</computeroutput> then <computeroutput>back() == item</computeroutput><sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full, the first element will be removed. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="push_back"><type>void</type><parameter name="item"><paramtype>rvalue_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert a new element at the end of the space optimized circular buffer. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>push_front(const_reference)</computeroutput>, <computeroutput>pop_back()</computeroutput>, <computeroutput>pop_front()</computeroutput> </para>
</para>
</para></description><postconditions><para>if <computeroutput>capacity().capacity() &gt; 0</computeroutput> then <computeroutput>back() == item</computeroutput><sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full, the first element will be removed. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). </simpara></throws></method>
<method name="push_back"><type>void</type><purpose>Insert a new element at the end of the space optimized circular buffer. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>push_front(const_reference)</computeroutput>, <computeroutput>pop_back()</computeroutput>, <computeroutput>pop_front()</computeroutput> </para>
</para>
</para></description><postconditions><para>if <computeroutput>capacity().capacity() &gt; 0</computeroutput> then <computeroutput>back() == item</computeroutput><sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full, the first element will be removed. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T()</computeroutput> throws. Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="push_front"><type>void</type><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert a new element at the beginning of the space optimized circular buffer. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>push_back(const_reference)</computeroutput>, <computeroutput>pop_back()</computeroutput>, <computeroutput>pop_front()</computeroutput> </para>
</para>
</para></description><postconditions><para>if <computeroutput>capacity().capacity() &gt; 0</computeroutput> then <computeroutput>front() == item</computeroutput><sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full, the last element will be removed. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="push_front"><type>void</type><parameter name="item"><paramtype>rvalue_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert a new element at the beginning of the space optimized circular buffer. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>push_back(const_reference)</computeroutput>, <computeroutput>pop_back()</computeroutput>, <computeroutput>pop_front()</computeroutput> </para>
</para>
</para></description><postconditions><para>if <computeroutput>capacity().capacity() &gt; 0</computeroutput> then <computeroutput>front() == item</computeroutput><sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full, the last element will be removed. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="push_front"><type>void</type><purpose>Insert a new element at the beginning of the space optimized circular buffer. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>push_back(const_reference)</computeroutput>, <computeroutput>pop_back()</computeroutput>, <computeroutput>pop_front()</computeroutput> </para>
</para>
</para></description><postconditions><para>if <computeroutput>capacity().capacity() &gt; 0</computeroutput> then <computeroutput>front() == item</computeroutput><sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full, the last element will be removed. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T()</computeroutput> throws. Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="pop_back"><type>void</type><purpose>Remove the last element from the space optimized circular buffer. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>pop_front()</computeroutput>, <computeroutput>push_back(const_reference)</computeroutput>, <computeroutput>push_front(const_reference)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>!empty()</computeroutput> </para>
</requires><postconditions><para>The last element is removed from the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively decreased. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). </simpara></throws></method>
<method name="pop_front"><type>void</type><purpose>Remove the first element from the space optimized circular buffer. </purpose><description><para>


<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>pop_back()</computeroutput>, <computeroutput>push_back(const_reference)</computeroutput>, <computeroutput>push_front(const_reference)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>!empty()</computeroutput> </para>
</requires><postconditions><para>The first element is removed from the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively decreased. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). </simpara></throws></method>
<method name="insert"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the <computeroutput>item</computeroutput> will be inserted. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert an element at the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The <computeroutput>item</computeroutput> will be inserted at the position <computeroutput>pos</computeroutput>.<sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full, the first element will be overwritten. If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full and the <computeroutput>pos</computeroutput> points to <computeroutput>begin()</computeroutput>, then the <computeroutput>item</computeroutput> will not be inserted. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><returns><para>Iterator to the inserted element or <computeroutput>begin()</computeroutput> if the <computeroutput>item</computeroutput> is not inserted. (See the <emphasis>Effect</emphasis>.) </para>
</returns><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="insert"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the <computeroutput>item</computeroutput> will be inserted. </para></description></parameter><parameter name="item"><paramtype>rvalue_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert an element at the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The <computeroutput>item</computeroutput> will be inserted at the position <computeroutput>pos</computeroutput>.<sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full, the first element will be overwritten. If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full and the <computeroutput>pos</computeroutput> points to <computeroutput>begin()</computeroutput>, then the <computeroutput>item</computeroutput> will not be inserted. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><returns><para>Iterator to the inserted element or <computeroutput>begin()</computeroutput> if the <computeroutput>item</computeroutput> is not inserted. (See the <emphasis>Effect</emphasis>.) </para>
</returns><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="insert"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the <computeroutput>item</computeroutput> will be inserted. </para></description></parameter><purpose>Insert an element at the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The <computeroutput>item</computeroutput> will be inserted at the position <computeroutput>pos</computeroutput>.<sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full, the first element will be overwritten. If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full and the <computeroutput>pos</computeroutput> points to <computeroutput>begin()</computeroutput>, then the <computeroutput>item</computeroutput> will not be inserted. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><returns><para>Iterator to the inserted element or <computeroutput>begin()</computeroutput> if the <computeroutput>item</computeroutput> is not inserted. (See the <emphasis>Effect</emphasis>.) </para>
</returns><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T()</computeroutput> throws. Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="insert"><type>void</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the <computeroutput>item</computeroutput>s will be inserted. </para></description></parameter><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of <computeroutput>item</computeroutput>s the to be inserted. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element whose copies will be inserted. </para></description></parameter><purpose>Insert <computeroutput>n</computeroutput> copies of the <computeroutput>item</computeroutput> at the specified position. </purpose><description><para>



<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>min[capacity().capacity(), size() + n]</computeroutput>). </para>
</formalpara>
<formalpara><title>Example</title><para>Consider a <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<sbr/>
<sbr/>
 <computeroutput>|1|2|3|4| | |</computeroutput><sbr/>
 <computeroutput>p ___^</computeroutput><sbr/>
<sbr/>
After inserting 5 elements at the position <computeroutput>p</computeroutput>:<sbr/>
<sbr/>
 <computeroutput>insert(p, (size_t)5, 0);</computeroutput><sbr/>
<sbr/>
actually only 4 elements get inserted and elements <computeroutput>1</computeroutput> and <computeroutput>2</computeroutput> are overwritten. This is due to the fact the insert operation preserves the capacity. After insertion the internal buffer looks like this:<sbr/>
<sbr/>
<computeroutput>|0|0|0|0|3|4|</computeroutput><sbr/>
 <sbr/>
For comparison if the capacity would not be preserved the internal buffer would then result in <computeroutput>|1|2|0|0|0|0|0|3|4|</computeroutput>. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The number of <computeroutput>min[n, (pos - begin()) + reserve()]</computeroutput> elements will be inserted at the position <computeroutput>pos</computeroutput>.<sbr/>
The number of <computeroutput>min[pos - begin(), max[0, n - reserve()]]</computeroutput> elements will be overwritten at the beginning of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>.<sbr/>
(See <emphasis>Example</emphasis> for the explanation.)<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="insert"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the range will be inserted. </para></description></parameter><parameter name="first"><paramtype>InputIterator</paramtype><description><para>The beginning of the range to be inserted. </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>The end of the range to be inserted. </para></description></parameter><purpose>Insert the range <computeroutput>[first, last)</computeroutput> at the specified position. </purpose><description><para>



<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>[size() + std::distance(first, last)]</computeroutput>; in <computeroutput>min[capacity().capacity(), size() + std::distance(first, last)]</computeroutput> if the <computeroutput>InputIterator</computeroutput> is a <ulink url="https://www.boost.org/sgi/stl/RandomAccessIterator.html">RandomAccessIterator</ulink>). </para>
</formalpara>
<formalpara><title>Example</title><para>Consider a <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<sbr/>
<sbr/>
 <computeroutput>|1|2|3|4| | |</computeroutput><sbr/>
 <computeroutput>p ___^</computeroutput><sbr/>
<sbr/>
After inserting a range of elements at the position <computeroutput>p</computeroutput>:<sbr/>
<sbr/>
 <computeroutput>int array[] = { 5, 6, 7, 8, 9 };</computeroutput><sbr/>
<computeroutput>insert(p, array, array + 5);</computeroutput><sbr/>
<sbr/>
 actually only elements <computeroutput>6</computeroutput>, <computeroutput>7</computeroutput>, <computeroutput>8</computeroutput> and <computeroutput>9</computeroutput> from the specified range get inserted and elements <computeroutput>1</computeroutput> and <computeroutput>2</computeroutput> are overwritten. This is due to the fact the insert operation preserves the capacity. After insertion the internal buffer looks like this:<sbr/>
<sbr/>
<computeroutput>|6|7|8|9|3|4|</computeroutput><sbr/>
<sbr/>
For comparison if the capacity would not be preserved the internal buffer would then result in <computeroutput>|1|2|5|6|7|8|9|3|4|</computeroutput>. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> or its end.<sbr/>
Valid range <computeroutput>[first, last)</computeroutput> where <computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> meet the requirements of an <ulink url="https://www.boost.org/sgi/stl/InputIterator.html">InputIterator</ulink>. </para>
</requires><postconditions><para>Elements from the range <computeroutput>[first + max[0, distance(first, last) - (pos - begin()) - reserve()], last)</computeroutput> will be inserted at the position <computeroutput>pos</computeroutput>.<sbr/>
The number of <computeroutput>min[pos - begin(), max[0, distance(first, last) - reserve()]]</computeroutput> elements will be overwritten at the beginning of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>.<sbr/>
(See <emphasis>Example</emphasis> for the explanation.)<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="rinsert"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position before which the <computeroutput>item</computeroutput> will be inserted. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert an element before the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The <computeroutput>item</computeroutput> will be inserted before the position <computeroutput>pos</computeroutput>.<sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full, the last element will be overwritten. If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full and the <computeroutput>pos</computeroutput> points to <computeroutput>end()</computeroutput>, then the <computeroutput>item</computeroutput> will not be inserted. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><returns><para>Iterator to the inserted element or <computeroutput>end()</computeroutput> if the <computeroutput>item</computeroutput> is not inserted. (See the <emphasis>Effect</emphasis>.) </para>
</returns><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="rinsert"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position before which the <computeroutput>item</computeroutput> will be inserted. </para></description></parameter><parameter name="item"><paramtype>rvalue_type</paramtype><description><para>The element to be inserted. </para></description></parameter><purpose>Insert an element before the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The <computeroutput>item</computeroutput> will be inserted before the position <computeroutput>pos</computeroutput>.<sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full, the last element will be overwritten. If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full and the <computeroutput>pos</computeroutput> points to <computeroutput>end()</computeroutput>, then the <computeroutput>item</computeroutput> will not be inserted. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><returns><para>Iterator to the inserted element or <computeroutput>end()</computeroutput> if the <computeroutput>item</computeroutput> is not inserted. (See the <emphasis>Effect</emphasis>.) </para>
</returns><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="rinsert"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position before which the <computeroutput>item</computeroutput> will be inserted. </para></description></parameter><purpose>Insert an element before the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The <computeroutput>item</computeroutput> will be inserted before the position <computeroutput>pos</computeroutput>.<sbr/>
 If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full, the last element will be overwritten. If the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> is full and the <computeroutput>pos</computeroutput> points to <computeroutput>end()</computeroutput>, then the <computeroutput>item</computeroutput> will not be inserted. If the capacity is <computeroutput>0</computeroutput>, nothing will be inserted.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><returns><para>Iterator to the inserted element or <computeroutput>end()</computeroutput> if the <computeroutput>item</computeroutput> is not inserted. (See the <emphasis>Effect</emphasis>.) </para>
</returns><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T()</computeroutput> throws. Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="rinsert"><type>void</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the <computeroutput>item</computeroutput>s will be inserted. </para></description></parameter><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of <computeroutput>item</computeroutput>s the to be inserted. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element whose copies will be inserted. </para></description></parameter><purpose>Insert <computeroutput>n</computeroutput> copies of the <computeroutput>item</computeroutput> before the specified position. </purpose><description><para>



<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>min[capacity().capacity(), size() + n]</computeroutput>). </para>
</formalpara>
<formalpara><title>Example</title><para>Consider a <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<sbr/>
<sbr/>
 <computeroutput>|1|2|3|4| | |</computeroutput><sbr/>
 <computeroutput>p ___^</computeroutput><sbr/>
<sbr/>
After inserting 5 elements before the position <computeroutput>p</computeroutput>:<sbr/>
<sbr/>
 <computeroutput>rinsert(p, (size_t)5, 0);</computeroutput><sbr/>
<sbr/>
actually only 4 elements get inserted and elements <computeroutput>3</computeroutput> and <computeroutput>4</computeroutput> are overwritten. This is due to the fact the rinsert operation preserves the capacity. After insertion the internal buffer looks like this:<sbr/>
<sbr/>
<computeroutput>|1|2|0|0|0|0|</computeroutput><sbr/>
 <sbr/>
For comparison if the capacity would not be preserved the internal buffer would then result in <computeroutput>|1|2|0|0|0|0|0|3|4|</computeroutput>. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, InputIterator, InputIterator)</computeroutput>, <computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> or its end. </para>
</requires><postconditions><para>The number of <computeroutput>min[n, (end() - pos) + reserve()]</computeroutput> elements will be inserted before the position <computeroutput>pos</computeroutput>.<sbr/>
The number of <computeroutput>min[end() - pos, max[0, n - reserve()]]</computeroutput> elements will be overwritten at the end of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>.<sbr/>
(See <emphasis>Example</emphasis> for the explanation.)<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="rinsert"><type>void</type><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator specifying the position where the range will be inserted. </para></description></parameter><parameter name="first"><paramtype>InputIterator</paramtype><description><para>The beginning of the range to be inserted. </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>The end of the range to be inserted. </para></description></parameter><purpose>Insert the range <computeroutput>[first, last)</computeroutput> before the specified position. </purpose><description><para>



<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in <computeroutput>[size() + std::distance(first, last)]</computeroutput>; in <computeroutput>min[capacity().capacity(), size() + std::distance(first, last)]</computeroutput> if the <computeroutput>InputIterator</computeroutput> is a <ulink url="https://www.boost.org/sgi/stl/RandomAccessIterator.html">RandomAccessIterator</ulink>). </para>
</formalpara>
<formalpara><title>Example</title><para>Consider a <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<sbr/>
<sbr/>
 <computeroutput>|1|2|3|4| | |</computeroutput><sbr/>
 <computeroutput>p ___^</computeroutput><sbr/>
<sbr/>
After inserting a range of elements before the position <computeroutput>p</computeroutput>:<sbr/>
<sbr/>
 <computeroutput>int array[] = { 5, 6, 7, 8, 9 };</computeroutput><sbr/>
<computeroutput>insert(p, array, array + 5);</computeroutput><sbr/>
<sbr/>
 actually only elements <computeroutput>5</computeroutput>, <computeroutput>6</computeroutput>, <computeroutput>7</computeroutput> and <computeroutput>8</computeroutput> from the specified range get inserted and elements <computeroutput>3</computeroutput> and <computeroutput>4</computeroutput> are overwritten. This is due to the fact the rinsert operation preserves the capacity. After insertion the internal buffer looks like this:<sbr/>
<sbr/>
<computeroutput>|1|2|5|6|7|8|</computeroutput><sbr/>
<sbr/>
For comparison if the capacity would not be preserved the internal buffer would then result in <computeroutput>|1|2|5|6|7|8|9|3|4|</computeroutput>. </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>rinsert(iterator, value_type)</computeroutput>, <computeroutput>rinsert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, value_type)</computeroutput>, <computeroutput>insert(iterator, size_type, value_type)</computeroutput>, <computeroutput>insert(iterator, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> or its end.<sbr/>
 Valid range <computeroutput>[first, last)</computeroutput> where <computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> meet the requirements of an <ulink url="https://www.boost.org/sgi/stl/InputIterator.html">InputIterator</ulink>. </para>
</requires><postconditions><para>Elements from the range <computeroutput>[first, last - max[0, distance(first, last) - (end() - pos) - reserve()])</computeroutput> will be inserted before the position <computeroutput>pos</computeroutput>.<sbr/>
The number of <computeroutput>min[end() - pos, max[0, distance(first, last) - reserve()]]</computeroutput> elements will be overwritten at the end of the <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>.<sbr/>
(See <emphasis>Example</emphasis> for the explanation.)<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively increased. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws. </simpara></throws></method>
<method name="erase"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator pointing at the element to be removed. </para></description></parameter><purpose>Remove an element at the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>erase(iterator, iterator)</computeroutput>, <computeroutput>rerase(iterator)</computeroutput>, <computeroutput>rerase(iterator, iterator)</computeroutput>, <computeroutput>clear()</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (but not an <computeroutput>end()</computeroutput>). </para>
</requires><postconditions><para>The element at the position <computeroutput>pos</computeroutput> is removed.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively decreased. </para>
</postconditions><returns><para>Iterator to the first element remaining beyond the removed element or <computeroutput>end()</computeroutput> if no such element exists. </para>
</returns><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="erase"><type>iterator</type><parameter name="first"><paramtype>iterator</paramtype><description><para>The beginning of the range to be removed. </para></description></parameter><parameter name="last"><paramtype>iterator</paramtype><description><para>The end of the range to be removed. </para></description></parameter><purpose>Erase the range <computeroutput>[first, last)</computeroutput>. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>erase(iterator)</computeroutput>, <computeroutput>rerase(iterator)</computeroutput>, <computeroutput>rerase(iterator, iterator)</computeroutput>, <computeroutput>clear()</computeroutput> </para>
</para>
</para></description><requires><para>Valid range <computeroutput>[first, last)</computeroutput>. </para>
</requires><postconditions><para>The elements from the range <computeroutput>[first, last)</computeroutput> are removed. (If <computeroutput>first == last</computeroutput> nothing is removed.)<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively decreased. </para>
</postconditions><returns><para>Iterator to the first element remaining beyond the removed elements or <computeroutput>end()</computeroutput> if no such element exists. </para>
</returns><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="rerase"><type>iterator</type><parameter name="pos"><paramtype>iterator</paramtype><description><para>An iterator pointing at the element to be removed. </para></description></parameter><purpose>Remove an element at the specified position. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<note><para>Basically there is no difference between <computeroutput>erase(iterator)</computeroutput> and this method. It is implemented only for consistency with the base <computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>erase(iterator)</computeroutput>, <computeroutput>erase(iterator, iterator)</computeroutput>, <computeroutput>rerase(iterator, iterator)</computeroutput>, <computeroutput>clear()</computeroutput> </para>
</para>
</para></description><requires><para><computeroutput>pos</computeroutput> is a valid iterator pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (but not an <computeroutput>end()</computeroutput>).<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively decreased. </para>
</requires><postconditions><para>The element at the position <computeroutput>pos</computeroutput> is removed. </para>
</postconditions><returns><para>Iterator to the first element remaining in front of the removed element or <computeroutput>begin()</computeroutput> if no such element exists. </para>
</returns><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="rerase"><type>iterator</type><parameter name="first"><paramtype>iterator</paramtype><description><para>The beginning of the range to be removed. </para></description></parameter><parameter name="last"><paramtype>iterator</paramtype><description><para>The end of the range to be removed. </para></description></parameter><purpose>Erase the range <computeroutput>[first, last)</computeroutput>. </purpose><description><para>




<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<note><para>Basically there is no difference between <computeroutput>erase(iterator, iterator)</computeroutput> and this method. It is implemented only for consistency with the base <computeroutput>&lt;<classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput>. </para>
</note>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>erase(iterator)</computeroutput>, <computeroutput>erase(iterator, iterator)</computeroutput>, <computeroutput>rerase(iterator)</computeroutput>, <computeroutput>clear()</computeroutput> </para>
</para>
</para></description><requires><para>Valid range <computeroutput>[first, last)</computeroutput>. </para>
</requires><postconditions><para>The elements from the range <computeroutput>[first, last)</computeroutput> are removed. (If <computeroutput>first == last</computeroutput> nothing is removed.)<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively decreased. </para>
</postconditions><returns><para>Iterator to the first element remaining in front of the removed elements or <computeroutput>begin()</computeroutput> if no such element exists. </para>
</returns><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::operator = (const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::operator = (T&amp;&amp;)</computeroutput> is noexcept. </simpara></throws></method>
<method name="clear"><type>void</type><purpose>Remove all stored elements from the space optimized circular buffer. </purpose><description><para>

<formalpara><title>Exception Safety</title><para>Basic. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>~circular_buffer_space_optimized()</computeroutput>, <computeroutput>erase(iterator)</computeroutput>, <computeroutput>erase(iterator, iterator)</computeroutput>, <computeroutput>rerase(iterator)</computeroutput>, <computeroutput>rerase(iterator, iterator)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>size() == 0</computeroutput><sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer may be predictively decreased. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). </simpara></throws></method>
</method-group>
<constructor specifiers="explicit" cv="noexcept"><parameter name="alloc"><paramtype>const allocator_type &amp;</paramtype><default>allocator_type()</default><description><para>The allocator. </para></description></parameter><purpose>Create an empty space optimized circular buffer with zero capacity. </purpose><description><para>


<formalpara><title>Complexity</title><para>Constant. </para>
</formalpara>
<warning><para>Since Boost version 1.36 the behaviour of this constructor has changed. Now it creates a space optimized circular buffer with zero capacity. </para>
</warning>
</para></description><postconditions><para><computeroutput>capacity().capacity() == 0 &amp;&amp; capacity().min_capacity() == 0 &amp;&amp; size() == 0</computeroutput> </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></constructor>
<constructor specifiers="explicit"><parameter name="capacity_ctrl"><paramtype>capacity_type</paramtype><description><para>The capacity controller representing the maximum number of elements which can be stored in the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> and the minimal allocated size of the internal buffer. </para></description></parameter><parameter name="alloc"><paramtype>const allocator_type &amp;</paramtype><default>allocator_type()</default><description><para>The allocator. </para></description></parameter><purpose>Create an empty space optimized circular buffer with the specified capacity. </purpose><description><para>


<formalpara><title>Complexity</title><para>Constant. </para>
</formalpara>
</para></description><postconditions><para><computeroutput>capacity() == capacity_ctrl &amp;&amp; size() == 0</computeroutput><sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer is <computeroutput>capacity_ctrl.min_capacity()</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). </simpara></throws></constructor>
<constructor><parameter name="capacity_ctrl"><paramtype>capacity_type</paramtype><description><para>The capacity controller representing the maximum number of elements which can be stored in the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> and the minimal allocated size of the internal buffer. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element the created <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be filled with. </para></description></parameter><parameter name="alloc"><paramtype>const allocator_type &amp;</paramtype><default>allocator_type()</default><description><para>The allocator. </para></description></parameter><purpose>Create a full space optimized circular buffer with the specified capacity filled with <computeroutput>capacity_ctrl.capacity()</computeroutput> copies of <computeroutput>item</computeroutput>. </purpose><description><para>


<formalpara><title>Complexity</title><para>Linear (in the <computeroutput>capacity_ctrl.capacity()</computeroutput>). </para>
</formalpara>
</para></description><postconditions><para><computeroutput>capacity() == capacity_ctrl &amp;&amp; full() &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this) [capacity_ctrl.capacity() - 1] == item </computeroutput><sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer is <computeroutput>capacity_ctrl.capacity()</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></constructor>
<constructor><parameter name="capacity_ctrl"><paramtype>capacity_type</paramtype><description><para>The capacity controller representing the maximum number of elements which can be stored in the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> and the minimal allocated size of the internal buffer. </para></description></parameter><parameter name="n"><paramtype>size_type</paramtype><description><para>The number of elements the created <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be filled with. </para></description></parameter><parameter name="item"><paramtype>param_value_type</paramtype><description><para>The element the created <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> will be filled with. </para></description></parameter><parameter name="alloc"><paramtype>const allocator_type &amp;</paramtype><default>allocator_type()</default><description><para>The allocator. </para></description></parameter><purpose>Create a space optimized circular buffer with the specified capacity filled with <computeroutput>n</computeroutput> copies of <computeroutput>item</computeroutput>. </purpose><description><para>



<formalpara><title>Complexity</title><para>Linear (in the <computeroutput>n</computeroutput>). </para>
</formalpara>
</para></description><requires><para><computeroutput>capacity_ctrl.capacity() &gt;= n</computeroutput> </para>
</requires><postconditions><para><computeroutput>capacity() == capacity_ctrl &amp;&amp; size() == n &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this)[n - 1] == item</computeroutput><sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer is <computeroutput>max[n, capacity_ctrl.min_capacity()]</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></constructor>
<constructor><parameter name="cb"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> to be copied. </para></description></parameter><purpose>The copy constructor. </purpose><description><para>Creates a copy of the specified <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>. 


<formalpara><title>Complexity</title><para>Linear (in the size of <computeroutput>cb</computeroutput>). </para>
</formalpara>
</para></description><postconditions><para><computeroutput>*this == cb</computeroutput><sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer is <computeroutput>cb.size()</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></constructor>
<constructor cv="noexcept"><parameter name="cb"><paramtype><classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;&amp;</paramtype><description><para><computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to 'steal' value from. </para></description></parameter><purpose>The move constructor. </purpose><description><para>Move constructs a <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> from <computeroutput>cb</computeroutput>, leaving <computeroutput>cb</computeroutput> empty. 



<formalpara><title>Constant.</title><para/>
</formalpara>
</para></description><requires><para>C++ compiler with rvalue references support. </para>
</requires><postconditions><para><computeroutput>cb.empty()</computeroutput> </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></constructor>
<constructor><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="first"><paramtype>InputIterator</paramtype><description><para>The beginning of the range to be copied. </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>The end of the range to be copied. </para></description></parameter><parameter name="alloc"><paramtype>const allocator_type &amp;</paramtype><default>allocator_type()</default><description><para>The allocator. </para></description></parameter><purpose>Create a full space optimized circular buffer filled with a copy of the range. </purpose><description><para>



<formalpara><title>Complexity</title><para>Linear (in the <computeroutput>std::distance(first, last)</computeroutput>). </para>
</formalpara>
</para></description><requires><para>Valid range <computeroutput>[first, last)</computeroutput>.<sbr/>
 <computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> have to meet the requirements of <ulink url="https://www.boost.org/sgi/stl/InputIterator.html">InputIterator</ulink>. </para>
</requires><postconditions><para><computeroutput>capacity().capacity() == std::distance(first, last) &amp;&amp; capacity().min_capacity() == 0 &amp;&amp; full() &amp;&amp; (*this)[0]== *first &amp;&amp; (*this)[1] == *(first + 1) &amp;&amp; ... &amp;&amp; (*this)[std::distance(first, last) - 1] == *(last - 1)</computeroutput><sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer is <computeroutput>std::distance(first, last)</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws or nothing if <computeroutput>T::T(T&amp;&amp;)</computeroutput> is noexcept and <computeroutput>InputIterator</computeroutput> is a move iterator. </simpara></throws></constructor>
<constructor><template>
          <template-type-parameter name="InputIterator"/>
        </template><parameter name="capacity_ctrl"><paramtype>capacity_type</paramtype><description><para>The capacity controller representing the maximum number of elements which can be stored in the <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> and the minimal allocated size of the internal buffer. </para></description></parameter><parameter name="first"><paramtype>InputIterator</paramtype><description><para>The beginning of the range to be copied. </para></description></parameter><parameter name="last"><paramtype>InputIterator</paramtype><description><para>The end of the range to be copied. </para></description></parameter><parameter name="alloc"><paramtype>const allocator_type &amp;</paramtype><default>allocator_type()</default><description><para>The allocator. </para></description></parameter><purpose>Create a space optimized circular buffer with the specified capacity (and the minimal guaranteed amount of allocated memory) filled with a copy of the range. </purpose><description><para>



<formalpara><title>Complexity</title><para>Linear (in <computeroutput>std::distance(first, last)</computeroutput>; in <computeroutput>min[capacity_ctrl.capacity(), std::distance(first, last)]</computeroutput> if the <computeroutput>InputIterator</computeroutput> is a <ulink url="https://www.boost.org/sgi/stl/RandomAccessIterator.html">RandomAccessIterator</ulink>). </para>
</formalpara>
</para></description><requires><para>Valid range <computeroutput>[first, last)</computeroutput>.<sbr/>
 <computeroutput>first</computeroutput> and <computeroutput>last</computeroutput> have to meet the requirements of <ulink url="https://www.boost.org/sgi/stl/InputIterator.html">InputIterator</ulink>. </para>
</requires><postconditions><para><computeroutput>capacity() == capacity_ctrl &amp;&amp; size() &lt;= std::distance(first, last) &amp;&amp; (*this)[0]== (last - capacity_ctrl.capacity()) &amp;&amp; (*this)[1] == *(last - capacity_ctrl.capacity() + 1) &amp;&amp; ... &amp;&amp; (*this)[capacity_ctrl.capacity() - 1] == *(last - 1)</computeroutput><sbr/>
<sbr/>
 If the number of items to be copied from the range <computeroutput>[first, last)</computeroutput> is greater than the specified <computeroutput>capacity_ctrl.capacity()</computeroutput> then only elements from the range <computeroutput>[last - capacity_ctrl.capacity(), last)</computeroutput> will be copied.<sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer is <computeroutput>max[capacity_ctrl.min_capacity(), min[capacity_ctrl.capacity(), std::distance(first, last)]]</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). Whatever <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></constructor>
<copy-assignment><type><classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</type><parameter name="cb"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype><description><para>The <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> to be copied. </para></description></parameter><purpose>The assign operator. </purpose><description><para>Makes this <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> to become a copy of the specified <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput>. 


<formalpara><title>Exception Safety</title><para>Strong. </para>
</formalpara>
<formalpara><title>Iterator Invalidation</title><para>Invalidates all iterators pointing to this <computeroutput><classname alt="boost::circular_buffer_space_optimized">circular_buffer_space_optimized</classname></computeroutput> (except iterators equal to <computeroutput>end()</computeroutput>). </para>
</formalpara>
<formalpara><title>Complexity</title><para>Linear (in the size of <computeroutput>cb</computeroutput>). </para>
</formalpara>
<para><emphasis role="bold">See Also:</emphasis><para><computeroutput>assign(size_type, const_reference)</computeroutput>, <computeroutput>assign(capacity_type, size_type, const_reference)</computeroutput>, <computeroutput>assign(InputIterator, InputIterator)</computeroutput>, <computeroutput>assign(capacity_type, InputIterator, InputIterator)</computeroutput> </para>
</para>
</para></description><postconditions><para><computeroutput>*this == cb</computeroutput><sbr/>
<sbr/>
 The amount of allocated memory in the internal buffer is <computeroutput>cb.size()</computeroutput>. </para>
</postconditions><throws><simpara><classname>An allocation error</classname> if memory is exhausted (<computeroutput>std::bad_alloc</computeroutput> if the standard allocator is used). <computeroutput>T::T(const T&amp;)</computeroutput> throws. </simpara></throws></copy-assignment>
<copy-assignment cv="noexcept"><type><classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</type><parameter name="cb"><paramtype><classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;&amp;</paramtype><description><para><computeroutput><classname alt="boost::circular_buffer">circular_buffer</classname></computeroutput> to 'steal' value from. </para></description></parameter><purpose>Move assigns content of <computeroutput>cb</computeroutput> to <computeroutput>*this</computeroutput>, leaving <computeroutput>cb</computeroutput> empty. </purpose><description><para>



<formalpara><title>Complexity</title><para>Constant. </para>
</formalpara>
</para></description><requires><para>C++ compiler with rvalue references support. </para>
</requires><postconditions><para><computeroutput>cb.empty()</computeroutput> </para>
</postconditions><throws><simpara><classname>Nothing.</classname> </simpara></throws></copy-assignment>
<method-group name="private static functions">
</method-group>
</class>






<function name="operator=="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><purpose>Test two space optimized circular buffers for equality. </purpose></function>
<function name="operator&lt;"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><purpose>Lexicographical comparison. </purpose></function>
<function name="operator!="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><purpose>Test two space optimized circular buffers for non-equality. </purpose></function>
<function name="operator&gt;"><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><purpose>Lexicographical comparison. </purpose></function>
<function name="operator&lt;="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><purpose>Lexicographical comparison. </purpose></function>
<function name="operator&gt;="><type>bool</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const <classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><purpose>Lexicographical comparison. </purpose></function>
<function name="swap"><type>void</type><template>
          <template-type-parameter name="T"/>
          <template-type-parameter name="Alloc"/>
        </template><parameter name="lhs"><paramtype><classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><parameter name="rhs"><paramtype><classname>circular_buffer_space_optimized</classname>&lt; T, Alloc &gt; &amp;</paramtype></parameter><purpose>Swap the contents of two space optimized circular buffers. </purpose></function>
</namespace>
</header>
</library-reference>