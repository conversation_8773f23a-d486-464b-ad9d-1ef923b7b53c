<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_parser::write_some (1 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Chapter 1. Boost.JSON">
<link rel="up" href="../write_some.html" title="basic_parser::write_some">
<link rel="prev" href="../write_some.html" title="basic_parser::write_some">
<link rel="next" href="overload2.html" title="basic_parser::write_some (2 of 2 overloads)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../write_some.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../write_some.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="json.ref.boost__json__basic_parser.write_some.overload1"></a><a class="link" href="overload1.html" title="basic_parser::write_some (1 of 2 overloads)">basic_parser::write_some
          (1 of 2 overloads)</a>
</h6></div></div></div>
<p>
            Parse some of an input string as JSON, incrementally.
          </p>
<h6>
<a name="json.ref.boost__json__basic_parser.write_some.overload1.h0"></a>
            <span class="phrase"><a name="json.ref.boost__json__basic_parser.write_some.overload1.synopsis"></a></span><a class="link" href="overload1.html#json.ref.boost__json__basic_parser.write_some.overload1.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span>
<span class="identifier">write_some</span><span class="special">(</span>
    <span class="keyword">bool</span> <span class="identifier">more</span><span class="special">,</span>
    <span class="keyword">char</span> <span class="keyword">const</span><span class="special">*</span> <span class="identifier">data</span><span class="special">,</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <span class="identifier">size</span><span class="special">,</span>
    <span class="identifier">error_code</span><span class="special">&amp;</span> <span class="identifier">ec</span><span class="special">);</span>
</pre>
<h6>
<a name="json.ref.boost__json__basic_parser.write_some.overload1.h1"></a>
            <span class="phrase"><a name="json.ref.boost__json__basic_parser.write_some.overload1.description"></a></span><a class="link" href="overload1.html#json.ref.boost__json__basic_parser.write_some.overload1.description">Description</a>
          </h6>
<p>
            This function parses the JSON in the specified buffer, calling the handler
            to emit each SAX parsing event. The parse proceeds from the current state,
            which is at the beginning of a new JSON or in the middle of the current
            JSON if any characters were already parsed.
          </p>
<p>
            The characters in the buffer are processed starting from the beginning,
            until one of the following conditions is met:
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                All of the characters in the buffer have been parsed, or
              </li>
<li class="listitem">
                Some of the characters in the buffer have been parsed and the JSON
                is complete, or
              </li>
<li class="listitem">
                A parsing error occurs.
              </li>
</ul></div>
<p>
            The supplied buffer does not need to contain the entire JSON. Subsequent
            calls can provide more serialized data, allowing JSON to be processed
            incrementally. The end of the serialized JSON can be indicated by passing
            <code class="computeroutput"><span class="identifier">more</span> <span class="special">=</span>
            <span class="keyword">false</span></code>.
          </p>
<h6>
<a name="json.ref.boost__json__basic_parser.write_some.overload1.h2"></a>
            <span class="phrase"><a name="json.ref.boost__json__basic_parser.write_some.overload1.complexity"></a></span><a class="link" href="overload1.html#json.ref.boost__json__basic_parser.write_some.overload1.complexity">Complexity</a>
          </h6>
<p>
            Linear in <code class="computeroutput"><span class="identifier">size</span></code>.
          </p>
<h6>
<a name="json.ref.boost__json__basic_parser.write_some.overload1.h3"></a>
            <span class="phrase"><a name="json.ref.boost__json__basic_parser.write_some.overload1.exception_safety"></a></span><a class="link" href="overload1.html#json.ref.boost__json__basic_parser.write_some.overload1.exception_safety">Exception
            Safety</a>
          </h6>
<p>
            Basic guarantee. Calls to the handler may throw. Upon error or exception,
            subsequent calls will fail until <a class="link" href="../reset.html" title="basic_parser::reset"><code class="computeroutput"><span class="identifier">reset</span></code></a> is called to parse a new
            JSON.
          </p>
<h6>
<a name="json.ref.boost__json__basic_parser.write_some.overload1.h4"></a>
            <span class="phrase"><a name="json.ref.boost__json__basic_parser.write_some.overload1.return_value"></a></span><a class="link" href="overload1.html#json.ref.boost__json__basic_parser.write_some.overload1.return_value">Return
            Value</a>
          </h6>
<p>
            The number of characters successfully parsed, which may be smaller than
            <code class="computeroutput"><span class="identifier">size</span></code>.
          </p>
<h6>
<a name="json.ref.boost__json__basic_parser.write_some.overload1.h5"></a>
            <span class="phrase"><a name="json.ref.boost__json__basic_parser.write_some.overload1.parameters"></a></span><a class="link" href="overload1.html#json.ref.boost__json__basic_parser.write_some.overload1.parameters">Parameters</a>
          </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                    <p>
                      Name
                    </p>
                  </th>
<th>
                    <p>
                      Description
                    </p>
                  </th>
</tr></thead>
<tbody>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">more</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      <code class="computeroutput"><span class="keyword">true</span></code> if there
                      are possibly more buffers in the current JSON, otherwise <code class="computeroutput"><span class="keyword">false</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">data</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      A pointer to a buffer of <code class="computeroutput"><span class="identifier">size</span></code>
                      characters to parse.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">size</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      The number of characters pointed to by <code class="computeroutput"><span class="identifier">data</span></code>.
                    </p>
                  </td>
</tr>
<tr>
<td>
                    <p>
                      <code class="computeroutput"><span class="identifier">ec</span></code>
                    </p>
                  </td>
<td>
                    <p>
                      Set to the error, if any occurred.
                    </p>
                  </td>
</tr>
</tbody>
</table></div>
</div>
<div class="copyright-footer">Copyright © 2019, 2020 Vinnie Falco<br>Copyright © 2020 Krystian Stasiowski<br>Copyright © 2022 Dmitry Arkhipov<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../write_some.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../write_some.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
