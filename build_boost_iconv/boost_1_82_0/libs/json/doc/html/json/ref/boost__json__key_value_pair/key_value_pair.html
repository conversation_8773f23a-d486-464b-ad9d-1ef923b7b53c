<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>key_value_pair::key_value_pair</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.JSON">
<link rel="up" href="../boost__json__key_value_pair.html" title="key_value_pair">
<link rel="prev" href="_dtor_key_value_pair.html" title="key_value_pair::~key_value_pair">
<link rel="next" href="key_value_pair/overload1.html" title="key_value_pair::key_value_pair (1 of 7 overloads)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="_dtor_key_value_pair.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__json__key_value_pair.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="key_value_pair/overload1.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="json.ref.boost__json__key_value_pair.key_value_pair"></a><a class="link" href="key_value_pair.html" title="key_value_pair::key_value_pair">key_value_pair::key_value_pair</a>
</h5></div></div></div>
<p>
          <a class="indexterm" name="idm22020"></a>
        </p>
<p>
          Copy constructor.
        </p>
<pre class="programlisting"><a class="link" href="key_value_pair/overload1.html" title="key_value_pair::key_value_pair (1 of 7 overloads)">key_value_pair</a><span class="special">(</span>
    <span class="identifier">key_value_pair</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">);</span>
  <span class="emphasis"><em>» <a class="link" href="key_value_pair/overload1.html" title="key_value_pair::key_value_pair (1 of 7 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>

<a class="link" href="key_value_pair/overload2.html" title="key_value_pair::key_value_pair (2 of 7 overloads)">key_value_pair</a><span class="special">(</span>
    <span class="identifier">key_value_pair</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">,</span>
    <span class="identifier">storage_ptr</span> <span class="identifier">sp</span><span class="special">);</span>
  <span class="emphasis"><em>» <a class="link" href="key_value_pair/overload2.html" title="key_value_pair::key_value_pair (2 of 7 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>
</pre>
<p>
          Move constructor.
        </p>
<pre class="programlisting"><a class="link" href="key_value_pair/overload3.html" title="key_value_pair::key_value_pair (3 of 7 overloads)">key_value_pair</a><span class="special">(</span>
    <span class="identifier">key_value_pair</span><span class="special">&amp;&amp;</span> <span class="identifier">other</span><span class="special">);</span>
  <span class="emphasis"><em>» <a class="link" href="key_value_pair/overload3.html" title="key_value_pair::key_value_pair (3 of 7 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>
</pre>
<p>
          Pilfer constructor.
        </p>
<pre class="programlisting"><a class="link" href="key_value_pair/overload4.html" title="key_value_pair::key_value_pair (4 of 7 overloads)">key_value_pair</a><span class="special">(</span>
    <span class="identifier">pilfered</span><span class="special">&lt;</span> <span class="identifier">key_value_pair</span> <span class="special">&gt;</span> <span class="identifier">other</span><span class="special">);</span>
  <span class="emphasis"><em>» <a class="link" href="key_value_pair/overload4.html" title="key_value_pair::key_value_pair (4 of 7 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>
</pre>
<p>
          Constructor.
        </p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">class</span><span class="special">...</span> <span class="identifier">Args</span><span class="special">&gt;</span>
<span class="keyword">explicit</span>
<a class="link" href="key_value_pair/overload5.html" title="key_value_pair::key_value_pair (5 of 7 overloads)">key_value_pair</a><span class="special">(</span>
    <span class="identifier">string_view</span> <span class="identifier">key</span><span class="special">,</span>
    <span class="identifier">Args</span><span class="special">&amp;&amp;...</span> <span class="identifier">args</span><span class="special">);</span>
  <span class="emphasis"><em>» <a class="link" href="key_value_pair/overload5.html" title="key_value_pair::key_value_pair (5 of 7 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>

<span class="keyword">explicit</span>
<a class="link" href="key_value_pair/overload6.html" title="key_value_pair::key_value_pair (6 of 7 overloads)">key_value_pair</a><span class="special">(</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">string_view</span><span class="special">,</span> <span class="identifier">json</span><span class="special">::</span><span class="identifier">value</span> <span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">p</span><span class="special">,</span>
    <span class="identifier">storage_ptr</span> <span class="identifier">sp</span> <span class="special">=</span> <span class="special">{});</span>
  <span class="emphasis"><em>» <a class="link" href="key_value_pair/overload6.html" title="key_value_pair::key_value_pair (6 of 7 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>

<span class="keyword">explicit</span>
<a class="link" href="key_value_pair/overload7.html" title="key_value_pair::key_value_pair (7 of 7 overloads)">key_value_pair</a><span class="special">(</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">string_view</span><span class="special">,</span> <span class="identifier">json</span><span class="special">::</span><span class="identifier">value</span> <span class="special">&gt;&amp;&amp;</span> <span class="identifier">p</span><span class="special">,</span>
    <span class="identifier">storage_ptr</span> <span class="identifier">sp</span> <span class="special">=</span> <span class="special">{});</span>
  <span class="emphasis"><em>» <a class="link" href="key_value_pair/overload7.html" title="key_value_pair::key_value_pair (7 of 7 overloads)"><code class="computeroutput"><span class="identifier">more</span><span class="special">...</span></code></a></em></span>
</pre>
</div>
<div class="copyright-footer">Copyright © 2019, 2020 Vinnie Falco<br>Copyright © 2020 Krystian Stasiowski<br>Copyright © 2022 Dmitry Arkhipov<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="_dtor_key_value_pair.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__json__key_value_pair.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="key_value_pair/overload1.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
