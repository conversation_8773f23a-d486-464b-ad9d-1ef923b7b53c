<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>monotonic_resource::release</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.JSON">
<link rel="up" href="../boost__json__monotonic_resource.html" title="monotonic_resource">
<link rel="prev" href="_dtor_monotonic_resource.html" title="monotonic_resource::~monotonic_resource">
<link rel="next" href="../boost__json__object.html" title="object">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="_dtor_monotonic_resource.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__json__monotonic_resource.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost__json__object.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="json.ref.boost__json__monotonic_resource.release"></a><a class="link" href="release.html" title="monotonic_resource::release">monotonic_resource::release</a>
</h5></div></div></div>
<p>
          <a class="indexterm" name="idm23387"></a>
        </p>
<p>
          Release all allocated memory.
        </p>
<h6>
<a name="json.ref.boost__json__monotonic_resource.release.h0"></a>
          <span class="phrase"><a name="json.ref.boost__json__monotonic_resource.release.synopsis"></a></span><a class="link" href="release.html#json.ref.boost__json__monotonic_resource.release.synopsis">Synopsis</a>
        </h6>
<pre class="programlisting"><span class="keyword">void</span>
<span class="identifier">release</span><span class="special">();</span>
</pre>
<h6>
<a name="json.ref.boost__json__monotonic_resource.release.h1"></a>
          <span class="phrase"><a name="json.ref.boost__json__monotonic_resource.release.description"></a></span><a class="link" href="release.html#json.ref.boost__json__monotonic_resource.release.description">Description</a>
        </h6>
<p>
          This function deallocates all allocated memory. If an initial buffer was
          provided upon construction, then all of the bytes will be available again
          for allocation. Allocated memory is deallocated even if deallocate has
          not been called for some of the allocated blocks.
        </p>
<h6>
<a name="json.ref.boost__json__monotonic_resource.release.h2"></a>
          <span class="phrase"><a name="json.ref.boost__json__monotonic_resource.release.complexity"></a></span><a class="link" href="release.html#json.ref.boost__json__monotonic_resource.release.complexity">Complexity</a>
        </h6>
<p>
          Linear in the number of deallocations performed.
        </p>
<h6>
<a name="json.ref.boost__json__monotonic_resource.release.h3"></a>
          <span class="phrase"><a name="json.ref.boost__json__monotonic_resource.release.exception_safety"></a></span><a class="link" href="release.html#json.ref.boost__json__monotonic_resource.release.exception_safety">Exception
          Safety</a>
        </h6>
<p>
          No-throw guarantee.
        </p>
</div>
<div class="copyright-footer">Copyright © 2019, 2020 Vinnie Falco<br>Copyright © 2020 Krystian Stasiowski<br>Copyright © 2022 Dmitry Arkhipov<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="_dtor_monotonic_resource.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__json__monotonic_resource.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost__json__object.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
