<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses-allocator construction</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.JSON">
<link rel="up" href="../allocators.html" title="Allocators">
<link rel="prev" href="storage_ptr.html" title="storage_ptr">
<link rel="next" href="../input_output.html" title="Input/Output">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="storage_ptr.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../allocators.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../input_output.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="json.allocators.uses_allocator"></a><a class="link" href="uses_allocator.html" title="Uses-allocator construction">Uses-allocator construction</a>
</h3></div></div></div>
<p>
        To support code bases which are already using polymorphic allocators, the
        containers in this library support <a href="https://en.cppreference.com/w/cpp/memory/uses_allocator" target="_top"><code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">uses_allocator</span></code></a>
        construction. For <a class="link" href="../ref/boost__json__array.html" title="array"><code class="computeroutput"><span class="identifier">array</span></code></a>, <a class="link" href="../ref/boost__json__object.html" title="object"><code class="computeroutput"><span class="identifier">object</span></code></a>, <a class="link" href="../ref/boost__json__string.html" title="string"><code class="computeroutput"><span class="identifier">string</span></code></a>, and <a class="link" href="../ref/boost__json__value.html" title="value"><code class="computeroutput"><span class="identifier">value</span></code></a>:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            The nested type <code class="computeroutput"><span class="identifier">allocator_type</span></code>
            is an alias for a <a class="link" href="../ref/boost__json__polymorphic_allocator.html" title="polymorphic_allocator"><code class="computeroutput"><span class="identifier">polymorphic_allocator</span></code></a>
          </li>
<li class="listitem">
            All eligible constructors which accept <a class="link" href="../ref/boost__json__storage_ptr.html" title="storage_ptr"><code class="computeroutput"><span class="identifier">storage_ptr</span></code></a> will also accept
            an instance of <a class="link" href="../ref/boost__json__polymorphic_allocator.html" title="polymorphic_allocator"><code class="computeroutput"><span class="identifier">polymorphic_allocator</span></code></a> in the
            same argument position.
          </li>
<li class="listitem">
            The member function <code class="computeroutput"><span class="identifier">get_allocator</span></code>
            returns an instance of <a class="link" href="../ref/boost__json__polymorphic_allocator.html" title="polymorphic_allocator"><code class="computeroutput"><span class="identifier">polymorphic_allocator</span></code></a> constructed
            from the <a class="link" href="../ref/boost__json__memory_resource.html" title="memory_resource"><code class="computeroutput"><span class="identifier">memory_resource</span></code></a> used by the
            container. Ownership of this memory resource is not transferred.
          </li>
</ul></div>
<p>
        Practically, this means that when a library container type is used in a standard
        container that uses a polymorphic allocator, the allocator will propagate
        to the JSON type. For example:
      </p>
<pre class="programlisting"><span class="comment">// We want to use this resource for all the containers</span>
<span class="identifier">monotonic_resource</span> <span class="identifier">mr</span><span class="special">;</span>

<span class="comment">// Declare a vector of JSON values</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">value</span><span class="special">,</span> <span class="identifier">polymorphic_allocator</span><span class="special">&lt;</span> <span class="identifier">value</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">v</span><span class="special">(</span> <span class="special">&amp;</span><span class="identifier">mr</span> <span class="special">);</span>

<span class="comment">// The polymorphic allocator will use our resource</span>
<span class="identifier">assert</span><span class="special">(</span> <span class="identifier">v</span><span class="special">.</span><span class="identifier">get_allocator</span><span class="special">().</span><span class="identifier">resource</span><span class="special">()</span> <span class="special">==</span> <span class="special">&amp;</span><span class="identifier">mr</span> <span class="special">);</span>

<span class="comment">// Add a string to the vector</span>
<span class="identifier">v</span><span class="special">.</span><span class="identifier">emplace_back</span><span class="special">(</span> <span class="string">"boost"</span> <span class="special">);</span>

<span class="comment">// The vector propagates the memory resource to the string</span>
<span class="identifier">assert</span><span class="special">(</span> <span class="identifier">v</span><span class="special">[</span><span class="number">0</span><span class="special">].</span><span class="identifier">storage</span><span class="special">().</span><span class="identifier">get</span><span class="special">()</span> <span class="special">==</span> <span class="special">&amp;</span><span class="identifier">mr</span> <span class="special">);</span>
</pre>
<p>
        Library containers can be constructed from polymorphic allocators:
      </p>
<pre class="programlisting"><span class="comment">// This vector will use the default memory resource</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">value</span><span class="special">,</span> <span class="identifier">polymorphic_allocator</span> <span class="special">&lt;</span> <span class="identifier">value</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">v</span><span class="special">;</span>

<span class="comment">// This value will same memory resource as the vector</span>
<span class="identifier">value</span> <span class="identifier">jv</span><span class="special">(</span> <span class="identifier">v</span><span class="special">.</span><span class="identifier">get_allocator</span><span class="special">()</span> <span class="special">);</span>

<span class="comment">// However, ownership is not transferred,</span>
<span class="identifier">assert</span><span class="special">(</span> <span class="special">!</span> <span class="identifier">jv</span><span class="special">.</span><span class="identifier">storage</span><span class="special">().</span><span class="identifier">is_shared</span><span class="special">()</span> <span class="special">);</span>

<span class="comment">// and deallocate is never null</span>
<span class="identifier">assert</span><span class="special">(</span> <span class="special">!</span> <span class="identifier">jv</span><span class="special">.</span><span class="identifier">storage</span><span class="special">().</span><span class="identifier">is_deallocate_trivial</span><span class="special">()</span> <span class="special">);</span>
</pre>
<p>
        The polymorphic allocator is propagated recursively. Child elements of child
        elements will use the same memory resource as the parent.
      </p>
</div>
<div class="copyright-footer">Copyright © 2019, 2020 Vinnie Falco<br>Copyright © 2020 Krystian Stasiowski<br>Copyright © 2022 Dmitry Arkhipov<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="storage_ptr.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../allocators.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../input_output.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
