<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.15"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Boost.Locale: boost::locale Namespace Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(initResizable);
/* @license-end */</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="section-basic.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="boost-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Boost.Locale
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(document).ready(function(){initNavTree('namespaceboost_1_1locale.html','');});
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#namespaces">Namespaces</a> &#124;
<a href="#nested-classes">Classes</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#enum-members">Enumerations</a> &#124;
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">boost::locale Namespace Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the main namespace that encloses all localization classes.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespaceboost_1_1locale_1_1as"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1as.html">as</a></td></tr>
<tr class="memdesc:namespaceboost_1_1locale_1_1as"><td class="mdescLeft">&#160;</td><td class="mdescRight">This namespace includes all manipulators that can be used on IO streams. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespaceboost_1_1locale_1_1boundary"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1boundary.html">boundary</a></td></tr>
<tr class="memdesc:namespaceboost_1_1locale_1_1boundary"><td class="mdescLeft">&#160;</td><td class="mdescRight">This namespace contains all operations required for boundary analysis of text. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespaceboost_1_1locale_1_1conv"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1conv.html">conv</a></td></tr>
<tr class="memdesc:namespaceboost_1_1locale_1_1conv"><td class="mdescLeft">&#160;</td><td class="mdescRight">Namespace that contains all functions related to character set conversion. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespaceboost_1_1locale_1_1flags"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1flags.html">flags</a></td></tr>
<tr class="memdesc:namespaceboost_1_1locale_1_1flags"><td class="mdescLeft">&#160;</td><td class="mdescRight">This namespace holds additional formatting flags that can be set using <a class="el" href="classboost_1_1locale_1_1ios__info.html" title="This class holds external data beyond existing fmtflags that std::ios_base holds.">ios_info</a>. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespaceboost_1_1locale_1_1gnu__gettext"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1gnu__gettext.html">gnu_gettext</a></td></tr>
<tr class="memdesc:namespaceboost_1_1locale_1_1gnu__gettext"><td class="mdescLeft">&#160;</td><td class="mdescRight">This namespace holds classes that provide GNU Gettext message catalogs support. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespaceboost_1_1locale_1_1period"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1period.html">period</a></td></tr>
<tr class="memdesc:namespaceboost_1_1locale_1_1period"><td class="mdescLeft">&#160;</td><td class="mdescRight">Namespace that contains various types for manipulation with dates. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespaceboost_1_1locale_1_1time__zone"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1time__zone.html">time_zone</a></td></tr>
<tr class="memdesc:namespaceboost_1_1locale_1_1time__zone"><td class="mdescLeft">&#160;</td><td class="mdescRight">namespace that holds functions for operating with global time zone <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespaceboost_1_1locale_1_1utf"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1utf.html">utf</a></td></tr>
<tr class="memdesc:namespaceboost_1_1locale_1_1utf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Namespace that holds basic operations on UTF encoded sequences. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespaceboost_1_1locale_1_1util"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale_1_1util.html">util</a></td></tr>
<tr class="memdesc:namespaceboost_1_1locale_1_1util"><td class="mdescLeft">&#160;</td><td class="mdescRight">This namespace provides various utility function useful for Boost.Locale's backends implementations. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1abstract__calendar.html">abstract_calendar</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1basic__format.html">basic_format</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">a printf like class that allows type-safe and locale aware message formatting  <a href="classboost_1_1locale_1_1basic__format.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">This class represents a message that can be converted to a specific locale message.  <a href="classboost_1_1locale_1_1basic__message.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1calendar.html">calendar</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">this class provides an access to general calendar information.  <a href="classboost_1_1locale_1_1calendar.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1calendar__facet.html">calendar_facet</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">the facet that generates calendar for specific locale  <a href="classboost_1_1locale_1_1calendar__facet.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1collator.html">collator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Collation facet.  <a href="classboost_1_1locale_1_1collator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1locale_1_1comparator.html">comparator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">This class can be used in STL algorithms and containers for comparison of strings with a level other than primary.  <a href="structboost_1_1locale_1_1comparator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1converter.html">converter</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">The facet that implements text manipulation.  <a href="classboost_1_1locale_1_1converter.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1converter__base.html">converter_base</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">This class provides base flags for text manipulation. It is used as base for converter facet.  <a href="classboost_1_1locale_1_1converter__base.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">this class represents a date time and allows to perform various operation according to the locale settings.  <a href="classboost_1_1locale_1_1date__time.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">This class represents a period: a pair of two <a class="el" href="classboost_1_1locale_1_1date__time.html" title="this class represents a date time and allows to perform various operation according to the locale set...">date_time</a> objects.  <a href="classboost_1_1locale_1_1date__time__duration.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1date__time__error.html">date_time_error</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">This error is thrown in case of invalid state that occurred.  <a href="classboost_1_1locale_1_1date__time__error.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1locale_1_1date__time__period.html">date_time_period</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">This class represents a pair of period_type and the integer values that describes its amount. For example 3 days or 4 years.  <a href="structboost_1_1locale_1_1date__time__period.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">this class that represents a set of periods,  <a href="classboost_1_1locale_1_1date__time__period__set.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1generator.html">generator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">the major class used for locale generation  <a href="classboost_1_1locale_1_1generator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1generic__codecvt.html">generic_codecvt</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generic codecvt facet for various stateless encodings to UTF-16 and UTF-32 using wchar_t, char32_t and char16_t.  <a href="classboost_1_1locale_1_1generic__codecvt.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1generic__codecvt_3_01CharType_00_01CodecvtImpl_00_011_01_4.html">generic_codecvt&lt; CharType, CodecvtImpl, 1 &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1generic__codecvt_3_01CharType_00_01CodecvtImpl_00_012_01_4.html">generic_codecvt&lt; CharType, CodecvtImpl, 2 &gt;</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">UTF-16 to/from UTF-8 codecvt facet to use with char16_t or wchar_t on Windows.  <a href="classboost_1_1locale_1_1generic__codecvt_3_01CharType_00_01CodecvtImpl_00_012_01_4.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1generic__codecvt_3_01CharType_00_01CodecvtImpl_00_014_01_4.html">generic_codecvt&lt; CharType, CodecvtImpl, 4 &gt;</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">UTF-32 to/from UTF-8 codecvt facet to use with char32_t or wchar_t on POSIX platforms.  <a href="classboost_1_1locale_1_1generic__codecvt_3_01CharType_00_01CodecvtImpl_00_014_01_4.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1generic__codecvt__base.html">generic_codecvt_base</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">A base class that used to define constants for <a class="el" href="classboost_1_1locale_1_1generic__codecvt.html" title="Generic codecvt facet for various stateless encodings to UTF-16 and UTF-32 using wchar_t,...">generic_codecvt</a>.  <a href="classboost_1_1locale_1_1generic__codecvt__base.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1hold__ptr.html">hold_ptr</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">a smart pointer similar to std::unique_ptr but the underlying object has the same constness as the pointer itself (unlike an ordinary pointer).  <a href="classboost_1_1locale_1_1hold__ptr.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1info.html">info</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">a facet that holds general information about locale  <a href="classboost_1_1locale_1_1info.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1ios__info.html">ios_info</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">This class holds external data beyond existing fmtflags that std::ios_base holds.  <a href="classboost_1_1locale_1_1ios__info.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1localization__backend.html">localization_backend</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">this class represents a localization backend that can be used for localizing your application.  <a href="classboost_1_1locale_1_1localization__backend.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1localization__backend__manager.html">localization_backend_manager</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Localization backend manager is a class that holds various backend and allows creation of their combination or selection.  <a href="classboost_1_1locale_1_1localization__backend__manager.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1message__format.html">message_format</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">This facet provides message formatting abilities.  <a href="classboost_1_1locale_1_1message__format.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1locale_1_1posix__time.html">posix_time</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1locale_1_1utf8__codecvt.html">utf8_codecvt</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Geneneric utf8 codecvt facet, it allows to convert UTF-8 strings to UTF-16 and UTF-32 using wchar_t, char32_t and char16_t.  <a href="classboost_1_1locale_1_1utf8__codecvt.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:gad7914df7b54382c1ad7f5360676fe2e8"><td class="memItemLeft" align="right" valign="top">
typedef <a class="el" href="classboost_1_1locale_1_1basic__format.html">basic_format</a>&lt; char &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__format.html#gad7914df7b54382c1ad7f5360676fe2e8">format</a></td></tr>
<tr class="memdesc:gad7914df7b54382c1ad7f5360676fe2e8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Definition of char based format. <br /></td></tr>
<tr class="separator:gad7914df7b54382c1ad7f5360676fe2e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga610f3ae827801febc962019cf82a2227"><td class="memItemLeft" align="right" valign="top">
typedef <a class="el" href="classboost_1_1locale_1_1basic__format.html">basic_format</a>&lt; wchar_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__format.html#ga610f3ae827801febc962019cf82a2227">wformat</a></td></tr>
<tr class="memdesc:ga610f3ae827801febc962019cf82a2227"><td class="mdescLeft">&#160;</td><td class="mdescRight">Definition of wchar_t based format. <br /></td></tr>
<tr class="separator:ga610f3ae827801febc962019cf82a2227"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7e1b668f020290ebca6570b4c12a36e6"><td class="memItemLeft" align="right" valign="top">
typedef <a class="el" href="classboost_1_1locale_1_1basic__format.html">basic_format</a>&lt; char16_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__format.html#ga7e1b668f020290ebca6570b4c12a36e6">u16format</a></td></tr>
<tr class="memdesc:ga7e1b668f020290ebca6570b4c12a36e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Definition of char16_t based format. <br /></td></tr>
<tr class="separator:ga7e1b668f020290ebca6570b4c12a36e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70ce1d532e859739182439f1f3321032"><td class="memItemLeft" align="right" valign="top">
typedef <a class="el" href="classboost_1_1locale_1_1basic__format.html">basic_format</a>&lt; char32_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__format.html#ga70ce1d532e859739182439f1f3321032">u32format</a></td></tr>
<tr class="memdesc:ga70ce1d532e859739182439f1f3321032"><td class="mdescLeft">&#160;</td><td class="mdescRight">Definition of char32_t based format. <br /></td></tr>
<tr class="separator:ga70ce1d532e859739182439f1f3321032"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga556e3e7696302902b2242a7a94516dee"><td class="memItemLeft" align="right" valign="top">
typedef <a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a>&lt; char &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__message.html#ga556e3e7696302902b2242a7a94516dee">message</a></td></tr>
<tr class="memdesc:ga556e3e7696302902b2242a7a94516dee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convenience typedef for char. <br /></td></tr>
<tr class="separator:ga556e3e7696302902b2242a7a94516dee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafea131aa123d666905076f34b77326a9"><td class="memItemLeft" align="right" valign="top">
typedef <a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a>&lt; wchar_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__message.html#gafea131aa123d666905076f34b77326a9">wmessage</a></td></tr>
<tr class="memdesc:gafea131aa123d666905076f34b77326a9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convenience typedef for wchar_t. <br /></td></tr>
<tr class="separator:gafea131aa123d666905076f34b77326a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43ddf342eec2ab9145d04727f8161e1c"><td class="memItemLeft" align="right" valign="top">
typedef <a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a>&lt; char16_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__message.html#ga43ddf342eec2ab9145d04727f8161e1c">u16message</a></td></tr>
<tr class="memdesc:ga43ddf342eec2ab9145d04727f8161e1c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convenience typedef for char16_t. <br /></td></tr>
<tr class="separator:ga43ddf342eec2ab9145d04727f8161e1c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6c0b40121a07c13de9e712845b43468f"><td class="memItemLeft" align="right" valign="top">
typedef <a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a>&lt; char32_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__message.html#ga6c0b40121a07c13de9e712845b43468f">u32message</a></td></tr>
<tr class="memdesc:ga6c0b40121a07c13de9e712845b43468f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convenience typedef for char32_t. <br /></td></tr>
<tr class="separator:ga6c0b40121a07c13de9e712845b43468f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:gaa3a9b33c6acdb1809ff44c56e3ad0ad4"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__collation.html#gaa3a9b33c6acdb1809ff44c56e3ad0ad4">collate_level</a> { <br />
&#160;&#160;<a class="el" href="group__collation.html#ggaa3a9b33c6acdb1809ff44c56e3ad0ad4a386bba5a5dc4fac215c9cf0b9a29b352">collate_level::primary</a> = 0, 
<a class="el" href="group__collation.html#ggaa3a9b33c6acdb1809ff44c56e3ad0ad4a1f7ba58706f9d405023da32864d059c8">collate_level::secondary</a> = 1, 
<a class="el" href="group__collation.html#ggaa3a9b33c6acdb1809ff44c56e3ad0ad4ac16e378185ee71ca686e24abc62bf832">collate_level::tertiary</a> = 2, 
<a class="el" href="group__collation.html#ggaa3a9b33c6acdb1809ff44c56e3ad0ad4a5ebecc968c4fc856fde9eaf5b89f86ba">collate_level::quaternary</a> = 3, 
<br />
&#160;&#160;<a class="el" href="group__collation.html#ggaa3a9b33c6acdb1809ff44c56e3ad0ad4aee0cbdbacdada19376449799774976e8">collate_level::identical</a> = 4
<br />
 }</td></tr>
<tr class="memdesc:gaa3a9b33c6acdb1809ff44c56e3ad0ad4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unicode collation level types.  <a href="group__collation.html#gaa3a9b33c6acdb1809ff44c56e3ad0ad4">More...</a><br /></td></tr>
<tr class="separator:gaa3a9b33c6acdb1809ff44c56e3ad0ad4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6a595a415b83b8a0c8f14c34eb66cc9f"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__convert.html#ga6a595a415b83b8a0c8f14c34eb66cc9f">norm_type</a> { <br />
&#160;&#160;<a class="el" href="group__convert.html#gga6a595a415b83b8a0c8f14c34eb66cc9fa6648d0eabb931f2e9d258570b297e98f">norm_nfd</a>, 
<a class="el" href="group__convert.html#gga6a595a415b83b8a0c8f14c34eb66cc9faf6fe7be275e5e13df415ab258105ada0">norm_nfc</a>, 
<a class="el" href="group__convert.html#gga6a595a415b83b8a0c8f14c34eb66cc9fa0fbc2ac042fc6f58af5818bfd06d5379">norm_nfkd</a>, 
<a class="el" href="group__convert.html#gga6a595a415b83b8a0c8f14c34eb66cc9fa0305c1f3405ea70facf4c6a5ffa40583">norm_nfkc</a>, 
<br />
&#160;&#160;<a class="el" href="group__convert.html#gga6a595a415b83b8a0c8f14c34eb66cc9faa29173d73d9be7fefcbb18c8712465d2">norm_default</a> = norm_nfc
<br />
 }</td></tr>
<tr class="memdesc:ga6a595a415b83b8a0c8f14c34eb66cc9f"><td class="mdescLeft">&#160;</td><td class="mdescRight">The type that defined <a href="http://unicode.org/reports/tr15/#Norm_Forms">normalization form</a>  <a href="group__convert.html#ga6a595a415b83b8a0c8f14c34eb66cc9f">More...</a><br /></td></tr>
<tr class="separator:ga6a595a415b83b8a0c8f14c34eb66cc9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a662f2b6ccb8e8123e50f0704fec0cef6"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a> : uint32_t { <br />
&#160;&#160;<a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6a5ce701c04021c9da33b17a7b371903b8">char_facet_t::nochar</a> = 0, 
<a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6a747f241c2d27fc06b5d75a18ef146c52">char_facet_t::char_f</a> = 1 &lt;&lt; 0, 
<a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6a8860d46e0e82cfe1caee9af99a57b4db">char_facet_t::wchar_f</a> = 1 &lt;&lt; 1, 
<a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6a6d52c0a668d7157436f8d9f706c4e114">char_facet_t::char16_f</a> = 1 &lt;&lt; 2, 
<br />
&#160;&#160;<a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6a82d3b8653dc0bedc46b1d706d2eabdcc">char_facet_t::char32_f</a> = 1 &lt;&lt; 3
<br />
 }</td></tr>
<tr class="separator:a662f2b6ccb8e8123e50f0704fec0cef6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aacab728d9d00542295bbd3c95a142629"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a> : uint32_t { <br />
&#160;&#160;<a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629a31168275dcaac634489082b54c4c66d0">category_t::convert</a> = 1 &lt;&lt; 0, 
<a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629abf0e0acec49363acebe287d869fbf449">category_t::collation</a> = 1 &lt;&lt; 1, 
<a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629af9f9c04813a46502873a0c2a3d361ce8">category_t::formatting</a> = 1 &lt;&lt; 2, 
<a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629adbc77665f51d780a776978e34f065af5">category_t::parsing</a> = 1 &lt;&lt; 3, 
<br />
&#160;&#160;<a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629a78e731027d8fd50ed642340b7c9a63b3">category_t::message</a> = 1 &lt;&lt; 4, 
<a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629a1c360a64cd5ceae6444b2dc0642e53ea">category_t::codepage</a> = 1 &lt;&lt; 5, 
<a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629a81fd830c85363675edb98d2879916d8c">category_t::boundary</a> = 1 &lt;&lt; 6, 
<a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629aa0e7b2a565119c0a7ec3126a16016113">category_t::calendar</a> = 1 &lt;&lt; 16, 
<br />
&#160;&#160;<a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629abb3ccd5881d651448ded1dac904054ac">category_t::information</a> = 1 &lt;&lt; 17
<br />
 }</td></tr>
<tr class="separator:aacab728d9d00542295bbd3c95a142629"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga24d6e31bf078fbbfac5084e9c84bbf23"><td class="memItemLeft" align="right" valign="top">
class&#160;</td><td class="memItemRight" valign="bottom"><b>BOOST_DEPRECATED</b> (&quot;Use <a class="el" href="group__collation.html#gaa3a9b33c6acdb1809ff44c56e3ad0ad4">collate_level</a>&quot;) collator_base</td></tr>
<tr class="separator:ga24d6e31bf078fbbfac5084e9c84bbf23"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf840d2b520af41863c32e3ea3545edfe"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:gaf840d2b520af41863c32e3ea3545edfe"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#gaf840d2b520af41863c32e3ea3545edfe">normalize</a> (const CharType *begin, const CharType *end, <a class="el" href="group__convert.html#ga6a595a415b83b8a0c8f14c34eb66cc9f">norm_type</a> n=<a class="el" href="group__convert.html#gga6a595a415b83b8a0c8f14c34eb66cc9faa29173d73d9be7fefcbb18c8712465d2">norm_default</a>, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:gaf840d2b520af41863c32e3ea3545edfe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabd219d9a694e6aeaf754edb023777733"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:gabd219d9a694e6aeaf754edb023777733"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#gabd219d9a694e6aeaf754edb023777733">normalize</a> (const std::basic_string&lt; CharType &gt; &amp;str, <a class="el" href="group__convert.html#ga6a595a415b83b8a0c8f14c34eb66cc9f">norm_type</a> n=<a class="el" href="group__convert.html#gga6a595a415b83b8a0c8f14c34eb66cc9faa29173d73d9be7fefcbb18c8712465d2">norm_default</a>, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:gabd219d9a694e6aeaf754edb023777733"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9712718cc802f4efbf05a84927a9fa7b"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga9712718cc802f4efbf05a84927a9fa7b"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#ga9712718cc802f4efbf05a84927a9fa7b">normalize</a> (const CharType *str, <a class="el" href="group__convert.html#ga6a595a415b83b8a0c8f14c34eb66cc9f">norm_type</a> n=<a class="el" href="group__convert.html#gga6a595a415b83b8a0c8f14c34eb66cc9faa29173d73d9be7fefcbb18c8712465d2">norm_default</a>, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:ga9712718cc802f4efbf05a84927a9fa7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaab5a4b5be764f5c7c6c307185d42abf4"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:gaab5a4b5be764f5c7c6c307185d42abf4"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#gaab5a4b5be764f5c7c6c307185d42abf4">to_upper</a> (const CharType *begin, const CharType *end, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:gaab5a4b5be764f5c7c6c307185d42abf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab2aba4b376a863f1a5dbb53ec94fc7e7"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:gab2aba4b376a863f1a5dbb53ec94fc7e7"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#gab2aba4b376a863f1a5dbb53ec94fc7e7">to_upper</a> (const std::basic_string&lt; CharType &gt; &amp;str, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:gab2aba4b376a863f1a5dbb53ec94fc7e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0f2bdafc801c407b2aa529c88dc1517b"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga0f2bdafc801c407b2aa529c88dc1517b"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#ga0f2bdafc801c407b2aa529c88dc1517b">to_upper</a> (const CharType *str, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:ga0f2bdafc801c407b2aa529c88dc1517b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1098041e20cb235ce92754830448638d"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga1098041e20cb235ce92754830448638d"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#ga1098041e20cb235ce92754830448638d">to_lower</a> (const CharType *begin, const CharType *end, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:ga1098041e20cb235ce92754830448638d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga544d7224b68fde156f7c0606fb98317b"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga544d7224b68fde156f7c0606fb98317b"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#ga544d7224b68fde156f7c0606fb98317b">to_lower</a> (const std::basic_string&lt; CharType &gt; &amp;str, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:ga544d7224b68fde156f7c0606fb98317b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga297279f4284723e4ddcd2d88d85d1786"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga297279f4284723e4ddcd2d88d85d1786"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#ga297279f4284723e4ddcd2d88d85d1786">to_lower</a> (const CharType *str, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:ga297279f4284723e4ddcd2d88d85d1786"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0cf65ff4ea4bf502c667823c0137a225"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga0cf65ff4ea4bf502c667823c0137a225"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#ga0cf65ff4ea4bf502c667823c0137a225">to_title</a> (const CharType *begin, const CharType *end, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:ga0cf65ff4ea4bf502c667823c0137a225"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga11cb245ab1692248b2b6b3c241b05682"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga11cb245ab1692248b2b6b3c241b05682"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#ga11cb245ab1692248b2b6b3c241b05682">to_title</a> (const std::basic_string&lt; CharType &gt; &amp;str, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:ga11cb245ab1692248b2b6b3c241b05682"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga033207a459e2aff84f8830836654f848"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga033207a459e2aff84f8830836654f848"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#ga033207a459e2aff84f8830836654f848">to_title</a> (const CharType *str, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:ga033207a459e2aff84f8830836654f848"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4008011465158c4c0baf85b74388af09"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga4008011465158c4c0baf85b74388af09"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#ga4008011465158c4c0baf85b74388af09">fold_case</a> (const CharType *begin, const CharType *end, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:ga4008011465158c4c0baf85b74388af09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga99023f92147da6106acc5da57d8c0409"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga99023f92147da6106acc5da57d8c0409"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#ga99023f92147da6106acc5da57d8c0409">fold_case</a> (const std::basic_string&lt; CharType &gt; &amp;str, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:ga99023f92147da6106acc5da57d8c0409"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga984b88be5677e743ce3203af979b796b"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga984b88be5677e743ce3203af979b796b"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__convert.html#ga984b88be5677e743ce3203af979b796b">fold_case</a> (const CharType *str, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="separator:ga984b88be5677e743ce3203af979b796b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5fb5bf4b94dcd67f45564e8015b57c20"><td class="memItemLeft" align="right" valign="top">
<a class="el" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__date__time.html#ga5fb5bf4b94dcd67f45564e8015b57c20">operator+</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a> &amp;a, const <a class="el" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a> &amp;b)</td></tr>
<tr class="memdesc:ga5fb5bf4b94dcd67f45564e8015b57c20"><td class="mdescLeft">&#160;</td><td class="mdescRight">Append two periods sets. Note this operator is not commutative. <br /></td></tr>
<tr class="separator:ga5fb5bf4b94dcd67f45564e8015b57c20"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0079bd8236eee75c4b5baa8497959b78"><td class="memItemLeft" align="right" valign="top">
<a class="el" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__date__time.html#ga0079bd8236eee75c4b5baa8497959b78">operator-</a> (const <a class="el" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a> &amp;a, const <a class="el" href="classboost_1_1locale_1_1date__time__period__set.html">date_time_period_set</a> &amp;b)</td></tr>
<tr class="memdesc:ga0079bd8236eee75c4b5baa8497959b78"><td class="mdescLeft">&#160;</td><td class="mdescRight">Append two period sets when all periods of set <b>change</b> their sign. <br /></td></tr>
<tr class="separator:ga0079bd8236eee75c4b5baa8497959b78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadac52301b5dbc9383cc35610417802a3"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:gadac52301b5dbc9383cc35610417802a3"><td class="memTemplItemLeft" align="right" valign="top">std::basic_ostream&lt; CharType &gt; &amp;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__date__time.html#gadac52301b5dbc9383cc35610417802a3">operator&lt;&lt;</a> (std::basic_ostream&lt; CharType &gt; &amp;out, const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;t)</td></tr>
<tr class="separator:gadac52301b5dbc9383cc35610417802a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf6e30351fc67e887e37853723c228484"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:gaf6e30351fc67e887e37853723c228484"><td class="memTemplItemLeft" align="right" valign="top">std::basic_istream&lt; CharType &gt; &amp;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__date__time.html#gaf6e30351fc67e887e37853723c228484">operator&gt;&gt;</a> (std::basic_istream&lt; CharType &gt; &amp;in, <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;t)</td></tr>
<tr class="separator:gaf6e30351fc67e887e37853723c228484"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga04d9cf366abf96ba0a208f618fc9fb06"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classboost_1_1locale_1_1date__time__duration.html">date_time_duration</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__date__time.html#ga04d9cf366abf96ba0a208f618fc9fb06">operator-</a> (const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;later, const <a class="el" href="classboost_1_1locale_1_1date__time.html">date_time</a> &amp;earlier)</td></tr>
<tr class="separator:ga04d9cf366abf96ba0a208f618fc9fb06"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga60d2cc884cc1d9c6d4486d25f49d28cb"><td class="memTemplParams" colspan="2">template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga60d2cc884cc1d9c6d4486d25f49d28cb"><td class="memTemplItemLeft" align="right" valign="top">std::basic_ostream&lt; CharType &gt; &amp;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__format.html#ga60d2cc884cc1d9c6d4486d25f49d28cb">operator&lt;&lt;</a> (std::basic_ostream&lt; CharType &gt; &amp;out, const <a class="el" href="classboost_1_1locale_1_1basic__format.html">basic_format</a>&lt; CharType &gt; &amp;fmt)</td></tr>
<tr class="separator:ga60d2cc884cc1d9c6d4486d25f49d28cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c2db06787c9ec4f1296025a562f210e"><td class="memItemLeft" align="right" valign="top"><a id="a2c2db06787c9ec4f1296025a562f210e"></a>
typedef&#160;</td><td class="memItemRight" valign="bottom"><b>BOOST_DEPRECATED</b> (&quot;Use <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a>&quot;) char_facet_t character_facet_type</td></tr>
<tr class="separator:a2c2db06787c9ec4f1296025a562f210e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f09fef32823ac120806038f500c3b77"><td class="memItemLeft" align="right" valign="top"><a id="a5f09fef32823ac120806038f500c3b77"></a>
typedef&#160;</td><td class="memItemRight" valign="bottom"><b>BOOST_DEPRECATED</b> (&quot;Use <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a>&quot;) category_t locale_category_type</td></tr>
<tr class="separator:a5f09fef32823ac120806038f500c3b77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19adeddeb599ce4451b24ac7e4da5cb8"><td class="memItemLeft" align="right" valign="top"><a id="a19adeddeb599ce4451b24ac7e4da5cb8"></a>
constexpr <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>operator|</b> (const <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a> lhs, const <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a> rhs)</td></tr>
<tr class="separator:a19adeddeb599ce4451b24ac7e4da5cb8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a36238bdefaa6689e707b064f1fb30e"><td class="memItemLeft" align="right" valign="top"><a id="a2a36238bdefaa6689e707b064f1fb30e"></a>
constexpr <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>operator^</b> (const <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a> lhs, const <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a> rhs)</td></tr>
<tr class="separator:a2a36238bdefaa6689e707b064f1fb30e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3eeddc1b92fa01495ef929b764fabe4"><td class="memItemLeft" align="right" valign="top"><a id="aa3eeddc1b92fa01495ef929b764fabe4"></a>
constexpr bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator &amp;</b> (const <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a> lhs, const <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a> rhs)</td></tr>
<tr class="separator:aa3eeddc1b92fa01495ef929b764fabe4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae02a43822947247298c71fa1b67306c8"><td class="memItemLeft" align="right" valign="top"><a id="ae02a43822947247298c71fa1b67306c8"></a>
BOOST_CXX14_CONSTEXPR <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator++</b> (<a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a> &amp;v)</td></tr>
<tr class="separator:ae02a43822947247298c71fa1b67306c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aebe82e059cb17ca136e05cb66ec9268d"><td class="memItemLeft" align="right" valign="top"><a id="aebe82e059cb17ca136e05cb66ec9268d"></a>
constexpr <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>operator|</b> (const <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a> lhs, const <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a> rhs)</td></tr>
<tr class="separator:aebe82e059cb17ca136e05cb66ec9268d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a661a13b8cb8b541471a64a5b7a057710"><td class="memItemLeft" align="right" valign="top"><a id="a661a13b8cb8b541471a64a5b7a057710"></a>
constexpr <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>operator^</b> (const <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a> lhs, const <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a> rhs)</td></tr>
<tr class="separator:a661a13b8cb8b541471a64a5b7a057710"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa22ccb1d6222b8e5f8a1a7c25d9c6bd7"><td class="memItemLeft" align="right" valign="top"><a id="aa22ccb1d6222b8e5f8a1a7c25d9c6bd7"></a>
constexpr bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator &amp;</b> (const <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a> lhs, const <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a> rhs)</td></tr>
<tr class="separator:aa22ccb1d6222b8e5f8a1a7c25d9c6bd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a611d5664c11b90b807a22c9101237409"><td class="memItemLeft" align="right" valign="top"><a id="a611d5664c11b90b807a22c9101237409"></a>
BOOST_CXX14_CONSTEXPR <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator++</b> (<a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a> &amp;v)</td></tr>
<tr class="separator:a611d5664c11b90b807a22c9101237409"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7a71c9cb13a0b3cfab819a975988710d"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga7a71c9cb13a0b3cfab819a975988710d"><td class="memTemplItemLeft" align="right" valign="top">std::basic_ostream&lt; CharType &gt; &amp;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#ga7a71c9cb13a0b3cfab819a975988710d">operator&lt;&lt;</a> (std::basic_ostream&lt; CharType &gt; &amp;out, const <a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a>&lt; CharType &gt; &amp;msg)</td></tr>
<tr class="memdesc:ga7a71c9cb13a0b3cfab819a975988710d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate message <em>msg</em> and write it to stream. <br /></td></tr>
<tr class="separator:ga7a71c9cb13a0b3cfab819a975988710d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr><td colspan="2"><div class="groupHeader">Indirect message translation function family</div></td></tr>
<tr><td colspan="2"><div class="groupText"><p><a class="anchor" id="boost_locale_translate_family"></a></p>
</div></td></tr>
<tr class="memitem:gae768006cdf5561f6d2e56b58303beae2"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:gae768006cdf5561f6d2e56b58303beae2"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a>&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#gae768006cdf5561f6d2e56b58303beae2">translate</a> (const CharType *msg)</td></tr>
<tr class="memdesc:gae768006cdf5561f6d2e56b58303beae2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate a message, <em>msg</em> is not copied. <br /></td></tr>
<tr class="separator:gae768006cdf5561f6d2e56b58303beae2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacacb715d73372d119ed8bd692a1af511"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:gacacb715d73372d119ed8bd692a1af511"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a>&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#gacacb715d73372d119ed8bd692a1af511">translate</a> (const CharType *context, const CharType *msg)</td></tr>
<tr class="memdesc:gacacb715d73372d119ed8bd692a1af511"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate a message in context, <em>msg</em> and <em>context</em> are not copied. <br /></td></tr>
<tr class="separator:gacacb715d73372d119ed8bd692a1af511"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf4d20d6b63bfb92c48c69c1447dee2bf"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:gaf4d20d6b63bfb92c48c69c1447dee2bf"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a>&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#gaf4d20d6b63bfb92c48c69c1447dee2bf">translate</a> (const CharType *single, const CharType *plural, int n)</td></tr>
<tr class="memdesc:gaf4d20d6b63bfb92c48c69c1447dee2bf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate a plural message form, <em>single</em> and <em>plural</em> are not copied. <br /></td></tr>
<tr class="separator:gaf4d20d6b63bfb92c48c69c1447dee2bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac36250159e07baf1b03da8c53131c229"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:gac36250159e07baf1b03da8c53131c229"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a>&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#gac36250159e07baf1b03da8c53131c229">translate</a> (const CharType *context, const CharType *single, const CharType *plural, int n)</td></tr>
<tr class="memdesc:gac36250159e07baf1b03da8c53131c229"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate a plural message from in constext, <em>context</em>, <em>single</em> and <em>plural</em> are not copied. <br /></td></tr>
<tr class="separator:gac36250159e07baf1b03da8c53131c229"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga87b05b145110f1f709c84587cc1c71eb"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga87b05b145110f1f709c84587cc1c71eb"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a>&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#ga87b05b145110f1f709c84587cc1c71eb">translate</a> (const std::basic_string&lt; CharType &gt; &amp;msg)</td></tr>
<tr class="memdesc:ga87b05b145110f1f709c84587cc1c71eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate a message, <em>msg</em> is copied. <br /></td></tr>
<tr class="separator:ga87b05b145110f1f709c84587cc1c71eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad6b32c7a3a27c2245f7b89b27f1b583c"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:gad6b32c7a3a27c2245f7b89b27f1b583c"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a>&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#gad6b32c7a3a27c2245f7b89b27f1b583c">translate</a> (const std::basic_string&lt; CharType &gt; &amp;context, const std::basic_string&lt; CharType &gt; &amp;msg)</td></tr>
<tr class="memdesc:gad6b32c7a3a27c2245f7b89b27f1b583c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate a message in context,<em>context</em> and <em>msg</em> is copied. <br /></td></tr>
<tr class="separator:gad6b32c7a3a27c2245f7b89b27f1b583c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7005bd38da348ad7c3be73161f3267c4"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga7005bd38da348ad7c3be73161f3267c4"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a>&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#ga7005bd38da348ad7c3be73161f3267c4">translate</a> (const std::basic_string&lt; CharType &gt; &amp;context, const std::basic_string&lt; CharType &gt; &amp;single, const std::basic_string&lt; CharType &gt; &amp;plural, int n)</td></tr>
<tr class="memdesc:ga7005bd38da348ad7c3be73161f3267c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate a plural message form in constext, <em>context</em>, <em>single</em> and <em>plural</em> are copied. <br /></td></tr>
<tr class="separator:ga7005bd38da348ad7c3be73161f3267c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf1678fee55d356102457e32a34796f12"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:gaf1678fee55d356102457e32a34796f12"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="classboost_1_1locale_1_1basic__message.html">basic_message</a>&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#gaf1678fee55d356102457e32a34796f12">translate</a> (const std::basic_string&lt; CharType &gt; &amp;single, const std::basic_string&lt; CharType &gt; &amp;plural, int n)</td></tr>
<tr class="memdesc:gaf1678fee55d356102457e32a34796f12"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate a plural message form, <em>single</em> and <em>plural</em> are copied. <br /></td></tr>
<tr class="separator:gaf1678fee55d356102457e32a34796f12"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr><td colspan="2"><div class="groupHeader">Direct message translation functions family</div></td></tr>
<tr><td colspan="2"><div class="groupText"><p><a class="anchor" id="boost_locale_gettext_family"></a></p>
</div></td></tr>
<tr class="memitem:ga501ad3a1fbaee5f3544b62df572aa889"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga501ad3a1fbaee5f3544b62df572aa889"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#ga501ad3a1fbaee5f3544b62df572aa889">gettext</a> (const CharType *id, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="memdesc:ga501ad3a1fbaee5f3544b62df572aa889"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate message <em>id</em> according to locale <em>loc</em>. <br /></td></tr>
<tr class="separator:ga501ad3a1fbaee5f3544b62df572aa889"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6c3cd7947bd5ef3b9834e11c0ed68f49"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga6c3cd7947bd5ef3b9834e11c0ed68f49"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#ga6c3cd7947bd5ef3b9834e11c0ed68f49">ngettext</a> (const CharType *s, const CharType *p, int n, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="memdesc:ga6c3cd7947bd5ef3b9834e11c0ed68f49"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate plural form according to locale <em>loc</em>. <br /></td></tr>
<tr class="separator:ga6c3cd7947bd5ef3b9834e11c0ed68f49"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga92c8a424ada0fc1bc00d264ec7d535ed"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga92c8a424ada0fc1bc00d264ec7d535ed"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#ga92c8a424ada0fc1bc00d264ec7d535ed">dgettext</a> (const char *domain, const CharType *id, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="memdesc:ga92c8a424ada0fc1bc00d264ec7d535ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate message <em>id</em> according to locale <em>loc</em> in domain <em>domain</em>. <br /></td></tr>
<tr class="separator:ga92c8a424ada0fc1bc00d264ec7d535ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga15fddb245466dde6f420f4d8d1abb465"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga15fddb245466dde6f420f4d8d1abb465"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#ga15fddb245466dde6f420f4d8d1abb465">dngettext</a> (const char *domain, const CharType *s, const CharType *p, int n, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="memdesc:ga15fddb245466dde6f420f4d8d1abb465"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate plural form according to locale <em>loc</em> in domain <em>domain</em>. <br /></td></tr>
<tr class="separator:ga15fddb245466dde6f420f4d8d1abb465"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4363b1400f416e94683c127cfeaa3539"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga4363b1400f416e94683c127cfeaa3539"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#ga4363b1400f416e94683c127cfeaa3539">pgettext</a> (const CharType *context, const CharType *id, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="memdesc:ga4363b1400f416e94683c127cfeaa3539"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate message <em>id</em> according to locale <em>loc</em> in context <em>context</em>. <br /></td></tr>
<tr class="separator:ga4363b1400f416e94683c127cfeaa3539"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3b59f88e59dc31d1fb899d9a7c2b46ea"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga3b59f88e59dc31d1fb899d9a7c2b46ea"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#ga3b59f88e59dc31d1fb899d9a7c2b46ea">npgettext</a> (const CharType *context, const CharType *s, const CharType *p, int n, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="memdesc:ga3b59f88e59dc31d1fb899d9a7c2b46ea"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate plural form according to locale <em>loc</em> in context <em>context</em>. <br /></td></tr>
<tr class="separator:ga3b59f88e59dc31d1fb899d9a7c2b46ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad6998fdd7ba170f2cc1bb3dd59482760"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:gad6998fdd7ba170f2cc1bb3dd59482760"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#gad6998fdd7ba170f2cc1bb3dd59482760">dpgettext</a> (const char *domain, const CharType *context, const CharType *id, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="memdesc:gad6998fdd7ba170f2cc1bb3dd59482760"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate message <em>id</em> according to locale <em>loc</em> in domain <em>domain</em> in context <em>context</em>. <br /></td></tr>
<tr class="separator:gad6998fdd7ba170f2cc1bb3dd59482760"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9bb0af363353f00d272a6ffa0464cc32"><td class="memTemplParams" colspan="2">
template&lt;typename CharType &gt; </td></tr>
<tr class="memitem:ga9bb0af363353f00d272a6ffa0464cc32"><td class="memTemplItemLeft" align="right" valign="top">std::basic_string&lt; CharType &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__message.html#ga9bb0af363353f00d272a6ffa0464cc32">dnpgettext</a> (const char *domain, const CharType *context, const CharType *s, const CharType *p, int n, const std::locale &amp;loc=std::locale())</td></tr>
<tr class="memdesc:ga9bb0af363353f00d272a6ffa0464cc32"><td class="mdescLeft">&#160;</td><td class="mdescRight">Translate plural form according to locale <em>loc</em> in domain <em>domain</em> in context <em>context</em>. <br /></td></tr>
<tr class="separator:ga9bb0af363353f00d272a6ffa0464cc32"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a7d70437cefe32c6d73145496e591d718"><td class="memItemLeft" align="right" valign="top"><a id="a7d70437cefe32c6d73145496e591d718"></a>
constexpr <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale.html#a7d70437cefe32c6d73145496e591d718">character_facet_first</a> = <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6a747f241c2d27fc06b5d75a18ef146c52">char_facet_t::char_f</a></td></tr>
<tr class="memdesc:a7d70437cefe32c6d73145496e591d718"><td class="mdescLeft">&#160;</td><td class="mdescRight">First facet specific for character type. <br /></td></tr>
<tr class="separator:a7d70437cefe32c6d73145496e591d718"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a01888465ff3ad471e01344dd195ae973"><td class="memItemLeft" align="right" valign="top">constexpr <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale.html#a01888465ff3ad471e01344dd195ae973">character_facet_last</a></td></tr>
<tr class="memdesc:a01888465ff3ad471e01344dd195ae973"><td class="mdescLeft">&#160;</td><td class="mdescRight">Last facet specific for character type.  <a href="#a01888465ff3ad471e01344dd195ae973">More...</a><br /></td></tr>
<tr class="separator:a01888465ff3ad471e01344dd195ae973"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a35e0b110f98270e215cc746d2bb6a1fb"><td class="memItemLeft" align="right" valign="top"><a id="a35e0b110f98270e215cc746d2bb6a1fb"></a>
constexpr <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale.html#a35e0b110f98270e215cc746d2bb6a1fb">all_characters</a> = <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a>(0xFFFFFFFFu)</td></tr>
<tr class="memdesc:a35e0b110f98270e215cc746d2bb6a1fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Special mask &ndash; generate all. <br /></td></tr>
<tr class="separator:a35e0b110f98270e215cc746d2bb6a1fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec9189dec983f00d79a51d8a9292416c"><td class="memItemLeft" align="right" valign="top"><a id="aec9189dec983f00d79a51d8a9292416c"></a>
constexpr <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale.html#aec9189dec983f00d79a51d8a9292416c">per_character_facet_first</a> = <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629a31168275dcaac634489082b54c4c66d0">category_t::convert</a></td></tr>
<tr class="memdesc:aec9189dec983f00d79a51d8a9292416c"><td class="mdescLeft">&#160;</td><td class="mdescRight">First facet specific for character. <br /></td></tr>
<tr class="separator:aec9189dec983f00d79a51d8a9292416c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a64a5091c788c93b4465a5e776a8e1bcb"><td class="memItemLeft" align="right" valign="top"><a id="a64a5091c788c93b4465a5e776a8e1bcb"></a>
constexpr <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale.html#a64a5091c788c93b4465a5e776a8e1bcb">per_character_facet_last</a> = <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629a81fd830c85363675edb98d2879916d8c">category_t::boundary</a></td></tr>
<tr class="memdesc:a64a5091c788c93b4465a5e776a8e1bcb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Last facet specific for character. <br /></td></tr>
<tr class="separator:a64a5091c788c93b4465a5e776a8e1bcb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac8d11d7a6c971fd5865eacada5220a2c"><td class="memItemLeft" align="right" valign="top"><a id="ac8d11d7a6c971fd5865eacada5220a2c"></a>
constexpr <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale.html#ac8d11d7a6c971fd5865eacada5220a2c">non_character_facet_first</a> = <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629aa0e7b2a565119c0a7ec3126a16016113">category_t::calendar</a></td></tr>
<tr class="memdesc:ac8d11d7a6c971fd5865eacada5220a2c"><td class="mdescLeft">&#160;</td><td class="mdescRight">First character independent facet. <br /></td></tr>
<tr class="separator:ac8d11d7a6c971fd5865eacada5220a2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae948847a73171fe908bf5f641deffc49"><td class="memItemLeft" align="right" valign="top"><a id="ae948847a73171fe908bf5f641deffc49"></a>
constexpr <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale.html#ae948847a73171fe908bf5f641deffc49">non_character_facet_last</a> = <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629abb3ccd5881d651448ded1dac904054ac">category_t::information</a></td></tr>
<tr class="memdesc:ae948847a73171fe908bf5f641deffc49"><td class="mdescLeft">&#160;</td><td class="mdescRight">Last character independent facet. <br /></td></tr>
<tr class="separator:ae948847a73171fe908bf5f641deffc49"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7295b3de8b4cde8183b2970638bf2eb"><td class="memItemLeft" align="right" valign="top"><a id="ad7295b3de8b4cde8183b2970638bf2eb"></a>
constexpr <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale.html#ad7295b3de8b4cde8183b2970638bf2eb">category_first</a> = <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629a31168275dcaac634489082b54c4c66d0">category_t::convert</a></td></tr>
<tr class="memdesc:ad7295b3de8b4cde8183b2970638bf2eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">First category facet. <br /></td></tr>
<tr class="separator:ad7295b3de8b4cde8183b2970638bf2eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a452805aa43886751adccf576466dd226"><td class="memItemLeft" align="right" valign="top"><a id="a452805aa43886751adccf576466dd226"></a>
constexpr <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale.html#a452805aa43886751adccf576466dd226">category_last</a> = <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629abb3ccd5881d651448ded1dac904054ac">category_t::information</a></td></tr>
<tr class="memdesc:a452805aa43886751adccf576466dd226"><td class="mdescLeft">&#160;</td><td class="mdescRight">Last category facet. <br /></td></tr>
<tr class="separator:a452805aa43886751adccf576466dd226"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad17b97ec2f0e091b20578d86ba2f8554"><td class="memItemLeft" align="right" valign="top"><a id="ad17b97ec2f0e091b20578d86ba2f8554"></a>
constexpr <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1locale.html#ad17b97ec2f0e091b20578d86ba2f8554">all_categories</a> = <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">category_t</a>(0xFFFFFFFFu)</td></tr>
<tr class="memdesc:ad17b97ec2f0e091b20578d86ba2f8554"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generate all of them. <br /></td></tr>
<tr class="separator:ad17b97ec2f0e091b20578d86ba2f8554"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>This is the main namespace that encloses all localization classes. </p>
</div><h2 class="groupheader">Enumeration Type Documentation</h2>
<a id="aacab728d9d00542295bbd3c95a142629"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aacab728d9d00542295bbd3c95a142629">&#9670;&nbsp;</a></span>category_t</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="namespaceboost_1_1locale.html#aacab728d9d00542295bbd3c95a142629">boost::locale::category_t</a> : uint32_t</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">strong</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Type used for more fine grained generation of facets</p>
<p>Supports bitwise OR and bitwise AND (the latter returning if the type is set) </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="aacab728d9d00542295bbd3c95a142629a31168275dcaac634489082b54c4c66d0"></a>convert&#160;</td><td class="fielddoc"><p>Generate conversion facets. </p>
</td></tr>
<tr><td class="fieldname"><a id="aacab728d9d00542295bbd3c95a142629abf0e0acec49363acebe287d869fbf449"></a>collation&#160;</td><td class="fielddoc"><p>Generate collation facets. </p>
</td></tr>
<tr><td class="fieldname"><a id="aacab728d9d00542295bbd3c95a142629af9f9c04813a46502873a0c2a3d361ce8"></a>formatting&#160;</td><td class="fielddoc"><p>Generate numbers, currency, date-time formatting facets. </p>
</td></tr>
<tr><td class="fieldname"><a id="aacab728d9d00542295bbd3c95a142629adbc77665f51d780a776978e34f065af5"></a>parsing&#160;</td><td class="fielddoc"><p>Generate numbers, currency, date-time formatting facets. </p>
</td></tr>
<tr><td class="fieldname"><a id="aacab728d9d00542295bbd3c95a142629a78e731027d8fd50ed642340b7c9a63b3"></a>message&#160;</td><td class="fielddoc"><p>Generate message facets. </p>
</td></tr>
<tr><td class="fieldname"><a id="aacab728d9d00542295bbd3c95a142629a1c360a64cd5ceae6444b2dc0642e53ea"></a>codepage&#160;</td><td class="fielddoc"><p>Generate character set conversion facets (derived from std::codecvt) </p>
</td></tr>
<tr><td class="fieldname"><a id="aacab728d9d00542295bbd3c95a142629a81fd830c85363675edb98d2879916d8c"></a>boundary&#160;</td><td class="fielddoc"><p>Generate boundary analysis facet. </p>
</td></tr>
<tr><td class="fieldname"><a id="aacab728d9d00542295bbd3c95a142629aa0e7b2a565119c0a7ec3126a16016113"></a>calendar&#160;</td><td class="fielddoc"><p>Generate boundary analysis facet. </p>
</td></tr>
<tr><td class="fieldname"><a id="aacab728d9d00542295bbd3c95a142629abb3ccd5881d651448ded1dac904054ac"></a>information&#160;</td><td class="fielddoc"><p>Generate general locale information facet. </p>
</td></tr>
</table>

</div>
</div>
<a id="a662f2b6ccb8e8123e50f0704fec0cef6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a662f2b6ccb8e8123e50f0704fec0cef6">&#9670;&nbsp;</a></span>char_facet_t</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">boost::locale::char_facet_t</a> : uint32_t</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">strong</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Type that specifies the character type that locales can be generated for</p>
<p>Supports bitwise OR and bitwise AND (the latter returning if the type is set) </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a662f2b6ccb8e8123e50f0704fec0cef6a5ce701c04021c9da33b17a7b371903b8"></a>nochar&#160;</td><td class="fielddoc"><p>Unspecified character category for character independent facets. </p>
</td></tr>
<tr><td class="fieldname"><a id="a662f2b6ccb8e8123e50f0704fec0cef6a747f241c2d27fc06b5d75a18ef146c52"></a>char_f&#160;</td><td class="fielddoc"><p>8-bit character facets </p>
</td></tr>
<tr><td class="fieldname"><a id="a662f2b6ccb8e8123e50f0704fec0cef6a8860d46e0e82cfe1caee9af99a57b4db"></a>wchar_f&#160;</td><td class="fielddoc"><p>wide character facets </p>
</td></tr>
<tr><td class="fieldname"><a id="a662f2b6ccb8e8123e50f0704fec0cef6a6d52c0a668d7157436f8d9f706c4e114"></a>char16_f&#160;</td><td class="fielddoc"><p>C++11 char16_t facets. </p>
</td></tr>
<tr><td class="fieldname"><a id="a662f2b6ccb8e8123e50f0704fec0cef6a82d3b8653dc0bedc46b1d706d2eabdcc"></a>char32_f&#160;</td><td class="fielddoc"><p>C++11 char32_t facets. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a01888465ff3ad471e01344dd195ae973"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a01888465ff3ad471e01344dd195ae973">&#9670;&nbsp;</a></span>character_facet_last</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">constexpr <a class="el" href="namespaceboost_1_1locale.html#a662f2b6ccb8e8123e50f0704fec0cef6">char_facet_t</a> boost::locale::character_facet_last</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">=</div><div class="line"></div><div class="line">      char_facet_t::char32_f</div></div><!-- fragment -->
<p>Last facet specific for character type. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->

    <li class="footer">
&copy; Copyright 2009-2012 Artyom Beilis,  Distributed under the <a href="https://www.boost.org/LICENSE_1_0.txt">Boost Software License</a>, Version 1.0.
    </li>
   </ul>
 </div>
</body>
</html>
