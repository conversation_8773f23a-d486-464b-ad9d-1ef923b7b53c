[table:platform Platform Details
[[Platform][[[Compiler][GNU C++ version 10.3.0]]
[[GMP][6.2.0]]
[[MPFR][262146]]
[[Boost][107800]]
[[Run date][Sep 30 2021]]
]

gmp_float      50        +                                  0.0183555
gmp_float      50        -                                  0.0136532
gmp_float      50        *                                  0.02086
gmp_float      50        /                                  0.101766
gmp_float      50        str                                0.000493989
gmp_float      50        +(int)                             0.00443909
gmp_float      50        -(int)                             0.0134294
gmp_float      50        *(int)                             0.00513201
gmp_float      50        /(int)                             0.0243611
gmp_float      50        construct                          0.0232445
gmp_float      50        construct(unsigned)                0.0299395
gmp_float      50        construct(unsigned long long)      0.0258903
gmp_float      50        +(unsigned long long)              0.0051023
gmp_float      50        -(unsigned long long)              0.0113255
gmp_float      50        *(unsigned long long)              0.0128361
gmp_float      50        /(unsigned long long)              0.0179778
gmp_float      50        +=(unsigned long long)             0.0164496
gmp_float      50        -=(unsigned long long)             0.0148782
gmp_float      50        *=(unsigned long long)             0.00765054
gmp_float      50        /=(unsigned long long)             0.0195279
gmp_float      100       +                                  0.0130366
gmp_float      100       -                                  0.0192537
gmp_float      100       *                                  0.0257348
gmp_float      100       /                                  0.138938
gmp_float      100       str                                0.00109572
gmp_float      100       +(int)                             0.00626549
gmp_float      100       -(int)                             0.0119272
gmp_float      100       *(int)                             0.00558264
gmp_float      100       /(int)                             0.0220741
gmp_float      100       construct                          0.0242744
gmp_float      100       construct(unsigned)                0.0252132
gmp_float      100       construct(unsigned long long)      0.0209379
gmp_float      100       +(unsigned long long)              0.00778526
gmp_float      100       -(unsigned long long)              0.0194584
gmp_float      100       *(unsigned long long)              0.00599344
gmp_float      100       /(unsigned long long)              0.0233296
gmp_float      100       +=(unsigned long long)             0.0137783
gmp_float      100       -=(unsigned long long)             0.0221136
gmp_float      100       *=(unsigned long long)             0.00979552
gmp_float      100       /=(unsigned long long)             0.032907
gmp_float      500       +                                  0.0273382
gmp_float      500       -                                  0.0260432
gmp_float      500       *                                  0.292919
gmp_float      500       /                                  0.430233
gmp_float      500       str                                0.00345598
gmp_float      500       +(int)                             0.0120673
gmp_float      500       -(int)                             0.0272281
gmp_float      500       *(int)                             0.0211042
gmp_float      500       /(int)                             0.0784889
gmp_float      500       construct                          0.0885159
gmp_float      500       construct(unsigned)                0.0829907
gmp_float      500       construct(unsigned long long)      0.0914671
gmp_float      500       +(unsigned long long)              0.0111554
gmp_float      500       -(unsigned long long)              0.026385
gmp_float      500       *(unsigned long long)              0.0219284
gmp_float      500       /(unsigned long long)              0.0704423
gmp_float      500       +=(unsigned long long)             0.0172751
gmp_float      500       -=(unsigned long long)             0.0300017
gmp_float      500       *=(unsigned long long)             0.0296053
gmp_float      500       /=(unsigned long long)             0.076721
gmp_int        128       +                                  0.00515104
gmp_int        128       -                                  0.00664573
gmp_int        128       *                                  0.00560549
gmp_int        128       /                                  0.04667
gmp_int        128       str                                0.000178807
gmp_int        128       +(int)                             0.00347405
gmp_int        128       -(int)                             0.00363988
gmp_int        128       *(int)                             0.00473426
gmp_int        128       /(int)                             0.0115696
gmp_int        128       construct                          0.00329962
gmp_int        128       construct(unsigned)                0.0241048
gmp_int        128       construct(unsigned long long)      0.0245077
gmp_int        128       %                                  0.0466966
gmp_int        128       |                                  0.00437203
gmp_int        128       &                                  0.00394167
gmp_int        128       ^                                  0.0048846
gmp_int        128       <<                                 0.00538874
gmp_int        128       >>                                 0.00242687
gmp_int        128       %(int)                             0.0118703
gmp_int        128       |(int)                             0.010008
gmp_int        128       &(int)                             0.0148993
gmp_int        128       ^(int)                             0.0106263
gmp_int        128       gcd                                0.170154
gmp_int        128       powm                               0.0381833
gmp_int        128       +(unsigned long long)              0.00470432
gmp_int        128       -(unsigned long long)              0.00866778
gmp_int        128       *(unsigned long long)              0.00371188
gmp_int        128       /(unsigned long long)              0.00955921
gmp_int        128       +=(unsigned long long)             0.00987134
gmp_int        128       -=(unsigned long long)             0.00635455
gmp_int        128       *=(unsigned long long)             0.00560359
gmp_int        128       /=(unsigned long long)             0.0134478
gmp_int        256       +                                  0.00997612
gmp_int        256       -                                  0.0128659
gmp_int        256       *                                  0.0134261
gmp_int        256       /                                  0.0571824
gmp_int        256       str                                0.000224466
gmp_int        256       +(int)                             0.0033482
gmp_int        256       -(int)                             0.00382312
gmp_int        256       *(int)                             0.0112929
gmp_int        256       /(int)                             0.0171517
gmp_int        256       construct                          0.0037155
gmp_int        256       construct(unsigned)                0.0227529
gmp_int        256       construct(unsigned long long)      0.020762
gmp_int        256       %                                  0.0514059
gmp_int        256       |                                  0.00480677
gmp_int        256       &                                  0.00417321
gmp_int        256       ^                                  0.00693092
gmp_int        256       <<                                 0.00869801
gmp_int        256       >>                                 0.0022372
gmp_int        256       %(int)                             0.0148079
gmp_int        256       |(int)                             0.0103284
gmp_int        256       &(int)                             0.00947844
gmp_int        256       ^(int)                             0.0160125
gmp_int        256       gcd                                0.761472
gmp_int        256       powm                               0.152912
gmp_int        256       +(unsigned long long)              0.00509394
gmp_int        256       -(unsigned long long)              0.0049585
gmp_int        256       *(unsigned long long)              0.00456418
gmp_int        256       /(unsigned long long)              0.0152731
gmp_int        256       +=(unsigned long long)             0.00672804
gmp_int        256       -=(unsigned long long)             0.00828179
gmp_int        256       *=(unsigned long long)             0.00858963
gmp_int        256       /=(unsigned long long)             0.0221032
gmp_int        512       +                                  0.0130487
gmp_int        512       -                                  0.0102195
gmp_int        512       *                                  0.0300231
gmp_int        512       /                                  0.0796599
gmp_int        512       str                                0.00061628
gmp_int        512       +(int)                             0.00425087
gmp_int        512       -(int)                             0.00424793
gmp_int        512       *(int)                             0.00578859
gmp_int        512       /(int)                             0.0232057
gmp_int        512       construct                          0.00624925
gmp_int        512       construct(unsigned)                0.0183369
gmp_int        512       construct(unsigned long long)      0.0202006
gmp_int        512       %                                  0.0722958
gmp_int        512       |                                  0.00628228
gmp_int        512       &                                  0.00524347
gmp_int        512       ^                                  0.00615531
gmp_int        512       <<                                 0.00888006
gmp_int        512       >>                                 0.00238226
gmp_int        512       %(int)                             0.024264
gmp_int        512       |(int)                             0.0247852
gmp_int        512       &(int)                             0.013507
gmp_int        512       ^(int)                             0.0173321
gmp_int        512       gcd                                1.62139
gmp_int        512       powm                               0.766677
gmp_int        512       +(unsigned long long)              0.00595229
gmp_int        512       -(unsigned long long)              0.00592785
gmp_int        512       *(unsigned long long)              0.00637276
gmp_int        512       /(unsigned long long)              0.0244044
gmp_int        512       +=(unsigned long long)             0.00868995
gmp_int        512       -=(unsigned long long)             0.00847054
gmp_int        512       *=(unsigned long long)             0.00977523
gmp_int        512       /=(unsigned long long)             0.0334445
gmp_int        1024      +                                  0.00971031
gmp_int        1024      -                                  0.0105533
gmp_int        1024      *                                  0.100546
gmp_int        1024      /                                  0.0881567
gmp_int        1024      str                                0.000778966
gmp_int        1024      +(int)                             0.0064494
gmp_int        1024      -(int)                             0.00561665
gmp_int        1024      *(int)                             0.00931559
gmp_int        1024      /(int)                             0.0422461
gmp_int        1024      construct                          0.00336255
gmp_int        1024      construct(unsigned)                0.0181871
gmp_int        1024      construct(unsigned long long)      0.0188747
gmp_int        1024      %                                  0.0918952
gmp_int        1024      |                                  0.00861647
gmp_int        1024      &                                  0.00800121
gmp_int        1024      ^                                  0.00963503
gmp_int        1024      <<                                 0.0193064
gmp_int        1024      >>                                 0.00454358
gmp_int        1024      %(int)                             0.0257336
gmp_int        1024      |(int)                             0.0135159
gmp_int        1024      &(int)                             0.0102081
gmp_int        1024      ^(int)                             0.0156309
gmp_int        1024      gcd                                3.48358
gmp_int        1024      powm                               5.14976
gmp_int        1024      +(unsigned long long)              0.00802101
gmp_int        1024      -(unsigned long long)              0.00806262
gmp_int        1024      *(unsigned long long)              0.0102277
gmp_int        1024      /(unsigned long long)              0.042466
gmp_int        1024      +=(unsigned long long)             0.0154667
gmp_int        1024      -=(unsigned long long)             0.0106078
gmp_int        1024      *=(unsigned long long)             0.0134989
gmp_int        1024      /=(unsigned long long)             0.0405622
cpp_int(fixed) 128       +                                  0.000877268
cpp_int(fixed) 128       -                                  0.00192988
cpp_int(fixed) 128       *                                  0.00116759
cpp_int(fixed) 128       /                                  0.0273912
cpp_int(fixed) 128       str                                0.00084634
cpp_int(fixed) 128       +(int)                             0.000698051
cpp_int(fixed) 128       -(int)                             0.00113016
cpp_int(fixed) 128       *(int)                             0.000662467
cpp_int(fixed) 128       /(int)                             0.0147198
cpp_int(fixed) 128       construct                          0.000587582
cpp_int(fixed) 128       construct(unsigned)                0.000872038
cpp_int(fixed) 128       construct(unsigned long long)      0.000903049
cpp_int(fixed) 128       %                                  0.0318553
cpp_int(fixed) 128       |                                  0.0043918
cpp_int(fixed) 128       &                                  0.00358104
cpp_int(fixed) 128       ^                                  0.00360758
cpp_int(fixed) 128       <<                                 0.00116939
cpp_int(fixed) 128       >>                                 0.000934446
cpp_int(fixed) 128       %(int)                             0.0172916
cpp_int(fixed) 128       |(int)                             0.00380628
cpp_int(fixed) 128       &(int)                             0.0038455
cpp_int(fixed) 128       ^(int)                             0.00371059
cpp_int(fixed) 128       gcd                                0.621889
cpp_int(fixed) 128       powm                               0.35895
cpp_int(fixed) 128       +(unsigned long long)              0.000900057
cpp_int(fixed) 128       -(unsigned long long)              0.00114855
cpp_int(fixed) 128       *(unsigned long long)              0.000745744
cpp_int(fixed) 128       /(unsigned long long)              0.0169001
cpp_int(fixed) 128       +=(unsigned long long)             0.000869555
cpp_int(fixed) 128       -=(unsigned long long)             0.000954108
cpp_int(fixed) 128       *=(unsigned long long)             0.000832044
cpp_int(fixed) 128       /=(unsigned long long)             0.0226255
cpp_int(fixed) 256       +                                  0.00687102
cpp_int(fixed) 256       -                                  0.010289
cpp_int(fixed) 256       *                                  0.0237359
cpp_int(fixed) 256       /                                  0.098585
cpp_int(fixed) 256       str                                0.000401216
cpp_int(fixed) 256       +(int)                             0.00578118
cpp_int(fixed) 256       -(int)                             0.00482789
cpp_int(fixed) 256       *(int)                             0.00648519
cpp_int(fixed) 256       /(int)                             0.0744498
cpp_int(fixed) 256       construct                          0.00243004
cpp_int(fixed) 256       construct(unsigned)                0.0026667
cpp_int(fixed) 256       construct(unsigned long long)      0.00253319
cpp_int(fixed) 256       %                                  0.0663805
cpp_int(fixed) 256       |                                  0.00661838
cpp_int(fixed) 256       &                                  0.00641591
cpp_int(fixed) 256       ^                                  0.00686442
cpp_int(fixed) 256       <<                                 0.00844969
cpp_int(fixed) 256       >>                                 0.0146615
cpp_int(fixed) 256       %(int)                             0.0247468
cpp_int(fixed) 256       |(int)                             0.00552682
cpp_int(fixed) 256       &(int)                             0.00630453
cpp_int(fixed) 256       ^(int)                             0.0073244
cpp_int(fixed) 256       gcd                                1.35865
cpp_int(fixed) 256       powm                               1.53516
cpp_int(fixed) 256       +(unsigned long long)              0.00648945
cpp_int(fixed) 256       -(unsigned long long)              0.00515424
cpp_int(fixed) 256       *(unsigned long long)              0.00489332
cpp_int(fixed) 256       /(unsigned long long)              0.0548884
cpp_int(fixed) 256       +=(unsigned long long)             0.00906799
cpp_int(fixed) 256       -=(unsigned long long)             0.00513296
cpp_int(fixed) 256       *=(unsigned long long)             0.00472864
cpp_int(fixed) 256       /=(unsigned long long)             0.0691018
cpp_int(fixed) 512       +                                  0.0237507
cpp_int(fixed) 512       -                                  0.00921062
cpp_int(fixed) 512       *                                  0.047591
cpp_int(fixed) 512       /                                  0.169344
cpp_int(fixed) 512       str                                0.00103815
cpp_int(fixed) 512       +(int)                             0.00668085
cpp_int(fixed) 512       -(int)                             0.00531307
cpp_int(fixed) 512       *(int)                             0.0088359
cpp_int(fixed) 512       /(int)                             0.141253
cpp_int(fixed) 512       construct                          0.00375174
cpp_int(fixed) 512       construct(unsigned)                0.00400614
cpp_int(fixed) 512       construct(unsigned long long)      0.00399907
cpp_int(fixed) 512       %                                  0.0966394
cpp_int(fixed) 512       |                                  0.00825362
cpp_int(fixed) 512       &                                  0.0089337
cpp_int(fixed) 512       ^                                  0.00927498
cpp_int(fixed) 512       <<                                 0.0173029
cpp_int(fixed) 512       >>                                 0.0155169
cpp_int(fixed) 512       %(int)                             0.0688759
cpp_int(fixed) 512       |(int)                             0.00775532
cpp_int(fixed) 512       &(int)                             0.00845281
cpp_int(fixed) 512       ^(int)                             0.00792226
cpp_int(fixed) 512       gcd                                3.28597
cpp_int(fixed) 512       powm                               8.07714
cpp_int(fixed) 512       +(unsigned long long)              0.008261
cpp_int(fixed) 512       -(unsigned long long)              0.00726552
cpp_int(fixed) 512       *(unsigned long long)              0.00764018
cpp_int(fixed) 512       /(unsigned long long)              0.135566
cpp_int(fixed) 512       +=(unsigned long long)             0.00681593
cpp_int(fixed) 512       -=(unsigned long long)             0.00597589
cpp_int(fixed) 512       *=(unsigned long long)             0.00714494
cpp_int(fixed) 512       /=(unsigned long long)             0.133354
cpp_int(fixed) 1024      +                                  0.0184876
cpp_int(fixed) 1024      -                                  0.0162226
cpp_int(fixed) 1024      *                                  0.153461
cpp_int(fixed) 1024      /                                  0.327451
cpp_int(fixed) 1024      str                                0.00335647
cpp_int(fixed) 1024      +(int)                             0.0123052
cpp_int(fixed) 1024      -(int)                             0.0190533
cpp_int(fixed) 1024      *(int)                             0.0211192
cpp_int(fixed) 1024      /(int)                             0.277366
cpp_int(fixed) 1024      construct                          0.00893771
cpp_int(fixed) 1024      construct(unsigned)                0.00981681
cpp_int(fixed) 1024      construct(unsigned long long)      0.0096748
cpp_int(fixed) 1024      %                                  0.181883
cpp_int(fixed) 1024      |                                  0.0148123
cpp_int(fixed) 1024      &                                  0.019269
cpp_int(fixed) 1024      ^                                  0.0172541
cpp_int(fixed) 1024      <<                                 0.0299716
cpp_int(fixed) 1024      >>                                 0.0222922
cpp_int(fixed) 1024      %(int)                             0.111133
cpp_int(fixed) 1024      |(int)                             0.0122062
cpp_int(fixed) 1024      &(int)                             0.0225444
cpp_int(fixed) 1024      ^(int)                             0.0160789
cpp_int(fixed) 1024      gcd                                6.8044
cpp_int(fixed) 1024      powm                               43.7564
cpp_int(fixed) 1024      +(unsigned long long)              0.0125768
cpp_int(fixed) 1024      -(unsigned long long)              0.0126235
cpp_int(fixed) 1024      *(unsigned long long)              0.0157115
cpp_int(fixed) 1024      /(unsigned long long)              0.2702
cpp_int(fixed) 1024      +=(unsigned long long)             0.00638493
cpp_int(fixed) 1024      -=(unsigned long long)             0.00748462
cpp_int(fixed) 1024      *=(unsigned long long)             0.0134446
cpp_int(fixed) 1024      /=(unsigned long long)             0.268888
cpp_int        128       +                                  0.0086037
cpp_int        128       -                                  0.00859678
cpp_int        128       *                                  0.0145016
cpp_int        128       /                                  0.0878919
cpp_int        128       str                                0.000264092
cpp_int        128       +(int)                             0.00534018
cpp_int        128       -(int)                             0.00347144
cpp_int        128       *(int)                             0.0072726
cpp_int        128       /(int)                             0.0520677
cpp_int        128       construct                          0.00190752
cpp_int        128       construct(unsigned)                0.00174712
cpp_int        128       construct(unsigned long long)      0.00192028
cpp_int        128       %                                  0.0481508
cpp_int        128       |                                  0.00991773
cpp_int        128       &                                  0.0101034
cpp_int        128       ^                                  0.0101384
cpp_int        128       <<                                 0.0116142
cpp_int        128       >>                                 0.011709
cpp_int        128       %(int)                             0.00790481
cpp_int        128       |(int)                             0.00805945
cpp_int        128       &(int)                             0.0124861
cpp_int        128       ^(int)                             0.00991885
cpp_int        128       gcd                                0.358587
cpp_int        128       powm                               0.565871
cpp_int        128       +(unsigned long long)              0.00893714
cpp_int        128       -(unsigned long long)              0.00807189
cpp_int        128       *(unsigned long long)              0.00669928
cpp_int        128       /(unsigned long long)              0.0582351
cpp_int        128       +=(unsigned long long)             0.0106404
cpp_int        128       -=(unsigned long long)             0.0103583
cpp_int        128       *=(unsigned long long)             0.0131299
cpp_int        128       /=(unsigned long long)             0.0701172
cpp_int        256       +                                  0.0160922
cpp_int        256       -                                  0.013219
cpp_int        256       *                                  0.0246772
cpp_int        256       /                                  0.181536
cpp_int        256       str                                0.000644609
cpp_int        256       +(int)                             0.0063589
cpp_int        256       -(int)                             0.00531251
cpp_int        256       *(int)                             0.00991594
cpp_int        256       /(int)                             0.108097
cpp_int        256       construct                          0.00258002
cpp_int        256       construct(unsigned)                0.00225895
cpp_int        256       construct(unsigned long long)      0.00200418
cpp_int        256       %                                  0.0825917
cpp_int        256       |                                  0.00939722
cpp_int        256       &                                  0.0128886
cpp_int        256       ^                                  0.0282768
cpp_int        256       <<                                 0.0147029
cpp_int        256       >>                                 0.0153366
cpp_int        256       %(int)                             0.0215141
cpp_int        256       |(int)                             0.0119795
cpp_int        256       &(int)                             0.0114113
cpp_int        256       ^(int)                             0.0115174
cpp_int        256       gcd                                1.42989
cpp_int        256       powm                               2.0199
cpp_int        256       +(unsigned long long)              0.0136811
cpp_int        256       -(unsigned long long)              0.00922027
cpp_int        256       *(unsigned long long)              0.00820918
cpp_int        256       /(unsigned long long)              0.0982056
cpp_int        256       +=(unsigned long long)             0.00996803
cpp_int        256       -=(unsigned long long)             0.0102937
cpp_int        256       *=(unsigned long long)             0.00790233
cpp_int        256       /=(unsigned long long)             0.105309
cpp_int        512       +                                  0.0131305
cpp_int        512       -                                  0.0117779
cpp_int        512       *                                  0.0631704
cpp_int        512       /                                  0.250544
cpp_int        512       str                                0.00141073
cpp_int        512       +(int)                             0.00666443
cpp_int        512       -(int)                             0.00548206
cpp_int        512       *(int)                             0.0194072
cpp_int        512       /(int)                             0.193637
cpp_int        512       construct                          0.00248269
cpp_int        512       construct(unsigned)                0.00241092
cpp_int        512       construct(unsigned long long)      0.00223886
cpp_int        512       %                                  0.127209
cpp_int        512       |                                  0.012635
cpp_int        512       &                                  0.018172
cpp_int        512       ^                                  0.0139063
cpp_int        512       <<                                 0.0238748
cpp_int        512       >>                                 0.0209131
cpp_int        512       %(int)                             0.065874
cpp_int        512       |(int)                             0.012189
cpp_int        512       &(int)                             0.0179186
cpp_int        512       ^(int)                             0.0198209
cpp_int        512       gcd                                3.30534
cpp_int        512       powm                               9.06469
cpp_int        512       +(unsigned long long)              0.0102989
cpp_int        512       -(unsigned long long)              0.00830959
cpp_int        512       *(unsigned long long)              0.0119122
cpp_int        512       /(unsigned long long)              0.151642
cpp_int        512       +=(unsigned long long)             0.0108279
cpp_int        512       -=(unsigned long long)             0.0103591
cpp_int        512       *=(unsigned long long)             0.0119079
cpp_int        512       /=(unsigned long long)             0.171387
cpp_int        1024      +                                  0.0163002
cpp_int        1024      -                                  0.0151597
cpp_int        1024      *                                  0.224062
cpp_int        1024      /                                  0.365546
cpp_int        1024      str                                0.00383604
cpp_int        1024      +(int)                             0.00836612
cpp_int        1024      -(int)                             0.00759591
cpp_int        1024      *(int)                             0.0214459
cpp_int        1024      /(int)                             0.296939
cpp_int        1024      construct                          0.00191598
cpp_int        1024      construct(unsigned)                0.00177047
cpp_int        1024      construct(unsigned long long)      0.00189442
cpp_int        1024      %                                  0.171986
cpp_int        1024      |                                  0.0152013
cpp_int        1024      &                                  0.0204051
cpp_int        1024      ^                                  0.0187085
cpp_int        1024      <<                                 0.0394659
cpp_int        1024      >>                                 0.0146098
cpp_int        1024      %(int)                             0.1044
cpp_int        1024      |(int)                             0.0134288
cpp_int        1024      &(int)                             0.0262296
cpp_int        1024      ^(int)                             0.0153479
cpp_int        1024      gcd                                7.01715
cpp_int        1024      powm                               46.9932
cpp_int        1024      +(unsigned long long)              0.0115471
cpp_int        1024      -(unsigned long long)              0.00988039
cpp_int        1024      *(unsigned long long)              0.0175683
cpp_int        1024      /(unsigned long long)              0.281298
cpp_int        1024      +=(unsigned long long)             0.0131949
cpp_int        1024      -=(unsigned long long)             0.0189898
cpp_int        1024      *=(unsigned long long)             0.0203561
cpp_int        1024      /=(unsigned long long)             0.299993
cpp_rational   128       +                                  0.415006
cpp_rational   128       -                                  0.471759
cpp_rational   128       *                                  0.797425
cpp_rational   128       /                                  2.28881
cpp_rational   128       str                                0.00168424
cpp_rational   128       +(int)                             0.0177811
cpp_rational   128       -(int)                             0.0292894
cpp_rational   128       *(int)                             0.0637195
cpp_rational   128       /(int)                             0.035134
cpp_rational   128       construct                          0.0135822
cpp_rational   128       construct(unsigned)                0.00672081
cpp_rational   128       construct(unsigned long long)      0.00806026
cpp_rational   128       +(value_type)                      0.0265647
cpp_rational   128       -(value_type)                      0.0408789
cpp_rational   128       *(value_type)                      0.408791
cpp_rational   128       /(value_type)                      0.431612
cpp_rational   128       +=(value_type)                     0.0335896
cpp_rational   128       -=(value_type)                     0.0421283
cpp_rational   128       *=(value_type)                     0.40255
cpp_rational   128       /=(value_type)                     0.40369
cpp_rational   128       +(unsigned long long)              0.0177151
cpp_rational   128       -(unsigned long long)              0.0296698
cpp_rational   128       *(unsigned long long)              0.161844
cpp_rational   128       /(unsigned long long)              0.170727
cpp_rational   128       +=(unsigned long long)             0.0565983
cpp_rational   128       -=(unsigned long long)             0.0366534
cpp_rational   128       *=(unsigned long long)             0.211848
cpp_rational   128       /=(unsigned long long)             0.216252
cpp_rational   256       +                                  1.53458
cpp_rational   256       -                                  1.52484
cpp_rational   256       *                                  2.96998
cpp_rational   256       /                                  6.34454
cpp_rational   256       str                                0.0033367
cpp_rational   256       +(int)                             0.0183744
cpp_rational   256       -(int)                             0.0346718
cpp_rational   256       *(int)                             0.0917847
cpp_rational   256       /(int)                             0.0774619
cpp_rational   256       construct                          0.00935293
cpp_rational   256       construct(unsigned)                0.0064826
cpp_rational   256       construct(unsigned long long)      0.00960336
cpp_rational   256       +(value_type)                      0.0391054
cpp_rational   256       -(value_type)                      0.0467416
cpp_rational   256       *(value_type)                      0.600225
cpp_rational   256       /(value_type)                      0.596248
cpp_rational   256       +=(value_type)                     0.0422867
cpp_rational   256       -=(value_type)                     0.0490152
cpp_rational   256       *=(value_type)                     0.629302
cpp_rational   256       /=(value_type)                     0.689514
cpp_rational   256       +(unsigned long long)              0.0241089
cpp_rational   256       -(unsigned long long)              0.0496956
cpp_rational   256       *(unsigned long long)              0.240069
cpp_rational   256       /(unsigned long long)              0.216019
cpp_rational   256       +=(unsigned long long)             0.0371419
cpp_rational   256       -=(unsigned long long)             0.0439181
cpp_rational   256       *=(unsigned long long)             0.226179
cpp_rational   256       /=(unsigned long long)             0.235781
cpp_rational   512       +                                  3.41194
cpp_rational   512       -                                  3.49648
cpp_rational   512       *                                  6.73224
cpp_rational   512       /                                  13.2036
cpp_rational   512       str                                0.00662873
cpp_rational   512       +(int)                             0.020452
cpp_rational   512       -(int)                             0.035503
cpp_rational   512       *(int)                             0.118274
cpp_rational   512       /(int)                             0.104628
cpp_rational   512       construct                          0.0083784
cpp_rational   512       construct(unsigned)                0.00618635
cpp_rational   512       construct(unsigned long long)      0.00769898
cpp_rational   512       +(value_type)                      0.044067
cpp_rational   512       -(value_type)                      0.0548939
cpp_rational   512       *(value_type)                      0.803009
cpp_rational   512       /(value_type)                      0.809662
cpp_rational   512       +=(value_type)                     0.0564267
cpp_rational   512       -=(value_type)                     0.053198
cpp_rational   512       *=(value_type)                     0.8029
cpp_rational   512       /=(value_type)                     0.832288
cpp_rational   512       +(unsigned long long)              0.0297836
cpp_rational   512       -(unsigned long long)              0.0461985
cpp_rational   512       *(unsigned long long)              0.298935
cpp_rational   512       /(unsigned long long)              0.292536
cpp_rational   512       +=(unsigned long long)             0.0556987
cpp_rational   512       -=(unsigned long long)             0.0491612
cpp_rational   512       *=(unsigned long long)             0.319695
cpp_rational   512       /=(unsigned long long)             0.314161
cpp_rational   1024      +                                  8.04044
cpp_rational   1024      -                                  7.71926
cpp_rational   1024      *                                  14.4259
cpp_rational   1024      /                                  29.3236
cpp_rational   1024      str                                0.0174979
cpp_rational   1024      +(int)                             0.0449351
cpp_rational   1024      -(int)                             0.0577029
cpp_rational   1024      *(int)                             0.218283
cpp_rational   1024      /(int)                             0.207067
cpp_rational   1024      construct                          0.00962697
cpp_rational   1024      construct(unsigned)                0.00923644
cpp_rational   1024      construct(unsigned long long)      0.0176689
cpp_rational   1024      +(value_type)                      0.105801
cpp_rational   1024      -(value_type)                      0.103471
cpp_rational   1024      *(value_type)                      1.54645
cpp_rational   1024      /(value_type)                      1.38001
cpp_rational   1024      +=(value_type)                     0.0840696
cpp_rational   1024      -=(value_type)                     0.0881228
cpp_rational   1024      *=(value_type)                     1.37083
cpp_rational   1024      /=(value_type)                     1.37478
cpp_rational   1024      +(unsigned long long)              0.046594
cpp_rational   1024      -(unsigned long long)              0.0581714
cpp_rational   1024      *(unsigned long long)              0.448194
cpp_rational   1024      /(unsigned long long)              0.435259
cpp_rational   1024      +=(unsigned long long)             0.0662456
cpp_rational   1024      -=(unsigned long long)             0.071761
cpp_rational   1024      *=(unsigned long long)             0.485819
cpp_rational   1024      /=(unsigned long long)             0.460033
mpq_rational   128       +                                  0.188327
mpq_rational   128       -                                  0.198113
mpq_rational   128       *                                  0.398662
mpq_rational   128       /                                  1.12509
mpq_rational   128       str                                0.000272779
mpq_rational   128       +(int)                             0.00859669
mpq_rational   128       -(int)                             0.0119819
mpq_rational   128       *(int)                             0.0338803
mpq_rational   128       /(int)                             0.0650149
mpq_rational   128       construct                          0.0195942
mpq_rational   128       construct(unsigned)                0.0434929
mpq_rational   128       construct(unsigned long long)      0.0392716
mpq_rational   128       +(value_type)                      0.0207456
mpq_rational   128       -(value_type)                      0.0241541
mpq_rational   128       *(value_type)                      0.20698
mpq_rational   128       /(value_type)                      0.236534
mpq_rational   128       +=(value_type)                     0.0174923
mpq_rational   128       -=(value_type)                     0.023896
mpq_rational   128       *=(value_type)                     0.206213
mpq_rational   128       /=(value_type)                     0.233487
mpq_rational   128       +(unsigned long long)              0.0085503
mpq_rational   128       -(unsigned long long)              0.0101037
mpq_rational   128       *(unsigned long long)              0.100601
mpq_rational   128       /(unsigned long long)              0.129932
mpq_rational   128       +=(unsigned long long)             0.00775585
mpq_rational   128       -=(unsigned long long)             0.00991947
mpq_rational   128       *=(unsigned long long)             0.107424
mpq_rational   128       /=(unsigned long long)             0.136121
mpq_rational   256       +                                  0.776716
mpq_rational   256       -                                  0.791584
mpq_rational   256       *                                  1.50207
mpq_rational   256       /                                  2.82848
mpq_rational   256       str                                0.000434761
mpq_rational   256       +(int)                             0.0101891
mpq_rational   256       -(int)                             0.013618
mpq_rational   256       *(int)                             0.0475114
mpq_rational   256       /(int)                             0.0713565
mpq_rational   256       construct                          0.0204126
mpq_rational   256       construct(unsigned)                0.0485316
mpq_rational   256       construct(unsigned long long)      0.0568506
mpq_rational   256       +(value_type)                      0.0245401
mpq_rational   256       -(value_type)                      0.030741
mpq_rational   256       *(value_type)                      0.247956
mpq_rational   256       /(value_type)                      0.270701
mpq_rational   256       +=(value_type)                     0.0202988
mpq_rational   256       -=(value_type)                     0.0240502
mpq_rational   256       *=(value_type)                     0.241165
mpq_rational   256       /=(value_type)                     0.269395
mpq_rational   256       +(unsigned long long)              0.0104799
mpq_rational   256       -(unsigned long long)              0.0117459
mpq_rational   256       *(unsigned long long)              0.102844
mpq_rational   256       /(unsigned long long)              0.131923
mpq_rational   256       +=(unsigned long long)             0.00984535
mpq_rational   256       -=(unsigned long long)             0.0117724
mpq_rational   256       *=(unsigned long long)             0.103523
mpq_rational   256       /=(unsigned long long)             0.137652
mpq_rational   512       +                                  1.93921
mpq_rational   512       -                                  1.98459
mpq_rational   512       *                                  3.60314
mpq_rational   512       /                                  6.22726
mpq_rational   512       str                                0.00103828
mpq_rational   512       +(int)                             0.014773
mpq_rational   512       -(int)                             0.0238773
mpq_rational   512       *(int)                             0.0548556
mpq_rational   512       /(int)                             0.0961679
mpq_rational   512       construct                          0.0221797
mpq_rational   512       construct(unsigned)                0.0513108
mpq_rational   512       construct(unsigned long long)      0.0695842
mpq_rational   512       +(value_type)                      0.0348044
mpq_rational   512       -(value_type)                      0.0417365
mpq_rational   512       *(value_type)                      0.302865
mpq_rational   512       /(value_type)                      0.329335
mpq_rational   512       +=(value_type)                     0.0355556
mpq_rational   512       -=(value_type)                     0.0287769
mpq_rational   512       *=(value_type)                     0.282857
mpq_rational   512       /=(value_type)                     0.333863
mpq_rational   512       +(unsigned long long)              0.016419
mpq_rational   512       -(unsigned long long)              0.0274924
mpq_rational   512       *(unsigned long long)              0.158713
mpq_rational   512       /(unsigned long long)              0.173528
mpq_rational   512       +=(unsigned long long)             0.0204678
mpq_rational   512       -=(unsigned long long)             0.0267879
mpq_rational   512       *=(unsigned long long)             0.131194
mpq_rational   512       /=(unsigned long long)             0.176279
mpq_rational   1024      +                                  3.79923
mpq_rational   1024      -                                  3.79396
mpq_rational   1024      *                                  7.3374
mpq_rational   1024      /                                  12.4178
mpq_rational   1024      str                                0.00192772
mpq_rational   1024      +(int)                             0.024704
mpq_rational   1024      -(int)                             0.0295391
mpq_rational   1024      *(int)                             0.0900656
mpq_rational   1024      /(int)                             0.16035
mpq_rational   1024      construct                          0.0277033
mpq_rational   1024      construct(unsigned)                0.0533278
mpq_rational   1024      construct(unsigned long long)      0.0650815
mpq_rational   1024      +(value_type)                      0.0541719
mpq_rational   1024      -(value_type)                      0.0478777
mpq_rational   1024      *(value_type)                      0.332089
mpq_rational   1024      /(value_type)                      0.355055
mpq_rational   1024      +=(value_type)                     0.0453556
mpq_rational   1024      -=(value_type)                     0.041857
mpq_rational   1024      *=(value_type)                     0.31582
mpq_rational   1024      /=(value_type)                     0.391409
mpq_rational   1024      +(unsigned long long)              0.0270577
mpq_rational   1024      -(unsigned long long)              0.040243
mpq_rational   1024      *(unsigned long long)              0.16579
mpq_rational   1024      /(unsigned long long)              0.246334
mpq_rational   1024      +=(unsigned long long)             0.032877
mpq_rational   1024      -=(unsigned long long)             0.0384444
mpq_rational   1024      *=(unsigned long long)             0.179978
mpq_rational   1024      /=(unsigned long long)             0.231505
tommath_int    128       +                                  0.0119929
tommath_int    128       -                                  0.0179911
tommath_int    128       *                                  0.0198027
tommath_int    128       /                                  1.11768
tommath_int    128       str                                0.00299152
tommath_int    128       +(int)                             0.0889366
tommath_int    128       -(int)                             0.0917047
tommath_int    128       *(int)                             0.103447
tommath_int    128       /(int)                             0.69963
tommath_int    128       construct                          0.147542
tommath_int    128       construct(unsigned)                0.180915
tommath_int    128       construct(unsigned long long)      0.165695
tommath_int    128       %                                  0.874748
tommath_int    128       |                                  0.00739324
tommath_int    128       &                                  0.00780901
tommath_int    128       ^                                  0.00825059
tommath_int    128       <<                                 0.0126218
tommath_int    128       >>                                 0.0959319
tommath_int    128       %(int)                             0.545134
tommath_int    128       |(int)                             0.0839291
tommath_int    128       &(int)                             0.0965583
tommath_int    128       ^(int)                             0.0968501
tommath_int    128       gcd                                1.36458
tommath_int    128       powm                               0.421869
tommath_int    128       +(unsigned long long)              0.0907784
tommath_int    128       -(unsigned long long)              0.0828488
tommath_int    128       *(unsigned long long)              0.107341
tommath_int    128       /(unsigned long long)              0.604848
tommath_int    128       +=(unsigned long long)             0.0768906
tommath_int    128       -=(unsigned long long)             0.0847801
tommath_int    128       *=(unsigned long long)             0.0959064
tommath_int    128       /=(unsigned long long)             0.619583
tommath_int    256       +                                  0.00709542
tommath_int    256       -                                  0.011194
tommath_int    256       *                                  0.0326465
tommath_int    256       /                                  0.989116
tommath_int    256       str                                0.00597113
tommath_int    256       +(int)                             0.0832749
tommath_int    256       -(int)                             0.0820224
tommath_int    256       *(int)                             0.0942242
tommath_int    256       /(int)                             0.769191
tommath_int    256       construct                          0.154596
tommath_int    256       construct(unsigned)                0.190967
tommath_int    256       construct(unsigned long long)      0.166489
tommath_int    256       %                                  1.03711
tommath_int    256       |                                  0.00891185
tommath_int    256       &                                  0.00928657
tommath_int    256       ^                                  0.0113947
tommath_int    256       <<                                 0.0201243
tommath_int    256       >>                                 0.0951565
tommath_int    256       %(int)                             0.807325
tommath_int    256       |(int)                             0.102949
tommath_int    256       &(int)                             0.0990734
tommath_int    256       ^(int)                             0.107261
tommath_int    256       gcd                                3.38266
tommath_int    256       powm                               1.29063
tommath_int    256       +(unsigned long long)              0.0981367
tommath_int    256       -(unsigned long long)              0.0941371
tommath_int    256       *(unsigned long long)              0.109883
tommath_int    256       /(unsigned long long)              0.876321
tommath_int    256       +=(unsigned long long)             0.0951277
tommath_int    256       -=(unsigned long long)             0.0957029
tommath_int    256       *=(unsigned long long)             0.117129
tommath_int    256       /=(unsigned long long)             0.964961
tommath_int    512       +                                  0.0133422
tommath_int    512       -                                  0.017801
tommath_int    512       *                                  0.0847288
tommath_int    512       /                                  1.38686
tommath_int    512       str                                0.0190933
tommath_int    512       +(int)                             0.0870848
tommath_int    512       -(int)                             0.0820267
tommath_int    512       *(int)                             0.0973642
tommath_int    512       /(int)                             0.938285
tommath_int    512       construct                          0.13579
tommath_int    512       construct(unsigned)                0.157218
tommath_int    512       construct(unsigned long long)      0.155821
tommath_int    512       %                                  1.29969
tommath_int    512       |                                  0.0112155
tommath_int    512       &                                  0.0113032
tommath_int    512       ^                                  0.0126854
tommath_int    512       <<                                 0.0215291
tommath_int    512       >>                                 0.0932504
tommath_int    512       %(int)                             0.930702
tommath_int    512       |(int)                             0.0775007
tommath_int    512       &(int)                             0.0815778
tommath_int    512       ^(int)                             0.079649
tommath_int    512       gcd                                7.37824
tommath_int    512       powm                               3.21051
tommath_int    512       +(unsigned long long)              0.0860324
tommath_int    512       -(unsigned long long)              0.0825755
tommath_int    512       *(unsigned long long)              0.107907
tommath_int    512       /(unsigned long long)              1.07209
tommath_int    512       +=(unsigned long long)             0.0792569
tommath_int    512       -=(unsigned long long)             0.0820388
tommath_int    512       *=(unsigned long long)             0.114277
tommath_int    512       /=(unsigned long long)             1.00956
tommath_int    1024      +                                  0.0143218
tommath_int    1024      -                                  0.0192393
tommath_int    1024      *                                  0.168568
tommath_int    1024      /                                  1.84837
tommath_int    1024      str                                0.0582251
tommath_int    1024      +(int)                             0.0809152
tommath_int    1024      -(int)                             0.0915478
tommath_int    1024      *(int)                             0.110883
tommath_int    1024      /(int)                             1.48179
tommath_int    1024      construct                          0.128148
tommath_int    1024      construct(unsigned)                0.150237
tommath_int    1024      construct(unsigned long long)      0.143768
tommath_int    1024      %                                  1.75398
tommath_int    1024      |                                  0.0160686
tommath_int    1024      &                                  0.0244762
tommath_int    1024      ^                                  0.0195826
tommath_int    1024      <<                                 0.0361972
tommath_int    1024      >>                                 0.0955953
tommath_int    1024      %(int)                             1.36603
tommath_int    1024      |(int)                             0.0767652
tommath_int    1024      &(int)                             0.0869813
tommath_int    1024      ^(int)                             0.0866224
tommath_int    1024      gcd                                15.4317
tommath_int    1024      powm                               12.628
tommath_int    1024      +(unsigned long long)              0.0806864
tommath_int    1024      -(unsigned long long)              0.0801335
tommath_int    1024      *(unsigned long long)              0.118102
tommath_int    1024      /(unsigned long long)              1.69916
tommath_int    1024      +=(unsigned long long)             0.0929332
tommath_int    1024      -=(unsigned long long)             0.100611
tommath_int    1024      *=(unsigned long long)             0.152996
tommath_int    1024      /=(unsigned long long)             1.68307
cpp_dec_float  50        +                                  0.0198859
cpp_dec_float  50        -                                  0.0194363
cpp_dec_float  50        *                                  0.061884
cpp_dec_float  50        /                                  0.83092
cpp_dec_float  50        str                                0.00145276
cpp_dec_float  50        +(int)                             0.0143136
cpp_dec_float  50        -(int)                             0.0216737
cpp_dec_float  50        *(int)                             0.0630393
cpp_dec_float  50        /(int)                             0.40163
cpp_dec_float  50        construct                          0.00383901
cpp_dec_float  50        construct(unsigned)                0.00770653
cpp_dec_float  50        construct(unsigned long long)      0.00840151
cpp_dec_float  50        +(unsigned long long)              0.0231609
cpp_dec_float  50        -(unsigned long long)              0.0248419
cpp_dec_float  50        *(unsigned long long)              0.0745654
cpp_dec_float  50        /(unsigned long long)              0.396014
cpp_dec_float  50        +=(unsigned long long)             0.017568
cpp_dec_float  50        -=(unsigned long long)             0.0249846
cpp_dec_float  50        *=(unsigned long long)             0.0706828
cpp_dec_float  50        /=(unsigned long long)             0.408938
cpp_dec_float  100       +                                  0.0216812
cpp_dec_float  100       -                                  0.0313385
cpp_dec_float  100       *                                  0.130292
cpp_dec_float  100       /                                  1.73437
cpp_dec_float  100       str                                0.00180621
cpp_dec_float  100       +(int)                             0.0169356
cpp_dec_float  100       -(int)                             0.0263524
cpp_dec_float  100       *(int)                             0.125171
cpp_dec_float  100       /(int)                             0.861622
cpp_dec_float  100       construct                          0.00443324
cpp_dec_float  100       construct(unsigned)                0.00867913
cpp_dec_float  100       construct(unsigned long long)      0.00854971
cpp_dec_float  100       +(unsigned long long)              0.0292658
cpp_dec_float  100       -(unsigned long long)              0.0287785
cpp_dec_float  100       *(unsigned long long)              0.147631
cpp_dec_float  100       /(unsigned long long)              0.991001
cpp_dec_float  100       +=(unsigned long long)             0.0236933
cpp_dec_float  100       -=(unsigned long long)             0.0396995
cpp_dec_float  100       *=(unsigned long long)             0.172634
cpp_dec_float  100       /=(unsigned long long)             1.27727
cpp_dec_float  500       +                                  0.207727
cpp_dec_float  500       -                                  0.21259
cpp_dec_float  500       *                                  1.6902
cpp_dec_float  500       /                                  22.744
cpp_dec_float  500       str                                0.0109211
cpp_dec_float  500       +(int)                             0.151114
cpp_dec_float  500       -(int)                             0.14371
cpp_dec_float  500       *(int)                             2.44271
cpp_dec_float  500       /(int)                             12.8722
cpp_dec_float  500       construct                          0.0256013
cpp_dec_float  500       construct(unsigned)                0.0455813
cpp_dec_float  500       construct(unsigned long long)      0.0429815
cpp_dec_float  500       +(unsigned long long)              0.148942
cpp_dec_float  500       -(unsigned long long)              0.150724
cpp_dec_float  500       *(unsigned long long)              2.68446
cpp_dec_float  500       /(unsigned long long)              14.1836
cpp_dec_float  500       +=(unsigned long long)             0.186187
cpp_dec_float  500       -=(unsigned long long)             0.199025
cpp_dec_float  500       *=(unsigned long long)             2.40228
cpp_dec_float  500       /=(unsigned long long)             10.9233
cpp_bin_float  50        +                                  0.0479759
cpp_bin_float  50        -                                  0.0688937
cpp_bin_float  50        *                                  0.0720574
cpp_bin_float  50        /                                  0.549759
cpp_bin_float  50        str                                0.00301165
cpp_bin_float  50        +(int)                             0.0524679
cpp_bin_float  50        -(int)                             0.0848243
cpp_bin_float  50        *(int)                             0.0399504
cpp_bin_float  50        /(int)                             0.141651
cpp_bin_float  50        construct                          0.0030982
cpp_bin_float  50        construct(unsigned)                0.00808983
cpp_bin_float  50        construct(unsigned long long)      0.0175206
cpp_bin_float  50        +(unsigned long long)              0.109321
cpp_bin_float  50        -(unsigned long long)              0.102878
cpp_bin_float  50        *(unsigned long long)              0.0508387
cpp_bin_float  50        /(unsigned long long)              0.19558
cpp_bin_float  50        +=(unsigned long long)             0.109847
cpp_bin_float  50        -=(unsigned long long)             0.129938
cpp_bin_float  50        *=(unsigned long long)             0.0500366
cpp_bin_float  50        /=(unsigned long long)             0.215322
cpp_bin_float  100       +                                  0.0901201
cpp_bin_float  100       -                                  0.0896788
cpp_bin_float  100       *                                  0.111688
cpp_bin_float  100       /                                  1.39191
cpp_bin_float  100       str                                0.0051705
cpp_bin_float  100       +(int)                             0.0871804
cpp_bin_float  100       -(int)                             0.084402
cpp_bin_float  100       *(int)                             0.0464923
cpp_bin_float  100       /(int)                             0.271491
cpp_bin_float  100       construct                          0.00608929
cpp_bin_float  100       construct(unsigned)                0.0120486
cpp_bin_float  100       construct(unsigned long long)      0.0267453
cpp_bin_float  100       +(unsigned long long)              0.128103
cpp_bin_float  100       -(unsigned long long)              0.0883961
cpp_bin_float  100       *(unsigned long long)              0.0540758
cpp_bin_float  100       /(unsigned long long)              0.244599
cpp_bin_float  100       +=(unsigned long long)             0.0942496
cpp_bin_float  100       -=(unsigned long long)             0.101449
cpp_bin_float  100       *=(unsigned long long)             0.0547429
cpp_bin_float  100       /=(unsigned long long)             0.256093
cpp_bin_float  500       +                                  0.219881
cpp_bin_float  500       -                                  0.196627
cpp_bin_float  500       *                                  0.850904
cpp_bin_float  500       /                                  5.32129
cpp_bin_float  500       str                                0.043165
cpp_bin_float  500       +(int)                             0.165014
cpp_bin_float  500       -(int)                             0.181209
cpp_bin_float  500       *(int)                             0.12206
cpp_bin_float  500       /(int)                             0.643802
cpp_bin_float  500       construct                          0.0152335
cpp_bin_float  500       construct(unsigned)                0.0530205
cpp_bin_float  500       construct(unsigned long long)      0.0570308
cpp_bin_float  500       +(unsigned long long)              0.16427
cpp_bin_float  500       -(unsigned long long)              0.185162
cpp_bin_float  500       *(unsigned long long)              0.0843974
cpp_bin_float  500       /(unsigned long long)              0.621615
cpp_bin_float  500       +=(unsigned long long)             0.157162
cpp_bin_float  500       -=(unsigned long long)             0.163157
cpp_bin_float  500       *=(unsigned long long)             0.0987612
cpp_bin_float  500       /=(unsigned long long)             0.638041
mpfr_float     50        +                                  0.0106639
mpfr_float     50        -                                  0.0274747
mpfr_float     50        *                                  0.026548
mpfr_float     50        /                                  0.239385
mpfr_float     50        str                                0.000800609
mpfr_float     50        +(int)                             0.029943
mpfr_float     50        -(int)                             0.0662094
mpfr_float     50        *(int)                             0.022273
mpfr_float     50        /(int)                             0.0362858
mpfr_float     50        construct                          0.04809
mpfr_float     50        construct(unsigned)                0.0751445
mpfr_float     50        construct(unsigned long long)      0.0787188
mpfr_float     50        +(unsigned long long)              0.0240813
mpfr_float     50        -(unsigned long long)              0.0438217
mpfr_float     50        *(unsigned long long)              0.0207341
mpfr_float     50        /(unsigned long long)              0.0603899
mpfr_float     50        +=(unsigned long long)             0.0528778
mpfr_float     50        -=(unsigned long long)             0.0613088
mpfr_float     50        *=(unsigned long long)             0.0350654
mpfr_float     50        /=(unsigned long long)             0.0458197
mpfr_float     50        +                                  0.0183477
mpfr_float     50        -                                  0.0241287
mpfr_float     50        *                                  0.0161758
mpfr_float     50        /                                  0.169939
mpfr_float     50        str                                0.00227407
mpfr_float     50        +(int)                             0.0256829
mpfr_float     50        -(int)                             0.0692297
mpfr_float     50        *(int)                             0.0336498
mpfr_float     50        /(int)                             0.0549347
mpfr_float     50        construct                          0.0773304
mpfr_float     50        construct(unsigned)                0.102564
mpfr_float     50        construct(unsigned long long)      0.08974
mpfr_float     50        +(unsigned long long)              0.0261189
mpfr_float     50        -(unsigned long long)              0.0606961
mpfr_float     50        *(unsigned long long)              0.0276134
mpfr_float     50        /(unsigned long long)              0.0520857
mpfr_float     50        +=(unsigned long long)             0.0571878
mpfr_float     50        -=(unsigned long long)             0.0589902
mpfr_float     50        *=(unsigned long long)             0.0229147
mpfr_float     50        /=(unsigned long long)             0.0523141
mpfr_float     100       +                                  0.0190178
mpfr_float     100       -                                  0.0395976
mpfr_float     100       *                                  0.0661794
mpfr_float     100       /                                  0.300455
mpfr_float     100       str                                0.00132816
mpfr_float     100       +(int)                             0.0300081
mpfr_float     100       -(int)                             0.0654074
mpfr_float     100       *(int)                             0.0339721
mpfr_float     100       /(int)                             0.0592293
mpfr_float     100       construct                          0.0866815
mpfr_float     100       construct(unsigned)                0.0730204
mpfr_float     100       construct(unsigned long long)      0.0720298
mpfr_float     100       +(unsigned long long)              0.0290995
mpfr_float     100       -(unsigned long long)              0.056868
mpfr_float     100       *(unsigned long long)              0.0276341
mpfr_float     100       /(unsigned long long)              0.0628383
mpfr_float     100       +=(unsigned long long)             0.0502709
mpfr_float     100       -=(unsigned long long)             0.0731736
mpfr_float     100       *=(unsigned long long)             0.0348128
mpfr_float     100       /=(unsigned long long)             0.071302
mpfr_float     500       +                                  0.0609463
mpfr_float     500       -                                  0.0646638
mpfr_float     500       *                                  0.321884
mpfr_float     500       /                                  1.2162
mpfr_float     500       str                                0.0048926
mpfr_float     500       +(int)                             0.062913
mpfr_float     500       -(int)                             0.0711089
mpfr_float     500       *(int)                             0.0792629
mpfr_float     500       /(int)                             0.126251
mpfr_float     500       construct                          0.0762248
mpfr_float     500       construct(unsigned)                0.0813206
mpfr_float     500       construct(unsigned long long)      0.0785786
mpfr_float     500       +(unsigned long long)              0.0616622
mpfr_float     500       -(unsigned long long)              0.0905736
mpfr_float     500       *(unsigned long long)              0.0506668
mpfr_float     500       /(unsigned long long)              0.117803
mpfr_float     500       +=(unsigned long long)             0.0787754
mpfr_float     500       -=(unsigned long long)             0.105476
mpfr_float     500       *=(unsigned long long)             0.0716736
mpfr_float     500       /=(unsigned long long)             0.122477
gmp_int        2048      +                                  0.0179704
gmp_int        2048      -                                  0.017975
gmp_int        2048      *                                  0.361988
gmp_int        2048      /                                  0.149182
gmp_int        2048      str                                0.00244981
gmp_int        2048      +(int)                             0.0118266
gmp_int        2048      -(int)                             0.0117639
gmp_int        2048      *(int)                             0.0241013
gmp_int        2048      /(int)                             0.0972941
gmp_int        2048      construct                          0.00384029
gmp_int        2048      construct(unsigned)                0.0286091
gmp_int        2048      construct(unsigned long long)      0.0290565
gmp_int        2048      %                                  0.142738
gmp_int        2048      |                                  0.0152602
gmp_int        2048      &                                  0.015523
gmp_int        2048      ^                                  0.0244266
gmp_int        2048      <<                                 0.039948
gmp_int        2048      >>                                 0.00453774
gmp_int        2048      %(int)                             0.0436939
gmp_int        2048      |(int)                             0.0277685
gmp_int        2048      &(int)                             0.0176239
gmp_int        2048      ^(int)                             0.0298873
gmp_int        2048      gcd                                8.18516
gmp_int        2048      +(unsigned long long)              0.0161988
gmp_int        2048      -(unsigned long long)              0.0184622
gmp_int        2048      *(unsigned long long)              0.0209189
gmp_int        2048      /(unsigned long long)              0.0759658
gmp_int        2048      +=(unsigned long long)             0.0160693
gmp_int        2048      -=(unsigned long long)             0.0219074
gmp_int        2048      *=(unsigned long long)             0.0301309
gmp_int        2048      /=(unsigned long long)             0.0919138
gmp_int        4096      +                                  0.0300729
gmp_int        4096      -                                  0.0321962
gmp_int        4096      *                                  1.11701
gmp_int        4096      /                                  0.208719
gmp_int        4096      str                                0.00486654
gmp_int        4096      +(int)                             0.0195694
gmp_int        4096      -(int)                             0.0225873
gmp_int        4096      *(int)                             0.0477494
gmp_int        4096      /(int)                             0.156597
gmp_int        4096      construct                          0.00385483
gmp_int        4096      construct(unsigned)                0.0220052
gmp_int        4096      construct(unsigned long long)      0.0244927
gmp_int        4096      %                                  0.24073
gmp_int        4096      |                                  0.0365187
gmp_int        4096      &                                  0.0384586
gmp_int        4096      ^                                  0.0429561
gmp_int        4096      <<                                 0.0597248
gmp_int        4096      >>                                 0.00313265
gmp_int        4096      %(int)                             0.0571251
gmp_int        4096      |(int)                             0.0360071
gmp_int        4096      &(int)                             0.0121449
gmp_int        4096      ^(int)                             0.0406408
gmp_int        4096      gcd                                18.7879
gmp_int        4096      +(unsigned long long)              0.0174726
gmp_int        4096      -(unsigned long long)              0.0220616
gmp_int        4096      *(unsigned long long)              0.0321931
gmp_int        4096      /(unsigned long long)              0.125208
gmp_int        4096      +=(unsigned long long)             0.0221405
gmp_int        4096      -=(unsigned long long)             0.0250737
gmp_int        4096      *=(unsigned long long)             0.0457849
gmp_int        4096      /=(unsigned long long)             0.14699
cpp_int        2688      +                                  0.0336398
cpp_int        2688      -                                  0.037733
cpp_int        2688      *                                  1.31825
cpp_int        2688      /                                  1.00586
cpp_int        2688      str                                0.0237422
cpp_int        2688      +(int)                             0.024805
cpp_int        2688      -(int)                             0.032706
cpp_int        2688      *(int)                             0.0426049
cpp_int        2688      /(int)                             0.859876
cpp_int        2688      construct                          0.00240729
cpp_int        2688      construct(unsigned)                0.00241711
cpp_int        2688      construct(unsigned long long)      0.00288069
cpp_int        2688      %                                  0.398213
cpp_int        2688      |                                  0.0334167
cpp_int        2688      &                                  0.0366949
cpp_int        2688      ^                                  0.0475314
cpp_int        2688      <<                                 0.0753453
cpp_int        2688      >>                                 0.0183679
cpp_int        2688      %(int)                             0.301827
cpp_int        2688      |(int)                             0.0214828
cpp_int        2688      &(int)                             0.0612567
cpp_int        2688      ^(int)                             0.0206665
cpp_int        2688      gcd                                21.9738
cpp_int        2688      +(unsigned long long)              0.0294774
cpp_int        2688      -(unsigned long long)              0.0222548
cpp_int        2688      *(unsigned long long)              0.052584
cpp_int        2688      /(unsigned long long)              0.832241
cpp_int        2688      +=(unsigned long long)             0.0201879
cpp_int        2688      -=(unsigned long long)             0.0277295
cpp_int        2688      *=(unsigned long long)             0.0527845
cpp_int        2688      /=(unsigned long long)             0.873081
cpp_int        2048      +                                  0.033949
cpp_int        2048      -                                  0.0507822
cpp_int        2048      *                                  0.918685
cpp_int        2048      /                                  0.702366
cpp_int        2048      str                                0.0137593
cpp_int        2048      +(int)                             0.0204874
cpp_int        2048      -(int)                             0.0176467
cpp_int        2048      *(int)                             0.047049
cpp_int        2048      /(int)                             0.630264
cpp_int        2048      construct                          0.00273948
cpp_int        2048      construct(unsigned)                0.00304865
cpp_int        2048      construct(unsigned long long)      0.00190833
cpp_int        2048      %                                  0.368469
cpp_int        2048      |                                  0.0293243
cpp_int        2048      &                                  0.0461406
cpp_int        2048      ^                                  0.0405646
cpp_int        2048      <<                                 0.0859331
cpp_int        2048      >>                                 0.0183489
cpp_int        2048      %(int)                             0.288068
cpp_int        2048      |(int)                             0.0309032
cpp_int        2048      &(int)                             0.063826
cpp_int        2048      ^(int)                             0.0264078
cpp_int        2048      gcd                                17.7583
cpp_int        2048      +(unsigned long long)              0.0237404
cpp_int        2048      -(unsigned long long)              0.0235442
cpp_int        2048      *(unsigned long long)              0.0539343
cpp_int        2048      /(unsigned long long)              0.706562
cpp_int        2048      +=(unsigned long long)             0.0186902
cpp_int        2048      -=(unsigned long long)             0.0265754
cpp_int        2048      *=(unsigned long long)             0.0373067
cpp_int        2048      /=(unsigned long long)             0.632889
tommath_int    2048      +                                  0.0211244
tommath_int    2048      -                                  0.0279804
tommath_int    2048      *                                  0.460786
tommath_int    2048      /                                  3.07934
tommath_int    2048      str                                0.202054
tommath_int    2048      +(int)                             0.083639
tommath_int    2048      -(int)                             0.0851246
tommath_int    2048      *(int)                             0.135402
tommath_int    2048      /(int)                             2.60921
tommath_int    2048      construct                          0.130974
tommath_int    2048      construct(unsigned)                0.143494
tommath_int    2048      construct(unsigned long long)      0.163349
tommath_int    2048      %                                  3.33706
tommath_int    2048      |                                  0.0267336
tommath_int    2048      &                                  0.0364387
tommath_int    2048      ^                                  0.0346198
tommath_int    2048      <<                                 0.0693055
tommath_int    2048      >>                                 0.134364
tommath_int    2048      %(int)                             2.60834
tommath_int    2048      |(int)                             0.0913086
tommath_int    2048      &(int)                             0.101302
tommath_int    2048      ^(int)                             0.102478
tommath_int    2048      gcd                                42.8729
tommath_int    2048      +(unsigned long long)              0.0926177
tommath_int    2048      -(unsigned long long)              0.100113
tommath_int    2048      *(unsigned long long)              0.169084
tommath_int    2048      /(unsigned long long)              3.12331
tommath_int    2048      +=(unsigned long long)             0.110375
tommath_int    2048      -=(unsigned long long)             0.105886
tommath_int    2048      *=(unsigned long long)             0.171704
tommath_int    2048      /=(unsigned long long)             3.0036
tommath_int    4096      +                                  0.0326177
tommath_int    4096      -                                  0.0532653
tommath_int    4096      *                                  1.58255
tommath_int    4096      /                                  6.59216
tommath_int    4096      str                                0.754295
tommath_int    4096      +(int)                             0.0938225
tommath_int    4096      -(int)                             0.101291
tommath_int    4096      *(int)                             0.196512
tommath_int    4096      /(int)                             5.83504
tommath_int    4096      construct                          0.13198
tommath_int    4096      construct(unsigned)                0.143689
tommath_int    4096      construct(unsigned long long)      0.146389
tommath_int    4096      %                                  6.41658
tommath_int    4096      |                                  0.0481236
tommath_int    4096      &                                  0.0552444
tommath_int    4096      ^                                  0.0568037
tommath_int    4096      <<                                 0.115375
tommath_int    4096      >>                                 0.196446
tommath_int    4096      %(int)                             5.91797
tommath_int    4096      |(int)                             0.109494
tommath_int    4096      &(int)                             0.128712
tommath_int    4096      ^(int)                             0.131519
tommath_int    4096      gcd                                136.362
tommath_int    4096      +(unsigned long long)              0.108939
tommath_int    4096      -(unsigned long long)              0.101139
tommath_int    4096      *(unsigned long long)              0.220461
tommath_int    4096      /(unsigned long long)              7.25882
tommath_int    4096      +=(unsigned long long)             0.11456
tommath_int    4096      -=(unsigned long long)             0.117418
tommath_int    4096      *=(unsigned long long)             0.250847
tommath_int    4096      /=(unsigned long long)             7.09868
cpp_int        4096      +                                  0.0412289
cpp_int        4096      -                                  0.0463464
cpp_int        4096      *                                  2.65154
cpp_int        4096      /                                  1.18106
cpp_int        4096      str                                0.0491109
cpp_int        4096      +(int)                             0.0198385
cpp_int        4096      -(int)                             0.0209158
cpp_int        4096      *(int)                             0.0576663
cpp_int        4096      /(int)                             1.08346
cpp_int        4096      construct                          0.0017072
cpp_int        4096      construct(unsigned)                0.0018378
cpp_int        4096      construct(unsigned long long)      0.00199036
cpp_int        4096      %                                  0.512768
cpp_int        4096      |                                  0.0377549
cpp_int        4096      &                                  0.0440714
cpp_int        4096      ^                                  0.0369865
cpp_int        4096      <<                                 0.0889652
cpp_int        4096      >>                                 0.0219967
cpp_int        4096      %(int)                             0.429244
cpp_int        4096      |(int)                             0.0232284
cpp_int        4096      &(int)                             0.0593859
cpp_int        4096      ^(int)                             0.0235546
cpp_int        4096      gcd                                32.2572
cpp_int        4096      +(unsigned long long)              0.0253811
cpp_int        4096      -(unsigned long long)              0.0242017
cpp_int        4096      *(unsigned long long)              0.0432066
cpp_int        4096      /(unsigned long long)              1.0584
cpp_int        4096      +=(unsigned long long)             0.0218483
cpp_int        4096      -=(unsigned long long)             0.0249248
cpp_int        4096      *=(unsigned long long)             0.0424701
cpp_int        4096      /=(unsigned long long)             1.05399
cpp_int        8192      +                                  0.083134
cpp_int        8192      -                                  0.0813138
cpp_int        8192      *                                  7.83314
cpp_int        8192      /                                  2.26453
cpp_int        8192      str                                0.171316
cpp_int        8192      +(int)                             0.0338486
cpp_int        8192      -(int)                             0.0402632
cpp_int        8192      *(int)                             0.0955853
cpp_int        8192      /(int)                             2.33772
cpp_int        8192      construct                          0.00191549
cpp_int        8192      construct(unsigned)                0.0017583
cpp_int        8192      construct(unsigned long long)      0.00200998
cpp_int        8192      %                                  1.03083
cpp_int        8192      |                                  0.0916779
cpp_int        8192      &                                  0.0811524
cpp_int        8192      ^                                  0.0918676
cpp_int        8192      <<                                 0.190562
cpp_int        8192      >>                                 0.0537598
cpp_int        8192      %(int)                             0.851447
cpp_int        8192      |(int)                             0.042441
cpp_int        8192      &(int)                             0.102968
cpp_int        8192      ^(int)                             0.0392727
cpp_int        8192      gcd                                76.6459
cpp_int        8192      +(unsigned long long)              0.0350422
cpp_int        8192      -(unsigned long long)              0.0317445
cpp_int        8192      *(unsigned long long)              0.0813634
cpp_int        8192      /(unsigned long long)              2.14867
cpp_int        8192      +=(unsigned long long)             0.0394761
cpp_int        8192      -=(unsigned long long)             0.0377524
cpp_int        8192      *=(unsigned long long)             0.0946934
cpp_int        8192      /=(unsigned long long)             2.14442
cpp_int        16384     +                                  0.190174
cpp_int        16384     -                                  0.191562
cpp_int        16384     *                                  26.1836
cpp_int        16384     /                                  4.52755
cpp_int        16384     str                                0.595522
cpp_int        16384     +(int)                             0.0760202
cpp_int        16384     -(int)                             0.0674681
cpp_int        16384     *(int)                             0.167493
cpp_int        16384     /(int)                             4.22641
cpp_int        16384     construct                          0.0016635
cpp_int        16384     construct(unsigned)                0.00197229
cpp_int        16384     construct(unsigned long long)      0.00168004
cpp_int        16384     %                                  1.96988
cpp_int        16384     |                                  0.152323
cpp_int        16384     &                                  0.146531
cpp_int        16384     ^                                  0.150955
cpp_int        16384     <<                                 0.32803
cpp_int        16384     >>                                 0.0745484
cpp_int        16384     %(int)                             1.57951
cpp_int        16384     |(int)                             0.0711061
cpp_int        16384     &(int)                             0.168385
cpp_int        16384     ^(int)                             0.0657809
cpp_int        16384     gcd                                201.791
cpp_int        16384     +(unsigned long long)              0.0761856
cpp_int        16384     -(unsigned long long)              0.0783054
cpp_int        16384     *(unsigned long long)              0.159452
cpp_int        16384     /(unsigned long long)              4.2547
cpp_int        16384     +=(unsigned long long)             0.0783171
cpp_int        16384     -=(unsigned long long)             0.070851
cpp_int        16384     *=(unsigned long long)             0.177219
cpp_int        16384     /=(unsigned long long)             4.37618
tommath_int    8192      +                                  0.060658
tommath_int    8192      -                                  0.0884881
tommath_int    8192      *                                  7.13744
tommath_int    8192      /                                  17.0525
tommath_int    8192      str                                2.82013
tommath_int    8192      +(int)                             0.11778
tommath_int    8192      -(int)                             0.115431
tommath_int    8192      *(int)                             0.30587
tommath_int    8192      /(int)                             15.4756
tommath_int    8192      construct                          0.126578
tommath_int    8192      construct(unsigned)                0.144368
tommath_int    8192      construct(unsigned long long)      0.139445
tommath_int    8192      %                                  17.1548
tommath_int    8192      |                                  0.0880424
tommath_int    8192      &                                  0.10108
tommath_int    8192      ^                                  0.0996668
tommath_int    8192      <<                                 0.222116
tommath_int    8192      >>                                 0.358689
tommath_int    8192      %(int)                             16.0225
tommath_int    8192      |(int)                             0.150489
tommath_int    8192      &(int)                             0.18102
tommath_int    8192      ^(int)                             0.190136
tommath_int    8192      gcd                                471.81
tommath_int    8192      +(unsigned long long)              0.117648
tommath_int    8192      -(unsigned long long)              0.118991
tommath_int    8192      *(unsigned long long)              0.361697
tommath_int    8192      /(unsigned long long)              19.8407
tommath_int    8192      +=(unsigned long long)             0.159384
tommath_int    8192      -=(unsigned long long)             0.189292
tommath_int    8192      *=(unsigned long long)             0.453899
tommath_int    8192      /=(unsigned long long)             20.7357
tommath_int    16384     +                                  0.229295
tommath_int    16384     -                                  0.358805
tommath_int    16384     *                                  25.8824
tommath_int    16384     /                                  51.7759
tommath_int    16384     str                                11.1251
tommath_int    16384     +(int)                             0.176275
tommath_int    16384     -(int)                             0.179066
tommath_int    16384     *(int)                             0.548109
tommath_int    16384     /(int)                             50.0929
tommath_int    16384     construct                          0.160796
tommath_int    16384     construct(unsigned)                0.182018
tommath_int    16384     construct(unsigned long long)      0.191943
tommath_int    16384     %                                  51.233
tommath_int    16384     |                                  0.213797
tommath_int    16384     &                                  0.245997
tommath_int    16384     ^                                  0.234837
tommath_int    16384     <<                                 0.538044
tommath_int    16384     >>                                 0.899272
tommath_int    16384     %(int)                             54.083
tommath_int    16384     |(int)                             0.205729
tommath_int    16384     &(int)                             0.276527
tommath_int    16384     ^(int)                             0.314742
tommath_int    16384     gcd                                1770.72
tommath_int    16384     +(unsigned long long)              0.175822
tommath_int    16384     -(unsigned long long)              0.175887
tommath_int    16384     *(unsigned long long)              0.667264
tommath_int    16384     /(unsigned long long)              57.2509
tommath_int    16384     +=(unsigned long long)             0.226317
tommath_int    16384     -=(unsigned long long)             0.2213
tommath_int    16384     *=(unsigned long long)             0.690731
tommath_int    16384     /=(unsigned long long)             67.9296
gmp_int        8192      +                                  0.0811195
gmp_int        8192      -                                  0.11812
gmp_int        8192      *                                  5.52441
gmp_int        8192      /                                  0.516606
gmp_int        8192      str                                0.0262254
gmp_int        8192      +(int)                             0.0700979
gmp_int        8192      -(int)                             0.0576919
gmp_int        8192      *(int)                             0.113505
gmp_int        8192      /(int)                             0.410334
gmp_int        8192      construct                          0.0081635
gmp_int        8192      construct(unsigned)                0.0635939
gmp_int        8192      construct(unsigned long long)      0.0709692
gmp_int        8192      %                                  0.603534
gmp_int        8192      |                                  0.0920584
gmp_int        8192      &                                  0.0887291
gmp_int        8192      ^                                  0.0805332
gmp_int        8192      <<                                 0.146992
gmp_int        8192      >>                                 0.00722919
gmp_int        8192      %(int)                             0.119886
gmp_int        8192      |(int)                             0.0874534
gmp_int        8192      &(int)                             0.0150182
gmp_int        8192      ^(int)                             0.0788165
gmp_int        8192      gcd                                58.8129
gmp_int        8192      +(unsigned long long)              0.0466003
gmp_int        8192      -(unsigned long long)              0.0435595
gmp_int        8192      *(unsigned long long)              0.0886586
gmp_int        8192      /(unsigned long long)              0.288938
gmp_int        8192      +=(unsigned long long)             0.0570676
gmp_int        8192      -=(unsigned long long)             0.05295
gmp_int        8192      *=(unsigned long long)             0.108914
gmp_int        8192      /=(unsigned long long)             0.319457
gmp_int        16384     +                                  0.134715
gmp_int        16384     -                                  0.14931
gmp_int        16384     *                                  11.3398
gmp_int        16384     /                                  0.638503
gmp_int        16384     str                                0.0398493
gmp_int        16384     +(int)                             0.0772241
gmp_int        16384     -(int)                             0.0818204
gmp_int        16384     *(int)                             0.137745
gmp_int        16384     /(int)                             0.595404
gmp_int        16384     construct                          0.00397999
gmp_int        16384     construct(unsigned)                0.0276289
gmp_int        16384     construct(unsigned long long)      0.0219346
gmp_int        16384     %                                  0.723753
gmp_int        16384     |                                  0.111118
gmp_int        16384     &                                  0.112421
gmp_int        16384     ^                                  0.124214
gmp_int        16384     <<                                 0.206887
gmp_int        16384     >>                                 0.00655899
gmp_int        16384     %(int)                             0.137682
gmp_int        16384     |(int)                             0.115602
gmp_int        16384     &(int)                             0.0212413
gmp_int        16384     ^(int)                             0.139399
gmp_int        16384     gcd                                122.14
gmp_int        16384     +(unsigned long long)              0.0668364
gmp_int        16384     -(unsigned long long)              0.0661641
gmp_int        16384     *(unsigned long long)              0.12958
gmp_int        16384     /(unsigned long long)              0.523737
gmp_int        16384     +=(unsigned long long)             0.0841208
gmp_int        16384     -=(unsigned long long)             0.0947863
gmp_int        16384     *=(unsigned long long)             0.208689
gmp_int        16384     /=(unsigned long long)             0.607595
[section:float_performance Float Type Perfomance]
[table Operator *
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][4.45465 (0.0720574s)][4.33995 (0.111688s)][2.90491 (0.850904s)]]
[[cpp_dec_float][3.82572 (0.061884s)][5.06288 (0.130292s)][5.77019 (1.6902s)]]
[[gmp_float][1.28958 (0.02086s)][[*1] (0.0257348s)][[*1] (0.292919s)]]
[[mpfr_float][[*1] (0.0161758s)][2.57159 (0.0661794s)][1.09888 (0.321884s)]]
]
[table Operator *(int)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][7.78457 (0.0399504s)][8.32801 (0.0464923s)][5.78369 (0.12206s)]]
[[cpp_dec_float][12.2836 (0.0630393s)][22.4215 (0.125171s)][115.745 (2.44271s)]]
[[gmp_float][[*1] (0.00513201s)][[*1] (0.00558264s)][[*1] (0.0211042s)]]
[[mpfr_float][6.55686 (0.0336498s)][6.08531 (0.0339721s)][3.75579 (0.0792629s)]]
]
[table Operator *(unsigned long long)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][3.96061 (0.0508387s)][9.0225 (0.0540758s)][3.84877 (0.0843974s)]]
[[cpp_dec_float][5.80905 (0.0745654s)][24.6321 (0.147631s)][122.419 (2.68446s)]]
[[gmp_float][[*1] (0.0128361s)][[*1] (0.00599344s)][[*1] (0.0219284s)]]
[[mpfr_float][2.15124 (0.0276134s)][4.61073 (0.0276341s)][2.31056 (0.0506668s)]]
]
[table Operator *=(unsigned long long)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][6.54027 (0.0500366s)][5.58857 (0.0547429s)][3.33593 (0.0987612s)]]
[[cpp_dec_float][9.23894 (0.0706828s)][17.6238 (0.172634s)][81.1437 (2.40228s)]]
[[gmp_float][[*1] (0.00765054s)][[*1] (0.00979552s)][[*1] (0.0296053s)]]
[[mpfr_float][2.99517 (0.0229147s)][3.55395 (0.0348128s)][2.42098 (0.0716736s)]]
]
[table Operator +
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][2.61482 (0.0479759s)][6.91285 (0.0901201s)][8.04302 (0.219881s)]]
[[cpp_dec_float][1.08384 (0.0198859s)][1.6631 (0.0216812s)][7.59843 (0.207727s)]]
[[gmp_float][1.00042 (0.0183555s)][[*1] (0.0130366s)][[*1] (0.0273382s)]]
[[mpfr_float][[*1] (0.0183477s)][1.4588 (0.0190178s)][2.22935 (0.0609463s)]]
]
[table Operator +(int)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][11.8195 (0.0524679s)][13.9144 (0.0871804s)][13.6745 (0.165014s)]]
[[cpp_dec_float][3.22445 (0.0143136s)][2.703 (0.0169356s)][12.5227 (0.151114s)]]
[[gmp_float][[*1] (0.00443909s)][[*1] (0.00626549s)][[*1] (0.0120673s)]]
[[mpfr_float][5.78563 (0.0256829s)][4.78942 (0.0300081s)][5.21353 (0.062913s)]]
]
[table Operator +(unsigned long long)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][21.4259 (0.109321s)][16.4546 (0.128103s)][14.7256 (0.16427s)]]
[[cpp_dec_float][4.53931 (0.0231609s)][3.75913 (0.0292658s)][13.3516 (0.148942s)]]
[[gmp_float][[*1] (0.0051023s)][[*1] (0.00778526s)][[*1] (0.0111554s)]]
[[mpfr_float][5.11905 (0.0261189s)][3.73777 (0.0290995s)][5.52758 (0.0616622s)]]
]
[table Operator +=(unsigned long long)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][6.6778 (0.109847s)][6.84045 (0.0942496s)][9.09759 (0.157162s)]]
[[cpp_dec_float][1.06799 (0.017568s)][1.71962 (0.0236933s)][10.7777 (0.186187s)]]
[[gmp_float][[*1] (0.0164496s)][[*1] (0.0137783s)][[*1] (0.0172751s)]]
[[mpfr_float][3.47654 (0.0571878s)][3.64857 (0.0502709s)][4.56004 (0.0787754s)]]
]
[table Operator -
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][5.04597 (0.0688937s)][4.65775 (0.0896788s)][7.55005 (0.196627s)]]
[[cpp_dec_float][1.42357 (0.0194363s)][1.62766 (0.0313385s)][8.16298 (0.21259s)]]
[[gmp_float][[*1] (0.0136532s)][[*1] (0.0192537s)][[*1] (0.0260432s)]]
[[mpfr_float][1.76726 (0.0241287s)][2.05663 (0.0395976s)][2.48295 (0.0646638s)]]
]
[table Operator -(int)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][6.31633 (0.0848243s)][7.07646 (0.084402s)][6.65523 (0.181209s)]]
[[cpp_dec_float][1.6139 (0.0216737s)][2.20945 (0.0263524s)][5.27802 (0.14371s)]]
[[gmp_float][[*1] (0.0134294s)][[*1] (0.0119272s)][[*1] (0.0272281s)]]
[[mpfr_float][5.1551 (0.0692297s)][5.48391 (0.0654074s)][2.6116 (0.0711089s)]]
]
[table Operator -(unsigned long long)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][9.08373 (0.102878s)][4.54284 (0.0883961s)][7.01771 (0.185162s)]]
[[cpp_dec_float][2.19345 (0.0248419s)][1.47898 (0.0287785s)][5.71248 (0.150724s)]]
[[gmp_float][[*1] (0.0113255s)][[*1] (0.0194584s)][[*1] (0.026385s)]]
[[mpfr_float][5.35926 (0.0606961s)][2.92255 (0.056868s)][3.43277 (0.0905736s)]]
]
[table Operator -=(unsigned long long)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][8.73347 (0.129938s)][4.58762 (0.101449s)][5.43827 (0.163157s)]]
[[cpp_dec_float][1.67927 (0.0249846s)][1.79526 (0.0396995s)][6.63379 (0.199025s)]]
[[gmp_float][[*1] (0.0148782s)][[*1] (0.0221136s)][[*1] (0.0300017s)]]
[[mpfr_float][3.96488 (0.0589902s)][3.30899 (0.0731736s)][3.51566 (0.105476s)]]
]
[table Operator /
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][5.40221 (0.549759s)][10.0182 (1.39191s)][12.3684 (5.32129s)]]
[[cpp_dec_float][8.16505 (0.83092s)][12.483 (1.73437s)][52.8644 (22.744s)]]
[[gmp_float][[*1] (0.101766s)][[*1] (0.138938s)][[*1] (0.430233s)]]
[[mpfr_float][1.6699 (0.169939s)][2.16251 (0.300455s)][2.82683 (1.2162s)]]
]
[table Operator /(int)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][5.81464 (0.141651s)][12.2991 (0.271491s)][8.20246 (0.643802s)]]
[[cpp_dec_float][16.4865 (0.40163s)][39.0331 (0.861622s)][164 (12.8722s)]]
[[gmp_float][[*1] (0.0243611s)][[*1] (0.0220741s)][[*1] (0.0784889s)]]
[[mpfr_float][2.25501 (0.0549347s)][2.6832 (0.0592293s)][1.60852 (0.126251s)]]
]
[table Operator /(unsigned long long)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][10.879 (0.19558s)][10.4845 (0.244599s)][8.82445 (0.621615s)]]
[[cpp_dec_float][22.028 (0.396014s)][42.4784 (0.991001s)][201.351 (14.1836s)]]
[[gmp_float][[*1] (0.0179778s)][[*1] (0.0233296s)][[*1] (0.0704423s)]]
[[mpfr_float][2.89723 (0.0520857s)][2.6935 (0.0628383s)][1.67233 (0.117803s)]]
]
[table Operator /=(unsigned long long)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][11.0264 (0.215322s)][7.78235 (0.256093s)][8.31638 (0.638041s)]]
[[cpp_dec_float][20.9412 (0.408938s)][38.8146 (1.27727s)][142.377 (10.9233s)]]
[[gmp_float][[*1] (0.0195279s)][[*1] (0.032907s)][[*1] (0.076721s)]]
[[mpfr_float][2.67894 (0.0523141s)][2.16678 (0.071302s)][1.5964 (0.122477s)]]
]
[table Operator construct
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][[*1] (0.0030982s)][1.37355 (0.00608929s)][[*1] (0.0152335s)]]
[[cpp_dec_float][1.23911 (0.00383901s)][[*1] (0.00443324s)][1.6806 (0.0256013s)]]
[[gmp_float][7.50258 (0.0232445s)][5.47554 (0.0242744s)][5.81062 (0.0885159s)]]
[[mpfr_float][24.9598 (0.0773304s)][19.5526 (0.0866815s)][5.00378 (0.0762248s)]]
]
[table Operator construct(unsigned long long)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][2.08541 (0.0175206s)][3.12821 (0.0267453s)][1.32687 (0.0570308s)]]
[[cpp_dec_float][[*1] (0.00840151s)][[*1] (0.00854971s)][[*1] (0.0429815s)]]
[[gmp_float][3.08163 (0.0258903s)][2.44895 (0.0209379s)][2.12806 (0.0914671s)]]
[[mpfr_float][10.6814 (0.08974s)][8.42482 (0.0720298s)][1.8282 (0.0785786s)]]
]
[table Operator construct(unsigned)
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][1.04974 (0.00808983s)][1.38823 (0.0120486s)][1.16321 (0.0530205s)]]
[[cpp_dec_float][[*1] (0.00770653s)][[*1] (0.00867913s)][[*1] (0.0455813s)]]
[[gmp_float][3.88496 (0.0299395s)][2.90503 (0.0252132s)][1.82072 (0.0829907s)]]
[[mpfr_float][13.3088 (0.102564s)][8.41333 (0.0730204s)][1.78408 (0.0813206s)]]
]
[table Operator str
[[Backend][50 Bits][100 Bits][500 Bits]]
[[cpp_bin_float][6.09659 (0.00301165s)][4.71882 (0.0051705s)][12.4899 (0.043165s)]]
[[cpp_dec_float][2.94087 (0.00145276s)][1.64843 (0.00180621s)][3.16004 (0.0109211s)]]
[[gmp_float][[*1] (0.000493989s)][[*1] (0.00109572s)][[*1] (0.00345598s)]]
[[mpfr_float][4.60349 (0.00227407s)][1.21213 (0.00132816s)][1.41569 (0.0048926s)]]
]
[endsect]
[section:integer_performance Integer Type Perfomance]
[table Operator %
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][1.51155 (0.0481508s)][1.60666 (0.0825917s)][1.75956 (0.127209s)][1.87154 (0.171986s)][2.58143 (0.368469s)][1.65419 (0.398213s)][[*1] (0.512768s)][1.42429 (1.03083s)][[*1] (1.96988s)]]
[[cpp_int(fixed)][[*1] (0.0318553s)][1.2913 (0.0663805s)][1.33672 (0.0966394s)][1.97924 (0.181883s)]]
[[gmp_int][1.4659 (0.0466966s)][[*1] (0.0514059s)][[*1] (0.0722958s)][[*1] (0.0918952s)][[*1] (0.142738s)][[*1] (0.24073s)][1.17701 (0.603534s)][[*1] (0.723753s)]]
[[tommath_int][27.46 (0.874748s)][20.1749 (1.03711s)][17.9774 (1.29969s)][19.0867 (1.75398s)][23.3789 (3.33706s)][26.6546 (6.41658s)][33.4553 (17.1548s)][70.788 (51.233s)]]
]
[table Operator %(int)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][[*1] (0.00790481s)][1.45288 (0.0215141s)][2.71488 (0.065874s)][4.05695 (0.1044s)][6.59285 (0.288068s)][5.28362 (0.301827s)][3.58045 (0.429244s)][6.18417 (0.851447s)][[*1] (1.57951s)]]
[[cpp_int(fixed)][2.18748 (0.0172916s)][1.67119 (0.0247468s)][2.83861 (0.0688759s)][4.3186 (0.111133s)]]
[[gmp_int][1.50165 (0.0118703s)][[*1] (0.0148079s)][[*1] (0.024264s)][[*1] (0.0257336s)][[*1] (0.0436939s)][[*1] (0.0571251s)][[*1] (0.119886s)][[*1] (0.137682s)]]
[[tommath_int][68.9623 (0.545134s)][54.52 (0.807325s)][38.3573 (0.930702s)][53.0833 (1.36603s)][59.6958 (2.60834s)][103.597 (5.91797s)][133.648 (16.0225s)][392.812 (54.083s)]]
]
[table Operator &
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][2.82137 (0.0101034s)][3.08842 (0.0128886s)][3.46566 (0.018172s)][2.55025 (0.0204051s)][2.97241 (0.0461406s)][[*1] (0.0366949s)][[*1] (0.0440714s)][[*1] (0.0811524s)][[*1] (0.146531s)]]
[[cpp_int(fixed)][[*1] (0.00358104s)][1.5374 (0.00641591s)][1.70378 (0.0089337s)][2.40825 (0.019269s)]]
[[gmp_int][1.10071 (0.00394167s)][[*1] (0.00417321s)][[*1] (0.00524347s)][[*1] (0.00800121s)][[*1] (0.015523s)][1.04806 (0.0384586s)][2.0133 (0.0887291s)][1.38531 (0.112421s)]]
[[tommath_int][2.18066 (0.00780901s)][2.22528 (0.00928657s)][2.15567 (0.0113032s)][3.05906 (0.0244762s)][2.34741 (0.0364387s)][1.50551 (0.0552444s)][2.29355 (0.10108s)][3.0313 (0.245997s)]]
]
[table Operator &(int)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][3.24695 (0.0124861s)][1.81002 (0.0114113s)][2.11984 (0.0179186s)][2.56949 (0.0262296s)][3.62157 (0.063826s)][5.04381 (0.0612567s)][3.95427 (0.0593859s)][4.84754 (0.102968s)][[*1] (0.168385s)]]
[[cpp_int(fixed)][[*1] (0.0038455s)][[*1] (0.00630453s)][[*1] (0.00845281s)][2.20848 (0.0225444s)]]
[[gmp_int][3.87448 (0.0148993s)][1.50343 (0.00947844s)][1.59793 (0.013507s)][[*1] (0.0102081s)][[*1] (0.0176239s)][[*1] (0.0121449s)][[*1] (0.0150182s)][[*1] (0.0212413s)]]
[[tommath_int][25.1094 (0.0965583s)][15.7147 (0.0990734s)][9.65097 (0.0815778s)][8.5208 (0.0869813s)][5.74798 (0.101302s)][10.598 (0.128712s)][12.0534 (0.18102s)][13.0183 (0.276527s)]]
]
[table Operator *
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][12.4201 (0.0145016s)][1.838 (0.0246772s)][2.10406 (0.0631704s)][2.22846 (0.224062s)][2.53789 (0.918685s)][1.18016 (1.31825s)][[*1] (2.65154s)][[*1] (7.83314s)][[*1] (26.1836s)]]
[[cpp_int(fixed)][[*1] (0.00116759s)][1.76789 (0.0237359s)][1.58515 (0.047591s)][1.52628 (0.153461s)]]
[[gmp_int][4.80091 (0.00560549s)][[*1] (0.0134261s)][[*1] (0.0300231s)][[*1] (0.100546s)][[*1] (0.361988s)][[*1] (1.11701s)][2.08347 (5.52441s)][1.44767 (11.3398s)]]
[[tommath_int][16.9604 (0.0198027s)][2.43157 (0.0326465s)][2.82213 (0.0847288s)][1.67653 (0.168568s)][1.27293 (0.460786s)][1.41678 (1.58255s)][2.69181 (7.13744s)][3.30421 (25.8824s)]]
]
[table Operator *(int)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][10.9781 (0.0072726s)][1.52901 (0.00991594s)][3.35266 (0.0194072s)][2.30215 (0.0214459s)][1.95214 (0.047049s)][[*1] (0.0426049s)][[*1] (0.0576663s)][[*1] (0.0955853s)][[*1] (0.167493s)]]
[[cpp_int(fixed)][[*1] (0.000662467s)][[*1] (0.00648519s)][1.52643 (0.0088359s)][2.26708 (0.0211192s)]]
[[gmp_int][7.14641 (0.00473426s)][1.74133 (0.0112929s)][[*1] (0.00578859s)][[*1] (0.00931559s)][[*1] (0.0241013s)][1.12075 (0.0477494s)][1.96831 (0.113505s)][1.44107 (0.137745s)]]
[[tommath_int][156.154 (0.103447s)][14.5292 (0.0942242s)][16.82 (0.0973642s)][11.9029 (0.110883s)][5.61803 (0.135402s)][4.61241 (0.196512s)][5.30415 (0.30587s)][5.73424 (0.548109s)]]
]
[table Operator *(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][8.98335 (0.00669928s)][1.79861 (0.00820918s)][1.86924 (0.0119122s)][1.71773 (0.0175683s)][2.57826 (0.0539343s)][1.63339 (0.052584s)][[*1] (0.0432066s)][[*1] (0.0813634s)][[*1] (0.159452s)]]
[[cpp_int(fixed)][[*1] (0.000745744s)][1.07211 (0.00489332s)][1.19888 (0.00764018s)][1.53618 (0.0157115s)]]
[[gmp_int][4.97741 (0.00371188s)][[*1] (0.00456418s)][[*1] (0.00637276s)][[*1] (0.0102277s)][[*1] (0.0209189s)][[*1] (0.0321931s)][2.05197 (0.0886586s)][1.59261 (0.12958s)]]
[[tommath_int][143.938 (0.107341s)][24.0751 (0.109883s)][16.9325 (0.107907s)][11.5473 (0.118102s)][8.08283 (0.169084s)][6.84808 (0.220461s)][8.37134 (0.361697s)][8.20104 (0.667264s)]]
]
[table Operator *=(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][15.7803 (0.0131299s)][1.67116 (0.00790233s)][1.66661 (0.0119079s)][1.51408 (0.0203561s)][1.23815 (0.0373067s)][1.15288 (0.0527845s)][[*1] (0.0424701s)][[*1] (0.0946934s)][[*1] (0.177219s)]]
[[cpp_int(fixed)][[*1] (0.000832044s)][[*1] (0.00472864s)][[*1] (0.00714494s)][[*1] (0.0134446s)]]
[[gmp_int][6.73473 (0.00560359s)][1.81651 (0.00858963s)][1.36813 (0.00977523s)][1.00404 (0.0134989s)][[*1] (0.0301309s)][[*1] (0.0457849s)][2.56449 (0.108914s)][2.20384 (0.208689s)]]
[[tommath_int][115.266 (0.0959064s)][24.7701 (0.117129s)][15.9941 (0.114277s)][11.3798 (0.152996s)][5.69861 (0.171704s)][5.47882 (0.250847s)][10.6875 (0.453899s)][7.29439 (0.690731s)]]
]
[table Operator +
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][9.80738 (0.0086037s)][2.34204 (0.0160922s)][1.00627 (0.0131305s)][1.67865 (0.0163002s)][1.88916 (0.033949s)][1.11861 (0.0336398s)][[*1] (0.0412289s)][[*1] (0.083134s)][[*1] (0.190174s)]]
[[cpp_int(fixed)][[*1] (0.000877268s)][[*1] (0.00687102s)][1.82016 (0.0237507s)][1.90391 (0.0184876s)]]
[[gmp_int][5.87168 (0.00515104s)][1.45191 (0.00997612s)][[*1] (0.0130487s)][[*1] (0.00971031s)][[*1] (0.0179704s)][[*1] (0.0300729s)][1.96754 (0.0811195s)][1.62045 (0.134715s)]]
[[tommath_int][13.6707 (0.0119929s)][1.03266 (0.00709542s)][1.02249 (0.0133422s)][1.4749 (0.0143218s)][1.17551 (0.0211244s)][1.08462 (0.0326177s)][1.47125 (0.060658s)][2.75813 (0.229295s)]]
]
[table Operator +(int)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][7.65014 (0.00534018s)][1.8992 (0.0063589s)][1.56778 (0.00666443s)][1.29719 (0.00836612s)][1.73231 (0.0204874s)][1.26754 (0.024805s)][[*1] (0.0198385s)][[*1] (0.0338486s)][[*1] (0.0760202s)]]
[[cpp_int(fixed)][[*1] (0.000698051s)][1.72665 (0.00578118s)][1.57164 (0.00668085s)][1.90796 (0.0123052s)]]
[[gmp_int][4.97679 (0.00347405s)][[*1] (0.0033482s)][[*1] (0.00425087s)][[*1] (0.0064494s)][[*1] (0.0118266s)][[*1] (0.0195694s)][3.53343 (0.0700979s)][2.28146 (0.0772241s)]]
[[tommath_int][127.407 (0.0889366s)][24.8716 (0.0832749s)][20.4864 (0.0870848s)][12.5462 (0.0809152s)][7.07209 (0.083639s)][4.79434 (0.0938225s)][5.93694 (0.11778s)][5.20775 (0.176275s)]]
]
[table Operator +(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][9.92952 (0.00893714s)][2.68575 (0.0136811s)][1.73024 (0.0102989s)][1.43961 (0.0115471s)][1.46556 (0.0237404s)][1.68706 (0.0294774s)][[*1] (0.0253811s)][[*1] (0.0350422s)][[*1] (0.0761856s)]]
[[cpp_int(fixed)][[*1] (0.000900057s)][1.27396 (0.00648945s)][1.38787 (0.008261s)][1.56798 (0.0125768s)]]
[[gmp_int][5.22669 (0.00470432s)][[*1] (0.00509394s)][[*1] (0.00595229s)][[*1] (0.00802101s)][[*1] (0.0161988s)][[*1] (0.0174726s)][1.83602 (0.0466003s)][1.90731 (0.0668364s)]]
[[tommath_int][100.859 (0.0907784s)][19.2654 (0.0981367s)][14.4537 (0.0860324s)][10.0594 (0.0806864s)][5.71755 (0.0926177s)][6.23483 (0.108939s)][4.63528 (0.117648s)][5.01743 (0.175822s)]]
]
[table Operator +=(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][12.2366 (0.0106404s)][1.48157 (0.00996803s)][1.58862 (0.0108279s)][2.06658 (0.0131949s)][1.1631 (0.0186902s)][[*1] (0.0201879s)][[*1] (0.0218483s)][[*1] (0.0394761s)][[*1] (0.0783171s)]]
[[cpp_int(fixed)][[*1] (0.000869555s)][1.34779 (0.00906799s)][[*1] (0.00681593s)][[*1] (0.00638493s)]]
[[gmp_int][11.3522 (0.00987134s)][[*1] (0.00672804s)][1.27495 (0.00868995s)][2.42237 (0.0154667s)][[*1] (0.0160693s)][1.09672 (0.0221405s)][2.61199 (0.0570676s)][2.13093 (0.0841208s)]]
[[tommath_int][88.4252 (0.0768906s)][14.139 (0.0951277s)][11.6282 (0.0792569s)][14.5551 (0.0929332s)][6.86867 (0.110375s)][5.67471 (0.11456s)][7.29502 (0.159384s)][5.733 (0.226317s)]]
]
[table Operator -
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][4.45457 (0.00859678s)][1.28478 (0.013219s)][1.27873 (0.0117779s)][1.43649 (0.0151597s)][2.82516 (0.0507822s)][1.17197 (0.037733s)][[*1] (0.0463464s)][[*1] (0.0813138s)][[*1] (0.191562s)]]
[[cpp_int(fixed)][[*1] (0.00192988s)][[*1] (0.010289s)][[*1] (0.00921062s)][1.5372 (0.0162226s)]]
[[gmp_int][3.4436 (0.00664573s)][1.25045 (0.0128659s)][1.10953 (0.0102195s)][[*1] (0.0105533s)][[*1] (0.017975s)][[*1] (0.0321962s)][2.54862 (0.11812s)][1.83623 (0.14931s)]]
[[tommath_int][9.3224 (0.0179911s)][1.08796 (0.011194s)][1.93265 (0.017801s)][1.82306 (0.0192393s)][1.55663 (0.0279804s)][1.6544 (0.0532653s)][1.90928 (0.0884881s)][4.41259 (0.358805s)]]
]
[table Operator -(int)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][3.07164 (0.00347144s)][1.38957 (0.00531251s)][1.29053 (0.00548206s)][1.35239 (0.00759591s)][1.50007 (0.0176467s)][1.44798 (0.032706s)][[*1] (0.0209158s)][[*1] (0.0402632s)][[*1] (0.0674681s)]]
[[cpp_int(fixed)][[*1] (0.00113016s)][1.26281 (0.00482789s)][1.25074 (0.00531307s)][3.3923 (0.0190533s)]]
[[gmp_int][3.22069 (0.00363988s)][[*1] (0.00382312s)][[*1] (0.00424793s)][[*1] (0.00561665s)][[*1] (0.0117639s)][[*1] (0.0225873s)][2.75829 (0.0576919s)][2.03214 (0.0818204s)]]
[[tommath_int][81.1433 (0.0917047s)][21.4543 (0.0820224s)][19.3098 (0.0820267s)][16.2994 (0.0915478s)][7.23608 (0.0851246s)][4.48441 (0.101291s)][5.51882 (0.115431s)][4.44737 (0.179066s)]]
]
[table Operator -(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][7.02787 (0.00807189s)][1.85949 (0.00922027s)][1.40179 (0.00830959s)][1.22546 (0.00988039s)][1.27526 (0.0235442s)][1.00875 (0.0222548s)][[*1] (0.0242017s)][[*1] (0.0317445s)][[*1] (0.0783054s)]]
[[cpp_int(fixed)][[*1] (0.00114855s)][1.03947 (0.00515424s)][1.22566 (0.00726552s)][1.56568 (0.0126235s)]]
[[gmp_int][7.54669 (0.00866778s)][[*1] (0.0049585s)][[*1] (0.00592785s)][[*1] (0.00806262s)][[*1] (0.0184622s)][[*1] (0.0220616s)][1.79985 (0.0435595s)][2.08427 (0.0661641s)]]
[[tommath_int][72.1332 (0.0828488s)][18.985 (0.0941371s)][13.9301 (0.0825755s)][9.93889 (0.0801335s)][5.42256 (0.100113s)][4.58437 (0.101139s)][4.91664 (0.118991s)][5.5407 (0.175887s)]]
]
[table Operator -=(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][10.8565 (0.0103583s)][2.00541 (0.0102937s)][1.73348 (0.0103591s)][2.53718 (0.0189898s)][1.21308 (0.0265754s)][1.10592 (0.0277295s)][[*1] (0.0249248s)][[*1] (0.0377524s)][[*1] (0.070851s)]]
[[cpp_int(fixed)][[*1] (0.000954108s)][[*1] (0.00513296s)][[*1] (0.00597589s)][[*1] (0.00748462s)]]
[[gmp_int][6.6602 (0.00635455s)][1.61345 (0.00828179s)][1.41745 (0.00847054s)][1.41728 (0.0106078s)][[*1] (0.0219074s)][[*1] (0.0250737s)][2.12439 (0.05295s)][2.51074 (0.0947863s)]]
[[tommath_int][88.858 (0.0847801s)][18.6448 (0.0957029s)][13.7283 (0.0820388s)][13.4423 (0.100611s)][4.83335 (0.105886s)][4.6829 (0.117418s)][7.59449 (0.189292s)][5.86189 (0.2213s)]]
]
[table Operator /
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][3.20876 (0.0878919s)][3.17469 (0.181536s)][3.14517 (0.250544s)][4.14655 (0.365546s)][4.70812 (0.702366s)][4.81923 (1.00586s)][2.28619 (1.18106s)][3.54663 (2.26453s)][[*1] (4.52755s)]]
[[cpp_int(fixed)][[*1] (0.0273912s)][1.72404 (0.098585s)][2.12584 (0.169344s)][3.71442 (0.327451s)]]
[[gmp_int][1.70383 (0.04667s)][[*1] (0.0571824s)][[*1] (0.0796599s)][[*1] (0.0881567s)][[*1] (0.149182s)][[*1] (0.208719s)][[*1] (0.516606s)][[*1] (0.638503s)]]
[[tommath_int][40.8044 (1.11768s)][17.2975 (0.989116s)][17.4097 (1.38686s)][20.9668 (1.84837s)][20.6415 (3.07934s)][31.5839 (6.59216s)][33.0087 (17.0525s)][81.0894 (51.7759s)]]
]
[table Operator /(int)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][4.50037 (0.0520677s)][6.30243 (0.108097s)][8.34437 (0.193637s)][7.02879 (0.296939s)][6.47793 (0.630264s)][5.49102 (0.859876s)][2.64044 (1.08346s)][3.92627 (2.33772s)][[*1] (4.22641s)]]
[[cpp_int(fixed)][1.27227 (0.0147198s)][4.34067 (0.0744498s)][6.08699 (0.141253s)][6.56549 (0.277366s)]]
[[gmp_int][[*1] (0.0115696s)][[*1] (0.0171517s)][[*1] (0.0232057s)][[*1] (0.0422461s)][[*1] (0.0972941s)][[*1] (0.156597s)][[*1] (0.410334s)][[*1] (0.595404s)]]
[[tommath_int][60.4712 (0.69963s)][44.8464 (0.769191s)][40.4334 (0.938285s)][35.0752 (1.48179s)][26.8178 (2.60921s)][37.2616 (5.83504s)][37.7146 (15.4756s)][84.1326 (50.0929s)]]
]
[table Operator /(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][6.09203 (0.0582351s)][6.42997 (0.0982056s)][6.2137 (0.151642s)][6.62408 (0.281298s)][9.30105 (0.706562s)][6.64685 (0.832241s)][3.66307 (1.0584s)][4.10257 (2.14867s)][[*1] (4.2547s)]]
[[cpp_int(fixed)][1.76794 (0.0169001s)][3.59379 (0.0548884s)][5.55499 (0.135566s)][6.36274 (0.2702s)]]
[[gmp_int][[*1] (0.00955921s)][[*1] (0.0152731s)][[*1] (0.0244044s)][[*1] (0.042466s)][[*1] (0.0759658s)][[*1] (0.125208s)][[*1] (0.288938s)][[*1] (0.523737s)]]
[[tommath_int][63.2738 (0.604848s)][57.3767 (0.876321s)][43.9301 (1.07209s)][40.0122 (1.69916s)][41.1147 (3.12331s)][57.9739 (7.25882s)][68.6676 (19.8407s)][109.312 (57.2509s)]]
]
[table Operator /=(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][5.21402 (0.0701172s)][4.76442 (0.105309s)][5.1245 (0.171387s)][7.39587 (0.299993s)][6.88568 (0.632889s)][5.93971 (0.873081s)][3.2993 (1.05399s)][3.52936 (2.14442s)][[*1] (4.37618s)]]
[[cpp_int(fixed)][1.68246 (0.0226255s)][3.12633 (0.0691018s)][3.98733 (0.133354s)][6.62903 (0.268888s)]]
[[gmp_int][[*1] (0.0134478s)][[*1] (0.0221032s)][[*1] (0.0334445s)][[*1] (0.0405622s)][[*1] (0.0919138s)][[*1] (0.14699s)][[*1] (0.319457s)][[*1] (0.607595s)]]
[[tommath_int][46.0731 (0.619583s)][43.6571 (0.964961s)][30.1861 (1.00956s)][41.4936 (1.68307s)][32.6785 (3.0036s)][48.2935 (7.09868s)][64.9093 (20.7357s)][111.801 (67.9296s)]]
]
[table Operator <<
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][9.93178 (0.0116142s)][1.74005 (0.0147029s)][2.68859 (0.0238748s)][2.04419 (0.0394659s)][2.15112 (0.0859331s)][1.26154 (0.0753453s)][[*1] (0.0889652s)][[*1] (0.190562s)][[*1] (0.32803s)]]
[[cpp_int(fixed)][[*1] (0.00116939s)][[*1] (0.00844969s)][1.94851 (0.0173029s)][1.55242 (0.0299716s)]]
[[gmp_int][4.60815 (0.00538874s)][1.02939 (0.00869801s)][[*1] (0.00888006s)][[*1] (0.0193064s)][[*1] (0.039948s)][[*1] (0.0597248s)][1.65224 (0.146992s)][1.08567 (0.206887s)]]
[[tommath_int][10.7935 (0.0126218s)][2.38166 (0.0201243s)][2.42444 (0.0215291s)][1.87488 (0.0361972s)][1.73489 (0.0693055s)][1.93177 (0.115375s)][2.49667 (0.222116s)][2.82346 (0.538044s)]]
]
[table Operator >>
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][12.5304 (0.011709s)][6.85524 (0.0153366s)][8.77866 (0.0209131s)][3.21549 (0.0146098s)][4.04361 (0.0183489s)][5.86336 (0.0183679s)][3.04276 (0.0219967s)][8.19636 (0.0537598s)][[*1] (0.0745484s)]]
[[cpp_int(fixed)][[*1] (0.000934446s)][6.55349 (0.0146615s)][6.51353 (0.0155169s)][4.90631 (0.0222922s)]]
[[gmp_int][2.59712 (0.00242687s)][[*1] (0.0022372s)][[*1] (0.00238226s)][[*1] (0.00454358s)][[*1] (0.00453774s)][[*1] (0.00313265s)][[*1] (0.00722919s)][[*1] (0.00655899s)]]
[[tommath_int][102.662 (0.0959319s)][42.5337 (0.0951565s)][39.1437 (0.0932504s)][21.0397 (0.0955953s)][29.6104 (0.134364s)][62.7092 (0.196446s)][49.6167 (0.358689s)][137.105 (0.899272s)]]
]
[table Operator ^
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][2.8103 (0.0101384s)][4.11932 (0.0282768s)][2.25923 (0.0139063s)][1.94172 (0.0187085s)][1.66067 (0.0405646s)][1.10651 (0.0475314s)][[*1] (0.0369865s)][[*1] (0.0918676s)][[*1] (0.150955s)]]
[[cpp_int(fixed)][[*1] (0.00360758s)][[*1] (0.00686442s)][1.50683 (0.00927498s)][1.79076 (0.0172541s)]]
[[gmp_int][1.35398 (0.0048846s)][1.00969 (0.00693092s)][[*1] (0.00615531s)][[*1] (0.00963503s)][[*1] (0.0244266s)][[*1] (0.0429561s)][2.17737 (0.0805332s)][1.3521 (0.124214s)]]
[[tommath_int][2.28701 (0.00825059s)][1.65996 (0.0113947s)][2.06089 (0.0126854s)][2.03244 (0.0195826s)][1.4173 (0.0346198s)][1.32237 (0.0568037s)][2.69468 (0.0996668s)][2.55626 (0.234837s)]]
]
[table Operator ^(int)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][2.67312 (0.00991885s)][1.57246 (0.0115174s)][2.50193 (0.0198209s)][[*1] (0.0153479s)][[*1] (0.0264078s)][[*1] (0.0206665s)][[*1] (0.0235546s)][[*1] (0.0392727s)][[*1] (0.0657809s)]]
[[cpp_int(fixed)][[*1] (0.00371059s)][[*1] (0.0073244s)][[*1] (0.00792226s)][1.04763 (0.0160789s)]]
[[gmp_int][2.86377 (0.0106263s)][2.18619 (0.0160125s)][2.18777 (0.0173321s)][1.01844 (0.0156309s)][1.13176 (0.0298873s)][1.9665 (0.0406408s)][3.34613 (0.0788165s)][3.54952 (0.139399s)]]
[[tommath_int][26.101 (0.0968501s)][14.6444 (0.107261s)][10.0538 (0.079649s)][5.64392 (0.0866224s)][3.8806 (0.102478s)][6.36386 (0.131519s)][8.07216 (0.190136s)][8.01428 (0.314742s)]]
]
[table Operator construct
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][3.24638 (0.00190752s)][1.06172 (0.00258002s)][[*1] (0.00248269s)][[*1] (0.00191598s)][[*1] (0.00273948s)][[*1] (0.00240729s)][[*1] (0.0017072s)][[*1] (0.00191549s)][[*1] (0.0016635s)]]
[[cpp_int(fixed)][[*1] (0.000587582s)][[*1] (0.00243004s)][1.51116 (0.00375174s)][4.66482 (0.00893771s)]]
[[gmp_int][5.61558 (0.00329962s)][1.52898 (0.0037155s)][2.51713 (0.00624925s)][1.755 (0.00336255s)][1.40183 (0.00384029s)][1.60131 (0.00385483s)][4.7818 (0.0081635s)][2.07779 (0.00397999s)]]
[[tommath_int][251.1 (0.147542s)][63.6186 (0.154596s)][54.6947 (0.13579s)][66.8839 (0.128148s)][47.8098 (0.130974s)][54.8252 (0.13198s)][74.1436 (0.126578s)][83.945 (0.160796s)]]
]
[table Operator construct(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][2.12644 (0.00192028s)][[*1] (0.00200418s)][[*1] (0.00223886s)][[*1] (0.00189442s)][[*1] (0.00190833s)][[*1] (0.00288069s)][[*1] (0.00199036s)][[*1] (0.00200998s)][[*1] (0.00168004s)]]
[[cpp_int(fixed)][[*1] (0.000903049s)][1.26395 (0.00253319s)][1.78621 (0.00399907s)][5.10701 (0.0096748s)]]
[[gmp_int][27.1389 (0.0245077s)][10.3593 (0.020762s)][9.02272 (0.0202006s)][9.96334 (0.0188747s)][15.2262 (0.0290565s)][8.50239 (0.0244927s)][35.6564 (0.0709692s)][10.9129 (0.0219346s)]]
[[tommath_int][183.484 (0.165695s)][83.0709 (0.166489s)][69.5984 (0.155821s)][75.8903 (0.143768s)][85.5979 (0.163349s)][50.8174 (0.146389s)][70.0602 (0.139445s)][95.4952 (0.191943s)]]
]
[table Operator construct(unsigned)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][2.00349 (0.00174712s)][[*1] (0.00225895s)][[*1] (0.00241092s)][[*1] (0.00177047s)][[*1] (0.00304865s)][[*1] (0.00241711s)][[*1] (0.0018378s)][[*1] (0.0017583s)][[*1] (0.00197229s)]]
[[cpp_int(fixed)][[*1] (0.000872038s)][1.1805 (0.0026667s)][1.66167 (0.00400614s)][5.54475 (0.00981681s)]]
[[gmp_int][27.6419 (0.0241048s)][10.0723 (0.0227529s)][7.60577 (0.0183369s)][10.2725 (0.0181871s)][9.38419 (0.0286091s)][9.10394 (0.0220052s)][34.6032 (0.0635939s)][15.7134 (0.0276289s)]]
[[tommath_int][207.462 (0.180915s)][84.5379 (0.190967s)][65.2109 (0.157218s)][84.8572 (0.150237s)][47.0682 (0.143494s)][59.4466 (0.143689s)][78.5549 (0.144368s)][103.519 (0.182018s)]]
]
[table Operator gcd
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][2.10743 (0.358587s)][1.8778 (1.42989s)][2.03859 (3.30534s)][2.01435 (7.01715s)][2.16957 (17.7583s)][1.16957 (21.9738s)][[*1] (32.2572s)][[*1] (76.6459s)][[*1] (201.791s)]]
[[cpp_int(fixed)][3.65486 (0.621889s)][1.78424 (1.35865s)][2.02664 (3.28597s)][1.95328 (6.8044s)]]
[[gmp_int][[*1] (0.170154s)][[*1] (0.761472s)][[*1] (1.62139s)][[*1] (3.48358s)][[*1] (8.18516s)][[*1] (18.7879s)][1.82325 (58.8129s)][1.59356 (122.14s)]]
[[tommath_int][8.01966 (1.36458s)][4.44226 (3.38266s)][4.55056 (7.37824s)][4.42983 (15.4317s)][5.23788 (42.8729s)][7.25799 (136.362s)][14.6265 (471.81s)][23.1025 (1770.72s)]]
]
[table Operator powm
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_int][14.8198 (0.565871s)][13.2096 (2.0199s)][11.8233 (9.06469s)][9.12533 (46.9932s)]]
[[cpp_int(fixed)][9.40069 (0.35895s)][10.0395 (1.53516s)][10.5353 (8.07714s)][8.49678 (43.7564s)]]
[[gmp_int][[*1] (0.0381833s)][[*1] (0.152912s)][[*1] (0.766677s)][[*1] (5.14976s)]]
[[tommath_int][11.0485 (0.421869s)][8.44037 (1.29063s)][4.18756 (3.21051s)][2.45216 (12.628s)]]
]
[table Operator str
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][1.47697 (0.000264092s)][2.87174 (0.000644609s)][2.28911 (0.00141073s)][4.92453 (0.00383604s)][5.61647 (0.0137593s)][4.87866 (0.0237422s)][1.87264 (0.0491109s)][4.29909 (0.171316s)][[*1] (0.595522s)]]
[[cpp_int(fixed)][4.73326 (0.00084634s)][1.78742 (0.000401216s)][1.68455 (0.00103815s)][4.30889 (0.00335647s)]]
[[gmp_int][[*1] (0.000178807s)][[*1] (0.000224466s)][[*1] (0.00061628s)][[*1] (0.000778966s)][[*1] (0.00244981s)][[*1] (0.00486654s)][[*1] (0.0262254s)][[*1] (0.0398493s)]]
[[tommath_int][16.7304 (0.00299152s)][26.6015 (0.00597113s)][30.9815 (0.0190933s)][74.7467 (0.0582251s)][82.4773 (0.202054s)][154.996 (0.754295s)][107.534 (2.82013s)][279.178 (11.1251s)]]
]
[table Operator |
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][2.26845 (0.00991773s)][1.955 (0.00939722s)][2.01122 (0.012635s)][1.76421 (0.0152013s)][1.92162 (0.0293243s)][[*1] (0.0334167s)][[*1] (0.0377549s)][[*1] (0.0916779s)][[*1] (0.152323s)]]
[[cpp_int(fixed)][1.00452 (0.0043918s)][1.37689 (0.00661838s)][1.3138 (0.00825362s)][1.71906 (0.0148123s)]]
[[gmp_int][[*1] (0.00437203s)][[*1] (0.00480677s)][[*1] (0.00628228s)][[*1] (0.00861647s)][[*1] (0.0152602s)][1.09283 (0.0365187s)][2.43832 (0.0920584s)][1.21204 (0.111118s)]]
[[tommath_int][1.69103 (0.00739324s)][1.85402 (0.00891185s)][1.78526 (0.0112155s)][1.86487 (0.0160686s)][1.75184 (0.0267336s)][1.44011 (0.0481236s)][2.33195 (0.0880424s)][2.33204 (0.213797s)]]
]
[table Operator |(int)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits][2048 Bits][2688 Bits][4096 Bits][8192 Bits][16384 Bits]]
[[cpp_int][2.11741 (0.00805945s)][2.16753 (0.0119795s)][1.5717 (0.012189s)][1.10016 (0.0134288s)][1.11289 (0.0309032s)][[*1] (0.0214828s)][[*1] (0.0232284s)][[*1] (0.042441s)][[*1] (0.0711061s)]]
[[cpp_int(fixed)][[*1] (0.00380628s)][[*1] (0.00552682s)][[*1] (0.00775532s)][[*1] (0.0122062s)]]
[[gmp_int][2.62934 (0.010008s)][1.86878 (0.0103284s)][3.19589 (0.0247852s)][1.1073 (0.0135159s)][[*1] (0.0277685s)][1.67609 (0.0360071s)][3.76493 (0.0874534s)][2.72382 (0.115602s)]]
[[tommath_int][22.0502 (0.0839291s)][18.6272 (0.102949s)][9.99323 (0.0775007s)][6.28905 (0.0767652s)][3.28821 (0.0913086s)][5.0968 (0.109494s)][6.47865 (0.150489s)][4.8474 (0.205729s)]]
]
[endsect]
[section:rational_performance Rational Type Perfomance]
[table Operator *
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][2.00025 (0.797425s)][1.97726 (2.96998s)][1.86844 (6.73224s)][1.96608 (14.4259s)]]
[[mpq_rational][[*1] (0.398662s)][[*1] (1.50207s)][[*1] (3.60314s)][[*1] (7.3374s)]]
]
[table Operator *(int)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][1.88073 (0.0637195s)][1.93184 (0.0917847s)][2.15609 (0.118274s)][2.4236 (0.218283s)]]
[[mpq_rational][[*1] (0.0338803s)][[*1] (0.0475114s)][[*1] (0.0548556s)][[*1] (0.0900656s)]]
]
[table Operator *(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][1.60877 (0.161844s)][2.33429 (0.240069s)][1.8835 (0.298935s)][2.70338 (0.448194s)]]
[[mpq_rational][[*1] (0.100601s)][[*1] (0.102844s)][[*1] (0.158713s)][[*1] (0.16579s)]]
]
[table Operator *(value_type)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][1.97503 (0.408791s)][2.42069 (0.600225s)][2.65138 (0.803009s)][4.65673 (1.54645s)]]
[[mpq_rational][[*1] (0.20698s)][[*1] (0.247956s)][[*1] (0.302865s)][[*1] (0.332089s)]]
]
[table Operator *=(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][1.97207 (0.211848s)][2.18482 (0.226179s)][2.43682 (0.319695s)][2.69933 (0.485819s)]]
[[mpq_rational][[*1] (0.107424s)][[*1] (0.103523s)][[*1] (0.131194s)][[*1] (0.179978s)]]
]
[table Operator *=(value_type)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][1.95211 (0.40255s)][2.60942 (0.629302s)][2.83854 (0.8029s)][4.34054 (1.37083s)]]
[[mpq_rational][[*1] (0.206213s)][[*1] (0.241165s)][[*1] (0.282857s)][[*1] (0.31582s)]]
]
[table Operator +
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][2.20364 (0.415006s)][1.97574 (1.53458s)][1.75945 (3.41194s)][2.11634 (8.04044s)]]
[[mpq_rational][[*1] (0.188327s)][[*1] (0.776716s)][[*1] (1.93921s)][[*1] (3.79923s)]]
]
[table Operator +(int)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][2.06836 (0.0177811s)][1.80334 (0.0183744s)][1.38442 (0.020452s)][1.81894 (0.0449351s)]]
[[mpq_rational][[*1] (0.00859669s)][[*1] (0.0101891s)][[*1] (0.014773s)][[*1] (0.024704s)]]
]
[table Operator +(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][2.07187 (0.0177151s)][2.3005 (0.0241089s)][1.81397 (0.0297836s)][1.72202 (0.046594s)]]
[[mpq_rational][[*1] (0.0085503s)][[*1] (0.0104799s)][[*1] (0.016419s)][[*1] (0.0270577s)]]
]
[table Operator +(value_type)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][1.2805 (0.0265647s)][1.59353 (0.0391054s)][1.26613 (0.044067s)][1.95307 (0.105801s)]]
[[mpq_rational][[*1] (0.0207456s)][[*1] (0.0245401s)][[*1] (0.0348044s)][[*1] (0.0541719s)]]
]
[table Operator +=(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][7.29749 (0.0565983s)][3.77253 (0.0371419s)][2.72128 (0.0556987s)][2.01495 (0.0662456s)]]
[[mpq_rational][[*1] (0.00775585s)][[*1] (0.00984535s)][[*1] (0.0204678s)][[*1] (0.032877s)]]
]
[table Operator +=(value_type)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][1.92025 (0.0335896s)][2.08321 (0.0422867s)][1.587 (0.0564267s)][1.85357 (0.0840696s)]]
[[mpq_rational][[*1] (0.0174923s)][[*1] (0.0202988s)][[*1] (0.0355556s)][[*1] (0.0453556s)]]
]
[table Operator -
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][2.38126 (0.471759s)][1.92631 (1.52484s)][1.76181 (3.49648s)][2.03462 (7.71926s)]]
[[mpq_rational][[*1] (0.198113s)][[*1] (0.791584s)][[*1] (1.98459s)][[*1] (3.79396s)]]
]
[table Operator -(int)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][2.44447 (0.0292894s)][2.54602 (0.0346718s)][1.4869 (0.035503s)][1.95344 (0.0577029s)]]
[[mpq_rational][[*1] (0.0119819s)][[*1] (0.013618s)][[*1] (0.0238773s)][[*1] (0.0295391s)]]
]
[table Operator -(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][2.93654 (0.0296698s)][4.23087 (0.0496956s)][1.68041 (0.0461985s)][1.4455 (0.0581714s)]]
[[mpq_rational][[*1] (0.0101037s)][[*1] (0.0117459s)][[*1] (0.0274924s)][[*1] (0.040243s)]]
]
[table Operator -(value_type)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][1.69242 (0.0408789s)][1.5205 (0.0467416s)][1.31525 (0.0548939s)][2.16115 (0.103471s)]]
[[mpq_rational][[*1] (0.0241541s)][[*1] (0.030741s)][[*1] (0.0417365s)][[*1] (0.0478777s)]]
]
[table Operator -=(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][3.69509 (0.0366534s)][3.7306 (0.0439181s)][1.8352 (0.0491612s)][1.86662 (0.071761s)]]
[[mpq_rational][[*1] (0.00991947s)][[*1] (0.0117724s)][[*1] (0.0267879s)][[*1] (0.0384444s)]]
]
[table Operator -=(value_type)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][1.76299 (0.0421283s)][2.03803 (0.0490152s)][1.84864 (0.053198s)][2.10533 (0.0881228s)]]
[[mpq_rational][[*1] (0.023896s)][[*1] (0.0240502s)][[*1] (0.0287769s)][[*1] (0.041857s)]]
]
[table Operator /
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][2.03433 (2.28881s)][2.24309 (6.34454s)][2.1203 (13.2036s)][2.36142 (29.3236s)]]
[[mpq_rational][[*1] (1.12509s)][[*1] (2.82848s)][[*1] (6.22726s)][[*1] (12.4178s)]]
]
[table Operator /(int)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][[*1] (0.035134s)][1.08556 (0.0774619s)][1.08797 (0.104628s)][1.29134 (0.207067s)]]
[[mpq_rational][1.85049 (0.0650149s)][[*1] (0.0713565s)][[*1] (0.0961679s)][[*1] (0.16035s)]]
]
[table Operator /(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][1.31397 (0.170727s)][1.63747 (0.216019s)][1.68581 (0.292536s)][1.76695 (0.435259s)]]
[[mpq_rational][[*1] (0.129932s)][[*1] (0.131923s)][[*1] (0.173528s)][[*1] (0.246334s)]]
]
[table Operator /(value_type)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][1.82473 (0.431612s)][2.20261 (0.596248s)][2.45848 (0.809662s)][3.88675 (1.38001s)]]
[[mpq_rational][[*1] (0.236534s)][[*1] (0.270701s)][[*1] (0.329335s)][[*1] (0.355055s)]]
]
[table Operator /=(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][1.58868 (0.216252s)][1.71288 (0.235781s)][1.78218 (0.314161s)][1.98715 (0.460033s)]]
[[mpq_rational][[*1] (0.136121s)][[*1] (0.137652s)][[*1] (0.176279s)][[*1] (0.231505s)]]
]
[table Operator /=(value_type)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][1.72896 (0.40369s)][2.55949 (0.689514s)][2.4929 (0.832288s)][3.51238 (1.37478s)]]
[[mpq_rational][[*1] (0.233487s)][[*1] (0.269395s)][[*1] (0.333863s)][[*1] (0.391409s)]]
]
[table Operator construct
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][[*1] (0.0135822s)][[*1] (0.00935293s)][[*1] (0.0083784s)][[*1] (0.00962697s)]]
[[mpq_rational][1.44264 (0.0195942s)][2.18249 (0.0204126s)][2.64725 (0.0221797s)][2.87767 (0.0277033s)]]
]
[table Operator construct(unsigned long long)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][[*1] (0.00806026s)][[*1] (0.00960336s)][[*1] (0.00769898s)][[*1] (0.0176689s)]]
[[mpq_rational][4.87225 (0.0392716s)][5.91987 (0.0568506s)][9.03811 (0.0695842s)][3.68339 (0.0650815s)]]
]
[table Operator construct(unsigned)
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][[*1] (0.00672081s)][[*1] (0.0064826s)][[*1] (0.00618635s)][[*1] (0.00923644s)]]
[[mpq_rational][6.47138 (0.0434929s)][7.48645 (0.0485316s)][8.2942 (0.0513108s)][5.77363 (0.0533278s)]]
]
[table Operator str
[[Backend][128 Bits][256 Bits][512 Bits][1024 Bits]]
[[cpp_rational][6.17439 (0.00168424s)][7.6748 (0.0033367s)][6.38435 (0.00662873s)][9.07696 (0.0174979s)]]
[[mpq_rational][[*1] (0.000272779s)][[*1] (0.000434761s)][[*1] (0.00103828s)][[*1] (0.00192772s)]]
]
[endsect]
