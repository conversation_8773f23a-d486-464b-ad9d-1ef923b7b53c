<html>
<!-- &copy; Copyright <PERSON><PERSON>, 2002, 2006, 2007, 2009, 2010, 2011 -->
<!-- Distributed under the Boost Software License, Version 1.0. -->
<!-- See http://www.boost.org/LICENSE_1_0.txt -->

<!-- generate-section-numbers=false -->

<head>
<meta http-equiv="Content-Language" content="en-us">
<meta name="GENERATOR" content="Microsoft FrontPage 5.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="styles.css" rel="stylesheet">
<title>Filesystem Reference</title>
</head>

<body>



<table border="0" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111">
  <tr>
    <td width="277">
<p>
<a href="../../../index.htm">
<img src="../../../boost.png" alt="boost.png (6897 bytes)" align="middle" width="300" height="86" border="0"></a></td>
    <td align="middle">
    <font size="7">Filesystem Library<br>
    Version 4</font></td>
  </tr>
</table>

<table border="0" cellpadding="5" cellspacing="0" style="border-collapse: collapse"
 bordercolor="#111111" bgcolor="#D7EEFF" width="100%">
  <tr>
    <td><a href="index.htm">Home</a> &nbsp;&nbsp;
    <a href="tutorial.html">Tutorial</a> &nbsp;&nbsp;
    <a href="reference.html">Reference</a> &nbsp;&nbsp;
    <a href="faq.htm">FAQ</a> &nbsp;&nbsp;
    <a href="release_history.html">Releases</a> &nbsp;&nbsp;
    <a href="portability_guide.htm">Portability</a> &nbsp;&nbsp;
    <a href="v4.html">V4</a> &nbsp;&nbsp;
    <a href="v3.html">V3 Intro</a> &nbsp;&nbsp;
    <a href="v3_design.html">V3 Design</a> &nbsp;&nbsp;
    <a href="deprecated.html">Deprecated</a> &nbsp;&nbsp;
    <a href="issue_reporting.html">Bug Reports </a>&nbsp;&nbsp;
    </td>
</table>

<h1>Reference Documentation</h1>



<h2><a name="TOC">Table of Contents</a></h2>

<table border="0" cellpadding="0" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%">
  <tr>
    <td width="33%" valign="top">
    <p><a href="#Introduction">Introduction</a><br>
    <a href="#Definitions">Definitions</a><br>
    <a href="#Conformance">Conformance</a><br>
    <a href="#Header-filesystem-synopsis">
    Header <code>&lt;boost/filesystem.hpp&gt;</code> synopsis</a><br>
    <a href="#Error-reporting">Error reporting</a><br>
    <a href="#class-path">Class <code>path</code></a><br>
&nbsp;&nbsp;&nbsp; <a href="#path-Conversions"><code>path</code> conversions</a><br>
&nbsp;&nbsp;&nbsp; <a href="#path-Conversions-to-native-format"><code>path</code>
    conversions to native format</a><br>
&nbsp;&nbsp;&nbsp; <a href="#path-Conversions-to-generic-format"><code>path</code>
    conversions to generic format</a><br>
&nbsp;&nbsp;&nbsp; <a href="#path-Encoding-conversions"><code>path</code>
    encoding conversions</a><br>
&nbsp;&nbsp;&nbsp; <a href="#path-Requirements"><code>path</code> requirements</a><br>
    &nbsp;&nbsp;&nbsp; <a href="#path-constructors"><code>path</code> constructors</a><br>
&nbsp;&nbsp;&nbsp; <a href="#path-assignments"><code>path</code> assignments</a><br>
    &nbsp;&nbsp;&nbsp;&nbsp;<a href="#path-appends"><code>path</code> appends</a><br>
    &nbsp;&nbsp; &nbsp;<a href="#path-concatenation"><code>path</code> concatenation</a><br>
&nbsp;&nbsp;&nbsp;&nbsp;<a href="#path-modifiers"><code>path</code> modifiers</a><br>
&nbsp;&nbsp;&nbsp;&nbsp;<a href="#path-native-format-observers"><code>path</code> native
    format observers</a><br>
&nbsp;&nbsp;&nbsp;&nbsp;<a href="#path-generic-format-observers"><code>path</code> generic
    format observers</a><br>
&nbsp;&nbsp;&nbsp; <a href="#path-compare"><code>path</code> compare</a><br>
&nbsp;&nbsp;&nbsp; <a href="#path-decomposition"><code>path</code> decomposition</a><br>
&nbsp;&nbsp;&nbsp; <a href="#path-query"><code>path</code> query</a><br>
&nbsp;&nbsp;&nbsp;&nbsp;<a href="#path-iterators"><code>path</code> iterators</a><br>

&nbsp;&nbsp;&nbsp;&nbsp;<a href="#path-deprecated-functions"><code>path</code> deprecated functions</a><br>
&nbsp;&nbsp;&nbsp;&nbsp;<a href="#path-non-member-functions"><code>path</code> non-member functions</a><br>
&nbsp;&nbsp;&nbsp;&nbsp;<a href="#path-inserter-extractor"><code>path</code> inserters and extractors</a><br>
 &nbsp;<a href="#Class-filesystem_error">Class <code>filesystem_error</code></a><br>
&nbsp;&nbsp;&nbsp; <a href="#filesystem_error-members"><code>filesystem_error</code>
    constructors</a><br>
&nbsp;&nbsp;&nbsp; <a href="#filesystem_error-path1"><code>filesystem_error</code> path1</a><br>
&nbsp;&nbsp;&nbsp; <a href="#filesystem_error-path2"><code>filesystem_error</code> path2</a><br>
&nbsp;&nbsp;&nbsp; <a href="#filesystem_error-what"><code>filesystem_error</code> what</a></td>
    <td width="33%" valign="top">
    <p>
    <a href="#Enum-file_type">Enum <code>file_type</code></a><br>
    <a href="#Enum-perms">Enum <code>perms</code></a><br>
    <a href="#file_status">Class
    <code>file_status</code></a><br>
&nbsp;&nbsp;&nbsp;
    <a href="#file_status">
    <code>file_status</code></a><a href="#file_status-constructors"> constructors</a><br>
&nbsp;&nbsp;&nbsp;&nbsp;<code><a href="#file_status-modifiers">file_status-modifiers</a></code><a href="#directory_entry-observers"> observers</a><br>
&nbsp;&nbsp;&nbsp;&nbsp;<code><a href="#file_status-observers">file_status-observers</a></code><a href="#directory_entry-modifiers"> modifiers</a><br>
<a href="#Class-directory_entry">Class <code>directory_entry</code></a><br>
&nbsp;&nbsp;&nbsp;
<a href="#directory_entry-constructors"><code>directory_entry</code> constructors</a><br>
&nbsp;&nbsp;&nbsp;&nbsp;<a href="#directory_entry-observers"><code>directory_entry</code> observers</a><br>
&nbsp;&nbsp;&nbsp;&nbsp;<a href="#directory_entry-modifiers"><code>directory_entry</code> modifiers</a><br>
<a href="#Class-directory_iterator">Class  <code>directory_iterator</code></a><br>
&nbsp;&nbsp;&nbsp; <a href="#directory_iterator-members"><code>directory_iterator</code>
    members</a><br>
<a href="#Class-recursive_directory_iterator">Class <code>recursive_directory_iterator</code></a><br>
    <a href="#Operational-functions">
    Operational functions</a><br>
    <code>&nbsp;&nbsp;&nbsp;&nbsp; <a href="#absolute">absolute</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#canonical">canonical</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#copy">copy</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#copy_directory">copy_directory</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#copy_file">copy_file</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#copy_symlink">copy_symlink</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#create_directories">create_directories</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#create_directory">create_directory</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#create_hard_link">create_hard_link</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#create_symlink">create_symlink</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#creation_time">creation_time</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#current_path">current_path</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#exists">exists</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#equivalent">equivalent</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#file_size">file_size</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#hard_link_count">hard_link_count</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#initial_path">initial_path</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#is_directory">is_directory</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#is_empty">is_empty</a></code></td>
    <td width="34%" valign="top">
    <p>
    <code>&nbsp;&nbsp;&nbsp;&nbsp; <a href="#is_other">is_other</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#is_regular_file">is_regular_file</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#is_symlink">is_symlink</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#last_write_time">last_write_time</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#permissions">permissions</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#read_symlink">read_symlink</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#relative">relative</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#remove">remove</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#remove_all">remove_all</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#rename">rename</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#resize_file">resize_file</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#space">space</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#status">status</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#status_known">status_known</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#symlink_status">symlink_status</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#system_complete">system_complete</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#temp_directory_path">temp_directory_path</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#unique_path">unique_path</a><br>
&nbsp;&nbsp;&nbsp;&nbsp; <a href="#weakly_canonical">weakly_canonical</a><br></code>
    <a href="#File-streams">File streams</a><br>
<a href="#path-decomposition-table">Path decomposition table</a><br>
    <a href="#windows-path-prefixes">Long paths on Windows and the
    <b>\\?\</b> namespace prefix</a><br>
<a href="#Acknowledgements">Acknowledgements</a><br>
<a href="#References">References</a><br>
&nbsp;</td>
  </tr>
</table>


<h2><a name="Introduction">Introduction</a></h2>

<p>This reference documentation describes components that C++ programs may use
to perform operations involving file systems, including paths, regular files,
and directories.</p>

<blockquote>
<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse"
  bordercolor="#111111" bgcolor="#D7EEFF" width="90%">
  <tr>
    <td width="100%" align="center" colspan="2">
    <p align="center"><b>C++11 Support</b><p align="left">This reference
    documentation is written as if all compilers supported C++11. Where
    possible, the implementation falls back to C++03 if a C++11 feature is not
    available.</td>
  </tr>
  <tr>
    <td width="35%" align="center">
    <p>
    <b>C++11 Feature</b></td>
    <td width="65%" align="center">
    <p>
    <b>Action if not supported by compiler</b></td>
  </tr>
  <tr>
    <td width="35%" align="left">
    <p>
    <code>noexcept</code></td>
    <td width="65%" align="left">
    <p>Keyword omitted.</td>
  </tr>
  <tr>
    <td width="35%" align="left">
    <p>
    <code>constexpr</code></td>
    <td width="65%" align="left">
    <p>Keyword omitted.</td>
  </tr>
  <tr>
    <td width="35%" align="left">
    <p>R-value references</td>
    <td width="65%" align="left">
    <p>Function signature omitted.</td>
  </tr>
  <tr>
    <td width="35%" align="left">
    <p>Defaulted and deleted functions</td>
    <td width="65%" align="left">
    <p>Workaround replacement functions provided.</td>
  </tr>
  <tr>
    <td width="35%" align="left">
    <p>Initializer lists</td>
    <td width="65%" align="left">
    <p>Not currently used.</td>
  </tr>
  <tr>
    <td width="35%" align="left">
    <p>Variadic templates</td>
    <td width="65%" align="left">
    <p>Not currently used.</td>
  </tr>
  <tr>
    <td width="35%" align="left">
    <p>Range-based for statements</td>
    <td width="65%" align="left">
    <p>Supporting functions always provided; they do no harm even for C++03
    compilers.</td>
  </tr>
</table>
</blockquote>



<h2><a name="Conformance">Conformance</a> [fs.conformance]</h2>

<h3>ISO/IEC 9945 conformance [fs.conform.9945]</h3>
<p>Some behavior in this reference documentation is specified by reference to ISO/IEC 9945. How such behavior is actually implemented is unspecified.</p>
<blockquote>
<p>[<i>Note:</i> This constitutes an &quot;as if&quot; rule for implementation of
operating system dependent behavior. In practice implementations will usually call native
operating system API's. <i>—end note</i>]</p>
</blockquote>
<p>Implementations are encouraged to provide such behavior

as it is defined by ISO/IEC 9945. Implementations shall document any
behavior that differs from the behavior defined by ISO/IEC 9945. Implementations that do not support exact
ISO/IEC 9945 behavior are
encouraged to provide behavior as close to ISO/IEC 9945 behavior as is reasonable given the
limitations of actual operating systems and file systems. If an implementation cannot provide any
reasonable behavior, the implementation shall report an error in an
implementation-defined manner.</p>
<blockquote>
<p>[<i>Note:</i> Such errors might be reported by an <code>#error</code> directive, a <code>
static_assert</code>, a <code>filesystem_error</code> exception, a special
return value, or some other manner. <i>—end note</i>]</p>
</blockquote>
<p>Implementations are not required to provide behavior that is not supported by
a particular file system.</p>
<blockquote>
<p>[<i>Example:</i> The <a href="http://en.wikipedia.org/wiki/FAT_filesystem">
FAT file system</a> used by some memory cards, camera memory, and floppy discs
does not support hard links, symlinks, and many other features of more capable
file systems. Implementations are only required to support the FAT features
supported by the host operating system. <i>—end example</i>]</p>
</blockquote>
<p>The behavior of functions described in this
reference
may differ from their specification in
the presence of <a href="#file-system-race">file system races</a>. No diagnostic is required.</p>
<p>If the possibility of a file system race would make it unreliable for a program to
test for a precondition before calling a function described in this reference documentation, <i>
Requires</i> is not specified for the condition. Instead, the condition is
specified as a <i>Throws</i> condition.</p>
<blockquote>
<p>[<i>Note:</i> As a design practice, preconditions are not specified when it
is unreasonable for a program to detect them prior to calling the function. <i>
—end note</i>]</p>
</blockquote>
<h3>Operating system dependent conformance [fs.conform.os]</h3>
<p>Some behavior is specified in this reference documentation as being
operating system dependent ([fs.def.osdep]). The operation system an
implementation is dependent upon is implementation defined.</p>
<p>It is permissible for an implementation to be dependent upon an operating
system emulator rather than the actual operating system.</p>
<blockquote>
<p>[<i>Example:</i> An implementation uses Cygwin, a Linux&reg; API emulator for
some Windows&reg; operating system versions. The implementation would define Cygwin
as its operating system. Users could refer to the Cygwin documentation to find
details of the operating system dependent behavior.&nbsp; <i>—end example</i>]</p>
<p><span style="background-color: #E0E0E0"><i>It is user and conformance test
detectable that such an implementation is running on Cygwin. Users would be
misled and conformance tests would fail if the implementation defined Linux or
Windows rather than Cygwin as the operating system, since real behavior is a
blend of the two.</i></span> </p>
</blockquote>
<h2><a name="Definitions">Definitions</a> [fs.definitions]</h2>
<p>The following definitions  shall apply throughout this reference documentation:</p>
<h3><a name="operating system dependent">operating system dependent</a> behavior
[fs.def.osdep]</h3>
<p>Behavior  that is dependent upon the behavior
and characteristics of an operating system. See [fs.conform.os].</p>
<h3><a name="file">file</a> [fs.def.file]</h3>
<p>An object that can be written to, or read from, or both. A file
has certain attributes, including type. File types include regular files
and directories. Other types of files, such as symbolic links, may be supported by the
implementation.</p>
<h3><a name="file-system">file system</a> [fs.def.filesystem]</h3>
<p>A collection of files and certain of their attributes.</p>
<h3><a name="filename">filename</a> [fs.def.filename]</h3>
  <p>The name of a file. Filenames <code>
  &quot;.&quot;</code>&nbsp;
and <code>&quot;..&quot;</code>&nbsp; have special meaning. The follow characteristics of
  filenames are operating system dependent:</p>
<ul>
  <li>
  <p>The permitted characters. See [<a href="#Operating-system-examples">fs.os.examples</a>].</p>
  </li>
  <li>
  <p>Specific filenames that are not permitted.</p>
  </li>
  <li>
  <p>Additional filenames that have special meaning.</p>
  </li>
  <li>
  <p>Case awareness and sensitivity during path resolution.</p>
  </li>
  <li>
  <p>Special rules that may apply to file types other than regular
  files, such as directories.</p>
  </li>
</ul>
<h3><a name="path">path</a> [fs.def.path]</h3>
<p>A sequence of elements that identify
the location of a file within a filesystem. The elements are the <i>root-name<sub>opt</sub></i>, <i>
root-directory<sub>opt</sub></i>, and an optional sequence of filenames. [<i>Note:</i>
A <a href="#pathname">pathname</a> is the concrete representation of a path. <i>—end note</i>]</p>

<h3><a name="Absolute-path">absolute path</a> [fs.def.absolute-path]</h3>
<p>A path that
unambiguously
identifies the location of a file without reference to an additional starting
location. The elements of a path that determine if it is absolute are
operating system dependent.</p>

<h3><a name="Relative-path">relative path</a> [fs.def.relative-path]</h3>
<p>A path that
is not absolute, and so only
unambiguously
identifies the location of a file when resolved relative to
an implied starting location. The elements of a path that determine if it is
relative are operating system dependent.&nbsp; [<i>Note:</i>
Paths <code>&quot;.&quot;</code> and <code>&quot;..&quot;</code> are relative paths. <i>—end note</i>]</p>
<h3><a name="canonical-path">canonical path</a> [fs.def.canonical-path]</h3>
<p>An absolute path that has
no elements that are symbolic links, and no <code>&quot;.&quot;</code> or <code>&quot;..&quot;</code> elements.</p>
<h3><a name="pathname">pathname</a> [fs.def.pathname]</h3>
<p>A character string that represents
the name of a
path. Pathnames are formatted according to the generic pathname grammar or an
operating system dependent
native pathname format.</p>

<h3><a name="native-pathname-format">native pathname format</a> [fs.def.native]</h3>
<p>The operating system dependent pathname format accepted by the host operating system.</p>
<h3><a name="normal-form">normal form</a> path [fs.def.normal]</h3>
<p>A path with no redundant directory separators, current directory (<i>dot</i>) or parent directory (<i>dot-dot</i>)
elements. The normal form for an empty path is an empty path. [<b>v3:</b> The normal form
for a path ending in a <i>directory-separator</i> that is not the root directory
is the same path with a current directory (<i>dot</i>) element appended.]</p>
<h3><a name="link">link</a> [fs.def.link]</h3>
<p>A directory entry object that associates a
filename with a file. On some file systems, several directory entries can
associate names with the same file.</p>
<h3><a name="hard-link">hard link</a> [fs.def.hardlink]</h3>
<p>A link to an existing file. Some
file systems support multiple hard links to a file. If the last hard link to a
file is removed, the file itself is removed.</p>
<blockquote>
<p>[<i>Note:</i> A hard link can be thought of as a shared-ownership smart
pointer to a file.<i> —end note</i>]<i> </i></p>
</blockquote>
<h3><a name="symbolic-link">symbolic link</a> [fs.def.symlink]</h3>
<p>A type of file with the
property that when the file is encountered during pathname resolution, a string
stored by the file is used to modify the pathname resolution.</p>
<blockquote>
<p>[<i>Note:</i> A symbolic link can be thought of as a raw pointer to a file.
If the file pointed to does not exist, the symbolic link is said to be a
&quot;dangling&quot; symbolic link.<i> —end note</i>]<i> </i></p>
</blockquote>
<h3><a name="file-system-race">file system race</a> [fs.def.race]</h3>
<p>The condition that occurs
when multiple threads, processes, or computers interleave access and
modification of
the same object within a file system.</p>
<h2><a name="generic-pathname-format">Generic pathname format</a> [path.generic]</h2>
<p><i>pathname:<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; root-name<sub>opt</sub>
root-directory<sub>opt</sub> relative-path<sub>opt</sub></i></p>
<p><i>root-name:<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </i>An
operating system dependent name that identifies the starting location for
absolute paths. </p>
<blockquote>
  <blockquote>
<p>[<i>Note:</i> Many operating systems define a name
beginning with two <i>directory-separator</i> characters as a <i>root-name</i>
that identifies network or other resource locations. Some operating systems define a single letter followed by a colon as a drive
specifier - a <i>root-name</i> identifying a specific device such as a disc drive. <i>—end note</i>]</p>
  </blockquote>
</blockquote>
<p><i>root-directory:<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
directory-separator</i></p>
<p><i>relative-path:<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
filename<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; relative-path
directory-separator<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; relative-path
directory-separator filename</i></p>
<p><i>filename:<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; name<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </i><code>&quot;.&quot;</code><i><br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </i><code>
&quot;..&quot;</code></p>
<p><i>preferred-separator:<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </i>An
operating system dependent directory separator character. May be a synonym for <i> <code>&quot;/&quot;</code>.</i></p>
<p><i>directory-separator:<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <code>&quot;/&quot;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &quot;/&quot;</code> directory-separator<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
preferred-separator<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
preferred-separator directory-separator</i></p>
<p>Multiple successive <i>directory-separator</i> characters are considered to
be the same as one <i>directory-separator</i> character.</p>
<p>The <i>filename</i>
<code>&quot;.&quot;</code> is considered to be a reference to the current directory. The
<i>filename</i> <code>&quot;..&quot;</code> is considered to be a reference to the
parent
directory. Specific <i>filenames</i> may have special meanings for a particular
operating system.</p>
<h2><a name="Operating-system-examples">Operating system dependent examples</a> (Informative) [fs.os.examples]</h2>
<p>Certain features are specified in this reference documentation as being operating system dependent. The following table shows the application of those
specifications for operating systems that use the ISO/IEC 9945  or Windows&reg; application program interfaces
(APIs).<sup>[footnote1]</sup></p>
<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111">
  <tr>
    <td valign="top" align="center">
    <p><b>Feature</b></td>
    <td valign="top" align="center">
    <p><b>Section</b></td>
    <td valign="top" align="center">
    <p><b>ISO/IEC 9945<br>
&nbsp;POSIX</b>&reg;<b> API</b></td>
    <td valign="top" align="center">
    <p><b>Windows</b>&reg;<b> API</b></td>
    <td valign="top" align="center">
    <p><b>Notes</b></td>
  </tr>
  <tr>
    <td valign="top">
    <p><code>path<br>
&nbsp;::value_type</code></td>
    <td valign="top">
    <p>[<a href="#class-path">class.path</a>]</td>
    <td valign="top">
    <p><code>char</code></td>
    <td valign="top">
    <p><code>wchar_t</code></td>
    <td valign="top">
    <p>&nbsp;</td>
  </tr>
  <tr>
    <td valign="top">
    <p><code>path::preferred<br>
&nbsp;_separator</code></td>
    <td valign="top">
    <p>[<a href="#class-path">class.path</a>]</td>
    <td valign="top">
    <p><code>'/'</code></td>
    <td valign="top">
    <p><code>L'\\'</code> (single backslash)</td>
    <td valign="top">
    <p>&nbsp;</td>
  </tr>
  <tr>
    <td valign="top">
    <p><code>path(&quot;/&quot;)<br>
&nbsp;.is_absolute()<br>
    path(&quot;c:/&quot;)<br>
&nbsp;.is_absolute()</code></td>
    <td valign="top">
    <p>[<a href="#path-query">path</a><br>
&nbsp;<a href="#path-query">.query</a>]</td>
    <td valign="top">
    <p><code><br>
    true<br>
    <br>
    false</code></td>
    <td valign="top">
    <p><code><br>
    false<br>
    <br>
    true</code></td>
    <td valign="top">
    <p>&nbsp;</td>
  </tr>
  <tr>
    <td valign="top">
    <p><code>path</code> argument disambiguation between generic format and
    native format</td>
    <td valign="top">
    <p>[<a href="#path-Conversions-to-native-format">path.arg</a><br>
&nbsp;<a href="#path-Conversions-to-native-format">.fmt.cvt</a>]</td>
    <td valign="top">
    <p>Not required</td>
    <td valign="top">
    <p>Not required</td>
    <td valign="top">
    <p>There is no need to distinguish between the generic format and native
    format for these operating systems.</td>
  </tr>
  <tr>
    <td valign="top">
    <p><code>path</code> argument format conversion</td>
    <td valign="top">
    <p>[<a href="#path-Conversions-to-native-format">path.arg</a><br>
&nbsp;<a href="#path-Conversions-to-native-format">.fmt.cvt</a>]</td>
    <td valign="top">
    <p>No conversion performed</td>
    <td valign="top">
    <p>No conversion performed</td>
    <td valign="top">
    <p>The generic format is already acceptable to the native API of these operating systems.</td>
  </tr>
  <tr>
    <td valign="top">
    <p><code>path<br>
&nbsp;(&quot;/cats/jane&quot;)<br>
&nbsp;.c_str()<br>
    path|<br>
&nbsp;(&quot;/cats/jane/&quot;)<br>
&nbsp;.c_str()</code></td>
    <td valign="top">
    <p>[<a href="#path-Conversions-to-native-format">path.arg</a><br>
&nbsp;<a href="#path-Conversions-to-native-format">.fmt.cvt</a>]</td>
    <td valign="top">
    <p> <code><br>
    <br>
    &quot;/cats/jane&quot;<br>
    <br>
    <br>
    &quot;/cats/jane/&quot;</code></td>
    <td valign="top">
    <p><code><br>
    <br>
    L&quot;/cats/jane&quot;<br>
    <br>
    <br>
    L&quot;/cats/jane/&quot;</code></td>
    <td valign="top">
    <p>These operating systems accept the same native separator between
    directory names and a final file name, so no format conversion is performed.
    Other operating systems might require conversion.</td>
  </tr>
  <tr>
    <td valign="top">
    <p>Format conversion by <code>path</code> native format observers</td>
    <td valign="top">
    <p>[<a href="#path-native-format-observers">path.native</a><br>
&nbsp;<a href="#path-native-format-observers">.obs</a>]</td>
    <td valign="top">
    <p>No conversion performed</td>
    <td valign="top">
    <p>No conversion performed</td>
    <td valign="top">
    <p>For efficiency, <code>path</code> objects are required to store pathnames in the native
    format regardless of operating system.</td>
  </tr>
  <tr>
    <td valign="top">
    <p>Format conversion by <code>path</code> generic format observers</td>
    <td valign="top">
    <p>[<a href="#path-generic-format-observers">path</a><br>
&nbsp;<a href="#path-generic-format-observers">.generic</a><br>
&nbsp;<a href="#path-generic-format-observers">.obs</a>]</td>
    <td valign="top">
    <p>No conversion performed</td>
    <td valign="top">
    <p>Backslashes converted to slashes</td>
    <td valign="top">
    <p>&nbsp;</td>
  </tr>
  <tr>
    <td valign="top">
    <p><code>p.<br>
&nbsp;make_preferred()</code></td>
    <td valign="top">
    <p>[<a href="#path-modifiers">fs.path</a><br>
&nbsp;<a href="#path-modifiers">.modifiers</a>]</td>
    <td valign="top">
    <p>No change</td>
    <td valign="top">
    <p>Slashes converted to backslashes</td>
    <td valign="top">
    <p>&nbsp;</td>
  </tr>
  <tr>
    <td valign="top">
    <p>Characters prohibited in filenames</td>
    <td valign="top">
    <p>[<a href="#filename">fs.def</a><br>
&nbsp;<a href="#filename">.filename</a>]</td>
    <td valign="top">
    <p>0x00, <code>'/'</code></td>
    <td valign="top">
    <p>0x00-0x1F, <code>'&quot;'</code>, <code>'*'</code>,<code>&nbsp;'*'</code>,
    <code>'&lt;'</code>,
    <code>'&gt;'</code>, <code>'?'</code>, <code>'\\'</code> (single backslash),
    <code>'/'</code>, <code>'|'</code></td>
    <td valign="top">
    <p>Many operating systems prohibit the ASCII control characters (0x00-0x1F)
    in filenames.</td>
  </tr>
  <tr>
    <td valign="top">
    <p>Initial imbued <code>path</code> locale</td>
    <td valign="top">
    <p>[<a href="#path-imbued-locale">path</a><br>
&nbsp;<a href="#path-imbued-locale">.imbued</a><br>
&nbsp;<a href="#path-imbued-locale">.locale</a>]</td>
    <td valign="top">
    <p> <code>std::<br>
&nbsp; locale(&quot;&quot;)<br>
    </code><sup>[footnote 2]</sup></td>
    <td valign="top">
    <p>Implementation supplied locale using <code>MultiByte<br>
&nbsp; ToWideChar</code>
    and <code>WideChar<br>
&nbsp; ToMultiByte</code> with a codepage of <code>CP_ACP</code>
    if <code>AreFileApisANSI</code> is true, otherwise codepage <code>CP_OEMCP</code>.<sup>[footnote
    3]</sup></td>
    <td valign="top">
    <p>Apple OS X&reg;:&nbsp; Implementation supplied locale providing UTF-8 <code>codecvt</code>
    facet.<sup>[footnote 4]</sup></td>
  </tr>
</table>
<p><sup>[footnote1]</sup> OS X&reg; and Windows&reg; are examples of commercially
available operating systems. This information is given for the convenience of
users of this document and does not constitute an endorsement by ISO or IEC of
these products.</p>
<p><sup>[footnote 2] </sup>Rationale: ISO C specifies <code>std::locale(&quot;&quot;)</code> as &quot;the locale-specific native
environment&quot;, while ISO/IEC 9945 says it &quot;Specifies an implementation-defined native
environment.&quot;</p>
<p><sup>[footnote 3] </sup>Rationale: This is the current behavior of C and C++
standard library functions that perform file operations using narrow character
strings to identify paths. Changing this behavior would be surprising and at
variance with existing code, particularly where user input is involved.</p>
<p><sup>[footnote 4]</sup> Rationale: Vendor's documentation states &quot;All BSD
system functions expect their string parameters to be in UTF-8 encoding and
nothing else.&quot;</p>
<h2><a name="Header-filesystem-synopsis">Header <code>&lt;boost/filesystem.hpp&gt;</code> synopsis</a>
[filesystem.synopsis]</h2>



<pre>namespace boost
{
  namespace filesystem
  {
    class <a href="#class-path">path</a>;

    bool lexicographical_compare(path::iterator first1, path::iterator last1,
      path::iterator first2, path::iterator last2);
    void swap(path&amp; lhs, path&amp; rhs) noexcept;
    std::size_t <a href="#hash_value">hash_value</a>(const path&amp; p);

    bool operator==(const path&amp; lhs, const path&amp; rhs);
    bool operator!=(const path&amp; lhs, const path&amp; rhs);
    bool operator&lt; (const path&amp; lhs, const path&amp; rhs);
    bool operator&lt;=(const path&amp; lhs, const path&amp; rhs);
    bool operator&gt; (const path&amp; lhs, const path&amp; rhs);
    bool operator&gt;=(const path&amp; lhs, const path&amp; rhs);

    path operator/ (const path&amp; lhs, const path&amp; rhs);

    std::ostream&amp;  operator&lt;&lt;( std::ostream&amp; os, const path&amp; p );
    std::wostream&amp; operator&lt;&lt;( std::wostream&amp; os, const path&amp; p );
    std::istream&amp;  operator&gt;&gt;( std::istream&amp; is, path&amp; p );
    std::wistream&amp; operator&gt;&gt;( std::wistream&amp; is, path&amp; p )

    class <a href="#Class-filesystem_error">filesystem_error</a>;
    class <a href="#Class-directory_entry">directory_entry</a>;

    class <a href="#Class-directory_iterator">directory_iterator</a>;

    // enable c++11 range-based for statements
    const directory_iterator&amp; <a href="#directory_iterator-non-member-functions">begin</a>(const directory_iterator&amp; iter);
    directory_iterator <a href="#directory_iterator-non-member-functions">end</a>(const directory_iterator&amp;);


    // enable BOOST_FOREACH
    directory_iterator&amp; range_begin(directory_iterator&amp; iter);
    directory_iterator range_begin(const directory_iterator&amp; iter);
    directory_iterator range_end(const directory_iterator&amp;);

    class <a href="#Class-recursive_directory_iterator">recursive_directory_iterator</a>;

    // enable c++11 range-based for statements
    const recursive_directory_iterator&amp;
      <a href="#recursive_directory_iterator-non-member-functions">begin</a>(const recursive_directory_iterator&amp; iter);
    recursive_directory_iterator
      <a href="#recursive_directory_iterator-non-member-functions">end</a>(const recursive_directory_iterator&amp;);


    // enable BOOST_FOREACH
    recursive_directory_iterator&amp;
      range_begin(recursive_directory_iterator&amp; iter);
    recursive_directory_iterator
      range_begin(const recursive_directory_iterator&amp; iter);
    recursive_directory_iterator
      range_end(const recursive_directory_iterator&amp;);

    enum <a name="file_type" href="#Enum-file_type">file_type</a>
    {
      status_error, file_not_found, regular_file, directory_file,
      symlink_file, block_file, character_file, fifo_file, socket_file,
      reparse_file, type_unknown
    };

    enum <a href="#Enum-perms">perms</a>
    {
      no_perms,
      owner_read, owner_write, owner_exe, owner_all,
      group_read, group_write, group_exe, group_all,
      others_read, others_write, others_exe, others_all, all_all,
      set_uid_on_exe, set_gid_on_exe, sticky_bit,
      perms_mask, perms_not_known,
      add_perms, remove_perms, symlink_perms
    };

    class <a href="#file_status">file_status</a>;

    struct <a name="space_info">space_info</a>  // returned by <a href="#space" style="text-decoration: none">space</a> function
    {
      uintmax_t capacity;
      uintmax_t free;
      uintmax_t available; // free space available to non-privileged process
    };

    enum class <a name="copy_options">copy_options</a>
    {
      none = 0u,
      // <a href="#copy_file">copy_file</a> options
      skip_existing,
      overwrite_existing,
      update_existing,
      synchronize_data,
      synchronize,
      // <a href="#copy">copy</a> options
      recursive,
      copy_symlinks,
      skip_symlinks,
      directories_only,
      create_symlinks,
      create_hard_links
    };

    // Deprecated, use <a href="#copy_options">copy_options</a> instead
    enum class <a name="copy_option">copy_option</a>
    {
      none = copy_options::none,
      fail_if_exists = none,
      overwrite_if_exists = copy_options::overwrite_existing
    };

    enum class <a name="directory_options">directory_options</a>
    {
      none = 0u,
      skip_permission_denied,
      follow_directory_symlink,
      pop_on_error
    };

    // Deprecated, use <a href="#directory_options">directory_options</a> instead
    enum class <a name="symlink_option">symlink_option</a>
    {
      none = directory_options::none,
      no_recurse = none,
      recurse = directory_options::follow_directory_symlink
    };

    // <a href="#Operational-functions">operational functions</a>

    path         <a href="#absolute">absolute</a>(const path&amp; p, const path&amp; base=current_path());
    path         <a href="#absolute">absolute</a>(const path&amp; p, system::error_code&amp; ec);
    path         <a href="#absolute">absolute</a>(const path&amp; p, const path&amp; base,
                   system::error_code&amp; ec);

    path         <a href="#canonical">canonical</a>(const path&amp; p, const path&amp; base = current_path());
    path         <a href="#canonical">canonical</a>(const path&amp; p, system::error_code&amp; ec);
    path         <a href="#canonical">canonical</a>(const path&amp; p, const path&amp; base,
                   system::error_code&amp; ec);

    void         <a href="#copy">copy</a>(const path&amp; from, const path&amp; to);
    void         <a href="#copy">copy</a>(const path&amp; from, const path&amp; to,
                   system::error_code&amp; ec);
    void         <a href="#copy">copy</a>(const path&amp; from, const path&amp; to,
                   <a href="#copy_options">copy_options</a> options);
    void         <a href="#copy">copy</a>(const path&amp; from, const path&amp; to,
                   <a href="#copy_options">copy_options</a> options, system::error_code&amp; ec);

    // Deprecated, use <a href="#create_directory">create_directory</a> instead
    void         <a href="#copy_directory">copy_directory</a>(const path&amp; from, const path&amp; to);
    void         <a href="#copy_directory">copy_directory</a>(const path&amp; from, const path&amp; to,
                   system::error_code&amp; ec);

    bool         <a href="#copy_file">copy_file</a>(const path&amp; from, const path&amp; to);
    bool         <a href="#copy_file">copy_file</a>(const path&amp; from, const path&amp; to,
                   system::error_code&amp; ec);
    bool         <a href="#copy_file">copy_file</a>(const path&amp; from, const path&amp; to,
                   <a href="#copy_options">copy_options</a> options);
    bool         <a href="#copy_file">copy_file</a>(const path&amp; from, const path&amp; to,
                   <a href="#copy_options">copy_options</a> options, system::error_code&amp; ec);
    // Deprecated, use overloads taking <a href="#copy_options">copy_options</a> instead
    bool         <a href="#copy_file">copy_file</a>(const path&amp; from, const path&amp; to,
                   <a href="#copy_option">copy_option</a> options);
    bool         <a href="#copy_file">copy_file</a>(const path&amp; from, const path&amp; to,
                   <a href="#copy_option">copy_option</a> options, system::error_code&amp; ec);

    void         <a href="#copy_symlink">copy_symlink</a>(const path&amp; existing_symlink,
                   const path&amp; new_symlink);
    void         <a href="#copy_symlink">copy_symlink</a>(const path&amp; existing_symlink,
                   const path&amp; new_symlink, system::error_code&amp; ec);

    bool         <a href="#create_directories">create_directories</a>(const path&amp; p);
    bool         <a href="#create_directories">create_directories</a>(const path&amp; p,
                   system::error_code&amp; ec);

    bool         <a href="#create_directory">create_directory</a>(const path&amp; p);
    bool         <a href="#create_directory">create_directory</a>(const path&amp; p, system::error_code&amp; ec);
    bool         <a href="#create_directory">create_directory</a>(const path&amp; p, const path&amp; existing);
    bool         <a href="#create_directory">create_directory</a>(const path&amp; p, const path&amp; existing, system::error_code&amp; ec);

    void         <a href="#create_directory_symlink">create_directory_symlink</a>(const path&amp; to,
                   const path&amp; new_symlink);
    void         <a href="#create_directory_symlink">create_directory_symlink</a>(const path&amp; to,
                   const path&amp; new_symlink, system::error_code&amp; ec);

    void         <a href="#create_hard_link">create_hard_link</a>(const path&amp; to, const path&amp; new_hard_link);
    void         <a href="#create_hard_link">create_hard_link</a>(const path&amp; to, const path&amp; new_hard_link,
                                  system::error_code&amp; ec);

    void         <a href="#create_symlink">create_symlink</a>(const path&amp; to, const path&amp; new_symlink);
    void         <a href="#create_symlink">create_symlink</a>(const path&amp; to, const path&amp; new_symlink,
                                system::error_code&amp; ec);

    std::time_t  <a href="#creation_time">creation_time</a>(const path&amp; p);
    std::time_t  <a href="#creation_time">creation_time</a>(const path&amp; p, system::error_code&amp; ec);

    path         <a href="#current_path">current_path</a>();
    path         <a href="#current_path">current_path</a>(system::error_code&amp; ec);
    void         <a href="#current_path">current_path</a>(const path&amp; p);
    void         <a href="#current_path">current_path</a>(const path&amp; p, system::error_code&amp; ec);

    bool         <a href="#exists">exists</a>(file_status s) noexcept;
    bool         <a href="#exists">exists</a>(const path&amp; p);
    bool         <a href="#exists">exists</a>(const path&amp; p, system::error_code&amp; ec) noexcept;

    bool         <a href="#equivalent">equivalent</a>(const path&amp; p1, const path&amp; p2);
    bool         <a href="#equivalent">equivalent</a>(const path&amp; p1, const path&amp; p2,
                   system::error_code&amp; ec);

    uintmax_t    <a href="#file_size">file_size</a>(const path&amp; p);
    uintmax_t    <a href="#file_size">file_size</a>(const path&amp; p, system::error_code&amp; ec);

    uintmax_t    <a href="#hard_link_count">hard_link_count</a>(const path&amp; p);
    uintmax_t    <a href="#hard_link_count">hard_link_count</a>(const path&amp; p, system::error_code&amp; ec);

    const path&amp;  <a href="#initial_path">initial_path</a>();
    const path&amp;  <a href="#initial_path">initial_path</a>(<code>system::error_code&amp; ec</code>);

    bool         <a href="#is_directory">is_directory</a>(file_status s) noexcept;
    bool         <a href="#is_directory2">is_directory</a>(const path&amp; p);
    bool         <a href="#is_directory2">is_directory</a>(const path&amp; p,
                   system::error_code&amp; ec) noexcept;

    bool         <a href="#is_empty">is_empty</a>(const path&amp; p);
    bool         <a href="#is_empty">is_empty</a>(const path&amp; p, system::error_code&amp; ec);

    bool         <a href="#is_other">is_other</a>(file_status s) noexcept;
    bool         <a href="#is_other2">is_other</a>(const path&amp; p,);
    bool         <a href="#is_other2">is_other</a>(const path&amp; p, system::error_code&amp; ec) noexcept;

    bool         <a href="#is_regular_file">is_regular_file</a>(file_status s) noexcept;
    bool         <a href="#is_regular_file2">is_regular_file</a>(const path&amp; p);
    bool         <a href="#is_regular_file2">is_regular_file</a>(const path&amp; p,
                   system::error_code&amp; ec) noexcept;

    bool         <a href="#is_symlink">is_symlink</a>(file_status s noexcept);
    bool         <a href="#is_symlink2">is_symlink</a>(const path&amp; p);
    bool         <a href="#is_symlink2">is_symlink</a>(const path&amp; p, system::error_code&amp; ec) noexcept;

    std::time_t  <a href="#last_write_time">last_write_time</a>(const path&amp; p);
    std::time_t  <a href="#last_write_time">last_write_time</a>(const path&amp; p, system::error_code&amp; ec);
    void         <a href="#last_write_time2">last_write_time</a>(const path&amp; p, const std::time_t new_time);
    void         <a href="#last_write_time2">last_write_time</a>(const path&amp; p, const std::time_t new_time,
                                 system::error_code&amp; ec);

    path         <a href="#read_symlink">read_symlink</a>(const path&amp; p);
    path         <a href="#read_symlink">read_symlink</a>(const path&amp; p, system::error_code&amp; ec);

    path         <a href="#relative">relative</a>(const path&amp; p, system::error_code&amp; ec);
    path         <a href="#relative">relative</a>(const path&amp; p, const path&amp; base=current_path());
    path         <a href="#relative">relative</a>(const path&amp; p,
                     const path&amp; base, system::error_code&amp; ec);

    bool         <a href="#remove">remove</a>(const path&amp; p);
    bool         <a href="#remove">remove</a>(const path&amp; p, system::error_code&amp; ec);

    uintmax_t    <a href="#remove_all">remove_all</a>(const path&amp; p);
    uintmax_t    <a href="#remove_all">remove_all</a>(const path&amp; p, system::error_code&amp; ec);

    void         <a href="#rename">rename</a>(const path&amp; from, const path&amp; to);
    void         <a href="#rename">rename</a>(const path&amp; from, const path&amp; to,
                   system::error_code&amp; ec);

    void         <a href="#resize_file">resize_file</a>(const path&amp; p, uintmax_t size);
    void         <a href="#resize_file2">resize_file</a>(const path&amp; p, uintmax_t size,
                   system::error_code&amp; ec);

    <a href="#space_info">space_info</a>   <a href="#space">space</a>(const path&amp; p);
    <a href="#space_info">space_info</a>   <a href="#space">space</a>(const path&amp; p, system::error_code&amp; ec);

    <a href="#file_status">file_status</a>  <a href="#status">status</a>(const path&amp; p);
    <a href="#file_status">file_status</a>  <a href="#status">status</a>(const path&amp; p, system::error_code&amp; ec) noexcept;

    bool         <a href="#status_known">status_known</a>(file_status s) noexcept;

    <a href="#file_status">file_status</a>  <a href="#symlink_status">symlink_status</a>(const path&amp; p);
    <a href="#file_status">file_status</a>  <a href="#symlink_status">symlink_status</a>(const path&amp; p,
                   system::error_code&amp; ec) noexcept;

    path         <a href="#system_complete">system_complete</a>(const path&amp; p);
    path         <a href="#system_complete">system_complete</a>(const path&amp; p, system::error_code&amp; ec);

    path         <a href="#temp_directory_path">temp_directory_path</a>();
    path         <a href="#temp_directory_path">temp_directory_path</a>(system::error_code&amp; ec);

    path         <a href="#unique_path">unique_path</a>(const path&amp; model=&quot;%%%%-%%%%-%%%%-%%%%&quot;);
    path         <a href="#unique_path">unique_path</a>(const path&amp; model, system::error_code&amp; ec);

    path         <a href="#weakly_canonical">weakly_canonical</a>(const path&amp; p, const path&amp; base=current_path());
    path         <a href="#weakly_canonical">weakly_canonical</a>(const path&amp; p, system::error_code&amp; ec);
    path         <a href="#weakly_canonical">weakly_canonical</a>(const path&amp; p, const path&amp; base,
                   system::error_code&amp; ec);

  }  // namespace filesystem
}  // namespace boost</pre>



<h2><a name="Error-reporting">Error reporting</a> [fs.err.report]</h2>
<p>Filesystem library functions often provide two overloads, one that
throws an exception to report file system errors, and another that sets an <code>error_code</code>.</p>
<blockquote>
<p>[<i>Note:</i> This supports two common use cases:</p>
<ul>
  <li>
  <p>Uses where file system
errors are truly exceptional and indicate a serious failure. Throwing an
  exception is the most appropriate response. This is the preferred default for
  most everyday programming.<br>
&nbsp;</li>
  <li>
  <p>Uses where file system errors are routine and do not necessarily represent
  failure. Returning an error code is the most appropriate response. This allows
  application specific error handling, including simply ignoring the error.</li>
</ul>
  <p><i>—end note</i>]</p>
</blockquote>
<p>Functions <b>not</b> having an argument of type <code>system::error_code&amp;</code> report errors as follows, unless otherwise specified:</p>
  <ul>
  <li>
  <p>When a call by the
  implementation to an operating system or other underlying API results in an
  error that prevents the function from meeting its specifications, an exception
  of type
<code>filesystem_error</code> is thrown.<br>
&nbsp;</li>
  <li>
  <p>Failure to allocate storage is reported by throwing an exception as described in the C++ standard,
  17.6.4.10 [res.on.exception.handling].<br>
&nbsp;</li>
  <li>
  <p>Destructors throw nothing.</li>
  </ul>
  <p>Functions having an argument of type <code>system::error_code&amp;</code> report errors as follows, unless otherwise
  specified:</p>
<ul>
  <li>
  <p>If a call by the
  implementation to an operating system or other underlying API results in an
  error that prevents the function from meeting its specifications, the
<code>system::error_code&amp;</code> argument is set as
  appropriate for the specific error. Otherwise, <code>clear()</code>
  is called on the
<code>system::error_code&amp;</code> argument.<br>
&nbsp;</li>
  <li>
  <p>Failure to allocate storage is reported by
  throwing an exception as described in the C++ standard,
  17.6.4.10 [res.on.exception.handling].</li>
</ul>
<h2><a name="class-path">Class <code>path</code> [class.path]</a></h2>
<p>An object of class <code>path</code> represents a <a href="#path">path</a>,
and contains a <a href="#pathname">pathname</a> Such an object is concerned only with the lexical and syntactic aspects
of a path. The path does not necessarily exist in external storage, and the
pathname is not necessarily valid for the current operating
system or for a particular file system.</p>

<div dir="ltr">

<pre>namespace boost
{
  namespace filesystem
  {
      class path
      {
      public:
        typedef <b><i><a href="#value_type">see below</a></i></b>                                    value_type;
        typedef std::basic_string&lt;value_type&gt;                string_type;
        typedef std::codecvt&lt;wchar_t, char, std::mbstate_t&gt;  codecvt_type;
        constexpr value_type                                 dot;
        constexpr value_type                                 separator;
        constexpr value_type                                 preferred_separator;

        // <a href="#path-constructors">constructors</a> and destructor
        path();
        path(const path&amp; p);
        path(path&amp;&amp; p) noexcept;

        template &lt;class <a href="#Source">Source</a>&gt;
          path(Source const&amp; source, const codecvt_type&amp; cvt=codecvt());

        template &lt;class <a href="#InputIterator">InputIterator</a>&gt;
          path(InputIterator begin, InputIterator end,
            const codecvt_type&amp; cvt=codecvt());

       ~path();

        // <a href="#path-assignments">assignments</a>
        path&amp; operator=(const path&amp; p);
        path&amp; operator=(path&amp;&amp; p) noexcept;

        template &lt;class <a href="#Source">Source</a>&gt;
          path&amp; operator=(Source const&amp; source);

        path&amp; assign(const path&amp; p);
        path&amp; assign(path&amp;&amp; p) noexcept;
        template &lt;class <a href="#Source">Source</a>&gt;
          path&amp; assign(Source const&amp; source,
            const codecvt_type&amp; cvt=codecvt())

        template &lt;class <a href="#InputIterator">InputIterator</a>&gt;
          path&amp; assign(InputIterator begin, InputIterator end,
            const codecvt_type&amp; cvt=codecvt());

        // <a href="#path-appends">appends</a>
        path&amp; operator/=(const path&amp; p);

        template &lt;class <a href="#Source">Source</a>&gt;
          path&amp; operator/=(Source const&amp; source);

        path&amp; append(const path&amp; x);
        template &lt;class <a href="#Source">Source</a>&gt;
          path&amp; append(Source const&amp; source,
            const codecvt_type&amp; cvt=codecvt());

        template &lt;class <a href="#InputIterator">InputIterator</a>&gt;
          path&amp; append(InputIterator begin, InputIterator end,
            const codecvt_type&amp; cvt=codecvt());

        // <a href="#path-concatenation">concatenation</a>
        path&amp; operator+=(const path&amp; x);
        template &lt;class <a href="#Source">Source</a>&gt;
          path&amp; operator+=(Source const&amp; source);
        path&amp; operator+=(value_type x);
        template &lt;class CharT&gt;
          path&amp; operator+=(CharT x);

        path&amp; concat(const path&amp; x);
        template &lt;class <a href="#Source">Source</a>&gt;
          path&amp; concat(Source const&amp; x,
            const codecvt_type&amp; cvt=codecvt());
        template &lt;class <a href="#InputIterator">InputIterator</a>&gt;
          path&amp; concat(InputIterator begin, InputIterator end,
            const codecvt_type&amp; cvt=codecvt());

        // <a href="#path-modifiers">modifiers</a>
        void  <a href="#path-clear">clear</a>();
        path&amp; <a href="#path-make_preferred">make_preferred</a>();
        path&amp; <a href="#path-remove_filename">remove_filename</a>();
        path&amp; <a href="#path-remove_filename_and_trailing_separators">remove_filename_and_trailing_separators</a>();
        path&amp; <a href="#path-replace_filename">replace_filename</a>(const path&amp; replacement);
        path&amp; <a href="#path-replace_extension">replace_extension</a>(const path&amp; new_extension = path());
        void  <a href="#path-swap">swap</a>(path&amp; rhs) noexcept;

        // lexical operations
        path <a href="#lexically_normal">lexically_normal</a>() const;
        path <a href="#lexically_relative">lexically_relative</a>(const path&amp; base) const;
        path <a href="#lexically_proximate">lexically_proximate</a>(const path&amp; base) const;

        // <a href="#path-native-format-observers">native format observers</a>
        const string_type&amp;  <a href="#native">native</a>() const noexcept;  // native format, encoding
        const value_type*   <a href="#c_str">c_str</a>() const noexcept;   // native().c_str()
        string_type::size_type <a href="#path-size">size</a>() const noexcept; // native().size()

        template &lt;class String&gt;
          String  <a href="#string-template">string</a>(const codecvt_type&amp; cvt=codecvt()) const;
        string    <a href="#string">string</a>(const codecvt_type&amp; cvt=codecvt()) const;
        wstring   <a href="#wstring">wstring</a>(const codecvt_type&amp; cvt=codecvt()) const;

        // <a href="#path-generic-format-observers">generic format observers</a>
        template &lt;class String&gt;
          String  <a href="#generic_string-template">generic_string</a>() const;

        string    <a href="#generic_string">generic_string</a>(const codecvt_type&amp; cvt=codecvt()) const;
        wstring   <a href="#generic_wstring">generic_wstring</a>(const codecvt_type&amp; cvt=codecvt()) const;

        // <a href="#path-compare">compare</a>
        int   <a href="#path-compare">compare</a>(const path&amp; p) const noexcept;
        int   <a href="#path-compare">compare</a>(const std::string&amp; s) const;
        int   <a href="#path-compare">compare</a>(const value_type* s) const;

        // <a href="#path-decomposition">decomposition</a>
        path  <a href="#path-root_name">root_name</a>() const;
        path  <a href="#path-root_directory">root_directory</a>() const;
        path  <a href="#path-root_path">root_path</a>() const;
        path  <a href="#path-relative_path">relative_path</a>() const;
        path  <a href="#path-parent_path">parent_path</a>() const;
        path  <a href="#path-filename">filename</a>() const;
        path  <a href="#path-stem">stem</a>() const;
        path  <a href="#path-extension">extension</a>() const;

        // <a href="#path-query">query</a>
        bool <a href="#path-query">empty</a>() const;
        bool <a href="#filename_is_dot">filename_is_dot</a>() const;
        bool <a href="#filename_is_dot_dot">filename_is_dot_dot</a>() const;
        bool <a href="#path-has_root_name">has_root_name</a>() const;
        bool <a href="#path-has_root_directory">has_root_directory</a>() const;
        bool <a href="#path-has_root_path">has_root_path</a>() const;
        bool <a href="#path-has_relative_path">has_relative_path</a>() const;
        bool <a href="#path-has_parent_path">has_parent_path</a>() const;
        bool <a href="#path-has_filename">has_filename</a>() const;
        bool <a href="#path-has_stem">has_stem</a>() const;
        bool <a href="#path-has_extension">has_extension</a>() const;
        bool <a href="#path-is_absolute">is_absolute</a>() const;
        bool <a href="#path-is_relative">is_relative</a>() const;

        // <a href="#path-iterators">iterators</a>
        class iterator;
        typedef iterator const_iterator;
        class reverse_iterator;
        typedef reverse_iterator const_reverse_iterator;

        iterator begin() const;
        iterator end() const;
        reverse_iterator rbegin() const;
        reverse_iterator rend() const;

        // <a href="#path-imbued-locale">imbued locale</a>
        static std::locale <a href="#path-imbue">imbue</a>(const std::locale&amp; loc);
        static const codecvt_type &amp; <a href="#path-codecvt">codecvt</a>();

      private:
        string_type pathname;  // <b><i>exposition only</i></b>
      };

  }  // namespace filesystem
}  // namespace boost</pre>

</div>

<p><code><a name="value_type">value_type</a></code> is a <code>typedef</code> for the
character type used by the operating system to represent pathnames.</p>


<h3><a name="path-Usage"><code>path</code> Usage concerns</a> [path.usage]</h3>

<h4>Multithreading concerns</h4>

<p>Filesystem library functions are not protected against data races. [Modifying
an object of a Filesystem library type that is shared between threads risks
undefined behavior unless objects of that type are explicitly specified as being
sharable without data races or the user supplies a locking mechanism. <i>—end
note</i>] [<i>Note:</i> Thus the Filesystem library behaves as if it were part
of the standard library, and C++ standard 17.6.4.10 <i>Shared objects and the
library</i> [res.on.objects] would thus apply.&nbsp; <i>—end note</i>]</p>

<h4>Windows concerns</h4>

<p>Visual C++ at least through version 2012 does not employ C++11-style static
initialization locks, so the initialization of <code>path::codecvt()</code> can
race, either with itself or <code>path::imbue()</code> if they are called from
a different thread. A workaround is to call:</p>

<blockquote>
<p><code>path::codecvt();&nbsp; // ensure VC++ does not race during
initialization.</code></p>

</blockquote>
<p>in the main thread before launching any additional threads. [<i>Note:</i> The
obvious fix of the Filesystem implementation doing the locking doesn't work
because of unrelated problems with the Microsoft compiler; for static linking
the runtime tries to do the initialization before main() starts, but doesn't
permit operating system lock calls at that time.&nbsp; <i>—end note</i>]</p>

<h4>POSIX concerns</h4>

<p>Filesystem library initialization may throw an exception on POSIX
systems (e.g. Linux, but not Mac OS X) that use environmental variables to
determine the encoding of paths. This happens when <code>std::locale(&quot;&quot;)</code>
throws because an environmental variable such as LANG is set to an invalid
value, so it can affect any use of&nbsp; <code>std::locale(&quot;&quot;)</code>, not just
the Filesystem library. Filesystem uses lazy initialization so the exception is
only thrown if a valid <code>std::locale(&quot;&quot;)</code> is actually needed, and also
so that the exception is thrown after <code>main()</code> starts.</p>

<p>Rather than waiting until a call to some Filesystem library function
unexpectedly triggers the exception when it calls <code>path::codecvt()</code>,
a program that needs be highly robust against environmental variable problems
may want to preemptively call <code>std::locale(&quot;&quot;)</code> within a try block,
catch the exception, and diagnose or repair the invalid environmental variable.</p>

<h3><a name="path-Conversions"><code>path</code> Conversions</a> [path.cvt]</h3>
<h4><code>path</code> argument conversions [<a name="path.arg.convert">path.arg.cvt</a>]</h4>
<h5><a name="path-Conversions-to-native-format"><code>path</code> argument
format conversions</a> [path.arg.fmt.cvt]</h5>
<p>Member function arguments that take character sequences representing paths
may use the <a href="#generic-pathname-format">generic pathname format</a> or
the <a href="#native-pathname-format">native pathname format</a>. Iff such  arguments
are in the generic format and the generic format is not acceptable to the
operating system as a native path,  conversion to native format shall be performed
during the processing of the argument. See [<a href="#Operating-system-examples">fs.os.examples</a>].</p>
<blockquote>
<p>[<i>Note:</i> Depending on the operating system, there may be no unambiguous way for an implementation to
always be able to distinguish between native format and generic format arguments.
This is by design as it simplifies use for operating systems that do not require
disambiguation. Should an implementation encounter an
operating system where disambiguation is required, an implementation can defined
an extension to distinguish between the formats.<i>
—end note</i>]</p>
</blockquote>

<p>If the native format requires
paths for regular files to be formatted differently from paths for directories, the
path shall be treated as a directory path if last element is a separator,
otherwise it shall be treated as a regular file path.</p>

<h5><a name="path-Encoding-conversions"><code>
path</code> argument encoding conversions</a>
[path.arg.encoding.cvt]</h5>
<p>For member function arguments that take character sequences representing
paths, if the value type of the argument is not <code>value_type</code> and one
of the value types is <code>char</code> and the other is <code>wchar_t</code>, conversion to <code>value_type</code>
shall be performed by the <code>path::codecvt()</code> facet. ([<a href="#path-imbued-locale">path.imbued.locale</a>]).</p>
<h4><a name="path-Conversions-to-generic-format"><code>path</code> Conversions
to generic format</a> [fs.cvt.to.generic]</h4>
<p><a href="#path-generic-format-observers">Generic format observer</a> functions
shall return strings formatted according to the <a href="#generic-pathname-format">generic pathname format</a>
using <i>preferred-separator</i>. See [<a href="#Operating-system-examples">fs.os.examples</a>].</p>
<h3><a name="path-Requirements"><code>path</code> Requirements</a> [path.req]</h3>
<p>Template parameters named <code><a name="InputIterator">InputIterator</a></code> are required to meet the
requirements for a C++ standard library <code>InputIterator</code> compliant iterator. The iterator's value type is required
to be one of: <code>char</code>, <code>wchar_t</code>. Collectively, these types are referred to as supported path character types.</p>
<p>Template parameters named <code><a name="Source">Source</a></code> are required to be one of:</p>
<ul>
  <li>
  <p>A <code>std::basic_string</code>, <code>std::basic_string_view</code>, <code>boost::container::basic_string</code> or <code>boost::basic_string_view</code> specialization with a value type of one of the supported path character types.</li>
  <li>
  <p><b>v3, deprecated:</b> A container with a value type of one of the supported path character types.</li>
  <li>
  <p>A pointer into a null terminated string. The value type is required
  to be a supported path character type.</li>
  <li>
  <p>A C-array of supported path character type containing a null terminated string.</li>
  <li>
  <p>A <code>boost::filesystem::directory_entry</code>.</li>
</ul>

<h3> <a name="path-constructors"> <code>
<font size="4">path</font></code> constructors</a> [path.construct]</h3>

<pre>template &lt;class <a href="#Source">Source</a>&gt;
path(Source const&amp; source, const codecvt_type&amp; cvt=codecvt());</pre>


<pre>template &lt;class <a href="#InputIterator">InputIterator</a>&gt;
path(InputIterator begin, InputIterator end, const codecvt_type&amp; cvt=codecvt());</pre>

<blockquote>
  <p><i>Effects:</i> Stores the contents [<code>begin</code>,<code>end</code>)
  of <code>source</code> in <code>pathname</code>, converting format and
  encoding if required ([<a href="#path.arg.convert">path.arg.convert</a>]).</p>
</blockquote>
<h3> <a name="path-assignments"> <code>
<font size="4">path</font></code> assignments</a> [path.assign]</h3>

<pre>path&amp; operator=(const path&amp; p);
path&amp; operator=(path&amp;&amp; p) noexcept;
template &lt;class <a href="#Source">Source</a>&gt;
path&amp; operator=(Source const&amp; source);
path&amp; assign(const path&amp; p);
path&amp; assign(path&amp;&amp; p) noexcept;
template &lt;class <a href="#Source">Source</a>&gt;
path&amp; assign(Source const&amp; source, const codecvt_type&amp; cvt=codecvt());
template &lt;class <a href="#InputIterator">InputIterator</a>&gt;
path&amp; assign(InputIterator begin, InputIterator end, const codecvt_type&amp; cvt=codecvt());</pre>

<blockquote>
  <p><i>Effects:</i> Stores the contents [<code>begin</code>,<code>end</code>)
  or <code>source</code> or <code>p</code> in <code>pathname</code>, converting format and
  encoding if required ([<a href="#path.arg.convert">path.arg.convert</a>]). </p>
  <p>
  <i>Returns: </i><code>*this</code></p>
  </blockquote>
<h3><a name="path-appends"><code>path</code> appends</a>
[path.append]</h3>
  <p>The append operations use <code>
  operator/=</code> to denote their semantic effect of appending <i>
  preferred-separator</i> when needed. </p>

<pre>path&amp; operator/=(const path&amp; p);
path&amp; append(const path&amp; p);</pre>

<blockquote>
  <p><i>Effects:</i></p>
  <blockquote>
    <p><b>v3:</b> Appends <code>path::preferred_separator</code> to <code>pathname</code>,
    converting format and encoding if required ([<a href="#path.arg.convert">path.arg.convert</a>]), unless:</p>
    <ul>
    <li>
    <p>an added separator
    would be redundant, or</li>
    <li>
    <p>would change a relative path to an absolute path, or</li>
    <li>
    <p><code>p.empty()</code>, or</li>
    <li>
    <p><code>*p.native().cbegin()</code> is a directory separator.</li>
    </ul>
    <p>Then appends <code>p.native()</code> to <code>pathname</code>.</p>
    <p><b>v4:</b> If <code>p.<a href="#path-is_absolute">is_absolute</a>() || (p.<a href="#path-has_root_name">has_root_name</a>() &amp;&amp; p.<a href="#path-root_name">root_name</a>() != <a href="#path-root_name">root_name</a>())</code>, assigns <code>p</code> to <code>*this</code>. Otherwise, modifies <code>*this</code> as if by these steps:
    <ul>
      <li>If <code>p.<a href="#path-has_root_name">has_root_directory</a>()</code>, removes root directory and relative path, if any.</li>
      <li>Let <code>x</code> be a <code>path</code> with contents of <code>p</code> without a root name. If <code><a href="#path-has_filename">has_filename</a>()</code> is <code>true</code> and <code>x</code> does not start with a directory separator, appends <code>path::preferred_separator</code>.</li>
      <li>Appends <code>x.native()</code>.</li>
    </ul>
    </p>
    <p>[<i>Note:</i> Whether the path is absolute or not depends on the target OS conventions. Because of this, the result of append operation may be different for different operating systems for some paths. For example, <code>path(&quot;//net/foo&quot;) / &quot;/bar&quot;</code> will result in <code>&quot;/bar&quot;</code> on POSIX systems and <code>&quot;//net/foo/bar&quot;</code> on Windows because <code>&quot;/bar&quot;</code> is an absolute path on POSIX systems but not on Windows. For portable behavior avoid appending paths with non-empty <a href="#path-root_path">root path</a>. <i>—end note</i>]</p>
  </blockquote>
  <p><i>Returns: </i><code>*this</code></p>
</blockquote>

<pre>template &lt;class <a href="#Source">Source</a>&gt;
path&amp; operator/=(Source const &amp; source);
template &lt;class <a href="#Source">Source</a>&gt;
path&amp; append(Source const &amp; source, const codecvt_type&amp; cvt=codecvt());
template &lt;class <a href="#InputIterator">InputIterator</a>&gt;
path&amp; append(InputIterator begin, InputIterator end, const codecvt_type&amp; cvt=codecvt());</pre>

  <blockquote>
  <p><i>Effects:</i></p>
  <blockquote>
    <p>As if <code>append(path(<i>args</i>))</code>, where <code><i>args</i></code> is the list of arguments passed to the operation.</p>
  </blockquote>
  <p><i>Returns: </i><code>*this</code></p>
  </blockquote>

<h3><a name="path-concatenation"><code>path</code> concatenation</a> [path.concat]</h3>

<pre>path&amp; operator+=(const path&amp; p);
path&amp; operator+=(value_type x);
template &lt;class <a href="#Source">Source</a>&gt;
path&amp; operator+=(Source const&amp; source);
template &lt;class CharT&gt;
path&amp; operator+=(CharT x);
path&amp; concat(const path&amp; p);
template &lt;class <a href="#Source">Source</a>&gt;
path&amp; concat(Source const&amp; source, const codecvt_type&amp; cvt=codecvt());
template &lt;class <a href="#InputIterator">InputIterator</a>&gt;
path&amp; concat(InputIterator begin, InputIterator end, const codecvt_type&amp; cvt=codecvt());</pre>

<blockquote><p><i>Postcondition:</i> <code>native() == prior_native + <i>effective-argument</i></code>,
 where <code>prior_native</code> is <code>native()</code> prior to the call to <code>operator+=</code>,
 and <code><i>effective-argument</i></code> is:</p>
 <ul><li>
   <p><code>p.native()</code> if <code>p</code> is present and is <code>const path&amp;</code>, otherwise</li>
 <li>
 <p><code>s</code>, where <code>s</code> is
  <code>std::basic_string&lt;typename std::iterator_traits&lt;InputIterator&gt;::value_type&gt;<br>s(begin, end)</code>,
   if <code>begin</code> and <code>end</code> arguments are present, otherwise</li>
 <li>
 <p><code>x</code>.</li></ul><p>If the value type of <code><i>effective-argument</i></code> would not be <code>path::value_type</code>, the actual argument or argument range is first
   converted so that <code><i>effective-argument</i></code> has value type <code>path::value_type</code>.</li> </p>
 <p><i>Returns: </i><code>*this</code></p>
 </blockquote>
<h3><a name="path-modifiers"> <code>
path</code> modifiers</a> [path.modifiers]</h3>

  <pre>void <a name="path-clear">clear</a>();</pre>

<blockquote>
<p><i>Postcondition:</i> <code>this-&gt;empty()</code> is true.</p>
</blockquote>

<pre>path&amp; <a name="path-make_preferred">make_preferred</a>();</pre>

<blockquote>
  <p><i>Effects:</i> <i>directory-separator</i>s are converted to <i>preferred-separator</i>s.
  See [<a href="#Operating-system-examples">fs.os.examples</a>].</p>
  <p><i>Returns:</i> <code>*this</code></p>
</blockquote>



<pre>path&amp; <a name="path-remove_filename">remove_filename</a>();</pre>

<blockquote>
  <p><i>Effects:</i> <b>v3:</b> As if, <code>*this = parent_path();</code></p>
  <p>[<i>Note:</i> This function is needed to efficiently implement <code>directory_iterator</code>. It is exposed to allow additional uses. The actual
  implementation may be much more efficient than <code>*this = parent_path()</code>&nbsp; <i>—end
  note</i>]</p>
  <p><b>v4:</b> Removes the <code>filename()</code> path element.</p>
  <p>[<i>Note:</i> Unlike <b>v3</b>, the trailing directory separator(s) are not removed.<i>—end
  note</i>]</p>
  <p><i>Returns:</i> <code>*this</code>.
</blockquote>

<pre>path&amp; <a name="path-remove_filename_and_trailing_separators">remove_filename_and_trailing_separators</a>();</pre>

<blockquote>
  <p><i>Effects:</i> As if, <code>*this = parent_path();</code></p>
  <p>[<i>Note:</i> This function is similar to <code>path::remove_filename</code> from <b>v3</b>, but is also usable in <b>v4</b>.<i>—end
  note</i>]</p>
  <p><i>Returns:</i> <code>*this</code>.
</blockquote>

<pre>path&amp; <a name="path-replace_filename">replace_filename</a>(const path&amp; replacement);</pre>

<blockquote>
  <p><i>Effects:</i> As if, <code>remove_filename().append(replacement);</code></p>
  <p><i>Returns:</i> <code>*this</code>.
</blockquote>

<pre>path&amp; <a name="path-replace_extension">replace_extension</a>(const path&amp; new_extension = path());</pre>

<blockquote>
  <p><i>Effects:</i></p>
  <ul>
    <li>
    <p>Any existing <code>extension()</code> is removed from the stored path,
    then</li>
    <li>
    <p>iff
  <code>new_extension</code> is not empty and does not begin with a dot
    character, a dot character is appended to the stored path, then</li>
    <li>
  <p>
  <code>new_extension</code> is appended to the stored path.</li>
  </ul>
  <p><i>Returns:</i> <code>*this</code></p>
</blockquote>

<pre><code>void <a name="path-swap">swap</a>(path&amp; rhs) noexcept;</code></pre>

<blockquote>
  <p><i>Effects:</i> Swaps the contents of the two paths.</p>
  <p><i>Complexity: </i>constant time.</p>
</blockquote>

  <h3><a name="path-lexical-operations"><code>path</code> lexical operations</a>
  [path.lex.ops]</h3>
  <pre>path <a name="lexically_normal">lexically_normal</a>() const;</pre>
<blockquote>
<p><i>Overview:</i> Returns <code>*this</code> with redundant current directory
(<i>dot</i>), parent directory (<i>dot-dot</i>), and <i>directory-separator</i> elements removed.</p>
<p><i>Returns:</i> <code>*this</code> in <a href="#normal-form">normal form</a>.</p>
<p><i>Remarks:</i> Uses <code>operator/=</code> to compose the returned path.</p>
<p>[<i>Example:</i></p>
<p><pre>std::cout &lt;&lt; path(&quot;foo/./bar/..&quot;).lexically_normal() &lt;&lt; std::endl;    // outputs &quot;foo&quot;
std::cout &lt;&lt; path(&quot;foo/.///bar/../&quot;).lexically_normal() &lt;&lt; std::endl; // v3: outputs &quot;foo/.&quot;
                                                                      // v4: outputs &quot;foo/&quot;</pre></p>
<p>On Windows, the
returned path&#39;s <i>directory-separator</i> characters will be backslashes rather than slashes, but that
does not affect <code>path</code> equality.<i> —end example</i>]</p>
</blockquote>

<pre>path <a name="lexically_relative">lexically_relative</a>(const path&amp; base) const;</pre>
  <blockquote>
  <p><i>Overview:</i> Returns <code>*this</code> made relative to <code>base</code>.
  Treats empty or identical paths as corner cases, not errors. Does not resolve
  symlinks. Does not first normalize <code>*this</code> or <code>base</code>.</p>

  <p><i>Remarks:</i> Uses <code>std::mismatch(begin(), end(), base.begin(), base.end())</code>, to determine the first mismatched element of
  <code>*this</code> and <code>base</code>. Uses <code>operator==</code> to
  determine if elements match. </p>

  <p><i>Returns:</i> </p>

  <ul>
    <li>
  <code>path()</code> if the first mismatched element of <code>*this</code> is equal to <code>
  begin()</code> or the first mismatched element
  of <code>base</code> is equal to <code>base.begin()</code>, or<br>&nbsp;
    </li>
    <li>
  <code>path(&quot;.&quot;)</code> if the first mismatched element of <code>
  *this</code> is equal to <code>
  end()</code> and the first mismatched element
  of <code>base</code> is equal to <code>base.end()</code>, or<br>&nbsp;
    </li>
    <li>An object of class <code>path</code> composed via application of <code>
    operator/= path(&quot;..&quot;)</code> for each element in the half-open
    range [first
    mismatched element of <code>base</code>, <code>base.end()</code>), and then
    application of <code>operator/=</code> for each element in the half-open
    range
    [first mismatched element of <code>*this</code>, <code>end()</code>).
    </li>
</ul>

<p>[<i>Example:</i></p>
<p><code>assert(path(&quot;/a/d&quot;).lexically_relative(&quot;/a/b/c&quot;) ==  &quot;../../d&quot;);<br>
assert(path(&quot;/a/b/c&quot;).lexically_relative(&quot;/a/d&quot;) ==  &quot;../b/c&quot;);<br>
assert(path(&quot;a/b/c&quot;).lexically_relative(&quot;a&quot;) == &quot;b/c&quot;);<br>
assert(path(&quot;a/b/c&quot;).lexically_relative(&quot;a/b/c/x/y&quot;) ==  &quot;../..&quot;);<br>
assert(path(&quot;a/b/c&quot;).lexically_relative(&quot;a/b/c&quot;) ==  &quot;.&quot;);<br>
assert(path(&quot;a/b&quot;).lexically_relative(&quot;c/d&quot;) ==  &quot;&quot;);</code></p>
<p>The above assertions will succeed.<i> </i>On Windows, the
returned path&#39;s <i>directory-separator</i>s will be backslashes rather than
forward slashes, but that
does not affect <code>path</code> equality.<i> —end example</i>]</p>

  <p>[<i>Note:</i> If symlink following semantics are desired, use the operational function <code>
  <a href="#relative">relative</a></code>&nbsp; <i>—end note</i>]</p>

  <p>[<i>Note:</i> If <a href="#lexically_normal">normalization</a> is needed to ensure
  consistent matching of elements, apply <code><a href="#lexically_normal">lexically_normal()</a></code>
  to <code>*this</code>, <code>base</code>, or both. <i>—end note</i>]</p>

<pre>path <a name="lexically_proximate">lexically_proximate</a>(const path&amp; base) const;</pre>
<blockquote>
<p><i>Returns:</i> If <code>lexically_relative(base)</code> returns a non-empty path, returns that path.
Otherwise returns <code>*this</code>.</p>

  <p>[<i>Note:</i> If symlink following semantics are desired, use the operational function <code>
  <a href="#relative">relative</a></code>&nbsp; <i>—end note</i>]</p>

  <p>[<i>Note:</i> If <a href="#lexically_normal">normalization</a> is needed to ensure
  consistent matching of elements, apply <code><a href="#lexically_normal">lexically_normal()</a></code>
  to <code>*this</code>, <code>base</code>, or both. <i>—end note</i>]</p>

</blockquote>


<h3> <a name="path-native-format-observers"><code><font size="4">path</font></code> native format observers</a>
[path.native.obs]</h3>
<p>The string returned by all native format observers is in the <a href="#native-pathname-format">native pathname format</a>.</p>

<pre>const string_type&amp; <a name="native">native</a>() const noexcept;</pre>

<blockquote>
<p><i>Returns:</i> <code>pathname</code>.</p>
</blockquote>

<pre>const value_type* <a name="c_str">c_str</a>() const noexcept;</pre>

<blockquote>
<p><i>Returns:</i> <code>pathname.c_str()</code>.</p>
</blockquote>

<pre>string_type::size_type <a name="path-size">size</a>() const noexcept;</pre>

<blockquote>
<p><i>Returns:</i> <code>pathname.size()</code>.</p>
</blockquote>

<pre>template &lt;class String&gt;
String <a name="string-template">string</a>(const codecvt_type&amp; cvt=codecvt()) const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>pathname</code>.</p>
<p><i>Remarks:</i> If <code>string_type</code> is a different type than <code>String</code>, conversion is performed by <code>cvt</code>.</p>
</blockquote>

<pre>string <a name="string">string</a>(const codecvt_type&amp; cvt=codecvt()) const;
wstring <a name="wstring">wstring</a>(const codecvt_type&amp; cvt=codecvt()) const;</pre>

<blockquote>
<p><i>Returns:</i> <code>pathname</code>.</p>
<p><i>Remarks:</i> If <code>string_type</code> is a different type than
function's return type, conversion is performed by <code>cvt</code>.</p>
</blockquote>

<h3> <a name="path-generic-format-observers"><code><font size="4">path</font></code> generic format observers</a>
[path.generic.obs]</h3>
<p>The string returned by all generic format observers is in the <a href="#generic-pathname-format">generic pathname format</a>.</p>

<pre>template &lt;class String&gt;
String <a name="generic_string-template">generic_string</a>(const codecvt_type&amp; cvt=codecvt()) const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>pathname</code>.</p>
<p><i>Remarks:</i> If <code>string_type</code> is a different type than <code>String</code>, conversion is performed by <code>cvt</code>.</p>
</blockquote>

<pre>string <a name="generic_string">generic_string</a>(const codecvt_type&amp; cvt=codecvt()) const;
wstring <a name="generic_wstring">generic_wstring</a>(const codecvt_type&amp; cvt=codecvt()) const;</pre>

<blockquote>
<p><i>Returns:</i> <code>pathname</code>.</p>
<p><i>Remarks:</i>&nbsp; If <code>string_type</code> is a different type than
function's return type, conversion is performed by <code>cvt</code>.</p>
</blockquote>

<h3> <a name="path-compare"><code>path</code> compare</a> [path.compare]</h3>

<pre>int compare(const path&amp; p) const noexcept;</pre>

<blockquote>
  <p><i>Returns:</i> A value less than 0 if the elements of <code>*this</code> are lexicographically less than the elements of <code>p</code>, otherwise a
  value greater than 0 if the elements of <code>*this</code> are
  lexicographically greater than the elements of <code>p</code>, otherwise 0.</p>
  <p>Remark: The elements are determined as if by iteration over the half-open
  range [<code>begin()</code>, <code>end()</code>) for <code>*this</code> and&nbsp; <code>p</code>.</p>
</blockquote>

<pre>int compare(const std::string&amp; s) const</pre>

<blockquote>
  <p><i>Returns:</i> <code>compare(path(s))</code>.</p>
</blockquote>

<pre>int compare(const value_type* s) const</pre>

<blockquote>
  <p><i>Returns:</i> <code>compare(path(s))</code>.</p>
</blockquote>
<h3> <a name="path-decomposition"> <code><font size="4">path</font></code> decomposition</a>
[path.decompose]</h3>
<p><span style="background-color: #E0E0E0"><i>See the <a href="#path-decomposition-table">Path decomposition table</a> for examples
for values returned by decomposition functions. The <a href="tutorial.html#Using-path-decomposition">Tutorial</a> may also be
helpful.</i></span></p>

<pre>path <a name="path-root_name">root_name</a>() const;</pre>

<blockquote>
<p><i>Returns:</i> <i>root-name,</i> if <code>pathname</code> includes <i>root-name</i>, otherwise <code>path()</code>. </p>
</blockquote>

<pre>path <a name="path-root_directory">root_directory</a>() const;</pre>

<blockquote>
<p><i>Returns:</i> <i>root-directory</i>, if <code>pathname</code> includes <i>root-directory</i>, otherwise <code>path()</code>.</p>
<p>If <i>root-directory</i> is composed of <i>slash name</i>, <i>slash</i> is
excluded from the returned string.</p>
</blockquote>

<pre>path <a name="path-root_path">root_path</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code><a href="#path-root_name">root_name</a>() / <a href="#path-root_directory">root_directory</a>()</code></p>
</blockquote>

<pre>path <a name="path-relative_path">relative_path</a>() const;</pre>

<blockquote>
<p><i>Returns:</i> A <code>path</code> composed from <code>pathname</code>, if <code>!empty()</code>, beginning
with the first <i>filename</i> after <i>root-path</i>. Otherwise, <code>path()</code>.</p>
</blockquote>

<pre>path <a name="path-parent_path">parent_path</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>(empty() || begin() == --end()) ? path() : <i>pp</i></code>, where <code><i>pp</i></code> is constructed as if by
  starting with an empty <code>path</code> and successively applying <code>operator/=</code> for each element in the range <code>begin()</code>, <code>--end()</code>.</p>
  <p>[<i>Example:</i></p>
  <blockquote>

    <pre>std::cout &lt;&lt; path(&quot;/foo/bar.txt&quot;).parent_path(); // outputs &quot;/foo&quot;
std::cout &lt;&lt; path(&quot;/foo/bar&quot;).parent_path();     // outputs &quot;/foo&quot;
std::cout &lt;&lt; path(&quot;/foo/bar/&quot;).parent_path();    // outputs &quot;/foo/bar&quot;
std::cout &lt;&lt; path(&quot;/&quot;).parent_path();            // outputs &quot;&quot;
std::cout &lt;&lt; path(&quot;.&quot;).parent_path();            // outputs &quot;&quot;
std::cout &lt;&lt; path(&quot;..&quot;).parent_path();           // outputs &quot;&quot;</pre>

  </blockquote>
  <p> See the last bullet item in the <a href="#path-iterators">
  forward traversal order</a> list for why the <code>&quot;/foo/bar/&quot;</code> example
  doesn&#39;t output <code>&quot;/foo&quot;</code>.</p>
  <p> <i>—end example</i>]</p>
</blockquote>

<pre>path <a name="path-filename">filename</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <b>v3:</b> <code>empty() ? path() : *--end()</code><br/>
    <b>v4:</b> <code>*this == <a href="#path-root_path">root_path</a>() ? path() : *--end()</code></p>
  <p>[<i>Example:</i></p>
  <blockquote>

    <pre>std::cout &lt;&lt; path(&quot;/foo/bar.txt&quot;).filename(); // outputs &quot;bar.txt&quot;
std::cout &lt;&lt; path(&quot;/foo/bar&quot;).filename();     // outputs &quot;bar&quot;
std::cout &lt;&lt; path(&quot;/foo/bar/&quot;).filename();    // v3 outputs &quot;.&quot;
                                              // v4 outputs &quot;&quot;
std::cout &lt;&lt; path(&quot;/&quot;).filename();            // v3 outputs &quot;/&quot;
                                              // v4 outputs &quot;&quot;
std::cout &lt;&lt; path(&quot;.&quot;).filename();            // outputs &quot;.&quot;
std::cout &lt;&lt; path(&quot;..&quot;).filename();           // outputs &quot;..&quot;</pre>

  </blockquote>
  <p> See the last bullet item in the <a href="#path-iterators">
  forward traversal order</a> list for why the <code>&quot;/foo/bar/&quot;</code> example
  doesn&apos;t output <code>&quot;bar&quot;</code>.</p>
  <p> <i>—end example</i>]</p>
</blockquote>

<pre>path <a name="path-stem">stem</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> If <code>p.<a href="path-filename">filename</a>()</code> does not contain dots, consist solely of one
  or to two dots, <i>[Since <b>v4</b>:</i> or contains exactly one dot as the initial character,<i>]</i> returns <code>p.filename()</code>.
  Otherwise returns the substring of <code>p.filename()</code> starting at its beginning and
  ending at the last dot (the dot is not included).</p>
  <p>[<i>Example:</i></p>
  <blockquote>

    <pre>std::cout &lt;&lt; path(&quot;/foo/bar.txt&quot;).stem() &lt;&lt; '\n'; // outputs &quot;bar&quot;
std::cout &lt;&lt; path(&quot;.hidden&quot;).stem() &lt;&lt; '\n';      // v3 outputs &quot;&quot;
                                                  // v4 outputs &quot;.hidden&quot;
path p = &quot;foo.bar.baz.tar&quot;;
for (; !p.extension().empty(); p = p.stem())
  std::cout &lt;&lt; p.extension() &lt;&lt; '\n';
  // outputs: .tar
  //          .baz
  //          .bar</pre>

  </blockquote>
  <p> <i>—end example</i>]</p>
</blockquote>

<pre>path <a name="path-extension">extension</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> The substring of <code>p.<a href="path-filename">filename</a>()</code> that is not included
  in <code>p.<a href="#path-stem">stem</a>()</code>.</p>
  <p><i>Remarks:</i> Implementations are permitted but not required to define additional
  behavior for file systems which append additional elements to extensions, such
  as alternate data streams or partitioned dataset names.</p>
  <p>[<i>Example:</i></p>
  <blockquote>

    <pre>std::cout &lt;&lt; path(&quot;/foo/bar.txt&quot;).extension(); // outputs &quot;.txt&quot;</pre>

  </blockquote>
  <p> <i>—end example</i>]</p>
  <p>[<i>Note:<b> </b></i>The dot is included in the return value so that it is
  possible to distinguish between no extension and an empty extension.
  See <a href="https://lists.boost.org/Archives/boost/2010/02/162028.php">
  https://lists.boost.org/Archives/boost/2010/02/162028.php</a> for more
  extensive rationale.&nbsp; <i>—end note</i>]</p>
</blockquote>
<h3> <a name="path-query"> <code><font size="4">path</font></code> query</a> [path.query]</h3>

<pre>bool <a name="path-empty">empty</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>m_pathname.empty()</code>.</p>
</blockquote>

<pre>bool <a name="filename_is_dot">filename_is_dot</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>filename() == path(&quot;.&quot;)</code></p>
  <p>[<i>Example:</i></p>
  <blockquote>

    <pre>std::cout &lt;&lt; path(&quot;.&quot;).filename_is_dot();     // outputs 1
std::cout &lt;&lt; path(&quot;/.&quot;).filename_is_dot();    // outputs 1
std::cout &lt;&lt; path(&quot;foo/.&quot;).filename_is_dot(); // outputs 1
std::cout &lt;&lt; path(&quot;foo/&quot;).filename_is_dot();  // v3 outputs 1, v4 outputs 0
std::cout &lt;&lt; path(&quot;/&quot;).filename_is_dot();     // outputs 0
std::cout &lt;&lt; path(&quot;/foo&quot;).filename_is_dot();  // outputs 0
std::cout &lt;&lt; path(&quot;/foo.&quot;).filename_is_dot(); // outputs 0
std::cout &lt;&lt; path(&quot;..&quot;).filename_is_dot();    // outputs 0</pre>

  </blockquote>
  <p> See the last bullet item in the <a href="#path-iterators">forward traversal order</a>
  list for why <code>path(&quot;foo/&quot;).filename()</code> is a dot filename in <b>v3</b>.</p>
  <p> <i>—end example</i>]</p>
</blockquote>

<pre>bool <a name="filename_is_dot_dot">filename_is_dot_dot</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>filename() == path(&quot;..&quot;)</code></p>
</blockquote>

<pre>bool <a name="path-has_root_path">has_root_path</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>!<a href="#path-root_path">root_path</a>().empty()</code></p>
</blockquote>

<pre>bool <a name="path-has_root_name">has_root_name</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>!<a href="#path-root_name">root_name</a>().empty()</code></p>
</blockquote>

<pre>bool <a name="path-has_root_directory">has_root_directory</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>!<a href="#path-root_directory">root_directory</a>().empty()</code></p>
</blockquote>

<pre>bool <a name="path-has_relative_path">has_relative_path</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>!<a href="#path-has_relative_path">relative_path</a>().empty()</code></p>
</blockquote>

<pre>bool <a name="path-has_parent_path">has_parent_path</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>!<a href="#path-parent_path">parent_path</a>().empty()</code></p>
</blockquote>

<pre>bool <a name="path-has_filename">has_filename</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>!<a href="#path-filename">filename</a>().empty()</code></p>
</blockquote>

<pre>bool <a name="path-has_stem">has_stem</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>!<a href="#path-stem">stem</a>().empty()</code></p>
</blockquote>

<pre>bool <a name="path-has_extension">has_extension</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>!<a href="#path-has_extension">extension</a>().empty()</code></p>
</blockquote>

<pre>bool <a name="path-is_absolute">is_absolute</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>true</code> if the elements of <code><a href="#path-root_path">root_path</a>()</code> uniquely identify a directory, else <code>false</code>.</p>
  <p>[<i>Note:</i> Path is considered absolute on POSIX systems if it has a <a href="#path-root_directory">root directory</a>, and on Windows - if it has both <a href="#path-root_name">root name</a> and <a href="#path-root_directory">root directory</a>. <i>—end note</i>]</p>
</blockquote>

<pre>bool <a name="path-is_relative">is_relative</a>() const;</pre>

<blockquote>
  <p><i>Returns:</i> <code>!<a href="#path-is_absolute">is_absolute</a>()</code>.</p>
</blockquote>
<h3> <a name="path-iterators"> <code>
<font size="4">path</font></code> iterators</a> [path.itr]</h3>
<p> Path iterators <code>iterator</code>, <code>const_iterator</code>,
<code>reverse_iterator</code>, and <code>const_reverse_iterator</code> iterate over the elements of the stored pathname.</p>
<p> Path iterators are constant iterators satisfying
the requirements of a bidirectional iterator (C++ Std, 24.1.4 Bidirectional
iterators [lib.bidirectional.iterators]). The <code>value_type</code>&nbsp; of
an iterator is <code>path</code>.</p>
  <blockquote>
<p> [<i>Note:</i> Path iterators store their value objects internally
and when dereferenced return references to those internal objects. They cannot
be used with iterator adaptors such as <code>std::reverse_iterator</code> that
assume references obtained by dereferencing an iterator point to objects that
out-live the iterator itself. <i>—end note</i>] </p>
</blockquote>
  <p>Calling any non-const member function of a <code>path</code> object
  invalidates all iterators referring to elements of that object.</p>
<p> The forward traversal order is as follows:</p>
<ul>
  <li>The <i>root-name</i> element, if present.</li>
  <li>The <i>root-directory</i> element, if present, in the generic format.
  [<i>Note:</i> the generic format is required to ensure lexicographical
  comparison works correctly. <i>—end note</i>]</li>
  <li>Each successive <i>filename</i> element, if present.</li>
  <li>[<b>v3:</b> <i>Dot</i>] [<b>v4:</b> Empty path], if one or more trailing non-root directory separators
  are present.</li>
</ul>
  <blockquote>
  <p>[<i>Note:</i> Treating the last element during iteration as [<b>v3:</b> <i>dot</i>] [<b>v4:</b> an empty path] when
  there is a trailing directory separator enables lexical (i.e. syntactic)
  distinction between paths to directories versus paths to regular files. Such a
  distinction is usually irrelevant on POSIX and Windows based operating
  systems, but may be a requirement on other operating systems. <i>—end note</i>]</p>
  </blockquote>
  <p>The backward traversal order is the reverse of forward traversal.</p>
  <pre>iterator begin() const;</pre>
<blockquote>
  <p><i>Returns:</i> An iterator for the first  element in forward traversal
  order. If no elements are present, the end iterator.</p>
</blockquote>
<pre>iterator end() const;</pre>
<blockquote>
  <p><i>Returns:</i> The end iterator.</p>
</blockquote>
  <pre>reverse_iterator rbegin() const;</pre>
<blockquote>
  <p><i>Returns:</i> An iterator for the first element in backward traversal
  order. If no elements are present, the end iterator.</p>
</blockquote>
<pre>reverse_iterator rend() const;</pre>
<blockquote>
  <p><i>Returns:</i> The end iterator.</p>
</blockquote>
 <h3><a name="path-imbued-locale"><code><font size="4"> path</font></code>
 imbued locale</a> [path.imbued.locale]</h3>
 <p><code>path</code> operations sometimes require encoding conversions between
 <code>pathname</code> and some other string object where one of the value types
 is <code>char</code> and the other is <code>wchar_t</code>. Such conversions
 shall be performed by the <code>path::codecvt()</code> facet.</p>
 <blockquote>
 <p><span style="background-color: #FFFF00">[</span><i><span style="background-color: #FFFF00">Example:</span></i><span style="background-color: #FFFF00">
 ... </span><i><span style="background-color: #FFFF00">—end example</span></i><span style="background-color: #FFFF00">]</span></p>
 </blockquote>
 <pre>static std::locale <a name="path-imbue">imbue</a>(const std::locale&amp; loc);</pre>
<blockquote>
  <p><i>Effects:</i> Stores a copy of <code>loc</code> as the imbued <code>path</code>
  locale.</p>
  <p><i>Returns:</i> The previous imbued <code>path</code> locale.</p>
  <p><i>Remarks:</i> The initial value of the imbued <code>path</code> locale is
  operating system dependent. It shall be a locale with a <code>codecvt</code>
  facet for a <code>char</code> string encoding appropriate for the operating
  system. See ([<a href="#Operating-system-examples">fs.os.examples</a>]).&nbsp; </p>
</blockquote>
<pre>static const codecvt_type&amp; <a name="path-codecvt">codecvt</a>();</pre>
<blockquote>
  <p><i>Returns:</i> The <code>codecvt</code> facet for the imbued<code> path</code>
  locale .</p>
</blockquote>


<h3> <a name="path-deprecated-functions"><code>path</code> deprecated functions</a></h3>
<p> Several member functions from previous versions of <code>class path</code> have been deprecated, either because they have been renamed or because the
functionality is no longer desirable or has become obsolete.</p>
<p> Deprecated functions available by default; will be suppressed if <code>BOOST_FILESYSTEM_NO_DEPRECATED</code> is defined:</p>
<blockquote>
  <pre>path&amp;  remove_leaf()           { return remove_filename(); }
path   leaf() const            { return filename(); }
path   branch_path() const     { return parent_path(); }
bool   has_leaf() const        { return !m_path.empty(); }
bool   has_branch_path() const { return !parent_path().empty(); }</pre>
</blockquote>
<p> Deprecated functions not available by default; will be supplied if <code>BOOST_FILESYSTEM_DEPRECATED</code> is defined:</p>
<blockquote>
<pre>const std::string  file_string() const               { return native_string(); }
const std::string  directory_string() const          { return native_string(); }
const std::string  native_file_string() const        { return native_string(); }
const std::string  native_directory_string() const   { return native_string(); }
const string_type  external_file_string() const      { return native(); }
const string_type  external_directory_string() const { return native(); }</pre>
</blockquote>

<h3> <a name="path-non-member-functions"> <code><font size="4">path</font></code> non-member functions</a>
[path.non-member]</h3>

  <pre>bool lexicographical_compare(path::iterator first1, path::iterator last1,
                             path::iterator first2, path::iterator last2);</pre>
<blockquote>
  <p><i>Returns:</i> <code>true</code> if the sequence of <code>native()</code> strings for the elements defined by the half-open range <code>[first1, last1)</code> is
  lexicographically less than the sequence of <code>native()</code> strings for
  the elements defined by the half-open range <code>[first2, last2)</code>. Returns <code>false</code> otherwise.</p>
  <p><i>Remarks:</i> If two sequences have the same number of elements and their
  corresponding elements are equivalent, then neither sequence is
  lexicographically less than the other. If one sequence is a prefix of the
  other, then the shorter sequence is lexicographically less than the longer
  sequence. Otherwise, the lexicographical comparison of the sequences yields
  the same result as the comparison of the first corresponding pair of elements
  that are not equivalent.</p>
  <p>[<i>Note:</i> A <code>path</code> aware <code>lexicographical_compare</code> algorithm is provided for historical reasons. <i>—end note</i>]</p>
</blockquote>

  <div>

  <pre>path <a name="lex-normal">lexically_normal</a>(const path&amp; p);</pre>

  </div>

<blockquote>
  <p><i>Overview:</i> Returns <code>p</code> with redundant current
  directory (<i>dot</i>), parent directory (<i>dot-dot</i>), and <i>
  directory-separator</i> elements removed.</p>
  <p><i>Returns:</i> <code>p</code> in
  <a href="#normal-form">
  normal form</a>.</p>
  <p><i>Remarks:</i> Uses <code>operator/=</code> to compose the
  returned path.</p>
  <p>[<i>Example:</i></p>
  <p><code>assert(lexically_normal(&quot;foo/./bar/..&quot;) == &quot;foo&quot;);<br>
  assert(lexically_normal(&quot;foo/.///bar/../&quot;) == &quot;foo/.&quot;);</code></p>
  <p>All of the above assertions will succeed.<i> </i>On Windows, the
  returned path&#39;s <i>directory-separator</i> characters will be backslashes
  rather than slashes, but that does not affect <code>path</code> equality.<i>
  —end example</i>]</p>
</blockquote>

  <div>

  <pre>path <a name="lex-relative">lexically_relative</a>(const path&amp; p, const path&amp; base);</pre>

  </div>

<blockquote>
  <p><i>Overview:</i> Returns <code>p</code> made relative to <code>
  base</code>. Treats empty or identical paths as corner cases, not errors. Does
  not resolve symlinks. Does not first normalize <code>p</code> or <code>base</code>.</p>
  <p><i>Remarks:</i> Uses <code>std::mismatch(p.begin(), p.end(),
  base.begin(), base.end())</code>, to determine the first mismatched element of
  <code>p</code> and <code>base</code>. Uses <code>operator==</code> to
  determine if elements match. </p>
  <p><i>Returns:</i> </p>
  <ul>
    <li>
    <p><code>path()</code> if the first mismatched element of <code>p</code>
    is equal to <code>p.begin()</code> or the first mismatched element of <code>
    base</code> is equal to <code>base.begin()</code>, or<br>
    &nbsp; </li>
    <li>
    <p><code>path(&quot;.&quot;)</code> if the first mismatched element of <code>
    p</code> is equal to <code>p.end()</code> and the first mismatched element
    of <code>base</code> is equal to <code>base.end()</code>, or<br>
    &nbsp; </li>
    <li>
    <p>An object of class <code>path</code> composed via application
    of <code>operator/= path(&quot;..&quot;)</code> for each element in the half-open
    range [first mismatched element of <code>base</code>, <code>base.end()</code>),
    and then application of <code>operator/=</code> for each element in the
    half-open range [first mismatched element of <code>p</code>, <code>p.end()</code>).
    </li>
  </ul>
  <p>[<i>Example:</i></p>
  <p><code>assert(lexically_relative(&quot;/a/d&quot;, &quot;/a/b/c&quot;) == &quot;../../d&quot;);<br>
  assert(lexically_relative(&quot;/a/b/c&quot;, &quot;/a/d&quot;) == &quot;../b/c&quot;);<br>
  assert(lexically_relative(&quot;a/b/c&quot;, &quot;a&quot;) == &quot;b/c&quot;);<br>
  assert(lexically_relative(&quot;a/b/c&quot;, &quot;a/b/c/x/y&quot;) == &quot;../..&quot;);<br>
  assert(lexically_relative(&quot;a/b/c&quot;, &quot;a/b/c&quot;) == &quot;.&quot;);<br>
  assert(lexically_relative(&quot;a/b&quot;, &quot;c/d&quot;) == &quot;&quot;);</code></p>
  <p>All of the above assertions will succeed.<i> </i>On Windows, the
  returned path&#39;s <i>directory-separator</i>s will be backslashes rather than
  forward slashes, but that does not affect <code>path</code> equality.<i> —end
  example</i>]</p>
  <p>[<i>Note:</i> If symlink following semantics are desired, use the
  operational function <code>
  <a href="#relative">
  relative</a></code>&nbsp; <i>—end note</i>]</p>
  <p>[<i>Note:</i> If <a href="#normal-form">normalization</a> is needed to ensure consistent matching of elements, wrap
  <code>p</code>, <code>base</code>, or both in calls <code>
  <a href="#lex-normal">lexically_normal</a>()</code>. <i>—end note</i>]</p>
</blockquote>
<pre>void swap(path&amp; lhs, path&amp; rhs) noexcept;</pre>
<blockquote>
  <p><i>Effects: </i><code>lhs.swap(rhs)</code>.</p>
</blockquote>
<pre>std::size_t <a name="hash_value">hash_value</a>(const path&amp; p);</pre>
<blockquote>
  <p><i>Returns:</i> A hash value for the path <code>p</code>. If
  for two paths, <code>p1 == p2</code> then <code>hash_value(p1) == hash_value(p2)</code>.</p>
  <p>This allows paths to be used with <a href="../../functional/hash/index.html">Boost.Hash</a>.</p>
</blockquote>
<pre>bool operator&lt; (const path&amp; lhs, const path&amp; rhs);</pre>
<blockquote>
  <p><i>Returns:</i> <code>return lhs.compare(rhs.begin) &lt; 0</code>.</p>
</blockquote>
<pre>bool operator&lt;=(const path&amp; lhs, const path&amp; rhs);</pre>
<blockquote>
  <p><i>Returns:</i> <code>!(rhs &lt; lhs)</code>.</p>
</blockquote>
<pre>bool operator&gt; (const path&amp; lhs, const path&amp; rhs);</pre>
<blockquote>
  <p><i>Returns:</i> <code>rhs &lt; lhs</code>.</p>
</blockquote>
<pre>bool operator&gt;=(const path&amp; lhs, const path&amp; rhs);</pre>
<blockquote>
  <p><i>Returns:</i> <code>!(lhs &lt; rhs)</code>.</p>
</blockquote>
<pre>bool operator==(const path&amp; lhs, const path&amp; rhs);</pre>
<blockquote>
  <p><i>Returns:</i> <code>!(lhs &lt; rhs) &amp;&amp; !(rhs &lt; lhs)</code>.</p>
  <p>[<i>Note:</i> <a name="Path-equality">Path equality</a> and path
  equivalence have different semantics.</p>
  <p>Equality is determined by the <code>path</code> non-member <code>operator==</code>, which considers the two path's lexical
  representations only. Thus <code>path(&quot;foo&quot;) == &quot;bar&quot;</code> is never <code>true</code>.</p>
  <p>Equivalence is determined by the <code><a href="#equivalent">equivalent</a>()</code> non-member function, which determines if two paths <a href="#path">resolve</a> to the same file system entity.
  Thus <code>equivalent(&quot;foo&quot;, &quot;bar&quot;)</code> will be <code>true</code> when both paths resolve to the same file.</p>
  <p>Programmers wishing to determine if two paths are &quot;the same&quot; must decide if
  &quot;the same&quot; means &quot;the same representation&quot; or &quot;resolve to the same actual
  file&quot;, and choose the appropriate function accordingly. <i>—end note</i>]</p>
</blockquote>
<pre>bool operator!=(const path&amp; lhs, const path&amp; rhs);</pre>
<blockquote>
  <p><i>Returns:</i> <code>!(lhs == rhs)</code>.</p>
</blockquote>
<pre>path operator/ (const path&amp; lhs, const path&amp; rhs);</pre>
<blockquote>
  <p><i>Returns:</i> <code>path(lhs) /= rhs</code>.</p>
</blockquote>
<h3> <a name="path-non-member-operators"><code><font size="4">path</font></code></a><a name="path-inserter-extractor"> inserter
  and extractor</a> [path.io]</h3>
<p> The inserter and extractor delimit the string with double-quotes (<code>&quot;</code>)
so that paths with embedded spaces will round trip correctly. Ampersand (<code>&amp;</code>)
is as an escape character, so the path can itself contain double quotes.</p>
<pre>template &lt;class Char, class Traits&gt;
std::basic_ostream&lt;Char, Traits&gt;&amp; operator&lt;&lt;(std::basic_ostream&lt;Char, Traits&gt;&amp; os,
                                             const path&amp; p);
</pre>
<blockquote>
  <p><i>Effects: </i>Insert characters into <code>os</code>:</p>
  <ul>
    <li>
  <p>A double-quote.</p>
    </li>
    <li>
  <p>Each character in <code>p.string&lt;std::basic_string&lt;Char&gt;&gt;()</code>.
  If the character to be inserted is equal to the escape character or a
  double-quote, as determined by <code>operator==</code>, first insert the
  escape character.</p>
    </li>
    <li>
  <p>A double-quote.</p>
    </li>
  </ul>
  <p>[<i>Note:</i> Effects are similar to:
<pre>std::basic_string&lt;Char&gt; str(p.string&lt;std::basic_string&lt;Char&gt;&gt;());
os &lt;&lt; <a href="../../io/doc/quoted_manip.html">boost::io::quoted</a>(str, static_cast&lt;Char&gt;('&amp;'));</pre>
<i>—end note</i>]</p>
  <p><i>Returns:</i> <code>os</code></p>
</blockquote>
<pre>template &lt;class Char, class Traits&gt;
std::basic_istream&lt;Char, Traits&gt;&amp; operator&gt;&gt;(std::basic_istream&lt;Char, Traits&gt;&amp; is,
                                             path&amp; p);
</pre>
<blockquote>
  <p><i>Effects:&nbsp; </i>Extract characters from <code>is</code>:</p>
  <ul>
    <li>If the first character that would be extracted is equal to double-quote,
    as determined by <code>operator==</code>, then:<ul>
      <li>Discard the initial double-quote.</li>
      <li>Save the value and then turn off the <code>skipws</code> flag.</li>
      <li><code>p.clear()</code></li>
      <li>Until an unescaped double-quote character is reached or <code>
      is.not_good()</code>, extract characters from <code>is</code> and append
      them to <code>p</code>, except that if an escape character is reached,
      ignore it and append the next character to <code>p</code>.</li>
      <li>Discard the final double-quote character.</li>
      <li>Restore the <code>skipws</code> flag to its original value.</li>
    </ul>
    </li>
    <li>Otherwise, <code>is &gt;&gt; p</code>.</li>
  </ul>
  <p>[<i>Note:</i> Effects are similar to:
<pre>std::basic_string&lt;Char&gt; str;
is &gt;&gt; <a href="../../io/doc/quoted_manip.html">boost::io::quoted</a>(str, static_cast&lt;Char&gt;('&amp;'));
p = str;</pre><i>—end note</i>]</p>
  <p><i>Returns:</i> <code>is</code></p>
  </blockquote>
<h2><a name="Class-filesystem_error">Class <code>filesystem_error</code>
[class.filesystem_error]</a></h2>
<pre>namespace boost
{
  namespace filesystem
  {
      class filesystem_error : public system_error
      {
      public:
        filesystem_error();
        filesystem_error(const filesystem_error&amp;);
        <a href="#filesystem_error-2-arg">filesystem_error</a>(const std::string&amp; what_arg,
          system::error_code ec);
        <a href="#filesystem_error-3-arg">filesystem_error</a>(const std::string&amp; what_arg,
          const path&amp; p1, system::error_code ec);
        <a href="#filesystem_error-4-arg">filesystem_error</a>(const std::string&amp; what_arg,
          const path&amp; p1, const path&amp; p2, system::error_code ec);

        filesystem_error&amp; filesystem_error(const filesystem_error&amp;);
       ~filesystem_error();

        filesystem_error&amp; operator=(const filesystem_error&amp;);

        const path&amp; <a href="#filesystem_error-path1">path1</a>() const;
        const path&amp; <a href="#filesystem_error-path2">path2</a>() const;

        const char * <a href="#filesystem_error-what">what</a>() const;
      };
  }  // namespace filesystem
}  // namespace boost</pre>
<p>The class template <code>filesystem_error</code> defines the type of
objects thrown as exceptions to report file system errors from functions described in this
reference documentation.</p>
<h3> <a name="filesystem_error-members"> <code>filesystem_error</code> members</a>
[filesystem_error.members]</h3>
<pre><a name="filesystem_error-2-arg">filesystem_error</a>(const std::string&amp; what_arg, error_code ec);</pre>
<blockquote>
  <p><i>Postcondition:</i></p>
  <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="46%">
    <tr>
      <td width="18%"><b>Expression</b></td>
      <td width="82%"><b>Value</b></td>
    </tr>
    <tr>
      <td width="18%" bgcolor="#FFFFFF"><code>
      runtime_error::what()</code></td>
      <td width="82%" bgcolor="#FFFFFF">
      <code><i>what_arg</i>.c_str()</code></td>
    </tr>
    <tr>
      <td width="18%"><code>code()</code></td>
      <td width="82%"><code>ec</code></td>
    </tr>
    <tr>
      <td width="18%"><code>path1().empty()</code></td>
      <td width="82%"><code>true</code></td>
    </tr>
    <tr>
      <td width="18%"><code>path2().empty()</code></td>
      <td width="82%"><code>true</code></td>
    </tr>
  </table>
</blockquote>
<pre><a name="filesystem_error-3-arg">filesystem_error</a>(const std::string&amp; what_arg, const path&amp; p1, error_code ec);</pre>
<blockquote>
  <p><i>Postcondition:</i></p>
  <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="46%">
    <tr>
      <td width="18%"><b>Expression</b></td>
      <td width="82%"><b>Value</b></td>
    </tr>
    <tr>
      <td width="18%" valign="top"><code>
      runtime_error::what()</code></td>
      <td width="82%">
      <code><i>what_arg</i>.c_str()</code></td>
    </tr>
    <tr>
      <td width="18%" valign="top"><code>code()</code></td>
      <td width="82%"><code>ec</code></td>
    </tr>
    <tr>
      <td width="18%" valign="top"><code>path1()</code></td>
      <td width="82%">Reference to stored copy of <code>p1</code></td>
    </tr>
    <tr>
      <td width="18%" valign="top"><code>path2().empty()</code></td>
      <td width="82%"><code>true</code></td>
    </tr>
  </table>
</blockquote>
<pre><a name="filesystem_error-4-arg">filesystem_error</a>(const std::string&amp; what_arg, const path&amp; p1, const path&amp; p2, error_code ec);</pre>
<blockquote>
  <p><i>Postcondition:</i></p>
  <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="46%">
    <tr>
      <td width="18%"><b>Expression</b></td>
      <td width="82%"><b>Value</b></td>
    </tr>
    <tr>
      <td width="18%"><code>
      runtime_error::what()</code></td>
      <td width="82%">
      <u>
      <code><i>w</i></code></u><code><i>hat_arg</i>.c_str()</code></td>
    </tr>
    <tr>
      <td width="18%"><code>code()</code></td>
      <td width="82%"><code>ec</code></td>
    </tr>
    <tr>
      <td width="18%"><code>path1()</code></td>
      <td width="82%">Reference to stored copy of <code>p1</code></td>
    </tr>
    <tr>
      <td width="18%"><code>path2()</code></td>
      <td width="82%">Reference to stored copy of <code>p2</code></td>
    </tr>
  </table>
</blockquote>
<pre>const path&amp; <a name="filesystem_error-path1">path1</a>() const;</pre>
<blockquote>
  <p><i>Returns:</i> Reference to copy of <code>p1</code> stored by the
  constructor, or, if none, an empty path.</p>
</blockquote>
<pre>const path&amp; <a name="filesystem_error-path2">path2</a>() const;</pre>
<blockquote>
  <p><i>Returns:</i> Reference to copy of <code>p2</code> stored by the
  constructor, or, if none, an empty path.</p>
</blockquote>
<pre>const char* <a name="filesystem_error-what">what</a>() const;</pre>
<blockquote>
  <p><i>Returns: </i>A string containing <code>runtime_error::what()</code>. The exact format is unspecified.
  Implementations are encouraged but not required to include <code>path1.native_string()</code>if not empty, <code>path2.native_string()</code>if
  not empty, and <code>system_error::what()</code> strings in the returned
  string.</p>
</blockquote>
<h2><a name="Enum-file_type">Enum file_type</a> [enum.file_type]</h2>
<p>This enum specifies constants uses to identify file types.</p>
<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111">
  <tr>
    <td><b>Constant Name</b></td>
    <td><b>Meaning</b></td>
  </tr>
  <tr>
    <td><code>status_error</code></td>
    <td>An error occurred while trying to obtain the status of the file. The
    file simply not being found is <b><u>not</u></b> considered a status error. </td>
  </tr>
  <tr>
    <td><code>file_not_found</code></td>
    <td>The file could not be found</td>
  </tr>
  <tr>
    <td><code>regular_file</code></td>
    <td>Regular file</td>
  </tr>
  <tr>
    <td><code>directory_file</code></td>
    <td>Directory file</td>
  </tr>
  <tr>
    <td><code>symlink_file</code></td>
    <td>Symbolic link file</td>
  </tr>
  <tr>
    <td><code>block_file</code></td>
    <td>Block special file</td>
  </tr>
  <tr>
    <td><code>character_file</code></td>
    <td>Character special file</td>
  </tr>
  <tr>
    <td><code>fifo_file</code></td>
    <td>FIFO or pipe file</td>
  </tr>
  <tr>
    <td><code>socket_file</code></td>
    <td>Socket file</td>
  </tr>
  <tr>
    <td><code>type_unknown</code></td>
    <td>The file exists, but it is of a system specific type not covered by any
    of the above cases.</td>
  </tr>
</table>
<h2><a name="Enum-perms">Enum perms</a> [enum.perms]</h2>
<p>This <code>enum</code> specifies bitmask constants uses to identify file
permissions. <i><span style="background-color: #E0E0E0">ISO/</span><span style="background-color: #E0E0E0">IEC</span><span style="background-color: #E0E0E0"> 9945
(POSIX) specifies actual values, and those values have been adopted here because
they are very familiar and ingrained for many POSIX
users.</span></i></p>
<blockquote>
<p><span style="background-color: #FFFF00">Windows: All permissions except write are currently ignored. There is only a
single write permission; setting write permission for owner, group, or others
sets write permission for all, and removing write permission for owner, group,
or others removes write permission for all. </span> </p>
</blockquote>
<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111">
  <tr>
    <td><b>Name</b></td>
    <td align="center"><b>Value<br>
    (octal)</b></td>
    <td align="center"><b>ISO/IEC 9945<br>
    macro</b></td>
    <td><b>Definition or notes</b></td>
  </tr>

<tr><td>
  <p><code>no_perms</code></td><td><code>0</code></td><td></td>
  <td>There are no permissions set for the file. Note: <code>file_not_found</code> is <code>no_perms</code> rather than <code>perms_not_known</code></td>
</tr>
<tr><td><code>owner_read</code></td><td><code>0400</code></td><td> <code>S_IRUSR</code></td>
  <td> Read permission, owner</td>
</tr>
<tr><td><code>owner_write</code></td><td><code>0200</code></td><td> <code>S_IWUSR</code></td>
  <td> Write permission, owner</td>
</tr>
<tr><td><code>owner_exe</code></td><td><code>0100</code></td><td>  <code>S_IXUSR</code></td>
  <td> Execute/search permission, owner</td>
</tr>
<tr><td><code>owner_all</code></td><td><code>0700</code></td><td>  <code>S_IRWXU</code></td>
  <td> Read, write, execute/search by owner; <code>owner_read | owner_write | owner_exe</code></td>
</tr>
<tr><td><code>group_read</code></td><td><code>040</code></td><td>  <code>S_IRGRP</code></td>
  <td> Read permission, group</td>
</tr>
<tr><td><code>group_write</code></td><td><code>020</code></td><td> <code>S_IWGRP</code></td>
  <td> Write permission, group</td>
</tr>
<tr><td><code>group_exe</code></td><td><code>010</code></td><td>   <code>S_IXGRP</code></td>
  <td> Execute/search permission, group</td>
</tr>
<tr><td><code>group_all</code></td><td><code>070</code></td><td>   <code>S_IRWXG</code></td>
  <td> Read, write, execute/search by group; <code>group_read | group_write | group_exe</code></td>
</tr>
<tr><td><code>others_read</code></td><td><code>04</code></td><td>  <code>S_IROTH</code></td>
  <td> Read permission, others</td>
</tr>
<tr><td><code>others_write</code></td><td><code>02</code></td><td> <code>S_IWOTH</code></td>
  <td> Write permission, others</td>
</tr>
<tr><td><code>others_exe</code></td><td><code>01</code></td><td>   <code>S_IXOTH</code></td>
  <td> Execute/search permission, others</td>
</tr>
<tr><td><code>others_all</code></td><td><code>07</code></td><td>   <code>S_IRWXO</code></td>
  <td>Read, write, execute/search by others; <code>others_read | others_write | others_exe</code></td>
</tr>
<tr><td><code>all_all</code></td><td><code>0777</code></td><td> </td><td><code>owner_all | group_all | others_all</code></td>
</tr>
<tr><td><code>set_uid_on_exe</code></td><td><code>04000</code></td><td> <code>S_ISUID</code></td>
  <td> Set-user-ID on execution</td>
</tr>
<tr><td><code>set_gid_on_exe</code></td><td><code>02000</code></td><td> <code>S_ISGID</code></td>
  <td> Set-group-ID on execution</td>
</tr>
<tr><td><code><a name="sticky_bit">sticky_bit</a> </code></td><td><code>01000</code></td><td> <code>S_ISVTX</code></td>
  <td> Operating system dependent. Inherently non-portable, even between ISO/IEC 9945
  operating systems.</td>
</tr>
<tr><td><code><a name="perms_mask">perms_mask</a></code></td><td><code>07777</code></td><td> &nbsp;</td>
  <td><code>all_all | set_uid_on_exe | set_gid_on_exe | sticky_bit</code></td>
</tr>
<tr><td><code>perms_not_known</code></td><td><code>0xFFFF</code></td><td></td><td>
  The permissions are not known, such as when a <code>file_status</code> object
  is created without specifying the permissions</td>
</tr>
<tr><td>
  <p><code>add_perms</code></td><td><code>0x1000</code></td><td></td><td>
  <p><code>permissions()</code> adds the argument permission bits to the
  file's current bits</td>
</tr>
<tr><td><code>remove_perms</code></td><td><code>0x2000</code></td><td></td><td>
  <code>permissions()</code> removes the argument permission bits from the
  file's current bits</td>
</tr>
<tr><td><code><a name="symlink_perms">symlink_perms</a></code></td><td><code>0x4000</code></td><td></td><td>
  <span style="background-color: #FFFF00">On ISO/</span><span style="background-color: #FFFF00">IEC</span><span style="background-color: #FFFF00"> 9945
  </span> <code><span style="background-color: #FFFF00">permissions()</span></code><span style="background-color: #FFFF00"> resolves symlinks unless
  </span> <code><span style="background-color: #FFFF00">symlink_perms</span></code><span style="background-color: #FFFF00"> is specified.
  Meaningless on Windows as </span> <code>
  <span style="background-color: #FFFF00">permissions()</span></code><span style="background-color: #FFFF00"> never resolves symlinks.
  Meaningless on Mac OS X and some other BSD systems as </span> <code>
  <span style="background-color: #FFFF00">permissions()</span></code><span style="background-color: #FFFF00"> always resolves symlinks. Get over it.</span></td>
</tr>

</table>
<h2><a name="file_status">Class file_status</a> [class.file_status]</h2>
<pre>namespace boost
{
  namespace filesystem
  {
      class file_status
      {
      public:

        // <a href="#file_status-constructors">constructors</a>
        file_status() noexcept;
        explicit file_status(<a href="#file_type">file_type</a> ft, <a href="#Enum-perms">perms</a> prms = perms_not_known) noexcept;

        // compiler generated
        file_status(const file_status&amp;) noexcept;
        file_status&amp; operator=(const file_status&amp;) noexcept;
       ~file_status() noexcept;

        // <a href="#file_status-observers">observers</a>
        <a href="#file_type">file_type</a>  type() const noexcept;
        <a href="#Enum-perms">perms</a>      permissions() const noexcept;

        // <a href="#file_status-modifiers">modifiers</a>
        void       type(<a href="#file_type">file_type</a> ft) noexcept;
        void       permissions(<a href="#Enum-perms">perms</a> prms) noexcept;
      };
  }  // namespace filesystem
}  // namespace boost</pre>
<p>An object of type <code>file_status</code> stores information about the type
and permissions of a file.</p>
<h3><a name="file_status-constructors"><code>file_status</code> constructors</a>
[file_status.cons]</h3>
<pre>explicit file_status() noexcept;</pre>
<blockquote>
  <p><i>Postconditions:</i> <code>type() == status_error</code>, <code>permissions() == perms_not_known</code>.</p>
</blockquote>
<pre>explicit file_status(<a href="#file_type">file_type</a> ft, <a href="#Enum-perms">perms</a> prms = perms_not_known) noexcept;</pre>
<blockquote>
  <p><i>Postconditions:</i> <code>type() == ft</code>, <code>permissions() == prms</code>.</p>
</blockquote>
  <h3><a name="file_status-observers"><code>file_status</code> observers</a> [file_status.obs]</h3>
<pre><a href="#file_type">file_type</a> type() const noexcept;</pre>
<blockquote>
  <p><i>Returns: </i>The value of <code>type()</code> specified by the <i>postconditions</i> of the most recent call to a constructor, operator=, or <code>type(file_type)</code> function.</p>
</blockquote>
<pre><a href="#Enum-perms">perms</a> permissions() const noexcept;</pre>
<blockquote>
  <p><i>Returns: </i>The value of <code>permissions()</code> specified by the <i>postconditions</i> of the most recent call to a constructor, operator=, or <code>permissions(perms)</code> function.</p>
</blockquote>
<h3><a name="file_status-modifiers"><code>file_status</code> modifiers</a> [file_status.mods]</h3>
<pre>void type(<a href="#file_type">file_type</a> ft) noexcept;</pre>
<blockquote>
  <p><i>Postconditions:</i> <code>type() == ft</code>.</p>
</blockquote>
<pre>void permissions(<a href="#Enum-perms">perms</a> prms) noexcept;</pre>
<blockquote>
  <p><i>Postconditions:</i> <code>permissions() == prms</code>.</p>
</blockquote>
<h2><a name="Class-directory_entry">Class <code>directory_entry</code></a> [class.directory_entry]</h2>

<pre>namespace boost
{
  namespace filesystem
  {
      class directory_entry
      {
      public:

        // <a href="#directory_entry-constructors">constructors</a> and destructor
        directory_entry();
        directory_entry(const directory_entry&amp;);
        explicit directory_entry(const path&amp; p, file_status st=file_status(),
          file_status symlink_st=file_status());
       ~directory_entry();

        // <a href="#directory_entry-modifiers">modifiers</a>
        directory_entry&amp; operator=(const directory_entry&amp;);
        void assign(const path&amp; p, file_status st=file_status(),
          file_status symlink_st=file_status());
        void replace_filename(const path&amp; p, file_status st=file_status(),
          file_status symlink_st=file_status());

        // <a href="#directory_entry-observers">observers</a>
        const path&amp;  path() const;
        file_status  status() const;
        file_status  status(system::error_code&amp; ec) const;
        file_status  symlink_status() const;
        file_status  symlink_status(system::error_code&amp; ec) const;

        bool operator&lt; (const directory_entry&amp; rhs);
        bool operator==(const directory_entry&amp; rhs);
        bool operator!=(const directory_entry&amp; rhs);
        bool operator&lt; (const directory_entry&amp; rhs);
        bool operator&lt;=(const directory_entry&amp; rhs);
        bool operator&gt; (const directory_entry&amp; rhs);
        bool operator&gt;=(const directory_entry&amp; rhs);
      private:
        path                 m_path;           // for exposition only
        mutable file_status  m_status;         // for exposition only; stat()-like
        mutable file_status  m_symlink_status; // for exposition only; lstat()-like
      };

  }  // namespace filesystem
}  // namespace boost</pre>

<p>A <code>directory_entry</code> object stores a <code>path object</code>,
a <code>file_status</code> object for non-symbolic link status, and a <code>file_status</code> object for symbolic link status. The <code>file_status</code> objects act as value caches.</p>
<blockquote>
<p>[<i>Note:</i> Because <code>status()</code>on a pathname may be a relatively expensive operation,
some operating systems provide status information as a byproduct of directory
iteration. Caching such status information can result is significant time savings. Cached and
non-cached results may differ in the presence of file system races. <i>—end note</i>]</p>
<p><span style="background-color: #E0E0E0"><i>Actual cold-boot timing of iteration over
a directory with 15,047 entries was six seconds for non-cached status queries
versus one second for cached status queries. Windows XP, 3.0 GHz processor, with
a moderately fast hard-drive. Similar speedups are expected on Linux and BSD-derived
systems that provide status as a by-product of directory iteration.</i></span></p>
</blockquote>
<h3> <a name="directory_entry-constructors"> <code>directory_entry </code>constructors</a>
[directory_entry.cons]</h3>
<pre>directory_entry();</pre>
<blockquote>
  <p><i>Postcondition:</i></p>
  <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="36%">
    <tr>
      <td width="18%"><b>Expression</b></td>
      <td width="82%"><b>Value</b></td>
    </tr>
    <tr>
      <td width="18%"><code>path().empty()</code></td>
      <td width="82%"><code>true</code></td>
    </tr>
    <tr>
      <td width="18%"><code>status()</code></td>
      <td width="82%"><code>file_status()</code></td>
    </tr>
    <tr>
      <td width="18%"><code>symlink_status()</code></td>
      <td width="82%"><code>file_status()</code></td>
    </tr>
  </table>
</blockquote>
<pre>explicit directory_entry(const path&amp; p, file_status st=file_status(), file_status symlink_st=file_status());</pre>
<blockquote>
  <p><i>Postcondition:</i></p>
  <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="36%">
    <tr>
      <td width="18%"><b>Expression</b></td>
      <td width="82%"><b>Value</b></td>
    </tr>
    <tr>
      <td width="18%"><code>path()</code></td>
      <td width="82%"><code>p</code></td>
    </tr>
    <tr>
      <td width="18%"><code>status()</code></td>
      <td width="82%"><code>st</code></td>
    </tr>
    <tr>
      <td width="18%"><code>symlink_status()</code></td>
      <td width="82%"><code>symlink_st</code></td>
    </tr>
  </table>
</blockquote>
<h3> <a name="directory_entry-modifiers"> <code>directory_entry </code>modifiers</a>
[directory_entry.mods]</h3>
<pre>void assign(const path&amp; p, file_status st=file_status(), file_status symlink_st=file_status());</pre>
<blockquote>
  <p><i>Postcondition:</i></p>
  <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="36%">
    <tr>
      <td width="18%"><b>Expression</b></td>
      <td width="82%"><b>Value</b></td>
    </tr>
    <tr>
      <td width="18%"><code>path()</code></td>
      <td width="82%"><code>p</code></td>
    </tr>
    <tr>
      <td width="18%"><code>status()</code></td>
      <td width="82%"><code>st</code></td>
    </tr>
    <tr>
      <td width="18%"><code>symlink_status()</code></td>
      <td width="82%"><code>symlink_st</code></td>
    </tr>
  </table>
</blockquote>
<pre>void replace_filename(const path&amp; p, file_status st=file_status(), file_status symlink_st=file_status());</pre>
<blockquote>
  <p><i>Postcondition:</i></p>
  <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="43%">
    <tr>
      <td width="18%"><b>Expression</b></td>
      <td width="82%"><b>Value</b></td>
    </tr>
    <tr>
      <td width="18%"><code>path()</code></td>
      <td width="82%"><code>path().branch() / s</code></td>
    </tr>
    <tr>
      <td width="18%"><code>status()</code></td>
      <td width="82%"><code>st</code></td>
    </tr>
    <tr>
      <td width="18%"><code>symlink_status()</code></td>
      <td width="82%"><code>symlink_st</code></td>
    </tr>
  </table>
</blockquote>
<h3> <a name="directory_entry-observers"> <code>directory_entry</code> observers</a>
[directory_entry.obs]</h3>
<pre>const path&amp; path() const;</pre>
<blockquote>
  <p><i>Returns:</i> <code>m_path</code></p>
</blockquote>
<pre>file_status status() const;
file_status status(system::error_code&amp; ec) const;</pre>
<blockquote>
<p><i>Effects:</i> As if,</p>
  <blockquote>
    <pre>if ( !status_known( m_status ) )
{
  if ( status_known(m_symlink_status) &amp;&amp; !is_symlink(m_symlink_status) )
    { m_status = m_symlink_status; }
  else { m_status = status(m_path<i>[, ec]</i>); }
}</pre>
  </blockquote>
  <p><i>Returns:</i> <code>m_status</code></p>

  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>

</blockquote>
<pre>file_status  symlink_status() const;
file_status  symlink_status(system::error_code&amp; ec) const;</pre>
<blockquote>
<p>
  <i>Effects:</i> As if,</p>
  <blockquote>
    <pre>if ( !status_known( m_symlink_status ) )
{
  m_symlink_status = symlink_status(m_path<i>[, ec]</i>);
}</pre>
  </blockquote>
  <p><i>Returns:</i> <code>m_symlink_status</code></p>

  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>

</blockquote>
<pre>bool operator==(const directory_entry&amp; rhs);</pre>
<blockquote>
  <p><i>Returns:</i> <code>m_path == rhs.m_path</code>.</p>
</blockquote>
<pre>bool operator!=(const directory_entry&amp; rhs);</pre>
<blockquote>
  <p><i>Returns:</i> <code>m_path != rhs.m_path</code>.</p>
</blockquote>
<pre>bool operator&lt; (const directory_entry&amp; rhs);</pre>
<blockquote>
  <p><i>Returns:</i> <code>m_path &lt; rhs.m_path</code>.</p>
</blockquote>
<pre>bool operator&lt;=(const directory_entry&amp; rhs);</pre>
<blockquote>
  <p><i>Returns:</i> <code>m_path &lt;= rhs.m_path</code>.</p>
</blockquote>
<pre>bool operator&gt; (const directory_entry&amp; rhs);</pre>
<blockquote>
  <p><i>Returns:</i> <code>m_path &gt; rhs.m_path</code>.</p>
</blockquote>
<pre>bool operator&gt;=(const directory_entry&amp; rhs);</pre>
<blockquote>
  <p><i>Returns:</i> <code>m_path &gt;= rhs.m_path</code>.</p>
</blockquote>
<h2><a name="Class-directory_iterator">Class <code>directory_iterator</code>
[class.directory_iterator]</a></h2>
<p>Objects of type <code>directory_iterator</code> provide standard library
compliant iteration over the contents of a directory. Also see class <code><a href="#Class-recursive_directory_iterator">recursive_directory_iterator</a></code>.</p>
<pre>namespace boost
{
  namespace filesystem
  {
      class directory_iterator
      {
      public:
        // <a href="#directory_iterator-members">member functions</a>

        directory_iterator() noexcept;  // creates the &quot;end&quot; iterator
        directory_iterator(const directory_iterator&amp;);
        explicit directory_iterator(const path&amp; p, <a href="#directory_options">directory_options</a> opts = directory_options::none);
        directory_iterator(const path&amp; p, system::error_code&amp; ec);
        directory_iterator(const path&amp; p, <a href="#directory_options">directory_options</a> opts, system::error_code&amp; ec);
       ~directory_iterator();

        directory_iterator&amp; operator=(const directory_iterator&amp;);

        directory_iterator&amp; operator++();
        directory_iterator&amp; increment(system::error_code&amp; ec);

        // other members as required by
        //  C++ Std, 24.1.1 Input iterators [input.iterators]
      };

  }  // namespace filesystem
}  // namespace boost</pre>
<p> <code>directory_iterator</code> satisfies the requirements of an
input iterator (C++ Std, 24.2.1, Input iterators [input.iterators]).</p>
<p>A <code>directory_iterator</code> reads successive elements from the directory for
which it was constructed, as if by calling ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/readdir_r.html">readdir() or readdir_r()</a></code>. After a <code>directory_iterator</code> is constructed, and every time <code>operator++</code> is called,
it reads a directory element and stores information about it in an object of type <code><a href="#Class-directory_entry">directory_entry</a></code>. <code>operator++</code> is not equality preserving; that is, <code>i == j</code> does not imply that <code>++i == ++j</code>. </p>
<blockquote>
<p>[<i>Note:</i> The practical consequence of not preserving equality is that directory iterators
can only be used for single-pass algorithms. <i>—end note</i>]</p>
</blockquote>
<p>If the end of the directory elements is reached, the iterator shall become equal to
the end iterator value. The constructor <code>directory_iterator()</code> with no arguments always constructs an end iterator object, which
shall be the only valid iterator for the end condition. The result of <code>operator*</code> on an end iterator is not defined. For any other iterator value
a <code>const directory_entry&amp;</code> is returned. The result of <code>operator-&gt;</code> on an end iterator is
undefined behavior. For any other iterator value a <code>const directory_entry*</code> is
returned. </p>
<p>Two end iterators are always equal. An end iterator shall not be equal to a non-end
iterator.</p>
<blockquote>
<p><i><span style="background-color: #E0E0E0">The above wording is based on the
Standard Library's istream_iterator wording.</span></i></p>
</blockquote>
<p>The result of calling the <code>path()</code> member of the <code>directory_entry</code> object obtained by dereferencing a <code>directory_iterator</code> is a reference to a <code>path</code> object composed of the directory argument from which the iterator was
constructed with filename of the directory entry appended as if by <code>operator/=</code>. </p>
<p>Directory iteration shall not yield directory entries for the current (<i>dot</i>)
and parent (<i>dot dot</i>) directories.</p>
<p>The order of directory entries obtained by dereferencing successive
increments of a <code>directory_iterator</code> is unspecified.</p>
<blockquote>
<p>[<i>Note:</i> Programs performing directory iteration may wish to test if the
path obtained by dereferencing a directory iterator actually exists. It could be
a
symbolic link to a non-existent file. Programs recursively
walking directory trees for purposes of removing and renaming entries may wish
to avoid following symbolic links.</p>
<p>If a file is removed from or added to a directory after the
construction of a <code>directory_iterator</code> for the directory, it is
unspecified whether or not subsequent incrementing of the iterator will ever
result in an iterator whose value is the removed or added directory entry. See
ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/readdir_r.html">readdir_r()</a></code>. <i>—end note</i>]</p>
</blockquote>
<h3><a name="directory_iterator-members"><code>directory_iterator</code> members</a>
[directory_iterator.members]</h3>

<p><code><a name="directory_iterator-default-ctor">directory_iterator</a>()
noexcept;</code></p>

<blockquote>

<p><i>Effects:</i> Constructs the end iterator.</p>

</blockquote>

<pre><code>explicit <a name="directory_iterator-ctor-path">directory_iterator</a>(</code>const path&amp; p, <a href="#directory_options">directory_options</a> opts = directory_options::none<code>);
directory_iterator(</code>const path&amp; p, system::error_code&amp; ec<code>);
directory_iterator(</code>const path&amp; p, <a href="#directory_options">directory_options</a> opts, system::error_code&amp; ec<code>);</code></pre>
<blockquote>

<p><i>Effects:</i> Constructs an iterator representing the first
entry in the directory <code>p</code> resolves to, if any; otherwise, the end iterator.
If opening the directory fails with a <code>permission_denied</code> error and <code>(opts &amp; directory_options::skip_permission_denied) != 0</code>,
constructs the end iterator and ignores the error (the operation completes successfully). If <code>opts</code> is not specified, it is assumed to be <code>directory_options::none</code>.</p>

<p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>

<p>[<i>Note:</i> To iterate over the current directory, use <code>directory_iterator(&quot;.&quot;)</code> rather than <code>directory_iterator(&quot;&quot;)</code>. <i>—end note</i>]</p>
</blockquote>
<pre>directory_iterator&amp; <a name="directory_iterator-increment">operator++</a>();
directory_iterator&amp; increment(system::error_code&amp; ec);</pre>
<blockquote>

<p><i>Effects:</i> As specified by the C++ Standard, 24.1.1 Input iterators [input.iterators]. In case of error the iterator is left in the end state.</p>

<p><i>Returns:</i> <code>*this</code>.</p>

<p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>

</blockquote>
<h3><a name="directory_iterator-non-member-functions"><code>directory_iterator</code> non-member functions</a></h3>
<pre>const directory_iterator&amp; begin(const directory_iterator&amp; iter);</pre>
<blockquote>
  <p><i>Returns: </i><code>iter</code>.</p>
</blockquote>
<pre>directory_iterator end(const directory_iterator&amp;);</pre>
<blockquote>
  <p><i>Returns: </i><code>directory_iterator()</code>.</p>
</blockquote>
<h2><a name="Class-recursive_directory_iterator">Class <code>recursive_directory_iterator</code>
[class.rec.dir.itr]</a></h2>
<p>Objects of type <code>recursive_directory_iterator</code> provide standard library
compliant iteration over the contents of a directory, including recursion into
its sub-directories.</p>
<pre>namespace boost
{
  namespace filesystem
  {
      class recursive_directory_iterator :
        public iterator&lt;input_iterator_tag, directory_entry&gt;
      {
      public:

        // constructors and destructor
        recursive_directory_iterator() noexcept;
        recursive_directory_iterator(const recursive_directory_iterator&amp;);
        explicit recursive_directory_iterator(const path&amp; p,
          <a href="#directory_options">directory_options</a> opts = directory_options::none);
        recursive_directory_iterator(const path&amp; p,
          <a href="#directory_options">directory_options</a> opts, system::error_code&amp; ec);
        // deprecated constructors, use overloads accepting directory_options instead
        explicit recursive_directory_iterator(const path&amp; p,
          <a href="#symlink_option">symlink_option</a> opts = symlink_option::none);
        recursive_directory_iterator(const path&amp; p,
          <a href="#symlink_option">symlink_option</a> opts, system::error_code&amp; ec);
        recursive_directory_iterator(const path&amp; p, system::error_code&amp; ec);
       ~recursive_directory_iterator();

        // observers
        int depth() const noexcept;
        bool recursion_pending() const noexcept;

        // deprecated observers
        int level() const noexcept;
        bool no_push_pending() const noexcept;

        // modifiers
        recursive_directory_iterator&amp; operator=(const recursive_directory_iterator&amp;);

        recursive_directory_iterator&amp; operator++();
        recursive_directory_iterator&amp; increment(system::error_code&amp; ec);

        void pop();
        void pop(system::error_code&amp; ec);
        void disable_recursion_pending(bool value = true) noexcept;

        // deprecated modifiers
        void no_push(bool value = true);

        // other members as required by
        //  C++ Std, Input iterators [input.iterators]

      private:
<i><b>        // actual data members will probably be stored in a shared object,
        // or some similar mechanism, to achieve the required input iterator
        // copy semantics</b></i>
        int            m_depth;              <b><i>// for exposition only</i></b>
        bool           m_recursion_pending;  <i><b>// for exposition only</b></i>
        <a href="#directory_options">directory_options</a> m_options;         <i><b>// for exposition only</b></i>
      };

  }  // namespace filesystem
}  // namespace boost</pre>

<p>The behavior of a <code>recursive_directory_iterator</code> is the same
as a <code>directory_iterator</code> unless otherwise specified.</p>
<ul>
  <li>Incrementing a <code>recursive_directory_iterator</code> pointing to a
  directory causes that directory itself to be iterated over, as specified by
  the <code>operator++</code> and <code>increment</code> functions.<br>
&nbsp;</li>
  <li>When a <code>recursive_directory_iterator</code> reaches the end of the directory currently being iterated
  over, or when <code>pop()</code> is called, <code>m_depth</code> is
  decremented, and iteration of the parent directory continues.</li>
</ul>
<pre>recursive_directory_iterator() noexcept;</pre>
<blockquote>

<p><i>Effects:</i> Constructs the end iterator.</p>

</blockquote>

<pre>explicit recursive_directory_iterator(const path&amp; p, <a href="#directory_options">directory_options</a> opts = directory_options::none);
recursive_directory_iterator(const path&amp; p, <a href="#directory_options">directory_options</a> opts, system::error_code&amp; ec);
explicit recursive_directory_iterator(const path&amp; p, <a href="#symlink_option">symlink_option</a> opts = symlink_option::none);
recursive_directory_iterator(const path&amp; p, <a href="#symlink_option">symlink_option</a> opts, system::error_code&amp; ec);
recursive_<code>directory_iterator(</code>const path&amp; p, system::error_code&amp; ec<code>);</code></pre>
<blockquote>

<p><i>Effects:</i>&nbsp; Constructs an iterator representing the first
entry in the directory <code>p</code> resolves to, if any; otherwise, the end iterator.</p>

<p><i>Postcondition: </i>Unless the end iterator was constructed,<i> </i><code>depth() == 0 &amp;&amp; recursion_pending() == true &amp;&amp; m_options == opts</code>.
For the signature without the <code>opts</code> argument, <code>opts</code> is assumed to be <code>directory_options::none</code>.</p>

<p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>

<p>[<i>Note:</i> To iterate over the current directory, use <code>recursive_directory_iterator(&quot;.&quot;)</code> rather than <code>recursive_directory_iterator(&quot;&quot;)</code>. <i>—end note</i>]</p>

<p>[<i>Note:</i> By default, <code>recursive_directory_iterator</code> does not
follow directory symlinks. To follow directory symlinks, specify <code>directory_options::follow_directory_symlink</code> in <code>opts</code>. <i>—end note</i>]</p>
</blockquote>
<pre>int depth() const noexcept;
int level() const noexcept;</pre>
<blockquote>
  <p><i>Requires:</i> <code>*this != recursive_directory_iterator()</code>.</p>
  <p><i>Returns:</i> <code>m_depth</code>.</p>
</blockquote>
<pre>bool recursion_pending() const noexcept;</pre>
<blockquote>
  <p><i>Requires:</i> <code>*this != recursive_directory_iterator()</code>.</p>
  <p><i>Returns:</i> <code>m_recursion_pending</code>.</p>
</blockquote>
<pre>bool no_push_pending() const noexcept;</pre>
<blockquote>
  <p><i>Requires:</i> <code>*this != recursive_directory_iterator()</code>.</p>
  <p><i>Returns:</i> <code>!recursion_pending()</code>.</p>
</blockquote>
<pre><code>recursive_directory_iterator</code>&amp; <a name="recursive_directory_iterator-increment">operator++</a>();
recursive_directory_iterator&amp; increment(system::error_code&amp; ec);</pre>
<blockquote>

<p><i>Effects:</i> As specified by the C++ Standard, 24.1.1 Input iterators [input.iterators],
except:</p>

<ul>
  <li>

<p>if <code>recursion_pending() &amp;&amp; is_directory(this-&gt;status())
&amp;&amp; (!is_symlink(this-&gt;symlink_status()) || (m_options &amp; directory_options::follow_directory_symlink) != 0)</code> then:
  <ul>
    <li>
      <p>directory <code>(*this)-&gt;path()</code> is opened and recursively iterated into and <code>m_depth</code> is incremented;</p>
    </li>
    <li>
      <p>if opening the directory fails with a <code>permission_denied</code> error and <code>(m_options &amp; directory_options::skip_permission_denied) != 0</code>,
      increment the iterator on the current level and ignore the error (the operation completes successfully).</p>
    </li>
  </ul>
</p>

  </li>
  <li>if there are no more directory entries at this level then <code>m_depth</code>
is decremented and iteration of the parent directory resumes.</li>
</ul>

<p>If the operation completes with an error, then
  <ul>
    <li>if <code>(m_options &amp; directory_options::pop_on_error) != 0</code>, the iterator is left in a state as if after repeatedly calling <code>pop()</code> until it succeeds or the iterator becomes equal to an end iterator; any <code>pop()</code> failures are not reported to the caller;</li>
    <li>otherwise, the iterator is left equal to an end iterator.</li>
  </ul>
</p>

<p><i>Postcondition:</i> <code>recursion_pending() == true</code>.</p>

<p><i>Returns:</i> <code>*this</code>.</p>

<p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>

</blockquote>
<pre>void pop();
void pop(system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Requires:</i> <code>*this != recursive_directory_iterator()</code>.</p>
  <p><i>Effects:</i> If <code>depth() == 0</code>, set <code>*this</code> to <code>recursive_directory_iterator()</code>.
  Otherwise, <code>--m_depth</code>, cease iteration of the directory currently being
  iterated over, and continue iteration over the parent directory.</p>
  <p>If the operation completes with an error, then
    <ul>
      <li>if <code>(m_options &amp; directory_options::pop_on_error) != 0</code>, the iterator is left in a state as if after repeatedly calling <code>pop()</code> until it succeeds or the iterator becomes equal to an end iterator; any <code>pop()</code> failures are not reported to the caller;</li>
      <li>otherwise, the iterator is left equal to an end iterator.</li>
    </ul>
  </p>
</blockquote>
<pre>void disable_recursion_pending(bool value = true) noexcept;
void no_push(bool value = true) noexcept;</pre>
<blockquote>
  <p><i>Requires:</i> <code>*this != recursive_directory_iterator()</code>.</p>
  <p><i>Postcondition:</i> <code>recursion_pending() == !value</code>.</p>
  <p>[<i>Note:</i> These functions are used to prevent
  unwanted recursion into a directory. <i>—end note</i>]</p>
</blockquote>
<h3><a name="recursive_directory_iterator-non-member-functions"><code>recursive_directory_iterator</code> non-member functions</a></h3>
<pre>const recursive_directory_iterator&amp; begin(const recursive_directory_iterator&amp; iter);</pre>
<blockquote>
  <p><i>Returns: </i><code>iter</code>.</p>
</blockquote>
<pre>recursive_directory_iterator end(const recursive_directory_iterator&amp;);</pre>
<blockquote>
  <p><i>Returns: </i><code>recursive_directory_iterator()</code>.</p>
</blockquote>
<h2><a name="Operational-functions">Operational functions</a> [fs.op.funcs]</h2>
<p>Operational functions query or modify files, including directories, in external
storage.</p>
<p>Operational functions access a file by resolving an
object of class <code>path</code> to a particular file in a file hierarchy. The
path is resolved as if by the ISO/IEC 9945 <a href="http://www.opengroup.org/onlinepubs/000095399/basedefs/xbd_chap04.html#tag_04_11">Pathname Resolution</a> mechanism.</p>
<p>[<i>Note: </i>Because hardware failures, network failures, <a href="#file-system-race">file system races</a>, and many
other kinds of errors occur frequently in file system operations, users should be aware
that any filesystem operational function, no matter how apparently innocuous, may encounter
an error.&nbsp;See <a href="#Error-reporting">Error reporting</a>. <i>—end note</i>]</p>
<pre>path <a name="absolute">absolute</a>(const path&amp; p, const path&amp; base=current_path());
path absolute(const path&amp; p, system::error_code&amp; ec);
path absolute(const path&amp; p, const path&amp; base, system::error_code&amp; ec);</pre>
  <blockquote>
  <p><i>Returns:</i> A <a href="#Absolute-path">absolute path</a> composed according to the
  following table</p>
  <table border="1" cellpadding="5" cellspacing="0" bordercolor="#111111" style="border-collapse: collapse">
    <tr>
      <td align="center">&nbsp;</td>
      <td align="center"><b><code>p.<br>
      has_root_directory()</code></b></td>
      <td align="center"><b><code>!p.has_root_directory()</code></b></td>
    </tr>
    <tr>
      <td align="center"><b><code>p.has_root_name()</code></b></td>
      <td align="left"><code>return p</code></td>
      <td align="left"><code>return<br>
&nbsp;p.root_name()<br>
&nbsp;/ absolute(base)<br>
&nbsp;&nbsp;&nbsp; .root_directory()<br>
      &nbsp;/ absolute(base)<br>
&nbsp;&nbsp;&nbsp; .relative_path()<br>
&nbsp;/ p.relative_path()</code></td>
    </tr>
    <tr>
      <td align="center"><b><code>!p.has_root_name()</code></b></td>
      <td align="left"><code>return<br>
&nbsp;absolute(base)<br>
&nbsp; .root_name()<br>
      &nbsp;/ p</code></td>
      <td align="left"><code>return absolute(base)<br>
&nbsp;/ p</code></td>
    </tr>
  </table>
  <p>For the overload without a <code>base</code> argument, <code>base</code> is <code>current_path()</code>.</p>
  <p>[<i>Note:</i> For the returned path, <code>rp</code>, <code>rp.is_absolute()</code> is <code>true</code>. <i>—end note</i>]</p>
  <p><i>Throws:</i>&nbsp; As specified in <a href="#Error-reporting">Error reporting</a>.</p>
</blockquote>
<pre>path <a name="canonical">canonical</a>(const path&amp; p, const path&amp; base = current_path());
path canonical(const path&amp; p, system::error_code&amp; ec);
path canonical(const path&amp; p, const path&amp; base, system::error_code&amp; ec);</pre>
<blockquote>
<p><i>Overview:</i> Converts <code>p</code>, which must exist, to an absolute
path that has no symbolic link, <a href="#Dot">dot</a>,
or <a href="#Dot">dot-dot</a> elements. </p>
<p><i>Returns:</i> A <a href="#canonical-path">canonical path</a> that refers to
the same file system object as <code>absolute(p, base)</code>. For the overload
without a <code>base</code> argument, <code>base</code> is <code>current_path()</code>.</p>
  <p><i>Throws:</i>&nbsp; As specified in <a href="#Error-reporting">Error reporting</a>.</p>

  <p><i>Remarks:</i> <code>!exists(p)</code> is an error.</p>

  <p>[<i>Note:</i> Canonical pathnames allow security checking of a path (eg.
  does this path live in /home/<USER>/home/<USER>/p>

</blockquote>
<pre>void copy(const path&amp; from, const path&amp; to);
void copy(const path&amp; from, const path&amp; to, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects: </i><code>copy(from, to, copy_options::none</code><i>[</i><code>, ec</code><i>]</i><code>)</code>.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
</blockquote>
<pre>void <a name="copy">copy</a>(const path&amp; from, const path&amp; to, <a href="#copy_options">copy_options</a> options);
void copy(const path&amp; from, const path&amp; to, <a href="#copy_options">copy_options</a> options, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Precondition:</i> <code>options</code> must contain at most one option from each of the following groups:
    <ul>
      <li><code>copy_options::skip_existing</code>, <code>copy_options::overwrite_existing</code> or <code>copy_options::update_existing</code>;</li>
      <li><code>copy_options::synchronize_data</code> or <code>copy_options::synchronize</code>;</li>
      <li><code>copy_options::recursive</code>;</li>
      <li><code>copy_options::copy_symlinks</code> or <code>copy_options::skip_symlinks</code>;</li>
      <li><code>copy_options::directories_only</code>, <code>copy_options::create_symlinks</code> or <code>copy_options::create_hard_links</code>.</li>
    </ul></p>
  <p><i>Effects:</i> Let <code>f</code> and <code>t</code> be <code>file_status</code> objects obtained the following way:
    <ul>
      <li>If <code>(options &amp; (copy_options::create_symlinks | copy_options::skip_symlinks)) != copy_options::none</code> then <code>f = <a href="#symlink_status">symlink_status</a>(from)</code> and <code>t = <a href="#symlink_status">symlink_status</a>(to)</code>;</li>
      <li>Otherwise, if <code>(options &amp; copy_options::copy_symlinks) != copy_options::none</code> then <code>f = <a href="#symlink_status">symlink_status</a>(from)</code> and <code>t = <a href="#status">status</a>(to)</code>;</li>
      <li>Otherwise, <code>f = <a href="#status">status</a>(from)</code> and <code>t = <a href="#status">status</a>(to)</code>.</li>
    </ul>
    Then, report an error if:
    <ul>
      <li><code>!exists(f)</code>, or</li>
      <li><code>equivalent(from, to)</code>, or</li>
      <li><code>is_other(f) || is_other(t)</code>, or</li>
      <li><code>is_directory(f) &amp;&amp; is_regular_file(t)</code>.</li>
    </ul>
    Otherwise, if <code>is_symlink(f)</code>, then:
    <ul>
      <li>If <code>(options &amp; copy_options::skip_symlinks) != copy_options::none</code> then return;</li>
      <li>Otherwise if <code>!exists(t) &amp;&amp; (options &amp; copy_options::copy_symlinks) != copy_options::none</code> then <code><a href="#copy_symlink">copy_symlink</a>(from, to)</code>;</li>
      <li>Otherwise report error.</li>
    </ul>
    Otherwise, if <code>is_regular_file(f)</code>, then:
    <ul>
      <li>If <code>(options &amp; copy_options::directories_only) != copy_options::none</code> then return;</li>
      <li>Otherwise if <code>(options &amp; copy_options::create_symlinks) != copy_options::none</code> then <code><a href="#create_symlink">create_symlink</a>(link, to)</code>, where <code>link</code> is determined as follows:
        <ul>
          <li>If <code>from.is_absolute()</code> then <code>link</code> equals to <code>from</code>;</li>
          <li>Otherwise, <code>link</code> is equivalent to <code><a href="#relative">relative</a>(<a href="#absolute">absolute</a>(to.parent_path()), <a href="#absolute">absolute</a>(from.parent_path())) / from.filename()</code>.</li>
        </ul>
      </li>
      <li>Otherwise if <code>(options &amp; copy_options::create_hard_links) != copy_options::none</code> then <code><a href="#create_hard_link">create_hard_link</a>(from, to)</code>;</li>
      <li>Otherwise if <code>is_directory(t)</code> then <code><a href="#copy_file">copy_file</a>(from, to / from.filename(), options)</code>;</li>
      <li>Otherwise <code><a href="#copy_file">copy_file</a>(from, to, options)</code>.</li>
    </ul>
    Otherwise, if <code>is_directory(f)</code>, then:
    <ul>
      <li>If <code>(options &amp; copy_options::create_symlinks) != copy_options::none</code> then report error with error code equal to <code>make_error_code(system::errc::is_a_directory)</code>;</li>
      <li>Otherwise if
        <ul>
          <li><code>(options &amp; copy_options::recursive) != copy_options::none</code>, or</li>
          <li><code>options == copy_options::none</code> and this call to <code>copy</code> is not a recursive call from <code>copy</code></li>
        </ul>
        then:
        <ul>
          <li>If <code>!exists(t)</code>, then <code><a href="#create_directory">create_directory</a>(to, from)</code>.</li>
          <li>Then, iterate over files in <code>from</code> and for each <code>directory_entry x</code> obtained during iteration invoke <code>copy(x.path(), to / x.path().filename(), options)</code>.</li>
        </ul>
      </li>
      <li>Otherwise, return.</li>
    </ul>
    Otherwise, for all unsupported file types of <code>f</code> report error.</p>

  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>

</blockquote>
<pre>void <a name="copy_directory">copy_directory</a>(const path&amp; from, const path&amp; to);
void copy_directory(const path&amp; from, const path&amp; to, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects: </i>Creates directory <code>to</code>, with
  attributes copied from directory <code>from</code>. The set of attributes
  copied is operating system dependent.</p>

  <p>[<i>Note:</i> For ISO 9945/POSIX based operating systems the
  attributes are those copied by native API <code>stat(from.c_str(), &amp;from_stat)</code>
  followed by <code>mkdir(to.c_str(),from_stat.st_mode)</code>.&nbsp; For
  Windows based operating systems the attributes are those copied by native
  API <code>CreateDirectoryExW(from.c_str(), to.c_str(), 0)</code>.&nbsp; <i>
  —end note</i>]</td>

  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>

  <p>[<i>Note:</i> This operation is deprecated, use <a href="#create_directory">create_directory</a> instead. —end note]</p>
</blockquote>

<pre>bool copy_file(const path&amp; from, const path&amp; to);
bool copy_file(const path&amp; from, const path&amp; to, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects: </i><code>return copy_file(from, to, copy_options::none</code><i>[</i><code>, ec</code><i>]</i><code>)</code>.</p>

  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>

</blockquote>
<pre>bool <a name="copy_file">copy_file</a>(const path&amp; from, const path&amp; to, <a href="#copy_options">copy_options</a> options);
bool copy_file(const path&amp; from, const path&amp; to, <a href="#copy_options">copy_options</a> options, system::error_code&amp; ec);
bool copy_file(const path&amp; from, const path&amp; to, <a href="#copy_option">copy_option</a> options);
bool copy_file(const path&amp; from, const path&amp; to, <a href="#copy_option">copy_option</a> options, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Precondition:</i> <code>options</code> must contain at most one option from each of the following groups:
    <ul>
      <li><code>copy_options::skip_existing</code>, <code>copy_options::overwrite_existing</code> or <code>copy_options::update_existing</code>;</li>
      <li><code>copy_options::synchronize_data</code> or <code>copy_options::synchronize</code>.</li>
    </ul>
  </p>
  <p><i>Effects:</i> Report an error if:
    <ul>
      <li><code>!is_regular_file(from)</code>, or</li>
      <li><code>exists(to) &amp;&amp; !is_regular_file(to)</code>, or</li>
      <li><code>exists(to) &amp;&amp; equivalent(from, to)</code>, or</li>
      <li><code>exists(to) &amp;&amp; (options &amp; (copy_options::skip_existing | copy_options::overwrite_existing)) == copy_options::none</code>.</li>
    </ul>
    Otherwise, return successfully with no effect if:
    <ul>
      <li><code>exists(to) &amp;&amp; (options &amp; copy_options::skip_existing) != copy_options::none</code>, or</li>
      <li><code>exists(to) &amp;&amp; (options &amp; copy_options::update_existing) != copy_options::none</code> and last write time of <code>from</code> is more recent than that of <code>to</code>.</li>
    </ul>
    Otherwise:
    <ul>
     <li>The contents and attributes of the file <code>from</code> resolves to are copied to the file <code>to</code> resolves to; then</li>
     <li>If <code>(options &amp; copy_options::synchronize) != copy_options::none</code>, the written data and attributes are synchronized with the permanent storage; otherwise</li>
     <li>If <code>(options &amp; copy_options::synchronize_data) != copy_options::none</code>, the written data is synchronized with the permanent storage.</li>
    </ul>
  </p>
  <p><i>Returns:</i> <code>true</code> if the file was copied without error, otherwise <code>false</code>.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
  <p>[<i>Note:</i> The overloads taking <a href="#copy_option"><code>copy_option</code></a> are deprecated. Their effect is equivalent to the corresponding overloads taking <a href="#copy_options"><code>copy_options</code></a> after casting the <code>options</code> argument to <a href="#copy_options"><code>copy_options</code></a>.]</p>
  <p>[<i>Note:</i> When <code>copy_options::update_existing</code> is specified, checking the write times of <code>from</code> and <code>to</code> may not be atomic with the copy operation. Another process may create or modify the file identified by <code>to</code> after the file modification times have been checked but before copying starts. In this case the target file will be overwritten.]</p>
  <p>[<i>Note:</i> The <code>copy_options::synchronize_data</code> and <code>copy_options::synchronize</code> options may have a significant performance impact. The <code>copy_options::synchronize_data</code> option may be less expensive than <code>copy_options::synchronize</code>. However, without these options, upon returning from <code>copy_file</code> it is not guaranteed that the copied file is completely written and preserved in case of a system failure. Any delayed write operations may fail after the function returns, at the point of physically writing the data to the underlying media, and this error will not be reported to the caller.]</p>
</blockquote>
<pre>void <a name="copy_symlink">copy_symlink</a>(const path&amp; existing_symlink, const path&amp; new_symlink);
void copy_symlink(const path&amp; existing_symlink, const path&amp; new_symlink, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects: </i><code>create_symlink(read_symlink(existing_symlink</code><i>[</i><code>, ec</code><i>]</i><code>), new_symlink</code><i>[</i><code>, ec</code><i>]</i><code>)</code>.</p>

  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>

</blockquote>
<pre>bool <a name="create_directories">create_directories</a>(const path&amp; p);
bool <a name="create_directories2">create_directories</a>(const path&amp; p, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects:</i> Establishes the postcondition by calling <code>
  create_directory()</code> for any element of <code>p</code> that does not
  exist.</p>
  <p><i>Postcondition:</i> <code>is_directory(p)</code></p>
  <p><i>Returns:</i> <code>true</code> if a new directory was created, otherwise <code>
  false</code>.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
  <p><i>Complexity:</i> <i>O(n+1)</i> where <i>n</i> is the number of elements
  of <code>p</code> that do not exist.</p>
</blockquote>
<pre>bool <a name="create_directory">create_directory</a>(const path&amp; p);
bool create_directory(const path&amp; p, system::error_code&amp; ec);
bool create_directory(const path&amp; p, const path&amp; existing);
bool create_directory(const path&amp; p, const path&amp; existing, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects:</i> Establishes the postcondition by attempting to create the
  directory <code>p</code> resolves to, as if by ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/mkdir.html">
  mkdir()</a></code>. For overloads without <code>existing</code> argument, the new directory is created with <code>S_IRWXU|S_IRWXG|S_IRWXO</code> mode.
  Overloads with <code>existing</code> argument obtain mode from <code>existing</code>, which must be a path to an existing directory. On Windows,
  <code>CreateDirectoryW(p.c_str(), NULL)</code> is used when <code>existing</code> is not specified and <code>CreateDirectoryExW(existing.c_str(), p.c_str(), NULL)</code> otherwise.
  Creation failure because <code>p</code> resolves to an existing directory shall not be
  treated as an error.</p>
  <p><i>Postcondition:</i> <code>is_directory(p)</code></p>
  <p><i>Returns:</i> <code>true</code> if a new directory was created, otherwise <code>false</code>.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
</blockquote>
<pre>void <a name="create_directory_symlink">create_directory_symlink</a>(const path&amp; to, const path&amp; new_symlink);
void create_directory_symlink(const path&amp; to, const path&amp; new_symlink, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects:</i> Establishes the postcondition, as if by ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/symlink.html">symlink()</a></code>.</p>
  <p><i>
  Postcondition:</i> <code>new_symlink</code> resolves to a symbolic link file that
  contains an unspecified representation of <code>to</code>.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
  <p>[<i>Note:</i> Some operating systems, such as Windows, require symlink creation to
  identify that the link is to a directory. Portable code should use <code>create_directory_symlink()</code> to create directory symlinks rather than <code>create_symlink()</code> <i>—end note</i>]</p>
  <p>[<i>Note:</i> Some operating systems do not support symbolic links at all or support
  them only for regular files.
  Some file systems do not
  support
  symbolic links regardless of the operating system - the FAT file system used on
  memory cards and flash drives, for example. <i>—end note</i>]</p>
  </blockquote>
<pre>void <a name="create_hard_link">create_hard_link</a>(const path&amp; to, const path&amp; new_hard_link);
void <a name="create_hard_link2">create_hard_link</a>(const path&amp; to, const path&amp; new_hard_link, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects:</i> Establishes the postcondition, as if by ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/link.html">link()</a></code>.</p>
  <p><i>Postcondition:</i></p>
  <ul>
    <li>&nbsp;<code>exists(to) &amp;&amp;
    exists(</code><code>new_hard_link</code><code>) &amp;&amp; equivalent(to,

    </code><code>new_hard_link</code><code>)</code></li>
    <li>The contents of the file or directory
    <code>to</code> resolves to are unchanged.</li>
  </ul>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
  <p>[<i>Note:</i> Some operating systems do not support hard links at all or support
  them only for regular files. Some file systems do not support hard
  links regardless of the operating system - the FAT file system used on memory
  cards and flash drives, for example. Some file systems limit the number of
  links per file. <i>—end note</i>]</p>
  </blockquote>
<pre>void <a name="create_symlink">create_symlink</a>(const path&amp; to, const path&amp; new_symlink);
void <a name="create_symlink2">create_symlink</a>(const path&amp; to, const path&amp; new_symlink, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects:</i> Establishes the postcondition, as if by ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/symlink.html">symlink()</a></code>.</p>
  <p><i>
  Postcondition:</i> <code>new_symlink</code> resolves to a symbolic link file that
  contains an unspecified representation of <code>to</code>.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
  <p>[<i>Note:</i> Some operating systems do not support symbolic links at all or support
  them only for regular files.
  Some file systems do not
  support
  symbolic links regardless of the operating system - the FAT system used on
  memory cards and flash drives, for example. <i>—end note</i>]</p>
  </blockquote>
<pre>path <a name="current_path">current_path</a>();
path <a name="current_path2">current_path</a>(system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Returns:</i> The current working directory path, as if by ISO/IEC
  9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/getcwd.html">getcwd()</a></code>. <code>is_absolute()</code> is true for the returned path.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
  <p>[<i>Note: </i>The <code>current_path()</code> name was chosen to emphasize that the return is a
  path, not just a single directory name.</p>
  <p>The current path as returned by many operating systems is a dangerous
  global variable. It may be changed unexpectedly by a third-party or system
  library functions, or by another thread.&nbsp; <i>—end note</i>]</p>
</blockquote>
<pre>void current_path(const path&amp; p);
void current_path(const path&amp; p, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects:</i> Establishes the postcondition, as if by ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/chdir.html">chdir()</a></code>.</p>
<p><i>Postcondition:</i> <code>equivalent(p, current_path())</code>.</p>
<p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
  <p>[<i>Note: </i>The current path for many operating systems is a dangerous
  global state. It may be changed unexpectedly by a third-party or system
  library functions, or by another thread.&nbsp; <i>—end note</i>]</p>
</blockquote>
<pre>bool <a name="exists">exists</a>(file_status s) noexcept;</pre>
<blockquote>
  <p><i>Returns:</i> <code>status_known(s) &amp;&amp; s.type() != file_not_found</code></p>
</blockquote>
<pre>bool <a name="exists2">exists</a>(const path&amp; p);
bool <a name="exists3">exists</a>(const path&amp; p, system::error_code&amp; ec) noexcept;</pre>
<blockquote>
  <p><i>Returns:</i> <code>exists(status(p))</code> or <code>exists(status(p, ec))</code>,
  respectively.</p>
<p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
</blockquote>
<pre><code>bool <a name="equivalent">equivalent</a>(const path&amp; p1, const path&amp; p2);
bool <a name="equivalent2">equivalent</a>(const path&amp; p1, const path&amp; p2, system::error_code&amp; ec);</code></pre>
<blockquote>
  <p><i>Effects:</i> Determines <code>file_status s1</code> and <code>s2</code>, as if by <code>status(p1)</code> and&nbsp; <code>status(p2)</code>,
  respectively.</p>
  <p><i>Returns:</i> <code>true</code>, if <code>sf1 ==
  sf2</code> and <code>p1</code> and <code>p2</code> resolve to the same file
  system entity, else <code>false</code>.</p>
  <blockquote>
  <p>Two paths are considered to resolve to the same
  file system entity if two candidate entities reside on the same device at the
  same location. This is determined as if by the values of the ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/basedefs/sys/stat.h.html">stat</a></code> structure<code>,</code> obtained as if by <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/stat.html">stat()</a></code> for the two paths, having equal <code>st_dev</code> values
  and equal <code>st_ino</code> values.</p>
  <p>[<i>Note:</i> ISO/IEC 9945 requires that <i>&quot;st_dev</i> must be unique within a Local Area Network&quot;. Conservative
  ISO/IEC 9945 implementations may also wish to check for equal <code>st_size</code> and <code>st_mtime</code> values. <i>Windows</i> implementations may use <code>GetFileInformationByHandle()</code> as a surrogate for <code>stat()</code>,
  and consider &quot;same&quot; to be equal values for <code>dwVolumeSerialNumber</code>, <code>nFileIndexHigh</code>, <code>nFileIndexLow</code>, <code>nFileSizeHigh</code>, <code>nFileSizeLow</code>, <code>ftLastWriteTime.dwLowDateTime</code>, and <code>ftLastWriteTime.dwHighDateTime</code>. <i>—end note</i>]</p>
  </blockquote>
  <p><i>Throws:</i> <code>filesystem_error</code> if <code>(!exists(s1) &amp;&amp; !exists(s2)) || (is_other(s1) &amp;&amp; is_other(s2))</code>,
  otherwise as specified in <a href="#Error-reporting">Error reporting</a>.</p>
</blockquote>

<pre>uintmax_t <a name="file_size">file_size</a>(const path&amp; p);
uintmax_t <a name="file_size2">file_size</a>(const path&amp; p, system::error_code&amp; ec);</pre>

<blockquote>
  <p><i>Returns:</i> If <code>exists(p) &amp;&amp; is_regular_file(p)</code>, the size
  in bytes
  of the file <code>p</code> resolves to, determined as if by the value of
  the ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/basedefs/sys/stat.h.html">stat</a></code> structure member <code>st_size</code> obtained as if by
  ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/stat.html">stat()</a></code>.
  Otherwise, <code>static_cast&lt;uintmax_t&gt;(-1)</code>.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
</blockquote>
<pre>uintmax_t <a name="hard_link_count">hard_link_count</a>(const path&amp; p);
uintmax_t hard_link_count(const path&amp; p, system::error_code&amp; ec);</pre>
<blockquote>

  <p><i>Returns:</i> The number of hard links for <code>p</code>.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>

</blockquote>

<pre>const path&amp; <a name="initial_path">initial_path</a>();
const path&amp; <a name="initial_path2">initial_path</a>(<code>system::error_code&amp; ec</code>);</pre>
<blockquote>
  <p><i>Returns:</i> <code>current_path()</code> as of the first call to <code>initial_path()</code>.</p>
  <p>[<i>Note:</i> <code>initial_path()</code> is not thread safe, and may return an undesirable result
  if called subsequent to a change to the current directory. These problems can
  be avoided by calling <code>initial_path()</code> immediately on entry to
  main().&nbsp; <i>—end note</i>]</p>
  <p><i>Throws:</i> For the first call, as specified in <a href="#Error-reporting">Error reporting</a>. Subsequent calls throw nothing.</p>
</blockquote>
<pre>bool <code><a name="is_directory">is_directory</a></code>(file_status s) noexcept;</pre>
<blockquote>
  <p><i>Returns:</i> <code>s.type() == directory_file</code></p>
</blockquote>
<pre><code>bool <a name="is_directory2">is_directory</a>(const path&amp; p);
bool <a name="is_directory3">is_directory</a>(const path&amp; p, system::error_code&amp; ec) noexcept;</code></pre>
<blockquote>
  <p><i>Returns:</i> <code>is_directory(status(p))</code> or <code>is_directory(status(p, ec))</code>,
  respectively.</p>
<p><i>Throws:</i> <code>filesystem_error</code>; overload with <code>error_code&amp;</code> throws
nothing.</p>
</blockquote>
<pre><code>bool <a name="is_empty">is_empty</a>(const path&amp; p);
bool <a name="is_empty2">is_empty</a></a>(const path&amp; p, system::error_code&amp; ec);</code></pre>
<blockquote>
  <p><i>Effects:</i> Determines <code>file_status s</code>, as if by <code>status(p, ec)</code>.</p>
  <p><i>Returns:</i> <code>is_directory(s)<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ? directory_iterator(p) == directory_iterator()<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : file_size(p) == 0;</code></p>
</blockquote>
<pre>bool <code><a name="is_regular_file">is_regular_file</a></code>(file_status s) noexcept;</pre>
<blockquote>
  <p><i>Returns:</i> <code>s.type() == regular_file</code></p>
</blockquote>
<pre><code>bool <a name="is_regular_file2">is_regular_file</a>(const path&amp; p);</code></pre>
<blockquote>
  <p><i>Returns:</i> <code>is_regular_file(status(p))</code>.</p>
  <p><i>Throws:</i> <code>filesystem_error</code> if <code>status(p)</code> would throw <code>filesystem_error.</code></p>
  </blockquote>
<pre><code>bool <a name="is_regular_file3">is_regular_file</a>(const path&amp; p, system::error_code&amp; ec) noexcept;</code></pre>
<blockquote>
  <p><i>Effects:</i> Sets <code>ec</code> as if by <code>status(p, ec)</code>. [<i>Note:</i> <code>status_error</code>, <code>file_not_found</code> and <code>type_unknown</code> cases set <code>ec</code> to error values. To distinguish between cases, call the <code>status</code> function directly. <i>—end
  note</i>] </p>
  <p><i>Returns:</i> <code>is_regular_file(status(p, ec))</code>.</p>
</blockquote>
<pre>bool <a name="is_other">is_other</a>(file_status s) noexcept;</pre>
<blockquote>
  <p><i>Returns:</i> <code>return exists(s) &amp;&amp; !is_regular_file(s) &amp;&amp; !is_directory(s) &amp;&amp; !is_symlink(s)</code></p>
</blockquote>
<pre><code>bool <a name="is_other2">is_other</a>(const path&amp; p);
bool <a name="is_other3">is_other</a>(const path&amp; p, system::error_code&amp; ec) noexcept;</code></pre>
<blockquote>
  <p><i>Returns:</i> <code>is_other(status(p))</code> or <code>is_other(status(p, ec))</code>,
  respectively.</p>
  <p><i>Throws:</i> <code>filesystem_error</code>; overload with <code>error_code&amp;</code> throws
  nothing.</p>
</blockquote>
<pre>bool <a name="is_symlink">is_symlink</a>(file_status s) noexcept;</pre>
<blockquote>
  <p><i>Returns:</i> <code>s.type() == symlink_file</code></p>
</blockquote>
<pre><code>bool <a name="is_symlink2">is_symlink</a>(const path&amp; p);
bool <a name="is_symlink3">is_symlink</a>(const path&amp; p, system::error_code&amp; ec) noexcept;</code></pre>
<blockquote>
  <p><i>Returns:</i> <code>is_symlink(symlink_status(p))</code> or <code>is_symlink(symlink_status(p, ec))</code>,
  respectively.</p>
  <p><i>Throws:</i> <code>filesystem_error</code>; overload with <code>error_code&amp;</code> throws
  nothing.</p>
</blockquote>
<pre><code>std::time_t <a name="creation_time">creation_time</a>(const path&amp; p);
std::time_t <a name="creation_time2">creation_time</a>(const path&amp; p, system::error_code&amp; ec);</code></pre>
<blockquote>
  <p><i>Returns:</i> The time of creation of the file to which <code>p</code> resolves.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
  <p>[<i>Note:</i> Not all platforms support querying file creation time. Where not supported, the operation will fail with
  <code>errc::function_not_supported</code> error code. <i>—end note</i>]</p>
</blockquote>
<pre><code>std::time_t <a name="last_write_time">last_write_time</a>(const path&amp; p);
std::time_t <a name="last_write_time2">last_write_time</a>(const path&amp; p, system::error_code&amp; ec);</code></pre>
<blockquote>
  <p><i>Returns:</i> The time of last data modification of <code>p</code>, determined as if by the
  value of the ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/basedefs/sys/stat.h.html">stat</a></code> structure member <code>st_mtime</code>&nbsp; obtained
  as if by ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/stat.html">stat()</a></code>.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
</blockquote>
<pre><code>void <a name="last_write_time3">last_write_time</a>(const path&amp; p, const std::time_t new_time);
void <a name="last_write_time4">last_write_time</a>(const path&amp; p, const std::time_t new_time, system::error_code&amp; ec);</code></pre>
<blockquote>
  <p><i>Effects:</i> Sets the time of last data modification of the file
  resolved to by <code>p</code> to <code>new_time</code>, as if by ISO/IEC
  9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/stat.html">stat()</a></code> followed by
  ISO/IEC 9945 <a href="http://www.opengroup.org/onlinepubs/000095399/functions/utime.html"><code>utime()</code></a>.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
  <p>[<i>Note:</i> A postcondition of <code>last_write_time(p) == new_time</code> is not specified since it might not hold for file systems
  with coarse time granularity. <i>—end note</i>]</p>
</blockquote>
<pre><code>void <a name="permissions">permissions</a>(const path&amp; p, <a href="#symlink_perms">perms</a> prms);
void permissions(const path&amp; p, <a href="#symlink_perms">perms</a> prms, system::error_code&amp; ec);</code></pre>
<blockquote>
  <p>
  <i>Requires:</i> <code>!((prms &amp; add_perms) &amp;&amp; (prms &amp; remove_perms))</code>.</p>
  <p><i>Effects:</i> Applies the effective permissions bits from <code>prms</code> to the file <code>p</code> resolves to, as if by
  ISO/IEC 9945 <code><a href="http://pubs.opengroup.org/onlinepubs/9699919799/functions/fchmodat.html">fchmodat()</a></code>. The effective permission bits are determined as
  specified by the following table. </p>
  <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111">
    <tr>
      <td><b>bits present in <code>prms</code></b></td>
      <td><b>Effective bits applied</b></td>
    </tr>
    <tr>
      <td>Neither <code>add_perms</code> nor <code>remove_perms</code></td>
      <td><code>prms &amp; perms_mask</code></td>
    </tr>
    <tr>
      <td><code>add_perms</code></td>
      <td>
      <p><code>status(p).permissions() | (prms &amp; <a href="#perms_mask">perms_mask</a>)</code> </td>
    </tr>
    <tr>
      <td><code>remove_perms</code></td>
      <td><code>status(p)</code><code>.permissions() &amp; ~(prms &amp; <a href="#perms_mask">perms_mask</a>) </code></td>
    </tr>
  </table>
  <p>[<i>Note:</i> Conceptually permissions are viewed as bits, but the actual
  implementation may use some other mechanism. <i>—end note</i>]</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
</blockquote>
<pre>path <a name="read_symlink">read_symlink</a>(const path&amp; p);
path read_symlink(const path&amp; p, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Returns:</i>&nbsp; If <code>p</code> resolves to a symbolic
  link, a <code>path</code> object containing the contents of that symbolic
  link. Otherwise an empty <code>path</code> object.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>. [<i>Note:</i> It is an error if <code>p</code> does not
  resolve to a symbolic link. <i>—end note</i>]</p>
</blockquote>
<pre>path <a name="relative">relative</a>(const path&amp; p, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Returns:</i> <code>relative(p, current_path(), ec)</code>.</p>
  <p><i>Throws:</i>&nbsp; As specified in Error reporting.</p>
</blockquote>
<pre>path relative(const path&amp; p, const path&amp; base=current_path());
path relative(const path&amp; p, const path&amp; base, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Overview:</i> Returns <code>p</code> made relative to <code>base</code>.
  Treats empty or identical paths as corner cases, not errors. Resolves symlinks
  and normalizes both <code>p</code> and <code>base</code> before other
  processing.</p>
  <p><i>Returns:</i> <code><a href="#weakly_canonical">weakly_canonical</a>(p).<a href="#lex-relative">lexically_relative</a>(<a href="#weakly_canonical">weakly_canonical</a>(base))</code>. The second form returns <code>path()</code>
  if an error occurs.</p>
  <p><i>Throws:</i> As specified in Error reporting.</p>
</blockquote>
<pre>bool <a name="remove">remove</a>(const path&amp; p);
bool <a name="remove2">remove</a>(const path&amp; p, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects:</i>&nbsp; If <code>exists(symlink_status(p,ec))</code>, it is
  removed
  as if by ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/remove.html">remove()</a></code>.</p>
  <blockquote>
  <p>[<i>Note:</i> A symbolic link is itself removed, rather than the file it
  resolves to being removed. <i>—end note</i>]</p>
  </blockquote>
  <p><i>Postcondition:</i> <code>!exists(symlink_status(p))</code>.</p>
  <p><i>Returns:</i>&nbsp; <code>false</code> if <code>p</code> did not exist, otherwise <code>true</code>.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
</blockquote>
<pre>uintmax_t <a name="remove_all">remove_all</a>(const path&amp; p);
uintmax_t <a name="remove_all2">remove_all</a>(const path&amp; p, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects:</i>&nbsp; Recursively deletes the contents of <code>p</code> if it exists,
  then deletes file <code>p</code> itself,
  as if by ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/remove.html">remove()</a></code>.</p>
  <blockquote>
  <p>[<i>Note:</i> A symbolic link is itself removed, rather than the file it
  resolves to being removed. <i>—end note</i>]</p>
  </blockquote>
  <p><i>Postcondition:</i> <code>!exists(p)</code></p>
  <p><i>Returns:</i> The number of files removed.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
</blockquote>
<pre>void <a name="rename">rename</a>(const path&amp; old_p, const path&amp; new_p);
void <a name="rename2">rename</a>(const path&amp; old_p, const path&amp; new_p, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects:</i> Renames <code>old_p</code> to <code>new_p</code>, as if by
  ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/rename.html">rename()</a></code>.</p>
  <blockquote>
  <p>[<i>Note:</i> If <code>old_p</code> and <code>new_p</code> resolve to the
  same existing file, no action is taken. Otherwise, if <code>new_p</code> resolves to an
  existing non-directory file, it is removed, while if <code>new_p</code> resolves to an
  existing directory, it is removed if empty on ISO/IEC 9945 but is an error on Windows. A symbolic link is itself renamed, rather than
  the file it resolves to being renamed. <i>—end note</i>]</p>
  </blockquote>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
</blockquote>
<pre>void <a name="resize_file">resize_file</a>(const path&amp; p, uintmax_t new_size);
void <a name="resize_file2">resize_file</a>(const path&amp; p, uintmax_t new_size, system::error_code&amp; ec);</pre>
<blockquote>
<p><i>Postcondition:</i> <code>file_size() == new_size</code>.</p>
<p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
  <p><i>Remarks:</i> Achieves its postconditions as if by ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/truncate.html">truncate()</a></code>.</p>
</blockquote>
<pre>space_info <a name="space">space</a>(const path&amp; p);
space_info <a name="space2">space</a>(const path&amp; p, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Returns:</i> An object of type <code><a href="#space_info">space_info</a></code>. The value of the <code>space_info</code> object is determined as if by
  using ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/statvfs.html"
   style="text-decoration: none">statvfs()</a></code> to obtain an ISO/IEC 9945 struct
  <code><a href="http://www.opengroup.org/onlinepubs/000095399/basedefs/sys/statvfs.h.html" style="text-decoration: none">statvfs</a></code>,
   and then multiplying its <code>f_blocks</code>, <code>f_bfree</code>,
   and <code>f_bavail</code> members by its <code>f_frsize</code> member,
   and assigning the results to the <code>capacity</code>, <code>free</code>,
  and <code>available</code> members respectively. Any members for which the
  value cannot be determined shall be set to -1.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
</blockquote>
<pre><a href="#file_status">file_status</a> <a name="status">status</a>(const path&amp; p);</pre>
<blockquote>
  <p><i>Effects: </i>As if:</p>
  <blockquote>
    <pre>system::error_code ec;
file_status result = status(p, ec);
if (result == status_error)
  throw filesystem_error(<i>implementation-supplied-message</i>, p, ec);
return result;</pre>
  </blockquote>
  <p><i>Returns:</i> See above.</p>
  <p><i>Throws:</i> <code>filesystem_error</code>.
[<i>Note:</i> <code>result</code> values of <code>file_status(file_not_found)</code>and <code>file_status(type_unknown)</code> are not considered failures and do not
  cause an exception to be
thrown.<i> —end note</i>] </p>
  </blockquote>
<pre><a href="#file_status">file_status</a> <a name="status2">status</a>(const path&amp; p, system::error_code&amp; ec) noexcept;</pre>
<blockquote>
  <p><i>Effects: </i></p>
  <blockquote>
    <p>If possible, determines the attributes
    of the file <code>p</code> resolves to, as if by ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/stat.html">stat()</a></code>.</p>
      If, during attribute determination, the underlying file system API reports
    an error, sets <code>ec</code> to indicate the specific error reported.
    Otherwise, <code>ec.clear()</code>.<blockquote>
      <p>[<i>Note:</i> This allows users to inspect the specifics of underlying
      API errors even when the value returned by <code>status()</code> is not <code>file_status(status_error)</code>.&nbsp; <i>—end note</i>]</p>
    </blockquote>
    </blockquote>
  <p><i>Returns:</i></p>
  <blockquote>
    <p>If <code>ec != error_code()</code>:</p>
    <ul>
      <li>If the specific error indicates that <code>p</code> cannot be resolved
      because some element of the path does not exist, return <code>
      file_status(file_not_found)</code>. [<i>Note:</i> ISO/IEC 9945 errors that
      indicate this are ENOENT or ENOTDIR. Windows equivalents
      include ERROR_FILE_NOT_FOUND, ERROR_PATH_NOT_FOUND, ERROR_INVALID_NAME,
      ERROR_INVALID_PARAMETER, ERROR_BAD_PATHNAME, and ERROR_BAD_NETPATH. <i>--
      end note</i>]<br>
&nbsp;</li>
      <li>Otherwise, if the specific error indicates that <code>p</code> can be resolved
      but the attributes cannot be determined, return <code>
      file_status(type_unknown)</code>. [<i>Note: </i>For example, Windows
      ERROR_SHARING_VIOLATION errors. For ISO/IEC 9945, the case never arises. <i>—end
      note</i>]<br>
&nbsp;</li>
      <li>Otherwise, return <code>
      file_status(status_error)</code>.</li>
    </ul>
        <blockquote>
        <p>[<i>Note:</i> These semantics distinguish between <code>p</code> being known not to exist, <code>p</code> existing but not being able to determine its attributes,
        and there being an error that prevents even knowing if <code>p</code> exists. These
        distinctions are important to some use cases.&nbsp;<i>—end note</i>]</p>
    </blockquote>
    <p>Otherwise,</p>
    <ul>
      <li>If the attributes indicate a regular file, as if by ISO/IEC 9945&nbsp;<a href="http://www.opengroup.org/onlinepubs/000095399/basedefs/sys/stat.h.html">S_ISREG()</a>,
      return <code>
      file_status(regular_file)</code>. [<i>Note:</i> <code>
regular_file</code> implies appropriate <code>&lt;fstream&gt;</code> operations
      would succeed, assuming no hardware, permission, access, or file system
      race
      errors. Lack of
<code>regular_file</code> does not necessarily imply <code>&lt;fstream&gt;</code> operations would
fail on a directory.
<i>—end note</i>]<br>
&nbsp;</li>
      <li>Otherwise, if the attributes indicate a directory, as if by ISO/IEC 9945
      <a href="http://www.opengroup.org/onlinepubs/000095399/basedefs/sys/stat.h.html">S_ISDIR()</a>,
      return <code>
      file_status(directory_file)</code>. [<i>Note:</i> <code>directory_file</code> implies <code>
directory_iterator(p)</code>would succeed.
<i>—end note</i>]<br>
&nbsp;</li>
      <li>Otherwise, if the attributes indicate a block special file, as if by ISO/IEC 9945
      <a href="http://www.opengroup.org/onlinepubs/000095399/basedefs/sys/stat.h.html">S_ISBLK()</a>,
      return <code>
      file_status(block_file)</code>.<br>
&nbsp;</li>
      <li>Otherwise, if the attributes indicate a character special file, as if by ISO/IEC 9945
      <a href="http://www.opengroup.org/onlinepubs/000095399/basedefs/sys/stat.h.html">S_ISCHR()</a>,
      return <code>
      file_status(character_file)</code>.<br>
&nbsp;</li>
      <li>Otherwise, if the attributes indicate a fifo or pipe file, as if by
      ISO/IEC 9945
      <a href="http://www.opengroup.org/onlinepubs/000095399/basedefs/sys/stat.h.html">S_ISFIFO()</a>,
      return <code>
      file_status(fifo_file)</code>.<br>
&nbsp;</li>
      <li>Otherwise, if the attributes indicate a socket, as if by ISO/IEC
      9945
      <a href="http://www.opengroup.org/onlinepubs/000095399/basedefs/sys/stat.h.html">S_ISSOCK()</a>,
      return <code>
      file_status(socket_file)</code>.<br>
&nbsp;</li>
      <li>Otherwise, return <code>
      file_status(type_unknown)</code>.</li>
    </ul>
    </blockquote>
      <p><i>Remarks:</i> If a symbolic link is encountered during pathname
      resolution,
      pathname resolution continues using the contents of the symbolic link.</p>
</blockquote>
<pre>bool <a name="status_known">status_known</a>(file_status s) noexcept;</pre>
<blockquote>
  <p><i>Returns:</i> <code>s.type() != status_error</code></p>
</blockquote>
<pre>file_status <a name="symlink_status">symlink_status</a>(const path&amp; p);
file_status <a name="symlink_status2">symlink_status</a>(const path&amp; p, system::error_code&amp; ec) noexcept;</pre>
<blockquote>
  <p><i>Effects:</i>&nbsp; Same as <a href="#status">status()</a>, above,
  except that the attributes
    of <code>p</code> are determined as if by ISO/IEC 9945 <code><a href="http://www.opengroup.org/onlinepubs/000095399/functions/lstat.html">lstat()</a></code>.</p>
</blockquote>
<blockquote>
      <p><i>Returns:</i> Same as <a href="#status">status()</a>, above, except
      that if the attributes indicate a symbolic link, as if by ISO/IEC 9945 <a class="external" href="http://www.opengroup.org/onlinepubs/000095399/basedefs/sys/stat.h.html">S_ISLNK()</a>, return <code>file_status(symlink_file)</code>.</p>
      <p><i>Remarks:</i> Pathname resolution terminates if <code>p</code> names a symbolic link.</p>
  <p><i>Throws:</i> <code>filesystem_error</code>; overload with <code>error_code&amp;</code> throws
  nothing.</p>
</blockquote>
<pre>path <a name="system_complete">system_complete</a>(const path&amp; p);
path <a name="system_complete2">system_complete</a>(const path&amp; p, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Effects:</i> Composes an absolute path from <code>p</code>, using the
  same rules used by the operating system to resolve a path passed as the
  filename argument to standard library open functions.</p>
  <p><i>Returns:</i> The composed path.</p>
  <p><i>Postcondition:</i> For the returned path, <code>rp</code>, <code>rp.is_absolute()</code> is true.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
  <p>[<i>Note:</i> For ISO/IEC 9945, <code>system_complete(p)</code> has the same semantics as <code><a href="#absolute">absolute</a>(p, <a href="#current_path">current_path</a>())</code>.</p>
  <p><a name="windows_effects">For <i>Windows</i></a>, <code>system_complete(p)</code> has the
  same semantics as <code><a href="#absolute">absolute</a>(p, <a href="#current_path">current_path</a>())</code> if <code>p.is_absolute() || !p.has_root_name()</code> or <code>p</code> and <code>base</code> have the same <code>root_name()</code>.
  Otherwise it acts like <code><a href="#absolute">absolute</a>(p, kinky)</code>, where <code>kinky</code> is the current directory for the <code>p.root_name()</code> drive. This will
  be the current directory of that drive the last time it was set, and thus may
  be <b>residue left over from a prior program</b> run by the command
  processor! Although these semantics are often useful, they are also very
  error-prone.</p>
</blockquote>
<pre>path <a name="temp_directory_path">temp_directory_path</a>();
path temp_directory_path(system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Returns:</i> A directory path suitable for temporary files under the
  conventions of the operating system. The specifics of how this path is
  determined are implementation defined. An error shall be reported if<code> !exists(p)
  || !is_directory(p)</code>, where <code>p</code> is the path to be returned.</p>
  <p>ISO/IEC 9945: The path supplied by the first environment variable found in the
  list TMPDIR, TMP, TEMP, TEMPDIR. If none of these are found, <code>&quot;/tmp&quot;</code>,
  or, if macro <code>__ANDROID__ </code>is defined, <code>&quot;/data/local/tmp&quot;</code>.</p>
  <p><i>Windows:</i> The path reported by the <i>Windows</i> <code>GetTempPath</code> API function.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
  <p>[<i>Note: </i>The <code>temp_directory_path()</code> name was chosen to emphasize that the return is a
  path, not just a single directory name.&nbsp; <i>—end note</i>]</p>
</blockquote>
<pre>path <a name="unique_path">unique_path</a>(const path&amp; model=&quot;%%%%-%%%%-%%%%-%%%%&quot;);
path unique_path(const path&amp; model, system::error_code&amp; ec);</pre>
<blockquote>
  <p>The <code>unique_path</code> function generates a path name suitable for
  creating temporary files, including directories. The name is based
  on a model that uses the percent sign character to specify replacement by a
  random hexadecimal digit. [<i>Note:</i> The more bits of randomness in the
  generated path name, the less likelihood of prior existence or being guessed.
  Each replacement hexadecimal digit in the model adds four bits of randomness.
  The default model thus provides 64 bits of randomness. This is sufficient for
  most applications. <i>—end note</i>]</p>
  <p><i>Returns:</i> A path identical to <code>model</code>, except that each
  occurrence of a percent sign character is replaced by a random hexadecimal
  digit character in the range 0-9, a-f.</p>
  <p><i>Throws:</i> As specified in <a href="#Error-reporting">Error reporting</a>.</p>
  <p><i>Remarks:</i> Implementations are encouraged to obtain the required
  randomness via a <a href="http://en.wikipedia.org/wiki/Cryptographically_secure_pseudorandom_number_generator">cryptographically secure pseudo-random number generator</a>, such as one
  provided by the operating system. [<i>Note</i>: Such generators may block
  until sufficient entropy develops. <i>—end note</i>]</p>
</blockquote>
<pre>path <a name="weakly_canonical">weakly_canonical</a>(const path&amp; p, const path&amp; base=current_path());
path weakly_canonical(const path&amp; p, system::error_code&amp; ec);
path weakly_canonical(const path&amp; p, const path&amp; base, system::error_code&amp; ec);</pre>
<blockquote>
  <p><i>Overview:</i> Returns <code>p</code> with symlinks resolved and the
  result normalized.</p>
  <p><i>Effects:</i> Let <code>head</code> be the path composed of the leading
  elements of <code>p</code> that exist and <code>tail</code> &mdash; from the rest of <code>p</code>.
  Where not specified, <code>base</code> is obtained from <code>current_path()</code> or <code>current_path(ec)</code>
  Calls <a href="#canonical"><code>canonical(head, base)</code></a> or <a href="#canonical"><code>canonical(head, base, ec)</code></a>,
  appends <code>tail</code> to the returned path using <code>operator/</code>.
  The result is then normalized and returned.</p>
  <p><i>Postcondition:</i> The returned path is in
  <a href="#normal-form">
  normal form</a>.</p>
  <p><i>Remarks:</i> Uses <code>operator/=</code> to compose the returned path.
  Uses the <code>status</code> function to determine existence.</p>
  <p><i>Remarks:</i> Implementations are encouraged to avoid unnecessary
  normalization such as when <code>canonical</code> has already been called on
  the entirety of <code>p</code>.</p>
  <p><i>Throws:</i>&nbsp; As specified in Error reporting.</p>
</blockquote>
<hr>

<!-- generate-section-numbers=false -->


<h3><a name="File-streams">File streams</a> -
<a href="../../../boost/filesystem/fstream.hpp">&lt;boost/filesystem/fstream.hpp&gt;</a></h3>
<p>Replacements are provided for the file stream classes from the C++ standard
library's <code>&lt;fstream&gt;</code> header. These replacement classes
publicly inherit from the standard library classes. In the Boost.Filesystem
version, constructors and open functions take <code>const path&amp;</code> arguments
instead of <code>
const char*</code> arguments. There are no other differences in syntax or
semantics.</p>
<pre>namespace boost
{
  namespace filesystem
  {
    template &lt; class charT, class traits = std::char_traits&lt;charT&gt; &gt;
    class basic_filebuf : public std::basic_filebuf&lt;charT,traits&gt;
    {
    public:
      basic_filebuf&lt;charT,traits&gt;*
        open(const path&amp; p, std::ios_base::openmode mode);
    };

    template &lt; class charT, class traits = std::char_traits&lt;charT&gt; &gt;
    class basic_ifstream : public std::basic_ifstream&lt;charT,traits&gt;
    {
    public:
      explicit basic_ifstream(const path&amp; p, std::ios_base::openmode mode=std::ios_base::in)
      void open(const path&amp; p, std::ios_base::openmode mode=std::ios_base::in);
    };

    template &lt; class charT, class traits = std::char_traits&lt;charT&gt; &gt;
    class basic_ofstream : public std::basic_ofstream&lt;charT,traits&gt;
    {
    public:
      explicit basic_ofstream(const path&amp; p, std::ios_base::openmode mode=std::ios_base::out);
      void open(const path&amp; p, std::ios_base::openmode mode=std::ios_base::out);
    };

    template &lt; class charT, class traits = std::char_traits&lt;charT&gt; &gt;
    class basic_fstream : public std::basic_fstream&lt;charT,traits&gt;
    {
    public:
      explicit basic_fstream(const path&amp; p,
        std::ios_base::openmode mode=std::ios_base::in | std::ios_base::out);
      void open(const path&amp; p,
        std::ios_base::openmode mode=std::ios_base::in | std::ios_base::out);
    };

    typedef basic_filebuf&lt;char&gt; filebuf;
    typedef basic_ifstream&lt;char&gt; ifstream;
    typedef basic_ofstream&lt;char&gt; ofstream;
    typedef basic_fstream&lt;char&gt; fstream;

    typedef basic_filebuf&lt;wchar_t&gt; wfilebuf;
    typedef basic_ifstream&lt;wchar_t&gt; wifstream;
    typedef basic_fstream&lt;wchar_t&gt; wfstream;
    typedef basic_ofstream&lt;wchar_t&gt; wofstream;

  }  // namespace filesystem
}  // namespace boost</pre>



<h2><a name="path-decomposition-table">Path decomposition table</a></h2>
<p>The table is generated by a program compiled with the Boost implementation.</p>
<p>Shaded entries indicate cases where ISO/IEC 9945 (POSIX) and Windows implementations yield different results. The top value is the
ISO/IEC 9945 result and the bottom value is the Windows result. <br>
<table border="1" cellspacing="0" cellpadding="5">
<p>
<tr><td><b>Constructor<br>argument</b></td>
<td><b>Iteration<br>over<br>Elements</b></td>
<td><b><code>string()</code></b></td>
<td><b><code>generic_<br>string()</code></b></td>
<td><b><code>root_<br>path()</code></b></td>
<td><b><code>root_<br>name()</code></b></td>
<td><b><code>root_<br>directory()</code></b></td>
<td><b><code>relative_<br>path()</code></b></td>
<td><b><code>parent_<br>path()</code></b></td>
<td><b><code>filename()</code></b></td>
</tr>
<tr>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
</tr>
<tr>
<td><code>.</code></td>
<td><code>.</code></td>
<td><code>.</code></td>
<td><code>.</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>.</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>.</code></td>
</tr>
<tr>
<td><code>..</code></td>
<td><code>..</code></td>
<td><code>..</code></td>
<td><code>..</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>..</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>..</code></td>
</tr>
<tr>
<td><code>foo</code></td>
<td><code>foo</code></td>
<td><code>foo</code></td>
<td><code>foo</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>foo</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>foo</code></td>
</tr>
<tr>
<td><code>/</code></td>
<td><code>/</code></td>
<td><code>/</code></td>
<td><code>/</code></td>
<td><code>/</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>/</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>/</code></td>
</tr>
<tr>
<td><code>/foo</code></td>
<td><code>/,foo</code></td>
<td><code>/foo</code></td>
<td><code>/foo</code></td>
<td><code>/</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>/</code></td>
<td><code>foo</code></td>
<td><code>/</code></td>
<td><code>foo</code></td>
</tr>
<tr>
<td><code>foo/</code></td>
<td><code>foo,.</code></td>
<td><code>foo/</code></td>
<td><code>foo/</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>foo/</code></td>
<td><code>foo</code></td>
<td><code>.</code></td>
</tr>
<tr>
<td><code>/foo/</code></td>
<td><code>/,foo,.</code></td>
<td><code>/foo/</code></td>
<td><code>/foo/</code></td>
<td><code>/</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>/</code></td>
<td><code>foo/</code></td>
<td><code>/foo</code></td>
<td><code>.</code></td>
</tr>
<tr>
<td><code>foo/bar</code></td>
<td><code>foo,bar</code></td>
<td><code>foo/bar</code></td>
<td><code>foo/bar</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>foo/bar</code></td>
<td><code>foo</code></td>
<td><code>bar</code></td>
</tr>
<tr>
<td><code>/foo/bar</code></td>
<td><code>/,foo,bar</code></td>
<td><code>/foo/bar</code></td>
<td><code>/foo/bar</code></td>
<td><code>/</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>/</code></td>
<td><code>foo/bar</code></td>
<td><code>/foo</code></td>
<td><code>bar</code></td>
</tr>
<tr>
<td><code>//net</code></td>
<td><code>//net</code></td>
<td><code>//net</code></td>
<td><code>//net</code></td>
<td><code>//net</code></td>
<td><code>//net</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>//net</code></td>
</tr>
<tr>
<td><code>//net/foo</code></td>
<td><code>//net,/,foo</code></td>
<td><code>//net/foo</code></td>
<td><code>//net/foo</code></td>
<td><code>//net/</code></td>
<td><code>//net</code></td>
<td><code>/</code></td>
<td><code>foo</code></td>
<td><code>//net/</code></td>
<td><code>foo</code></td>
</tr>
<tr>
<td><code>///foo///</code></td>
<td><code>/,foo,.</code></td>
<td><code>///foo///</code></td>
<td><code>///foo///</code></td>
<td><code>/</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>/</code></td>
<td><code>foo///</code></td>
<td><code>///foo</code></td>
<td><code>.</code></td>
</tr>
<tr>
<td><code>///foo///bar</code></td>
<td><code>/,foo,bar</code></td>
<td><code>///foo///bar</code></td>
<td><code>///foo///bar</code></td>
<td><code>/</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>/</code></td>
<td><code>foo///bar</code></td>
<td><code>///foo</code></td>
<td><code>bar</code></td>
</tr>
<tr>
<td><code>/.</code></td>
<td><code>/,.</code></td>
<td><code>/.</code></td>
<td><code>/.</code></td>
<td><code>/</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>/</code></td>
<td><code>.</code></td>
<td><code>/</code></td>
<td><code>.</code></td>
</tr>
<tr>
<td><code>./</code></td>
<td><code>.,.</code></td>
<td><code>./</code></td>
<td><code>./</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>./</code></td>
<td><code>.</code></td>
<td><code>.</code></td>
</tr>
<tr>
<td><code>/..</code></td>
<td><code>/,..</code></td>
<td><code>/..</code></td>
<td><code>/..</code></td>
<td><code>/</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>/</code></td>
<td><code>..</code></td>
<td><code>/</code></td>
<td><code>..</code></td>
</tr>
<tr>
<td><code>../</code></td>
<td><code>..,.</code></td>
<td><code>../</code></td>
<td><code>../</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>../</code></td>
<td><code>..</code></td>
<td><code>.</code></td>
</tr>
<tr>
<td><code>foo/.</code></td>
<td><code>foo,.</code></td>
<td><code>foo/.</code></td>
<td><code>foo/.</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>foo/.</code></td>
<td><code>foo</code></td>
<td><code>.</code></td>
</tr>
<tr>
<td><code>foo/..</code></td>
<td><code>foo,..</code></td>
<td><code>foo/..</code></td>
<td><code>foo/..</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>foo/..</code></td>
<td><code>foo</code></td>
<td><code>..</code></td>
</tr>
<tr>
<td><code>foo/./</code></td>
<td><code>foo,.,.</code></td>
<td><code>foo/./</code></td>
<td><code>foo/./</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>foo/./</code></td>
<td><code>foo/.</code></td>
<td><code>.</code></td>
</tr>
<tr>
<td><code>foo/./bar</code></td>
<td><code>foo,.,bar</code></td>
<td><code>foo/./bar</code></td>
<td><code>foo/./bar</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>foo/./bar</code></td>
<td><code>foo/.</code></td>
<td><code>bar</code></td>
</tr>
<tr>
<td><code>foo/..</code></td>
<td><code>foo,..</code></td>
<td><code>foo/..</code></td>
<td><code>foo/..</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>foo/..</code></td>
<td><code>foo</code></td>
<td><code>..</code></td>
</tr>
<tr>
<td><code>foo/../</code></td>
<td><code>foo,..,.</code></td>
<td><code>foo/../</code></td>
<td><code>foo/../</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>foo/../</code></td>
<td><code>foo/..</code></td>
<td><code>.</code></td>
</tr>
<tr>
<td><code>foo/../bar</code></td>
<td><code>foo,..,bar</code></td>
<td><code>foo/../bar</code></td>
<td><code>foo/../bar</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>foo/../bar</code></td>
<td><code>foo/..</code></td>
<td><code>bar</code></td>
</tr>
<tr>
<td><code>c:</code></td>
<td><code>c:</code></td>
<td><code>c:</code></td>
<td><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>c:</code><br><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>c:</code></td>
</tr>
<tr>
<td><code>c:/</code></td>
<td><code>c:,.</code><br><code>c:,/</code></td>
<td><code>c:/</code></td>
<td><code>c:/</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:/</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>/</code></td>
<td><code>c:/</code><br><font size="-1"><i>empty</i></font></td>
<td><code>c:</code></td>
<td><code>.</code><br><code>/</code></td>
</tr>
<tr>
<td><code>c:foo</code></td>
<td><code>c:foo</code><br><code>c:,foo</code></td>
<td><code>c:foo</code></td>
<td><code>c:foo</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>c:foo</code><br><code>foo</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><code>c:foo</code><br><code>foo</code></td>
</tr>
<tr>
<td><code>c:/foo</code></td>
<td><code>c:,foo</code><br><code>c:,/,foo</code></td>
<td><code>c:/foo</code></td>
<td><code>c:/foo</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:/</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>/</code></td>
<td><code>c:/foo</code><br><code>foo</code></td>
<td><code>c:</code><br><code>c:/</code></td>
<td><code>foo</code></td>
</tr>
<tr>
<td><code>c:foo/</code></td>
<td><code>c:foo,.</code><br><code>c:,foo,.</code></td>
<td><code>c:foo/</code></td>
<td><code>c:foo/</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>c:foo/</code><br><code>foo/</code></td>
<td><code>c:foo</code></td>
<td><code>.</code></td>
</tr>
<tr>
<td><code>c:/foo/</code></td>
<td><code>c:,foo,.</code><br><code>c:,/,foo,.</code></td>
<td><code>c:/foo/</code></td>
<td><code>c:/foo/</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:/</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>/</code></td>
<td><code>c:/foo/</code><br><code>foo/</code></td>
<td><code>c:/foo</code></td>
<td><code>.</code></td>
</tr>
<tr>
<td><code>c:/foo/bar</code></td>
<td><code>c:,foo,bar</code><br><code>c:,/,foo,bar</code></td>
<td><code>c:/foo/bar</code></td>
<td><code>c:/foo/bar</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:/</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>/</code></td>
<td><code>c:/foo/bar</code><br><code>foo/bar</code></td>
<td><code>c:/foo</code></td>
<td><code>bar</code></td>
</tr>
<tr>
<td><code>prn:</code></td>
<td><code>prn:</code></td>
<td><code>prn:</code></td>
<td><code>prn:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>prn:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>prn:</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>prn:</code><br><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>prn:</code></td>
</tr>
<tr>
<td><code>c:\</code></td>
<td><code>c:\</code><br><code>c:,/</code></td>
<td><code>c:\</code></td>
<td><code>c:\</code><br><code>c:/</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:\</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>\</code></td>
<td><code>c:\</code><br><font size="-1"><i>empty</i></font></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><code>c:\</code><br><code>\</code></td>
</tr>
<tr>
<td><code>c:foo</code></td>
<td><code>c:foo</code><br><code>c:,foo</code></td>
<td><code>c:foo</code></td>
<td><code>c:foo</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>c:foo</code><br><code>foo</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><code>c:foo</code><br><code>foo</code></td>
</tr>
<tr>
<td><code>c:\foo</code></td>
<td><code>c:\foo</code><br><code>c:,/,foo</code></td>
<td><code>c:\foo</code></td>
<td><code>c:\foo</code><br><code>c:/foo</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:\</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>\</code></td>
<td><code>c:\foo</code><br><code>foo</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:\</code></td>
<td><code>c:\foo</code><br><code>foo</code></td>
</tr>
<tr>
<td><code>c:foo\</code></td>
<td><code>c:foo\</code><br><code>c:,foo,.</code></td>
<td><code>c:foo\</code></td>
<td><code>c:foo\</code><br><code>c:foo/</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font></td>
<td><code>c:foo\</code><br><code>foo\</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:foo</code></td>
<td><code>c:foo\</code><br><code>.</code></td>
</tr>
<tr>
<td><code>c:\foo\</code></td>
<td><code>c:\foo\</code><br><code>c:,/,foo,.</code></td>
<td><code>c:\foo\</code></td>
<td><code>c:\foo\</code><br><code>c:/foo/</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:\</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>\</code></td>
<td><code>c:\foo\</code><br><code>foo\</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:\foo</code></td>
<td><code>c:\foo\</code><br><code>.</code></td>
</tr>
<tr>
<td><code>c:\foo/</code></td>
<td><code>c:\foo,.</code><br><code>c:,/,foo,.</code></td>
<td><code>c:\foo/</code></td>
<td><code>c:\foo/</code><br><code>c:/foo/</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:\</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>\</code></td>
<td><code>c:\foo/</code><br><code>foo/</code></td>
<td><code>c:\foo</code></td>
<td><code>.</code></td>
</tr>
<tr>
<td><code>c:/foo\bar</code></td>
<td><code>c:,foo\bar</code><br><code>c:,/,foo,bar</code></td>
<td><code>c:/foo\bar</code></td>
<td><code>c:/foo\bar</code><br><code>c:/foo/bar</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:/</code></td>
<td><font size="-1"><i>empty</i></font><br><code>c:</code></td>
<td><font size="-1"><i>empty</i></font><br><code>/</code></td>
<td><code>c:/foo\bar</code><br><code>foo\bar</code></td>
<td><code>c:</code><br><code>c:/foo</code></td>
<td><code>foo\bar</code><br><code>bar</code></td>
</tr>
</table>

<h2><a name="windows-path-prefixes"></a>Long paths on Windows and the
<b>\\?\</b> namespace prefix</h2>
<p>The Microsoft Windows &quot;Maximum Path Length Limitation&quot; specifies:</p>
<blockquote>
<p>In the Windows API (with some exceptions ...), the maximum length for a path
is <code>MAX_PATH</code>, which is defined as 260 characters.</p>
<p>The Windows API has many functions that also have Unicode versions to permit
an extended-length path for a maximum total path length of 32,767 characters.
... To specify an extended-length path, use the &quot;\\?\&quot; prefix. For
example, &quot;\\?\D:\<em>very long path</em>&quot;.&nbsp;<i>[C++ string literals
require backslashes be doubled, of course.]</i></p>
</blockquote>
<p>Because most Boost.Filesystem operational functions just pass the contents of
a class path object to the Windows API, they do work with the extended-length
prefixes. But some won't work, because to the limitations imposed by Windows.</p>
<p>Effectively, the &quot;\\?\&quot; prefix informs the underlying Windows API
that the path identifies an object in the Win32 filesystem namespace and disables
most of the internal path processing, including normalization, by the Windows API.
While it lifts the length limitation, this imposes a number of other restrictions on
the acceptable paths that are listed in the section below. There are a number of
other prefixes that have different special meanings, such as &quot;\\.\&quot; (a local
device namespace prefix) and &quot;\??\&quot; (NT object namespace prefix). Note that
UNC paths can be extended with the prefixes by referring to a special &quot;UNC&quot;
device. For example, &quot;\\server\share&quot; can be extended as &quot;\\?\UNC\server\share&quot;.</p>
<p>In terms of Boost.Filesystem, the namespace prefixes are considered as parts of the path's
root name. For example, the root name of &quot;\\?\c:\foo&quot; is &quot;\\?\c:&quot;.
This interpretation is chosen to allow root paths to be iterable, that is you can list
the contents of the &quot;\\?\c:\&quot; directory, provided that drive C: exists and you have
the necessary permissions. <i>[Note: You can't list the contents of &quot;\\?\&quot;
as such path does not exist.]</i></p>

<h3>Cautions for paths with namespace prefixes</h3>
<ul>
  <li>Individual components of a path are still are limited to whatever is
  supported for the particular filesystem, commonly 255 characters.</li>
  <li>Only backslashes are acceptable as directory separators. Slashes are
  not treated as separators.</li>
  <li>All paths must be absolute and must not contain &quot;.&quot; or &quot;..&quot; components.</li>
  <li>Once an absolute path grows beyond 260 characters, it is essentially
  poisoned and all operations must use extended-length prefixes. So even a
  simple operation like <code>create_directory(&quot;a&quot;)</code> will fail if the
  absolute path of the resulting directory would exceed 260 characters.</li>
  <li>Certain Boost.Filesystem functions that decompose their argument path and
  then work on individual relative directories or files will not work properly
  with namespace prefix paths.</li>
</ul>

<h2><a name="Acknowledgements">Acknowledgements</a></h2>
<p>This Filesystem Library is dedicated to my wife, Sonda, who provided the
support necessary to see both a trial implementation and the proposal itself
through to completion. She gave me the strength to continue after a difficult
year of cancer treatment in the middle of it all.</p>
<p>Many people contributed technical comments, ideas, and suggestions to the
Boost Filesystem Library. See <a href="http://www.boost.org/libs/filesystem/doc/index.htm#Acknowledgements">http://www.boost.org/libs/filesystem/doc/index.htm#Acknowledgements</a>.</p>
<p>Dietmar Kühl contributed the original Boost Filesystem Library <code>directory_iterator</code> design. Peter Dimov, Walter Landry, Rob Stewart, and Thomas
Witt were particularly helpful in refining the library.</p>
<p>The create_directories, extension, basename, and replace_extension functions
were developed by Vladimir Prus. The temp_directory_path function was
contributed by Jeff Flinn. David Svoboda suggested the canonical function and
provided pseudo-code.</p>
<p>Howard Hinnant and John Maddock reviewed a draft of the version 2 proposal, and
identified a number of mistakes or weaknesses, resulting in a more polished
final document.</p>
<p>Peter Dimov suggested a single class path, with member templates to adapt to
multiple string types. His idea became the basis for the version 3 path design.</p>
<h2><a name="References">References</a></h2>
<table border="0" cellpadding="5" cellspacing="0" style="border-collapse: collapse" bordercolor="#111111" width="100%">
  <tr>
    <td width="16%" valign="top">[<a name="ISO_POSIX">ISO/IEC 9945</a>]</td>
    <td width="84%">ISO/IEC 9945:2003, IEEE&nbsp;Std&nbsp;1003.1-2001, and The Open Group
    Base Specifications, Issue 6. Also known as The Single UNIX&reg;
    Specification, Version 3. Available from each of the organizations involved
    in its creation. For example, read online or download from <a href="http://www.unix.org/single_unix_specification/">www.unix.org/single_unix_specification/</a>. The ISO JTC1/SC22/WG15 -
    POSIX homepage is <a href="http://www.open-std.org/jtc1/sc22/WG15/">www.open-std.org/jtc1/sc22/WG15/</a></td>
  </tr>
  <tr>
    <td width="16%" valign="top">[Abrahams]</td>
    <td width="84%">Dave Abrahams, Error and Exception Handling, <a href="http://www.boost.org/more/error_handling.html">www.boost.org/more/error_handling.html</a></td>
  </tr>
</table>
<hr>
<p>&copy; Copyright Beman Dawes, 2002, 2006, 2007, 2009, 2010, 2011</p>
<p>&copy; Copyright Andrey Semashev, 2019-2021</p>
<p>Distributed under the Boost Software License, Version 1.0. See
<a href="http://www.boost.org/LICENSE_1_0.txt">www.boost.org/LICENSE_1_0.txt</a></p>
</body>
</html>
