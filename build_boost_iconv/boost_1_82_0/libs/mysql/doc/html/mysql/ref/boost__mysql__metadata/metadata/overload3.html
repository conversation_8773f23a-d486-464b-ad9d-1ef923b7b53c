<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>metadata::metadata (3 of 3 overloads)</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Chapter 1. Boost.MySQL">
<link rel="up" href="../metadata.html" title="metadata::metadata">
<link rel="prev" href="overload2.html" title="metadata::metadata (2 of 3 overloads)">
<link rel="next" href="../operator_eq_.html" title="metadata::operator=">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload2.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../metadata.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../operator_eq_.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="mysql.ref.boost__mysql__metadata.metadata.overload3"></a><a class="link" href="overload3.html" title="metadata::metadata (3 of 3 overloads)">metadata::metadata
          (3 of 3 overloads)</a>
</h6></div></div></div>
<p>
            Copy constructor.
          </p>
<h6>
<a name="mysql.ref.boost__mysql__metadata.metadata.overload3.h0"></a>
            <span class="phrase"><a name="mysql.ref.boost__mysql__metadata.metadata.overload3.synopsis"></a></span><a class="link" href="overload3.html#mysql.ref.boost__mysql__metadata.metadata.overload3.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="identifier">metadata</span><span class="special">(</span>
    <span class="keyword">const</span> <span class="identifier">metadata</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">);</span>
</pre>
<h6>
<a name="mysql.ref.boost__mysql__metadata.metadata.overload3.h1"></a>
            <span class="phrase"><a name="mysql.ref.boost__mysql__metadata.metadata.overload3.exception_safety"></a></span><a class="link" href="overload3.html#mysql.ref.boost__mysql__metadata.metadata.overload3.exception_safety">Exception
            safety</a>
          </h6>
<p>
            Strong guarantee. Internal allocations may throw.
          </p>
</div>
<div class="copyright-footer">Copyright © 2019-2023 Ruben Perez<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload2.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../metadata.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../operator_eq_.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
