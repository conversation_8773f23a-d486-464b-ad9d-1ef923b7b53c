<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>rows::rows (1 of 4 overloads)</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Chapter 1. Boost.MySQL">
<link rel="up" href="../rows.html" title="rows::rows">
<link rel="prev" href="../rows.html" title="rows::rows">
<link rel="next" href="overload2.html" title="rows::rows (2 of 4 overloads)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../rows.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../rows.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="mysql.ref.boost__mysql__rows.rows.overload1"></a><a class="link" href="overload1.html" title="rows::rows (1 of 4 overloads)">rows::rows
          (1 of 4 overloads)</a>
</h6></div></div></div>
<p>
            Construct an empty <code class="computeroutput"><span class="identifier">rows</span></code>
            object.
          </p>
<h6>
<a name="mysql.ref.boost__mysql__rows.rows.overload1.h0"></a>
            <span class="phrase"><a name="mysql.ref.boost__mysql__rows.rows.overload1.synopsis"></a></span><a class="link" href="overload1.html#mysql.ref.boost__mysql__rows.rows.overload1.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="identifier">rows</span><span class="special">();</span>
</pre>
<h6>
<a name="mysql.ref.boost__mysql__rows.rows.overload1.h1"></a>
            <span class="phrase"><a name="mysql.ref.boost__mysql__rows.rows.overload1.exception_safety"></a></span><a class="link" href="overload1.html#mysql.ref.boost__mysql__rows.rows.overload1.exception_safety">Exception
            safety</a>
          </h6>
<p>
            No-throw guarantee.
          </p>
</div>
<div class="copyright-footer">Copyright © 2019-2023 Ruben Perez<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../rows.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../rows.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
