<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>connection::quit (2 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Chapter 1. Boost.MySQL">
<link rel="up" href="../quit.html" title="connection::quit">
<link rel="prev" href="overload1.html" title="connection::quit (1 of 2 overloads)">
<link rel="next" href="../async_quit.html" title="connection::async_quit">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../quit.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../async_quit.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="mysql.ref.boost__mysql__connection.quit.overload2"></a><a class="link" href="overload2.html" title="connection::quit (2 of 2 overloads)">connection::quit
          (2 of 2 overloads)</a>
</h6></div></div></div>
<p>
            Notifies the MySQL server that the client wants to end the session and
            shutdowns SSL.
          </p>
<h6>
<a name="mysql.ref.boost__mysql__connection.quit.overload2.h0"></a>
            <span class="phrase"><a name="mysql.ref.boost__mysql__connection.quit.overload2.synopsis"></a></span><a class="link" href="overload2.html#mysql.ref.boost__mysql__connection.quit.overload2.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="keyword">void</span>
<span class="identifier">quit</span><span class="special">();</span>
</pre>
<h6>
<a name="mysql.ref.boost__mysql__connection.quit.overload2.h1"></a>
            <span class="phrase"><a name="mysql.ref.boost__mysql__connection.quit.overload2.description"></a></span><a class="link" href="overload2.html#mysql.ref.boost__mysql__connection.quit.overload2.description">Description</a>
          </h6>
<p>
            Sends a quit request to the MySQL server. If the connection is using
            SSL, this function will also perform the SSL shutdown. You should close
            the underlying physical connection after calling this function.
          </p>
<p>
            If the <code class="computeroutput"><span class="identifier">Stream</span></code> template
            parameter fulfills the <code class="computeroutput"><span class="identifier">SocketConnection</span></code>
            requirements, use <a class="link" href="../close.html" title="connection::close"><code class="computeroutput"><span class="identifier">connection</span><span class="special">::</span><span class="identifier">close</span></code></a> instead of this function,
            as it also takes care of closing the underlying stream.
          </p>
</div>
<div class="copyright-footer">Copyright © 2019-2023 Ruben Perez<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../quit.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../async_quit.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
