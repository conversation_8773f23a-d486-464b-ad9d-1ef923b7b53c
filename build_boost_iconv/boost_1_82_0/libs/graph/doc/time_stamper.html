<HTML>
<!--
     Copyright (c) <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> 2000

     Distributed under the Boost Software License, Version 1.0.
     (See accompanying file LICENSE_1_0.txt or copy at
     http://www.boost.org/LICENSE_1_0.txt)
  -->
<Head>
<Title>Boost Graph Library: time_stamper</Title>
<BODY BGCOLOR="#ffffff" LINK="#0000ee" TEXT="#000000" VLINK="#551a8b"
        ALINK="#ff0000">
<IMG SRC="../../../boost.png"
     ALT="C++ Boost" width="277" height="86">

<BR Clear>

<H1>
<pre>
time_stamper&lt;TimeMap, TimeT, EventTag&gt;
</pre>
</H1>

This is an <a href="./EventVisitor.html">EventVisitor</a> that can be
used to &quot;stamp&quot; a time at some event-point within an
algorithm. An example of this is recording the discover or finish time
of a vertex during a graph search.

<p>
<tt>time_stamper</tt> can be used with graph algorithms by
wrapping it with the algorithm specific adaptor, such as <a
href="./bfs_visitor.html"><tt>bfs_visitor</tt></a> and <a
href="./dfs_visitor.html"><tt>dfs_visitor</tt></a>. Also, this event
visitor can be combined with other event visitors using
<tt>std::pair</tt> to form an EventVisitorList.

<h3>Example</h3>

The following example shows the usage of the <tt>time_stamper</tt>.


<pre>
  std::vector<default_color_type> color(num_vertices(G));
  std::vector<size_type> dtime(num_vertices(G));
  std::vector<size_type> ftime(num_vertices(G));

  int time = 0;
  boost::breadth_first_search
    (G, vertex(s, G), make_bfs_visitor(
     std::make_pair(stamp_times(dtime.begin(), time, on_discover_vertex()),
                    stamp_times(ftime.begin(), time, on_finish_vertex()))),
     color.begin());
</pre>

<h3>Model of</h3>

<a href="./EventVisitor.html">EventVisitor</a>


<H3>Where Defined</H3>

<P>
<a href="../../../boost/graph/visitors.hpp">
<TT>boost/graph/visitors.hpp</TT></a>

<H3>Template Parameters</H3>

<P>
<TABLE border>
<TR>
<th>Parameter</th><th>Description</th><th>Default</th>
</tr>

<TR><TD><TT>TimeMap</TT></TD>
<TD>
A <a
href="../../property_map/doc/WritablePropertyMap.html">WritablePropertyMap</a>
where the <tt>key_type</tt> is the vertex descriptor type or edge
descriptor of the graph (depending on the kind of event tag) and
where the <tt>TimeT</tt> type is convertible to the
<tt>value_type</tt> of the time property map.
</TD>
<TD>&nbsp;</TD>
</TR>

<TR><TD><TT>TimeT</TT></TD>
<TD>
The type for the time counter which should be convertible to the
<tt>value_type</tt> of the time property map
</TD>
<TD>&nbsp;</TD>
</TR>


<TR><TD><TT>EventTag</TT></TD>
<TD>
The tag to specify when the <tt>time_stamper</tt> should be
applied during the graph algorithm.
</TD>
<TD>&nbsp;</TD>
</TR>

</table>

<H2>Associated Types</H2>

<table border>

<tr>
<th>Type</th><th>Description</th>
</tr>

<tr>
<td><tt>time_stamper::event_filter</tt></td>
<td>
This will be the same type as the template parameter <tt>EventTag</tt>.
</td>
</tr>

</table>

<h3>Member Functions</h3>

<p>

<table border>
<tr>
<th>Member</th><th>Description</th>
</tr>

<tr>
<td><tt>
time_stamper(TimeMap time_pa, TimeT& t);
</tt></td>
<td>
Construct a time stamper object with time property map
<tt>time_pa</tt> and time counter <tt>t</tt>.
</td>
</tr>

<tr>
<td><tt>
template &lt;class X, class Graph&gt;<br>
void operator()(X x, const Graph& g);
</tt></td>
<td>
This increments the time count and &quot;stamps&quot; the time:<br>
<tt>put(time_pa, x, ++t);</tt>
</td>
</tr>

</table>

<h3>Non-Member Functions</h3>

<table border>
<tr>
<th>Function</th><th>Description</th>
</tr>

<tr><td><tt>
template &lt;class TimeMap, class TimeT, class Tag&gt;<br>
time_stamper&lt;TimeMap, TimeT, Tag&gt;<br>
stamp_times(TimeMap pa, TimeT& t, Tag);
</tt></td><td>
A convenient way to create a <tt>time_stamper</tt>.
</td></tr>

</table>

<h3>See Also</h3>

<a href="./visitor_concepts.html">Visitor concepts</a>
<p>
The following are other event visitors: <a
<a href="./distance_recorder.html"><tt>distance_recorder</tt></a>,
<a href="./time_stamper.html"><tt>time_stamper</tt></a>,
and <a href="./property_writer.html"><tt>property_writer</tt></a>.


<br>
<HR>
<TABLE>
<TR valign=top>
<TD nowrap>Copyright &copy; 2000-2001</TD><TD>
<A HREF="http://www.boost.org/people/jeremy_siek.htm">Jeremy Siek</A>,
Indiana University (<A
HREF="mailto:<EMAIL>"><EMAIL></A>)<br>
<A HREF="http://www.boost.org/people/liequan_lee.htm">Lie-Quan Lee</A>, Indiana University (<A HREF="mailto:<EMAIL>"><EMAIL></A>)<br>
<A HREF="https://homes.cs.washington.edu/~al75">Andrew Lumsdaine</A>,
Indiana University (<A
HREF="mailto:<EMAIL>"><EMAIL></A>)
</TD></TR></TABLE>

</BODY>
</HTML>
<!--  LocalWords:  TimeMap TimeT EventTag EventVisitor bfs dfs EventVisitorList
 -->
<!--  LocalWords:  cpp num dtime ftime int WritablePropertyMap map
 -->
<!--  LocalWords:  const Siek Univ Quan Lumsdaine
 -->
