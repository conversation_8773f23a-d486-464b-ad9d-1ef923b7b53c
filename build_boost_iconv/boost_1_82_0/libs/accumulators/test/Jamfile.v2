# (C) Copyright 2005: <PERSON>
# Distributed under the Boost Software License, Version 1.0.
# (See accompanying file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

# bring in rules for testing
import testing ;
import feature ;

feature.feature iterator_debugging
  : on off
  : composite propagated optional
  ;

feature.compose <iterator_debugging>off
  : <define>_HAS_ITERATOR_DEBUGGING=0
  ;

project
    : default-build
      <link>static
    : requirements
      <library>/boost/test//boost_unit_test_framework
      <include>../../..
      <toolset>msvc:<define>_SCL_SECURE_NO_DEPRECATE
      # MSVC's iterator debugging causes some tests to run forever.
      <toolset>msvc:<iterator_debugging>off
      <toolset>intel-win:<iterator_debugging>off
      <toolset>gcc:<cxxflags>"-Wno-deprecated-declarations"
      <toolset>clang:<cxxflags>"-Wno-deprecated-declarations"
      <toolset>intel-linux:<cxxflags>"-Wno-deprecated-declarations"
      <library>/boost/serialization
    ;

alias accumulators_regular_tests
    : [ run count.cpp ]
      [ run covariance.cpp ]
      [ run droppable.cpp ]
      [ run error_of.cpp ]
      [ run extended_p_square.cpp : : : <toolset>msvc-14.2,<variant>release,<cxxstd>14:<build>no ]
      [ run extended_p_square_quantile.cpp ]
      [ run external_accumulator.cpp ]
      [ run external_weights.cpp ]
      [ run kurtosis.cpp ]
      [ run max.cpp ]
      [ run mean.cpp ]
      [ run median.cpp ]
      [ run min.cpp ]
      [ run moment.cpp ]
      [ run p_square_cumul_dist.cpp ]
      [ run p_square_quantile.cpp ]
      [ run reference.cpp ]
      [ run rolling_count.cpp ]
      [ run rolling_sum.cpp ]
      [ run rolling_mean.cpp ]
      [ run rolling_variance.cpp ]
      [ run rolling_moment.cpp ]
      [ run skewness.cpp ]
      [ run sum.cpp ]
      [ run sum_kahan.cpp ]
      [ run tail.cpp ]
      [ run tail_mean.cpp ]
      [ run tail_quantile.cpp ]
      [ run variance.cpp ]
      [ run vector.cpp ]
      [ run weighted_covariance.cpp ]
      [ run weighted_extended_p_square.cpp : : : <toolset>msvc-14.2,<variant>release,<address-model>32,<cxxstd>14:<build>no ]
      [ run weighted_kurtosis.cpp ]
      [ run weighted_mean.cpp ]
      [ run weighted_median.cpp ]
      [ run weighted_moment.cpp ]
      [ run weighted_p_square_cumul_dist.cpp ]
      [ run weighted_p_square_quantile.cpp ]
      [ run weighted_skewness.cpp ]
      [ run weighted_sum.cpp ]
      [ run weighted_sum_kahan.cpp ]
      [ run weighted_variance.cpp ]
    ;

alias accumulators_test_valarray
    :
    : <target-os>linux
      <toolset>clang
# TODO: Find the correct attribute that detects libc++.
    ;

alias accumulators_test_valarray
    :
    : <target-os>darwin
    ;

alias accumulators_test_valarray
    : [ run valarray.cpp ]
    ;

alias accumulators_test_tail_variate_means
    :
    : <toolset>msvc
      <toolset-msvc:version>14.0
    ;

alias accumulators_test_tail_variate_means
    : [ run tail_variate_means.cpp ]
    ;

alias accumulators_tests_pot_quantile_and_weighted_tail
    :
    : <target-os>linux
      <toolset>gcc
      <toolset-gcc:version>4.4.7
    ;

alias accumulators_tests_pot_quantile_and_weighted_tail
    : [ run pot_quantile.cpp ]
      [ run weighted_pot_quantile.cpp ]
      [ run weighted_tail_mean.cpp ]
      [ run weighted_tail_quantile.cpp ]
    ;

alias accumulators_test_weighted_tail_variate_means
    :
    : <target-os>linux
      <toolset>gcc
      <toolset-gcc:version>4.4.7
    ;

alias accumulators_test_weighted_tail_variate_means
    :
    : <toolset>msvc
      <toolset-msvc:version>14.0
    ;

alias accumulators_test_weighted_tail_variate_means
    : [ run weighted_tail_variate_means.cpp ]
    ;

test-suite "accumulators"
    : accumulators_regular_tests
      accumulators_test_valarray
      accumulators_test_tail_variate_means
      accumulators_tests_pot_quantile_and_weighted_tail
      accumulators_test_weighted_tail_variate_means
    ;
