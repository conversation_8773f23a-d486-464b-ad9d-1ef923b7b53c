<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Boost.Config</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="index.html" title="Boost.Config">
<link rel="next" href="boost_config/boost_macro_reference.html" title="Boost Macro Reference">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav"><a accesskey="n" href="boost_config/boost_macro_reference.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a></div>
<div class="article">
<div class="titlepage">
<div>
<div><h2 class="title">
<a name="config"></a>Boost.Config</h2></div>
<div><div class="authorgroup"><div class="author"><h3 class="author">
<span class="firstname">Vesa Karvonen, John Maddock</span> <span class="surname">Beman Dawes</span>
</h3></div></div></div>
<div><p class="copyright">Copyright © 2001-2007 Beman Dawes, Vesa Karvonen, John
      Maddock</p></div>
<div><div class="legalnotice">
<a name="config.legal"></a><p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></div>
</div>
<hr>
</div>
<div class="toc">
<p><b>Table of Contents</b></p>
<dl class="toc">
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform">Configuring
    Boost for Your Platform</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.using_the_default_boost_configuration">Using
      the default boost configuration</a></span></dt>
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.the__boost_config_hpp__header">The
      &lt;boost/config.hpp&gt; header</a></span></dt>
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.using_the_configure_script">Using
      the configure script</a></span></dt>
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.user_settable_options">User
      settable options</a></span></dt>
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.advanced_configuration_usage">Advanced
      configuration usage</a></span></dt>
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.testing_the_boost_configuration">Testing
      the boost configuration</a></span></dt>
</dl></dd>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html">Boost Macro Reference</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.broad_c___standard_level_checks">Broad
      C++ Standard Level Checks</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__03_defects">Macros
      that describe C++03 defects</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_optional_features">Macros
      that describe optional features</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported">Macros
      that describe C++11 features not supported</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_allow_use_of_c__11_features_with_c__03_compilers">Macros
      that allow use of C++11 features with C++03 compilers</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__14_features_not_supported">Macros
      that describe C++14 features not supported</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_allow_use_of_c__14_features_with_c__11_or_earlier_compilers">Macros
      that allow use of C++14 features with C++11 or earlier compilers</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__17_features_not_supported">Macros
      that describe C++17 features not supported</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_allow_use_of_c__17_features_with_c__14_or_earlier_compilers">Macros
      that allow use of C++17 features with C++14 or earlier compilers</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__20_features_not_supported">Macros
      that describe C++20 features not supported</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_features_that_have_been_removed_from_the_standard_">Macros
      that describe features that have been removed from the standard.</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.boost_helper_macros">Boost
      Helper Macros</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.boost_informational_macros">Boost
      Informational Macros</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.boost_deprecated_macros">Boost
      Deprecated Macros</a></span></dt>
<dt><span class="section"><a href="boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_for_libraries_with_separate_source_code">Macros
      for libraries with separate source code</a></span></dt>
</dl></dd>
<dt><span class="section"><a href="boost_config/build_config.html">Build Time Configuration</a></span></dt>
<dt><span class="section"><a href="boost_config/cstdint.html">Standard Integer Types</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="boost_config/cstdint.html#boost_config.cstdint.overview">Overview</a></span></dt>
<dt><span class="section"><a href="boost_config/cstdint.html#boost_config.cstdint.rationale">Rationale</a></span></dt>
<dt><span class="section"><a href="boost_config/cstdint.html#boost_config.cstdint.ce"><span class="emphasis"><em>Caveat emptor</em></span></a></span></dt>
<dt><span class="section"><a href="boost_config/cstdint.html#boost_config.cstdint.exact_width_integer_types">Exact-width
      integer types</a></span></dt>
<dt><span class="section"><a href="boost_config/cstdint.html#boost_config.cstdint.minimum_width_integer_types">Minimum-width
      integer types</a></span></dt>
<dt><span class="section"><a href="boost_config/cstdint.html#boost_config.cstdint.fastest_minimum_width_integer_types">Fastest
      minimum-width integer types</a></span></dt>
<dt><span class="section"><a href="boost_config/cstdint.html#boost_config.cstdint.greatest_width_integer_types">Greatest-width
      integer types</a></span></dt>
<dt><span class="section"><a href="boost_config/cstdint.html#boost_config.cstdint.integer_constant_macros">Integer
      Constant Macros</a></span></dt>
<dt><span class="section"><a href="boost_config/cstdint.html#boost_config.cstdint.intptr">Integers for Storing Pointers</a></span></dt>
</dl></dd>
<dt><span class="section"><a href="boost_config/guidelines_for_boost_authors.html">Guidelines for
    Boost Authors</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="boost_config/guidelines_for_boost_authors.html#boost_config.guidelines_for_boost_authors.warnings">Disabling
      Compiler Warnings</a></span></dt>
<dt><span class="section"><a href="boost_config/guidelines_for_boost_authors.html#boost_config.guidelines_for_boost_authors.adding_new_defect_macros">Adding
      New Defect Macros</a></span></dt>
<dt><span class="section"><a href="boost_config/guidelines_for_boost_authors.html#boost_config.guidelines_for_boost_authors.adding_new_feature_test_macros">Adding
      New Feature Test Macros</a></span></dt>
<dt><span class="section"><a href="boost_config/guidelines_for_boost_authors.html#boost_config.guidelines_for_boost_authors.modifying_the_boost_configuration_headers">Modifying
      the Boost Configuration Headers</a></span></dt>
</dl></dd>
<dt><span class="section"><a href="boost_config/rationale.html">Rationale</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="boost_config/rationale.html#boost_config.rationale.the_problem">The problem</a></span></dt>
<dt><span class="section"><a href="boost_config/rationale.html#boost_config.rationale.the_solution">The solution</a></span></dt>
</dl></dd>
<dt><span class="section"><a href="boost_config/acknowledgements.html">Acknowledgements</a></span></dt>
</dl>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_config.configuring_boost_for_your_platform"></a><a class="link" href="index.html#boost_config.configuring_boost_for_your_platform" title="Configuring Boost for Your Platform">Configuring
    Boost for Your Platform</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.using_the_default_boost_configuration">Using
      the default boost configuration</a></span></dt>
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.the__boost_config_hpp__header">The
      &lt;boost/config.hpp&gt; header</a></span></dt>
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.using_the_configure_script">Using
      the configure script</a></span></dt>
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.user_settable_options">User
      settable options</a></span></dt>
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.advanced_configuration_usage">Advanced
      configuration usage</a></span></dt>
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.testing_the_boost_configuration">Testing
      the boost configuration</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.configuring_boost_for_your_platform.using_the_default_boost_configuration"></a><a class="link" href="index.html#boost_config.configuring_boost_for_your_platform.using_the_default_boost_configuration" title="Using the default boost configuration">Using
      the default boost configuration</a>
</h3></div></div></div>
<p>
        Boost comes already configured for most common compilers and platforms; you
        should be able to use boost "as is". Since the compiler is configured
        separately from the standard library, the default configuration should work
        even if you replace the compiler's standard library with a third-party standard
        library (like <a href="http://stlport.sourceforge.net" target="_top">STLport</a>).
      </p>
<p>
        Using boost "as is" without trying to reconfigure is the recommended
        method for using boost. You can, however, run the configure script if you
        want to, and there are regression tests provided that allow you to test the
        current boost configuration with your particular compiler setup.
      </p>
<p>
        Boost library users can request support for additional compilers or platforms
        by visiting our <a href="https://github.com/boostorg/config/issues" target="_top">Github</a>
        and submitting a support request.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.configuring_boost_for_your_platform.the__boost_config_hpp__header"></a><a class="link" href="index.html#boost_config.configuring_boost_for_your_platform.the__boost_config_hpp__header" title="The &lt;boost/config.hpp&gt; header">The
      &lt;boost/config.hpp&gt; header</a>
</h3></div></div></div>
<p>
        Boost library implementations access configuration macros via
      </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <a href="../../../../boost/config.hpp" target="_top">&lt;boost/config.hpp&gt;</a>
</pre>
<p>
        While Boost library users are not required to include that file directly,
        or use those configuration macros, such use is acceptable. The configuration
        macros are documented as to their purpose, usage, and limitations which makes
        them usable by both Boost library and user code.
      </p>
<p>
        Boost <a class="link" href="boost_config/boost_macro_reference.html#config_info_macros">informational</a> or <a class="link" href="boost_config/boost_macro_reference.html#config_helpers">helper</a>
        macros are designed for use by Boost users as well as for our own internal
        use. Note however, that the <a class="link" href="boost_config/boost_macro_reference.html#config_features">feature test</a>
        and <a class="link" href="boost_config/boost_macro_reference.html#config_defects">defect test</a> macros were designed
        for internal use by Boost libraries, not user code, so they can change at
        any time (though no gratuitous changes are made to them). Boost library problems
        resulting from changes to the configuration macros are caught by the Boost
        regression tests, so the Boost libraries are updated to account for those
        changes. By contrast, Boost library user code can be adversely affected by
        changes to the macros without warning. The best way to keep abreast of changes
        to the macros used in user code is to monitor the discussions on the Boost
        developers list.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.configuring_boost_for_your_platform.using_the_configure_script"></a><a name="config_config_script"></a><a class="link" href="index.html#boost_config.configuring_boost_for_your_platform.using_the_configure_script" title="Using the configure script">Using
      the configure script</a>
</h3></div></div></div>
<div class="important"><table border="0" summary="Important">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Important]" src="../../../../doc/src/images/important.png"></td>
<th align="left">Important</th>
</tr>
<tr><td align="left" valign="top"><p>
          This configure script only sets up the Boost headers for use with a particular
          compiler. It has no effect on Boost.Build, or how the libraries are built.
        </p></td></tr>
</table></div>
<p>
        If you know that boost is incorrectly configured for your particular setup,
        and you are on a UNIX like platform, then you may want to try and improve
        things by running the boost configure script. From a shell command prompt
        you will need to cd into <span class="emphasis"><em>&lt;boost-root&gt;</em></span><code class="computeroutput"><span class="special">/</span><span class="identifier">libs</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span></code>
        and type:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <code class="computeroutput"><span class="identifier">sh</span> <span class="special">./</span><span class="identifier">configure</span></code>
        </p></blockquote></div>
<p>
        you will see a list of the items being checked as the script works its way
        through the regression tests. Note that the configure script only really
        auto-detects your compiler if it's called g++, c++ or CC. If you are using
        some other compiler you will need to set one or more of the following environment
        variables:
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Variable
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  CXX
                </p>
              </td>
<td>
                <p>
                  The name of the compiler, for example <code class="computeroutput"><span class="identifier">c</span><span class="special">++</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  CXXFLAGS
                </p>
              </td>
<td>
                <p>
                  The compiler flags to use, for example <code class="computeroutput"><span class="special">-</span><span class="identifier">O2</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  LDFLAGS
                </p>
              </td>
<td>
                <p>
                  The linker flags to use, for example <code class="computeroutput"><span class="special">-</span><span class="identifier">L</span><span class="special">/</span><span class="identifier">mypath</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  LIBS
                </p>
              </td>
<td>
                <p>
                  Any libraries to link in, for example <code class="computeroutput"><span class="special">-</span><span class="identifier">lpthread</span></code>.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<p>
        For example to run the configure script with HP aCC, you might use something
        like:
      </p>
<pre class="programlisting"><span class="keyword">export</span> <span class="identifier">CXX</span><span class="special">=</span><span class="string">"aCC"</span>
<span class="keyword">export</span> <span class="identifier">CXXFLAGS</span><span class="special">=</span><span class="string">"-Aa -DAportable -D__HPACC_THREAD_SAFE_RB_TREE \
   -DRWSTD_MULTI_THREAD -DRW_MULTI_THREAD -D_REENTRANT -D_THREAD_SAFE"</span>
<span class="keyword">export</span> <span class="identifier">LDFLAGS</span><span class="special">=</span><span class="string">"-DAportable"</span>
<span class="keyword">export</span> <span class="identifier">LIBS</span><span class="special">=</span><span class="string">"-lpthread"</span>
<span class="identifier">sh</span> <span class="special">./</span><span class="identifier">configure</span>
</pre>
<p>
        However you run the configure script, when it finishes you will find a new
        header -<code class="computeroutput"><span class="identifier">user</span><span class="special">.</span><span class="identifier">hpp</span></code>- located in the <span class="emphasis"><em>&lt;boost-root&gt;</em></span><code class="computeroutput"><span class="special">/</span><span class="identifier">libs</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span></code>
        directory. <span class="bold"><strong>Note that configure does not install this
        header into your boost include path by default</strong></span>. This header contains
        all the options generated by the configure script, plus a header-section
        that contains the user settable options from the default version of <a href="../../../../boost/config/user.hpp" target="_top">&lt;boost/config/user.hpp&gt;</a>
        (located under <span class="emphasis"><em>&lt;boost-root&gt;</em></span><code class="computeroutput"><span class="special">/</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span></code>).
        There are two ways you can use this header:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <span class="bold"><strong>Option 1:</strong></span> copy the header into <span class="emphasis"><em>&lt;boost-root&gt;</em></span><code class="computeroutput"><span class="special">/</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span></code> so that it replaces the default user.hpp
            provided by boost. This option allows only one configure-generated setup;
            boost developers should avoid this option, as it incurs the danger of
            accidentally committing a configure-modified <a href="../../../../boost/config/user.hpp" target="_top">&lt;boost/config/user.hpp&gt;</a>
            to the svn repository (something you will not be thanked for!).
          </li>
<li class="listitem">
            <span class="bold"><strong>Option 2:</strong></span> give the header a more memorable
            name, and place it somewhere convenient; then, define the macro <code class="computeroutput"><span class="identifier">BOOST_USER_CONFIG</span></code> to point to it. For
            example create a new sub-directory <span class="emphasis"><em>&lt;boost-root&gt;</em></span><code class="computeroutput"><span class="special">/</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span></code> <code class="computeroutput"><span class="identifier">user</span><span class="special">/</span></code>, and copy the header there; for example
            as <code class="computeroutput"><span class="identifier">multithread</span><span class="special">-</span><span class="identifier">gcc</span><span class="special">-</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span></code>. Then, when compiling add the command
            line option: <code class="computeroutput"><span class="special">-</span><span class="identifier">DBOOST_USER_CONFIG</span><span class="special">=</span><span class="string">"&lt;boost/config/user/multithread-gcc-config.hpp&gt;"</span></code>,
            and boost will use the new configuration header. This option allows you
            to generate more than one configuration header, and to keep them separate
            from the boost source - so that updates to the source do not interfere
            with your configuration.
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.configuring_boost_for_your_platform.user_settable_options"></a><a name="config_user_settable"></a><a class="link" href="index.html#boost_config.configuring_boost_for_your_platform.user_settable_options" title="User settable options">User
      settable options</a>
</h3></div></div></div>
<p>
        There are some configuration-options that represent user choices, rather
        than compiler defects or platform specific options. These are listed in
        <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">user</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
        and at the start of a configure-generated <code class="computeroutput"><span class="identifier">user</span><span class="special">.</span><span class="identifier">hpp</span></code> header.
        You can define these on the command line, or by editing <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">user</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>, they are listed in the following table:
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Macro
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_USER_CONFIG</span></code>
                </p>
              </td>
<td>
                <p>
                  When defined, it should point to the name of the user configuration
                  file to include prior to any boost configuration files. When not
                  defined, defaults to <a href="../../../../boost/config/user.hpp" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">user</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code></a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_COMPILER_CONFIG</span></code>
                </p>
              </td>
<td>
                <p>
                  When defined, it should point to the name of the compiler configuration
                  file to use. Defining this cuts out the compiler selection logic,
                  and eliminates the dependency on the header containing that logic.
                  For example if you are using gcc, then you could define BOOST_COMPILER_CONFIG
                  to <a href="../../../../boost/config/compiler/gcc.hpp" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">compiler</span><span class="special">/</span><span class="identifier">gcc</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code></a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_STDLIB_CONFIG</span></code>
                </p>
              </td>
<td>
                <p>
                  When defined, it should point to the name of the standard library
                  configuration file to use. Defining this cuts out the standard
                  library selection logic, and eliminates the dependency on the header
                  containing that logic. For example if you are using STLport, then
                  you could define <code class="computeroutput"><span class="identifier">BOOST_STDLIB_CONFIG</span></code>
                  to <a href="../../../../boost/config/stdlib/stlport.hpp" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">stdlib</span><span class="special">/</span><span class="identifier">stlport</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code></a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_PLATFORM_CONFIG</span></code>
                </p>
              </td>
<td>
                <p>
                  When defined, it should point to the name of the platform configuration
                  file to use. Defining this cuts out the platform selection logic,
                  and eliminates the dependency on the header containing that logic.
                  For example if you are compiling on linux, then you could define
                  <code class="computeroutput"><span class="identifier">BOOST_PLATFORM_CONFIG</span></code>
                  to <a href="../../../../boost/config/platform/linux.hpp" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">platform</span><span class="special">/</span><span class="identifier">linux</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code></a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_COMPILER_CONFIG</span></code>
                </p>
              </td>
<td>
                <p>
                  When defined, no compiler configuration file is selected or included,
                  define when the compiler is fully conformant with the standard,
                  or where the user header (see <code class="computeroutput"><span class="identifier">BOOST_USER_CONFIG</span></code>),
                  has had any options necessary added to it, for example by an autoconf
                  generated configure script.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STDLIB_CONFIG</span></code>
                </p>
              </td>
<td>
                <p>
                  When defined, no standard library configuration file is selected
                  or included, define when the standard library is fully conformant
                  with the standard, or where the user header (see <code class="computeroutput"><span class="identifier">BOOST_USER_CONFIG</span></code>), has had any
                  options necessary added to it, for example by an autoconf generated
                  configure script.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_PLATFORM_CONFIG</span></code>
                </p>
              </td>
<td>
                <p>
                  When defined, no platform configuration file is selected or included,
                  define when the platform is fully conformant with the standard
                  (and has no useful extra features), or where the user header (see
                  <code class="computeroutput"><span class="identifier">BOOST_USER_CONFIG</span></code>),
                  has had any options necessary added to it, for example by an autoconf
                  generated configure script.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CONFIG</span></code>
                </p>
              </td>
<td>
                <p>
                  Equivalent to defining all of <code class="computeroutput"><span class="identifier">BOOST_NO_COMPILER_CONFIG</span></code>,
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STDLIB_CONFIG</span></code>
                  and <code class="computeroutput"><span class="identifier">BOOST_NO_PLATFORM_CONFIG</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_STRICT_CONFIG</span></code>
                </p>
              </td>
<td>
                <p>
                  The normal behavior for compiler versions that are newer than the
                  last known version, is to assume that they have all the same defects
                  as the last known version. By setting this define, then compiler
                  versions that are newer than the last known version are assumed
                  to be fully conforming with the standard. This is probably most
                  useful for boost developers or testers, and for those who want
                  to use boost to test beta compiler versions.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_ASSERT_CONFIG</span></code>
                </p>
              </td>
<td>
                <p>
                  When this flag is set, if the config finds anything unknown, then
                  it will stop with a #error rather than continue. Boost regression
                  testers should set this define, as should anyone who wants to quickly
                  check whether boost is supported on their platform.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_DISABLE_THREADS</span></code>
                </p>
              </td>
<td>
                <p>
                  When defined, disables threading support, even if the compiler
                  in its current translation mode supports multiple threads.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_DISABLE_WIN32</span></code>
                </p>
              </td>
<td>
                <p>
                  When defined, disables the use of Win32 specific API's, even when
                  these are available. Also has the effect of setting <code class="computeroutput"><span class="identifier">BOOST_DISABLE_THREADS</span></code> unless
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_PTHREADS</span></code>
                  is set. This option may be set automatically by the config system
                  when it detects that the compiler is in "strict mode".
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_DISABLE_ABI_HEADERS</span></code>
                </p>
              </td>
<td>
                <p>
                  Stops boost headers from including any prefix/suffix headers that
                  normally control things like struct packing and alignment.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_ABI_PREFIX</span></code>
                </p>
              </td>
<td>
                <p>
                  A prefix header to include in place of whatever boost.config would
                  normally select, any replacement should set up struct packing and
                  alignment options as required.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_ABI_SUFFIX</span></code>
                </p>
              </td>
<td>
                <p>
                  A suffix header to include in place of whatever boost.config would
                  normally select, any replacement should undo the effects of the
                  prefix header.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_ALL_DYN_LINK</span></code>
                </p>
              </td>
<td>
                <p>
                  Forces all libraries that have separate source, to be linked as
                  dll's rather than static libraries on Microsoft Windows (this macro
                  is used to turn on <code class="computeroutput"><span class="identifier">__declspec</span><span class="special">(</span><span class="identifier">dllimport</span><span class="special">)</span></code> modifiers, so that the compiler
                  knows which symbols to look for in a dll rather than in a static
                  library). Note that there may be some libraries that can only be
                  statically linked (Boost.Test for example) and others which may
                  only be dynamically linked (Boost.Thread for example), in these
                  cases this macro has no effect.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_</span></code><span class="emphasis"><em>WHATEVER</em></span><code class="computeroutput"><span class="identifier">_DYN_LINK</span></code>
                </p>
              </td>
<td>
                <p>
                  Forces library "whatever" to be linked as a dll rather
                  than a static library on Microsoft Windows: replace the <span class="emphasis"><em>WHATEVER</em></span>
                  part of the macro name with the name of the library that you want
                  to dynamically link to, for example use <code class="computeroutput"><span class="identifier">BOOST_DATE_TIME_DYN_LINK</span></code>
                  or <code class="computeroutput"><span class="identifier">BOOST_REGEX_DYN_LINK</span></code>
                  etc (this macro is used to turn on <code class="computeroutput"><span class="identifier">__declspec</span><span class="special">(</span><span class="identifier">dllimport</span><span class="special">)</span></code> modifiers, so that the compiler
                  knows which symbols to look for in a dll rather than in a static
                  library). Note that there may be some libraries that can only be
                  statically linked (Boost.Test for example) and others which may
                  only be dynamically linked (Boost.Thread for example), in these
                  cases this macro is unsupported.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_ALL_NO_LIB</span></code>
                </p>
              </td>
<td>
                <p>
                  Tells the config system not to automatically select which libraries
                  to link against. Normally if a compiler supports #pragma lib, then
                  the correct library build variant will be automatically selected
                  and linked against, simply by the act of including one of that
                  library's headers. This macro turns that feature off.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_</span></code><span class="emphasis"><em>WHATEVER</em></span><code class="computeroutput"><span class="identifier">_NO_LIB</span></code>
                </p>
              </td>
<td>
                <p>
                  Tells the config system not to automatically select which library
                  to link against for library "whatever", replace <span class="emphasis"><em>WHATEVER</em></span>
                  in the macro name with the name of the library; for example <code class="computeroutput"><span class="identifier">BOOST_DATE_TIME_NO_LIB</span></code> or <code class="computeroutput"><span class="identifier">BOOST_REGEX_NO_LIB</span></code>. Normally
                  if a compiler supports <code class="computeroutput"><span class="preprocessor">#pragma</span>
                  <span class="identifier">lib</span></code>, then the correct
                  library build variant will be automatically selected and linked
                  against, simply by the act of including one of that library's headers.
                  This macro turns that feature off.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_LIB_DIAGNOSTIC</span></code>
                </p>
              </td>
<td>
                <p>
                  Causes the auto-linking code to output diagnostic messages indicating
                  the name of the library that is selected for linking.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_LIB_BUILDID</span></code>
                </p>
              </td>
<td>
                <p>
                  If you built Boost using the <code class="computeroutput"><span class="special">--</span><span class="identifier">buildid</span></code> option then set this
                  macro to the same value as you passed to bjam. For example if you
                  built using <code class="computeroutput"><span class="identifier">bjam</span> <span class="identifier">address</span><span class="special">-</span><span class="identifier">model</span><span class="special">=</span><span class="number">64</span> <span class="special">--</span><span class="identifier">buildid</span><span class="special">=</span><span class="identifier">amd64</span></code> then compile your code
                  with <code class="computeroutput"><span class="special">-</span><span class="identifier">DBOOST_LIB_BUILDID</span><span class="special">=</span><span class="identifier">amd64</span></code>
                  to ensure the correct libraries are selected at link time.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_LIB_TOOLSET</span></code>
                </p>
              </td>
<td>
                <p>
                  Overrides the name of the toolset part of the name of library being
                  linked to; note if defined this must be defined to a quoted string
                  literal, for example "abc".
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.configuring_boost_for_your_platform.advanced_configuration_usage"></a><a class="link" href="index.html#boost_config.configuring_boost_for_your_platform.advanced_configuration_usage" title="Advanced configuration usage">Advanced
      configuration usage</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.advanced_configuration_usage.example_1__creating_our_own_frozen_configuration">Example
        1: creating our own frozen configuration</a></span></dt>
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.advanced_configuration_usage.example_2__skipping_files_that_you_don_t_need">Example
        2: skipping files that you don't need</a></span></dt>
<dt><span class="section"><a href="index.html#boost_config.configuring_boost_for_your_platform.advanced_configuration_usage.example_3__using_configure_script_to_freeze_the_boost_configuration">Example
        3: using configure script to freeze the boost configuration</a></span></dt>
</dl></div>
<p>
        By setting various macros on the compiler command line or by editing <a href="../../../../boost/config/user.hpp" target="_top">&lt;boost/config/user.hpp&gt;</a>,
        the boost configuration setup can be optimised in a variety of ways.
      </p>
<p>
        Boost's configuration is structured so that the user-configuration is included
        first (defaulting to <a href="../../../../boost/config/user.hpp" target="_top">&lt;boost/config/user.hpp&gt;</a>
        if <code class="computeroutput"><span class="identifier">BOOST_USER_CONFIG</span></code> is not
        defined). This sets up any user-defined policies, and gives the user-configuration
        a chance to influence what happens next.
      </p>
<p>
        Next the compiler, standard library, and platform configuration files are
        included. These are included via macros (<code class="computeroutput"><span class="identifier">BOOST_COMPILER_CONFIG</span></code>
        etc, <a class="link" href="index.html#config_user_settable">see user settable macros</a>),
        and if the corresponding macro is undefined then a separate header that detects
        which compiler/standard library/platform is in use is included in order to
        set these. The config can be told to ignore these headers altogether if the
        corresponding <code class="computeroutput"><span class="identifier">BOOST_NO_XXX</span></code>
        macro is set (for example <code class="computeroutput"><span class="identifier">BOOST_NO_COMPILER_CONFIG</span></code>
        to disable including any compiler configuration file - <a class="link" href="index.html#config_user_settable">see
        user settable macros</a>).
      </p>
<p>
        Finally the boost configuration header, includes <a href="../../../../boost/config/detail/suffix.hpp" target="_top">&lt;boost/config/detail/suffix.hpp&gt;</a>;
        this header contains any boiler plate configuration code - for example where
        one boost macro being set implies that another must be set also.
      </p>
<p>
        The following usage examples represent just a few of the possibilities:
      </p>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_config.configuring_boost_for_your_platform.advanced_configuration_usage.example_1__creating_our_own_frozen_configuration"></a><a class="link" href="index.html#boost_config.configuring_boost_for_your_platform.advanced_configuration_usage.example_1__creating_our_own_frozen_configuration" title="Example 1: creating our own frozen configuration">Example
        1: creating our own frozen configuration</a>
</h4></div></div></div>
<p>
          Lets suppose that we're building boost with Visual C++ 6, and STLport 4.0.
          Lets suppose also that we don't intend to update our compiler or standard
          library any time soon. In order to avoid breaking dependencies when we
          update boost, we may want to "freeze" our configuration headers,
          so that we only have to rebuild our project if the boost code itself has
          changed, and not because the boost config has been updated for more recent
          versions of Visual C++ or STLport. We'll start by realising that the configuration
          files in use are: <a href="../../../../boost/config/compiler/visualc.hpp" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">compiler</span><span class="special">/</span><span class="identifier">visualc</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code></a> for the compiler, <a href="../../../../boost/config/stdlib/stlport.hpp" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">stdlib</span><span class="special">/</span><span class="identifier">stlport</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code></a> for the standard library, and
          <a href="../../../../boost/config/platform/win32.hpp" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">platform</span><span class="special">/</span><span class="identifier">win32</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code></a> for the platform. Next we'll
          create our own private configuration directory: <code class="computeroutput"><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">mysetup</span><span class="special">/</span></code>, and copy the configuration files into
          there. Finally, open up <a href="../../../../boost/config/user.hpp" target="_top">&lt;boost/config/user.hpp&gt;</a>
          and edit the following defines:
        </p>
<pre class="programlisting"><span class="preprocessor">#define</span> <span class="identifier">BOOST_COMPILER_CONFIG</span> <span class="string">"boost/config/mysetup/visualc.hpp"</span>
<span class="preprocessor">#define</span> <span class="identifier">BOOST_STDLIB_CONFIG</span> <span class="string">"boost/config/mysetup/stlport.hpp"</span>
<span class="preprocessor">#define</span> <span class="identifier">BOOST_USER_CONFIG</span> <span class="string">"boost/config/mysetup/win32.hpp"</span>
</pre>
<p>
          Now when you use boost, its configuration header will go straight to our
          "frozen" versions, and ignore the default versions, you will
          now be insulated from any configuration changes when you update boost.
          This technique is also useful if you want to modify some of the boost configuration
          files; for example if you are working with a beta compiler release not
          yet supported by boost.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_config.configuring_boost_for_your_platform.advanced_configuration_usage.example_2__skipping_files_that_you_don_t_need"></a><a class="link" href="index.html#boost_config.configuring_boost_for_your_platform.advanced_configuration_usage.example_2__skipping_files_that_you_don_t_need" title="Example 2: skipping files that you don't need">Example
        2: skipping files that you don't need</a>
</h4></div></div></div>
<p>
          Lets suppose that you're using boost with a compiler that is fully conformant
          with the standard; you're not interested in the fact that older versions
          of your compiler may have had bugs, because you know that your current
          version does not need any configuration macros setting. In a case like
          this, you can define <code class="computeroutput"><span class="identifier">BOOST_NO_COMPILER_CONFIG</span></code>
          either on the command line, or in <a href="../../../../boost/config/user.hpp" target="_top">&lt;boost/config/user.hpp&gt;</a>,
          and miss out the compiler configuration header altogether (actually you
          miss out two headers, one which works out what the compiler is, and one
          that configures boost for it). This has two consequences: the first is
          that less code has to be compiled, and the second that you have removed
          a dependency on two boost headers.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_config.configuring_boost_for_your_platform.advanced_configuration_usage.example_3__using_configure_script_to_freeze_the_boost_configuration"></a><a class="link" href="index.html#boost_config.configuring_boost_for_your_platform.advanced_configuration_usage.example_3__using_configure_script_to_freeze_the_boost_configuration" title="Example 3: using configure script to freeze the boost configuration">Example
        3: using configure script to freeze the boost configuration</a>
</h4></div></div></div>
<p>
          If you are working on a unix-like platform then you can use the configure
          script to generate a "frozen" configuration based on your current
          compiler setup - <a class="link" href="index.html#config_config_script">see using the configure
          script for more details</a>.
        </p>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.configuring_boost_for_your_platform.testing_the_boost_configuration"></a><a class="link" href="index.html#boost_config.configuring_boost_for_your_platform.testing_the_boost_configuration" title="Testing the boost configuration">Testing
      the boost configuration</a>
</h3></div></div></div>
<p>
        The boost configuration library provides a full set of regression test programs
        under the <span class="emphasis"><em>&lt;boost-root&gt;</em></span><code class="computeroutput"><span class="special">/</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span></code>
        <code class="computeroutput"><span class="identifier">test</span><span class="special">/</span></code>
        sub-directory:
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  File
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">config_info</span><span class="special">.</span><span class="identifier">cpp</span></code>
                </p>
              </td>
<td>
                <p>
                  Prints out a detailed description of your compiler/standard library/platform
                  setup, plus your current boost configuration. The information provided
                  by this program is useful in setting up the boost configuration
                  files. If you report that boost is incorrectly configured for your
                  compiler/library/platform then please include the output from this
                  program when reporting the changes required.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">config_test</span><span class="special">.</span><span class="identifier">cpp</span></code>
                </p>
              </td>
<td>
                <p>
                  A monolithic test program that includes most of the individual
                  test cases. This provides a quick check to see if boost is correctly
                  configured for your compiler/library/platform.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">limits_test</span><span class="special">.</span><span class="identifier">cpp</span></code>
                </p>
              </td>
<td>
                <p>
                  Tests your standard library's <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span></code>
                  implementation (or its boost provided replacement if <code class="computeroutput"><span class="identifier">BOOST_NO_LIMITS</span></code> is defined).
                  This test file fails with most versions of numeric_limits, mainly
                  due to the way that some compilers treat NAN's and infinity.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">no_</span><span class="special">*</span><span class="identifier">pass</span><span class="special">.</span><span class="identifier">cpp</span></code>
                </p>
              </td>
<td>
                <p>
                  Individual compiler defect test files. Each of these should compile,
                  if one does not then the corresponding <code class="computeroutput"><span class="identifier">BOOST_NO_XXX</span></code>
                  macro needs to be defined - see each test file for specific details.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">no_</span><span class="special">*</span><span class="identifier">fail</span><span class="special">.</span><span class="identifier">cpp</span></code>
                </p>
              </td>
<td>
                <p>
                  Individual compiler defect test files. Each of these should not
                  compile, if one does then the corresponding <code class="computeroutput"><span class="identifier">BOOST_NO_XXX</span></code>
                  macro is defined when it need not be - see each test file for specific
                  details.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">has_</span><span class="special">*</span><span class="identifier">pass</span><span class="special">.</span><span class="identifier">cpp</span></code>
                </p>
              </td>
<td>
                <p>
                  Individual feature test files. If one of these does not compile
                  then the corresponding <code class="computeroutput"><span class="identifier">BOOST_HAS_XXX</span></code>
                  macro is defined when it should not be - see each test file for
                  specific details.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">has_</span><span class="special">*</span><span class="identifier">fail</span><span class="special">.</span><span class="identifier">cpp</span></code>
                </p>
              </td>
<td>
                <p>
                  Individual feature test files. If one of these does compile then
                  the corresponding <code class="computeroutput"><span class="identifier">BOOST_HAS_XXX</span></code>
                  macro can be safely defined - see each test file for specific details.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<p>
        Although you can run the configuration regression tests as individual test
        files, there are rather a lot of them, so there are a couple of shortcuts
        to help you out:
      </p>
<p>
        Alternatively you can run the configure script like this:
      </p>
<div class="blockquote"><blockquote class="blockquote"><p>
          <code class="computeroutput"><span class="special">./</span><span class="identifier">configure</span>
          <span class="special">--</span><span class="identifier">enable</span><span class="special">-</span><span class="identifier">test</span></code>
        </p></blockquote></div>
<p>
        in which case the script will test the current configuration rather than
        creating a new one from scratch.
      </p>
<p>
        If you are reporting the results of these tests for a new platform/library/compiler
        then please include a log of the full compiler output, the output from <code class="computeroutput"><span class="identifier">config_info</span><span class="special">.</span><span class="identifier">cpp</span></code>, and the pass/fail test results.
      </p>
</div>
</div>
</div>
<div class="copyright-footer"></div>
<hr>
<div class="spirit-nav"><a accesskey="n" href="boost_config/boost_macro_reference.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a></div>
</body>
</html>
