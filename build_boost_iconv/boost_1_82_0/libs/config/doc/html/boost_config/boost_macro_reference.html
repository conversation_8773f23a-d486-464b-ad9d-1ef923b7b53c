<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Boost Macro Reference</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="Boost.Config">
<link rel="up" href="../index.html" title="Boost.Config">
<link rel="prev" href="../index.html" title="Boost.Config">
<link rel="next" href="build_config.html" title="Build Time Configuration">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../index.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="build_config.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_config.boost_macro_reference"></a><a class="link" href="boost_macro_reference.html" title="Boost Macro Reference">Boost Macro Reference</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.broad_c___standard_level_checks">Broad
      C++ Standard Level Checks</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__03_defects">Macros
      that describe C++03 defects</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_optional_features">Macros
      that describe optional features</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported">Macros
      that describe C++11 features not supported</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_allow_use_of_c__11_features_with_c__03_compilers">Macros
      that allow use of C++11 features with C++03 compilers</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__14_features_not_supported">Macros
      that describe C++14 features not supported</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_allow_use_of_c__14_features_with_c__11_or_earlier_compilers">Macros
      that allow use of C++14 features with C++11 or earlier compilers</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__17_features_not_supported">Macros
      that describe C++17 features not supported</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_allow_use_of_c__17_features_with_c__14_or_earlier_compilers">Macros
      that allow use of C++17 features with C++14 or earlier compilers</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__20_features_not_supported">Macros
      that describe C++20 features not supported</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_features_that_have_been_removed_from_the_standard_">Macros
      that describe features that have been removed from the standard.</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.boost_helper_macros">Boost
      Helper Macros</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.boost_informational_macros">Boost
      Informational Macros</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.boost_deprecated_macros">Boost
      Deprecated Macros</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_for_libraries_with_separate_source_code">Macros
      for libraries with separate source code</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.broad_c___standard_level_checks"></a><a name="config_defects"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.broad_c___standard_level_checks" title="Broad C++ Standard Level Checks">Broad
      C++ Standard Level Checks</a>
</h3></div></div></div>
<p>
        These are for the times when you just want to check "does this compiler
        conform to C++11" or some such.
      </p>
<h5>
<a name="boost_config.boost_macro_reference.broad_c___standard_level_checks.h0"></a>
        <span class="phrase"><a name="boost_config.boost_macro_reference.broad_c___standard_level_checks.c___standard_level"></a></span><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.broad_c___standard_level_checks.c___standard_level">C++
        Standard Level</a>
      </h5>
<p>
        The macro <code class="computeroutput"><span class="identifier">BOOST_CXX_VERSION</span></code>
        is set to the C++ standard version - it has the same value as <code class="computeroutput"><span class="identifier">__cplusplus</span></code> if <code class="computeroutput"><span class="identifier">__cplusplus</span></code>
        is set to a sensible value, otherwise the current C++ standard level in effect
        (for example for MSVC this is set to the value of <code class="computeroutput"><span class="identifier">_MSVC_LANG</span></code>).
      </p>
<p>
        Available standard values are:
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  standard
                </p>
              </th>
<th>
                <p>
                  value
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  2003
                </p>
              </td>
<td>
                <p>
                  199711L
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  2011
                </p>
              </td>
<td>
                <p>
                  201103L
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  2014
                </p>
              </td>
<td>
                <p>
                  201402L
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  2017
                </p>
              </td>
<td>
                <p>
                  201703L
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  2020
                </p>
              </td>
<td>
                <p>
                  202002L
                </p>
              </td>
</tr>
</tbody>
</table></div>
<p>
        You should use this macro as a basic sanity check that the compiler is minimally
        at the required standard level - there may still be bugs, unimplemented features,
        and/or missing headers. The individual feature checks should be used for
        those.
      </p>
<h5>
<a name="boost_config.boost_macro_reference.broad_c___standard_level_checks.h1"></a>
        <span class="phrase"><a name="boost_config.boost_macro_reference.broad_c___standard_level_checks.strict_standard_level_checks"></a></span><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.broad_c___standard_level_checks.strict_standard_level_checks">Strict
        Standard Level Checks</a>
      </h5>
<p>
        The following macros:
      </p>
<pre class="programlisting"><span class="identifier">BOOST_NO_CXX03</span>
<span class="identifier">BOOST_NO_CXX11</span>
<span class="identifier">BOOST_NO_CXX14</span>
<span class="identifier">BOOST_NO_CXX17</span>
</pre>
<p>
        Are set if <span class="bold"><strong>any</strong></span> of our defect macros are
        set for that standard level, .
      </p>
<p>
        Note that these are therefore very strict checks and as a result, other than
        very recent gcc/clang releases, many compilers will end up defining <span class="emphasis"><em>all</em></span>
        of these macros: even when <code class="computeroutput"><span class="identifier">BOOST_CXX_VERSION</span></code>
        indicates a much more recent standard is in effect.
      </p>
<h5>
<a name="boost_config.boost_macro_reference.broad_c___standard_level_checks.h2"></a>
        <span class="phrase"><a name="boost_config.boost_macro_reference.broad_c___standard_level_checks.strict_standard_check_headers"></a></span><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.broad_c___standard_level_checks.strict_standard_check_headers">Strict
        Standard Check Headers</a>
      </h5>
<p>
        The following headers:
      </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">assert_cxx03</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">assert_cxx11</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">assert_cxx14</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">assert_cxx17</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<p>
        Can be used to assert that none of our defect macros are set for that standard
        (or any older standards).
      </p>
<p>
        The headers will provide a slightly more useful error message than just checking
        on <code class="computeroutput"><span class="identifier">BOOST_NO_CXX03</span></code> for example,
        as they will indicate which defect macro produced the error.
      </p>
<p>
        As noted abovem these are very strict checks, and therefore other than recent
        gcc/clang releases, many compilers will genrate a <code class="computeroutput"><span class="preprocessor">#error</span></code>
        on including <span class="emphasis"><em>any</em></span> of these headers.
      </p>
<h5>
<a name="boost_config.boost_macro_reference.broad_c___standard_level_checks.h3"></a>
        <span class="phrase"><a name="boost_config.boost_macro_reference.broad_c___standard_level_checks.sd6_feature_test_macros"></a></span><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.broad_c___standard_level_checks.sd6_feature_test_macros">SD6
        Feature Test Macros</a>
      </h5>
<p>
        Note that none of the above checks take into account any standard library
        SD6 feature test macros, as doing so would require <code class="computeroutput"><span class="preprocessor">#include</span></code>ing
        almost the whole standard library.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.macros_that_describe_c__03_defects"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__03_defects" title="Macros that describe C++03 defects">Macros
      that describe C++03 defects</a>
</h3></div></div></div>
<p>
        The following macros all describe features that are required by the C++03
        standard, if one of the following macros is defined, then it represents a
        defect in the compiler's conformance with the 2003 standard.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Macro
                </p>
              </th>
<th>
                <p>
                  Section
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_BCB_PARTIAL_SPECIALIZATION_BUG</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler exhibits certain partial specialisation bug - probably
                  Borland C++ Builder specific.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_FUNCTION_SCOPE_USING_DECLARATION_BREAKS_ADL</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Argument dependent lookup fails if there is a using declaration
                  for the symbol being looked up in the current scope. For example,
                  using <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">get_pointer</span></code>; prevents ADL from
                  finding overloads of <code class="computeroutput"><span class="identifier">get_pointer</span></code>
                  in namespaces nested inside boost (but not elsewhere). Probably
                  Borland specific.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_ADL_BARRIER</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler locates and searches namespaces that it should <span class="emphasis"><em>*not*</em></span>
                  in fact search when performing argument dependent lookup.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_ARGUMENT_DEPENDENT_LOOKUP</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Compiler does not implement argument-dependent lookup (also named
                  Koenig lookup); see std::3.4.2 [basic.koenig.lookup]
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_AUTO_PTR</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  If the compiler / library supplies non-standard or broken <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">auto_ptr</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_COMPLETE_VALUE_INITIALIZATION</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Compiler has not completely implemented value-initialization. See
                  also <a href="../../../../utility/value_init.htm#compiler_issues" target="_top">The
                  Utility/Value Init docs</a>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CTYPE_FUNCTIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The Platform does not provide functions for the character-classifying
                  operations <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">ctype</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code> and <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">cctype</span><span class="special">&gt;</span></code>,
                  only macros.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CV_SPECIALIZATIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  If template specialisations for cv-qualified types conflict with
                  a specialisation for a cv-unqualififed type.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CV_VOID_SPECIALIZATIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  If template specialisations for cv-void types conflict with a specialisation
                  for void.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CWCHAR</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The Platform does not provide <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">wchar</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>
                  and <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">cwchar</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CWCTYPE</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The Platform does not provide <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">wctype</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>
                  and <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">cwctype</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_FENV_H</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform, Standard library
                </p>
              </td>
<td>
                <p>
                  The C standard library doesn't provide <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">fenv</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>.
                  <a href="../../../../../boost/detail/fenv.hpp" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">detail</span><span class="special">/</span><span class="identifier">fenv</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code></a>
                  should be included instead of <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">fenv</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>
                  for maximum portability on platforms which do provide <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">fenv</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_DEPENDENT_NESTED_DERIVATIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler fails to compile a nested class that has a dependent
                  base class:
</p>
<pre class="table-programlisting"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">foo</span> <span class="special">:</span> <span class="special">{</span>
   <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">U</span><span class="special">&gt;</span>
   <span class="keyword">struct</span> <span class="identifier">bar</span> <span class="special">:</span> <span class="keyword">public</span> <span class="identifier">U</span> <span class="special">{};</span>
</pre>
<p>
                  };
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_DEPENDENT_TYPES_IN_TEMPLATE_VALUE_PARAMETERS</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Template value parameters cannot have a dependent type, for example:
</p>
<pre class="table-programlisting"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">T</span><span class="special">::</span><span class="identifier">type</span> <span class="identifier">value</span><span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">X</span> <span class="special">{</span> <span class="special">...</span> <span class="special">};</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_EXCEPTION_STD_NAMESPACE</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard Library
                </p>
              </td>
<td>
                <p>
                  The standard library does not put some or all of the contents of
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">exception</span><span class="special">&gt;</span></code> in namespace std.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_EXCEPTIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler does not support exception handling (this setting
                  is typically required by many C++ compilers for embedded platforms).
                  Note that there is no requirement for boost libraries to honor
                  this configuration setting - indeed doing so may be impossible
                  in some cases. Those libraries that do honor this will typically
                  abort if a critical error occurs - you have been warned!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_FUNCTION_TEMPLATE_ORDERING</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler does not perform function template ordering or its
                  function template ordering is incorrect.
</p>
<pre class="table-programlisting"><span class="comment">// #1</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">void</span> <span class="identifier">f</span><span class="special">(</span><span class="identifier">T</span><span class="special">);</span>

<span class="comment">// #2</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span><span class="keyword">class</span> <span class="identifier">U</span><span class="special">&gt;</span> <span class="keyword">void</span> <span class="identifier">f</span><span class="special">(</span><span class="identifier">T</span><span class="special">(*)(</span><span class="identifier">U</span><span class="special">));</span>

<span class="keyword">void</span> <span class="identifier">bar</span><span class="special">(</span><span class="keyword">int</span><span class="special">);</span>

<span class="identifier">f</span><span class="special">(&amp;</span><span class="identifier">bar</span><span class="special">);</span> <span class="comment">// should choose #2.</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_INCLASS_MEMBER_INITIALIZATION</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Compiler violates std::9.4.2/4.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_INTRINSIC_WCHAR_T</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The C++ implementation does not provide <code class="computeroutput"><span class="keyword">wchar_t</span></code>,
                  or it is really a synonym for another integral type. Use this symbol
                  to decide whether it is appropriate to explicitly specialize a
                  template on <code class="computeroutput"><span class="keyword">wchar_t</span></code>
                  if there is already a specialization for other integer types.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_IOSFWD</span></code>
                </p>
              </td>
<td>
                <p>
                  std lib
                </p>
              </td>
<td>
                <p>
                  The standard library lacks <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">iosfwd</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_IOSTREAM</span></code>
                </p>
              </td>
<td>
                <p>
                  std lib
                </p>
              </td>
<td>
                <p>
                  The standard library lacks <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span></code>,
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">istream</span><span class="special">&gt;</span></code> or <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">ostream</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_IS_ABSTRACT</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The C++ compiler does not support SFINAE with abstract types, this
                  is covered by <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/cwg_defects.html#337" target="_top">Core
                  Language DR337</a>, but is not part of the current standard.
                  Fortunately most compilers that support SFINAE also support this
                  DR. See also BOOST_NO_SFINAE and BOOST_NO_SFINAE_EXPR
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_LIMITS</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The C++ implementation does not provide the <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">limits</span><span class="special">&gt;</span></code>
                  header. Never check for this symbol in library code; always include
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">limits</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>, which guarantees to provide
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_NUMERIC_LIMITS</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  C++11 additions to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">numeric_limits</span></code>
                  are not available for use. <code class="computeroutput"><span class="keyword">static</span>
                  <span class="identifier">function</span> <span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">lowest</span><span class="special">()</span></code> the lowest finite value representable
                  by the numeric type. <code class="computeroutput"><span class="keyword">static</span>
                  <span class="keyword">int</span> <span class="keyword">const</span>
                  <span class="identifier">max_digits10</span></code> the number
                  of decimal digits that are required to make sure that two distinct
                  values of the type have distinct decimal representations. <code class="computeroutput"><span class="keyword">template</span><span class="special">&lt;&gt;</span>
                  <span class="keyword">class</span> <span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="keyword">char16_t</span><span class="special">&gt;;</span></code>, see also <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_CHAR16_T</span></code>,
                  <code class="computeroutput"><span class="keyword">template</span><span class="special">&lt;&gt;</span>
                  <span class="keyword">class</span> <span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="keyword">char32_t</span><span class="special">&gt;;</span></code> see also <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_CHAR32_T</span></code>.
                  Replaces BOOST_NO_NUMERIC_LIMITS_LOWEST.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_LIMITS_COMPILE_TIME_CONSTANTS</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  Constants such as <code class="computeroutput"><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">is_signed</span></code>
                  are not available for use at compile-time.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_LONG_LONG_NUMERIC_LIMITS</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  There is no specialization for <code class="computeroutput"><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="keyword">long</span>
                  <span class="keyword">long</span><span class="special">&gt;</span></code>
                  and <code class="computeroutput"><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="keyword">unsigned</span>
                  <span class="keyword">long</span> <span class="keyword">long</span><span class="special">&gt;</span></code>. <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">limits</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                  will then add these specializations as a standard library "fix"
                  only if the compiler supports the <code class="computeroutput"><span class="keyword">long</span>
                  <span class="keyword">long</span></code> datatype.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_MEMBER_FUNCTION_SPECIALIZATIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the specialization of individual
                  member functions of template classes.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_MEMBER_TEMPLATE_KEYWORD</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  If the compiler supports member templates, but not the template
                  keyword when accessing member template classes.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_MEMBER_TEMPLATE_FRIENDS</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Member template friend syntax (<code class="computeroutput"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span>
                  <span class="identifier">P</span><span class="special">&gt;</span>
                  <span class="keyword">friend</span> <span class="keyword">class</span>
                  <span class="identifier">frd</span><span class="special">;</span></code>)
                  described in the C++ Standard, 14.5.3, not supported.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_MEMBER_TEMPLATES</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Member template functions not fully supported.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_MS_INT64_NUMERIC_LIMITS</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  There is no specialization for <code class="computeroutput"><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="identifier">__int64</span><span class="special">&gt;</span></code> and <code class="computeroutput"><span class="identifier">numeric_limits</span><span class="special">&lt;</span><span class="keyword">unsigned</span>
                  <span class="identifier">__int64</span><span class="special">&gt;</span></code>.
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">limits</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code> will then add these specializations
                  as a standard library "fix", only if the compiler supports
                  the <code class="computeroutput"><span class="identifier">__int64</span></code> datatype.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_NESTED_FRIENDSHIP</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Compiler doesn't allow a nested class to access private members
                  of its containing class. Probably Borland/CodeGear specific.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_OPERATORS_IN_NAMESPACE</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Compiler requires inherited operator friend functions to be defined
                  at namespace scope, then using'ed to boost. Probably GCC specific.
                  See <a href="../../../../../boost/operators.hpp" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">operators</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code></a> for example.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_PARTIAL_SPECIALIZATION_IMPLICIT_DEFAULT_ARGS</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler does not correctly handle partial specializations
                  which depend upon default arguments in the primary template.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_POINTER_TO_MEMBER_CONST</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler does not correctly handle pointers to const member
                  functions, preventing use of these in overloaded function templates.
                  See <a href="../../../../../boost/functional.hpp" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">functional</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code></a> for example.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_POINTER_TO_MEMBER_TEMPLATE_PARAMETERS</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Pointers to members don't work when used as template parameters.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_PRIVATE_IN_AGGREGATE</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler misreads 8.5.1, treating classes as non-aggregate
                  if they contain private or protected member functions.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_RESTRICT_REFERENCES</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Compiler-specific <code class="computeroutput"><span class="identifier">restrict</span></code>
                  keyword can not be applied to references.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_RTTI</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler may (or may not) have the typeid operator, but RTTI
                  on the dynamic type of an object is not supported.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_SFINAE</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the "Substitution Failure Is
                  Not An Error" meta-programming idiom. This is the lightweight
                  pre-C++11 version of SFINAE.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_SFINAE_EXPR</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler does not support usage of SFINAE with arbitrary expressions.
                  This is the post-C++11 SFINAE, but excludes a few specific corner
                  cases, see also BOOST_NO_CXX11_SFINAE_EXPR.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_ALLOCATOR</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The C++ standard library does not provide a standards conforming
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_DISTANCE</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The platform does not have a conforming version of <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">distance</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_ITERATOR</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The C++ implementation fails to provide the <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator</span></code>
                  class. Note that post C++17, this macro is re-purposed to indicate
                  that std::iterator has been removed or deprecated.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_ITERATOR_TRAITS</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The compiler does not provide a standard compliant implementation
                  of <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator_traits</span></code>. Note that the
                  compiler may still have a non-standard implementation.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_LOCALE</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The standard library lacks <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_MESSAGES</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The standard library lacks a conforming <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">messages</span></code>
                  facet.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_MIN_MAX</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The C++ standard library does not provide the <code class="computeroutput"><span class="identifier">min</span><span class="special">()</span></code> and <code class="computeroutput"><span class="identifier">max</span><span class="special">()</span></code> template functions that should
                  be in <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">algorithm</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_OUTPUT_ITERATOR_ASSIGN</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  Defined if the standard library's output iterators are not assignable.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_TYPEINFO</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The &lt;typeinfo&gt; header declares <code class="computeroutput"><span class="identifier">type_info</span></code>
                  in the global namespace instead of namespace std.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_USE_FACET</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The standard library lacks a conforming <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">use_facet</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_WSTREAMBUF</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The standard library's implementation of <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_streambuf</span><span class="special">&lt;</span><span class="keyword">wchar_t</span><span class="special">&gt;</span></code> is either missing, incomplete,
                  or buggy.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_WSTRING</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The standard library lacks <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STDC_NAMESPACE</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler, Platform
                </p>
              </td>
<td>
                <p>
                  The contents of C++ standard headers for C library functions (the
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">c</span><span class="special">...&gt;</span></code> headers) have not been placed
                  in namespace std. This test is difficult - some libraries "fake"
                  the std C functions by adding using declarations to import them
                  into namespace std, unfortunately they don't necessarily catch
                  all of them...
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STRINGSTREAM</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The C++ implementation does not provide the <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">sstream</span><span class="special">&gt;</span></code>
                  header.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_SWPRINTF</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform does not have a conforming version of <code class="computeroutput"><span class="identifier">swprintf</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_TEMPLATE_PARTIAL_SPECIALIZATION</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Class template partial specialization (14.5.4 [temp.class.spec])
                  not supported.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_TEMPLATED_IOSTREAMS</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide templated iostream classes.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_TEMPLATED_ITERATOR_CONSTRUCTORS</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide templated iterator constructors
                  for its containers.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_TEMPLATE_TEMPLATES</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler does not support template template parameters.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_TYPEID</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the typeid operator at all.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_TYPENAME_WITH_CTOR</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The typename keyword cannot be used when creating a temporary of
                  a Dependent type.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_UNREACHABLE_RETURN_DETECTION</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  If a return is unreachable, then no return statement should be
                  required, however some compilers insist on it, while other issue
                  a bunch of warnings if it is in fact present.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_USING_DECLARATION_OVERLOADS_FROM_TYPENAME_BASE</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler will not accept a using declaration that brings a
                  function from a typename used as a base class into a derived class
                  if functions of the same name are present in the derived class.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_USING_TEMPLATE</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler will not accept a using declaration that imports a
                  template class or function from another namespace. Originally a
                  Borland specific problem with imports to/from the global namespace,
                  extended to MSVC6 which has a specific issue with importing template
                  classes (but not functions).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_VOID_RETURNS</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler does not allow a void function to return the result
                  of calling another void function.
</p>
<pre class="table-programlisting"><span class="keyword">void</span> <span class="identifier">f</span><span class="special">()</span> <span class="special">{}</span>
<span class="keyword">void</span> <span class="identifier">g</span><span class="special">()</span> <span class="special">{</span> <span class="keyword">return</span> <span class="identifier">f</span><span class="special">();</span> <span class="special">}</span>
</pre>
<p>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.macros_that_describe_optional_features"></a><a name="config_features"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_optional_features" title="Macros that describe optional features">Macros
      that describe optional features</a>
</h3></div></div></div>
<p>
        The following macros describe features that are not required by the C++ standard.
        The macro is only defined if the feature is present.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Macro
                </p>
              </th>
<th>
                <p>
                  Section
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_BETHREADS</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform supports BeOS style threads.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_CLOCK_GETTIME</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has the POSIX API <code class="computeroutput"><span class="identifier">clock_gettime</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_DIRENT_H</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has the POSIX header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">dirent</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_EXPM1</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has the functions <code class="computeroutput"><span class="identifier">expm1</span></code>,
                  <code class="computeroutput"><span class="identifier">expm1f</span></code> and <code class="computeroutput"><span class="identifier">expm1l</span></code> in <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">math</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_FLOAT128</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler has <code class="computeroutput"><span class="identifier">__float128</span></code>
                  as a native type which is distinct from all the regular C++ floating
                  point types.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_FTIME</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has the Win32 API type FTIME.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_GETSYSTEMTIMEASFILETIME</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has the Win32 API GetSystemTimeAsFileTime.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_GETTIMEOFDAY</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has the POSIX API <code class="computeroutput"><span class="identifier">gettimeofday</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_HASH</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The C++ implementation provides the (SGI) hash_set and hash_map
                  classes. When defined, <code class="computeroutput"><span class="identifier">BOOST_HASH_SET_HEADER</span></code>
                  and <code class="computeroutput"><span class="identifier">BOOST_HASH_MAP_HEADER</span></code>
                  will contain the names of the header needed to access hash_set
                  and hash_map; <code class="computeroutput"><span class="identifier">BOOST_STD_EXTENSION_NAMESPACE</span></code>
                  will provide the namespace in which the two class templates reside.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_INT128</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler has <code class="computeroutput"><span class="identifier">__int128</span></code>
                  and <code class="computeroutput"><span class="keyword">unsigned</span> <span class="identifier">__int128</span></code>
                  as native types which are distinct from all the regular C++ integer
                  types.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_LOG1P</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has the functions <code class="computeroutput"><span class="identifier">log1p</span></code>,
                  <code class="computeroutput"><span class="identifier">log1pf</span></code> and <code class="computeroutput"><span class="identifier">log1pl</span></code> in <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">math</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_MACRO_USE_FACET</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The standard library lacks a conforming <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">use_facet</span></code>,
                  but has a macro <code class="computeroutput"><span class="identifier">_USE</span><span class="special">(</span><span class="identifier">loc</span><span class="special">,</span> <span class="identifier">Type</span><span class="special">)</span></code> that does the job. This is primarily
                  for the Dinkumware std lib.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_MS_INT64</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler supports the <code class="computeroutput"><span class="identifier">__int64</span></code>
                  data type.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_NANOSLEEP</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has the POSIX API nanosleep.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_NL_TYPES_H</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has an <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">nl_types</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_NRVO</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Indicated that the compiler supports the named return value optimization
                  (NRVO). Used to select the most efficient implementation for some
                  function. See <a href="../../../../../boost/operators.hpp" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">operators</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code></a> for example.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_PARTIAL_STD_ALLOCATOR</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard Library
                </p>
              </td>
<td>
                <p>
                  The standard library has a partially conforming <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator</span></code>
                  class, but without any of the member templates.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_PRAGMA_ONCE</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler recognizes the <code class="computeroutput"><span class="preprocessor">#pragma</span>
                  <span class="identifier">once</span></code> directive which
                  tells that the containing header should be included only once while
                  preprocessing the current translation unit. The pragma may improve
                  compile times of large projects with some compilers.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_PRAGMA_DETECT_MISMATCH</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  The compiler recognizes the <code class="computeroutput"><span class="preprocessor">#pragma</span>
                  <span class="identifier">detect_mismatch</span><span class="special">(</span><span class="string">"name"</span><span class="special">,</span>
                  <span class="string">"value"</span><span class="special">)</span></code>
                  directive which tells that the link stage should be terminated
                  with error if values for provided <code class="computeroutput"><span class="string">"name"</span></code>
                  missmatch. This pragma may be a help in preventing ODR violations
                  and ensuring that different modules are compiled with same flags.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_PTHREAD_DELAY_NP</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has the POSIX API <code class="computeroutput"><span class="identifier">pthread_delay_np</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_PTHREAD_MUTEXATTR_SETTYPE</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has the POSIX API <code class="computeroutput"><span class="identifier">pthread_mutexattr_settype</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_PTHREAD_YIELD</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has the POSIX API <code class="computeroutput"><span class="identifier">pthread_yield</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_PTHREADS</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform support POSIX style threads.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_SCHED_YIELD</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has the POSIX API <code class="computeroutput"><span class="identifier">sched_yield</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_SGI_TYPE_TRAITS</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler, Standard library
                </p>
              </td>
<td>
                <p>
                  The compiler has native support for SGI style type traits.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_STDINT_H</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform has a <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">stdint</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_SLIST</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The C++ implementation provides the (SGI) slist class. When defined,
                  <code class="computeroutput"><span class="identifier">BOOST_SLIST_HEADER</span></code>
                  will contain the name of the header needed to access <code class="computeroutput"><span class="identifier">slist</span></code> and <code class="computeroutput"><span class="identifier">BOOST_STD_EXTENSION_NAMESPACE</span></code>
                  will provide the namespace in which <code class="computeroutput"><span class="identifier">slist</span></code>
                  resides.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_STLP_USE_FACET</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The standard library lacks a conforming <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">use_facet</span></code>,
                  but has a workaround class-version that does the job. This is primarily
                  for the STLport std lib.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_ARRAY</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a TR1 conforming version of <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">array</span><span class="special">&gt;</span></code>.
                  This macro is only guaranteed to be defined after including one
                  of the headers from Boost.TR1. Further this macro is now deprecated
                  in favour of BOOST_NO_CXX11_HDR_ARRAY.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_COMPLEX_OVERLOADS</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a version of <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">complex</span><span class="special">&gt;</span></code>
                  that supports passing scalars to the complex number algorithms.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_COMPLEX_INVERSE_TRIG</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a version of <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">complex</span><span class="special">&gt;</span></code>
                  that includes the new inverse trig functions from TR1.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_REFERENCE_WRAPPER</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has TR1 conforming reference wrappers in <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">functional</span><span class="special">&gt;</span></code>. This macro is only guaranteed
                  to be defined after including one of the headers from Boost.TR1.
                  Further this macro is now deprecated in favour of BOOST_NO_CXX11_HDR_FUNCTIONAL.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_RESULT_OF</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a TR1 conforming result_of template in <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">functional</span><span class="special">&gt;</span></code>. This macro is only guaranteed
                  to be defined after including one of the headers from Boost.TR1.
                  Further this macro is now deprecated in favour of BOOST_NO_CXX11_HDR_FUNCTIONAL.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_MEM_FN</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a TR1 conforming mem_fn function template in <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">functional</span><span class="special">&gt;</span></code>. This macro is only guaranteed
                  to be defined after including one of the headers from Boost.TR1.
                  Further this macro is now deprecated in favour of BOOST_NO_CXX11_HDR_FUNCTIONAL.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_BIND</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a TR1 conforming bind function template in <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">functional</span><span class="special">&gt;</span></code>. This macro is only guaranteed
                  to be defined after including one of the headers from Boost.TR1.
                  Further this macro is now deprecated in favour of BOOST_NO_CXX11_HDR_FUNCTIONAL.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_FUNCTION</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a TR1 conforming function class template in <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">functional</span><span class="special">&gt;</span></code>. This macro is only guaranteed
                  to be defined after including one of the headers from Boost.TR1.
                  Further this macro is now deprecated in favour of BOOST_NO_CXX11_HDR_FUNCTIONAL.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_HASH</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a TR1 conforming hash function template in <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">functional</span><span class="special">&gt;</span></code>. This macro is only guaranteed
                  to be defined after including one of the headers from Boost.TR1.
                  Further this macro is now deprecated in favour of BOOST_NO_CXX11_HDR_FUNCTIONAL.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_SHARED_PTR</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a TR1 conforming <code class="computeroutput"><span class="identifier">shared_ptr</span></code>
                  class template in <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">memory</span><span class="special">&gt;</span></code>.
                  This macro is only guaranteed to be defined after including one
                  of the headers from Boost.TR1. Further this macro is now deprecated
                  in favour of BOOST_NO_CXX11_SMART_PTR.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_RANDOM</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a TR1 conforming version of <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">random</span><span class="special">&gt;</span></code>.
                  This macro is only guaranteed to be defined after including one
                  of the headers from Boost.TR1. Further this macro is now deprecated
                  in favour of BOOST_NO_CXX11_HDR_RANDOM.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_REGEX</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a TR1 conforming version of <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">regex</span><span class="special">&gt;</span></code>.
                  This macro is only guaranteed to be defined after including one
                  of the headers from Boost.TR1. Further this macro is now deprecated
                  in favour of BOOST_NO_CXX11_HDR_REGEX.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_TUPLE</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a TR1 conforming version of <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">tuple</span><span class="special">&gt;</span></code>.
                  This macro is only guaranteed to be defined after including one
                  of the headers from Boost.TR1. Further this macro is now deprecated
                  in favour of BOOST_NO_CXX11_HDR_TUPLE.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_TYPE_TRAITS</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a TR1 conforming version of <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">type_traits</span><span class="special">&gt;</span></code>.
                  This macro is only guaranteed to be defined after including one
                  of the headers from Boost.TR1. Further this macro is now deprecated
                  in favour of BOOST_NO_CXX11_HDR_TYPE_TRAITS.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_UTILITY</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has the TR1 additions to <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">utility</span><span class="special">&gt;</span></code>
                  (tuple interface to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span></code>).
                  This macro is only guaranteed to be defined after including one
                  of the headers from Boost.TR1. Further this macro is now deprecated
                  in favour of BOOST_NO_CXX11_HDR_TUPLE.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_UNORDERED_MAP</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a TR1 conforming version of <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">unordered_map</span><span class="special">&gt;</span></code>.
                  This macro is only guaranteed to be defined after including one
                  of the headers from Boost.TR1. Further this macro is now deprecated
                  in favour of BOOST_NO_CXX11_HDR_UNORDERED_MAP.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_UNORDERED_SET</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The library has a TR1 conforming version of <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">unordered_set</span><span class="special">&gt;</span></code>.
                  This macro is only guaranteed to be defined after including one
                  of the headers from Boost.TR1. Further this macro is now deprecated
                  in favour of BOOST_NO_CXX11_HDR_UNORDERED_SET.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  Implies all the other <code class="computeroutput"><span class="identifier">BOOST_HAS_TR1_</span><span class="special">*</span></code> macros should be set.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_THREADS</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform, Compiler
                </p>
              </td>
<td>
                <p>
                  Defined if the compiler, in its current translation mode, supports
                  multiple threads of execution.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_TWO_ARG_USE_FACET</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  The standard library lacks a conforming std::use_facet, but has
                  a two argument version that does the job. This is primarily for
                  the Rogue Wave std lib.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_UNISTD_H</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The Platform provides <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">unistd</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_WINTHREADS</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  The platform supports MS Windows style threads.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_MSVC_STD_ITERATOR</span></code>
                </p>
              </td>
<td>
                <p>
                  Standard library
                </p>
              </td>
<td>
                <p>
                  Microsoft's broken version of <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator</span></code>
                  is being used. This implies that <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator</span></code>
                  takes no more than two template parameters.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_MSVC6_MEMBER_TEMPLATES</span></code>
                </p>
              </td>
<td>
                <p>
                  Compiler
                </p>
              </td>
<td>
                <p>
                  Microsoft Visual C++ 6.0 has enough member template idiosyncrasies
                  (being polite) that <code class="computeroutput"><span class="identifier">BOOST_NO_MEMBER_TEMPLATES</span></code>
                  is defined for this compiler. <code class="computeroutput"><span class="identifier">BOOST_MSVC6_MEMBER_TEMPLATES</span></code>
                  is defined to allow compiler specific workarounds. This macro gets
                  defined automatically if <code class="computeroutput"><span class="identifier">BOOST_NO_MEMBER_TEMPLATES</span></code>
                  is not defined - in other words this is treated as a strict subset
                  of the features required by the standard.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_STDINT_H</span></code>
                </p>
              </td>
<td>
                <p>
                  Platform
                </p>
              </td>
<td>
                <p>
                  There are no 1998 C++ Standard headers <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">stdint</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>
                  or <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">cstdint</span><span class="special">&gt;</span></code>, although the 1999 C Standard
                  does include <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">stdint</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>.
                  If <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">stdint</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code> is present, <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">stdint</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>
                  can make good use of it, so a flag is supplied (signalling presence;
                  thus the default is not present, conforming to the current C++
                  standard).
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported" title="Macros that describe C++11 features not supported">Macros
      that describe C++11 features not supported</a>
</h3></div></div></div>
<p>
        The following macros describe features in the 2011 ISO C++ standard, formerly
        known as C++0x, that are not yet supported by a particular compiler or library.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Macro
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_ADDRESSOF</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library header &lt;memory&gt; has no working std::addressof.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_ALIGNAS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the <code class="computeroutput"><span class="keyword">alignas</span></code>
                  keyword.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_ALIGNOF</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the <code class="computeroutput"><span class="keyword">alignof</span></code>
                  keyword.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_ALLOCATOR</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide a C++11 version of <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator</span></code> in &lt;memory&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_ATOMIC_SMART_PTR</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library &lt;memory&gt; does not support atomic smart
                  pointer operations.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_AUTO_DECLARATIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support type deduction for variables declared
                  with the <code class="computeroutput"><span class="keyword">auto</span></code> keyword
                  (<code class="computeroutput"><span class="keyword">auto</span> <span class="identifier">var</span>
                  <span class="special">=</span> <span class="special">...;</span></code>).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_AUTO_MULTIDECLARATIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support type deduction for multiple variables
                  declared with the <code class="computeroutput"><span class="keyword">auto</span></code>
                  keyword (<code class="computeroutput"><span class="keyword">auto</span> <span class="identifier">var</span>
                  <span class="special">=</span> <span class="special">...,</span>
                  <span class="special">*</span><span class="identifier">ptr</span>
                  <span class="special">=</span> <span class="special">...;</span></code>).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_CHAR16_T</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support type <code class="computeroutput"><span class="keyword">char16_t</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_CHAR32_T</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support type <code class="computeroutput"><span class="keyword">char32_t</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_CONSTEXPR</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support <code class="computeroutput"><span class="keyword">constexpr</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_DECLTYPE</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support <code class="computeroutput"><span class="keyword">decltype</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_DECLTYPE_N3276</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the extension to <code class="computeroutput"><span class="keyword">decltype</span></code>
                  described in <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2011/n3276.pdf" target="_top">N3276</a>,
                  accepted in Madrid, March 2011.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_DELETED_FUNCTIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support deleted (<code class="computeroutput"><span class="special">=</span>
                  <span class="keyword">delete</span></code>) functions.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_DEFAULTED_FUNCTIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support defaulted (<code class="computeroutput"><span class="special">=</span>
                  <span class="keyword">default</span></code>) functions.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_DEFAULTED_MOVES</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support defaulted move constructor or assignment.
                  Other defaulted functions may still be supported.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_EXPLICIT_CONVERSION_OPERATORS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support explicit conversion operators (<code class="computeroutput"><span class="keyword">explicit</span> <span class="keyword">operator</span>
                  <span class="identifier">T</span><span class="special">()</span></code>).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_EXTERN_TEMPLATE</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support explicit instantiation forward declarations
                  for templates (<code class="computeroutput"><span class="keyword">extern</span> <span class="keyword">template</span> <span class="special">...</span></code>).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_FINAL</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the C++ class-virt-specifier final.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_FIXED_LENGTH_VARIADIC_TEMPLATE_EXPANSION_PACKS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support expanding a variadic template parameter
                  pack into a template containing one or more fixed arguments
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_FUNCTION_TEMPLATE_DEFAULT_ARGS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support default template arguments for function
                  templates.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_ATOMIC</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;atomic&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_ARRAY</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;array&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_CHRONO</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;chrono&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_CODECVT</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;codecvt&gt;. Note
                  that this header is deprecated post C++17, and therefore the macro
                  may be set as a result of the feature being deliberately removed.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_CONDITION_VARIABLE</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;condition_variable&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_EXCEPTION</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide a C++11 compatible version
                  of &lt;exception&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_FORWARD_LIST</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;forward_list&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_FUNCTIONAL</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide a C++11 compatible version
                  of &lt;functional&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_FUTURE</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;future&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_INITIALIZER_LIST</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;initializer_list&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_MUTEX</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;mutex&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_RANDOM</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;random&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_RATIO</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;ratio&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_REGEX</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;regex&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_SYSTEM_ERROR</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;system_error&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_THREAD</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;thread&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_TUPLE</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;tuple&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_TYPEINDEX</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;typeindex&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_TYPE_TRAITS</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;type_traits&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_UNORDERED_MAP</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;unordered_map&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_UNORDERED_SET</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;unordered_set&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_INLINE_NAMESPACES</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support inline namespaces.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_LAMBDAS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support Lambdas.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_LOCAL_CLASS_TEMPLATE_PARAMETERS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not allow to pass local classes as template parameters
                  (this macro intentionally does not control passing of unnamed types
                  as template parameters, see also <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2008/n2657.htm" target="_top">N2657</a>).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_NON_PUBLIC_DEFAULTED_FUNCTIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support defaulted (<code class="computeroutput"><span class="special">=</span>
                  <span class="keyword">default</span></code>) functions in access
                  control sections other than <code class="computeroutput"><span class="keyword">public</span></code>.
                  Public defaulted functions may still be supported, as indicated
                  by <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_DEFAULTED_FUNCTIONS</span></code>.
                  Some compilers implementing an early draft of the C++11 standard
                  (in particular, incorporating <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/cwg_defects.html#906" target="_top">DR906</a>)
                  are susceptible to this problem.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_NOEXCEPT</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support <code class="computeroutput"><span class="keyword">noexcept</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_NULLPTR</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support <code class="computeroutput"><span class="keyword">nullptr</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_NUMERIC_LIMITS</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">limits</span><span class="special">&gt;</span></code>
                  header does not support the C++11 version of <code class="computeroutput"><span class="identifier">numeric_limits</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_OVERRIDE</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support <code class="computeroutput"><span class="identifier">override</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_POINTER_TRAITS</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide a C++11 version of <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pointer_traits</span></code> in &lt;memory&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_RANGE_BASED_FOR</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support range-based for statements.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_RAW_LITERALS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support raw string literals.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_REF_QUALIFIERS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support ref-qualifiers on member functions
                  as described in <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2439.htm" target="_top">N2439</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_RVALUE_REFERENCES</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support r-value references.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_SCOPED_ENUMS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support scoped enumerations (<code class="computeroutput"><span class="keyword">enum</span> <span class="keyword">class</span></code>).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_SFINAE_EXPR</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support usage of C++11 SFINAE with arbitrary
                  expressions. Use this macro only if you are using all of the features
                  of SFINAE including substitution-failure-on-private-member-access.
                  Otherwise use BOOST_NO_SFINAE_EXPR or BOOST_NO_SFINAE which get
                  defined for fewer compilers.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_SMART_PTR</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library header &lt;memory&gt; has no shared_ptr and
                  unique_ptr.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_STATIC_ASSERT</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support <code class="computeroutput"><span class="keyword">static_assert</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_STD_ALIGN</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library header &lt;memory&gt; has no working std::align.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_STD_UNORDERED</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not support &lt;unordered_map&gt; and
                  &lt;unordered_set&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_TEMPLATE_ALIASES</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support template aliases.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_THREAD_LOCAL</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the <code class="computeroutput"><span class="keyword">thread_local</span></code>
                  storage specifier.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_TRAILING_RESULT_TYPES</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the new function result type specification
                  syntax (e.g. <code class="computeroutput"><span class="keyword">auto</span> <span class="identifier">foo</span><span class="special">(</span><span class="identifier">T</span><span class="special">)</span>
                  <span class="special">-&gt;</span> <span class="identifier">T</span><span class="special">;</span></code>).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_UNICODE_LITERALS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support Unicode (<code class="computeroutput"><span class="identifier">u8</span></code>,
                  <code class="computeroutput"><span class="identifier">u</span></code>, <code class="computeroutput"><span class="identifier">U</span></code>) literals.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_UNIFIED_INITIALIZATION_SYNTAX</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the <a href="http://en.wikipedia.org/wiki/C%2B%2B0x#Uniform_initialization" target="_top">C++11
                  Unified Initialization Syntax</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_UNRESTRICTED_UNION</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support an unrestricted union. This is a
                  union that may contain static data as well as user-defined member
                  data with non-trivial special member functions.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_USER_DEFINED_LITERALS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support user defined literals.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_VARIADIC_TEMPLATES</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support variadic templates.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_VARIADIC_MACROS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support variadic macros.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_LONG_LONG</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support <code class="computeroutput"><span class="keyword">long</span>
                  <span class="keyword">long</span></code>.
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.macros_that_allow_use_of_c__11_features_with_c__03_compilers"></a><a name="config_11_for_03"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_allow_use_of_c__11_features_with_c__03_compilers" title="Macros that allow use of C++11 features with C++03 compilers">Macros
      that allow use of C++11 features with C++03 compilers</a>
</h3></div></div></div>
<p>
        The following macros allow use of C++11 features even with compilers that
        do not yet provide compliant C++11 support.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Macro
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_ALIGNMENT</span><span class="special">(</span><span class="identifier">X</span><span class="special">)</span></code>, <code class="computeroutput"><span class="identifier">BOOST_NO_ALIGNMENT</span></code>
                </p>
              </td>
<td>
                <p>
                  Some compilers don't support the <code class="computeroutput"><span class="keyword">alignas</span></code>
                  keyword but provide other means to specify alignment (usually,
                  through compiler-specific attributes). The macro <code class="computeroutput"><span class="identifier">BOOST_ALIGNMENT</span><span class="special">(</span><span class="identifier">X</span><span class="special">)</span></code>
                  will expand to the <code class="computeroutput"><span class="keyword">alignas</span><span class="special">(</span><span class="identifier">X</span><span class="special">)</span></code> keyword if the compiler supports
                  it or to some compiler-specific attribute to achieve the specified
                  alignment. If no such compiler-specific attribute is known then
                  <code class="computeroutput"><span class="identifier">BOOST_ALIGNMENT</span><span class="special">(</span><span class="identifier">X</span><span class="special">)</span></code> will expand to nothing and <code class="computeroutput"><span class="identifier">BOOST_NO_ALIGNMENT</span></code> will be defined.
                  Unlike native <code class="computeroutput"><span class="keyword">alignas</span></code>,
                  <code class="computeroutput"><span class="identifier">X</span></code> must always be
                  a compile-time integer constant. The macro can be used to specify
                  alignment of types and data:
</p>
<pre class="table-programlisting"><span class="keyword">struct</span> <span class="identifier">BOOST_ALIGNMENT</span><span class="special">(</span><span class="number">16</span><span class="special">)</span> <span class="identifier">my_data</span>
<span class="special">{</span>
    <span class="keyword">char</span> <span class="identifier">c</span><span class="special">[</span><span class="number">16</span><span class="special">];</span>
<span class="special">};</span>
<span class="identifier">BOOST_ALIGNMENT</span><span class="special">(</span><span class="number">8</span><span class="special">)</span> <span class="keyword">int</span> <span class="identifier">arr</span><span class="special">[</span><span class="number">32</span><span class="special">];</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_CONSTEXPR</span></code>
                </p>
              </td>
<td>
                <p>
                  Some compilers don't support the use of <code class="computeroutput"><span class="keyword">constexpr</span></code>.
                  This macro expands to nothing on those compilers, and <code class="computeroutput"><span class="keyword">constexpr</span></code> elsewhere. For example,
                  when defining a constexpr function or constructor replace:
</p>
<pre class="table-programlisting"><span class="keyword">constexpr</span> <span class="identifier">tuple</span><span class="special">();</span>
</pre>
<p>
                  with:
</p>
<pre class="table-programlisting"><span class="identifier">BOOST_CONSTEXPR</span> <span class="identifier">tuple</span><span class="special">();</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_CONSTEXPR_OR_CONST</span></code>
                </p>
              </td>
<td>
                <p>
                  Some compilers don't support the use of <code class="computeroutput"><span class="keyword">constexpr</span></code>.
                  This macro expands to <code class="computeroutput"><span class="keyword">const</span></code>
                  on those compilers, and <code class="computeroutput"><span class="keyword">constexpr</span></code>
                  elsewhere. For example, when defining const expr variables replace:
</p>
<pre class="table-programlisting"><span class="keyword">static</span> <span class="keyword">constexpr</span> <span class="identifier">UIntType</span> <span class="identifier">xor_mask</span> <span class="special">=</span> <span class="identifier">a</span><span class="special">;</span>
</pre>
<p>
                  with:
</p>
<pre class="table-programlisting"><span class="keyword">static</span> <span class="identifier">BOOST_CONSTEXPR_OR_CONST</span> <span class="identifier">UIntType</span> <span class="identifier">xor_mask</span> <span class="special">=</span> <span class="identifier">a</span><span class="special">;</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_OVERRIDE</span></code>
                </p>
              </td>
<td>
                <p>
                  If <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_OVERRIDE</span></code>
                  is not defined (i.e. C++11 compliant compilers), expands to <code class="computeroutput"><span class="identifier">override</span></code> keyword, otherwise expands
                  to nothing.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_STATIC_CONSTEXPR</span></code>
                </p>
              </td>
<td>
                <p>
                  This is a shortcut for <code class="computeroutput"><span class="keyword">static</span>
                  <span class="identifier">BOOST_CONSTEXPR_OR_CONST</span></code>.
                  For example, when defining const expr variables replace:
</p>
<pre class="table-programlisting"><span class="keyword">static</span> <span class="keyword">constexpr</span> <span class="identifier">UIntType</span> <span class="identifier">xor_mask</span> <span class="special">=</span> <span class="identifier">a</span><span class="special">;</span>
</pre>
<p>
                  with:
</p>
<pre class="table-programlisting"><span class="identifier">BOOST_STATIC_CONSTEXPR</span> <span class="identifier">UIntType</span> <span class="identifier">xor_mask</span> <span class="special">=</span> <span class="identifier">a</span><span class="special">;</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_DEFAULTED_FUNCTION</span><span class="special">(</span><span class="identifier">fun</span><span class="special">,</span> <span class="identifier">body</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  This macro is intended to be used within a class definition in
                  order to declare a default implementation of function <code class="computeroutput"><span class="identifier">fun</span></code>. For the compilers that do
                  not support C++11 defaulted functions the macro will expand into
                  an inline function definition with the <code class="computeroutput"><span class="identifier">body</span></code>
                  implementation. For example:
</p>
<pre class="table-programlisting"><span class="keyword">struct</span> <span class="identifier">my_struct</span>
<span class="special">{</span>
    <span class="identifier">BOOST_DEFAULTED_FUNCTION</span><span class="special">(</span><span class="identifier">my_struct</span><span class="special">(),</span> <span class="special">{})</span>
<span class="special">};</span>
</pre>
<p>
                  is equivalent to:
</p>
<pre class="table-programlisting"><span class="keyword">struct</span> <span class="identifier">my_struct</span>
<span class="special">{</span>
    <span class="identifier">my_struct</span><span class="special">()</span> <span class="special">=</span> <span class="keyword">default</span><span class="special">;</span>
<span class="special">};</span>
</pre>
<p>
                  or:
</p>
<pre class="table-programlisting"><span class="keyword">struct</span> <span class="identifier">my_struct</span>
<span class="special">{</span>
    <span class="identifier">my_struct</span><span class="special">()</span> <span class="special">{}</span>
<span class="special">};</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_DELETED_FUNCTION</span><span class="special">(</span><span class="identifier">fun</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  This macro is intended to be used within a class definition in
                  order to declare a deleted function <code class="computeroutput"><span class="identifier">fun</span></code>.
                  For the compilers that do not support C++11 deleted functions the
                  macro will expand into a private function declaration with no definition.
                  Since the macro may change the access mode, it is recommended to
                  use this macro at the end of the class definition. For example:
</p>
<pre class="table-programlisting"><span class="keyword">struct</span> <span class="identifier">noncopyable</span>
<span class="special">{</span>
    <span class="identifier">BOOST_DELETED_FUNCTION</span><span class="special">(</span><span class="identifier">noncopyable</span><span class="special">(</span><span class="identifier">noncopyable</span> <span class="keyword">const</span><span class="special">&amp;))</span>
    <span class="identifier">BOOST_DELETED_FUNCTION</span><span class="special">(</span><span class="identifier">noncopyable</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=</span> <span class="special">(</span><span class="identifier">noncopyable</span> <span class="keyword">const</span><span class="special">&amp;))</span>
<span class="special">};</span>
</pre>
<p>
                  is equivalent to:
</p>
<pre class="table-programlisting"><span class="keyword">struct</span> <span class="identifier">noncopyable</span>
<span class="special">{</span>
    <span class="identifier">noncopyable</span><span class="special">(</span><span class="identifier">noncopyable</span> <span class="keyword">const</span><span class="special">&amp;)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>
    <span class="identifier">noncopyable</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=</span> <span class="special">(</span><span class="identifier">noncopyable</span> <span class="keyword">const</span><span class="special">&amp;)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>
<span class="special">};</span>
</pre>
<p>
                  or:
</p>
<pre class="table-programlisting"><span class="keyword">struct</span> <span class="identifier">noncopyable</span>
<span class="special">{</span>
<span class="keyword">private</span><span class="special">:</span>
    <span class="identifier">noncopyable</span><span class="special">(</span><span class="identifier">noncopyable</span> <span class="keyword">const</span><span class="special">&amp;);</span>
    <span class="identifier">noncopyable</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=</span> <span class="special">(</span><span class="identifier">noncopyable</span> <span class="keyword">const</span><span class="special">&amp;);</span>
<span class="special">};</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
</p>
<pre class="table-programlisting"><span class="identifier">BOOST_NOEXCEPT</span>
<span class="identifier">BOOST_NOEXCEPT_OR_NOTHROW</span>
<span class="identifier">BOOST_NOEXCEPT_IF</span><span class="special">(</span><span class="identifier">Predicate</span><span class="special">)</span>
<span class="identifier">BOOST_NOEXCEPT_EXPR</span><span class="special">(</span><span class="identifier">Expression</span><span class="special">)</span>
</pre>
<p>
                </p>
              </td>
<td>
                <p>
                  If <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_NOEXCEPT</span></code>
                  is defined (i.e. C++03 compliant compilers) these macros are defined
                  as:
                </p>
                <div class="blockquote"><blockquote class="blockquote">
<p>
</p>
<pre class="table-programlisting"><span class="preprocessor">#define</span> <span class="identifier">BOOST_NOEXCEPT</span>
<span class="preprocessor">#define</span> <span class="identifier">BOOST_NOEXCEPT_OR_NOTHROW</span> <span class="keyword">throw</span><span class="special">()</span>
<span class="preprocessor">#define</span> <span class="identifier">BOOST_NOEXCEPT_IF</span><span class="special">(</span><span class="identifier">Predicate</span><span class="special">)</span>
<span class="preprocessor">#define</span> <span class="identifier">BOOST_NOEXCEPT_EXPR</span><span class="special">(</span><span class="identifier">Expression</span><span class="special">)</span> <span class="keyword">false</span>
</pre>
<p>
                  </p>
</blockquote></div>
                <p>
                  If <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_NOEXCEPT</span></code>
                  is not defined (i.e. C++11 compliant compilers) they are defined
                  as:
                </p>
                <div class="blockquote"><blockquote class="blockquote">
<p>
</p>
<pre class="table-programlisting"><span class="preprocessor">#define</span> <span class="identifier">BOOST_NOEXCEPT</span> <span class="keyword">noexcept</span>
<span class="preprocessor">#define</span> <span class="identifier">BOOST_NOEXCEPT_OR_NOTHROW</span> <span class="keyword">noexcept</span>
<span class="preprocessor">#define</span> <span class="identifier">BOOST_NOEXCEPT_IF</span><span class="special">(</span><span class="identifier">Predicate</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">((</span><span class="identifier">Predicate</span><span class="special">))</span>
<span class="preprocessor">#define</span> <span class="identifier">BOOST_NOEXCEPT_EXPR</span><span class="special">(</span><span class="identifier">Expression</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">((</span><span class="identifier">Expression</span><span class="special">))</span>
</pre>
<p>
                  </p>
</blockquote></div>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_FINAL</span></code>
                </p>
              </td>
<td>
                <p>
                  If <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_FINAL</span></code>
                  is not defined (i.e. C++11 compliant compilers), expands to <code class="computeroutput"><span class="identifier">final</span></code> keyword, otherwise expands
                  to nothing.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_MSVC_ENABLE_2012_NOV_CTP</span></code>
                </p>
              </td>
<td>
                <p>
                  For Microsoft Visual C++ 2012, enable the C++11 features supplied
                  by the November 2012 Community Technology Preview. These features
                  are not automatically enabled because the CTP is non-supported
                  alpha code that is not recommended for production use. This macro
                  must be defined before including any Boost headers, and must be
                  defined for all translation units in the program, including Boost
                  library builds. This macro will no longer have any effect once
                  an official Microsoft release supports the CTP features.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NULLPTR</span></code>
                </p>
              </td>
<td>
                <p>
                  If <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_NULLPTR</span></code>
                  is not defined (i.e. C++11 compliant compilers), expands to <code class="computeroutput"><span class="keyword">nullptr</span></code>, otherwise expands to <code class="computeroutput"><span class="number">0</span></code>.
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.macros_that_describe_c__14_features_not_supported"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__14_features_not_supported" title="Macros that describe C++14 features not supported">Macros
      that describe C++14 features not supported</a>
</h3></div></div></div>
<p>
        The following macros describe features in the 2014 ISO C++ standard, formerly
        known as C++0y, that are not yet supported by a particular compiler or library.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Macro
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX14_AGGREGATE_NSDMI</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support member initializer for aggregates
                  as in the following example:
                </p>
                <div class="blockquote"><blockquote class="blockquote">
<p>
</p>
<pre class="table-programlisting"><span class="keyword">struct</span> <span class="identifier">Foo</span>
<span class="special">{</span>
  <span class="keyword">int</span> <span class="identifier">x</span><span class="special">,</span> <span class="identifier">y</span> <span class="special">=</span> <span class="number">42</span><span class="special">;</span>
<span class="special">};</span>

<span class="identifier">Foo</span> <span class="identifier">foo</span> <span class="special">=</span> <span class="special">{</span> <span class="number">0</span> <span class="special">};</span>
</pre>
<p>
                  </p>
</blockquote></div>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX14_BINARY_LITERALS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not binary literals (e.g. <code class="computeroutput"><span class="number">0</span><span class="identifier">b1010</span></code>).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX14_CONSTEXPR</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support relaxed <code class="computeroutput"><span class="keyword">constexpr</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX14_DECLTYPE_AUTO</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support <code class="computeroutput"><span class="keyword">decltype</span><span class="special">(</span><span class="keyword">auto</span><span class="special">)</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX14_DIGIT_SEPARATORS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support digit separators (e.g. <code class="computeroutput"><span class="number">1</span><span class="char">'000'</span><span class="number">000</span></code>).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX14_STD_EXCHANGE</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">exchange</span><span class="special">()</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX14_GENERIC_LAMBDAS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support generic lambda (e.g. <code class="computeroutput"><span class="special">[](</span><span class="keyword">auto</span>
                  <span class="identifier">v</span><span class="special">){</span>
                  <span class="special">}</span></code>).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX14_HDR_SHARED_MUTEX</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library does not provide header &lt;shared_mutex&gt;.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX14_INITIALIZED_LAMBDA_CAPTURES</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support initialized lambda capture (e.g.
                  <code class="computeroutput"><span class="special">[</span><span class="identifier">foo</span>
                  <span class="special">=</span> <span class="number">42</span><span class="special">]{</span> <span class="special">}</span></code>).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX14_RETURN_TYPE_DEDUCTION</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support return type deduction for normal
                  functions (e.g. <code class="computeroutput"><span class="keyword">auto</span> <span class="identifier">f</span><span class="special">()</span>
                  <span class="special">{</span> <span class="keyword">return</span>
                  <span class="identifier">val</span><span class="special">;</span>
                  <span class="special">}</span></code>).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX14_VARIABLE_TEMPLATES</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support variable template (e.g. <code class="computeroutput"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">T</span>
                  <span class="identifier">kibi</span> <span class="special">=</span>
                  <span class="identifier">T</span><span class="special">(</span><span class="number">1024</span><span class="special">);</span></code>).
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.macros_that_allow_use_of_c__14_features_with_c__11_or_earlier_compilers"></a><a name="config_14_for_11"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_allow_use_of_c__14_features_with_c__11_or_earlier_compilers" title="Macros that allow use of C++14 features with C++11 or earlier compilers">Macros
      that allow use of C++14 features with C++11 or earlier compilers</a>
</h3></div></div></div>
<p>
        The following macros allow use of C++14 features even with compilers that
        do not yet provide compliant C++14 support.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Macro
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_CXX14_CONSTEXPR</span></code>
                </p>
              </td>
<td>
                <p>
                  This macro works similar to <code class="computeroutput"><span class="identifier">BOOST_CONSTEXPR</span></code>,
                  but expands to <code class="computeroutput"><span class="keyword">constexpr</span></code>
                  only if the C++14 "relaxed" <code class="computeroutput"><span class="keyword">constexpr</span></code>
                  is available.
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.macros_that_describe_c__17_features_not_supported"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__17_features_not_supported" title="Macros that describe C++17 features not supported">Macros
      that describe C++17 features not supported</a>
</h3></div></div></div>
<p>
        The following macros describe features in the 2017 ISO C++ standard, formerly
        known as C++1z, that are not yet supported by a particular compiler or library.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Macro
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_HDR_ANY</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">any</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_HDR_CHARCONV</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">charconv</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_HDR_EXECUTION</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">execution</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_HDR_FILESYSTEM</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">filesystem</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_HDR_MEMORY_RESOURCE</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">memory_resource</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_HDR_OPTIONAL</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">optional</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_HDR_STRING_VIEW</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">string_view</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_HDR_VARIANT</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">variant</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_STD_APPLY</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">apply</span><span class="special">()</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_STD_INVOKE</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">invoke</span><span class="special">()</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_ITERATOR_TRAITS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support SFINAE-friendly <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator_traits</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_IF_CONSTEXPR</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support <code class="computeroutput"><span class="keyword">if</span>
                  <span class="keyword">constexpr</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_INLINE_VARIABLES</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support <code class="computeroutput"><span class="keyword">inline</span></code>
                  variables.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_DEDUCTION_GUIDES</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support class template argument deduction
                  (CTAD) guides.
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.macros_that_allow_use_of_c__17_features_with_c__14_or_earlier_compilers"></a><a name="config_17_for_14"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_allow_use_of_c__17_features_with_c__14_or_earlier_compilers" title="Macros that allow use of C++17 features with C++14 or earlier compilers">Macros
      that allow use of C++17 features with C++14 or earlier compilers</a>
</h3></div></div></div>
<p>
        The following macros allow use of C++17 features even with compilers that
        do not yet provide compliant C++17 support.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Macro
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_INLINE_VARIABLE</span></code>
                </p>
              </td>
<td>
                <p>
                  This macro expands to <code class="computeroutput"><span class="keyword">inline</span></code>
                  on compilers that support C++17 inline variables and to nothing
                  otherwise. Users may need to check for <code class="computeroutput"><span class="identifier">BOOST_NO_CXX17_INLINE_VARIABLES</span></code>
                  for further adjustments to the code.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_IF_CONSTEXPR</span></code>
                </p>
              </td>
<td>
                <p>
                  Expands to <code class="computeroutput"><span class="keyword">if</span> <span class="keyword">constexpr</span></code>
                  when supported, or <code class="computeroutput"><span class="keyword">if</span></code>
                  otherwise.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_INLINE_CONSTEXPR</span></code>
                </p>
              </td>
<td>
                <p>
                  This is a shortcut for <code class="computeroutput"><span class="identifier">BOOST_INLINE_VARIABLE</span>
                  <span class="identifier">BOOST_CONSTEXPR_OR_CONST</span></code>.
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.macros_that_describe_c__20_features_not_supported"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__20_features_not_supported" title="Macros that describe C++20 features not supported">Macros
      that describe C++20 features not supported</a>
</h3></div></div></div>
<p>
        The following macros describe features in the 2020 ISO C++ standard, formerly
        known as C++2a, that are not yet supported by a particular compiler or library.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Macro
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_BARRIER</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">barrier</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_FORMAT</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">format</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_SOURCE_LOCATION</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">source_location</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_BIT</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">bit</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_LATCH</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">latch</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_SPAN</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">span</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_COMPARE</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">compare</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_NUMBERS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">numbers</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_STOP_TOKEN</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">stop_token</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_CONCEPTS</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">concepts</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_RANGES</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">ranges</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_SYNCSTREAM</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">syncstream</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_COROUTINE</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">coroutine</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_SEMAPHORE</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">semaphore</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX20_HDR_VERSION</span></code>
                </p>
              </td>
<td>
                <p>
                  The compiler does not support the header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">version</span><span class="special">&gt;</span></code>.
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.macros_that_describe_features_that_have_been_removed_from_the_standard_"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_features_that_have_been_removed_from_the_standard_" title="Macros that describe features that have been removed from the standard.">Macros
      that describe features that have been removed from the standard.</a>
</h3></div></div></div>
<p>
        The following macros describe features which were required by one version
        of the standard, but have been removed by later versions.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Macro
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX98_RANDOM_SHUFFLE</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library no longer supports <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">random_shuffle</span><span class="special">()</span></code>. It was deprecated in C++11 and
                  is removed from C++14.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_AUTO_PTR</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library no longer supports <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">auto_ptr</span></code>.
                  It was deprecated in C++11 and is removed from C++14.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX98_FUNCTION_BASE</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library no longer supports <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">unary_function</span></code>
                  and <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">binary_function</span></code>. They were deprecated
                  in C++11 and is removed from C++14.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX98_BINDERS</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library no longer supports <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">bind1st</span></code>,
                  <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">bind2nd</span></code>, <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">ptr_fun</span></code>
                  and <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">mem_fun</span></code>. They were deprecated
                  in C++11 and is removed from C++14.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_ATOMIC_SMART_PTR</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library no longer supports atomic overloads for <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">shared_ptr</span></code>: use <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">atomic</span><span class="special">&lt;</span><span class="identifier">shared_ptr</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;&gt;</span></code>
                  instead.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_CODECVT</span></code>
                </p>
              </td>
<td>
                <p>
                  The standard library no longer supports <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">codecvt</span><span class="special">&gt;</span></code>,
                  there is as yet no replacement.
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.boost_helper_macros"></a><a name="config_helpers"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.boost_helper_macros" title="Boost Helper Macros">Boost
      Helper Macros</a>
</h3></div></div></div>
<p>
        The following macros are either simple helpers, or macros that provide workarounds
        for compiler/standard library defects.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Macro
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_WORKAROUND</span></code>
                </p>
              </td>
<td>
                <p>
                  This macro is used where a compiler specific workaround is required
                  that is not otherwise described by one of the other Boost.Config
                  macros. To use the macro you must first
</p>
<pre class="table-programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">workaround</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
</pre>
<p>
                  usage is then:
</p>
<pre class="table-programlisting"><span class="preprocessor">#if</span> <span class="identifier">BOOST_WORKAROUND</span><span class="special">(</span><span class="identifier">MACRONAME</span><span class="special">,</span> <span class="identifier">CONDITION</span><span class="special">)</span>
   <span class="comment">// workaround code goes here...</span>
<span class="preprocessor">#else</span>
   <span class="comment">// Standard conforming code goes here...</span>
<span class="preprocessor">#endif</span>
</pre>
<p>
                  where <code class="computeroutput"><span class="identifier">MACRONAME</span></code>
                  is a macro that usually describes the version number to be tested
                  against, and <code class="computeroutput"><span class="identifier">CONDITION</span></code>
                  is a comparison operator followed by a value. For example <code class="computeroutput"><span class="identifier">BOOST_WORKAROUND</span><span class="special">(</span><span class="identifier">BOOST_INTEL</span><span class="special">,</span>
                  <span class="special">&lt;=</span> <span class="number">1010</span><span class="special">)</span></code> would evaluate to <code class="computeroutput"><span class="number">1</span></code> for Intel C++ 10.1 and earlier.
                </p>
                <p>
                  The macro can also be used with <code class="computeroutput"><span class="identifier">BOOST_TESTED_AT</span></code>
                  if all current compiler versions exhibit the issue, but the issue
                  is expected to be fixed at some later point.
                </p>
                <p>
                  For example <code class="computeroutput"><span class="identifier">BOOST_WORKAROUND</span><span class="special">(</span><span class="identifier">__BORLANDC__</span><span class="special">,</span> <span class="identifier">BOOST_TESTED_AT</span><span class="special">(</span><span class="number">0x590</span><span class="special">))</span></code> would normally evaluate to <code class="computeroutput"><span class="number">1</span></code> for all values of <code class="computeroutput"><span class="identifier">__BORLANDC__</span></code> <span class="emphasis"><em>unless</em></span>
                  the macro <code class="computeroutput"><span class="identifier">BOOST_DETECT_OUTDATED_WORKAROUNDS</span></code>
                  is defined, in which case evaluates to <code class="computeroutput"><span class="special">(</span><span class="identifier">__BORLANDC__</span> <span class="special">&lt;=</span>
                  <span class="number">0x590</span><span class="special">)</span></code>.
                </p>
                <p>
                  <span class="bold"><strong>Note</strong></span>: the ultimate source of documentation
                  for this macro is in <a href="../../../../../boost/config/workaround.hpp" target="_top">boost/config/workaround.hpp</a>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_PREVENT_MACRO_SUBSTITUTION</span></code>
                </p>
              </td>
<td>
                <p>
                  Sometimes you have a function name with the same name as a C macro,
                  for example "min" and "max" member functions,
                  in which case one can prevent the function being expanded as a
                  macro using:
</p>
<pre class="table-programlisting"><span class="identifier">someclass</span><span class="special">.</span><span class="identifier">min</span> <span class="identifier">BOOST_PREVENT_MACRO_SUBSTITUTION</span><span class="special">(</span><span class="identifier">arg1</span><span class="special">,</span> <span class="identifier">arg2</span><span class="special">);</span>
</pre>
<p>
                  The following also works in most, but not all, contexts:
</p>
<pre class="table-programlisting"><span class="special">(</span><span class="identifier">someclass</span><span class="special">.</span><span class="identifier">max</span><span class="special">)(</span><span class="identifier">arg1</span><span class="special">,</span> <span class="identifier">arg2</span><span class="special">);</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_DEDUCED_TYPENAME</span></code>
                </p>
              </td>
<td>
                <p>
                  Some compilers don't support the use of typename for dependent
                  types in deduced contexts. This macro expands to nothing on those
                  compilers, and typename elsewhere. For example, replace: <code class="computeroutput"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">void</span>
                  <span class="identifier">f</span><span class="special">(</span><span class="identifier">T</span><span class="special">,</span>
                  <span class="keyword">typename</span> <span class="identifier">T</span><span class="special">::</span><span class="identifier">type</span><span class="special">);</span></code> with: <code class="computeroutput"><span class="keyword">template</span>
                  <span class="special">&lt;</span><span class="keyword">class</span>
                  <span class="identifier">T</span><span class="special">&gt;</span>
                  <span class="keyword">void</span> <span class="identifier">f</span><span class="special">(</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">BOOST_DEDUCED_TYPENAME</span>
                  <span class="identifier">T</span><span class="special">::</span><span class="identifier">type</span><span class="special">);</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HASH_MAP_HEADER</span></code>
                </p>
              </td>
<td>
                <p>
                  The header to include to get the SGI <code class="computeroutput"><span class="identifier">hash_map</span></code>
                  class. This macro is only available if <code class="computeroutput"><span class="identifier">BOOST_HAS_HASH</span></code>
                  is defined.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HASH_SET_HEADER</span></code>
                </p>
              </td>
<td>
                <p>
                  The header to include to get the SGI <code class="computeroutput"><span class="identifier">hash_set</span></code>
                  class. This macro is only available if <code class="computeroutput"><span class="identifier">BOOST_HAS_HASH</span></code>
                  is defined.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_SLIST_HEADER</span></code>
                </p>
              </td>
<td>
                <p>
                  The header to include to get the SGI <code class="computeroutput"><span class="identifier">slist</span></code>
                  class. This macro is only available if <code class="computeroutput"><span class="identifier">BOOST_HAS_SLIST</span></code>
                  is defined.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_STD_EXTENSION_NAMESPACE</span></code>
                </p>
              </td>
<td>
                <p>
                  The namespace used for std library extensions (hashtable classes
                  etc).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_STATIC_CONSTANT</span><span class="special">(</span><span class="identifier">Type</span><span class="special">,</span> <span class="identifier">assignment</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  On compilers which don't allow in-class initialization of static
                  integral constant members, we must use enums as a workaround if
                  we want the constants to be available at compile-time. This macro
                  gives us a convenient way to declare such constants. For example
                  instead of:
</p>
<pre class="table-programlisting"><span class="keyword">struct</span> <span class="identifier">foo</span><span class="special">{</span>
   <span class="keyword">static</span> <span class="keyword">const</span> <span class="keyword">int</span> <span class="identifier">value</span> <span class="special">=</span> <span class="number">2</span><span class="special">;</span>
<span class="special">};</span>
</pre>
<p>
                  use:
</p>
<pre class="table-programlisting"><span class="keyword">struct</span> <span class="identifier">foo</span><span class="special">{</span>
   <span class="identifier">BOOST_STATIC_CONSTANT</span><span class="special">(</span><span class="keyword">int</span><span class="special">,</span> <span class="identifier">value</span> <span class="special">=</span> <span class="number">2</span><span class="special">);</span>
<span class="special">};</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_UNREACHABLE_RETURN</span><span class="special">(</span><span class="identifier">result</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  Normally evaluates to nothing, but evaluates to return x; if the
                  compiler requires a return, even when it can never be reached.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_FALLTHROUGH</span></code>
                </p>
              </td>
<td>
                <p>
                  The BOOST_FALLTHROUGH macro can be used to annotate implicit fall-through
                  between switch labels:
</p>
<pre class="table-programlisting"><span class="keyword">switch</span> <span class="special">(</span><span class="identifier">x</span><span class="special">)</span> <span class="special">{</span>
<span class="keyword">case</span> <span class="number">40</span><span class="special">:</span>
<span class="keyword">case</span> <span class="number">41</span><span class="special">:</span>
   <span class="keyword">if</span> <span class="special">(</span><span class="identifier">truth_is_out_there</span><span class="special">)</span> <span class="special">{</span>
      <span class="special">++</span><span class="identifier">x</span><span class="special">;</span>
      <span class="identifier">BOOST_FALLTHROUGH</span><span class="special">;</span>  <span class="comment">// Use instead of/along with annotations in</span>
      <span class="comment">// comments.</span>
   <span class="special">}</span> <span class="keyword">else</span> <span class="special">{</span>
     <span class="keyword">return</span> <span class="identifier">x</span><span class="special">;</span>
   <span class="special">}</span>
   <span class="keyword">case</span> <span class="number">42</span><span class="special">:</span>
      <span class="special">...</span>
</pre>
<p>
                  As shown in the example above, the BOOST_FALLTHROUGH macro should
                  be followed by a semicolon. It is designed to mimic control-flow
                  statements like 'break;', so it can be placed in most places where
                  'break;' can, but only if there are no statements on the execution
                  path between it and the next switch label.
                </p>
                <p>
                  When compiled with Clang &gt;3.2 in C++11 mode, the BOOST_FALLTHROUGH
                  macro is expanded to <code class="computeroutput"><span class="special">[[</span><span class="identifier">clang</span><span class="special">::</span><span class="identifier">fallthrough</span><span class="special">]]</span></code>
                  attribute, which is analysed when performing switch labels fall-through
                  diagnostic ('-Wimplicit-fallthrough'). See clang <a href="http://clang.llvm.org/docs/LanguageExtensions.html#clang__fallthrough" target="_top">documentation
                  on language extensions for details.</a>
                </p>
                <p>
                  When used with unsupported compilers, the BOOST_FALLTHROUGH macro
                  has no effect on diagnostics.
                </p>
                <p>
                  In either case this macro has no effect on runtime behavior and
                  performance of code.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_EXPLICIT_TEMPLATE_TYPE</span><span class="special">(</span><span class="identifier">t</span><span class="special">)</span></code>
                </p>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_EXPLICIT_TEMPLATE_NON_TYPE</span><span class="special">(</span><span class="identifier">t</span><span class="special">,</span><span class="identifier">v</span><span class="special">)</span></code>
                </p>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_APPEND_EXPLICIT_TEMPLATE_TYPE</span><span class="special">(</span><span class="identifier">t</span><span class="special">)</span></code>
                </p>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_APPEND_EXPLICIT_TEMPLATE_NON_TYPE</span><span class="special">(</span><span class="identifier">t</span><span class="special">,</span><span class="identifier">v</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  Some compilers silently "fold" different function template
                  instantiations if some of the template parameters don't appear
                  in the function parameter list. For instance:
</p>
<pre class="table-programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">iostream</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">ostream</span><span class="special">&gt;</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">typeinfo</span><span class="special">&gt;</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">int</span> <span class="identifier">n</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">f</span><span class="special">()</span> <span class="special">{</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">n</span> <span class="special">&lt;&lt;</span> <span class="char">' '</span><span class="special">;</span> <span class="special">}</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">g</span><span class="special">()</span> <span class="special">{</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="keyword">typeid</span><span class="special">(</span><span class="identifier">T</span><span class="special">).</span><span class="identifier">name</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="char">' '</span><span class="special">;</span> <span class="special">}</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span> <span class="special">{</span>
  <span class="identifier">f</span><span class="special">&lt;</span><span class="number">1</span><span class="special">&gt;();</span>
  <span class="identifier">f</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;();</span>

  <span class="identifier">g</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;();</span>
  <span class="identifier">g</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;();</span>
<span class="special">}</span>
</pre>
<p>
                  incorrectly outputs <code class="literal">2 2 double double</code> on VC++
                  6. These macros, to be used in the function parameter list, fix
                  the problem without effects on the calling syntax. For instance,
                  in the case above write:
</p>
<pre class="table-programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">int</span> <span class="identifier">n</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">f</span><span class="special">(</span><span class="identifier">BOOST_EXPLICIT_TEMPLATE_NON_TYPE</span><span class="special">(</span><span class="keyword">int</span><span class="special">,</span> <span class="identifier">n</span><span class="special">))</span> <span class="special">{</span> <span class="special">...</span> <span class="special">}</span>

<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">g</span><span class="special">(</span><span class="identifier">BOOST_EXPLICIT_TEMPLATE_TYPE</span><span class="special">(</span><span class="identifier">T</span><span class="special">))</span> <span class="special">{</span> <span class="special">...</span> <span class="special">}</span>
</pre>
<p>
                  Beware that they can declare (for affected compilers) a dummy defaulted
                  parameter, so they
                </p>
                <p>
                  <span class="bold"><strong>a)</strong></span> should be always invoked <span class="bold"><strong>at the end</strong></span> of the parameter list
                </p>
                <p>
                  <span class="bold"><strong>b)</strong></span> can't be used if your function
                  template is multiply declared.
                </p>
                <p>
                  Furthermore, in order to add any needed comma separator, an <code class="computeroutput"><span class="identifier">APPEND_</span><span class="special">*</span></code>
                  version must be used when the macro invocation appears after a
                  normal parameter declaration or after the invocation of another
                  macro of this same group.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_USE_FACET</span><span class="special">(</span><span class="identifier">Type</span><span class="special">,</span> <span class="identifier">loc</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  When the standard library does not have a conforming <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">use_facet</span></code> there are various workarounds
                  available, but they differ from library to library. This macro
                  provides a consistent way to access a locale's facets. For example,
                  replace: <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">use_facet</span><span class="special">&lt;</span><span class="identifier">Type</span><span class="special">&gt;(</span><span class="identifier">loc</span><span class="special">);</span></code>
                  with: <code class="computeroutput"><span class="identifier">BOOST_USE_FACET</span><span class="special">(</span><span class="identifier">Type</span><span class="special">,</span> <span class="identifier">loc</span><span class="special">);</span></code> Note do not add a <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span></code>
                  prefix to the front of <code class="computeroutput"><span class="identifier">BOOST_USE_FACET</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_FACET</span><span class="special">(</span><span class="identifier">Type</span><span class="special">,</span> <span class="identifier">loc</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  When the standard library does not have a comforming <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">has_facet</span></code> there are various workarounds
                  available, but they differ from library to library. This macro
                  provides a consistent way to check a locale's facets. For example,
                  replace: <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">has_facet</span><span class="special">&lt;</span><span class="identifier">Type</span><span class="special">&gt;(</span><span class="identifier">loc</span><span class="special">);</span></code>
                  with: <code class="computeroutput"><span class="identifier">BOOST_HAS_FACET</span><span class="special">(</span><span class="identifier">Type</span><span class="special">,</span> <span class="identifier">loc</span><span class="special">);</span></code> Note do not add a <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span></code>
                  prefix to the front of <code class="computeroutput"><span class="identifier">BOOST_HAS_FACET</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NESTED_TEMPLATE</span></code>
                </p>
              </td>
<td>
                <p>
                  Member templates are supported by some compilers even though they
                  can't use the <code class="computeroutput"><span class="identifier">A</span><span class="special">::</span><span class="keyword">template</span>
                  <span class="identifier">member</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  syntax, as a workaround replace: <code class="computeroutput"><span class="keyword">typedef</span>
                  <span class="keyword">typename</span> <span class="identifier">A</span><span class="special">::</span><span class="keyword">template</span>
                  <span class="identifier">rebind</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span>
                  <span class="identifier">binder</span><span class="special">;</span></code>
                  with: <code class="computeroutput"><span class="keyword">typedef</span> <span class="keyword">typename</span>
                  <span class="identifier">A</span><span class="special">::</span><span class="identifier">BOOST_NESTED_TEMPLATE</span> <span class="identifier">rebind</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span> <span class="identifier">binder</span><span class="special">;</span></code>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_STRINGIZE</span><span class="special">(</span><span class="identifier">X</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  Converts the parameter <code class="computeroutput"><span class="identifier">X</span></code>
                  to a string after macro replacement on <code class="computeroutput"><span class="identifier">X</span></code>
                  has been performed.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_JOIN</span><span class="special">(</span><span class="identifier">X</span><span class="special">,</span><span class="identifier">Y</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  This piece of macro magic joins the two arguments together, even
                  when one of the arguments is itself a macro (see 16.3.1 in C++
                  standard). This is normally used to create a mangled name in combination
                  with a predefined macro such a __LINE__.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_RESTRICT</span></code>
                </p>
              </td>
<td>
                <p>
                  This macro can be used in place of the compiler-specific variant
                  of the C99 <code class="computeroutput"><span class="identifier">restrict</span></code>
                  keyword to notify the compiler that, for the lifetime of the qualified
                  pointer variable, only it and its derivative values will be used
                  to access the object it references. This limits the effect of pointer
                  aliasing and helps optimizers in generating better code. However,
                  if this condition is violated, undefined behavior may occur.
                </p>
                <p>
                  Usage example:
</p>
<pre class="table-programlisting"><span class="keyword">void</span> <span class="identifier">perform_computation</span><span class="special">(</span> <span class="keyword">float</span><span class="special">*</span> <span class="identifier">BOOST_RESTRICT</span> <span class="identifier">in</span><span class="special">,</span> <span class="keyword">float</span><span class="special">*</span> <span class="identifier">BOOST_RESTRICT</span> <span class="identifier">out</span> <span class="special">)</span>
<span class="special">{</span>
  <span class="special">*</span><span class="identifier">out</span> <span class="special">=</span> <span class="special">*</span><span class="identifier">in</span> <span class="special">*</span> <span class="number">0.5f</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_FORCEINLINE</span></code>
                </p>
              </td>
<td>
                <p>
                  This macro can be used in place of the <code class="computeroutput"><span class="keyword">inline</span></code>
                  keyword to instruct the compiler that the function should always
                  be inlined. Overuse of this macro can lead to significant bloat,
                  while good use can increase performance in certain cases, such
                  as computation-intensive code built through generative programming
                  techniques.
                </p>
                <p>
                  Usage example:
</p>
<pre class="table-programlisting"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">BOOST_FORCEINLINE</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">f</span><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">t</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">return</span> <span class="identifier">t</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
                </p>
                <p>
                  Note that use of this macro can lead to cryptic error messages
                  with some compilers. Consider defining it to <code class="computeroutput"><span class="keyword">inline</span></code>
                  before including the Boost.Config header in order to be able to
                  debug errors more easily.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NOINLINE</span></code>
                </p>
              </td>
<td>
                <p>
                  This macro can be used in place of the <code class="computeroutput"><span class="keyword">inline</span></code>
                  keyword to instruct the compiler that the function should never
                  be inlined. One should typically use this macro to mark functions
                  that are unlikely to be called, such as error handling routines.
                </p>
                <p>
                  Usage example:
</p>
<pre class="table-programlisting"><span class="identifier">BOOST_NOINLINE</span> <span class="keyword">void</span> <span class="identifier">handle_error</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">descr</span><span class="special">)</span>
<span class="special">{</span>
    <span class="comment">// ...</span>
<span class="special">}</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NORETURN</span></code>
                </p>
              </td>
<td>
                <p>
                  This macro can be used before the function declaration or definition
                  to instruct the compiler that the function does not return normally
                  (i.e. with a <code class="computeroutput"><span class="keyword">return</span></code>
                  statement or by leaving the function scope, if the function return
                  type is <code class="computeroutput"><span class="keyword">void</span></code>). The
                  macro can be used to mark functions that always throw exceptions
                  or terminate the application. Compilers that support this markup
                  may use this information to specifically organize the code surrounding
                  calls to this function and suppress warnings about missing <code class="computeroutput"><span class="keyword">return</span></code> statements in the functions
                  enclosing such calls.
                </p>
                <p>
                  Usage example:
</p>
<pre class="table-programlisting"><span class="identifier">BOOST_NORETURN</span> <span class="keyword">void</span> <span class="identifier">on_error_occurred</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span><span class="special">*</span> <span class="identifier">descr</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">throw</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">runtime_error</span><span class="special">(</span><span class="identifier">descr</span><span class="special">);</span>
<span class="special">}</span>
</pre>
<p>
                </p>
                <p>
                  If the compiler does not support this markup, <code class="computeroutput"><span class="identifier">BOOST_NORETURN</span></code>
                  is defined empty and an additional macro <code class="computeroutput"><span class="identifier">BOOST_NO_NORETURN</span></code>
                  is defined.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_LIKELY</span><span class="special">(</span><span class="identifier">X</span><span class="special">)</span></code>
                </p>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_UNLIKELY</span><span class="special">(</span><span class="identifier">X</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  These macros communicate to the compiler that the conditional expression
                  <code class="computeroutput"><span class="identifier">X</span></code> is likely or
                  unlikely to yield a positive result. The expression should result
                  in a boolean value. The result of the macro is an integer or boolean
                  value equivalent to the result of <code class="computeroutput"><span class="identifier">X</span></code>.
                </p>
                <p>
                  The macros are intended to be used in branching statements. The
                  additional hint they provide can be used by the compiler to arrange
                  the compiled code of the branches more effectively.
                </p>
                <p>
                  Usage example:
</p>
<pre class="table-programlisting"><span class="keyword">if</span> <span class="special">(</span><span class="identifier">BOOST_UNLIKELY</span><span class="special">(</span><span class="identifier">ptr</span> <span class="special">==</span> <span class="identifier">NULL</span><span class="special">))</span>
  <span class="identifier">handle_error</span><span class="special">(</span><span class="string">"ptr is NULL"</span><span class="special">);</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_ATTRIBUTE_UNUSED</span></code>
                </p>
              </td>
<td>
                <p>
                  Expands to <code class="computeroutput"><span class="identifier">__attribute__</span><span class="special">((</span><span class="identifier">unused</span><span class="special">))</span></code> when this is available - can
                  be used to disable compiler warnings relating to unused types or
                  variables.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_ATTRIBUTE_NODISCARD</span></code>
                </p>
              </td>
<td>
                <p>
                  Expands to <code class="computeroutput"><span class="special">[[</span><span class="identifier">nodiscard</span><span class="special">]]</span></code> when this is available - can
                  be used to create a warning when a type or variable is unused.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_ATTRIBUTE_NO_UNIQUE_ADDRESS</span></code>
                </p>
              </td>
<td>
                <p>
                  Expands to <code class="computeroutput"><span class="special">[[</span><span class="identifier">no_unique_address</span><span class="special">]]</span></code> when this is available - can
                  be used to indicate that a non-static data member need not have
                  a unique address (for example empty classes).
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_MAY_ALIAS</span></code>,
                  <code class="computeroutput"><span class="identifier">BOOST_NO_MAY_ALIAS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_MAY_ALIAS</span></code>
                  expands to a type attribute that can be used to mark types that
                  may alias other types. Pointers or references to such marked types
                  can be used to access objects of other types. If the compiler supports
                  this feature <code class="computeroutput"><span class="identifier">BOOST_NO_MAY_ALIAS</span></code>
                  is not defined. Otherwise <code class="computeroutput"><span class="identifier">BOOST_MAY_ALIAS</span></code>
                  expands to nothing and <code class="computeroutput"><span class="identifier">BOOST_NO_MAY_ALIAS</span></code>
                  is defined.
                </p>
                <p>
                  Usage example:
</p>
<pre class="table-programlisting"><span class="keyword">struct</span> <span class="identifier">BOOST_MAY_ALIAS</span> <span class="identifier">aliasing_struct</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="keyword">unsigned</span> <span class="keyword">int</span> <span class="identifier">BOOST_MAY_ALIAS</span> <span class="identifier">aliasing_uint</span><span class="special">;</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_DEPRECATED</span><span class="special">(</span><span class="identifier">M</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  Expands to an attribute for a symbol that generates warnings when
                  that symbol is used in code. The warnings may contain a message
                  <code class="computeroutput"><span class="identifier">M</span></code>, which must be
                  a string literal. This attribute may be applied to types, functions
                  or objects and is typically used to mark parts of the API as deprecated
                  with a recommendation of replacement.
                </p>
                <p>
                  Example:
</p>
<pre class="table-programlisting"><span class="identifier">BOOST_DEPRECATED</span><span class="special">(</span><span class="string">"Use bar() instead."</span><span class="special">)</span>
<span class="keyword">void</span> <span class="identifier">foo</span><span class="special">();</span>

<span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">T</span> <span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">BOOST_DEPRECATED</span><span class="special">(</span><span class="string">"Use std::unique_ptr instead."</span><span class="special">)</span> <span class="identifier">auto_ptr</span>
<span class="special">{</span>
<span class="special">};</span>

<span class="identifier">BOOST_DEPRECATED</span><span class="special">(</span><span class="string">"Use std::numeric_limits&lt;int&gt;::max() instead."</span><span class="special">)</span>
<span class="keyword">const</span> <span class="keyword">int</span> <span class="identifier">max_int</span> <span class="special">=</span> <span class="number">0x7fffffff</span><span class="special">;</span>
</pre>
<p>
                </p>
                <p>
                  The warnings issued by <code class="computeroutput"><span class="identifier">BOOST_DEPRECATED</span></code>
                  can be suppressed by defining <code class="computeroutput"><span class="identifier">BOOST_ALLOW_DEPRECATED_SYMBOLS</span></code>
                  or <code class="computeroutput"><span class="identifier">BOOST_ALLOW_DEPRECATED</span></code>
                  macros.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_PRAGMA_MESSAGE</span><span class="special">(</span><span class="identifier">M</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined in header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">pragma_message</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>,
                  this macro expands to the equivalent of <code class="computeroutput"><span class="preprocessor">#pragma</span>
                  <span class="identifier">message</span><span class="special">(</span><span class="identifier">M</span><span class="special">)</span></code>.
                  <code class="computeroutput"><span class="identifier">M</span></code> must be a string
                  literal.
                </p>
                <p>
                  Example: <code class="computeroutput"><span class="identifier">BOOST_PRAGMA_MESSAGE</span><span class="special">(</span><span class="string">"This header
                  is deprecated."</span><span class="special">)</span></code>
                </p>
                <p>
                  The messages issued by <code class="computeroutput"><span class="identifier">BOOST_PRAGMA_MESSAGE</span></code>
                  can be suppressed by defining the macro <code class="computeroutput"><span class="identifier">BOOST_DISABLE_PRAGMA_MESSAGE</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HEADER_DEPRECATED</span><span class="special">(</span><span class="identifier">A</span><span class="special">)</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined in header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">header_deprecated</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>,
                  this macro issues the message "This header is deprecated.
                  Use <code class="computeroutput"><span class="identifier">A</span></code> instead."
                  via <code class="computeroutput"><span class="identifier">BOOST_PRAGMA_MESSAGE</span></code>.
                  <code class="computeroutput"><span class="identifier">A</span></code> must be a string
                  literal.
                </p>
                <p>
                  Example: <code class="computeroutput"><span class="identifier">BOOST_HEADER_DEPRECATED</span><span class="special">(</span><span class="string">"&lt;boost/config/workaround.hpp&gt;"</span><span class="special">)</span></code>
                </p>
                <p>
                  The messages issued by <code class="computeroutput"><span class="identifier">BOOST_HEADER_DEPRECATED</span></code>
                  can be suppressed by defining <code class="computeroutput"><span class="identifier">BOOST_ALLOW_DEPRECATED_HEADERS</span></code>
                  or <code class="computeroutput"><span class="identifier">BOOST_ALLOW_DEPRECATED</span></code>
                  macros.
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.boost_informational_macros"></a><a name="config_info_macros"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.boost_informational_macros" title="Boost Informational Macros">Boost
      Informational Macros</a>
</h3></div></div></div>
<p>
        The following macros describe boost features; these are, generally speaking
        the only boost macros that should be tested in user code.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Macro
                </p>
              </th>
<th>
                <p>
                  Header
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_VERSION</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">version</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Describes the boost version number in XYYYZZ format such that:
                  <code class="computeroutput"><span class="special">(</span><span class="identifier">BOOST_VERSION</span>
                  <span class="special">%</span> <span class="number">100</span><span class="special">)</span></code> is the sub-minor version, <code class="computeroutput"><span class="special">((</span><span class="identifier">BOOST_VERSION</span>
                  <span class="special">/</span> <span class="number">100</span><span class="special">)</span> <span class="special">%</span> <span class="number">1000</span><span class="special">)</span></code>
                  is the minor version, and <code class="computeroutput"><span class="special">(</span><span class="identifier">BOOST_VERSION</span> <span class="special">/</span>
                  <span class="number">100000</span><span class="special">)</span></code>
                  is the major version.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_INT64_T</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">cstdint</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code> <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">stdint</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined if there are no 64-bit integral types: <code class="computeroutput"><span class="identifier">int64_t</span></code>,
                  <code class="computeroutput"><span class="identifier">uint64_t</span></code> etc.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_INTEGRAL_INT64_T</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">cstdint</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code> <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">stdint</span><span class="special">.</span><span class="identifier">h</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined if <code class="computeroutput"><span class="identifier">int64_t</span></code>
                  as defined by <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">cstdint</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                  is not usable in integral constant expressions.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_MSVC</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined if the compiler is really Microsoft Visual C++, as opposed
                  to one of the many other compilers that also define <code class="computeroutput"><span class="identifier">_MSC_VER</span></code>. Has the same value
                  as _MSC_VER.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_MSVC_FULL_VER</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined to a normalised 9 digit version of _MSC_FULL_VER (which
                  sometimes only has 8 digits), the macro has the form VVMMPPPPP
                  where VV is the major version number, MM is the minor version number,
                  and PPPPP is the compiler build number.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_GCC</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined if the compiler is really GCC, as opposed to one of the
                  many other compilers that also define <code class="computeroutput"><span class="identifier">__GNUC__</span></code>.
                  Has the value: <code class="computeroutput"><span class="identifier">__GNUC__</span>
                  <span class="special">*</span> <span class="number">10000</span>
                  <span class="special">+</span> <span class="identifier">__GNUC_MINOR__</span>
                  <span class="special">*</span> <span class="number">100</span>
                  <span class="special">+</span> <span class="identifier">__GNUC_PATCHLEVEL__</span></code>.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_INTEL</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined if the compiler is an Intel compiler, takes the same value
                  as the compiler version macro.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_CLANG</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined to 1 if the compiler is the Clang compiler.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_CLANG_VERSION</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined to the version of the Clang compiler, usually <code class="computeroutput"><span class="identifier">__clang_major__</span> <span class="special">*</span>
                  <span class="number">10000</span> <span class="special">+</span>
                  <span class="identifier">__clang_minor__</span> <span class="special">*</span>
                  <span class="number">100</span> <span class="special">+</span>
                  <span class="identifier">__clang_patchlevel__</span></code>.
                  On Apple Clang, has a best-effort value reflecting the upstream
                  version, rather than the Apple version.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_BORLANDC</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined to the value of __BORLANDC__ if the compiler is the Embarcadero
                  non-clang based compiler.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_CODEGEARC</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined to the value of __CODEGEARC__ if the compiler is the Embarcadero
                  non-clang based compiler.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_EMBTC</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined to the value of __CODEGEARC__ if the compiler is the Embarcadero
                  clang based compiler.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_WINDOWS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined if the Windows platform API is available.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_DINKUMWARE_STDLIB</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined if the dinkumware standard library is in use, takes the
                  same value as the Dinkumware library version macro <code class="computeroutput"><span class="identifier">_CPPLIB_VER</span></code> if defined, otherwise
                  1.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_MSSTL_VERSION</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined if the Microsoft Visual C++ standard library is in use.
                  Has the value of <code class="computeroutput"><span class="identifier">_MSVC_STL_VERSION</span></code>
                  when that is defined, and a synthesized value of the same format
                  otherwise. Example values are 143 for VS2022/msvc-14.3, 142 for
                  VS2019/msvc-14.2, 141 for VS2017/msvc-14.1, 140 for VS2015/msvc-14.0,
                  120 for VS2013/msvc-12.0, and so on.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_COMPILER</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined as a string describing the name and version number of the
                  compiler in use. Mainly for debugging the configuration.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_STDLIB</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined as a string describing the name and version number of the
                  standard library in use. Mainly for debugging the configuration.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_PLATFORM</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined as a string describing the name of the platform. Mainly
                  for debugging the configuration.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_LIBSTDCXX_VERSION</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
                </p>
              </td>
<td>
                <p>
                  Defined if the libstdc++ standard library is in use. Has the value
                  of normalised 5 digit integer of the form VVMMM where VV is the
                  major version number, MM is the minor version number.
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.boost_deprecated_macros"></a><a name="deprecated_macros"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.boost_deprecated_macros" title="Boost Deprecated Macros">Boost
      Deprecated Macros</a>
</h3></div></div></div>
<p>
        The following have been deprecated; please use the replacements instead.
        They will be removed in a future version of boost.
      </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Deprecated Macro
                </p>
              </th>
<th>
                <p>
                  Replacement
                </p>
              </th>
<th>
                <p>
                  When deprecated
                </p>
              </th>
<th>
                <p>
                  When removed
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_ARRAY</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_ARRAY</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_CHRONO</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_CHRONO</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_CODECVT</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_CODECVT</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_CONDITION_VARIABLE</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_CONDITION_VARIABLE</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_FORWARD_LIST</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_FORWARD_LIST</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_FUTURE</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_FUTURE</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_INITIALIZER_LIST</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_INITIALIZER_LIST</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_INITIALIZER_LISTS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_INITIALIZER_LIST</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_MUTEX</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_MUTEX</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_RANDOM</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_RANDOM</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_RATIO</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_RATIO</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_REGEX</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_REGEX</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_SYSTEM_ERROR</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_SYSTEM_ERROR</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_THREAD</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_THREAD</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_TUPLE</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_TUPLE</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_TYPE_TRAITS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_TYPE_TRAITS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_TYPEINDEX</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_TYPEINDEX</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_UNORDERED_SET</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_UNORDERED_SET</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_0X_HDR_UNORDERED_MAP</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_UNORDERED_MAP</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_UNORDERED</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_HDR_UNORDERED_SET</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.50
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
              </td>
<td>
              </td>
<td>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_AUTO_DECLARATIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_AUTO_DECLARATIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_AUTO_MULTIDECLARATIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_AUTO_MULTIDECLARATIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CHAR16_T</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_CHAR16_T</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CHAR32_T</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_CHAR32_T</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_TEMPLATE_ALIASES</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_TEMPLATE_ALIASES</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CONSTEXPR</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_CONSTEXPR</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_DECLTYPE</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_DECLTYPE</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_DECLTYPE_N3276</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_DECLTYPE_N3276</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_DEFAULTED_FUNCTIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_DEFAULTED_FUNCTIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_DELETED_FUNCTIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_DELETED_FUNCTIONS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_EXPLICIT_CONVERSION_OPERATORS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_EXPLICIT_CONVERSION_OPERATORS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_EXTERN_TEMPLATE</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_EXTERN_TEMPLATE</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_FUNCTION_TEMPLATE_DEFAULT_ARGS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_FUNCTION_TEMPLATE_DEFAULT_ARGS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_LAMBDAS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_LAMBDAS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_LOCAL_CLASS_TEMPLATE_PARAMETERS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_LOCAL_CLASS_TEMPLATE_PARAMETERS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_NOEXCEPT</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_NOEXCEPT</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_NULLPTR</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_NULLPTR</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_RAW_LITERALS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_RAW_LITERALS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_RVALUE_REFERENCES</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_RVALUE_REFERENCES</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_SCOPED_ENUMS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_SCOPED_ENUMS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STATIC_ASSERT</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_STATIC_ASSERT</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_STD_UNORDERED</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_STD_UNORDERED</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_UNICODE_LITERALS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_UNICODE_LITERALS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_UNIFIED_INITIALIZATION_SYNTAX</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_UNIFIED_INITIALIZATION_SYNTAX</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_VARIADIC_TEMPLATES</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_VARIADIC_TEMPLATES</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_VARIADIC_MACROS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_VARIADIC_MACROS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_NUMERIC_LIMITS_LOWEST</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_NUMERIC_LIMITS</span></code>
                </p>
              </td>
<td>
                <p>
                  Boost 1.51
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
              </td>
<td>
              </td>
<td>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_STATIC_ASSERT</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_STATIC_ASSERT</span></code>
                  (negated)
                </p>
              </td>
<td>
                <p>
                  Boost 1.53
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_VARIADIC_TMPL</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_VARIADIC_TEMPLATES</span></code>
                  (negated)
                </p>
              </td>
<td>
                <p>
                  Boost 1.53
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_RVALUE_REFS</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_RVALUE_REFERENCES</span></code>
                  (negated)
                </p>
              </td>
<td>
                <p>
                  Boost 1.53
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_CHAR16_T</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_CHAR16_T</span></code>
                  (negated)
                </p>
              </td>
<td>
                <p>
                  Boost 1.53
                </p>
              </td>
<td>
              </td>
</tr>
<tr>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_HAS_CHAR32_T</span></code>
                </p>
              </td>
<td>
                <p>
                  <code class="computeroutput"><span class="identifier">BOOST_NO_CXX11_CHAR32_T</span></code>
                  (negated)
                </p>
              </td>
<td>
                <p>
                  Boost 1.53
                </p>
              </td>
<td>
              </td>
</tr>
</tbody>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_config.boost_macro_reference.macros_for_libraries_with_separate_source_code"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_for_libraries_with_separate_source_code" title="Macros for libraries with separate source code">Macros
      for libraries with separate source code</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_for_libraries_with_separate_source_code.macros_controlling_shared_library_symbol_visibility">Macros
        controlling shared library symbol visibility</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_for_libraries_with_separate_source_code.abi_fixing">ABI
        Fixing</a></span></dt>
<dt><span class="section"><a href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_for_libraries_with_separate_source_code.automatic_library_selection">Automatic
        library selection</a></span></dt>
</dl></div>
<p>
        The following macros and helper headers are of use to authors whose libraries
        include separate source code, and are intended to address several issues:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Controlling shared library symbol visibility
          </li>
<li class="listitem">
            Fixing the ABI of the compiled library
          </li>
<li class="listitem">
            Selecting which compiled library to link against based upon the compilers
            settings
          </li>
</ul></div>
<p>
        See <a href="http://www.boost.org/development/separate_compilation.html" target="_top">Guidelines
        for Authors of Boost Libraries Containing Separate Source</a>
      </p>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_config.boost_macro_reference.macros_for_libraries_with_separate_source_code.macros_controlling_shared_library_symbol_visibility"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_for_libraries_with_separate_source_code.macros_controlling_shared_library_symbol_visibility" title="Macros controlling shared library symbol visibility">Macros
        controlling shared library symbol visibility</a>
</h4></div></div></div>
<p>
          Some compilers support C++ extensions that control which symbols will be
          exported from shared libraries such as dynamic shared objects (DSO's) on
          Unix-like systems or dynamic-link libraries (DLL's) on Windows.
        </p>
<p>
          The Microsoft VC++ compiler has long supplied <code class="computeroutput"><span class="identifier">__declspec</span><span class="special">(</span><span class="identifier">dllexport</span><span class="special">)</span></code> and <code class="computeroutput"><span class="identifier">__declspec</span><span class="special">(</span><span class="identifier">dllimport</span><span class="special">)</span></code> extensions for this purpose, as do virtually
          all other compilers targeting the Windows platform.
        </p>
<p>
          Modern versions of the GNU GCC compiler provide the <code class="computeroutput"><span class="identifier">__attribute__</span><span class="special">((</span><span class="identifier">visibility</span><span class="special">(</span><span class="string">"default"</span><span class="special">)))</span></code> extension to indicate that a symbol
          should be exported. All other symbols may be hidden by using the <code class="computeroutput"><span class="special">-</span><span class="identifier">fvisibility</span><span class="special">-</span><span class="identifier">hidden</span></code>
          or <code class="computeroutput"><span class="special">-</span><span class="identifier">fvisibility</span><span class="special">-</span><span class="identifier">ms</span><span class="special">-</span><span class="identifier">compat</span></code> compiler switches.
        </p>
<p>
          Boost supplies several macros to make it easier to manage symbol visibility
          in a way that is portable between compilers and operating systems.
        </p>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Macro
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">BOOST_SYMBOL_EXPORT</span></code>
                  </p>
                </td>
<td>
                  <p>
                    Defines the syntax of a C++ language extension that indicates
                    a symbol is to be exported from a shared library. If the compiler
                    has no such extension, the macro is defined with no replacement
                    text.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">BOOST_SYMBOL_IMPORT</span></code>
                  </p>
                </td>
<td>
                  <p>
                    Defines the syntax of a C++ language extension that indicates
                    a symbol is to be imported from a shared library. If the compiler
                    has no such extension, the macro is defined with no replacement
                    text.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">BOOST_SYMBOL_VISIBLE</span></code>
                  </p>
                </td>
<td>
                  <p>
                    Defines the syntax of a C++ language extension that indicates
                    a symbol is to be globally visible. If the compiler has no such
                    extension, the macro is defined with no replacement text. Needed
                    for classes that are not otherwise exported, but are used by
                    RTTI. Examples include class for objects that will be thrown
                    as exceptions or used in dynamic_casts, across shared library
                    boundaries. For example, a header-only exception class might
                    look like this:
</p>
<pre class="table-programlisting"><span class="keyword">class</span> <span class="identifier">BOOST_SYMBOL_VISIBLE</span> <span class="identifier">my_exception</span> <span class="special">:</span> <span class="keyword">public</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">runtime_error</span> <span class="special">{</span> <span class="special">...</span> <span class="special">};</span>
</pre>
<p>
                    Without BOOST_SYMBOL_VISIBLE, it would be impossible to catch
                    my_exception thrown from a shared library compiled by GCC with
                    the -fvisibility=hidden option.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">BOOST_HAS_DECLSPEC</span></code>
                  </p>
                </td>
<td>
                  <p>
                    The compiler has C++ extensions <code class="computeroutput"><span class="identifier">__declspec</span><span class="special">(</span><span class="identifier">dllexport</span><span class="special">)</span></code> and <code class="computeroutput"><span class="identifier">__declspec</span><span class="special">(</span><span class="identifier">dllimport</span><span class="special">)</span></code> to control export/import of
                    symbols from shared libraries. <span class="emphasis"><em>Deprecated. This macro
                    is no longer necessary since BOOST_SYMBOL_EXPORT and BOOST_SYMBOL_IMPORT
                    are now supplied. It is provided to support legacy code.</em></span>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">BOOST_DISABLE_EXPLICIT_SYMBOL_VISIBILITY</span></code>
                  </p>
                </td>
<td>
                  <p>
                    Disables the effect of the BOOST_SYMBOL_EXPORT, BOOST_SYMBOL_IMPORT
                    and BOOST_SYMBOL_VISIBLE macros, in order to revert to the default
                    compiler behaviour. Note that this option should never be used
                    if Boost libraries are being linking against dynamically, or
                    if you are building a shared library that exposes Boost types
                    in its public API. It is however advisable when statically-linking
                    against Boost to prevent Boost symbols from leaking from the
                    binary: for instance because you are building a plug-in for a
                    software which may itself use Boost which could cause ODR conflicts.
                  </p>
                </td>
</tr>
</tbody>
</table></div>
<p>
          Typical usage:
        </p>
<p>
          <span class="bold"><strong>boost/foo/config.hpp</strong></span>
        </p>
<pre class="programlisting"><span class="special">...</span>
<span class="preprocessor">#if</span> <span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_ALL_DYN_LINK</span><span class="special">)</span> <span class="special">||</span> <span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_FOO_DYN_LINK</span><span class="special">)</span>
<span class="preprocessor"># if</span> <span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_FOO_SOURCE</span><span class="special">)</span>
<span class="preprocessor">#   define</span> <span class="identifier">BOOST_FOO_DECL</span> <span class="identifier">BOOST_SYMBOL_EXPORT</span>
<span class="preprocessor"># else</span>
<span class="preprocessor">#   define</span> <span class="identifier">BOOST_FOO_DECL</span> <span class="identifier">BOOST_SYMBOL_IMPORT</span>
<span class="preprocessor"># endif</span>
<span class="preprocessor">#else</span>
<span class="preprocessor"># define</span> <span class="identifier">BOOST_FOO_DECL</span>
<span class="preprocessor">#endif</span>
<span class="special">...</span>
</pre>
<p>
          <span class="bold"><strong>boost/foo/foo.hpp</strong></span>
        </p>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">foo</span><span class="special">/</span><span class="identifier">config</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="special">...</span>
<span class="keyword">class</span> <span class="identifier">BOOST_FOO_DECL</span> <span class="identifier">bar</span> <span class="special">{</span> <span class="special">...</span> <span class="special">};</span>
<span class="special">...</span>
<span class="keyword">void</span> <span class="identifier">BOOST_FOO_DECL</span> <span class="identifier">f</span><span class="special">();</span>
<span class="special">...</span>
</pre>
<p>
          <span class="bold"><strong>boost/libs/foo/src/foo.cpp</strong></span>
        </p>
<pre class="programlisting"><span class="preprocessor">#define</span> <span class="identifier">BOOST_FOO_SOURCE</span>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">foo</span><span class="special">/</span><span class="identifier">foo</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="special">...</span>
<span class="keyword">void</span> <span class="identifier">BOOST_FOO_DECL</span> <span class="identifier">f</span><span class="special">()</span>
<span class="special">{</span>
  <span class="special">...</span>
<span class="special">}</span>
<span class="special">...</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_config.boost_macro_reference.macros_for_libraries_with_separate_source_code.abi_fixing"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_for_libraries_with_separate_source_code.abi_fixing" title="ABI Fixing">ABI
        Fixing</a>
</h4></div></div></div>
<p>
          When linking against a pre-compiled library it vital that the ABI used
          by the compiler when building the library <span class="emphasis"><em>matches exactly</em></span>
          the ABI used by the code using the library. In this case ABI means things
          like the struct packing arrangement used, the name mangling scheme used,
          or the size of some types (enum types for example). This is separate from
          things like threading support, or runtime library variations, which have
          to be dealt with by build variants. To put this in perspective there is
          one compiler (Borland's) that has so many compiler options that make subtle
          changes to the ABI, that at least in theory there 3200 combinations, and
          that's without considering runtime library variations. Fortunately these
          variations can be managed by <code class="computeroutput"><span class="preprocessor">#pragma</span></code>'s
          that tell the compiler what ABI to use for the types declared in your library.
          In order to avoid sprinkling <code class="computeroutput"><span class="preprocessor">#pragma</span></code>'s
          all over the boost headers, there are some prefix and suffix headers that
          do the job. Typical usage is:
        </p>
<p>
          <span class="bold"><strong>my_library.hpp</strong></span>
        </p>
<pre class="programlisting"><span class="preprocessor">#ifndef</span> <span class="identifier">MY_INCLUDE_GUARD</span>
<span class="preprocessor">#define</span> <span class="identifier">MY_INCLUDE_GUARD</span>

<span class="comment">// all includes go here:</span>
<code class="literal"><span class="bold"><strong>#include &lt;boost/config.hpp&gt;</strong></span></code>
<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">whatever</span><span class="special">&gt;</span>

<code class="literal"><span class="bold"><strong>#include &lt;boost/config/abi_prefix.hpp&gt;</strong></span></code> <span class="comment">// must be the last #include</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>

<span class="comment">// your code goes here</span>

<span class="special">}</span>

<code class="literal"><span class="bold"><strong>#include &lt;boost/config/abi_suffix.hpp&gt;</strong></span></code> <span class="comment">// pops abi_prefix.hpp pragmas</span>

<span class="preprocessor">#endif</span> <span class="comment">// include guard</span>
</pre>
<p>
          <span class="bold"><strong>my_library.cpp</strong></span>
        </p>
<pre class="programlisting"><span class="special">...</span>
<span class="comment">// nothing special need be done in the implementation file</span>
<span class="special">...</span>
</pre>
<p>
          The user can disable this mechanism by defining <code class="computeroutput"><span class="identifier">BOOST_DISABLE_ABI_HEADERS</span></code>,
          or they can define <code class="computeroutput"><span class="identifier">BOOST_ABI_PREFIX</span></code>
          and/or <code class="computeroutput"><span class="identifier">BOOST_ABI_SUFFIX</span></code>
          to point to their own prefix/suffix headers if they so wish.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_config.boost_macro_reference.macros_for_libraries_with_separate_source_code.automatic_library_selection"></a><a class="link" href="boost_macro_reference.html#boost_config.boost_macro_reference.macros_for_libraries_with_separate_source_code.automatic_library_selection" title="Automatic library selection">Automatic
        library selection</a>
</h4></div></div></div>
<p>
          It is essential that users link to a build of a library which was built
          against the same runtime library that their application will be built against
          -if this does not happen then the library will not be binary compatible
          with their own code- and there is a high likelihood that their application
          will experience runtime crashes. These kinds of problems can be extremely
          time consuming and difficult to debug, and often lead to frustrated users
          and authors alike (simply selecting the right library to link against is
          not as easy as it seems when their are 6-8 of them to chose from, and some
          users seem to be blissfully unaware that there even are different runtimes
          available to them).
        </p>
<p>
          To solve this issue, some compilers allow source code to contain <code class="computeroutput"><span class="preprocessor">#pragma</span></code>'s that instruct the linker
          which library to link against, all the user need do is include the headers
          they need, place the compiled libraries in their library search path, and
          the compiler and linker do the rest. Boost.config supports this via the
          header <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">auto_link</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>, before including this header one or
          more of the following macros need to be defined:
        </p>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term"><code class="computeroutput"><span class="identifier">BOOST_LIB_NAME</span></code></span></dt>
<dd><p>
                Required: An identifier containing the basename of the library, for
                example 'boost_regex'.
              </p></dd>
<dt><span class="term"><code class="computeroutput"><span class="identifier">BOOST_DYN_LINK</span></code></span></dt>
<dd><p>
                Optional: when set link to dll rather than static library.
              </p></dd>
<dt><span class="term"><code class="computeroutput"><span class="identifier">BOOST_LIB_DIAGNOSTIC</span></code></span></dt>
<dd><p>
                Optional: when set the header will print out the name of the library
                selected (useful for debugging).
              </p></dd>
<dt><span class="term"><code class="computeroutput"><span class="identifier">BOOST_AUTO_LINK_NOMANGLE</span></code></span></dt>
<dd><p>
                Optional: whan set specifies that we should link to BOOST_LIB_NAME.lib,
                rather than a mangled-name version.
              </p></dd>
<dt><span class="term"><code class="computeroutput"><span class="identifier">BOOST_AUTO_LINK_TAGGED</span></code></span></dt>
<dd><p>
                Optional: Specifies that we link to libraries built with the --layout=tagged
                option. This is essentially the same as the default name-mangled
                version, but without the compiler name and version, or the Boost
                version. Just the build options.
              </p></dd>
<dt><span class="term"><code class="computeroutput"><span class="identifier">BOOST_AUTO_LINK_SYSTEM</span></code></span></dt>
<dd><p>
                Optional: Specifies that we link to libraries built with the --layout=system
                option. This is essentially the same as the non-name-mangled version,
                but with the prefix to differentiate static and dll builds
              </p></dd>
</dl>
</div>
<p>
          If the compiler supports this mechanism, then it will be told to link against
          the appropriately named library, the actual algorithm used to mangle the
          name of the library is documented inside <code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">auto_link</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code>
          and has to match that used to create the libraries via bjam 's install
          rules.
        </p>
<p>
          <span class="bold"><strong>my_library.hpp</strong></span>
        </p>
<pre class="programlisting"><span class="special">...</span>
<span class="comment">//</span>
<span class="comment">// Don't include auto-linking code if the user has disabled it by</span>
<span class="comment">// defining BOOST_ALL_NO_LIB, or BOOST_MY_LIBRARY_NO_LIB, or if this </span>
<span class="comment">// is one of our own source files (signified by BOOST_MY_LIBRARY_SOURCE):</span>
<span class="comment">//</span>
<span class="preprocessor">#if</span> <span class="special">!</span><span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_ALL_NO_LIB</span><span class="special">)</span> <span class="special">&amp;&amp;</span> <span class="special">!</span><span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_MY_LIBRARY_NO_LIB</span><span class="special">)</span> <span class="special">&amp;&amp;</span> <span class="special">!</span><span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_MY_LIBRARY_SOURCE</span><span class="special">)</span>
<span class="preprocessor">#  define</span> <span class="identifier">BOOST_LIB_NAME</span> <span class="identifier">boost_my_library</span>
<span class="preprocessor">#  ifdef</span> <span class="identifier">BOOST_MY_LIBRARY_DYN_LINK</span>
<span class="preprocessor">#     define</span> <span class="identifier">BOOST_DYN_LINK</span>
<span class="preprocessor">#  endif</span>
<span class="preprocessor">#  include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">config</span><span class="special">/</span><span class="identifier">auto_link</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="preprocessor">#endif</span>
<span class="special">...</span>
</pre>
<p>
          <span class="bold"><strong>my_library.cpp</strong></span>
        </p>
<pre class="programlisting"><span class="comment">// define BOOST_MY_LIBRARY_SOURCE so that the header knows that the</span>
<span class="comment">// library is being built (possibly exporting rather than importing code)</span>
<span class="comment">//</span>
<span class="preprocessor">#define</span> <span class="identifier">BOOST_MY_LIBRARY_SOURCE</span>

<span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">my_library</span><span class="special">/</span><span class="identifier">my_library</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="special">...</span>
</pre>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2001-2007 Beman Dawes, Vesa Karvonen, John
      Maddock<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../index.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../index.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="build_config.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
