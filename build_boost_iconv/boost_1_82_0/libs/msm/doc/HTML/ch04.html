<html><head>
      <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
   <title>Chapter&nbsp;4.&nbsp; Performance / Compilers</title><link rel="stylesheet" href="boostbook.css" type="text/css"><meta name="generator" content="DocBook XSL-NS Stylesheets V1.75.2"><link rel="home" href="index.html" title="Meta State Machine (MSM)"><link rel="up" href="pt01.html" title="Part&nbsp;I.&nbsp;User' guide"><link rel="prev" href="ch03s05.html" title="Back-end"><link rel="next" href="ch04s02.html" title="Executable size"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">Chapter&nbsp;4.&nbsp; Performance / Compilers</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="ch03s05.html">Prev</a>&nbsp;</td><th width="60%" align="center">Part&nbsp;I.&nbsp;User' guide</th><td width="20%" align="right">&nbsp;<a accesskey="n" href="ch04s02.html">Next</a></td></tr></table><hr></div><div class="chapter" title="Chapter&nbsp;4.&nbsp; Performance / Compilers"><div class="titlepage"><div><div><h2 class="title"><a name="d0e2773"></a>Chapter&nbsp;4.&nbsp; Performance / Compilers</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl><dt><span class="sect1"><a href="ch04.html#d0e2795">Speed</a></span></dt><dt><span class="sect1"><a href="ch04s02.html">Executable size</a></span></dt><dt><span class="sect1"><a href="ch04s03.html">Supported compilers</a></span></dt><dt><span class="sect1"><a href="ch04s04.html"> Limitations </a></span></dt><dt><span class="sect1"><a href="ch04s05.html"> Compilers corner </a></span></dt></dl></div><p>Tests were made on different PCs running Windows XP and Vista and compiled with
                VC9 SP1 or Ubuntu and compiled with g++ 4.2 and 4.3. For these tests, the same
                player state machine was written using Boost.Statechart, as a <a class="link" href="examples/SCSimple.cpp" target="_top">state machine with only simple states</a>
                and as a <a class="link" href="examples/SCComposite.cpp" target="_top">state machine with a composite
                    state</a>. The same simple and composite state machines are implemented with
                MSM with a standard frontend <a class="link" href="examples/MsmSimple.cpp" target="_top">(simple)</a><a class="link" href="examples/MsmComposite.cpp" target="_top">(composite)</a>,
                the simple one also with <a class="link" href="examples/MsmSimpleFunctors.cpp" target="_top">functors</a> and with <a class="link" href="examples/EumlSimple.cpp" target="_top">eUML</a>. As these simple machines need no terminate/interrupt states, no
                message queue and have no-throw guarantee on their actions, the MSM state machines
                are defined with minimum functionality. Test machine is a Q6600 2.4GHz, Vista
                64.</p><div class="sect1" title="Speed"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="d0e2795"></a>Speed</h2></div></div></div><p>VC9:</p><div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem"><p>The simple test completes 90 times faster with MSM than with
                                Boost.Statechart</p></li><li class="listitem"><p>The composite test completes 25 times faster with MSM</p></li></ul></div><p>gcc 4.2.3 (Ubuntu 8.04 in VMWare, same PC):</p><div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem"><p>The simple test completes 46 times faster with MSM</p></li><li class="listitem"><p>The composite test completes 19 times faster with Msm</p></li></ul></div></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="ch03s05.html">Prev</a>&nbsp;</td><td width="20%" align="center"><a accesskey="u" href="pt01.html">Up</a></td><td width="40%" align="right">&nbsp;<a accesskey="n" href="ch04s02.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Back-end&nbsp;</td><td width="20%" align="center"><a accesskey="h" href="index.html">Home</a></td><td width="40%" align="right" valign="top">&nbsp;Executable size</td></tr></table></div></body></html>