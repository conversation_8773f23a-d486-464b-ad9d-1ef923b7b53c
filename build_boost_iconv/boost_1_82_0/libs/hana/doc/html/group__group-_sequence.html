<!--
Copyright <PERSON> 2013-2017
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
-->
<!-- boost-no-inspect -->
<!-- HTML header for doxygen *******-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<title>Boost.Hana: Sequence</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
// Copyright Louis Dionne 2013-2017
// Distributed under the Boost Software License, Version 1.0.
// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
MathJax.Hub.Config({
    "HTML-CSS": {
        linebreaks: {
            automatic: true,
            width: "75% container"
        }
    }
});
</script>
<script type="text/javascript" async="async" src="https://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<!-- Additional javascript for drawing charts. -->
<script type="text/javascript" src="highcharts.js"></script>
<script type="text/javascript" src="highcharts-data.js"></script>
<script type="text/javascript" src="highcharts-exporting.js"></script>
<script type="text/javascript" src="chart.js"></script>
<script type="text/javascript" src="hana.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="Boost.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">Boost.Hana
   &#160;<span id="projectnumber">1.7.1</span>
   </div>
   <div id="projectbrief">Your standard library for metaprogramming</div>
  </td>
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.svg"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(document).ready(function(){initNavTree('group__group-_sequence.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">Sequence<div class="ingroups"><a class="el" href="group__group-concepts.html">Concepts</a></div></div>  </div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>The <code>Sequence</code> concept represents generic index-based sequences. </p>
<p>Compared to other abstract concepts, the Sequence concept is very specific. It represents generic index-based sequences. The reason why such a specific concept is provided is because there are a lot of models that behave exactly the same while being implemented in wildly different ways. It is useful to regroup all those data types under the same umbrella for the purpose of generic programming.</p>
<p>In fact, models of this concept are not only <em>similar</em>. They are actually <em>isomorphic</em>, in a sense that we define below, which is a fancy way of rigorously saying that they behave exactly the same to an external observer.</p>
<h2><a class="anchor" id="autotoc_md171"></a>
Minimal complete definition</h2>
<p><code>Iterable</code>, <code>Foldable</code>, and <code>make</code></p>
<p>The <code>Sequence</code> concept does not provide basic methods that could be used as a minimal complete definition; instead, it borrows methods from other concepts and add laws to them. For this reason, it is necessary to specialize the <code>Sequence</code> metafunction in Hana's namespace to tell Hana that a type is indeed a <code>Sequence</code>. Explicitly specializing the <code>Sequence</code> metafunction can be seen like a seal saying "this data type satisfies the additional laws of a `Sequence`", since those can't be checked by Hana automatically.</p>
<h2><a class="anchor" id="autotoc_md172"></a>
Laws</h2>
<p>The laws for being a <code>Sequence</code> are simple, and their goal is to restrict the semantics that can be associated to the functions provided by other concepts. First, a <code>Sequence</code> must be a finite <code>Iterable</code> (thus a <code>Foldable</code> too). Secondly, for a <code>Sequence</code> tag <code>S</code>, <code>make&lt;S&gt;(x1, ..., xn)</code> must be an object of tag <code>S</code> and whose linearization is <code>[x1, ..., xn]</code>. This basically ensures that objects of tag <code>S</code> are equivalent to their linearization, and that they can be created from such a linearization (with <code>make</code>).</p>
<p>While it would be possible in theory to handle infinite sequences, doing so complicates the implementation of many algorithms. For simplicity, the current version of the library only handles finite sequences. However, note that this does not affect in any way the potential for having infinite <code>Searchable</code>s and <code>Iterable</code>s.</p>
<h2><a class="anchor" id="autotoc_md173"></a>
Refined concepts</h2>
<ol type="1">
<li><code>Comparable</code> (definition provided automatically)<br  />
Two <code>Sequence</code>s are equal if and only if they contain the same number of elements and their elements at any given index are equal. <div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="not__equal_8hpp.html">boost/hana/not_equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(hana::make_tuple(1, 2, 3) == hana::make_tuple(1, 2, 3), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(hana::make_tuple(1, 2, 3) != hana::make_tuple(1, 2, 3, 4));</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="aassert_8hpp_html"><div class="ttname"><a href="assert_8hpp.html">assert.hpp</a></div><div class="ttdoc">Defines macros to perform different kinds of assertions.</div></div>
<div class="ttc" id="aequal_8hpp_html"><div class="ttname"><a href="equal_8hpp.html">equal.hpp</a></div><div class="ttdoc">Defines boost::hana::equal.</div></div>
<div class="ttc" id="agroup__group-assertions_html_ga2c5006540936d9f8880e3a39f4fcc035"><div class="ttname"><a href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a></div><div class="ttdeci">#define BOOST_HANA_CONSTANT_CHECK(...)</div><div class="ttdoc">Equivalent to BOOST_HANA_CONSTANT_ASSERT, but not influenced by the BOOST_HANA_CONFIG_DISABLE_ASSERTI...</div><div class="ttdef"><b>Definition:</b> assert.hpp:239</div></div>
<div class="ttc" id="anamespaceboost_1_1hana_html"><div class="ttname"><a href="namespaceboost_1_1hana.html">boost::hana</a></div><div class="ttdoc">Namespace containing everything in the library.</div><div class="ttdef"><b>Definition:</b> accessors.hpp:20</div></div>
<div class="ttc" id="anot__equal_8hpp_html"><div class="ttname"><a href="not__equal_8hpp.html">not_equal.hpp</a></div><div class="ttdoc">Defines boost::hana::not_equal.</div></div>
<div class="ttc" id="atuple_8hpp_html"><div class="ttname"><a href="tuple_8hpp.html">tuple.hpp</a></div><div class="ttdoc">Defines boost::hana::tuple.</div></div>
</div><!-- fragment --></li>
<li><code>Orderable</code> (definition provided automatically)<br  />
<code>Sequence</code>s are ordered using the traditional lexicographical ordering. <div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="less_8hpp.html">boost/hana/less.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(hana::make_tuple(1, 2, 3) &lt; hana::make_tuple(2, 3, 4), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(hana::make_tuple(1, 2, 3) &lt; hana::make_tuple(1, 2, 3, 4), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="aless_8hpp_html"><div class="ttname"><a href="less_8hpp.html">less.hpp</a></div><div class="ttdoc">Defines boost::hana::less.</div></div>
</div><!-- fragment --></li>
<li><code>Functor</code> (definition provided automatically)<br  />
<code>Sequence</code>s implement <code>transform</code> as the mapping of a function over each element of the sequence. This is somewhat equivalent to what <code>std::transform</code> does to ranges of iterators. Also note that mapping a function over an empty sequence returns an empty sequence and never applies the function, as would be expected. <div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="transform_8hpp.html">boost/hana/transform.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;sstream&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;string&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keyword">auto</span> to_string = [](<span class="keyword">auto</span> x) {</div>
<div class="line">    std::ostringstream ss;</div>
<div class="line">    ss &lt;&lt; x;</div>
<div class="line">    <span class="keywordflow">return</span> ss.str();</div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() {</div>
<div class="line">    <a class="code" href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a>(</div>
<div class="line">        hana::transform(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, <span class="stringliteral">&quot;345&quot;</span>, std::string{<span class="stringliteral">&quot;67&quot;</span>}), to_string) ==</div>
<div class="line">        hana::make_tuple(<span class="stringliteral">&quot;1&quot;</span>, <span class="stringliteral">&quot;2&quot;</span>, <span class="stringliteral">&quot;345&quot;</span>, <span class="stringliteral">&quot;67&quot;</span>)</div>
<div class="line">    );</div>
<div class="line">}</div>
<div class="ttc" id="agroup__group-assertions_html_ga4796ae107d58b67e0bbccd5ae6f70101"><div class="ttname"><a href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a></div><div class="ttdeci">#define BOOST_HANA_RUNTIME_CHECK(...)</div><div class="ttdoc">Equivalent to BOOST_HANA_RUNTIME_ASSERT, but not influenced by the BOOST_HANA_CONFIG_DISABLE_ASSERTIO...</div><div class="ttdef"><b>Definition:</b> assert.hpp:209</div></div>
<div class="ttc" id="atransform_8hpp_html"><div class="ttname"><a href="transform_8hpp.html">transform.hpp</a></div><div class="ttdoc">Defines boost::hana::transform.</div></div>
</div><!-- fragment --></li>
<li><code>Applicative</code> (definition provided automatically)<br  />
First, <code>lift</code>ing a value into a <code>Sequence</code> is the same as creating a singleton sequence containing that value. Second, applying a sequence of functions to a sequence of values will apply each function to all the values in the sequence, and then return a list of all the results. In other words, <div class="fragment"><div class="line">ap([f1, ..., fN], [x1, ..., xM]) == [</div>
<div class="line">    f1(x1), ..., f1(xM),</div>
<div class="line">    ...</div>
<div class="line">    fN(x1), ..., fN(xM)</div>
<div class="line">]</div>
</div><!-- fragment --> Example: <div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="ap_8hpp.html">boost/hana/ap.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="ext_2std_2tuple_8hpp.html">boost/hana/ext/std/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="flip_8hpp.html">boost/hana/functional/flip.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="lift_8hpp.html">boost/hana/lift.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="pair_8hpp.html">boost/hana/pair.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;tuple&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(hana::lift&lt;hana::tuple_tag&gt;(<span class="charliteral">&#39;x&#39;</span>) == hana::make_tuple(<span class="charliteral">&#39;x&#39;</span>), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_comparable.html#gacaf1ebea6b3ab96ac9dcb82f0e64e547">hana::equal</a>(hana::lift&lt;hana::ext::std::tuple_tag&gt;(<span class="charliteral">&#39;x&#39;</span>), std::make_tuple(<span class="charliteral">&#39;x&#39;</span>)), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line">constexpr <span class="keyword">auto</span> f = hana::make_pair;</div>
<div class="line">constexpr <span class="keyword">auto</span> g = <a class="code" href="group__group-functional.html#ga004f884cdbb85c2efe3383c1db450094">hana::flip</a>(hana::make_pair);</div>
<div class="line">static_assert(</div>
<div class="line">    hana::ap(hana::make_tuple(f, g), hana::make_tuple(1, 2, 3), hana::make_tuple(<span class="charliteral">&#39;a&#39;</span>, <span class="charliteral">&#39;b&#39;</span>))</div>
<div class="line">        ==</div>
<div class="line">    hana::make_tuple(</div>
<div class="line">        f(1, <span class="charliteral">&#39;a&#39;</span>), f(1, <span class="charliteral">&#39;b&#39;</span>), f(2, <span class="charliteral">&#39;a&#39;</span>), f(2, <span class="charliteral">&#39;b&#39;</span>), f(3, <span class="charliteral">&#39;a&#39;</span>), f(3, <span class="charliteral">&#39;b&#39;</span>),</div>
<div class="line">        g(1, <span class="charliteral">&#39;a&#39;</span>), g(1, <span class="charliteral">&#39;b&#39;</span>), g(2, <span class="charliteral">&#39;a&#39;</span>), g(2, <span class="charliteral">&#39;b&#39;</span>), g(3, <span class="charliteral">&#39;a&#39;</span>), g(3, <span class="charliteral">&#39;b&#39;</span>)</div>
<div class="line">    )</div>
<div class="line">, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="aap_8hpp_html"><div class="ttname"><a href="ap_8hpp.html">ap.hpp</a></div><div class="ttdoc">Defines boost::hana::ap.</div></div>
<div class="ttc" id="aext_2std_2tuple_8hpp_html"><div class="ttname"><a href="ext_2std_2tuple_8hpp.html">tuple.hpp</a></div><div class="ttdoc">Adapts std::tuple for use with Hana.</div></div>
<div class="ttc" id="aflip_8hpp_html"><div class="ttname"><a href="flip_8hpp.html">flip.hpp</a></div><div class="ttdoc">Defines boost::hana::flip.</div></div>
<div class="ttc" id="agroup__group-_comparable_html_gacaf1ebea6b3ab96ac9dcb82f0e64e547"><div class="ttname"><a href="group__group-_comparable.html#gacaf1ebea6b3ab96ac9dcb82f0e64e547">boost::hana::equal</a></div><div class="ttdeci">constexpr auto equal</div><div class="ttdoc">Returns a Logical representing whether x is equal to y.</div><div class="ttdef"><b>Definition:</b> equal.hpp:64</div></div>
<div class="ttc" id="agroup__group-functional_html_ga004f884cdbb85c2efe3383c1db450094"><div class="ttname"><a href="group__group-functional.html#ga004f884cdbb85c2efe3383c1db450094">boost::hana::flip</a></div><div class="ttdeci">constexpr auto flip</div><div class="ttdoc">Invoke a function with its two first arguments reversed.</div><div class="ttdef"><b>Definition:</b> flip.hpp:31</div></div>
<div class="ttc" id="alift_8hpp_html"><div class="ttname"><a href="lift_8hpp.html">lift.hpp</a></div><div class="ttdoc">Defines boost::hana::lift.</div></div>
<div class="ttc" id="apair_8hpp_html"><div class="ttname"><a href="pair_8hpp.html">pair.hpp</a></div><div class="ttdoc">Defines boost::hana::pair.</div></div>
</div><!-- fragment --></li>
<li><code>Monad</code> (definition provided automatically)<br  />
First, <code>flaten</code>ning a <code>Sequence</code> takes a sequence of sequences and concatenates them to get a larger sequence. In other words, <div class="fragment"><div class="line">flatten([[a1, ..., aN], ..., [z1, ..., zM]]) == [</div>
<div class="line">    a1, ..., aN, ..., z1, ..., zM</div>
<div class="line">]</div>
</div><!-- fragment --> This acts like a <code>std::tuple_cat</code> function, except it receives a sequence of sequences instead of a variadic pack of sequences to flatten.<br  />
<b>Example</b>: <div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="flatten_8hpp.html">boost/hana/flatten.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(</div>
<div class="line">    hana::flatten(hana::make_tuple(</div>
<div class="line">        hana::make_tuple(1, 2),</div>
<div class="line">        hana::make_tuple(3, 4),</div>
<div class="line">        hana::make_tuple(hana::make_tuple(5, 6))</div>
<div class="line">    ))</div>
<div class="line">    == hana::make_tuple(1, 2, 3, 4, hana::make_tuple(5, 6))</div>
<div class="line">, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="aflatten_8hpp_html"><div class="ttname"><a href="flatten_8hpp.html">flatten.hpp</a></div><div class="ttdoc">Defines boost::hana::flatten.</div></div>
</div><!-- fragment --> Also note that the model of <code>Monad</code> for <code>Sequence</code>s can be seen as modeling nondeterminism. A nondeterministic computation can be modeled as a function which returns a sequence of possible results. In this line of thought, <code>chain</code>ing a sequence of values into such a function will return a sequence of all the possible output values, i.e. a sequence of all the values applied to all the functions in the sequences.<br  />
<b>Example</b>: <div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="chain_8hpp.html">boost/hana/chain.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="for__each_8hpp.html">boost/hana/for_each.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="traits_8hpp.html">boost/hana/traits.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="type_8hpp.html">boost/hana/type.hpp</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;type_traits&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;utility&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">// Using the `tuple` Monad, we generate all the possible combinations of</span></div>
<div class="line"><span class="comment">// cv-qualifiers and reference qualifiers. Then, we use the `optional`</span></div>
<div class="line"><span class="comment">// Monad to make sure that our generic function can be called with</span></div>
<div class="line"><span class="comment">// arguments of any of those types.</span></div>
<div class="line"> </div>
<div class="line"><span class="comment">// cv_qualifiers : type -&gt; tuple(type)</span></div>
<div class="line"><span class="keyword">auto</span> cv_qualifiers = [](<span class="keyword">auto</span> t) {</div>
<div class="line">    <span class="keywordflow">return</span> hana::make_tuple(</div>
<div class="line">        t,</div>
<div class="line">        hana::traits::add_const(t),</div>
<div class="line">        hana::traits::add_volatile(t),</div>
<div class="line">        hana::traits::add_volatile(hana::traits::add_const(t))</div>
<div class="line">    );</div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><span class="comment">// ref_qualifiers : type -&gt; tuple(type)</span></div>
<div class="line"><span class="keyword">auto</span> ref_qualifiers = [](<span class="keyword">auto</span> t) {</div>
<div class="line">    <span class="keywordflow">return</span> hana::make_tuple(</div>
<div class="line">        hana::traits::add_lvalue_reference(t),</div>
<div class="line">        hana::traits::add_rvalue_reference(t)</div>
<div class="line">    );</div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><span class="keyword">auto</span> possible_args = cv_qualifiers(hana::type_c&lt;int&gt;) | ref_qualifiers;</div>
<div class="line"> </div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(</div>
<div class="line">    possible_args == hana::make_tuple(</div>
<div class="line">                        hana::type_c&lt;int&amp;&gt;,</div>
<div class="line">                        hana::type_c&lt;int&amp;&amp;&gt;,</div>
<div class="line">                        hana::type_c&lt;int const&amp;&gt;,</div>
<div class="line">                        hana::type_c&lt;int const&amp;&amp;&gt;,</div>
<div class="line">                        hana::type_c&lt;int volatile&amp;&gt;,</div>
<div class="line">                        hana::type_c&lt;int volatile&amp;&amp;&gt;,</div>
<div class="line">                        hana::type_c&lt;int const volatile&amp;&gt;,</div>
<div class="line">                        hana::type_c&lt;int const volatile&amp;&amp;&gt;</div>
<div class="line">                    )</div>
<div class="line">);</div>
<div class="line"> </div>
<div class="line"><span class="keyword">struct </span>some_function {</div>
<div class="line">    <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line">    <span class="keywordtype">void</span> operator()(T&amp;&amp;)<span class="keyword"> const </span>{ }</div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() {</div>
<div class="line">    <a class="code" href="group__group-_foldable.html#ga2af382f7e644ce3707710bbad313e9c2">hana::for_each</a>(possible_args, [](<span class="keyword">auto</span> t) {</div>
<div class="line">        <span class="keyword">using</span> T = <span class="keyword">typename</span> decltype(t)::type;</div>
<div class="line">        static_assert(decltype(hana::is_valid(some_function{})(std::declval&lt;T&gt;())){},</div>
<div class="line">        <span class="stringliteral">&quot;some_function should be callable with any type of argument&quot;</span>);</div>
<div class="line">    });</div>
<div class="line">}</div>
<div class="ttc" id="achain_8hpp_html"><div class="ttname"><a href="chain_8hpp.html">chain.hpp</a></div><div class="ttdoc">Defines boost::hana::chain.</div></div>
<div class="ttc" id="afor__each_8hpp_html"><div class="ttname"><a href="for__each_8hpp.html">for_each.hpp</a></div><div class="ttdoc">Defines boost::hana::for_each.</div></div>
<div class="ttc" id="agroup__group-_foldable_html_ga2af382f7e644ce3707710bbad313e9c2"><div class="ttname"><a href="group__group-_foldable.html#ga2af382f7e644ce3707710bbad313e9c2">boost::hana::for_each</a></div><div class="ttdeci">constexpr auto for_each</div><div class="ttdoc">Perform an action on each element of a foldable, discarding the result each time.</div><div class="ttdef"><b>Definition:</b> for_each.hpp:39</div></div>
<div class="ttc" id="atraits_8hpp_html"><div class="ttname"><a href="traits_8hpp.html">traits.hpp</a></div><div class="ttdoc">Defines function-like equivalents to the standard &lt;type_traits&gt;, and also to some utilities like std:...</div></div>
<div class="ttc" id="atype_8hpp_html"><div class="ttname"><a href="type_8hpp.html">type.hpp</a></div><div class="ttdoc">Defines boost::hana::type and related utilities.</div></div>
</div><!-- fragment --></li>
<li><code>MonadPlus</code> (definition provided automatically)<br  />
<code>Sequence</code>s are models of the <code>MonadPlus</code> concept by considering the empty sequence as the unit of <code>concat</code>, and sequence concatenation as <code>concat</code>. <div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="append_8hpp.html">boost/hana/append.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="concat_8hpp.html">boost/hana/concat.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="empty_8hpp.html">boost/hana/empty.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;string&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"><span class="keyword">using namespace </span>std::string_literals;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(hana::empty&lt;hana::tuple_tag&gt;() == hana::make_tuple());</div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_monad_plus.html#ga08624924fe05f0cfbfbd6e439db01873">hana::append</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), <span class="keyword">nullptr</span>)</div>
<div class="line">                        == hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3, <span class="keyword">nullptr</span>), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() {</div>
<div class="line">    <a class="code" href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a>(</div>
<div class="line">        <a class="code" href="group__group-_monad_plus.html#ga1946e96c3b4c178c7ae8703724c29c37">hana::concat</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), hana::make_tuple(<span class="stringliteral">&quot;abcdef&quot;</span>s)) ==</div>
<div class="line">        hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3, <span class="stringliteral">&quot;abcdef&quot;</span>s)</div>
<div class="line">    );</div>
<div class="line">}</div>
<div class="ttc" id="aappend_8hpp_html"><div class="ttname"><a href="append_8hpp.html">append.hpp</a></div><div class="ttdoc">Defines boost::hana::append.</div></div>
<div class="ttc" id="aconcat_8hpp_html"><div class="ttname"><a href="concat_8hpp.html">concat.hpp</a></div><div class="ttdoc">Defines boost::hana::concat.</div></div>
<div class="ttc" id="aempty_8hpp_html"><div class="ttname"><a href="empty_8hpp.html">empty.hpp</a></div><div class="ttdoc">Defines boost::hana::empty.</div></div>
<div class="ttc" id="agroup__group-_monad_plus_html_ga08624924fe05f0cfbfbd6e439db01873"><div class="ttname"><a href="group__group-_monad_plus.html#ga08624924fe05f0cfbfbd6e439db01873">boost::hana::append</a></div><div class="ttdeci">constexpr auto append</div><div class="ttdoc">Append an element to a monadic structure.</div><div class="ttdef"><b>Definition:</b> append.hpp:52</div></div>
<div class="ttc" id="agroup__group-_monad_plus_html_ga1946e96c3b4c178c7ae8703724c29c37"><div class="ttname"><a href="group__group-_monad_plus.html#ga1946e96c3b4c178c7ae8703724c29c37">boost::hana::concat</a></div><div class="ttdeci">constexpr auto concat</div><div class="ttdoc">Combine two monadic structures together.</div><div class="ttdef"><b>Definition:</b> concat.hpp:47</div></div>
</div><!-- fragment --></li>
<li><code>Foldable</code><br  />
The model of <code>Foldable</code> for <code>Sequence</code>s is uniquely determined by the model of <code>Iterable</code>. <div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="fold__left_8hpp.html">boost/hana/fold_left.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;sstream&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keyword">auto</span> to_string = [](<span class="keyword">auto</span> x) {</div>
<div class="line">    std::ostringstream ss;</div>
<div class="line">    ss &lt;&lt; x;</div>
<div class="line">    <span class="keywordflow">return</span> ss.str();</div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><span class="keyword">auto</span> show = [](<span class="keyword">auto</span> x, <span class="keyword">auto</span> y) {</div>
<div class="line">    <span class="keywordflow">return</span> <span class="stringliteral">&quot;(&quot;</span> + to_string(x) + <span class="stringliteral">&quot; + &quot;</span> + to_string(y) + <span class="stringliteral">&quot;)&quot;</span>;</div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() {</div>
<div class="line">    <a class="code" href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a>(</div>
<div class="line">        hana::fold_left(hana::make_tuple(2, <span class="stringliteral">&quot;3&quot;</span>, <span class="charliteral">&#39;4&#39;</span>), <span class="stringliteral">&quot;1&quot;</span>, show) == <span class="stringliteral">&quot;(((1 + 2) + 3) + 4)&quot;</span></div>
<div class="line">    );</div>
<div class="line">}</div>
<div class="ttc" id="afold__left_8hpp_html"><div class="ttname"><a href="fold__left_8hpp.html">fold_left.hpp</a></div><div class="ttdoc">Defines boost::hana::fold_left.</div></div>
</div><!-- fragment --></li>
<li><code>Iterable</code><br  />
The model of <code>Iterable</code> for <code>Sequence</code>s corresponds to iteration over each element of the sequence, in order. This model is not provided automatically, and it is in fact part of the minimal complete definition for the <code>Sequence</code> concept. <div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="drop__front_8hpp.html">boost/hana/drop_front.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="front_8hpp.html">boost/hana/front.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="is__empty_8hpp.html">boost/hana/is_empty.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#ga8a67ea10e8082dbe6705e573fa978444">hana::front</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3)) == 1, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757">hana::drop_front</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3)) == hana::make_tuple(<span class="charliteral">&#39;2&#39;</span>, 3.3), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(!<a class="code" href="group__group-_iterable.html#ga2a05f564f8a7e4afa04fcbc07ad8f394">hana::is_empty</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3)));</div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(<a class="code" href="group__group-_iterable.html#ga2a05f564f8a7e4afa04fcbc07ad8f394">hana::is_empty</a>(hana::make_tuple()));</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="adrop__front_8hpp_html"><div class="ttname"><a href="drop__front_8hpp.html">drop_front.hpp</a></div><div class="ttdoc">Defines boost::hana::drop_front.</div></div>
<div class="ttc" id="afront_8hpp_html"><div class="ttname"><a href="front_8hpp.html">front.hpp</a></div><div class="ttdoc">Defines boost::hana::front.</div></div>
<div class="ttc" id="agroup__group-_iterable_html_ga2a05f564f8a7e4afa04fcbc07ad8f394"><div class="ttname"><a href="group__group-_iterable.html#ga2a05f564f8a7e4afa04fcbc07ad8f394">boost::hana::is_empty</a></div><div class="ttdeci">constexpr auto is_empty</div><div class="ttdoc">Returns whether the iterable is empty.</div><div class="ttdef"><b>Definition:</b> is_empty.hpp:33</div></div>
<div class="ttc" id="agroup__group-_iterable_html_ga8a67ea10e8082dbe6705e573fa978444"><div class="ttname"><a href="group__group-_iterable.html#ga8a67ea10e8082dbe6705e573fa978444">boost::hana::front</a></div><div class="ttdeci">constexpr auto front</div><div class="ttdoc">Returns the first element of a non-empty iterable.</div><div class="ttdef"><b>Definition:</b> front.hpp:32</div></div>
<div class="ttc" id="agroup__group-_iterable_html_gad23ce0a4906e2bb0a52f38837b134757"><div class="ttname"><a href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757">boost::hana::drop_front</a></div><div class="ttdeci">constexpr auto drop_front</div><div class="ttdoc">Drop the first n elements of an iterable, and return the rest.</div><div class="ttdef"><b>Definition:</b> drop_front.hpp:47</div></div>
<div class="ttc" id="ais__empty_8hpp_html"><div class="ttname"><a href="is__empty_8hpp.html">is_empty.hpp</a></div><div class="ttdoc">Defines boost::hana::is_empty.</div></div>
</div><!-- fragment --></li>
<li><code>Searchable</code> (definition provided automatically)<br  />
Searching through a <code>Sequence</code> is equivalent to just searching through a list of the values it contains. The keys and the values on which the search is performed are both the elements of the sequence. <div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="contains_8hpp.html">boost/hana/contains.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="core_2is__a_8hpp.html">boost/hana/core/is_a.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="find__if_8hpp.html">boost/hana/find_if.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="optional_8hpp.html">boost/hana/optional.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;string&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"><span class="keyword">using namespace </span>std::string_literals;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() {</div>
<div class="line">    <a class="code" href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a>(</div>
<div class="line">        <a class="code" href="group__group-_searchable.html#ga7f99b80672aa80a7eb8b223955ce546f">hana::find_if</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3, <span class="stringliteral">&quot;abc&quot;</span>s), hana::is_a&lt;std::string&gt;) == hana::just(<span class="stringliteral">&quot;abc&quot;</span>s)</div>
<div class="line">    );</div>
<div class="line"> </div>
<div class="line">    <a class="code" href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a>(</div>
<div class="line">        <span class="stringliteral">&quot;abc&quot;</span>s ^<a class="code" href="group__group-_searchable.html#ga0d9456ceda38b6ca664998e79d7c45b7">hana::in</a>^ hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3, <span class="stringliteral">&quot;abc&quot;</span>s)</div>
<div class="line">    );</div>
<div class="line">}</div>
<div class="ttc" id="acontains_8hpp_html"><div class="ttname"><a href="contains_8hpp.html">contains.hpp</a></div><div class="ttdoc">Defines boost::hana::contains and boost::hana::in.</div></div>
<div class="ttc" id="acore_2is__a_8hpp_html"><div class="ttname"><a href="core_2is__a_8hpp.html">is_a.hpp</a></div><div class="ttdoc">Defines boost::hana::is_a and boost::hana::is_an.</div></div>
<div class="ttc" id="afind__if_8hpp_html"><div class="ttname"><a href="find__if_8hpp.html">find_if.hpp</a></div><div class="ttdoc">Defines boost::hana::find_if.</div></div>
<div class="ttc" id="agroup__group-_searchable_html_ga0d9456ceda38b6ca664998e79d7c45b7"><div class="ttname"><a href="group__group-_searchable.html#ga0d9456ceda38b6ca664998e79d7c45b7">boost::hana::in</a></div><div class="ttdeci">constexpr auto in</div><div class="ttdoc">Return whether the key occurs in the structure.</div><div class="ttdef"><b>Definition:</b> contains.hpp:70</div></div>
<div class="ttc" id="agroup__group-_searchable_html_ga7f99b80672aa80a7eb8b223955ce546f"><div class="ttname"><a href="group__group-_searchable.html#ga7f99b80672aa80a7eb8b223955ce546f">boost::hana::find_if</a></div><div class="ttdeci">constexpr auto find_if</div><div class="ttdoc">Finds the value associated to the first key satisfying a predicate.</div><div class="ttdef"><b>Definition:</b> find_if.hpp:41</div></div>
<div class="ttc" id="aoptional_8hpp_html"><div class="ttname"><a href="optional_8hpp.html">optional.hpp</a></div><div class="ttdoc">Defines boost::hana::optional.</div></div>
</div><!-- fragment --></li>
</ol>
<h2><a class="anchor" id="autotoc_md174"></a>
Concrete models</h2>
<p><code><a class="el" href="structboost_1_1hana_1_1tuple.html" title="General purpose index-based heterogeneous sequence with a fixed length.">hana::tuple</a></code> </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:gac10231310abc86b056585ea0d0e96ef7"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gac10231310abc86b056585ea0d0e96ef7">boost::hana::drop_back</a></td></tr>
<tr class="memdesc:gac10231310abc86b056585ea0d0e96ef7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Drop the last <code>n</code> elements of a finite sequence, and return the rest.  <a href="group__group-_sequence.html#gac10231310abc86b056585ea0d0e96ef7">More...</a><br /></td></tr>
<tr class="separator:gac10231310abc86b056585ea0d0e96ef7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae22a1a184b1b2dd550fa4fa619bed2e9"><td class="memItemLeft" align="right" valign="top">constexpr insert_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gae22a1a184b1b2dd550fa4fa619bed2e9">boost::hana::insert</a> {}</td></tr>
<tr class="memdesc:gae22a1a184b1b2dd550fa4fa619bed2e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Insert a value at a given index in a sequence.  <a href="group__group-_sequence.html#gae22a1a184b1b2dd550fa4fa619bed2e9">More...</a><br /></td></tr>
<tr class="separator:gae22a1a184b1b2dd550fa4fa619bed2e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3410ba833cf1ff1d929fcfda4df2eae1"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga3410ba833cf1ff1d929fcfda4df2eae1">boost::hana::insert_range</a></td></tr>
<tr class="memdesc:ga3410ba833cf1ff1d929fcfda4df2eae1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Insert several values at a given index in a sequence.  <a href="group__group-_sequence.html#ga3410ba833cf1ff1d929fcfda4df2eae1">More...</a><br /></td></tr>
<tr class="separator:ga3410ba833cf1ff1d929fcfda4df2eae1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa18061cd0f63cfaae89abf43ff92b79e"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gaa18061cd0f63cfaae89abf43ff92b79e">boost::hana::intersperse</a></td></tr>
<tr class="memdesc:gaa18061cd0f63cfaae89abf43ff92b79e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Insert a value between each pair of elements in a finite sequence.  <a href="group__group-_sequence.html#gaa18061cd0f63cfaae89abf43ff92b79e">More...</a><br /></td></tr>
<tr class="separator:gaa18061cd0f63cfaae89abf43ff92b79e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac1e182ac088f1990edd739424d30ea07"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gac1e182ac088f1990edd739424d30ea07">boost::hana::permutations</a></td></tr>
<tr class="memdesc:gac1e182ac088f1990edd739424d30ea07"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return a sequence of all the permutations of the given sequence.  <a href="group__group-_sequence.html#gac1e182ac088f1990edd739424d30ea07">More...</a><br /></td></tr>
<tr class="separator:gac1e182ac088f1990edd739424d30ea07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga80724ec8ecf319a1e695988a69e22f87"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga80724ec8ecf319a1e695988a69e22f87">boost::hana::remove_at</a></td></tr>
<tr class="memdesc:ga80724ec8ecf319a1e695988a69e22f87"><td class="mdescLeft">&#160;</td><td class="mdescRight">Remove the element at a given index from a sequence.  <a href="group__group-_sequence.html#ga80724ec8ecf319a1e695988a69e22f87">More...</a><br /></td></tr>
<tr class="separator:ga80724ec8ecf319a1e695988a69e22f87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae70b0815645c7d81bb636a1eed1a65c6"><td class="memTemplParams" colspan="2">template&lt;std::size_t n&gt; </td></tr>
<tr class="memitem:gae70b0815645c7d81bb636a1eed1a65c6"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gae70b0815645c7d81bb636a1eed1a65c6">boost::hana::remove_at_c</a></td></tr>
<tr class="memdesc:gae70b0815645c7d81bb636a1eed1a65c6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>remove_at</code>; provided for convenience.  <a href="group__group-_sequence.html#gae70b0815645c7d81bb636a1eed1a65c6">More...</a><br /></td></tr>
<tr class="separator:gae70b0815645c7d81bb636a1eed1a65c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6f6d5c1f335780c91d29626fde615c78"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga6f6d5c1f335780c91d29626fde615c78">boost::hana::remove_range</a></td></tr>
<tr class="memdesc:ga6f6d5c1f335780c91d29626fde615c78"><td class="mdescLeft">&#160;</td><td class="mdescRight">Remove the elements inside a given range of indices from a sequence.  <a href="group__group-_sequence.html#ga6f6d5c1f335780c91d29626fde615c78">More...</a><br /></td></tr>
<tr class="separator:ga6f6d5c1f335780c91d29626fde615c78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4696efcdee7d95ab4a391bb896a840b5"><td class="memTemplParams" colspan="2">template&lt;std::size_t from, std::size_t to&gt; </td></tr>
<tr class="memitem:ga4696efcdee7d95ab4a391bb896a840b5"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga4696efcdee7d95ab4a391bb896a840b5">boost::hana::remove_range_c</a></td></tr>
<tr class="memdesc:ga4696efcdee7d95ab4a391bb896a840b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>remove_range</code>; provided for convenience.  <a href="group__group-_sequence.html#ga4696efcdee7d95ab4a391bb896a840b5">More...</a><br /></td></tr>
<tr class="separator:ga4696efcdee7d95ab4a391bb896a840b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga28037560e8f224c53cf6ac168d03a067"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga28037560e8f224c53cf6ac168d03a067">boost::hana::reverse</a></td></tr>
<tr class="memdesc:ga28037560e8f224c53cf6ac168d03a067"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reverse a sequence.  <a href="group__group-_sequence.html#ga28037560e8f224c53cf6ac168d03a067">More...</a><br /></td></tr>
<tr class="separator:ga28037560e8f224c53cf6ac168d03a067"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec484fb349500149d90717f6e68f7bcd"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gaec484fb349500149d90717f6e68f7bcd">boost::hana::scan_left</a></td></tr>
<tr class="memdesc:gaec484fb349500149d90717f6e68f7bcd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Fold a Sequence to the left and return a list containing the successive reduction states.  <a href="group__group-_sequence.html#gaec484fb349500149d90717f6e68f7bcd">More...</a><br /></td></tr>
<tr class="separator:gaec484fb349500149d90717f6e68f7bcd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga54d141f901866dfab29b052857123bab"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga54d141f901866dfab29b052857123bab">boost::hana::scan_right</a></td></tr>
<tr class="memdesc:ga54d141f901866dfab29b052857123bab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Fold a Sequence to the right and return a list containing the successive reduction states.  <a href="group__group-_sequence.html#ga54d141f901866dfab29b052857123bab">More...</a><br /></td></tr>
<tr class="separator:ga54d141f901866dfab29b052857123bab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga245d8abaf6ba67e64020be51c8366081"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga245d8abaf6ba67e64020be51c8366081">boost::hana::slice</a></td></tr>
<tr class="memdesc:ga245d8abaf6ba67e64020be51c8366081"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract the elements of a <code>Sequence</code> at the given indices.  <a href="group__group-_sequence.html#ga245d8abaf6ba67e64020be51c8366081">More...</a><br /></td></tr>
<tr class="separator:ga245d8abaf6ba67e64020be51c8366081"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae1f6a2a9cb70564d43c6b3c663b25dd7"><td class="memTemplParams" colspan="2">template&lt;std::size_t from, std::size_t to&gt; </td></tr>
<tr class="memitem:gae1f6a2a9cb70564d43c6b3c663b25dd7"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gae1f6a2a9cb70564d43c6b3c663b25dd7">boost::hana::slice_c</a></td></tr>
<tr class="memdesc:gae1f6a2a9cb70564d43c6b3c663b25dd7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shorthand to <code>slice</code> a contiguous range of elements.  <a href="group__group-_sequence.html#gae1f6a2a9cb70564d43c6b3c663b25dd7">More...</a><br /></td></tr>
<tr class="separator:gae1f6a2a9cb70564d43c6b3c663b25dd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8d302de01b94b4b17f3bd81e09f42920"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga8d302de01b94b4b17f3bd81e09f42920">boost::hana::take_back</a></td></tr>
<tr class="memdesc:ga8d302de01b94b4b17f3bd81e09f42920"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the last <code>n</code> elements of a sequence, or the whole sequence if the sequence has less than <code>n</code> elements.  <a href="group__group-_sequence.html#ga8d302de01b94b4b17f3bd81e09f42920">More...</a><br /></td></tr>
<tr class="separator:ga8d302de01b94b4b17f3bd81e09f42920"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa4d4818952083e3b27c83b0ed645e322"><td class="memTemplParams" colspan="2">template&lt;std::size_t n&gt; </td></tr>
<tr class="memitem:gaa4d4818952083e3b27c83b0ed645e322"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gaa4d4818952083e3b27c83b0ed645e322">boost::hana::take_back_c</a></td></tr>
<tr class="memdesc:gaa4d4818952083e3b27c83b0ed645e322"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>take_back</code>; provided for convenience.  <a href="group__group-_sequence.html#gaa4d4818952083e3b27c83b0ed645e322">More...</a><br /></td></tr>
<tr class="separator:gaa4d4818952083e3b27c83b0ed645e322"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5112e6070d29b4f7fde3f44825da3316"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga5112e6070d29b4f7fde3f44825da3316">boost::hana::take_front</a></td></tr>
<tr class="memdesc:ga5112e6070d29b4f7fde3f44825da3316"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the first <code>n</code> elements of a sequence, or the whole sequence if the sequence has less than <code>n</code> elements.  <a href="group__group-_sequence.html#ga5112e6070d29b4f7fde3f44825da3316">More...</a><br /></td></tr>
<tr class="separator:ga5112e6070d29b4f7fde3f44825da3316"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3779f62fea92af00113a9290f1c680eb"><td class="memTemplParams" colspan="2">template&lt;std::size_t n&gt; </td></tr>
<tr class="memitem:ga3779f62fea92af00113a9290f1c680eb"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga3779f62fea92af00113a9290f1c680eb">boost::hana::take_front_c</a></td></tr>
<tr class="memdesc:ga3779f62fea92af00113a9290f1c680eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>take_front</code>; provided for convenience.  <a href="group__group-_sequence.html#ga3779f62fea92af00113a9290f1c680eb">More...</a><br /></td></tr>
<tr class="separator:ga3779f62fea92af00113a9290f1c680eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2d4db4ec5ec5bc16fe74f57de12697fd"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga2d4db4ec5ec5bc16fe74f57de12697fd">boost::hana::take_while</a></td></tr>
<tr class="memdesc:ga2d4db4ec5ec5bc16fe74f57de12697fd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Take elements from a sequence while the <code>predicate</code> is satisfied.  <a href="group__group-_sequence.html#ga2d4db4ec5ec5bc16fe74f57de12697fd">More...</a><br /></td></tr>
<tr class="separator:ga2d4db4ec5ec5bc16fe74f57de12697fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa5a378d4e71a91e0d6cd3959d9818e8a"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gaa5a378d4e71a91e0d6cd3959d9818e8a">boost::hana::zip</a></td></tr>
<tr class="memdesc:gaa5a378d4e71a91e0d6cd3959d9818e8a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Zip one sequence or more.  <a href="group__group-_sequence.html#gaa5a378d4e71a91e0d6cd3959d9818e8a">More...</a><br /></td></tr>
<tr class="separator:gaa5a378d4e71a91e0d6cd3959d9818e8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade78593b3ff51fc5479e1da97142fef5"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gade78593b3ff51fc5479e1da97142fef5">boost::hana::zip_shortest</a></td></tr>
<tr class="memdesc:gade78593b3ff51fc5479e1da97142fef5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Zip one sequence or more.  <a href="group__group-_sequence.html#gade78593b3ff51fc5479e1da97142fef5">More...</a><br /></td></tr>
<tr class="separator:gade78593b3ff51fc5479e1da97142fef5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae7a51104a77db79a0407d7d67b034667"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gae7a51104a77db79a0407d7d67b034667">boost::hana::zip_shortest_with</a></td></tr>
<tr class="memdesc:gae7a51104a77db79a0407d7d67b034667"><td class="mdescLeft">&#160;</td><td class="mdescRight">Zip one sequence or more with a given function.  <a href="group__group-_sequence.html#gae7a51104a77db79a0407d7d67b034667">More...</a><br /></td></tr>
<tr class="separator:gae7a51104a77db79a0407d7d67b034667"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6a4bf8549ce69b5b5b7377aec225a0e3"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga6a4bf8549ce69b5b5b7377aec225a0e3">boost::hana::zip_with</a></td></tr>
<tr class="memdesc:ga6a4bf8549ce69b5b5b7377aec225a0e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Zip one sequence or more with a given function.  <a href="group__group-_sequence.html#ga6a4bf8549ce69b5b5b7377aec225a0e3">More...</a><br /></td></tr>
<tr class="separator:ga6a4bf8549ce69b5b5b7377aec225a0e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Variable Documentation</h2>
<a id="gac10231310abc86b056585ea0d0e96ef7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac10231310abc86b056585ea0d0e96ef7">&#9670;&nbsp;</a></span>drop_back</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::drop_back</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2drop__back_8hpp.html">boost/hana/fwd/drop_back.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs[, <span class="keyword">auto</span> <span class="keyword">const</span>&amp; n]) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Drop the last <code>n</code> elements of a finite sequence, and return the rest. </p>
<p>Given a finite <code>Sequence</code> <code>xs</code> with a linearization of <code>[x1, ..., xm]</code> and a non-negative <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> <code>n</code>, <code>drop_back(xs, n)</code> is a sequence with the same tag as <code>xs</code> whose linearization is <code>[x1, ..., xm-n]</code>. If <code>n</code> is not given, it defaults to an <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> with a value equal to <code>1</code>.</p>
<p>In case <code>length(xs) &lt;= n</code>, <code>drop_back</code> will simply drop the whole sequence without failing, thus returning an empty sequence.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The sequence from which elements are dropped.</td></tr>
    <tr><td class="paramname">n</td><td>A non-negative <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> representing the number of elements to be dropped from the end of the sequence. If <code>n</code> is not given, it defaults to an <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> with a value equal to <code>1</code>.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md205"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="drop__back_8hpp.html">boost/hana/drop_back.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">constexpr <span class="keyword">auto</span> xs = hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 2.0);</div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#gac10231310abc86b056585ea0d0e96ef7">hana::drop_back</a>(xs, hana::size_c&lt;0&gt;) == xs, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#gac10231310abc86b056585ea0d0e96ef7">hana::drop_back</a>(xs, hana::size_c&lt;1&gt;) == hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#gac10231310abc86b056585ea0d0e96ef7">hana::drop_back</a>(xs, hana::size_c&lt;2&gt;) == hana::make_tuple(0), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(<a class="code" href="group__group-_sequence.html#gac10231310abc86b056585ea0d0e96ef7">hana::drop_back</a>(xs, hana::size_c&lt;3&gt;) == hana::make_tuple());</div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(<a class="code" href="group__group-_sequence.html#gac10231310abc86b056585ea0d0e96ef7">hana::drop_back</a>(xs, hana::size_c&lt;4&gt;) == hana::make_tuple());</div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#gac10231310abc86b056585ea0d0e96ef7">hana::drop_back</a>(xs) == hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="adrop__back_8hpp_html"><div class="ttname"><a href="drop__back_8hpp.html">drop_back.hpp</a></div><div class="ttdoc">Defines boost::hana::drop_back.</div></div>
<div class="ttc" id="agroup__group-_sequence_html_gac10231310abc86b056585ea0d0e96ef7"><div class="ttname"><a href="group__group-_sequence.html#gac10231310abc86b056585ea0d0e96ef7">boost::hana::drop_back</a></div><div class="ttdeci">constexpr auto drop_back</div><div class="ttdoc">Drop the last n elements of a finite sequence, and return the rest.</div><div class="ttdef"><b>Definition:</b> drop_back.hpp:44</div></div>
<div class="ttc" id="aintegral__constant_8hpp_html"><div class="ttname"><a href="integral__constant_8hpp.html">integral_constant.hpp</a></div><div class="ttdoc">Defines boost::hana::integral_constant.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="gae22a1a184b1b2dd550fa4fa619bed2e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae22a1a184b1b2dd550fa4fa619bed2e9">&#9670;&nbsp;</a></span>insert</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::insert {}</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">related</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2insert_8hpp.html">boost/hana/fwd/insert.hpp</a>&gt;</code></p>

<p>Insert a value at a given index in a sequence. </p>
<p>Given a sequence, an index and an element to insert, <code>insert</code> inserts the element at the given index.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The sequence in which a value should be inserted.</td></tr>
    <tr><td class="paramname">n</td><td>The index at which an element should be inserted. This must be a non-negative <code>Constant</code> of an integral type, and it must also be true that <code>n &lt; length(xs)</code> if <code>xs</code> is a finite sequence.</td></tr>
    <tr><td class="paramname">element</td><td>The element to insert in the sequence.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md240"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="insert_8hpp.html">boost/hana/insert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;string&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"><span class="keyword">using namespace </span>hana::literals;</div>
<div class="line"><span class="keyword">using namespace </span>std::literals;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() {</div>
<div class="line">    <span class="keyword">auto</span> xs = hana::make_tuple(<span class="stringliteral">&quot;Hello&quot;</span>s, <span class="stringliteral">&quot;world!&quot;</span>s);</div>
<div class="line"> </div>
<div class="line">    <a class="code" href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a>(</div>
<div class="line">        <a class="code" href="group__group-_sequence.html#gae22a1a184b1b2dd550fa4fa619bed2e9">hana::insert</a>(xs, 1_c, <span class="stringliteral">&quot; &quot;</span>s) == hana::make_tuple(<span class="stringliteral">&quot;Hello&quot;</span>s, <span class="stringliteral">&quot; &quot;</span>s, <span class="stringliteral">&quot;world!&quot;</span>s)</div>
<div class="line">    );</div>
<div class="line">}</div>
<div class="ttc" id="agroup__group-_sequence_html_gae22a1a184b1b2dd550fa4fa619bed2e9"><div class="ttname"><a href="group__group-_sequence.html#gae22a1a184b1b2dd550fa4fa619bed2e9">boost::hana::insert</a></div><div class="ttdeci">constexpr insert_t insert</div><div class="ttdoc">Insert a value at a given index in a sequence.</div><div class="ttdef"><b>Definition:</b> insert.hpp:29</div></div>
<div class="ttc" id="ainsert_8hpp_html"><div class="ttname"><a href="insert_8hpp.html">insert.hpp</a></div><div class="ttdoc">Defines boost::hana::insert.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga3410ba833cf1ff1d929fcfda4df2eae1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3410ba833cf1ff1d929fcfda4df2eae1">&#9670;&nbsp;</a></span>insert_range</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::insert_range</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2insert__range_8hpp.html">boost/hana/fwd/insert_range.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs, <span class="keyword">auto</span>&amp;&amp; n, <span class="keyword">auto</span>&amp;&amp; elements) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Insert several values at a given index in a sequence. </p>
<p>Given a sequence, an index and any <code>Foldable</code> containing elements to insert, <code>insert_range</code> inserts the elements in the <code>Foldable</code> at the given index of the sequence.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The sequence in which values should be inserted.</td></tr>
    <tr><td class="paramname">n</td><td>The index at which elements should be inserted. This must be a non-negative <code>Constant</code> of an integral type, and it must also be true that <code>n &lt; length(xs)</code> if <code>xs</code> is a finite sequence.</td></tr>
    <tr><td class="paramname">elements</td><td>A <code>Foldable</code> containing elements to insert in the sequence.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md241"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="insert__range_8hpp.html">boost/hana/insert_range.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;string&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"><span class="keyword">using namespace </span>hana::literals;</div>
<div class="line"><span class="keyword">using namespace </span>std::literals;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() {</div>
<div class="line">    <span class="keyword">auto</span> xs = hana::make_tuple(<span class="stringliteral">&quot;Hello&quot;</span>s, <span class="stringliteral">&quot;world!&quot;</span>s);</div>
<div class="line">    <a class="code" href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a>(</div>
<div class="line">        <a class="code" href="group__group-_sequence.html#ga3410ba833cf1ff1d929fcfda4df2eae1">hana::insert_range</a>(xs, 1_c, hana::make_tuple(1, 2, 3)) == hana::make_tuple(<span class="stringliteral">&quot;Hello&quot;</span>s, 1, 2, 3, <span class="stringliteral">&quot;world!&quot;</span>s)</div>
<div class="line">    );</div>
<div class="line">}</div>
<div class="ttc" id="agroup__group-_sequence_html_ga3410ba833cf1ff1d929fcfda4df2eae1"><div class="ttname"><a href="group__group-_sequence.html#ga3410ba833cf1ff1d929fcfda4df2eae1">boost::hana::insert_range</a></div><div class="ttdeci">constexpr auto insert_range</div><div class="ttdoc">Insert several values at a given index in a sequence.</div><div class="ttdef"><b>Definition:</b> insert_range.hpp:41</div></div>
<div class="ttc" id="ainsert__range_8hpp_html"><div class="ttname"><a href="insert__range_8hpp.html">insert_range.hpp</a></div><div class="ttdoc">Defines boost::hana::insert_range.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="gaa18061cd0f63cfaae89abf43ff92b79e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa18061cd0f63cfaae89abf43ff92b79e">&#9670;&nbsp;</a></span>intersperse</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::intersperse</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2intersperse_8hpp.html">boost/hana/fwd/intersperse.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs, <span class="keyword">auto</span>&amp;&amp; z) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Insert a value between each pair of elements in a finite sequence. </p>
<p>Given a finite <code>Sequence</code> <code>xs</code> with a linearization of <code>[x1, x2, ..., xn]</code>, <code>intersperse(xs, z)</code> is a new sequence with a linearization of <code>[x1, z, x2, z, x3, ..., xn-1, z, xn]</code>. In other words, it inserts the <code>z</code> element between every pair of elements of the original sequence. If the sequence is empty or has a single element, <code>intersperse</code> returns the sequence as-is. In all cases, the sequence must be finite.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The sequence in which a value is interspersed.</td></tr>
    <tr><td class="paramname">z</td><td>The value to be inserted between every pair of elements of the sequence.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md244"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="intersperse_8hpp.html">boost/hana/intersperse.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#gaa18061cd0f63cfaae89abf43ff92b79e">hana::intersperse</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), <span class="charliteral">&#39;x&#39;</span>) == hana::make_tuple(1, <span class="charliteral">&#39;x&#39;</span>, <span class="charliteral">&#39;2&#39;</span>, <span class="charliteral">&#39;x&#39;</span>, 3.3), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(<a class="code" href="group__group-_sequence.html#gaa18061cd0f63cfaae89abf43ff92b79e">hana::intersperse</a>(hana::make_tuple(), <span class="charliteral">&#39;x&#39;</span>) == hana::make_tuple());</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="agroup__group-_sequence_html_gaa18061cd0f63cfaae89abf43ff92b79e"><div class="ttname"><a href="group__group-_sequence.html#gaa18061cd0f63cfaae89abf43ff92b79e">boost::hana::intersperse</a></div><div class="ttdeci">constexpr auto intersperse</div><div class="ttdoc">Insert a value between each pair of elements in a finite sequence.</div><div class="ttdef"><b>Definition:</b> intersperse.hpp:41</div></div>
<div class="ttc" id="aintersperse_8hpp_html"><div class="ttname"><a href="intersperse_8hpp.html">intersperse.hpp</a></div><div class="ttdoc">Defines boost::hana::intersperse.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="gac1e182ac088f1990edd739424d30ea07"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac1e182ac088f1990edd739424d30ea07">&#9670;&nbsp;</a></span>permutations</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::permutations</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2permutations_8hpp.html">boost/hana/fwd/permutations.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Return a sequence of all the permutations of the given sequence. </p>
<p>Specifically, <code>permutations(xs)</code> is a sequence whose elements are permutations of the original sequence <code>xs</code>. The permutations are not guaranteed to be in any specific order. Also note that the number of permutations grows very rapidly as the length of the original sequence increases. The growth rate is <code>O(length(xs)!)</code>; with a sequence <code>xs</code> of length only 8, <code>permutations(xs)</code> contains over 40 000 elements!</p>
<h2><a class="anchor" id="autotoc_md316"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="all__of_8hpp.html">boost/hana/all_of.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="config_8hpp.html">boost/hana/config.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="contains_8hpp.html">boost/hana/contains.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="curry_8hpp.html">boost/hana/functional/curry.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="permutations_8hpp.html">boost/hana/permutations.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">BOOST_HANA_CONSTEXPR_LAMBDA <span class="keyword">auto</span> is_permutation_of = hana::curry&lt;2&gt;([](<span class="keyword">auto</span> xs, <span class="keyword">auto</span> perm) {</div>
<div class="line">    <span class="keywordflow">return</span> <a class="code" href="group__group-_searchable.html#ga38e7748956cbc9f3d9bb035ac8577906">hana::contains</a>(<a class="code" href="group__group-_sequence.html#gac1e182ac088f1990edd739424d30ea07">hana::permutations</a>(xs), perm);</div>
<div class="line">});</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() {</div>
<div class="line">    <a class="code" href="group__group-assertions.html#gac7aafc41e4dcc7d1f1929fb00f010d2a">BOOST_HANA_CONSTEXPR_CHECK</a>(</div>
<div class="line">        <a class="code" href="group__group-_searchable.html#ga3a168950082f38afd9edf256f336c8ba">hana::all_of</a>(</div>
<div class="line">            hana::make_tuple(</div>
<div class="line">                hana::make_tuple(<span class="charliteral">&#39;1&#39;</span>, 2, 3.0),</div>
<div class="line">                hana::make_tuple(<span class="charliteral">&#39;1&#39;</span>, 3.0, 2),</div>
<div class="line">                hana::make_tuple(2, <span class="charliteral">&#39;1&#39;</span>, 3.0),</div>
<div class="line">                hana::make_tuple(2, 3.0, <span class="charliteral">&#39;1&#39;</span>),</div>
<div class="line">                hana::make_tuple(3.0, <span class="charliteral">&#39;1&#39;</span>, 2),</div>
<div class="line">                hana::make_tuple(3.0, 2, <span class="charliteral">&#39;1&#39;</span>)</div>
<div class="line">            ),</div>
<div class="line">            is_permutation_of(hana::make_tuple(<span class="charliteral">&#39;1&#39;</span>, 2, 3.0))</div>
<div class="line">        )</div>
<div class="line">    );</div>
<div class="line">}</div>
<div class="ttc" id="aall__of_8hpp_html"><div class="ttname"><a href="all__of_8hpp.html">all_of.hpp</a></div><div class="ttdoc">Defines boost::hana::all_of.</div></div>
<div class="ttc" id="aconfig_8hpp_html"><div class="ttname"><a href="config_8hpp.html">config.hpp</a></div><div class="ttdoc">Defines configuration macros used throughout the library.</div></div>
<div class="ttc" id="acurry_8hpp_html"><div class="ttname"><a href="curry_8hpp.html">curry.hpp</a></div><div class="ttdoc">Defines boost::hana::curry.</div></div>
<div class="ttc" id="agroup__group-_searchable_html_ga38e7748956cbc9f3d9bb035ac8577906"><div class="ttname"><a href="group__group-_searchable.html#ga38e7748956cbc9f3d9bb035ac8577906">boost::hana::contains</a></div><div class="ttdeci">constexpr auto contains</div><div class="ttdoc">Returns whether the key occurs in the structure.</div><div class="ttdef"><b>Definition:</b> contains.hpp:42</div></div>
<div class="ttc" id="agroup__group-_searchable_html_ga3a168950082f38afd9edf256f336c8ba"><div class="ttname"><a href="group__group-_searchable.html#ga3a168950082f38afd9edf256f336c8ba">boost::hana::all_of</a></div><div class="ttdeci">constexpr auto all_of</div><div class="ttdoc">Returns whether all the keys of the structure satisfy the predicate.</div><div class="ttdef"><b>Definition:</b> all_of.hpp:38</div></div>
<div class="ttc" id="agroup__group-_sequence_html_gac1e182ac088f1990edd739424d30ea07"><div class="ttname"><a href="group__group-_sequence.html#gac1e182ac088f1990edd739424d30ea07">boost::hana::permutations</a></div><div class="ttdeci">constexpr auto permutations</div><div class="ttdoc">Return a sequence of all the permutations of the given sequence.</div><div class="ttdef"><b>Definition:</b> permutations.hpp:34</div></div>
<div class="ttc" id="agroup__group-assertions_html_gac7aafc41e4dcc7d1f1929fb00f010d2a"><div class="ttname"><a href="group__group-assertions.html#gac7aafc41e4dcc7d1f1929fb00f010d2a">BOOST_HANA_CONSTEXPR_CHECK</a></div><div class="ttdeci">#define BOOST_HANA_CONSTEXPR_CHECK(...)</div><div class="ttdoc">Equivalent to BOOST_HANA_CONSTEXPR_ASSERT, but not influenced by the BOOST_HANA_CONFIG_DISABLE_ASSERT...</div><div class="ttdef"><b>Definition:</b> assert.hpp:300</div></div>
<div class="ttc" id="apermutations_8hpp_html"><div class="ttname"><a href="permutations_8hpp.html">permutations.hpp</a></div><div class="ttdoc">Defines boost::hana::permutations.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga80724ec8ecf319a1e695988a69e22f87"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga80724ec8ecf319a1e695988a69e22f87">&#9670;&nbsp;</a></span>remove_at</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::remove_at</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2remove__at_8hpp.html">boost/hana/fwd/remove_at.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs, <span class="keyword">auto</span> <span class="keyword">const</span>&amp; n) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Remove the element at a given index from a sequence. </p>
<p><code>remove_at</code> returns a new sequence identical to the original, except that the element at the given index is removed. Specifically, <code>remove_at([x0, ..., xn-1, xn, xn+1, ..., xm], n)</code> is a new sequence equivalent to <code>[x0, ..., xn-1, xn+1, ..., xm]</code>.</p>
<dl class="section note"><dt>Note</dt><dd>The behavior is undefined if the index is out of the bounds of the sequence.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>A sequence from which an element is to be removed.</td></tr>
    <tr><td class="paramname">n</td><td>An non-negative <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> representing the index of the element to be removed from the sequence. The behavior is undefined if that index is not in the bounds of the sequence.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md329"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="remove__at_8hpp.html">boost/hana/remove_at.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">constexpr <span class="keyword">auto</span> xs = hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 2.2, 3u);</div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#ga80724ec8ecf319a1e695988a69e22f87">hana::remove_at</a>(xs, hana::size_c&lt;2&gt;) == hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 3u), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="agroup__group-_sequence_html_ga80724ec8ecf319a1e695988a69e22f87"><div class="ttname"><a href="group__group-_sequence.html#ga80724ec8ecf319a1e695988a69e22f87">boost::hana::remove_at</a></div><div class="ttdeci">constexpr auto remove_at</div><div class="ttdoc">Remove the element at a given index from a sequence.</div><div class="ttdef"><b>Definition:</b> remove_at.hpp:46</div></div>
<div class="ttc" id="aremove__at_8hpp_html"><div class="ttname"><a href="remove__at_8hpp.html">remove_at.hpp</a></div><div class="ttdoc">Defines boost::hana::remove_at and boost::hana::remove_at_c.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="gae70b0815645c7d81bb636a1eed1a65c6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae70b0815645c7d81bb636a1eed1a65c6">&#9670;&nbsp;</a></span>remove_at_c</h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;std::size_t n&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::remove_at_c</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2remove__at_8hpp.html">boost/hana/fwd/remove_at.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs) {</div>
<div class="line">        <span class="keywordflow">return</span> <a class="code" href="group__group-_sequence.html#ga80724ec8ecf319a1e695988a69e22f87">hana::remove_at</a>(forwarded(xs), hana::size_c&lt;n&gt;);</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Equivalent to <code>remove_at</code>; provided for convenience. </p>
<h2><a class="anchor" id="autotoc_md330"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="remove__at_8hpp.html">boost/hana/remove_at.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">constexpr <span class="keyword">auto</span> xs = hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 2.2, 3u);</div>
<div class="line"> </div>
<div class="line">static_assert(hana::remove_at_c&lt;2&gt;(xs) == hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 3u), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga6f6d5c1f335780c91d29626fde615c78"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6f6d5c1f335780c91d29626fde615c78">&#9670;&nbsp;</a></span>remove_range</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::remove_range</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2remove__range_8hpp.html">boost/hana/fwd/remove_range.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs, <span class="keyword">auto</span> <span class="keyword">const</span>&amp; from, <span class="keyword">auto</span> <span class="keyword">const</span>&amp; <a class="code" href="group__group-core.html#gadc70755c1d059139297814fb3bfeb91e">to</a>) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
<div class="ttc" id="agroup__group-core_html_gadc70755c1d059139297814fb3bfeb91e"><div class="ttname"><a href="group__group-core.html#gadc70755c1d059139297814fb3bfeb91e">boost::hana::to</a></div><div class="ttdeci">constexpr auto to</div><div class="ttdoc">Converts an object from one data type to another.</div><div class="ttdef"><b>Definition:</b> to.hpp:97</div></div>
</div><!-- fragment -->
<p>Remove the elements inside a given range of indices from a sequence. </p>
<p><code>remove_range</code> returns a new sequence identical to the original, except that elements at indices in the provided range are removed. Specifically, <code>remove_range([x0, ..., xn], from, to)</code> is a new sequence equivalent to <code>[x0, ..., x_from-1, x_to, ..., xn]</code>.</p>
<dl class="section note"><dt>Note</dt><dd>The behavior is undefined if the range contains any index out of the bounds of the sequence.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>A sequence from which elements are removed.</td></tr>
    <tr><td class="paramname">[from,to)</td><td>An half-open interval of <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code>s representing the indices of the elements to be removed from the sequence. The <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code>s in the half-open interval must be non-negative and in the bounds of the sequence. The half-open interval must also be valid, meaning that <code>from &lt;= to</code>.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md332"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="remove__range_8hpp.html">boost/hana/remove_range.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">constexpr <span class="keyword">auto</span> xs = hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 2.2, 3u, 4, 5.5);</div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#ga6f6d5c1f335780c91d29626fde615c78">hana::remove_range</a>(xs, hana::size_c&lt;2&gt;, hana::size_c&lt;4&gt;) == hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 4, 5.5), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="agroup__group-_sequence_html_ga6f6d5c1f335780c91d29626fde615c78"><div class="ttname"><a href="group__group-_sequence.html#ga6f6d5c1f335780c91d29626fde615c78">boost::hana::remove_range</a></div><div class="ttdeci">constexpr auto remove_range</div><div class="ttdoc">Remove the elements inside a given range of indices from a sequence.</div><div class="ttdef"><b>Definition:</b> remove_range.hpp:49</div></div>
<div class="ttc" id="aremove__range_8hpp_html"><div class="ttname"><a href="remove__range_8hpp.html">remove_range.hpp</a></div><div class="ttdoc">Defines boost::hana::remove_range and boost::hana::remove_range_c.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga4696efcdee7d95ab4a391bb896a840b5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4696efcdee7d95ab4a391bb896a840b5">&#9670;&nbsp;</a></span>remove_range_c</h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;std::size_t from, std::size_t to&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::remove_range_c</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2remove__range_8hpp.html">boost/hana/fwd/remove_range.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs) {</div>
<div class="line">        <span class="keywordflow">return</span> <a class="code" href="group__group-_sequence.html#ga6f6d5c1f335780c91d29626fde615c78">hana::remove_range</a>(forwarded(xs), hana::size_c&lt;from&gt;, hana::size_c&lt;to&gt;);</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Equivalent to <code>remove_range</code>; provided for convenience. </p>
<h2><a class="anchor" id="autotoc_md333"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="remove__range_8hpp.html">boost/hana/remove_range.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">constexpr <span class="keyword">auto</span> xs = hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 2.2, 3u, 4, 5.5);</div>
<div class="line"> </div>
<div class="line">static_assert(hana::remove_range_c&lt;2, 4&gt;(xs) == hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 4, 5.5), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga28037560e8f224c53cf6ac168d03a067"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga28037560e8f224c53cf6ac168d03a067">&#9670;&nbsp;</a></span>reverse</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::reverse</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2reverse_8hpp.html">boost/hana/fwd/reverse.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Reverse a sequence. </p>
<p>Specifically, <code>reverse(xs)</code> is a new sequence containing the same elements as <code>xs</code>, except in reverse order.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The sequence to reverse.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md339"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="reverse_8hpp.html">boost/hana/reverse.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#ga28037560e8f224c53cf6ac168d03a067">hana::reverse</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3)) == hana::make_tuple(3.3, <span class="charliteral">&#39;2&#39;</span>, 1), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="agroup__group-_sequence_html_ga28037560e8f224c53cf6ac168d03a067"><div class="ttname"><a href="group__group-_sequence.html#ga28037560e8f224c53cf6ac168d03a067">boost::hana::reverse</a></div><div class="ttdeci">constexpr auto reverse</div><div class="ttdoc">Reverse a sequence.</div><div class="ttdef"><b>Definition:</b> reverse.hpp:33</div></div>
<div class="ttc" id="areverse_8hpp_html"><div class="ttname"><a href="reverse_8hpp.html">reverse.hpp</a></div><div class="ttdoc">Defines boost::hana::reverse.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="gaec484fb349500149d90717f6e68f7bcd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaec484fb349500149d90717f6e68f7bcd">&#9670;&nbsp;</a></span>scan_left</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::scan_left</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2scan__left_8hpp.html">boost/hana/fwd/scan_left.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs[, <span class="keyword">auto</span>&amp;&amp; state], <span class="keyword">auto</span> <span class="keyword">const</span>&amp; f) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Fold a Sequence to the left and return a list containing the successive reduction states. </p>
<p>Like <code>fold_left</code>, <code>scan_left</code> reduces a sequence to a single value using a binary operation. However, unlike <code>fold_left</code>, it builds up a sequence of the intermediary results computed along the way and returns that instead of only the final reduction state. Like <code>fold_left</code>, <code>scan_left</code> can be used with or without an initial reduction state.</p>
<p>When the sequence is empty, two things may arise. If an initial state was provided, a singleton list containing that state is returned. Otherwise, if no initial state was provided, an empty list is returned. In particular, unlike for <code>fold_left</code>, using <code>scan_left</code> on an empty sequence without an initial state is not an error.</p>
<p>More specifically, <code>scan_left([x1, ..., xn], state, f)</code> is a sequence whose <code>i</code>th element is equivalent to <code>fold_left([x1, ..., xi], state, f)</code>. The no-state variant is handled in an analogous way. For illustration, consider this left fold on a short sequence: </p><div class="fragment"><div class="line">fold_left([x1, x2, x3], state, f) == f(f(f(state, x1), x2), x3)</div>
</div><!-- fragment --><p>The analogous sequence generated with <code>scan_left</code> will be </p><div class="fragment"><div class="line"><a class="code" href="group__group-_sequence.html#gaec484fb349500149d90717f6e68f7bcd">scan_left</a>([x1, x2, x3], state, f) == [</div>
<div class="line">    state,</div>
<div class="line">    f(state, x1),</div>
<div class="line">    f(f(state, x1), x2),</div>
<div class="line">    f(f(f(state, x1), x2), x3)</div>
<div class="line">]</div>
<div class="ttc" id="agroup__group-_sequence_html_gaec484fb349500149d90717f6e68f7bcd"><div class="ttname"><a href="group__group-_sequence.html#gaec484fb349500149d90717f6e68f7bcd">boost::hana::scan_left</a></div><div class="ttdeci">constexpr auto scan_left</div><div class="ttdoc">Fold a Sequence to the left and return a list containing the successive reduction states.</div><div class="ttdef"><b>Definition:</b> scan_left.hpp:86</div></div>
</div><!-- fragment --><p>Similarly, consider this left fold (without an initial state) on a short sequence: </p><div class="fragment"><div class="line">fold_left([x1, x2, x3, x4], f) == f(f(f(x1, x2), x3), x4)</div>
</div><!-- fragment --><p>The analogous sequence generated with <code>scan_left</code> will be </p><div class="fragment"><div class="line"><a class="code" href="group__group-_sequence.html#gaec484fb349500149d90717f6e68f7bcd">scan_left</a>([x1, x2, x3, x4], f) == [</div>
<div class="line">    x1,</div>
<div class="line">    f(x1, x2),</div>
<div class="line">    f(f(x1, x2), x3),</div>
<div class="line">    f(f(f(x1, x2), x3), x4)</div>
<div class="line">]</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The sequence to scan from the left.</td></tr>
    <tr><td class="paramname">state</td><td>The (optional) initial reduction state.</td></tr>
    <tr><td class="paramname">f</td><td>A binary function called as <code>f(state, x)</code>, where <code>state</code> is the result accumulated so far and <code>x</code> is an element in the sequence. If no initial state is provided, <code>f</code> is called as <code>f(x1, x2)</code>, where <code>x1</code> and <code>x2</code> are both elements of the sequence.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md341"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="scan__left_8hpp.html">boost/hana/scan_left.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;sstream&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keyword">auto</span> to_string = [](<span class="keyword">auto</span> x) {</div>
<div class="line">    std::ostringstream ss;</div>
<div class="line">    ss &lt;&lt; x;</div>
<div class="line">    <span class="keywordflow">return</span> ss.str();</div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><span class="keyword">auto</span> f = [](<span class="keyword">auto</span> state, <span class="keyword">auto</span> element) {</div>
<div class="line">    <span class="keywordflow">return</span> <span class="stringliteral">&quot;f(&quot;</span> + to_string(state) + <span class="stringliteral">&quot;, &quot;</span> + to_string(element) + <span class="stringliteral">&quot;)&quot;</span>;</div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() {</div>
<div class="line">    <span class="comment">// with initial state</span></div>
<div class="line">    <a class="code" href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a>(<a class="code" href="group__group-_sequence.html#gaec484fb349500149d90717f6e68f7bcd">hana::scan_left</a>(hana::make_tuple(2, <span class="stringliteral">&quot;3&quot;</span>, <span class="charliteral">&#39;4&#39;</span>), 1, f) == hana::make_tuple(</div>
<div class="line">        1,</div>
<div class="line">        <span class="stringliteral">&quot;f(1, 2)&quot;</span>,</div>
<div class="line">        <span class="stringliteral">&quot;f(f(1, 2), 3)&quot;</span>,</div>
<div class="line">        <span class="stringliteral">&quot;f(f(f(1, 2), 3), 4)&quot;</span></div>
<div class="line">    ));</div>
<div class="line"> </div>
<div class="line">    <span class="comment">// without initial state</span></div>
<div class="line">    <a class="code" href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a>(<a class="code" href="group__group-_sequence.html#gaec484fb349500149d90717f6e68f7bcd">hana::scan_left</a>(hana::make_tuple(1, <span class="stringliteral">&quot;2&quot;</span>, <span class="charliteral">&#39;3&#39;</span>), f) == hana::make_tuple(</div>
<div class="line">        1,</div>
<div class="line">        <span class="stringliteral">&quot;f(1, 2)&quot;</span>,</div>
<div class="line">        <span class="stringliteral">&quot;f(f(1, 2), 3)&quot;</span></div>
<div class="line">    ));</div>
<div class="line">}</div>
<div class="ttc" id="ascan__left_8hpp_html"><div class="ttname"><a href="scan__left_8hpp.html">scan_left.hpp</a></div><div class="ttdoc">Defines boost::hana::scan_left.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga54d141f901866dfab29b052857123bab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga54d141f901866dfab29b052857123bab">&#9670;&nbsp;</a></span>scan_right</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::scan_right</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2scan__right_8hpp.html">boost/hana/fwd/scan_right.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs[, <span class="keyword">auto</span>&amp;&amp; state], <span class="keyword">auto</span> <span class="keyword">const</span>&amp; f) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Fold a Sequence to the right and return a list containing the successive reduction states. </p>
<p>Like <code>fold_right</code>, <code>scan_right</code> reduces a sequence to a single value using a binary operation. However, unlike <code>fold_right</code>, it builds up a sequence of the intermediary results computed along the way and returns that instead of only the final reduction state. Like <code>fold_right</code>, <code>scan_right</code> can be used with or without an initial reduction state.</p>
<p>When the sequence is empty, two things may arise. If an initial state was provided, a singleton list containing that state is returned. Otherwise, if no initial state was provided, an empty list is returned. In particular, unlike for <code>fold_right</code>, using <code>scan_right</code> on an empty sequence without an initial state is not an error.</p>
<p>More specifically, <code>scan_right([x1, ..., xn], state, f)</code> is a sequence whose <code>i</code>th element is equivalent to <code>fold_right([x1, ..., xi], state, f)</code>. The no-state variant is handled in an analogous way. For illustration, consider this right fold on a short sequence: </p><div class="fragment"><div class="line">fold_right([x1, x2, x3], state, f) == f(x1, f(x2, f(x3, state)))</div>
</div><!-- fragment --><p>The analogous sequence generated with <code>scan_right</code> will be </p><div class="fragment"><div class="line"><a class="code" href="group__group-_sequence.html#ga54d141f901866dfab29b052857123bab">scan_right</a>([x1, x2, x3], state, f) == [</div>
<div class="line">    f(x1, f(x2, f(x3, state))),</div>
<div class="line">          f(x2, f(x3, state)),</div>
<div class="line">                f(x3, state),</div>
<div class="line">                      state</div>
<div class="line">]</div>
<div class="ttc" id="agroup__group-_sequence_html_ga54d141f901866dfab29b052857123bab"><div class="ttname"><a href="group__group-_sequence.html#ga54d141f901866dfab29b052857123bab">boost::hana::scan_right</a></div><div class="ttdeci">constexpr auto scan_right</div><div class="ttdoc">Fold a Sequence to the right and return a list containing the successive reduction states.</div><div class="ttdef"><b>Definition:</b> scan_right.hpp:86</div></div>
</div><!-- fragment --><p>Similarly, consider this right fold (without an initial state) on a short sequence: </p><div class="fragment"><div class="line">fold_right([x1, x2, x3, x4], f) == f(x1, f(x2, f(x3, x4)))</div>
</div><!-- fragment --><p>The analogous sequence generated with <code>scan_right</code> will be </p><div class="fragment"><div class="line"><a class="code" href="group__group-_sequence.html#ga54d141f901866dfab29b052857123bab">scan_right</a>([x1, x2, x3, x4], f) == [</div>
<div class="line">    f(x1, f(x2, f(x3, x4))),</div>
<div class="line">          f(x2, f(x3, x4)),</div>
<div class="line">                f(x3, x4),</div>
<div class="line">                      x4</div>
<div class="line">]</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The sequence to scan from the right.</td></tr>
    <tr><td class="paramname">state</td><td>The (optional) initial reduction state.</td></tr>
    <tr><td class="paramname">f</td><td>A binary function called as <code>f(x, state)</code>, where <code>state</code> is the result accumulated so far and <code>x</code> is an element in the sequence. When no initial state is provided, <code>f</code> is called as <code>f(x1, x2)</code>, where <code>x1</code> and <code>x2</code> are elements of the sequence.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md342"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="scan__right_8hpp.html">boost/hana/scan_right.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;sstream&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keyword">auto</span> to_string = [](<span class="keyword">auto</span> x) {</div>
<div class="line">    std::ostringstream ss;</div>
<div class="line">    ss &lt;&lt; x;</div>
<div class="line">    <span class="keywordflow">return</span> ss.str();</div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><span class="keyword">auto</span> f = [](<span class="keyword">auto</span> element, <span class="keyword">auto</span> state) {</div>
<div class="line">    <span class="keywordflow">return</span> <span class="stringliteral">&quot;f(&quot;</span> + to_string(element) + <span class="stringliteral">&quot;, &quot;</span> + to_string(state) + <span class="stringliteral">&quot;)&quot;</span>;</div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() {</div>
<div class="line">    <span class="comment">// with initial state</span></div>
<div class="line">    <a class="code" href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a>(<a class="code" href="group__group-_sequence.html#ga54d141f901866dfab29b052857123bab">hana::scan_right</a>(hana::make_tuple(1, <span class="stringliteral">&quot;2&quot;</span>, <span class="charliteral">&#39;3&#39;</span>), 4, f) == hana::make_tuple(</div>
<div class="line">        <span class="stringliteral">&quot;f(1, f(2, f(3, 4)))&quot;</span>,</div>
<div class="line">        <span class="stringliteral">&quot;f(2, f(3, 4))&quot;</span>,</div>
<div class="line">        <span class="stringliteral">&quot;f(3, 4)&quot;</span>,</div>
<div class="line">        4</div>
<div class="line">    ));</div>
<div class="line"> </div>
<div class="line">    <span class="comment">// without initial state</span></div>
<div class="line">    <a class="code" href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a>(<a class="code" href="group__group-_sequence.html#ga54d141f901866dfab29b052857123bab">hana::scan_right</a>(hana::make_tuple(1, <span class="stringliteral">&quot;2&quot;</span>, <span class="charliteral">&#39;3&#39;</span>), f) == hana::make_tuple(</div>
<div class="line">        <span class="stringliteral">&quot;f(1, f(2, 3))&quot;</span>,</div>
<div class="line">        <span class="stringliteral">&quot;f(2, 3)&quot;</span>,</div>
<div class="line">        <span class="charliteral">&#39;3&#39;</span></div>
<div class="line">    ));</div>
<div class="line">}</div>
<div class="ttc" id="ascan__right_8hpp_html"><div class="ttname"><a href="scan__right_8hpp.html">scan_right.hpp</a></div><div class="ttdoc">Defines boost::hana::scan_right.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga245d8abaf6ba67e64020be51c8366081"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga245d8abaf6ba67e64020be51c8366081">&#9670;&nbsp;</a></span>slice</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::slice</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2slice_8hpp.html">boost/hana/fwd/slice.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs, <span class="keyword">auto</span>&amp;&amp; indices) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Extract the elements of a <code>Sequence</code> at the given indices. </p>
<p>Given an arbitrary sequence of <code>indices</code>, <code>slice</code> returns a new sequence of the elements of the original sequence that appear at those indices. In other words, </p><div class="fragment"><div class="line"><a class="code" href="group__group-_sequence.html#ga245d8abaf6ba67e64020be51c8366081">slice</a>([x1, ..., xn], [i1, ..., ik]) == [x_i1, ..., x_ik]</div>
<div class="ttc" id="agroup__group-_sequence_html_ga245d8abaf6ba67e64020be51c8366081"><div class="ttname"><a href="group__group-_sequence.html#ga245d8abaf6ba67e64020be51c8366081">boost::hana::slice</a></div><div class="ttdeci">constexpr auto slice</div><div class="ttdoc">Extract the elements of a Sequence at the given indices.</div><div class="ttdef"><b>Definition:</b> slice.hpp:53</div></div>
</div><!-- fragment --><p>The indices do not have to be ordered or contiguous in any particular way, but they must not be out of the bounds of the sequence. It is also possible to specify the same index multiple times, in which case the element at this index will be repeatedly included in the resulting sequence.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The sequence from which a subsequence is extracted.</td></tr>
    <tr><td class="paramname">indices</td><td>A compile-time <code>Foldable</code> containing non-negative <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code>s representing the indices. The indices are 0-based, and they must all be in bounds of the <code>xs</code> sequence. Note that any <code>Foldable</code> will really do (no need for an <code>Iterable</code>, for example); the linearization of the <code>indices</code> is used to determine the order of the elements included in the slice.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md355"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="core_2to_8hpp.html">boost/hana/core/to.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="filter_8hpp.html">boost/hana/filter.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="length_8hpp.html">boost/hana/length.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="mod_8hpp.html">boost/hana/mod.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="range_8hpp.html">boost/hana/range.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="slice_8hpp.html">boost/hana/slice.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="type_8hpp.html">boost/hana/type.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"><span class="keyword">using namespace </span>hana::literals;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">// Slice a contiguous range</span></div>
<div class="line">constexpr <span class="keyword">auto</span> xs = hana::make_tuple(0, <span class="charliteral">&#39;1&#39;</span>, 2.2, 3_c, hana::type_c&lt;float&gt;);</div>
<div class="line"> </div>
<div class="line">static_assert(</div>
<div class="line">    <a class="code" href="group__group-_sequence.html#ga245d8abaf6ba67e64020be51c8366081">hana::slice</a>(xs, hana::tuple_c&lt;std::size_t, 1, 2, 3&gt;) ==</div>
<div class="line">    hana::make_tuple(<span class="charliteral">&#39;1&#39;</span>, 2.2, 3_c)</div>
<div class="line">, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">// A more complex example with a non-contiguous range</span></div>
<div class="line">constexpr <span class="keyword">auto</span> letters = hana::to_tuple(hana::range_c&lt;char, &#39;a&#39;, &#39;z&#39;&gt;);</div>
<div class="line">constexpr <span class="keyword">auto</span> indices = hana::to_tuple(hana::make_range(hana::size_c&lt;0&gt;, <a class="code" href="group__group-_foldable.html#gaf0f8f717245620dc28cd7d7fa44d7475">hana::length</a>(letters)));</div>
<div class="line"> </div>
<div class="line"><span class="keyword">auto</span> even_indices = <a class="code" href="group__group-_monad_plus.html#ga65cc6d9f522fb9e8e3b28d80ee5c822a">hana::filter</a>(indices, [](<span class="keyword">auto</span> n) {</div>
<div class="line">    <span class="keywordflow">return</span> n % hana::size_c&lt;2&gt; == hana::size_c&lt;0&gt;;</div>
<div class="line">});</div>
<div class="line"> </div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(</div>
<div class="line">    <a class="code" href="group__group-_sequence.html#ga245d8abaf6ba67e64020be51c8366081">hana::slice</a>(letters, even_indices) == hana::tuple_c&lt;<span class="keywordtype">char</span>,</div>
<div class="line">        <span class="charliteral">&#39;a&#39;</span>, <span class="charliteral">&#39;c&#39;</span>, <span class="charliteral">&#39;e&#39;</span>, <span class="charliteral">&#39;g&#39;</span>, <span class="charliteral">&#39;i&#39;</span>, <span class="charliteral">&#39;k&#39;</span>, <span class="charliteral">&#39;m&#39;</span>, <span class="charliteral">&#39;o&#39;</span>, <span class="charliteral">&#39;q&#39;</span>, <span class="charliteral">&#39;s&#39;</span>, <span class="charliteral">&#39;u&#39;</span>, <span class="charliteral">&#39;w&#39;</span>, <span class="charliteral">&#39;y&#39;</span></div>
<div class="line">    &gt;</div>
<div class="line">);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="acore_2to_8hpp_html"><div class="ttname"><a href="core_2to_8hpp.html">to.hpp</a></div><div class="ttdoc">Defines boost::hana::to and related utilities.</div></div>
<div class="ttc" id="afilter_8hpp_html"><div class="ttname"><a href="filter_8hpp.html">filter.hpp</a></div><div class="ttdoc">Defines boost::hana::filter.</div></div>
<div class="ttc" id="agroup__group-_foldable_html_gaf0f8f717245620dc28cd7d7fa44d7475"><div class="ttname"><a href="group__group-_foldable.html#gaf0f8f717245620dc28cd7d7fa44d7475">boost::hana::length</a></div><div class="ttdeci">constexpr auto length</div><div class="ttdoc">Return the number of elements in a foldable structure.</div><div class="ttdef"><b>Definition:</b> length.hpp:34</div></div>
<div class="ttc" id="agroup__group-_monad_plus_html_ga65cc6d9f522fb9e8e3b28d80ee5c822a"><div class="ttname"><a href="group__group-_monad_plus.html#ga65cc6d9f522fb9e8e3b28d80ee5c822a">boost::hana::filter</a></div><div class="ttdeci">constexpr auto filter</div><div class="ttdoc">Filter a monadic structure using a custom predicate.</div><div class="ttdef"><b>Definition:</b> filter.hpp:65</div></div>
<div class="ttc" id="alength_8hpp_html"><div class="ttname"><a href="length_8hpp.html">length.hpp</a></div><div class="ttdoc">Defines boost::hana::length.</div></div>
<div class="ttc" id="amod_8hpp_html"><div class="ttname"><a href="mod_8hpp.html">mod.hpp</a></div><div class="ttdoc">Defines boost::hana::mod.</div></div>
<div class="ttc" id="arange_8hpp_html"><div class="ttname"><a href="range_8hpp.html">range.hpp</a></div><div class="ttdoc">Defines boost::hana::range.</div></div>
<div class="ttc" id="aslice_8hpp_html"><div class="ttname"><a href="slice_8hpp.html">slice.hpp</a></div><div class="ttdoc">Defines boost::hana::slice and boost::hana::slice_c.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="gae1f6a2a9cb70564d43c6b3c663b25dd7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae1f6a2a9cb70564d43c6b3c663b25dd7">&#9670;&nbsp;</a></span>slice_c</h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;std::size_t from, std::size_t to&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::slice_c</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2slice_8hpp.html">boost/hana/fwd/slice.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs) {</div>
<div class="line">        <span class="keywordflow">return</span> <a class="code" href="group__group-_sequence.html#ga245d8abaf6ba67e64020be51c8366081">hana::slice</a>(forwarded(xs), hana::range_c&lt;std::size_t, from, to&gt;);</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Shorthand to <code>slice</code> a contiguous range of elements. </p>
<p><code>slice_c</code> is simply a shorthand to slice a contiguous range of elements. In particular, <code>slice_c&lt;from, to&gt;(xs)</code> is equivalent to <code>slice(xs, range_c&lt;std::size_t, from, to&gt;)</code>, which simply slices all the elements of <code>xs</code> contained in the half-open interval delimited by <code>[from, to)</code>. Like for <code>slice</code>, the indices used with <code>slice_c</code> are 0-based and they must be in the bounds of the sequence being sliced.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">from</td><td>The index of the first element in the slice.</td></tr>
    <tr><td class="paramname">to</td><td>One-past the index of the last element in the slice. It must hold that <code>from &lt;= to</code>.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md356"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="slice_8hpp.html">boost/hana/slice.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="type_8hpp.html">boost/hana/type.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(</div>
<div class="line">    hana::slice_c&lt;1, 3&gt;(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3, hana::type_c&lt;float&gt;))</div>
<div class="line">            ==</div>
<div class="line">    hana::make_tuple(<span class="charliteral">&#39;2&#39;</span>, 3.3)</div>
<div class="line">, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga8d302de01b94b4b17f3bd81e09f42920"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8d302de01b94b4b17f3bd81e09f42920">&#9670;&nbsp;</a></span>take_back</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::take_back</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2take__back_8hpp.html">boost/hana/fwd/take_back.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs, <span class="keyword">auto</span> <span class="keyword">const</span>&amp; n) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Returns the last <code>n</code> elements of a sequence, or the whole sequence if the sequence has less than <code>n</code> elements. </p>
<p>Given a <code>Sequence</code> <code>xs</code> and an <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> <code>n</code>, <code>take_back(xs, n)</code> is a new sequence containing the last <code>n</code> elements of <code>xs</code>, in the same order. If <code>length(xs) &lt;= n</code>, the whole sequence is returned and no error is triggered.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The sequence to take the elements from.</td></tr>
    <tr><td class="paramname">n</td><td>A non-negative <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> representing the number of elements to keep in the resulting sequence.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md371"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="take__back_8hpp.html">boost/hana/take_back.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(<a class="code" href="group__group-_sequence.html#ga8d302de01b94b4b17f3bd81e09f42920">hana::take_back</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), hana::size_c&lt;0&gt;) == hana::make_tuple());</div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#ga8d302de01b94b4b17f3bd81e09f42920">hana::take_back</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), hana::size_c&lt;1&gt;) == hana::make_tuple(3.3), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#ga8d302de01b94b4b17f3bd81e09f42920">hana::take_back</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), hana::size_c&lt;2&gt;) == hana::make_tuple(<span class="charliteral">&#39;2&#39;</span>, 3.3), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#ga8d302de01b94b4b17f3bd81e09f42920">hana::take_back</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), hana::size_c&lt;3&gt;) == hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#ga8d302de01b94b4b17f3bd81e09f42920">hana::take_back</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), hana::size_c&lt;4&gt;) == hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="agroup__group-_sequence_html_ga8d302de01b94b4b17f3bd81e09f42920"><div class="ttname"><a href="group__group-_sequence.html#ga8d302de01b94b4b17f3bd81e09f42920">boost::hana::take_back</a></div><div class="ttdeci">constexpr auto take_back</div><div class="ttdoc">Returns the last n elements of a sequence, or the whole sequence if the sequence has less than n elem...</div><div class="ttdef"><b>Definition:</b> take_back.hpp:42</div></div>
<div class="ttc" id="atake__back_8hpp_html"><div class="ttname"><a href="take__back_8hpp.html">take_back.hpp</a></div><div class="ttdoc">Defines boost::hana::take_back.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="gaa4d4818952083e3b27c83b0ed645e322"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa4d4818952083e3b27c83b0ed645e322">&#9670;&nbsp;</a></span>take_back_c</h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;std::size_t n&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::take_back_c</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2take__back_8hpp.html">boost/hana/fwd/take_back.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs) {</div>
<div class="line">        <span class="keywordflow">return</span> <a class="code" href="group__group-_sequence.html#ga8d302de01b94b4b17f3bd81e09f42920">hana::take_back</a>(forwarded(xs), hana::size_c&lt;n&gt;);</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Equivalent to <code>take_back</code>; provided for convenience. </p>
<h2><a class="anchor" id="autotoc_md372"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="take__back_8hpp.html">boost/hana/take_back.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(hana::take_back_c&lt;2&gt;(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3)) == hana::make_tuple(<span class="charliteral">&#39;2&#39;</span>, 3.3), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga5112e6070d29b4f7fde3f44825da3316"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5112e6070d29b4f7fde3f44825da3316">&#9670;&nbsp;</a></span>take_front</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::take_front</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2take__front_8hpp.html">boost/hana/fwd/take_front.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs, <span class="keyword">auto</span> <span class="keyword">const</span>&amp; n) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Returns the first <code>n</code> elements of a sequence, or the whole sequence if the sequence has less than <code>n</code> elements. </p>
<p>Given a <code>Sequence</code> <code>xs</code> and an <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> <code>n</code>, <code>take_front(xs, n)</code> is a new sequence containing the first <code>n</code> elements of <code>xs</code>, in the same order. If <code>length(xs) &lt;= n</code>, the whole sequence is returned and no error is triggered.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The sequence to take the elements from.</td></tr>
    <tr><td class="paramname">n</td><td>A non-negative <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> representing the number of elements to keep in the resulting sequence.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md373"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="take__front_8hpp.html">boost/hana/take_front.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(<a class="code" href="group__group-_sequence.html#ga5112e6070d29b4f7fde3f44825da3316">hana::take_front</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), hana::size_c&lt;0&gt;) == hana::make_tuple());</div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#ga5112e6070d29b4f7fde3f44825da3316">hana::take_front</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), hana::size_c&lt;1&gt;) == hana::make_tuple(1), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#ga5112e6070d29b4f7fde3f44825da3316">hana::take_front</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), hana::size_c&lt;2&gt;) == hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#ga5112e6070d29b4f7fde3f44825da3316">hana::take_front</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), hana::size_c&lt;3&gt;) == hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">static_assert(<a class="code" href="group__group-_sequence.html#ga5112e6070d29b4f7fde3f44825da3316">hana::take_front</a>(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), hana::size_c&lt;4&gt;) == hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="agroup__group-_sequence_html_ga5112e6070d29b4f7fde3f44825da3316"><div class="ttname"><a href="group__group-_sequence.html#ga5112e6070d29b4f7fde3f44825da3316">boost::hana::take_front</a></div><div class="ttdeci">constexpr auto take_front</div><div class="ttdoc">Returns the first n elements of a sequence, or the whole sequence if the sequence has less than n ele...</div><div class="ttdef"><b>Definition:</b> take_front.hpp:42</div></div>
<div class="ttc" id="atake__front_8hpp_html"><div class="ttname"><a href="take__front_8hpp.html">take_front.hpp</a></div><div class="ttdoc">Defines boost::hana::take_front and boost::hana::take_front_c.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga3779f62fea92af00113a9290f1c680eb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3779f62fea92af00113a9290f1c680eb">&#9670;&nbsp;</a></span>take_front_c</h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;std::size_t n&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::take_front_c</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2take__front_8hpp.html">boost/hana/fwd/take_front.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs) {</div>
<div class="line">        <span class="keywordflow">return</span> <a class="code" href="group__group-_sequence.html#ga5112e6070d29b4f7fde3f44825da3316">hana::take_front</a>(forwarded(xs), hana::size_c&lt;n&gt;);</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Equivalent to <code>take_front</code>; provided for convenience. </p>
<h2><a class="anchor" id="autotoc_md374"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="take__front_8hpp.html">boost/hana/take_front.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(hana::take_front_c&lt;2&gt;(hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>, 3.3)) == hana::make_tuple(1, <span class="charliteral">&#39;2&#39;</span>), <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga2d4db4ec5ec5bc16fe74f57de12697fd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2d4db4ec5ec5bc16fe74f57de12697fd">&#9670;&nbsp;</a></span>take_while</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::take_while</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2take__while_8hpp.html">boost/hana/fwd/take_while.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; xs, <span class="keyword">auto</span>&amp;&amp; predicate) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Take elements from a sequence while the <code>predicate</code> is satisfied. </p>
<p>Specifically, <code>take_while</code> returns a new sequence containing the longest prefix of <code>xs</code> in which all the elements satisfy the given predicate.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xs</td><td>The sequence to take elements from.</td></tr>
    <tr><td class="paramname">predicate</td><td>A function called as <code>predicate(x)</code>, where <code>x</code> is an element of the sequence, and returning a <code>Logical</code> representing whether <code>x</code> should be included in the resulting sequence. In the current version of the library, <code>predicate</code> has to return a <code>Constant Logical</code>.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md375"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="less_8hpp.html">boost/hana/less.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="take__while_8hpp.html">boost/hana/take_while.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"><span class="keyword">using namespace </span>hana::literals;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><a class="code" href="group__group-assertions.html#ga2c5006540936d9f8880e3a39f4fcc035">BOOST_HANA_CONSTANT_CHECK</a>(</div>
<div class="line">    <a class="code" href="group__group-_sequence.html#ga2d4db4ec5ec5bc16fe74f57de12697fd">hana::take_while</a>(hana::tuple_c&lt;int, 0, 1, 2, 3&gt;, <a class="code" href="group__group-_orderable.html#gad510011602bdb14686f1c4ec145301c9">hana::less</a>.than(2_c))</div>
<div class="line">    ==</div>
<div class="line">    hana::tuple_c&lt;int, 0, 1&gt;</div>
<div class="line">);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="agroup__group-_orderable_html_gad510011602bdb14686f1c4ec145301c9"><div class="ttname"><a href="group__group-_orderable.html#gad510011602bdb14686f1c4ec145301c9">boost::hana::less</a></div><div class="ttdeci">constexpr auto less</div><div class="ttdoc">Returns a Logical representing whether x is less than y.</div><div class="ttdef"><b>Definition:</b> less.hpp:37</div></div>
<div class="ttc" id="agroup__group-_sequence_html_ga2d4db4ec5ec5bc16fe74f57de12697fd"><div class="ttname"><a href="group__group-_sequence.html#ga2d4db4ec5ec5bc16fe74f57de12697fd">boost::hana::take_while</a></div><div class="ttdeci">constexpr auto take_while</div><div class="ttdoc">Take elements from a sequence while the predicate is satisfied.</div><div class="ttdef"><b>Definition:</b> take_while.hpp:40</div></div>
<div class="ttc" id="atake__while_8hpp_html"><div class="ttname"><a href="take__while_8hpp.html">take_while.hpp</a></div><div class="ttdoc">Defines boost::hana::take_while.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="gaa5a378d4e71a91e0d6cd3959d9818e8a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa5a378d4e71a91e0d6cd3959d9818e8a">&#9670;&nbsp;</a></span>zip</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::zip</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2zip_8hpp.html">boost/hana/fwd/zip.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; x1, ..., <span class="keyword">auto</span>&amp;&amp; xn) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Zip one sequence or more. </p>
<p>Given <code>n</code> sequences <code>s1, ..., sn</code>, <code>zip</code> produces a sequence whose <code>i</code>-th element is a tuple of <code>(s1[i], ..., sn[i])</code>, where <code>sk[i]</code> denotes the <code>i</code>-th element of the <code>k</code>-th sequence. In other words, <code>zip</code> produces a sequence of the form </p><div class="fragment"><div class="line">[</div>
<div class="line">    make_tuple(s1[0], ..., sn[0]),</div>
<div class="line">    make_tuple(s1[1], ..., sn[1]),</div>
<div class="line">    ...</div>
<div class="line">    make_tuple(s1[M], ..., sn[M])</div>
<div class="line">]</div>
</div><!-- fragment --><p> where <code>M</code> is the length of the sequences, which are all assumed to have the same length. Assuming the sequences to all have the same size allows the library to perform some optimizations. To zip sequences that may have different lengths, <code>zip_shortest</code> should be used instead. Also note that it is an error to provide no sequence at all, i.e. <code>zip</code> expects at least one sequence.</p>
<h2><a class="anchor" id="autotoc_md410"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="zip_8hpp.html">boost/hana/zip.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(</div>
<div class="line">    <a class="code" href="group__group-_sequence.html#gaa5a378d4e71a91e0d6cd3959d9818e8a">hana::zip</a>(hana::make_tuple(1, <span class="charliteral">&#39;a&#39;</span>), hana::make_tuple(2, 3.3))</div>
<div class="line">    ==</div>
<div class="line">    hana::make_tuple(hana::make_tuple(1, 2), hana::make_tuple(<span class="charliteral">&#39;a&#39;</span>, 3.3))</div>
<div class="line">, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="agroup__group-_sequence_html_gaa5a378d4e71a91e0d6cd3959d9818e8a"><div class="ttname"><a href="group__group-_sequence.html#gaa5a378d4e71a91e0d6cd3959d9818e8a">boost::hana::zip</a></div><div class="ttdeci">constexpr auto zip</div><div class="ttdoc">Zip one sequence or more.</div><div class="ttdef"><b>Definition:</b> zip.hpp:45</div></div>
<div class="ttc" id="azip_8hpp_html"><div class="ttname"><a href="zip_8hpp.html">zip.hpp</a></div><div class="ttdoc">Defines boost::hana::zip.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="gade78593b3ff51fc5479e1da97142fef5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gade78593b3ff51fc5479e1da97142fef5">&#9670;&nbsp;</a></span>zip_shortest</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::zip_shortest</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2zip__shortest_8hpp.html">boost/hana/fwd/zip_shortest.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; x1, ..., <span class="keyword">auto</span>&amp;&amp; xn) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Zip one sequence or more. </p>
<p>Given <code>n</code> sequences <code>s1, ..., sn</code>, <code>zip_shortest</code> produces a sequence whose <code>i</code>-th element is a tuple of <code>(s1[i], ..., sn[i])</code>, where <code>sk[i]</code> denotes the <code>i</code>-th element of the <code>k</code>-th sequence. In other words, <code>zip_shortest</code> produces a sequence of the form </p><div class="fragment"><div class="line">[</div>
<div class="line">    make_tuple(s1[0], ..., sn[0]),</div>
<div class="line">    make_tuple(s1[1], ..., sn[1]),</div>
<div class="line">    ...</div>
<div class="line">    make_tuple(s1[M], ..., sn[M])</div>
<div class="line">]</div>
</div><!-- fragment --><p> where <code>M</code> is the length of the shortest sequence. Hence, the returned sequence stops when the shortest input sequence is exhausted. If you know that all the sequences you are about to zip have the same length, you should use <code>zip</code> instead, since it can be more optimized. Also note that it is an error to provide no sequence at all, i.e. <code>zip_shortest</code> expects at least one sequence.</p>
<h2><a class="anchor" id="autotoc_md411"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="zip__shortest_8hpp.html">boost/hana/zip_shortest.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(</div>
<div class="line">    <a class="code" href="group__group-_sequence.html#gade78593b3ff51fc5479e1da97142fef5">hana::zip_shortest</a>(hana::make_tuple(1, <span class="charliteral">&#39;a&#39;</span>), hana::make_tuple(2, 3.3), hana::make_tuple(3, <span class="charliteral">&#39;c&#39;</span>, <span class="stringliteral">&quot;ignored&quot;</span>))</div>
<div class="line">    ==</div>
<div class="line">    hana::make_tuple(hana::make_tuple(1, 2, 3), hana::make_tuple(<span class="charliteral">&#39;a&#39;</span>, 3.3, <span class="charliteral">&#39;c&#39;</span>))</div>
<div class="line">, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="agroup__group-_sequence_html_gade78593b3ff51fc5479e1da97142fef5"><div class="ttname"><a href="group__group-_sequence.html#gade78593b3ff51fc5479e1da97142fef5">boost::hana::zip_shortest</a></div><div class="ttdeci">constexpr auto zip_shortest</div><div class="ttdoc">Zip one sequence or more.</div><div class="ttdef"><b>Definition:</b> zip_shortest.hpp:45</div></div>
<div class="ttc" id="azip__shortest_8hpp_html"><div class="ttname"><a href="zip__shortest_8hpp.html">zip_shortest.hpp</a></div><div class="ttdoc">Defines boost::hana::zip_shortest.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="gae7a51104a77db79a0407d7d67b034667"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae7a51104a77db79a0407d7d67b034667">&#9670;&nbsp;</a></span>zip_shortest_with</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::zip_shortest_with</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2zip__shortest__with_8hpp.html">boost/hana/fwd/zip_shortest_with.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; f, <span class="keyword">auto</span>&amp;&amp; x1, ..., <span class="keyword">auto</span>&amp;&amp; xn) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Zip one sequence or more with a given function. </p>
<p>Given a <code>n</code>-ary function <code>f</code> and <code>n</code> sequences <code>s1, ..., sn</code>, <code>zip_shortest_with</code> produces a sequence whose <code>i</code>-th element is <code>f(s1[i], ..., sn[i])</code>, where <code>sk[i]</code> denotes the <code>i</code>-th element of the <code>k</code>-th sequence. In other words, <code>zip_shortest_with</code> produces a sequence of the form </p><div class="fragment"><div class="line">[</div>
<div class="line">    f(s1[0], ..., sn[0]),</div>
<div class="line">    f(s1[1], ..., sn[1]),</div>
<div class="line">    ...</div>
<div class="line">    f(s1[M], ..., sn[M])</div>
<div class="line">]</div>
</div><!-- fragment --><p> where <code>M</code> is the length of the shortest sequence. Hence, the returned sequence stops when the shortest input sequence is exhausted. If you know that all the sequences you are about to zip have the same length, you should use <code>zip_with</code> instead, since it can be more optimized. Also note that it is an error to provide no sequence at all, i.e. <code>zip_shortest_with</code> expects at least one sequence.</p>
<h2><a class="anchor" id="autotoc_md412"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="mult_8hpp.html">boost/hana/mult.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="zip__shortest__with_8hpp.html">boost/hana/zip_shortest_with.hpp</a>&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(</div>
<div class="line">    <a class="code" href="group__group-_sequence.html#gae7a51104a77db79a0407d7d67b034667">hana::zip_shortest_with</a>(<a class="code" href="group__group-_ring.html#ga052d31c269a6a438cc8004c9ad1efdfa">hana::mult</a>, hana::make_tuple(1, 2, 3, 4), hana::make_tuple(5, 6, 7, 8, <span class="stringliteral">&quot;ignored&quot;</span>))</div>
<div class="line">    ==</div>
<div class="line">    hana::make_tuple(5, 12, 21, 32)</div>
<div class="line">, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="agroup__group-_ring_html_ga052d31c269a6a438cc8004c9ad1efdfa"><div class="ttname"><a href="group__group-_ring.html#ga052d31c269a6a438cc8004c9ad1efdfa">boost::hana::mult</a></div><div class="ttdeci">constexpr auto mult</div><div class="ttdoc">Associative operation of a Ring.</div><div class="ttdef"><b>Definition:</b> mult.hpp:47</div></div>
<div class="ttc" id="agroup__group-_sequence_html_gae7a51104a77db79a0407d7d67b034667"><div class="ttname"><a href="group__group-_sequence.html#gae7a51104a77db79a0407d7d67b034667">boost::hana::zip_shortest_with</a></div><div class="ttdeci">constexpr auto zip_shortest_with</div><div class="ttdoc">Zip one sequence or more with a given function.</div><div class="ttdef"><b>Definition:</b> zip_shortest_with.hpp:46</div></div>
<div class="ttc" id="amult_8hpp_html"><div class="ttname"><a href="mult_8hpp.html">mult.hpp</a></div><div class="ttdoc">Defines boost::hana::mult.</div></div>
<div class="ttc" id="azip__shortest__with_8hpp_html"><div class="ttname"><a href="zip__shortest__with_8hpp.html">zip_shortest_with.hpp</a></div><div class="ttdoc">Defines boost::hana::zip_shortest_with.</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ga6a4bf8549ce69b5b5b7377aec225a0e3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6a4bf8549ce69b5b5b7377aec225a0e3">&#9670;&nbsp;</a></span>zip_with</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::zip_with</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><code>#include &lt;<a class="el" href="fwd_2zip__with_8hpp.html">boost/hana/fwd/zip_with.hpp</a>&gt;</code></p>
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span>&amp;&amp; f, <span class="keyword">auto</span>&amp;&amp; x1, ..., <span class="keyword">auto</span>&amp;&amp; xn) {</div>
<div class="line">        <span class="keywordflow">return</span> tag-dispatched;</div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Zip one sequence or more with a given function. </p>
<p>Given a <code>n</code>-ary function <code>f</code> and <code>n</code> sequences <code>s1, ..., sn</code>, <code>zip_with</code> produces a sequence whose <code>i</code>-th element is <code>f(s1[i], ..., sn[i])</code>, where <code>sk[i]</code> denotes the <code>i</code>-th element of the <code>k</code>-th sequence. In other words, <code>zip_with</code> produces a sequence of the form </p><div class="fragment"><div class="line">[</div>
<div class="line">    f(s1[0], ..., sn[0]),</div>
<div class="line">    f(s1[1], ..., sn[1]),</div>
<div class="line">    ...</div>
<div class="line">    f(s1[M], ..., sn[M])</div>
<div class="line">]</div>
</div><!-- fragment --><p> where <code>M</code> is the length of the sequences, which are all assumed to have the same length. Assuming the sequences to all have the same size allows the library to perform some optimizations. To zip sequences that may have different lengths, <code>zip_shortest_with</code> should be used instead. Also note that it is an error to provide no sequence at all, i.e. <code>zip_with</code> expects at least one sequence.</p>
<h2><a class="anchor" id="autotoc_md413"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="equal_8hpp.html">boost/hana/equal.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="ext_2std_2tuple_8hpp.html">boost/hana/ext/std/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="mult_8hpp.html">boost/hana/mult.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="transform_8hpp.html">boost/hana/transform.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="tuple_8hpp.html">boost/hana/tuple.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="type_8hpp.html">boost/hana/type.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="unpack_8hpp.html">boost/hana/unpack.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="zip__with_8hpp.html">boost/hana/zip_with.hpp</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;tuple&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;type_traits&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;utility&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">// Basic usage:</span></div>
<div class="line">static_assert(</div>
<div class="line">    <a class="code" href="group__group-_sequence.html#ga6a4bf8549ce69b5b5b7377aec225a0e3">hana::zip_with</a>(<a class="code" href="group__group-_ring.html#ga052d31c269a6a438cc8004c9ad1efdfa">hana::mult</a>, hana::make_tuple(1, 2, 3, 4), hana::make_tuple(5, 6, 7, 8))</div>
<div class="line">    ==</div>
<div class="line">    hana::make_tuple(5, 12, 21, 32)</div>
<div class="line">, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">// Example of computing a tuple of all the common types of several tuples:</span></div>
<div class="line"><span class="keyword">template</span>&lt;<span class="keyword">typename</span>... Ts&gt;</div>
<div class="line"><span class="keyword">using</span> common_tuple_t = <span class="keyword">typename</span> decltype(</div>
<div class="line">    <a class="code" href="group__group-_foldable.html#ga7b0c23944364ce61136e10b978ae2170">hana::unpack</a>(</div>
<div class="line">        <a class="code" href="group__group-_sequence.html#ga6a4bf8549ce69b5b5b7377aec225a0e3">hana::zip_with</a>(</div>
<div class="line">            hana::metafunction&lt;std::common_type&gt;,</div>
<div class="line">            hana::transform(std::declval&lt;Ts&gt;(), hana::decltype_)...</div>
<div class="line">        ),</div>
<div class="line">        hana::template_&lt;std::tuple&gt;</div>
<div class="line">    )</div>
<div class="line">)::type;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line">static_assert(std::is_same&lt;</div>
<div class="line">    common_tuple_t&lt;</div>
<div class="line">        <a class="code" href="structstd_1_1tuple.html">std::tuple&lt;bool, int, unsigned&gt;</a>,</div>
<div class="line">        <a class="code" href="structstd_1_1tuple.html">std::tuple&lt;char, long, long&gt;</a>,</div>
<div class="line">        <a class="code" href="structstd_1_1tuple.html">std::tuple&lt;int, long long, double&gt;</a></div>
<div class="line">    &gt;,</div>
<div class="line">    <a class="code" href="structstd_1_1tuple.html">std::tuple&lt;int, long long, double&gt;</a></div>
<div class="line">&gt;::<a class="code" href="group__group-_constant.html#ga1687520692a6b0c49e3a69de2980f388">value</a>, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() { }</div>
<div class="ttc" id="agroup__group-_constant_html_ga1687520692a6b0c49e3a69de2980f388"><div class="ttname"><a href="group__group-_constant.html#ga1687520692a6b0c49e3a69de2980f388">boost::hana::value</a></div><div class="ttdeci">constexpr auto value</div><div class="ttdoc">Return the compile-time value associated to a constant.</div><div class="ttdef"><b>Definition:</b> value.hpp:54</div></div>
<div class="ttc" id="agroup__group-_foldable_html_ga7b0c23944364ce61136e10b978ae2170"><div class="ttname"><a href="group__group-_foldable.html#ga7b0c23944364ce61136e10b978ae2170">boost::hana::unpack</a></div><div class="ttdeci">constexpr auto unpack</div><div class="ttdoc">Invoke a function with the elements of a Foldable as arguments.</div><div class="ttdef"><b>Definition:</b> unpack.hpp:79</div></div>
<div class="ttc" id="agroup__group-_sequence_html_ga6a4bf8549ce69b5b5b7377aec225a0e3"><div class="ttname"><a href="group__group-_sequence.html#ga6a4bf8549ce69b5b5b7377aec225a0e3">boost::hana::zip_with</a></div><div class="ttdeci">constexpr auto zip_with</div><div class="ttdoc">Zip one sequence or more with a given function.</div><div class="ttdef"><b>Definition:</b> zip_with.hpp:46</div></div>
<div class="ttc" id="astructstd_1_1tuple_html"><div class="ttname"><a href="structstd_1_1tuple.html">std::tuple</a></div><div class="ttdoc">Adapter for std::tuples.</div><div class="ttdef"><b>Definition:</b> tuple.hpp:49</div></div>
<div class="ttc" id="aunpack_8hpp_html"><div class="ttname"><a href="unpack_8hpp.html">unpack.hpp</a></div><div class="ttdoc">Defines boost::hana::unpack.</div></div>
<div class="ttc" id="azip__with_8hpp_html"><div class="ttname"><a href="zip__with_8hpp.html">zip_with.hpp</a></div><div class="ttdoc">Defines boost::hana::zip_with.</div></div>
</div><!-- fragment --> 
</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!--
Copyright Louis Dionne 2013-2017
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
-->
<!-- boost-no-inspect -->
<!-- HTML footer for doxygen *******-->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
  </ul>
</div>
</body>
</html>
