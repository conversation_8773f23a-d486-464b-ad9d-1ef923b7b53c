<!--
Copyright <PERSON> 2013-2017
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
-->
<!-- boost-no-inspect -->
<!-- HTML header for doxygen *******-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<title>Boost.Hana: boost::hana Namespace Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
// Copyright Louis Dionne 2013-2017
// Distributed under the Boost Software License, Version 1.0.
// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
MathJax.Hub.Config({
    "HTML-CSS": {
        linebreaks: {
            automatic: true,
            width: "75% container"
        }
    }
});
</script>
<script type="text/javascript" async="async" src="https://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<!-- Additional javascript for drawing charts. -->
<script type="text/javascript" src="highcharts.js"></script>
<script type="text/javascript" src="highcharts-data.js"></script>
<script type="text/javascript" src="highcharts-exporting.js"></script>
<script type="text/javascript" src="chart.js"></script>
<script type="text/javascript" src="hana.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="Boost.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">Boost.Hana
   &#160;<span id="projectnumber">1.7.1</span>
   </div>
   <div id="projectbrief">Your standard library for metaprogramming</div>
  </td>
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.svg"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(document).ready(function(){initNavTree('namespaceboost_1_1hana.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#namespaces">Namespaces</a> &#124;
<a href="#nested-classes">Classes</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">boost::hana Namespace Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Namespace containing everything in the library.  
<a href="namespaceboost_1_1hana.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespaceboost_1_1hana_1_1literals"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1hana_1_1literals.html">literals</a></td></tr>
<tr class="memdesc:namespaceboost_1_1hana_1_1literals"><td class="mdescLeft">&#160;</td><td class="mdescRight">Namespace containing C++14 user-defined literals provided by Hana. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1has__common.html">has_common</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Metafunction returning whether two data types share a common data type.  <a href="structboost_1_1hana_1_1has__common.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1is__default.html">is_default</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether a tag-dispatched method implementation is a default implementation.  <a href="structboost_1_1hana_1_1is__default.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1is__convertible.html">is_convertible</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether there is a Hana-conversion from a data type to another.  <a href="structboost_1_1hana_1_1is__convertible.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1is__embedded.html">is_embedded</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether a data type can be embedded into another data type.  <a href="structboost_1_1hana_1_1is__embedded.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1integral__constant__tag.html">integral_constant_tag</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tag representing <code><a class="el" href="structboost_1_1hana_1_1integral__constant.html" title="Compile-time value of an integral type.">hana::integral_constant</a></code>.  <a href="structboost_1_1hana_1_1integral__constant__tag.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1integral__constant.html">integral_constant</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compile-time value of an integral type.  <a href="structboost_1_1hana_1_1integral__constant.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1basic__tuple.html">basic_tuple</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Stripped down version of <code><a class="el" href="structboost_1_1hana_1_1tuple.html" title="General purpose index-based heterogeneous sequence with a fixed length.">hana::tuple</a></code>.  <a href="structboost_1_1hana_1_1basic__tuple.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1basic__tuple__tag.html">basic_tuple_tag</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tag representing <code><a class="el" href="structboost_1_1hana_1_1basic__tuple.html" title="Stripped down version of hana::tuple.">hana::basic_tuple</a></code>.  <a href="structboost_1_1hana_1_1basic__tuple__tag.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1_integral_constant.html">IntegralConstant</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">The <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> concept represents compile-time integral values.  <a href="structboost_1_1hana_1_1_integral_constant.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1common.html">common</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Metafunction returning the common data type between two data types.  <a href="structboost_1_1hana_1_1common.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1default__.html">default_</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Mark a tag-dispatched method implementation as a default implementation.  <a href="structboost_1_1hana_1_1default__.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1tag__of.html">tag_of</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Metafunction returning the tag associated to <code>T</code>.  <a href="structboost_1_1hana_1_1tag__of.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1embedding.html">embedding</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Marks a conversion between data types as being an embedding.  <a href="structboost_1_1hana_1_1embedding.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1when.html">when</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Enable a partial specialization only if a boolean condition is true.  <a href="structboost_1_1hana_1_1when.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1lazy.html">lazy</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight"><code><a class="el" href="structboost_1_1hana_1_1lazy.html" title="hana::lazy implements superficial laziness via a monadic interface.">hana::lazy</a></code> implements superficial laziness via a monadic interface.  <a href="structboost_1_1hana_1_1lazy.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1lazy__tag.html">lazy_tag</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tag representing <code><a class="el" href="structboost_1_1hana_1_1lazy.html" title="hana::lazy implements superficial laziness via a monadic interface.">hana::lazy</a></code>.  <a href="structboost_1_1hana_1_1lazy__tag.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1map__tag.html">map_tag</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tag representing <code><a class="el" href="structboost_1_1hana_1_1map.html" title="Basic associative container requiring unique, Comparable and Hashable keys.">hana::map</a></code>s.  <a href="structboost_1_1hana_1_1map__tag.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1map.html">map</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Basic associative container requiring unique, <code>Comparable</code> and <code>Hashable</code> keys.  <a href="structboost_1_1hana_1_1map.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1optional.html">optional</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Optional value whose optional-ness is known at compile-time.  <a href="structboost_1_1hana_1_1optional.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1optional__tag.html">optional_tag</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tag representing a <code><a class="el" href="structboost_1_1hana_1_1optional.html" title="Optional value whose optional-ness is known at compile-time.">hana::optional</a></code>.  <a href="structboost_1_1hana_1_1optional__tag.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1pair.html">pair</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generic container for two elements.  <a href="structboost_1_1hana_1_1pair.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1pair__tag.html">pair_tag</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tag representing <code><a class="el" href="structboost_1_1hana_1_1pair.html" title="Generic container for two elements.">hana::pair</a></code>.  <a href="structboost_1_1hana_1_1pair__tag.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1range.html">range</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compile-time half-open interval of <code><a class="el" href="structboost_1_1hana_1_1integral__constant.html" title="Compile-time value of an integral type.">hana::integral_constant</a></code>s.  <a href="structboost_1_1hana_1_1range.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1range__tag.html">range_tag</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tag representing a <code><a class="el" href="structboost_1_1hana_1_1range.html" title="Compile-time half-open interval of hana::integral_constants.">hana::range</a></code>.  <a href="structboost_1_1hana_1_1range__tag.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1set.html">set</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Basic unordered container requiring unique, <code>Comparable</code> and <code>Hashable</code> keys.  <a href="structboost_1_1hana_1_1set.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1set__tag.html">set_tag</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tag representing the <code><a class="el" href="structboost_1_1hana_1_1set.html" title="Basic unordered container requiring unique, Comparable and Hashable keys.">hana::set</a></code> container.  <a href="structboost_1_1hana_1_1set__tag.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1string.html">string</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compile-time string.  <a href="structboost_1_1hana_1_1string.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1string__tag.html">string_tag</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tag representing a compile-time string.  <a href="structboost_1_1hana_1_1string__tag.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1tuple.html">tuple</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">General purpose index-based heterogeneous sequence with a fixed length.  <a href="structboost_1_1hana_1_1tuple.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1tuple__tag.html">tuple_tag</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tag representing <code><a class="el" href="structboost_1_1hana_1_1tuple.html" title="General purpose index-based heterogeneous sequence with a fixed length.">hana::tuple</a></code>s.  <a href="structboost_1_1hana_1_1tuple__tag.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1basic__type.html">basic_type</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Base class of <code><a class="el" href="structboost_1_1hana_1_1type.html" title="C++ type in value-level representation.">hana::type</a></code>; used for pattern-matching.  <a href="structboost_1_1hana_1_1basic__type.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1type.html">type</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">C++ type in value-level representation.  <a href="structboost_1_1hana_1_1type.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1hana_1_1type__tag.html">type_tag</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tag representing <code><a class="el" href="structboost_1_1hana_1_1type.html" title="C++ type in value-level representation.">hana::type</a></code>.  <a href="structboost_1_1hana_1_1type__tag.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga4da46c97755c0f430b063711b66ca05b"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U &gt; </td></tr>
<tr class="memitem:ga4da46c97755c0f430b063711b66ca05b"><td class="memTemplItemLeft" align="right" valign="top">using&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-core.html#ga4da46c97755c0f430b063711b66ca05b">common_t</a> = typename <a class="el" href="structboost_1_1hana_1_1common.html">common</a>&lt; T, U &gt;::<a class="el" href="structboost_1_1hana_1_1type.html">type</a></td></tr>
<tr class="memdesc:ga4da46c97755c0f430b063711b66ca05b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Alias to <code>common&lt;T, U&gt;::type</code>, provided for convenience.  <a href="group__group-core.html#ga4da46c97755c0f430b063711b66ca05b">More...</a><br /></td></tr>
<tr class="separator:ga4da46c97755c0f430b063711b66ca05b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga686d1236161b5690ab302500077988e1"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga686d1236161b5690ab302500077988e1"><td class="memTemplItemLeft" align="right" valign="top">using&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-core.html#ga686d1236161b5690ab302500077988e1">tag_of_t</a> = typename <a class="el" href="structboost_1_1hana_1_1tag__of.html">hana::tag_of</a>&lt; T &gt;::<a class="el" href="structboost_1_1hana_1_1type.html">type</a></td></tr>
<tr class="memdesc:ga686d1236161b5690ab302500077988e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Alias to <code><a class="el" href="structboost_1_1hana_1_1tag__of.html" title="Metafunction returning the tag associated to T.">tag_of</a>&lt;T&gt;::type</code>, provided for convenience.  <a href="group__group-core.html#ga686d1236161b5690ab302500077988e1">More...</a><br /></td></tr>
<tr class="separator:ga686d1236161b5690ab302500077988e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0f5d717bbf6646619bb6219b104384dc"><td class="memTemplParams" colspan="2">template&lt;typename ... &gt; </td></tr>
<tr class="memitem:ga0f5d717bbf6646619bb6219b104384dc"><td class="memTemplItemLeft" align="right" valign="top">using&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-core.html#ga0f5d717bbf6646619bb6219b104384dc">when_valid</a> = <a class="el" href="structboost_1_1hana_1_1when.html">when</a>&lt; true &gt;</td></tr>
<tr class="memdesc:ga0f5d717bbf6646619bb6219b104384dc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Variant of <code>when</code> allowing specializations to be enabled only if an expression is well-formed.  <a href="group__group-core.html#ga0f5d717bbf6646619bb6219b104384dc">More...</a><br /></td></tr>
<tr class="separator:ga0f5d717bbf6646619bb6219b104384dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga141761435a7826b3cbe646b4f59eaf0a"><td class="memItemLeft" align="right" valign="top">auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_struct.html#ga141761435a7826b3cbe646b4f59eaf0a">BOOST_HANA_ADAPT_ADT</a> (...)</td></tr>
<tr class="memdesc:ga141761435a7826b3cbe646b4f59eaf0a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines a model of <code>Struct</code> with the given accessors.  <a href="group__group-_struct.html#ga141761435a7826b3cbe646b4f59eaf0a">More...</a><br /></td></tr>
<tr class="separator:ga141761435a7826b3cbe646b4f59eaf0a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaba3b4d2cf342bfca773e90fc20bfae91"><td class="memItemLeft" align="right" valign="top">auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_struct.html#gaba3b4d2cf342bfca773e90fc20bfae91">BOOST_HANA_ADAPT_STRUCT</a> (...)</td></tr>
<tr class="memdesc:gaba3b4d2cf342bfca773e90fc20bfae91"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines a model of <code>Struct</code> with the given members.  <a href="group__group-_struct.html#gaba3b4d2cf342bfca773e90fc20bfae91">More...</a><br /></td></tr>
<tr class="separator:gaba3b4d2cf342bfca773e90fc20bfae91"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4b7188568b24c715ec8e43595de6844d"><td class="memItemLeft" align="right" valign="top">times&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_applicative.html#ga4b7188568b24c715ec8e43595de6844d">A</a> (T_1) \times \cdots \times A(T_n) \<a class="el" href="group__group-core.html#gadc70755c1d059139297814fb3bfeb91e">to</a> A(U) @f$. const expr auto ap</td></tr>
<tr class="memdesc:ga4b7188568b24c715ec8e43595de6844d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Lifted application.  <a href="group__group-_applicative.html#ga4b7188568b24c715ec8e43595de6844d">More...</a><br /></td></tr>
<tr class="separator:ga4b7188568b24c715ec8e43595de6844d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab9efb238a82207d91643994c5295cf8c"><td class="memItemLeft" align="right" valign="top">auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_struct.html#gab9efb238a82207d91643994c5295cf8c">BOOST_HANA_DEFINE_STRUCT</a> (...)</td></tr>
<tr class="memdesc:gab9efb238a82207d91643994c5295cf8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines members of a structure, while at the same time modeling <code>Struct</code>.  <a href="group__group-_struct.html#gab9efb238a82207d91643994c5295cf8c">More...</a><br /></td></tr>
<tr class="separator:gab9efb238a82207d91643994c5295cf8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:ga835970cb25a0c8dc200f1e5f8943538b"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga835970cb25a0c8dc200f1e5f8943538b">always</a></td></tr>
<tr class="memdesc:ga835970cb25a0c8dc200f1e5f8943538b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return a constant function returning <code>x</code> regardless of the argument(s) it is invoked with.  <a href="group__group-functional.html#ga835970cb25a0c8dc200f1e5f8943538b">More...</a><br /></td></tr>
<tr class="separator:ga835970cb25a0c8dc200f1e5f8943538b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga30027c383676084be151ef3c6cf2829f"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga30027c383676084be151ef3c6cf2829f">apply</a></td></tr>
<tr class="memdesc:ga30027c383676084be151ef3c6cf2829f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Invokes a Callable with the given arguments.  <a href="group__group-functional.html#ga30027c383676084be151ef3c6cf2829f">More...</a><br /></td></tr>
<tr class="separator:ga30027c383676084be151ef3c6cf2829f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6acc765a35c4dc85f0deab4785831a3d"><td class="memTemplParams" colspan="2">template&lt;std::size_t n&gt; </td></tr>
<tr class="memitem:ga6acc765a35c4dc85f0deab4785831a3d"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga6acc765a35c4dc85f0deab4785831a3d">arg</a></td></tr>
<tr class="memdesc:ga6acc765a35c4dc85f0deab4785831a3d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the <code>n</code>th passed argument.  <a href="group__group-functional.html#ga6acc765a35c4dc85f0deab4785831a3d">More...</a><br /></td></tr>
<tr class="separator:ga6acc765a35c4dc85f0deab4785831a3d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga41ada6b336e9d5bcb101ff0c737acbd0"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga41ada6b336e9d5bcb101ff0c737acbd0">capture</a></td></tr>
<tr class="memdesc:ga41ada6b336e9d5bcb101ff0c737acbd0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create a function capturing the given variables.  <a href="group__group-functional.html#ga41ada6b336e9d5bcb101ff0c737acbd0">More...</a><br /></td></tr>
<tr class="separator:ga41ada6b336e9d5bcb101ff0c737acbd0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3b16146e53efcdf9ecbb9a7b21f8cd0b"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga3b16146e53efcdf9ecbb9a7b21f8cd0b">compose</a></td></tr>
<tr class="memdesc:ga3b16146e53efcdf9ecbb9a7b21f8cd0b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the composition of two functions or more.  <a href="group__group-functional.html#ga3b16146e53efcdf9ecbb9a7b21f8cd0b">More...</a><br /></td></tr>
<tr class="separator:ga3b16146e53efcdf9ecbb9a7b21f8cd0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga49ea872ade5ac8f6c10052c495302e89"><td class="memTemplParams" colspan="2">template&lt;std::size_t n&gt; </td></tr>
<tr class="memitem:ga49ea872ade5ac8f6c10052c495302e89"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga49ea872ade5ac8f6c10052c495302e89">curry</a></td></tr>
<tr class="memdesc:ga49ea872ade5ac8f6c10052c495302e89"><td class="mdescLeft">&#160;</td><td class="mdescRight">Curry a function up to the given number of arguments.  <a href="group__group-functional.html#ga49ea872ade5ac8f6c10052c495302e89">More...</a><br /></td></tr>
<tr class="separator:ga49ea872ade5ac8f6c10052c495302e89"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8c6f17b58ce527c7650eb878f01f2cd2"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga8c6f17b58ce527c7650eb878f01f2cd2">mathtt</a></td></tr>
<tr class="memdesc:ga8c6f17b58ce527c7650eb878f01f2cd2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Invoke a function with the results of invoking other functions on its arguments.  <a href="group__group-functional.html#ga8c6f17b58ce527c7650eb878f01f2cd2">More...</a><br /></td></tr>
<tr class="separator:ga8c6f17b58ce527c7650eb878f01f2cd2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1393f40da2e8da6e0c12fce953e56a6c"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga1393f40da2e8da6e0c12fce953e56a6c">fix</a></td></tr>
<tr class="memdesc:ga1393f40da2e8da6e0c12fce953e56a6c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return a function computing the fixed point of a function.  <a href="group__group-functional.html#ga1393f40da2e8da6e0c12fce953e56a6c">More...</a><br /></td></tr>
<tr class="separator:ga1393f40da2e8da6e0c12fce953e56a6c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga004f884cdbb85c2efe3383c1db450094"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga004f884cdbb85c2efe3383c1db450094">flip</a></td></tr>
<tr class="memdesc:ga004f884cdbb85c2efe3383c1db450094"><td class="mdescLeft">&#160;</td><td class="mdescRight">Invoke a function with its two first arguments reversed.  <a href="group__group-functional.html#ga004f884cdbb85c2efe3383c1db450094">More...</a><br /></td></tr>
<tr class="separator:ga004f884cdbb85c2efe3383c1db450094"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaef38cf34324c8edbd3597ae71811d00d"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#gaef38cf34324c8edbd3597ae71811d00d">id</a></td></tr>
<tr class="memdesc:gaef38cf34324c8edbd3597ae71811d00d"><td class="mdescLeft">&#160;</td><td class="mdescRight">The identity function &ndash; returns its argument unchanged.  <a href="group__group-functional.html#gaef38cf34324c8edbd3597ae71811d00d">More...</a><br /></td></tr>
<tr class="separator:gaef38cf34324c8edbd3597ae71811d00d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7bdafba6dc801f1d2d83731ad9714557"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga7bdafba6dc801f1d2d83731ad9714557">infix</a></td></tr>
<tr class="memdesc:ga7bdafba6dc801f1d2d83731ad9714557"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return an equivalent function that can also be applied in infix notation.  <a href="group__group-functional.html#ga7bdafba6dc801f1d2d83731ad9714557">More...</a><br /></td></tr>
<tr class="separator:ga7bdafba6dc801f1d2d83731ad9714557"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafca60c09e1f7a32a2b52baaf6515c279"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#gafca60c09e1f7a32a2b52baaf6515c279">lockstep</a></td></tr>
<tr class="memdesc:gafca60c09e1f7a32a2b52baaf6515c279"><td class="mdescLeft">&#160;</td><td class="mdescRight">Invoke a function with the result of invoking other functions on its arguments, in lockstep.  <a href="group__group-functional.html#gafca60c09e1f7a32a2b52baaf6515c279">More...</a><br /></td></tr>
<tr class="separator:gafca60c09e1f7a32a2b52baaf6515c279"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga35c4fc3c5677b9f558150b90e74d3ab1"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga35c4fc3c5677b9f558150b90e74d3ab1">on</a></td></tr>
<tr class="memdesc:ga35c4fc3c5677b9f558150b90e74d3ab1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Invoke a function with the result of invoking another function on each argument.  <a href="group__group-functional.html#ga35c4fc3c5677b9f558150b90e74d3ab1">More...</a><br /></td></tr>
<tr class="separator:ga35c4fc3c5677b9f558150b90e74d3ab1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga83e71bae315e299f9f5f9de77b012139"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga83e71bae315e299f9f5f9de77b012139">overload</a></td></tr>
<tr class="memdesc:ga83e71bae315e299f9f5f9de77b012139"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pick one of several functions to call based on overload resolution.  <a href="group__group-functional.html#ga83e71bae315e299f9f5f9de77b012139">More...</a><br /></td></tr>
<tr class="separator:ga83e71bae315e299f9f5f9de77b012139"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa46de6f618d9f14edb1589b36b6e75ec"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#gaa46de6f618d9f14edb1589b36b6e75ec">overload_linearly</a></td></tr>
<tr class="memdesc:gaa46de6f618d9f14edb1589b36b6e75ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Call the first function that produces a valid call expression.  <a href="group__group-functional.html#gaa46de6f618d9f14edb1589b36b6e75ec">More...</a><br /></td></tr>
<tr class="separator:gaa46de6f618d9f14edb1589b36b6e75ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga778b2daa27882e71d28b6f2b38982ddf"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga778b2daa27882e71d28b6f2b38982ddf">partial</a></td></tr>
<tr class="memdesc:ga778b2daa27882e71d28b6f2b38982ddf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partially apply a function to some arguments.  <a href="group__group-functional.html#ga778b2daa27882e71d28b6f2b38982ddf">More...</a><br /></td></tr>
<tr class="separator:ga778b2daa27882e71d28b6f2b38982ddf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaefe9fd152cba94be71c2b5b9de689d23"><td class="memItemLeft" align="right" valign="top">constexpr unspecified&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#gaefe9fd152cba94be71c2b5b9de689d23">_</a> {}</td></tr>
<tr class="memdesc:gaefe9fd152cba94be71c2b5b9de689d23"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create simple functions representing C++ operators inline.  <a href="group__group-functional.html#gaefe9fd152cba94be71c2b5b9de689d23">More...</a><br /></td></tr>
<tr class="separator:gaefe9fd152cba94be71c2b5b9de689d23"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e648f0d3fc0209ec024e9d759a5e8f8"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-functional.html#ga6e648f0d3fc0209ec024e9d759a5e8f8">reverse_partial</a></td></tr>
<tr class="memdesc:ga6e648f0d3fc0209ec024e9d759a5e8f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Partially apply a function to some arguments.  <a href="group__group-functional.html#ga6e648f0d3fc0209ec024e9d759a5e8f8">More...</a><br /></td></tr>
<tr class="separator:ga6e648f0d3fc0209ec024e9d759a5e8f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga983a55dbd93d766fd37689ea32e4ddfb"><td class="memTemplParams" colspan="2">template&lt;typename S &gt; </td></tr>
<tr class="memitem:ga983a55dbd93d766fd37689ea32e4ddfb"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_struct.html#ga983a55dbd93d766fd37689ea32e4ddfb">accessors</a></td></tr>
<tr class="memdesc:ga983a55dbd93d766fd37689ea32e4ddfb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a <code>Sequence</code> of pairs representing the accessors of the data structure.  <a href="group__group-_struct.html#ga983a55dbd93d766fd37689ea32e4ddfb">More...</a><br /></td></tr>
<tr class="separator:ga983a55dbd93d766fd37689ea32e4ddfb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga81ae9764dd7818ad36270c6419fb1082"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_searchable.html#ga81ae9764dd7818ad36270c6419fb1082">all</a></td></tr>
<tr class="memdesc:ga81ae9764dd7818ad36270c6419fb1082"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether all the keys of the structure are true-valued.  <a href="group__group-_searchable.html#ga81ae9764dd7818ad36270c6419fb1082">More...</a><br /></td></tr>
<tr class="separator:ga81ae9764dd7818ad36270c6419fb1082"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a168950082f38afd9edf256f336c8ba"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_searchable.html#ga3a168950082f38afd9edf256f336c8ba">all_of</a></td></tr>
<tr class="memdesc:ga3a168950082f38afd9edf256f336c8ba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether all the keys of the structure satisfy the <code>predicate</code>.  <a href="group__group-_searchable.html#ga3a168950082f38afd9edf256f336c8ba">More...</a><br /></td></tr>
<tr class="separator:ga3a168950082f38afd9edf256f336c8ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga14066f5672867c123524e0e0978069eb"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_logical.html#ga14066f5672867c123524e0e0978069eb">and_</a></td></tr>
<tr class="memdesc:ga14066f5672867c123524e0e0978069eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return whether all the arguments are true-valued.  <a href="group__group-_logical.html#ga14066f5672867c123524e0e0978069eb">More...</a><br /></td></tr>
<tr class="separator:ga14066f5672867c123524e0e0978069eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab7d632b9319b10b1eb7e98f9e1cf8a28"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_searchable.html#gab7d632b9319b10b1eb7e98f9e1cf8a28">any</a></td></tr>
<tr class="memdesc:gab7d632b9319b10b1eb7e98f9e1cf8a28"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether any key of the structure is true-valued.  <a href="group__group-_searchable.html#gab7d632b9319b10b1eb7e98f9e1cf8a28">More...</a><br /></td></tr>
<tr class="separator:gab7d632b9319b10b1eb7e98f9e1cf8a28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5f7ff0125c448983e1b96c3ffb84f646"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_searchable.html#ga5f7ff0125c448983e1b96c3ffb84f646">any_of</a></td></tr>
<tr class="memdesc:ga5f7ff0125c448983e1b96c3ffb84f646"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether any key of the structure satisfies the <code>predicate</code>.  <a href="group__group-_searchable.html#ga5f7ff0125c448983e1b96c3ffb84f646">More...</a><br /></td></tr>
<tr class="separator:ga5f7ff0125c448983e1b96c3ffb84f646"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga08624924fe05f0cfbfbd6e439db01873"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_monad_plus.html#ga08624924fe05f0cfbfbd6e439db01873">append</a></td></tr>
<tr class="memdesc:ga08624924fe05f0cfbfbd6e439db01873"><td class="mdescLeft">&#160;</td><td class="mdescRight">Append an element to a monadic structure.  <a href="group__group-_monad_plus.html#ga08624924fe05f0cfbfbd6e439db01873">More...</a><br /></td></tr>
<tr class="separator:ga08624924fe05f0cfbfbd6e439db01873"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8a484304380eae38f3d9663d98860129"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga8a484304380eae38f3d9663d98860129">at</a></td></tr>
<tr class="memdesc:ga8a484304380eae38f3d9663d98860129"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>n</code>th element of an iterable.  <a href="group__group-_iterable.html#ga8a484304380eae38f3d9663d98860129">More...</a><br /></td></tr>
<tr class="separator:ga8a484304380eae38f3d9663d98860129"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4cb99cfbef936cb267e76f66f40f529c"><td class="memTemplParams" colspan="2">template&lt;std::size_t n&gt; </td></tr>
<tr class="memitem:ga4cb99cfbef936cb267e76f66f40f529c"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga4cb99cfbef936cb267e76f66f40f529c">at_c</a></td></tr>
<tr class="memdesc:ga4cb99cfbef936cb267e76f66f40f529c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>at</code>; provided for convenience.  <a href="group__group-_iterable.html#ga4cb99cfbef936cb267e76f66f40f529c">More...</a><br /></td></tr>
<tr class="separator:ga4cb99cfbef936cb267e76f66f40f529c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3c1826aee6c6eb577810bb99c5c3e53d"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_searchable.html#ga3c1826aee6c6eb577810bb99c5c3e53d">at_key</a></td></tr>
<tr class="memdesc:ga3c1826aee6c6eb577810bb99c5c3e53d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the value associated to the given key in a structure, or fail.  <a href="group__group-_searchable.html#ga3c1826aee6c6eb577810bb99c5c3e53d">More...</a><br /></td></tr>
<tr class="separator:ga3c1826aee6c6eb577810bb99c5c3e53d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3f4d0035345a453284e46303862d463"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#gab3f4d0035345a453284e46303862d463">back</a></td></tr>
<tr class="memdesc:gab3f4d0035345a453284e46303862d463"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the last element of a non-empty and finite iterable.  <a href="group__group-_iterable.html#gab3f4d0035345a453284e46303862d463">More...</a><br /></td></tr>
<tr class="separator:gab3f4d0035345a453284e46303862d463"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9c2ffe2e51780e57a38d9e7e31b87cdc"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_comparable.html#ga9c2ffe2e51780e57a38d9e7e31b87cdc">comparing</a></td></tr>
<tr class="memdesc:ga9c2ffe2e51780e57a38d9e7e31b87cdc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a function performing <code>equal</code> after applying a transformation to both arguments.  <a href="group__group-_comparable.html#ga9c2ffe2e51780e57a38d9e7e31b87cdc">More...</a><br /></td></tr>
<tr class="separator:ga9c2ffe2e51780e57a38d9e7e31b87cdc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1946e96c3b4c178c7ae8703724c29c37"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_monad_plus.html#ga1946e96c3b4c178c7ae8703724c29c37">concat</a></td></tr>
<tr class="memdesc:ga1946e96c3b4c178c7ae8703724c29c37"><td class="mdescLeft">&#160;</td><td class="mdescRight">Combine two monadic structures together.  <a href="group__group-_monad_plus.html#ga1946e96c3b4c178c7ae8703724c29c37">More...</a><br /></td></tr>
<tr class="separator:ga1946e96c3b4c178c7ae8703724c29c37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga38e7748956cbc9f3d9bb035ac8577906"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_searchable.html#ga38e7748956cbc9f3d9bb035ac8577906">contains</a></td></tr>
<tr class="memdesc:ga38e7748956cbc9f3d9bb035ac8577906"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the key occurs in the structure.  <a href="group__group-_searchable.html#ga38e7748956cbc9f3d9bb035ac8577906">More...</a><br /></td></tr>
<tr class="separator:ga38e7748956cbc9f3d9bb035ac8577906"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0d9456ceda38b6ca664998e79d7c45b7"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_searchable.html#ga0d9456ceda38b6ca664998e79d7c45b7">in</a> = <a class="el" href="group__group-functional.html#ga7bdafba6dc801f1d2d83731ad9714557">hana::infix</a>(<a class="el" href="group__group-functional.html#ga004f884cdbb85c2efe3383c1db450094">hana::flip</a>(<a class="el" href="group__group-_searchable.html#ga38e7748956cbc9f3d9bb035ac8577906">hana::contains</a>))</td></tr>
<tr class="memdesc:ga0d9456ceda38b6ca664998e79d7c45b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return whether the key occurs in the structure.  <a href="group__group-_searchable.html#ga0d9456ceda38b6ca664998e79d7c45b7">More...</a><br /></td></tr>
<tr class="separator:ga0d9456ceda38b6ca664998e79d7c45b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga38cf78e1e3e262f7f1c71ddd9ca70cd9"><td class="memTemplParams" colspan="2">template&lt;typename Tag , typename optional_T &gt; </td></tr>
<tr class="memitem:ga38cf78e1e3e262f7f1c71ddd9ca70cd9"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-core.html#ga38cf78e1e3e262f7f1c71ddd9ca70cd9">is_a</a> = see-documentation</td></tr>
<tr class="memdesc:ga38cf78e1e3e262f7f1c71ddd9ca70cd9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the tag of an object matches a given tag.  <a href="group__group-core.html#ga38cf78e1e3e262f7f1c71ddd9ca70cd9">More...</a><br /></td></tr>
<tr class="separator:ga38cf78e1e3e262f7f1c71ddd9ca70cd9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7fdbde52f5fe384a816c6f39ff272df9"><td class="memTemplParams" colspan="2">
template&lt;typename Tag , typename ... T&gt; </td></tr>
<tr class="memitem:ga7fdbde52f5fe384a816c6f39ff272df9"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-core.html#ga7fdbde52f5fe384a816c6f39ff272df9">is_an</a> = <a class="el" href="group__group-core.html#ga38cf78e1e3e262f7f1c71ddd9ca70cd9">is_a</a>&lt;Tag, T...&gt;</td></tr>
<tr class="memdesc:ga7fdbde52f5fe384a816c6f39ff272df9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>is_a</code>; provided for consistency with the rules of the English language. <br /></td></tr>
<tr class="separator:ga7fdbde52f5fe384a816c6f39ff272df9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1d92480f0af1029878e773dafa3e2f60"><td class="memTemplParams" colspan="2">template&lt;typename Tag &gt; </td></tr>
<tr class="memitem:ga1d92480f0af1029878e773dafa3e2f60"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-core.html#ga1d92480f0af1029878e773dafa3e2f60">make</a></td></tr>
<tr class="memdesc:ga1d92480f0af1029878e773dafa3e2f60"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create an object of the given tag with the given arguments.  <a href="group__group-core.html#ga1d92480f0af1029878e773dafa3e2f60">More...</a><br /></td></tr>
<tr class="separator:ga1d92480f0af1029878e773dafa3e2f60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc70755c1d059139297814fb3bfeb91e"><td class="memTemplParams" colspan="2">template&lt;typename To &gt; </td></tr>
<tr class="memitem:gadc70755c1d059139297814fb3bfeb91e"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-core.html#gadc70755c1d059139297814fb3bfeb91e">to</a></td></tr>
<tr class="memdesc:gadc70755c1d059139297814fb3bfeb91e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts an object from one data type to another.  <a href="group__group-core.html#gadc70755c1d059139297814fb3bfeb91e">More...</a><br /></td></tr>
<tr class="separator:gadc70755c1d059139297814fb3bfeb91e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3159cfa41be18a396926741b0a3fdefd"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_foldable.html#ga3159cfa41be18a396926741b0a3fdefd">count</a></td></tr>
<tr class="memdesc:ga3159cfa41be18a396926741b0a3fdefd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the number of elements in the structure that compare equal to a given value.  <a href="group__group-_foldable.html#ga3159cfa41be18a396926741b0a3fdefd">More...</a><br /></td></tr>
<tr class="separator:ga3159cfa41be18a396926741b0a3fdefd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga39d71be65d5b98e7d035a3e5c607e1b4"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_foldable.html#ga39d71be65d5b98e7d035a3e5c607e1b4">count_if</a></td></tr>
<tr class="memdesc:ga39d71be65d5b98e7d035a3e5c607e1b4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the number of elements in the structure for which the <code>predicate</code> is satisfied.  <a href="group__group-_foldable.html#ga39d71be65d5b98e7d035a3e5c607e1b4">More...</a><br /></td></tr>
<tr class="separator:ga39d71be65d5b98e7d035a3e5c607e1b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaf46c168f721da9effcc7336a997f5d6"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_monad_plus.html#gaaf46c168f721da9effcc7336a997f5d6">cycle</a></td></tr>
<tr class="memdesc:gaaf46c168f721da9effcc7336a997f5d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Combine a monadic structure with itself <code>n</code> times.  <a href="group__group-_monad_plus.html#gaaf46c168f721da9effcc7336a997f5d6">More...</a><br /></td></tr>
<tr class="separator:gaaf46c168f721da9effcc7336a997f5d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4225a7988ce98903228913dde53762df"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_euclidean_ring.html#ga4225a7988ce98903228913dde53762df">div</a></td></tr>
<tr class="memdesc:ga4225a7988ce98903228913dde53762df"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generalized integer division.  <a href="group__group-_euclidean_ring.html#ga4225a7988ce98903228913dde53762df">More...</a><br /></td></tr>
<tr class="separator:ga4225a7988ce98903228913dde53762df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac10231310abc86b056585ea0d0e96ef7"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gac10231310abc86b056585ea0d0e96ef7">drop_back</a></td></tr>
<tr class="memdesc:gac10231310abc86b056585ea0d0e96ef7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Drop the last <code>n</code> elements of a finite sequence, and return the rest.  <a href="group__group-_sequence.html#gac10231310abc86b056585ea0d0e96ef7">More...</a><br /></td></tr>
<tr class="separator:gac10231310abc86b056585ea0d0e96ef7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad23ce0a4906e2bb0a52f38837b134757"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757">drop_front</a></td></tr>
<tr class="memdesc:gad23ce0a4906e2bb0a52f38837b134757"><td class="mdescLeft">&#160;</td><td class="mdescRight">Drop the first <code>n</code> elements of an iterable, and return the rest.  <a href="group__group-_iterable.html#gad23ce0a4906e2bb0a52f38837b134757">More...</a><br /></td></tr>
<tr class="separator:gad23ce0a4906e2bb0a52f38837b134757"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4dbc6a82f03ca35b7ac418ca30889cc4"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga4dbc6a82f03ca35b7ac418ca30889cc4">drop_front_exactly</a></td></tr>
<tr class="memdesc:ga4dbc6a82f03ca35b7ac418ca30889cc4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Drop the first <code>n</code> elements of an iterable, and return the rest.  <a href="group__group-_iterable.html#ga4dbc6a82f03ca35b7ac418ca30889cc4">More...</a><br /></td></tr>
<tr class="separator:ga4dbc6a82f03ca35b7ac418ca30889cc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9f1d02c74a6bdc1db260e0d6a8f1ee56"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga9f1d02c74a6bdc1db260e0d6a8f1ee56">drop_while</a></td></tr>
<tr class="memdesc:ga9f1d02c74a6bdc1db260e0d6a8f1ee56"><td class="mdescLeft">&#160;</td><td class="mdescRight">Drop elements from an iterable up to, but excluding, the first element for which the <code>predicate</code> is not satisfied.  <a href="group__group-_iterable.html#ga9f1d02c74a6bdc1db260e0d6a8f1ee56">More...</a><br /></td></tr>
<tr class="separator:ga9f1d02c74a6bdc1db260e0d6a8f1ee56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa6be1e83ad72b9d69b43b4bada0f3a75"><td class="memTemplParams" colspan="2">template&lt;typename M &gt; </td></tr>
<tr class="memitem:gaa6be1e83ad72b9d69b43b4bada0f3a75"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_monad_plus.html#gaa6be1e83ad72b9d69b43b4bada0f3a75">empty</a></td></tr>
<tr class="memdesc:gaa6be1e83ad72b9d69b43b4bada0f3a75"><td class="mdescLeft">&#160;</td><td class="mdescRight">Identity of the monadic combination <code>concat</code>.  <a href="group__group-_monad_plus.html#gaa6be1e83ad72b9d69b43b4bada0f3a75">More...</a><br /></td></tr>
<tr class="separator:gaa6be1e83ad72b9d69b43b4bada0f3a75"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacaf1ebea6b3ab96ac9dcb82f0e64e547"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_comparable.html#gacaf1ebea6b3ab96ac9dcb82f0e64e547">equal</a></td></tr>
<tr class="memdesc:gacaf1ebea6b3ab96ac9dcb82f0e64e547"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a <code>Logical</code> representing whether <code>x</code> is equal to <code>y</code>.  <a href="group__group-_comparable.html#gacaf1ebea6b3ab96ac9dcb82f0e64e547">More...</a><br /></td></tr>
<tr class="separator:gacaf1ebea6b3ab96ac9dcb82f0e64e547"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab64636f84de983575aac0208f5fa840c"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_logical.html#gab64636f84de983575aac0208f5fa840c">eval_if</a></td></tr>
<tr class="memdesc:gab64636f84de983575aac0208f5fa840c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Conditionally execute one of two branches based on a condition.  <a href="group__group-_logical.html#gab64636f84de983575aac0208f5fa840c">More...</a><br /></td></tr>
<tr class="separator:gab64636f84de983575aac0208f5fa840c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga65cc6d9f522fb9e8e3b28d80ee5c822a"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_monad_plus.html#ga65cc6d9f522fb9e8e3b28d80ee5c822a">filter</a></td></tr>
<tr class="memdesc:ga65cc6d9f522fb9e8e3b28d80ee5c822a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter a monadic structure using a custom predicate.  <a href="group__group-_monad_plus.html#ga65cc6d9f522fb9e8e3b28d80ee5c822a">More...</a><br /></td></tr>
<tr class="separator:ga65cc6d9f522fb9e8e3b28d80ee5c822a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6b6cdd69942b0fe3bf5254247f9c861e"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_searchable.html#ga6b6cdd69942b0fe3bf5254247f9c861e">find</a></td></tr>
<tr class="memdesc:ga6b6cdd69942b0fe3bf5254247f9c861e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Finds the value associated to the given key in a structure.  <a href="group__group-_searchable.html#ga6b6cdd69942b0fe3bf5254247f9c861e">More...</a><br /></td></tr>
<tr class="separator:ga6b6cdd69942b0fe3bf5254247f9c861e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f99b80672aa80a7eb8b223955ce546f"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_searchable.html#ga7f99b80672aa80a7eb8b223955ce546f">find_if</a></td></tr>
<tr class="memdesc:ga7f99b80672aa80a7eb8b223955ce546f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Finds the value associated to the first key satisfying a predicate.  <a href="group__group-_searchable.html#ga7f99b80672aa80a7eb8b223955ce546f">More...</a><br /></td></tr>
<tr class="separator:ga7f99b80672aa80a7eb8b223955ce546f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga34bbf4281de06dc3540441e8b2bd24f4"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_product.html#ga34bbf4281de06dc3540441e8b2bd24f4">first</a></td></tr>
<tr class="memdesc:ga34bbf4281de06dc3540441e8b2bd24f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the first element of a pair.  <a href="group__group-_product.html#ga34bbf4281de06dc3540441e8b2bd24f4">More...</a><br /></td></tr>
<tr class="separator:ga34bbf4281de06dc3540441e8b2bd24f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa0fde17f3b947a0678a1c0c01232f2cc"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_foldable.html#gaa0fde17f3b947a0678a1c0c01232f2cc">fold</a> = fold_left</td></tr>
<tr class="memdesc:gaa0fde17f3b947a0678a1c0c01232f2cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>fold_left</code>; provided for convenience.  <a href="group__group-_foldable.html#gaa0fde17f3b947a0678a1c0c01232f2cc">More...</a><br /></td></tr>
<tr class="separator:gaa0fde17f3b947a0678a1c0c01232f2cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2af382f7e644ce3707710bbad313e9c2"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_foldable.html#ga2af382f7e644ce3707710bbad313e9c2">for_each</a></td></tr>
<tr class="memdesc:ga2af382f7e644ce3707710bbad313e9c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Perform an action on each element of a foldable, discarding the result each time.  <a href="group__group-_foldable.html#ga2af382f7e644ce3707710bbad313e9c2">More...</a><br /></td></tr>
<tr class="separator:ga2af382f7e644ce3707710bbad313e9c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8a67ea10e8082dbe6705e573fa978444"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga8a67ea10e8082dbe6705e573fa978444">front</a></td></tr>
<tr class="memdesc:ga8a67ea10e8082dbe6705e573fa978444"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the first element of a non-empty iterable.  <a href="group__group-_iterable.html#ga8a67ea10e8082dbe6705e573fa978444">More...</a><br /></td></tr>
<tr class="separator:ga8a67ea10e8082dbe6705e573fa978444"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga19fcf61d8d1179903952c0f564c538aa"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_foldable.html#ga19fcf61d8d1179903952c0f564c538aa">fuse</a></td></tr>
<tr class="memdesc:ga19fcf61d8d1179903952c0f564c538aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transform a function taking multiple arguments into a function that can be called with a compile-time <code>Foldable</code>.  <a href="group__group-_foldable.html#ga19fcf61d8d1179903952c0f564c538aa">More...</a><br /></td></tr>
<tr class="separator:ga19fcf61d8d1179903952c0f564c538aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf9a073eafebbe514fb19dff82318f198"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_orderable.html#gaf9a073eafebbe514fb19dff82318f198">greater</a></td></tr>
<tr class="memdesc:gaf9a073eafebbe514fb19dff82318f198"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a <code>Logical</code> representing whether <code>x</code> is greater than <code>y</code>.  <a href="group__group-_orderable.html#gaf9a073eafebbe514fb19dff82318f198">More...</a><br /></td></tr>
<tr class="separator:gaf9a073eafebbe514fb19dff82318f198"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6023631e7d0a01e16dc3fa4221fbd703"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_orderable.html#ga6023631e7d0a01e16dc3fa4221fbd703">greater_equal</a></td></tr>
<tr class="memdesc:ga6023631e7d0a01e16dc3fa4221fbd703"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a <code>Logical</code> representing whether <code>x</code> is greater than or equal to <code>y</code>.  <a href="group__group-_orderable.html#ga6023631e7d0a01e16dc3fa4221fbd703">More...</a><br /></td></tr>
<tr class="separator:ga6023631e7d0a01e16dc3fa4221fbd703"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafd655d2222367131e7a63616e93dd080"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_logical.html#gafd655d2222367131e7a63616e93dd080">if_</a></td></tr>
<tr class="memdesc:gafd655d2222367131e7a63616e93dd080"><td class="mdescLeft">&#160;</td><td class="mdescRight">Conditionally return one of two values based on a condition.  <a href="group__group-_logical.html#gafd655d2222367131e7a63616e93dd080">More...</a><br /></td></tr>
<tr class="separator:gafd655d2222367131e7a63616e93dd080"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5332fd1dd82edf08379958ba21d57a87"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga5332fd1dd82edf08379958ba21d57a87">index_if</a></td></tr>
<tr class="memdesc:ga5332fd1dd82edf08379958ba21d57a87"><td class="mdescLeft">&#160;</td><td class="mdescRight">Finds the value associated to the first key satisfying a predicate.  <a href="group__group-_iterable.html#ga5332fd1dd82edf08379958ba21d57a87">More...</a><br /></td></tr>
<tr class="separator:ga5332fd1dd82edf08379958ba21d57a87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae22a1a184b1b2dd550fa4fa619bed2e9"><td class="memItemLeft" align="right" valign="top">constexpr insert_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gae22a1a184b1b2dd550fa4fa619bed2e9">insert</a> {}</td></tr>
<tr class="memdesc:gae22a1a184b1b2dd550fa4fa619bed2e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Insert a value at a given index in a sequence.  <a href="group__group-_sequence.html#gae22a1a184b1b2dd550fa4fa619bed2e9">More...</a><br /></td></tr>
<tr class="separator:gae22a1a184b1b2dd550fa4fa619bed2e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3410ba833cf1ff1d929fcfda4df2eae1"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga3410ba833cf1ff1d929fcfda4df2eae1">insert_range</a></td></tr>
<tr class="memdesc:ga3410ba833cf1ff1d929fcfda4df2eae1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Insert several values at a given index in a sequence.  <a href="group__group-_sequence.html#ga3410ba833cf1ff1d929fcfda4df2eae1">More...</a><br /></td></tr>
<tr class="separator:ga3410ba833cf1ff1d929fcfda4df2eae1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa18061cd0f63cfaae89abf43ff92b79e"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gaa18061cd0f63cfaae89abf43ff92b79e">intersperse</a></td></tr>
<tr class="memdesc:gaa18061cd0f63cfaae89abf43ff92b79e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Insert a value between each pair of elements in a finite sequence.  <a href="group__group-_sequence.html#gaa18061cd0f63cfaae89abf43ff92b79e">More...</a><br /></td></tr>
<tr class="separator:gaa18061cd0f63cfaae89abf43ff92b79e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3b8269d4f5cdd6dd549fae32280795a0"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_searchable.html#ga3b8269d4f5cdd6dd549fae32280795a0">is_disjoint</a></td></tr>
<tr class="memdesc:ga3b8269d4f5cdd6dd549fae32280795a0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether two <code>Searchable</code>s are disjoint.  <a href="group__group-_searchable.html#ga3b8269d4f5cdd6dd549fae32280795a0">More...</a><br /></td></tr>
<tr class="separator:ga3b8269d4f5cdd6dd549fae32280795a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2a05f564f8a7e4afa04fcbc07ad8f394"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_iterable.html#ga2a05f564f8a7e4afa04fcbc07ad8f394">is_empty</a></td></tr>
<tr class="memdesc:ga2a05f564f8a7e4afa04fcbc07ad8f394"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the iterable is empty.  <a href="group__group-_iterable.html#ga2a05f564f8a7e4afa04fcbc07ad8f394">More...</a><br /></td></tr>
<tr class="separator:ga2a05f564f8a7e4afa04fcbc07ad8f394"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadccfc79f1acdd8043d2baa16df16ec9f"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_searchable.html#gadccfc79f1acdd8043d2baa16df16ec9f">is_subset</a></td></tr>
<tr class="memdesc:gadccfc79f1acdd8043d2baa16df16ec9f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether a structure contains a subset of the keys of another structure.  <a href="group__group-_searchable.html#gadccfc79f1acdd8043d2baa16df16ec9f">More...</a><br /></td></tr>
<tr class="separator:gadccfc79f1acdd8043d2baa16df16ec9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf8c7199742581e6e66c8397def68e2d3"><td class="memItemLeft" align="right" valign="top">constexpr keys_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_struct.html#gaf8c7199742581e6e66c8397def68e2d3">keys</a> {}</td></tr>
<tr class="memdesc:gaf8c7199742581e6e66c8397def68e2d3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a <code>Sequence</code> containing the name of the members of the data structure.  <a href="group__group-_struct.html#gaf8c7199742581e6e66c8397def68e2d3">More...</a><br /></td></tr>
<tr class="separator:gaf8c7199742581e6e66c8397def68e2d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf0f8f717245620dc28cd7d7fa44d7475"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_foldable.html#gaf0f8f717245620dc28cd7d7fa44d7475">length</a></td></tr>
<tr class="memdesc:gaf0f8f717245620dc28cd7d7fa44d7475"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the number of elements in a foldable structure.  <a href="group__group-_foldable.html#gaf0f8f717245620dc28cd7d7fa44d7475">More...</a><br /></td></tr>
<tr class="separator:gaf0f8f717245620dc28cd7d7fa44d7475"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad510011602bdb14686f1c4ec145301c9"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_orderable.html#gad510011602bdb14686f1c4ec145301c9">less</a></td></tr>
<tr class="memdesc:gad510011602bdb14686f1c4ec145301c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a <code>Logical</code> representing whether <code>x</code> is less than <code>y</code>.  <a href="group__group-_orderable.html#gad510011602bdb14686f1c4ec145301c9">More...</a><br /></td></tr>
<tr class="separator:gad510011602bdb14686f1c4ec145301c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9917dd82beb67151bf5657245d37b851"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_orderable.html#ga9917dd82beb67151bf5657245d37b851">less_equal</a></td></tr>
<tr class="memdesc:ga9917dd82beb67151bf5657245d37b851"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a <code>Logical</code> representing whether <code>x</code> is less than or equal to <code>y</code>.  <a href="group__group-_orderable.html#ga9917dd82beb67151bf5657245d37b851">More...</a><br /></td></tr>
<tr class="separator:ga9917dd82beb67151bf5657245d37b851"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga712038d7abbc7159f8792788f7cd0c73"><td class="memTemplParams" colspan="2">template&lt;typename A &gt; </td></tr>
<tr class="memitem:ga712038d7abbc7159f8792788f7cd0c73"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_applicative.html#ga712038d7abbc7159f8792788f7cd0c73">lift</a></td></tr>
<tr class="memdesc:ga712038d7abbc7159f8792788f7cd0c73"><td class="mdescLeft">&#160;</td><td class="mdescRight">Lift a value into an <code>Applicative</code> structure.  <a href="group__group-_applicative.html#ga712038d7abbc7159f8792788f7cd0c73">More...</a><br /></td></tr>
<tr class="separator:ga712038d7abbc7159f8792788f7cd0c73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga999eee8ca8750f9b1afa0d7a1db28030"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_orderable.html#ga999eee8ca8750f9b1afa0d7a1db28030">max</a></td></tr>
<tr class="memdesc:ga999eee8ca8750f9b1afa0d7a1db28030"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the greatest of its arguments according to the <code>less</code> ordering.  <a href="group__group-_orderable.html#ga999eee8ca8750f9b1afa0d7a1db28030">More...</a><br /></td></tr>
<tr class="separator:ga999eee8ca8750f9b1afa0d7a1db28030"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad301dd8e9fb4639d7874619c97d6d427"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_struct.html#gad301dd8e9fb4639d7874619c97d6d427">members</a></td></tr>
<tr class="memdesc:gad301dd8e9fb4639d7874619c97d6d427"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a <code>Sequence</code> containing the members of a <code>Struct</code>.  <a href="group__group-_struct.html#gad301dd8e9fb4639d7874619c97d6d427">More...</a><br /></td></tr>
<tr class="separator:gad301dd8e9fb4639d7874619c97d6d427"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2d54f189ea6f57fb2c0d772169440c5c"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_orderable.html#ga2d54f189ea6f57fb2c0d772169440c5c">min</a></td></tr>
<tr class="memdesc:ga2d54f189ea6f57fb2c0d772169440c5c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the smallest of its arguments according to the <code>less</code> ordering.  <a href="group__group-_orderable.html#ga2d54f189ea6f57fb2c0d772169440c5c">More...</a><br /></td></tr>
<tr class="separator:ga2d54f189ea6f57fb2c0d772169440c5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2020c526324f361a2b990fe8d1b07c20"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_group.html#ga2020c526324f361a2b990fe8d1b07c20">minus</a></td></tr>
<tr class="memdesc:ga2020c526324f361a2b990fe8d1b07c20"><td class="mdescLeft">&#160;</td><td class="mdescRight">Subtract two elements of a group.  <a href="group__group-_group.html#ga2020c526324f361a2b990fe8d1b07c20">More...</a><br /></td></tr>
<tr class="separator:ga2020c526324f361a2b990fe8d1b07c20"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9b47b223d5b02db933b3c93b5bd1a062"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_euclidean_ring.html#ga9b47b223d5b02db933b3c93b5bd1a062">mod</a></td></tr>
<tr class="memdesc:ga9b47b223d5b02db933b3c93b5bd1a062"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generalized integer modulus.  <a href="group__group-_euclidean_ring.html#ga9b47b223d5b02db933b3c93b5bd1a062">More...</a><br /></td></tr>
<tr class="separator:ga9b47b223d5b02db933b3c93b5bd1a062"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga052d31c269a6a438cc8004c9ad1efdfa"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_ring.html#ga052d31c269a6a438cc8004c9ad1efdfa">mult</a></td></tr>
<tr class="memdesc:ga052d31c269a6a438cc8004c9ad1efdfa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Associative operation of a <code>Ring</code>.  <a href="group__group-_ring.html#ga052d31c269a6a438cc8004c9ad1efdfa">More...</a><br /></td></tr>
<tr class="separator:ga052d31c269a6a438cc8004c9ad1efdfa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga02e81002f40ba52eac4cf1974c7e0cdb"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_group.html#ga02e81002f40ba52eac4cf1974c7e0cdb">negate</a></td></tr>
<tr class="memdesc:ga02e81002f40ba52eac4cf1974c7e0cdb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the inverse of an element of a group.  <a href="group__group-_group.html#ga02e81002f40ba52eac4cf1974c7e0cdb">More...</a><br /></td></tr>
<tr class="separator:ga02e81002f40ba52eac4cf1974c7e0cdb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga614ff1e575806f59246b17006e19d479"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_searchable.html#ga614ff1e575806f59246b17006e19d479">none</a></td></tr>
<tr class="memdesc:ga614ff1e575806f59246b17006e19d479"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether all of the keys of the structure are false-valued.  <a href="group__group-_searchable.html#ga614ff1e575806f59246b17006e19d479">More...</a><br /></td></tr>
<tr class="separator:ga614ff1e575806f59246b17006e19d479"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43954c791b5b1351fb009e2a643d00f5"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_searchable.html#ga43954c791b5b1351fb009e2a643d00f5">none_of</a></td></tr>
<tr class="memdesc:ga43954c791b5b1351fb009e2a643d00f5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether none of the keys of the structure satisfy the <code>predicate</code>.  <a href="group__group-_searchable.html#ga43954c791b5b1351fb009e2a643d00f5">More...</a><br /></td></tr>
<tr class="separator:ga43954c791b5b1351fb009e2a643d00f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4a7c9d7037601d5e553fd20777958980"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_logical.html#ga4a7c9d7037601d5e553fd20777958980">not_</a></td></tr>
<tr class="memdesc:ga4a7c9d7037601d5e553fd20777958980"><td class="mdescLeft">&#160;</td><td class="mdescRight">Negates a <code>Logical</code>.  <a href="group__group-_logical.html#ga4a7c9d7037601d5e553fd20777958980">More...</a><br /></td></tr>
<tr class="separator:ga4a7c9d7037601d5e553fd20777958980"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae33be2e0d5e04f19082f4b7740dfc9cd"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_comparable.html#gae33be2e0d5e04f19082f4b7740dfc9cd">not_equal</a></td></tr>
<tr class="memdesc:gae33be2e0d5e04f19082f4b7740dfc9cd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a <code>Logical</code> representing whether <code>x</code> is not equal to <code>y</code>.  <a href="group__group-_comparable.html#gae33be2e0d5e04f19082f4b7740dfc9cd">More...</a><br /></td></tr>
<tr class="separator:gae33be2e0d5e04f19082f4b7740dfc9cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadea531feb3b0a1c5c3d777f7ab45e932"><td class="memTemplParams" colspan="2">template&lt;typename R &gt; </td></tr>
<tr class="memitem:gadea531feb3b0a1c5c3d777f7ab45e932"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_ring.html#gadea531feb3b0a1c5c3d777f7ab45e932">one</a></td></tr>
<tr class="memdesc:gadea531feb3b0a1c5c3d777f7ab45e932"><td class="mdescLeft">&#160;</td><td class="mdescRight">Identity of the <code>Ring</code> multiplication.  <a href="group__group-_ring.html#gadea531feb3b0a1c5c3d777f7ab45e932">More...</a><br /></td></tr>
<tr class="separator:gadea531feb3b0a1c5c3d777f7ab45e932"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga68c00efbeb69339bfa157a78ebdd3f87"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_logical.html#ga68c00efbeb69339bfa157a78ebdd3f87">or_</a></td></tr>
<tr class="memdesc:ga68c00efbeb69339bfa157a78ebdd3f87"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return whether any of the arguments is true-valued.  <a href="group__group-_logical.html#ga68c00efbeb69339bfa157a78ebdd3f87">More...</a><br /></td></tr>
<tr class="separator:ga68c00efbeb69339bfa157a78ebdd3f87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf7e94ba859710cd6ba6152e5dc18977d"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_orderable.html#gaf7e94ba859710cd6ba6152e5dc18977d">ordering</a></td></tr>
<tr class="memdesc:gaf7e94ba859710cd6ba6152e5dc18977d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a function performing <code>less</code> after applying a transformation to both arguments.  <a href="group__group-_orderable.html#gaf7e94ba859710cd6ba6152e5dc18977d">More...</a><br /></td></tr>
<tr class="separator:gaf7e94ba859710cd6ba6152e5dc18977d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac1e182ac088f1990edd739424d30ea07"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gac1e182ac088f1990edd739424d30ea07">permutations</a></td></tr>
<tr class="memdesc:gac1e182ac088f1990edd739424d30ea07"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return a sequence of all the permutations of the given sequence.  <a href="group__group-_sequence.html#gac1e182ac088f1990edd739424d30ea07">More...</a><br /></td></tr>
<tr class="separator:gac1e182ac088f1990edd739424d30ea07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeb5d4a1e967e319712f9e4791948896c"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_monoid.html#gaeb5d4a1e967e319712f9e4791948896c">plus</a></td></tr>
<tr class="memdesc:gaeb5d4a1e967e319712f9e4791948896c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Associative binary operation on a <code>Monoid</code>.  <a href="group__group-_monoid.html#gaeb5d4a1e967e319712f9e4791948896c">More...</a><br /></td></tr>
<tr class="separator:gaeb5d4a1e967e319712f9e4791948896c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0ee3cff9ec646bcc7217f00ee6099b72"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_ring.html#ga0ee3cff9ec646bcc7217f00ee6099b72">power</a></td></tr>
<tr class="memdesc:ga0ee3cff9ec646bcc7217f00ee6099b72"><td class="mdescLeft">&#160;</td><td class="mdescRight">Elevate a ring element to its <code>n</code>th power.  <a href="group__group-_ring.html#ga0ee3cff9ec646bcc7217f00ee6099b72">More...</a><br /></td></tr>
<tr class="separator:ga0ee3cff9ec646bcc7217f00ee6099b72"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3022fdfe454dc9bc1f79b5dfeba13b5e"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_monad_plus.html#ga3022fdfe454dc9bc1f79b5dfeba13b5e">prefix</a></td></tr>
<tr class="memdesc:ga3022fdfe454dc9bc1f79b5dfeba13b5e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inserts a value before each element of a monadic structure.  <a href="group__group-_monad_plus.html#ga3022fdfe454dc9bc1f79b5dfeba13b5e">More...</a><br /></td></tr>
<tr class="separator:ga3022fdfe454dc9bc1f79b5dfeba13b5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga69afbfd4e91125e3e52fcb409135ca7c"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_monad_plus.html#ga69afbfd4e91125e3e52fcb409135ca7c">prepend</a></td></tr>
<tr class="memdesc:ga69afbfd4e91125e3e52fcb409135ca7c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Prepend an element to a monadic structure.  <a href="group__group-_monad_plus.html#ga69afbfd4e91125e3e52fcb409135ca7c">More...</a><br /></td></tr>
<tr class="separator:ga69afbfd4e91125e3e52fcb409135ca7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga17fe9c1982c882807f3358b4138c5744"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_foldable.html#ga17fe9c1982c882807f3358b4138c5744">product</a> = see documentation</td></tr>
<tr class="memdesc:ga17fe9c1982c882807f3358b4138c5744"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the product of the numbers of a structure.  <a href="group__group-_foldable.html#ga17fe9c1982c882807f3358b4138c5744">More...</a><br /></td></tr>
<tr class="separator:ga17fe9c1982c882807f3358b4138c5744"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5ee54dc1195f9e5cf48bfd51ba231ae5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_monad_plus.html#ga5ee54dc1195f9e5cf48bfd51ba231ae5">mathrm</a></td></tr>
<tr class="memdesc:ga5ee54dc1195f9e5cf48bfd51ba231ae5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Remove all the elements of a monadic structure that are equal to some value.  <a href="group__group-_monad_plus.html#ga5ee54dc1195f9e5cf48bfd51ba231ae5">More...</a><br /></td></tr>
<tr class="separator:ga5ee54dc1195f9e5cf48bfd51ba231ae5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga80724ec8ecf319a1e695988a69e22f87"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga80724ec8ecf319a1e695988a69e22f87">remove_at</a></td></tr>
<tr class="memdesc:ga80724ec8ecf319a1e695988a69e22f87"><td class="mdescLeft">&#160;</td><td class="mdescRight">Remove the element at a given index from a sequence.  <a href="group__group-_sequence.html#ga80724ec8ecf319a1e695988a69e22f87">More...</a><br /></td></tr>
<tr class="separator:ga80724ec8ecf319a1e695988a69e22f87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae70b0815645c7d81bb636a1eed1a65c6"><td class="memTemplParams" colspan="2">template&lt;std::size_t n&gt; </td></tr>
<tr class="memitem:gae70b0815645c7d81bb636a1eed1a65c6"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gae70b0815645c7d81bb636a1eed1a65c6">remove_at_c</a></td></tr>
<tr class="memdesc:gae70b0815645c7d81bb636a1eed1a65c6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>remove_at</code>; provided for convenience.  <a href="group__group-_sequence.html#gae70b0815645c7d81bb636a1eed1a65c6">More...</a><br /></td></tr>
<tr class="separator:gae70b0815645c7d81bb636a1eed1a65c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6f6d5c1f335780c91d29626fde615c78"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga6f6d5c1f335780c91d29626fde615c78">remove_range</a></td></tr>
<tr class="memdesc:ga6f6d5c1f335780c91d29626fde615c78"><td class="mdescLeft">&#160;</td><td class="mdescRight">Remove the elements inside a given range of indices from a sequence.  <a href="group__group-_sequence.html#ga6f6d5c1f335780c91d29626fde615c78">More...</a><br /></td></tr>
<tr class="separator:ga6f6d5c1f335780c91d29626fde615c78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4696efcdee7d95ab4a391bb896a840b5"><td class="memTemplParams" colspan="2">template&lt;std::size_t from, std::size_t to&gt; </td></tr>
<tr class="memitem:ga4696efcdee7d95ab4a391bb896a840b5"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga4696efcdee7d95ab4a391bb896a840b5">remove_range_c</a></td></tr>
<tr class="memdesc:ga4696efcdee7d95ab4a391bb896a840b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>remove_range</code>; provided for convenience.  <a href="group__group-_sequence.html#ga4696efcdee7d95ab4a391bb896a840b5">More...</a><br /></td></tr>
<tr class="separator:ga4696efcdee7d95ab4a391bb896a840b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a405f3dd84fc6f5003e64f8da104a1b54"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespaceboost_1_1hana.html#a405f3dd84fc6f5003e64f8da104a1b54">repeat</a></td></tr>
<tr class="memdesc:a405f3dd84fc6f5003e64f8da104a1b54"><td class="mdescLeft">&#160;</td><td class="mdescRight">Invokes a nullary function <code>n</code> times.  <a href="namespaceboost_1_1hana.html#a405f3dd84fc6f5003e64f8da104a1b54">More...</a><br /></td></tr>
<tr class="separator:a405f3dd84fc6f5003e64f8da104a1b54"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad5f48c79d11923d6c1d70b18b7dd3f19"><td class="memTemplParams" colspan="2">template&lt;typename M &gt; </td></tr>
<tr class="memitem:gad5f48c79d11923d6c1d70b18b7dd3f19"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_monad_plus.html#gad5f48c79d11923d6c1d70b18b7dd3f19">replicate</a></td></tr>
<tr class="memdesc:gad5f48c79d11923d6c1d70b18b7dd3f19"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create a monadic structure by combining a lifted value with itself <code>n</code> times.  <a href="group__group-_monad_plus.html#gad5f48c79d11923d6c1d70b18b7dd3f19">More...</a><br /></td></tr>
<tr class="separator:gad5f48c79d11923d6c1d70b18b7dd3f19"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga28037560e8f224c53cf6ac168d03a067"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga28037560e8f224c53cf6ac168d03a067">reverse</a></td></tr>
<tr class="memdesc:ga28037560e8f224c53cf6ac168d03a067"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reverse a sequence.  <a href="group__group-_sequence.html#ga28037560e8f224c53cf6ac168d03a067">More...</a><br /></td></tr>
<tr class="separator:ga28037560e8f224c53cf6ac168d03a067"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec484fb349500149d90717f6e68f7bcd"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gaec484fb349500149d90717f6e68f7bcd">scan_left</a></td></tr>
<tr class="memdesc:gaec484fb349500149d90717f6e68f7bcd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Fold a Sequence to the left and return a list containing the successive reduction states.  <a href="group__group-_sequence.html#gaec484fb349500149d90717f6e68f7bcd">More...</a><br /></td></tr>
<tr class="separator:gaec484fb349500149d90717f6e68f7bcd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga54d141f901866dfab29b052857123bab"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga54d141f901866dfab29b052857123bab">scan_right</a></td></tr>
<tr class="memdesc:ga54d141f901866dfab29b052857123bab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Fold a Sequence to the right and return a list containing the successive reduction states.  <a href="group__group-_sequence.html#ga54d141f901866dfab29b052857123bab">More...</a><br /></td></tr>
<tr class="separator:ga54d141f901866dfab29b052857123bab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7bb979d59ffc3ab862cb7d9dc7730077"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_product.html#ga7bb979d59ffc3ab862cb7d9dc7730077">second</a></td></tr>
<tr class="memdesc:ga7bb979d59ffc3ab862cb7d9dc7730077"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the second element of a pair.  <a href="group__group-_product.html#ga7bb979d59ffc3ab862cb7d9dc7730077">More...</a><br /></td></tr>
<tr class="separator:ga7bb979d59ffc3ab862cb7d9dc7730077"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8ec3ac9a6f5014db943f61ebc9e1e36e"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_foldable.html#ga8ec3ac9a6f5014db943f61ebc9e1e36e">size</a> = <a class="el" href="group__group-_foldable.html#gaf0f8f717245620dc28cd7d7fa44d7475">hana::length</a></td></tr>
<tr class="memdesc:ga8ec3ac9a6f5014db943f61ebc9e1e36e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>length</code>; provided for consistency with the standard library.  <a href="group__group-_foldable.html#ga8ec3ac9a6f5014db943f61ebc9e1e36e">More...</a><br /></td></tr>
<tr class="separator:ga8ec3ac9a6f5014db943f61ebc9e1e36e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga245d8abaf6ba67e64020be51c8366081"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga245d8abaf6ba67e64020be51c8366081">slice</a></td></tr>
<tr class="memdesc:ga245d8abaf6ba67e64020be51c8366081"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract the elements of a <code>Sequence</code> at the given indices.  <a href="group__group-_sequence.html#ga245d8abaf6ba67e64020be51c8366081">More...</a><br /></td></tr>
<tr class="separator:ga245d8abaf6ba67e64020be51c8366081"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae1f6a2a9cb70564d43c6b3c663b25dd7"><td class="memTemplParams" colspan="2">template&lt;std::size_t from, std::size_t to&gt; </td></tr>
<tr class="memitem:gae1f6a2a9cb70564d43c6b3c663b25dd7"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gae1f6a2a9cb70564d43c6b3c663b25dd7">slice_c</a></td></tr>
<tr class="memdesc:gae1f6a2a9cb70564d43c6b3c663b25dd7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shorthand to <code>slice</code> a contiguous range of elements.  <a href="group__group-_sequence.html#gae1f6a2a9cb70564d43c6b3c663b25dd7">More...</a><br /></td></tr>
<tr class="separator:gae1f6a2a9cb70564d43c6b3c663b25dd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga61dab15f6ecf379121d4096fe0c8ab13"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_monad_plus.html#ga61dab15f6ecf379121d4096fe0c8ab13">suffix</a></td></tr>
<tr class="memdesc:ga61dab15f6ecf379121d4096fe0c8ab13"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inserts a value after each element of a monadic structure.  <a href="group__group-_monad_plus.html#ga61dab15f6ecf379121d4096fe0c8ab13">More...</a><br /></td></tr>
<tr class="separator:ga61dab15f6ecf379121d4096fe0c8ab13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga650def4b2e98f4273d8b9b7aa5a2fc28"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_foldable.html#ga650def4b2e98f4273d8b9b7aa5a2fc28">sum</a> = see documentation</td></tr>
<tr class="memdesc:ga650def4b2e98f4273d8b9b7aa5a2fc28"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the sum of the numbers of a structure.  <a href="group__group-_foldable.html#ga650def4b2e98f4273d8b9b7aa5a2fc28">More...</a><br /></td></tr>
<tr class="separator:ga650def4b2e98f4273d8b9b7aa5a2fc28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8d302de01b94b4b17f3bd81e09f42920"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga8d302de01b94b4b17f3bd81e09f42920">take_back</a></td></tr>
<tr class="memdesc:ga8d302de01b94b4b17f3bd81e09f42920"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the last <code>n</code> elements of a sequence, or the whole sequence if the sequence has less than <code>n</code> elements.  <a href="group__group-_sequence.html#ga8d302de01b94b4b17f3bd81e09f42920">More...</a><br /></td></tr>
<tr class="separator:ga8d302de01b94b4b17f3bd81e09f42920"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa4d4818952083e3b27c83b0ed645e322"><td class="memTemplParams" colspan="2">template&lt;std::size_t n&gt; </td></tr>
<tr class="memitem:gaa4d4818952083e3b27c83b0ed645e322"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gaa4d4818952083e3b27c83b0ed645e322">take_back_c</a></td></tr>
<tr class="memdesc:gaa4d4818952083e3b27c83b0ed645e322"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>take_back</code>; provided for convenience.  <a href="group__group-_sequence.html#gaa4d4818952083e3b27c83b0ed645e322">More...</a><br /></td></tr>
<tr class="separator:gaa4d4818952083e3b27c83b0ed645e322"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5112e6070d29b4f7fde3f44825da3316"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga5112e6070d29b4f7fde3f44825da3316">take_front</a></td></tr>
<tr class="memdesc:ga5112e6070d29b4f7fde3f44825da3316"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the first <code>n</code> elements of a sequence, or the whole sequence if the sequence has less than <code>n</code> elements.  <a href="group__group-_sequence.html#ga5112e6070d29b4f7fde3f44825da3316">More...</a><br /></td></tr>
<tr class="separator:ga5112e6070d29b4f7fde3f44825da3316"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3779f62fea92af00113a9290f1c680eb"><td class="memTemplParams" colspan="2">template&lt;std::size_t n&gt; </td></tr>
<tr class="memitem:ga3779f62fea92af00113a9290f1c680eb"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga3779f62fea92af00113a9290f1c680eb">take_front_c</a></td></tr>
<tr class="memdesc:ga3779f62fea92af00113a9290f1c680eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>take_front</code>; provided for convenience.  <a href="group__group-_sequence.html#ga3779f62fea92af00113a9290f1c680eb">More...</a><br /></td></tr>
<tr class="separator:ga3779f62fea92af00113a9290f1c680eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2d4db4ec5ec5bc16fe74f57de12697fd"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga2d4db4ec5ec5bc16fe74f57de12697fd">take_while</a></td></tr>
<tr class="memdesc:ga2d4db4ec5ec5bc16fe74f57de12697fd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Take elements from a sequence while the <code>predicate</code> is satisfied.  <a href="group__group-_sequence.html#ga2d4db4ec5ec5bc16fe74f57de12697fd">More...</a><br /></td></tr>
<tr class="separator:ga2d4db4ec5ec5bc16fe74f57de12697fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5e0735de01a24f681c55aedfeb6d13bf"><td class="memTemplParams" colspan="2">template&lt;typename M &gt; </td></tr>
<tr class="memitem:ga5e0735de01a24f681c55aedfeb6d13bf"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_monad.html#ga5e0735de01a24f681c55aedfeb6d13bf">tap</a></td></tr>
<tr class="memdesc:ga5e0735de01a24f681c55aedfeb6d13bf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tap inside a monadic chain.  <a href="group__group-_monad.html#ga5e0735de01a24f681c55aedfeb6d13bf">More...</a><br /></td></tr>
<tr class="separator:ga5e0735de01a24f681c55aedfeb6d13bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaddd3789de43cf989babb10cdc0b447a"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_monad.html#gaaddd3789de43cf989babb10cdc0b447a">then</a></td></tr>
<tr class="memdesc:gaaddd3789de43cf989babb10cdc0b447a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sequentially compose two monadic actions, discarding any value produced by the first but not its effects.  <a href="group__group-_monad.html#gaaddd3789de43cf989babb10cdc0b447a">More...</a><br /></td></tr>
<tr class="separator:gaaddd3789de43cf989babb10cdc0b447a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga246419f6c3263b648412f346106e6543"><td class="memTemplParams" colspan="2">template&lt;template&lt; typename ... &gt; class F&gt; </td></tr>
<tr class="memitem:ga246419f6c3263b648412f346106e6543"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_metafunction.html#ga246419f6c3263b648412f346106e6543">template_</a></td></tr>
<tr class="memdesc:ga246419f6c3263b648412f346106e6543"><td class="mdescLeft">&#160;</td><td class="mdescRight">Lift a template to a Metafunction.  <a href="group__group-_metafunction.html#ga246419f6c3263b648412f346106e6543">More...</a><br /></td></tr>
<tr class="separator:ga246419f6c3263b648412f346106e6543"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaa4f85cb8cbce21f5c04ef40ca35cc6a"><td class="memTemplParams" colspan="2">template&lt;template&lt; typename ... &gt; class F&gt; </td></tr>
<tr class="memitem:gaaa4f85cb8cbce21f5c04ef40ca35cc6a"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_metafunction.html#gaaa4f85cb8cbce21f5c04ef40ca35cc6a">metafunction</a></td></tr>
<tr class="memdesc:gaaa4f85cb8cbce21f5c04ef40ca35cc6a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Lift a MPL-style metafunction to a Metafunction.  <a href="group__group-_metafunction.html#gaaa4f85cb8cbce21f5c04ef40ca35cc6a">More...</a><br /></td></tr>
<tr class="separator:gaaa4f85cb8cbce21f5c04ef40ca35cc6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacec153d7f86aa7cf1efd813b3fd212b4"><td class="memTemplParams" colspan="2">template&lt;typename F &gt; </td></tr>
<tr class="memitem:gacec153d7f86aa7cf1efd813b3fd212b4"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_metafunction.html#gacec153d7f86aa7cf1efd813b3fd212b4">metafunction_class</a></td></tr>
<tr class="memdesc:gacec153d7f86aa7cf1efd813b3fd212b4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Lift a MPL-style metafunction class to a Metafunction.  <a href="group__group-_metafunction.html#gacec153d7f86aa7cf1efd813b3fd212b4">More...</a><br /></td></tr>
<tr class="separator:gacec153d7f86aa7cf1efd813b3fd212b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf7045fe6a627f88f5f646dad22d37aae"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_metafunction.html#gaf7045fe6a627f88f5f646dad22d37aae">integral</a></td></tr>
<tr class="memdesc:gaf7045fe6a627f88f5f646dad22d37aae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Turn a <code>Metafunction</code> into a function taking <code>type</code>s and returning a default-constructed object.  <a href="group__group-_metafunction.html#gaf7045fe6a627f88f5f646dad22d37aae">More...</a><br /></td></tr>
<tr class="separator:gaf7045fe6a627f88f5f646dad22d37aae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6d4093318f46472e62f9539a4dc998a9"><td class="memTemplParams" colspan="2">template&lt;template&lt; typename ... &gt; class F&gt; </td></tr>
<tr class="memitem:ga6d4093318f46472e62f9539a4dc998a9"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_metafunction.html#ga6d4093318f46472e62f9539a4dc998a9">trait</a> = <a class="el" href="group__group-_metafunction.html#gaf7045fe6a627f88f5f646dad22d37aae">hana::integral</a>(<a class="el" href="group__group-_metafunction.html#gaaa4f85cb8cbce21f5c04ef40ca35cc6a">hana::metafunction</a>&lt;F&gt;)</td></tr>
<tr class="memdesc:ga6d4093318f46472e62f9539a4dc998a9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Alias to <code>integral(metafunction&lt;F&gt;)</code>, provided for convenience.  <a href="group__group-_metafunction.html#ga6d4093318f46472e62f9539a4dc998a9">More...</a><br /></td></tr>
<tr class="separator:ga6d4093318f46472e62f9539a4dc998a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7b0c23944364ce61136e10b978ae2170"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_foldable.html#ga7b0c23944364ce61136e10b978ae2170">unpack</a></td></tr>
<tr class="memdesc:ga7b0c23944364ce61136e10b978ae2170"><td class="mdescLeft">&#160;</td><td class="mdescRight">Invoke a function with the elements of a Foldable as arguments.  <a href="group__group-_foldable.html#ga7b0c23944364ce61136e10b978ae2170">More...</a><br /></td></tr>
<tr class="separator:ga7b0c23944364ce61136e10b978ae2170"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1687520692a6b0c49e3a69de2980f388"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga1687520692a6b0c49e3a69de2980f388"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_constant.html#ga1687520692a6b0c49e3a69de2980f388">value</a></td></tr>
<tr class="memdesc:ga1687520692a6b0c49e3a69de2980f388"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the compile-time value associated to a constant.  <a href="group__group-_constant.html#ga1687520692a6b0c49e3a69de2980f388">More...</a><br /></td></tr>
<tr class="separator:ga1687520692a6b0c49e3a69de2980f388"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab46a092deeb205f2c92c335d4312a991"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_constant.html#gab46a092deeb205f2c92c335d4312a991">value_of</a></td></tr>
<tr class="memdesc:gab46a092deeb205f2c92c335d4312a991"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equivalent to <code>value</code>, but can be passed to higher-order algorithms.  <a href="group__group-_constant.html#gab46a092deeb205f2c92c335d4312a991">More...</a><br /></td></tr>
<tr class="separator:gab46a092deeb205f2c92c335d4312a991"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga08a767b86c330cac67daa891406d2730"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_logical.html#ga08a767b86c330cac67daa891406d2730">while_</a></td></tr>
<tr class="memdesc:ga08a767b86c330cac67daa891406d2730"><td class="mdescLeft">&#160;</td><td class="mdescRight">Apply a function to an initial state while some predicate is satisfied.  <a href="group__group-_logical.html#ga08a767b86c330cac67daa891406d2730">More...</a><br /></td></tr>
<tr class="separator:ga08a767b86c330cac67daa891406d2730"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad459ac17b6bab8ead1cae7de0032f3c6"><td class="memTemplParams" colspan="2">template&lt;typename M &gt; </td></tr>
<tr class="memitem:gad459ac17b6bab8ead1cae7de0032f3c6"><td class="memTemplItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="group__group-_monoid.html#gad459ac17b6bab8ead1cae7de0032f3c6">zero</a></td></tr>
<tr class="memdesc:gad459ac17b6bab8ead1cae7de0032f3c6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Identity of <code>plus</code>.  <a href="group__group-_monoid.html#gad459ac17b6bab8ead1cae7de0032f3c6">More...</a><br /></td></tr>
<tr class="separator:gad459ac17b6bab8ead1cae7de0032f3c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa5a378d4e71a91e0d6cd3959d9818e8a"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gaa5a378d4e71a91e0d6cd3959d9818e8a">zip</a></td></tr>
<tr class="memdesc:gaa5a378d4e71a91e0d6cd3959d9818e8a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Zip one sequence or more.  <a href="group__group-_sequence.html#gaa5a378d4e71a91e0d6cd3959d9818e8a">More...</a><br /></td></tr>
<tr class="separator:gaa5a378d4e71a91e0d6cd3959d9818e8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade78593b3ff51fc5479e1da97142fef5"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gade78593b3ff51fc5479e1da97142fef5">zip_shortest</a></td></tr>
<tr class="memdesc:gade78593b3ff51fc5479e1da97142fef5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Zip one sequence or more.  <a href="group__group-_sequence.html#gade78593b3ff51fc5479e1da97142fef5">More...</a><br /></td></tr>
<tr class="separator:gade78593b3ff51fc5479e1da97142fef5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae7a51104a77db79a0407d7d67b034667"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#gae7a51104a77db79a0407d7d67b034667">zip_shortest_with</a></td></tr>
<tr class="memdesc:gae7a51104a77db79a0407d7d67b034667"><td class="mdescLeft">&#160;</td><td class="mdescRight">Zip one sequence or more with a given function.  <a href="group__group-_sequence.html#gae7a51104a77db79a0407d7d67b034667">More...</a><br /></td></tr>
<tr class="separator:gae7a51104a77db79a0407d7d67b034667"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6a4bf8549ce69b5b5b7377aec225a0e3"><td class="memItemLeft" align="right" valign="top">constexpr auto&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__group-_sequence.html#ga6a4bf8549ce69b5b5b7377aec225a0e3">zip_with</a></td></tr>
<tr class="memdesc:ga6a4bf8549ce69b5b5b7377aec225a0e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Zip one sequence or more with a given function.  <a href="group__group-_sequence.html#ga6a4bf8549ce69b5b5b7377aec225a0e3">More...</a><br /></td></tr>
<tr class="separator:ga6a4bf8549ce69b5b5b7377aec225a0e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Namespace containing everything in the library. </p>
</div><h2 class="groupheader">Variable Documentation</h2>
<a id="a405f3dd84fc6f5003e64f8da104a1b54"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a405f3dd84fc6f5003e64f8da104a1b54">&#9670;&nbsp;</a></span>repeat</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">constexpr auto boost::hana::repeat</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= [](<span class="keyword">auto</span> <span class="keyword">const</span>&amp; n, <span class="keyword">auto</span>&amp;&amp; f) -&gt; <span class="keywordtype">void</span> {</div>
<div class="line">        f(); f(); ... f(); </div>
<div class="line">    }</div>
</div><!-- fragment -->
<p>Invokes a nullary function <code>n</code> times. </p>
<p>Given an <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> <code>n</code> and a nullary function <code>f</code>, <code>repeat(n, f)</code> will call <code>f</code> <code>n</code> times. In particular, any decent compiler should expand <code>repeat(n, f)</code> to </p><div class="fragment"><div class="line">f(); f(); ... f(); <span class="comment">// n times total</span></div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">n</td><td>An <code><a class="el" href="structboost_1_1hana_1_1_integral_constant.html" title="The IntegralConstant concept represents compile-time integral values.">IntegralConstant</a></code> holding a non-negative value representing the number of times <code>f</code> should be repeatedly invoked.</td></tr>
    <tr><td class="paramname">f</td><td>A function to repeatedly invoke <code>n</code> times. <code>f</code> is allowed to have side effects.</td></tr>
  </table>
  </dd>
</dl>
<h2><a class="anchor" id="autotoc_md334"></a>
Example</h2>
<div class="fragment"><div class="line"><span class="comment">// Copyright Louis Dionne 2013-2017</span></div>
<div class="line"><span class="comment">// Distributed under the Boost Software License, Version 1.0.</span></div>
<div class="line"><span class="comment">// (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="assert_8hpp.html">boost/hana/assert.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="integral__constant_8hpp.html">boost/hana/integral_constant.hpp</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="repeat_8hpp.html">boost/hana/repeat.hpp</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;string&gt;</span></div>
<div class="line"><span class="keyword">namespace </span>hana = <a class="code" href="namespaceboost_1_1hana.html">boost::hana</a>;</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main() {</div>
<div class="line">    std::string s;</div>
<div class="line">    <span class="keywordflow">for</span> (<span class="keywordtype">char</span> letter = <span class="charliteral">&#39;a&#39;</span>; letter &lt;= <span class="charliteral">&#39;g&#39;</span>; ++letter)</div>
<div class="line">        <a class="code" href="namespaceboost_1_1hana.html#a405f3dd84fc6f5003e64f8da104a1b54">hana::repeat</a>(hana::int_c&lt;3&gt;, [&amp;] { s += letter; });</div>
<div class="line"> </div>
<div class="line">    <a class="code" href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a>(s == <span class="stringliteral">&quot;aaabbbcccdddeeefffggg&quot;</span>);</div>
<div class="line">}</div>
<div class="ttc" id="aassert_8hpp_html"><div class="ttname"><a href="assert_8hpp.html">assert.hpp</a></div><div class="ttdoc">Defines macros to perform different kinds of assertions.</div></div>
<div class="ttc" id="agroup__group-assertions_html_ga4796ae107d58b67e0bbccd5ae6f70101"><div class="ttname"><a href="group__group-assertions.html#ga4796ae107d58b67e0bbccd5ae6f70101">BOOST_HANA_RUNTIME_CHECK</a></div><div class="ttdeci">#define BOOST_HANA_RUNTIME_CHECK(...)</div><div class="ttdoc">Equivalent to BOOST_HANA_RUNTIME_ASSERT, but not influenced by the BOOST_HANA_CONFIG_DISABLE_ASSERTIO...</div><div class="ttdef"><b>Definition:</b> assert.hpp:209</div></div>
<div class="ttc" id="aintegral__constant_8hpp_html"><div class="ttname"><a href="integral__constant_8hpp.html">integral_constant.hpp</a></div><div class="ttdoc">Defines boost::hana::integral_constant.</div></div>
<div class="ttc" id="anamespaceboost_1_1hana_html"><div class="ttname"><a href="namespaceboost_1_1hana.html">boost::hana</a></div><div class="ttdoc">Namespace containing everything in the library.</div><div class="ttdef"><b>Definition:</b> accessors.hpp:20</div></div>
<div class="ttc" id="anamespaceboost_1_1hana_html_a405f3dd84fc6f5003e64f8da104a1b54"><div class="ttname"><a href="namespaceboost_1_1hana.html#a405f3dd84fc6f5003e64f8da104a1b54">boost::hana::repeat</a></div><div class="ttdeci">constexpr auto repeat</div><div class="ttdoc">Invokes a nullary function n times.</div><div class="ttdef"><b>Definition:</b> repeat.hpp:42</div></div>
<div class="ttc" id="arepeat_8hpp_html"><div class="ttname"><a href="repeat_8hpp.html">repeat.hpp</a></div><div class="ttdoc">Defines boost::hana::repeat.</div></div>
</div><!-- fragment --> 
</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!--
Copyright Louis Dionne 2013-2017
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)
-->
<!-- boost-no-inspect -->
<!-- HTML footer for doxygen *******-->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><b>boost</b></li><li class="navelem"><a class="el" href="namespaceboost_1_1hana.html">hana</a></li>
  </ul>
</div>
</body>
</html>
