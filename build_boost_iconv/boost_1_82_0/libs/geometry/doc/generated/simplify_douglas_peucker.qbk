[/============================================================================
  Boost.Geometry (aka GGL, Generic Geometry Library)

  Copyright (c) 2007-2013 Barend <PERSON>, Amsterdam, the Netherlands.
  Copyright (c) 2008-2013 <PERSON>, Paris, France.
  Copyright (c) 2009-2013 Mateusz <PERSON>kot, London, UK.

  Use, modification and distribution is subject to the Boost Software License,
  Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
  http://www.boost.org/LICENSE_1_0.txt)
=============================================================================/]


[/ Generated by doxygen_xml2qbk 1.1.1, don't change, will be overwritten automatically]
[/ Generated from doxy/doxygen_output/xml/classboost_1_1geometry_1_1strategy_1_1simplify_1_1do<PERSON>__peucker.xml]
[section:strategy_simplify_douglas_peucker strategy::simplify::douglas_peucker]

'''<indexterm><primary>strategy</primary></indexterm><indexterm><primary>simplify</primary></indexterm><indexterm><primary>douglas_peucker</primary></indexterm>'''
Implements the simplify algorithm. 

[heading Description]
The douglas\u005fpeucker strategy simplifies a linestring, ring or vector of points using the well-known Douglas-Peucker algorithm. 

[heading Synopsis]
``template<typename Point, typename PointDistanceStrategy>
class strategy::simplify::douglas_peucker
{
  // ...
};
``

[heading Template parameter(s)]
[table
[[Parameter] [Description]]
[[typename Point] [the point type ]]
[[typename PointDistanceStrategy] [point-segment distance strategy to be used ]]
]

[heading Member Function(s)]
[table
[[Function] [Description] [Parameters]  [Returns]]
[[``template<typename Range, typename OutputIterator>
OutputIterator apply(Range const & , OutputIterator out, distance_type const & )``

] [] [[* Range const &]: [']:  

[* OutputIterator]: ['out]:  

[* distance_type const &]: [']:  



][

]
]
]

[heading Header]
`#include <boost/geometry/strategies/agnostic/simplify_douglas_peucker.hpp>`

[endsect]

