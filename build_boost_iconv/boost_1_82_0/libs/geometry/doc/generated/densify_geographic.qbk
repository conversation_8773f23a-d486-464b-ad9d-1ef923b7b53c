[/============================================================================
  Boost.Geometry (aka GGL, Generic Geometry Library)

  Copyright (c) 2007-2013 Barend <PERSON>, Amsterdam, the Netherlands.
  Copyright (c) 2008-2013 <PERSON>, Paris, France.
  Copyright (c) 2009-2013 Mateusz <PERSON>kot, London, UK.

  Use, modification and distribution is subject to the Boost Software License,
  Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
  http://www.boost.org/LICENSE_1_0.txt)
=============================================================================/]


[/ Generated by doxygen_xml2qbk 1.1.1, don't change, will be overwritten automatically]
[/ Generated from doxy/doxygen_output/xml/classboost_1_1geometry_1_1strategy_1_1densify_1_1geographic.xml]
[section:strategy_densify_geographic strategy::densify::geographic]

'''<indexterm><primary>strategy</primary></indexterm><indexterm><primary>densify</primary></indexterm><indexterm><primary>geographic</primary></indexterm>'''
Densification of geographic segment. 

[heading Synopsis]
``template<typename FormulaPolicy, typename Spheroid, typename CalculationType>
class strategy::densify::geographic
{
  // ...
};
``

[heading Template parameter(s)]
[table
[[Parameter] [Default] [Description]]
[[typename FormulaPolicy] [strategy::andoyer] [The geodesic formulas used internally. ]]
[[typename Spheroid] [srs::spheroid<double>] [The spheroid model. ]]
[[typename CalculationType] [void] [numeric type for calculation (e.g. high precision); if [*void] then it is extracted automatically from the coordinate type and (if necessary) promoted to floating point]]
]

[heading Constructor(s)]
[table
[[Function] [Description] [Parameters] ]
[[``geographic()``

] [] [

]]
[[``geographic(Spheroid const & spheroid)``

] [] [[* Spheroid const &]: ['spheroid]:  



]]
]

[heading Member Function(s)]
[table
[[Function] [Description] [Parameters]  [Returns]]
[[``template<typename Point, typename AssignPolicy, typename T>
void apply(Point const & p0, Point const & p1, AssignPolicy & policy,
           T const & length_threshold)``

] [] [[* Point const &]: ['p0]:  

[* Point const &]: ['p1]:  

[* AssignPolicy &]: ['policy]:  

[* T const &]: ['length_threshold]:  



][

]
]
[[``Spheroid const  & model()``

] [] [

][

]
]
]

[heading Header]
`#include <boost/geometry/strategies/geographic/densify.hpp>`

[heading See also]
* [link geometry.reference.algorithms.densify.densify_4_with_strategy densify (with strategy)]
* [link geometry.reference.srs.srs_spheroid srs::spheroid]

[endsect]

