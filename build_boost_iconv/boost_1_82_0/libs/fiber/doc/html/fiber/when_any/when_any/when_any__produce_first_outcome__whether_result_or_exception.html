<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>when_any, produce first outcome, whether result or exception</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Fiber">
<link rel="up" href="../when_any.html" title="when_any">
<link rel="prev" href="when_any__return_value.html" title="when_any, return value">
<link rel="next" href="when_any__produce_first_success.html" title="when_any, produce first success">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="when_any__return_value.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../when_any.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="when_any__produce_first_success.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="fiber.when_any.when_any.when_any__produce_first_outcome__whether_result_or_exception"></a><a class="link" href="when_any__produce_first_outcome__whether_result_or_exception.html" title="when_any, produce first outcome, whether result or exception">when_any,
        produce first outcome, whether result or exception</a>
</h4></div></div></div>
<p>
          We may not be running in an environment in which we can guarantee no exception
          will be thrown by any of our task functions. In that case, the above implementations
          of <code class="computeroutput"><span class="identifier">wait_first_something</span><span class="special">()</span></code> would be naïve: as mentioned in <a class="link" href="../../fiber_mgmt.html#exceptions">the section on Fiber Management</a>, an uncaught
          exception in one of our task fibers would cause <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">terminate</span><span class="special">()</span></code> to be called.
        </p>
<p>
          Let's at least ensure that such an exception would propagate to the fiber
          awaiting the first result. We can use <a class="link" href="../../synchronization/futures/future.html#class_future"><code class="computeroutput">future&lt;&gt;</code></a> to transport
          either a return value or an exception. Therefore, we will change <a class="link" href="when_any__return_value.html#wait_first_value"><code class="computeroutput"><span class="identifier">wait_first_value</span><span class="special">()</span></code></a>'s <a class="link" href="../../synchronization/channels/buffered_channel.html#class_buffered_channel"><code class="computeroutput">buffered_channel&lt;&gt;</code></a> to
          hold <code class="computeroutput"><span class="identifier">future</span><span class="special">&lt;</span>
          <span class="identifier">T</span> <span class="special">&gt;</span></code>
          items instead of simply <code class="computeroutput"><span class="identifier">T</span></code>.
        </p>
<p>
          Once we have a <code class="computeroutput"><span class="identifier">future</span><span class="special">&lt;&gt;</span></code>
          in hand, all we need do is call <a class="link" href="../../synchronization/futures/future.html#future_get"><code class="computeroutput">future::get()</code></a>, which will either
          return the value or rethrow the exception.
        </p>
<p>
          <a name="wait_first_outcome"></a>
</p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">Fn</span><span class="special">,</span> <span class="keyword">typename</span> <span class="special">...</span> <span class="identifier">Fns</span> <span class="special">&gt;</span>
<span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">result_of</span><span class="special">&lt;</span> <span class="identifier">Fn</span><span class="special">()</span> <span class="special">&gt;::</span><span class="identifier">type</span>
<span class="identifier">wait_first_outcome</span><span class="special">(</span> <span class="identifier">Fn</span> <span class="special">&amp;&amp;</span> <span class="identifier">function</span><span class="special">,</span> <span class="identifier">Fns</span> <span class="special">&amp;&amp;</span> <span class="special">...</span> <span class="identifier">functions</span><span class="special">)</span> <span class="special">{</span>
    <span class="comment">// In this case, the value we pass through the channel is actually a</span>
    <span class="comment">// future -- which is already ready. future can carry either a value or an</span>
    <span class="comment">// exception.</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">result_of</span><span class="special">&lt;</span> <span class="identifier">Fn</span><span class="special">()</span> <span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">return_t</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">fibers</span><span class="special">::</span><span class="identifier">future</span><span class="special">&lt;</span> <span class="identifier">return_t</span> <span class="special">&gt;</span> <span class="identifier">future_t</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">fibers</span><span class="special">::</span><span class="identifier">buffered_channel</span><span class="special">&lt;</span> <span class="identifier">future_t</span> <span class="special">&gt;</span> <span class="identifier">channel_t</span><span class="special">;</span>
    <span class="keyword">auto</span> <span class="identifier">chanp</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">make_shared</span><span class="special">&lt;</span> <span class="identifier">channel_t</span> <span class="special">&gt;(</span> <span class="number">64</span><span class="special">)</span> <span class="special">);</span>
    <span class="comment">// launch all the relevant fibers</span>
    <span class="identifier">wait_first_outcome_impl</span><span class="special">&lt;</span> <span class="identifier">return_t</span> <span class="special">&gt;(</span> <span class="identifier">chanp</span><span class="special">,</span>
                                         <span class="identifier">std</span><span class="special">::</span><span class="identifier">forward</span><span class="special">&lt;</span> <span class="identifier">Fn</span> <span class="special">&gt;(</span> <span class="identifier">function</span><span class="special">),</span>
                                         <span class="identifier">std</span><span class="special">::</span><span class="identifier">forward</span><span class="special">&lt;</span> <span class="identifier">Fns</span> <span class="special">&gt;(</span> <span class="identifier">functions</span><span class="special">)</span> <span class="special">...</span> <span class="special">);</span>
    <span class="comment">// retrieve the first future</span>
    <span class="identifier">future_t</span> <span class="identifier">future</span><span class="special">(</span> <span class="identifier">chanp</span><span class="special">-&gt;</span><span class="identifier">value_pop</span><span class="special">()</span> <span class="special">);</span>
    <span class="comment">// close the channel: no subsequent push() has to succeed</span>
    <span class="identifier">chanp</span><span class="special">-&gt;</span><span class="identifier">close</span><span class="special">();</span>
    <span class="comment">// either return value or throw exception</span>
    <span class="keyword">return</span> <span class="identifier">future</span><span class="special">.</span><span class="identifier">get</span><span class="special">();</span>
<span class="special">}</span>
</pre>
<p>
        </p>
<p>
          So far so good — but there's a timing issue. How should we obtain the <code class="computeroutput"><span class="identifier">future</span><span class="special">&lt;&gt;</span></code>
          to <a class="link" href="../../synchronization/channels/buffered_channel.html#buffered_channel_push"><code class="computeroutput">buffered_channel::push()</code></a> on the queue?
        </p>
<p>
          We could call <a class="link" href="../../synchronization/futures/future.html#fibers_async"><code class="computeroutput">fibers::async()</code></a>. That would certainly produce
          a <code class="computeroutput"><span class="identifier">future</span><span class="special">&lt;&gt;</span></code>
          for the task function. The trouble is that it would return too quickly!
          We only want <code class="computeroutput"><span class="identifier">future</span><span class="special">&lt;&gt;</span></code>
          items for <span class="emphasis"><em>completed</em></span> tasks on our <code class="computeroutput"><span class="identifier">queue</span><span class="special">&lt;&gt;</span></code>. In fact, we only want the <code class="computeroutput"><span class="identifier">future</span><span class="special">&lt;&gt;</span></code>
          for the one that completes first. If each fiber launched by <code class="computeroutput"><span class="identifier">wait_first_outcome</span><span class="special">()</span></code>
          were to <code class="computeroutput"><span class="identifier">push</span><span class="special">()</span></code>
          the result of calling <code class="computeroutput"><span class="identifier">async</span><span class="special">()</span></code>, the queue would only ever report the
          result of the leftmost task item — <span class="emphasis"><em>not</em></span> the one that
          completes most quickly.
        </p>
<p>
          Calling <a class="link" href="../../synchronization/futures/future.html#future_get"><code class="computeroutput">future::get()</code></a> on the future returned by <code class="computeroutput"><span class="identifier">async</span><span class="special">()</span></code>
          wouldn't be right. You can only call <code class="computeroutput"><span class="identifier">get</span><span class="special">()</span></code> once per <code class="computeroutput"><span class="identifier">future</span><span class="special">&lt;&gt;</span></code> instance! And if there were an
          exception, it would be rethrown inside the helper fiber at the producer
          end of the queue, rather than propagated to the consumer end.
        </p>
<p>
          We could call <a class="link" href="../../synchronization/futures/future.html#future_wait"><code class="computeroutput">future::wait()</code></a>. That would block the helper fiber
          until the <code class="computeroutput"><span class="identifier">future</span><span class="special">&lt;&gt;</span></code>
          became ready, at which point we could <code class="computeroutput"><span class="identifier">push</span><span class="special">()</span></code> it to be retrieved by <code class="computeroutput"><span class="identifier">wait_first_outcome</span><span class="special">()</span></code>.
        </p>
<p>
          That would work — but there's a simpler tactic that avoids creating an extra
          fiber. We can wrap the task function in a <a class="link" href="../../synchronization/futures/packaged_task.html#class_packaged_task"><code class="computeroutput">packaged_task&lt;&gt;</code></a>.
          While one naturally thinks of passing a <code class="computeroutput"><span class="identifier">packaged_task</span><span class="special">&lt;&gt;</span></code> to a new fiber — that is, in fact,
          what <code class="computeroutput"><span class="identifier">async</span><span class="special">()</span></code>
          does — in this case, we're already running in the helper fiber at the producer
          end of the queue! We can simply <span class="emphasis"><em>call</em></span> the <code class="computeroutput"><span class="identifier">packaged_task</span><span class="special">&lt;&gt;</span></code>.
          On return from that call, the task function has completed, meaning that
          the <code class="computeroutput"><span class="identifier">future</span><span class="special">&lt;&gt;</span></code>
          obtained from the <code class="computeroutput"><span class="identifier">packaged_task</span><span class="special">&lt;&gt;</span></code> is certain to be ready. At that
          point we can simply <code class="computeroutput"><span class="identifier">push</span><span class="special">()</span></code> it to the queue.
        </p>
<p>
          <a name="wait_first_outcome_impl"></a>
</p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">CHANP</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">Fn</span> <span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">wait_first_outcome_impl</span><span class="special">(</span> <span class="identifier">CHANP</span> <span class="identifier">chan</span><span class="special">,</span> <span class="identifier">Fn</span> <span class="special">&amp;&amp;</span> <span class="identifier">function</span><span class="special">)</span> <span class="special">{</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">fibers</span><span class="special">::</span><span class="identifier">fiber</span><span class="special">(</span>
        <span class="comment">// Use std::bind() here for C++11 compatibility. C++11 lambda capture</span>
        <span class="comment">// can't move a move-only Fn type, but bind() can. Let bind() move the</span>
        <span class="comment">// channel pointer and the function into the bound object, passing</span>
        <span class="comment">// references into the lambda.</span>
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">bind</span><span class="special">(</span>
            <span class="special">[](</span> <span class="identifier">CHANP</span> <span class="special">&amp;</span> <span class="identifier">chan</span><span class="special">,</span>
                <span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">decay</span><span class="special">&lt;</span> <span class="identifier">Fn</span> <span class="special">&gt;::</span><span class="identifier">type</span> <span class="special">&amp;</span> <span class="identifier">function</span><span class="special">)</span> <span class="special">{</span>
                <span class="comment">// Instantiate a packaged_task to capture any exception thrown by</span>
                <span class="comment">// function.</span>
                <span class="identifier">boost</span><span class="special">::</span><span class="identifier">fibers</span><span class="special">::</span><span class="identifier">packaged_task</span><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">()</span> <span class="special">&gt;</span> <span class="identifier">task</span><span class="special">(</span> <span class="identifier">function</span><span class="special">);</span>
                <span class="comment">// Immediately run this packaged_task on same fiber. We want</span>
                <span class="comment">// function() to have completed BEFORE we push the future.</span>
                <span class="identifier">task</span><span class="special">();</span>
                <span class="comment">// Pass the corresponding future to consumer. Ignore</span>
                <span class="comment">// channel_op_status returned by push(): might be closed; we</span>
                <span class="comment">// simply don't care.</span>
                <span class="identifier">chan</span><span class="special">-&gt;</span><span class="identifier">push</span><span class="special">(</span> <span class="identifier">task</span><span class="special">.</span><span class="identifier">get_future</span><span class="special">()</span> <span class="special">);</span>
            <span class="special">},</span>
            <span class="identifier">chan</span><span class="special">,</span>
            <span class="identifier">std</span><span class="special">::</span><span class="identifier">forward</span><span class="special">&lt;</span> <span class="identifier">Fn</span> <span class="special">&gt;(</span> <span class="identifier">function</span><span class="special">)</span>
        <span class="special">)).</span><span class="identifier">detach</span><span class="special">();</span>
<span class="special">}</span>
</pre>
<p>
        </p>
<p>
          Calling it might look like this:
        </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="identifier">result</span> <span class="special">=</span> <span class="identifier">wait_first_outcome</span><span class="special">(</span>
        <span class="special">[](){</span> <span class="keyword">return</span> <span class="identifier">sleeper</span><span class="special">(</span><span class="string">"wfos_first"</span><span class="special">,</span>   <span class="number">50</span><span class="special">);</span> <span class="special">},</span>
        <span class="special">[](){</span> <span class="keyword">return</span> <span class="identifier">sleeper</span><span class="special">(</span><span class="string">"wfos_second"</span><span class="special">,</span> <span class="number">100</span><span class="special">);</span> <span class="special">},</span>
        <span class="special">[](){</span> <span class="keyword">return</span> <span class="identifier">sleeper</span><span class="special">(</span><span class="string">"wfos_third"</span><span class="special">,</span>  <span class="number">150</span><span class="special">);</span> <span class="special">});</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"wait_first_outcome(success) =&gt; "</span> <span class="special">&lt;&lt;</span> <span class="identifier">result</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="identifier">assert</span><span class="special">(</span><span class="identifier">result</span> <span class="special">==</span> <span class="string">"wfos_first"</span><span class="special">);</span>

<span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="identifier">thrown</span><span class="special">;</span>
<span class="keyword">try</span> <span class="special">{</span>
    <span class="identifier">result</span> <span class="special">=</span> <span class="identifier">wait_first_outcome</span><span class="special">(</span>
            <span class="special">[](){</span> <span class="keyword">return</span> <span class="identifier">sleeper</span><span class="special">(</span><span class="string">"wfof_first"</span><span class="special">,</span>   <span class="number">50</span><span class="special">,</span> <span class="keyword">true</span><span class="special">);</span> <span class="special">},</span>
            <span class="special">[](){</span> <span class="keyword">return</span> <span class="identifier">sleeper</span><span class="special">(</span><span class="string">"wfof_second"</span><span class="special">,</span> <span class="number">100</span><span class="special">);</span> <span class="special">},</span>
            <span class="special">[](){</span> <span class="keyword">return</span> <span class="identifier">sleeper</span><span class="special">(</span><span class="string">"wfof_third"</span><span class="special">,</span>  <span class="number">150</span><span class="special">);</span> <span class="special">});</span>
<span class="special">}</span> <span class="keyword">catch</span> <span class="special">(</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">exception</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">e</span><span class="special">)</span> <span class="special">{</span>
    <span class="identifier">thrown</span> <span class="special">=</span> <span class="identifier">e</span><span class="special">.</span><span class="identifier">what</span><span class="special">();</span>
<span class="special">}</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"wait_first_outcome(fail) threw '"</span> <span class="special">&lt;&lt;</span> <span class="identifier">thrown</span>
          <span class="special">&lt;&lt;</span> <span class="string">"'"</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
<span class="identifier">assert</span><span class="special">(</span><span class="identifier">thrown</span> <span class="special">==</span> <span class="string">"wfof_first"</span><span class="special">);</span>
</pre>
<p>
        </p>
</div>
<div class="copyright-footer">Copyright © 2013 Oliver Kowalke<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="when_any__return_value.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../when_any.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="when_any__produce_first_success.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
