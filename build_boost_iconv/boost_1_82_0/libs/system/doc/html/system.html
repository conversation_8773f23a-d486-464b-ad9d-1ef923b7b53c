<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="generator" content="Asciidoctor 2.0.16">
<meta name="author" content="<PERSON><PERSON>, <PERSON>, <PERSON>">
<title>Boost.System: Extensible Error Reporting</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700">
<style>
/*! Asciidoctor default stylesheet | MIT License | https://asciidoctor.org */
/* Uncomment the following line when using as a custom stylesheet */
/* @import "https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"; */
html{font-family:sans-serif;-webkit-text-size-adjust:100%}
a{background:none}
a:focus{outline:thin dotted}
a:active,a:hover{outline:0}
h1{font-size:2em;margin:.67em 0}
b,strong{font-weight:bold}
abbr{font-size:.9em}
abbr[title]{cursor:help;border-bottom:1px dotted #dddddf;text-decoration:none}
dfn{font-style:italic}
hr{height:0}
mark{background:#ff0;color:#000}
code,kbd,pre,samp{font-family:monospace;font-size:1em}
pre{white-space:pre-wrap}
q{quotes:"\201C" "\201D" "\2018" "\2019"}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
img{border:0}
svg:not(:root){overflow:hidden}
figure{margin:0}
audio,video{display:inline-block}
audio:not([controls]){display:none;height:0}
fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}
legend{border:0;padding:0}
button,input,select,textarea{font-family:inherit;font-size:100%;margin:0}
button,input{line-height:normal}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
input[type=checkbox],input[type=radio]{padding:0}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
textarea{overflow:auto;vertical-align:top}
table{border-collapse:collapse;border-spacing:0}
*,::before,::after{box-sizing:border-box}
html,body{font-size:100%}
body{background:#fff;color:rgba(0,0,0,.8);padding:0;margin:0;font-family:"Noto Serif","DejaVu Serif",serif;line-height:1;position:relative;cursor:auto;-moz-tab-size:4;-o-tab-size:4;tab-size:4;word-wrap:anywhere;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}
a:hover{cursor:pointer}
img,object,embed{max-width:100%;height:auto}
object,embed{height:100%}
img{-ms-interpolation-mode:bicubic}
.left{float:left!important}
.right{float:right!important}
.text-left{text-align:left!important}
.text-right{text-align:right!important}
.text-center{text-align:center!important}
.text-justify{text-align:justify!important}
.hide{display:none}
img,object,svg{display:inline-block;vertical-align:middle}
textarea{height:auto;min-height:50px}
select{width:100%}
.subheader,.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{line-height:1.45;color:#7a2518;font-weight:400;margin-top:0;margin-bottom:.25em}
div,dl,dt,dd,ul,ol,li,h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6,pre,form,p,blockquote,th,td{margin:0;padding:0}
a{color:#2156a5;text-decoration:underline;line-height:inherit}
a:hover,a:focus{color:#1d4b8f}
a img{border:0}
p{line-height:1.6;margin-bottom:1.25em;text-rendering:optimizeLegibility}
p aside{font-size:.875em;line-height:1.35;font-style:italic}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{font-family:"Open Sans","DejaVu Sans",sans-serif;font-weight:300;font-style:normal;color:#ba3925;text-rendering:optimizeLegibility;margin-top:1em;margin-bottom:.5em;line-height:1.0125em}
h1 small,h2 small,h3 small,#toctitle small,.sidebarblock>.content>.title small,h4 small,h5 small,h6 small{font-size:60%;color:#e99b8f;line-height:0}
h1{font-size:2.125em}
h2{font-size:1.6875em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.375em}
h4,h5{font-size:1.125em}
h6{font-size:1em}
hr{border:solid #dddddf;border-width:1px 0 0;clear:both;margin:1.25em 0 1.1875em}
em,i{font-style:italic;line-height:inherit}
strong,b{font-weight:bold;line-height:inherit}
small{font-size:60%;line-height:inherit}
code{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;font-weight:400;color:rgba(0,0,0,.9)}
ul,ol,dl{line-height:1.6;margin-bottom:1.25em;list-style-position:outside;font-family:inherit}
ul,ol{margin-left:1.5em}
ul li ul,ul li ol{margin-left:1.25em;margin-bottom:0}
ul.square li ul,ul.circle li ul,ul.disc li ul{list-style:inherit}
ul.square{list-style-type:square}
ul.circle{list-style-type:circle}
ul.disc{list-style-type:disc}
ol li ul,ol li ol{margin-left:1.25em;margin-bottom:0}
dl dt{margin-bottom:.3125em;font-weight:bold}
dl dd{margin-bottom:1.25em}
blockquote{margin:0 0 1.25em;padding:.5625em 1.25em 0 1.1875em;border-left:1px solid #ddd}
blockquote,blockquote p{line-height:1.6;color:rgba(0,0,0,.85)}
@media screen and (min-width:768px){h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2}
h1{font-size:2.75em}
h2{font-size:2.3125em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.6875em}
h4{font-size:1.4375em}}
table{background:#fff;margin-bottom:1.25em;border:1px solid #dedede;word-wrap:normal}
table thead,table tfoot{background:#f7f8f7}
table thead tr th,table thead tr td,table tfoot tr th,table tfoot tr td{padding:.5em .625em .625em;font-size:inherit;color:rgba(0,0,0,.8);text-align:left}
table tr th,table tr td{padding:.5625em .625em;font-size:inherit;color:rgba(0,0,0,.8)}
table tr.even,table tr.alt{background:#f8f8f7}
table thead tr th,table tfoot tr th,table tbody tr td,table tr td,table tfoot tr td{line-height:1.6}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2;word-spacing:-.05em}
h1 strong,h2 strong,h3 strong,#toctitle strong,.sidebarblock>.content>.title strong,h4 strong,h5 strong,h6 strong{font-weight:400}
.center{margin-left:auto;margin-right:auto}
.stretch{width:100%}
.clearfix::before,.clearfix::after,.float-group::before,.float-group::after{content:" ";display:table}
.clearfix::after,.float-group::after{clear:both}
:not(pre).nobreak{word-wrap:normal}
:not(pre).nowrap{white-space:nowrap}
:not(pre).pre-wrap{white-space:pre-wrap}
:not(pre):not([class^=L])>code{font-size:.9375em;font-style:normal!important;letter-spacing:0;padding:.1em .5ex;word-spacing:-.15em;background:#f7f7f8;border-radius:4px;line-height:1.45;text-rendering:optimizeSpeed}
pre{color:rgba(0,0,0,.9);font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;line-height:1.45;text-rendering:optimizeSpeed}
pre code,pre pre{color:inherit;font-size:inherit;line-height:inherit}
pre>code{display:block}
pre.nowrap,pre.nowrap pre{white-space:pre;word-wrap:normal}
em em{font-style:normal}
strong strong{font-weight:400}
.keyseq{color:rgba(51,51,51,.8)}
kbd{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;display:inline-block;color:rgba(0,0,0,.8);font-size:.65em;line-height:1.45;background:#f7f7f7;border:1px solid #ccc;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.2),inset 0 0 0 .1em #fff;margin:0 .15em;padding:.2em .5em;vertical-align:middle;position:relative;top:-.1em;white-space:nowrap}
.keyseq kbd:first-child{margin-left:0}
.keyseq kbd:last-child{margin-right:0}
.menuseq,.menuref{color:#000}
.menuseq b:not(.caret),.menuref{font-weight:inherit}
.menuseq{word-spacing:-.02em}
.menuseq b.caret{font-size:1.25em;line-height:.8}
.menuseq i.caret{font-weight:bold;text-align:center;width:.45em}
b.button::before,b.button::after{position:relative;top:-1px;font-weight:400}
b.button::before{content:"[";padding:0 3px 0 2px}
b.button::after{content:"]";padding:0 2px 0 3px}
p a>code:hover{color:rgba(0,0,0,.9)}
#header,#content,#footnotes,#footer{width:100%;margin:0 auto;max-width:62.5em;*zoom:1;position:relative;padding-left:.9375em;padding-right:.9375em}
#header::before,#header::after,#content::before,#content::after,#footnotes::before,#footnotes::after,#footer::before,#footer::after{content:" ";display:table}
#header::after,#content::after,#footnotes::after,#footer::after{clear:both}
#content{margin-top:1.25em}
#content::before{content:none}
#header>h1:first-child{color:rgba(0,0,0,.85);margin-top:2.25rem;margin-bottom:0}
#header>h1:first-child+#toc{margin-top:8px;border-top:1px solid #dddddf}
#header>h1:only-child,body.toc2 #header>h1:nth-last-child(2){border-bottom:1px solid #dddddf;padding-bottom:8px}
#header .details{border-bottom:1px solid #dddddf;line-height:1.45;padding-top:.25em;padding-bottom:.25em;padding-left:.25em;color:rgba(0,0,0,.6);display:flex;flex-flow:row wrap}
#header .details span:first-child{margin-left:-.125em}
#header .details span.email a{color:rgba(0,0,0,.85)}
#header .details br{display:none}
#header .details br+span::before{content:"\00a0\2013\00a0"}
#header .details br+span.author::before{content:"\00a0\22c5\00a0";color:rgba(0,0,0,.85)}
#header .details br+span#revremark::before{content:"\00a0|\00a0"}
#header #revnumber{text-transform:capitalize}
#header #revnumber::after{content:"\00a0"}
#content>h1:first-child:not([class]){color:rgba(0,0,0,.85);border-bottom:1px solid #dddddf;padding-bottom:8px;margin-top:0;padding-top:1rem;margin-bottom:1.25rem}
#toc{border-bottom:1px solid #e7e7e9;padding-bottom:.5em}
#toc>ul{margin-left:.125em}
#toc ul.sectlevel0>li>a{font-style:italic}
#toc ul.sectlevel0 ul.sectlevel1{margin:.5em 0}
#toc ul{font-family:"Open Sans","DejaVu Sans",sans-serif;list-style-type:none}
#toc li{line-height:1.3334;margin-top:.3334em}
#toc a{text-decoration:none}
#toc a:active{text-decoration:underline}
#toctitle{color:#7a2518;font-size:1.2em}
@media screen and (min-width:768px){#toctitle{font-size:1.375em}
body.toc2{padding-left:15em;padding-right:0}
#toc.toc2{margin-top:0!important;background:#f8f8f7;position:fixed;width:15em;left:0;top:0;border-right:1px solid #e7e7e9;border-top-width:0!important;border-bottom-width:0!important;z-index:1000;padding:1.25em 1em;height:100%;overflow:auto}
#toc.toc2 #toctitle{margin-top:0;margin-bottom:.8rem;font-size:1.2em}
#toc.toc2>ul{font-size:.9em;margin-bottom:0}
#toc.toc2 ul ul{margin-left:0;padding-left:1em}
#toc.toc2 ul.sectlevel0 ul.sectlevel1{padding-left:0;margin-top:.5em;margin-bottom:.5em}
body.toc2.toc-right{padding-left:0;padding-right:15em}
body.toc2.toc-right #toc.toc2{border-right-width:0;border-left:1px solid #e7e7e9;left:auto;right:0}}
@media screen and (min-width:1280px){body.toc2{padding-left:20em;padding-right:0}
#toc.toc2{width:20em}
#toc.toc2 #toctitle{font-size:1.375em}
#toc.toc2>ul{font-size:.95em}
#toc.toc2 ul ul{padding-left:1.25em}
body.toc2.toc-right{padding-left:0;padding-right:20em}}
#content #toc{border:1px solid #e0e0dc;margin-bottom:1.25em;padding:1.25em;background:#f8f8f7;border-radius:4px}
#content #toc>:first-child{margin-top:0}
#content #toc>:last-child{margin-bottom:0}
#footer{max-width:none;background:rgba(0,0,0,.8);padding:1.25em}
#footer-text{color:hsla(0,0%,100%,.8);line-height:1.44}
#content{margin-bottom:.625em}
.sect1{padding-bottom:.625em}
@media screen and (min-width:768px){#content{margin-bottom:1.25em}
.sect1{padding-bottom:1.25em}}
.sect1:last-child{padding-bottom:0}
.sect1+.sect1{border-top:1px solid #e7e7e9}
#content h1>a.anchor,h2>a.anchor,h3>a.anchor,#toctitle>a.anchor,.sidebarblock>.content>.title>a.anchor,h4>a.anchor,h5>a.anchor,h6>a.anchor{position:absolute;z-index:1001;width:1.5ex;margin-left:-1.5ex;display:block;text-decoration:none!important;visibility:hidden;text-align:center;font-weight:400}
#content h1>a.anchor::before,h2>a.anchor::before,h3>a.anchor::before,#toctitle>a.anchor::before,.sidebarblock>.content>.title>a.anchor::before,h4>a.anchor::before,h5>a.anchor::before,h6>a.anchor::before{content:"\00A7";font-size:.85em;display:block;padding-top:.1em}
#content h1:hover>a.anchor,#content h1>a.anchor:hover,h2:hover>a.anchor,h2>a.anchor:hover,h3:hover>a.anchor,#toctitle:hover>a.anchor,.sidebarblock>.content>.title:hover>a.anchor,h3>a.anchor:hover,#toctitle>a.anchor:hover,.sidebarblock>.content>.title>a.anchor:hover,h4:hover>a.anchor,h4>a.anchor:hover,h5:hover>a.anchor,h5>a.anchor:hover,h6:hover>a.anchor,h6>a.anchor:hover{visibility:visible}
#content h1>a.link,h2>a.link,h3>a.link,#toctitle>a.link,.sidebarblock>.content>.title>a.link,h4>a.link,h5>a.link,h6>a.link{color:#ba3925;text-decoration:none}
#content h1>a.link:hover,h2>a.link:hover,h3>a.link:hover,#toctitle>a.link:hover,.sidebarblock>.content>.title>a.link:hover,h4>a.link:hover,h5>a.link:hover,h6>a.link:hover{color:#a53221}
details,.audioblock,.imageblock,.literalblock,.listingblock,.stemblock,.videoblock{margin-bottom:1.25em}
details{margin-left:1.25rem}
details>summary{cursor:pointer;display:block;position:relative;line-height:1.6;margin-bottom:.625rem;-webkit-tap-highlight-color:transparent}
details>summary::before{content:"";border:solid transparent;border-left:solid;border-width:.3em 0 .3em .5em;position:absolute;top:.5em;left:-1.25rem;transform:translateX(15%)}
details[open]>summary::before{border:solid transparent;border-top:solid;border-width:.5em .3em 0;transform:translateY(15%)}
details>summary::after{content:"";width:1.25rem;height:1em;position:absolute;top:.3em;left:-1.25rem}
.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{text-rendering:optimizeLegibility;text-align:left;font-family:"Noto Serif","DejaVu Serif",serif;font-size:1rem;font-style:italic}
table.tableblock.fit-content>caption.title{white-space:nowrap;width:0}
.paragraph.lead>p,#preamble>.sectionbody>[class=paragraph]:first-of-type p{font-size:1.21875em;line-height:1.6;color:rgba(0,0,0,.85)}
.admonitionblock>table{border-collapse:separate;border:0;background:none;width:100%}
.admonitionblock>table td.icon{text-align:center;width:80px}
.admonitionblock>table td.icon img{max-width:none}
.admonitionblock>table td.icon .title{font-weight:bold;font-family:"Open Sans","DejaVu Sans",sans-serif;text-transform:uppercase}
.admonitionblock>table td.content{padding-left:1.125em;padding-right:1.25em;border-left:1px solid #dddddf;color:rgba(0,0,0,.6);word-wrap:anywhere}
.admonitionblock>table td.content>:last-child>:last-child{margin-bottom:0}
.exampleblock>.content{border:1px solid #e6e6e6;margin-bottom:1.25em;padding:1.25em;background:#fff;border-radius:4px}
.exampleblock>.content>:first-child{margin-top:0}
.exampleblock>.content>:last-child{margin-bottom:0}
.sidebarblock{border:1px solid #dbdbd6;margin-bottom:1.25em;padding:1.25em;background:#f3f3f2;border-radius:4px}
.sidebarblock>:first-child{margin-top:0}
.sidebarblock>:last-child{margin-bottom:0}
.sidebarblock>.content>.title{color:#7a2518;margin-top:0;text-align:center}
.exampleblock>.content>:last-child>:last-child,.exampleblock>.content .olist>ol>li:last-child>:last-child,.exampleblock>.content .ulist>ul>li:last-child>:last-child,.exampleblock>.content .qlist>ol>li:last-child>:last-child,.sidebarblock>.content>:last-child>:last-child,.sidebarblock>.content .olist>ol>li:last-child>:last-child,.sidebarblock>.content .ulist>ul>li:last-child>:last-child,.sidebarblock>.content .qlist>ol>li:last-child>:last-child{margin-bottom:0}
.literalblock pre,.listingblock>.content>pre{border-radius:4px;overflow-x:auto;padding:1em;font-size:.8125em}
@media screen and (min-width:768px){.literalblock pre,.listingblock>.content>pre{font-size:.90625em}}
@media screen and (min-width:1280px){.literalblock pre,.listingblock>.content>pre{font-size:1em}}
.literalblock pre,.listingblock>.content>pre:not(.highlight),.listingblock>.content>pre[class=highlight],.listingblock>.content>pre[class^="highlight "]{background:#f7f7f8}
.literalblock.output pre{color:#f7f7f8;background:rgba(0,0,0,.9)}
.listingblock>.content{position:relative}
.listingblock code[data-lang]::before{display:none;content:attr(data-lang);position:absolute;font-size:.75em;top:.425rem;right:.5rem;line-height:1;text-transform:uppercase;color:inherit;opacity:.5}
.listingblock:hover code[data-lang]::before{display:block}
.listingblock.terminal pre .command::before{content:attr(data-prompt);padding-right:.5em;color:inherit;opacity:.5}
.listingblock.terminal pre .command:not([data-prompt])::before{content:"$"}
.listingblock pre.highlightjs{padding:0}
.listingblock pre.highlightjs>code{padding:1em;border-radius:4px}
.listingblock pre.prettyprint{border-width:0}
.prettyprint{background:#f7f7f8}
pre.prettyprint .linenums{line-height:1.45;margin-left:2em}
pre.prettyprint li{background:none;list-style-type:inherit;padding-left:0}
pre.prettyprint li code[data-lang]::before{opacity:1}
pre.prettyprint li:not(:first-child) code[data-lang]::before{display:none}
table.linenotable{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.linenotable td[class]{color:inherit;vertical-align:top;padding:0;line-height:inherit;white-space:normal}
table.linenotable td.code{padding-left:.75em}
table.linenotable td.linenos{border-right:1px solid;opacity:.35;padding-right:.5em}
pre.pygments .lineno{border-right:1px solid;opacity:.35;display:inline-block;margin-right:.75em}
pre.pygments .lineno::before{content:"";margin-right:-.125em}
.quoteblock{margin:0 1em 1.25em 1.5em;display:table}
.quoteblock:not(.excerpt)>.title{margin-left:-1.5em;margin-bottom:.75em}
.quoteblock blockquote,.quoteblock p{color:rgba(0,0,0,.85);font-size:1.15rem;line-height:1.75;word-spacing:.1em;letter-spacing:0;font-style:italic;text-align:justify}
.quoteblock blockquote{margin:0;padding:0;border:0}
.quoteblock blockquote::before{content:"\201c";float:left;font-size:2.75em;font-weight:bold;line-height:.6em;margin-left:-.6em;color:#7a2518;text-shadow:0 1px 2px rgba(0,0,0,.1)}
.quoteblock blockquote>.paragraph:last-child p{margin-bottom:0}
.quoteblock .attribution{margin-top:.75em;margin-right:.5ex;text-align:right}
.verseblock{margin:0 1em 1.25em}
.verseblock pre{font-family:"Open Sans","DejaVu Sans",sans-serif;font-size:1.15rem;color:rgba(0,0,0,.85);font-weight:300;text-rendering:optimizeLegibility}
.verseblock pre strong{font-weight:400}
.verseblock .attribution{margin-top:1.25rem;margin-left:.5ex}
.quoteblock .attribution,.verseblock .attribution{font-size:.9375em;line-height:1.45;font-style:italic}
.quoteblock .attribution br,.verseblock .attribution br{display:none}
.quoteblock .attribution cite,.verseblock .attribution cite{display:block;letter-spacing:-.025em;color:rgba(0,0,0,.6)}
.quoteblock.abstract blockquote::before,.quoteblock.excerpt blockquote::before,.quoteblock .quoteblock blockquote::before{display:none}
.quoteblock.abstract blockquote,.quoteblock.abstract p,.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{line-height:1.6;word-spacing:0}
.quoteblock.abstract{margin:0 1em 1.25em;display:block}
.quoteblock.abstract>.title{margin:0 0 .375em;font-size:1.15em;text-align:center}
.quoteblock.excerpt>blockquote,.quoteblock .quoteblock{padding:0 0 .25em 1em;border-left:.25em solid #dddddf}
.quoteblock.excerpt,.quoteblock .quoteblock{margin-left:0}
.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{color:inherit;font-size:1.0625rem}
.quoteblock.excerpt .attribution,.quoteblock .quoteblock .attribution{color:inherit;font-size:.85rem;text-align:left;margin-right:0}
p.tableblock:last-child{margin-bottom:0}
td.tableblock>.content{margin-bottom:1.25em;word-wrap:anywhere}
td.tableblock>.content>:last-child{margin-bottom:-1.25em}
table.tableblock,th.tableblock,td.tableblock{border:0 solid #dedede}
table.grid-all>*>tr>*{border-width:1px}
table.grid-cols>*>tr>*{border-width:0 1px}
table.grid-rows>*>tr>*{border-width:1px 0}
table.frame-all{border-width:1px}
table.frame-ends{border-width:1px 0}
table.frame-sides{border-width:0 1px}
table.frame-none>colgroup+*>:first-child>*,table.frame-sides>colgroup+*>:first-child>*{border-top-width:0}
table.frame-none>:last-child>:last-child>*,table.frame-sides>:last-child>:last-child>*{border-bottom-width:0}
table.frame-none>*>tr>:first-child,table.frame-ends>*>tr>:first-child{border-left-width:0}
table.frame-none>*>tr>:last-child,table.frame-ends>*>tr>:last-child{border-right-width:0}
table.stripes-all tr,table.stripes-odd tr:nth-of-type(odd),table.stripes-even tr:nth-of-type(even),table.stripes-hover tr:hover{background:#f8f8f7}
th.halign-left,td.halign-left{text-align:left}
th.halign-right,td.halign-right{text-align:right}
th.halign-center,td.halign-center{text-align:center}
th.valign-top,td.valign-top{vertical-align:top}
th.valign-bottom,td.valign-bottom{vertical-align:bottom}
th.valign-middle,td.valign-middle{vertical-align:middle}
table thead th,table tfoot th{font-weight:bold}
tbody tr th{background:#f7f8f7}
tbody tr th,tbody tr th p,tfoot tr th,tfoot tr th p{color:rgba(0,0,0,.8);font-weight:bold}
p.tableblock>code:only-child{background:none;padding:0}
p.tableblock{font-size:1em}
ol{margin-left:1.75em}
ul li ol{margin-left:1.5em}
dl dd{margin-left:1.125em}
dl dd:last-child,dl dd:last-child>:last-child{margin-bottom:0}
ol>li p,ul>li p,ul dd,ol dd,.olist .olist,.ulist .ulist,.ulist .olist,.olist .ulist{margin-bottom:.625em}
ul.checklist,ul.none,ol.none,ul.no-bullet,ol.no-bullet,ol.unnumbered,ul.unstyled,ol.unstyled{list-style-type:none}
ul.no-bullet,ol.no-bullet,ol.unnumbered{margin-left:.625em}
ul.unstyled,ol.unstyled{margin-left:0}
ul.checklist>li>p:first-child{margin-left:-1em}
ul.checklist>li>p:first-child>.fa-square-o:first-child,ul.checklist>li>p:first-child>.fa-check-square-o:first-child{width:1.25em;font-size:.8em;position:relative;bottom:.125em}
ul.checklist>li>p:first-child>input[type=checkbox]:first-child{margin-right:.25em}
ul.inline{display:flex;flex-flow:row wrap;list-style:none;margin:0 0 .625em -1.25em}
ul.inline>li{margin-left:1.25em}
.unstyled dl dt{font-weight:400;font-style:normal}
ol.arabic{list-style-type:decimal}
ol.decimal{list-style-type:decimal-leading-zero}
ol.loweralpha{list-style-type:lower-alpha}
ol.upperalpha{list-style-type:upper-alpha}
ol.lowerroman{list-style-type:lower-roman}
ol.upperroman{list-style-type:upper-roman}
ol.lowergreek{list-style-type:lower-greek}
.hdlist>table,.colist>table{border:0;background:none}
.hdlist>table>tbody>tr,.colist>table>tbody>tr{background:none}
td.hdlist1,td.hdlist2{vertical-align:top;padding:0 .625em}
td.hdlist1{font-weight:bold;padding-bottom:1.25em}
td.hdlist2{word-wrap:anywhere}
.literalblock+.colist,.listingblock+.colist{margin-top:-.5em}
.colist td:not([class]):first-child{padding:.4em .75em 0;line-height:1;vertical-align:top}
.colist td:not([class]):first-child img{max-width:none}
.colist td:not([class]):last-child{padding:.25em 0}
.thumb,.th{line-height:0;display:inline-block;border:4px solid #fff;box-shadow:0 0 0 1px #ddd}
.imageblock.left{margin:.25em .625em 1.25em 0}
.imageblock.right{margin:.25em 0 1.25em .625em}
.imageblock>.title{margin-bottom:0}
.imageblock.thumb,.imageblock.th{border-width:6px}
.imageblock.thumb>.title,.imageblock.th>.title{padding:0 .125em}
.image.left,.image.right{margin-top:.25em;margin-bottom:.25em;display:inline-block;line-height:0}
.image.left{margin-right:.625em}
.image.right{margin-left:.625em}
a.image{text-decoration:none;display:inline-block}
a.image object{pointer-events:none}
sup.footnote,sup.footnoteref{font-size:.875em;position:static;vertical-align:super}
sup.footnote a,sup.footnoteref a{text-decoration:none}
sup.footnote a:active,sup.footnoteref a:active{text-decoration:underline}
#footnotes{padding-top:.75em;padding-bottom:.75em;margin-bottom:.625em}
#footnotes hr{width:20%;min-width:6.25em;margin:-.25em 0 .75em;border-width:1px 0 0}
#footnotes .footnote{padding:0 .375em 0 .225em;line-height:1.3334;font-size:.875em;margin-left:1.2em;margin-bottom:.2em}
#footnotes .footnote a:first-of-type{font-weight:bold;text-decoration:none;margin-left:-1.05em}
#footnotes .footnote:last-of-type{margin-bottom:0}
#content #footnotes{margin-top:-.625em;margin-bottom:0;padding:.75em 0}
.gist .file-data>table{border:0;background:#fff;width:100%;margin-bottom:0}
.gist .file-data>table td.line-data{width:99%}
div.unbreakable{page-break-inside:avoid}
.big{font-size:larger}
.small{font-size:smaller}
.underline{text-decoration:underline}
.overline{text-decoration:overline}
.line-through{text-decoration:line-through}
.aqua{color:#00bfbf}
.aqua-background{background:#00fafa}
.black{color:#000}
.black-background{background:#000}
.blue{color:#0000bf}
.blue-background{background:#0000fa}
.fuchsia{color:#bf00bf}
.fuchsia-background{background:#fa00fa}
.gray{color:#606060}
.gray-background{background:#7d7d7d}
.green{color:#006000}
.green-background{background:#007d00}
.lime{color:#00bf00}
.lime-background{background:#00fa00}
.maroon{color:#600000}
.maroon-background{background:#7d0000}
.navy{color:#000060}
.navy-background{background:#00007d}
.olive{color:#606000}
.olive-background{background:#7d7d00}
.purple{color:#600060}
.purple-background{background:#7d007d}
.red{color:#bf0000}
.red-background{background:#fa0000}
.silver{color:#909090}
.silver-background{background:#bcbcbc}
.teal{color:#006060}
.teal-background{background:#007d7d}
.white{color:#bfbfbf}
.white-background{background:#fafafa}
.yellow{color:#bfbf00}
.yellow-background{background:#fafa00}
span.icon>.fa{cursor:default}
a span.icon>.fa{cursor:inherit}
.admonitionblock td.icon [class^="fa icon-"]{font-size:2.5em;text-shadow:1px 1px 2px rgba(0,0,0,.5);cursor:default}
.admonitionblock td.icon .icon-note::before{content:"\f05a";color:#19407c}
.admonitionblock td.icon .icon-tip::before{content:"\f0eb";text-shadow:1px 1px 2px rgba(155,155,0,.8);color:#111}
.admonitionblock td.icon .icon-warning::before{content:"\f071";color:#bf6900}
.admonitionblock td.icon .icon-caution::before{content:"\f06d";color:#bf3400}
.admonitionblock td.icon .icon-important::before{content:"\f06a";color:#bf0000}
.conum[data-value]{display:inline-block;color:#fff!important;background:rgba(0,0,0,.8);border-radius:50%;text-align:center;font-size:.75em;width:1.67em;height:1.67em;line-height:1.67em;font-family:"Open Sans","DejaVu Sans",sans-serif;font-style:normal;font-weight:bold}
.conum[data-value] *{color:#fff!important}
.conum[data-value]+b{display:none}
.conum[data-value]::after{content:attr(data-value)}
pre .conum[data-value]{position:relative;top:-.125em}
b.conum *{color:inherit!important}
.conum:not([data-value]):empty{display:none}
dt,th.tableblock,td.content,div.footnote{text-rendering:optimizeLegibility}
h1,h2,p,td.content,span.alt,summary{letter-spacing:-.01em}
p strong,td.content strong,div.footnote strong{letter-spacing:-.005em}
p,blockquote,dt,td.content,span.alt,summary{font-size:1.0625rem}
p{margin-bottom:1.25rem}
.sidebarblock p,.sidebarblock dt,.sidebarblock td.content,p.tableblock{font-size:1em}
.exampleblock>.content{background:#fffef7;border-color:#e0e0dc;box-shadow:0 1px 4px #e0e0dc}
.print-only{display:none!important}
@page{margin:1.25cm .75cm}
@media print{*{box-shadow:none!important;text-shadow:none!important}
html{font-size:80%}
a{color:inherit!important;text-decoration:underline!important}
a.bare,a[href^="#"],a[href^="mailto:"]{text-decoration:none!important}
a[href^="http:"]:not(.bare)::after,a[href^="https:"]:not(.bare)::after{content:"(" attr(href) ")";display:inline-block;font-size:.875em;padding-left:.25em}
abbr[title]{border-bottom:1px dotted}
abbr[title]::after{content:" (" attr(title) ")"}
pre,blockquote,tr,img,object,svg{page-break-inside:avoid}
thead{display:table-header-group}
svg{max-width:100%}
p,blockquote,dt,td.content{font-size:1em;orphans:3;widows:3}
h2,h3,#toctitle,.sidebarblock>.content>.title{page-break-after:avoid}
#header,#content,#footnotes,#footer{max-width:none}
#toc,.sidebarblock,.exampleblock>.content{background:none!important}
#toc{border-bottom:1px solid #dddddf!important;padding-bottom:0!important}
body.book #header{text-align:center}
body.book #header>h1:first-child{border:0!important;margin:2.5em 0 1em}
body.book #header .details{border:0!important;display:block;padding:0!important}
body.book #header .details span:first-child{margin-left:0!important}
body.book #header .details br{display:block}
body.book #header .details br+span::before{content:none!important}
body.book #toc{border:0!important;text-align:left!important;padding:0!important;margin:0!important}
body.book #toc,body.book #preamble,body.book h1.sect0,body.book .sect1>h2{page-break-before:always}
.listingblock code[data-lang]::before{display:block}
#footer{padding:0 .9375em}
.hide-on-print{display:none!important}
.print-only{display:block!important}
.hide-for-print{display:none!important}
.show-for-print{display:inherit!important}}
@media amzn-kf8,print{#header>h1:first-child{margin-top:1.25rem}
.sect1{padding:0!important}
.sect1+.sect1{border:0}
#footer{background:none}
#footer-text{color:rgba(0,0,0,.6);font-size:.9em}}
@media amzn-kf8{#header,#content,#footnotes,#footer{padding:0}}
</style>
<style>
pre.rouge table td { padding: 5px; }
pre.rouge table pre { margin: 0; }
pre.rouge .cm {
  color: #999988;
  font-style: italic;
}
pre.rouge .cp {
  color: #999999;
  font-weight: bold;
}
pre.rouge .c1 {
  color: #999988;
  font-style: italic;
}
pre.rouge .cs {
  color: #999999;
  font-weight: bold;
  font-style: italic;
}
pre.rouge .c, pre.rouge .ch, pre.rouge .cd, pre.rouge .cpf {
  color: #999988;
  font-style: italic;
}
pre.rouge .err {
  color: #a61717;
  background-color: #e3d2d2;
}
pre.rouge .gd {
  color: #000000;
  background-color: #ffdddd;
}
pre.rouge .ge {
  color: #000000;
  font-style: italic;
}
pre.rouge .gr {
  color: #aa0000;
}
pre.rouge .gh {
  color: #999999;
}
pre.rouge .gi {
  color: #000000;
  background-color: #ddffdd;
}
pre.rouge .go {
  color: #888888;
}
pre.rouge .gp {
  color: #555555;
}
pre.rouge .gs {
  font-weight: bold;
}
pre.rouge .gu {
  color: #aaaaaa;
}
pre.rouge .gt {
  color: #aa0000;
}
pre.rouge .kc {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kd {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kn {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kp {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kr {
  color: #000000;
  font-weight: bold;
}
pre.rouge .kt {
  color: #445588;
  font-weight: bold;
}
pre.rouge .k, pre.rouge .kv {
  color: #000000;
  font-weight: bold;
}
pre.rouge .mf {
  color: #009999;
}
pre.rouge .mh {
  color: #009999;
}
pre.rouge .il {
  color: #009999;
}
pre.rouge .mi {
  color: #009999;
}
pre.rouge .mo {
  color: #009999;
}
pre.rouge .m, pre.rouge .mb, pre.rouge .mx {
  color: #009999;
}
pre.rouge .sa {
  color: #000000;
  font-weight: bold;
}
pre.rouge .sb {
  color: #d14;
}
pre.rouge .sc {
  color: #d14;
}
pre.rouge .sd {
  color: #d14;
}
pre.rouge .s2 {
  color: #d14;
}
pre.rouge .se {
  color: #d14;
}
pre.rouge .sh {
  color: #d14;
}
pre.rouge .si {
  color: #d14;
}
pre.rouge .sx {
  color: #d14;
}
pre.rouge .sr {
  color: #009926;
}
pre.rouge .s1 {
  color: #d14;
}
pre.rouge .ss {
  color: #990073;
}
pre.rouge .s, pre.rouge .dl {
  color: #d14;
}
pre.rouge .na {
  color: #008080;
}
pre.rouge .bp {
  color: #999999;
}
pre.rouge .nb {
  color: #0086B3;
}
pre.rouge .nc {
  color: #445588;
  font-weight: bold;
}
pre.rouge .no {
  color: #008080;
}
pre.rouge .nd {
  color: #3c5d5d;
  font-weight: bold;
}
pre.rouge .ni {
  color: #800080;
}
pre.rouge .ne {
  color: #990000;
  font-weight: bold;
}
pre.rouge .nf, pre.rouge .fm {
  color: #990000;
  font-weight: bold;
}
pre.rouge .nl {
  color: #990000;
  font-weight: bold;
}
pre.rouge .nn {
  color: #555555;
}
pre.rouge .nt {
  color: #000080;
}
pre.rouge .vc {
  color: #008080;
}
pre.rouge .vg {
  color: #008080;
}
pre.rouge .vi {
  color: #008080;
}
pre.rouge .nv, pre.rouge .vm {
  color: #008080;
}
pre.rouge .ow {
  color: #000000;
  font-weight: bold;
}
pre.rouge .o {
  color: #000000;
  font-weight: bold;
}
pre.rouge .w {
  color: #bbbbbb;
}
pre.rouge {
  background-color: #f8f8f8;
}
</style>
</head>
<body class="article toc2 toc-left">
<div id="header">
<h1>Boost.System: Extensible Error Reporting</h1>
<div class="details">
<span id="author" class="author">Beman Dawes, Christopher Kohlhoff, Peter Dimov</span><br>
</div>
<div id="toc" class="toc2">
<div id="toctitle">Table of Contents</div>
<ul class="sectlevel1">
<li><a href="#introduction">Introduction</a></li>
<li><a href="#usage">Usage</a>
<ul class="sectlevel2">
<li><a href="#usage_returning_errors_from_os_apis_under_posix">Returning Errors from OS APIs under POSIX</a></li>
<li><a href="#usage_returning_errors_from_os_apis_under_windows">Returning Errors from OS APIs under Windows</a></li>
<li><a href="#usage_returning_specific_errors_under_posix">Returning Specific Errors under POSIX</a></li>
<li><a href="#usage_returning_specific_errors_under_windows">Returning Specific Errors under Windows</a></li>
<li><a href="#usage_attaching_a_source_location_to_error_codes">Attaching a Source Location to Error Codes</a></li>
<li><a href="#usage_obtaining_textual_representations_of_error_codes_for_logging_and_display">Obtaining Textual Representations of Error Codes for Logging and Display</a></li>
<li><a href="#usage_composing_functions_returning_error_codes">Composing Functions Returning Error Codes</a></li>
<li><a href="#usage_providing_dual_throwing_and_nonthrowing_overloads">Providing Dual (Throwing and Nonthrowing) Overloads</a></li>
<li><a href="#usage_resultt_as_an_alternative_to_dual_apis">result&lt;T&gt; as an Alternative to Dual APIs</a></li>
<li><a href="#usage_testing_for_specific_error_conditions">Testing for Specific Error Conditions</a></li>
<li><a href="#usage_adapting_existing_integer_error_values">Adapting Existing Integer Error Values</a>
<ul class="sectlevel3">
<li><a href="#usage_adapting_sqlite_errors">Adapting SQLite Errors</a></li>
<li><a href="#usage_adapting_zlib_errors">Adapting ZLib Errors</a></li>
<li><a href="#usage_supporting_comparisons_against_conditions">Supporting Comparisons against Conditions</a></li>
</ul>
</li>
<li><a href="#usage_defining_library_specific_error_codes">Defining Library-Specific Error Codes</a></li>
<li><a href="#usage_defining_library_specific_error_conditions">Defining Library-Specific Error Conditions</a></li>
</ul>
</li>
<li><a href="#changes">Revision History</a>
<ul class="sectlevel2">
<li><a href="#changes_in_boost_1_81">Changes in Boost 1.81</a></li>
<li><a href="#changes_in_boost_1_80">Changes in Boost 1.80</a></li>
<li><a href="#changes_in_boost_1_79">Changes in Boost 1.79</a></li>
<li><a href="#changes_in_boost_1_78">Changes in Boost 1.78</a></li>
<li><a href="#changes_in_boost_1_77">Changes in Boost 1.77</a></li>
<li><a href="#changes_in_boost_1_76">Changes in Boost 1.76</a></li>
<li><a href="#changes_in_boost_1_75">Changes in Boost 1.75</a></li>
<li><a href="#changes_in_boost_1_74">Changes in Boost 1.74</a></li>
<li><a href="#changes_in_boost_1_69">Changes in Boost 1.69</a></li>
<li><a href="#changes_in_boost_1_68">Changes in Boost 1.68</a></li>
<li><a href="#changes_in_boost_1_65">Changes in Boost 1.65</a></li>
</ul>
</li>
<li><a href="#rationale">Design Rationale</a></li>
<li><a href="#reference">Reference</a>
<ul class="sectlevel2">
<li><a href="#ref_use_of_c11_and_c14_features">Use of C&#43;&#43;11 and C&#43;&#43;14 Features</a></li>
<li><a href="#ref_macros">Macros</a></li>
<li><a href="#ref_deprecated_names">Deprecated Names</a></li>
<li><a href="#ref_boostsystemis_error_code_enum_hpp">&lt;boost/system/&#8203;is_error_code_enum.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#ref_is_error_code_enum">is_error_code_enum</a></li>
</ul>
</li>
<li><a href="#ref_boostsystemis_error_condition_enum_hpp">&lt;boost/system/&#8203;is_error_condition_enum.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#ref_is_error_condition_enum">is_error_condition_enum</a></li>
</ul>
</li>
<li><a href="#ref_boostsystemerrc_hpp">&lt;boost/system/&#8203;errc.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#ref_errc">errc</a></li>
</ul>
</li>
<li><a href="#ref_boostsystemerror_category_hpp">&lt;boost/system/&#8203;error_category.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#ref_error_category">error_category</a>
<ul class="sectlevel4">
<li><a href="#ref_constructors">Constructors</a></li>
<li><a href="#ref_virtuals">Virtuals</a></li>
<li><a href="#ref_comparisons">Comparisons</a></li>
<li><a href="#ref_conversions">Conversions</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#ref_boostsystemsystem_category_hpp">&lt;boost/system/&#8203;system_category.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#ref_system_category">system_category</a></li>
</ul>
</li>
<li><a href="#ref_boostsystemgeneric_category_hpp">&lt;boost/system/&#8203;generic_category.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#ref_generic_category">generic_category</a></li>
</ul>
</li>
<li><a href="#ref_boostsystemerror_code_hpp">&lt;boost/system/&#8203;error_code.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#ref_error_code">error_code</a>
<ul class="sectlevel4">
<li><a href="#ref_constructors_2">Constructors</a></li>
<li><a href="#ref_modifiers">Modifiers</a></li>
<li><a href="#ref_observers">Observers</a></li>
<li><a href="#ref_comparisons_2">Comparisons</a></li>
<li><a href="#ref_conversions_2">Conversions</a></li>
<li><a href="#ref_to_string">to_string</a></li>
<li><a href="#ref_stream_insertion">Stream Insertion</a></li>
<li><a href="#ref_what">what</a></li>
<li><a href="#ref_nonmembers">Nonmembers</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#ref_boostsystemerror_condition_hpp">&lt;boost/system/&#8203;error_condition.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#ref_error_condition">error_condition</a>
<ul class="sectlevel4">
<li><a href="#ref_constructors_3">Constructors</a></li>
<li><a href="#ref_modifiers_2">Modifiers</a></li>
<li><a href="#ref_observers_2">Observers</a></li>
<li><a href="#ref_comparisons_3">Comparisons</a></li>
<li><a href="#ref_conversions_3">Conversions</a></li>
<li><a href="#ref_to_string_2">to_string</a></li>
<li><a href="#ref_stream_insertion_2">Stream Insertion</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#ref_boostsystemsystem_error_hpp">&lt;boost/system/&#8203;system_error.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#ref_system_error">system_error</a>
<ul class="sectlevel4">
<li><a href="#ref_constructors_4">Constructors</a></li>
<li><a href="#ref_observers_3">Observers</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#ref_boostsystemresult_hpp">&lt;boost/system/result.hpp&gt;</a>
<ul class="sectlevel3">
<li><a href="#ref_synopsis">Synopsis</a></li>
<li><a href="#ref_throw_exception_from_error">throw_exception_from_error</a></li>
<li><a href="#ref_resultt_e">result&lt;T, E&gt;</a>
<ul class="sectlevel4">
<li><a href="#ref_constructors_5">Constructors</a></li>
<li><a href="#ref_queries">Queries</a></li>
<li><a href="#ref_checked_value_access">Checked Value Access</a></li>
<li><a href="#ref_unchecked_value_access">Unchecked Value Access</a></li>
<li><a href="#ref_error_access">Error Access</a></li>
<li><a href="#ref_emplace">emplace</a></li>
<li><a href="#ref_swap">swap</a></li>
<li><a href="#ref_equality">Equality</a></li>
<li><a href="#ref_stream_insertion_3">Stream Insertion</a></li>
</ul>
</li>
<li><a href="#ref_resultvoid_e">result&lt;void, E&gt;</a>
<ul class="sectlevel4">
<li><a href="#ref_constructors_6">Constructors</a></li>
<li><a href="#ref_queries_2">Queries</a></li>
<li><a href="#ref_checked_value_access_2">Checked Value Access</a></li>
<li><a href="#ref_unchecked_value_access_2">Unchecked Value Access</a></li>
<li><a href="#ref_error_access_2">Error Access</a></li>
<li><a href="#ref_emplace_2">emplace</a></li>
<li><a href="#ref_swap_2">swap</a></li>
<li><a href="#ref_equality_2">Equality</a></li>
<li><a href="#ref_stream_insertion_4">Stream Insertion</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#ref_boostsystem_hpp">&lt;boost/system.hpp&gt;</a></li>
</ul>
</li>
<li><a href="#history">History</a></li>
<li><a href="#acknowledgments">Acknowledgments</a></li>
<li><a href="#copyright">Copyright and License</a></li>
</ul>
</div>
</div>
<div id="content">
<div class="sect1">
<h2 id="introduction">Introduction</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Errors originating from the operating system or other low-level application
program interfaces (APIs) are typically reported via an integer representing
an error code, either by returning the code directly from the function (e.g.
<code>pthread_mutex_init</code>) or by using a side channel such as the <code>errno</code>
pseudo-variable under POSIX or <code>GetLastError()</code> under Windows.</p>
</div>
<div class="paragraph">
<p>However, these integer error values can only be interpreted when their source
is known. The value 5 under Windows means <code>ERROR_ACCESS_DENIED</code> when returned
by <code>GetLastError()</code>, but <code>EIO</code> when retrieved from <code>errno</code>. And conversely,
the same error condition "access denied" is represented by the value 5 when
returned by <code>GetLastError()</code> and 13 (<code>EACCES</code>) when retrieved from <code>errno</code>.</p>
</div>
<div class="paragraph">
<p>This means that in order for code to be able to handle errors from both
sources (to retrieve a text message describing the error, or to check whether
the error means "access denied"), it needs to know where the integer error
value originated. For this to be possible, the integer error value needs to
be accompanied by a piece of information identifying the source.</p>
</div>
<div class="paragraph">
<p>Boost.System provides a framework in which this is possible. Errors are
represented by a class <code>error_code</code> which contains both the error value and
a pointer to their source (called "category"), represented as a class derived
from <code>error_category</code>.</p>
</div>
<div class="paragraph">
<p>The category provides member functions such as <code>message</code>, which returns a text
message for a specific error value, and <code>equivalent</code>, which can be used to test
whether a specific error value correspond to an error condition such as "access
denied". <code>error_code</code> uses these category-provided functions in the
implementation of its <code>message</code> and <code>operator==</code> member functions.</p>
</div>
<div class="paragraph">
<p>Boost.System contains two predefined category classes, the generic category
(a reference to which is returned by <code>generic_category()</code>) and the system
category (<code>system_category()</code>). The generic category represents the error
values of the portable subset of <code>errno</code> values defined by the POSIX standard,
whereas the system category is OS dependent. Under POSIX, the system category
represents the <code>errno</code> values returned by the OS APIs (a superset of those in
the generic category), whereas under Windows, the system category represents
the error values returned by <code>GetLastError()</code>.</p>
</div>
<div class="paragraph">
<p>The framework is extensible. Users can define their own categories by
deriving a class from <code>error_category</code> and implementing a function that
returns a reference to an instance of it. This capability is useful both for
describing library-defined error values, and for adapting existing C API
libraries that return integer error values.</p>
</div>
<div class="paragraph">
<p>For those who prefer error reporting via exceptions, Boost.System provides
a standard exception class <code>system_error</code> that stores an <code>error_code</code>.</p>
</div>
<div class="paragraph">
<p>Boost.System was standardized in C&#43;&#43;11 as <code>&lt;system_error&gt;</code>. For a while,
the two were equivalent, but Boost.System has evolved since then and now
contains a number of extensions over its standard sibling:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>A non-allocating overload of <code>message</code>;</p>
</li>
<li>
<p>Support for nonzero error codes meaning success, via the <code>failed</code> member
functions;</p>
</li>
<li>
<p>Support for 64 bit category identifiers, as a solution to the problem
that sometimes it&#8217;s not possible to ensure that only one instance of a
category exists in the program;</p>
</li>
<li>
<p>Support for attaching source locations (file/line/function) to error codes;</p>
</li>
<li>
<p>A class <code>result&lt;T&gt;</code> that can be used to return either a value or an error
code from a function;</p>
</li>
<li>
<p>Various other minor improvements.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p><code>boost::system::error_code</code> can be converted to, and constructed from,
<code>std::error_code</code>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="usage">Usage</h2>
<div class="sectionbody">
<div class="paragraph">
<p>All of the following code snippets assume that these lines</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#include &lt;boost/system.hpp&gt;
</span><span class="k">namespace</span> <span class="n">sys</span> <span class="o">=</span> <span class="n">boost</span><span class="o">::</span><span class="n">system</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>are in effect.</p>
</div>
<div class="sect2">
<h3 id="usage_returning_errors_from_os_apis_under_posix">Returning Errors from OS APIs under POSIX</h3>
<div class="paragraph">
<p>Let&#8217;s suppose that we&#8217;re implementing a portable <code>file</code> wrapper
over the OS file APIs. Its general outline is shown below:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">class</span> <span class="nc">file</span>
<span class="p">{</span>
<span class="nl">private:</span>

    <span class="kt">int</span> <span class="n">fd_</span><span class="p">;</span>

<span class="nl">public:</span>

    <span class="c1">// ...</span>

    <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">read</span><span class="p">(</span> <span class="kt">void</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">);</span>
    <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">write</span><span class="p">(</span> <span class="kt">void</span> <span class="k">const</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">);</span>
<span class="p">};</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Since we&#8217;re implementing the POSIX version of <code>file</code>, its
data member is a POSIX file descriptor <code>int fd_;</code>, although other
implementations will differ.</p>
</div>
<div class="paragraph">
<p>Our <code>read</code> and <code>write</code> functions return the number of bytes transferred, and signal
errors via the output parameter <code>ec</code>, of type <code>boost::system::error_code</code>.</p>
</div>
<div class="paragraph">
<p>An implementation of <code>file::read</code> might look like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">file</span><span class="o">::</span><span class="n">read</span><span class="p">(</span> <span class="kt">void</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="kt">ssize_t</span> <span class="n">r</span> <span class="o">=</span> <span class="o">::</span><span class="n">read</span><span class="p">(</span> <span class="n">fd_</span><span class="p">,</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">size</span> <span class="p">);</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">r</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">errno</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">system_category</span><span class="p">()</span> <span class="p">);</span>
        <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="n">ec</span> <span class="o">=</span> <span class="p">{};</span> <span class="c1">// ec.clear(); under C++03</span>
    <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>We first call the POSIX API <code>read</code>; if it returns an error, we store the <code>errno</code>
value in <code>ec</code>, using the system category, and return 0 as bytes transferred.
Otherwise, we clear <code>ec</code> to signal success, and return the result of <code>::read</code>.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
Clearing <code>ec</code> on successful returns is an important step; do not omit it.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>Under POSIX, the system category corresponds to POSIX <code>errno</code> values, which is
why we use it.</p>
</div>
<div class="paragraph">
<p>In principle, since the generic category <em>also</em> corresponds to <code>errno</code> values
under all platforms, we could have used it here; however, by convention under
POSIX, if the <code>errno</code> value comes from the OS (the "system"), we use the system
category for it. That&#8217;s because the system category values may be a
platform-specific superset of the generic (platform-independent) values.</p>
</div>
<div class="paragraph">
<p>The implementation of <code>file::write</code> is basically the same. We show it here for
completeness:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">file</span><span class="o">::</span><span class="n">write</span><span class="p">(</span> <span class="kt">void</span> <span class="k">const</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="kt">ssize_t</span> <span class="n">r</span> <span class="o">=</span> <span class="o">::</span><span class="n">write</span><span class="p">(</span> <span class="n">fd_</span><span class="p">,</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">size</span> <span class="p">);</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">r</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">errno</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">system_category</span><span class="p">()</span> <span class="p">);</span>
        <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="n">ec</span> <span class="o">=</span> <span class="p">{};</span> <span class="c1">// ec.clear(); under C++03</span>
    <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="usage_returning_errors_from_os_apis_under_windows">Returning Errors from OS APIs under Windows</h3>
<div class="paragraph">
<p>Under Windows, our <code>file</code> object will store a <code>HANDLE</code> instead of an <code>int</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">class</span> <span class="nc">file</span>
<span class="p">{</span>
<span class="nl">private:</span>

    <span class="n">HANDLE</span> <span class="n">fh_</span><span class="p">;</span>

<span class="nl">public:</span>

    <span class="c1">// as before</span>
<span class="p">};</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>and the implementation of <code>file::read</code> will look like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">file</span><span class="o">::</span><span class="n">read</span><span class="p">(</span> <span class="kt">void</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">DWORD</span> <span class="n">r</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>

    <span class="k">if</span><span class="p">(</span> <span class="o">::</span><span class="n">ReadFile</span><span class="p">(</span> <span class="n">fh_</span><span class="p">,</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="o">&amp;</span><span class="n">r</span><span class="p">,</span> <span class="mi">0</span> <span class="p">)</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="c1">// success</span>
        <span class="n">ec</span> <span class="o">=</span> <span class="p">{};</span> <span class="c1">// ec.clear(); under C++03</span>
    <span class="p">}</span>
    <span class="k">else</span>
    <span class="p">{</span>
        <span class="c1">// failure</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="o">::</span><span class="n">GetLastError</span><span class="p">(),</span> <span class="n">sys</span><span class="o">::</span><span class="n">system_category</span><span class="p">()</span> <span class="p">);</span>
    <span class="p">}</span>

    <span class="c1">// In both cases, r is bytes transferred</span>
    <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Here, the system category corresponds to the values defined in the system
header <code>&lt;winerror.h&gt;</code> and returned by <code>GetLastError()</code>. Since we use the
Win32 API <code>ReadFile</code> to implement <code>file::read</code>, and it returns the error
code via <code>GetLastError()</code>, we again store that value in <code>ec</code> as belonging
to the system category.</p>
</div>
<div class="paragraph">
<p>The implementation of <code>file::write</code> is, again, the same.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">file</span><span class="o">::</span><span class="n">write</span><span class="p">(</span> <span class="kt">void</span> <span class="k">const</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">DWORD</span> <span class="n">r</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>

    <span class="k">if</span><span class="p">(</span> <span class="o">::</span><span class="n">WriteFile</span><span class="p">(</span> <span class="n">fh_</span><span class="p">,</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="o">&amp;</span><span class="n">r</span><span class="p">,</span> <span class="mi">0</span> <span class="p">)</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span> <span class="o">=</span> <span class="p">{};</span> <span class="c1">// ec.clear(); under C++03</span>
    <span class="p">}</span>
    <span class="k">else</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="o">::</span><span class="n">GetLastError</span><span class="p">(),</span> <span class="n">sys</span><span class="o">::</span><span class="n">system_category</span><span class="p">()</span> <span class="p">);</span>
    <span class="p">}</span>

    <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="usage_returning_specific_errors_under_posix">Returning Specific Errors under POSIX</h3>
<div class="paragraph">
<p>Our implementation of <code>file::read</code> has a problem; it accepts <code>std::size_t</code>
values for <code>size</code>, but the behavior of <code>::read</code> is unspecified when the
requested value does not fit in <code>ssize_t</code>. To avoid reliance on unspecified
behavior, let&#8217;s add a check for this condition and return an error:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">file</span><span class="o">::</span><span class="n">read</span><span class="p">(</span> <span class="kt">void</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">if</span><span class="p">(</span> <span class="n">size</span> <span class="o">&gt;</span> <span class="n">SSIZE_MAX</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">EINVAL</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">generic_category</span><span class="p">()</span> <span class="p">);</span>
        <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="kt">ssize_t</span> <span class="n">r</span> <span class="o">=</span> <span class="o">::</span><span class="n">read</span><span class="p">(</span> <span class="n">fd_</span><span class="p">,</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">size</span> <span class="p">);</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">r</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">errno</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">system_category</span><span class="p">()</span> <span class="p">);</span>
        <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="n">ec</span> <span class="o">=</span> <span class="p">{};</span> <span class="c1">// ec.clear(); under C++03</span>
    <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>In this case, since we&#8217;re returning the fixed <code>errno</code> value <code>EINVAL</code>, which
is part of the portable subset defined by the generic category, we mark the
error value in <code>ec</code> as belonging to the generic category.</p>
</div>
<div class="paragraph">
<p>It&#8217;s possible to use system as well, as <code>EINVAL</code> is also a system category
value under POSIX; however, using the generic category for values belonging
to the portable <code>errno</code> subset is slightly preferrable.</p>
</div>
<div class="paragraph">
<p>Our implementation of <code>file::write</code> needs to undergo a similar treatment.
There, however, we&#8217;ll apply another change. When there&#8217;s no space left on
the disk, <code>::write</code> returns a number of bytes written that is lower than
what we requested with <code>size</code>, but our function signals no error. We&#8217;ll make
it return <code>ENOSPC</code> in this case.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">file</span><span class="o">::</span><span class="n">write</span><span class="p">(</span> <span class="kt">void</span> <span class="k">const</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">if</span><span class="p">(</span> <span class="n">size</span> <span class="o">&gt;</span> <span class="n">SSIZE_MAX</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">EINVAL</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">generic_category</span><span class="p">()</span> <span class="p">);</span>
        <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="kt">ssize_t</span> <span class="n">r</span> <span class="o">=</span> <span class="o">::</span><span class="n">write</span><span class="p">(</span> <span class="n">fd_</span><span class="p">,</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">size</span> <span class="p">);</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">r</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">errno</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">system_category</span><span class="p">()</span> <span class="p">);</span>
        <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">r</span> <span class="o">&lt;</span> <span class="n">size</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">ENOSPC</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">system_category</span><span class="p">()</span> <span class="p">);</span>
    <span class="p">}</span>
    <span class="k">else</span>
    <span class="p">{</span>
        <span class="n">ec</span> <span class="o">=</span> <span class="p">{};</span> <span class="c1">// ec.clear(); under C++03</span>
    <span class="p">}</span>

    <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>We&#8217;ve used the system category to make it appear that the <code>ENOSPC</code> value
has come from the <code>::write</code> API, mostly to illustrate that this is also a
possible approach. Using a generic value would have worked just as well.</p>
</div>
</div>
<div class="sect2">
<h3 id="usage_returning_specific_errors_under_windows">Returning Specific Errors under Windows</h3>
<div class="paragraph">
<p>Not much to say; the situation under Windows is exactly the same. The only
difference is that we <em>must</em> use the generic category for returning <code>errno</code>
values. The system category does not work; the integer values in the system
category are entirely different from those in the generic category.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">file</span><span class="o">::</span><span class="n">read</span><span class="p">(</span> <span class="kt">void</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">DWORD</span> <span class="n">r</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">size</span> <span class="o">&gt;</span> <span class="n">MAXDWORD</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">EINVAL</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">generic_category</span><span class="p">()</span> <span class="p">);</span>
    <span class="p">}</span>
    <span class="k">else</span> <span class="k">if</span><span class="p">(</span> <span class="o">::</span><span class="n">ReadFile</span><span class="p">(</span> <span class="n">fh_</span><span class="p">,</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="o">&amp;</span><span class="n">r</span><span class="p">,</span> <span class="mi">0</span> <span class="p">)</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span> <span class="o">=</span> <span class="p">{};</span> <span class="c1">// ec.clear(); under C++03</span>
    <span class="p">}</span>
    <span class="k">else</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="o">::</span><span class="n">GetLastError</span><span class="p">(),</span> <span class="n">sys</span><span class="o">::</span><span class="n">system_category</span><span class="p">()</span> <span class="p">);</span>
    <span class="p">}</span>

    <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
<span class="p">}</span>

<span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">file</span><span class="o">::</span><span class="n">write</span><span class="p">(</span> <span class="kt">void</span> <span class="k">const</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">DWORD</span> <span class="n">r</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">size</span> <span class="o">&gt;</span> <span class="n">MAXDWORD</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">EINVAL</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">generic_category</span><span class="p">()</span> <span class="p">);</span>
    <span class="p">}</span>
    <span class="k">else</span> <span class="k">if</span><span class="p">(</span> <span class="o">::</span><span class="n">WriteFile</span><span class="p">(</span> <span class="n">fh_</span><span class="p">,</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="o">&amp;</span><span class="n">r</span><span class="p">,</span> <span class="mi">0</span> <span class="p">)</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="k">if</span><span class="p">(</span> <span class="n">r</span> <span class="o">&lt;</span> <span class="n">size</span> <span class="p">)</span>
        <span class="p">{</span>
            <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">ENOSPC</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">generic_category</span><span class="p">()</span> <span class="p">);</span>
        <span class="p">}</span>
        <span class="k">else</span>
        <span class="p">{</span>
            <span class="n">ec</span> <span class="o">=</span> <span class="p">{};</span> <span class="c1">// ec.clear(); under C++03</span>
        <span class="p">}</span>
    <span class="p">}</span>
    <span class="k">else</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="o">::</span><span class="n">GetLastError</span><span class="p">(),</span> <span class="n">sys</span><span class="o">::</span><span class="n">system_category</span><span class="p">()</span> <span class="p">);</span>
    <span class="p">}</span>

    <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="usage_attaching_a_source_location_to_error_codes">Attaching a Source Location to Error Codes</h3>
<div class="paragraph">
<p>Unlike the standard <code>&lt;system_error&gt;</code>, Boost.System allows source locations
(file/line/function) to be stored in <code>error_code</code>, so that functions handling
the error can display or log the source code location where the error occurred.
To take advantage of this functionality, our POSIX <code>file::read</code> function needs
to be augmented as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">file</span><span class="o">::</span><span class="n">read</span><span class="p">(</span> <span class="kt">void</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">if</span><span class="p">(</span> <span class="n">size</span> <span class="o">&gt;</span> <span class="n">SSIZE_MAX</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="k">static</span> <span class="k">constexpr</span> <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="n">loc</span> <span class="o">=</span> <span class="n">BOOST_CURRENT_LOCATION</span><span class="p">;</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">EINVAL</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">generic_category</span><span class="p">(),</span> <span class="o">&amp;</span><span class="n">loc</span> <span class="p">);</span>
        <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="kt">ssize_t</span> <span class="n">r</span> <span class="o">=</span> <span class="o">::</span><span class="n">read</span><span class="p">(</span> <span class="n">fd_</span><span class="p">,</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">size</span> <span class="p">);</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">r</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="k">static</span> <span class="k">constexpr</span> <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="n">loc</span> <span class="o">=</span> <span class="n">BOOST_CURRENT_LOCATION</span><span class="p">;</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">errno</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">system_category</span><span class="p">(),</span> <span class="o">&amp;</span><span class="n">loc</span> <span class="p">);</span>
        <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="n">ec</span> <span class="o">=</span> <span class="p">{};</span> <span class="c1">// ec.clear(); under C++03</span>
    <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>That is, before every <code>ec.assign</code> statement, we need to declare a
<code>static constexpr</code> variable holding the current source location, then pass
a pointer to it to <code>assign</code>. Since <code>error_code</code> is small and there&#8217;s no space
in it for more than a pointer, we can&#8217;t just store the <code>source_location</code> in it
by value.</p>
</div>
<div class="paragraph">
<p><code>BOOST_CURRENT_LOCATION</code> is a macro expanding to the current source location
(a combination of <code>__FILE__</code>, <code>__LINE__</code>, and <code>BOOST_CURRENT_FUNCTION</code>.)
It&#8217;s defined and documented in <a href="../../../assert/index.html">Boost.Assert</a>.</p>
</div>
<div class="paragraph">
<p>Under C&#43;&#43;03, instead of <code>static constexpr</code>, one needs to use <code>static const</code>.
Another option is <code>BOOST_STATIC_CONSTEXPR</code>, a
<a href="../../../config/index.html">Boost.Config</a> macro that expands to either
<code>static constexpr</code> or <code>static const</code>, as appropriate.</p>
</div>
<div class="paragraph">
<p>To avoid repeating this boilerplate each time we do <code>ec.assign</code>, we can define
a macro:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#define ASSIGN(ec, ...) { \
    BOOST_STATIC_CONSTEXPR boost::source_location loc = BOOST_CURRENT_LOCATION; \
    (ec).assign(__VA_ARGS__, &amp;loc); }</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>which we can now use to augment, for example, the POSIX implementation of <code>file::write</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">file</span><span class="o">::</span><span class="n">write</span><span class="p">(</span> <span class="kt">void</span> <span class="k">const</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">if</span><span class="p">(</span> <span class="n">size</span> <span class="o">&gt;</span> <span class="n">SSIZE_MAX</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ASSIGN</span><span class="p">(</span> <span class="n">ec</span><span class="p">,</span> <span class="n">EINVAL</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">generic_category</span><span class="p">()</span> <span class="p">);</span>
        <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="kt">ssize_t</span> <span class="n">r</span> <span class="o">=</span> <span class="o">::</span><span class="n">write</span><span class="p">(</span> <span class="n">fd_</span><span class="p">,</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">size</span> <span class="p">);</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">r</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ASSIGN</span><span class="p">(</span> <span class="n">ec</span><span class="p">,</span> <span class="n">errno</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">system_category</span><span class="p">()</span> <span class="p">);</span>
        <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">r</span> <span class="o">&lt;</span> <span class="n">size</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ASSIGN</span><span class="p">(</span> <span class="n">ec</span><span class="p">,</span> <span class="n">ENOSPC</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">generic_category</span><span class="p">()</span> <span class="p">);</span>
    <span class="p">}</span>
    <span class="k">else</span>
    <span class="p">{</span>
        <span class="n">ec</span> <span class="o">=</span> <span class="p">{};</span> <span class="c1">// ec.clear(); under C++03</span>
    <span class="p">}</span>

    <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="usage_obtaining_textual_representations_of_error_codes_for_logging_and_display">Obtaining Textual Representations of Error Codes for Logging and Display</h3>
<div class="paragraph">
<p>Assuming that we have an <code>error_code</code> instance <code>ec</code>, returned to us by some
function, we have a variety of means to obtain textual representations of the
error code represented therein.</p>
</div>
<div class="paragraph">
<p><code>ec.to_string()</code> gives us the result of streaming <code>ec</code> into a <code>std::ostream</code>,
e.g. if <code>std::cout &lt;&lt; ec &lt;&lt; std::endl;</code> outputs <code>system:6</code>, this is what
<code>ec.to_string()</code> will return. (<code>system:6</code> under Windows is <code>ERROR_INVALID_HANDLE</code>
from <code>&lt;winerror.h&gt;</code>.)</p>
</div>
<div class="paragraph">
<p>To obtain a human-readable error message corresponding to this code, we can
use <code>ec.message()</code>. For <code>ERROR_INVALID_HANDLE</code>, it would give us "The handle is
invalid" - possibly localized.</p>
</div>
<div class="paragraph">
<p>If <code>ec</code> contains a source location, we can obtain its textual representation
via <code>ec.location().to_string()</code>. This will give us something like</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="text">C:\Projects\testbed2019\testbed2019.cpp:98 in function 'unsigned __int64 __cdecl file::read(void *,unsigned __int64,class boost::system::error_code &amp;)'</code></pre>
</div>
</div>
<div class="paragraph">
<p>if there is a location in <code>ec</code>, and</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="text">(unknown source location)</code></pre>
</div>
</div>
<div class="paragraph">
<p>if there isn&#8217;t. (<code>ec.has_location()</code> is <code>true</code> when <code>ec</code> contains a location.)</p>
</div>
<div class="paragraph">
<p>Finally, <code>ec.what()</code> will give us a string that contains all of the above,
something like</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="text">The handle is invalid [system:6 at C:\Projects\testbed2019\testbed2019.cpp:98 in function 'unsigned __int64 __cdecl file::read(void *,unsigned __int64,class boost::system::error_code &amp;)']</code></pre>
</div>
</div>
<div class="paragraph">
<p>Most logging and diagnostic output that is not intended for the end user would
probably end up using <code>what()</code>. (<code>ec.what()</code>, augmented with the prefix
supplied at construction, is also what <code>boost::system::system_error::what()</code>
would return.)</p>
</div>
</div>
<div class="sect2">
<h3 id="usage_composing_functions_returning_error_codes">Composing Functions Returning Error Codes</h3>
<div class="paragraph">
<p>Let&#8217;s suppose that we need to implement a file copy function, with the following
interface:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="nf">file_copy</span><span class="p">(</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">src</span><span class="p">,</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">dest</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p><code>file_copy</code> uses <code>src.read</code> to read bytes from <code>src</code>, then writes these bytes
to <code>dest</code> using <code>dest.write</code>. This continues until one of these operations signals
an error, or until end of file is reached. It returns the number of bytes written,
and uses <code>ec</code> to signal an error.</p>
</div>
<div class="paragraph">
<p>Here is one possible implementation:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="nf">file_copy</span><span class="p">(</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">src</span><span class="p">,</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">dest</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">r</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>

    <span class="k">for</span><span class="p">(</span> <span class="p">;;</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="kt">unsigned</span> <span class="kt">char</span> <span class="n">buffer</span><span class="p">[</span> <span class="mi">1024</span> <span class="p">];</span>

        <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">n</span> <span class="o">=</span> <span class="n">src</span><span class="p">.</span><span class="n">read</span><span class="p">(</span> <span class="n">buffer</span><span class="p">,</span> <span class="k">sizeof</span><span class="p">(</span> <span class="n">buffer</span> <span class="p">),</span> <span class="n">ec</span> <span class="p">);</span>

        <span class="c1">// read failed, leave the error in ec and return</span>
        <span class="k">if</span><span class="p">(</span> <span class="n">ec</span><span class="p">.</span><span class="n">failed</span><span class="p">()</span> <span class="p">)</span> <span class="k">return</span> <span class="n">r</span><span class="p">;</span>

        <span class="c1">// end of file has been reached, exit loop</span>
        <span class="k">if</span><span class="p">(</span> <span class="n">n</span> <span class="o">==</span> <span class="mi">0</span> <span class="p">)</span> <span class="k">return</span> <span class="n">r</span><span class="p">;</span>

        <span class="n">r</span> <span class="o">+=</span> <span class="n">dest</span><span class="p">.</span><span class="n">write</span><span class="p">(</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">n</span><span class="p">,</span> <span class="n">ec</span> <span class="p">);</span>

        <span class="c1">// write failed, leave the error in ec and return</span>
        <span class="k">if</span><span class="p">(</span> <span class="n">ec</span><span class="p">.</span><span class="n">failed</span><span class="p">()</span> <span class="p">)</span> <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
    <span class="p">}</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Note that there is no longer any difference between POSIX and Windows
implementations; their differences are contained in <code>file::read</code> and
<code>file::write</code>. <code>file_copy</code> is portable and works under any platform.</p>
</div>
<div class="paragraph">
<p>The general pattern in writing such higher-level functions is that
they pass the output <code>error_code</code> parameter <code>ec</code> they received from
the caller directly as the output parameter to the lower-level functions
they are built upon. This way, when they detect a failure in an intermediate
operation (by testing <code>ec.failed()</code>), they can immediately return to the
caller, because the error code is already in its proper place.</p>
</div>
<div class="paragraph">
<p>Note that <code>file_copy</code> doesn&#8217;t even need to clear <code>ec</code> on success, by
using <code>ec = {};</code>. Since we&#8217;ve already tested <code>ec.failed()</code>, we know that
<code>ec</code> contains a value that means success.</p>
</div>
</div>
<div class="sect2">
<h3 id="usage_providing_dual_throwing_and_nonthrowing_overloads">Providing Dual (Throwing and Nonthrowing) Overloads</h3>
<div class="paragraph">
<p>Functions that signal errors via an output <code>error_code&amp; ec</code> parameter
require that the caller check <code>ec</code> after calling them, and take appropriate
action (such as return immediately, as above.) Forgetting to check <code>ec</code>
results in logic errors.</p>
</div>
<div class="paragraph">
<p>While this is a preferred coding style for some, others prefer exceptions,
which one cannot forget to check.</p>
</div>
<div class="paragraph">
<p>An approach that has been introduced by
<a href="../../../filesystem/index.html">Boost.Filesystem</a> (which later turned
into <code>std::filesystem</code>) is to provide both alternatives: a nonthrowing
function taking <code>error_code&amp; ec</code>, as <code>file_copy</code> above, and a throwing
function that does not take an <code>error_code</code> output parameter, and throws
exceptions on failure.</p>
</div>
<div class="paragraph">
<p>This is how this second throwing function is typically implemented:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="nf">file_copy</span><span class="p">(</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">src</span><span class="p">,</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">dest</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span> <span class="n">ec</span><span class="p">;</span>
    <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">r</span> <span class="o">=</span> <span class="n">file_copy</span><span class="p">(</span> <span class="n">src</span><span class="p">,</span> <span class="n">dest</span><span class="p">,</span> <span class="n">ec</span> <span class="p">);</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ec</span><span class="p">.</span><span class="n">failed</span><span class="p">()</span> <span class="p">)</span> <span class="k">throw</span> <span class="n">sys</span><span class="o">::</span><span class="n">system_error</span><span class="p">(</span> <span class="n">ec</span><span class="p">,</span> <span class="n">__func__</span> <span class="p">);</span>

    <span class="k">return</span> <span class="n">r</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>That is, we simply call the nonthrowing overload of <code>file_copy</code>, and if
it signals failure in <code>ec</code>, throw a <code>system_error</code> exception.</p>
</div>
<div class="paragraph">
<p>We use our function name <code>__func__</code> (<code>"file_copy"</code>) as the prefix,
although that&#8217;s a matter of taste.</p>
</div>
<div class="paragraph">
<p>Note that typically under this style the overloads taking <code>error_code&amp; ec</code>
are decorated with <code>noexcept</code>, so that it&#8217;s clear that they don&#8217;t throw
exceptions (although we haven&#8217;t done so in the preceding examples in order
to keep the code C&#43;&#43;03-friendly.)</p>
</div>
</div>
<div class="sect2">
<h3 id="usage_resultt_as_an_alternative_to_dual_apis">result&lt;T&gt; as an Alternative to Dual APIs</h3>
<div class="paragraph">
<p>Instead of providing two functions for every operation, an alternative
approach is to make the function return <code>sys::result&lt;T&gt;</code> instead of <code>T</code>.
<code>result&lt;T&gt;</code> is a class holding either <code>T</code> or <code>error_code</code>, similar to
<a href="../../../variant2/index.html"><code>variant&lt;T, error_code&gt;</code></a>.</p>
</div>
<div class="paragraph">
<p>Clients that prefer to check for errors and not rely on exceptions can
test whether a <code>result&lt;T&gt; r</code> contains a value via <code>if( r )</code> or its more
verbose equivalent <code>if( r.has_value() )</code>, then obtain the value via
<code>*r</code> or <code>r.value()</code>. If <code>r</code> doesn&#8217;t contain a value, the <code>error_code</code>
it holds can be obtained with <code>r.error()</code>.</p>
</div>
<div class="paragraph">
<p>Those who prefer exceptions just call <code>r.value()</code> directly, without
checking. In the no-value case, this will automatically throw a
<code>system_error</code> corresponding to the <code>error_code</code> in <code>r</code>.</p>
</div>
<div class="paragraph">
<p>Assuming our base <code>file</code> API is unchanged, this variation of <code>file_copy</code>
would look like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">sys</span><span class="o">::</span><span class="n">result</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span><span class="o">&gt;</span> <span class="n">file_copy</span><span class="p">(</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">src</span><span class="p">,</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">dest</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">r</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
    <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span> <span class="n">ec</span><span class="p">;</span>

    <span class="k">for</span><span class="p">(</span> <span class="p">;;</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="kt">unsigned</span> <span class="kt">char</span> <span class="n">buffer</span><span class="p">[</span> <span class="mi">1024</span> <span class="p">];</span>

        <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">n</span> <span class="o">=</span> <span class="n">src</span><span class="p">.</span><span class="n">read</span><span class="p">(</span> <span class="n">buffer</span><span class="p">,</span> <span class="k">sizeof</span><span class="p">(</span> <span class="n">buffer</span> <span class="p">),</span> <span class="n">ec</span> <span class="p">);</span>

        <span class="k">if</span><span class="p">(</span> <span class="n">ec</span><span class="p">.</span><span class="n">failed</span><span class="p">()</span> <span class="p">)</span> <span class="k">return</span> <span class="n">ec</span><span class="p">;</span>
        <span class="k">if</span><span class="p">(</span> <span class="n">n</span> <span class="o">==</span> <span class="mi">0</span> <span class="p">)</span> <span class="k">return</span> <span class="n">r</span><span class="p">;</span>

        <span class="n">r</span> <span class="o">+=</span> <span class="n">dest</span><span class="p">.</span><span class="n">write</span><span class="p">(</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">n</span><span class="p">,</span> <span class="n">ec</span> <span class="p">);</span>

        <span class="k">if</span><span class="p">(</span> <span class="n">ec</span><span class="p">.</span><span class="n">failed</span><span class="p">()</span> <span class="p">)</span> <span class="k">return</span> <span class="n">ec</span><span class="p">;</span>
    <span class="p">}</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The only difference here is that we return <code>ec</code> on error, instead of
<code>r</code>.</p>
</div>
<div class="paragraph">
<p>Note, however, that we can no longer return both an error code and a
number of transferred bytes; that is, we can no longer signal <em>partial
success</em>. This is often not an issue at higher levels, but lower-level
primitives such as <code>file::read</code> and <code>file::write</code> might be better off
written using the old style.</p>
</div>
<div class="paragraph">
<p>Nevertheless, to demonstrate how <code>result</code> returning APIs are composed,
we&#8217;ll show how <code>file_copy</code> would look if <code>file::read</code> and <code>file::write</code>
returned <code>result&lt;size_t&gt;</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">class</span> <span class="nc">file</span>
<span class="p">{</span>
<span class="nl">public:</span>

    <span class="c1">// ...</span>

    <span class="n">sys</span><span class="o">::</span><span class="n">result</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span><span class="o">&gt;</span> <span class="n">read</span><span class="p">(</span> <span class="kt">void</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span> <span class="p">);</span>
    <span class="n">sys</span><span class="o">::</span><span class="n">result</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span><span class="o">&gt;</span> <span class="n">write</span><span class="p">(</span> <span class="kt">void</span> <span class="k">const</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">size</span> <span class="p">);</span>
<span class="p">};</span>

<span class="n">sys</span><span class="o">::</span><span class="n">result</span><span class="o">&lt;</span><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span><span class="o">&gt;</span> <span class="n">file_copy</span><span class="p">(</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">src</span><span class="p">,</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">dest</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">m</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>

    <span class="k">for</span><span class="p">(</span> <span class="p">;;</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="kt">unsigned</span> <span class="kt">char</span> <span class="n">buffer</span><span class="p">[</span> <span class="mi">1024</span> <span class="p">];</span>

        <span class="k">auto</span> <span class="n">r</span> <span class="o">=</span> <span class="n">src</span><span class="p">.</span><span class="n">read</span><span class="p">(</span> <span class="n">buffer</span><span class="p">,</span> <span class="k">sizeof</span><span class="p">(</span> <span class="n">buffer</span> <span class="p">)</span> <span class="p">);</span>
        <span class="k">if</span><span class="p">(</span> <span class="o">!</span><span class="n">r</span> <span class="p">)</span> <span class="k">return</span> <span class="n">r</span><span class="p">;</span>

        <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">n</span> <span class="o">=</span> <span class="o">*</span><span class="n">r</span><span class="p">;</span>
        <span class="k">if</span><span class="p">(</span> <span class="n">n</span> <span class="o">==</span> <span class="mi">0</span> <span class="p">)</span> <span class="k">return</span> <span class="n">m</span><span class="p">;</span>

        <span class="k">auto</span> <span class="n">r2</span> <span class="o">=</span> <span class="n">dest</span><span class="p">.</span><span class="n">write</span><span class="p">(</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">n</span> <span class="p">);</span>
        <span class="k">if</span><span class="p">(</span> <span class="o">!</span><span class="n">r2</span> <span class="p">)</span> <span class="k">return</span> <span class="n">r2</span><span class="p">;</span>

        <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">n2</span> <span class="o">=</span> <span class="o">*</span><span class="n">r2</span><span class="p">;</span>
        <span class="n">m</span> <span class="o">+=</span> <span class="n">n2</span><span class="p">;</span>
    <span class="p">}</span>
<span class="p">}</span></code></pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="usage_testing_for_specific_error_conditions">Testing for Specific Error Conditions</h3>
<div class="paragraph">
<p>Let&#8217;s suppose that we have called a function that signals failure
using <code>error_code</code>, we have passed it an <code>error_code</code> variable <code>ec</code>,
and now for some reason want to check whether the function has
failed with an error code of <code>EINVAL</code>, "invalid argument".</p>
</div>
<div class="paragraph">
<p>Since <code>error_code</code> can be compared for equality, our first instict
might be <code>if( ec == error_code( EINVAL, generic_category() )</code>.</p>
</div>
<div class="paragraph">
<p>This is wrong, and we should never do it.</p>
</div>
<div class="paragraph">
<p>First, under POSIX, the function might have returned <code>EINVAL</code> from
the system category (because the error might have been returned by
an OS API, and not by the function itself, as was the case in our
<code>read</code> and <code>write</code> implementations.)</p>
</div>
<div class="paragraph">
<p>Since <code>error_code</code> comparisons are exact, <code>EINVAL</code> from the generic
category does not compare equal to <code>EINVAL</code> from the system category.</p>
</div>
<div class="paragraph">
<p>(And before you start thinking about just comparing <code>ec.value()</code> to
<code>EINVAL</code>, read on.)</p>
</div>
<div class="paragraph">
<p>Second, under Windows, the function might have returned <code>error_code(
ERROR_INVALID_PARAMETER, system_category() )</code>. As we have already
mentioned, the integer error values in the system category under
Windows are completely unrelated to the integer <code>errno</code> values.</p>
</div>
<div class="paragraph">
<p>The correct approach is to compare <code>ec</code> not to specific error codes,
but to <code>error_condition( EINVAL, generic_category() )</code>. Error
conditions are a platform-independent way to represent the meaning
of the concrete error codes. In our case, all error codes, under
both POSIX and Windows, that represent <code>EINVAL</code> will compare equal
to <code>error_condition( EINVAL, generic_category() )</code>.</p>
</div>
<div class="paragraph">
<p>In short, you should never compare error codes to error codes, and
should compare them to error conditions instead. This is the purpose
of the <code>error_condition</code> class, which is very frequently misunderstood.</p>
</div>
<div class="paragraph">
<p>Since</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">if</span><span class="p">(</span> <span class="n">ec</span> <span class="o">==</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_condition</span><span class="p">(</span> <span class="n">EINVAL</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">generic_category</span><span class="p">()</span> <span class="p">)</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="c1">// handle EINVAL</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>is a bit verbose, Boost.System provides enumerator values for the
<code>errno</code> values against which an error code can be compared directly.</p>
</div>
<div class="paragraph">
<p>These enumerators are defined in <a href="#ref_errc"><code>&lt;boost/system/errc.hpp&gt;</code></a>,
and enable the above test to be written</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">if</span><span class="p">(</span> <span class="n">ec</span> <span class="o">==</span> <span class="n">sys</span><span class="o">::</span><span class="n">errc</span><span class="o">::</span><span class="n">invalid_argument</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="c1">// handle EINVAL</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>which is what one should generally use for testing for a specific error
condition, as a best practice.</p>
</div>
</div>
<div class="sect2">
<h3 id="usage_adapting_existing_integer_error_values">Adapting Existing Integer Error Values</h3>
<div class="paragraph">
<p>Libraries with C (or <code>extern "C"</code>) APIs often signal failure by returning
a library-specific integer error code (with zero typically being reserved
for "no error".) When writing portable C&#43;&#43; wrappers, we need to decide
how to expose these error codes, and using <code>error_code</code> is a good way to
do it.</p>
</div>
<div class="paragraph">
<p>Because the integer error codes are library specific, and in general match
neither <code>errno</code> values or system category values, we need to define a
library-specific error category.</p>
</div>
<div class="sect3">
<h4 id="usage_adapting_sqlite_errors">Adapting SQLite Errors</h4>
<div class="paragraph">
<p>We&#8217;ll take SQLite as an example. The general outline of a custom error
category is as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">class</span> <span class="nc">sqlite3_category_impl</span><span class="o">:</span> <span class="k">public</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_category</span>
<span class="p">{</span>
    <span class="c1">// TODO add whatever's needed here</span>
<span class="p">};</span>

<span class="n">sys</span><span class="o">::</span><span class="n">error_category</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">sqlite3_category</span><span class="p">()</span>
<span class="p">{</span>
    <span class="k">static</span> <span class="k">const</span> <span class="n">sqlite3_category_impl</span> <span class="n">instance</span><span class="p">;</span>
    <span class="k">return</span> <span class="n">instance</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>which can then be used similarly to the predefined generic and system
categories:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">int</span> <span class="n">r</span> <span class="o">=</span> <span class="n">some_sqlite3_function</span><span class="p">(</span> <span class="p">...</span> <span class="p">);</span>
<span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">r</span><span class="p">,</span> <span class="n">sqlite3_category</span><span class="p">()</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>If we try to compile the above category definition as-is, it will complain
about our not implementing two pure virtual member functions, <code>name</code> and
<code>message</code>, so at minimum, we&#8217;ll need to add these. In addition, we&#8217;ll also
implement the non-allocating overload of <code>message</code>. It&#8217;s not pure virtual,
but its default implementation calls the <code>std::string</code>-returning overload,
and that&#8217;s almost never what one wants. (This default implementation is only
provided for backward compatibility, in order to not break existing
user-defined categories that were written before this overload was added.)</p>
</div>
<div class="paragraph">
<p>So, the minimum we need to implement is this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">class</span> <span class="nc">sqlite3_category_impl</span><span class="o">:</span> <span class="k">public</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_category</span>
<span class="p">{</span>
<span class="nl">public:</span>

    <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">name</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span><span class="p">;</span>
    <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
<span class="p">};</span></code></pre>
</div>
</div>
<div class="paragraph">
<p><code>name</code> is easy, it just returns the category name:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">sqlite3_category_impl</span><span class="o">::</span><span class="n">name</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span>
<span class="p">{</span>
    <span class="k">return</span> <span class="s">"sqlite3"</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p><code>message</code> is used to obtain an error message given an integer error code.
SQLite provides the function <code>sqlite3_errstr</code> for this, so we don&#8217;t need
to do any work:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">sqlite3_category_impl</span><span class="o">::</span><span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span>
<span class="p">{</span>
    <span class="k">return</span> <span class="n">sqlite3_errstr</span><span class="p">(</span> <span class="n">ev</span> <span class="p">);</span>
<span class="p">}</span>

<span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">sqlite3_category_impl</span><span class="o">::</span><span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span>
<span class="p">{</span>
    <span class="n">std</span><span class="o">::</span><span class="n">snprintf</span><span class="p">(</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">len</span><span class="p">,</span> <span class="s">"%s"</span><span class="p">,</span> <span class="n">sqlite3_errstr</span><span class="p">(</span> <span class="n">ev</span> <span class="p">)</span> <span class="p">);</span>
    <span class="k">return</span> <span class="n">buffer</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>and we&#8217;re done. <code>sqlite3_category()</code> can now be used like the predefined
categories, and we can put an SQLite error code <code>int r</code> into a Boost.System
<code>error_code ec</code> by means of <code>ec.assign( r, sqlite3_category() )</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="usage_adapting_zlib_errors">Adapting ZLib Errors</h4>
<div class="paragraph">
<p>Another widely used C library is ZLib, and the portion of <code>zlib.h</code> that
defines its error codes is shown below:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#define Z_OK            0
#define Z_STREAM_END    1
#define Z_NEED_DICT     2
#define Z_ERRNO        (-1)
#define Z_STREAM_ERROR (-2)
#define Z_DATA_ERROR   (-3)
#define Z_MEM_ERROR    (-4)
#define Z_BUF_ERROR    (-5)
#define Z_VERSION_ERROR (-6)
</span><span class="cm">/* Return codes for the compression/decompression functions. Negative values
 * are errors, positive values are used for special but normal events.
 */</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>There are three relevant differences with the previous case of SQLite:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>While for SQLite all non-zero values were errors, as is the typical case,
here negative values are errors, but positive values are "special but normal",
that is, they represent success, not failure;</p>
</li>
<li>
<p>ZLib does not provide a function that returns the error message corresponding
to a specific error code;</p>
</li>
<li>
<p>When <code>Z_ERRNO</code> is returned, the error code should be retrieved from <code>errno</code>.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Our category implementation will look like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">class</span> <span class="nc">zlib_category_impl</span><span class="o">:</span> <span class="k">public</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_category</span>
<span class="p">{</span>
<span class="nl">public:</span>

    <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">name</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span><span class="p">;</span>
    <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="kt">bool</span> <span class="n">failed</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
<span class="p">};</span>

<span class="n">sys</span><span class="o">::</span><span class="n">error_category</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">zlib_category</span><span class="p">()</span>
<span class="p">{</span>
    <span class="k">static</span> <span class="k">const</span> <span class="n">zlib_category_impl</span> <span class="n">instance</span><span class="p">;</span>
    <span class="k">return</span> <span class="n">instance</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>As usual, the implementation of <code>name</code> is trivial:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">zlib_category_impl</span><span class="o">::</span><span class="n">name</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span>
<span class="p">{</span>
    <span class="k">return</span> <span class="s">"zlib"</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>We&#8217;ll need to work a bit harder to implement <code>message</code> this time, as there&#8217;s
no preexisting function to lean on:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">zlib_category_impl</span><span class="o">::</span><span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span>
<span class="p">{</span>
    <span class="k">switch</span><span class="p">(</span> <span class="n">ev</span> <span class="p">)</span>
    <span class="p">{</span>
    <span class="k">case</span> <span class="n">Z_OK</span><span class="p">:</span>            <span class="k">return</span> <span class="s">"No error"</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">Z_STREAM_END</span><span class="p">:</span>    <span class="k">return</span> <span class="s">"End of stream"</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">Z_NEED_DICT</span><span class="p">:</span>     <span class="k">return</span> <span class="s">"A dictionary is needed"</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">Z_ERRNO</span><span class="p">:</span>         <span class="k">return</span> <span class="s">"OS API error"</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">Z_STREAM_ERROR</span><span class="p">:</span>  <span class="k">return</span> <span class="s">"Inconsistent stream state or invalid argument"</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">Z_DATA_ERROR</span><span class="p">:</span>    <span class="k">return</span> <span class="s">"Data error"</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">Z_MEM_ERROR</span><span class="p">:</span>     <span class="k">return</span> <span class="s">"Out of memory"</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">Z_BUF_ERROR</span><span class="p">:</span>     <span class="k">return</span> <span class="s">"Insufficient buffer space"</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">Z_VERSION_ERROR</span><span class="p">:</span> <span class="k">return</span> <span class="s">"Library version mismatch"</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="n">std</span><span class="o">::</span><span class="n">snprintf</span><span class="p">(</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">len</span><span class="p">,</span> <span class="s">"Unknown zlib error %d"</span><span class="p">,</span> <span class="n">ev</span> <span class="p">);</span>
    <span class="k">return</span> <span class="n">buffer</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This is a typical implementation of the non-throwing <code>message</code> overload. Note
that <code>message</code> is allowed to return something different from <code>buffer</code>, which
means that we can return character literals directly, without copying them
into the supplied buffer first. This allows our function to return the correct
message text even when the buffer is too small.</p>
</div>
<div class="paragraph">
<p>The <code>std::string</code> overload of <code>message</code> is now trivial:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">zlib_category_impl</span><span class="o">::</span><span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span>
<span class="p">{</span>
    <span class="kt">char</span> <span class="n">buffer</span><span class="p">[</span> <span class="mi">64</span> <span class="p">];</span>
    <span class="k">return</span> <span class="k">this</span><span class="o">-&gt;</span><span class="n">message</span><span class="p">(</span> <span class="n">ev</span><span class="p">,</span> <span class="n">buffer</span><span class="p">,</span> <span class="k">sizeof</span><span class="p">(</span> <span class="n">buffer</span> <span class="p">)</span> <span class="p">);</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Finally, we need to implement <code>failed</code>, in order to override its default
behavior of returning <code>true</code> for all nonzero values:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">bool</span> <span class="n">zlib_category_impl</span><span class="o">::</span><span class="n">failed</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span>
<span class="p">{</span>
    <span class="k">return</span> <span class="n">ev</span> <span class="o">&lt;</span> <span class="mi">0</span><span class="p">;</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This completes the implementation of <code>zlib_category()</code> and takes care of the
first two bullets above, but we still haven&#8217;t addressed the third one; namely,
that we need to retrieve the error from <code>errno</code> in the <code>Z_ERRNO</code> case.</p>
</div>
<div class="paragraph">
<p>To do that, we&#8217;ll define a helper function that would be used to assign a ZLib
error code to an <code>error_code</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">void</span> <span class="nf">assign_zlib_error</span><span class="p">(</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">ec</span><span class="p">,</span> <span class="kt">int</span> <span class="n">r</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">if</span><span class="p">(</span> <span class="n">r</span> <span class="o">!=</span> <span class="n">Z_ERRNO</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">r</span><span class="p">,</span> <span class="n">zlib_category</span><span class="p">()</span> <span class="p">);</span>
    <span class="p">}</span>
    <span class="k">else</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">errno</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">generic_category</span><span class="p">()</span> <span class="p">);</span>
    <span class="p">}</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>so that, instead of using <code>ec.assign( r, zlib_category() )</code> directly, code
would do</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">int</span> <span class="n">r</span> <span class="o">=</span> <span class="n">some_zlib_function</span><span class="p">(</span> <span class="p">...</span> <span class="p">);</span>
<span class="n">assign_zlib_error</span><span class="p">(</span> <span class="n">ec</span><span class="p">,</span> <span class="n">r</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>We can stop here, as this covers everything we set out to do, but we can take
an extra step and enable source locations for our error codes. For that, we&#8217;ll
need to change <code>assign_zlib_error</code> to take a <code>source_location</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">void</span> <span class="nf">assign_zlib_error</span><span class="p">(</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">ec</span><span class="p">,</span> <span class="kt">int</span> <span class="n">r</span><span class="p">,</span> <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span><span class="o">*</span> <span class="n">loc</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">if</span><span class="p">(</span> <span class="n">r</span> <span class="o">!=</span> <span class="n">Z_ERRNO</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">r</span><span class="p">,</span> <span class="n">zlib_category</span><span class="p">(),</span> <span class="n">loc</span> <span class="p">);</span>
    <span class="p">}</span>
    <span class="k">else</span>
    <span class="p">{</span>
        <span class="n">ec</span><span class="p">.</span><span class="n">assign</span><span class="p">(</span> <span class="n">errno</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">generic_category</span><span class="p">(),</span> <span class="n">loc</span> <span class="p">);</span>
    <span class="p">}</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Define a helper macro to avoid the boilerplate of defining the <code>static
constexpr</code> source location object each time:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="cp">#define ASSIGN_ZLIB_ERROR(ec, r) { \
    BOOST_STATIC_CONSTEXPR boost::source_location loc = BOOST_CURRENT_LOCATION; \
    assign_zlib_error( ec, r, &amp;loc ); }</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>And then use the macro instead of the function:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">int</span> <span class="n">r</span> <span class="o">=</span> <span class="n">some_zlib_function</span><span class="p">(</span> <span class="p">...</span> <span class="p">);</span>
<span class="n">ASSIGN_ZLIB_ERROR</span><span class="p">(</span> <span class="n">ec</span><span class="p">,</span> <span class="n">r</span> <span class="p">);</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="usage_supporting_comparisons_against_conditions">Supporting Comparisons against Conditions</h4>
<div class="paragraph">
<p>We notice that some of the ZLib error codes correspond to portable <code>errno</code>
conditions. <code>Z_STREAM_ERROR</code>, for instance, is returned in cases where
POSIX functions would have returned <code>EINVAL</code>; <code>Z_MEM_ERROR</code> is <code>ENOMEM</code>;
and <code>Z_BUF_ERROR</code>, insufficient space in the output buffer to store the
result, roughly corresponds to <code>ERANGE</code>, result out of range.</p>
</div>
<div class="paragraph">
<p>To encode this relationship, we need to implement either
<code>default_error_condition</code> or <code>equivalent</code> in our category. Since we have
a simple one to one mapping, the former will suffice:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">class</span> <span class="nc">zlib_category_impl</span><span class="o">:</span> <span class="k">public</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_category</span>
<span class="p">{</span>
<span class="nl">public:</span>

    <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">name</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span><span class="p">;</span>
    <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="kt">bool</span> <span class="n">failed</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="n">sys</span><span class="o">::</span><span class="n">error_condition</span> <span class="n">default_error_condition</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
<span class="p">};</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The implementation is straightforward:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">sys</span><span class="o">::</span><span class="n">error_condition</span> <span class="n">zlib_category_impl</span><span class="o">::</span><span class="n">default_error_condition</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span>
<span class="p">{</span>
    <span class="k">switch</span><span class="p">(</span> <span class="n">ev</span> <span class="p">)</span>
    <span class="p">{</span>
    <span class="k">case</span> <span class="n">Z_OK</span><span class="p">:</span>            <span class="k">return</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_condition</span><span class="p">();</span>
    <span class="k">case</span> <span class="n">Z_STREAM_ERROR</span><span class="p">:</span>  <span class="k">return</span> <span class="n">sys</span><span class="o">::</span><span class="n">errc</span><span class="o">::</span><span class="n">invalid_argument</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">Z_MEM_ERROR</span><span class="p">:</span>     <span class="k">return</span> <span class="n">sys</span><span class="o">::</span><span class="n">errc</span><span class="o">::</span><span class="n">not_enough_memory</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">Z_BUF_ERROR</span><span class="p">:</span>     <span class="k">return</span> <span class="n">sys</span><span class="o">::</span><span class="n">errc</span><span class="o">::</span><span class="n">result_out_of_range</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="k">return</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_condition</span><span class="p">(</span> <span class="n">ev</span><span class="p">,</span> <span class="o">*</span><span class="k">this</span> <span class="p">);</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Once this is added, we will be able to compare a ZLib <code>error_code ec</code> against
<code>errc</code> enumerators:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">if</span><span class="p">(</span> <span class="n">ec</span> <span class="o">==</span> <span class="n">sys</span><span class="o">::</span><span class="n">errc</span><span class="o">::</span><span class="n">not_enough_memory</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="c1">// Z_MEM_ERROR, or ENOMEM</span>
<span class="p">}</span></code></pre>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="usage_defining_library_specific_error_codes">Defining Library-Specific Error Codes</h3>
<div class="paragraph">
<p>Let&#8217;s suppose that we are writing a library <code>libmyimg</code> for reading some
hypothetical image format, and that we have defined the following API
function for that:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">libmyimg</span>
<span class="p">{</span>

<span class="k">struct</span> <span class="nc">image</span><span class="p">;</span>

<span class="kt">void</span> <span class="n">load_image</span><span class="p">(</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">f</span><span class="p">,</span> <span class="n">image</span><span class="o">&amp;</span> <span class="n">im</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">);</span>

<span class="p">}</span> <span class="c1">// namespace libmyimg</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>(using our portable <code>file</code> class from the preceding examples.)</p>
</div>
<div class="paragraph">
<p>Our hypothetical image format is simple, consisting of a fixed header,
followed by the image data, so an implementation of <code>load_image</code> might
have the following structure:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">libmyimg</span>
<span class="p">{</span>

<span class="k">struct</span> <span class="nc">image_header</span>
<span class="p">{</span>
    <span class="kt">uint32_t</span> <span class="n">signature</span><span class="p">;</span>
    <span class="kt">uint32_t</span> <span class="n">width</span><span class="p">;</span>
    <span class="kt">uint32_t</span> <span class="n">height</span><span class="p">;</span>
    <span class="kt">uint32_t</span> <span class="n">bits_per_pixel</span><span class="p">;</span>
    <span class="kt">uint32_t</span> <span class="n">channels</span><span class="p">;</span>
<span class="p">};</span>

<span class="kt">void</span> <span class="n">load_image_header</span><span class="p">(</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">f</span><span class="p">,</span> <span class="n">image_header</span><span class="o">&amp;</span> <span class="n">im</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">);</span>

<span class="k">struct</span> <span class="nc">image</span><span class="p">;</span>

<span class="kt">void</span> <span class="n">load_image</span><span class="p">(</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">f</span><span class="p">,</span> <span class="n">image</span><span class="o">&amp;</span> <span class="n">im</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">image_header</span> <span class="n">ih</span> <span class="o">=</span> <span class="p">{};</span>
    <span class="n">load_image_header</span><span class="p">(</span> <span class="n">f</span><span class="p">,</span> <span class="n">ih</span><span class="p">,</span> <span class="n">ec</span> <span class="p">);</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ec</span><span class="p">.</span><span class="n">failed</span><span class="p">()</span> <span class="p">)</span> <span class="k">return</span><span class="p">;</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ih</span><span class="p">.</span><span class="n">signature</span> <span class="o">!=</span> <span class="mh">0xFF0AD71A</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="c1">// return an "invalid signature" error</span>
    <span class="p">}</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ih</span><span class="p">.</span><span class="n">width</span> <span class="o">==</span> <span class="mi">0</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="c1">// return an "invalid width" error</span>
    <span class="p">}</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ih</span><span class="p">.</span><span class="n">height</span> <span class="o">==</span> <span class="mi">0</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="c1">// return an "invalid height" error</span>
    <span class="p">}</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ih</span><span class="p">.</span><span class="n">bits_per_pixel</span> <span class="o">!=</span> <span class="mi">8</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="c1">// return an "unsupported bit depth" error</span>
    <span class="p">}</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ih</span><span class="p">.</span><span class="n">channels</span> <span class="o">!=</span> <span class="mi">1</span> <span class="o">&amp;&amp;</span> <span class="n">ih</span><span class="p">.</span><span class="n">channels</span> <span class="o">!=</span> <span class="mi">3</span> <span class="o">&amp;&amp;</span> <span class="n">ih</span><span class="p">.</span><span class="n">channels</span> <span class="o">!=</span> <span class="mi">4</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="c1">// return an "unsupported channel count" error</span>
    <span class="p">}</span>

    <span class="c1">// initialize `im` and read image data</span>

    <span class="c1">// ...</span>
<span class="p">}</span>

<span class="p">}</span> <span class="c1">// namespace libmyimg</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>We can see that we need to define five error codes of our own. (Our function
can also return other kinds of failures in <code>ec</code>&#8201;&#8212;&#8201;those will come from
<code>file::read</code> which <code>load_image_header</code> will use to read the header.)</p>
</div>
<div class="paragraph">
<p>To define these errors, we&#8217;ll use a scoped enumeration type. (This example
will take advantage of C&#43;&#43;11 features.)</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">libmyimg</span>
<span class="p">{</span>

<span class="k">enum</span> <span class="k">class</span> <span class="nc">error</span>
<span class="p">{</span>
    <span class="n">success</span> <span class="o">=</span> <span class="mi">0</span><span class="p">,</span>

    <span class="n">invalid_signature</span><span class="p">,</span>
    <span class="n">invalid_width</span><span class="p">,</span>
    <span class="n">invalid_height</span><span class="p">,</span>
    <span class="n">unsupported_bit_depth</span><span class="p">,</span>
    <span class="n">unsupported_channel_count</span>
<span class="p">};</span>

<span class="p">}</span> <span class="c1">// namespace libmyimg</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Boost.System supports being told that an enumeration type represents an error
code, which enables implicit conversions between the enumeration type and
<code>error_code</code>. It&#8217;s done by specializing the <code>is_error_code_enum</code> type trait,
which resides in <code>namespace boost::system</code> like the rest of the library:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span>
<span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span>
<span class="p">{</span>

<span class="k">template</span><span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_error_code_enum</span><span class="o">&lt;</span> <span class="o">::</span><span class="n">libmyimg</span><span class="o">::</span><span class="n">error</span> <span class="o">&gt;:</span> <span class="n">std</span><span class="o">::</span><span class="n">true_type</span>
<span class="p">{</span>
<span class="p">};</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Once this is in place, we can now assign values of <code>libmyimg::error</code> to
<code>sys::error_code</code>, which enables the implementation of <code>load_image</code> to be
written as follows:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">void</span> <span class="nf">load_image</span><span class="p">(</span> <span class="n">file</span><span class="o">&amp;</span> <span class="n">f</span><span class="p">,</span> <span class="n">image</span><span class="o">&amp;</span> <span class="n">im</span><span class="p">,</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="n">image_header</span> <span class="n">ih</span> <span class="o">=</span> <span class="p">{};</span>
    <span class="n">load_image_header</span><span class="p">(</span> <span class="n">f</span><span class="p">,</span> <span class="n">ih</span><span class="p">,</span> <span class="n">ec</span> <span class="p">);</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ec</span><span class="p">.</span><span class="n">failed</span><span class="p">()</span> <span class="p">)</span> <span class="k">return</span><span class="p">;</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ih</span><span class="p">.</span><span class="n">signature</span> <span class="o">!=</span> <span class="mh">0xFF0AD71A</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span> <span class="o">=</span> <span class="n">error</span><span class="o">::</span><span class="n">invalid_signature</span><span class="p">;</span>
        <span class="k">return</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ih</span><span class="p">.</span><span class="n">width</span> <span class="o">==</span> <span class="mi">0</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span> <span class="o">=</span> <span class="n">error</span><span class="o">::</span><span class="n">invalid_width</span><span class="p">;</span>
        <span class="k">return</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ih</span><span class="p">.</span><span class="n">height</span> <span class="o">==</span> <span class="mi">0</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span> <span class="o">=</span> <span class="n">error</span><span class="o">::</span><span class="n">invalid_height</span><span class="p">;</span>
        <span class="k">return</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ih</span><span class="p">.</span><span class="n">bits_per_pixel</span> <span class="o">!=</span> <span class="mi">8</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span> <span class="o">=</span> <span class="n">error</span><span class="o">::</span><span class="n">unsupported_bit_depth</span><span class="p">;</span>
        <span class="k">return</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ih</span><span class="p">.</span><span class="n">channels</span> <span class="o">!=</span> <span class="mi">1</span> <span class="o">&amp;&amp;</span> <span class="n">ih</span><span class="p">.</span><span class="n">channels</span> <span class="o">!=</span> <span class="mi">3</span> <span class="o">&amp;&amp;</span> <span class="n">ih</span><span class="p">.</span><span class="n">channels</span> <span class="o">!=</span> <span class="mi">4</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="n">ec</span> <span class="o">=</span> <span class="n">error</span><span class="o">::</span><span class="n">unsupported_channel_count</span><span class="p">;</span>
        <span class="k">return</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="c1">// initialize `image` and read image data</span>

    <span class="c1">// ...</span>
<span class="p">}</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This is however not enough; we still need to define the error category
for our enumerators, and associate them with it.</p>
</div>
<div class="paragraph">
<p>The first step follows our previous two examples very closely:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">libmyimg</span>
<span class="p">{</span>

<span class="k">class</span> <span class="nc">myimg_category_impl</span><span class="o">:</span> <span class="k">public</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_category</span>
<span class="p">{</span>
<span class="nl">public:</span>

    <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">name</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span><span class="p">;</span>
    <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
<span class="p">};</span>

<span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">myimg_category_impl</span><span class="o">::</span><span class="n">name</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span>
<span class="p">{</span>
    <span class="k">return</span> <span class="s">"libmyimg"</span><span class="p">;</span>
<span class="p">}</span>

<span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">myimg_category_impl</span><span class="o">::</span><span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span>
<span class="p">{</span>
    <span class="kt">char</span> <span class="n">buffer</span><span class="p">[</span> <span class="mi">64</span> <span class="p">];</span>
    <span class="k">return</span> <span class="k">this</span><span class="o">-&gt;</span><span class="n">message</span><span class="p">(</span> <span class="n">ev</span><span class="p">,</span> <span class="n">buffer</span><span class="p">,</span> <span class="k">sizeof</span><span class="p">(</span> <span class="n">buffer</span> <span class="p">)</span> <span class="p">);</span>
<span class="p">}</span>

<span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">myimg_category_impl</span><span class="o">::</span><span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span>
<span class="p">{</span>
    <span class="k">switch</span><span class="p">(</span> <span class="k">static_cast</span><span class="o">&lt;</span><span class="n">error</span><span class="o">&gt;</span><span class="p">(</span> <span class="n">ev</span> <span class="p">)</span> <span class="p">)</span>
    <span class="p">{</span>
    <span class="k">case</span> <span class="n">error</span><span class="o">::</span><span class="n">success</span><span class="p">:</span>                   <span class="k">return</span> <span class="s">"No error"</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">error</span><span class="o">::</span><span class="n">invalid_signature</span><span class="p">:</span>         <span class="k">return</span> <span class="s">"Invalid image signature"</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">error</span><span class="o">::</span><span class="n">invalid_width</span><span class="p">:</span>             <span class="k">return</span> <span class="s">"Invalid image width"</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">error</span><span class="o">::</span><span class="n">invalid_height</span><span class="p">:</span>            <span class="k">return</span> <span class="s">"Invalid image height"</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">error</span><span class="o">::</span><span class="n">unsupported_bit_depth</span><span class="p">:</span>     <span class="k">return</span> <span class="s">"Unsupported bit depth"</span><span class="p">;</span>
    <span class="k">case</span> <span class="n">error</span><span class="o">::</span><span class="n">unsupported_channel_count</span><span class="p">:</span> <span class="k">return</span> <span class="s">"Unsupported number of channels"</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="n">std</span><span class="o">::</span><span class="n">snprintf</span><span class="p">(</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">len</span><span class="p">,</span> <span class="s">"Unknown libmyimg error %d"</span><span class="p">,</span> <span class="n">ev</span> <span class="p">);</span>
    <span class="k">return</span> <span class="n">buffer</span><span class="p">;</span>
<span class="p">}</span>

<span class="n">sys</span><span class="o">::</span><span class="n">error_category</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">myimg_category</span><span class="p">()</span>
<span class="p">{</span>
    <span class="k">static</span> <span class="k">const</span> <span class="n">myimg_category_impl</span> <span class="n">instance</span><span class="p">;</span>
    <span class="k">return</span> <span class="n">instance</span><span class="p">;</span>
<span class="p">}</span>

<span class="p">}</span> <span class="c1">// namespace libmyimg</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The second step involves implementing a function <code>make_error_code</code> in
the namespace of our enumeration type <code>error</code> that takes <code>error</code> and
returns <code>boost::system::error_code</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">libmyimg</span>
<span class="p">{</span>

<span class="n">sys</span><span class="o">::</span><span class="n">error_code</span> <span class="n">make_error_code</span><span class="p">(</span> <span class="n">error</span> <span class="n">e</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">return</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_code</span><span class="p">(</span> <span class="k">static_cast</span><span class="o">&lt;</span><span class="kt">int</span><span class="o">&gt;</span><span class="p">(</span> <span class="n">e</span> <span class="p">),</span> <span class="n">myimg_category</span><span class="p">()</span> <span class="p">);</span>
<span class="p">}</span>

<span class="p">}</span> <span class="c1">// namespace libmyimg</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Now <code>load_image</code> will compile, and we just need to fill the rest of its
implementation with code that uses <code>file::read</code> to read the image data.</p>
</div>
<div class="paragraph">
<p>There&#8217;s one additional embellishment we can make. As we know, Boost.System
was proposed for, and accepted into, the C&#43;&#43;11 standard, and now there&#8217;s
a standard implementation of it in <code>&lt;system_error&gt;</code>. We can make our
error enumeration type compatible with <code>std::error_code</code> as well, by
specializing the standard type trait <code>std::is_error_code_enum</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">std</span>
<span class="p">{</span>

<span class="k">template</span><span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_error_code_enum</span><span class="o">&lt;</span> <span class="o">::</span><span class="n">libmyimg</span><span class="o">::</span><span class="n">error</span> <span class="o">&gt;:</span> <span class="n">std</span><span class="o">::</span><span class="n">true_type</span>
<span class="p">{</span>
<span class="p">};</span>

<span class="p">}</span> <span class="c1">// namespace std</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>This makes our enumerators convertible to <code>std::error_code</code>.</p>
</div>
<div class="paragraph">
<p>(The reason this works is that <code>boost::system::error_code</code> is convertible
to <code>std::error_code</code>, so the return value of our <code>make_error_code</code> overload
can be used to initialize a <code>std::error_code</code>.)</p>
</div>
</div>
<div class="sect2">
<h3 id="usage_defining_library_specific_error_conditions">Defining Library-Specific Error Conditions</h3>
<div class="paragraph">
<p>All of the <code>libmyimg::error</code> error codes we have so far represent the same
error condition - invalid or unsupported image format. It might make sense to
enable testing for this condition without the need to enumerate all five
specific codes. To do this, we can define an error condition enumeration type:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">libmyimg</span>
<span class="p">{</span>

<span class="k">enum</span> <span class="k">class</span> <span class="nc">condition</span>
<span class="p">{</span>
    <span class="n">invalid_format</span> <span class="o">=</span> <span class="mi">1</span>
<span class="p">};</span>

<span class="p">}</span> <span class="c1">// namespace libmyimg</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>which we can tag as representing an error condition by specializing
<code>is_error_condition_enum</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span>
<span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span>
<span class="p">{</span>

<span class="k">template</span><span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_error_condition_enum</span><span class="o">&lt;</span> <span class="o">::</span><span class="n">libmyimg</span><span class="o">::</span><span class="n">condition</span> <span class="o">&gt;:</span> <span class="n">std</span><span class="o">::</span><span class="n">true_type</span>
<span class="p">{</span>
<span class="p">};</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span>

<span class="k">namespace</span> <span class="n">std</span>
<span class="p">{</span>

<span class="k">template</span><span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_error_condition_enum</span><span class="o">&lt;</span> <span class="o">::</span><span class="n">libmyimg</span><span class="o">::</span><span class="n">condition</span> <span class="o">&gt;:</span> <span class="n">std</span><span class="o">::</span><span class="n">true_type</span>
<span class="p">{</span>
<span class="p">};</span>

<span class="p">}</span> <span class="c1">// namespace std</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Similarly to the error code enumeration type, which needed a <code>make_error_code</code>
overload, this one will need to have a <code>make_error_condition</code> overload, and a
category.</p>
</div>
<div class="paragraph">
<p>It&#8217;s in principle possible to reuse the category we already defined for our
error codes, by making the condition values start from, say, 10000 instead of
1. This saves some typing, but a better practice is to use a separate category
for the error conditions. So that&#8217;s what we&#8217;ll do:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">libmyimg</span>
<span class="p">{</span>

<span class="k">class</span> <span class="nc">myimg_condition_category_impl</span><span class="o">:</span> <span class="k">public</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_category</span>
<span class="p">{</span>
<span class="nl">public:</span>

    <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">name</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span><span class="p">;</span>
    <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
<span class="p">};</span>

<span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">myimg_condition_category_impl</span><span class="o">::</span><span class="n">name</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span>
<span class="p">{</span>
    <span class="k">return</span> <span class="s">"libmyimg_condition"</span><span class="p">;</span>
<span class="p">}</span>

<span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">myimg_condition_category_impl</span><span class="o">::</span><span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span>
<span class="p">{</span>
    <span class="kt">char</span> <span class="n">buffer</span><span class="p">[</span> <span class="mi">64</span> <span class="p">];</span>
    <span class="k">return</span> <span class="k">this</span><span class="o">-&gt;</span><span class="n">message</span><span class="p">(</span> <span class="n">ev</span><span class="p">,</span> <span class="n">buffer</span><span class="p">,</span> <span class="k">sizeof</span><span class="p">(</span> <span class="n">buffer</span> <span class="p">)</span> <span class="p">);</span>
<span class="p">}</span>

<span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">myimg_condition_category_impl</span><span class="o">::</span><span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span>
<span class="p">{</span>
    <span class="k">switch</span><span class="p">(</span> <span class="k">static_cast</span><span class="o">&lt;</span><span class="n">condition</span><span class="o">&gt;</span><span class="p">(</span> <span class="n">ev</span> <span class="p">)</span> <span class="p">)</span>
    <span class="p">{</span>
    <span class="k">case</span> <span class="n">condition</span><span class="o">::</span><span class="n">invalid_format</span><span class="p">:</span> <span class="k">return</span> <span class="s">"Invalid or unsupported image format"</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="n">std</span><span class="o">::</span><span class="n">snprintf</span><span class="p">(</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">len</span><span class="p">,</span> <span class="s">"Unknown libmyimg condition %d"</span><span class="p">,</span> <span class="n">ev</span> <span class="p">);</span>
    <span class="k">return</span> <span class="n">buffer</span><span class="p">;</span>
<span class="p">}</span>

<span class="n">sys</span><span class="o">::</span><span class="n">error_category</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">myimg_condition_category</span><span class="p">()</span>
<span class="p">{</span>
    <span class="k">static</span> <span class="k">const</span> <span class="n">myimg_condition_category_impl</span> <span class="n">instance</span><span class="p">;</span>
    <span class="k">return</span> <span class="n">instance</span><span class="p">;</span>
<span class="p">}</span>

<span class="n">sys</span><span class="o">::</span><span class="n">error_condition</span> <span class="n">make_error_condition</span><span class="p">(</span> <span class="n">condition</span> <span class="n">e</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="k">return</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_condition</span><span class="p">(</span> <span class="k">static_cast</span><span class="o">&lt;</span><span class="kt">int</span><span class="o">&gt;</span><span class="p">(</span> <span class="n">e</span> <span class="p">),</span> <span class="n">myimg_condition_category</span><span class="p">()</span> <span class="p">);</span>
<span class="p">}</span>

<span class="p">}</span> <span class="c1">// namespace libmyimg</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>We have our condition, but it doesn&#8217;t do anything yet. To enable
<code>libmyimg::condition::invalid_format</code> to compare equal to our error codes,
we need to implement <code>default_error_condition</code> in the error code category:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">libmyimg</span>
<span class="p">{</span>

<span class="k">class</span> <span class="nc">myimg_category_impl</span><span class="o">:</span> <span class="k">public</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_category</span>
<span class="p">{</span>
<span class="nl">public:</span>

    <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">name</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span><span class="p">;</span>
    <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="n">sys</span><span class="o">::</span><span class="n">error_condition</span> <span class="n">default_error_condition</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
<span class="p">};</span>

<span class="n">sys</span><span class="o">::</span><span class="n">error_condition</span> <span class="n">myimg_category_impl</span><span class="o">::</span><span class="n">default_error_condition</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span>
<span class="p">{</span>
    <span class="k">switch</span><span class="p">(</span> <span class="k">static_cast</span><span class="o">&lt;</span><span class="n">error</span><span class="o">&gt;</span><span class="p">(</span> <span class="n">ev</span> <span class="p">)</span> <span class="p">)</span>
    <span class="p">{</span>
    <span class="k">case</span> <span class="n">error</span><span class="o">::</span><span class="n">success</span><span class="p">:</span>

        <span class="k">return</span> <span class="p">{};</span>

    <span class="k">case</span> <span class="n">error</span><span class="o">::</span><span class="n">invalid_signature</span><span class="p">:</span>
    <span class="k">case</span> <span class="n">error</span><span class="o">::</span><span class="n">invalid_width</span><span class="p">:</span>
    <span class="k">case</span> <span class="n">error</span><span class="o">::</span><span class="n">invalid_height</span><span class="p">:</span>
    <span class="k">case</span> <span class="n">error</span><span class="o">::</span><span class="n">unsupported_bit_depth</span><span class="p">:</span>
    <span class="k">case</span> <span class="n">error</span><span class="o">::</span><span class="n">unsupported_channel_count</span><span class="p">:</span>

        <span class="k">return</span> <span class="n">condition</span><span class="o">::</span><span class="n">invalid_format</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="k">return</span> <span class="n">sys</span><span class="o">::</span><span class="n">error_condition</span><span class="p">(</span> <span class="n">ev</span><span class="p">,</span> <span class="o">*</span><span class="k">this</span> <span class="p">);</span>
<span class="p">}</span>

<span class="p">}</span> <span class="c1">// namespace libmyimg</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>That&#8217;s it; now <code>ec == libmyimg::condition::invalid_format</code> can be used to test
whether <code>ec</code> contains one of our error codes corresponding to the "invalid
image format" condition.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="changes">Revision History</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="changes_in_boost_1_81">Changes in Boost 1.81</h3>
<div class="ulist">
<ul>
<li>
<p>The macro <code>BOOST_SYSTEM_DISABLE_THREADS</code> can now be defined to disable
the use of <code>&lt;mutex&gt;</code> (e.g. on single-threaded libstdc++).</p>
</li>
<li>
<p>Added <code>value_type</code>, <code>error_type</code>, <code>in_place_value</code>, <code>in_place_error</code>
to <code>result&lt;&gt;</code>.</p>
</li>
<li>
<p>Added <code>emplace</code> to <code>result&lt;&gt;</code>.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_boost_1_80">Changes in Boost 1.80</h3>
<div class="ulist">
<ul>
<li>
<p>When an <code>error_code</code> is converted to <code>std::error_code</code> and then back
to <code>error_code</code>, the original is now restored, if possible.</p>
</li>
<li>
<p>Reworked the conversion from <code>error_category</code> to <code>std::error_category</code>
to avoid the one-time allocation that shows up on leak checkers.</p>
</li>
<li>
<p>Added a constructor that allows replacing the source location of an
<code>error_code</code>, and a corresponding <code>assign</code>.</p>
</li>
<li>
<p>Added a converting constructor to <code>result</code>.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_boost_1_79">Changes in Boost 1.79</h3>
<div class="ulist">
<ul>
<li>
<p>Added a <code>boost::source_location</code> parameter to <code>throw_exception_from_error</code>.</p>
</li>
<li>
<p>Added <code>throw_exception_from_error</code> overloads for <code>errc::errc_t</code>,
<code>std::error_code</code>, <code>std::errc</code>, <code>std::exception_ptr</code>.</p>
</li>
<li>
<p><code>result&lt;T&gt;::value</code> now automatically supplies <code>BOOST_CURRENT_LOCATION</code> to
<code>throw_exception_from_error</code> via a default argument.</p>
</li>
<li>
<p>Added an <code>errc::make_error_code</code> overload taking a source location.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_boost_1_78">Changes in Boost 1.78</h3>
<div class="ulist">
<ul>
<li>
<p>Added support for source locations to <code>error_code</code>.</p>
</li>
<li>
<p>Added <code>error_code::to_string</code>, <code>error_condition::to_string</code>, <code>error_code::what</code>.</p>
</li>
<li>
<p><code>system_error::what()</code> now contains the source location, if present.</p>
</li>
<li>
<p>Added <code>result&lt;T, E = error_code&gt;</code>, a class holding either a value or an
error, defined in <code>&lt;boost/system/result.hpp&gt;</code>.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_boost_1_77">Changes in Boost 1.77</h3>
<div class="ulist">
<ul>
<li>
<p>The conversion operator from <code>error_category</code> to <code>std::error_category</code>
has been improved and no longer requires <code>&lt;map&gt;</code> or <code>&lt;mutex&gt;</code>.</p>
</li>
<li>
<p>The comparison operators of <code>error_category</code> are now inline friends
instead of member functions (a side effect of the previous change.)</p>
</li>
<li>
<p><code>error_condition</code> now defers calling <code>generic_category()</code> to avoid
instantiating the object until it&#8217;s actually needed.</p>
</li>
<li>
<p><code>error_condition::failed</code> and <code>error_condition::message</code> have been
undeprecated, and <code>operator bool()</code> now once again returns <code>failed()</code>.</p>
</li>
<li>
<p>The system category now doesn&#8217;t call <code>generic_category()</code>, to avoid
instantiating the object.</p>
</li>
<li>
<p>The return value of <code>default_error_condition</code> changes in some cases into
an <code>error_condition</code> from the generic category, instead of from the system
category. This happens on POSIX when the input <code>error_code</code> is from
the system category and does not correspond to any <code>errc_t</code> value.</p>
</li>
<li>
<p>The interoperability of <code>error_code</code> and <code>std::error_code</code> has been
improved substantially. It is now possible to construct
<code>boost::system::error_code</code> from <code>std::error_code</code>, and it&#8217;s possible
to pass <code>boost::system::error_code</code> to functions taking <code>std::error_code&amp;</code>.</p>
</li>
<li>
<p>A stream insertion operator for <code>error_condition</code> has been added.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_boost_1_76">Changes in Boost 1.76</h3>
<div class="ulist">
<ul>
<li>
<p><code>windows_error.hpp</code> is no longer deprecated.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_boost_1_75">Changes in Boost 1.75</h3>
<div class="ulist">
<ul>
<li>
<p>The platform-specific headers <code>windows_error.hpp</code>, <code>linux_error.hpp</code>,
and <code>cygwin_error.hpp</code> emit deprecation messages and are slated for
removal.</p>
</li>
<li>
<p>The old names for <code>generic_category()</code> and <code>system_category()</code> emit
deprecation messages and are slated for removal.</p>
</li>
<li>
<p><code>error_condition::failed</code> is deprecated and is slated for removal.
<code>operator bool()</code> for <code>error_condition</code> has been reverted to its old
meaning of <code>value() != 0</code>. This is done for compatibility with
<code>std::error_condition</code> as the next release is expected to improve
interoperability with <code>&lt;system_error&gt;</code> even further. <em>Note that this
does not affect</em> <code>error_code::failed</code>, which is still alive and well.</p>
</li>
<li>
<p>The overload of <code>error_condition::message</code> that takes a buffer is
deprecated and is slated for removal, for the same reasons. <em>Note that
this does not affect</em> <code>error_code::message</code>.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_boost_1_74">Changes in Boost 1.74</h3>
<div class="ulist">
<ul>
<li>
<p><code>operator bool()</code> now returns <code>failed()</code> instead of <code>value() != 0</code>.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_boost_1_69">Changes in Boost 1.69</h3>
<div class="ulist">
<ul>
<li>
<p>Boost.System is now header-only. A stub library is still built for
compatibility, but linking to it is no longer necessary.</p>
</li>
<li>
<p>Even more functions have been marked <code>constexpr</code>.</p>
</li>
<li>
<p>The destructor of <code>error_category</code> is now protected and no longer
virtual. This is a <em>potentially breaking change</em> but its impact
is expected to be limited.</p>
</li>
<li>
<p><code>error_category</code> now has a constructor that accepts a 64 bit identifier,
enabling distinct category objects to compare equal.</p>
</li>
<li>
<p>The constructors of <code>error_category</code> are now protected.</p>
</li>
<li>
<p>A non-allocating, nonthrowing overload of <code>message</code> has been added.</p>
</li>
<li>
<p>A virtual function <code>failed</code> has been added, allowing categories for
which success is not synonymous with 0.</p>
</li>
<li>
<p>The deprecated <code>boost::system::throws</code> object has been removed.</p>
</li>
<li>
<p><code>boost::throws()</code> is now deprecated and its use is discouraged.</p>
</li>
<li>
<p>The constructor of <code>system_error</code> taking a single <code>error_code</code> argument
is now explicit.</p>
</li>
<li>
<p><code>system_error::code()</code> now returns by value.</p>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_boost_1_68">Changes in Boost 1.68</h3>
<div class="paragraph">
<p>On a C&#43;&#43;14 compiler, many Boost.System functions and member functions
are now <code>constexpr</code>, and <code>error_code</code> and <code>error_condition</code> are literal
classes.</p>
</div>
<div class="paragraph">
<p>In addition to enabling use in constant expressions (and <code>constexpr</code>
functions), this significantly improves the quality of the generated code.</p>
</div>
<div class="paragraph">
<p>As a result of this change, however, now using Boost.System from C&#43;&#43;14
or C&#43;&#43;17 code requires that the library be also built with C&#43;&#43;14 or
above. This is the default on GCC 6 and newer, but not on GCC 5 or Clang.
One can build Boost for C&#43;&#43;14 by passing the <code>cxxstd=14</code> option to <code>b2</code>.</p>
</div>
<div class="paragraph">
<p>(Previous versions allowed code built against any C&#43;&#43; standard to link
with Boost.System built against any C&#43;&#43; standard. In 1.68, code using
any C&#43;&#43; standard can link with Boost.System built with C&#43;&#43;14 or above,
but if Boost.System is built with C&#43;&#43;11 or below, only code also built
with C&#43;&#43;11 and below can link to it successfully.)</p>
</div>
</div>
<div class="sect2">
<h3 id="changes_in_boost_1_65">Changes in Boost 1.65</h3>
<div class="paragraph">
<p>On a C&#43;&#43;11 compiler, Boost.System now provides implicit conversions
from <code>boost::system::error_category</code>, <code>error_code</code>, and <code>error_condition</code>
to their standard equivalents from <code>&lt;system_error&gt;</code>.</p>
</div>
<div class="paragraph">
<p>This allows libraries to expose a C&#43;&#43;11 interface and report errors
via <code>std::error_code</code> even when using Boost.System, directly or through a
dependency such as Boost.ASIO.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="rationale">Design Rationale</h2>
<div class="sectionbody">
<div class="paragraph">
<p><code>error_code</code> and <code>error_condition</code> are designed as value types so
they can be copied without slicing and do not require heap allocation, but
still have polymorphic behavior based on the error category. This is achieved
by abstract base class <code>error_category</code> supplying the polymorphic behavior,
and <code>error_code</code> and <code>error_condition</code> containing a pointer to an object of a
type derived from <code>error_category</code>.</p>
</div>
<div class="paragraph">
<p>Many of the detailed design decisions were driven by the requirements that
users to be able to add additional error categories, and that it be no more
difficult to write portable code than system-specific code.</p>
</div>
<div class="paragraph">
<p>The <code>operator&lt;&lt;</code> overload for <code>error_code</code> eliminates a misleading conversion to
<code>bool</code> in code like <code>cout &lt;&lt; ec</code>, where <code>ec</code> is of type <code>error_code</code>. It is also
useful in its own right.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="reference">Reference</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="ref_use_of_c11_and_c14_features">Use of C&#43;&#43;11 and C&#43;&#43;14 Features</h3>
<div class="paragraph">
<p>The library is documented to use several C&#43;&#43;11 and C&#43;&#43;14 features,
including <code>noexcept</code>, explicit conversion operators and <code>constexpr</code>. The
actual implementation uses C&#43;&#43;11 and C&#43;&#43;14 features only when they are
available, and otherwise falls back on C&#43;&#43;03 features.</p>
</div>
</div>
<div class="sect2">
<h3 id="ref_macros">Macros</h3>
<div class="paragraph">
<p>When <code>BOOST_SYSTEM_ENABLE_DEPRECATED</code> is defined, the library provides
deprecated features for compatibility. These features are bound to eventually
disappear.</p>
</div>
<div class="paragraph">
<p>When <code>BOOST_SYSTEM_USE_UTF8</code> is defined, on Windows the library returns
UTF-8 messages using code page <code>CP_UTF8</code> instead of the default <code>CP_ACP</code>.
This macro has no effect on POSIX.</p>
</div>
<div class="paragraph">
<p>When <code>BOOST_SYSTEM_DISABLE_THREADS</code> is defined, the library assumes that
the current platform doesn&#8217;t support multiple threads and disables the use
of the standard header <code>&lt;mutex&gt;</code>, eliminating the mutex locks. The single
threaded <code>libstdc++</code> is one such platform.</p>
</div>
</div>
<div class="sect2">
<h3 id="ref_deprecated_names">Deprecated Names</h3>
<div class="paragraph">
<p>In the process of adding Boost.System to the C&#43;&#43;11 standard library, the
C&#43;&#43; committee changed some names. To ease transition, Boost.System deprecates
the old names, but will provide them when the macro <code>BOOST_SYSTEM_ENABLE_DEPRECATED</code> is defined.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<colgroup>
<col style="width: 50%;">
<col style="width: 50%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">Old usage, now deprecated</th>
<th class="tableblock halign-left valign-top">Replacement</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>get_generic_category()</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>generic_category()</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>get_system_category()</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>system_category()</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>namespace posix</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>namespace errc</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>namespace posix_error</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>namespace errc</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>get_posix_category()</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>generic_category()</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>posix_category</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>generic_category()</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>errno_ecat</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>generic_category()</code></p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>native_ecat</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>system_category()</code></p></td>
</tr>
</tbody>
</table>
</div>
<div class="sect2">
<h3 id="ref_boostsystemis_error_code_enum_hpp">&lt;boost/system/&#8203;is_error_code_enum.hpp&gt;</h3>
<div class="sect3">
<h4 id="ref_is_error_code_enum">is_error_code_enum</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span> <span class="p">{</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">is_error_code_enum</span> <span class="p">{</span> <span class="k">static</span> <span class="k">const</span> <span class="kt">bool</span> <span class="n">value</span> <span class="o">=</span> <span class="nb">false</span><span class="p">;</span> <span class="p">};</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Users may specialize <code>is_error_code_enum</code> for their error enumeration
types to indicate that they should be eligible for automatic conversions
to <code>error_code</code>. This conversion calls <code>make_error_code(e)</code>, which should
be provided in the same namespace as the enumeration type.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="ref_boostsystemis_error_condition_enum_hpp">&lt;boost/system/&#8203;is_error_condition_enum.hpp&gt;</h3>
<div class="sect3">
<h4 id="ref_is_error_condition_enum">is_error_condition_enum</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span> <span class="p">{</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span>
  <span class="k">struct</span> <span class="nc">is_error_condition_enum</span> <span class="p">{</span> <span class="k">static</span> <span class="k">const</span> <span class="kt">bool</span> <span class="n">value</span> <span class="o">=</span> <span class="nb">false</span><span class="p">;</span> <span class="p">};</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>Users may specialize <code>is_error_condition_enum</code> for their error enumeration
types to indicate that they should be eligible for automatic conversions
to <code>error_condition</code>. This conversion calls <code>make_error_condition(e)</code>, which
should be provided in the same namespace as the enumeration type.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="ref_boostsystemerrc_hpp">&lt;boost/system/&#8203;errc.hpp&gt;</h3>
<div class="sect3">
<h4 id="ref_errc">errc</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span> <span class="p">{</span>

<span class="k">namespace</span> <span class="n">errc</span> <span class="p">{</span>
  <span class="k">enum</span> <span class="n">errc_t</span>
  <span class="p">{</span>
    <span class="n">success</span> <span class="o">=</span> <span class="mi">0</span><span class="p">,</span>
    <span class="n">address_family_not_supported</span><span class="p">,</span>   <span class="c1">//EAFNOSUPPORT</span>
    <span class="n">address_in_use</span><span class="p">,</span>                 <span class="c1">//EADDRINUSE</span>
    <span class="n">address_not_available</span><span class="p">,</span>          <span class="c1">//EADDRNOTAVAIL</span>
    <span class="n">already_connected</span><span class="p">,</span>              <span class="c1">//EISCONN</span>
    <span class="n">argument_list_too_long</span><span class="p">,</span>         <span class="c1">//E2BIG</span>
    <span class="n">argument_out_of_domain</span><span class="p">,</span>         <span class="c1">//EDOM</span>
    <span class="n">bad_address</span><span class="p">,</span>                    <span class="c1">//EFAULT</span>
    <span class="n">bad_file_descriptor</span><span class="p">,</span>            <span class="c1">//EBADF</span>
    <span class="n">bad_message</span><span class="p">,</span>                    <span class="c1">//EBADMSG</span>
    <span class="n">broken_pipe</span><span class="p">,</span>                    <span class="c1">//EPIPE</span>
    <span class="n">connection_aborted</span><span class="p">,</span>             <span class="c1">//ECONNABORTED</span>
    <span class="n">connection_already_in_progress</span><span class="p">,</span> <span class="c1">//EALREADY</span>
    <span class="n">connection_refused</span><span class="p">,</span>             <span class="c1">//ECONNREFUSED</span>
    <span class="n">connection_reset</span><span class="p">,</span>               <span class="c1">//ECONNRESET</span>
    <span class="n">cross_device_link</span><span class="p">,</span>              <span class="c1">//EXDEV</span>
    <span class="n">destination_address_required</span><span class="p">,</span>   <span class="c1">//EDESTADDRREQ</span>
    <span class="n">device_or_resource_busy</span><span class="p">,</span>        <span class="c1">//EBUSY</span>
    <span class="n">directory_not_empty</span><span class="p">,</span>            <span class="c1">//ENOTEMPTY</span>
    <span class="n">executable_format_error</span><span class="p">,</span>        <span class="c1">//ENOEXEC</span>
    <span class="n">file_exists</span><span class="p">,</span>                    <span class="c1">//EEXIST</span>
    <span class="n">file_too_large</span><span class="p">,</span>                 <span class="c1">//EFBIG</span>
    <span class="n">filename_too_long</span><span class="p">,</span>              <span class="c1">//ENAMETOOLONG</span>
    <span class="n">function_not_supported</span><span class="p">,</span>         <span class="c1">//ENOSYS</span>
    <span class="n">host_unreachable</span><span class="p">,</span>               <span class="c1">//EHOSTUNREACH</span>
    <span class="n">identifier_removed</span><span class="p">,</span>             <span class="c1">//EIDRM</span>
    <span class="n">illegal_byte_sequence</span><span class="p">,</span>          <span class="c1">//EILSEQ</span>
    <span class="n">inappropriate_io_control_operation</span><span class="p">,</span> <span class="c1">//ENOTTY</span>
    <span class="n">interrupted</span><span class="p">,</span>                    <span class="c1">//EINTR</span>
    <span class="n">invalid_argument</span><span class="p">,</span>               <span class="c1">//EINVAL</span>
    <span class="n">invalid_seek</span><span class="p">,</span>                   <span class="c1">//ESPIPE</span>
    <span class="n">io_error</span><span class="p">,</span>                       <span class="c1">//EIO</span>
    <span class="n">is_a_directory</span><span class="p">,</span>                 <span class="c1">//EISDIR</span>
    <span class="n">message_size</span><span class="p">,</span>                   <span class="c1">//EMSGSIZE</span>
    <span class="n">network_down</span><span class="p">,</span>                   <span class="c1">//ENETDOWN</span>
    <span class="n">network_reset</span><span class="p">,</span>                  <span class="c1">//ENETRESET</span>
    <span class="n">network_unreachable</span><span class="p">,</span>            <span class="c1">//ENETUNREACH</span>
    <span class="n">no_buffer_space</span><span class="p">,</span>                <span class="c1">//ENOBUFS</span>
    <span class="n">no_child_process</span><span class="p">,</span>               <span class="c1">//ECHILD</span>
    <span class="n">no_link</span><span class="p">,</span>                        <span class="c1">//ENOLINK</span>
    <span class="n">no_lock_available</span><span class="p">,</span>              <span class="c1">//ENOLCK</span>
    <span class="n">no_message_available</span><span class="p">,</span>           <span class="c1">//ENODATA</span>
    <span class="n">no_message</span><span class="p">,</span>                     <span class="c1">//ENOMSG</span>
    <span class="n">no_protocol_option</span><span class="p">,</span>             <span class="c1">//ENOPROTOOPT</span>
    <span class="n">no_space_on_device</span><span class="p">,</span>             <span class="c1">//ENOSPC</span>
    <span class="n">no_stream_resources</span><span class="p">,</span>            <span class="c1">//ENOSR</span>
    <span class="n">no_such_device_or_address</span><span class="p">,</span>      <span class="c1">//ENXIO</span>
    <span class="n">no_such_device</span><span class="p">,</span>                 <span class="c1">//ENODEV</span>
    <span class="n">no_such_file_or_directory</span><span class="p">,</span>      <span class="c1">//ENOENT</span>
    <span class="n">no_such_process</span><span class="p">,</span>                <span class="c1">//ESRCH</span>
    <span class="n">not_a_directory</span><span class="p">,</span>                <span class="c1">//ENOTDIR</span>
    <span class="n">not_a_socket</span><span class="p">,</span>                   <span class="c1">//ENOTSOCK</span>
    <span class="n">not_a_stream</span><span class="p">,</span>                   <span class="c1">//ENOSTR</span>
    <span class="n">not_connected</span><span class="p">,</span>                  <span class="c1">//ENOTCONN</span>
    <span class="n">not_enough_memory</span><span class="p">,</span>              <span class="c1">//ENOMEM</span>
    <span class="n">not_supported</span><span class="p">,</span>                  <span class="c1">//ENOTSUP</span>
    <span class="n">operation_canceled</span><span class="p">,</span>             <span class="c1">//ECANCELED</span>
    <span class="n">operation_in_progress</span><span class="p">,</span>          <span class="c1">//EINPROGRESS</span>
    <span class="n">operation_not_permitted</span><span class="p">,</span>        <span class="c1">//EPERM</span>
    <span class="n">operation_not_supported</span><span class="p">,</span>        <span class="c1">//EOPNOTSUPP</span>
    <span class="n">operation_would_block</span><span class="p">,</span>          <span class="c1">//EWOULDBLOCK</span>
    <span class="n">owner_dead</span><span class="p">,</span>                     <span class="c1">//EOWNERDEAD</span>
    <span class="n">permission_denied</span><span class="p">,</span>              <span class="c1">//EACCES</span>
    <span class="n">protocol_error</span><span class="p">,</span>                 <span class="c1">//EPROTO</span>
    <span class="n">protocol_not_supported</span><span class="p">,</span>         <span class="c1">//EPROTONOSUPPORT</span>
    <span class="n">read_only_file_system</span><span class="p">,</span>          <span class="c1">//EROFS</span>
    <span class="n">resource_deadlock_would_occur</span><span class="p">,</span>  <span class="c1">//EDEADLK</span>
    <span class="n">resource_unavailable_try_again</span><span class="p">,</span> <span class="c1">//EAGAIN</span>
    <span class="n">result_out_of_range</span><span class="p">,</span>            <span class="c1">//ERANGE</span>
    <span class="n">state_not_recoverable</span><span class="p">,</span>          <span class="c1">//ENOTRECOVERABLE</span>
    <span class="n">stream_timeout</span><span class="p">,</span>                 <span class="c1">//ETIME</span>
    <span class="n">text_file_busy</span><span class="p">,</span>                 <span class="c1">//ETXTBSY</span>
    <span class="n">timed_out</span><span class="p">,</span>                      <span class="c1">//ETIMEDOUT</span>
    <span class="n">too_many_files_open_in_system</span><span class="p">,</span>  <span class="c1">//ENFILE</span>
    <span class="n">too_many_files_open</span><span class="p">,</span>            <span class="c1">//EMFILE</span>
    <span class="n">too_many_links</span><span class="p">,</span>                 <span class="c1">//EMLINK</span>
    <span class="n">too_many_symbolic_link_levels</span><span class="p">,</span>  <span class="c1">//ELOOP</span>
    <span class="n">value_too_large</span><span class="p">,</span>                <span class="c1">//EOVERFLOW</span>
    <span class="n">wrong_protocol_type</span>             <span class="c1">//EPROTOTYPE</span>
  <span class="p">};</span>

<span class="p">}</span> <span class="c1">// namespace errc</span>

<span class="k">template</span><span class="o">&lt;</span><span class="p">&gt;</span> <span class="k">struct</span> <span class="nc">is_error_condition_enum</span><span class="o">&lt;</span><span class="n">errc</span><span class="o">::</span><span class="n">errc_t</span><span class="o">&gt;</span>
  <span class="p">{</span> <span class="k">static</span> <span class="k">const</span> <span class="kt">bool</span> <span class="n">value</span> <span class="o">=</span> <span class="nb">true</span><span class="p">;</span> <span class="p">};</span>

<span class="k">constexpr</span> <span class="n">error_condition</span> <span class="n">make_error_condition</span><span class="p">(</span> <span class="n">errc</span><span class="o">::</span><span class="n">errc_t</span> <span class="n">e</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

<span class="k">constexpr</span> <span class="n">error_code</span> <span class="n">make_error_code</span><span class="p">(</span> <span class="n">errc</span><span class="o">::</span><span class="n">errc_t</span> <span class="n">e</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

<span class="n">error_code</span> <span class="n">make_error_code</span><span class="p">(</span> <span class="n">errc</span><span class="o">::</span><span class="n">errc_t</span> <span class="n">e</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">*</span> <span class="n">loc</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="paragraph">
<p>The predefined enumeration type <code>errc::errc_t</code> provides named constants
corresponding to the values of the <code>&lt;cerrno&gt;</code> macros.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">error_condition</span> <span class="n">make_error_condition</span><span class="p">(</span> <span class="n">errc</span><span class="o">::</span><span class="n">errc_t</span> <span class="n">e</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>error_condition( e, generic_category() )</code>.</p>
</dd>
</dl>
</div>
<div class="paragraph">
<p>Since <code>errc::errc_t</code> provides a specialization of <code>is_error_condition_enum</code>
and an overload of <code>make_error_condition</code>, it can be converted implicitly to
an <code>error_condition</code>. This is typically useful when comparing <code>error_code</code>
values returned from APIs to a portable condition, as in the below example:</p>
</div>
</li>
<li>
<p></p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">void</span> <span class="nf">api_function</span><span class="p">(</span> <span class="n">boost</span><span class="o">::</span><span class="n">system</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">);</span>

<span class="kt">void</span> <span class="nf">my_function</span><span class="p">()</span>
<span class="p">{</span>
    <span class="n">boost</span><span class="o">::</span><span class="n">system</span><span class="o">::</span><span class="n">error_code</span> <span class="n">ec</span><span class="p">;</span>
    <span class="n">api_function</span><span class="p">(</span> <span class="n">ec</span> <span class="p">);</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">ec</span> <span class="o">==</span> <span class="n">boost</span><span class="o">::</span><span class="n">system</span><span class="o">::</span><span class="n">errc</span><span class="o">::</span><span class="n">no_such_file_or_directory</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="c1">// an entity wasn't found (ENOENT)</span>
        <span class="c1">// handle this condition</span>
    <span class="p">}</span>
<span class="p">}</span></code></pre>
</div>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">error_code</span> <span class="n">make_error_code</span><span class="p">(</span> <span class="n">errc</span><span class="o">::</span><span class="n">errc_t</span> <span class="n">e</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>error_code( e, generic_category() )</code>.</p>
</dd>
</dl>
</div>
<div class="paragraph">
<p>In addition to <code>make_error_condition</code>, <code>errc::errc_t</code> provides an overload of
<code>make_error_code</code>. This allows the creation of generic error codes, an
operation typically useful when a function needs to signal a generic failure
that does not come from an underlying API, such as for instance an out of
memory condition:</p>
</div>
</li>
<li>
<p></p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">void</span> <span class="nf">my_api_function</span><span class="p">(</span> <span class="n">boost</span><span class="o">::</span><span class="n">system</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="kt">void</span><span class="o">*</span> <span class="n">p</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">malloc</span><span class="p">(</span> <span class="mi">16</span> <span class="p">);</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">p</span> <span class="o">==</span> <span class="mi">0</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="c1">// return ENOMEM</span>
        <span class="n">ec</span> <span class="o">=</span> <span class="n">make_error_code</span><span class="p">(</span> <span class="n">boost</span><span class="o">::</span><span class="n">system</span><span class="o">::</span><span class="n">errc</span><span class="o">::</span><span class="n">out_of_memory</span> <span class="p">);</span>
        <span class="k">return</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="c1">// use p</span>
<span class="p">}</span></code></pre>
</div>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">error_code</span> <span class="n">make_error_code</span><span class="p">(</span> <span class="n">errc</span><span class="o">::</span><span class="n">errc_t</span> <span class="n">e</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">*</span> <span class="n">loc</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>error_code( e, generic_category(), loc )</code>.</p>
</dd>
</dl>
</div>
<div class="paragraph">
<p>Same as the above overload, but takes a source location.</p>
</div>
</li>
<li>
<p></p>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">void</span> <span class="nf">my_api_function</span><span class="p">(</span> <span class="n">boost</span><span class="o">::</span><span class="n">system</span><span class="o">::</span><span class="n">error_code</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span>
<span class="p">{</span>
    <span class="kt">void</span><span class="o">*</span> <span class="n">p</span> <span class="o">=</span> <span class="n">std</span><span class="o">::</span><span class="n">malloc</span><span class="p">(</span> <span class="mi">16</span> <span class="p">);</span>

    <span class="k">if</span><span class="p">(</span> <span class="n">p</span> <span class="o">==</span> <span class="mi">0</span> <span class="p">)</span>
    <span class="p">{</span>
        <span class="c1">// return ENOMEM</span>

        <span class="n">BOOST_STATIC_CONSTEXPR</span> <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="n">loc</span> <span class="o">=</span>
          <span class="n">BOOST_CURRENT_LOCATION</span><span class="p">;</span>

        <span class="n">ec</span> <span class="o">=</span> <span class="n">make_error_code</span><span class="p">(</span> <span class="n">boost</span><span class="o">::</span><span class="n">system</span><span class="o">::</span><span class="n">errc</span><span class="o">::</span><span class="n">out_of_memory</span><span class="p">,</span> <span class="o">&amp;</span><span class="n">loc</span> <span class="p">);</span>
        <span class="k">return</span><span class="p">;</span>
    <span class="p">}</span>

    <span class="c1">// use p</span>
<span class="p">}</span></code></pre>
</div>
</div>
</li>
</ul>
</div>
</div>
</div>
<div class="sect2">
<h3 id="ref_boostsystemerror_category_hpp">&lt;boost/system/&#8203;error_category.hpp&gt;</h3>
<div class="sect3">
<h4 id="ref_error_category">error_category</h4>
<div class="paragraph">
<p>The class <code>error_category</code> defines the base class for types used
to identify the source and encoding of a particular category of error code.</p>
</div>
<div class="paragraph">
<p>Classes may be derived from <code>error_category</code> to support categories of
errors in addition to those defined in Boost.System.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span> <span class="p">{</span>

<span class="k">class</span> <span class="nc">error_category</span>
<span class="p">{</span>
<span class="nl">public:</span> <span class="c1">// noncopyable</span>

    <span class="n">error_category</span><span class="p">(</span> <span class="n">error_category</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="p">)</span> <span class="o">=</span> <span class="k">delete</span><span class="p">;</span>
    <span class="n">error_category</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span> <span class="n">error_category</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="p">)</span> <span class="o">=</span> <span class="k">delete</span><span class="p">;</span>

<span class="nl">protected:</span>

    <span class="o">~</span><span class="n">error_category</span><span class="p">()</span> <span class="o">=</span> <span class="k">default</span><span class="p">;</span>

    <span class="k">constexpr</span> <span class="n">error_category</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">explicit</span> <span class="k">constexpr</span> <span class="n">error_category</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">long</span> <span class="kt">long</span> <span class="n">id</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

<span class="nl">public:</span>

    <span class="k">virtual</span> <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">name</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>

    <span class="k">virtual</span> <span class="n">error_condition</span> <span class="n">default_error_condition</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">virtual</span> <span class="kt">bool</span> <span class="n">equivalent</span><span class="p">(</span> <span class="kt">int</span> <span class="n">code</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span> <span class="p">)</span>
      <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">virtual</span> <span class="kt">bool</span> <span class="n">equivalent</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span><span class="p">,</span> <span class="kt">int</span> <span class="n">condition</span> <span class="p">)</span>
      <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">virtual</span> <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
    <span class="k">virtual</span> <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span>
      <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">virtual</span> <span class="kt">bool</span> <span class="n">failed</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&lt;</span> <span class="p">(</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">operator</span> <span class="n">std</span><span class="o">::</span><span class="n">error_category</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="p">()</span> <span class="k">const</span><span class="p">;</span>

<span class="nl">private:</span>

    <span class="kt">unsigned</span> <span class="kt">long</span> <span class="kt">long</span> <span class="n">id_</span><span class="p">;</span> <span class="c1">// exposition only</span>
<span class="p">};</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="sect4">
<h5 id="ref_constructors">Constructors</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">error_category</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Initializes <code>id_</code> to 0.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>Since equivalence for categories that do not have an identifier is
based on comparing object addresses, a user-defined derived category of type
<code>C</code> that uses this constructor should ensure that only one object of type <code>C</code>
exists in the program.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">explicit</span> <span class="k">constexpr</span> <span class="n">error_category</span><span class="p">(</span> <span class="kt">unsigned</span> <span class="kt">long</span> <span class="kt">long</span> <span class="n">id</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Initializes <code>id_</code> to <code>id</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>User-defined derived categories that use this constructor are considered
equivalent when their identifiers match. Therefore, those categories may have more
than one instance existing in a program, but to minimize the possibility of
collision, their identifiers must be randomly chosen (at the time the category
is implemented, not at runtime). One way of generating a 64 bit random identifier
is <a href="https://www.random.org/cgi-bin/randbyte?nbytes=8&amp;format=h" class="bare">https://www.random.org/cgi-bin/randbyte?nbytes=8&amp;format=h</a>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_virtuals">Virtuals</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">virtual</span> <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">name</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>In derived classes, a character literal naming the error category.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">virtual</span> <span class="n">error_condition</span> <span class="n">default_error_condition</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>In derived classes, an error condition corresponding to <code>ev</code>.
The returned error condition will typically come from the generic category.</p>
</li>
<li>
<p>In the default implementation, <code>error_condition( ev, *this )</code>.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">virtual</span> <span class="kt">bool</span> <span class="n">equivalent</span><span class="p">(</span> <span class="kt">int</span> <span class="n">code</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span> <span class="p">)</span>
  <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>In derived classes, <code>true</code> when <code>error_code( code, *this )</code> is equivalent to <code>condition</code>.</p>
</li>
<li>
<p>In the default implementation, <code>default_error_condition( code ) == condition</code>.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">virtual</span> <span class="kt">bool</span> <span class="n">equivalent</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span><span class="p">,</span> <span class="kt">int</span> <span class="n">condition</span> <span class="p">)</span>
  <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>In derived classes, <code>true</code> when <code>code</code> is equivalent to <code>error_condition( condition, *this )</code>.</p>
</li>
<li>
<p>In the default implementation, <code>*this == code.category() &amp;&amp; code.value() == condition</code>.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">virtual</span> <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>In derived classes, a string that describes the error denoted by <code>ev</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">virtual</span> <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">message</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span>
  <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>Derived classes should either</p>
<div class="ulist">
<ul>
<li>
<p>return a pointer to a character literal describing the error denoted by <code>ev</code>, or</p>
</li>
<li>
<p>copy a string describing the error into <code>buffer</code>, truncating it to <code>len-1</code>
characters and storing a null terminator, and return <code>buffer</code>. If <code>len</code> is 0,
nothing is copied, but the function still returns <code>buffer</code>. Note that
when <code>len</code> is 0, <code>buffer</code> may be <code>nullptr</code>.</p>
</li>
</ul>
</div>
</li>
<li>
<p>The default implementation calls <code>message(ev)</code> and copies the result into
<code>buffer</code>, truncating it to <code>len-1</code> characters and storing a null terminator.
If <code>len</code> is 0, copies nothing. Returns <code>buffer</code>. If <code>message(ev)</code> throws an
exception, the string <code>"Message text unavailable"</code> is used.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Example: </dt>
<dd>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">const</span> <span class="kt">char</span><span class="o">*</span> <span class="n">my_category</span><span class="o">::</span><span class="n">message</span><span class="p">(</span><span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="kt">char</span><span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="kt">size_t</span> <span class="n">len</span><span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span>
<span class="p">{</span>
    <span class="k">switch</span><span class="p">(</span><span class="n">ev</span><span class="p">)</span>
    <span class="p">{</span>
    <span class="k">case</span> <span class="mi">0</span><span class="p">:</span> <span class="k">return</span> <span class="s">"no error"</span><span class="p">;</span>
    <span class="k">case</span> <span class="mi">1</span><span class="p">:</span> <span class="k">return</span> <span class="s">"voltage out of range"</span><span class="p">;</span>
    <span class="k">case</span> <span class="mi">2</span><span class="p">:</span> <span class="k">return</span> <span class="s">"impedance mismatch"</span><span class="p">;</span>

    <span class="k">case</span> <span class="mi">31</span><span class="p">:</span>
    <span class="k">case</span> <span class="mi">32</span><span class="p">:</span>
    <span class="k">case</span> <span class="mi">33</span><span class="p">:</span>

        <span class="n">std</span><span class="o">::</span><span class="n">snprintf</span><span class="p">(</span><span class="n">buffer</span><span class="p">,</span> <span class="n">len</span><span class="p">,</span> <span class="s">"component %d failure"</span><span class="p">,</span> <span class="n">ev</span><span class="o">-</span><span class="mi">30</span><span class="p">);</span>
        <span class="k">return</span> <span class="n">buffer</span><span class="p">;</span>

    <span class="nl">default:</span>

        <span class="n">std</span><span class="o">::</span><span class="n">snprintf</span><span class="p">(</span><span class="n">buffer</span><span class="p">,</span> <span class="n">len</span><span class="p">,</span> <span class="s">"unknown error %d"</span><span class="p">,</span> <span class="n">ev</span><span class="p">);</span>
        <span class="k">return</span> <span class="n">buffer</span><span class="p">;</span>
    <span class="p">}</span>
<span class="p">}</span></code></pre>
</div>
</div>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">virtual</span> <span class="kt">bool</span> <span class="n">failed</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>In derived classes, <code>true</code> when <code>ev</code> represents a failure.</p>
</li>
<li>
<p>In the default implementation, <code>ev != 0</code>.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>All calls to this function with the same <code>ev</code> must return the same value.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_comparisons">Comparisons</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>rhs.id_ == 0? &amp;lhs == &amp;rhs: lhs.id_ == rhs.id_</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>Two category objects are considered equivalent when they have
matching nonzero identifiers, or are the same object.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!( lhs == rhs )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&lt;</span> <span class="p">(</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>If <code>lhs.id_ &lt; rhs.id_</code>, <code>true</code>;</p>
</li>
<li>
<p>Otherwise, if <code>lhs.id_ &gt; rhs.id_</code>, <code>false</code>;</p>
</li>
<li>
<p>Otherwise, if <code>rhs.id_ != 0</code>, <code>false</code>;</p>
</li>
<li>
<p>Otherwise, <code>std::less&lt;error_category const *&gt;()( &amp;lhs, &amp;rhs )</code>.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_conversions">Conversions</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">operator</span> <span class="n">std</span><span class="o">::</span><span class="n">error_category</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="p">()</span> <span class="k">const</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A reference to an <code>std::error_category</code> object corresponding
to <code>*this</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="ref_boostsystemsystem_category_hpp">&lt;boost/system/&#8203;system_category.hpp&gt;</h3>
<div class="sect3">
<h4 id="ref_system_category">system_category</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span> <span class="p">{</span>

<span class="k">constexpr</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">system_category</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">system_category</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A reference to a predefined <code>error_category</code> object identifying
errors originating from the operating system.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
</div>
<div class="sect2">
<h3 id="ref_boostsystemgeneric_category_hpp">&lt;boost/system/&#8203;generic_category.hpp&gt;</h3>
<div class="sect3">
<h4 id="ref_generic_category">generic_category</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span> <span class="p">{</span>

<span class="k">constexpr</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">generic_category</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">generic_category</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A reference to a predefined <code>error_category</code> object identifying
portable error codes and conditions.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
</div>
<div class="sect2">
<h3 id="ref_boostsystemerror_code_hpp">&lt;boost/system/&#8203;error_code.hpp&gt;</h3>
<div class="sect3">
<h4 id="ref_error_code">error_code</h4>
<div class="paragraph">
<p>The class <code>error_code</code> describes an object used to hold error code
values, such as those originating from the operating system or other
low-level application program interfaces. It&#8217;s an adjunct to error reporting
by exception.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span> <span class="p">{</span>

<span class="k">class</span> <span class="nc">error_code</span> <span class="p">{</span>
<span class="nl">public:</span>

    <span class="c1">// constructors</span>

    <span class="k">constexpr</span> <span class="n">error_code</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="n">error_code</span><span class="p">(</span> <span class="kt">int</span> <span class="n">val</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">cat</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="n">error_code</span><span class="p">(</span> <span class="kt">int</span> <span class="n">val</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">cat</span><span class="p">,</span>
      <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">*</span> <span class="n">loc</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">ErrorCodeEnum</span><span class="p">&gt;</span>
      <span class="k">constexpr</span> <span class="n">error_code</span><span class="p">(</span> <span class="n">ErrorCodeEnum</span> <span class="n">e</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="n">error_code</span><span class="p">(</span> <span class="n">error_code</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">ec</span><span class="p">,</span>
      <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">*</span> <span class="n">loc</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="n">error_code</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="c1">// modifiers</span>

    <span class="k">constexpr</span> <span class="kt">void</span> <span class="n">assign</span><span class="p">(</span> <span class="kt">int</span> <span class="n">val</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">cat</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="kt">void</span> <span class="n">assign</span><span class="p">(</span> <span class="kt">int</span> <span class="n">val</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">cat</span><span class="p">,</span>
      <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">*</span> <span class="n">loc</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">ErrorCodeEnum</span><span class="p">&gt;</span>
      <span class="k">constexpr</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span> <span class="n">ErrorCodeEnum</span> <span class="n">e</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="kt">void</span> <span class="n">assign</span><span class="p">(</span> <span class="n">error_code</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">ec</span><span class="p">,</span>
      <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">*</span> <span class="n">loc</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">constexpr</span> <span class="kt">void</span> <span class="n">clear</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="c1">// observers</span>

    <span class="k">constexpr</span> <span class="kt">int</span> <span class="n">value</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">category</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="n">error_condition</span> <span class="n">default_error_condition</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">message</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span>
    <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">message</span><span class="p">(</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">failed</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="k">explicit</span> <span class="k">operator</span> <span class="kt">bool</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="kt">bool</span> <span class="n">has_location</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">location</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="c1">// comparisons</span>

    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&lt;</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span>
      <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span> <span class="n">E</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span>
      <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="n">E</span> <span class="n">lhs</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span>
      <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span> <span class="n">E</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span>
      <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="n">E</span> <span class="n">lhs</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="c1">// conversions</span>

    <span class="k">operator</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span>
    <span class="k">operator</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span><span class="p">();</span>
    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">operator</span> <span class="n">T</span><span class="o">&amp;</span> <span class="p">();</span> <span class="c1">// only when T=std::error_code</span>

    <span class="c1">// to_string</span>

    <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">to_string</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span>

    <span class="c1">// stream insertion</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">charT</span><span class="p">,</span> <span class="k">class</span> <span class="nc">traits</span><span class="p">&gt;</span>
      <span class="k">friend</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span>
        <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span> <span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">ec</span> <span class="p">);</span>

    <span class="c1">// what</span>

    <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">what</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span>
<span class="p">};</span>

<span class="c1">// non-member functions</span>

<span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">hash_value</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">ec</span> <span class="p">);</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="sect4">
<h5 id="ref_constructors_2">Constructors</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">error_code</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>value() == 0</code>; <code>category() == system_category()</code>; <code>!has_location()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">error_code</span><span class="p">(</span> <span class="kt">int</span> <span class="n">val</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">cat</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>value() == val</code>; <code>category() == cat</code>; <code>!has_location()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">error_code</span><span class="p">(</span> <span class="kt">int</span> <span class="n">val</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">cat</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">*</span> <span class="n">loc</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>loc</code> points to a valid <code>boost::source_location</code> object with static storage duration.</p>
</dd>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>value() == val</code>; <code>category() == cat</code>; <code>has_location()</code>; <code>&amp;location() == loc</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">ErrorCodeEnum</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">error_code</span><span class="p">(</span> <span class="n">ErrorCodeEnum</span> <span class="n">e</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>*this == make_error_code( e )</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This constructor is only enabled when <code>is_error_code_enum&lt;ErrorCodeEnum&gt;::value</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">error_code</span><span class="p">(</span> <span class="n">error_code</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">ec</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">*</span> <span class="n">loc</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>loc</code> points to a valid <code>boost::source_location</code> object with static storage duration, or is <code>nullptr</code>.</p>
</dd>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>*this == ec</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>When <code>ec</code> is a default-constructed <code>error_code</code> or wraps a <code>std::error_code</code>,
or when <code>loc</code> is <code>nullptr</code>, <code>*this</code> stores no location (<code>has_location()</code> is <code>false</code>).
Otherwise, <code>*this</code> stores <code>loc</code> (<code>has_location()</code> is <code>true</code> and <code>&amp;location()</code> is <code>loc</code>.)</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">error_code</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">ec</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Construct an <code>error_code</code> that wraps <code>ec</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p><code>value()</code> and <code>category()</code> are unspecified. <code>has_location()</code> is <code>false</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_modifiers">Modifiers</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">void</span> <span class="n">assign</span><span class="p">(</span> <span class="kt">int</span> <span class="n">val</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">cat</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>*this = error_code( val, cat )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">void</span> <span class="n">assign</span><span class="p">(</span> <span class="kt">int</span> <span class="n">val</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">cat</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">*</span> <span class="n">loc</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>*this = error_code( val, cat, loc )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">ErrorCodeEnum</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span> <span class="n">ErrorCodeEnum</span> <span class="n">e</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>*this == make_error_code( e )</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This operator is only enabled when <code>is_error_code_enum&lt;ErrorCodeEnum&gt;::value</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">void</span> <span class="n">assign</span><span class="p">(</span> <span class="n">error_code</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">ec</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">*</span> <span class="n">loc</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>*this = error_code( ec, loc )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">void</span> <span class="n">clear</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>*this = error_code()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_observers">Observers</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">int</span> <span class="n">value</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>the error value.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">category</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>the error category.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">error_condition</span> <span class="n">default_error_condition</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>category().default_error_condition( value() )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">message</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>If <code>*this</code> wraps a <code>std::error_code</code> object <code>ec</code>, <code>ec.message()</code>.
Otherwise, <code>category().message( value() )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">message</span><span class="p">(</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>If <code>*this</code> wraps a <code>std::error_code</code> object <code>ec</code>, copies the
string returned from <code>ec.message()</code> into <code>buffer</code> and returns <code>buffer</code>.
Otherwise, returns <code>category().message( value(), buffer, len )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">failed</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>If <code>*this</code> wraps a <code>std::error_code</code> object <code>ec</code>, <code>ec.value() != 0</code>.
Otherwise, <code>category().failed( value() )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="k">explicit</span> <span class="k">operator</span> <span class="kt">bool</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>failed()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">bool</span> <span class="n">has_location</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>true</code> if <code>*this</code> has been constructed with a pointer to a source
location, <code>false</code> otherwise.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">location</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>*loc</code> if <code>*this</code> has been constructed with a pointer to a source
location <code>loc</code>, a reference to a default-constructed <code>boost::source_location</code> otherwise.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_comparisons_2">Comparisons</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>If both <code>lhs</code> and <code>rhs</code> wrap <code>std::error_code</code> objects <code>e1</code> and <code>e2</code>, <code>e1 == e2</code>.
Otherwise, <code>lhs.value() == rhs.value() &amp;&amp; lhs.category() == rhs.category()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!( lhs == rhs )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&lt;</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>If both <code>lhs</code> and <code>rhs</code> wrap <code>std::error_code</code> objects <code>e1</code> and <code>e2</code>, <code>e1 &lt; e2</code>.
Otherwise, <code>lhs.category() &lt; rhs.category() || (lhs.category() == rhs.category() &amp;&amp; lhs.value() &lt; rhs.value())</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>If <code>code</code> wraps a <code>std::error_code</code> object <code>ec</code>, <code>ec == static_cast&lt;std::error_condition&gt;( condition )</code>.
Otherwise, <code>code.category().equivalent( code.value(), condition ) || condition.category().equivalent( code, condition.value() )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!( lhs == rhs )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>static_cast&lt;std::error_code&gt;(lhs) == rhs</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>lhs == static_cast&lt;std::error_code&gt;(rhs)</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!( lhs == rhs )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span>
  <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span> <span class="n">E</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
</dl>
</div>
<div class="ulist disc">
<ul class="disc">
<li>
<p>When <code>is_error_code_enum&lt;E&gt;::value</code> is <code>true</code>, returns <code>lhs == make_error_code(rhs)</code>;</p>
</li>
<li>
<p>When <code>is_error_condition_enum&lt;E&gt;::value</code> is <code>true</code>, returns <code>lhs == make_error_condition(rhs)</code>;</p>
</li>
<li>
<p>Otherwise, this overload is disabled.</p>
</li>
</ul>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span>
  <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="n">E</span> <span class="n">lhs</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
</dl>
</div>
<div class="ulist disc">
<ul class="disc">
<li>
<p>When <code>is_error_code_enum&lt;E&gt;::value</code> is <code>true</code>, returns <code>make_error_code(lhs) == rhs</code>;</p>
</li>
<li>
<p>When <code>is_error_condition_enum&lt;E&gt;::value</code> is <code>true</code>, returns <code>make_error_condition(lhs) == rhs</code>;</p>
</li>
<li>
<p>Otherwise, this overload is disabled.</p>
</li>
</ul>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span>
  <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span> <span class="n">E</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span>
  <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="n">E</span> <span class="n">lhs</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!( lhs == rhs )</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>These overloads are only enabled when
<code>is_error_code_enum&lt;E&gt;::value</code> is <code>true</code> or
<code>is_error_condition_enum&lt;E&gt;::value</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_conversions_2">Conversions</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">operator</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">operator</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span><span class="p">();</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>If <code>*this</code> wraps a <code>std::error_code</code> object <code>ec</code>, <code>ec</code>.
Otherwise, <code>std::error_code( value(), category() )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">&gt;</span> <span class="k">operator</span> <span class="n">T</span><span class="o">&amp;</span><span class="p">();</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>If <code>*this</code> wraps a <code>std::error_code</code> object <code>ec</code>,
returns a reference to <code>ec</code>.
Otherwise, makes <code>*this</code> wrap <code>std::error_code( *this )</code>,
then returns a reference to it.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This operator is only enabled when <code>T</code> is <code>std::error_code</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_to_string">to_string</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">to_string</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>If <code>*this</code> wraps a <code>std::error_code</code> object <code>e2</code>, a string that
is the concatenation of <code>"std:"</code>, <code>e2.category().name()</code>, <code>':'</code>, and the
string representation of <code>e2.value()</code>. Otherwise, the concatenation of
<code>category().name()</code>, <code>':'</code>, and the string representation of <code>value()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_stream_insertion">Stream Insertion</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">charT</span><span class="p">,</span> <span class="k">class</span> <span class="nc">traits</span><span class="p">&gt;</span>
  <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span>
    <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span> <span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">ec</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>os &lt;&lt; to_string()</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>os</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_what">what</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">what</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A string representation of <code>*this</code>, suitable for logging and
diagnostic output. Typically incorporates <code>message()</code>, <code>to_string()</code>, and
<code>location().to_string()</code> (if available.)</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_nonmembers">Nonmembers</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="nf">hash_value</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">ec</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>If <code>ec</code> wraps a <code>std::error_code</code> object <code>e2</code>, <code>std::hash&lt;std::error_code&gt;()(e2)</code>.
Otherwise, a hash value representing <code>ec</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="ref_boostsystemerror_condition_hpp">&lt;boost/system/&#8203;error_condition.hpp&gt;</h3>
<div class="sect3">
<h4 id="ref_error_condition">error_condition</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span> <span class="p">{</span>

<span class="k">class</span> <span class="nc">error_condition</span> <span class="p">{</span>
<span class="nl">public:</span>

    <span class="c1">// constructors</span>

    <span class="k">constexpr</span> <span class="n">error_condition</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="n">error_condition</span><span class="p">(</span> <span class="kt">int</span> <span class="n">val</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">cat</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">ErrorConditionEnum</span><span class="p">&gt;</span>
      <span class="k">constexpr</span> <span class="n">error_condition</span><span class="p">(</span> <span class="n">ErrorConditionEnum</span> <span class="n">e</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="c1">// modifiers</span>

    <span class="k">constexpr</span> <span class="kt">void</span> <span class="n">assign</span><span class="p">(</span> <span class="kt">int</span> <span class="n">val</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">cat</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">typename</span> <span class="nc">ErrorConditionEnum</span><span class="p">&gt;</span>
      <span class="k">constexpr</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span> <span class="n">ErrorConditionEnum</span> <span class="n">e</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">constexpr</span> <span class="kt">void</span> <span class="n">clear</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="c1">// observers</span>

    <span class="k">constexpr</span> <span class="kt">int</span> <span class="n">value</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">category</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">message</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span>
    <span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">message</span><span class="p">(</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">failed</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="k">explicit</span> <span class="k">operator</span> <span class="kt">bool</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="c1">// comparisons</span>

    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&lt;</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="c1">// conversions</span>

    <span class="k">operator</span> <span class="n">std</span><span class="o">::</span><span class="n">error_condition</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span>

    <span class="c1">// to_string</span>

    <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">to_string</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span>

    <span class="c1">// stream insertion</span>

    <span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">charT</span><span class="p">,</span> <span class="k">class</span> <span class="nc">traits</span><span class="p">&gt;</span>
      <span class="k">friend</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span>
        <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span> <span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">en</span> <span class="p">);</span>
<span class="p">};</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="sect4">
<h5 id="ref_constructors_3">Constructors</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">error_condition</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>value() == 0</code>; <code>category() == generic_category()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">error_condition</span><span class="p">(</span> <span class="kt">int</span> <span class="n">val</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">cat</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>value() == val</code>; <code>category() == cat</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">ErrorConditionEnum</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">error_condition</span><span class="p">(</span> <span class="n">ErrorConditionEnum</span> <span class="n">e</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>*this == make_error_condition( e )</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This constructor is only enabled when <code>is_error_condition_enum&lt;ErrorConditionEnum&gt;::value</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_modifiers_2">Modifiers</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">void</span> <span class="n">assign</span><span class="p">(</span> <span class="kt">int</span> <span class="n">val</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">cat</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>value() == val</code>; <code>category() == cat</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">ErrorConditionEnum</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="k">operator</span><span class="o">=</span><span class="p">(</span> <span class="n">ErrorConditionEnum</span> <span class="n">e</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>*this == make_error_condition( e )</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This operator is only enabled when <code>is_error_condition_enum&lt;ErrorConditionEnum&gt;::value</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">void</span> <span class="n">clear</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>value() == 0</code>; <code>category() == generic_category()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_observers_2">Observers</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">int</span> <span class="n">value</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>the error value.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">category</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>the error category.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">message</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>category().message( value() )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="kt">char</span> <span class="k">const</span> <span class="o">*</span> <span class="n">message</span><span class="p">(</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">buffer</span><span class="p">,</span> <span class="n">std</span><span class="o">::</span><span class="kt">size_t</span> <span class="n">len</span> <span class="p">)</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>category().message( value(), buffer, len )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">failed</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>category().failed( value() )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="k">explicit</span> <span class="k">operator</span> <span class="kt">bool</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>failed()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_comparisons_3">Comparisons</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>lhs.value() == rhs.value() &amp;&amp; lhs.category() == rhs.category()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!( lhs == rhs )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">&lt;</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>lhs.category() &lt; rhs.category() || (lhs.category() == rhs.category() &amp;&amp; lhs.value() &lt; rhs.value())</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">condition</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">code</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>code == static_cast&lt;std::error_condition&gt;( rhs )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">lhs</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="o">&amp;</span> <span class="n">rhs</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!( lhs == rhs )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_conversions_3">Conversions</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">operator</span> <span class="n">std</span><span class="o">::</span><span class="n">error_condition</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>std::error_condition( value(), category() )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_to_string_2">to_string</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="n">to_string</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>The concatenation of <code>"cond:"</code>, <code>category().name()</code>, <code>':'</code>,
and the string representation of <code>value()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_stream_insertion_2">Stream Insertion</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span> <span class="o">&lt;</span><span class="k">class</span> <span class="nc">charT</span><span class="p">,</span> <span class="k">class</span> <span class="nc">traits</span><span class="p">&gt;</span>
  <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span>
    <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span> <span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">charT</span><span class="p">,</span> <span class="n">traits</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_condition</span> <span class="o">&amp;</span> <span class="n">en</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>os &lt;&lt; en.to_string()</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>os</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="ref_boostsystemsystem_error_hpp">&lt;boost/system/&#8203;system_error.hpp&gt;</h3>
<div class="sect3">
<h4 id="ref_system_error">system_error</h4>
<div class="paragraph">
<p>The class <code>system_error</code> describes an exception object used to
report errors that have an associated <code>error_code</code>. Such errors
typically originate from operating system or other low-level
application program interfaces.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span> <span class="p">{</span>

<span class="k">class</span> <span class="nc">system_error</span><span class="o">:</span> <span class="k">public</span> <span class="n">std</span><span class="o">::</span><span class="n">runtime_error</span>
<span class="p">{</span>
<span class="nl">public:</span>

    <span class="k">explicit</span> <span class="n">system_error</span><span class="p">(</span> <span class="n">error_code</span> <span class="n">ec</span> <span class="p">);</span>
    <span class="n">system_error</span><span class="p">(</span> <span class="n">error_code</span> <span class="n">ec</span><span class="p">,</span> <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">what_arg</span> <span class="p">);</span>
    <span class="n">system_error</span><span class="p">(</span> <span class="n">error_code</span> <span class="n">ec</span><span class="p">,</span> <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="o">&amp;</span> <span class="n">what_arg</span> <span class="p">);</span>

    <span class="n">system_error</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">ecat</span> <span class="p">);</span>
    <span class="n">system_error</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">ecat</span><span class="p">,</span>
      <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">what_arg</span> <span class="p">);</span>
    <span class="n">system_error</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">ecat</span><span class="p">,</span>
      <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="o">&amp;</span> <span class="n">what_arg</span> <span class="p">);</span>

    <span class="n">error_code</span> <span class="n">code</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">what</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
<span class="p">};</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="sect4">
<h5 id="ref_constructors_4">Constructors</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">explicit</span> <span class="nf">system_error</span><span class="p">(</span> <span class="n">error_code</span> <span class="n">ec</span> <span class="p">);</span>
<span class="n">system_error</span><span class="p">(</span> <span class="n">error_code</span> <span class="n">ec</span><span class="p">,</span> <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">what_arg</span> <span class="p">);</span>
<span class="n">system_error</span><span class="p">(</span> <span class="n">error_code</span> <span class="n">ec</span><span class="p">,</span> <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="o">&amp;</span> <span class="n">what_arg</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>code() == ec</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">system_error</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">ecat</span><span class="p">,</span>
  <span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">what_arg</span> <span class="p">);</span>
<span class="n">system_error</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">ecat</span><span class="p">,</span>
  <span class="k">const</span> <span class="n">std</span><span class="o">::</span><span class="n">string</span> <span class="o">&amp;</span> <span class="n">what_arg</span> <span class="p">);</span>
<span class="n">system_error</span><span class="p">(</span> <span class="kt">int</span> <span class="n">ev</span><span class="p">,</span> <span class="k">const</span> <span class="n">error_category</span> <span class="o">&amp;</span> <span class="n">ecat</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>code() == error_code( ev, ecat )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_observers_3">Observers</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">error_code</span> <span class="n">code</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>ec</code> or <code>error_code( ev, ecat )</code>, from the constructor, as appropriate.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">const</span> <span class="kt">char</span> <span class="o">*</span> <span class="n">what</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A null-terminated character string incorporating the arguments supplied
in the constructor, typically of the form <code>what_arg + ": " + code().message()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="ref_boostsystemresult_hpp">&lt;boost/system/result.hpp&gt;</h3>
<div class="paragraph">
<p>This header defines the class template <code>result&lt;T, E&gt;</code>. Unlike the rest of the library,
it requires C&#43;&#43;11.</p>
</div>
<div class="sect3">
<h4 id="ref_synopsis">Synopsis</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span> <span class="p">{</span>

<span class="c1">// throw_exception_from_error</span>

<span class="n">BOOST_NORETURN</span> <span class="kr">inline</span> <span class="kt">void</span> <span class="n">throw_exception_from_error</span><span class="p">(</span> <span class="n">error_code</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">e</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="p">);</span>

<span class="n">BOOST_NORETURN</span> <span class="kr">inline</span> <span class="kt">void</span> <span class="n">throw_exception_from_error</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">e</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="p">);</span>

<span class="n">BOOST_NORETURN</span> <span class="kr">inline</span> <span class="kt">void</span> <span class="n">throw_exception_from_error</span><span class="p">(</span> <span class="n">errc</span><span class="o">::</span><span class="n">errc_t</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">e</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="p">);</span>

<span class="n">BOOST_NORETURN</span> <span class="kr">inline</span> <span class="kt">void</span> <span class="n">throw_exception_from_error</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">errc</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">e</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="p">);</span>

<span class="n">BOOST_NORETURN</span> <span class="kr">inline</span> <span class="kt">void</span> <span class="n">throw_exception_from_error</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">exception_ptr</span> <span class="o">&amp;</span> <span class="n">e</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="p">);</span>

<span class="c1">// in_place_*</span>

<span class="k">using</span> <span class="n">in_place_value_t</span> <span class="o">=</span> <span class="cm">/*unspecified*/</span><span class="p">;</span>
<span class="k">constexpr</span> <span class="n">in_place_value_t</span> <span class="n">in_place_value</span><span class="p">{};</span>

<span class="k">using</span> <span class="n">in_place_error_t</span> <span class="o">=</span> <span class="cm">/*unspecified*/</span><span class="p">;</span>
<span class="k">constexpr</span> <span class="n">in_place_error_t</span> <span class="n">in_place_error</span><span class="p">{};</span>

<span class="c1">// result</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="k">class</span> <span class="nc">E</span> <span class="o">=</span> <span class="n">error_code</span><span class="p">&gt;</span> <span class="k">class</span> <span class="nc">result</span><span class="p">;</span>
<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span> <span class="k">class</span> <span class="nc">result</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="n">E</span><span class="o">&gt;</span><span class="p">;</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
</div>
<div class="sect3">
<h4 id="ref_throw_exception_from_error">throw_exception_from_error</h4>
<div class="paragraph">
<p>The function <code>throw_exception_from_error</code> is called by <code>result&lt;T, E&gt;::value()</code> when
the result holds an error. Its purpose is to throw an exception that represents the
error held in the result.</p>
</div>
<div class="paragraph">
<p>An implementation for the common and default case where <code>E</code> is <code>error_code</code> is
already provided. It throws <code>system_error(e)</code>.</p>
</div>
<div class="paragraph">
<p>If <code>result&lt;T, E&gt;</code> is used with other error types, the user is expected to provide
an appropriate overload of <code>throw_exception_from_error</code> in the namespace of <code>E</code>.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">BOOST_NORETURN</span> <span class="kr">inline</span> <span class="kt">void</span> <span class="nf">throw_exception_from_error</span><span class="p">(</span> <span class="n">error_code</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">e</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>boost::throw_with_location( system_error( e ), loc )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">BOOST_NORETURN</span> <span class="kr">inline</span> <span class="kt">void</span> <span class="nf">throw_exception_from_error</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">error_code</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">e</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>boost::throw_with_location( std::system_error( e ), loc )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">BOOST_NORETURN</span> <span class="kr">inline</span> <span class="kt">void</span> <span class="nf">throw_exception_from_error</span><span class="p">(</span> <span class="n">errc</span><span class="o">::</span><span class="n">errc_t</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">e</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>boost::throw_with_location( system_error( make_error_code( e ) ), loc )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">BOOST_NORETURN</span> <span class="kr">inline</span> <span class="kt">void</span> <span class="nf">throw_exception_from_error</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">errc</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">e</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p><code>boost::throw_with_location( std::system_error( make_error_code( e ) ), loc )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="n">BOOST_NORETURN</span> <span class="kr">inline</span> <span class="kt">void</span> <span class="nf">throw_exception_from_error</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">exception_ptr</span> <span class="o">&amp;</span> <span class="n">e</span><span class="p">,</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<div class="ulist disc">
<ul class="disc">
<li>
<p>If <code>e</code> isn&#8217;t null, <code>std::rethrow_exception( e )</code>.</p>
</li>
<li>
<p>Otherwise, <code>boost::throw_with_location( std::bad_exception(), loc )</code>.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect3">
<h4 id="ref_resultt_e">result&lt;T, E&gt;</h4>
<div class="paragraph">
<p><code>result&lt;T, E&gt;</code> stores either a value of type <code>T</code>, or an error of type <code>E</code>. <code>E</code> defaults
to <code>error_code</code>. In a typical use, functions that can fail return <code>result&lt;T&gt;</code>.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span> <span class="p">{</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="k">class</span> <span class="nc">E</span> <span class="o">=</span> <span class="n">error_code</span><span class="p">&gt;</span> <span class="k">class</span> <span class="nc">result</span>
<span class="p">{</span>
<span class="nl">public:</span>

    <span class="k">using</span> <span class="n">value_type</span> <span class="o">=</span> <span class="n">T</span><span class="p">;</span>
    <span class="k">using</span> <span class="n">error_type</span> <span class="o">=</span> <span class="n">E</span><span class="p">;</span>

    <span class="k">static</span> <span class="k">constexpr</span> <span class="n">in_place_value_t</span> <span class="n">in_place_value</span><span class="p">{};</span>
    <span class="k">static</span> <span class="k">constexpr</span> <span class="n">in_place_error_t</span> <span class="n">in_place_error</span><span class="p">{};</span>

    <span class="c1">// constructors</span>

    <span class="k">constexpr</span> <span class="n">result</span><span class="p">();</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
      <span class="k">constexpr</span> <span class="n">result</span><span class="p">(</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
      <span class="k">constexpr</span> <span class="n">result</span><span class="p">(</span> <span class="n">in_place_value_t</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
      <span class="k">constexpr</span> <span class="n">result</span><span class="p">(</span> <span class="n">in_place_error_t</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T2</span><span class="p">,</span> <span class="k">class</span> <span class="nc">E2</span><span class="p">&gt;</span>
      <span class="k">constexpr</span> <span class="n">result</span><span class="p">(</span> <span class="n">result</span><span class="o">&lt;</span><span class="n">T2</span><span class="p">,</span> <span class="n">E2</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T2</span><span class="p">,</span> <span class="k">class</span> <span class="nc">E2</span><span class="p">&gt;</span>
      <span class="k">constexpr</span> <span class="n">result</span><span class="p">(</span> <span class="n">result</span><span class="o">&lt;</span><span class="n">T2</span><span class="p">,</span> <span class="n">E2</span><span class="o">&gt;&amp;&amp;</span> <span class="n">r2</span> <span class="p">);</span>

    <span class="c1">// queries</span>

    <span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">has_value</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">has_error</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="k">explicit</span> <span class="k">operator</span> <span class="kt">bool</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="c1">// checked value access</span>

    <span class="k">constexpr</span> <span class="n">T</span><span class="o">&amp;</span> <span class="n">value</span><span class="p">(</span> <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="o">=</span>
      <span class="n">BOOST_CURRENT_LOCATION</span> <span class="p">)</span> <span class="o">&amp;</span> <span class="p">;</span>

    <span class="k">constexpr</span> <span class="n">T</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">value</span><span class="p">(</span> <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="o">=</span>
      <span class="n">BOOST_CURRENT_LOCATION</span> <span class="p">)</span> <span class="k">const</span><span class="o">&amp;</span> <span class="p">;</span>

    <span class="k">constexpr</span> <span class="n">T</span><span class="o">&amp;&amp;</span> <span class="n">value</span><span class="p">(</span> <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="o">=</span>
      <span class="n">BOOST_CURRENT_LOCATION</span> <span class="p">)</span> <span class="o">&amp;&amp;</span> <span class="p">;</span>

    <span class="k">constexpr</span> <span class="n">T</span> <span class="k">const</span><span class="o">&amp;&amp;</span> <span class="n">value</span><span class="p">(</span> <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="o">=</span>
      <span class="n">BOOST_CURRENT_LOCATION</span> <span class="p">)</span> <span class="k">const</span><span class="o">&amp;&amp;</span> <span class="p">;</span>

    <span class="c1">// unchecked value access</span>

    <span class="k">constexpr</span> <span class="n">T</span><span class="o">*</span> <span class="k">operator</span><span class="o">-&gt;</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="n">T</span> <span class="k">const</span><span class="o">*</span> <span class="k">operator</span><span class="o">-&gt;</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">constexpr</span> <span class="n">T</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">*</span><span class="p">()</span> <span class="o">&amp;</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="n">T</span> <span class="k">const</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">*</span><span class="p">()</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="n">T</span><span class="o">&amp;&amp;</span> <span class="k">operator</span><span class="o">*</span><span class="p">()</span> <span class="o">&amp;&amp;</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="n">T</span> <span class="k">const</span><span class="o">&amp;&amp;</span> <span class="k">operator</span><span class="o">*</span><span class="p">()</span> <span class="k">const</span> <span class="o">&amp;&amp;</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="c1">// error access</span>

    <span class="k">constexpr</span> <span class="n">E</span> <span class="n">error</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span>

    <span class="c1">// emplace</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
      <span class="k">constexpr</span> <span class="n">T</span><span class="o">&amp;</span> <span class="n">emplace</span><span class="p">(</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>

    <span class="c1">// swap</span>

    <span class="k">constexpr</span> <span class="kt">void</span> <span class="n">swap</span><span class="p">(</span> <span class="n">result</span><span class="o">&amp;</span> <span class="n">r</span> <span class="p">);</span>
    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">void</span> <span class="n">swap</span><span class="p">(</span> <span class="n">result</span> <span class="o">&amp;</span> <span class="n">r1</span><span class="p">,</span> <span class="n">result</span> <span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span>

    <span class="c1">// equality</span>

    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r1</span><span class="p">,</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span>
    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r1</span><span class="p">,</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span>
<span class="p">};</span>

<span class="c1">// stream insertion</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">Ch</span><span class="p">,</span> <span class="k">class</span> <span class="nc">Tr</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span>
  <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span>
    <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="n">result</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span> <span class="n">E</span><span class="o">&gt;</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r</span> <span class="p">);</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="sect4">
<h5 id="ref_constructors_5">Constructors</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="nf">result</span><span class="p">();</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>*this</code> holds the value <code>T()</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This constructor is only enabled when <code>std::is_default_constructible&lt;T&gt;::value</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="nf">result</span><span class="p">(</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>If <code>std::is_constructible&lt;T, A&#8230;&#8203;&gt;::value &amp;&amp; !std::is_constructible&lt;E, A&#8230;&#8203;&gt;::value</code>,
ensures that <code>*this</code> holds the value <code>T( std::forward&lt;A&gt;(a)&#8230;&#8203; )</code>.</p>
</li>
<li>
<p>If <code>std::is_constructible&lt;E, A&#8230;&#8203;&gt;::value &amp;&amp; !std::is_constructible&lt;T, A&#8230;&#8203;&gt;::value</code>,
ensures that <code>*this</code> holds the error <code>E( std::forward&lt;A&gt;(a)&#8230;&#8203; )</code>.</p>
</li>
<li>
<p>Otherwise, this constructor does not participate in overload resolution.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This constructor is only enabled when <code>sizeof&#8230;&#8203;(T) &gt; 0</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="nf">result</span><span class="p">(</span> <span class="n">in_place_value_t</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>*this</code> holds the value <code>T( std::forward&lt;A&gt;(a)&#8230;&#8203; )</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This constructor is only enabled when <code>std::is_constructible&lt;T, A&#8230;&#8203;&gt;::value</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="nf">result</span><span class="p">(</span> <span class="n">in_place_error_t</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>*this</code> holds the error <code>E( std::forward&lt;A&gt;(a)&#8230;&#8203; )</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This constructor is only enabled when <code>std::is_constructible&lt;E, A&#8230;&#8203;&gt;::value</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T2</span><span class="p">,</span> <span class="k">class</span> <span class="nc">E2</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="nf">result</span><span class="p">(</span> <span class="n">result</span><span class="o">&lt;</span><span class="n">T2</span><span class="p">,</span> <span class="n">E2</span><span class="o">&gt;</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p>If <code>r2.has_value()</code> is <code>true</code>, <code>*this</code> holds the value <code>T( *r2 )</code>, otherwise <code>*this</code> holds the value <code>E( r2.error() )</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This constructor is only enabled when <code>std::is_convertible&lt;T2, T&gt;::value &amp;&amp; std::is_convertible&lt;E2, E&gt;::value</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">T2</span><span class="p">,</span> <span class="k">class</span> <span class="nc">E2</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="nf">result</span><span class="p">(</span> <span class="n">result</span><span class="o">&lt;</span><span class="n">T2</span><span class="p">,</span> <span class="n">E2</span><span class="o">&gt;&amp;&amp;</span> <span class="n">r2</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p>If <code>r2.has_value()</code> is <code>true</code>, <code>*this</code> holds the value <code>T( std::move( *r2 ) )</code>, otherwise <code>*this</code> holds the value <code>E( r2.error() )</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This constructor is only enabled when <code>std::is_convertible&lt;T2, T&gt;::value &amp;&amp; std::is_convertible&lt;E2, E&gt;::value</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_queries">Queries</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">has_value</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>true</code> when <code>*this</code> holds a value, <code>false</code> otherwise.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">has_error</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!has_value()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="k">explicit</span> <span class="k">operator</span> <span class="kt">bool</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>has_value()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_checked_value_access">Checked Value Access</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">T</span><span class="o">&amp;</span> <span class="n">value</span><span class="p">(</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="o">=</span> <span class="n">BOOST_CURRENT_LOCATION</span> <span class="p">)</span> <span class="o">&amp;</span> <span class="p">;</span>

<span class="k">constexpr</span> <span class="n">T</span> <span class="k">const</span><span class="o">&amp;</span> <span class="n">value</span><span class="p">(</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="o">=</span> <span class="n">BOOST_CURRENT_LOCATION</span> <span class="p">)</span> <span class="k">const</span><span class="o">&amp;</span> <span class="p">;</span>

<span class="k">constexpr</span> <span class="n">T</span><span class="o">&amp;&amp;</span> <span class="n">value</span><span class="p">(</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="o">=</span> <span class="n">BOOST_CURRENT_LOCATION</span> <span class="p">)</span> <span class="o">&amp;&amp;</span> <span class="p">;</span>

<span class="k">constexpr</span> <span class="n">T</span> <span class="k">const</span><span class="o">&amp;&amp;</span> <span class="n">value</span><span class="p">(</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="o">=</span> <span class="n">BOOST_CURRENT_LOCATION</span> <span class="p">)</span> <span class="k">const</span><span class="o">&amp;&amp;</span> <span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>If <code>*this</code> holds a value, returns a reference to it. Otherwise,
calls <code>throw_exception_from_error</code>, passing it a reference to
the held error, and <code>loc</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_unchecked_value_access">Unchecked Value Access</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">T</span><span class="o">*</span> <span class="k">operator</span><span class="o">-&gt;</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>
<span class="k">constexpr</span> <span class="n">T</span> <span class="k">const</span><span class="o">*</span> <span class="k">operator</span><span class="o">-&gt;</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>If <code>*this</code> holds a value, a pointer to it. Otherwise, <code>nullptr</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">T</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">*</span><span class="p">()</span> <span class="o">&amp;</span> <span class="k">noexcept</span><span class="p">;</span>
<span class="k">constexpr</span> <span class="n">T</span> <span class="k">const</span><span class="o">&amp;</span> <span class="k">operator</span><span class="o">*</span><span class="p">()</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>*this</code> holds a value.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>*operator-&gt;()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">T</span><span class="o">&amp;&amp;</span> <span class="k">operator</span><span class="o">*</span><span class="p">()</span> <span class="o">&amp;&amp;</span> <span class="k">noexcept</span><span class="p">;</span>
<span class="k">constexpr</span> <span class="n">T</span> <span class="k">const</span><span class="o">&amp;&amp;</span> <span class="k">operator</span><span class="o">*</span><span class="p">()</span> <span class="k">const</span> <span class="o">&amp;&amp;</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>*this</code> holds a value.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>std::move( *operator-&gt;() )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_error_access">Error Access</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">E</span> <span class="n">error</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>If <code>*this</code> holds an error, returns it. Otherwise, returns <code>E()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_emplace">emplace</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">T</span><span class="o">&amp;</span> <span class="n">emplace</span><span class="p">(</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>*this</code> holds the value <code>T( std::forward&lt;A&gt;(a)&#8230;&#8203; )</code>.</p>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>A reference to the contained value.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_swap">swap</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">void</span> <span class="nf">swap</span><span class="p">(</span> <span class="n">result</span><span class="o">&amp;</span> <span class="n">r</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Exchanges the contents of <code>*this</code> and <code>r</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">void</span> <span class="nf">swap</span><span class="p">(</span> <span class="n">result</span> <span class="o">&amp;</span> <span class="n">r1</span><span class="p">,</span> <span class="n">result</span> <span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Exchanges the contents of <code>r1</code> and <code>r2</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_equality">Equality</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r1</span><span class="p">,</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>If <code>r1</code> holds a value <code>t1</code> and <code>r2</code> holds a value <code>t2</code>, returns <code>t1 == t2</code>.</p>
</li>
<li>
<p>If <code>r1</code> holds an error <code>e1</code> and <code>r2</code> holds an error <code>e2</code>, returns <code>e1 == e2</code>.</p>
</li>
<li>
<p>Otherwise, returns <code>false</code>.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r1</span><span class="p">,</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!( r1 == r2 )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_stream_insertion_3">Stream Insertion</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">Ch</span><span class="p">,</span> <span class="k">class</span> <span class="nc">Tr</span><span class="p">,</span> <span class="k">class</span> <span class="nc">T</span><span class="p">,</span> <span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span>
  <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span>
    <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="n">result</span><span class="o">&lt;</span><span class="n">T</span><span class="p">,</span> <span class="n">E</span><span class="o">&gt;</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>If <code>*this</code> holds a value <code>t</code>, <code>os &lt;&lt; "value:" &lt;&lt; t</code>.</p>
</li>
<li>
<p>If <code>*this</code> holds an error <code>e</code>, <code>os &lt;&lt; "error:" &lt;&lt; e</code>.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>os</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
</div>
<div class="sect3">
<h4 id="ref_resultvoid_e">result&lt;void, E&gt;</h4>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">namespace</span> <span class="n">boost</span> <span class="p">{</span>
<span class="k">namespace</span> <span class="n">system</span> <span class="p">{</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span> <span class="k">class</span> <span class="nc">result</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="n">E</span><span class="o">&gt;</span>
<span class="p">{</span>
<span class="nl">public:</span>

    <span class="k">using</span> <span class="n">value_type</span> <span class="o">=</span> <span class="kt">void</span><span class="p">;</span>
    <span class="k">using</span> <span class="n">error_type</span> <span class="o">=</span> <span class="n">E</span><span class="p">;</span>

    <span class="k">static</span> <span class="k">constexpr</span> <span class="n">in_place_value_t</span> <span class="n">in_place_value</span><span class="p">{};</span>
    <span class="k">static</span> <span class="k">constexpr</span> <span class="n">in_place_error_t</span> <span class="n">in_place_error</span><span class="p">{};</span>

    <span class="c1">// constructors</span>

    <span class="k">constexpr</span> <span class="n">result</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
      <span class="k">constexpr</span> <span class="n">result</span><span class="p">(</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>

    <span class="k">constexpr</span> <span class="n">result</span><span class="p">(</span> <span class="n">in_place_value_t</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
      <span class="k">constexpr</span> <span class="n">result</span><span class="p">(</span> <span class="n">in_place_error_t</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span>

    <span class="c1">// queries</span>

    <span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">has_value</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">has_error</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="k">explicit</span> <span class="k">operator</span> <span class="kt">bool</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="c1">// checked value access</span>

    <span class="k">constexpr</span> <span class="kt">void</span> <span class="n">value</span><span class="p">(</span> <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="o">=</span>
      <span class="n">BOOST_CURRENT_LOCATION</span> <span class="p">)</span> <span class="k">const</span><span class="p">;</span>

    <span class="c1">// unchecked value access</span>

    <span class="k">constexpr</span> <span class="kt">void</span><span class="o">*</span> <span class="k">operator</span><span class="o">-&gt;</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>
    <span class="k">constexpr</span> <span class="kt">void</span> <span class="k">const</span><span class="o">*</span> <span class="k">operator</span><span class="o">-&gt;</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="k">constexpr</span> <span class="kt">void</span> <span class="k">operator</span><span class="o">*</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span>

    <span class="c1">// error access</span>

    <span class="k">constexpr</span> <span class="n">E</span> <span class="n">error</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span>

    <span class="c1">// emplace</span>

    <span class="k">constexpr</span> <span class="kt">void</span> <span class="n">emplace</span><span class="p">();</span>

    <span class="c1">// swap</span>

    <span class="k">constexpr</span> <span class="kt">void</span> <span class="n">swap</span><span class="p">(</span> <span class="n">result</span><span class="o">&amp;</span> <span class="n">r</span> <span class="p">);</span>
    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">void</span> <span class="n">swap</span><span class="p">(</span> <span class="n">result</span> <span class="o">&amp;</span> <span class="n">r1</span><span class="p">,</span> <span class="n">result</span> <span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span>

    <span class="c1">// equality</span>

    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r1</span><span class="p">,</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span>
    <span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r1</span><span class="p">,</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span>
<span class="p">};</span>

<span class="c1">// stream insertion</span>

<span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">Ch</span><span class="p">,</span> <span class="k">class</span> <span class="nc">Tr</span><span class="p">,</span> <span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span>
  <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span>
    <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="n">result</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="n">E</span><span class="o">&gt;</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r</span> <span class="p">);</span>

<span class="p">}</span> <span class="c1">// namespace system</span>
<span class="p">}</span> <span class="c1">// namespace boost</span></code></pre>
</div>
</div>
<div class="sect4">
<h5 id="ref_constructors_6">Constructors</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">result</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>*this</code> holds an unspecified value.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="nf">result</span><span class="p">(</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>If <code>std::is_constructible&lt;E, A&#8230;&#8203;&gt;::value</code>,
ensures that <code>*this</code> holds the error <code>E( std::forward&lt;A&gt;(a)&#8230;&#8203; )</code>.</p>
</li>
<li>
<p>Otherwise, this constructor does not participate in overload resolution.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This constructor is only enabled when <code>sizeof&#8230;&#8203;(T) &gt; 0</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="n">result</span><span class="p">(</span> <span class="n">in_place_value_t</span> <span class="p">)</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>*this</code> holds an unspecified value.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span><span class="o">...</span> <span class="nc">A</span><span class="p">&gt;</span>
  <span class="k">constexpr</span> <span class="nf">result</span><span class="p">(</span> <span class="n">in_place_error_t</span><span class="p">,</span> <span class="n">A</span><span class="o">&amp;&amp;</span><span class="p">...</span> <span class="n">a</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>*this</code> holds the error <code>E( std::forward&lt;A&gt;(a)&#8230;&#8203; )</code>.</p>
</dd>
<dt class="hdlist1">Remarks: </dt>
<dd>
<p>This constructor is only enabled when <code>std::is_constructible&lt;E, A&#8230;&#8203;&gt;::value</code> is <code>true</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_queries_2">Queries</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">has_value</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>true</code> when <code>*this</code> holds a value, <code>false</code> otherwise.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">bool</span> <span class="n">has_error</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!has_value()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="k">explicit</span> <span class="k">operator</span> <span class="kt">bool</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>has_value()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_checked_value_access_2">Checked Value Access</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">void</span> <span class="n">value</span><span class="p">(</span>
  <span class="n">boost</span><span class="o">::</span><span class="n">source_location</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">loc</span> <span class="o">=</span> <span class="n">BOOST_CURRENT_LOCATION</span> <span class="p">)</span> <span class="k">const</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>If <code>*this</code> doesn&#8217;t hold a value, calls <code>throw_exception_from_error</code>,
passing it a reference to the held error, and <code>loc</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_unchecked_value_access_2">Unchecked Value Access</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">void</span><span class="o">*</span> <span class="k">operator</span><span class="o">-&gt;</span><span class="p">()</span> <span class="k">noexcept</span><span class="p">;</span>
<span class="k">constexpr</span> <span class="kt">void</span> <span class="k">const</span><span class="o">*</span> <span class="k">operator</span><span class="o">-&gt;</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p>If <code>*this</code> holds a value, a pointer to it. Otherwise, <code>nullptr</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">void</span> <span class="k">operator</span><span class="o">*</span><span class="p">()</span> <span class="k">const</span> <span class="k">noexcept</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Requires: </dt>
<dd>
<p><code>*this</code> holds a value.</p>
</dd>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>none.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_error_access_2">Error Access</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="n">E</span> <span class="n">error</span><span class="p">()</span> <span class="k">const</span><span class="p">;</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>If <code>*this</code> holds an error, returns it. Otherwise, returns <code>E()</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_emplace_2">emplace</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">void</span> <span class="nf">emplace</span><span class="p">();</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Ensures: </dt>
<dd>
<p><code>*this</code> holds an unspecified value.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_swap_2">swap</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">constexpr</span> <span class="kt">void</span> <span class="nf">swap</span><span class="p">(</span> <span class="n">result</span><span class="o">&amp;</span> <span class="n">r</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Exchanges the contents of <code>*this</code> and <code>r</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">void</span> <span class="nf">swap</span><span class="p">(</span> <span class="n">result</span> <span class="o">&amp;</span> <span class="n">r1</span><span class="p">,</span> <span class="n">result</span> <span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<p>Exchanges the contents of <code>r1</code> and <code>r2</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_equality_2">Equality</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">==</span><span class="p">(</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r1</span><span class="p">,</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>If <code>r1</code> and <code>r2</code> hold values, returns <code>true</code>.</p>
</li>
<li>
<p>If <code>r1</code> holds an error <code>e1</code> and <code>r2</code> holds an error <code>e2</code>, returns <code>e1 == e2</code>.</p>
</li>
<li>
<p>Otherwise, returns <code>false</code>.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
</li>
</ul>
</div>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">friend</span> <span class="k">constexpr</span> <span class="kt">bool</span> <span class="k">operator</span><span class="o">!=</span><span class="p">(</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r1</span><span class="p">,</span> <span class="n">result</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r2</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>!( r1 == r2 )</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
<div class="sect4">
<h5 id="ref_stream_insertion_4">Stream Insertion</h5>
<div class="listingblock">
<div class="content">
<pre class="rouge highlight"><code data-lang="c++"><span class="k">template</span><span class="o">&lt;</span><span class="k">class</span> <span class="nc">Ch</span><span class="p">,</span> <span class="k">class</span> <span class="nc">Tr</span><span class="p">,</span> <span class="k">class</span> <span class="nc">E</span><span class="p">&gt;</span>
  <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span>
    <span class="k">operator</span><span class="o">&lt;&lt;</span><span class="p">(</span> <span class="n">std</span><span class="o">::</span><span class="n">basic_ostream</span><span class="o">&lt;</span><span class="n">Ch</span><span class="p">,</span> <span class="n">Tr</span><span class="o">&gt;&amp;</span> <span class="n">os</span><span class="p">,</span> <span class="n">result</span><span class="o">&lt;</span><span class="kt">void</span><span class="p">,</span> <span class="n">E</span><span class="o">&gt;</span> <span class="k">const</span> <span class="o">&amp;</span> <span class="n">r</span> <span class="p">);</span></code></pre>
</div>
</div>
<div class="ulist none">
<ul class="none">
<li>
<p></p>
<div class="dlist">
<dl>
<dt class="hdlist1">Effects: </dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>If <code>*this</code> holds a value, <code>os &lt;&lt; "value:void"</code>.</p>
</li>
<li>
<p>If <code>*this</code> holds an error <code>e</code>, <code>os &lt;&lt; "error:" &lt;&lt; e</code>.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Returns: </dt>
<dd>
<p><code>os</code>.</p>
</dd>
</dl>
</div>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect2">
<h3 id="ref_boostsystem_hpp">&lt;boost/system.hpp&gt;</h3>
<div class="paragraph">
<p>This convenience header includes all the headers previously described.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="history">History</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2006/n1975.html">N1975</a>,
Filesystem Library Proposal for TR2, accepted for Library Technical
Report 2 (TR2) at the Berlin meeting, included additional components to
supplement the Standard Library&#8217;s Diagnostics clause. Since then, these error
reporting components have received wider public scrutiny and enhancements have
been made to the design. The enhanced version has been used by
<a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2006/n2054.pdf">N2054</a>,
Networking  Library Proposal for TR2, demonstrating that these error reporting
components are useful beyond the original Filesystem Library.</p>
</div>
<div class="paragraph">
<p>The original proposal viewed error categories as a binary choice between
<code>errno</code> (i.e. POSIX-style) and the native operating system&#8217;s error
codes. The proposed components now allow as many additional error categories as
are needed by either implementations or by users. The need to support additional
error categories, for example, occurs in some networking library implementations
because they are built on top of the POSIX <code>getaddrinfo</code> API that
uses error codes not based on <code>errno</code>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="acknowledgments">Acknowledgments</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Christopher Kohlhoff and Peter Dimov made important contributions to the
design. Comments and suggestions were also received from Pavel Vozenilek,
Gennaro Prota, Dave Abrahams, Jeff Garland, Iain Hanson, Oliver Kowalke, and
Oleg Abrosimov. Christopher Kohlhoff suggested several improvements to the
<a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2006/n2066.html">N2066</a>
paper. Johan Nilsson&#8217;s comments led to several of the refinements in N2066.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="copyright">Copyright and License</h2>
<div class="sectionbody">
<div class="paragraph">
<p>This documentation is</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Copyright 2003-2017 Beman Dawes</p>
</li>
<li>
<p>Copyright 2018-2022 Peter Dimov</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>and is distributed under the <a href="http://www.boost.org/LICENSE_1_0.txt">Boost Software License, Version 1.0</a>.</p>
</div>
</div>
</div>
</div>
<div id="footer">
<div id="footer-text">
Last updated 2023-04-10 13:47:39 UTC
</div>
</div>
<style>

*:not(pre)>code { background: none; color: #600000; }
:not(pre):not([class^=L])>code { background: none; color: #600000; }

</style>
</body>
</html>