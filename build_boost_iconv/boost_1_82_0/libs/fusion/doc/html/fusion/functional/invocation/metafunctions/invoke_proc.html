<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>invoke_procedure</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Chapter 1. Fusion 2.2">
<link rel="up" href="../metafunctions.html" title="Metafunctions">
<link rel="prev" href="invoke.html" title="invoke">
<link rel="next" href="invoke_fobj.html" title="invoke_function_object">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="invoke.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../metafunctions.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="invoke_fobj.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="fusion.functional.invocation.metafunctions.invoke_proc"></a><a class="link" href="invoke_proc.html" title="invoke_procedure">invoke_procedure</a>
</h5></div></div></div>
<h6>
<a name="fusion.functional.invocation.metafunctions.invoke_proc.h0"></a>
            <span class="phrase"><a name="fusion.functional.invocation.metafunctions.invoke_proc.description"></a></span><a class="link" href="invoke_proc.html#fusion.functional.invocation.metafunctions.invoke_proc.description">Description</a>
          </h6>
<p>
            Returns the result type of <a class="link" href="../functions/invoke_proc.html" title="invoke_procedure"><code class="computeroutput"><span class="identifier">invoke_procedure</span></code></a>.
          </p>
<h6>
<a name="fusion.functional.invocation.metafunctions.invoke_proc.h1"></a>
            <span class="phrase"><a name="fusion.functional.invocation.metafunctions.invoke_proc.synopsis"></a></span><a class="link" href="invoke_proc.html#fusion.functional.invocation.metafunctions.invoke_proc.synopsis">Synopsis</a>
          </h6>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">result_of</span>
<span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span>
        <span class="keyword">typename</span> <span class="identifier">Function</span><span class="special">,</span>
        <span class="keyword">class</span> <span class="identifier">Sequence</span>
        <span class="special">&gt;</span>
    <span class="keyword">struct</span> <span class="identifier">invoke_procedure</span>
    <span class="special">{</span>
        <span class="keyword">typedef</span> <span class="emphasis"><em>unspecified</em></span> <span class="identifier">type</span><span class="special">;</span>
    <span class="special">};</span>
<span class="special">}</span>
</pre>
<h6>
<a name="fusion.functional.invocation.metafunctions.invoke_proc.h2"></a>
            <span class="phrase"><a name="fusion.functional.invocation.metafunctions.invoke_proc.see_also"></a></span><a class="link" href="invoke_proc.html#fusion.functional.invocation.metafunctions.invoke_proc.see_also">See
            also</a>
          </h6>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <a class="link" href="../functions/invoke_proc.html" title="invoke_procedure"><code class="computeroutput"><span class="identifier">invoke_procedure</span></code></a>
              </li>
<li class="listitem">
                <a class="link" href="../../adapters/fused_procedure.html" title="fused_procedure"><code class="computeroutput"><span class="identifier">fused_procedure</span></code></a>
              </li>
</ul></div>
</div>
<div class="copyright-footer">Copyright © 2001-2006, 2011, 2012 Joel de Guzman,
      Dan Marsden, Tobias Schwinger<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="invoke.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../metafunctions.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="invoke_fobj.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
