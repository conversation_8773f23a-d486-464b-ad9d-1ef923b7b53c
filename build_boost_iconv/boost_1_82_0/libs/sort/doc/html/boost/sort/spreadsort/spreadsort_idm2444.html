<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Function template spreadsort</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Boost.Sort">
<link rel="up" href="../../../header/boost/sort/spreadsort/spreadsort_hpp.html" title="Header &lt;boost/sort/spreadsort/spreadsort.hpp&gt;">
<link rel="prev" href="spreadsort_idm2396.html" title="Function template spreadsort">
<link rel="next" href="spreadsort_idm2492.html" title="Function template spreadsort">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="spreadsort_idm2396.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/sort/spreadsort/spreadsort_hpp.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="spreadsort_idm2492.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.sort.spreadsort.spreadsort_idm2444"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Function template spreadsort</span></h2>
<p>boost::sort::spreadsort::spreadsort — Generic <code class="computeroutput">spreadsort</code> variant detecting string element type so call to <code class="computeroutput">string_sort</code> for <code class="computeroutput">std::wstrings</code>. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../header/boost/sort/spreadsort/spreadsort_hpp.html" title="Header &lt;boost/sort/spreadsort/spreadsort.hpp&gt;">boost/sort/spreadsort/spreadsort.hpp</a>&gt;

</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> RandomAccessIter<span class="special">&gt;</span> 
  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">enable_if_c</span><span class="special">&lt;</span> <span class="identifier">is_same</span><span class="special">&lt;</span> <span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span> <span class="identifier">RandomAccessIter</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value_type</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span> <span class="special">&amp;&amp;</span><span class="keyword">sizeof</span><span class="special">(</span><span class="keyword">wchar_t</span><span class="special">)</span><span class="special">==</span><span class="number">2</span><span class="special">,</span> <span class="keyword">void</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> 
  <span class="identifier">spreadsort</span><span class="special">(</span><span class="identifier">RandomAccessIter</span> first<span class="special">,</span> <span class="identifier">RandomAccessIter</span> last<span class="special">)</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm3713"></a><h2>Description</h2>
<p>If the data type provided is a wstring, <code class="computeroutput">string_sort</code> is used. </p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../../../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>Sorting other data types requires picking between <code class="computeroutput">integer_sort</code>, <code class="computeroutput">float_sort</code> and <code class="computeroutput">string_sort</code> directly, as <code class="computeroutput">spreadsort</code> won't accept types that don't have the appropriate <code class="computeroutput">type_traits</code>. Also, 2-byte wide-characters are the limit above which string_sort is inefficient, so on platforms with wider characters, this will not accept wstrings.</p></td></tr>
</table></div>
<p>






</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>Iterator pointer to first element. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>Iterator pointing to one beyond the end of data.</p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>[<code class="computeroutput">first</code>, <code class="computeroutput">last</code>) is a valid range. </p></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">RandomAccessIter</code> <code class="computeroutput">value_type</code> is mutable. </p></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">RandomAccessIter</code> <code class="computeroutput">value_type</code> is <a href="http://en.cppreference.com/w/cpp/concept/LessThanComparable" target="_top">LessThanComparable</a> </p></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">RandomAccessIter</code> <code class="computeroutput">value_type</code> supports the <code class="computeroutput">operator&gt;&gt;</code>, which returns an integer-type right-shifted a specified number of bits. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The elements in the range [<code class="computeroutput">first</code>, <code class="computeroutput">last</code>) are sorted in ascending order. </p></td>
</tr>
</tbody>
</table></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2014-2017 Steven
      Ross, Francisco Tapia, Orson Peters<p>
        Distributed under the <a href="http://boost.org/LICENSE_1_0.txt" target="_top">Boost
        Software License, Version 1.0</a>.
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="spreadsort_idm2396.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/sort/spreadsort/spreadsort_hpp.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="spreadsort_idm2492.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
