<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen *******"/>
<title>Boost.Sort: DATA_TYPE Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td style="padding-left: 0.5em;">
   <div id="projectname">Boost.Sort
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen ******* -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="struct_d_a_t_a___t_y_p_e-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">DATA_TYPE Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a6a24b4532734d210a7b3a9c71a3678f5"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_a_t_a___t_y_p_e.html#a6a24b4532734d210a7b3a9c71a3678f5">operator&lt;</a> (const <a class="el" href="struct_d_a_t_a___t_y_p_e.html">DATA_TYPE</a> &amp;y) const </td></tr>
<tr class="separator:a6a24b4532734d210a7b3a9c71a3678f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a0f17d79c6492cb604f3da783a5017b54"><td class="memItemLeft" align="right" valign="top">string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_a_t_a___t_y_p_e.html#a0f17d79c6492cb604f3da783a5017b54">a</a></td></tr>
<tr class="separator:a0f17d79c6492cb604f3da783a5017b54"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa28561fc8e223d84187ccfaf99953bae"><td class="memItemLeft" align="right" valign="top"><a class="el" href="floatfunctorsample_8cpp.html#ae35c40bc2f912c11f0e36ac66cba4489">KEY_TYPE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_a_t_a___t_y_p_e.html#aa28561fc8e223d84187ccfaf99953bae">key</a></td></tr>
<tr class="separator:aa28561fc8e223d84187ccfaf99953bae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae644cc131b810b459261b3426e2c459c"><td class="memItemLeft" align="right" valign="top">std::string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_a_t_a___t_y_p_e.html#ae644cc131b810b459261b3426e2c459c">data</a></td></tr>
<tr class="separator:ae644cc131b810b459261b3426e2c459c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a437ac9cab171cc3fff62212c65b3810c"><td class="memItemLeft" align="right" valign="top">time_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_a_t_a___t_y_p_e.html#a437ac9cab171cc3fff62212c65b3810c">birth</a></td></tr>
<tr class="separator:a437ac9cab171cc3fff62212c65b3810c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a7008d5cecd603c1aaf8ff9ed849621"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_a_t_a___t_y_p_e.html#a2a7008d5cecd603c1aaf8ff9ed849621">net_worth</a></td></tr>
<tr class="separator:a2a7008d5cecd603c1aaf8ff9ed849621"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad2c00885d0c6afec26d51a53ae05e403"><td class="memItemLeft" align="right" valign="top">string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_a_t_a___t_y_p_e.html#ad2c00885d0c6afec26d51a53ae05e403">first_name</a></td></tr>
<tr class="separator:ad2c00885d0c6afec26d51a53ae05e403"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a12df0589010b4a79d824dbea9326ba43"><td class="memItemLeft" align="right" valign="top">string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_a_t_a___t_y_p_e.html#a12df0589010b4a79d824dbea9326ba43">last_name</a></td></tr>
<tr class="separator:a12df0589010b4a79d824dbea9326ba43"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af18aa9620a8309d88c829d6af27824e3"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_d_a_t_a___t_y_p_e.html#af18aa9620a8309d88c829d6af27824e3">key</a></td></tr>
<tr class="separator:af18aa9620a8309d88c829d6af27824e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a6a24b4532734d210a7b3a9c71a3678f5"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool DATA_TYPE::operator&lt; </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="struct_d_a_t_a___t_y_p_e.html">DATA_TYPE</a> &amp;&#160;</td>
          <td class="paramname"><em>y</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a0f17d79c6492cb604f3da783a5017b54"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">string DATA_TYPE::a</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a437ac9cab171cc3fff62212c65b3810c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">time_t DATA_TYPE::birth</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae644cc131b810b459261b3426e2c459c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">std::string DATA_TYPE::data</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad2c00885d0c6afec26d51a53ae05e403"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">string DATA_TYPE::first_name</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af18aa9620a8309d88c829d6af27824e3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int DATA_TYPE::key</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa28561fc8e223d84187ccfaf99953bae"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="floatfunctorsample_8cpp.html#ae35c40bc2f912c11f0e36ac66cba4489">KEY_TYPE</a> DATA_TYPE::key</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a12df0589010b4a79d824dbea9326ba43"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">string DATA_TYPE::last_name</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2a7008d5cecd603c1aaf8ff9ed849621"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float DATA_TYPE::net_worth</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following files:<ul>
<li>I:/modular-boost/libs/sort/example/<a class="el" href="caseinsensitive_8cpp.html">caseinsensitive.cpp</a></li>
<li>I:/modular-boost/libs/sort/example/<a class="el" href="charstringsample_8cpp.html">charstringsample.cpp</a></li>
<li>I:/modular-boost/libs/sort/example/<a class="el" href="floatfunctorsample_8cpp.html">floatfunctorsample.cpp</a></li>
<li>I:/modular-boost/libs/sort/example/<a class="el" href="generalizedstruct_8cpp.html">generalizedstruct.cpp</a></li>
<li>I:/modular-boost/libs/sort/example/<a class="el" href="keyplusdatasample_8cpp.html">keyplusdatasample.cpp</a></li>
<li>I:/modular-boost/libs/sort/example/<a class="el" href="reversestringfunctorsample_8cpp.html">reversestringfunctorsample.cpp</a></li>
<li>I:/modular-boost/libs/sort/example/<a class="el" href="stringfunctorsample_8cpp.html">stringfunctorsample.cpp</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 9 2015 14:20:24 for Boost.Sort by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> *******
</small></address>
</body>
</html>
