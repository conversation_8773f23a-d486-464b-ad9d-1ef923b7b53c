<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="generator" content="Docutils 0.14: http://docutils.sourceforge.net/" />
<title>Boost Pointer Container Library</title>
<style type="text/css">

/*
:Author: <PERSON> (<EMAIL>)
:Id: $Id: html4css1.css 7952 2016-07-26 18:15:59Z milde $
:Copyright: This stylesheet has been placed in the public domain.

Default cascading style sheet for the HTML output of Docutils.

See http://docutils.sf.net/docs/howto/html-stylesheets.html for how to
customize this style sheet.
*/

/* used to remove borders from tables and images */
.borderless, table.borderless td, table.borderless th {
  border: 0 }

table.borderless td, table.borderless th {
  /* Override padding for "table.docutils td" with "! important".
     The right padding separates the table cells. */
  padding: 0 0.5em 0 0 ! important }

.first {
  /* Override more specific margin styles with "! important". */
  margin-top: 0 ! important }

.last, .with-subtitle {
  margin-bottom: 0 ! important }

.hidden {
  display: none }

.subscript {
  vertical-align: sub;
  font-size: smaller }

.superscript {
  vertical-align: super;
  font-size: smaller }

a.toc-backref {
  text-decoration: none ;
  color: black }

blockquote.epigraph {
  margin: 2em 5em ; }

dl.docutils dd {
  margin-bottom: 0.5em }

object[type="image/svg+xml"], object[type="application/x-shockwave-flash"] {
  overflow: hidden;
}

/* Uncomment (and remove this text!) to get bold-faced definition list terms
dl.docutils dt {
  font-weight: bold }
*/

div.abstract {
  margin: 2em 5em }

div.abstract p.topic-title {
  font-weight: bold ;
  text-align: center }

div.admonition, div.attention, div.caution, div.danger, div.error,
div.hint, div.important, div.note, div.tip, div.warning {
  margin: 2em ;
  border: medium outset ;
  padding: 1em }

div.admonition p.admonition-title, div.hint p.admonition-title,
div.important p.admonition-title, div.note p.admonition-title,
div.tip p.admonition-title {
  font-weight: bold ;
  font-family: sans-serif }

div.attention p.admonition-title, div.caution p.admonition-title,
div.danger p.admonition-title, div.error p.admonition-title,
div.warning p.admonition-title, .code .error {
  color: red ;
  font-weight: bold ;
  font-family: sans-serif }

/* Uncomment (and remove this text!) to get reduced vertical space in
   compound paragraphs.
div.compound .compound-first, div.compound .compound-middle {
  margin-bottom: 0.5em }

div.compound .compound-last, div.compound .compound-middle {
  margin-top: 0.5em }
*/

div.dedication {
  margin: 2em 5em ;
  text-align: center ;
  font-style: italic }

div.dedication p.topic-title {
  font-weight: bold ;
  font-style: normal }

div.figure {
  margin-left: 2em ;
  margin-right: 2em }

div.footer, div.header {
  clear: both;
  font-size: smaller }

div.line-block {
  display: block ;
  margin-top: 1em ;
  margin-bottom: 1em }

div.line-block div.line-block {
  margin-top: 0 ;
  margin-bottom: 0 ;
  margin-left: 1.5em }

div.sidebar {
  margin: 0 0 0.5em 1em ;
  border: medium outset ;
  padding: 1em ;
  background-color: #ffffee ;
  width: 40% ;
  float: right ;
  clear: right }

div.sidebar p.rubric {
  font-family: sans-serif ;
  font-size: medium }

div.system-messages {
  margin: 5em }

div.system-messages h1 {
  color: red }

div.system-message {
  border: medium outset ;
  padding: 1em }

div.system-message p.system-message-title {
  color: red ;
  font-weight: bold }

div.topic {
  margin: 2em }

h1.section-subtitle, h2.section-subtitle, h3.section-subtitle,
h4.section-subtitle, h5.section-subtitle, h6.section-subtitle {
  margin-top: 0.4em }

h1.title {
  text-align: center }

h2.subtitle {
  text-align: center }

hr.docutils {
  width: 75% }

img.align-left, .figure.align-left, object.align-left, table.align-left {
  clear: left ;
  float: left ;
  margin-right: 1em }

img.align-right, .figure.align-right, object.align-right, table.align-right {
  clear: right ;
  float: right ;
  margin-left: 1em }

img.align-center, .figure.align-center, object.align-center {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

table.align-center {
  margin-left: auto;
  margin-right: auto;
}

.align-left {
  text-align: left }

.align-center {
  clear: both ;
  text-align: center }

.align-right {
  text-align: right }

/* reset inner alignment in figures */
div.align-right {
  text-align: inherit }

/* div.align-center * { */
/*   text-align: left } */

.align-top    {
  vertical-align: top }

.align-middle {
  vertical-align: middle }

.align-bottom {
  vertical-align: bottom }

ol.simple, ul.simple {
  margin-bottom: 1em }

ol.arabic {
  list-style: decimal }

ol.loweralpha {
  list-style: lower-alpha }

ol.upperalpha {
  list-style: upper-alpha }

ol.lowerroman {
  list-style: lower-roman }

ol.upperroman {
  list-style: upper-roman }

p.attribution {
  text-align: right ;
  margin-left: 50% }

p.caption {
  font-style: italic }

p.credits {
  font-style: italic ;
  font-size: smaller }

p.label {
  white-space: nowrap }

p.rubric {
  font-weight: bold ;
  font-size: larger ;
  color: maroon ;
  text-align: center }

p.sidebar-title {
  font-family: sans-serif ;
  font-weight: bold ;
  font-size: larger }

p.sidebar-subtitle {
  font-family: sans-serif ;
  font-weight: bold }

p.topic-title {
  font-weight: bold }

pre.address {
  margin-bottom: 0 ;
  margin-top: 0 ;
  font: inherit }

pre.literal-block, pre.doctest-block, pre.math, pre.code {
  margin-left: 2em ;
  margin-right: 2em }

pre.code .ln { color: grey; } /* line numbers */
pre.code, code { background-color: #eeeeee }
pre.code .comment, code .comment { color: #5C6576 }
pre.code .keyword, code .keyword { color: #3B0D06; font-weight: bold }
pre.code .literal.string, code .literal.string { color: #0C5404 }
pre.code .name.builtin, code .name.builtin { color: #352B84 }
pre.code .deleted, code .deleted { background-color: #DEB0A1}
pre.code .inserted, code .inserted { background-color: #A3D289}

span.classifier {
  font-family: sans-serif ;
  font-style: oblique }

span.classifier-delimiter {
  font-family: sans-serif ;
  font-weight: bold }

span.interpreted {
  font-family: sans-serif }

span.option {
  white-space: nowrap }

span.pre {
  white-space: pre }

span.problematic {
  color: red }

span.section-subtitle {
  /* font-size relative to parent (h1..h6 element) */
  font-size: 80% }

table.citation {
  border-left: solid 1px gray;
  margin-left: 1px }

table.docinfo {
  margin: 2em 4em }

table.docutils {
  margin-top: 0.5em ;
  margin-bottom: 0.5em }

table.footnote {
  border-left: solid 1px black;
  margin-left: 1px }

table.docutils td, table.docutils th,
table.docinfo td, table.docinfo th {
  padding-left: 0.5em ;
  padding-right: 0.5em ;
  vertical-align: top }

table.docutils th.field-name, table.docinfo th.docinfo-name {
  font-weight: bold ;
  text-align: left ;
  white-space: nowrap ;
  padding-left: 0 }

/* "booktabs" style (no vertical lines) */
table.docutils.booktabs {
  border: 0px;
  border-top: 2px solid;
  border-bottom: 2px solid;
  border-collapse: collapse;
}
table.docutils.booktabs * {
  border: 0px;
}
table.docutils.booktabs th {
  border-bottom: thin solid;
  text-align: left;
}

h1 tt.docutils, h2 tt.docutils, h3 tt.docutils,
h4 tt.docutils, h5 tt.docutils, h6 tt.docutils {
  font-size: 100% }

ul.auto-toc {
  list-style-type: none }

</style>
</head>
<body>
<div class="document" id="boost-pointer-container-library">
<h1 class="title"><img alt="Boost" src="boost.png" /> Pointer Container Library</h1>

<div class="section" id="class-ptr-array">
<h1>Class <tt class="docutils literal">ptr_array</tt></h1>
<p>A <tt class="docutils literal">ptr_array&lt;T,size&gt;</tt> is a pointer container that uses an underlying <tt class="docutils literal"><span class="pre">boost::array&lt;void*,size&gt;</span></tt>
to store the pointers. The class is useful when there is no requirement
of dynamic expansion and when no overhead is tolerable.</p>
<p><strong>Hierarchy:</strong></p>
<ul class="simple">
<li><a class="reference external" href="reversible_ptr_container.html">reversible_ptr_container</a><ul>
<li><a class="reference external" href="ptr_sequence_adapter.html">ptr_sequence_adapter</a><ul>
<li><a class="reference external" href="ptr_vector.html">ptr_vector</a></li>
<li><a class="reference external" href="ptr_list.html">ptr_list</a></li>
<li><a class="reference external" href="ptr_deque.html">ptr_deque</a></li>
<li><tt class="docutils literal">ptr_array</tt></li>
</ul>
</li>
</ul>
</li>
</ul>
<p><strong>Navigate:</strong></p>
<ul class="simple">
<li><a class="reference external" href="ptr_container.html">home</a></li>
<li><a class="reference external" href="reference.html">reference</a></li>
</ul>
<p><strong>Synopsis:</strong></p>
<pre class="literal-block">
namespace boost
{

    template
    &lt;
        class T,
        size_t N,
        CloneAllocator = heap_clone_allocator
    &gt;
    class ptr_array : public <em>implementation-defined</em>
    {
    public: // <a class="reference internal" href="#construct-copy-destroy">construct/copy/destroy</a>
        ptr_array();
        explicit ptr_array( const ptr_array&amp; r );
        template&lt; class U &gt;
        explicit ptr_array( const ptr_array&lt;U,N&gt;&amp; r );
        explicit ptr_array( <a class="reference external" href="compatible_smart_ptr.html"><em>compatible-smart-ptr</em></a>&lt;ptr_array&gt;&amp; r );

        ptr_array&amp; operator=( const ptr_array&amp; r );
        template&lt; class U &gt;
        ptr_array&amp; operator=( const ptr_array&lt;U,N&gt;&amp; r );
        ptr_array&amp; operator=( <a class="reference external" href="compatible_smart_ptr.html"><em>compatible-smart-ptr</em></a>&lt;this_type&gt; r );

    public: // <a class="reference external" href="reversible_ptr_container.html#iterators">iterators</a>

    public: // <a class="reference external" href="reversible_ptr_container.html#capacity">capacity</a>

    public: // <a class="reference internal" href="#element-access">element access</a>
        T&amp;        front();
        const T&amp;  front() const;
        T&amp;        back();
        const T&amp;  back() const;

        template&lt; size_t idx &gt;
        T&amp;        at();
        template&lt; size_t idx &gt;
        const T&amp;  at() const;
        T&amp;        at( size_t );
        const T&amp;  at( size_t );

        T&amp;        operator[]( size_t );
        const T&amp;  operator[]( size_t ) const;

    public: // <a class="reference internal" href="#modifiers">modifiers</a>
        void  swap( ptr_array&amp; r );

        template&lt; size_t idx &gt;
        auto_type replace( T* r );
        template&lt; size_t idx, class U &gt;
        auto_type replace( <a class="reference external" href="compatible_smart_ptr.html"><em>compatible-smart-ptr</em></a>&lt;U&gt; r );
        auto_type replace( size_t idx, T* r );
        template&lt; class U &gt;
        auto_type replace( size_t idx, <a class="reference external" href="compatible_smart_ptr.html"><em>compatible-smart-ptr</em></a>&lt;U&gt; r );

    public: // <a class="reference internal" href="#pointer-container-requirements">pointer container requirements</a>
        <a class="reference external" href="compatible_smart_ptr.html"><em>compatible-smart-ptr</em></a>&lt;ptr_array&gt;  clone() const;
        <a class="reference external" href="compatible_smart_ptr.html"><em>compatible-smart-ptr</em></a>&lt;ptr_array&gt;  release();
        template&lt; size_t idx &gt;
        bool                      is_null() const;
        bool                      is_null( size_t idx ) const;

    }; //  class 'ptr_sequence_adapter'

} // namespace 'boost'
</pre>
</div>
<div class="section" id="semantics">
<h1>Semantics</h1>
<div class="section" id="semantics-construct-copy-destroy">
<span id="construct-copy-destroy"></span><h2>Semantics: construct/copy/destroy</h2>
<ul>
<li><p class="first"><tt class="docutils literal"><span class="pre">ptr_array();</span></tt></p>
<blockquote>
<ul class="simple">
<li>Effects: constructs array where each element is null</li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal">explicit ptr_array( const ptr_array&amp; r );</tt></p>
</li>
<li><p class="first"><tt class="docutils literal">template&lt; class U &gt;
explicit ptr_array( const ptr_array&lt;U,N&gt;&amp; r );</tt></p>
<ul class="simple">
<li>Effects: Constructs array by cloning <tt class="docutils literal">r</tt></li>
</ul>
</li>
<li><p class="first"><tt class="docutils literal">ptr_array( <span class="pre"><a class="reference external" href="compatible_smart_ptr.html"><em>compatible-smart-ptr</em></a>&lt;ptr_array&gt;&amp;</span> r );</tt></p>
<blockquote>
<ul class="simple">
<li>Effects: take ownership of the supplied pointers</li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal">ptr_array&amp; operator=( const ptr_array&amp; r );</tt></p>
</li>
<li><p class="first"><tt class="docutils literal">template&lt; class U &gt; ptr_array&amp; operator=( const ptr_array&lt;U,N&gt;&amp; r );</tt></p>
<blockquote>
<ul class="simple">
<li>Effects: Assigns a clone of <tt class="docutils literal">r</tt></li>
<li>Exception safety: Strong guarantee</li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal">ptr_array&amp; operator=( <span class="pre"><a class="reference external" href="compatible_smart_ptr.html"><em>compatible-smart-ptr</em></a>&lt;this_type&gt;</span> r );</tt></p>
<blockquote>
<ul class="simple">
<li>Effects: take ownership of the supplied pointers</li>
<li>Throws: Nothing</li>
</ul>
</blockquote>
</li>
</ul>
</div>
<div class="section" id="semantics-element-access">
<span id="element-access"></span><h2>Semantics: element access</h2>
<ul>
<li><p class="first"><tt class="docutils literal">T&amp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span class="pre">front();</span></tt></p>
</li>
<li><p class="first"><tt class="docutils literal">const T&amp; front() const;</tt></p>
<blockquote>
<ul class="simple">
<li>Requirements: <tt class="docutils literal">not <span class="pre">empty();</span></tt></li>
<li>Effects: <tt class="docutils literal">return <span class="pre">*begin();</span></tt></li>
<li>Throws: <tt class="docutils literal">bad_ptr_container_operation</tt> if <tt class="docutils literal">empty() == true</tt></li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal">T&amp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span class="pre">back();</span></tt></p>
</li>
<li><p class="first"><tt class="docutils literal">const T&amp; back() const;</tt></p>
<blockquote>
<ul class="simple">
<li>Requirements: <tt class="docutils literal">not <span class="pre">empty();</span></tt></li>
<li>Effects: <tt class="docutils literal">return <span class="pre">*--end();</span></tt></li>
<li>Throws: <tt class="docutils literal">bad_ptr_container_operation</tt> if <tt class="docutils literal">empty() == true</tt></li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal">template&lt; size_t idx &gt; T&amp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; at( size_type n );</tt></p>
</li>
<li><p class="first"><tt class="docutils literal">template&lt; size_t idx &gt; const T&amp; at( size_type n ) const;</tt></p>
<blockquote>
<ul class="simple">
<li>Requirements: <tt class="docutils literal">idx &lt; size()</tt> (compile-time enforced)</li>
<li>Effects: Returns a reference to the <tt class="docutils literal">n</tt>'th element</li>
<li>Throws: nothing</li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal">T&amp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; at( size_type n );</tt></p>
</li>
<li><p class="first"><tt class="docutils literal">const T&amp; at( size_type n ) const;</tt></p>
<blockquote>
<ul class="simple">
<li>Requirements: <tt class="docutils literal">n &lt; size()</tt></li>
<li>Effects: Returns a reference to the <tt class="docutils literal">n</tt>'th element</li>
<li>Throws: <tt class="docutils literal">bad_index</tt> if <tt class="docutils literal">n &gt;=size()</tt></li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal">T&amp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span class="pre">operator[](</span> size_type n );</tt></p>
</li>
<li><p class="first"><tt class="docutils literal">const T&amp; <span class="pre">operator[](</span> size_type n ) const;</tt></p>
<blockquote>
<ul class="simple">
<li>Requirements: <tt class="docutils literal">n &lt; size()</tt></li>
<li>Effects: Returns a reference to the <tt class="docutils literal">n</tt>'th element</li>
<li>Throws: Nothing</li>
</ul>
</blockquote>
</li>
</ul>
</div>
<div class="section" id="semantics-modifiers">
<span id="modifiers"></span><h2>Semantics: modifiers</h2>
<ul>
<li><p class="first"><tt class="docutils literal">void swap( ptr_array&amp; r );</tt></p>
<blockquote>
<ul class="simple">
<li>Effects: swaps the two arrays</li>
<li>Complexity: Linear</li>
<li>Throws: nothing</li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal">template&lt; size_t idx &gt; auto_type replace( T* r );</tt></p>
<blockquote>
<ul>
<li><p class="first">Requirements:</p>
<blockquote>
<ul class="simple">
<li><tt class="docutils literal">idx &lt; size()</tt> (compile-time enforced)</li>
<li><tt class="docutils literal">r != 0</tt></li>
</ul>
</blockquote>
</li>
<li><p class="first">Effects: returns the object indexed by <tt class="docutils literal">idx</tt> and replaces it with <tt class="docutils literal">r</tt>.</p>
</li>
<li><p class="first">Throws: <tt class="docutils literal">bad_pointer</tt> if <tt class="docutils literal">x == 0</tt>.</p>
</li>
<li><p class="first">Exception safety: Strong guarantee</p>
</li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal">template&lt; size_t idx, class U &gt; auto_type replace( <span class="pre"><a class="reference external" href="compatible_smart_ptr.html"><em>compatible-smart-ptr</em></a>&lt;U&gt;</span> r );</tt></p>
<blockquote>
<ul class="simple">
<li>Effects: <tt class="docutils literal">return replace&lt;idx&gt;( r.release() );</tt></li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal">auto_type replace( size_t idx, T* r );</tt></p>
<blockquote>
<ul class="simple">
<li>Requirements: `` x != 0 and idx &lt; size()``</li>
<li>Effects: returns the object indexed by <tt class="docutils literal">idx</tt> and replaces it with <tt class="docutils literal">x</tt>.</li>
<li>Throws: <tt class="docutils literal">bad_index</tt> if <tt class="docutils literal">idx &gt;= size()</tt> and <tt class="docutils literal">bad_pointer</tt> if <tt class="docutils literal">x == 0</tt>.</li>
<li>Exception safety: Strong guarantee</li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal">template&lt; class U &gt; auto_type replace( size_t idx, <span class="pre"><a class="reference external" href="compatible_smart_ptr.html"><em>compatible-smart-ptr</em></a>&lt;U&gt;</span> r );</tt></p>
<blockquote>
<ul class="simple">
<li>Effects: <tt class="docutils literal">return replace( idx, r.release() );</tt></li>
</ul>
</blockquote>
</li>
</ul>
</div>
<div class="section" id="semantics-pointer-container-requirements">
<span id="pointer-container-requirements"></span><h2>Semantics: pointer container requirements</h2>
<ul>
<li><p class="first"><tt class="docutils literal"><span class="pre"><a class="reference external" href="compatible_smart_ptr.html"><em>compatible-smart-ptr</em></a>&lt;ptr_array&gt;</span>&nbsp; clone() const;</tt></p>
<blockquote>
<ul class="simple">
<li>Effects: Returns a deep copy of the container</li>
<li>Throws: <tt class="docutils literal"><span class="pre">std::bad_alloc</span></tt> if there is not enough memory to make a clone of the container</li>
<li>Complexity: Linear</li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal"><span class="pre"><a class="reference external" href="compatible_smart_ptr.html"><em>compatible-smart-ptr</em></a>&lt;ptr_array&gt;</span>&nbsp; <span class="pre">release();</span></tt></p>
<blockquote>
<ul class="simple">
<li>Effects: Releases ownership of the container. This is a useful way of returning a container from a function.</li>
<li>Postconditions: <tt class="docutils literal">empty() == true</tt> and all pointers are null</li>
<li>Throws: <tt class="docutils literal"><span class="pre">std::bad_alloc</span></tt> if the return value cannot be allocated</li>
<li>Exception safety: Strong guarantee</li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal">template&lt; size_t idx &gt; bool is_null() const;</tt></p>
<blockquote>
<ul class="simple">
<li>Requirements: <tt class="docutils literal">idx &lt; size()</tt> (compile-time enforced)</li>
<li>Effects: returns whether the pointer at index <tt class="docutils literal">idx</tt> is null</li>
<li>Exception safety: Nothrow guarantee</li>
</ul>
</blockquote>
</li>
<li><p class="first"><tt class="docutils literal">bool is_null( size_type idx ) const;</tt></p>
<blockquote>
<ul class="simple">
<li>Requirements: <tt class="docutils literal">idx &lt; size()</tt></li>
<li>Effects: returns whether the pointer at index <tt class="docutils literal">idx</tt> is null</li>
<li>Exception safety: Nothrow guarantee</li>
</ul>
</blockquote>
</li>
</ul>
<hr/><table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Copyright:</th><td class="field-body">Thorsten Ottosen 2004-2006. Use, modification and distribution is subject to the Boost Software License, Version 1.0 (see <a class="reference external" href="http://www.boost.org/LICENSE_1_0.txt">LICENSE_1_0.txt</a>).</td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
</body>
</html>
