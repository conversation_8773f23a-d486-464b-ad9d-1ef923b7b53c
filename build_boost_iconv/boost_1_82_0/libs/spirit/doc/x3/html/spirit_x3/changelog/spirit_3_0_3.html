<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Spirit V3.0.3 (Boost V1.69.0)</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Spirit X3 3.10">
<link rel="up" href="../changelog.html" title="Changelog">
<link rel="prev" href="spirit_3_0_4.html" title="Spirit V3.0.4 (Boost V1.70.0)">
<link rel="next" href="spirit_3_0_2.html" title="Spirit V3.0.2 (Boost V1.68.0)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="spirit_3_0_4.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../changelog.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="spirit_3_0_2.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="spirit_x3.changelog.spirit_3_0_3"></a><a class="link" href="spirit_3_0_3.html" title="Spirit V3.0.3 (Boost V1.69.0)">Spirit V3.0.3 (Boost
      V1.69.0)</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Drop own FP routines in favor of <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span></code>.
            <a href="https://github.com/boostorg/spirit/pull/392" target="_top">GH#392</a> <a href="https://svn.boost.org/trac/boost/ticket/13531" target="_top">TRAC#13531</a>
          </li>
<li class="listitem">
            Missing visibility mark on exception types. <a href="https://github.com/boostorg/spirit/pull/409" target="_top">GH#409</a>
          </li>
<li class="listitem">
            to_utf8: Fixed <code class="computeroutput"><span class="keyword">wchar_t</span></code> handling
            on Windows. <a href="https://github.com/boostorg/spirit/pull/413" target="_top">GH#413</a> <a href="https://github.com/boostorg/spirit/issues/395" target="_top">GH#395</a>
          </li>
</ul></div>
</div>
<div class="copyright-footer">Copyright © 2001-2018 Joel de Guzman,
      Hartmut Kaiser<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="spirit_3_0_4.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../changelog.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="spirit_3_0_2.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
