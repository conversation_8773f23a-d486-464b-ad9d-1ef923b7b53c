<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="generator" content="Docutils 0.14: http://docutils.sourceforge.net/" />
<title>The Boost Parameter Library Reference Documentation</title>
<meta name="authors" content="<PERSON>" />
<meta name="organization" content="BoostPro Computing" />
<meta name="date" content="2005-07-17" />
<meta name="copyright" content="Copyright <PERSON>, <PERSON> 2005-2009.  Distributed under the Boost Software License, Version 1.0.  (See accompanying file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)" />
<link rel="stylesheet" href="rst.css" type="text/css" />
</head>
<body>
<div class="document" id="the-boost-parameter-library-reference-documentation">
<h1 class="title">The Boost Parameter Library Reference Documentation</h1>
<table class="docinfo" frame="void" rules="none">
<col class="docinfo-name" />
<col class="docinfo-content" />
<tbody valign="top">
<tr><th class="docinfo-name">Authors: <AUTHORS>
<td>David Abrahams
<br />Daniel Wallin</td></tr>
<tr><th class="docinfo-name">Contact:</th>
<td><a class="first reference external" href="mailto:dave&#64;boost-consulting.com">dave&#64;boost-consulting.com</a>, <a class="last reference external" href="mailto:daniel&#64;boostpro.com">daniel&#64;boostpro.com</a></td></tr>
<tr><th class="docinfo-name">Organization:</th>
<td><a class="first last reference external" href="http://www.boostpro.com">BoostPro Computing</a></td></tr>
<tr><th class="docinfo-name">Date:</th>
<td>2005-07-17</td></tr>
<tr><th class="docinfo-name">Copyright:</th>
<td>Copyright David Abrahams, Daniel Wallin
2005-2009.  Distributed under the Boost Software License,
Version 1.0.  (See accompanying file LICENSE_1_0.txt
or copy at <a class="reference external" href="http://www.boost.org/LICENSE_1_0.txt">http://www.boost.org/LICENSE_1_0.txt</a>)</td></tr>
</tbody>
</table>
<p><a class="reference external" href="../../../../index.htm"><img alt="Boost" src="../../../../boost.png" /></a></p>
<hr class="docutils" />
<div class="contents topic" id="contents">
<p class="topic-title first">Contents</p>
<ul class="auto-toc simple">
<li><a class="reference internal" href="#preliminaries" id="id31">1&nbsp;&nbsp;&nbsp;Preliminaries</a><ul class="auto-toc">
<li><a class="reference internal" href="#namespaces" id="id32">1.1&nbsp;&nbsp;&nbsp;Namespaces</a></li>
<li><a class="reference internal" href="#exceptions" id="id33">1.2&nbsp;&nbsp;&nbsp;Exceptions</a></li>
<li><a class="reference internal" href="#thread-safety" id="id34">1.3&nbsp;&nbsp;&nbsp;Thread Safety</a></li>
<li><a class="reference internal" href="#typography" id="id35">1.4&nbsp;&nbsp;&nbsp;Typography</a></li>
</ul>
</li>
<li><a class="reference internal" href="#terminology" id="id36">2&nbsp;&nbsp;&nbsp;Terminology</a></li>
<li><a class="reference internal" href="#concepts" id="id37">3&nbsp;&nbsp;&nbsp;Concepts</a><ul class="auto-toc">
<li><a class="reference internal" href="#argumentpack" id="id38">3.1&nbsp;&nbsp;&nbsp;<span class="concept">ArgumentPack</span></a></li>
<li><a class="reference internal" href="#id3" id="id39">3.2&nbsp;&nbsp;&nbsp;<span class="concept">ParameterSpec</span></a></li>
</ul>
</li>
<li><a class="reference internal" href="#class-templates" id="id40">4&nbsp;&nbsp;&nbsp;Class Templates</a><ul class="auto-toc">
<li><a class="reference internal" href="#id5" id="id41">4.1&nbsp;&nbsp;&nbsp;<tt class="docutils literal">keyword</tt></a></li>
<li><a class="reference internal" href="#id7" id="id42">4.2&nbsp;&nbsp;&nbsp;<tt class="docutils literal">template_keyword</tt></a></li>
<li><a class="reference internal" href="#id8" id="id43">4.3&nbsp;&nbsp;&nbsp;<tt class="docutils literal">parameters</tt></a></li>
<li><a class="reference internal" href="#optional-required" id="id44">4.4&nbsp;&nbsp;&nbsp;<tt class="docutils literal">optional</tt>, <tt class="docutils literal">required</tt></a></li>
<li><a class="reference internal" href="#id9" id="id45">4.5&nbsp;&nbsp;&nbsp;<tt class="docutils literal">deduced</tt></a></li>
</ul>
</li>
<li><a class="reference internal" href="#metafunctions" id="id46">5&nbsp;&nbsp;&nbsp;Metafunctions</a><ul class="auto-toc">
<li><a class="reference internal" href="#id10" id="id47">5.1&nbsp;&nbsp;&nbsp;<tt class="docutils literal">binding</tt></a></li>
<li><a class="reference internal" href="#id11" id="id48">5.2&nbsp;&nbsp;&nbsp;<tt class="docutils literal">lazy_binding</tt></a></li>
<li><a class="reference internal" href="#id13" id="id49">5.3&nbsp;&nbsp;&nbsp;<tt class="docutils literal">value_type</tt></a></li>
<li><a class="reference internal" href="#id14" id="id50">5.4&nbsp;&nbsp;&nbsp;<tt class="docutils literal">lazy_value_type</tt></a></li>
<li><a class="reference internal" href="#id16" id="id51">5.5&nbsp;&nbsp;&nbsp;<tt class="docutils literal">are_tagged_arguments</tt></a></li>
<li><a class="reference internal" href="#id17" id="id52">5.6&nbsp;&nbsp;&nbsp;<tt class="docutils literal">is_argument_pack</tt></a></li>
<li><a class="reference internal" href="#id18" id="id53">5.7&nbsp;&nbsp;&nbsp;<tt class="docutils literal"><span class="pre">result_of::compose</span></tt></a></li>
</ul>
</li>
<li><a class="reference internal" href="#function-templates" id="id54">6&nbsp;&nbsp;&nbsp;Function Templates</a><ul class="auto-toc">
<li><a class="reference internal" href="#id19" id="id55">6.1&nbsp;&nbsp;&nbsp;<tt class="docutils literal">compose</tt></a></li>
</ul>
</li>
<li><a class="reference internal" href="#code-generation-macros" id="id56">7&nbsp;&nbsp;&nbsp;Code Generation Macros</a><ul class="auto-toc">
<li><a class="reference internal" href="#boost-parameter-function-result-name-tag-namespace-arguments" id="id57">7.1&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_FUNCTION(result, name, tag_namespace, arguments)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-member-function-result-name-tag-namespace-arguments" id="id58">7.2&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_MEMBER_FUNCTION(result, name, tag_namespace, arguments)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-const-member-function-result-name-tag-ns-arguments" id="id59">7.3&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_CONST_MEMBER_FUNCTION(result, name, tag_ns, arguments)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-function-call-operator-result-tag-namespace-arguments" id="id60">7.4&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_FUNCTION_CALL_OPERATOR(result, tag_namespace, arguments)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-const-function-call-operator-result-tag-ns-arguments" id="id61">7.5&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_CONST_FUNCTION_CALL_OPERATOR(result, tag_ns, arguments)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-constructor-cls-impl-tag-namespace-arguments" id="id62">7.6&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_CONSTRUCTOR(cls, impl, tag_namespace, arguments)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-basic-function-result-name-tag-namespace-arguments" id="id63">7.7&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_BASIC_FUNCTION(result, name, tag_namespace, arguments)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-basic-member-function-result-name-tag-ns-arguments" id="id64">7.8&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_BASIC_MEMBER_FUNCTION(result, name, tag_ns, arguments)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-basic-const-member-function-result-name-tag-ns-args" id="id65">7.9&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_BASIC_CONST_MEMBER_FUNCTION(result, name, tag_ns, args)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-basic-function-call-operator-result-tag-ns-arguments" id="id66">7.10&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_BASIC_FUNCTION_CALL_OPERATOR(result, tag_ns, arguments)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-basic-const-function-call-operator-result-tag-ns-args" id="id67">7.11&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_BASIC_CONST_FUNCTION_CALL_OPERATOR(result, tag_ns, args)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-no-spec-function-result-name" id="id68">7.12&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_FUNCTION(result, name)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-no-spec-member-function-result-name" id="id69">7.13&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_MEMBER_FUNCTION(result, name)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-no-spec-const-member-function-result-name" id="id70">7.14&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONST_MEMBER_FUNCTION(result, name)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-no-spec-function-call-operator-result" id="id71">7.15&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_FUNCTION_CALL_OPERATOR(result)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-no-spec-const-function-call-operator-result" id="id72">7.16&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONST_FUNCTION_CALL_OPERATOR(result)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-no-spec-constructor-cls-impl" id="id73">7.17&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONSTRUCTOR(cls, impl)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-no-spec-no-base-constructor-cls-impl" id="id74">7.18&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_NO_BASE_CONSTRUCTOR(cls, impl)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-name-name" id="id75">7.19&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NAME(name)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-nested-keyword-tag-namespace-name-alias" id="id76">7.20&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NESTED_KEYWORD(tag_namespace, name, alias)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-template-keyword-name" id="id77">7.21&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_TEMPLATE_KEYWORD(name)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-fun-r-n-l-h-p" id="id78">7.22&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_FUN(r, n, l, h, p)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-keyword-n-k" id="id79">7.23&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_KEYWORD(n, k)</tt></a></li>
<li><a class="reference internal" href="#boost-parameter-match-p-a-x" id="id80">7.24&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_MATCH(p, a, x)</tt></a></li>
</ul>
</li>
<li><a class="reference internal" href="#configuration-macros" id="id81">8&nbsp;&nbsp;&nbsp;Configuration Macros</a><ul class="auto-toc">
<li><a class="reference internal" href="#id21" id="id82">8.1&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a></li>
<li><a class="reference internal" href="#id22" id="id83">8.2&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_DISABLE_PERFECT_FORWARDING</tt></a></li>
<li><a class="reference internal" href="#id23" id="id84">8.3&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a></li>
<li><a class="reference internal" href="#id24" id="id85">8.4&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_DISABLE_MP11_USAGE</tt></a></li>
<li><a class="reference internal" href="#id25" id="id86">8.5&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_VARIADIC_MPL_SEQUENCE</tt></a></li>
<li><a class="reference internal" href="#id26" id="id87">8.6&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_MAX_ARITY</tt></a></li>
<li><a class="reference internal" href="#id27" id="id88">8.7&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_COMPOSE_MAX_ARITY</tt></a></li>
<li><a class="reference internal" href="#id28" id="id89">8.8&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_EXPONENTIAL_OVERLOAD_THRESHOLD_ARITY</tt></a></li>
<li><a class="reference internal" href="#outside-of-this-library" id="id90">8.9&nbsp;&nbsp;&nbsp;...Outside Of This Library</a></li>
</ul>
</li>
<li><a class="reference internal" href="#tutorial" id="id91">9&nbsp;&nbsp;&nbsp;Tutorial</a></li>
</ul>
</div>
<hr class="docutils" />
<div class="section" id="preliminaries">
<h1><a class="toc-backref" href="#id31">1&nbsp;&nbsp;&nbsp;Preliminaries</a></h1>
<p>This section covers some basic information you'll need to know in order to
understand this reference.</p>
<div class="section" id="namespaces">
<h2><a class="toc-backref" href="#id32">1.1&nbsp;&nbsp;&nbsp;Namespaces</a></h2>
<p>In this document, all unqualified identifiers should be assumed to be defined
in namespace <tt class="docutils literal"><span class="pre">boost::parameter</span></tt> unless otherwise specified.</p>
</div>
<div class="section" id="exceptions">
<h2><a class="toc-backref" href="#id33">1.2&nbsp;&nbsp;&nbsp;Exceptions</a></h2>
<p>No operation described in this document throws an exception unless otherwise
specified.</p>
</div>
<div class="section" id="thread-safety">
<h2><a class="toc-backref" href="#id34">1.3&nbsp;&nbsp;&nbsp;Thread Safety</a></h2>
<p>All components of this library can be used safely from multiple threads
without synchronization.<a class="footnote-reference" href="#thread" id="id2"><sup>1</sup></a></p>
</div>
<div class="section" id="typography">
<h2><a class="toc-backref" href="#id35">1.4&nbsp;&nbsp;&nbsp;Typography</a></h2>
<p>Names written in <span class="concept">sans serif type</span> represent <a class="reference internal" href="#concepts">concepts</a>.</p>
<p>In code blocks, <em>italic type</em> represents unspecified text that satisfies the
requirements given in the detailed description that follows the code block.</p>
<p>In a specification of the tokens generated by a macro, <strong>bold type</strong> is used
to highlight the position of the expanded macro argument in the result.</p>
<p>The special character β represents the value of <a class="reference internal" href="#boost-parameter-max-arity"><tt class="docutils literal">BOOST_PARAMETER_MAX_ARITY</tt></a>.</p>
</div>
</div>
<hr class="docutils" />
<div class="section" id="terminology">
<h1><a class="toc-backref" href="#id36">2&nbsp;&nbsp;&nbsp;Terminology</a></h1>
<dl class="docutils" id="kw">
<dt>keyword</dt>
<dd>The name of a function parameter.</dd>
</dl>
<span class="target" id="keyword-tag-type"></span><dl class="docutils">
<dt>keyword tag type</dt>
<dd>A type used to uniquely identify a function parameter.  Typically its name
will be the same as that of the parameter.</dd>
</dl>
<span class="target" id="positional"></span><dl class="docutils">
<dt>positional argument</dt>
<dd>An argument passed with no explicit keyword.  Its parameter is determined
in the usual C++ way: by position with respect to a parameter list.</dd>
</dl>
<span class="target" id="tag-type"></span><dl class="docutils">
<dt>tag type</dt>
<dd>Shorthand for “<a class="reference internal" href="#keyword-tag-type">keyword tag type</a>.”</dd>
</dl>
<span class="target" id="keyword-object"></span><dl class="docutils">
<dt>keyword object</dt>
<dd>An instance of <a class="reference internal" href="#keyword"><tt class="docutils literal">keyword</tt></a><tt class="docutils literal">&lt;T&gt;</tt> for some <a class="reference internal" href="#tag-type">tag type</a> <tt class="docutils literal">T</tt>.</dd>
</dl>
<span class="target" id="tagged-reference"></span><dl class="docutils">
<dt>tagged reference</dt>
<dd><p class="first">An object whose type is associated with a <a class="reference internal" href="#keyword-tag-type">keyword tag type</a> (the object's
<em>keyword</em>), and that holds a reference (to the object's <em>value</em>).</p>
<p class="last">As a shorthand, a “tagged reference to <tt class="docutils literal">x</tt>” means a tagged reference
whose <em>value</em> is <tt class="docutils literal">x</tt>.</p>
</dd>
</dl>
<span class="target" id="tagged-default"></span><dl class="docutils">
<dt>tagged default</dt>
<dd>A <a class="reference internal" href="#tagged-reference">tagged reference</a> whose <em>value</em> represents the value of a
default argument.</dd>
</dl>
<span class="target" id="tagged-lazy-default"></span><dl class="docutils">
<dt>tagged lazy default</dt>
<dd>A <a class="reference internal" href="#tagged-reference">tagged reference</a> whose <em>value</em>, when invoked with no arguments,
computes a default argument value.</dd>
</dl>
<span class="target" id="intended-argument-type"></span><dl class="docutils">
<dt>intended argument type</dt>
<dd>The <em>intended argument type</em> of a single-element <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> is the
type of its element's <em>value</em>.  The intended argument type of any other
type <tt class="docutils literal">X</tt> is <tt class="docutils literal">X</tt> itself.</dd>
</dl>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p class="last">In this reference, we will use concept names (and other names) to describe
both types and objects, depending on context.  So for example, “an
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>” can refer to a type that models <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>
<em>or</em> an object of such a type.</p>
</div>
</div>
<hr class="docutils" />
<div class="section" id="concepts">
<h1><a class="toc-backref" href="#id37">3&nbsp;&nbsp;&nbsp;Concepts</a></h1>
<p>This section describes the generic type concepts used by the Parameter
library.</p>
<div class="section" id="argumentpack">
<h2><a class="toc-backref" href="#id38">3.1&nbsp;&nbsp;&nbsp;<span class="concept">ArgumentPack</span></a></h2>
<p>An <span class="concept">ArgumentPack</span> is a collection of <a class="reference internal" href="#tagged-reference">tagged reference</a>s to the actual
arguments passed to a function.  Every <span class="concept">ArgumentPack</span> is also a valid <a class="reference external" href="../../../mpl/doc/refmanual/forward-sequence.html">MPL
Forward Sequence</a> and <a class="reference external" href="../../../mpl/doc/refmanual/associative-sequence.html">MPL Associative Sequence</a> consisting of the <a class="reference internal" href="#keyword-tag-type">keyword tag type</a>s in its <a class="reference internal" href="#tagged-reference">tagged reference</a>s.  If <tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt>
is defined, then every <span class="concept">ArgumentPack</span> is also a valid <a class="reference external" href="../../../mp11/doc/html/mp11.html">Boost.MP11</a> map whose
keys are <a class="reference internal" href="#keyword-tag-type">keyword tag type</a>s.  The <a class="reference external" href="../../test/singular.cpp">singular.cpp</a>, <a class="reference external" href="../../test/compose.cpp">compose.cpp</a>, and
<a class="reference external" href="../../test/mpl.cpp">mpl.cpp</a> test programs demonstrate this functionality.</p>
<div class="section" id="requirements">
<h3>Requirements</h3>
<p>In the table below,</p>
<ul class="simple">
<li><tt class="docutils literal">A</tt> is a model of <span class="concept">ArgumentPack</span></li>
<li><tt class="docutils literal">x</tt> is an instance of <tt class="docutils literal">A</tt></li>
<li><tt class="docutils literal">u</tt> is a <a class="reference internal" href="#keyword-object">keyword object</a> of type <tt class="docutils literal">K</tt></li>
<li><tt class="docutils literal">v</tt> is a <a class="reference internal" href="#tagged-default">tagged default</a> with <a class="reference internal" href="#tag-type">tag type</a> <tt class="docutils literal">L</tt> and <em>value</em> of type <tt class="docutils literal">D</tt></li>
<li><tt class="docutils literal">w</tt> is a <a class="reference internal" href="#tagged-lazy-default">tagged lazy default</a> with <a class="reference internal" href="#tag-type">tag type</a> <tt class="docutils literal">M</tt> and <em>value</em> of type <tt class="docutils literal">E const</tt></li>
<li><tt class="docutils literal">z</tt> is an <span class="concept">ArgumentPack</span> containing a single element (as created by <a class="reference internal" href="#keyword"><tt class="docutils literal">keyword</tt></a><tt class="docutils literal"><span class="pre">&lt;…&gt;::operator=</span></tt>)</li>
</ul>
<p>Any exceptions thrown from the invocation of <tt class="docutils literal">w</tt>'s <em>value</em>
will be propagated to the caller.</p>
<table border="1" class="docutils">
<caption><span class="concept">ArgumentPack</span> requirements</caption>
<colgroup>
<col width="14%" />
<col width="40%" />
<col width="19%" />
<col width="27%" />
</colgroup>
<thead valign="bottom">
<tr><th class="head">Expression</th>
<th class="head">Type</th>
<th class="head">Requirements</th>
<th class="head">Semantics/Notes</th>
</tr>
</thead>
<tbody valign="top">
<tr><td><tt class="docutils literal">x[u]</tt></td>
<td><tt class="docutils literal">binding&lt;A, <span class="pre">K&gt;::type</span></tt></td>
<td><tt class="docutils literal">x</tt> contains
an element <em>b</em>
whose <a class="reference internal" href="#kw">keyword</a> is
<tt class="docutils literal">K</tt></td>
<td>Returns <em>b</em>'s
<em>value</em> (by
reference).</td>
</tr>
<tr><td><tt class="docutils literal">x[u]</tt></td>
<td><tt class="docutils literal">binding&lt;A, L, <span class="pre">D&gt;::type</span></tt></td>
<td><em>none</em></td>
<td>If <tt class="docutils literal">x</tt> contains an
element <em>b</em> whose
<a class="reference internal" href="#kw">keyword</a> is the same as
<tt class="docutils literal">u</tt>'s, returns
<em>b</em>'s <em>value</em> (by
reference).
Otherwise, returns
<tt class="docutils literal">u</tt>'s <em>value</em>.</td>
</tr>
<tr><td><tt class="docutils literal">x[w]</tt></td>
<td><tt class="docutils literal">lazy_binding&lt;A, M, <span class="pre">E&gt;::type</span></tt></td>
<td><em>none</em></td>
<td>If <tt class="docutils literal">x</tt> contains an
element <em>b</em> whose
<a class="reference internal" href="#kw">keyword</a> is the same as
<tt class="docutils literal">w</tt>'s, returns
<em>b</em>'s <em>value</em> (by
reference).
Otherwise, invokes
<tt class="docutils literal">w</tt>'s <em>value</em>
and returns the
result.</td>
</tr>
<tr><td><tt class="docutils literal">x, z</tt></td>
<td>Model of <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a></td>
<td><em>none</em></td>
<td>Returns an
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>
containing all the
elements of both
<tt class="docutils literal">x</tt> and <tt class="docutils literal">z</tt>.</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="section" id="id3">
<span id="parameterspec"></span><h2><a class="toc-backref" href="#id39">3.2&nbsp;&nbsp;&nbsp;<span class="concept">ParameterSpec</span></a></h2>
<p>A <span class="concept">ParameterSpec</span> describes the type requirements for arguments corresponding
to a given <a class="reference internal" href="#kw">keyword</a> and indicates whether the argument is optional or
required.  The table below details the allowed forms and describes their
condition for satisfaction by an actual argument type. In each row,</p>
<ul class="simple" id="conditions">
<li><tt class="docutils literal">K</tt> is the <span class="concept">ParameterSpec</span>'s <a class="reference internal" href="#keyword-tag-type">keyword tag type</a></li>
<li><tt class="docutils literal">A</tt> is an <a class="reference internal" href="#intended-argument-type">intended argument type</a> associated with <tt class="docutils literal">K</tt>, if any</li>
<li><tt class="docutils literal">P</tt> is a model of <span class="concept">ArgumentPack</span> that contains <tt class="docutils literal">A</tt></li>
<li><tt class="docutils literal">F</tt> is an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary Metafunction Class</a></li>
</ul>
<table border="1" class="docutils">
<caption><span class="concept">ParameterSpec</span> allowed forms and conditions of satisfaction</caption>
<colgroup>
<col width="32%" />
<col width="14%" />
<col width="54%" />
</colgroup>
<thead valign="bottom">
<tr><th class="head">Type</th>
<th class="head"><tt class="docutils literal">A</tt>
required</th>
<th class="head">Condition <tt class="docutils literal">A</tt> must satisfy</th>
</tr>
</thead>
<tbody valign="top">
<tr><td><tt class="docutils literal">K</tt></td>
<td>no</td>
<td><em>n/a</em></td>
</tr>
<tr><td><a class="reference internal" href="#optional"><tt class="docutils literal">optional</tt></a><tt class="docutils literal">&lt;K,F&gt;</tt></td>
<td>no</td>
<td><tt class="docutils literal"><span class="pre">mpl::apply2&lt;F,A,P&gt;::type::value</span></tt> is
<tt class="docutils literal">true</tt>.</td>
</tr>
<tr><td><a class="reference internal" href="#required"><tt class="docutils literal">required</tt></a><tt class="docutils literal">&lt;K,F&gt;</tt></td>
<td>yes</td>
<td><tt class="docutils literal"><span class="pre">mpl::apply2&lt;F,A,P&gt;::type::value</span></tt> is
<tt class="docutils literal">true</tt>.</td>
</tr>
</tbody>
</table>
<p>The information in a <span class="concept">ParameterSpec</span> is used to <a class="reference external" href="index.html#controlling-overload-resolution">limit</a> the arguments that
will be matched by <a class="reference external" href="index.html#forwarding-functions">forwarding functions</a>.</p>
</div>
</div>
<hr class="docutils" />
<div class="section" id="class-templates">
<h1><a class="toc-backref" href="#id40">4&nbsp;&nbsp;&nbsp;Class Templates</a></h1>
<div class="section" id="id5">
<span id="keyword"></span><h2><a class="toc-backref" href="#id41">4.1&nbsp;&nbsp;&nbsp;<tt class="docutils literal">keyword</tt></a></h2>
<p>The type of every <a class="reference internal" href="#keyword-object">keyword object</a> is a specialization of <tt class="docutils literal">keyword</tt>.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/keyword.hpp">boost/parameter/keyword.hpp</a></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename Tag&gt;
struct keyword
{
    typedef Tag tag;

    template &lt;typename T&gt;
    constexpr typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
        typename <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
            <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_scalar.html">boost::is_scalar</a>&lt;T&gt;
          , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for scalar types.
          , <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
                <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                    typename Tag::qualifier
                  , boost::parameter::in_reference
                &gt;
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for &quot;in&quot; references.
              , <a class="reference external" href="../../../mpl/doc/refmanual/if.html">mpl::if_</a>&lt;
                    <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                        typename Tag::qualifier
                      , boost::parameter::forward_reference
                    &gt;
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for &quot;forward&quot; references.
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for all other reference categories.
                &gt;
            &gt;
        &gt;::type
      , <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>
    &gt;::type
        <a class="reference internal" href="#assignment-operator"><tt class="docutils literal">operator=</tt></a>(T const&amp; value) const;

    template &lt;typename T&gt;
    constexpr typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
        typename <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
            typename <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
                <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                    typename Tag::qualifier
                  , boost::parameter::out_reference
                &gt;
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // The reference category is &quot;out&quot;.
              , <a class="reference external" href="../../../mpl/doc/refmanual/if.html">mpl::if_</a>&lt;
                    <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                        typename Tag::qualifier
                      , boost::parameter::forward_reference
                    &gt;
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // The reference category is &quot;forward&quot;.
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // The reference category is neither &quot;out&quot; nor &quot;forward&quot;.
                &gt;
            &gt;::type
          , <a class="reference external" href="../../../mpl/doc/refmanual/if.html">mpl::if_</a>&lt;
                <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_const.html">boost::is_const</a>&lt;T&gt;
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for reference-to-const types.
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for referece-to-mutable types.
            &gt;
          , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for references neither &quot;out&quot; nor &quot;forward&quot;.
        &gt;::type
      , <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>
    &gt;::type
        <a class="reference internal" href="#assignment-operator"><tt class="docutils literal">operator=</tt></a>(T&amp; value) const;

    template &lt;typename T&gt;
    constexpr typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
        typename <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
            <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_scalar.html">boost::is_scalar</a>&lt;T&gt;
          , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for scalar types.
          , <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
                <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                    typename Tag::qualifier
                  , boost::parameter::in_reference
                &gt;
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for &quot;in&quot; references.
              , <a class="reference external" href="../../../mpl/doc/refmanual/if.html">mpl::if_</a>&lt;
                    <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                        typename Tag::qualifier
                      , boost::parameter::forward_reference
                    &gt;
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for &quot;forward&quot; references.
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for all other reference categories.
                &gt;
            &gt;
        &gt;::type
      , <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>
    &gt;::type
        <a class="reference internal" href="#assignment-operator"><tt class="docutils literal">operator=</tt></a>(T const&amp;&amp; value) const;

    template &lt;typename T&gt;
    constexpr typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
        typename <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
            <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_scalar.html">boost::is_scalar</a>&lt;T&gt;
          , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for scalar types.
          , <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
                <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                    typename Tag::qualifier
                  , boost::parameter::consume_reference
                &gt;
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for &quot;consume&quot; references.
              , <a class="reference external" href="../../../mpl/doc/refmanual/if.html">mpl::if_</a>&lt;
                    <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                        typename Tag::qualifier
                      , boost::parameter::forward_reference
                    &gt;
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for &quot;forward&quot; references.
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for all other reference categories.
                &gt;
            &gt;
        &gt;::type
      , <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>
    &gt;::type
        <a class="reference internal" href="#assignment-operator"><tt class="docutils literal">operator=</tt></a>(T&amp;&amp; value) const;

    template &lt;typename T&gt;
    constexpr typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
        typename <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
            <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_scalar.html">boost::is_scalar</a>&lt;T&gt;
          , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for scalar types.
          , <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
                <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                    typename Tag::qualifier
                  , boost::parameter::in_reference
                &gt;
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for &quot;in&quot; references.
              , <a class="reference external" href="../../../mpl/doc/refmanual/if.html">mpl::if_</a>&lt;
                    <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                        typename Tag::qualifier
                      , boost::parameter::forward_reference
                    &gt;
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for &quot;forward&quot; references.
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for all other reference categories.
                &gt;
            &gt;
        &gt;::type
      , <em>tagged default</em>
    &gt;::type
        <a class="reference internal" href="#bitwise-or-operator"><tt class="docutils literal">operator|</tt></a>(T const&amp; x) const;

    template &lt;typename T&gt;
    constexpr typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
        typename <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
            typename <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
                <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                    typename Tag::qualifier
                  , boost::parameter::out_reference
                &gt;
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // The reference category is &quot;out&quot;.
              , <a class="reference external" href="../../../mpl/doc/refmanual/if.html">mpl::if_</a>&lt;
                    <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                        typename Tag::qualifier
                      , boost::parameter::forward_reference
                    &gt;
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // The reference category is &quot;forward&quot;.
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // The reference category is neither &quot;out&quot; nor &quot;forward&quot;.
                &gt;
            &gt;::type
          , <a class="reference external" href="../../../mpl/doc/refmanual/if.html">mpl::if_</a>&lt;
                <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_const.html">boost::is_const</a>&lt;T&gt;
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for reference-to-const types.
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for referece-to-mutable types.
            &gt;
          , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for references neither &quot;out&quot; nor &quot;forward&quot;.
        &gt;::type
      , <em>tagged default</em>
    &gt;::type
        <a class="reference internal" href="#bitwise-or-operator"><tt class="docutils literal">operator|</tt></a>(T&amp; x) const;

    template &lt;typename T&gt;
    constexpr typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
        typename <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
            <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_scalar.html">boost::is_scalar</a>&lt;T&gt;
          , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for scalar types.
          , <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
                <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                    typename Tag::qualifier
                  , boost::parameter::in_reference
                &gt;
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for &quot;in&quot; references.
              , <a class="reference external" href="../../../mpl/doc/refmanual/if.html">mpl::if_</a>&lt;
                    <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                        typename Tag::qualifier
                      , boost::parameter::forward_reference
                    &gt;
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for &quot;forward&quot; references.
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for all other reference categories.
                &gt;
            &gt;
        &gt;::type
      , <em>tagged default</em>
    &gt;::type
        <a class="reference internal" href="#bitwise-or-operator"><tt class="docutils literal">operator|</tt></a>(T const&amp;&amp; x) const;

    template &lt;typename T&gt;
    constexpr typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
        typename <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
            <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_scalar.html">boost::is_scalar</a>&lt;T&gt;
          , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for scalar types.
          , <a class="reference external" href="../../../mpl/doc/refmanual/eval-if.html">boost::mpl::eval_if</a>&lt;
                <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                    typename Tag::qualifier
                  , boost::parameter::consume_reference
                &gt;
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for &quot;consume&quot; references.
              , <a class="reference external" href="../../../mpl/doc/refmanual/if.html">mpl::if_</a>&lt;
                    <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                        typename Tag::qualifier
                      , boost::parameter::forward_reference
                    &gt;
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> // Enable this overload for &quot;forward&quot; references.
                  , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // Disable this overload for all other reference categories.
                &gt;
            &gt;
        &gt;::type
      , <em>tagged default</em>
    &gt;::type constexpr
        <a class="reference internal" href="#bitwise-or-operator"><tt class="docutils literal">operator|</tt></a>(T&amp;&amp; value) const;

    template &lt;typename F&gt;
    constexpr <em>tagged lazy default</em> <a class="reference internal" href="#logical-or-operator"><tt class="docutils literal">operator||</tt></a>(F const&amp;) const;

    template &lt;typename F&gt;
    constexpr <em>tagged lazy default</em> <a class="reference internal" href="#logical-or-operator"><tt class="docutils literal">operator||</tt></a>(F&amp;) const;

    static keyword&lt;Tag&gt; const&amp; instance;

    static keyword&lt;Tag&gt;&amp; <a class="reference internal" href="#get">get</a>();
};
</pre>
<p id="assignment-operator"><tt class="docutils literal">operator=</tt></p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Synopsis:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename T&gt;
constexpr <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> operator=(T const&amp; value) const;

template &lt;typename T&gt;
constexpr <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> operator=(T&amp; value) const;

template &lt;typename T&gt;
constexpr <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> operator=(T const&amp;&amp; value) const;

template &lt;typename T&gt;
constexpr <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> operator=(T&amp;&amp; value) const;
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body">one of the following:</td>
</tr>
</tbody>
</table>
<ul class="simple">
<li>The nested <tt class="docutils literal">qualifier</tt> type of <tt class="docutils literal">Tag</tt> must be <tt class="docutils literal">forward_reference</tt>.</li>
<li>To use the <tt class="docutils literal">const</tt> lvalue reference overload, <tt class="docutils literal">T</tt> must be scalar, or
the nested <tt class="docutils literal">qualifier</tt> type of <tt class="docutils literal">Tag</tt> must be <tt class="docutils literal">in_reference</tt>.</li>
<li>To use the mutable lvalue reference overload, the nested <tt class="docutils literal">qualifier</tt>
type of <tt class="docutils literal">Tag</tt> must be <tt class="docutils literal">out_reference</tt> or <tt class="docutils literal">in_out_reference</tt>, and
<tt class="docutils literal">T</tt> must not be <tt class="docutils literal">const</tt>-qualified.</li>
<li>To use the <tt class="docutils literal">const</tt> rvalue reference overload for non-scalar <tt class="docutils literal">T</tt>, the
nested <tt class="docutils literal">qualifier</tt> type of <tt class="docutils literal">Tag</tt> must be <tt class="docutils literal">in_reference</tt>.</li>
<li>To use the mutable rvalue reference overload for non-scalar <tt class="docutils literal">T</tt>, the
nested <tt class="docutils literal">qualifier</tt> type of <tt class="docutils literal">Tag</tt> must be <tt class="docutils literal">consume_reference</tt> or
<tt class="docutils literal">move_from_reference</tt>.</li>
</ul>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Returns:</th><td class="field-body">an <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>  containing a single <a class="reference internal" href="#tagged-reference">tagged reference</a> to
<tt class="docutils literal">value</tt> with <a class="reference internal" href="#kw">keyword</a> <tt class="docutils literal">Tag</tt></td>
</tr>
</tbody>
</table>
<p id="bitwise-or-operator"><tt class="docutils literal">operator|</tt></p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Synopsis:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename T&gt;
constexpr <em>tagged default</em> operator|(T const&amp; x) const;

template &lt;typename T&gt;
constexpr <em>tagged default</em> operator|(T&amp; x) const;

template &lt;typename T&gt;
constexpr <em>tagged default</em> operator|(T const&amp;&amp; x) const;

template &lt;typename T&gt;
constexpr <em>tagged default</em> operator|(T&amp;&amp; x) const;
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body">one of the following:</td>
</tr>
</tbody>
</table>
<ul class="simple">
<li>The nested <tt class="docutils literal">qualifier</tt> type of <tt class="docutils literal">Tag</tt> must be <tt class="docutils literal">forward_reference</tt>.</li>
<li>To use the <tt class="docutils literal">const</tt> lvalue reference overload, <tt class="docutils literal">T</tt> must be scalar, or
the nested <tt class="docutils literal">qualifier</tt> type of <tt class="docutils literal">Tag</tt> must be <tt class="docutils literal">in_reference</tt>.</li>
<li>To use the mutable lvalue reference overload, the nested <tt class="docutils literal">qualifier</tt>
type of <tt class="docutils literal">Tag</tt> must be <tt class="docutils literal">out_reference</tt> or <tt class="docutils literal">in_out_reference</tt>, and
<tt class="docutils literal">T</tt> must not be <tt class="docutils literal">const</tt>-qualified.</li>
<li>To use the <tt class="docutils literal">const</tt> rvalue reference overload for non-scalar <tt class="docutils literal">T</tt>, the
nested <tt class="docutils literal">qualifier</tt> type of <tt class="docutils literal">Tag</tt> must be <tt class="docutils literal">in_reference</tt>.</li>
<li>To use the mutable rvalue reference overload for non-scalar <tt class="docutils literal">T</tt>, the
nested <tt class="docutils literal">qualifier</tt> type of <tt class="docutils literal">Tag</tt> must be <tt class="docutils literal">consume_reference</tt> or
<tt class="docutils literal">move_from_reference</tt>.</li>
</ul>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Returns:</th><td class="field-body">a <a class="reference internal" href="#tagged-default">tagged default</a> with <em>value</em> <tt class="docutils literal">x</tt> and <a class="reference internal" href="#kw">keyword</a> <tt class="docutils literal">Tag</tt>.</td>
</tr>
</tbody>
</table>
<p id="logical-or-operator"><tt class="docutils literal">operator||</tt></p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Synopsis:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename F&gt;
constexpr <em>tagged lazy default</em> operator||(F const&amp; g) const;

template &lt;typename F&gt;
constexpr <em>tagged lazy default</em> operator||(F&amp; g) const;
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body"><tt class="docutils literal">g()</tt> must be valid, with type
<a class="reference external" href="../../../utility/utility.htm#result_of"><tt class="docutils literal"><span class="pre">boost::result_of</span></tt></a><tt class="docutils literal"><span class="pre">&lt;F()&gt;::type</span></tt>.<a class="footnote-reference" href="#no-result-of" id="id6"><sup>2</sup></a></td>
</tr>
<tr class="field"><th class="field-name">Returns:</th><td class="field-body">a <a class="reference internal" href="#tagged-lazy-default">tagged lazy default</a> with <em>value</em> <tt class="docutils literal">g</tt> and <a class="reference internal" href="#kw">keyword</a> <tt class="docutils literal">Tag</tt>.</td>
</tr>
</tbody>
</table>
<p id="instance"><tt class="docutils literal">instance</tt></p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Synopsis:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
static keyword&lt;Tag&gt; const&amp; instance;
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Returns:</th><td class="field-body">a “singleton instance”: the same object will be returned on each
invocation of <tt class="docutils literal">instance</tt>.</td>
</tr>
<tr class="field"><th class="field-name">Thread Safety:</th><td class="field-body"><tt class="docutils literal">instance</tt> can be accessed from multiple threads simultaneously.</td>
</tr>
</tbody>
</table>
<p id="get"><tt class="docutils literal">get</tt></p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Synopsis:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
static keyword&lt;Tag&gt;&amp; get();
</pre>
<div class="admonition admonition-deprecated">
<p class="first admonition-title">Deprecated</p>
<p class="last">This function has been deprecated in favor of <tt class="docutils literal">instance</tt>.</p>
</div>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Returns:</th><td class="field-body">a “singleton instance”: the same object will be returned on each
invocation of <tt class="docutils literal">get()</tt>.</td>
</tr>
<tr class="field"><th class="field-name">Thread Safety:</th><td class="field-body"><tt class="docutils literal">get()</tt> can be called from multiple threads simultaneously.</td>
</tr>
</tbody>
</table>
</div>
<div class="section" id="id7">
<span id="template-keyword"></span><h2><a class="toc-backref" href="#id42">4.2&nbsp;&nbsp;&nbsp;<tt class="docutils literal">template_keyword</tt></a></h2>
<p>This class template encapsulates a named template parameter.  Every type
generated by the <a class="reference internal" href="#boost-parameter-template-keyword"><tt class="docutils literal">BOOST_PARAMETER_TEMPLATE_KEYWORD</tt></a> macro is a specialization
of <tt class="docutils literal">template_keyword</tt>.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/template_keyword.hpp">boost/parameter/template_keyword.hpp</a></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename Tag, typename T&gt;
struct template_keyword
{
    typedef Tag key_type;
    typedef T value_type;
    typedef <em>implementation defined</em> reference;
};
</pre>
<p>The <a class="reference external" href="../../test/ntp.cpp">test/ntp.cpp</a> test program demonstrates proper usage of this class template.</p>
</div>
<div class="section" id="id8">
<span id="parameters"></span><h2><a class="toc-backref" href="#id43">4.3&nbsp;&nbsp;&nbsp;<tt class="docutils literal">parameters</tt></a></h2>
<p>Provides an interface for assembling the actual arguments to a <cite>forwarding
function</cite> into an <span class="concept">ArgumentPack</span>, in which any <a class="reference internal" href="#positional">positional</a> arguments will be
tagged according to the corresponding template argument to <tt class="docutils literal">parameters</tt>.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/parameters.hpp">boost/parameter/parameters.hpp</a></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename ...PSpec&gt;
struct parameters
{
    template &lt;typename ...Args&gt;
    struct <a class="reference internal" href="#match"><tt class="docutils literal">match</tt></a>
    {
        typedef … type;
    };

    template &lt;typename ...Args&gt;
    <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> <a class="reference internal" href="#function-call-operator"><tt class="docutils literal">operator()</tt></a>(Args&amp;&amp;... args) const;
};
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body">Each element in the <tt class="docutils literal">PSpec</tt> parameter pack must be a model of
<a class="reference internal" href="#parameterspec"><span class="concept">ParameterSpec</span></a>.</td>
</tr>
</tbody>
</table>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p>In this section, <tt class="docutils literal">R</tt> ## <em>i</em> and <tt class="docutils literal">K</tt> ## <em>i</em> are defined as
follows, for any argument type <tt class="docutils literal">A</tt> ## <em>i</em>:</p>
<div class="last line-block">
<div class="line">let <tt class="docutils literal">D0</tt> the set [d0, …, d ## <em>j</em>] of all <strong>deduced</strong></div>
<div class="line"><em>parameter specs</em> in the <tt class="docutils literal">PSpec</tt> parameter pack</div>
<div class="line"><tt class="docutils literal">R</tt> ## <em>i</em> is the <a class="reference internal" href="#intended-argument-type">intended argument type</a> of <tt class="docutils literal">A</tt> ## <em>i</em></div>
<div class="line"><br /></div>
<div class="line">if <tt class="docutils literal">A</tt> ## <em>i</em> is a result type of <tt class="docutils literal"><span class="pre">keyword&lt;T&gt;::</span></tt> <a class="reference internal" href="#assignment-operator"><tt class="docutils literal">operator=</tt></a></div>
<div class="line">then</div>
<div class="line-block">
<div class="line"><tt class="docutils literal">K</tt> ## <em>i</em> is <tt class="docutils literal">T</tt></div>
</div>
<div class="line">else</div>
<div class="line-block">
<div class="line">if some <tt class="docutils literal">A</tt> ## <em>j</em> where <em>j</em> ≤ <em>i</em> is a result type of</div>
<div class="line"><tt class="docutils literal"><span class="pre">keyword&lt;T&gt;::</span></tt> <a class="reference internal" href="#assignment-operator"><tt class="docutils literal">operator=</tt></a></div>
<div class="line"><em>or</em> some <tt class="docutils literal">P</tt> ## <em>j</em> in <em>j</em> ≤ <em>i</em> is <strong>deduced</strong></div>
<div class="line">then</div>
<div class="line-block">
<div class="line">if some <em>parameter spec</em> <tt class="docutils literal">d</tt> ## <em>j</em> in <tt class="docutils literal">D</tt> ## <em>i</em></div>
<div class="line">matches <tt class="docutils literal">A</tt> ## <em>i</em></div>
<div class="line">then</div>
<div class="line-block">
<div class="line"><tt class="docutils literal">K</tt> ## <em>i</em> is the <a class="reference internal" href="#keyword-tag-type">keyword tag type</a> of <tt class="docutils literal">d</tt> ## <em>j</em>.</div>
<div class="line"><tt class="docutils literal">D</tt><sub>i+1</sub> is <tt class="docutils literal">D</tt> ## <em>i</em> - [ <tt class="docutils literal">d</tt> ## <em>j</em>]</div>
</div>
</div>
<div class="line">else</div>
<div class="line-block">
<div class="line"><tt class="docutils literal">K</tt> ## <em>i</em> is the <a class="reference internal" href="#keyword-tag-type">keyword tag type</a> of <tt class="docutils literal">P</tt> ## <em>i</em>.</div>
</div>
</div>
</div>
</div>
<dl class="docutils" id="match">
<dt><tt class="docutils literal">match</tt></dt>
<dd>A <a class="reference external" href="../../../mpl/doc/refmanual/metafunction.html">Metafunction</a> used to remove a <a class="reference external" href="index.html#forwarding-functions">forwarding function</a> from overload
resolution.</dd>
</dl>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Returns:</th><td class="field-body">if all elements in <tt class="docutils literal"><span class="pre">Params...</span></tt> are <em>satisfied</em> (see below), then
<tt class="docutils literal"><span class="pre">parameters&lt;Params...&gt;</span></tt>.  Otherwise, <tt class="docutils literal"><span class="pre">match&lt;Args...&gt;::type</span></tt> is not
defined.</td>
</tr>
</tbody>
</table>
<p>Each element <tt class="docutils literal">P</tt> in <tt class="docutils literal"><span class="pre">Params...</span></tt> is <strong>satisfied</strong> if either:</p>
<ul class="simple">
<li><tt class="docutils literal">P</tt> is the <em>unspecified</em> default</li>
<li><strong>or</strong>, <tt class="docutils literal">P</tt> is a <em>keyword tag type</em></li>
<li><dl class="first docutils">
<dt><strong>or</strong>, <tt class="docutils literal">P</tt> is <a class="reference internal" href="#optional"><tt class="docutils literal">optional</tt></a> <tt class="docutils literal">&lt;X,F&gt;</tt> and either</dt>
<dd><ul class="first last">
<li><tt class="docutils literal">X</tt> is not <tt class="docutils literal">K</tt> ## <em>i</em> for any <em>i</em>,</li>
<li><dl class="first docutils">
<dt><strong>or</strong> <tt class="docutils literal">X</tt> is some <tt class="docutils literal">K</tt> ## <em>i</em>  and <tt class="docutils literal"><span class="pre">mpl::apply&lt;F,R</span></tt> ## <em>i</em></dt>
<dd><tt class="docutils literal"><span class="pre">&gt;::type::value</span></tt> is <tt class="docutils literal">true</tt></dd>
</dl>
</li>
</ul>
</dd>
</dl>
</li>
<li><dl class="first docutils">
<dt><strong>or</strong>, <tt class="docutils literal">P</tt> is <a class="reference internal" href="#required"><tt class="docutils literal">required</tt></a> <tt class="docutils literal">&lt;X,F&gt;</tt>, and</dt>
<dd><ul class="first last">
<li><tt class="docutils literal">X</tt> is some <tt class="docutils literal">K</tt> ## <em>i</em>, <strong>and</strong></li>
<li><tt class="docutils literal"><span class="pre">mpl::apply&lt;F,R</span></tt> ## <em>i</em> <tt class="docutils literal"><span class="pre">&gt;::type::value</span></tt> is <tt class="docutils literal">true</tt></li>
</ul>
</dd>
</dl>
</li>
</ul>
<p id="function-call-operator"><tt class="docutils literal">operator()</tt></p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Synopsis:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename ...Args&gt;
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> operator()(Args&amp;&amp;... args) const;
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Returns:</th><td class="field-body"><p class="first">An <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> containing, for each <tt class="docutils literal">a</tt> ## <em>i</em>,</p>
<ul class="last simple">
<li>if <tt class="docutils literal">a</tt> ## <em>i</em>  is a single-element <span class="concept">ArgumentPack</span>, its element</li>
<li><dl class="first docutils">
<dt>Otherwise, a <a class="reference internal" href="#tagged-reference">tagged reference</a> with <a class="reference internal" href="#kw">keyword</a> <tt class="docutils literal">K</tt> ## <em>i</em> and <em>value</em></dt>
<dd><tt class="docutils literal">a</tt> ## <em>i</em></dd>
</dl>
</li>
</ul>
</td>
</tr>
</tbody>
</table>
</div>
<div class="section" id="optional-required">
<span id="required"></span><span id="optional"></span><h2><a class="toc-backref" href="#id44">4.4&nbsp;&nbsp;&nbsp;<tt class="docutils literal">optional</tt>, <tt class="docutils literal">required</tt></a></h2>
<p>These templates describe the requirements on a function parameter.</p>
<p><tt class="docutils literal">optional</tt> is defined in: <a class="reference external" href="../../../../boost/parameter/optional.hpp">boost/parameter/optional.hpp</a></p>
<p><tt class="docutils literal">required</tt> is defined in: <a class="reference external" href="../../../../boost/parameter/required.hpp">boost/parameter/required.hpp</a></p>
<p>Both headers are included by: <a class="reference external" href="../../../../boost/parameter/preprocessor.hpp">boost/parameter/preprocessor.hpp</a></p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name" colspan="2">Specializations model:</th></tr>
<tr class="field"><td>&nbsp;</td><td class="field-body"><a class="reference internal" href="#parameterspec"><span class="concept">ParameterSpec</span></a></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename Tag, typename Predicate = <em>unspecified</em>&gt;
struct optional;

template &lt;typename Tag, typename Predicate = <em>unspecified</em>&gt;
struct required;
</pre>
<p>The default value of <tt class="docutils literal">Predicate</tt> is an unspecified <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary Metafunction
Class</a> that returns <tt class="docutils literal"><span class="pre">mpl::true_</span></tt> for any argument.  If
<a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> is defined, then the default value of
<tt class="docutils literal">Predicate</tt> is also a <a class="reference external" href="../../../mp11/doc/html/mp11.html">Boost.MP11</a>-style quoted metafunction that returns
<tt class="docutils literal"><span class="pre">mp11::mp_true</span></tt> for any argument.</p>
</div>
<div class="section" id="id9">
<span id="deduced"></span><h2><a class="toc-backref" href="#id45">4.5&nbsp;&nbsp;&nbsp;<tt class="docutils literal">deduced</tt></a></h2>
<p>This template is used to wrap the <em>keyword tag</em> argument to
<tt class="docutils literal">optional</tt> or <tt class="docutils literal">required</tt>.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/deduced.hpp">boost/parameter/deduced.hpp</a></td>
</tr>
<tr class="field"><th class="field-name">Included by:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor.hpp">boost/parameter/preprocessor.hpp</a></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename Tag&gt;
struct deduced;
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body">nothing</td>
</tr>
</tbody>
</table>
</div>
</div>
<hr class="docutils" />
<div class="section" id="metafunctions">
<h1><a class="toc-backref" href="#id46">5&nbsp;&nbsp;&nbsp;Metafunctions</a></h1>
<p>A <a class="reference external" href="../../../mpl/doc/refmanual/metafunction.html">Metafunction</a> is conceptually a function that operates on, and returns,
C++ types.</p>
<div class="section" id="id10">
<span id="binding"></span><h2><a class="toc-backref" href="#id47">5.1&nbsp;&nbsp;&nbsp;<tt class="docutils literal">binding</tt></a></h2>
<p>Returns the result type of indexing an argument pack with a
<a class="reference internal" href="#keyword-tag-type">keyword tag type</a> or with a <a class="reference internal" href="#tagged-default">tagged default</a>.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/binding.hpp">boost/parameter/binding.hpp</a></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename A, typename K, typename D = void_&gt;
struct binding
{
    typedef … type;
};
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body"><tt class="docutils literal">A</tt> must be a model of <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>.</td>
</tr>
<tr class="field"><th class="field-name">Returns:</th><td class="field-body">the reference type of the <a class="reference internal" href="#tagged-reference">tagged reference</a> in <tt class="docutils literal">A</tt> having
<a class="reference internal" href="#keyword-tag-type">keyword tag type</a> <tt class="docutils literal">K</tt>, if any.  If no such <a class="reference internal" href="#tagged-reference">tagged reference</a> exists,
returns <tt class="docutils literal">D</tt>.</td>
</tr>
</tbody>
</table>
</div>
<div class="section" id="id11">
<span id="lazy-binding"></span><h2><a class="toc-backref" href="#id48">5.2&nbsp;&nbsp;&nbsp;<tt class="docutils literal">lazy_binding</tt></a></h2>
<p>Returns the result type of indexing an argument pack with a
<a class="reference internal" href="#tagged-lazy-default">tagged lazy default</a>.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/binding.hpp">boost/parameter/binding.hpp</a></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename A, typename K, typename F&gt;
struct lazy_binding
{
    typedef … type;
};
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body"><tt class="docutils literal">A</tt> must be a model of <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>.</td>
</tr>
<tr class="field"><th class="field-name">Returns:</th><td class="field-body">the reference type of the <a class="reference internal" href="#tagged-reference">tagged reference</a> in <tt class="docutils literal">A</tt> having
<a class="reference internal" href="#keyword-tag-type">keyword tag type</a> <tt class="docutils literal">K</tt>, if any.  If no such <a class="reference internal" href="#tagged-reference">tagged reference</a> exists,
returns <a class="reference external" href="../../../utility/utility.htm#result_of"><tt class="docutils literal"><span class="pre">boost::result_of</span></tt></a><tt class="docutils literal"><span class="pre">&lt;F()&gt;::type</span></tt>.<a class="footnote-reference" href="#no-result-of" id="id12"><sup>2</sup></a></td>
</tr>
</tbody>
</table>
</div>
<div class="section" id="id13">
<span id="value-type"></span><h2><a class="toc-backref" href="#id49">5.3&nbsp;&nbsp;&nbsp;<tt class="docutils literal">value_type</tt></a></h2>
<p>Returns the result type of indexing an argument pack with a
<a class="reference internal" href="#keyword-tag-type">keyword tag type</a> or with a <a class="reference internal" href="#tagged-default">tagged default</a>.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/value_type.hpp">boost/parameter/value_type.hpp</a></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename A, typename K, typename D = void_&gt;
struct value_type
{
    typedef … type;
};
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body"><p class="first"><tt class="docutils literal">A</tt> must be a model of <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>.</p>
</td>
</tr>
<tr class="field"><th class="field-name">Returns:</th><td class="field-body"><p class="first">the (possibly const-qualified) type of the <a class="reference internal" href="#tagged-reference">tagged reference</a> in
<tt class="docutils literal">A</tt> having <a class="reference internal" href="#keyword-tag-type">keyword tag type</a> <tt class="docutils literal">K</tt>, if any.  If no such
<a class="reference internal" href="#tagged-reference">tagged reference</a> exists, returns <tt class="docutils literal">D</tt>.  Equivalent to:</p>
<pre class="literal-block">
typename <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/remove_reference.html">boost::remove_reference</a>&lt;
    typename <a class="reference internal" href="#binding"><tt class="docutils literal">binding</tt></a>&lt;A, K, D&gt;::type
&gt;::type
</pre>
<p class="last">… when <tt class="docutils literal">D</tt> is not a reference type.</p>
</td>
</tr>
</tbody>
</table>
</div>
<div class="section" id="id14">
<span id="lazy-value-type"></span><h2><a class="toc-backref" href="#id50">5.4&nbsp;&nbsp;&nbsp;<tt class="docutils literal">lazy_value_type</tt></a></h2>
<p>Returns the result type of indexing an argument pack with a
<a class="reference internal" href="#tagged-lazy-default">tagged lazy default</a>.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/value_type.hpp">boost/parameter/value_type.hpp</a></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename A, typename K, typename F&gt;
struct lazy_value_type
{
    typedef … type;
};
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body"><tt class="docutils literal">A</tt> must be a model of <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>.</td>
</tr>
<tr class="field"><th class="field-name">Returns:</th><td class="field-body">the (possibly const-qualified) type of the <a class="reference internal" href="#tagged-reference">tagged reference</a> in
<tt class="docutils literal">A</tt> having <a class="reference internal" href="#keyword-tag-type">keyword tag type</a> <tt class="docutils literal">K</tt>, if any.  If no such
<a class="reference internal" href="#tagged-reference">tagged reference</a> exists, returns
<a class="reference external" href="../../../utility/utility.htm#result_of"><tt class="docutils literal"><span class="pre">boost::result_of</span></tt></a><tt class="docutils literal"><span class="pre">&lt;F()&gt;::type</span></tt>.<a class="footnote-reference" href="#no-result-of" id="id15"><sup>2</sup></a></td>
</tr>
</tbody>
</table>
</div>
<div class="section" id="id16">
<span id="are-tagged-arguments"></span><h2><a class="toc-backref" href="#id51">5.5&nbsp;&nbsp;&nbsp;<tt class="docutils literal">are_tagged_arguments</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/are_tagged_arguments.hpp">boost/parameter/are_tagged_arguments.hpp</a></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename T0, typename ...Pack&gt;
struct are_tagged_arguments
    // : <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> if T0 and all elements in Pack are
    // tagged reference types, <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> otherwise.
{
};
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Returns:</th><td class="field-body"><tt class="docutils literal"><span class="pre">mpl::true_</span></tt> if <tt class="docutils literal">T0</tt> and all elements in parameter pack <tt class="docutils literal">Pack</tt> are
<a class="reference internal" href="#tagged-reference">tagged reference</a> types, <tt class="docutils literal"><span class="pre">mpl::false_</span></tt> otherwise.</td>
</tr>
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>When implementing a Boost.Parameter-enabled constructor for a container that
conforms to the C++ standard, one needs to remember that the standard requires
the presence of other constructors that are typically defined as templates,
such as range constructors.  To avoid overload ambiguities between the two
constructors, use this metafunction in conjunction with <tt class="docutils literal">disable_if</tt> to
define the range constructor.</p>
<pre class="literal-block">
template &lt;typename B&gt;
class frontend : public B
{
    struct _enabler
    {
    };

 public:
    <a class="reference internal" href="#boost-parameter-no-spec-constructor"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONSTRUCTOR</tt></a>(frontend, (B))

    template &lt;typename Iterator&gt;
    frontend(
        Iterator itr
      , Iterator itr_end
      , typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>&lt;
            are_tagged_arguments&lt;Iterator&gt;
          , _enabler
        &gt;::type = _enabler()
    ) : B(itr, itr_end)
    {
    }
};
</pre>
</div>
<div class="section" id="id17">
<span id="is-argument-pack"></span><h2><a class="toc-backref" href="#id52">5.6&nbsp;&nbsp;&nbsp;<tt class="docutils literal">is_argument_pack</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/is_argument_pack.hpp">boost/parameter/is_argument_pack.hpp</a></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename T&gt;
struct is_argument_pack
    // : <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a> if T is a model of <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>,
    // <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> otherwise.
{
};
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Returns:</th><td class="field-body"><tt class="docutils literal"><span class="pre">mpl::true_</span></tt> if <tt class="docutils literal">T</tt> is a model of <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, <tt class="docutils literal"><span class="pre">mpl::false_</span></tt>
otherwise.</td>
</tr>
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>To avoid overload ambiguities between a constructor that takes in an
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> and a templated conversion constructor, use this
metafunction in conjunction with <tt class="docutils literal">enable_if</tt>.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(a0)

template &lt;typename T&gt;
class backend0
{
    struct _enabler
    {
    };

    T a0;

 public:
    template &lt;typename ArgPack&gt;
    explicit backend0(
        ArgPack const&amp; args
      , typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
            is_argument_pack&lt;ArgPack&gt;
          , _enabler
        &gt;::type = _enabler()
    ) : a0(args[_a0])
    {
    }

    template &lt;typename U&gt;
    backend0(
        backend0&lt;U&gt; const&amp; copy
      , typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
            <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_convertible.html">boost::is_convertible</a>&lt;U,T&gt;
          , _enabler
        &gt;::type = _enabler()
    ) : a0(copy.get_a0())
    {
    }

    T const&amp; get_a0() const
    {
        return this-&gt;a0;
    }
};
</pre>
</div>
<div class="section" id="id18">
<span id="result-of-compose"></span><h2><a class="toc-backref" href="#id53">5.7&nbsp;&nbsp;&nbsp;<tt class="docutils literal"><span class="pre">result_of::compose</span></tt></a></h2>
<p>Returns the result type of the <a class="reference internal" href="#compose"><tt class="docutils literal">compose</tt></a> function.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/compose.hpp">boost/parameter/compose.hpp</a></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename ...TaggedArgs&gt;
struct compose
  : <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
        <a class="reference internal" href="#are-tagged-arguments"><tt class="docutils literal">are_tagged_arguments</tt></a>&lt;T0,Pack...&gt;
      , <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>
    &gt;
{
};

template &lt;&gt;
struct compose&lt;&gt;
{
    typedef <em>empty</em> <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> type;
};
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body">All elements in <tt class="docutils literal">TaggedArgs</tt> must be <a class="reference internal" href="#tagged-reference">tagged reference</a> types, if
specified.</td>
</tr>
<tr class="field"><th class="field-name">Returns:</th><td class="field-body">the result type of the <a class="reference internal" href="#compose"><tt class="docutils literal">compose</tt></a> function.</td>
</tr>
</tbody>
</table>
</div>
</div>
<hr class="docutils" />
<div class="section" id="function-templates">
<h1><a class="toc-backref" href="#id54">6&nbsp;&nbsp;&nbsp;Function Templates</a></h1>
<div class="section" id="id19">
<span id="compose"></span><h2><a class="toc-backref" href="#id55">6.1&nbsp;&nbsp;&nbsp;<tt class="docutils literal">compose</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/compose.hpp">boost/parameter/compose.hpp</a></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename ...Pack&gt;
constexpr typename <a class="reference internal" href="#result-of-compose"><tt class="docutils literal"><span class="pre">result_of::compose</span></tt></a>&lt;Pack...&gt;::type
    compose(Pack const&amp;... args);
</pre>
<p>This function facilitates easier variadic argument composition.  It is used by
the <a class="reference internal" href="#boost-parameter-no-spec-function"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_FUNCTION</tt></a>,
<a class="reference internal" href="#boost-parameter-no-spec-member-function"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_MEMBER_FUNCTION</tt></a>,
<a class="reference internal" href="#boost-parameter-no-spec-const-member-function"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONST_MEMBER_FUNCTION</tt></a>,
<a class="reference internal" href="#boost-parameter-no-spec-function-call-operator"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_FUNCTION_CALL_OPERATOR</tt></a>,
<a class="reference internal" href="#boost-parameter-no-spec-const-function-call-operator"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONST_FUNCTION_CALL_OPERATOR</tt></a>,
<a class="reference internal" href="#boost-parameter-no-spec-constructor"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONSTRUCTOR</tt></a>, and
<a class="reference internal" href="#boost-parameter-no-spec-no-base-constructor"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_NO_BASE_CONSTRUCTOR</tt></a> code generation macros.  You
can use it to write your own code generation macros if the ones provided by
this library do not suffice.</p>
<p>Unlike the <a class="reference internal" href="#tagged-reference">tagged reference</a> comma operator, the <tt class="docutils literal">compose()</tt> function is
variadic, as mentioned before.  However, the <a class="reference internal" href="#tagged-reference">tagged reference</a> comma operator
can be invoked indefinitely and therefore does not limit the size of the
resulting <span class="concept">ArgumentPack</span>, while the <tt class="docutils literal">compose()</tt> function cannot take in more
than <a class="reference internal" href="#boost-parameter-compose-max-arity"><tt class="docutils literal">BOOST_PARAMETER_COMPOSE_MAX_ARITY</tt></a> arguments for compilers that do not
support perfect forwarding.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body">All elements in <tt class="docutils literal">args</tt> must be <a class="reference internal" href="#tagged-reference">tagged reference</a> objects, if
specified.</td>
</tr>
<tr class="field"><th class="field-name">Returns:</th><td class="field-body">an <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> containing all elements in <tt class="docutils literal">args</tt>, if
specified; an empty <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> otherwise.</td>
</tr>
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
BOOST_PARAMETER_NAME(index)
BOOST_PARAMETER_NAME(name)

template &lt;typename ArgumentPack&gt;
int print_name_and_index(ArgumentPack const&amp; args)
{
    std::cout &lt;&lt; &quot;index = &quot; &lt;&lt; args[_index];
    std::cout &lt;&lt; &quot;name = &quot; &lt;&lt; args[_name];
    std::cout &lt;&lt; &quot;; &quot; &lt;&lt; std::endl;
    return 0;
}

int y = print_name_and_index(compose(_index = 3, _name = &quot;jones&quot;));
</pre>
<p>The <a class="reference external" href="../../test/compose.cpp">compose.cpp</a> test program shows more examples using this function.</p>
</div>
</div>
<hr class="docutils" />
<div class="section" id="code-generation-macros">
<h1><a class="toc-backref" href="#id56">7&nbsp;&nbsp;&nbsp;Code Generation Macros</a></h1>
<p>Macros in this section can be used to ease the writing of code
using the Parameter library by eliminating repetitive boilerplate.</p>
<div class="section" id="boost-parameter-function-result-name-tag-namespace-arguments">
<span id="boost-parameter-function"></span><h2><a class="toc-backref" href="#id57">7.1&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_FUNCTION(result, name, tag_namespace, arguments)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor.hpp">boost/parameter/preprocessor.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a function that can take in positional arguments, composed
arguments, named arguments, and deduced arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>The return type of each of the following function templates falls under a
different value category.</p>
<pre class="literal-block">
template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; rvalue_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const rvalue_const_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp; lvalue_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; lset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return lset;
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp; lvalue_const_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const clset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return clset;
}
</pre>
<p>The <tt class="docutils literal"><span class="pre">U::evaluate_category</span></tt> static member function template has a simple job:
to return the correct value category when passed in an object returned by one
of the functions defined above.  Assume that
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is defined.</p>
<pre class="literal-block">
enum invoked
{
    passed_by_lvalue_reference_to_const
  , passed_by_lvalue_reference
  , passed_by_rvalue_reference_to_const
  , passed_by_rvalue_reference
};

struct U
{
    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;)
    {
        return passed_by_lvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;)
    {
        return passed_by_lvalue_reference;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;&amp;)
    {
        return passed_by_rvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;&amp;)
    {
        return passed_by_rvalue_reference;
    }
};
</pre>
<p>Define the named parameters that will comprise the argument specification that
this macro will use.  Ensure that all their tag types are in the same
namespace, which is <tt class="docutils literal">kw</tt> in this case.  The identifiers with leading
underscores can be passed to the bracket operator of <tt class="docutils literal">args</tt> to extract the
same argument to which the corresponding named parameter (without underscores)
is bound, as will be shown later.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lrc, kw) in(lrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lr, kw) in_out(lr))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rrc, kw) in(rrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rr, kw) consume(rr))
</pre>
<p>Use the macro as a substitute for a normal function header.  Enclose the
return type <tt class="docutils literal">bool</tt> in parentheses.  For each parameter, also enclose the
expected value type in parentheses.  Since the value types are mutually
exclusive, you can wrap the parameters in a <tt class="docutils literal">(deduced …)</tt>
clause.  Otherwise, just as with a normal function, the order in which you
specify the parameters determines their position.  Also, just as with a normal
function, optional parameters have default values, whereas required parameters
do not.  Within the function body, either simply use the parameter name or
pass the matching identifier with the leading underscore to the bracket
operator of <tt class="docutils literal">args</tt> to extract the corresponding argument.  Note that the
second method doesn't require <tt class="docutils literal"><span class="pre">std::forward</span></tt> to preserve value categories.</p>
<pre class="literal-block">
BOOST_PARAMETER_FUNCTION((bool), evaluate, kw,
    (deduced
        (required
            (lrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;1&gt;))
            (lr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;2&gt;))
        )
        (optional
            (rrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;3&gt;), rvalue_const_bitset&lt;2&gt;())
            (rr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;4&gt;), rvalue_bitset&lt;3&gt;())
        )
    )
)
{
    BOOST_TEST_EQ(
        passed_by_lvalue_reference_to_const
      , U::evaluate_category&lt;0&gt;(lrc)
    );
    BOOST_TEST_EQ(
        passed_by_lvalue_reference
      , U::evaluate_category&lt;1&gt;(lr)
    );
    BOOST_TEST_EQ(
        passed_by_rvalue_reference_to_const
      , U::evaluate_category&lt;2&gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;rrc0_type&gt;(rrc0))
    );
    BOOST_TEST_EQ(
        passed_by_rvalue_reference
      , U::evaluate_category&lt;3&gt;(args[_rr0])
    );

    return true;
}
</pre>
<p>The following function calls are legal.</p>
<pre class="literal-block">
evaluate(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
  , rvalue_bitset&lt;3&gt;()
);
evaluate(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
);
evaluate((  // composed arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
));
evaluate(  // named arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
);
evaluate(  // named arguments
    _lr0 = lvalue_bitset&lt;1&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>Because the parameters were wrapped in a <tt class="docutils literal">(deduced …)</tt> clause, the following
function calls are also legal.</p>
<pre class="literal-block">
evaluate(  // deduced arguments
    rvalue_bitset&lt;3&gt;()
  , lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
);
evaluate(  // deduced arguments
    lvalue_bitset&lt;1&gt;()
  , lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>The <a class="reference external" href="../../test/preprocessor.cpp">preprocessor.cpp</a>, <a class="reference external" href="../../test/preprocessor_deduced.cpp">preprocessor_deduced.cpp</a>, and
<a class="reference external" href="../../test/preprocessor_eval_category.cpp">preprocessor_eval_category.cpp</a> test programs demonstrate proper usage of this
macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function.</li>
<li><tt class="docutils literal">name</tt> is the base name of the function; it determines the name of the
generated forwarding functions.</li>
<li><tt class="docutils literal">tag_namespace</tt> is the namespace in which the keywords used by the
function resides.</li>
<li><tt class="docutils literal">arguments</tt> is a <a class="reference external" href="../../../preprocessor/doc/index.html">Boost.Preprocessor</a> <a class="reference external" href="../../../preprocessor/doc/data/sequences.html">sequence</a> of
<em>argument-specifiers</em>, as defined below.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<pre class="literal-block">
argument-specifiers ::= <em>specifier-group0</em> {<em>specifier-group0</em>}

specifier-group0 ::= <em>specifier-group1</em> |
    (
        '<strong>(</strong>' '<strong>deduced</strong>'
            <em>specifier-group1</em> {<em>specifier-group1</em>}
        '<strong>)</strong>'
    )

specifier-group1 ::=
    (
        '<strong>(</strong>' '<strong>optional</strong>'
            <em>optional-specifier</em> {<em>optional-specifier</em>}
        '<strong>)</strong>'
    ) | (
        '<strong>(</strong>' '<strong>required</strong>'
            <em>required-specifier</em> {<em>required-specifier</em>}
        '<strong>)</strong>'
    )

optional-specifier ::=
    '<strong>(</strong>'
        <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> '<strong>,</strong>' <em>default-value</em>
    ')'

required-specifier ::=
    '<strong>(</strong>' <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> ')'

restriction ::=
    ( '<strong>*</strong>' '<strong>(</strong>' <em>mfc</em> '<strong>)</strong>' ) |
    ( '<strong>(</strong>' <em>type-name</em> '<strong>)</strong>' ) |
    '<strong>*</strong>'
</pre>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">argument-name</span></tt> is any valid C++ identifier.</li>
<li><tt class="docutils literal"><span class="pre">default-value</span></tt> is any valid C++ expression; if necessary, user code can
compute it in terms of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>, where <tt class="docutils literal"><span class="pre">previous-name</span></tt>
is the <tt class="docutils literal"><span class="pre">argument-name</span></tt> in a previous <tt class="docutils literal"><span class="pre">specifier-group0</span></tt> or
<tt class="docutils literal"><span class="pre">specifier-group1</span></tt>.  <em>This expression will be invoked exactly once.</em></li>
<li><tt class="docutils literal">mfc</tt> is an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary Metafunction Class</a> whose first argument will
be the type of the corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument
will be the entire <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is a <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean
Integral Constant</a>; however, user code <em>cannot</em> compute <tt class="docutils literal">mfc</tt> in terms
of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>.</li>
<li><tt class="docutils literal"><span class="pre">type-name</span></tt> is either the name of a <strong>target type</strong> or an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary
Metafunction Class</a> whose first argument will be the type of the
corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument will be the entire
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is the <strong>target type</strong>.  If
<tt class="docutils literal">restriction</tt> uses this form, then the type of the generated name
<tt class="docutils literal"><span class="pre">argument-name</span> ## _type</tt> will be computed in terms of the <strong>target
type</strong>, and the generated reference <tt class="docutils literal"><span class="pre">argument-name</span></tt> (but not its
corresponding entry in <tt class="docutils literal">args</tt>) will be cast to that type.</li>
</ul>
<p><strong>Approximate expansion:</strong></p>
<p>Where:</p>
<ul class="simple">
<li><tt class="docutils literal">n</tt> denotes the <em>minimum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
<li><tt class="docutils literal">m</tt> denotes the <em>maximum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
</ul>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename Args&gt;
using boost_param_result_ ## __LINE__ ## <strong>name</strong> = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename Args&gt;
struct boost_param_result_ ## __LINE__ ## <strong>name</strong>
{
    typedef <strong>result</strong> type;
};

struct boost_param_params_ ## __LINE__ ## <strong>name</strong>
  : <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        <em>list of parameter specifications, based on arguments</em>
    &gt;
{
};

typedef boost_param_params_ ## __LINE__ ## <strong>name</strong>
    boost_param_parameters_ ## __LINE__ ## <strong>name</strong>;

template &lt;typename Args&gt;
typename boost_param_result_ ## __LINE__ ## <strong>name</strong>&lt;Args&gt;::type
    boost_param_impl ## __LINE__ ## <strong>name</strong>(Args const&amp;);

template &lt;typename A0, …, typename A ## <strong>n</strong>&gt;
<strong>result</strong> <strong>name</strong>(
    A0&amp;&amp; a0, …, A ## <strong>n</strong>&amp;&amp; a ## <strong>n</strong>
  , typename boost_param_parameters_ ## __LINE__ ## <strong>name</strong>
    ::match&lt;A0, …, A ## <strong>n</strong>&gt;::type
    = boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()
)
{
    return boost_param_impl ## __LINE__ ## <strong>name</strong>(
        boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>n</strong>&gt;(a ## <strong>n</strong>)
        )
    );
}

<span class="vellipsis">⋮</span>

template &lt;typename A0, …, typename A ## <strong>m</strong>&gt;
<strong>result</strong> <strong>name</strong>(
    A0&amp;&amp; a0, …, A ## <strong>m</strong>&amp;&amp; a ## <strong>m</strong>
  , typename boost_param_parameters_ ## __LINE__ ## <strong>name</strong>
    ::match&lt;A0, …, A ## <strong>m</strong>&gt;::type
    = boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()
)
{
    return boost_param_impl ## __LINE__ ## <strong>name</strong>(
        boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>m</strong>&gt;(a ## <strong>m</strong>)
        )
    );
}

template &lt;
    typename ResultType
  , typename Args
  , typename <em>argument name</em> ## <strong>0</strong> ## _type
  , …
  , typename <em>argument name</em> ## <strong>n</strong> ## _type
&gt;
ResultType
    boost_param_dispatch_0boost_ ## __LINE__ ## <strong>name</strong>(
        (ResultType(*)())
      , Args const&amp; args
      , <em>argument name</em> ## <strong>0</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>0</strong>
      , …
      , <em>argument name</em> ## <strong>n</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>m</strong>
    );

<span class="vellipsis">⋮</span>

template &lt;
    typename ResultType
  , typename Args
  , typename <em>argument name</em> ## <strong>0</strong> ## _type
  , …
  , typename <em>argument name</em> ## <strong>m</strong> ## _type
&gt;
ResultType
    boost_param_dispatch_0boost_ ## __LINE__ ## <strong>name</strong>(
        (ResultType(*)())
      , Args const&amp; args
      , <em>argument name</em> ## <strong>0</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>0</strong>
      , …
      , <em>argument name</em> ## <strong>m</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>m</strong>
    );

template &lt;typename Args&gt;
typename boost_param_result_ ## __LINE__ ## <strong>name</strong>&lt;Args&gt;::type
    boost_param_impl ## __LINE__ ## <strong>name</strong>(Args const&amp; args)
{
    return boost_param_dispatch_0boost_ ## __LINE__ ## <strong>name</strong>(
        static_cast&lt;
            typename boost_param_result_ ## __LINE__ ## <strong>name</strong>&lt;
                Args
            &gt;::type(*)()
        &gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , args
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of required parameter</em> ## <strong>0</strong>
            &gt;::type
        &gt;(args[ <em>keyword object of required parameter</em> ## <strong>0</strong>])
      , …
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of required parameter</em> ## <strong>n</strong>
            &gt;::type
        &gt;(args[ <em>keyword object of required parameter</em> ## <strong>n</strong>])
    );
}

template &lt;
    typename ResultType
  , typename Args
  , typename <em>argument name</em> ## <strong>0</strong> ## _type
  , …
  , typename <em>argument name</em> ## <strong>n</strong> ## _type
&gt;
ResultType
    boost_param_dispatch_0boost_ ## __LINE__ ## <strong>name</strong>(
        (ResultType(*)())
      , Args const&amp; args
      , <em>argument name</em> ## <strong>0</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>0</strong>
      , …
      , <em>argument name</em> ## <strong>n</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>n</strong>
    )
{
    return boost_param_dispatch_0boost_ ## __LINE__ ## <strong>name</strong>(
        static_cast&lt;ResultType(*)()&gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , (args, <em>keyword object of optional parameter</em> ## <strong>n + 1</strong> =
            <em>default value of optional parameter</em> ## <strong>n + 1</strong>
        )
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;<em>argument name</em> ## <strong>0</strong> ## _type&gt;(
            <em>argument name</em> ## <strong>0</strong>
        )
      , …
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;<em>argument name</em> ## <strong>n</strong> ## _type&gt;(
            <em>argument name</em> ## <strong>n</strong>
        )
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of optional parameter</em> ## <strong>n + 1</strong>
            &gt;::type
        &gt;(<em>default value of optional parameter</em> ## <strong>n + 1</strong>)
    );
}

<span class="vellipsis">⋮</span>

template &lt;
    typename ResultType
  , typename Args
  , typename <em>argument name</em> ## <strong>0</strong> ## _type
  , …
  , typename <em>argument name</em> ## <strong>m</strong> ## _type
&gt;
ResultType
    boost_param_dispatch_0boost_ ## __LINE__ ## <strong>name</strong>(
        (ResultType(*)())
      , Args const&amp; args
      , <em>argument name</em> ## <strong>0</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>0</strong>
      , …
      , <em>argument name</em> ## <strong>m</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>m</strong>
    )
</pre>
</div>
<div class="section" id="boost-parameter-member-function-result-name-tag-namespace-arguments">
<span id="boost-parameter-member-function"></span><h2><a class="toc-backref" href="#id58">7.2&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_MEMBER_FUNCTION(result, name, tag_namespace, arguments)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor.hpp">boost/parameter/preprocessor.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a member function that can take in positional arguments, composed
arguments, named arguments, and deduced arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>The return type of each of the following function templates falls under a
different value category.</p>
<pre class="literal-block">
template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; rvalue_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const rvalue_const_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp; lvalue_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; lset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return lset;
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp; lvalue_const_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const clset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return clset;
}
</pre>
<p>The <tt class="docutils literal"><span class="pre">U::evaluate_category</span></tt> static member function template has a simple job:
to return the correct value category when passed in an object returned by one
of the functions defined above.  Assume that
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is defined.</p>
<pre class="literal-block">
enum invoked
{
    passed_by_lvalue_reference_to_const
  , passed_by_lvalue_reference
  , passed_by_rvalue_reference_to_const
  , passed_by_rvalue_reference
};

struct U
{
    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;)
    {
        return passed_by_lvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;)
    {
        return passed_by_lvalue_reference;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;&amp;)
    {
        return passed_by_rvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;&amp;)
    {
        return passed_by_rvalue_reference;
    }
};
</pre>
<p>Define the named parameters that will comprise the argument specification that
this macro will use.  Ensure that all their tag types are in the same
namespace, which is <tt class="docutils literal">kw</tt> in this case.  The identifiers with leading
underscores can be passed to the bracket operator of <tt class="docutils literal">args</tt> to extract the
same argument to which the corresponding named parameter (without underscores)
is bound, as will be shown later.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lrc, kw) in(lrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lr, kw) in_out(lr))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rrc, kw) in(rrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rr, kw) consume(rr))
</pre>
<p>Use the macro as a substitute for a normal <tt class="docutils literal">static</tt> member function
header.  Enclose the return type <tt class="docutils literal">bool</tt> in parentheses.  For each parameter,
also enclose the expected value type in parentheses.  Since the value types
are mutually exclusive, you can wrap the parameters in a <tt class="docutils literal">(deduced …)</tt>
clause.  Otherwise, just as with a normal function, the order in which you
specify the parameters determines their position.  Also, just as with a normal
function, optional parameters have default values, whereas required parameters
do not.  Within the function body, either simply use the parameter name or
pass the matching identifier with the leading underscore to the bracket
operator of <tt class="docutils literal">args</tt> to extract the corresponding argument.  Note that the
second method doesn't require <tt class="docutils literal"><span class="pre">std::forward</span></tt> to preserve value categories.</p>
<pre class="literal-block">
struct B
{
    BOOST_PARAMETER_MEMBER_FUNCTION((bool), static evaluate, kw,
        (deduced
            (required
                (lrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;1&gt;))
                (lr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;2&gt;))
            )
            (optional
                (rrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;3&gt;), rvalue_const_bitset&lt;2&gt;())
                (rr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;4&gt;), rvalue_bitset&lt;3&gt;())
            )
        )
    )
    {
        BOOST_TEST_EQ(
            passed_by_lvalue_reference_to_const
          , U::evaluate_category&lt;0&gt;(lrc)
        );
        BOOST_TEST_EQ(
            passed_by_lvalue_reference
          , U::evaluate_category&lt;1&gt;(lr)
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference_to_const
          , U::evaluate_category&lt;2&gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;rrc0_type&gt;(rrc0))
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference
          , U::evaluate_category&lt;3&gt;(args[_rr0])
        );

        return true;
    }
};
</pre>
<p>The following function calls are legal.</p>
<pre class="literal-block">
B::evaluate(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
  , rvalue_bitset&lt;3&gt;()
);
B::evaluate(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
);
B::evaluate((  // composed arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
));
B::evaluate(  // named arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
);
B::evaluate(  // named arguments
    _lr0 = lvalue_bitset&lt;1&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>Because the parameters were wrapped in a <tt class="docutils literal">(deduced …)</tt> clause, the following
function calls are also legal.</p>
<pre class="literal-block">
B::evaluate(  // deduced arguments
    rvalue_bitset&lt;3&gt;()
  , lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
);
B::evaluate(  // deduced arguments
    lvalue_bitset&lt;1&gt;()
  , lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>The <a class="reference external" href="../../test/preprocessor.cpp">preprocessor.cpp</a> and <a class="reference external" href="../../test/preprocessor_eval_category.cpp">preprocessor_eval_category.cpp</a> test programs
demonstrate proper usage of this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function.</li>
<li><tt class="docutils literal">name</tt> is the base name of the function; it determines the name of the
generated forwarding functions.  <tt class="docutils literal">name</tt> may be qualified by the
<tt class="docutils literal">static</tt> keyword to declare the member function and its helpers as not
associated with any object of the enclosing type.</li>
<li><tt class="docutils literal">tag_namespace</tt> is the namespace in which the keywords used by the
function resides.</li>
<li><tt class="docutils literal">arguments</tt> is a <a class="reference external" href="../../../preprocessor/doc/index.html">Boost.Preprocessor</a> <a class="reference external" href="../../../preprocessor/doc/data/sequences.html">sequence</a> of
<em>argument-specifiers</em>, as defined below.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<pre class="literal-block">
argument-specifiers ::= <em>specifier-group0</em> {<em>specifier-group0</em>}

specifier-group0 ::= <em>specifier-group1</em> |
    (
        '<strong>(</strong>' '<strong>deduced</strong>'
            <em>specifier-group1</em> {<em>specifier-group1</em>}
        '<strong>)</strong>'
    )

specifier-group1 ::=
    (
        '<strong>(</strong>' '<strong>optional</strong>'
            <em>optional-specifier</em> {<em>optional-specifier</em>}
        '<strong>)</strong>'
    ) | (
        '<strong>(</strong>' '<strong>required</strong>'
            <em>required-specifier</em> {<em>required-specifier</em>}
        '<strong>)</strong>'
    )

optional-specifier ::=
    '<strong>(</strong>'
        <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> '<strong>,</strong>' <em>default-value</em>
    ')'

required-specifier ::=
    '<strong>(</strong>' <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> ')'

restriction ::=
    ( '<strong>*</strong>' '<strong>(</strong>' <em>mfc</em> '<strong>)</strong>' ) |
    ( '<strong>(</strong>' <em>type-name</em> '<strong>)</strong>' ) |
    '<strong>*</strong>'
</pre>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">argument-name</span></tt> is any valid C++ identifier.</li>
<li><tt class="docutils literal"><span class="pre">default-value</span></tt> is any valid C++ expression; if necessary, user code can
compute it in terms of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>, where <tt class="docutils literal"><span class="pre">previous-name</span></tt>
is the <tt class="docutils literal"><span class="pre">argument-name</span></tt> in a previous <tt class="docutils literal"><span class="pre">specifier-group0</span></tt> or
<tt class="docutils literal"><span class="pre">specifier-group1</span></tt>.  <em>This expression will be invoked exactly once.</em></li>
<li><tt class="docutils literal">mfc</tt> is an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary Metafunction Class</a> whose first argument will
be the type of the corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument
will be the entire <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is a <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean
Integral Constant</a>; however, user code <em>cannot</em> compute <tt class="docutils literal">mfc</tt> in terms
of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>.</li>
<li><tt class="docutils literal"><span class="pre">type-name</span></tt> is either the name of a <strong>target type</strong> or an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary
Metafunction Class</a> whose first argument will be the type of the
corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument will be the entire
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is the <strong>target type</strong>.  If
<tt class="docutils literal">restriction</tt> uses this form, then the type of the generated name
<tt class="docutils literal"><span class="pre">argument-name</span> ## _type</tt> will be computed in terms of the <strong>target
type</strong>, and the generated reference <tt class="docutils literal"><span class="pre">argument-name</span></tt> (but not its
corresponding entry in <tt class="docutils literal">args</tt>) will be cast to that type.</li>
</ul>
<p><strong>Approximate expansion:</strong></p>
<p>Where:</p>
<ul class="simple">
<li><tt class="docutils literal">n</tt> denotes the <em>minimum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
<li><tt class="docutils literal">m</tt> denotes the <em>maximum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
</ul>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename Args&gt;
using boost_param_result_ ## __LINE__ ## <strong>name</strong> = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename Args&gt;
struct boost_param_result_ ## __LINE__ ## <strong>name</strong>
{
    typedef <strong>result</strong> type;
};

struct boost_param_params_ ## __LINE__ ## <strong>name</strong>
  : <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        <em>list of parameter specifications, based on arguments</em>
    &gt;
{
};

typedef boost_param_params_ ## __LINE__ ## <strong>name</strong>
    boost_param_parameters_ ## __LINE__ ## <strong>name</strong>;

template &lt;typename A0, …, typename A ## <strong>n</strong>&gt;
<strong>result</strong> <strong>name</strong>(
    A0&amp;&amp; a0, …, A ## <strong>n</strong>&amp;&amp; a ## <strong>n</strong>
  , typename boost_param_parameters_ ## __LINE__ ## <strong>name</strong>
    ::match&lt;A0, …, A ## <strong>n</strong>&gt;::type
    = boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()
)
{
    return this-&gt;boost_param_impl ## __LINE__ ## <strong>name</strong>(
        boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>n</strong>&gt;(a ## <strong>n</strong>)
        )
    );
}

<span class="vellipsis">⋮</span>

template &lt;typename A0, …, typename A ## <strong>m</strong>&gt;
<strong>result</strong> <strong>name</strong>(
    A0&amp;&amp; a0, …, A ## <strong>m</strong>&amp;&amp; a ## <strong>m</strong>
  , typename boost_param_parameters_ ## __LINE__ ## <strong>name</strong>
    ::match&lt;A0, …, A ## <strong>m</strong>&gt;::type
    = boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()
)
{
    return this-&gt;boost_param_impl ## __LINE__ ## <strong>name</strong>(
        boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>m</strong>&gt;(a ## <strong>m</strong>)
        )
    );
}

template &lt;typename Args&gt;
typename boost_param_result_ ## __LINE__ ## <strong>name</strong>&lt;Args&gt;::type
    boost_param_impl ## __LINE__ ## <strong>name</strong>(Args const&amp; args)
{
    return this-&gt;boost_param_dispatch_0boost_ ## __LINE__ ## <strong>name</strong>(
        static_cast&lt;
            typename boost_param_result_ ## __LINE__ ## <strong>name</strong>&lt;
                Args
            &gt;::type(*)()
        &gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , args
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of required parameter</em> ## <strong>0</strong>
            &gt;::type
        &gt;(args[ <em>keyword object of required parameter</em> ## <strong>0</strong>])
      , …
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of required parameter</em> ## <strong>n</strong>
            &gt;::type
        &gt;(args[ <em>keyword object of required parameter</em> ## <strong>n</strong>])
    );
}

template &lt;
    typename ResultType
  , typename Args
  , typename <em>argument name</em> ## <strong>0</strong> ## _type
  , …
  , typename <em>argument name</em> ## <strong>n</strong> ## _type
&gt;
ResultType
    boost_param_dispatch_0boost_ ## __LINE__ ## <strong>name</strong>(
        (ResultType(*)())
      , Args const&amp; args
      , <em>argument name</em> ## <strong>0</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>0</strong>
      , …
      , <em>argument name</em> ## <strong>n</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>n</strong>
    )
{
    return this-&gt;boost_param_dispatch_0boost_ ## __LINE__ ## <strong>name</strong>(
        static_cast&lt;ResultType(*)()&gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , (args, <em>keyword object of optional parameter</em> ## <strong>n + 1</strong> =
            <em>default value of optional parameter</em> ## <strong>n + 1</strong>
        )
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;<em>argument name</em> ## <strong>0</strong> ## _type&gt;(
            <em>argument name</em> ## <strong>0</strong>
        )
      , …
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;<em>argument name</em> ## <strong>n</strong> ## _type&gt;(
            <em>argument name</em> ## <strong>n</strong>
        )
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of optional parameter</em> ## <strong>n + 1</strong>
            &gt;::type
        &gt;(<em>default value of optional parameter</em> ## <strong>n + 1</strong>)
    );
}

<span class="vellipsis">⋮</span>

template &lt;
    typename ResultType
  , typename Args
  , typename <em>argument name</em> ## <strong>0</strong> ## _type
  , …
  , typename <em>argument name</em> ## <strong>m</strong> ## _type
&gt;
ResultType
    boost_param_dispatch_0boost_ ## __LINE__ ## <strong>name</strong>(
        (ResultType(*)())
      , Args const&amp; args
      , <em>argument name</em> ## <strong>0</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>0</strong>
      , …
      , <em>argument name</em> ## <strong>m</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>m</strong>
    )
</pre>
</div>
<div class="section" id="boost-parameter-const-member-function-result-name-tag-ns-arguments">
<span id="boost-parameter-const-member-function"></span><h2><a class="toc-backref" href="#id59">7.3&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_CONST_MEMBER_FUNCTION(result, name, tag_ns, arguments)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor.hpp">boost/parameter/preprocessor.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a member function that can take in positional arguments, composed
arguments, named arguments, and deduced arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>The return type of each of the following function templates falls under a
different value category.</p>
<pre class="literal-block">
template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; rvalue_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const rvalue_const_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp; lvalue_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; lset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return lset;
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp; lvalue_const_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const clset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return clset;
}
</pre>
<p>The <tt class="docutils literal"><span class="pre">U::evaluate_category</span></tt> static member function template has a simple job:
to return the correct value category when passed in an object returned by one
of the functions defined above.  Assume that
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is defined.</p>
<pre class="literal-block">
enum invoked
{
    passed_by_lvalue_reference_to_const
  , passed_by_lvalue_reference
  , passed_by_rvalue_reference_to_const
  , passed_by_rvalue_reference
};

struct U
{
    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;)
    {
        return passed_by_lvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;)
    {
        return passed_by_lvalue_reference;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;&amp;)
    {
        return passed_by_rvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;&amp;)
    {
        return passed_by_rvalue_reference;
    }
};
</pre>
<p>Define the named parameters that will comprise the argument specification that
this macro will use.  Ensure that all their tag types are in the same
namespace, which is <tt class="docutils literal">kw</tt> in this case.  The identifiers with leading
underscores can be passed to the bracket operator of <tt class="docutils literal">args</tt> to extract the
same argument to which the corresponding named parameter (without underscores)
is bound, as will be shown later.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lrc, kw) in(lrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lr, kw) in_out(lr))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rrc, kw) in(rrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rr, kw) consume(rr))
</pre>
<p>Use the macro as a substitute for a normal <tt class="docutils literal">const</tt> member function
header.  Enclose the return type <tt class="docutils literal">bool</tt> in parentheses.  For each parameter,
also enclose the expected value type in parentheses.  Since the value types
are mutually exclusive, you can wrap the parameters in a <tt class="docutils literal">(deduced …)</tt>
clause.  Otherwise, just as with a normal function, the order in which you
specify the parameters determines their position.  Also, just as with a normal
function, optional parameters have default values, whereas required parameters
do not.  Within the function body, either simply use the parameter name or
pass the matching identifier with the leading underscore to the bracket
operator of <tt class="docutils literal">args</tt> to extract the corresponding argument.  Note that the
second method doesn't require <tt class="docutils literal"><span class="pre">std::forward</span></tt> to preserve value categories.</p>
<pre class="literal-block">
struct B
{
    B()
    {
    }

    BOOST_PARAMETER_CONST_MEMBER_FUNCTION((bool), evaluate, kw,
        (deduced
            (required
                (lrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;1&gt;))
                (lr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;2&gt;))
            )
            (optional
                (rrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;3&gt;), rvalue_const_bitset&lt;2&gt;())
                (rr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;4&gt;), rvalue_bitset&lt;3&gt;())
            )
        )
    )
    {
        BOOST_TEST_EQ(
            passed_by_lvalue_reference_to_const
          , U::evaluate_category&lt;0&gt;(lrc)
        );
        BOOST_TEST_EQ(
            passed_by_lvalue_reference
          , U::evaluate_category&lt;1&gt;(lr)
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference_to_const
          , U::evaluate_category&lt;2&gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;rrc0_type&gt;(rrc0))
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference
          , U::evaluate_category&lt;3&gt;(args[_rr0])
        );

        return true;
    }
};
</pre>
<p>The following function calls are legal.</p>
<pre class="literal-block">
B const b = B();
b.evaluate(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
  , rvalue_bitset&lt;3&gt;()
);
b.evaluate(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
);
b.evaluate((  // composed arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
));
b.evaluate(  // named arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
);
b.evaluate(  // named arguments
    _lr0 = lvalue_bitset&lt;1&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>Because the parameters were wrapped in a <tt class="docutils literal">(deduced …)</tt> clause, the following
function calls are also legal.</p>
<pre class="literal-block">
b.evaluate(  // deduced arguments
    rvalue_bitset&lt;3&gt;()
  , lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
);
b.evaluate(  // deduced arguments
    lvalue_bitset&lt;1&gt;()
  , lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>The <a class="reference external" href="../../test/preprocessor.cpp">preprocessor.cpp</a> test program demonstrates proper usage of this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function.</li>
<li><tt class="docutils literal">name</tt> is the base name of the function; it determines the name of the
generated forwarding functions.</li>
<li><tt class="docutils literal">tag_namespace</tt> is the namespace in which the keywords used by the
function resides.</li>
<li><tt class="docutils literal">arguments</tt> is a <a class="reference external" href="../../../preprocessor/doc/index.html">Boost.Preprocessor</a> <a class="reference external" href="../../../preprocessor/doc/data/sequences.html">sequence</a> of
<em>argument-specifiers</em>, as defined below.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<pre class="literal-block">
argument-specifiers ::= <em>specifier-group0</em> {<em>specifier-group0</em>}

specifier-group0 ::= <em>specifier-group1</em> |
    (
        '<strong>(</strong>' '<strong>deduced</strong>'
            <em>specifier-group1</em> {<em>specifier-group1</em>}
        '<strong>)</strong>'
    )

specifier-group1 ::=
    (
        '<strong>(</strong>' '<strong>optional</strong>'
            <em>optional-specifier</em> {<em>optional-specifier</em>}
        '<strong>)</strong>'
    ) | (
        '<strong>(</strong>' '<strong>required</strong>'
            <em>required-specifier</em> {<em>required-specifier</em>}
        '<strong>)</strong>'
    )

optional-specifier ::=
    '<strong>(</strong>'
        <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> '<strong>,</strong>' <em>default-value</em>
    ')'

required-specifier ::=
    '<strong>(</strong>' <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> ')'

restriction ::=
    ( '<strong>*</strong>' '<strong>(</strong>' <em>mfc</em> '<strong>)</strong>' ) |
    ( '<strong>(</strong>' <em>type-name</em> '<strong>)</strong>' ) |
    '<strong>*</strong>'
</pre>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">argument-name</span></tt> is any valid C++ identifier.</li>
<li><tt class="docutils literal"><span class="pre">default-value</span></tt> is any valid C++ expression; if necessary, user code can
compute it in terms of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>, where <tt class="docutils literal"><span class="pre">previous-name</span></tt>
is the <tt class="docutils literal"><span class="pre">argument-name</span></tt> in a previous <tt class="docutils literal"><span class="pre">specifier-group0</span></tt> or
<tt class="docutils literal"><span class="pre">specifier-group1</span></tt>.  <em>This expression will be invoked exactly once.</em></li>
<li><tt class="docutils literal">mfc</tt> is an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary Metafunction Class</a> whose first argument will
be the type of the corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument
will be the entire <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is a <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean
Integral Constant</a>; however, user code <em>cannot</em> compute <tt class="docutils literal">mfc</tt> in terms
of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>.</li>
<li><tt class="docutils literal"><span class="pre">type-name</span></tt> is either the name of a <strong>target type</strong> or an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary
Metafunction Class</a> whose first argument will be the type of the
corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument will be the entire
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is the <strong>target type</strong>.  If
<tt class="docutils literal">restriction</tt> uses this form, then the type of the generated name
<tt class="docutils literal"><span class="pre">argument-name</span> ## _type</tt> will be computed in terms of the <strong>target
type</strong>, and the generated reference <tt class="docutils literal"><span class="pre">argument-name</span></tt> (but not its
corresponding entry in <tt class="docutils literal">args</tt>) will be cast to that type.</li>
</ul>
<p><strong>Approximate expansion:</strong></p>
<p>Where:</p>
<ul class="simple">
<li><tt class="docutils literal">n</tt> denotes the <em>minimum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
<li><tt class="docutils literal">m</tt> denotes the <em>maximum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
</ul>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename Args&gt;
using boost_param_result_const_ ## __LINE__ ## <strong>name</strong> = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename Args&gt;
struct boost_param_result_const_ ## __LINE__ ## <strong>name</strong>
{
    typedef <strong>result</strong> type;
};

struct boost_param_params_const_ ## __LINE__ ## <strong>name</strong>
  : <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        <em>list of parameter specifications, based on arguments</em>
    &gt;
{
};

typedef boost_param_params_const_ ## __LINE__ ## <strong>name</strong>
    boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>;

template &lt;typename A0, …, typename A ## <strong>n</strong>&gt;
<strong>result</strong> <strong>name</strong>(
    A0&amp;&amp; a0, …, A ## <strong>n</strong>&amp;&amp; a ## <strong>n</strong>
  , typename boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>
    ::match&lt;A0, …, A ## <strong>n</strong>&gt;::type
    = boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>()
) const
{
    return this-&gt;boost_param_impl_const ## __LINE__ ## <strong>name</strong>(
        boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>n</strong>&gt;(a ## <strong>n</strong>)
        )
    );
}

<span class="vellipsis">⋮</span>

template &lt;typename A0, …, typename A ## <strong>m</strong>&gt;
<strong>result</strong> <strong>name</strong>(
    A0&amp;&amp; a0, …, A ## <strong>m</strong>&amp;&amp; a ## <strong>m</strong>
  , typename boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>
    ::match&lt;A0, …, A ## <strong>m</strong>&gt;::type
    = boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>()
) const
{
    return this-&gt;boost_param_impl_const ## __LINE__ ## <strong>name</strong>(
        boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>m</strong>&gt;(a ## <strong>m</strong>)
        )
    );
}

template &lt;typename Args&gt;
typename boost_param_result_const_ ## __LINE__ ## <strong>name</strong>&lt;Args&gt;::type
    boost_param_impl_const ## __LINE__ ## <strong>name</strong>(Args const&amp; args) const
{
    return this-&gt;
    boost_param_dispatch_const_0boost_ ## __LINE__ ## <strong>name</strong>(
        static_cast&lt;
            typename boost_param_result_const_ ## __LINE__ ## <strong>name</strong>&lt;
                Args
            &gt;::type(*)()
        &gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , args
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of required parameter</em> ## <strong>0</strong>
            &gt;::type
        &gt;(args[ <em>keyword object of required parameter</em> ## <strong>0</strong>])
      , …
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of required parameter</em> ## <strong>n</strong>
            &gt;::type
        &gt;(args[ <em>keyword object of required parameter</em> ## <strong>n</strong>])
    );
}

template &lt;
    typename ResultType
  , typename Args
  , typename <em>argument name</em> ## <strong>0</strong> ## _type
  , …
  , typename <em>argument name</em> ## <strong>n</strong> ## _type
&gt;
ResultType
    boost_param_dispatch_const_0boost_ ## __LINE__ ## <strong>name</strong>(
        (ResultType(*)())
      , Args const&amp; args
      , <em>argument name</em> ## <strong>0</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>0</strong>
      , …
      , <em>argument name</em> ## <strong>n</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>n</strong>
    ) const
{
    return this-&gt;
    boost_param_dispatch_const_0boost_ ## __LINE__ ## <strong>name</strong>(
        static_cast&lt;ResultType(*)()&gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , (args, <em>keyword object of optional parameter</em> ## <strong>n + 1</strong> =
            <em>default value of optional parameter</em> ## <strong>n + 1</strong>
        )
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;<em>argument name</em> ## <strong>0</strong> ## _type&gt;(
            <em>argument name</em> ## <strong>0</strong>
        )
      , …
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;<em>argument name</em> ## <strong>n</strong> ## _type&gt;(
            <em>argument name</em> ## <strong>n</strong>
        )
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of optional parameter</em> ## <strong>n + 1</strong>
            &gt;::type
        &gt;(<em>default value of optional parameter</em> ## <strong>n + 1</strong>)
    );
}

<span class="vellipsis">⋮</span>

template &lt;
    typename ResultType
  , typename Args
  , typename <em>argument name</em> ## <strong>0</strong> ## _type
  , …
  , typename <em>argument name</em> ## <strong>m</strong> ## _type
&gt;
ResultType
    boost_param_dispatch_const_0boost_ ## __LINE__ ## <strong>name</strong>(
        (ResultType(*)())
      , Args const&amp; args
      , <em>argument name</em> ## <strong>0</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>0</strong>
      , …
      , <em>argument name</em> ## <strong>m</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>m</strong>
    ) const
</pre>
</div>
<div class="section" id="boost-parameter-function-call-operator-result-tag-namespace-arguments">
<span id="boost-parameter-function-call-operator"></span><h2><a class="toc-backref" href="#id60">7.4&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_FUNCTION_CALL_OPERATOR(result, tag_namespace, arguments)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor.hpp">boost/parameter/preprocessor.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a function call operator that can take in positional arguments,
composed arguments, named arguments, and deduced arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>Define the named parameters that will comprise the argument specification that
this macro will use.  Ensure that all their tag types are in the same
namespace, which is <tt class="docutils literal">tag</tt> by default.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(y)
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(z)
</pre>
<p>Use the macro as a substitute for a normal function call operator
header.  Enclose the return type in parentheses.  For each parameter, also
enclose the expected value type in parentheses.  Since the value types are
mutually exclusive, you can wrap the parameters in a <tt class="docutils literal">(deduced …)</tt>
clause.  This is especially useful when implementing multiple
Boost.Parameter-enabled function call operator overloads.</p>
<pre class="literal-block">
class char_reader
{
    int index;
    char const* key;

 public:
    explicit char_reader(char const* k) : index(0), key(k)
    {
    }

    BOOST_PARAMETER_FUNCTION_CALL_OPERATOR((void), tag,
        (deduced
            (required
                (y, (int))
                (z, (char const*))
            )
        )
    )
    {
        this-&gt;index = y;
        this-&gt;key = z;
    }

    <a class="reference internal" href="#boost-parameter-const-function-call-operator"><tt class="docutils literal">BOOST_PARAMETER_CONST_FUNCTION_CALL_OPERATOR</tt></a>((char), tag,
        (deduced
            (required
                (y, (bool))
                (z, (<a class="reference external" href="http://en.cppreference.com/w/cpp/container/map">std::map</a>&lt;char const*, <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>&gt;))
            )
        )
    )
    {
        return y ? (
            (z.find(this-&gt;key)-&gt;second)[this-&gt;index]
        ) : this-&gt;key[this-&gt;index];
    }
};
</pre>
<p>As with regular argument-dependent lookup, the value types of the arguments
passed in determine which function call operator overload gets invoked.</p>
<pre class="literal-block">
char const* keys[] = {&quot;foo&quot;, &quot;bar&quot;, &quot;baz&quot;};
<a class="reference external" href="http://en.cppreference.com/w/cpp/container/map">std::map</a>&lt;char const*, <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>&gt; k2s;
k2s[keys[0]] = <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>(&quot;qux&quot;);
k2s[keys[1]] = <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>(&quot;wmb&quot;);
k2s[keys[2]] = <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>(&quot;zxc&quot;);
char_reader r(keys[0]);

// positional arguments
BOOST_TEST_EQ('q', (r(true, k2s)));
BOOST_TEST_EQ('f', (r(false, k2s)));

// named arguments
r(_z = keys[1], _y = 1);
BOOST_TEST_EQ('m', (r(_z = k2s, _y = true)));
BOOST_TEST_EQ('a', (r(_z = k2s, _y = false)));

// deduced arguments
r(keys[2], 2);
BOOST_TEST_EQ('c', (r(k2s, true)));
BOOST_TEST_EQ('z', (r(k2s, false)));
</pre>
<p>The <a class="reference external" href="../../test/preprocessor.cpp">preprocessor.cpp</a> and <a class="reference external" href="../../test/preprocessor_deduced.cpp">preprocessor_deduced.cpp</a> test programs
demonstrate proper usage of this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function call operator.</li>
<li><tt class="docutils literal">tag_namespace</tt> is the namespace in which the keywords used by the
function call operator resides.</li>
<li><tt class="docutils literal">arguments</tt> is a <a class="reference external" href="../../../preprocessor/doc/index.html">Boost.Preprocessor</a> <a class="reference external" href="../../../preprocessor/doc/data/sequences.html">sequence</a> of
<em>argument-specifiers</em>, as defined below.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<pre class="literal-block">
argument-specifiers ::= <em>specifier-group0</em> {<em>specifier-group0</em>}

specifier-group0 ::= <em>specifier-group1</em> |
    (
        '<strong>(</strong>' '<strong>deduced</strong>'
            <em>specifier-group1</em> {<em>specifier-group1</em>}
        '<strong>)</strong>'
    )

specifier-group1 ::=
    (
        '<strong>(</strong>' '<strong>optional</strong>'
            <em>optional-specifier</em> {<em>optional-specifier</em>}
        '<strong>)</strong>'
    ) | (
        '<strong>(</strong>' '<strong>required</strong>'
            <em>required-specifier</em> {<em>required-specifier</em>}
        '<strong>)</strong>'
    )

optional-specifier ::=
    '<strong>(</strong>'
        <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> '<strong>,</strong>' <em>default-value</em>
    ')'

required-specifier ::=
    '<strong>(</strong>' <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> ')'

restriction ::=
    ( '<strong>*</strong>' '<strong>(</strong>' <em>mfc</em> '<strong>)</strong>' ) |
    ( '<strong>(</strong>' <em>type-name</em> '<strong>)</strong>' ) |
    '<strong>*</strong>'
</pre>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">argument-name</span></tt> is any valid C++ identifier.</li>
<li><tt class="docutils literal"><span class="pre">default-value</span></tt> is any valid C++ expression; if necessary, user code can
compute it in terms of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>, where <tt class="docutils literal"><span class="pre">previous-name</span></tt>
is the <tt class="docutils literal"><span class="pre">argument-name</span></tt> in a previous <tt class="docutils literal"><span class="pre">specifier-group0</span></tt> or
<tt class="docutils literal"><span class="pre">specifier-group1</span></tt>.  <em>This expression will be invoked exactly once.</em></li>
<li><tt class="docutils literal">mfc</tt> is an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary Metafunction Class</a> whose first argument will
be the type of the corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument
will be the entire <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is a <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean
Integral Constant</a>; however, user code <em>cannot</em> compute <tt class="docutils literal">mfc</tt> in terms
of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>.</li>
<li><tt class="docutils literal"><span class="pre">type-name</span></tt> is either the name of a <strong>target type</strong> or an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary
Metafunction Class</a> whose first argument will be the type of the
corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument will be the entire
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is the <strong>target type</strong>.  If
<tt class="docutils literal">restriction</tt> uses this form, then the type of the generated name
<tt class="docutils literal"><span class="pre">argument-name</span> ## _type</tt> will be computed in terms of the <strong>target
type</strong>, and the generated reference <tt class="docutils literal"><span class="pre">argument-name</span></tt> (but not its
corresponding entry in <tt class="docutils literal">args</tt>) will be cast to that type.</li>
</ul>
<p><strong>Approximate expansion:</strong></p>
<p>Where:</p>
<ul class="simple">
<li><tt class="docutils literal">n</tt> denotes the <em>minimum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
<li><tt class="docutils literal">m</tt> denotes the <em>maximum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
</ul>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename Args&gt;
using boost_param_result_ ## __LINE__ ## operator = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename Args&gt;
struct boost_param_result_ ## __LINE__ ## operator
{
    typedef <strong>result</strong> type;
};

struct boost_param_params_ ## __LINE__ ## operator
  : <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        <em>list of parameter specifications, based on arguments</em>
    &gt;
{
};

typedef boost_param_params_ ## __LINE__ ## operator
    boost_param_parameters_ ## __LINE__ ## operator;

template &lt;typename A0, …, typename A ## <strong>n</strong>&gt;
<strong>result</strong> operator()(
    A0&amp;&amp; a0, …, A ## <strong>n</strong>&amp;&amp; a ## <strong>n</strong>
  , typename boost_param_parameters_ ## __LINE__ ## operator::match&lt;
        A0, …, A ## <strong>n</strong>
    &gt;::type = boost_param_parameters_ ## __LINE__ ## operator()
)
{
    return this-&gt;boost_param_impl ## __LINE__ ## operator(
        boost_param_parameters_ ## __LINE__ ## operator()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>n</strong>&gt;(a ## <strong>n</strong>)
        )
    );
}

<span class="vellipsis">⋮</span>

template &lt;typename A0, …, typename A ## <strong>m</strong>&gt;
<strong>result</strong> operator()(
    A0&amp;&amp; a0, …, A ## <strong>m</strong>&amp;&amp; a ## <strong>m</strong>
  , typename boost_param_parameters_ ## __LINE__ ## operator::match&lt;
        A0, …, A ## <strong>m</strong>
    &gt;::type = boost_param_parameters_ ## __LINE__ ## operator()
)
{
    return this-&gt;boost_param_impl ## __LINE__ ## operator(
        boost_param_parameters_ ## __LINE__ ## operator()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>m</strong>&gt;(a ## <strong>m</strong>)
        )
    );
}

template &lt;typename Args&gt;
typename boost_param_result_ ## __LINE__ ## operator&lt;Args&gt;::type
    boost_param_impl ## __LINE__ ## operator(Args const&amp; args)
{
    return this-&gt;boost_param_dispatch_0boost_ ## __LINE__ ## operator(
        static_cast&lt;
            typename boost_param_result_ ## __LINE__ ## operator&lt;
                Args
            &gt;::type(*)()
        &gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , args
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of required parameter</em> ## <strong>0</strong>
            &gt;::type
        &gt;(args[ <em>keyword object of required parameter</em> ## <strong>0</strong>])
      , …
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of required parameter</em> ## <strong>n</strong>
            &gt;::type
        &gt;(args[ <em>keyword object of required parameter</em> ## <strong>n</strong>])
    );
}

template &lt;
    typename ResultType
  , typename Args
  , typename <em>argument name</em> ## <strong>0</strong> ## _type
  , …
  , typename <em>argument name</em> ## <strong>n</strong> ## _type
&gt;
ResultType
    boost_param_dispatch_0boost_ ## __LINE__ ## operator(
        (ResultType(*)())
      , Args const&amp; args
      , <em>argument name</em> ## <strong>0</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>0</strong>
      , …
      , <em>argument name</em> ## <strong>n</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>n</strong>
    )
{
    return this-&gt;boost_param_dispatch_0boost_ ## __LINE__ ## operator(
        static_cast&lt;ResultType(*)()&gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , (args, <em>keyword object of optional parameter</em> ## <strong>n + 1</strong> =
            <em>default value of optional parameter</em> ## <strong>n + 1</strong>
        )
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;<em>argument name</em> ## <strong>0</strong> ## _type&gt;(
            <em>argument name</em> ## <strong>0</strong>
        )
      , …
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;<em>argument name</em> ## <strong>n</strong> ## _type&gt;(
            <em>argument name</em> ## <strong>n</strong>
        )
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of optional parameter</em> ## <strong>n + 1</strong>
            &gt;::type
        &gt;(<em>default value of optional parameter</em> ## <strong>n + 1</strong>)
    );
}

<span class="vellipsis">⋮</span>

template &lt;
    typename ResultType
  , typename Args
  , typename <em>argument name</em> ## <strong>0</strong> ## _type
  , …
  , typename <em>argument name</em> ## <strong>m</strong> ## _type
&gt;
ResultType
    boost_param_dispatch_0boost_ ## __LINE__ ## operator(
        (ResultType(*)())
      , Args const&amp; args
      , <em>argument name</em> ## <strong>0</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>0</strong>
      , …
      , <em>argument name</em> ## <strong>m</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>m</strong>
    )
</pre>
</div>
<div class="section" id="boost-parameter-const-function-call-operator-result-tag-ns-arguments">
<span id="boost-parameter-const-function-call-operator"></span><h2><a class="toc-backref" href="#id61">7.5&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_CONST_FUNCTION_CALL_OPERATOR(result, tag_ns, arguments)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor.hpp">boost/parameter/preprocessor.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a function call operator that can take in positional arguments,
composed arguments, named arguments, and deduced arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>The return type of each of the following function templates falls under a
different value category.</p>
<pre class="literal-block">
template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; rvalue_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const rvalue_const_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp; lvalue_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; lset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return lset;
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp; lvalue_const_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const clset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return clset;
}
</pre>
<p>The <tt class="docutils literal"><span class="pre">U::evaluate_category</span></tt> static member function template has a simple job:
to return the correct value category when passed in an object returned by one
of the functions defined above.  Assume that
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is defined.</p>
<pre class="literal-block">
enum invoked
{
    passed_by_lvalue_reference_to_const
  , passed_by_lvalue_reference
  , passed_by_rvalue_reference_to_const
  , passed_by_rvalue_reference
};

struct U
{
    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;)
    {
        return passed_by_lvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;)
    {
        return passed_by_lvalue_reference;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;&amp;)
    {
        return passed_by_rvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;&amp;)
    {
        return passed_by_rvalue_reference;
    }
};
</pre>
<p>Define the named parameters that will comprise the argument specification that
this macro will use.  Ensure that all their tag types are in the same
namespace, which is <tt class="docutils literal">kw</tt> in this case.  The identifiers with leading
underscores can be passed to the bracket operator of <tt class="docutils literal">args</tt> to extract the
same argument to which the corresponding named parameter (without underscores)
is bound, as will be shown later.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lrc, kw) in(lrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lr, kw) in_out(lr))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rrc, kw) in(rrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rr, kw) consume(rr))
</pre>
<p>Use the macro as a substitute for a normal <tt class="docutils literal">const</tt> function call operator
header.  Enclose the return type <tt class="docutils literal">bool</tt> in parentheses.  For each parameter,
also enclose the expected value type in parentheses.  Since the value types
are mutually exclusive, you can wrap the parameters in a <tt class="docutils literal">(deduced …)</tt>
clause.  Otherwise, just as with a normal function, the order in which you
specify the parameters determines their position.  Also, just as with a normal
function, optional parameters have default values, whereas required parameters
do not.  Within the function body, either simply use the parameter name or
pass the matching identifier with the leading underscore to the bracket
operator of <tt class="docutils literal">args</tt> to extract the corresponding argument.  Note that the
second method doesn't require <tt class="docutils literal"><span class="pre">std::forward</span></tt> to preserve value categories.</p>
<pre class="literal-block">
struct B
{
    B()
    {
    }

    BOOST_PARAMETER_CONST_FUNCTION_CALL_OPERATOR((bool), kw,
        (deduced
            (required
                (lrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;1&gt;))
                (lr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;2&gt;))
            )
            (optional
                (rrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;3&gt;), rvalue_const_bitset&lt;2&gt;())
                (rr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;4&gt;), rvalue_bitset&lt;3&gt;())
            )
        )
    )
    {
        BOOST_TEST_EQ(
            passed_by_lvalue_reference_to_const
          , U::evaluate_category&lt;0&gt;(lrc)
        );
        BOOST_TEST_EQ(
            passed_by_lvalue_reference
          , U::evaluate_category&lt;1&gt;(lr)
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference_to_const
          , U::evaluate_category&lt;2&gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;rrc0_type&gt;(rrc0))
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference
          , U::evaluate_category&lt;3&gt;(args[_rr0])
        );

        return true;
    }
};
</pre>
<p>The following function calls are legal.</p>
<pre class="literal-block">
B const b = B();
b(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
  , rvalue_bitset&lt;3&gt;()
);
b(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
);
b((  // composed arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
));
b(  // named arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
);
b(  // named arguments
    _lr0 = lvalue_bitset&lt;1&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>Because the parameters were wrapped in a <tt class="docutils literal">(deduced …)</tt> clause, the following
function calls are also legal.</p>
<pre class="literal-block">
b(  // deduced arguments
    rvalue_bitset&lt;3&gt;()
  , lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
);
b(  // deduced arguments
    lvalue_bitset&lt;1&gt;()
  , lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>The <a class="reference external" href="../../test/preprocessor.cpp">preprocessor.cpp</a>, <a class="reference external" href="../../test/preprocessor_deduced.cpp">preprocessor_deduced.cpp</a>, and
<a class="reference external" href="../../test/preprocessor_eval_cat_8.cpp">preprocessor_eval_cat_8.cpp</a> test programs demonstrate proper usage of this
macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function call operator.</li>
<li><tt class="docutils literal">tag_namespace</tt> is the namespace in which the keywords used by the
function call operator resides.</li>
<li><tt class="docutils literal">arguments</tt> is a <a class="reference external" href="../../../preprocessor/doc/index.html">Boost.Preprocessor</a> <a class="reference external" href="../../../preprocessor/doc/data/sequences.html">sequence</a> of
<em>argument-specifiers</em>, as defined below.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<pre class="literal-block">
argument-specifiers ::= <em>specifier-group0</em> {<em>specifier-group0</em>}

specifier-group0 ::= <em>specifier-group1</em> |
    (
        '<strong>(</strong>' '<strong>deduced</strong>'
            <em>specifier-group1</em> {<em>specifier-group1</em>}
        '<strong>)</strong>'
    )

specifier-group1 ::=
    (
        '<strong>(</strong>' '<strong>optional</strong>'
            <em>optional-specifier</em> {<em>optional-specifier</em>}
        '<strong>)</strong>'
    ) | (
        '<strong>(</strong>' '<strong>required</strong>'
            <em>required-specifier</em> {<em>required-specifier</em>}
        '<strong>)</strong>'
    )

optional-specifier ::=
    '<strong>(</strong>'
        <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> '<strong>,</strong>' <em>default-value</em>
    ')'

required-specifier ::=
    '<strong>(</strong>' <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> ')'

restriction ::=
    ( '<strong>*</strong>' '<strong>(</strong>' <em>mfc</em> '<strong>)</strong>' ) |
    ( '<strong>(</strong>' <em>type-name</em> '<strong>)</strong>' ) |
    '<strong>*</strong>'
</pre>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">argument-name</span></tt> is any valid C++ identifier.</li>
<li><tt class="docutils literal"><span class="pre">default-value</span></tt> is any valid C++ expression; if necessary, user code can
compute it in terms of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>, where <tt class="docutils literal"><span class="pre">previous-name</span></tt>
is the <tt class="docutils literal"><span class="pre">argument-name</span></tt> in a previous <tt class="docutils literal"><span class="pre">specifier-group0</span></tt> or
<tt class="docutils literal"><span class="pre">specifier-group1</span></tt>.  <em>This expression will be invoked exactly once.</em></li>
<li><tt class="docutils literal">mfc</tt> is an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary Metafunction Class</a> whose first argument will
be the type of the corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument
will be the entire <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is a <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean
Integral Constant</a>; however, user code <em>cannot</em> compute <tt class="docutils literal">mfc</tt> in terms
of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>.</li>
<li><tt class="docutils literal"><span class="pre">type-name</span></tt> is either the name of a <strong>target type</strong> or an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary
Metafunction Class</a> whose first argument will be the type of the
corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument will be the entire
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is the <strong>target type</strong>.  If
<tt class="docutils literal">restriction</tt> uses this form, then the type of the generated name
<tt class="docutils literal"><span class="pre">argument-name</span> ## _type</tt> will be computed in terms of the <strong>target
type</strong>, and the generated reference <tt class="docutils literal"><span class="pre">argument-name</span></tt> (but not its
corresponding entry in <tt class="docutils literal">args</tt>) will be cast to that type.</li>
</ul>
<p><strong>Approximate expansion:</strong></p>
<p>Where:</p>
<ul class="simple">
<li><tt class="docutils literal">n</tt> denotes the <em>minimum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
<li><tt class="docutils literal">m</tt> denotes the <em>maximum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
</ul>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename Args&gt;
using boost_param_result_const_ ## __LINE__ ## operator = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename Args&gt;
struct boost_param_result_const_ ## __LINE__ ## operator
{
    typedef <strong>result</strong> type;
};

struct boost_param_params_const_ ## __LINE__ ## operator
  : <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        <em>list of parameter specifications, based on arguments</em>
    &gt;
{
};

typedef boost_param_params_const_ ## __LINE__ ## operator
    boost_param_parameters_const_ ## __LINE__ ## operator;

template &lt;typename A0, …, typename A ## <strong>n</strong>&gt;
<strong>result</strong> operator()(
    A0&amp;&amp; a0, …, A ## <strong>n</strong>&amp;&amp; a ## <strong>n</strong>
  , typename boost_param_parameters_const_ ## __LINE__ ## operator
    ::match&lt;A0, …, A ## <strong>n</strong>&gt;::type
    = boost_param_parameters_const_ ## __LINE__ ## operator()
) const
{
    return this-&gt;boost_param_impl_const ## __LINE__ ## operator(
        boost_param_parameters_const_ ## __LINE__ ## operator()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>n</strong>&gt;(a ## <strong>n</strong>)
        )
    );
}

<span class="vellipsis">⋮</span>

template &lt;typename A0, …, typename A ## <strong>m</strong>&gt;
<strong>result</strong> operator()(
    A0&amp;&amp; a0, …, A ## <strong>m</strong>&amp;&amp; a ## <strong>m</strong>
  , typename boost_param_parameters_const_ ## __LINE__ ## operator
    ::match&lt;A0, …, A ## <strong>m</strong>&gt;::type
    = boost_param_parameters_const_ ## __LINE__ ## operator()
) const
{
    return this-&gt;boost_param_impl_const ## __LINE__ ## operator(
        boost_param_parameters_const_ ## __LINE__ ## operator()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>m</strong>&gt;(a ## <strong>m</strong>)
        )
    );
}

template &lt;typename Args&gt;
typename boost_param_result_const_ ## __LINE__ ## operator&lt;Args&gt;::type
    boost_param_impl_const ## __LINE__ ## operator(Args const&amp; args) const
{
    return this-&gt;
    boost_param_dispatch_const_0boost_ ## __LINE__ ## operator(
        static_cast&lt;
            typename boost_param_result_const_ ## __LINE__ ## operator&lt;
                Args
            &gt;::type(*)()
        &gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , args
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of required parameter</em> ## <strong>0</strong>
            &gt;::type
        &gt;(args[ <em>keyword object of required parameter</em> ## <strong>0</strong>])
      , …
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of required parameter</em> ## <strong>n</strong>
            &gt;::type
        &gt;(args[ <em>keyword object of required parameter</em> ## <strong>n</strong>])
    );
}

template &lt;
    typename ResultType
  , typename Args
  , typename <em>argument name</em> ## <strong>0</strong> ## _type
  , …
  , typename <em>argument name</em> ## <strong>n</strong> ## _type
&gt;
ResultType
    boost_param_dispatch_const_0boost_ ## __LINE__ ## operator(
        (ResultType(*)())
      , Args const&amp; args
      , <em>argument name</em> ## <strong>0</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>0</strong>
      , …
      , <em>argument name</em> ## <strong>n</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>n</strong>
    ) const
{
    return this-&gt;
    boost_param_dispatch_const_0boost_ ## __LINE__ ## operator(
        static_cast&lt;ResultType(*)()&gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , (args, <em>keyword object of optional parameter</em> ## <strong>n + 1</strong> =
            <em>default value of optional parameter</em> ## <strong>n + 1</strong>
        )
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;<em>argument name</em> ## <strong>0</strong> ## _type&gt;(
            <em>argument name</em> ## <strong>0</strong>
        )
      , …
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;<em>argument name</em> ## <strong>n</strong> ## _type&gt;(
            <em>argument name</em> ## <strong>n</strong>
        )
      , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;
            typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;
                Args
              , <em>keyword tag type of optional parameter</em> ## <strong>n + 1</strong>
            &gt;::type
        &gt;(<em>default value of optional parameter</em> ## <strong>n + 1</strong>)
    );
}

<span class="vellipsis">⋮</span>

template &lt;
    typename ResultType
  , typename Args
  , typename <em>argument name</em> ## <strong>0</strong> ## _type
  , …
  , typename <em>argument name</em> ## <strong>m</strong> ## _type
&gt;
ResultType
    boost_param_dispatch_const_0boost_ ## __LINE__ ## operator(
        (ResultType(*)())
      , Args const&amp; args
      , <em>argument name</em> ## <strong>0</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>0</strong>
      , …
      , <em>argument name</em> ## <strong>m</strong> ## _type&amp;&amp; <em>argument name</em> ## <strong>m</strong>
    ) const
</pre>
</div>
<div class="section" id="boost-parameter-constructor-cls-impl-tag-namespace-arguments">
<span id="boost-parameter-constructor"></span><h2><a class="toc-backref" href="#id62">7.6&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_CONSTRUCTOR(cls, impl, tag_namespace, arguments)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor.hpp">boost/parameter/preprocessor.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a constructor that can take in positional arguments, composed
arguments, named arguments, and deduced arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>Define the named parameters that will comprise the argument specification that
this macro will use.  Ensure that all their tag types are in the same
namespace, which is <tt class="docutils literal">tag</tt> by default.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(y)
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(z)
</pre>
<p>In the base class, implement a delegate constructor template that takes in an
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>.  You must pass the identifiers with leading underscores to
<tt class="docutils literal">args</tt> in order to extract the corresponding arguments.</p>
<pre class="literal-block">
class char_read_base
{
    int index;
    char const* key;

 public:
    template &lt;typename Args&gt;
    explicit char_read_base(Args const&amp; args)
      : index(args[_y]), key(args[_z])
    {
    }

    <a class="reference internal" href="#boost-parameter-const-function-call-operator"><tt class="docutils literal">BOOST_PARAMETER_CONST_FUNCTION_CALL_OPERATOR</tt></a>((char), tag,
        (deduced
            (required
                (y, (bool))
                (z, (<a class="reference external" href="http://en.cppreference.com/w/cpp/container/map">std::map</a>&lt;char const*, <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>&gt;))
            )
        )
    )
    {
        return y ? (
            (z.find(this-&gt;key)-&gt;second)[this-&gt;index]
        ) : this-&gt;key[this-&gt;index];
    }
};
</pre>
<p>Use the macro as a substitute for a normal constructor definition.  Note the
lack of an explicit body.  Enclose the base type in parentheses.  For each
parameter, also enclose the expected value type in parentheses.  Since the
value types are mutually exclusive, you can wrap the parameters in a
<tt class="docutils literal">(deduced …)</tt> clause.</p>
<pre class="literal-block">
struct char_reader : public char_read_base
{
    BOOST_PARAMETER_CONSTRUCTOR(char_reader, (char_read_base), tag,
        (deduced
            (required
                (y, (int))
                (z, (char const*))
            )
        )
    )
};
</pre>
<p>The following <tt class="docutils literal">char_reader</tt> constructor calls are legal.</p>
<pre class="literal-block">
char const* keys[] = {&quot;foo&quot;, &quot;bar&quot;, &quot;baz&quot;};
<a class="reference external" href="http://en.cppreference.com/w/cpp/container/map">std::map</a>&lt;char const*, <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>&gt; k2s;
k2s[keys[0]] = <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>(&quot;qux&quot;);
k2s[keys[1]] = <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>(&quot;wmb&quot;);
k2s[keys[2]] = <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>(&quot;zxc&quot;);

// positional arguments
char_reader r0(0, keys[0]);
BOOST_TEST_EQ('q', (r0(true, k2s)));
BOOST_TEST_EQ('f', (r0(false, k2s)));

// named arguments
char_reader r1(_z = keys[1], _y = 1);
BOOST_TEST_EQ('m', (r1(_z = k2s, _y = true)));
BOOST_TEST_EQ('a', (r1(_z = k2s, _y = false)));

// deduced arguments
char_reader r2(keys[2], 2);
BOOST_TEST_EQ('c', (r2(k2s, true)));
BOOST_TEST_EQ('z', (r2(k2s, false)));
</pre>
<p>The <a class="reference external" href="../../test/preprocessor.cpp">preprocessor.cpp</a> and <a class="reference external" href="../../test/preprocessor_deduced.cpp">preprocessor_deduced.cpp</a> test programs
demonstrate proper usage of this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">cls</tt> is the name of the enclosing class.</li>
<li><tt class="docutils literal">impl</tt> is the parenthesized implementation base class for <tt class="docutils literal">cls</tt>.</li>
<li><tt class="docutils literal">tag_namespace</tt> is the namespace in which the keywords used by the
constructor resides.</li>
<li><tt class="docutils literal">arguments</tt> is a list of <em>argument-specifiers</em>, as defined below.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<pre class="literal-block">
argument-specifiers ::= <em>specifier-group0</em> {<em>specifier-group0</em>}

specifier-group0 ::= <em>specifier-group1</em> |
    (
        '<strong>(</strong>' '<strong>deduced</strong>'
            <em>specifier-group1</em> {<em>specifier-group1</em>}
        '<strong>)</strong>'
    )

specifier-group1 ::=
    (
        '<strong>(</strong>' '<strong>optional</strong>'
            <em>specifier</em> {<em>specifier</em>}
        '<strong>)</strong>'
    ) | (
        '<strong>(</strong>' '<strong>required</strong>'
            <em>specifier</em> {<em>specifier</em>}
        '<strong>)</strong>'
    )

specifier ::=
    '<strong>(</strong>' <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> ')'

restriction ::=
    ( '<strong>*</strong>' '<strong>(</strong>' <em>mfc</em> '<strong>)</strong>' ) |
    ( '<strong>(</strong>' <em>type-name</em> '<strong>)</strong>' ) |
    '<strong>*</strong>'
</pre>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">argument-name</span></tt> is any valid C++ identifier.</li>
<li><tt class="docutils literal">mfc</tt> is an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary Metafunction Class</a> whose first argument will
be the type of the corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument
will be the entire <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is a <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean
Integral Constant</a>; however, user code <em>cannot</em> compute <tt class="docutils literal">mfc</tt> in terms
of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>.</li>
<li><tt class="docutils literal"><span class="pre">type-name</span></tt> is either the name of a <strong>target type</strong> or an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary
Metafunction Class</a> whose first argument will be the type of the
corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument will be the entire
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is the <strong>target type</strong>.</li>
</ul>
<p>Note that <em>specifier</em> does not include <em>default-value</em>.  It is up to the
delegate constructor in <tt class="docutils literal">impl</tt> to determine the default value of all
optional arguments.</p>
<p><strong>Approximate expansion:</strong></p>
<p>Where:</p>
<ul class="simple">
<li><tt class="docutils literal">n</tt> denotes the <em>minimum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
<li><tt class="docutils literal">m</tt> denotes the <em>maximum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
</ul>
<pre class="literal-block">
struct boost_param_params_ ## __LINE__ ## ctor
  : <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        <em>list of parameter specifications, based on arguments</em>
    &gt;
{
};

typedef boost_param_params_ ## __LINE__ ## ctor
    constructor_parameters ## __LINE__;

template &lt;typename A0, …, typename A ## <strong>n</strong>&gt;
<strong>cls</strong>(A0&amp;&amp; a0, …, A ## <strong>n</strong> &amp;&amp; a ## <strong>n</strong>)
  : <strong>impl</strong>(
        constructor_parameters ## __LINE__(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>n</strong>&gt;(a ## <strong>n</strong>)
        )
    )
{
}

<span class="vellipsis">⋮</span>

template &lt;typename A0, …, typename A ## <strong>m</strong>&gt;
<strong>cls</strong>(A0&amp;&amp; a0, …, A ## <strong>m</strong> &amp;&amp; a ## <strong>m</strong>)
  : <strong>impl</strong>(
        constructor_parameters ## __LINE__(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>m</strong>&gt;(a ## <strong>m</strong>)
        )
    )
{
}
</pre>
</div>
<div class="section" id="boost-parameter-basic-function-result-name-tag-namespace-arguments">
<span id="boost-parameter-basic-function"></span><h2><a class="toc-backref" href="#id63">7.7&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_BASIC_FUNCTION(result, name, tag_namespace, arguments)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor.hpp">boost/parameter/preprocessor.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a function that can take in positional arguments, composed
arguments, named arguments, and deduced arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>The return type of each of the following function templates falls under a
different value category.</p>
<pre class="literal-block">
template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; rvalue_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const rvalue_const_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp; lvalue_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; lset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return lset;
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp; lvalue_const_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const clset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return clset;
}
</pre>
<p>The <tt class="docutils literal"><span class="pre">U::evaluate_category</span></tt> static member function template has a simple job:
to return the correct value category when passed in an object returned by one
of the functions defined above.  Assume that
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is defined.</p>
<pre class="literal-block">
enum invoked
{
    passed_by_lvalue_reference_to_const
  , passed_by_lvalue_reference
  , passed_by_rvalue_reference_to_const
  , passed_by_rvalue_reference
};

struct U
{
    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;)
    {
        return passed_by_lvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;)
    {
        return passed_by_lvalue_reference;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;&amp;)
    {
        return passed_by_rvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;&amp;)
    {
        return passed_by_rvalue_reference;
    }
};
</pre>
<p>Define the named parameters that will comprise the argument specification that
this macro will use.  Ensure that all their tag types are in the same
namespace, which is <tt class="docutils literal">kw</tt> in this case.  The identifiers with leading
underscores can be passed to the bracket operator of <tt class="docutils literal">args</tt> to extract the
same argument to which the corresponding named parameter (without underscores)
is bound, as will be shown later.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lrc, kw) in(lrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lr, kw) in_out(lr))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rrc, kw) in(rrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rr, kw) consume(rr))
</pre>
<p>Use the macro as a substitute for a normal function header.  Enclose the
return type <tt class="docutils literal">bool</tt> in parentheses.  For each parameter, also enclose the
expected value type in parentheses.  Since the value types are mutually
exclusive, you can wrap the parameters in a <tt class="docutils literal">(deduced …)</tt>
clause.  Otherwise, just as with a normal function, the order in which you
specify the parameters determines their position.  However, unlike a normal
function, default values must be specified within the function body.  Also
within the function body, you must pass the matching identifier with the
leading underscore to the bracket operator of <tt class="docutils literal">args</tt> to extract the
corresponding argument, but at least this doesn't require <tt class="docutils literal"><span class="pre">std::forward</span></tt> to
preserve value categories.</p>
<pre class="literal-block">
BOOST_PARAMETER_BASIC_FUNCTION((bool), evaluate, kw,
    (deduced
        (required
            (lrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;1&gt;))
            (lr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;2&gt;))
        )
        (optional
            (rrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;3&gt;))
            (rr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;4&gt;))
        )
    )
)
{
    BOOST_TEST_EQ(
        passed_by_lvalue_reference_to_const
      , U::evaluate_category&lt;0&gt;(args[_lrc])
    );
    BOOST_TEST_EQ(
        passed_by_lvalue_reference
      , U::evaluate_category&lt;1&gt;(args[_lr])
    );
    BOOST_TEST_EQ(
        passed_by_rvalue_reference_to_const
      , U::evaluate_category&lt;2&gt;(
            args[_rrc0 | rvalue_const_bitset&lt;2&gt;()]
        )
    );
    BOOST_TEST_EQ(
        passed_by_rvalue_reference
      , U::evaluate_category&lt;3&gt;(args[_rr0 | rvalue_bitset&lt;3&gt;()])
    );

    return true;
}
</pre>
<p>The following function calls are legal.</p>
<pre class="literal-block">
evaluate(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
  , rvalue_bitset&lt;3&gt;()
);
evaluate(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
);
evaluate((  // composed arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
));
evaluate(  // named arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
);
evaluate(  // named arguments
    _lr0 = lvalue_bitset&lt;1&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>Because the parameters were wrapped in a <tt class="docutils literal">(deduced …)</tt> clause, the following
function calls are also legal.</p>
<pre class="literal-block">
evaluate(  // deduced arguments
    rvalue_bitset&lt;3&gt;()
  , lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
);
evaluate(  // deduced arguments
    lvalue_bitset&lt;1&gt;()
  , lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>The <a class="reference external" href="../../test/preprocessor.cpp">preprocessor.cpp</a> test program demonstrates proper usage of this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function.</li>
<li><tt class="docutils literal">name</tt> is the base name of the function; it determines the name of the
generated forwarding functions.</li>
<li><tt class="docutils literal">tag_namespace</tt> is the namespace in which the keywords used by the
function resides.</li>
<li><tt class="docutils literal">arguments</tt> is a <a class="reference external" href="../../../preprocessor/doc/index.html">Boost.Preprocessor</a> <a class="reference external" href="../../../preprocessor/doc/data/sequences.html">sequence</a> of
<em>argument-specifiers</em>, as defined below.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<pre class="literal-block">
argument-specifiers ::= <em>specifier-group0</em> {<em>specifier-group0</em>}

specifier-group0 ::= <em>specifier-group1</em> |
    (
        '<strong>(</strong>' '<strong>deduced</strong>'
            <em>specifier-group1</em> {<em>specifier-group1</em>}
        '<strong>)</strong>'
    )

specifier-group1 ::=
    (
        '<strong>(</strong>' '<strong>optional</strong>'
            <em>specifier</em> {<em>specifier</em>}
        '<strong>)</strong>'
    ) | (
        '<strong>(</strong>' '<strong>required</strong>'
            <em>specifier</em> {<em>specifier</em>}
        '<strong>)</strong>'
    )

specifier ::=
    '<strong>(</strong>' <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> ')'

restriction ::=
    ( '<strong>*</strong>' '<strong>(</strong>' <em>mfc</em> '<strong>)</strong>' ) |
    ( '<strong>(</strong>' <em>type-name</em> '<strong>)</strong>' ) |
    '<strong>*</strong>'
</pre>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">argument-name</span></tt> is any valid C++ identifier.</li>
<li><tt class="docutils literal">mfc</tt> is an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary Metafunction Class</a> whose first argument will
be the type of the corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument
will be the entire <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is a <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean
Integral Constant</a>; however, user code <em>cannot</em> compute <tt class="docutils literal">mfc</tt> in terms
of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>.</li>
<li><tt class="docutils literal"><span class="pre">type-name</span></tt> is either the name of a <strong>target type</strong> or an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary
Metafunction Class</a> whose first argument will be the type of the
corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument will be the entire
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is the <strong>target type</strong>.</li>
</ul>
<p>Note that <em>specifier</em> does not include <em>default-value</em>.  It is up to the
function body to determine the default value of all optional arguments.</p>
<p><strong>Approximate expansion:</strong></p>
<p>Where:</p>
<ul class="simple">
<li><tt class="docutils literal">n</tt> denotes the <em>minimum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
<li><tt class="docutils literal">m</tt> denotes the <em>maximum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
</ul>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename Args&gt;
using boost_param_result_ ## __LINE__ ## <strong>name</strong> = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename Args&gt;
struct boost_param_result_ ## __LINE__ ## <strong>name</strong>
{
    typedef <strong>result</strong> type;
};

struct boost_param_params_ ## __LINE__ ## <strong>name</strong>
  : <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        <em>list of parameter specifications, based on arguments</em>
    &gt;
{
};

typedef boost_param_params_ ## __LINE__ ## <strong>name</strong>
    boost_param_parameters_ ## __LINE__ ## <strong>name</strong>;

template &lt;typename Args&gt;
typename boost_param_result_ ## __LINE__ ## <strong>name</strong>&lt;Args&gt;::type
    boost_param_impl ## <strong>name</strong>(Args const&amp;);

template &lt;typename A0, …, typename A ## <strong>n</strong>&gt;
<strong>result</strong> <strong>name</strong>(
    A0&amp;&amp; a0, …, A ## <strong>n</strong>&amp;&amp; a ## <strong>n</strong>
  , typename boost_param_parameters_ ## __LINE__ ## <strong>name</strong>
    ::match&lt;A0, …, A ## <strong>n</strong>&gt;::type
    = boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()
)
{
    return boost_param_impl ## __LINE__ ## <strong>name</strong>(
        boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>n</strong>&gt;(a ## <strong>n</strong>)
        )
    );
}

<span class="vellipsis">⋮</span>

template &lt;typename A0, …, typename A ## <strong>m</strong>&gt;
<strong>result</strong> <strong>name</strong>(
    A0&amp;&amp; a0, …, A ## <strong>m</strong>&amp;&amp; a ## <strong>m</strong>
  , typename boost_param_parameters_ ## __LINE__ ## <strong>name</strong>
    ::match&lt;A0, …, A ## <strong>m</strong>&gt;::type
    = boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()
)
{
    return boost_param_impl ## __LINE__ ## <strong>name</strong>(
        boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>m</strong>&gt;(a ## <strong>m</strong>)
        )
    );
}

template &lt;typename Args&gt;
typename boost_param_result_ ## __LINE__ ## <strong>name</strong>&lt;Args&gt;::type
    boost_param_impl ## __LINE__ ## <strong>name</strong>(Args const&amp; args)
</pre>
<p>Only the <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> type <tt class="docutils literal">Args</tt> and its object instance <tt class="docutils literal">args</tt> are
available for use within the function body.</p>
</div>
<div class="section" id="boost-parameter-basic-member-function-result-name-tag-ns-arguments">
<span id="boost-parameter-basic-member-function"></span><h2><a class="toc-backref" href="#id64">7.8&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_BASIC_MEMBER_FUNCTION(result, name, tag_ns, arguments)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor.hpp">boost/parameter/preprocessor.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a member function that can take in positional arguments, composed
arguments, named arguments, and deduced arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>The return type of each of the following function templates falls under a
different value category.</p>
<pre class="literal-block">
template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; rvalue_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const rvalue_const_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp; lvalue_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; lset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return lset;
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp; lvalue_const_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const clset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return clset;
}
</pre>
<p>The <tt class="docutils literal"><span class="pre">U::evaluate_category</span></tt> static member function template has a simple job:
to return the correct value category when passed in an object returned by one
of the functions defined above.  Assume that
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is defined.</p>
<pre class="literal-block">
enum invoked
{
    passed_by_lvalue_reference_to_const
  , passed_by_lvalue_reference
  , passed_by_rvalue_reference_to_const
  , passed_by_rvalue_reference
};

struct U
{
    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;)
    {
        return passed_by_lvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;)
    {
        return passed_by_lvalue_reference;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;&amp;)
    {
        return passed_by_rvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;&amp;)
    {
        return passed_by_rvalue_reference;
    }
};
</pre>
<p>Define the named parameters that will comprise the argument specification that
this macro will use.  Ensure that all their tag types are in the same
namespace, which is <tt class="docutils literal">kw</tt> in this case.  The identifiers with leading
underscores can be passed to the bracket operator of <tt class="docutils literal">args</tt> to extract the
same argument to which the corresponding named parameter (without underscores)
is bound, as will be shown later.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lrc, kw) in(lrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lr, kw) in_out(lr))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rrc, kw) in(rrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rr, kw) consume(rr))
</pre>
<p>Use the macro as a substitute for a normal <tt class="docutils literal">static</tt> member function
header.  Enclose the return type <tt class="docutils literal">bool</tt> in parentheses.  For each parameter,
also enclose the expected value type in parentheses.  Since the value types
are mutually exclusive, you can wrap the parameters in a <tt class="docutils literal">(deduced …)</tt>
clause.  Otherwise, just as with a normal function, the order in which you
specify the parameters determines their position.  However, unlike a normal
function, default values must be specified within the function body.  Also
within the function body, you must pass the matching identifier with the
leading underscore to the bracket operator of <tt class="docutils literal">args</tt> to extract the
corresponding argument, but at least this doesn't require <tt class="docutils literal"><span class="pre">std::forward</span></tt> to
preserve value categories.</p>
<pre class="literal-block">
struct B
{
    BOOST_PARAMETER_BASIC_MEMBER_FUNCTION((bool), static evaluate, kw,
        (deduced
            (required
                (lrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;1&gt;))
                (lr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;2&gt;))
            )
            (optional
                (rrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;3&gt;))
                (rr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;4&gt;))
            )
        )
    )
    {
        BOOST_TEST_EQ(
            passed_by_lvalue_reference_to_const
          , U::evaluate_category&lt;0&gt;(args[_lrc])
        );
        BOOST_TEST_EQ(
            passed_by_lvalue_reference
          , U::evaluate_category&lt;1&gt;(args[_lr])
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference_to_const
          , U::evaluate_category&lt;2&gt;(
                args[_rrc0 | rvalue_const_bitset&lt;2&gt;()]
            )
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference
          , U::evaluate_category&lt;3&gt;(
                args[_rr0 | rvalue_bitset&lt;3&gt;()]
            )
        );

        return true;
    }
};
</pre>
<p>The following function calls are legal.</p>
<pre class="literal-block">
B::evaluate(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
  , rvalue_bitset&lt;3&gt;()
);
B::evaluate(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
);
B::evaluate((  // composed arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
));
B::evaluate(  // named arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
);
B::evaluate(  // named arguments
    _lr0 = lvalue_bitset&lt;1&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>Because the parameters were wrapped in a <tt class="docutils literal">(deduced …)</tt> clause, the following
function calls are also legal.</p>
<pre class="literal-block">
B::evaluate(  // deduced arguments
    rvalue_bitset&lt;3&gt;()
  , lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
);
B::evaluate(  // deduced arguments
    lvalue_bitset&lt;1&gt;()
  , lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>The <a class="reference external" href="../../test/preprocessor.cpp">preprocessor.cpp</a> test program demonstrates proper usage of this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function.</li>
<li><tt class="docutils literal">name</tt> is the base name of the function; it determines the name of the
generated forwarding functions.  <tt class="docutils literal">name</tt> may be qualified by the
<tt class="docutils literal">static</tt> keyword to declare the member function and its helpers as not
associated with any object of the enclosing type.</li>
<li><tt class="docutils literal">tag_namespace</tt> is the namespace in which the keywords used by the
function resides.</li>
<li><tt class="docutils literal">arguments</tt> is a <a class="reference external" href="../../../preprocessor/doc/index.html">Boost.Preprocessor</a> <a class="reference external" href="../../../preprocessor/doc/data/sequences.html">sequence</a> of
<em>argument-specifiers</em>, as defined below.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<pre class="literal-block">
argument-specifiers ::= <em>specifier-group0</em> {<em>specifier-group0</em>}

specifier-group0 ::= <em>specifier-group1</em> |
    (
        '<strong>(</strong>' '<strong>deduced</strong>'
            <em>specifier-group1</em> {<em>specifier-group1</em>}
        '<strong>)</strong>'
    )

specifier-group1 ::=
    (
        '<strong>(</strong>' '<strong>optional</strong>'
            <em>specifier</em> {<em>specifier</em>}
        '<strong>)</strong>'
    ) | (
        '<strong>(</strong>' '<strong>required</strong>'
            <em>specifier</em> {<em>specifier</em>}
        '<strong>)</strong>'
    )

specifier ::=
    '<strong>(</strong>' <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> ')'

restriction ::=
    ( '<strong>*</strong>' '<strong>(</strong>' <em>mfc</em> '<strong>)</strong>' ) |
    ( '<strong>(</strong>' <em>type-name</em> '<strong>)</strong>' ) |
    '<strong>*</strong>'
</pre>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">argument-name</span></tt> is any valid C++ identifier.</li>
<li><tt class="docutils literal">mfc</tt> is an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary Metafunction Class</a> whose first argument will
be the type of the corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument
will be the entire <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is a <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean
Integral Constant</a>; however, user code <em>cannot</em> compute <tt class="docutils literal">mfc</tt> in terms
of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>.</li>
<li><tt class="docutils literal"><span class="pre">type-name</span></tt> is either the name of a <strong>target type</strong> or an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary
Metafunction Class</a> whose first argument will be the type of the
corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument will be the entire
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is the <strong>target type</strong>.</li>
</ul>
<p>Note that <em>specifier</em> does not include <em>default-value</em>.  It is up to the
function body to determine the default value of all optional arguments.</p>
<p><strong>Approximate expansion:</strong></p>
<p>Where:</p>
<ul class="simple">
<li><tt class="docutils literal">n</tt> denotes the <em>minimum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
<li><tt class="docutils literal">m</tt> denotes the <em>maximum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
</ul>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename Args&gt;
using boost_param_result_ ## __LINE__ ## <strong>name</strong> = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename Args&gt;
struct boost_param_result_ ## __LINE__ ## <strong>name</strong>
{
    typedef <strong>result</strong> type;
};

struct boost_param_params_ ## __LINE__ ## <strong>name</strong>
  : <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        <em>list of parameter specifications, based on arguments</em>
    &gt;
{
};

typedef boost_param_params_ ## __LINE__ ## <strong>name</strong>
    boost_param_parameters_ ## __LINE__ ## <strong>name</strong>;

template &lt;typename A0, …, typename A ## <strong>n</strong>&gt;
<strong>result</strong> <strong>name</strong>(
    A0&amp;&amp; a0, …, A ## <strong>n</strong>&amp;&amp; a ## <strong>n</strong>
  , typename boost_param_parameters_ ## __LINE__ ## <strong>name</strong>
    ::match&lt;A0, …, A ## <strong>n</strong>&gt;::type
    = boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()
)
{
    return this-&gt;boost_param_impl ## <strong>name</strong>(
        boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>n</strong>&gt;(a ## <strong>n</strong>)
        )
    );
}

<span class="vellipsis">⋮</span>

template &lt;typename A0, …, typename A ## <strong>m</strong>&gt;
<strong>result</strong> <strong>name</strong>(
    A0&amp;&amp; a0, …, A ## <strong>m</strong>&amp;&amp; a ## <strong>m</strong>
  , typename boost_param_parameters_ ## __LINE__ ## <strong>name</strong>
    ::match&lt;A0, …, A ## <strong>m</strong>&gt;::type
    = boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()
)
{
    return this-&gt;boost_param_impl ## <strong>name</strong>(
        boost_param_parameters_ ## __LINE__ ## <strong>name</strong>()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>m</strong>&gt;(a ## <strong>m</strong>)
        )
    );
}

template &lt;typename Args&gt;
typename boost_param_result_ ## __LINE__ ## <strong>name</strong>&lt;Args&gt;::type
    boost_param_impl ## <strong>name</strong>(Args const&amp; args)
</pre>
<p>Only the <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> type <tt class="docutils literal">Args</tt> and its object instance <tt class="docutils literal">args</tt> are
available for use within the function body.</p>
</div>
<div class="section" id="boost-parameter-basic-const-member-function-result-name-tag-ns-args">
<span id="boost-parameter-basic-const-member-function"></span><h2><a class="toc-backref" href="#id65">7.9&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_BASIC_CONST_MEMBER_FUNCTION(result, name, tag_ns, args)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor.hpp">boost/parameter/preprocessor.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a member function that can take in positional arguments, composed
arguments, named arguments, and deduced arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>The return type of each of the following function templates falls under a
different value category.</p>
<pre class="literal-block">
template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; rvalue_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const rvalue_const_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp; lvalue_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; lset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return lset;
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp; lvalue_const_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const clset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return clset;
}
</pre>
<p>The <tt class="docutils literal"><span class="pre">U::evaluate_category</span></tt> static member function template has a simple job:
to return the correct value category when passed in an object returned by one
of the functions defined above.  Assume that
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is defined.</p>
<pre class="literal-block">
enum invoked
{
    passed_by_lvalue_reference_to_const
  , passed_by_lvalue_reference
  , passed_by_rvalue_reference_to_const
  , passed_by_rvalue_reference
};

struct U
{
    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;)
    {
        return passed_by_lvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;)
    {
        return passed_by_lvalue_reference;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;&amp;)
    {
        return passed_by_rvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;&amp;)
    {
        return passed_by_rvalue_reference;
    }
};
</pre>
<p>Define the named parameters that will comprise the argument specification that
this macro will use.  Ensure that all their tag types are in the same
namespace, which is <tt class="docutils literal">kw</tt> in this case.  The identifiers with leading
underscores can be passed to the bracket operator of <tt class="docutils literal">args</tt> to extract the
same argument to which the corresponding named parameter (without underscores)
is bound, as will be shown later.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lrc, kw) in(lrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lr, kw) in_out(lr))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rrc, kw) in(rrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rr, kw) consume(rr))
</pre>
<p>Use the macro as a substitute for a normal <tt class="docutils literal">const</tt> member function
header.  Enclose the return type <tt class="docutils literal">bool</tt> in parentheses.  For each parameter,
also enclose the expected value type in parentheses.  Since the value types
are mutually exclusive, you can wrap the parameters in a <tt class="docutils literal">(deduced …)</tt>
clause.  Otherwise, just as with a normal function, the order in which you
specify the parameters determines their position.  However, unlike a normal
function, default values must be specified within the function body.  Also
within the function body, you must pass the matching identifier with the
leading underscore to the bracket operator of <tt class="docutils literal">args</tt> to extract the
corresponding argument, but at least this doesn't require <tt class="docutils literal"><span class="pre">std::forward</span></tt> to
preserve value categories.</p>
<pre class="literal-block">
struct B
{
    B()
    {
    }

    BOOST_PARAMETER_BASIC_CONST_MEMBER_FUNCTION((bool), evaluate, kw,
        (deduced
            (required
                (lrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;1&gt;))
                (lr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;2&gt;))
            )
            (optional
                (rrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;3&gt;))
                (rr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;4&gt;))
            )
        )
    )
    {
        BOOST_TEST_EQ(
            passed_by_lvalue_reference_to_const
          , U::evaluate_category&lt;0&gt;(args[_lrc])
        );
        BOOST_TEST_EQ(
            passed_by_lvalue_reference
          , U::evaluate_category&lt;1&gt;(args[_lr])
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference_to_const
          , U::evaluate_category&lt;2&gt;(
                args[_rrc0 | rvalue_const_bitset&lt;2&gt;()]
            )
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference
          , U::evaluate_category&lt;3&gt;(
                args[_rr0 | rvalue_bitset&lt;3&gt;()]
            )
        );

        return true;
    }
};
</pre>
<p>The following function calls are legal.</p>
<pre class="literal-block">
B const b = B();
b.evaluate(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
  , rvalue_bitset&lt;3&gt;()
);
b.evaluate(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
);
b.evaluate((  // composed arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
));
b.evaluate(  // named arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
);
b.evaluate(  // named arguments
    _lr0 = lvalue_bitset&lt;1&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>Because the parameters were wrapped in a <tt class="docutils literal">(deduced …)</tt> clause, the following
function calls are also legal.</p>
<pre class="literal-block">
b.evaluate(  // deduced arguments
    rvalue_bitset&lt;3&gt;()
  , lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
);
b.evaluate(  // deduced arguments
    lvalue_bitset&lt;1&gt;()
  , lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>The <a class="reference external" href="../../test/preprocessor.cpp">preprocessor.cpp</a> test program demonstrates proper usage of this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function.</li>
<li><tt class="docutils literal">name</tt> is the base name of the function; it determines the name of the
generated forwarding functions.</li>
<li><tt class="docutils literal">tag_namespace</tt> is the namespace in which the keywords used by the
function resides.</li>
<li><tt class="docutils literal">arguments</tt> is a <a class="reference external" href="../../../preprocessor/doc/index.html">Boost.Preprocessor</a> <a class="reference external" href="../../../preprocessor/doc/data/sequences.html">sequence</a> of
<em>argument-specifiers</em>, as defined below.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<pre class="literal-block">
argument-specifiers ::= <em>specifier-group0</em> {<em>specifier-group0</em>}

specifier-group0 ::= <em>specifier-group1</em> |
    (
        '<strong>(</strong>' '<strong>deduced</strong>'
            <em>specifier-group1</em> {<em>specifier-group1</em>}
        '<strong>)</strong>'
    )

specifier-group1 ::=
    (
        '<strong>(</strong>' '<strong>optional</strong>'
            <em>specifier</em> {<em>specifier</em>}
        '<strong>)</strong>'
    ) | (
        '<strong>(</strong>' '<strong>required</strong>'
            <em>specifier</em> {<em>specifier</em>}
        '<strong>)</strong>'
    )

specifier ::=
    '<strong>(</strong>' <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> ')'

restriction ::=
    ( '<strong>*</strong>' '<strong>(</strong>' <em>mfc</em> '<strong>)</strong>' ) |
    ( '<strong>(</strong>' <em>type-name</em> '<strong>)</strong>' ) |
    '<strong>*</strong>'
</pre>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">argument-name</span></tt> is any valid C++ identifier.</li>
<li><tt class="docutils literal">mfc</tt> is an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary Metafunction Class</a> whose first argument will
be the type of the corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument
will be the entire <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is a <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean
Integral Constant</a>; however, user code <em>cannot</em> compute <tt class="docutils literal">mfc</tt> in terms
of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>.</li>
<li><tt class="docutils literal"><span class="pre">type-name</span></tt> is either the name of a <strong>target type</strong> or an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary
Metafunction Class</a> whose first argument will be the type of the
corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument will be the entire
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is the <strong>target type</strong>.</li>
</ul>
<p>Note that <em>specifier</em> does not include <em>default-value</em>.  It is up to the
function body to determine the default value of all optional arguments.</p>
<p><strong>Approximate expansion:</strong></p>
<p>Where:</p>
<ul class="simple">
<li><tt class="docutils literal">n</tt> denotes the <em>minimum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
<li><tt class="docutils literal">m</tt> denotes the <em>maximum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
</ul>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename Args&gt;
using boost_param_result_const_ ## __LINE__ ## <strong>name</strong> = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename Args&gt;
struct boost_param_result_const_ ## __LINE__ ## <strong>name</strong>
{
    typedef <strong>result</strong> type;
};

struct boost_param_params_const_ ## __LINE__ ## <strong>name</strong>
  : <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        <em>list of parameter specifications, based on arguments</em>
    &gt;
{
};

typedef boost_param_params_const_ ## __LINE__ ## <strong>name</strong>
    boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>;

template &lt;typename A0, …, typename A ## <strong>n</strong>&gt;
<strong>result</strong> <strong>name</strong>(
    A0&amp;&amp; a0, …, A ## <strong>n</strong>&amp;&amp; a ## <strong>n</strong>
  , typename boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>
    ::match&lt;A0, …, A ## <strong>n</strong>&gt;::type
    = boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>()
) const
{
    return this-&gt;boost_param_impl_const ## __LINE__ ## <strong>name</strong>(
        boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>n</strong>&gt;(a ## <strong>n</strong>)
        )
    );
}

<span class="vellipsis">⋮</span>

template &lt;typename A0, …, typename A ## <strong>m</strong>&gt;
<strong>result</strong> <strong>name</strong>(
    A0&amp;&amp; a0, …, A ## <strong>m</strong>&amp;&amp; a ## <strong>m</strong>
  , typename boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>
    ::match&lt;A0, …, A ## <strong>m</strong>&gt;::type
    = boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>()
) const
{
    return this-&gt;boost_param_impl_const ## __LINE__ ## <strong>name</strong>(
        boost_param_parameters_const_ ## __LINE__ ## <strong>name</strong>()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>m</strong>&gt;(a ## <strong>m</strong>)
        )
    );
}

template &lt;typename Args&gt;
typename boost_param_result_const_ ## __LINE__ ## <strong>name</strong>&lt;Args&gt;::type
    boost_param_impl_const ## __LINE__ ## <strong>name</strong>(Args const&amp; args) const
</pre>
<p>Only the <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> type <tt class="docutils literal">Args</tt> and its object instance <tt class="docutils literal">args</tt> are
available for use within the function body.</p>
</div>
<div class="section" id="boost-parameter-basic-function-call-operator-result-tag-ns-arguments">
<span id="boost-parameter-basic-function-call-operator"></span><h2><a class="toc-backref" href="#id66">7.10&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_BASIC_FUNCTION_CALL_OPERATOR(result, tag_ns, arguments)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor.hpp">boost/parameter/preprocessor.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a function call operator that can take in positional arguments,
composed arguments, named arguments, and deduced arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>Define the named parameters that will comprise the argument specification that
this macro will use.  Ensure that all their tag types are in the same
namespace, which is <tt class="docutils literal">tag</tt> by default.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(y)
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(z)
</pre>
<p>Use the macro as a substitute for a normal function call operator
header.  Enclose the return type in parentheses.  For each parameter, also
enclose the expected value type in parentheses.  Since the value types are
mutually exclusive, you can wrap the parameters in a <tt class="docutils literal">(deduced …)</tt>
clause.  This is especially useful when implementing multiple
Boost.Parameter-enabled function call operator overloads.</p>
<pre class="literal-block">
class char_reader
{
    int index;
    char const* key;

 public:
    explicit char_reader(char const* k) : index(0), key(k)
    {
    }

    BOOST_PARAMETER_BASIC_FUNCTION_CALL_OPERATOR((void), tag,
        (deduced
            (required
                (y, (int))
                (z, (char const*))
            )
        )
    )
    {
        this-&gt;index = args[_y];
        this-&gt;key = args[_z];
    }

    <a class="reference internal" href="#boost-parameter-basic-const-function-call-operator"><tt class="docutils literal">BOOST_PARAMETER_BASIC_CONST_FUNCTION_CALL_OPERATOR</tt></a>((char), tag,
        (deduced
            (required
                (y, (bool))
                (z, (<a class="reference external" href="http://en.cppreference.com/w/cpp/container/map">std::map</a>&lt;char const*, <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>&gt;))
            )
        )
    )
    {
        return args[_y] ? (
            (args[_z].find(this-&gt;key)-&gt;second)[this-&gt;index]
        ) : this-&gt;key[this-&gt;index];
    }
};
</pre>
<p>As with regular argument-dependent lookup, the value types of the arguments
passed in determine which function call operator overload gets invoked.</p>
<pre class="literal-block">
char const* keys[] = {&quot;foo&quot;, &quot;bar&quot;, &quot;baz&quot;};
<a class="reference external" href="http://en.cppreference.com/w/cpp/container/map">std::map</a>&lt;char const*, <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>&gt; k2s;
k2s[keys[0]] = <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>(&quot;qux&quot;);
k2s[keys[1]] = <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>(&quot;wmb&quot;);
k2s[keys[2]] = <a class="reference external" href="http://en.cppreference.com/w/cpp/string/basic_string">std::string</a>(&quot;zxc&quot;);
char_reader r(keys[0]);

// positional arguments
BOOST_TEST_EQ('q', (r(true, k2s)));
BOOST_TEST_EQ('f', (r(false, k2s)));

// named arguments
r(_z = keys[1], _y = 1);
BOOST_TEST_EQ('m', (r(_z = k2s, _y = true)));
BOOST_TEST_EQ('a', (r(_z = k2s, _y = false)));

// deduced arguments
r(keys[2], 2);
BOOST_TEST_EQ('c', (r(k2s, true)));
BOOST_TEST_EQ('z', (r(k2s, false)));
</pre>
<p>The <a class="reference external" href="../../test/preprocessor.cpp">preprocessor.cpp</a> test program demonstrates proper usage of this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function call operator.</li>
<li><tt class="docutils literal">tag_namespace</tt> is the namespace in which the keywords used by the
function call operator resides.</li>
<li><tt class="docutils literal">arguments</tt> is a <a class="reference external" href="../../../preprocessor/doc/index.html">Boost.Preprocessor</a> <a class="reference external" href="../../../preprocessor/doc/data/sequences.html">sequence</a> of
<em>argument-specifiers</em>, as defined below.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<pre class="literal-block">
argument-specifiers ::= <em>specifier-group0</em> {<em>specifier-group0</em>}

specifier-group0 ::= <em>specifier-group1</em> |
    (
        '<strong>(</strong>' '<strong>deduced</strong>'
            <em>specifier-group1</em> {<em>specifier-group1</em>}
        '<strong>)</strong>'
    )

specifier-group1 ::=
    (
        '<strong>(</strong>' '<strong>optional</strong>'
            <em>specifier</em> {<em>specifier</em>}
        '<strong>)</strong>'
    ) | (
        '<strong>(</strong>' '<strong>required</strong>'
            <em>specifier</em> {<em>specifier</em>}
        '<strong>)</strong>'
    )

specifier ::=
    '<strong>(</strong>' <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> ')'

restriction ::=
    ( '<strong>*</strong>' '<strong>(</strong>' <em>mfc</em> '<strong>)</strong>' ) |
    ( '<strong>(</strong>' <em>type-name</em> '<strong>)</strong>' ) |
    '<strong>*</strong>'
</pre>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">argument-name</span></tt> is any valid C++ identifier.</li>
<li><tt class="docutils literal">mfc</tt> is an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary Metafunction Class</a> whose first argument will
be the type of the corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument
will be the entire <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is a <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean
Integral Constant</a>; however, user code <em>cannot</em> compute <tt class="docutils literal">mfc</tt> in terms
of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>.</li>
<li><tt class="docutils literal"><span class="pre">type-name</span></tt> is either the name of a <strong>target type</strong> or an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary
Metafunction Class</a> whose first argument will be the type of the
corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument will be the entire
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is the <strong>target type</strong>.</li>
</ul>
<p>Note that <em>specifier</em> does not include <em>default-value</em>.  It is up to the
function body to determine the default value of all optional arguments.</p>
<p><strong>Approximate expansion:</strong></p>
<p>Where:</p>
<ul class="simple">
<li><tt class="docutils literal">n</tt> denotes the <em>minimum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
<li><tt class="docutils literal">m</tt> denotes the <em>maximum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
</ul>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename Args&gt;
using boost_param_result_ ## __LINE__ ## operator = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename Args&gt;
struct boost_param_result_ ## __LINE__ ## operator
{
    typedef <strong>result</strong> type;
};

struct boost_param_params_ ## __LINE__ ## operator
  : <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        <em>list of parameter specifications, based on arguments</em>
    &gt;
{
};

typedef boost_param_params_ ## __LINE__ ## operator
    boost_param_parameters_ ## __LINE__ ## operator;

template &lt;typename A0, …, typename A ## <strong>n</strong>&gt;
<strong>result</strong> operator()(
    A0&amp;&amp; a0, …, A ## <strong>n</strong>&amp;&amp; a ## <strong>n</strong>
  , typename boost_param_parameters_ ## __LINE__ ## operator::match&lt;
        A0, …, A ## <strong>n</strong>
    &gt;::type = boost_param_parameters_ ## __LINE__ ## operator()
)
{
    return this-&gt;boost_param_impl ## __LINE__ ## operator(
        boost_param_parameters_ ## __LINE__ ## operator()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>n</strong>&gt;(a ## <strong>n</strong>)
        )
    );
}

<span class="vellipsis">⋮</span>

template &lt;typename A0, …, typename A ## <strong>m</strong>&gt;
<strong>result</strong> operator()(
    A0&amp;&amp; a0, …, A ## <strong>m</strong>&amp;&amp; a ## <strong>m</strong>
  , typename boost_param_parameters_ ## __LINE__ ## operator::match&lt;
        A0, …, A ## <strong>m</strong>
    &gt;::type = boost_param_parameters_ ## __LINE__ ## operator()
)
{
    return this-&gt;boost_param_impl ## __LINE__ ## operator(
        boost_param_parameters_ ## __LINE__ ## operator()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>m</strong>&gt;(a ## <strong>m</strong>)
        )
    );
}

template &lt;typename Args&gt;
typename boost_param_result_ ## __LINE__ ## operator&lt;Args&gt;::type
    boost_param_impl ## __LINE__ ## operator(Args const&amp; args)
</pre>
<p>Only the <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> type <tt class="docutils literal">Args</tt> and its object instance <tt class="docutils literal">args</tt> are
available for use within the function call operator body.</p>
</div>
<div class="section" id="boost-parameter-basic-const-function-call-operator-result-tag-ns-args">
<span id="boost-parameter-basic-const-function-call-operator"></span><h2><a class="toc-backref" href="#id67">7.11&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_BASIC_CONST_FUNCTION_CALL_OPERATOR(result, tag_ns, args)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor.hpp">boost/parameter/preprocessor.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a function call operator that can take in positional arguments,
composed arguments, named arguments, and deduced arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>The return type of each of the following function templates falls under a
different value category.</p>
<pre class="literal-block">
template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; rvalue_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const rvalue_const_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp; lvalue_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; lset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return lset;
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp; lvalue_const_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const clset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return clset;
}
</pre>
<p>The <tt class="docutils literal"><span class="pre">U::evaluate_category</span></tt> static member function template has a simple job:
to return the correct value category when passed in an object returned by one
of the functions defined above.  Assume that
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is defined.</p>
<pre class="literal-block">
enum invoked
{
    passed_by_lvalue_reference_to_const
  , passed_by_lvalue_reference
  , passed_by_rvalue_reference_to_const
  , passed_by_rvalue_reference
};

struct U
{
    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;)
    {
        return passed_by_lvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;)
    {
        return passed_by_lvalue_reference;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;&amp;)
    {
        return passed_by_rvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;&amp;)
    {
        return passed_by_rvalue_reference;
    }
};
</pre>
<p>Define the named parameters that will comprise the argument specification that
this macro will use.  Ensure that all their tag types are in the same
namespace, which is <tt class="docutils literal">kw</tt> in this case.  The identifiers with leading
underscores can be passed to the bracket operator of <tt class="docutils literal">args</tt> to extract the
same argument to which the corresponding named parameter (without underscores)
is bound, as will be shown later.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lrc, kw) in(lrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lr, kw) in_out(lr))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rrc, kw) in(rrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rr, kw) consume(rr))
</pre>
<p>Use the macro as a substitute for a normal <tt class="docutils literal">const</tt> function call operator
header.  Enclose the return type <tt class="docutils literal">bool</tt> in parentheses.  For each parameter,
also enclose the expected value type in parentheses.  Since the value types
are mutually exclusive, you can wrap the parameters in a <tt class="docutils literal">(deduced …)</tt>
clause.  Otherwise, just as with a normal function, the order in which you
specify the parameters determines their position.  However, unlike a normal
function, default values must be specified within the function body.  Also
within the function body, you must pass the matching identifier with the
leading underscore to the bracket operator of <tt class="docutils literal">args</tt> to extract the
corresponding argument, but at least this doesn't require <tt class="docutils literal"><span class="pre">std::forward</span></tt> to
preserve value categories.</p>
<pre class="literal-block">
struct B
{
    B()
    {
    }

    BOOST_PARAMETER_BASIC_CONST_FUNCTION_CALL_OPERATOR((bool), kw,
        (deduced
            (required
                (lrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;1&gt;))
                (lr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;2&gt;))
            )
            (optional
                (rrc, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;3&gt;))
                (rr, (<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;4&gt;))
            )
        )
    )
    {
        BOOST_TEST_EQ(
            passed_by_lvalue_reference_to_const
          , U::evaluate_category&lt;0&gt;(args[_lrc])
        );
        BOOST_TEST_EQ(
            passed_by_lvalue_reference
          , U::evaluate_category&lt;1&gt;(args[_lr])
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference_to_const
          , U::evaluate_category&lt;2&gt;(
                args[_rrc0 | rvalue_const_bitset&lt;2&gt;()]
            )
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference
          , U::evaluate_category&lt;3&gt;(
                args[_rr0 | rvalue_bitset&lt;3&gt;()]
            )
        );

        return true;
    }
};
</pre>
<p>The following function calls are legal.</p>
<pre class="literal-block">
B const b = B();
b(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
  , rvalue_bitset&lt;3&gt;()
);
b(  // positional arguments
    lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
);
b((  // composed arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
));
b(  // named arguments
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
);
b(  // named arguments
    _lr0 = lvalue_bitset&lt;1&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>Because the parameters were wrapped in a <tt class="docutils literal">(deduced …)</tt> clause, the following
function calls are also legal.</p>
<pre class="literal-block">
b(  // deduced arguments
    rvalue_bitset&lt;3&gt;()
  , lvalue_const_bitset&lt;0&gt;()
  , lvalue_bitset&lt;1&gt;()
  , rvalue_const_bitset&lt;2&gt;()
);
b(  // deduced arguments
    lvalue_bitset&lt;1&gt;()
  , lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>The <a class="reference external" href="../../test/preprocessor.cpp">preprocessor.cpp</a> test program demonstrates proper usage of this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function call operator.</li>
<li><tt class="docutils literal">tag_namespace</tt> is the namespace in which the keywords used by the
function call operator resides.</li>
<li><tt class="docutils literal">arguments</tt> is a <a class="reference external" href="../../../preprocessor/doc/index.html">Boost.Preprocessor</a> <a class="reference external" href="../../../preprocessor/doc/data/sequences.html">sequence</a> of
<em>argument-specifiers</em>, as defined below.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<pre class="literal-block">
argument-specifiers ::= <em>specifier-group0</em> {<em>specifier-group0</em>}

specifier-group0 ::= <em>specifier-group1</em> |
    (
        '<strong>(</strong>' '<strong>deduced</strong>'
            <em>specifier-group1</em> {<em>specifier-group1</em>}
        '<strong>)</strong>'
    )

specifier-group1 ::=
    (
        '<strong>(</strong>' '<strong>optional</strong>'
            <em>specifier</em> {<em>specifier</em>}
        '<strong>)</strong>'
    ) | (
        '<strong>(</strong>' '<strong>required</strong>'
            <em>specifier</em> {<em>specifier</em>}
        '<strong>)</strong>'
    )

specifier ::=
    '<strong>(</strong>' <em>argument-name</em> '<strong>,</strong>' <em>restriction</em> ')'

restriction ::=
    ( '<strong>*</strong>' '<strong>(</strong>' <em>mfc</em> '<strong>)</strong>' ) |
    ( '<strong>(</strong>' <em>type-name</em> '<strong>)</strong>' ) |
    '<strong>*</strong>'
</pre>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">argument-name</span></tt> is any valid C++ identifier.</li>
<li><tt class="docutils literal">mfc</tt> is an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary Metafunction Class</a> whose first argument will
be the type of the corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument
will be the entire <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is a <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean
Integral Constant</a>; however, user code <em>cannot</em> compute <tt class="docutils literal">mfc</tt> in terms
of <tt class="docutils literal"><span class="pre">previous-name</span> ## _type</tt>.</li>
<li><tt class="docutils literal"><span class="pre">type-name</span></tt> is either the name of a <strong>target type</strong> or an <a class="reference external" href="../../../mpl/doc/refmanual/metafunction-class.html">MPL Binary
Metafunction Class</a> whose first argument will be the type of the
corresponding <tt class="docutils literal"><span class="pre">argument-name</span></tt>, whose second argument will be the entire
<a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>, and whose return type is the <strong>target type</strong>.</li>
</ul>
<p>Note that <em>specifier</em> does not include <em>default-value</em>.  It is up to the
function body to determine the default value of all optional arguments.</p>
<p><strong>Approximate expansion:</strong></p>
<p>Where:</p>
<ul class="simple">
<li><tt class="docutils literal">n</tt> denotes the <em>minimum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
<li><tt class="docutils literal">m</tt> denotes the <em>maximum</em> arity, as determined from <tt class="docutils literal">arguments</tt>.</li>
</ul>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename Args&gt;
using boost_param_result_const_ ## __LINE__ ## operator = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename Args&gt;
struct boost_param_result_const_ ## __LINE__ ## operator
{
    typedef <strong>result</strong> type;
};

struct boost_param_params_const_ ## __LINE__ ## operator
  : <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        <em>list of parameter specifications, based on arguments</em>
    &gt;
{
};

typedef boost_param_params_const_ ## __LINE__ ## operator
    boost_param_parameters_const_ ## __LINE__ ## operator;

template &lt;typename A0, …, typename A ## <strong>n</strong>&gt;
<strong>result</strong> operator()(
    A0&amp;&amp; a0, …, A ## <strong>n</strong>&amp;&amp; a ## <strong>n</strong>
  , typename boost_param_parameters_const_ ## __LINE__ ## operator
    ::match&lt;A0, …, A ## <strong>n</strong>&gt;::type
    = boost_param_parameters_const_ ## __LINE__ ## operator()
) const
{
    return this-&gt;boost_param_impl_const ## __LINE__ ## operator(
        boost_param_parameters_const_ ## __LINE__ ## operator()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>n</strong>&gt;(a ## <strong>n</strong>)
        )
    );
}

<span class="vellipsis">⋮</span>

template &lt;typename A0, …, typename A ## <strong>m</strong>&gt;
<strong>result</strong> operator()(
    A0&amp;&amp; a0, …, A ## <strong>m</strong>&amp;&amp; a ## <strong>m</strong>
  , typename boost_param_parameters_const_ ## __LINE__ ## operator
    ::match&lt;A0, …, A ## <strong>m</strong>&gt;::type
    = boost_param_parameters_const_ ## __LINE__ ## operator()
) const
{
    return this-&gt;boost_param_impl_const ## __LINE__ ## operator(
        boost_param_parameters_const_ ## __LINE__ ## operator()(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A0&gt;(a0)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>m</strong>&gt;(a ## <strong>m</strong>)
        )
    );
}

template &lt;typename Args&gt;
typename boost_param_result_const_ ## __LINE__ ## operator&lt;Args&gt;::type
    boost_param_impl_const ## __LINE__ ## operator(Args const&amp; args) const
</pre>
<p>Only the <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> type <tt class="docutils literal">Args</tt> and its object instance <tt class="docutils literal">args</tt> are
available for use within the function call operator body.</p>
</div>
<div class="section" id="boost-parameter-no-spec-function-result-name">
<span id="boost-parameter-no-spec-function"></span><h2><a class="toc-backref" href="#id68">7.12&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_FUNCTION(result, name)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor_no_spec.hpp">boost/parameter/preprocessor_no_spec.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a function that can take in named arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>The return type of each of the following function templates falls under a
different value category.</p>
<pre class="literal-block">
template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; rvalue_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const rvalue_const_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp; lvalue_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; lset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return lset;
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp; lvalue_const_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const clset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return clset;
}
</pre>
<p>The <tt class="docutils literal"><span class="pre">U::evaluate_category</span></tt> static member function template has a simple job:
to return the correct value category when passed in an object returned by one
of the functions defined above.  Assume that
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is defined.</p>
<pre class="literal-block">
enum invoked
{
    passed_by_lvalue_reference_to_const
  , passed_by_lvalue_reference
  , passed_by_rvalue_reference_to_const
  , passed_by_rvalue_reference
};

struct U
{
    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;)
    {
        return passed_by_lvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;)
    {
        return passed_by_lvalue_reference;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;&amp;)
    {
        return passed_by_rvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;&amp;)
    {
        return passed_by_rvalue_reference;
    }
};
</pre>
<p>Named parameters are required when invoking the function; however, none of
their tags need to be in the same namespace.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lrc, kw0) in(lrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lr, kw1) in_out(lr))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rrc, kw2) in(rrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rr, kw3) consume(rr))
</pre>
<p>Use the macro as a substitute for a variadic function header.  Enclose the
return type <tt class="docutils literal">bool</tt> in parentheses.</p>
<pre class="literal-block">
BOOST_PARAMETER_NO_SPEC_FUNCTION((bool), evaluate)
{
    BOOST_TEST_EQ(
        passed_by_lvalue_reference_to_const
      , U::evaluate_category&lt;0&gt;(args[_lrc])
    );
    BOOST_TEST_EQ(
        passed_by_lvalue_reference
      , U::evaluate_category&lt;1&gt;(args[_lr])
    );
    BOOST_TEST_EQ(
        passed_by_rvalue_reference_to_const
      , U::evaluate_category&lt;2&gt;(
            args[_rrc | rvalue_const_bitset&lt;2&gt;()]
        )
    );
    BOOST_TEST_EQ(
        passed_by_rvalue_reference
      , U::evaluate_category&lt;3&gt;(args[_rr | rvalue_bitset&lt;3&gt;()])
    );

    return true;
}
</pre>
<p>To invoke the function, bind all its arguments to named parameters.</p>
<pre class="literal-block">
evaluate(
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
);
evaluate(
    _lr0 = lvalue_bitset&lt;1&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>The <a class="reference external" href="../../test/preprocessor_eval_cat_no_spec.cpp">preprocessor_eval_cat_no_spec.cpp</a> test program demonstrates proper usage of
this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function.</li>
<li><tt class="docutils literal">name</tt> is the base name of the function; it determines the name of the
generated implementation function.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<p>None.</p>
<p><strong>Approximate expansion:</strong></p>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
using boost_param_no_spec_result_ ## __LINE__ ## <strong>name</strong> = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
struct boost_param_no_spec_result_ ## __LINE__ ## <strong>name</strong>
{
    typedef <strong>result</strong> type;
};

template &lt;typename ResultType, typename Args&gt;
ResultType
    boost_param_no_spec_impl ## __LINE__ ## <strong>name</strong>(
        (ResultType(*)())
      , Args const&amp; args
    );

template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
inline typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>&lt;
    <a class="reference internal" href="#are-tagged-arguments"><tt class="docutils literal">are_tagged_arguments</tt></a>&lt;TaggedArg0,TaggedArgs...&gt;
  , boost_param_no_spec_result_ ## __LINE__ ## <strong>name</strong>&lt;
        TaggedArg0
      , TaggedArgs...
    &gt;
&gt;::type
    <strong>name</strong>(TaggedArg0 const&amp; arg0, TaggedArgs const&amp;... args)
{
    return boost_param_no_spec_impl ## __LINE__ ## <strong>name</strong>(
        static_cast&lt;
            typename
            boost_param_no_spec_result_ ## __LINE__ ## <strong>name</strong>&lt;
                TaggedArg0
              , TaggedArgs...
            &gt;::type(*)()
        &gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , <a class="reference internal" href="#compose"><tt class="docutils literal">compose</tt></a>(arg0, args...)
    );
}

template &lt;typename ResultType, typename Args&gt;
ResultType
    boost_param_no_spec_impl ## __LINE__ ## <strong>name</strong>(
        (ResultType(*)())
      , Args const&amp; args
    )
</pre>
<p>Only the <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> type <tt class="docutils literal">Args</tt> and its object instance <tt class="docutils literal">args</tt> are
available for use within the function body.</p>
</div>
<div class="section" id="boost-parameter-no-spec-member-function-result-name">
<span id="boost-parameter-no-spec-member-function"></span><h2><a class="toc-backref" href="#id69">7.13&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_MEMBER_FUNCTION(result, name)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor_no_spec.hpp">boost/parameter/preprocessor_no_spec.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a member function that can take in named arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>When designing a front-end class template whose back-end is configurable via
parameterized inheritance, it can be useful to omit argument specifiers from
a named-parameter member function so that the delegate member functions of the
back-end classes can enforce their own specifications.</p>
<pre class="literal-block">
template &lt;typename B&gt;
struct frontend : B
{
    frontend() : B()
    {
    }

    BOOST_PARAMETER_NO_SPEC_MEMBER_FUNCTION((void), initialize)
    {
        this-&gt;initialize_impl(args);
    }
};
</pre>
<p>Named parameters are required when invoking the member function; however, none
of their tags need to be in the same namespace.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(a0)
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(a1)
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(a2)
</pre>
<p>For this example, each of the back-end class templates requires its own
parameter to be present in the argument pack.  In practice, such parameters
should be optional, with default values.</p>
<pre class="literal-block">
template &lt;typename T&gt;
class backend0
{
    T a0;

 public:
    backend0() : a0()
    {
    }

    T const&amp; get_a0() const
    {
        return this-&gt;a0;
    }

 protected:
    template &lt;typename ArgPack&gt;
    void initialize_impl(ArgPack const&amp; args)
    {
        this-&gt;a0 = args[_a0];
    }
};

template &lt;typename B, typename T&gt;
class backend1 : public B
{
    T a1;

 public:
    backend1() : B(), a1()
    {
    }

    T const&amp; get_a1() const
    {
        return this-&gt;a1;
    }

 protected:
    template &lt;typename ArgPack&gt;
    void initialize_impl(ArgPack const&amp; args)
    {
        B::initialize_impl(args);
        this-&gt;a1 = args[_a1];
    }
};

template &lt;typename B, typename T&gt;
class backend2 : public B
{
    T a2;

 public:
    backend2() : B(), a2()
    {
    }

    T const&amp; get_a2() const
    {
        return this-&gt;a2;
    }

 protected:
    template &lt;typename ArgPack&gt;
    void initialize_impl(ArgPack const&amp; args)
    {
        B::initialize_impl(args);
        this-&gt;a2 = args[_a2];
    }
};
</pre>
<p>This example shows that while <tt class="docutils literal">backend0</tt> must always be the root base class
template and that <tt class="docutils literal">frontend</tt> must always be the most derived class template,
the other back-ends can be chained together in different orders.</p>
<pre class="literal-block">
char const* p = &quot;foo&quot;;
frontend&lt;
    backend2&lt;backend1&lt;backend0&lt;char const*&gt;, char&gt;, int&gt;
&gt; composed_obj0;
frontend&lt;
    backend1&lt;backend2&lt;backend0&lt;char const*&gt;, int&gt;, char&gt;
&gt; composed_obj1;
composed_obj0.initialize(_a2 = 4, _a1 = ' ', _a0 = p);
composed_obj1.initialize(_a0 = p, _a1 = ' ', _a2 = 4);
BOOST_TEST_EQ(composed_obj0.get_a0(), composed_obj1.get_a0());
BOOST_TEST_EQ(composed_obj0.get_a1(), composed_obj1.get_a1());
BOOST_TEST_EQ(composed_obj0.get_a2(), composed_obj1.get_a2());
</pre>
<p>The <a class="reference external" href="../../test/parameterized_inheritance.cpp">parameterized_inheritance.cpp</a> and <a class="reference external" href="../../test/preprocessor_eval_cat_no_spec.cpp">preprocessor_eval_cat_no_spec.cpp</a> test
programs demonstrate proper usage of this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function.</li>
<li><tt class="docutils literal">name</tt> is the base name of the function; it determines the name of the
generated implementation function.  <tt class="docutils literal">name</tt> may be qualified by the
<tt class="docutils literal">static</tt> keyword to declare the member function and its helpers as not
associated with any object of the enclosing type.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<p>None.</p>
<p><strong>Approximate expansion:</strong></p>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
using boost_param_no_spec_result_ ## __LINE__ ## <strong>name</strong> = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
struct boost_param_no_spec_result_ ## __LINE__ ## <strong>name</strong>
{
    typedef <strong>result</strong> type;
};

template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
inline typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>&lt;
    <a class="reference internal" href="#are-tagged-arguments"><tt class="docutils literal">are_tagged_arguments</tt></a>&lt;TaggedArg0,TaggedArgs...&gt;
  , boost_param_no_spec_result_ ## __LINE__ ## <strong>name</strong>&lt;
        TaggedArg0
      , TaggedArgs...
    &gt;
&gt;::type
    <strong>name</strong>(TaggedArg0 const&amp; arg0, TaggedArgs const&amp;... args)
{
    return this-&gt;boost_param_no_spec_impl ## __LINE__ ## <strong>name</strong>(
        static_cast&lt;
            typename
            boost_param_no_spec_result_ ## __LINE__ ## <strong>name</strong>&lt;
                TaggedArg0
              , TaggedArgs...
            &gt;::type(*)()
        &gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , <a class="reference internal" href="#compose"><tt class="docutils literal">compose</tt></a>(arg0, args...)
    );
}

template &lt;typename ResultType, typename Args&gt;
ResultType
    boost_param_no_spec_impl ## __LINE__ ## <strong>name</strong>(
        (ResultType(*)())
      , Args const&amp; args
    )
</pre>
<p>Only the <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> type <tt class="docutils literal">Args</tt> and its object instance <tt class="docutils literal">args</tt> are
available for use within the function body.</p>
</div>
<div class="section" id="boost-parameter-no-spec-const-member-function-result-name">
<span id="boost-parameter-no-spec-const-member-function"></span><h2><a class="toc-backref" href="#id70">7.14&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONST_MEMBER_FUNCTION(result, name)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor_no_spec.hpp">boost/parameter/preprocessor_no_spec.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a member function that can take in named arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>The return type of each of the following function templates falls under a
different value category.</p>
<pre class="literal-block">
template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; rvalue_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const rvalue_const_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp; lvalue_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; lset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return lset;
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp; lvalue_const_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const clset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return clset;
}
</pre>
<p>The <tt class="docutils literal"><span class="pre">U::evaluate_category</span></tt> static member function template has a simple job:
to return the correct value category when passed in an object returned by one
of the functions defined above.  Assume that
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is defined.</p>
<pre class="literal-block">
enum invoked
{
    passed_by_lvalue_reference_to_const
  , passed_by_lvalue_reference
  , passed_by_rvalue_reference_to_const
  , passed_by_rvalue_reference
};

struct U
{
    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;)
    {
        return passed_by_lvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;)
    {
        return passed_by_lvalue_reference;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;&amp;)
    {
        return passed_by_rvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;&amp;)
    {
        return passed_by_rvalue_reference;
    }
};
</pre>
<p>Named parameters are required when invoking the member function; however, none
of their tags need to be in the same namespace.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lrc, kw0) in(lrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lr, kw1) in_out(lr))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rrc, kw2) in(rrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rr, kw3) consume(rr))
</pre>
<p>Use the macro as a substitute for a variadic function header.  Enclose the
return type <tt class="docutils literal">bool</tt> in parentheses.  The macro will qualify the function with
the <tt class="docutils literal">const</tt> keyword.</p>
<pre class="literal-block">
struct D
{
    D()
    {
    }

    BOOST_PARAMETER_NO_SPEC_CONST_MEMBER_FUNCTION((bool), evaluate_m)
    {
        BOOST_TEST_EQ(
            passed_by_lvalue_reference_to_const
          , U::evaluate_category&lt;0&gt;(args[_lrc])
        );
        BOOST_TEST_EQ(
            passed_by_lvalue_reference
          , U::evaluate_category&lt;1&gt;(args[_lr])
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference_to_const
          , U::evaluate_category&lt;2&gt;(
                args[_rrc | rvalue_const_bitset&lt;2&gt;()]
            )
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference
          , U::evaluate_category&lt;3&gt;(
                args[_rr | rvalue_bitset&lt;3&gt;()]
            )
        );

        return true;
    }
};
</pre>
<p>To invoke the member function, bind all its arguments to named parameters.</p>
<pre class="literal-block">
D const d = D();
d.evaluate_m(
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
);
d.evaluate_m(
    _lr0 = lvalue_bitset&lt;1&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>The <a class="reference external" href="../../test/preprocessor_eval_cat_no_spec.cpp">preprocessor_eval_cat_no_spec.cpp</a> test program demonstrates proper usage of
this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function.</li>
<li><tt class="docutils literal">name</tt> is the base name of the function; it determines the name of the
generated implementation function.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<p>None.</p>
<p><strong>Approximate expansion:</strong></p>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
using boost_param_no_spec_result_const_ ## __LINE__ ## <strong>name</strong> = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
struct boost_param_no_spec_result_const_ ## __LINE__ ## <strong>name</strong>
{
    typedef <strong>result</strong> type;
};

template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
inline typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>&lt;
    <a class="reference internal" href="#are-tagged-arguments"><tt class="docutils literal">are_tagged_arguments</tt></a>&lt;TaggedArg0,TaggedArgs...&gt;
  , boost_param_no_spec_result_const_ ## __LINE__ ## <strong>name</strong>&lt;
        TaggedArg0
      , TaggedArgs...
    &gt;
&gt;::type
    <strong>name</strong>(TaggedArg0 const&amp; arg0, TaggedArgs const&amp;... args) const
{
    return this-&gt;boost_param_no_spec_impl_const ## __LINE__ ## <strong>name</strong>(
        static_cast&lt;
            typename
            boost_param_no_spec_result_const_ ## __LINE__ ## <strong>name</strong>&lt;
                TaggedArg0
              , TaggedArgs...
            &gt;::type(*)()
        &gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , <a class="reference internal" href="#compose"><tt class="docutils literal">compose</tt></a>(arg0, args...)
    );
}

template &lt;typename ResultType, typename Args&gt;
ResultType
    boost_param_no_spec_impl_const ## __LINE__ ## <strong>name</strong>(
        (ResultType(*)())
      , Args const&amp; args
    ) const
</pre>
<p>Only the <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> type <tt class="docutils literal">Args</tt> and its object instance <tt class="docutils literal">args</tt> are
available for use within the function body.</p>
</div>
<div class="section" id="boost-parameter-no-spec-function-call-operator-result">
<span id="boost-parameter-no-spec-function-call-operator"></span><h2><a class="toc-backref" href="#id71">7.15&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_FUNCTION_CALL_OPERATOR(result)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor_no_spec.hpp">boost/parameter/preprocessor_no_spec.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a function call operator that can take in named arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>When designing a front-end class template whose back-end is configurable via
parameterized inheritance, it can be useful to omit argument specifiers from
a named-parameter function call operator so that the delegate member functions
of the back-end classes can enforce their own specifications.</p>
<pre class="literal-block">
template &lt;typename B&gt;
struct frontend : B
{
    frontend() : B()
    {
    }

    BOOST_PARAMETER_NO_SPEC_FUNCTION_CALL_OPERATOR((void))
    {
        this-&gt;initialize_impl(args);
    }
};
</pre>
<p>Named parameters are required when invoking the function call operator;
however, none of their tags need to be in the same namespace.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(a0)
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(a1)
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(a2)
</pre>
<p>For this example, each of the back-end class templates requires its own
parameter to be present in the argument pack.  In practice, such parameters
should be optional, with default values.</p>
<pre class="literal-block">
template &lt;typename T&gt;
class backend0
{
    T a0;

 public:
    backend0() : a0()
    {
    }

    T const&amp; get_a0() const
    {
        return this-&gt;a0;
    }

 protected:
    template &lt;typename ArgPack&gt;
    void initialize_impl(ArgPack const&amp; args)
    {
        this-&gt;a0 = args[_a0];
    }
};

template &lt;typename B, typename T&gt;
class backend1 : public B
{
    T a1;

 public:
    backend1() : B(), a1()
    {
    }

    T const&amp; get_a1() const
    {
        return this-&gt;a1;
    }

 protected:
    template &lt;typename ArgPack&gt;
    void initialize_impl(ArgPack const&amp; args)
    {
        B::initialize_impl(args);
        this-&gt;a1 = args[_a1];
    }
};

template &lt;typename B, typename T&gt;
class backend2 : public B
{
    T a2;

 public:
    backend2() : B(), a2()
    {
    }

    T const&amp; get_a2() const
    {
        return this-&gt;a2;
    }

 protected:
    template &lt;typename ArgPack&gt;
    void initialize_impl(ArgPack const&amp; args)
    {
        B::initialize_impl(args);
        this-&gt;a2 = args[_a2];
    }
};
</pre>
<p>This example shows that while <tt class="docutils literal">backend0</tt> must always be the root base class
template and that <tt class="docutils literal">frontend</tt> must always be the most derived class template,
the other back-ends can be chained together in different orders.</p>
<pre class="literal-block">
char const* p = &quot;foo&quot;;
frontend&lt;
    backend2&lt;backend1&lt;backend0&lt;char const*&gt;, char&gt;, int&gt;
&gt; composed_obj0;
frontend&lt;
    backend1&lt;backend2&lt;backend0&lt;char const*&gt;, int&gt;, char&gt;
&gt; composed_obj1;
composed_obj0(_a2 = 4, _a1 = ' ', _a0 = p);
composed_obj1(_a0 = p, _a1 = ' ', _a2 = 4);
BOOST_TEST_EQ(composed_obj0.get_a0(), composed_obj1.get_a0());
BOOST_TEST_EQ(composed_obj0.get_a1(), composed_obj1.get_a1());
BOOST_TEST_EQ(composed_obj0.get_a2(), composed_obj1.get_a2());
</pre>
<p>The <a class="reference external" href="../../test/parameterized_inheritance.cpp">parameterized_inheritance.cpp</a> and <a class="reference external" href="../../test/preprocessor_eval_cat_no_spec.cpp">preprocessor_eval_cat_no_spec.cpp</a> test
programs demonstrate proper usage of this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function call operator.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<p>None.</p>
<p><strong>Approximate expansion:</strong></p>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
using boost_param_no_spec_result_ ## __LINE__ ## operator = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
struct boost_param_no_spec_result_ ## __LINE__ ## operator
{
    typedef <strong>result</strong> type;
};

template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
inline typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>&lt;
    <a class="reference internal" href="#are-tagged-arguments"><tt class="docutils literal">are_tagged_arguments</tt></a>&lt;TaggedArg0,TaggedArgs...&gt;
  , boost_param_no_spec_result_ ## __LINE__ ## operator&lt;
        TaggedArg0
      , TaggedArgs...
    &gt;
&gt;::type
    operator()(TaggedArg0 const&amp; arg0, TaggedArgs const&amp;... args)
{
    return this-&gt;boost_param_no_spec_impl ## __LINE__ ## operator(
        static_cast&lt;
            typename
            boost_param_no_spec_result_ ## __LINE__ ## operator&lt;
                TaggedArg0
              , TaggedArgs...
            &gt;::type(*)()
        &gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , <a class="reference internal" href="#compose"><tt class="docutils literal">compose</tt></a>(arg0, args...)
    );
}

template &lt;typename ResultType, typename Args&gt;
ResultType
    boost_param_no_spec_impl ## __LINE__ ## operator(
        (ResultType(*)())
      , Args const&amp; args
    )
</pre>
<p>Only the <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> type <tt class="docutils literal">Args</tt> and its object instance <tt class="docutils literal">args</tt> are
available for use within the function body.</p>
</div>
<div class="section" id="boost-parameter-no-spec-const-function-call-operator-result">
<span id="boost-parameter-no-spec-const-function-call-operator"></span><h2><a class="toc-backref" href="#id72">7.16&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONST_FUNCTION_CALL_OPERATOR(result)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor_no_spec.hpp">boost/parameter/preprocessor_no_spec.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a function call operator that can take in named arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>The return type of each of the following function templates falls under a
different value category.</p>
<pre class="literal-block">
template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; rvalue_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const rvalue_const_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp; lvalue_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; lset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return lset;
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp; lvalue_const_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const clset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return clset;
}
</pre>
<p>The <tt class="docutils literal"><span class="pre">U::evaluate_category</span></tt> static member function template has a simple job:
to return the correct value category when passed in an object returned by one
of the functions defined above.  Assume that
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is defined.</p>
<pre class="literal-block">
enum invoked
{
    passed_by_lvalue_reference_to_const
  , passed_by_lvalue_reference
  , passed_by_rvalue_reference_to_const
  , passed_by_rvalue_reference
};

struct U
{
    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;)
    {
        return passed_by_lvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;)
    {
        return passed_by_lvalue_reference;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;&amp;)
    {
        return passed_by_rvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;&amp;)
    {
        return passed_by_rvalue_reference;
    }
};
</pre>
<p>Named parameters are required when invoking the function call operator;
however, none of their tags need to be in the same namespace.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lrc, kw0) in(lrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lr, kw1) in_out(lr))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rrc, kw2) in(rrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rr, kw3) consume(rr))
</pre>
<p>Use the macro as a substitute for a variadic function call operator
header.  Enclose the return type <tt class="docutils literal">bool</tt> in parentheses.  The macro will
qualify the function with the <tt class="docutils literal">const</tt> keyword.</p>
<pre class="literal-block">
struct D
{
    D()
    {
    }

    BOOST_PARAMETER_NO_SPEC_CONST_FUNCTION_CALL_OPERATOR((bool))
    {
        BOOST_TEST_EQ(
            passed_by_lvalue_reference_to_const
          , U::evaluate_category&lt;0&gt;(args[_lrc])
        );
        BOOST_TEST_EQ(
            passed_by_lvalue_reference
          , U::evaluate_category&lt;1&gt;(args[_lr])
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference_to_const
          , U::evaluate_category&lt;2&gt;(
                args[_rrc | rvalue_const_bitset&lt;2&gt;()]
            )
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference
          , U::evaluate_category&lt;3&gt;(
                args[_rr | rvalue_bitset&lt;3&gt;()]
            )
        );

        return true;
    }
};
</pre>
<p>To invoke the function call operator, bind all its arguments to named
parameters.</p>
<pre class="literal-block">
D const d = D();
d(
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
);
d(
    _lr0 = lvalue_bitset&lt;1&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>The <a class="reference external" href="../../test/preprocessor_eval_cat_no_spec.cpp">preprocessor_eval_cat_no_spec.cpp</a> test program demonstrates proper usage of this
macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">result</tt> is the parenthesized return type of the function call operator.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<p>None.</p>
<p><strong>Approximate expansion:</strong></p>
<pre class="literal-block">
// If <strong>result</strong> is a template instantiation of <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if_c</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::disable_if_c</a>,
// <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if</a>, <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_disable_if_c</a>, or
// <a class="reference external" href="http://en.cppreference.com/w/cpp/types/enable_if">std::enable_if</a>:
template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
using boost_param_no_spec_result_const_ ## __LINE__ ## operator = <strong>result</strong>;

// If <strong>result</strong> is a simple return type:
template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
struct boost_param_no_spec_result_const_ ## __LINE__ ## operator
{
    typedef <strong>result</strong> type;
};

template &lt;typename TaggedArg0, typename ...TaggedArgs&gt;
inline typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::lazy_enable_if</a>&lt;
    <a class="reference internal" href="#are-tagged-arguments"><tt class="docutils literal">are_tagged_arguments</tt></a>&lt;TaggedArg0,TaggedArgs...&gt;
  , boost_param_no_spec_result_const_ ## __LINE__ ## operator&lt;
        TaggedArg0
      , TaggedArgs...
    &gt;
&gt;::type
    operator()(
        TaggedArg0 const&amp; arg0
      , TaggedArgs const&amp;... args
    ) const
{
    return this-&gt;boost_param_no_spec_impl_const ## __LINE__ ## operator(
        static_cast&lt;
            typename
            boost_param_no_spec_result_const_ ## __LINE__ ## operator&lt;
                TaggedArg0
              , TaggedArgs...
            &gt;::type(*)()
        &gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
      , <a class="reference internal" href="#compose"><tt class="docutils literal">compose</tt></a>(arg0, args...)
    );
}

template &lt;typename ResultType, typename Args&gt;
ResultType
    boost_param_no_spec_impl_const ## __LINE__ ## operator(
        (ResultType(*)())
      , Args const&amp; args
    ) const
</pre>
<p>Only the <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> type <tt class="docutils literal">Args</tt> and its object instance <tt class="docutils literal">args</tt> are
available for use within the function body.</p>
</div>
<div class="section" id="boost-parameter-no-spec-constructor-cls-impl">
<span id="boost-parameter-no-spec-constructor"></span><h2><a class="toc-backref" href="#id73">7.17&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONSTRUCTOR(cls, impl)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor_no_spec.hpp">boost/parameter/preprocessor_no_spec.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a constructor that can take in named arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>When designing a front-end class template whose back-end is configurable via
parameterized inheritance, it can be useful to omit argument specifiers from
a named-parameter constructor so that the delegate constructors of the
back-end classes can enforce their own specifications.</p>
<pre class="literal-block">
template &lt;typename B&gt;
struct frontend : B
{
    BOOST_PARAMETER_NO_SPEC_CONSTRUCTOR(frontend, (B))
};
</pre>
<p>Named parameters are required when invoking the constructor; however, none of
their tags need to be in the same namespace.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(a0)
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(a1)
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(a2)
</pre>
<p>For this example, each of the back-end class templates requires its own
parameter to be present in the argument pack.  In practice, such parameters
should be optional, with default values.</p>
<pre class="literal-block">
struct _enabler
{
};

template &lt;typename T&gt;
class backend0
{
    T a0;

 public:
    template &lt;typename ArgPack&gt;
    explicit backend0(
        ArgPack const&amp; args
      , typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
            <a class="reference internal" href="#is-argument-pack"><tt class="docutils literal">is_argument_pack</tt></a>&lt;ArgPack&gt;
          , _enabler
        &gt;::type = _enabler()
    ) : a0(args[_a0])
    {
    }

    T const&amp; get_a0() const
    {
        return this-&gt;a0;
    }
};

template &lt;typename B, typename T&gt;
class backend1 : public B
{
    T a1;

 public:
    template &lt;typename ArgPack&gt;
    explicit backend1(
        ArgPack const&amp; args
      , typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
            <a class="reference internal" href="#is-argument-pack"><tt class="docutils literal">is_argument_pack</tt></a>&lt;ArgPack&gt;
          , _enabler
        &gt;::type = _enabler()
    ) : B(args), a1(args[_a1])
    {
    }

    T const&amp; get_a1() const
    {
        return this-&gt;a1;
    }
};

template &lt;typename B, typename T&gt;
class backend2 : public B
{
    T a2;

 public:
    template &lt;typename ArgPack&gt;
    explicit backend2(
        ArgPack const&amp; args
      , typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
            <a class="reference internal" href="#is-argument-pack"><tt class="docutils literal">is_argument_pack</tt></a>&lt;ArgPack&gt;
          , _enabler
        &gt;::type = _enabler()
    ) : B(args), a2(args[_a2])
    {
    }

    T const&amp; get_a2() const
    {
        return this-&gt;a2;
    }
};
</pre>
<p>This example shows that while <tt class="docutils literal">backend0</tt> must always be the root base class
template and that <tt class="docutils literal">frontend</tt> must always be the most derived class template,
the other back-ends can be chained together in different orders.</p>
<pre class="literal-block">
char const* p = &quot;foo&quot;;
frontend&lt;
    backend2&lt;backend1&lt;backend0&lt;char const*&gt;, char&gt;, int&gt;
&gt; composed_obj0(_a2 = 4, _a1 = ' ', _a0 = p);
frontend&lt;
    backend1&lt;backend2&lt;backend0&lt;char const*&gt;, int&gt;, char&gt;
&gt; composed_obj1(_a0 = p, _a1 = ' ', _a2 = 4);
BOOST_TEST_EQ(composed_obj0.get_a0(), composed_obj1.get_a0());
BOOST_TEST_EQ(composed_obj0.get_a1(), composed_obj1.get_a1());
BOOST_TEST_EQ(composed_obj0.get_a2(), composed_obj1.get_a2());
</pre>
<p>The <a class="reference external" href="../../test/parameterized_inheritance.cpp">parameterized_inheritance.cpp</a> and <a class="reference external" href="../../test/preprocessor_eval_cat_no_spec.cpp">preprocessor_eval_cat_no_spec.cpp</a> test
programs demonstrate proper usage of this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">cls</tt> is the name of the enclosing class.</li>
<li><tt class="docutils literal">impl</tt> is the parenthesized implementation base class for <tt class="docutils literal">cls</tt>.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<p>None.</p>
<p><strong>Approximate expansion:</strong></p>
<pre class="literal-block">
template &lt;
    typename TaggedArg0
  , typename ...TaggedArgs
  , typename = typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
        <a class="reference internal" href="#are-tagged-arguments"><tt class="docutils literal">are_tagged_arguments</tt></a>&lt;TaggedArg0,TaggedArgs...&gt;
    &gt;::type
&gt;
inline explicit <strong>cls</strong>(
    TaggedArg0 const&amp; arg0
  , TaggedArgs const&amp;... args
) : <strong>impl</strong>(<a class="reference internal" href="#compose"><tt class="docutils literal">compose</tt></a>(arg0, args...))
{
}
</pre>
</div>
<div class="section" id="boost-parameter-no-spec-no-base-constructor-cls-impl">
<span id="boost-parameter-no-spec-no-base-constructor"></span><h2><a class="toc-backref" href="#id74">7.18&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_NO_BASE_CONSTRUCTOR(cls, impl)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/preprocessor_no_spec.hpp">boost/parameter/preprocessor_no_spec.hpp</a></td>
</tr>
</tbody>
</table>
<p>Generates a constructor that can take in named arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>The return type of each of the following function templates falls under a
different value category.</p>
<pre class="literal-block">
template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; rvalue_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const rvalue_const_bitset()
{
    return <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp; lvalue_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; lset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return lset;
}

template &lt;std::size_t N&gt;
<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp; lvalue_const_bitset()
{
    static <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const clset = <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;();
    return clset;
}
</pre>
<p>The <tt class="docutils literal"><span class="pre">U::evaluate_category</span></tt> static member function template has a simple job:
to return the correct value category when passed in an object returned by one
of the functions defined above.  Assume that
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is defined.</p>
<pre class="literal-block">
enum invoked
{
    passed_by_lvalue_reference_to_const
  , passed_by_lvalue_reference
  , passed_by_rvalue_reference_to_const
  , passed_by_rvalue_reference
};

struct U
{
    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;)
    {
        return passed_by_lvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;)
    {
        return passed_by_lvalue_reference;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt; const&amp;&amp;)
    {
        return passed_by_rvalue_reference_to_const;
    }

    template &lt;std::size_t N&gt;
    static invoked evaluate_category(<a class="reference external" href="http://en.cppreference.com/w/cpp/utility/bitset">std::bitset</a>&lt;N + 1&gt;&amp;&amp;)
    {
        return passed_by_rvalue_reference;
    }
};
</pre>
<p>Named parameters are required when invoking the constructor; however, none of
their tags need to be in the same namespace.</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lrc, kw0) in(lrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_lr, kw1) in_out(lr))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rrc, kw2) in(rrc))
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>((_rr, kw3) consume(rr))
</pre>
<p>Unlike <a class="reference internal" href="#boost-parameter-no-spec-constructor"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONSTRUCTOR</tt></a>, this macro doesn't require a
base class, only a delegate function to which the generated constructor can
pass its <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a>.</p>
<pre class="literal-block">
struct D
{
    BOOST_PARAMETER_NO_SPEC_NO_BASE_CONSTRUCTOR(D, D::_evaluate)

 private:
    template &lt;typename Args&gt;
    static bool _evaluate(Args const&amp; args)
    {
        BOOST_TEST_EQ(
            passed_by_lvalue_reference_to_const
          , U::evaluate_category&lt;0&gt;(args[_lrc])
        );
        BOOST_TEST_EQ(
            passed_by_lvalue_reference
          , U::evaluate_category&lt;1&gt;(args[_lr])
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference_to_const
          , U::evaluate_category&lt;2&gt;(
                args[_rrc | rvalue_const_bitset&lt;2&gt;()]
            )
        );
        BOOST_TEST_EQ(
            passed_by_rvalue_reference
          , U::evaluate_category&lt;3&gt;(
                args[_rr | rvalue_bitset&lt;3&gt;()]
            )
        );

        return true;
    }
};
</pre>
<p>To invoke the constructor, bind all its arguments to named parameters.</p>
<pre class="literal-block">
D dp0(
    _rr0 = rvalue_bitset&lt;3&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
  , _lr0 = lvalue_bitset&lt;1&gt;()
  , _rrc0 = rvalue_const_bitset&lt;2&gt;()
);
D dp1(
    _lr0 = lvalue_bitset&lt;1&gt;()
  , _lrc0 = lvalue_const_bitset&lt;0&gt;()
);
</pre>
<p>The <a class="reference external" href="../../test/preprocessor_eval_cat_no_spec.cpp">preprocessor_eval_cat_no_spec.cpp</a> test program demonstrates proper usage of
this macro.</p>
<p><strong>Macro parameters:</strong></p>
<ul class="simple">
<li><tt class="docutils literal">cls</tt> is the name of the enclosing class.</li>
<li><tt class="docutils literal">func</tt> is a function that takes in the <a class="reference internal" href="#argumentpack"><span class="concept">ArgumentPack</span></a> that the
generated constructor passes on.</li>
</ul>
<p><strong>Argument specifiers syntax:</strong></p>
<p>None.</p>
<p><strong>Approximate expansion:</strong></p>
<pre class="literal-block">
template &lt;
    typename TaggedArg0
  , typename ...TaggedArgs
  , typename = typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;
        <a class="reference internal" href="#are-tagged-arguments"><tt class="docutils literal">are_tagged_arguments</tt></a>&lt;TaggedArg0,TaggedArgs...&gt;
    &gt;::type
&gt;
inline explicit <strong>cls</strong>(
    TaggedArg0 const&amp; arg0
  , TaggedArgs const&amp;... args
)
{
    <strong>func</strong>(<a class="reference internal" href="#compose"><tt class="docutils literal">compose</tt></a>(arg0, args...));
}
</pre>
</div>
<div class="section" id="boost-parameter-name-name">
<span id="boost-parameter-name"></span><h2><a class="toc-backref" href="#id75">7.19&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NAME(name)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/name.hpp">boost/parameter/name.hpp</a></td>
</tr>
</tbody>
</table>
<p>Declares a tag-type and keyword object.</p>
<p><strong>If</strong> <em>name</em> is of the form:</p>
<pre class="literal-block">
(<em>object-name</em>, <em>namespace-name</em>) <em>qualifier</em>(<em>tag-name</em>)
</pre>
<p><strong>then</strong></p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body"><em>qualifier</em> is either <tt class="docutils literal">in</tt>, <tt class="docutils literal">out</tt>, <tt class="docutils literal">in_out</tt>, <tt class="docutils literal">consume</tt>,
<tt class="docutils literal">move_from</tt>, or <tt class="docutils literal">forward</tt>.</td>
</tr>
<tr class="field"><th class="field-name">Expands to:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
namespace <em>namespace-name</em> {

    struct <em>tag-name</em>
    {
        static constexpr char const* keyword_name()
        {
            return ## <em>tag-name</em>;
        }

        typedef <em>unspecified</em> _;
        typedef <em>unspecified</em> _1;
        typedef boost::parameter::<em>qualifier</em> ## _reference qualifier;

        // The following definitions are available only when
        // <a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> is defined.

        template &lt;typename ArgumentPack&gt;
        using binding_fn = typename <a class="reference internal" href="#binding"><tt class="docutils literal">binding</tt></a>&lt;
            ArgumentPack
          , <em>tag-name</em>
        &gt;::type;

        template &lt;typename ArgumentPack&gt;
        using fn = typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;ArgumentPack, <em>tag-name</em>&gt;::type;
    };
}

<a class="reference internal" href="#keyword"><tt class="docutils literal">keyword</tt></a>&lt;<em>tag-namespace</em>::<em>tag-name</em>&gt; const&amp; <em>object-name</em>
    = <a class="reference internal" href="#keyword"><tt class="docutils literal">keyword</tt></a>&lt;<em>tag-namespace</em>::<em>tag-name</em>&gt;::instance;
</pre>
<p><strong>Else If</strong> <em>name</em> is of the form:</p>
<pre class="literal-block">
(<em>tag-name</em>, <em>namespace-name</em>) <em>object-name</em>
</pre>
<p><strong>then</strong></p>
<p>Treats <em>name</em> as if it were of the form:</p>
<pre class="literal-block">
(forward(<em>tag-name</em>), <em>namespace-name</em>) <em>object-name</em>
</pre>
<p><strong>Else If</strong> <em>name</em> is of the form:</p>
<pre class="literal-block">
<em>qualifier</em>(<em>tag-name</em>)
</pre>
<p><strong>then</strong></p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body"><em>qualifier</em> is either <tt class="docutils literal">in</tt>, <tt class="docutils literal">out</tt>, <tt class="docutils literal">in_out</tt>, <tt class="docutils literal">consume</tt>,
<tt class="docutils literal">move_from</tt>, or <tt class="docutils literal">forward</tt>.</td>
</tr>
<tr class="field"><th class="field-name">Expands to:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
namespace tag {

    struct <em>tag-name</em>
    {
        static constexpr char const* keyword_name()
        {
            return ## <em>tag-name</em>;
        }

        typedef <em>unspecified</em> _;
        typedef <em>unspecified</em> _1;
        typedef boost::parameter::<em>qualifier</em> ## _reference qualifier;

        // The following definitions are available only when
        // <a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> is defined.

        template &lt;typename ArgumentPack&gt;
        using binding_fn = typename <a class="reference internal" href="#binding"><tt class="docutils literal">binding</tt></a>&lt;
            ArgumentPack
          , <em>tag-name</em>
        &gt;::type;

        template &lt;typename ArgumentPack&gt;
        using fn = typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;ArgumentPack, <em>tag-name</em>&gt;::type;
    };
}

<a class="reference internal" href="#keyword"><tt class="docutils literal">keyword</tt></a>&lt;tag::<em>tag-name</em>&gt; const&amp; _ ## <em>tag-name</em>
    = <a class="reference internal" href="#keyword"><tt class="docutils literal">keyword</tt></a>&lt;tag::<em>tag-name</em>&gt;::instance;
</pre>
<p><strong>Else</strong></p>
<p>Treats <em>name</em> as if it were of the form:</p>
<pre class="literal-block">
forward(<em>tag-name</em>)
</pre>
</div>
<div class="section" id="boost-parameter-nested-keyword-tag-namespace-name-alias">
<span id="boost-parameter-nested-keyword"></span><h2><a class="toc-backref" href="#id76">7.20&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_NESTED_KEYWORD(tag_namespace, name, alias)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/nested_keyword.hpp">boost/parameter/nested_keyword.hpp</a></td>
</tr>
</tbody>
</table>
<p>Declares a tag-type, a keyword object, and an alias for that object nested in
the tag-type.</p>
<p><strong>If</strong> <em>name</em> is of the form:</p>
<pre class="literal-block">
<em>qualifier</em>(<em>tag-name</em>)
</pre>
<p><strong>then</strong></p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Requires:</th><td class="field-body"><em>qualifier</em> is either <tt class="docutils literal">in</tt>, <tt class="docutils literal">out</tt>, <tt class="docutils literal">in_out</tt>, <tt class="docutils literal">consume</tt>,
<tt class="docutils literal">move_from</tt>, or <tt class="docutils literal">forward</tt>.</td>
</tr>
<tr class="field"><th class="field-name">Expands to:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
namespace tag {

    struct <em>tag-name</em>
    {
        static constexpr char const* keyword_name()
        {
            return ## <em>tag-name</em> ## _;
        }

        typedef <em>unspecified</em> _;
        typedef <em>unspecified</em> _1;
        typedef boost::parameter::<em>qualifier</em> ## _reference qualifier;
        static <a class="reference internal" href="#keyword"><tt class="docutils literal">keyword</tt></a>&lt;<em>tag-name</em>&gt; const&amp; <em>alias</em>;

        // The following definitions are available only when
        // <a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> is defined.

        template &lt;typename ArgumentPack&gt;
        using binding_fn = typename <a class="reference internal" href="#binding"><tt class="docutils literal">binding</tt></a>&lt;
            ArgumentPack
          , <em>tag-name</em>
        &gt;::type;

        template &lt;typename ArgumentPack&gt;
        using fn = typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;ArgumentPack, <em>tag-name</em>&gt;::type;
    };

    <a class="reference internal" href="#keyword"><tt class="docutils literal">keyword</tt></a>&lt;<em>tag-name</em>&gt; const&amp; tag::<em>tag-name</em>::<em>alias</em>
        = <a class="reference internal" href="#keyword"><tt class="docutils literal">keyword</tt></a>&lt;<em>tag-name</em>&gt;::instance;
}

<a class="reference internal" href="#keyword"><tt class="docutils literal">keyword</tt></a>&lt;tag::<em>tag-name</em>&gt; const&amp; tag::<em>tag-name</em>::<em>name</em>
    = <a class="reference internal" href="#keyword"><tt class="docutils literal">keyword</tt></a>&lt;tag::<em>tag-name</em>&gt;::instance;
</pre>
<p><strong>Else</strong></p>
<p>Treats <em>name</em> as if it were of the form:</p>
<pre class="literal-block">
forward(<em>tag-name</em>)
</pre>
</div>
<div class="section" id="boost-parameter-template-keyword-name">
<span id="boost-parameter-template-keyword"></span><h2><a class="toc-backref" href="#id77">7.21&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_TEMPLATE_KEYWORD(name)</tt></a></h2>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/template_keyword.hpp">boost/parameter/template_keyword.hpp</a></td>
</tr>
<tr class="field"><th class="field-name">Included by:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/name.hpp">boost/parameter/name.hpp</a></td>
</tr>
<tr class="field"><th class="field-name">Expands to:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
namespace tag {

    struct <em>name</em>;
}

template &lt;typename T&gt;
struct <em>name</em> : <a class="reference internal" href="#template-keyword"><tt class="docutils literal">template_keyword</tt></a>&lt;tag:: <em>name</em>, T&gt;
{
};
</pre>
<p>The <a class="reference external" href="../../test/function_type_tpl_param.cpp">function_type_tpl_param.cpp</a> test program demonstrates proper usage of
this macro.</p>
</div>
<div class="section" id="boost-parameter-fun-r-n-l-h-p">
<span id="boost-parameter-fun"></span><h2><a class="toc-backref" href="#id78">7.22&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_FUN(r, n, l, h, p)</tt></a></h2>
<div class="admonition admonition-deprecated">
<p class="first admonition-title">Deprecated</p>
<p class="last">This macro has been deprecated in favor of
<tt class="docutils literal">BOOST_PARAMETER_FUNCTION</tt>.</p>
</div>
<p>Generates a sequence of <a class="reference external" href="index.html#forwarding-functions">forwarding function</a> templates named
<tt class="docutils literal">n</tt>, with arities ranging from <tt class="docutils literal">l</tt> to <tt class="docutils literal">h</tt>, returning <tt class="docutils literal">r</tt>,
and using <tt class="docutils literal">p</tt> to control overload resolution and assign tags to
positional arguments.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/macros.hpp">boost/parameter/macros.hpp</a></td>
</tr>
<tr class="field"><th class="field-name">Requires:</th><td class="field-body"><tt class="docutils literal">l</tt> and <tt class="docutils literal">h</tt> are nonnegative integer tokens
such that <tt class="docutils literal">l</tt> &lt; <tt class="docutils literal">h</tt></td>
</tr>
<tr class="field"><th class="field-name">Expands to:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
template &lt;typename A1, typename A2, …, typename A ## <strong>l</strong>&gt;
r
    name(
        A1 &amp;&amp; a1, A2 &amp;&amp; a2, …, A ## <strong>l</strong> &amp;&amp; a ## <strong>l</strong>
      , typename <strong>p</strong>::match&lt;A1, A2, …, A ## <strong>l</strong>&gt;::type p = <strong>p</strong>()
    )
{
    return <strong>name</strong>_with_named_params(
        <strong>p</strong>(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A1&gt;(a1)
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A2&gt;(a2)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>l</strong>&gt;(a ## <strong>l</strong>)
        )
    );
}

template &lt;
    typename A1
  , typename A2
  , …
  , typename A ## <strong>l</strong>
  , typename A ## <a class="reference external" href="../../../preprocessor/doc/ref/inc.html">BOOST_PP_INC</a>(<strong>l</strong>)
&gt;
r
    name(
        A1 &amp;&amp; a1, A2 &amp;&amp; a2, …, A ## <strong>l</strong> &amp;&amp; a ## <strong>l</strong>
      , A ## <a class="reference external" href="../../../preprocessor/doc/ref/inc.html">BOOST_PP_INC</a>(<strong>l</strong>) const&amp; a ## <a class="reference external" href="../../../preprocessor/doc/ref/inc.html">BOOST_PP_INC</a>(<strong>l</strong>)
      , typename <strong>p</strong>::match&lt;
            A1, A2, …, A ## <strong>l</strong>, A ## <a class="reference external" href="../../../preprocessor/doc/ref/inc.html">BOOST_PP_INC</a>(<strong>l</strong>)
        &gt;::type p = <strong>p</strong>()
    )
{
    return <strong>name</strong>_with_named_params(
        <strong>p</strong>(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A1&gt;(a1)
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A2&gt;(a2)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>l</strong>&gt;(a ## <strong>l</strong>)
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <a class="reference external" href="../../../preprocessor/doc/ref/inc.html">BOOST_PP_INC</a>(<strong>l</strong>)&gt;(
                a ## <a class="reference external" href="../../../preprocessor/doc/ref/inc.html">BOOST_PP_INC</a>(<strong>l</strong>)
            )
        )
    );
}

<span class="vellipsis">⋮</span>

template &lt;typename A1, typename A2, …, typename A ## <strong>h</strong>&gt;
r
    name(
        A1 &amp;&amp; a1, A2 &amp;&amp; a2, …, A ## <strong>h</strong> &amp;&amp; x ## <strong>h</strong>
      , typename <strong>p</strong>::match&lt;A1, A2, …, A ## <strong>h</strong>&gt;::type p = <strong>p</strong>()
    )
{
    return <strong>name</strong>_with_named_params(
        <strong>p</strong>(
            <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A1&gt;(a1)
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A2&gt;(a2)
          , …
          , <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/forward">std::forward</a>&lt;A ## <strong>h</strong>&gt;(a ## <strong>h</strong>)
        )
    );
}
</pre>
<p>The <a class="reference external" href="../../test/macros.cpp">macros.cpp</a> and <a class="reference external" href="../../test/macros_eval_category.cpp">macros_eval_category.cpp</a> test programs demonstrate proper
usage of this macro.</p>
</div>
<div class="section" id="boost-parameter-keyword-n-k">
<span id="boost-parameter-keyword"></span><h2><a class="toc-backref" href="#id79">7.23&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_KEYWORD(n, k)</tt></a></h2>
<div class="admonition admonition-deprecated">
<p class="first admonition-title">Deprecated</p>
<p class="last">This macro has been deprecated in favor of
<tt class="docutils literal">BOOST_PARAMETER_NAME</tt>.</p>
</div>
<p>Generates the declaration of a <a class="reference internal" href="#keyword-tag-type">keyword tag type</a> named <tt class="docutils literal">k</tt> in
namespace <tt class="docutils literal">n</tt> and a corresponding <a class="reference internal" href="#keyword-object">keyword object</a> definition in
the enclosing namespace.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/keyword.hpp">boost/parameter/keyword.hpp</a></td>
</tr>
<tr class="field"><th class="field-name">Expands to:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
namespace <strong>n</strong> {

    struct <strong>k</strong>
    {
        static constexpr char const* keyword_name()
        {
            return ## <em>k</em>;
        }

        typedef <em>unspecified</em> _;
        typedef <em>unspecified</em> _1;
        typedef boost::parameter::forward_reference qualifier;

        // The following definitions are available only when
        // <a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> is defined.

        template &lt;typename ArgumentPack&gt;
        using binding_fn = typename <a class="reference internal" href="#binding"><tt class="docutils literal">binding</tt></a>&lt;
            ArgumentPack
          , <em>k</em>
        &gt;::type;

        template &lt;typename ArgumentPack&gt;
        using fn = typename <a class="reference internal" href="#value-type"><tt class="docutils literal">value_type</tt></a>&lt;ArgumentPack, <em>k</em>&gt;::type;
    };
}

namespace {

    <a class="reference internal" href="#keyword"><tt class="docutils literal">keyword</tt></a>&lt;<em>n</em>::<strong>k</strong>&gt; const&amp; <strong>k</strong>
        = <a class="reference internal" href="#keyword"><tt class="docutils literal">keyword</tt></a>&lt;<em>n</em>::<strong>k</strong>&gt;::instance;
}
</pre>
</div>
<div class="section" id="boost-parameter-match-p-a-x">
<h2><a class="toc-backref" href="#id80">7.24&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_MATCH(p, a, x)</tt></a></h2>
<p>Generates a defaulted parameter declaration for a <a class="reference external" href="index.html#forwarding-functions">forwarding function</a>.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/match.hpp">boost/parameter/match.hpp</a></td>
</tr>
<tr class="field"><th class="field-name">Requires:</th><td class="field-body"><tt class="docutils literal">a</tt> is a <a class="reference external" href="../../../preprocessor/doc/data.html">Boost.Preprocessor sequence</a> of the form</td>
</tr>
</tbody>
</table>
<pre class="literal-block">
(A0)(A1)…(A ## <em>n</em>)
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Expands to:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
typename <strong>p</strong>::match&lt;<strong>A0</strong>, <strong>A1</strong>, …, <strong>A</strong> ## <em>n</em>&gt;::type
    <strong>x</strong> = <strong>p</strong>()
</pre>
</div>
</div>
<div class="section" id="configuration-macros">
<h1><a class="toc-backref" href="#id81">8&nbsp;&nbsp;&nbsp;Configuration Macros</a></h1>
<div class="section" id="id21">
<span id="boost-parameter-has-perfect-forwarding"></span><h2><a class="toc-backref" href="#id82">8.1&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a></h2>
<p>Determines whether or not the library supports perfect forwarding, or the
preservation of parameter value categories.  Users can manually disable this
macro by <tt class="docutils literal">#defining</tt> the <a class="reference internal" href="#boost-parameter-disable-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_DISABLE_PERFECT_FORWARDING</tt></a>
macro.  Otherwise, the library will <tt class="docutils literal">#define</tt> this macro if and only if it
is not already defined, and if the configuration macros
<a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__03_defects"><tt class="docutils literal">BOOST_NO_FUNCTION_TEMPLATE_ORDERING</tt></a>, <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__03_defects"><tt class="docutils literal">BOOST_NO_SFINAE</tt></a>,
<a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_RVALUE_REFERENCES</tt></a>, <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_VARIADIC_TEMPLATES</tt></a>, and
<a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_FUNCTION_TEMPLATE_DEFAULT_ARGS</tt></a> are not already defined by
<a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a>.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/config.hpp">boost/parameter/config.hpp</a></td>
</tr>
</tbody>
</table>
</div>
<div class="section" id="id22">
<span id="boost-parameter-disable-perfect-forwarding"></span><h2><a class="toc-backref" href="#id83">8.2&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_DISABLE_PERFECT_FORWARDING</tt></a></h2>
<p>It may be necessary to test user code in case perfect forwarding support is
unavailable.  Users can <tt class="docutils literal">#define</tt> this macro either in their project
settings or before including any library header files.  Doing so will leave
both <a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> and
<a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> undefined.</p>
</div>
<div class="section" id="id23">
<span id="boost-parameter-can-use-mp11"></span><h2><a class="toc-backref" href="#id84">8.3&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a></h2>
<p>Determines whether or not the library can use <a class="reference external" href="../../../mp11/doc/html/mp11.html">Boost.MP11</a>, a C++11
metaprogramming library.  Users can manually disable this macro by
<tt class="docutils literal">#defining</tt> the <a class="reference internal" href="#boost-parameter-disable-mp11-usage"><tt class="docutils literal">BOOST_PARAMETER_DISABLE_MP11_USAGE</tt></a> macro or the
<a class="reference internal" href="#boost-parameter-disable-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_DISABLE_PERFECT_FORWARDING</tt></a> macro.  Otherwise, the library
will <tt class="docutils literal">#define</tt> this macro if and only if it is not already defined, if
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is defined, and if the configuration
macros <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_CONSTEXPR</tt></a>, <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_DECLTYPE_N3276</tt></a>,
<a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_AUTO_DECLARATIONS</tt></a>, <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_TEMPLATE_ALIASES</tt></a>,
<a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_STATIC_ASSERT</tt></a>, <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_HDR_TYPE_TRAITS</tt></a>,
<a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_HDR_INITIALIZER_LIST</tt></a>, and <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_HDR_TUPLE</tt></a>
are not already defined by <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a>.</p>
<div class="admonition admonition-usage-note">
<p class="first admonition-title">Usage Note</p>
<p class="last"><a class="reference external" href="../../../mp11/doc/html/mp11.html">Boost.MP11</a> and <a class="reference external" href="../../../mpl/doc/index.html">Boost.MPL</a> are <strong>not</strong> mutually exclusive.  It's
perfectly acceptable to specify deduced parameters using both quoted
metafunctions and metafunction classes, for example.  See
<a class="reference external" href="../../test/evaluate_category.cpp">evaluate_category.cpp</a>.</p>
</div>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/config.hpp">boost/parameter/config.hpp</a></td>
</tr>
<tr class="field"><th class="field-name">Example usage:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<p>Given the following definitions:</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(x)

template &lt;typename A0&gt;
typename <a class="reference external" href="../../../core/doc/html/core/enable_if.html">boost::enable_if</a>&lt;<a class="reference external" href="http://en.cppreference.com/w/cpp/types/is_same">std::is_same</a>&lt;int,A0&gt;,int&gt;::type
    sfinae(A0 const&amp; a0)
{
    return 0;
}
</pre>
<p><a class="reference external" href="../../../mp11/doc/html/mp11.html">Boost.MP11</a> allows deduced parameters to be defined more succinctly:</p>
<pre class="literal-block">
template &lt;typename T, typename Args&gt;
using predicate = <a class="reference external" href="http://en.cppreference.com/w/cpp/types/is_convertible">std::is_convertible</a>&lt;T,char const*&gt;;

<a class="reference internal" href="#boost-parameter-function"><tt class="docutils literal">BOOST_PARAMETER_FUNCTION</tt></a>((int), sfinae, tag,
    (deduced
        (optional
            (x
              , *(<a class="reference external" href="../../../mp11/doc/html/mp11.html#mp_quotef">boost::mp11::mp_quote</a>&lt;predicate&gt;)
              , static_cast&lt;char const*&gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
            )
        )
    )
)
{
    return 1;
}
</pre>
<p>Without <a class="reference external" href="../../../mp11/doc/html/mp11.html">Boost.MP11</a>, deduced parameter definitions tend to be more verbose:</p>
<pre class="literal-block">
struct predicate
{
    template &lt;typename T, typename Args&gt;
    struct apply
      : <a class="reference external" href="../../../mpl/doc/refmanual/if.html">mpl::if_</a>&lt;
            <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_convertible.html">boost::is_convertible</a>&lt;T,char const*&gt;
          , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a>  // Still have to convert to a
          , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean Integral Constant</a>.
        &gt;
    {
    };
};

<a class="reference internal" href="#boost-parameter-function"><tt class="docutils literal">BOOST_PARAMETER_FUNCTION</tt></a>((int), sfinae, tag,
    (deduced
        (optional
            (x
              , *(predicate)
              , static_cast&lt;char const*&gt;(<a class="reference external" href="http://en.cppreference.com/w/cpp/language/nullptr">std::nullptr</a>)
            )
        )
    )
)
{
    return 1;
}
</pre>
<p>Either way, the following assertions will succeed:</p>
<pre class="literal-block">
assert(1 == sfinae());
assert(1 == sfinae(&quot;foo&quot;));
assert(0 == sfinae(1));
</pre>
<p>As another example, given the following declarations and definitions:</p>
<pre class="literal-block">
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(x)
<a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a>(y)

template &lt;typename E, typename Args&gt;
void check0(E const&amp; e, Args const&amp; args);

template &lt;typename P, typename E, typename ...Args&gt;
void check(E const&amp; e, Args const&amp;... args)
{
    check0(e, P()(args...));
}
</pre>
<p>Argument packs qualify as <a class="reference external" href="../../../mp11/doc/html/mp11.html">Boost.MP11</a>-style lists containing
<a class="reference internal" href="#keyword-tag-type">keyword tag type</a>s:</p>
<pre class="literal-block">
template &lt;typename Args&gt;
struct some_functor
{
    template &lt;typename K&gt;
    void operator()(K&amp;&amp;) const
    {
        // K is one of tag::x, tag::y, etc.
    }
};

template &lt;typename E, typename Args&gt;
void check0(E const&amp; e, Args const&amp; args)
{
    boost::mp11::mp_for_each&lt;E&gt;(some_functor&lt;Args&gt;());
}
</pre>
<p>The first check determines whether or not the argument type of <tt class="docutils literal">_y</tt> is the
same as the reference type of <tt class="docutils literal">_x</tt>, while the second check determines
whether or not the argument type of <tt class="docutils literal">_y</tt> is convertible to the value type of
<tt class="docutils literal">_x</tt>.  Here, it's possible to access the reference and value result types of
indexing an argument pack a little more directly:</p>
<pre class="literal-block">
// Use mp_bind on tag::x::binding_fn to access the reference type of _x.
check&lt;
    <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        tag::x
      , <a class="reference internal" href="#optional"><tt class="docutils literal">optional</tt></a>&lt;
            <a class="reference internal" href="#deduced"><tt class="docutils literal">deduced</tt></a>&lt;tag::y&gt;
          , <a class="reference external" href="../../../mp11/doc/html/mp11.html#mp_bindf_t">boost::mp11::mp_bind</a>&lt;
                <a class="reference external" href="http://en.cppreference.com/w/cpp/types/is_same">std::is_same</a> // <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>, standard version.
              , <a class="reference external" href="../../../mp11/doc/html/mp11.html#1_9">boost::mp11::_1</a> // will be bound to the argument type of _y.
              , <a class="reference external" href="../../../mp11/doc/html/mp11.html#mp_bindf_t">boost::mp11::mp_bind</a>&lt;
                    tag::x::binding_fn
                  , <a class="reference external" href="../../../mp11/doc/html/mp11.html#1_9">boost::mp11::_2</a> // will be bound to the argument pack type.
                &gt;
            &gt;
        &gt;
    &gt;
&gt;((_x = 0, _y = 1), 0, 1);

// Use mp_bind_q on tag::x to access the value type of _x.
check&lt;
    <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        tag::x
      , <a class="reference internal" href="#optional"><tt class="docutils literal">optional</tt></a>&lt;
            <a class="reference internal" href="#deduced"><tt class="docutils literal">deduced</tt></a>&lt;tag::y&gt;
          , <a class="reference external" href="../../../mp11/doc/html/mp11.html#mp_bindf_t">boost::mp11::mp_bind</a>&lt;
                <a class="reference external" href="http://en.cppreference.com/w/cpp/types/is_convertible">std::is_convertible</a> // <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_convertible.html">boost::is_convertible</a>, standard version.
              , <a class="reference external" href="../../../mp11/doc/html/mp11.html#1_9">boost::mp11::_1</a> // will be bound to the argument type of _y.
              , <a class="reference external" href="../../../mp11/doc/html/mp11.html#mp_bind_qq_t">boost::mp11::mp_bind_q</a>&lt;
                    tag::x
                  , <a class="reference external" href="../../../mp11/doc/html/mp11.html#1_9">boost::mp11::_2</a> // will be bound to the argument pack type.
                &gt;
            &gt;
        &gt;
    &gt;
&gt;((_x = 0U, _y = 1U), 0U, 1U);
</pre>
<p>Argument packs still qualify as <a class="reference external" href="../../../mpl/doc/index.html">Boost.MPL</a>-style lists containing
<a class="reference internal" href="#keyword-tag-type">keyword tag type</a>s:</p>
<pre class="literal-block">
template &lt;typename Args&gt;
struct some_functor
{
    template &lt;typename K&gt;
    void operator()(K) const
    {
        // K is one of tag::x, tag::y, etc.
    }
};

template &lt;typename E, typename Args&gt;
void check0(E const&amp; e, Args const&amp; args)
{
    boost::mpl::for_each&lt;E&gt;(some_functor&lt;Args&gt;());
}
</pre>
<p>However, without <a class="reference external" href="../../../mp11/doc/html/mp11.html">Boost.MP11</a>, the corresponding checks become a little more
verbose:</p>
<pre class="literal-block">
check&lt;
    <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        tag::x
      , <a class="reference internal" href="#optional"><tt class="docutils literal">optional</tt></a>&lt;
            <a class="reference internal" href="#deduced"><tt class="docutils literal">deduced</tt></a>&lt;tag::y&gt;
          , <a class="reference external" href="../../../mpl/doc/refmanual/if.html">mpl::if_</a>&lt;
                <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_same.html">boost::is_same</a>&lt;
                    <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/add_lvalue_reference.html">boost::add_lvalue_reference</a>&lt;<a class="reference external" href="../../../mp11/doc/html/mp11.html#1_9">boost::mp11::_1</a>&gt;
                  , <a class="reference internal" href="#binding"><tt class="docutils literal">binding</tt></a>&lt;<a class="reference external" href="../../../mp11/doc/html/mp11.html#1_9">boost::mp11::_2</a>, tag::x&gt;
                &gt;
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a>  // Still have to convert to a
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean Integral Constant</a>.
            &gt;
        &gt;
    &gt;
&gt;((_x = 0, _y = 1), 0, 1);

// Use tag::x::_ or tag::x::_1 to access the value type of _x.
check&lt;
    <a class="reference internal" href="#parameters"><tt class="docutils literal">parameters</tt></a>&lt;
        tag::x
      , <a class="reference internal" href="#optional"><tt class="docutils literal">optional</tt></a>&lt;
            <a class="reference internal" href="#deduced"><tt class="docutils literal">deduced</tt></a>&lt;tag::y&gt;
          , <a class="reference external" href="../../../mpl/doc/refmanual/if.html">mpl::if_</a>&lt;
                <a class="reference external" href="../../../type_traits/doc/html/boost_typetraits/is_convertible.html">boost::is_convertible</a>&lt;<a class="reference external" href="../../../mp11/doc/html/mp11.html#1_9">boost::mp11::_1</a>, tag::x::_1&gt;
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::true_</a>  // Still have to convert to a
              , <a class="reference external" href="../../../mpl/doc/refmanual/bool.html">mpl::false_</a> // <a class="reference external" href="../../../mpl/doc/refmanual/integral-constant.html">Boolean Integral Constant</a>.
            &gt;
        &gt;
    &gt;
&gt;((_x = 0U, _y = 1U), 0U, 1U);
</pre>
<p>The <a class="reference external" href="../../test/singular.cpp">singular.cpp</a>, <a class="reference external" href="../../test/compose.cpp">compose.cpp</a>, <a class="reference external" href="../../test/optional_deduced_sfinae.cpp">optional_deduced_sfinae.cpp</a>, and
<a class="reference external" href="../../test/deduced_dependent_predicate.cpp">deduced_dependent_predicate.cpp</a> test programs demonstrate proper usage of this
macro.</p>
</div>
<div class="section" id="id24">
<span id="boost-parameter-disable-mp11-usage"></span><h2><a class="toc-backref" href="#id85">8.4&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_DISABLE_MP11_USAGE</tt></a></h2>
<p>It may be necessary to disable usage of <a class="reference external" href="../../../mp11/doc/html/mp11.html">Boost.MP11</a> for compilers that
cannot support it.  Users can <tt class="docutils literal">#define</tt> this macro either in their project
settings or before including any library header files.  Doing so will leave
<a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> undefined.</p>
</div>
<div class="section" id="id25">
<span id="boost-parameter-variadic-mpl-sequence"></span><h2><a class="toc-backref" href="#id86">8.5&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_VARIADIC_MPL_SEQUENCE</tt></a></h2>
<p>If <a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is <tt class="docutils literal">#defined</tt>, then determines
the <a class="reference external" href="../../../mpl/doc/refmanual/variadic-sequence.html">MPL Variadic Sequence</a> underlying the nested <tt class="docutils literal">parameter_spec</tt> type of
<tt class="docutils literal">parameters</tt>.  If the user does not manually <tt class="docutils literal">#define</tt> this macro, then the
library will <tt class="docutils literal">#define</tt> it as <a class="reference external" href="../../../mp11/doc/html/mp11.html#mp_listt"><tt class="docutils literal"><span class="pre">boost::mp11::mp_list</span></tt></a> if
<a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> is defined, <a class="reference external" href="../../../fusion/doc/html/fusion/container/list.html"><tt class="docutils literal"><span class="pre">boost::fusion::list</span></tt></a> if
<a class="reference external" href="../../../../boost/fusion/container/list/list_fwd.hpp"><tt class="docutils literal">BOOST_FUSION_HAS_VARIADIC_LIST</tt></a> is defined (by <a class="reference external" href="../../../fusion/doc/html/index.html">Boost.Fusion</a>),
<a class="reference external" href="../../../fusion/doc/html/fusion/container/deque.html"><tt class="docutils literal"><span class="pre">boost::fusion::deque</span></tt></a> if <a class="reference external" href="../../../../boost/fusion/container/deque/deque_fwd.hpp"><tt class="docutils literal">BOOST_FUSION_HAS_VARIADIC_DEQUE</tt></a> is defined
(by <a class="reference external" href="../../../fusion/doc/html/index.html">Boost.Fusion</a>), or <a class="reference external" href="../../../mpl/doc/refmanual/vector.html"><tt class="docutils literal"><span class="pre">boost::mpl::vector</span></tt></a> otherwise.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Example:</th><td class="field-body"></td>
</tr>
</tbody>
</table>
<pre class="literal-block">
#define BOOST_PARAMETER_VARIADIC_MPL_SEQUENCE <a class="reference external" href="../../../fusion/doc/html/fusion/container/vector.html"><tt class="docutils literal"><span class="pre">boost::fusion::vector</span></tt></a>
</pre>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/parameters.hpp">boost/parameter/parameters.hpp</a></td>
</tr>
</tbody>
</table>
</div>
<div class="section" id="id26">
<span id="boost-parameter-max-arity"></span><h2><a class="toc-backref" href="#id87">8.6&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_MAX_ARITY</tt></a></h2>
<p>If <a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is <tt class="docutils literal">#defined</tt>, then:</p>
<ul class="simple">
<li>If the <a class="reference external" href="../../../mpl/doc/refmanual/variadic-sequence.html">MPL Variadic Sequence</a> underlying the nested <tt class="docutils literal">parameter_spec</tt>
type of <tt class="docutils literal">parameters</tt> does not have a size limit--which is the case with
<a class="reference external" href="../../../mp11/doc/html/mp11.html#mp_listt"><tt class="docutils literal"><span class="pre">boost::mp11::mp_list</span></tt></a>, <a class="reference external" href="../../../fusion/doc/html/fusion/container/list.html"><tt class="docutils literal"><span class="pre">boost::fusion::list</span></tt></a>, and <a class="reference external" href="../../../fusion/doc/html/fusion/container/deque.html"><tt class="docutils literal"><span class="pre">boost::fusion::deque</span></tt></a>, but not
<a class="reference external" href="../../../mpl/doc/refmanual/vector.html"><tt class="docutils literal"><span class="pre">boost::mpl::vector</span></tt></a>--then this macro can be safely ignored.  User code that
manually defines <a class="reference internal" href="#boost-parameter-variadic-mpl-sequence"><tt class="docutils literal">BOOST_PARAMETER_VARIADIC_MPL_SEQUENCE</tt></a> should also
manually define this macro to the size limit of the sequence if it has
one.</li>
</ul>
<p>If <a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is <strong>not</strong> <tt class="docutils literal">#defined</tt>, then:</p>
<ul class="simple">
<li>Mutable references must be wrapped by <a class="reference external" href="../../../core/doc/html/core/ref.html"><tt class="docutils literal"><span class="pre">boost::ref</span></tt></a> or <a class="reference external" href="http://en.cppreference.com/w/cpp/utility/functional/ref"><tt class="docutils literal"><span class="pre">std::ref</span></tt></a> if passed
by position to Boost.Parameter-enabled functions with arity greater than
or equal to <a class="reference internal" href="#boost-parameter-exponential-overload-threshold-arity"><tt class="docutils literal">BOOST_PARAMETER_EXPONENTIAL_OVERLOAD_THRESHOLD_ARITY</tt></a>.</li>
</ul>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/config.hpp">boost/parameter/config.hpp</a></td>
</tr>
<tr class="field"><th class="field-name">Default Value:</th><td class="field-body"><a class="reference external" href="../../../mpl/doc/refmanual/limit-vector-size.html"><tt class="docutils literal">BOOST_MPL_LIMIT_VECTOR_SIZE</tt></a> (defined by <a class="reference external" href="../../../mpl/doc/index.html">Boost.MPL</a>) if
perfect forwarding is supported, <tt class="docutils literal">8</tt> otherwise.</td>
</tr>
<tr class="field"><th class="field-name">Minimum Value:</th><td class="field-body"><tt class="docutils literal">2</tt></td>
</tr>
<tr class="field"><th class="field-name">Maximum Value:</th><td class="field-body"><a class="reference internal" href="#boost-parameter-compose-max-arity"><tt class="docutils literal">BOOST_PARAMETER_COMPOSE_MAX_ARITY</tt></a></td>
</tr>
</tbody>
</table>
</div>
<div class="section" id="id27">
<span id="boost-parameter-compose-max-arity"></span><h2><a class="toc-backref" href="#id88">8.7&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_COMPOSE_MAX_ARITY</tt></a></h2>
<p>If <a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is <strong>not</strong> <tt class="docutils literal">#defined</tt>, then
determines the maximum number of arguments supported by the <tt class="docutils literal">compose</tt> function
and by the <a class="reference internal" href="#boost-parameter-no-spec-function"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_FUNCTION</tt></a>,
<a class="reference internal" href="#boost-parameter-no-spec-member-function"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_MEMBER_FUNCTION</tt></a>,
<a class="reference internal" href="#boost-parameter-no-spec-const-member-function"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONST_MEMBER_FUNCTION</tt></a>,
<a class="reference internal" href="#boost-parameter-no-spec-function-call-operator"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_FUNCTION_CALL_OPERATOR</tt></a>,
<a class="reference internal" href="#boost-parameter-no-spec-const-function-call-operator"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONST_FUNCTION_CALL_OPERATOR</tt></a>,
<a class="reference internal" href="#boost-parameter-no-spec-constructor"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_CONSTRUCTOR</tt></a>, and
<a class="reference internal" href="#boost-parameter-no-spec-no-base-constructor"><tt class="docutils literal">BOOST_PARAMETER_NO_SPEC_NO_BASE_CONSTRUCTOR</tt></a> code generation macros.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/config.hpp">boost/parameter/config.hpp</a></td>
</tr>
<tr class="field"><th class="field-name">Default Value:</th><td class="field-body"><tt class="docutils literal">20</tt> for a few older compilers, <tt class="docutils literal">64</tt> otherwise</td>
</tr>
<tr class="field"><th class="field-name">Minimum Value:</th><td class="field-body"><tt class="docutils literal">2</tt></td>
</tr>
</tbody>
</table>
</div>
<div class="section" id="id28">
<span id="boost-parameter-exponential-overload-threshold-arity"></span><h2><a class="toc-backref" href="#id89">8.8&nbsp;&nbsp;&nbsp;<tt class="docutils literal">BOOST_PARAMETER_EXPONENTIAL_OVERLOAD_THRESHOLD_ARITY</tt></a></h2>
<p>If this library does <strong>not</strong> support perfect forwarding, determines the number
of arguments less than which <tt class="docutils literal">parameters</tt> generates an exponential number of
function call operator overloads, and greater than or equal to which
<tt class="docutils literal">parameters</tt> does not.  Will only be <tt class="docutils literal">#defined</tt> by the library if it is
not already <tt class="docutils literal">#defined</tt> and <a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> is
<strong>not</strong> <tt class="docutils literal">#defined</tt>.</p>
<table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field"><th class="field-name">Defined in:</th><td class="field-body"><a class="reference external" href="../../../../boost/parameter/config.hpp">boost/parameter/config.hpp</a></td>
</tr>
<tr class="field"><th class="field-name">Default Value:</th><td class="field-body"><tt class="docutils literal">0</tt></td>
</tr>
<tr class="field"><th class="field-name">Minimum Value:</th><td class="field-body"><tt class="docutils literal">0</tt></td>
</tr>
</tbody>
</table>
</div>
<div class="section" id="outside-of-this-library">
<h2><a class="toc-backref" href="#id90">8.9&nbsp;&nbsp;&nbsp;...Outside Of This Library</a></h2>
<ol class="arabic simple">
<li>If <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a> defines the macro
<a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__03_defects"><tt class="docutils literal">BOOST_NO_FUNCTION_TEMPLATE_ORDERING</tt></a>, then the macros
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> and
<a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> will be left undefined; otherwise, the
code generation macros would not work correctly.</li>
<li>If <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a> defines the macro <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__03_defects"><tt class="docutils literal">BOOST_NO_SFINAE</tt></a>, then the macros
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> and
<a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> will be left undefined; otherwise, keyword
types generated by <a class="reference internal" href="#boost-parameter-name"><tt class="docutils literal">BOOST_PARAMETER_NAME</tt></a> and
<a class="reference internal" href="#boost-parameter-nested-keyword"><tt class="docutils literal">BOOST_PARAMETER_NESTED_KEYWORD</tt></a> would not work correctly.</li>
<li>If <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a> defines the macro
<a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_RVALUE_REFERENCES</tt></a>, then the macros
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> and
<a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> will be left undefined.</li>
<li>If <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a> defines the macro <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_VARIADIC_TEMPLATES</tt></a>,
then the macros <a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> and
<a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> will be left undefined.</li>
<li>If <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a> defines the macro
<a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_FUNCTION_TEMPLATE_DEFAULT_ARGS</tt></a>, then the macros
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a> and
<a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> will be left undefined.</li>
<li>If <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a> defines the macro <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_CONSTEXPR</tt></a>, then the
macro <a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> will be left undefined.</li>
<li>If <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a> defines the macro <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_DECLTYPE_N3276</tt></a>,
then the macro <a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> will be left undefined.</li>
<li>If <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a> defines the macro <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_AUTO_DECLARATIONS</tt></a>,
then the macro <a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> will be left undefined.</li>
<li>If <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a> defines the macro <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_TEMPLATE_ALIASES</tt></a>,
then the macro <a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> will be left undefined.</li>
<li>If <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a> defines the macro <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_STATIC_ASSERT</tt></a>, then
the macro <a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> will be left undefined.</li>
<li>If <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a> defines the macro <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_HDR_TYPE_TRAITS</tt></a>,
then the macro <a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> will be left undefined.</li>
<li>If <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a> defines the macro
<a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_HDR_INITIALIZER_LIST</tt></a>, then the macro
<a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> will be left undefined.</li>
<li>If <a class="reference external" href="../../../config/doc/html/index.html">Boost.Config</a> defines the macro <a class="reference external" href="../../../config/doc/html/boost_config/boost_macro_reference.html#boost_config.boost_macro_reference.macros_that_describe_c__11_features_not_supported"><tt class="docutils literal">BOOST_NO_CXX11_HDR_TUPLE</tt></a>, then the
macro <a class="reference internal" href="#boost-parameter-can-use-mp11"><tt class="docutils literal">BOOST_PARAMETER_CAN_USE_MP11</tt></a> will be left undefined.</li>
<li>If <a class="reference external" href="../../../fusion/doc/html/index.html">Boost.Fusion</a> defines the macro <a class="reference external" href="../../../../boost/fusion/container/list/list_fwd.hpp"><tt class="docutils literal">BOOST_FUSION_HAS_VARIADIC_LIST</tt></a>,
if this library defines the macro
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a>, and if
<a class="reference internal" href="#boost-parameter-variadic-mpl-sequence"><tt class="docutils literal">BOOST_PARAMETER_VARIADIC_MPL_SEQUENCE</tt></a> is left undefined, then the
<a class="reference external" href="../../../mpl/doc/refmanual/variadic-sequence.html">MPL Variadic Sequence</a> underlying the nested <tt class="docutils literal">parameter_spec</tt> type of
<tt class="docutils literal">parameters</tt> will be <a class="reference external" href="../../../fusion/doc/html/fusion/container/list.html"><tt class="docutils literal"><span class="pre">boost::fusion::list</span></tt></a>.</li>
<li>If <a class="reference external" href="../../../fusion/doc/html/index.html">Boost.Fusion</a> defines the macro <a class="reference external" href="../../../../boost/fusion/container/deque/deque_fwd.hpp"><tt class="docutils literal">BOOST_FUSION_HAS_VARIADIC_DEQUE</tt></a>,
if this library defines the macro
<a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a>, and if
<a class="reference internal" href="#boost-parameter-variadic-mpl-sequence"><tt class="docutils literal">BOOST_PARAMETER_VARIADIC_MPL_SEQUENCE</tt></a> is left undefined, then the
<a class="reference external" href="../../../mpl/doc/refmanual/variadic-sequence.html">MPL Variadic Sequence</a> underlying the nested <tt class="docutils literal">parameter_spec</tt> type of
<tt class="docutils literal">parameters</tt> will be <a class="reference external" href="../../../fusion/doc/html/fusion/container/deque.html"><tt class="docutils literal"><span class="pre">boost::fusion::deque</span></tt></a>.</li>
<li>The value that <a class="reference external" href="../../../mpl/doc/index.html">Boost.MPL</a> defines the macro
<a class="reference external" href="../../../mpl/doc/refmanual/limit-vector-size.html"><tt class="docutils literal">BOOST_MPL_LIMIT_VECTOR_SIZE</tt></a> as will be the value that this library
defines the macro <a class="reference internal" href="#boost-parameter-max-arity"><tt class="docutils literal">BOOST_PARAMETER_MAX_ARITY</tt></a> as if this library defines
the macro <a class="reference internal" href="#boost-parameter-has-perfect-forwarding"><tt class="docutils literal">BOOST_PARAMETER_HAS_PERFECT_FORWARDING</tt></a>.</li>
</ol>
</div>
</div>
<div class="section" id="tutorial">
<h1><a class="toc-backref" href="#id91">9&nbsp;&nbsp;&nbsp;Tutorial</a></h1>
<p>Follow <a class="reference external" href="index.html#tutorial">this link</a> to the Boost.Parameter tutorial documentation.</p>
<hr class="docutils" />
<table class="docutils footnote" frame="void" id="thread" rules="none">
<colgroup><col class="label" /><col /></colgroup>
<tbody valign="top">
<tr><td class="label"><a class="fn-backref" href="#id2">[1]</a></td><td>References to tag objects may be initialized multiple
times.  This scenario can only occur in the presence of
threading.  Because the C++ standard doesn't consider threading,
it doesn't explicitly allow or forbid multiple initialization of
references.  That said, it's hard to imagine an implementation
where it could make a difference.</td></tr>
</tbody>
</table>
<table class="docutils footnote" frame="void" id="no-result-of" rules="none">
<colgroup><col class="label" /><col /></colgroup>
<tbody valign="top">
<tr><td class="label">[2]</td><td><em>(<a class="fn-backref" href="#id6">1</a>, <a class="fn-backref" href="#id12">2</a>, <a class="fn-backref" href="#id15">3</a>)</em> Where <a class="reference external" href="../../../utility/utility.htm#BOOST_NO_RESULT_OF"><tt class="docutils literal">BOOST_NO_RESULT_OF</tt></a> is <tt class="docutils literal">#defined</tt>,
<a class="reference external" href="../../../utility/utility.htm#result_of"><tt class="docutils literal"><span class="pre">boost::result_of</span></tt></a><tt class="docutils literal"><span class="pre">&lt;F()&gt;::type</span></tt> is replaced by <tt class="docutils literal"><span class="pre">F::result_type</span></tt>.</td></tr>
</tbody>
</table>
</div>
</div>
<div class="footer">
<hr class="footer" />
Generated on: 2023-04-10 13:50 UTC.
Generated by <a class="reference external" href="http://docutils.sourceforge.net/">Docutils</a> from <a class="reference external" href="http://docutils.sourceforge.net/rst.html">reStructuredText</a> source.

</div>
</body>
</html>
