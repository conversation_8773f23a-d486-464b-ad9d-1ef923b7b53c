<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">


<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    
    <title>Decorators &#8212; Boost.HigherOrderFunctions 0.6 documentation</title>
    
    <link rel="stylesheet" href="../../_static/boostbook.css" type="text/css" />
    <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
    
    <script type="text/javascript">
      var DOCUMENTATION_OPTIONS = {
        URL_ROOT:    '../../',
        VERSION:     '0.6',
        COLLAPSE_INDEX: false,
        FILE_SUFFIX: '.html',
        HAS_SOURCE:  true,
        SOURCELINK_SUFFIX: '.txt'
      };
    </script>
    <script type="text/javascript" src="../../_static/jquery.js"></script>
    <script type="text/javascript" src="../../_static/underscore.js"></script>
    <script type="text/javascript" src="../../_static/doctools.js"></script>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="capture" href="../../include/boost/hof/capture.html" />
    <link rel="prev" title="unpack" href="../../include/boost/hof/unpack.html" /> 
  </head>
  <body role="document">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86"src="../../_static/boost.png"></td>
</tr></table>

<div class="spirit-nav">
<a accesskey="p"  href="../../include/boost/hof/unpack.html"><img src="../../_static/prev.png" alt="Prev"></a>
<a accesskey="u" href="reference.html"><img src="../../_static/up.png" alt="Up"></a>
<a accesskey="h" href="../index.html"><img src="../../_static/home.png" alt="Home"></a>
<a accesskey="n"  href="../../include/boost/hof/capture.html"><img src="../../_static/next.png" alt="Next"></a>
</div>
  

    <div class="document">
  <div class="chapter">
      <div class="body" role="main">
        
  <div class="section" id="decorators">
<h1>Decorators<a class="headerlink" href="#decorators" title="Permalink to this headline">¶</a></h1>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../include/boost/hof/capture.html">capture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../include/boost/hof/if.html">if</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../include/boost/hof/limit.html">limit</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../include/boost/hof/repeat.html">repeat</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../include/boost/hof/repeat_while.html">repeat_while</a></li>
</ul>
</div>
</div>


      </div>
  </div>
      <div class="clearer"></div>
    </div>
    <div class="footer" role="contentinfo">
    <table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
    <td align="left"></td>
    <td align="right"><div class="copyright-footer">
            &#169; Copyright 2016, Paul Fultz II.
        
          Created using <a href="http://sphinx-doc.org/">Sphinx</a> 1.5.6.
          <p>Distributed under the Boost Software License, Version 1.0.
          (See accompanying file <code class="filename">LICENSE_1_0.txt</code> or copy at 
          <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
          </p>
    </div></td>
    </tr></table>
    </div>
  </body>
</html>