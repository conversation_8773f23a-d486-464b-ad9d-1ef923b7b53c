<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template dense_output_runge_kutta</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Numeric.Odeint">
<link rel="up" href="../../../header/boost/numeric/odeint/stepper/dense_output_runge_kutta_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/dense_output_runge_kutta.hpp&gt;">
<link rel="prev" href="../../../header/boost/numeric/odeint/stepper/dense_output_runge_kutta_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/dense_output_runge_kutta.hpp&gt;">
<link rel="next" href="dense_output_rung_idm27540.html" title="Class template dense_output_runge_kutta&lt;Stepper, explicit_controlled_stepper_fsal_tag&gt;">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../logo.jpg"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../../header/boost/numeric/odeint/stepper/dense_output_runge_kutta_hpp.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/stepper/dense_output_runge_kutta_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="dense_output_rung_idm27540.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.numeric.odeint.dense_output_runge_kutta"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template dense_output_runge_kutta</span></h2>
<p>boost::numeric::odeint::dense_output_runge_kutta</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../header/boost/numeric/odeint/stepper/dense_output_runge_kutta_hpp.html" title="Header &lt;boost/numeric/odeint/stepper/dense_output_runge_kutta.hpp&gt;">boost/numeric/odeint/stepper/dense_output_runge_kutta.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Stepper<span class="special">,</span> 
         <span class="keyword">typename</span> StepperCategory <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">Stepper</span><span class="special">::</span><span class="identifier">stepper_category</span><span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="dense_output_runge_kutta.html" title="Class template dense_output_runge_kutta">dense_output_runge_kutta</a> <span class="special">{</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="idm40403"></a><h2>Description</h2>
<div class="refsect2">
<a name="idm40405"></a><h3>Specializations</h3>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem"><p><a class="link" href="dense_output_rung_idm27540.html" title="Class template dense_output_runge_kutta&lt;Stepper, explicit_controlled_stepper_fsal_tag&gt;">Class template dense_output_runge_kutta&lt;Stepper, explicit_controlled_stepper_fsal_tag&gt;</a></p></li>
<li class="listitem"><p><a class="link" href="dense_output_rung_idm27685.html" title="Class template dense_output_runge_kutta&lt;Stepper, stepper_tag&gt;">Class template dense_output_runge_kutta&lt;Stepper, stepper_tag&gt;</a></p></li>
</ul></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2009-2015 Karsten Ahnert and Mario Mulansky<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../../header/boost/numeric/odeint/stepper/dense_output_runge_kutta_hpp.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../header/boost/numeric/odeint/stepper/dense_output_runge_kutta_hpp.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="dense_output_rung_idm27540.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
