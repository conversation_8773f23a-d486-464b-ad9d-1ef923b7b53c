<?xml version="1.0" standalone="yes"?>
<library-reference id="odeint_reference"><title>odeint Reference</title><header name="boost/numeric/odeint/integrate/check_adapter.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="checked_observer"><template>
      <template-type-parameter name="Observer"/>
      <template-type-parameter name="Checker"/>
    </template><purpose>Adapter to combine observer and checker. </purpose><typedef name="observer_type"><type>Observer</type></typedef>
<typedef name="checker_type"><type>Checker</type></typedef>
<method-group name="public member functions">
<method name="operator()" cv="const"><type>void</type><template>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
        </template><parameter name="state"><paramtype>const State &amp;</paramtype></parameter><parameter name="t"><paramtype>Time</paramtype></parameter></method>
</method-group>
<constructor><parameter name="observer"><paramtype>observer_type &amp;</paramtype></parameter><parameter name="checker"><paramtype>checker_type &amp;</paramtype></parameter></constructor>
</class><class name="checked_stepper"><template>
      <template-type-parameter name="Stepper"/>
      <template-type-parameter name="Checker"/>
      <template-type-parameter name="StepperCategory"><default>typename base_tag&lt;typename Stepper::stepper_category&gt;::type</default></template-type-parameter>
    </template></class><class-specialization name="checked_stepper"><template>
      <template-type-parameter name="ControlledStepper"/>
      <template-type-parameter name="Checker"/>
    </template><specialization><template-arg>ControlledStepper</template-arg><template-arg>Checker</template-arg><template-arg>controlled_stepper_tag</template-arg></specialization><purpose>Adapter to combine controlled stepper and checker. </purpose><typedef name="stepper_type"><type>ControlledStepper</type></typedef>
<typedef name="checker_type"><type>Checker</type></typedef>
<typedef name="state_type"><type>stepper_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_type::time_type</type></typedef>
<method-group name="public member functions">
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="state"><paramtype>StateInOut &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype></parameter><purpose>forward of the do_step method </purpose></method>
</method-group>
<constructor><parameter name="stepper"><paramtype>stepper_type &amp;</paramtype></parameter><parameter name="checker"><paramtype>checker_type &amp;</paramtype></parameter><purpose>Construct the checked_stepper. </purpose></constructor>
</class-specialization><class-specialization name="checked_stepper"><template>
      <template-type-parameter name="DenseOutStepper"/>
      <template-type-parameter name="Checker"/>
    </template><specialization><template-arg>DenseOutStepper</template-arg><template-arg>Checker</template-arg><template-arg>dense_output_stepper_tag</template-arg></specialization><purpose>Adapter to combine dense out stepper and checker. </purpose><typedef name="stepper_type"><type>DenseOutStepper</type></typedef>
<typedef name="checker_type"><type>Checker</type></typedef>
<typedef name="state_type"><type>stepper_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_type::time_type</type></typedef>
<method-group name="public member functions">
<method name="do_step"><type>std::pair&lt; time_type, time_type &gt;</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter></method>
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x0"><paramtype>const StateType &amp;</paramtype></parameter><parameter name="t0"><paramtype>time_type</paramtype></parameter><parameter name="dt0"><paramtype>time_type</paramtype></parameter></method>
<method name="calc_state" cv="const"><type>void</type><template>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="x"><paramtype>StateOut &amp;</paramtype></parameter></method>
<method name="calc_state" cv="const"><type>void</type><template>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="x"><paramtype>const StateOut &amp;</paramtype></parameter></method>
<method name="current_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="current_time" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="previous_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="previous_time" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="current_time_step" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter></method>
</method-group>
<constructor><parameter name="stepper"><paramtype>stepper_type &amp;</paramtype></parameter><parameter name="checker"><paramtype>checker_type &amp;</paramtype></parameter><purpose>Construct the checked_stepper. </purpose></constructor>
</class-specialization><class-specialization name="checked_stepper"><template>
      <template-type-parameter name="Stepper"/>
      <template-type-parameter name="Checker"/>
    </template><specialization><template-arg>Stepper</template-arg><template-arg>Checker</template-arg><template-arg>stepper_tag</template-arg></specialization><purpose>Adapter to combine basic stepper and checker. </purpose><typedef name="stepper_type"><type>Stepper</type></typedef>
<typedef name="checker_type"><type>Checker</type></typedef>
<typedef name="state_type"><type>stepper_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_type::time_type</type></typedef>
<method-group name="public member functions">
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="state"><paramtype>StateInOut &amp;</paramtype></parameter><parameter name="t"><paramtype>const time_type</paramtype></parameter><parameter name="dt"><paramtype>const time_type</paramtype></parameter><purpose>forward of the do_step method </purpose></method>
</method-group>
<constructor><parameter name="stepper"><paramtype>stepper_type &amp;</paramtype></parameter><parameter name="checker"><paramtype>checker_type &amp;</paramtype></parameter><purpose>Construct the checked_stepper. </purpose></constructor>
</class-specialization>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/integrate/integrate.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
























<function name="integrate"><type>boost::enable_if&lt; typename has_value_type&lt; State &gt;::type, size_t &gt;::type</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ordinary differential equation. </para></description></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype><description><para>The initial state. </para></description></parameter><parameter name="start_time"><paramtype>Time</paramtype><description><para>Start time of the integration. </para></description></parameter><parameter name="end_time"><paramtype>Time</paramtype><description><para>End time of the integration. </para></description></parameter><parameter name="dt"><paramtype>Time</paramtype><description><para>Initial step size, will be adjusted during the integration. </para></description></parameter><parameter name="observer"><paramtype>Observer</paramtype><description><para>Observer that will be called after each time step. </para></description></parameter><purpose>Integrates the ODE. </purpose><description><para>Integrates the ODE given by system from start_time to end_time starting with start_state as initial condition and dt as initial time step. This function uses a dense output dopri5 stepper and performs an adaptive integration with step size control, thus dt changes during the integration. This method uses standard error bounds of 1E-6. After each step, the observer is called.</para><para><note><para>A second version of this function template exists which explicitly expects the value type as template parameter, i.e. integrate&lt; double &gt;( sys , x , t0 , t1 , dt , obs );</para>
</note>


</para></description><returns><para>The number of steps performed. </para>
</returns></function>
<function name="integrate"><type>size_t</type><template>
          <template-type-parameter name="Value"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="end_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter></function>
<function name="integrate"><type>size_t</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ordinary differential equation. </para></description></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype><description><para>The initial state. </para></description></parameter><parameter name="start_time"><paramtype>Time</paramtype><description><para>Start time of the integration. </para></description></parameter><parameter name="end_time"><paramtype>Time</paramtype><description><para>End time of the integration. </para></description></parameter><parameter name="dt"><paramtype>Time</paramtype><description><para>Initial step size, will be adjusted during the integration. </para></description></parameter><purpose>Integrates the ODE without observer calls. </purpose><description><para>Integrates the ODE given by system from start_time to end_time starting with start_state as initial condition and dt as initial time step. This function uses a dense output dopri5 stepper and performs an adaptive integration with step size control, thus dt changes during the integration. This method uses standard error bounds of 1E-6. No observer is called.</para><para><note><para>A second version of this function template exists which explicitly expects the value type as template parameter, i.e. integrate&lt; double &gt;( sys , x , t0 , t1 , dt );</para>
</note>


</para></description><returns><para>The number of steps performed. </para>
</returns></function>
<function name="integrate"><type>size_t</type><template>
          <template-type-parameter name="Value"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="end_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter></function>
























</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/integrate/integrate_adaptive.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">




















<function name="integrate_adaptive"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to be used for numerical integration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>Function/Functor defining the rhs of the ODE. </para></description></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype><description><para>The initial condition x0. </para></description></parameter><parameter name="start_time"><paramtype>Time</paramtype><description><para>The initial time t0. </para></description></parameter><parameter name="end_time"><paramtype>Time</paramtype><description><para>The final integration time tend. </para></description></parameter><parameter name="dt"><paramtype>Time</paramtype><description><para>The time step between observer calls, <emphasis>not</emphasis> necessarily the time step of the integration. </para></description></parameter><parameter name="observer"><paramtype>Observer</paramtype><description><para>Function/Functor called at equidistant time intervals. </para></description></parameter><purpose>Integrates the ODE with adaptive step size. </purpose><description><para>This function integrates the ODE given by system with the given stepper. The observer is called after each step. If the stepper has no error control, the step size remains constant and the observer is called at equidistant time points t0+n*dt. If the stepper is a ControlledStepper, the step size is adjusted and the observer is called in non-equidistant intervals.</para><para>

</para></description><returns><para>The number of steps performed. </para>
</returns></function>
<function name="integrate_adaptive"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>const State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="end_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><purpose>Second version to solve the forwarding problem, can be called with Boost.Range as start_state. </purpose></function>
<function name="integrate_adaptive"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="end_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><purpose>integrate_adaptive without an observer. </purpose></function>
<function name="integrate_adaptive"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>const State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="end_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><purpose>Second version to solve the forwarding problem, can be called with Boost.Range as start_state. </purpose></function>




























</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/integrate/integrate_const.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">














<function name="integrate_const"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
          <template-type-parameter name="StepOverflowChecker"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to be used for numerical integration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>Function/Functor defining the rhs of the ODE. </para></description></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype><description><para>The initial condition x0. </para></description></parameter><parameter name="start_time"><paramtype>Time</paramtype><description><para>The initial time t0. </para></description></parameter><parameter name="end_time"><paramtype>Time</paramtype><description><para>The final integration time tend. </para></description></parameter><parameter name="dt"><paramtype>Time</paramtype><description><para>The time step between observer calls, <emphasis>not</emphasis> necessarily the time step of the integration. </para></description></parameter><parameter name="observer"><paramtype>Observer</paramtype><description><para>[optional] Function/Functor called at equidistant time intervals. </para></description></parameter><parameter name="checker"><paramtype>StepOverflowChecker</paramtype><description><para>[optional] Functor to check for step count overflows, if no checker is provided, no exception is thrown. </para></description></parameter><purpose>Integrates the ODE with constant step size. </purpose><description><para>Integrates the ODE defined by system using the given stepper. This method ensures that the observer is called at constant intervals dt. If the Stepper is a normal stepper without step size control, dt is also used for the numerical scheme. If a ControlledStepper is provided, the algorithm might reduce the step size to meet the error bounds, but it is ensured that the observer is always called at equidistant time points t0 + n*dt. If a DenseOutputStepper is used, the step size also may vary and the dense output is used to call the observer at equidistant time points. If a <classname alt="boost::numeric::odeint::max_step_checker">max_step_checker</classname> is provided as StepOverflowChecker, a no_progress_error is thrown if too many steps (default: 500) are performed without progress, i.e. in between observer calls. If no checker is provided, no such overflow check is performed.</para><para>

</para></description><returns><para>The number of steps performed. </para>
</returns></function>
<function name="integrate_const"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
          <template-type-parameter name="StepOverflowChecker"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>const State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="end_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><parameter name="checker"><paramtype>StepOverflowChecker</paramtype></parameter><purpose>Second version to solve the forwarding problem, can be called with Boost.Range as start_state. </purpose></function>
<function name="integrate_const"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="end_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><purpose>integrate_const without step overflow checker </purpose></function>
<function name="integrate_const"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>const State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="end_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><purpose>Second version to solve the forwarding problem, can be called with Boost.Range as start_state. </purpose></function>
<function name="integrate_const"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="end_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><purpose>integrate_const without observer calls </purpose></function>
<function name="integrate_const"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>const State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="end_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><purpose>Second version to solve the forwarding problem, can be called with Boost.Range as start_state. </purpose></function>
































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/integrate/integrate_n_steps.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">








<function name="integrate_n_steps"><type>Time</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
          <template-type-parameter name="StepOverflowChecker"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="num_of_steps"><paramtype>size_t</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><parameter name="checker"><paramtype>StepOverflowChecker</paramtype></parameter></function>
<function name="integrate_n_steps"><type>Time</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
          <template-type-parameter name="StepOverflowChecker"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>const State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="num_of_steps"><paramtype>size_t</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><parameter name="checker"><paramtype>StepOverflowChecker</paramtype></parameter><purpose>Solves the forwarding problem, can be called with Boost.Range as start_state. </purpose></function>
<function name="integrate_n_steps"><type>Time</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to be used for numerical integration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>Function/Functor defining the rhs of the ODE. </para></description></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype><description><para>The initial condition x0. </para></description></parameter><parameter name="start_time"><paramtype>Time</paramtype><description><para>The initial time t0. </para></description></parameter><parameter name="dt"><paramtype>Time</paramtype><description><para>The time step between observer calls, <emphasis>not</emphasis> necessarily the time step of the integration. </para></description></parameter><parameter name="num_of_steps"><paramtype>size_t</paramtype><description><para>Number of steps to be performed </para></description></parameter><parameter name="observer"><paramtype>Observer</paramtype><description><para>Function/Functor called at equidistant time intervals. </para></description></parameter><purpose>The same function as above, but without checker. </purpose><description><para>Integrates the ODE with constant step size.</para><para>This function is similar to integrate_const. The observer is called at equidistant time intervals t0 + n*dt. If the Stepper is a normal stepper without step size control, dt is also used for the numerical scheme. If a ControlledStepper is provided, the algorithm might reduce the step size to meet the error bounds, but it is ensured that the observer is always called at equidistant time points t0 + n*dt. If a DenseOutputStepper is used, the step size also may vary and the dense output is used to call the observer at equidistant time points. The final integration time is always t0 + num_of_steps*dt. If a <classname alt="boost::numeric::odeint::max_step_checker">max_step_checker</classname> is provided as StepOverflowChecker, a no_progress_errror is thrown if too many steps (default: 500) are performed without progress, i.e. in between observer calls. If no checker is provided, no such overflow check is performed.</para><para>

</para></description><returns><para>The number of steps performed. </para>
</returns></function>
<function name="integrate_n_steps"><type>Time</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>const State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="num_of_steps"><paramtype>size_t</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><purpose>Solves the forwarding problem, can be called with Boost.Range as start_state. </purpose></function>
<function name="integrate_n_steps"><type>Time</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="num_of_steps"><paramtype>size_t</paramtype></parameter><purpose>The same function as above, but without observer calls. </purpose></function>
<function name="integrate_n_steps"><type>Time</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Time"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>const State &amp;</paramtype></parameter><parameter name="start_time"><paramtype>Time</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="num_of_steps"><paramtype>size_t</paramtype></parameter><purpose>Solves the forwarding problem, can be called with Boost.Range as start_state. </purpose></function>






































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/integrate/integrate_times.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<function name="integrate_times"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="TimeIterator"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
          <template-type-parameter name="StepOverflowChecker"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype></parameter><parameter name="times_start"><paramtype>TimeIterator</paramtype></parameter><parameter name="times_end"><paramtype>TimeIterator</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><parameter name="checker"><paramtype>StepOverflowChecker</paramtype></parameter></function>
<function name="integrate_times"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="TimeIterator"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
          <template-type-parameter name="StepOverflowChecker"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>const State &amp;</paramtype></parameter><parameter name="times_start"><paramtype>TimeIterator</paramtype></parameter><parameter name="times_end"><paramtype>TimeIterator</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><parameter name="checker"><paramtype>StepOverflowChecker</paramtype></parameter><purpose>Solves the forwarding problem, can be called with Boost.Range as start_state. </purpose></function>
<function name="integrate_times"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="TimeRange"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
          <template-type-parameter name="StepOverflowChecker"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype></parameter><parameter name="times"><paramtype>const TimeRange &amp;</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><parameter name="checker"><paramtype>StepOverflowChecker</paramtype></parameter><purpose>The same function as above, but with the observation times given as range. </purpose></function>
<function name="integrate_times"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="TimeRange"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
          <template-type-parameter name="StepOverflowChecker"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>const State &amp;</paramtype></parameter><parameter name="times"><paramtype>const TimeRange &amp;</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><parameter name="checker"><paramtype>StepOverflowChecker</paramtype></parameter><purpose>Solves the forwarding problem, can be called with Boost.Range as start_state. </purpose></function>
<function name="integrate_times"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="TimeIterator"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to be used for numerical integration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>Function/Functor defining the rhs of the ODE. </para></description></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype><description><para>The initial condition x0. </para></description></parameter><parameter name="times_start"><paramtype>TimeIterator</paramtype><description><para>Iterator to the start time </para></description></parameter><parameter name="times_end"><paramtype>TimeIterator</paramtype><description><para>Iterator to the end time </para></description></parameter><parameter name="dt"><paramtype>Time</paramtype><description><para>The time step between observer calls, <emphasis>not</emphasis> necessarily the time step of the integration. </para></description></parameter><parameter name="observer"><paramtype>Observer</paramtype><description><para>Function/Functor called at equidistant time intervals. </para></description></parameter><purpose>Integrates the ODE with observer calls at given time points. </purpose><description><para>Integrates the ODE given by system using the given stepper. This function does observer calls at the subsequent time points given by the range times_start, times_end. If the stepper has not step size control, the step size might be reduced occasionally to ensure observer calls exactly at the time points from the given sequence. If the stepper is a ControlledStepper, the step size is adjusted to meet the error bounds, but also might be reduced occasionally to ensure correct observer calls. If a DenseOutputStepper is provided, the dense output functionality is used to call the observer at the given times. The end time of the integration is always *(end_time-1). If a <classname alt="boost::numeric::odeint::max_step_checker">max_step_checker</classname> is provided as StepOverflowChecker, a no_progress_error is thrown if too many steps (default: 500) are performed without progress, i.e. in between observer calls. If no checker is provided, no such overflow check is performed.</para><para>

</para></description><returns><para>The number of steps performed. </para>
</returns></function>
<function name="integrate_times"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="TimeIterator"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>const State &amp;</paramtype></parameter><parameter name="times_start"><paramtype>TimeIterator</paramtype></parameter><parameter name="times_end"><paramtype>TimeIterator</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><purpose>Solves the forwarding problem, can be called with Boost.Range as start_state. </purpose></function>
<function name="integrate_times"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="TimeRange"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>State &amp;</paramtype></parameter><parameter name="times"><paramtype>const TimeRange &amp;</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><purpose>The same function as above, but with the observation times given as range. </purpose></function>
<function name="integrate_times"><type>size_t</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="TimeRange"/>
          <template-type-parameter name="Time"/>
          <template-type-parameter name="Observer"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="start_state"><paramtype>const State &amp;</paramtype></parameter><parameter name="times"><paramtype>const TimeRange &amp;</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter><parameter name="observer"><paramtype>Observer</paramtype></parameter><purpose>Solves the forwarding problem, can be called with Boost.Range as start_state. </purpose></function>












































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/integrate/max_step_checker.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="failed_step_checker"><inherit access="public">boost::numeric::odeint::max_step_checker</inherit><purpose>A class for performing overflow checks on the failed step count in step size adjustments. </purpose><description><para>Used internally within the dense output stepper and integrate routines. </para></description><method-group name="public member functions">
<method name="operator()"><type>void</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Increases the counter and performs the iteration check. </purpose></method>
<method name="reset"><type>void</type><purpose>Resets the <classname alt="boost::numeric::odeint::max_step_checker">max_step_checker</classname> by setting the internal counter to 0. </purpose></method>
</method-group>
<constructor><parameter name="max_steps"><paramtype>const int</paramtype><default>500</default></parameter><purpose>Construct the <classname alt="boost::numeric::odeint::failed_step_checker">failed_step_checker</classname>. max_steps is the maximal number of iterations allowed before runtime_error is thrown. </purpose></constructor>
</class><class name="max_step_checker"><purpose>A class for performing overflow checks on the step count in integrate functions. </purpose><description><para>Provide an instance of this class to integrate functions if you want to throw a runtime error if too many steps are performed without progress during the integrate routine. </para></description><method-group name="public member functions">
<method name="reset"><type>void</type><purpose>Resets the <classname alt="boost::numeric::odeint::max_step_checker">max_step_checker</classname> by setting the internal counter to 0. </purpose></method>
<method name="operator()"><type>void</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Increases the counter and performs the iteration check. </purpose></method>
</method-group>
<constructor><parameter name="max_steps"><paramtype>const int</paramtype><default>500</default></parameter><purpose>Construct the <classname alt="boost::numeric::odeint::max_step_checker">max_step_checker</classname>. max_steps is the maximal number of iterations allowed before runtime_error is thrown. </purpose></constructor>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/iterator/adaptive_iterator.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="adaptive_iterator"><template>
      <template-type-parameter name="Stepper"><purpose><para>The stepper type which should be used during the iteration. </para></purpose></template-type-parameter>
      <template-type-parameter name="System"><purpose><para>The type of the system function (ODE) which should be solved. </para></purpose></template-type-parameter>
      <template-type-parameter name="State"><purpose><para>The state type of the ODE. </para></purpose></template-type-parameter>
    </template><purpose>ODE Iterator with adaptive step size. The value type of this iterator is the state type of the stepper. </purpose><description><para>Implements an iterator representing the solution of an ODE from t_start to t_end evaluated at steps with an adaptive step size dt. After each iteration the iterator dereferences to the state x at the next time t+dt where dt is controlled by the stepper. This iterator can be used with ControlledSteppers and DenseOutputSteppers and it always makes use of the all the given steppers capabilities. A for_each over such an iterator range behaves similar to the integrate_adaptive routine.</para><para><classname alt="boost::numeric::odeint::adaptive_iterator">adaptive_iterator</classname> is a model of single-pass iterator.</para><para>The value type of this iterator is the state type of the stepper. Hence one can only access the state and not the current time.</para><para>
</para></description><method-group name="public member functions">
</method-group>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter><parameter name="t_start"><paramtype>time_type</paramtype></parameter><parameter name="t_end"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></constructor>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter></constructor>
</class>
















































<function name="make_adaptive_iterator_begin"><type><classname>adaptive_iterator</classname>&lt; Stepper, System, State &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. </para></description></parameter><parameter name="t_start"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time. </para></description></parameter><parameter name="t_end"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The end time, at which the iteration should stop. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::adaptive_iterator">adaptive_iterator</classname>. Constructs a begin iterator. </purpose><description><para>

</para></description><returns><para>The adaptive iterator. </para>
</returns></function>
<function name="make_adaptive_iterator_end"><type><classname>adaptive_iterator</classname>&lt; Stepper, System, State &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::adaptive_iterator">adaptive_iterator</classname>. Constructs a end iterator. </purpose><description><para>

</para></description><returns><para>The adaptive iterator. </para>
</returns></function>
<function name="make_adaptive_range"><type>std::pair&lt; <classname>adaptive_iterator</classname>&lt; Stepper, System, State &gt;, <classname>adaptive_iterator</classname>&lt; Stepper, System, State &gt; &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. </para></description></parameter><parameter name="t_start"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time. </para></description></parameter><parameter name="t_end"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The end time, at which the iteration should stop. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><purpose>Factory function to construct a single pass range of adaptive iterators. A range is here a pair of <classname alt="boost::numeric::odeint::adaptive_iterator">adaptive_iterator</classname>. </purpose><description><para>

</para></description><returns><para>The adaptive range. </para>
</returns></function>
</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/iterator/adaptive_time_iterator.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="adaptive_time_iterator"><template>
      <template-type-parameter name="Stepper"><purpose><para>The stepper type which should be used during the iteration. </para></purpose></template-type-parameter>
      <template-type-parameter name="System"><purpose><para>The type of the system function (ODE) which should be solved. </para></purpose></template-type-parameter>
      <template-type-parameter name="State"><purpose><para>The state type of the ODE. </para></purpose></template-type-parameter>
    </template><purpose>ODE Iterator with adaptive step size. The value type of this iterator is a std::pair containing state and time. </purpose><description><para>Implements an iterator representing the solution of an ODE from t_start to t_end evaluated at steps with an adaptive step size dt. After each iteration the iterator dereferences to a pair containing state and time at the next time point t+dt where dt is controlled by the stepper. This iterator can be used with ControlledSteppers and DenseOutputSteppers and it always makes use of the all the given steppers capabilities. A for_each over such an iterator range behaves similar to the integrate_adaptive routine.</para><para><classname alt="boost::numeric::odeint::adaptive_iterator">adaptive_iterator</classname> is a model of single-pass iterator.</para><para>The value type of this iterator is a std::pair of state and time of the stepper.</para><para>
</para></description><method-group name="public member functions">
</method-group>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter><parameter name="t_start"><paramtype>time_type</paramtype></parameter><parameter name="t_end"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></constructor>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter></constructor>
</class>













































<function name="make_adaptive_time_iterator_begin"><type><classname>adaptive_time_iterator</classname>&lt; Stepper, System, State &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::adaptive_time_iterator">adaptive_time_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t_start"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time. </para></description></parameter><parameter name="t_end"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The end time, at which the iteration should stop. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::adaptive_time_iterator">adaptive_time_iterator</classname>. Constructs a begin iterator. </purpose><description><para>

</para></description><returns><para>The adaptive time iterator. </para>
</returns></function>
<function name="make_adaptive_time_iterator_end"><type><classname>adaptive_time_iterator</classname>&lt; Stepper, System, State &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::adaptive_time_iterator">adaptive_time_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::adaptive_time_iterator">adaptive_time_iterator</classname>. Constructs a end iterator. </purpose><description><para>

</para></description><returns><para>The adaptive time iterator. </para>
</returns></function>
<function name="make_adaptive_time_range"><type>std::pair&lt; <classname>adaptive_time_iterator</classname>&lt; Stepper, System, State &gt;, <classname>adaptive_time_iterator</classname>&lt; Stepper, System, State &gt; &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::adaptive_time_iterator">adaptive_time_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t_start"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time. </para></description></parameter><parameter name="t_end"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The end time, at which the iteration should stop. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><purpose>Factory function to construct a single pass range of adaptive time iterators. A range is here a pair of adaptive_time_iterators. </purpose><description><para>

</para></description><returns><para>The adaptive time range. </para>
</returns></function>



</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/iterator/const_step_iterator.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="const_step_iterator"><template>
      <template-type-parameter name="Stepper"><purpose><para>The stepper type which should be used during the iteration. </para></purpose></template-type-parameter>
      <template-type-parameter name="System"><purpose><para>The type of the system function (ODE) which should be solved. </para></purpose></template-type-parameter>
      <template-type-parameter name="State"><purpose><para>The state type of the ODE. </para></purpose></template-type-parameter>
    </template><purpose>ODE Iterator with constant step size. The value type of this iterator is the state type of the stepper. </purpose><description><para>Implements an iterator representing the solution of an ODE from t_start to t_end evaluated at steps with constant step size dt. After each iteration the iterator dereferences to the state x at the next time t+dt. This iterator can be used with Steppers and DenseOutputSteppers and it always makes use of the all the given steppers capabilities. A for_each over such an iterator range behaves similar to the integrate_const routine.</para><para><classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> is a model of single-pass iterator.</para><para>The value type of this iterator is the state type of the stepper. Hence one can only access the state and not the current time.</para><para>
</para></description><method-group name="public member functions">
</method-group>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter><parameter name="t_start"><paramtype>time_type</paramtype></parameter><parameter name="t_end"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></constructor>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter></constructor>
</class>










































<function name="make_const_step_iterator_begin"><type><classname>const_step_iterator</classname>&lt; Stepper, System, State &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t_start"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time. </para></description></parameter><parameter name="t_end"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The end time, at which the iteration should stop. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname>. Constructs a begin iterator. </purpose><description><para>

</para></description><returns><para>The const step iterator. </para>
</returns></function>
<function name="make_const_step_iterator_end"><type><classname>const_step_iterator</classname>&lt; Stepper, System, State &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname>. Constructs a end iterator. </purpose><description><para>

</para></description><returns><para>The <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname>. </para>
</returns></function>
<function name="make_const_step_range"><type>std::pair&lt; <classname>const_step_iterator</classname>&lt; Stepper, System, State &gt;, <classname>const_step_iterator</classname>&lt; Stepper, System, State &gt; &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> store a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t_start"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time. </para></description></parameter><parameter name="t_end"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The end time, at which the iteration should stop. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><purpose>Factory function to construct a single pass range of const step iterators. A range is here a pair of <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname>. </purpose><description><para>

</para></description><returns><para>The const step range. </para>
</returns></function>






</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/iterator/const_step_time_iterator.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="const_step_time_iterator"><template>
      <template-type-parameter name="Stepper"><purpose><para>The stepper type which should be used during the iteration. </para></purpose></template-type-parameter>
      <template-type-parameter name="System"><purpose><para>The type of the system function (ODE) which should be solved. </para></purpose></template-type-parameter>
      <template-type-parameter name="State"><purpose><para>The state type of the ODE. </para></purpose></template-type-parameter>
    </template><purpose>ODE Iterator with constant step size. The value type of this iterator is a std::pair containing state and time. </purpose><description><para>Implements an iterator representing the solution of an ODE from t_start to t_end evaluated at steps with constant step size dt. After each iteration the iterator dereferences to a pair containing state and time at the next time point t+dt.. This iterator can be used with Steppers and DenseOutputSteppers and it always makes use of the all the given steppers capabilities. A for_each over such an iterator range behaves similar to the integrate_const routine.</para><para><classname alt="boost::numeric::odeint::const_step_time_iterator">const_step_time_iterator</classname> is a model of single-pass iterator.</para><para>The value type of this iterator is a pair with the state type and time type of the stepper.</para><para>
</para></description><method-group name="public member functions">
</method-group>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter><parameter name="t_start"><paramtype>time_type</paramtype></parameter><parameter name="t_end"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></constructor>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter></constructor>
</class>







































<function name="make_const_step_time_iterator_begin"><type><classname>const_step_time_iterator</classname>&lt; Stepper, System, State &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_time_iterator">const_step_time_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t_start"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time. </para></description></parameter><parameter name="t_end"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The end time, at which the iteration should stop. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::const_step_time_iterator">const_step_time_iterator</classname>. Constructs a begin iterator. </purpose><description><para>

</para></description><returns><para>The const step time iterator. </para>
</returns></function>
<function name="make_const_step_time_iterator_end"><type><classname>const_step_time_iterator</classname>&lt; Stepper, System, State &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_time_iterator">const_step_time_iterator</classname> store a reference of s and changes its value during the iteration. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::const_step_time_iterator">const_step_time_iterator</classname>. Constructs a end iterator. </purpose><description><para>

</para></description><returns><para>The const step time iterator. </para>
</returns></function>
<function name="make_const_step_time_range"><type>std::pair&lt; <classname>const_step_time_iterator</classname>&lt; Stepper, System, State &gt;, <classname>const_step_time_iterator</classname>&lt; Stepper, System, State &gt; &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_time_iterator">const_step_time_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t_start"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype></parameter><parameter name="t_end"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The end time, at which the iteration should stop. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><purpose>Factory function to construct a single pass range of <classname alt="boost::numeric::odeint::const_step_time_iterator">const_step_time_iterator</classname>. A range is here a pair of <classname alt="boost::numeric::odeint::const_step_time_iterator">const_step_time_iterator</classname>. </purpose><description><para>

</para></description><returns><para>The const step time range. </para>
</returns></function>









</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/iterator/n_step_iterator.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="n_step_iterator"><template>
      <template-type-parameter name="Stepper"><purpose><para>The stepper type which should be used during the iteration. </para></purpose></template-type-parameter>
      <template-type-parameter name="System"><purpose><para>The type of the system function (ODE) which should be solved. </para></purpose></template-type-parameter>
      <template-type-parameter name="State"><purpose><para>The state type of the ODE. </para></purpose></template-type-parameter>
    </template><purpose>ODE Iterator with constant step size. The value type of this iterator is the state type of the stepper. </purpose><description><para>Implements an iterator representing the solution of an ODE starting from t with n steps and a constant step size dt. After each iteration the iterator dereferences to the state x at the next time t+dt. This iterator can be used with Steppers and DenseOutputSteppers and it always makes use of the all the given steppers capabilities. A for_each over such an iterator range behaves similar to the integrate_n_steps routine.</para><para><classname alt="boost::numeric::odeint::n_step_iterator">n_step_iterator</classname> is a model of single-pass iterator.</para><para>The value type of this iterator is the state type of the stepper. Hence one can only access the state and not the current time.</para><para>
</para></description><method-group name="public member functions">
</method-group>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="num_of_steps"><paramtype>size_t</paramtype></parameter></constructor>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter></constructor>
</class>




































<function name="make_n_step_iterator_begin"><type><classname>n_step_iterator</classname>&lt; Stepper, System, State &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><parameter name="num_of_steps"><paramtype>size_t</paramtype><description><para>The number of steps to be executed. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::n_step_iterator">n_step_iterator</classname>. Constructs a begin iterator. </purpose><description><para>

</para></description><returns><para>The n-step iterator. </para>
</returns></function>
<function name="make_n_step_iterator_end"><type><classname>n_step_iterator</classname>&lt; Stepper, System, State &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::n_step_iterator">n_step_iterator</classname>. Constructs an end iterator. </purpose><description><para>

</para></description><returns><para>The <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname>. </para>
</returns></function>
<function name="make_n_step_range"><type>std::pair&lt; <classname>n_step_iterator</classname>&lt; Stepper, System, State &gt;, <classname>n_step_iterator</classname>&lt; Stepper, System, State &gt; &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> store a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><parameter name="num_of_steps"><paramtype>size_t</paramtype><description><para>The number of steps to be executed. </para></description></parameter><purpose>Factory function to construct a single pass range of n-step iterators. A range is here a pair of <classname alt="boost::numeric::odeint::n_step_iterator">n_step_iterator</classname>. </purpose><description><para>

</para></description><returns><para>The n-step range. </para>
</returns></function>












</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/iterator/n_step_time_iterator.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="n_step_time_iterator"><template>
      <template-type-parameter name="Stepper"><purpose><para>The stepper type which should be used during the iteration. </para></purpose></template-type-parameter>
      <template-type-parameter name="System"><purpose><para>The type of the system function (ODE) which should be solved. </para></purpose></template-type-parameter>
      <template-type-parameter name="State"><purpose><para>The state type of the ODE. </para></purpose></template-type-parameter>
    </template><purpose>ODE Iterator with constant step size. The value type of this iterator is a std::pair containing state and time. </purpose><description><para>Implements an iterator representing the solution of an ODE starting from t with n steps and a constant step size dt. After each iteration the iterator dereferences to a pair of state and time at the next time t+dt. This iterator can be used with Steppers and DenseOutputSteppers and it always makes use of the all the given steppers capabilities. A for_each over such an iterator range behaves similar to the integrate_n_steps routine.</para><para><classname alt="boost::numeric::odeint::n_step_time_iterator">n_step_time_iterator</classname> is a model of single-pass iterator.</para><para>The value type of this iterator is pair of state and time.</para><para>
</para></description><method-group name="public member functions">
</method-group>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="num_of_steps"><paramtype>size_t</paramtype></parameter></constructor>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter></constructor>
</class>

































<function name="make_n_step_time_iterator_begin"><type><classname>n_step_time_iterator</classname>&lt; Stepper, System, State &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><parameter name="num_of_steps"><paramtype>size_t</paramtype><description><para>The number of steps to be executed. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::n_step_time_iterator">n_step_time_iterator</classname>. Constructs a begin iterator. </purpose><description><para>

</para></description><returns><para>The n-step iterator. </para>
</returns></function>
<function name="make_n_step_time_iterator_end"><type><classname>n_step_time_iterator</classname>&lt; Stepper, System, State &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::n_step_time_iterator">n_step_time_iterator</classname>. Constructs an end iterator. </purpose><description><para>

</para></description><returns><para>The <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname>. </para>
</returns></function>
<function name="make_n_step_time_range"><type>std::pair&lt; <classname>n_step_time_iterator</classname>&lt; Stepper, System, State &gt;, <classname>n_step_time_iterator</classname>&lt; Stepper, System, State &gt; &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> store a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><parameter name="num_of_steps"><paramtype>size_t</paramtype><description><para>The number of steps to be executed. </para></description></parameter><purpose>Factory function to construct a single pass range of n-step iterators. A range is here a pair of <classname alt="boost::numeric::odeint::n_step_time_iterator">n_step_time_iterator</classname>. </purpose><description><para>

</para></description><returns><para>The n-step range. </para>
</returns></function>















</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/iterator/times_iterator.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="times_iterator"><template>
      <template-type-parameter name="Stepper"><purpose><para>The stepper type which should be used during the iteration. </para></purpose></template-type-parameter>
      <template-type-parameter name="System"><purpose><para>The type of the system function (ODE) which should be solved. </para></purpose></template-type-parameter>
      <template-type-parameter name="State"><purpose><para>The state type of the ODE. </para></purpose></template-type-parameter>
      <template-type-parameter name="TimeIterator"><purpose><para>The iterator type for the sequence of time points. </para></purpose></template-type-parameter>
    </template><purpose>ODE Iterator with given evaluation points. The value type of this iterator is the state type of the stepper. </purpose><description><para>Implements an iterator representing the solution of an ODE from *t_start to *t_end evaluated at time points given by the sequence t_start to t_end. t_start and t_end are iterators representing a sequence of time points where the solution of the ODE should be evaluated. After each iteration the iterator dereferences to the state x at the next time *t_start++ until t_end is reached. This iterator can be used with Steppers, ControlledSteppers and DenseOutputSteppers and it always makes use of the all the given steppers capabilities. A for_each over such an iterator range behaves similar to the integrate_times routine.</para><para><classname alt="boost::numeric::odeint::times_iterator">times_iterator</classname> is a model of single-pass iterator.</para><para>The value type of this iterator is the state type of the stepper. Hence one can only access the state and not the current time.</para><para>
</para></description><method-group name="public member functions">
</method-group>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter><parameter name="t_start"><paramtype>TimeIterator</paramtype></parameter><parameter name="t_end"><paramtype>TimeIterator</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></constructor>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter></constructor>
</class>






























<function name="make_times_iterator_begin"><type><classname>times_iterator</classname>&lt; Stepper, System, State, TimeIterator &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="TimeIterator"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t_start"><paramtype>TimeIterator</paramtype><description><para>Begin iterator of the sequence of evaluation time points. </para></description></parameter><parameter name="t_end"><paramtype>TimeIterator</paramtype><description><para>End iterator of the sequence of evaluation time points. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::times_iterator">times_iterator</classname>. Constructs a begin iterator. </purpose><description><para>

</para></description><returns><para>The times iterator. </para>
</returns></function>
<function name="make_times_iterator_end"><type><classname>times_iterator</classname>&lt; Stepper, System, State, TimeIterator &gt;</type><template>
          <template-type-parameter name="TimeIterator"/>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::times_iterator">times_iterator</classname>. Constructs an end iterator. </purpose><description><para>


This function needs the TimeIterator type specifically defined as a template parameter. </para></description><returns><para>The times iterator.</para>
</returns></function>
<function name="make_times_range"><type>std::pair&lt; <classname>times_iterator</classname>&lt; Stepper, System, State, TimeIterator &gt;, <classname>times_iterator</classname>&lt; Stepper, System, State, TimeIterator &gt; &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="TimeIterator"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> store a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t_start"><paramtype>TimeIterator</paramtype><description><para>Begin iterator of the sequence of evaluation time points. </para></description></parameter><parameter name="t_end"><paramtype>TimeIterator</paramtype><description><para>End iterator of the sequence of evaluation time points. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><purpose>Factory function to construct a single pass range of times iterators. A range is here a pair of <classname alt="boost::numeric::odeint::times_iterator">times_iterator</classname>. </purpose><description><para>

</para></description><returns><para>The times iterator range. </para>
</returns></function>


















</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/iterator/times_time_iterator.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="times_time_iterator"><template>
      <template-type-parameter name="Stepper"><purpose><para>The stepper type which should be used during the iteration. </para></purpose></template-type-parameter>
      <template-type-parameter name="System"><purpose><para>The type of the system function (ODE) which should be solved. </para></purpose></template-type-parameter>
      <template-type-parameter name="State"><purpose><para>The state type of the ODE. </para></purpose></template-type-parameter>
      <template-type-parameter name="TimeIterator"><purpose><para>The iterator type for the sequence of time points. </para></purpose></template-type-parameter>
    </template><purpose>ODE Iterator with given evaluation points. The value type of this iterator is a std::pair containing state and time. </purpose><description><para>Implements an iterator representing the solution of an ODE from *t_start to *t_end evaluated at time points given by the sequence t_start to t_end. t_start and t_end are iterators representing a sequence of time points where the solution of the ODE should be evaluated. After each iteration the iterator dereferences to a pair with the state and the time at the next evaluation point *t_start++ until t_end is reached. This iterator can be used with Steppers, ControlledSteppers and DenseOutputSteppers and it always makes use of the all the given steppers capabilities. A for_each over such an iterator range behaves similar to the integrate_times routine.</para><para><classname alt="boost::numeric::odeint::times_time_iterator">times_time_iterator</classname> is a model of single-pass iterator.</para><para>The value type of this iterator is a pair of state and time type.</para><para>
</para></description><method-group name="public member functions">
</method-group>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter><parameter name="t_start"><paramtype>TimeIterator</paramtype></parameter><parameter name="t_end"><paramtype>TimeIterator</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></constructor>
<constructor><parameter name="stepper"><paramtype>Stepper</paramtype></parameter><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="s"><paramtype>State &amp;</paramtype></parameter></constructor>
</class>



























<function name="make_times_time_iterator_begin"><type><classname>times_time_iterator</classname>&lt; Stepper, System, State, TimeIterator &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="TimeIterator"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t_start"><paramtype>TimeIterator</paramtype><description><para>Begin iterator of the sequence of evaluation time points. </para></description></parameter><parameter name="t_end"><paramtype>TimeIterator</paramtype><description><para>End iterator of the sequence of evaluation time points. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::times_time_iterator">times_time_iterator</classname>. Constructs a begin iterator. </purpose><description><para>

</para></description><returns><para>The times_time iterator. </para>
</returns></function>
<function name="make_times_time_iterator_end"><type><classname>times_time_iterator</classname>&lt; Stepper, System, State, TimeIterator &gt;</type><template>
          <template-type-parameter name="TimeIterator"/>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> stores a reference of s and changes its value during the iteration. </para></description></parameter><purpose>Factory function for <classname alt="boost::numeric::odeint::times_time_iterator">times_time_iterator</classname>. Constructs an end iterator. </purpose><description><para>


This function needs the TimeIterator type specifically defined as a template parameter. </para></description><returns><para>The times_time iterator.</para>
</returns></function>
<function name="make_times_time_range"><type>std::pair&lt; <classname>times_time_iterator</classname>&lt; Stepper, System, State, TimeIterator &gt;, <classname>times_time_iterator</classname>&lt; Stepper, System, State, TimeIterator &gt; &gt;</type><template>
          <template-type-parameter name="Stepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="State"/>
          <template-type-parameter name="TimeIterator"/>
        </template><parameter name="stepper"><paramtype>Stepper</paramtype><description><para>The stepper to use during the iteration. </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function (ODE) to solve. </para></description></parameter><parameter name="x"><paramtype>State &amp;</paramtype><description><para>The initial state. <classname alt="boost::numeric::odeint::const_step_iterator">const_step_iterator</classname> store a reference of s and changes its value during the iteration. </para></description></parameter><parameter name="t_start"><paramtype>TimeIterator</paramtype><description><para>Begin iterator of the sequence of evaluation time points. </para></description></parameter><parameter name="t_end"><paramtype>TimeIterator</paramtype><description><para>End iterator of the sequence of evaluation time points. </para></description></parameter><parameter name="dt"><paramtype>typename traits::time_type&lt; Stepper &gt;::type</paramtype><description><para>The initial time step. </para></description></parameter><purpose>Factory function to construct a single pass range of times_time iterators. A range is here a pair of <classname alt="boost::numeric::odeint::times_iterator">times_iterator</classname>. </purpose><description><para>

</para></description><returns><para>The times_time iterator range. </para>
</returns></function>





















</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/adams_bashforth.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="adams_bashforth"><template>
      <template-nontype-parameter name="Steps"><type>size_t</type><purpose><para>The number of steps (maximal 8). </para></purpose></template-nontype-parameter>
      <template-type-parameter name="State"><purpose><para>The state type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The value type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default><purpose><para>The type representing the time derivative of the state. </para></purpose></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The time representing the independent variable - the time. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
      <template-type-parameter name="InitializingStepper"><default><classname alt="boost::numeric::odeint::extrapolation_stepper">extrapolation_stepper</classname>&lt; order_helper&lt;Steps&gt;::value,                                                    State, Value, Deriv, Time,                                                   Algebra, Operations, Resizer &gt;</default><purpose><para>The stepper for the first two steps. </para></purpose></template-type-parameter>
    </template><inherit access="public">algebra_stepper_base&lt; Algebra, Operations &gt;</inherit><purpose>The Adams-Bashforth multistep algorithm. </purpose><description><para>The Adams-Bashforth method is a multi-step algorithm with configurable step number. The step number is specified as template parameter Steps and it then uses the result from the previous Steps steps. See also <ulink url="http://en.wikipedia.org/wiki/Linear_multistep_method">en.wikipedia.org/wiki/Linear_multistep_method</ulink>. Currently, a maximum of Steps=8 is supported. The method is explicit and fulfills the Stepper concept. Step size control or continuous output are not provided.</para><para>This class derives from algebra_base and inherits its interface via CRTP (current recurring template pattern). For more details see algebra_stepper_base.</para><para>
</para></description><typedef name="state_type"><type>State</type></typedef>
<typedef name="wrapped_state_type"><type>state_wrapper&lt; state_type &gt;</type></typedef>
<typedef name="value_type"><type>Value</type></typedef>
<typedef name="deriv_type"><type>Deriv</type></typedef>
<typedef name="wrapped_deriv_type"><type>state_wrapper&lt; deriv_type &gt;</type></typedef>
<typedef name="time_type"><type>Time</type></typedef>
<typedef name="resizer_type"><type>Resizer</type></typedef>
<typedef name="stepper_category"><type>stepper_tag</type></typedef>
<typedef name="initializing_stepper_type"><type>InitializingStepper</type></typedef>
<typedef name="algebra_stepper_base_type"><type>algebra_stepper_base&lt; Algebra, Operations &gt;</type></typedef>
<typedef name="algebra_type"><type>algebra_stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>algebra_stepper_base_type::operations_type</type></typedef>
<typedef name="order_type"><type>unsigned short</type></typedef>
<typedef name="step_storage_type"><type><emphasis>unspecified</emphasis></type></typedef>
<data-member name="steps" specifiers="static"><type>const size_t</type></data-member>
<data-member name="order_value" specifiers="static"><type>const order_type</type></data-member>
<method-group name="public member functions">
<method name="order" cv="const"><type>order_type</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the order of the algorithm, which is equal to the number of steps. </purpose><description><para>
</para></description><returns><para>order of the method. </para>
</returns></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ordinary differential equation. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype><description><para>The state of the ODE which should be solved. After calling do_step the result is updated in x. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. It transforms the result in-place. </purpose><description><para>
</para></description></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>const StateInOut &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><purpose>Second version to solve the forwarding problem, can be called with Boost.Range as StateInOut. </purpose></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>The method performs one step with the stepper passed by Stepper. The state of the ODE is updated out-of-place. </purpose><description><para>
</para></description></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>const StateOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><purpose>Second version to solve the forwarding problem, can be called with Boost.Range as StateOut. </purpose></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
<method name="step_storage" cv="const"><type>const step_storage_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the storage of intermediate results. </purpose><description><para>
</para></description><returns><para>The storage of intermediate results. </para>
</returns></method>
<method name="step_storage"><type>step_storage_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the storage of intermediate results. </purpose><description><para>
</para></description><returns><para>The storage of intermediate results. </para>
</returns></method>
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="ExplicitStepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="explicit_stepper"><paramtype>ExplicitStepper</paramtype><description><para>the stepper used to fill the buffer of previous step results </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ordinary differential equation. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. After calling do_step the result is updated in x. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>Initialized the stepper. Does Steps-1 steps with the explicit_stepper to fill the buffer. </purpose><description><para>
</para></description></method>
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ordinary differential equation. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>StateIn &amp;</paramtype><description><para>The initial state of the ODE which should be solved, updated in this method. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The initial value of the time, updated in this method. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>Initialized the stepper. Does Steps-1 steps with an internal instance of InitializingStepper to fill the buffer. </purpose><description><para><note><para>The state x and time t are updated to the values after Steps-1 initial steps. </para>
</note>

</para></description></method>
<method name="reset"><type>void</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Resets the internal buffer of the stepper. </purpose></method>
<method name="is_initialized" cv="const"><type>bool</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns true if the stepper has been initialized. </purpose><description><para>
</para></description><returns><para>bool true if stepper is initialized, false otherwise </para>
</returns></method>
<method name="initializing_stepper" cv="const"><type>const initializing_stepper_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the internal initializing stepper instance. </purpose><description><para>
</para></description><returns><para>initializing_stepper </para>
</returns></method>
<method name="initializing_stepper"><type>initializing_stepper_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the internal initializing stepper instance. </purpose><description><para>
</para></description><returns><para>initializing_stepper </para>
</returns></method>
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored. </para></description></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::adams_bashforth">adams_bashforth</classname> class. This constructor can be used as a default constructor if the algebra has a default constructor. </purpose><description><para>
</para></description></constructor>
<method-group name="private member functions">
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class><struct name="order_helper"><template>
      <template-nontype-parameter name="N"><type>int</type></template-nontype-parameter>
    </template><inherit access="public">boost::mpl::max::type&lt; mpl::eval_if&lt; mpl::equal_to&lt; mpl::modulus&lt; int_&lt; N &gt;, int_&lt; 2 &gt; &gt;, int_&lt; 0 &gt; &gt;, int_&lt; N &gt;, int_&lt; N+1 &gt; &gt;::type, int_&lt; 4 &gt; &gt;</inherit></struct>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/adams_bashforth_moulton.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="adams_bashforth_moulton"><template>
      <template-nontype-parameter name="Steps"><type>size_t</type><purpose><para>The number of steps (maximal 8). </para></purpose></template-nontype-parameter>
      <template-type-parameter name="State"><purpose><para>The state type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The value type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default><purpose><para>The type representing the time derivative of the state. </para></purpose></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The time representing the independent variable - the time. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
      <template-type-parameter name="InitializingStepper"><default><classname alt="boost::numeric::odeint::runge_kutta4">runge_kutta4</classname>&lt; State , Value , Deriv , Time , Algebra , Operations, Resizer &gt;</default><purpose><para>The stepper for the first two steps. </para></purpose></template-type-parameter>
    </template><purpose>The Adams-Bashforth-Moulton multistep algorithm. </purpose><description><para>The Adams-Bashforth method is a multi-step predictor-corrector algorithm with configurable step number. The step number is specified as template parameter Steps and it then uses the result from the previous Steps steps. See also <ulink url="http://en.wikipedia.org/wiki/Linear_multistep_method">en.wikipedia.org/wiki/Linear_multistep_method</ulink>. Currently, a maximum of Steps=8 is supported. The method is explicit and fulfills the Stepper concept. Step size control or continuous output are not provided.</para><para>This class derives from algebra_base and inherits its interface via CRTP (current recurring template pattern). For more details see algebra_stepper_base.</para><para>
</para></description><typedef name="state_type"><type>State</type></typedef>
<typedef name="wrapped_state_type"><type>state_wrapper&lt; state_type &gt;</type></typedef>
<typedef name="value_type"><type>Value</type></typedef>
<typedef name="deriv_type"><type>Deriv</type></typedef>
<typedef name="wrapped_deriv_type"><type>state_wrapper&lt; deriv_type &gt;</type></typedef>
<typedef name="time_type"><type>Time</type></typedef>
<typedef name="algebra_type"><type>Algebra</type></typedef>
<typedef name="operations_type"><type>Operations</type></typedef>
<typedef name="resizer_type"><type>Resizer</type></typedef>
<typedef name="stepper_category"><type>stepper_tag</type></typedef>
<typedef name="initializing_stepper_type"><type>InitializingStepper</type></typedef>
<typedef name="order_type"><type>unsigned short</type></typedef>
<data-member name="steps" specifiers="static"><type>const size_t</type></data-member>
<data-member name="order_value" specifiers="static"><type>const order_type</type></data-member>
<method-group name="public member functions">
<method name="order" cv="const"><type>order_type</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the order of the algorithm, which is equal to the number of steps+1. </purpose><description><para>
</para></description><returns><para>order of the method. </para>
</returns></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ordinary differential equation. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype><description><para>The state of the ODE which should be solved. After calling do_step the result is updated in x. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. It transforms the result in-place. </purpose><description><para>
</para></description></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>const StateInOut &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><purpose>Second version to solve the forwarding problem, can be called with Boost.Range as StateInOut. </purpose></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>const StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>The method performs one step with the stepper passed by Stepper. The state of the ODE is updated out-of-place. </purpose><description><para>
</para></description></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><purpose>Second version to solve the forwarding problem, can be called with Boost.Range as StateOut. </purpose></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="ExplicitStepper"/>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="explicit_stepper"><paramtype>ExplicitStepper</paramtype><description><para>the stepper used to fill the buffer of previous step results </para></description></parameter><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ordinary differential equation. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>StateIn &amp;</paramtype><description><para>The initial state of the ODE which should be solved, updated after in this method. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The initial time, updated in this method. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>Initialized the stepper. Does Steps-1 steps with the explicit_stepper to fill the buffer. </purpose><description><para><note><para>The state x and time t are updated to the values after Steps-1 initial steps. </para>
</note>

</para></description></method>
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ordinary differential equation. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. After calling do_step the result is updated in x. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>Initialized the stepper. Does Steps-1 steps using the standard initializing stepper of the underlying <classname alt="boost::numeric::odeint::adams_bashforth">adams_bashforth</classname> stepper. </purpose><description><para>
</para></description></method>
<method name="reset"><type>void</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Resets the internal buffers of the stepper. </purpose></method>
</method-group>
<constructor><parameter name=""><paramtype>void</paramtype></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::adams_bashforth">adams_bashforth</classname> class. </purpose></constructor>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><description><para>A copy of algebra is made and stored. </para></description></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::adams_bashforth">adams_bashforth</classname> class. This constructor can be used as a default constructor if the algebra has a default constructor. </purpose><description><para>
</para></description></constructor>
<method-group name="private member functions">
<method name="do_step_impl1"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="do_step_impl2"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>StateIn const &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>StateInOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/adams_moulton.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="adams_moulton"><template>
      <template-nontype-parameter name="Steps"><type>size_t</type></template-nontype-parameter>
      <template-type-parameter name="State"/>
      <template-type-parameter name="Value"><default>double</default></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default></template-type-parameter>
    </template><typedef name="state_type"><type>State</type></typedef>
<typedef name="wrapped_state_type"><type>state_wrapper&lt; state_type &gt;</type></typedef>
<typedef name="value_type"><type>Value</type></typedef>
<typedef name="deriv_type"><type>Deriv</type></typedef>
<typedef name="wrapped_deriv_type"><type>state_wrapper&lt; deriv_type &gt;</type></typedef>
<typedef name="time_type"><type>Time</type></typedef>
<typedef name="algebra_type"><type>Algebra</type></typedef>
<typedef name="operations_type"><type>Operations</type></typedef>
<typedef name="resizer_type"><type>Resizer</type></typedef>
<typedef name="stepper_category"><type>stepper_tag</type></typedef>
<typedef name="stepper_type"><type>adams_moulton&lt; Steps, State, Value, Deriv, Time, Algebra, Operations, Resizer &gt;</type></typedef>
<typedef name="order_type"><type>unsigned short</type></typedef>
<typedef name="step_storage_type"><type><emphasis>unspecified</emphasis></type></typedef>
<data-member name="steps" specifiers="static"><type>const size_t</type></data-member>
<data-member name="order_value" specifiers="static"><type>const order_type</type></data-member>
<method-group name="public member functions">
<method name="order" cv="const"><type>order_type</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="ABBuf"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype></parameter><parameter name="pred"><paramtype>StateIn const &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="buf"><paramtype>const ABBuf &amp;</paramtype></parameter></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="ABBuf"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>const StateInOut &amp;</paramtype></parameter><parameter name="pred"><paramtype>StateIn const &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="buf"><paramtype>const ABBuf &amp;</paramtype></parameter></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="PredIn"/>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="ABBuf"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype></parameter><parameter name="pred"><paramtype>const PredIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="buf"><paramtype>const ABBuf &amp;</paramtype></parameter></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="PredIn"/>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="ABBuf"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype></parameter><parameter name="pred"><paramtype>const PredIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>const StateOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="buf"><paramtype>const ABBuf &amp;</paramtype></parameter></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype></parameter></method>
<method name="algebra"><type>algebra_type &amp;</type></method>
<method name="algebra" cv="const"><type>const algebra_type &amp;</type></method>
</method-group>
<constructor/>
<constructor><parameter name="algebra"><paramtype>algebra_type &amp;</paramtype></parameter></constructor>
<copy-assignment><type>adams_moulton &amp;</type><parameter name="stepper"><paramtype>const adams_moulton &amp;</paramtype></parameter></copy-assignment>
<method-group name="private member functions">
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="PredIn"/>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="ABBuf"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype></parameter><parameter name="pred"><paramtype>const PredIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="buf"><paramtype>const ABBuf &amp;</paramtype></parameter></method>
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/adaptive_adams_bashforth_moulton.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="adaptive_adams_bashforth_moulton"><template>
      <template-nontype-parameter name="Steps"><type>size_t</type></template-nontype-parameter>
      <template-type-parameter name="State"/>
      <template-type-parameter name="Value"><default>double</default></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default></template-type-parameter>
    </template><inherit access="public">algebra_stepper_base&lt; Algebra, Operations &gt;</inherit><typedef name="order_type"><type>unsigned short</type></typedef>
<typedef name="state_type"><type>State</type></typedef>
<typedef name="value_type"><type>Value</type></typedef>
<typedef name="deriv_type"><type>Deriv</type></typedef>
<typedef name="time_type"><type>Time</type></typedef>
<typedef name="wrapped_state_type"><type>state_wrapper&lt; state_type &gt;</type></typedef>
<typedef name="wrapped_deriv_type"><type>state_wrapper&lt; deriv_type &gt;</type></typedef>
<typedef name="algebra_stepper_base_type"><type>algebra_stepper_base&lt; Algebra, Operations &gt;</type></typedef>
<typedef name="algebra_type"><type>algebra_stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>algebra_stepper_base_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>Resizer</type></typedef>
<typedef name="stepper_category"><type>error_stepper_tag</type></typedef>
<typedef name="coeff_type"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="stepper_type"><type>adaptive_adams_bashforth_moulton&lt; Steps, State, Value, Deriv, Time, Algebra, Operations, Resizer &gt;</type></typedef>
<data-member name="steps" specifiers="static"><type>const size_t</type></data-member>
<data-member name="order_value" specifiers="static"><type>const order_type</type></data-member>
<method-group name="public member functions">
<method name="order" cv="const"><type>order_type</type></method>
<method name="stepper_order" cv="const"><type>order_type</type></method>
<method name="error_order" cv="const"><type>order_type</type></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="inOut"><paramtype>state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>state_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="inOut"><paramtype>state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="xerr"><paramtype>state_type &amp;</paramtype></parameter></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>state_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="xerr"><paramtype>state_type &amp;</paramtype></parameter></method>
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="ExplicitStepper"/>
          <template-type-parameter name="System"/>
        </template><parameter name="stepper"><paramtype>ExplicitStepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="inOut"><paramtype>state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="inOut"><paramtype>state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>state_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype></parameter><parameter name="xerr"><paramtype>state_type &amp;</paramtype></parameter></method>
<method name="coeff" cv="const"><type>const coeff_type &amp;</type></method>
<method name="coeff"><type>coeff_type &amp;</type></method>
<method name="reset"><type>void</type></method>
<method name="dxdt" cv="const"><type>const deriv_type &amp;</type></method>
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default></parameter></constructor>
<method-group name="private member functions">
<method name="resize_dxdt_impl"><type>bool</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype></parameter></method>
<method name="resize_xnew_impl"><type>bool</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype></parameter></method>
<method name="resize_xerr_impl"><type>bool</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/bulirsch_stoer.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="bulirsch_stoer"><template>
      <template-type-parameter name="State"><purpose><para>The state type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The value type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default><purpose><para>The type representing the time derivative of the state. </para></purpose></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The time representing the independent variable - the time. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><purpose>The Bulirsch-Stoer algorithm. </purpose><description><para>The Bulirsch-Stoer is a controlled stepper that adjusts both step size and order of the method. The algorithm uses the modified midpoint and a polynomial extrapolation compute the solution.</para><para>
</para></description><typedef name="state_type"><type>State</type></typedef>
<typedef name="value_type"><type>Value</type></typedef>
<typedef name="deriv_type"><type>Deriv</type></typedef>
<typedef name="time_type"><type>Time</type></typedef>
<typedef name="algebra_type"><type>Algebra</type></typedef>
<typedef name="operations_type"><type>Operations</type></typedef>
<typedef name="resizer_type"><type>Resizer</type></typedef>
<data-member name="m_k_max" specifiers="static"><type>const size_t</type></data-member>
<method-group name="public member functions">
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype><description><para>The state of the ODE which should be solved. Overwritten if the step is successful. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. </purpose><description><para>This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed. Also, the internal order of the stepper is adjusted if required.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>const StateInOut &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype></parameter><purpose>Second version to solve the forwarding problem, can be used with Boost.Range as StateInOut. </purpose></method>
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
          <template-type-parameter name="DerivIn"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype><description><para>The state of the ODE which should be solved. Overwritten if the step is successful. </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of state. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. </purpose><description><para>This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed. Also, the internal order of the stepper is adjusted if required.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="try_step"><type>boost::disable_if&lt; boost::is_same&lt; StateIn, time_type &gt;, controlled_step_result &gt;::type</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>Used to store the result of the step. </para></description></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. </purpose><description><para><note><para>This method is disabled if state_type=time_type to avoid ambiguity.</para>
</note>
This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed. Also, the internal order of the stepper is adjusted if required.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of state. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>Used to store the result of the step. </para></description></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. </purpose><description><para>This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed. Also, the internal order of the stepper is adjusted if required.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="reset"><type>void</type><purpose>Resets the internal state of the stepper. </purpose></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
</method-group>
<constructor><parameter name="eps_abs"><paramtype>value_type</paramtype><default>1E-6</default><description><para>Absolute tolerance level. </para></description></parameter><parameter name="eps_rel"><paramtype>value_type</paramtype><default>1E-6</default><description><para>Relative tolerance level. </para></description></parameter><parameter name="factor_x"><paramtype>value_type</paramtype><default>1.0</default><description><para>Factor for the weight of the state. </para></description></parameter><parameter name="factor_dxdt"><paramtype>value_type</paramtype><default>1.0</default><description><para>Factor for the weight of the derivative. </para></description></parameter><parameter name="max_dt"><paramtype>time_type</paramtype><default>static_cast&lt; time_type &gt;(0)</default></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::bulirsch_stoer">bulirsch_stoer</classname> class, including initialization of the error bounds. </purpose><description><para>
</para></description></constructor>
<method-group name="private member functions">
<method name="resize_m_dxdt"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="resize_m_xnew"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="try_step_v1"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype></parameter></method>
<method name="extrapolate"><type>void</type><template>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="k"><paramtype>size_t</paramtype></parameter><parameter name="table"><paramtype>state_table_type &amp;</paramtype></parameter><parameter name="coeff"><paramtype>const value_matrix &amp;</paramtype></parameter><parameter name="xest"><paramtype>StateInOut &amp;</paramtype></parameter></method>
<method name="calc_h_opt" cv="const"><type>time_type</type><parameter name="h"><paramtype>time_type</paramtype></parameter><parameter name="error"><paramtype>value_type</paramtype></parameter><parameter name="k"><paramtype>size_t</paramtype></parameter></method>
<method name="set_k_opt"><type>controlled_step_result</type><parameter name="k"><paramtype>size_t</paramtype></parameter><parameter name="work"><paramtype>const inv_time_vector &amp;</paramtype></parameter><parameter name="h_opt"><paramtype>const time_vector &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype></parameter></method>
<method name="in_convergence_window" cv="const"><type>bool</type><parameter name="k"><paramtype>size_t</paramtype></parameter></method>
<method name="should_reject" cv="const"><type>bool</type><parameter name="error"><paramtype>value_type</paramtype></parameter><parameter name="k"><paramtype>size_t</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/bulirsch_stoer_dense_out.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="bulirsch_stoer_dense_out"><template>
      <template-type-parameter name="State"><purpose><para>The state type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The value type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default><purpose><para>The type representing the time derivative of the state. </para></purpose></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The time representing the independent variable - the time. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><purpose>The Bulirsch-Stoer algorithm. </purpose><description><para>The Bulirsch-Stoer is a controlled stepper that adjusts both step size and order of the method. The algorithm uses the modified midpoint and a polynomial extrapolation compute the solution. This class also provides dense output facility.</para><para>
</para></description><typedef name="state_type"><type>State</type></typedef>
<typedef name="value_type"><type>Value</type></typedef>
<typedef name="deriv_type"><type>Deriv</type></typedef>
<typedef name="time_type"><type>Time</type></typedef>
<typedef name="algebra_type"><type>Algebra</type></typedef>
<typedef name="operations_type"><type>Operations</type></typedef>
<typedef name="resizer_type"><type>Resizer</type></typedef>
<typedef name="stepper_category"><type>dense_output_stepper_tag</type></typedef>
<data-member name="m_k_max" specifiers="static"><type>const size_t</type></data-member>
<method-group name="public member functions">
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="DerivOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of state. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>Used to store the result of the step. </para></description></parameter><parameter name="dxdt_new"><paramtype>DerivOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. </purpose><description><para>This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed. Also, the internal order of the stepper is adjusted if required.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x0"><paramtype>const StateType &amp;</paramtype><description><para>The initial state. </para></description></parameter><parameter name="t0"><paramtype>const time_type &amp;</paramtype><description><para>The initial time. </para></description></parameter><parameter name="dt0"><paramtype>const time_type &amp;</paramtype><description><para>The initial time step. </para></description></parameter><purpose>Initializes the dense output stepper. </purpose><description><para>
</para></description></method>
<method name="do_step"><type>std::pair&lt; time_type, time_type &gt;</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ordinary differential equation. It must fulfill the Simple System concept. </para></description></parameter><purpose>Does one time step. This is the main method that should be used to integrate an ODE with this stepper. </purpose><description><para><note><para>initialize has to be called before using this method to set the initial conditions x,t and the stepsize. </para>
</note>


</para></description><returns><para>Pair with start and end time of the integration step. </para>
</returns></method>
<method name="calc_state" cv="const"><type>void</type><template>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="t"><paramtype>time_type</paramtype><description><para>The time at which the solution should be calculated, has to be in the current time interval. </para></description></parameter><parameter name="x"><paramtype>StateOut &amp;</paramtype><description><para>The output variable where the result is written into. </para></description></parameter><purpose>Calculates the solution at an intermediate point within the last step. </purpose><description><para>
</para></description></method>
<method name="current_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the current state of the solution. </purpose><description><para>
</para></description><returns><para>The current state of the solution x(t). </para>
</returns></method>
<method name="current_time" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the current time of the solution. </purpose><description><para>
</para></description><returns><para>The current time of the solution t. </para>
</returns></method>
<method name="previous_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the last state of the solution. </purpose><description><para>
</para></description><returns><para>The last state of the solution x(t-dt). </para>
</returns></method>
<method name="previous_time" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the last time of the solution. </purpose><description><para>
</para></description><returns><para>The last time of the solution t-dt. </para>
</returns></method>
<method name="current_time_step" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the current step size. </purpose><description><para>
</para></description><returns><para>The current step size. </para>
</returns></method>
<method name="reset"><type>void</type><purpose>Resets the internal state of the stepper. </purpose></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
</method-group>
<constructor><parameter name="eps_abs"><paramtype>value_type</paramtype><default>1E-6</default><description><para>Absolute tolerance level. </para></description></parameter><parameter name="eps_rel"><paramtype>value_type</paramtype><default>1E-6</default><description><para>Relative tolerance level. </para></description></parameter><parameter name="factor_x"><paramtype>value_type</paramtype><default>1.0</default><description><para>Factor for the weight of the state. </para></description></parameter><parameter name="factor_dxdt"><paramtype>value_type</paramtype><default>1.0</default><description><para>Factor for the weight of the derivative. </para></description></parameter><parameter name="max_dt"><paramtype>time_type</paramtype><default>static_cast&lt; time_type &gt;(0)</default></parameter><parameter name="control_interpolation"><paramtype>bool</paramtype><default>false</default><description><para>Set true to additionally control the error of the interpolation. </para></description></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::bulirsch_stoer">bulirsch_stoer</classname> class, including initialization of the error bounds. </purpose><description><para>
</para></description></constructor>
<method-group name="private member functions">
<method name="extrapolate"><type>void</type><template>
          <template-type-parameter name="StateInOut"/>
          <template-type-parameter name="StateVector"/>
        </template><parameter name="k"><paramtype>size_t</paramtype></parameter><parameter name="table"><paramtype>StateVector &amp;</paramtype></parameter><parameter name="coeff"><paramtype>const value_matrix &amp;</paramtype></parameter><parameter name="xest"><paramtype>StateInOut &amp;</paramtype></parameter><parameter name="order_start_index"><paramtype>size_t</paramtype><default>0</default></parameter></method>
<method name="extrapolate_dense_out"><type>void</type><template>
          <template-type-parameter name="StateVector"/>
        </template><parameter name="k"><paramtype>size_t</paramtype></parameter><parameter name="table"><paramtype>StateVector &amp;</paramtype></parameter><parameter name="coeff"><paramtype>const value_matrix &amp;</paramtype></parameter><parameter name="order_start_index"><paramtype>size_t</paramtype><default>0</default></parameter></method>
<method name="calc_h_opt" cv="const"><type>time_type</type><parameter name="h"><paramtype>time_type</paramtype></parameter><parameter name="error"><paramtype>value_type</paramtype></parameter><parameter name="k"><paramtype>size_t</paramtype></parameter></method>
<method name="in_convergence_window" cv="const"><type>bool</type><parameter name="k"><paramtype>size_t</paramtype></parameter></method>
<method name="should_reject" cv="const"><type>bool</type><parameter name="error"><paramtype>value_type</paramtype></parameter><parameter name="k"><paramtype>size_t</paramtype></parameter></method>
<method name="prepare_dense_output"><type>value_type</type><template>
          <template-type-parameter name="StateIn1"/>
          <template-type-parameter name="DerivIn1"/>
          <template-type-parameter name="StateIn2"/>
          <template-type-parameter name="DerivIn2"/>
        </template><parameter name="k"><paramtype>int</paramtype></parameter><parameter name="x_start"><paramtype>const StateIn1 &amp;</paramtype></parameter><parameter name="dxdt_start"><paramtype>const DerivIn1 &amp;</paramtype></parameter><parameter name=""><paramtype>const StateIn2 &amp;</paramtype></parameter><parameter name=""><paramtype>const DerivIn2 &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="calculate_finite_difference"><type>void</type><template>
          <template-type-parameter name="DerivIn"/>
        </template><parameter name="j"><paramtype>size_t</paramtype></parameter><parameter name="kappa"><paramtype>size_t</paramtype></parameter><parameter name="fac"><paramtype>value_type</paramtype></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype></parameter></method>
<method name="do_interpolation" cv="const"><type>void</type><template>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype></parameter></method>
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="get_current_state"><type>state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_current_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_state"><type>state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_current_deriv"><type>deriv_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_current_deriv" cv="const"><type>const deriv_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_deriv"><type>deriv_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_deriv" cv="const"><type>const deriv_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="toggle_current_state"><type>void</type><parameter name=""><paramtype>void</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/controlled_adams_bashforth_moulton.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="controlled_adams_bashforth_moulton"><template>
      <template-type-parameter name="ErrorStepper"/>
      <template-type-parameter name="StepAdjuster"><default>detail::pid_step_adjuster&lt; typename ErrorStepper::state_type,     typename ErrorStepper::value_type,    typename ErrorStepper::deriv_type,    typename ErrorStepper::time_type,    typename ErrorStepper::algebra_type,    typename ErrorStepper::operations_type,    detail::H211PI    &gt;</default></template-type-parameter>
      <template-type-parameter name="OrderAdjuster"><default>default_order_adjuster&lt; ErrorStepper::order_value,    typename ErrorStepper::state_type,    typename ErrorStepper::value_type,    typename ErrorStepper::algebra_type    &gt;</default></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default></template-type-parameter>
    </template><typedef name="stepper_type"><type>ErrorStepper</type></typedef>
<typedef name="state_type"><type>stepper_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>Resizer</type></typedef>
<typedef name="step_adjuster_type"><type>StepAdjuster</type></typedef>
<typedef name="order_adjuster_type"><type>OrderAdjuster</type></typedef>
<typedef name="stepper_category"><type>controlled_stepper_tag</type></typedef>
<typedef name="wrapped_state_type"><type>stepper_type::wrapped_state_type</type></typedef>
<typedef name="wrapped_deriv_type"><type>stepper_type::wrapped_deriv_type</type></typedef>
<typedef name="error_storage_type"><type>boost::array&lt; wrapped_state_type, 4 &gt;</type></typedef>
<typedef name="coeff_type"><type>stepper_type::coeff_type</type></typedef>
<typedef name="controlled_stepper_type"><type>controlled_adams_bashforth_moulton&lt; ErrorStepper, StepAdjuster, OrderAdjuster, Resizer &gt;</type></typedef>
<data-member name="order_value" specifiers="static"><type>const stepper_type::order_type</type></data-member>
<method-group name="public member functions">
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="ExplicitStepper"/>
          <template-type-parameter name="System"/>
        </template><parameter name="stepper"><paramtype>ExplicitStepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="inOut"><paramtype>state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="inOut"><paramtype>state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="initialize_controlled"><type>void</type><template>
          <template-type-parameter name="ExplicitStepper"/>
          <template-type-parameter name="System"/>
        </template><parameter name="stepper"><paramtype>ExplicitStepper</paramtype></parameter><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="inOut"><paramtype>state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype></parameter></method>
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="inOut"><paramtype>state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype></parameter></method>
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="out"><paramtype>state_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype></parameter></method>
<method name="reset"><type>void</type></method>
</method-group>
<constructor><parameter name="step_adjuster"><paramtype>step_adjuster_type</paramtype><default>step_adjuster_type()</default></parameter></constructor>
<method-group name="private member functions">
<method name="resize_dxdt_impl"><type>bool</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype></parameter></method>
<method name="resize_xerr_impl"><type>bool</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype></parameter></method>
<method name="resize_xnew_impl"><type>bool</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype></parameter></method>
</method-group>
</class><class name="default_order_adjuster"><template>
      <template-nontype-parameter name="MaxOrder"><type>size_t</type></template-nontype-parameter>
      <template-type-parameter name="State"/>
      <template-type-parameter name="Value"><default>double</default></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default></template-type-parameter>
    </template><typedef name="state_type"><type>State</type></typedef>
<typedef name="value_type"><type>Value</type></typedef>
<typedef name="wrapped_state_type"><type>state_wrapper&lt; state_type &gt;</type></typedef>
<typedef name="algebra_type"><type>Algebra</type></typedef>
<method-group name="public member functions">
<method name="adjust_order"><type>size_t</type><parameter name="order"><paramtype>size_t</paramtype></parameter><parameter name="init"><paramtype>size_t</paramtype></parameter><parameter name="xerr"><paramtype>boost::array&lt; wrapped_state_type, 4 &gt; &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default></parameter></constructor>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/controlled_runge_kutta.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="controlled_runge_kutta"><template>
      <template-type-parameter name="ErrorStepper"/>
      <template-type-parameter name="ErrorChecker"><default><classname alt="boost::numeric::odeint::default_error_checker">default_error_checker</classname>&lt; typename ErrorStepper::value_type ,    typename ErrorStepper::algebra_type ,    typename ErrorStepper::operations_type &gt;</default></template-type-parameter>
      <template-type-parameter name="StepAdjuster"><default>default_step_adjuster&lt; typename ErrorStepper::value_type ,    typename ErrorStepper::time_type &gt;</default></template-type-parameter>
      <template-type-parameter name="Resizer"><default>typename ErrorStepper::resizer_type</default></template-type-parameter>
      <template-type-parameter name="ErrorStepperCategory"><default>typename ErrorStepper::stepper_category</default></template-type-parameter>
    </template></class><class-specialization name="controlled_runge_kutta"><template>
      <template-type-parameter name="ErrorStepper"><purpose><para>The stepper type with error estimation, has to fulfill the ErrorStepper concept. </para></purpose></template-type-parameter>
      <template-type-parameter name="ErrorChecker"><purpose><para>The error checker </para></purpose></template-type-parameter>
      <template-type-parameter name="StepAdjuster"/>
      <template-type-parameter name="Resizer"><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><specialization><template-arg>ErrorStepper</template-arg><template-arg>ErrorChecker</template-arg><template-arg>StepAdjuster</template-arg><template-arg>Resizer</template-arg><template-arg>explicit_error_stepper_fsal_tag</template-arg></specialization><purpose>Implements step size control for Runge-Kutta FSAL steppers with error estimation. </purpose><description><para>This class implements the step size control for FSAL Runge-Kutta steppers with error estimation.</para><para>
</para></description><typedef name="stepper_type"><type>ErrorStepper</type></typedef>
<typedef name="state_type"><type>stepper_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>Resizer</type></typedef>
<typedef name="error_checker_type"><type>ErrorChecker</type></typedef>
<typedef name="step_adjuster_type"><type>StepAdjuster</type></typedef>
<typedef name="stepper_category"><type>explicit_controlled_stepper_fsal_tag</type></typedef>
<method-group name="public member functions">
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype><description><para>The state of the ODE which should be solved. Overwritten if the step is successful. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. </purpose><description><para>This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>const StateInOut &amp;</paramtype><description><para>The state of the ODE which should be solved. Overwritten if the step is successful. Can be a boost range. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. Solves the forwarding problem and allows for using boost range as state_type. </purpose><description><para>This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="try_step"><type>boost::disable_if&lt; boost::is_same&lt; StateIn, time_type &gt;, controlled_step_result &gt;::type</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>Used to store the result of the step. </para></description></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. </purpose><description><para><note><para>This method is disabled if state_type=time_type to avoid ambiguity.</para>
</note>
This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
          <template-type-parameter name="DerivInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype><description><para>The state of the ODE which should be solved. Overwritten if the step is successful. </para></description></parameter><parameter name="dxdt"><paramtype>DerivInOut &amp;</paramtype><description><para>The derivative of state. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. </purpose><description><para>This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="DerivOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. </para></description></parameter><parameter name="dxdt_in"><paramtype>const DerivIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>Used to store the result of the step. </para></description></parameter><parameter name="dxdt_out"><paramtype>DerivOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. </purpose><description><para>This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="reset"><type>void</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Resets the internal state of the underlying FSAL stepper. </purpose></method>
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="DerivIn"/>
        </template><parameter name="deriv"><paramtype>const DerivIn &amp;</paramtype><description><para>The initial derivative of the ODE. </para></description></parameter><purpose>Initializes the internal state storing an internal copy of the derivative. </purpose><description><para>
</para></description></method>
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>const StateIn &amp;</paramtype><description><para>The initial state of the ODE which should be solved. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The initial time. </para></description></parameter><purpose>Initializes the internal state storing an internal copy of the derivative. </purpose><description><para>
</para></description></method>
<method name="is_initialized" cv="const"><type>bool</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns true if the stepper has been initialized, false otherwise. </purpose><description><para>
</para></description><returns><para>true, if the stepper has been initialized, false otherwise. </para>
</returns></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
<method name="stepper"><type>stepper_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the instance of the underlying stepper. </purpose><description><para>
</para></description><returns><para>The instance of the underlying stepper. </para>
</returns></method>
<method name="stepper" cv="const"><type>const stepper_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the instance of the underlying stepper. </purpose><description><para>
</para></description><returns><para>The instance of the underlying stepper. </para>
</returns></method>
</method-group>
<constructor><parameter name="error_checker"><paramtype>const error_checker_type &amp;</paramtype><default>error_checker_type()</default><description><para>An instance of the error checker. </para></description></parameter><parameter name="step_adjuster"><paramtype>const step_adjuster_type &amp;</paramtype><default>step_adjuster_type()</default></parameter><parameter name="stepper"><paramtype>const stepper_type &amp;</paramtype><default>stepper_type()</default><description><para>An instance of the underlying stepper. </para></description></parameter><purpose>Constructs the controlled Runge-Kutta stepper. </purpose><description><para>
</para></description></constructor>
<method-group name="private member functions">
<method name="resize_m_xerr_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="resize_m_dxdt_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="resize_m_dxdt_new_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="resize_m_xnew_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="try_step_v1"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype></parameter></method>
</method-group>
</class-specialization><class-specialization name="controlled_runge_kutta"><template>
      <template-type-parameter name="ErrorStepper"><purpose><para>The stepper type with error estimation, has to fulfill the ErrorStepper concept. </para></purpose></template-type-parameter>
      <template-type-parameter name="ErrorChecker"><purpose><para>The error checker </para></purpose></template-type-parameter>
      <template-type-parameter name="StepAdjuster"/>
      <template-type-parameter name="Resizer"><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><specialization><template-arg>ErrorStepper</template-arg><template-arg>ErrorChecker</template-arg><template-arg>StepAdjuster</template-arg><template-arg>Resizer</template-arg><template-arg>explicit_error_stepper_tag</template-arg></specialization><purpose>Implements step size control for Runge-Kutta steppers with error estimation. </purpose><description><para>This class implements the step size control for standard Runge-Kutta steppers with error estimation.</para><para>
</para></description><typedef name="stepper_type"><type>ErrorStepper</type></typedef>
<typedef name="state_type"><type>stepper_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>Resizer</type></typedef>
<typedef name="error_checker_type"><type>ErrorChecker</type></typedef>
<typedef name="step_adjuster_type"><type>StepAdjuster</type></typedef>
<typedef name="stepper_category"><type>explicit_controlled_stepper_tag</type></typedef>
<method-group name="public member functions">
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype><description><para>The state of the ODE which should be solved. Overwritten if the step is successful. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. </purpose><description><para>This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>const StateInOut &amp;</paramtype><description><para>The state of the ODE which should be solved. Overwritten if the step is successful. Can be a boost range. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. Solves the forwarding problem and allows for using boost range as state_type. </purpose><description><para>This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
          <template-type-parameter name="DerivIn"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype><description><para>The state of the ODE which should be solved. Overwritten if the step is successful. </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of state. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. </purpose><description><para>This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="try_step"><type>boost::disable_if&lt; boost::is_same&lt; StateIn, time_type &gt;, controlled_step_result &gt;::type</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>Used to store the result of the step. </para></description></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. </purpose><description><para><note><para>This method is disabled if state_type=time_type to avoid ambiguity.</para>
</note>
This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="try_step"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of state. </para></description></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype><description><para>The value of the time. Updated if the step is successful. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>Used to store the result of the step. </para></description></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype><description><para>The step size. Updated. </para></description></parameter><purpose>Tries to perform one step. </purpose><description><para>This method tries to do one step with step size dt. If the error estimate is to large, the step is rejected and the method returns fail and the step size dt is reduced. If the error estimate is acceptably small, the step is performed, success is returned and dt might be increased to make the steps as large as possible. This method also updates t if a step is performed.</para><para>

</para></description><returns><para>success if the step was accepted, fail otherwise. </para>
</returns></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
<method name="stepper"><type>stepper_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the instance of the underlying stepper. </purpose><description><para>
</para></description><returns><para>The instance of the underlying stepper. </para>
</returns></method>
<method name="stepper" cv="const"><type>const stepper_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the instance of the underlying stepper. </purpose><description><para>
</para></description><returns><para>The instance of the underlying stepper. </para>
</returns></method>
</method-group>
<constructor><parameter name="error_checker"><paramtype>const error_checker_type &amp;</paramtype><default>error_checker_type()</default><description><para>An instance of the error checker. </para></description></parameter><parameter name="step_adjuster"><paramtype>const step_adjuster_type &amp;</paramtype><default>step_adjuster_type()</default></parameter><parameter name="stepper"><paramtype>const stepper_type &amp;</paramtype><default>stepper_type()</default><description><para>An instance of the underlying stepper. </para></description></parameter><purpose>Constructs the controlled Runge-Kutta stepper. </purpose><description><para>
</para></description></constructor>
<method-group name="private member functions">
<method name="try_step_v1"><type>controlled_step_result</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype></parameter></method>
<method name="resize_m_xerr_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="resize_m_dxdt_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="resize_m_xnew_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class-specialization><class name="default_error_checker"><template>
      <template-type-parameter name="Value"><purpose><para>The value type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><purpose><para>The operations type. </para></purpose></template-type-parameter>
    </template><purpose>The default error checker to be used with Runge-Kutta error steppers. </purpose><description><para>This class provides the default mechanism to compare the error estimates reported by Runge-Kutta error steppers with user defined error bounds. It is used by the controlled_runge_kutta steppers.</para><para>
</para></description><typedef name="value_type"><type>Value</type></typedef>
<typedef name="algebra_type"><type>Algebra</type></typedef>
<typedef name="operations_type"><type>Operations</type></typedef>
<method-group name="public member functions">
<method name="error" cv="const"><type>value_type</type><template>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Deriv"/>
          <template-type-parameter name="Err"/>
          <template-type-parameter name="Time"/>
        </template><parameter name="x_old"><paramtype>const State &amp;</paramtype></parameter><parameter name="dxdt_old"><paramtype>const Deriv &amp;</paramtype></parameter><parameter name="x_err"><paramtype>Err &amp;</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter></method>
<method name="error" cv="const"><type>value_type</type><template>
          <template-type-parameter name="State"/>
          <template-type-parameter name="Deriv"/>
          <template-type-parameter name="Err"/>
          <template-type-parameter name="Time"/>
        </template><parameter name="algebra"><paramtype>algebra_type &amp;</paramtype></parameter><parameter name="x_old"><paramtype>const State &amp;</paramtype></parameter><parameter name="dxdt_old"><paramtype>const Deriv &amp;</paramtype></parameter><parameter name="x_err"><paramtype>Err &amp;</paramtype></parameter><parameter name="dt"><paramtype>Time</paramtype></parameter></method>
</method-group>
<constructor><parameter name="eps_abs"><paramtype>value_type</paramtype><default>static_cast&lt; value_type &gt;(1.0e-6)</default></parameter><parameter name="eps_rel"><paramtype>value_type</paramtype><default>static_cast&lt; value_type &gt;(1.0e-6)</default></parameter><parameter name="a_x"><paramtype>value_type</paramtype><default>static_cast&lt; value_type &gt;(1)</default></parameter><parameter name="a_dxdt"><paramtype>value_type</paramtype><default>static_cast&lt; value_type &gt;(1)</default></parameter></constructor>
</class><class name="default_step_adjuster"><template>
      <template-type-parameter name="Value"/>
      <template-type-parameter name="Time"/>
    </template><typedef name="time_type"><type>Time</type></typedef>
<typedef name="value_type"><type>Value</type></typedef>
<method-group name="public member functions">
<method name="decrease_step" cv="const"><type>time_type</type><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="error"><paramtype>const value_type</paramtype></parameter><parameter name="error_order"><paramtype>const int</paramtype></parameter></method>
<method name="increase_step" cv="const"><type>time_type</type><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="error"><paramtype>value_type</paramtype></parameter><parameter name="stepper_order"><paramtype>const int</paramtype></parameter></method>
<method name="check_step_size_limit"><type>bool</type><parameter name="dt"><paramtype>const time_type</paramtype></parameter></method>
<method name="get_max_dt"><type>time_type</type></method>
</method-group>
<constructor><parameter name="max_dt"><paramtype>const time_type</paramtype><default>static_cast&lt; time_type &gt;(0)</default></parameter></constructor>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/controlled_step_result.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<enum name="controlled_step_result"><enumvalue name="success"><description><para>The trial step was successful, hence the state and the time have been advanced. </para></description></enumvalue><enumvalue name="fail"><description><para>The step was not successful and might possibly be repeated with a small step size. </para></description></enumvalue><purpose>Enum representing the return values of the controlled steppers. </purpose></enum>




















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/dense_output_runge_kutta.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="dense_output_runge_kutta"><template>
      <template-type-parameter name="Stepper"/>
      <template-type-parameter name="StepperCategory"><default>typename Stepper::stepper_category</default></template-type-parameter>
    </template></class><class-specialization name="dense_output_runge_kutta"><template>
      <template-type-parameter name="Stepper"><purpose><para>The stepper type of the underlying algorithm. </para></purpose></template-type-parameter>
    </template><specialization><template-arg>Stepper</template-arg><template-arg>explicit_controlled_stepper_fsal_tag</template-arg></specialization><purpose>The class representing dense-output Runge-Kutta steppers with FSAL property. </purpose><description><para>The interface is the same as for <classname alt="boost::numeric::odeint::dense_output_runge_kutta&lt; Stepper, stepper_tag &gt;">dense_output_runge_kutta&lt; Stepper , stepper_tag &gt;</classname>. This class provides dense output functionality based on methods with step size controlled</para><para>
</para></description><typedef name="controlled_stepper_type"><type>Stepper</type></typedef>
<typedef name="stepper_type"><type>controlled_stepper_type::stepper_type</type></typedef>
<typedef name="state_type"><type>stepper_type::state_type</type></typedef>
<typedef name="wrapped_state_type"><type>stepper_type::wrapped_state_type</type></typedef>
<typedef name="value_type"><type>stepper_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_type::deriv_type</type></typedef>
<typedef name="wrapped_deriv_type"><type>stepper_type::wrapped_deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>stepper_type::resizer_type</type></typedef>
<typedef name="stepper_category"><type>dense_output_stepper_tag</type></typedef>
<typedef name="dense_output_stepper_type"><type>dense_output_runge_kutta&lt; Stepper &gt;</type></typedef>
<method-group name="public member functions">
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x0"><paramtype>const StateType &amp;</paramtype></parameter><parameter name="t0"><paramtype>time_type</paramtype></parameter><parameter name="dt0"><paramtype>time_type</paramtype></parameter></method>
<method name="do_step"><type>std::pair&lt; time_type, time_type &gt;</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter></method>
<method name="calc_state" cv="const"><type>void</type><template>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="x"><paramtype>StateOut &amp;</paramtype></parameter></method>
<method name="calc_state" cv="const"><type>void</type><template>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="x"><paramtype>const StateOut &amp;</paramtype></parameter></method>
<method name="resize"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype></parameter></method>
<method name="current_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="current_time" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="previous_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="previous_time" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="current_time_step" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter></method>
</method-group>
<constructor><parameter name="stepper"><paramtype>const controlled_stepper_type &amp;</paramtype><default>controlled_stepper_type()</default></parameter></constructor>
<method-group name="private member functions">
<method name="get_current_state"><type>state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_current_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_state"><type>state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_current_deriv"><type>deriv_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_current_deriv" cv="const"><type>const deriv_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_deriv"><type>deriv_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_deriv" cv="const"><type>const deriv_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="toggle_current_state"><type>void</type><parameter name=""><paramtype>void</paramtype></parameter></method>
</method-group>
</class-specialization><class-specialization name="dense_output_runge_kutta"><template>
      <template-type-parameter name="Stepper"><purpose><para>The stepper type of the underlying algorithm. </para></purpose></template-type-parameter>
    </template><specialization><template-arg>Stepper</template-arg><template-arg>stepper_tag</template-arg></specialization><purpose>The class representing dense-output Runge-Kutta steppers. </purpose><description><para><note><para>In this stepper, the initialize method has to be called before using the do_step method.</para>
</note>
The dense-output functionality allows to interpolate the solution between subsequent integration points using intermediate results obtained during the computation. This version works based on a normal stepper without step-size control.</para><para>
</para></description><typedef name="stepper_type"><type>Stepper</type></typedef>
<typedef name="state_type"><type>stepper_type::state_type</type></typedef>
<typedef name="wrapped_state_type"><type>stepper_type::wrapped_state_type</type></typedef>
<typedef name="value_type"><type>stepper_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_type::deriv_type</type></typedef>
<typedef name="wrapped_deriv_type"><type>stepper_type::wrapped_deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>stepper_type::resizer_type</type></typedef>
<typedef name="stepper_category"><type>dense_output_stepper_tag</type></typedef>
<typedef name="dense_output_stepper_type"><type>dense_output_runge_kutta&lt; Stepper &gt;</type></typedef>
<method-group name="public member functions">
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x0"><paramtype>const StateType &amp;</paramtype><description><para>The initial state of the ODE which should be solved. </para></description></parameter><parameter name="t0"><paramtype>time_type</paramtype><description><para>The initial time, at which the step should be performed. </para></description></parameter><parameter name="dt0"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>Initializes the stepper. Has to be called before do_step can be used to set the initial conditions and the step size. </purpose><description><para>
</para></description></method>
<method name="do_step"><type>std::pair&lt; time_type, time_type &gt;</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ordinary differential equation. It must fulfill the Simple System concept. </para></description></parameter><purpose>Does one time step. </purpose><description><para><note><para>initialize has to be called before using this method to set the initial conditions x,t and the stepsize. </para>
</note>


</para></description><returns><para>Pair with start and end time of the integration step. </para>
</returns></method>
<method name="calc_state" cv="const"><type>void</type><template>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="t"><paramtype>time_type</paramtype><description><para>The time at which the solution should be calculated, has to be in the current time interval. </para></description></parameter><parameter name="x"><paramtype>StateOut &amp;</paramtype><description><para>The output variable where the result is written into. </para></description></parameter><purpose>Calculates the solution at an intermediate point. </purpose><description><para>
</para></description></method>
<method name="calc_state" cv="const"><type>void</type><template>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="t"><paramtype>time_type</paramtype><description><para>The time at which the solution should be calculated, has to be in the current time interval. </para></description></parameter><parameter name="x"><paramtype>const StateOut &amp;</paramtype><description><para>The output variable where the result is written into, can be a boost range. </para></description></parameter><purpose>Calculates the solution at an intermediate point. Solves the forwarding problem. </purpose><description><para>
</para></description></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
<method name="current_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the current state of the solution. </purpose><description><para>
</para></description><returns><para>The current state of the solution x(t). </para>
</returns></method>
<method name="current_time" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the current time of the solution. </purpose><description><para>
</para></description><returns><para>The current time of the solution t. </para>
</returns></method>
<method name="previous_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the last state of the solution. </purpose><description><para>
</para></description><returns><para>The last state of the solution x(t-dt). </para>
</returns></method>
<method name="previous_time" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the last time of the solution. </purpose><description><para>
</para></description><returns><para>The last time of the solution t-dt. </para>
</returns></method>
<method name="current_time_step" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Returns the current time step. </purpose><description><para>
</para></description><returns><para>dt. </para>
</returns></method>
</method-group>
<constructor><parameter name="stepper"><paramtype>const stepper_type &amp;</paramtype><default>stepper_type()</default><description><para>An instance of the underlying stepper. </para></description></parameter><purpose>Constructs the dense_output_runge_kutta class. An instance of the underlying stepper can be provided. </purpose><description><para>
</para></description></constructor>
<method-group name="private member functions">
<method name="get_current_state"><type>state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_current_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_state"><type>state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="toggle_current_state"><type>void</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class-specialization>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/euler.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="euler"><template>
      <template-type-parameter name="State"><purpose><para>The state type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The value type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default><purpose><para>The type representing the time derivative of the state. </para></purpose></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The time representing the independent variable - the time. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><inherit access="public">explicit_stepper_base</inherit><purpose>An implementation of the Euler method. </purpose><description><para>The Euler method is a very simply solver for ordinary differential equations. This method should not be used for real applications. It is only useful for demonstration purposes. Step size control is not provided but trivial continuous output is available.</para><para>This class derives from explicit_stepper_base and inherits its interface via CRTP (current recurring template pattern), see explicit_stepper_base</para><para>
</para></description><typedef name="stepper_base_type"><type>explicit_stepper_base&lt; <classname>euler</classname>&lt; ... &gt;,... &gt;</type></typedef>
<typedef name="state_type"><type>stepper_base_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_base_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_base_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_base_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>stepper_base_type::resizer_type</type></typedef>
<method-group name="public member functions">
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name=""><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. </para></description></parameter><parameter name=""><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. The result is updated out of place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Access to this step functionality is provided by explicit_stepper_base and <computeroutput>do_step_impl</computeroutput> should not be called directly. </purpose><description><para>
</para></description></method>
<method name="calc_state" cv="const"><type>void</type><template>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="StateIn1"/>
          <template-type-parameter name="StateIn2"/>
        </template><parameter name="x"><paramtype>StateOut &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="old_state"><paramtype>const StateIn1 &amp;</paramtype></parameter><parameter name="t_old"><paramtype>time_type</paramtype></parameter><parameter name=""><paramtype>const StateIn2 &amp;</paramtype></parameter><parameter name=""><paramtype>time_type</paramtype></parameter><purpose>This method is used for continuous output and it calculates the state <computeroutput>x</computeroutput> at a time <computeroutput>t</computeroutput> from the knowledge of two states <computeroutput>old_state</computeroutput> and <computeroutput>current_state</computeroutput> at time points <computeroutput>t_old</computeroutput> and <computeroutput>t_new</computeroutput>. </purpose></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored inside explicit_stepper_base. </para></description></parameter><purpose>Constructs the euler class. This constructor can be used as a default constructor of the algebra has a default constructor. </purpose><description><para>
</para></description></constructor>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/explicit_error_generic_rk.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="explicit_error_generic_rk"><template>
      <template-nontype-parameter name="StageCount"><type>size_t</type><purpose><para>The number of stages of the Runge-Kutta algorithm. </para></purpose></template-nontype-parameter>
      <template-nontype-parameter name="Order"><type>size_t</type><purpose><para>The order of a stepper if the stepper is used without error estimation. </para></purpose></template-nontype-parameter>
      <template-nontype-parameter name="StepperOrder"><type>size_t</type><purpose><para>The order of a step if the stepper is used with error estimation. Usually Order and StepperOrder have the same value. </para></purpose></template-nontype-parameter>
      <template-nontype-parameter name="ErrorOrder"><type>size_t</type><purpose><para>The order of the error step if the stepper is used with error estimation. </para></purpose></template-nontype-parameter>
      <template-type-parameter name="State"><purpose><para>The type representing the state of the ODE. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The floating point type which is used in the computations. </para></purpose></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The type representing the independent variable - the time - of the ODE. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><inherit access="public">explicit_error_stepper_base</inherit><purpose>A generic implementation of explicit Runge-Kutta algorithms with error estimation. This class is as a base class for all explicit Runge-Kutta steppers with error estimation. </purpose><description><para>This class implements the explicit Runge-Kutta algorithms with error estimation in a generic way. The Butcher tableau is passed to the stepper which constructs the stepper scheme with the help of a template-metaprogramming algorithm. ToDo : Add example!</para><para>This class derives explicit_error_stepper_base which provides the stepper interface.</para><para>
</para></description><typedef name="stepper_base_type"><type>explicit_stepper_base&lt; ... &gt;</type></typedef>
<typedef name="state_type"><type>stepper_base_type::state_type</type></typedef>
<typedef name="wrapped_state_type"><type>stepper_base_type::wrapped_state_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_base_type::deriv_type</type></typedef>
<typedef name="wrapped_deriv_type"><type>stepper_base_type::wrapped_deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_base_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_base_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>stepper_base_type::resizer_type</type></typedef>
<typedef name="rk_algorithm_type"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="coef_a_type"><type>rk_algorithm_type::coef_a_type</type></typedef>
<typedef name="coef_b_type"><type>rk_algorithm_type::coef_b_type</type></typedef>
<typedef name="coef_c_type"><type>rk_algorithm_type::coef_c_type</type></typedef>
<data-member name="stage_count" specifiers="static"><type>const size_t</type></data-member>
<method-group name="public member functions">
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="Err"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><parameter name="xerr"><paramtype>Err &amp;</paramtype><description><para>The result of the error estimation is written in xerr. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. The result is updated out-of-place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Futhermore, an estimation of the error is stored in <computeroutput>xerr</computeroutput>. <computeroutput>do_step_impl</computeroutput> is used by explicit_error_stepper_base. </purpose><description><para>
</para></description></method>
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. The result is updated out-of-place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Access to this step functionality is provided by explicit_stepper_base and <computeroutput>do_step_impl</computeroutput> should not be called directly. </purpose><description><para>
</para></description></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
</method-group>
<constructor><parameter name="a"><paramtype>const coef_a_type &amp;</paramtype><description><para>Triangular matrix of parameters b in the Butcher tableau. </para></description></parameter><parameter name="b"><paramtype>const coef_b_type &amp;</paramtype><description><para>Last row of the butcher tableau. </para></description></parameter><parameter name="b2"><paramtype>const coef_b_type &amp;</paramtype><description><para>Parameters for lower-order evaluation to estimate the error. </para></description></parameter><parameter name="c"><paramtype>const coef_c_type &amp;</paramtype><description><para>Parameters to calculate the time points in the Butcher tableau. </para></description></parameter><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored inside explicit_stepper_base. </para></description></parameter><purpose>Constructs the explicit_error_generik_rk class with the given parameters a, b, b2 and c. See examples section for details on the coefficients. </purpose><description><para>
</para></description></constructor>
<method-group name="private member functions">
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/explicit_generic_rk.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="explicit_generic_rk"><template>
      <template-nontype-parameter name="StageCount"><type>size_t</type><purpose><para>The number of stages of the Runge-Kutta algorithm. </para></purpose></template-nontype-parameter>
      <template-nontype-parameter name="Order"><type>size_t</type><purpose><para>The order of the stepper. </para></purpose></template-nontype-parameter>
      <template-type-parameter name="State"><purpose><para>The type representing the state of the ODE. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><purpose><para>The floating point type which is used in the computations. </para></purpose></template-type-parameter>
      <template-type-parameter name="Deriv"/>
      <template-type-parameter name="Time"><purpose><para>The type representing the independent variable - the time - of the ODE. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><inherit access="public">explicit_stepper_base</inherit><purpose>A generic implementation of explicit Runge-Kutta algorithms. This class is as a base class for all explicit Runge-Kutta steppers. </purpose><description><para>This class implements the explicit Runge-Kutta algorithms without error estimation in a generic way. The Butcher tableau is passed to the stepper which constructs the stepper scheme with the help of a template-metaprogramming algorithm. ToDo : Add example!</para><para>This class derives explicit_stepper_base which provides the stepper interface.</para><para>
</para></description><typedef name="stepper_base_type"><type>explicit_stepper_base&lt; ... &gt;</type></typedef>
<typedef name="state_type"><type>stepper_base_type::state_type</type></typedef>
<typedef name="wrapped_state_type"><type>stepper_base_type::wrapped_state_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_base_type::deriv_type</type></typedef>
<typedef name="wrapped_deriv_type"><type>stepper_base_type::wrapped_deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_base_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_base_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>stepper_base_type::resizer_type</type></typedef>
<typedef name="rk_algorithm_type"><type><emphasis>unspecified</emphasis></type></typedef>
<typedef name="coef_a_type"><type>rk_algorithm_type::coef_a_type</type></typedef>
<typedef name="coef_b_type"><type>rk_algorithm_type::coef_b_type</type></typedef>
<typedef name="coef_c_type"><type>rk_algorithm_type::coef_c_type</type></typedef>
<method-group name="public member functions">
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. The result is updated out of place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Access to this step functionality is provided by explicit_stepper_base and <computeroutput>do_step_impl</computeroutput> should not be called directly. </purpose><description><para>
</para></description></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
</method-group>
<constructor><parameter name="a"><paramtype>const coef_a_type &amp;</paramtype><description><para>Triangular matrix of parameters b in the Butcher tableau. </para></description></parameter><parameter name="b"><paramtype>const coef_b_type &amp;</paramtype><description><para>Last row of the butcher tableau. </para></description></parameter><parameter name="c"><paramtype>const coef_c_type &amp;</paramtype><description><para>Parameters to calculate the time points in the Butcher tableau. </para></description></parameter><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored inside explicit_stepper_base. </para></description></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::explicit_generic_rk">explicit_generic_rk</classname> class. See examples section for details on the coefficients. </purpose><description><para>
</para></description></constructor>
<method-group name="private member functions">
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/extrapolation_stepper.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="extrapolation_stepper"><template>
      <template-nontype-parameter name="Order"><type>unsigned short</type></template-nontype-parameter>
      <template-type-parameter name="State"/>
      <template-type-parameter name="Value"><default>double</default></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default></template-type-parameter>
    </template><inherit access="public">explicit_error_stepper_base</inherit><purpose>Extrapolation stepper with configurable order, and error estimation. </purpose><description><para>The extrapolation stepper is a stepper with error estimation and configurable order. The order is given as template parameter and needs to be an <emphasis>odd</emphasis> number. The stepper is based on several executions of the modified midpoint method and a Richardson extrapolation. This is essentially the same technique as for <classname alt="boost::numeric::odeint::bulirsch_stoer">bulirsch_stoer</classname>, but without the variable order.</para><para><note><para>The Order parameter has to be an even number greater 2. </para>
</note>
</para></description><typedef name="stepper_base_type"><type>explicit_error_stepper_base&lt; <classname>extrapolation_stepper</classname>&lt; ... &gt;,... &gt;</type></typedef>
<typedef name="state_type"><type>stepper_base_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_base_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_base_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_base_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>stepper_base_type::resizer_type</type></typedef>
<typedef name="order_type"><type>unsigned short</type></typedef>
<data-member name="order_value" specifiers="static"><type>const order_type</type></data-member>
<data-member name="stepper_order_value" specifiers="static"><type>const order_type</type></data-member>
<data-member name="error_order_value" specifiers="static"><type>const order_type</type></data-member>
<data-member name="m_k_max" specifiers="static"><type>const size_t</type></data-member>
<method-group name="private member functions">
<method name="BOOST_STATIC_ASSERT_MSG"><type/><parameter name=""><paramtype>((Order % 2)==0) &amp;&amp;(Order &gt; 2)</paramtype></parameter><parameter name=""><paramtype>"extrapolation_stepper requires even Order larger than 2"</paramtype></parameter></method>
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="resize_m_xout"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="extrapolate"><type>void</type><template>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="k"><paramtype>size_t</paramtype></parameter><parameter name="table"><paramtype>state_table_type &amp;</paramtype></parameter><parameter name="coeff"><paramtype>const value_matrix &amp;</paramtype></parameter><parameter name="xest"><paramtype>StateInOut &amp;</paramtype></parameter></method>
</method-group>
<method-group name="public member functions">
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="Err"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="xerr"><paramtype>Err &amp;</paramtype></parameter></method>
<method name="do_step_impl_io"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="Err"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="inout"><paramtype>StateInOut &amp;</paramtype></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="xerr"><paramtype>Err &amp;</paramtype></parameter></method>
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="do_step_impl_io"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
          <template-type-parameter name="DerivIn"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="inout"><paramtype>StateInOut &amp;</paramtype></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="do_step_dxdt_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
          <template-type-parameter name="DerivIn"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="do_step_dxdt_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default></parameter></constructor>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/generation.hpp">
</header>
<header name="boost/numeric/odeint/stepper/implicit_euler.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="implicit_euler"><template>
      <template-type-parameter name="ValueType"/>
      <template-type-parameter name="Resizer"><default>initially_resizer</default></template-type-parameter>
    </template><typedef name="value_type"><type>ValueType</type></typedef>
<typedef name="time_type"><type>value_type</type></typedef>
<typedef name="state_type"><type>boost::numeric::ublas::vector&lt; value_type &gt;</type></typedef>
<typedef name="wrapped_state_type"><type>state_wrapper&lt; state_type &gt;</type></typedef>
<typedef name="deriv_type"><type>state_type</type></typedef>
<typedef name="wrapped_deriv_type"><type>state_wrapper&lt; deriv_type &gt;</type></typedef>
<typedef name="matrix_type"><type>boost::numeric::ublas::matrix&lt; value_type &gt;</type></typedef>
<typedef name="wrapped_matrix_type"><type>state_wrapper&lt; matrix_type &gt;</type></typedef>
<typedef name="pmatrix_type"><type>boost::numeric::ublas::permutation_matrix&lt; size_t &gt;</type></typedef>
<typedef name="wrapped_pmatrix_type"><type>state_wrapper&lt; pmatrix_type &gt;</type></typedef>
<typedef name="resizer_type"><type>Resizer</type></typedef>
<typedef name="stepper_category"><type>stepper_tag</type></typedef>
<typedef name="stepper_type"><type>implicit_euler&lt; ValueType, Resizer &gt;</type></typedef>
<method-group name="public member functions">
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="epsilon"><paramtype>value_type</paramtype><default>1E-6</default></parameter></constructor>
<method-group name="private member functions">
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="solve"><type>void</type><parameter name="x"><paramtype>state_type &amp;</paramtype></parameter><parameter name="m"><paramtype>matrix_type &amp;</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/modified_midpoint.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="modified_midpoint"><template>
      <template-type-parameter name="State"/>
      <template-type-parameter name="Value"><default>double</default></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default></template-type-parameter>
    </template><inherit access="public">explicit_stepper_base</inherit><description><para>Implementation of the modified midpoint method with a configurable number of intermediate steps. This class is used by the Bulirsch-Stoer algorithm and is not meant for direct usage. </para></description><typedef name="stepper_base_type"><type>explicit_stepper_base&lt; <classname>modified_midpoint</classname>&lt; State, Value, Deriv, Time, Algebra, Operations, Resizer &gt;, 2, State, Value, Deriv, Time, Algebra, Operations, Resizer &gt;</type></typedef>
<typedef name="state_type"><type>stepper_base_type::state_type</type></typedef>
<typedef name="wrapped_state_type"><type>stepper_base_type::wrapped_state_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_base_type::deriv_type</type></typedef>
<typedef name="wrapped_deriv_type"><type>stepper_base_type::wrapped_deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_base_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_base_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>stepper_base_type::resizer_type</type></typedef>
<typedef name="stepper_type"><type>stepper_base_type::stepper_type</type></typedef>
<method-group name="public member functions">
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="set_steps"><type>void</type><parameter name="steps"><paramtype>unsigned short</paramtype></parameter></method>
<method name="steps" cv="const"><type>unsigned short</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="steps"><paramtype>unsigned short</paramtype><default>2</default></parameter><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default></parameter></constructor>
<method-group name="private member functions">
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class><class name="modified_midpoint_dense_out"><template>
      <template-type-parameter name="State"/>
      <template-type-parameter name="Value"><default>double</default></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default></template-type-parameter>
    </template><description><para>Implementation of the modified midpoint method with a configurable number of intermediate steps. This class is used by the dense output Bulirsch-Stoer algorithm and is not meant for direct usage. <note><para>This stepper is for internal use only and does not meet any stepper concept. </para>
</note>
</para></description><typedef name="state_type"><type>State</type></typedef>
<typedef name="value_type"><type>Value</type></typedef>
<typedef name="deriv_type"><type>Deriv</type></typedef>
<typedef name="time_type"><type>Time</type></typedef>
<typedef name="algebra_type"><type>Algebra</type></typedef>
<typedef name="operations_type"><type>Operations</type></typedef>
<typedef name="resizer_type"><type>Resizer</type></typedef>
<typedef name="wrapped_state_type"><type>state_wrapper&lt; state_type &gt;</type></typedef>
<typedef name="wrapped_deriv_type"><type>state_wrapper&lt; deriv_type &gt;</type></typedef>
<typedef name="stepper_type"><type><classname>modified_midpoint_dense_out</classname>&lt; State, Value, Deriv, Time, Algebra, Operations, Resizer &gt;</type></typedef>
<typedef name="deriv_table_type"><type>std::vector&lt; wrapped_deriv_type &gt;</type></typedef>
<method-group name="public member functions">
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="x_mp"><paramtype>state_type &amp;</paramtype></parameter><parameter name="derivs"><paramtype>deriv_table_type &amp;</paramtype></parameter></method>
<method name="set_steps"><type>void</type><parameter name="steps"><paramtype>unsigned short</paramtype></parameter></method>
<method name="steps" cv="const"><type>unsigned short</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="resize"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="steps"><paramtype>unsigned short</paramtype><default>2</default></parameter><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default></parameter></constructor>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/rosenbrock4.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<struct name="default_rosenbrock_coefficients"><template>
      <template-type-parameter name="Value"/>
    </template><typedef name="value_type"><type>Value</type></typedef>
<typedef name="order_type"><type>unsigned short</type></typedef>
<data-member name="gamma"><type>const value_type</type></data-member>
<data-member name="d1"><type>const value_type</type></data-member>
<data-member name="d2"><type>const value_type</type></data-member>
<data-member name="d3"><type>const value_type</type></data-member>
<data-member name="d4"><type>const value_type</type></data-member>
<data-member name="c2"><type>const value_type</type></data-member>
<data-member name="c3"><type>const value_type</type></data-member>
<data-member name="c4"><type>const value_type</type></data-member>
<data-member name="c21"><type>const value_type</type></data-member>
<data-member name="a21"><type>const value_type</type></data-member>
<data-member name="c31"><type>const value_type</type></data-member>
<data-member name="c32"><type>const value_type</type></data-member>
<data-member name="a31"><type>const value_type</type></data-member>
<data-member name="a32"><type>const value_type</type></data-member>
<data-member name="c41"><type>const value_type</type></data-member>
<data-member name="c42"><type>const value_type</type></data-member>
<data-member name="c43"><type>const value_type</type></data-member>
<data-member name="a41"><type>const value_type</type></data-member>
<data-member name="a42"><type>const value_type</type></data-member>
<data-member name="a43"><type>const value_type</type></data-member>
<data-member name="c51"><type>const value_type</type></data-member>
<data-member name="c52"><type>const value_type</type></data-member>
<data-member name="c53"><type>const value_type</type></data-member>
<data-member name="c54"><type>const value_type</type></data-member>
<data-member name="a51"><type>const value_type</type></data-member>
<data-member name="a52"><type>const value_type</type></data-member>
<data-member name="a53"><type>const value_type</type></data-member>
<data-member name="a54"><type>const value_type</type></data-member>
<data-member name="c61"><type>const value_type</type></data-member>
<data-member name="c62"><type>const value_type</type></data-member>
<data-member name="c63"><type>const value_type</type></data-member>
<data-member name="c64"><type>const value_type</type></data-member>
<data-member name="c65"><type>const value_type</type></data-member>
<data-member name="d21"><type>const value_type</type></data-member>
<data-member name="d22"><type>const value_type</type></data-member>
<data-member name="d23"><type>const value_type</type></data-member>
<data-member name="d24"><type>const value_type</type></data-member>
<data-member name="d25"><type>const value_type</type></data-member>
<data-member name="d31"><type>const value_type</type></data-member>
<data-member name="d32"><type>const value_type</type></data-member>
<data-member name="d33"><type>const value_type</type></data-member>
<data-member name="d34"><type>const value_type</type></data-member>
<data-member name="d35"><type>const value_type</type></data-member>
<data-member name="stepper_order" specifiers="static"><type>const order_type</type></data-member>
<data-member name="error_order" specifiers="static"><type>const order_type</type></data-member>
<method-group name="public member functions">
</method-group>
<constructor><parameter name=""><paramtype>void</paramtype></parameter></constructor>
</struct><class name="rosenbrock4"><template>
      <template-type-parameter name="Value"/>
      <template-type-parameter name="Coefficients"><default>default_rosenbrock_coefficients&lt; Value &gt;</default></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default></template-type-parameter>
    </template><typedef name="value_type"><type>Value</type></typedef>
<typedef name="state_type"><type>boost::numeric::ublas::vector&lt; value_type &gt;</type></typedef>
<typedef name="deriv_type"><type>state_type</type></typedef>
<typedef name="time_type"><type>value_type</type></typedef>
<typedef name="matrix_type"><type>boost::numeric::ublas::matrix&lt; value_type &gt;</type></typedef>
<typedef name="pmatrix_type"><type>boost::numeric::ublas::permutation_matrix&lt; size_t &gt;</type></typedef>
<typedef name="resizer_type"><type>Resizer</type></typedef>
<typedef name="rosenbrock_coefficients"><type>Coefficients</type></typedef>
<typedef name="stepper_category"><type>stepper_tag</type></typedef>
<typedef name="order_type"><type>unsigned short</type></typedef>
<typedef name="wrapped_state_type"><type>state_wrapper&lt; state_type &gt;</type></typedef>
<typedef name="wrapped_deriv_type"><type>state_wrapper&lt; deriv_type &gt;</type></typedef>
<typedef name="wrapped_matrix_type"><type>state_wrapper&lt; matrix_type &gt;</type></typedef>
<typedef name="wrapped_pmatrix_type"><type>state_wrapper&lt; pmatrix_type &gt;</type></typedef>
<typedef name="stepper_type"><type>rosenbrock4&lt; Value, Coefficients, Resizer &gt;</type></typedef>
<data-member name="stepper_order" specifiers="static"><type>const order_type</type></data-member>
<data-member name="error_order" specifiers="static"><type>const order_type</type></data-member>
<method-group name="public member functions">
<method name="order" cv="const"><type>order_type</type></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>const state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="xout"><paramtype>state_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="xerr"><paramtype>state_type &amp;</paramtype></parameter></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter><parameter name="xerr"><paramtype>state_type &amp;</paramtype></parameter></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>const state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="xout"><paramtype>state_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="prepare_dense_output"><type>void</type></method>
<method name="calc_state"><type>void</type><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="x"><paramtype>state_type &amp;</paramtype></parameter><parameter name="x_old"><paramtype>const state_type &amp;</paramtype></parameter><parameter name="t_old"><paramtype>time_type</paramtype></parameter><parameter name="x_new"><paramtype>const state_type &amp;</paramtype></parameter><parameter name="t_new"><paramtype>time_type</paramtype></parameter></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name=""><paramtype>void</paramtype></parameter></constructor>
<method-group name="protected member functions">
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="resize_x_err"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/rosenbrock4_controller.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="rosenbrock4_controller"><template>
      <template-type-parameter name="Stepper"/>
    </template><typedef name="stepper_type"><type>Stepper</type></typedef>
<typedef name="value_type"><type>stepper_type::value_type</type></typedef>
<typedef name="state_type"><type>stepper_type::state_type</type></typedef>
<typedef name="wrapped_state_type"><type>stepper_type::wrapped_state_type</type></typedef>
<typedef name="time_type"><type>stepper_type::time_type</type></typedef>
<typedef name="deriv_type"><type>stepper_type::deriv_type</type></typedef>
<typedef name="wrapped_deriv_type"><type>stepper_type::wrapped_deriv_type</type></typedef>
<typedef name="resizer_type"><type>stepper_type::resizer_type</type></typedef>
<typedef name="stepper_category"><type>controlled_stepper_tag</type></typedef>
<typedef name="controller_type"><type>rosenbrock4_controller&lt; Stepper &gt;</type></typedef>
<method-group name="public member functions">
<method name="error"><type>value_type</type><parameter name="x"><paramtype>const state_type &amp;</paramtype></parameter><parameter name="xold"><paramtype>const state_type &amp;</paramtype></parameter><parameter name="xerr"><paramtype>const state_type &amp;</paramtype></parameter></method>
<method name="last_error" cv="const"><type>value_type</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="try_step"><type>boost::numeric::odeint::controlled_step_result</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype></parameter></method>
<method name="try_step"><type>boost::numeric::odeint::controlled_step_result</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="sys"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>const state_type &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="xout"><paramtype>state_type &amp;</paramtype></parameter><parameter name="dt"><paramtype>time_type &amp;</paramtype></parameter></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype></parameter></method>
<method name="stepper"><type>stepper_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="stepper" cv="const"><type>const stepper_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
</method-group>
<constructor><parameter name="atol"><paramtype>value_type</paramtype><default>1.0e-6</default></parameter><parameter name="rtol"><paramtype>value_type</paramtype><default>1.0e-6</default></parameter><parameter name="stepper"><paramtype>const stepper_type &amp;</paramtype><default>stepper_type()</default></parameter></constructor>
<constructor><parameter name="atol"><paramtype>value_type</paramtype></parameter><parameter name="rtol"><paramtype>value_type</paramtype></parameter><parameter name="max_dt"><paramtype>time_type</paramtype></parameter><parameter name="stepper"><paramtype>const stepper_type &amp;</paramtype><default>stepper_type()</default></parameter></constructor>
<method-group name="protected member functions">
<method name="resize_m_xerr"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="resize_m_xnew"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/rosenbrock4_dense_output.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="rosenbrock4_dense_output"><template>
      <template-type-parameter name="ControlledStepper"/>
    </template><typedef name="controlled_stepper_type"><type>ControlledStepper</type></typedef>
<typedef name="unwrapped_controlled_stepper_type"><type>unwrap_reference&lt; controlled_stepper_type &gt;::type</type></typedef>
<typedef name="stepper_type"><type>unwrapped_controlled_stepper_type::stepper_type</type></typedef>
<typedef name="value_type"><type>stepper_type::value_type</type></typedef>
<typedef name="state_type"><type>stepper_type::state_type</type></typedef>
<typedef name="wrapped_state_type"><type>stepper_type::wrapped_state_type</type></typedef>
<typedef name="time_type"><type>stepper_type::time_type</type></typedef>
<typedef name="deriv_type"><type>stepper_type::deriv_type</type></typedef>
<typedef name="wrapped_deriv_type"><type>stepper_type::wrapped_deriv_type</type></typedef>
<typedef name="resizer_type"><type>stepper_type::resizer_type</type></typedef>
<typedef name="stepper_category"><type>dense_output_stepper_tag</type></typedef>
<typedef name="dense_output_stepper_type"><type>rosenbrock4_dense_output&lt; ControlledStepper &gt;</type></typedef>
<method-group name="public member functions">
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x0"><paramtype>const StateType &amp;</paramtype></parameter><parameter name="t0"><paramtype>time_type</paramtype></parameter><parameter name="dt0"><paramtype>time_type</paramtype></parameter></method>
<method name="do_step"><type>std::pair&lt; time_type, time_type &gt;</type><template>
          <template-type-parameter name="System"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter></method>
<method name="calc_state"><type>void</type><template>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="x"><paramtype>StateOut &amp;</paramtype></parameter></method>
<method name="calc_state"><type>void</type><template>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="x"><paramtype>const StateOut &amp;</paramtype></parameter></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype></parameter></method>
<method name="current_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="current_time" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="previous_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="previous_time" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="current_time_step" cv="const"><type>time_type</type><parameter name=""><paramtype>void</paramtype></parameter></method>
</method-group>
<constructor><parameter name="stepper"><paramtype>const controlled_stepper_type &amp;</paramtype><default>controlled_stepper_type()</default></parameter></constructor>
<method-group name="private member functions">
<method name="get_current_state"><type>state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_current_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_state"><type>state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_state" cv="const"><type>const state_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="toggle_current_state"><type>void</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/runge_kutta4.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="runge_kutta4"><template>
      <template-type-parameter name="State"><purpose><para>The state type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The value type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default><purpose><para>The type representing the time derivative of the state. </para></purpose></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The time representing the independent variable - the time. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><inherit access="public">boost::numeric::odeint::explicit_generic_rk&lt; StageCount, Order, State, Value, Deriv, Time, Algebra, Operations, Resizer &gt;</inherit><purpose>The classical Runge-Kutta stepper of fourth order. </purpose><description><para>The Runge-Kutta method of fourth order is one standard method for solving ordinary differential equations and is widely used, see also <ulink url="http://en.wikipedia.org/wiki/Runge%E2%80%93Kutta_methods">en.wikipedia.org/wiki/Runge-Kutta_methods</ulink> The method is explicit and fulfills the Stepper concept. Step size control or continuous output are not provided.</para><para>This class derives from explicit_stepper_base and inherits its interface via CRTP (current recurring template pattern). Furthermore, it derivs from <classname alt="boost::numeric::odeint::explicit_generic_rk">explicit_generic_rk</classname> which is a generic Runge-Kutta algorithm. For more details see explicit_stepper_base and <classname alt="boost::numeric::odeint::explicit_generic_rk">explicit_generic_rk</classname>.</para><para>
</para></description><typedef name="state_type"><type>stepper_base_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_base_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_base_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_base_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>stepper_base_type::resizer_type</type></typedef>
<method-group name="public member functions">
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. The result is updated out of place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Access to this step functionality is provided by explicit_stepper_base and <computeroutput>do_step_impl</computeroutput> should not be called directly. </purpose><description><para>
</para></description></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored inside explicit_stepper_base. </para></description></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::runge_kutta4">runge_kutta4</classname> class. This constructor can be used as a default constructor if the algebra has a default constructor. </purpose><description><para>
</para></description></constructor>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/runge_kutta4_classic.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="runge_kutta4_classic"><template>
      <template-type-parameter name="State"><purpose><para>The state type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The value type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default><purpose><para>The type representing the time derivative of the state. </para></purpose></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The time representing the independent variable - the time. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><inherit access="public">explicit_stepper_base</inherit><purpose>The classical Runge-Kutta stepper of fourth order. </purpose><description><para>The Runge-Kutta method of fourth order is one standard method for solving ordinary differential equations and is widely used, see also <ulink url="http://en.wikipedia.org/wiki/Runge%E2%80%93Kutta_methods">en.wikipedia.org/wiki/Runge-Kutta_methods</ulink> The method is explicit and fulfills the Stepper concept. Step size control or continuous output are not provided. This class implements the method directly, hence the generic Runge-Kutta algorithm is not used.</para><para>This class derives from explicit_stepper_base and inherits its interface via CRTP (current recurring template pattern). For more details see explicit_stepper_base.</para><para>
</para></description><typedef name="stepper_base_type"><type>explicit_stepper_base&lt; <classname>runge_kutta4_classic</classname>&lt; ... &gt;,... &gt;</type></typedef>
<typedef name="state_type"><type>stepper_base_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_base_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_base_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_base_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>stepper_base_type::resizer_type</type></typedef>
<method-group name="public member functions">
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. The result is updated out of place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Access to this step functionality is provided by explicit_stepper_base and <computeroutput>do_step_impl</computeroutput> should not be called directly. </purpose><description><para>
</para></description></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateType"/>
        </template><parameter name="x"><paramtype>const StateType &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored inside explicit_stepper_base. </para></description></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::runge_kutta4_classic">runge_kutta4_classic</classname> class. This constructor can be used as a default constructor if the algebra has a default constructor. </purpose><description><para>
</para></description></constructor>
<method-group name="private member functions">
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/runge_kutta_cash_karp54.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="runge_kutta_cash_karp54"><template>
      <template-type-parameter name="State"><purpose><para>The state type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The value type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default><purpose><para>The type representing the time derivative of the state. </para></purpose></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The time representing the independent variable - the time. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><inherit access="public">boost::numeric::odeint::explicit_error_generic_rk&lt; StageCount, Order, StepperOrder, ErrorOrder, State, Value, Deriv, Time, Algebra, Operations, Resizer &gt;</inherit><purpose>The Runge-Kutta Cash-Karp method. </purpose><description><para>The Runge-Kutta Cash-Karp method is one of the standard methods for solving ordinary differential equations, see <ulink url="http://en.wikipedia.org/wiki/Cash%E2%80%93Karp_methods">en.wikipedia.org/wiki/Cash-Karp_methods</ulink>. The method is explicit and fulfills the Error Stepper concept. Step size control is provided but continuous output is not available for this method.</para><para>This class derives from explicit_error_stepper_base and inherits its interface via CRTP (current recurring template pattern). Furthermore, it derivs from <classname alt="boost::numeric::odeint::explicit_error_generic_rk">explicit_error_generic_rk</classname> which is a generic Runge-Kutta algorithm with error estimation. For more details see explicit_error_stepper_base and <classname alt="boost::numeric::odeint::explicit_error_generic_rk">explicit_error_generic_rk</classname>.</para><para>
</para></description><typedef name="state_type"><type>stepper_base_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_base_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_base_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_base_type::operations_type</type></typedef>
<typedef name="resizer_typ"><type>stepper_base_type::resizer_type</type></typedef>
<method-group name="public member functions">
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="Err"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><parameter name="xerr"><paramtype>Err &amp;</paramtype><description><para>The result of the error estimation is written in xerr. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. The result is updated out-of-place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Futhermore, an estimation of the error is stored in <computeroutput>xerr</computeroutput>. <computeroutput>do_step_impl</computeroutput> is used by explicit_error_stepper_base. </purpose><description><para>
</para></description></method>
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. The result is updated out-of-place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Access to this step functionality is provided by explicit_stepper_base and <computeroutput>do_step_impl</computeroutput> should not be called directly. </purpose><description><para>
</para></description></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored inside explicit_stepper_base. </para></description></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::runge_kutta_cash_karp54">runge_kutta_cash_karp54</classname> class. This constructor can be used as a default constructor if the algebra has a default constructor. </purpose><description><para>
</para></description></constructor>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/runge_kutta_cash_karp54_classic.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="runge_kutta_cash_karp54_classic"><template>
      <template-type-parameter name="State"><purpose><para>The state type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The value type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default><purpose><para>The type representing the time derivative of the state. </para></purpose></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The time representing the independent variable - the time. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><inherit access="public">explicit_error_stepper_base</inherit><purpose>The Runge-Kutta Cash-Karp method implemented without the generic Runge-Kutta algorithm. </purpose><description><para>The Runge-Kutta Cash-Karp method is one of the standard methods for solving ordinary differential equations, see <ulink url="http://en.wikipedia.org/wiki/Cash%E2%80%93Karp_method">en.wikipedia.org/wiki/Cash-Karp_method</ulink>. The method is explicit and fulfills the Error Stepper concept. Step size control is provided but continuous output is not available for this method.</para><para>This class derives from explicit_error_stepper_base and inherits its interface via CRTP (current recurring template pattern). This class implements the method directly, hence the generic Runge-Kutta algorithm is not used.</para><para>
</para></description><typedef name="stepper_base_type"><type>explicit_error_stepper_base&lt; <classname>runge_kutta_cash_karp54_classic</classname>&lt; ... &gt;,... &gt;</type></typedef>
<typedef name="state_type"><type>stepper_base_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_base_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_base_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_base_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>stepper_base_type::resizer_type</type></typedef>
<method-group name="public member functions">
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="Err"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><parameter name="xerr"><paramtype>Err &amp;</paramtype><description><para>The result of the error estimation is written in xerr. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. </purpose><description><para>The result is updated out-of-place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Futhermore, an estimation of the error is stored in <computeroutput>xerr</computeroutput>. Access to this step functionality is provided by explicit_error_stepper_base and <computeroutput>do_step_impl</computeroutput> should not be called directly.</para><para>
</para></description></method>
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. The result is updated out-of-place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Access to this step functionality is provided by explicit_error_stepper_base and <computeroutput>do_step_impl</computeroutput> should not be called directly. </purpose><description><para>
</para></description></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored inside explicit_stepper_base. </para></description></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::runge_kutta_cash_karp54_classic">runge_kutta_cash_karp54_classic</classname> class. This constructor can be used as a default constructor if the algebra has a default constructor. </purpose><description><para>
</para></description></constructor>
<method-group name="private member functions">
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/runge_kutta_dopri5.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="runge_kutta_dopri5"><template>
      <template-type-parameter name="State"><purpose><para>The state type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The value type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default><purpose><para>The type representing the time derivative of the state. </para></purpose></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The time representing the independent variable - the time. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><inherit access="public">explicit_error_stepper_fsal_base</inherit><purpose>The Runge-Kutta Dormand-Prince 5 method. </purpose><description><para>The Runge-Kutta Dormand-Prince 5 method is a very popular method for solving ODEs, see <ulink url=""/>. The method is explicit and fulfills the Error Stepper concept. Step size control is provided but continuous output is available which make this method favourable for many applications.</para><para>This class derives from explicit_error_stepper_fsal_base and inherits its interface via CRTP (current recurring template pattern). The method possesses the FSAL (first-same-as-last) property. See explicit_error_stepper_fsal_base for more details.</para><para>
</para></description><typedef name="stepper_base_type"><type>explicit_error_stepper_fsal_base&lt; <classname>runge_kutta_dopri5</classname>&lt; ... &gt;,... &gt;</type></typedef>
<typedef name="state_type"><type>stepper_base_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_base_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_base_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_base_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>stepper_base_type::resizer_type</type></typedef>
<method-group name="public member functions">
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="DerivOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt_in"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. dxdt_in is not modified by this method </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dxdt_out"><paramtype>DerivOut &amp;</paramtype><description><para>The result of the new derivative at time t+dt. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt_in</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. The result is updated out-of-place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Furthermore, the derivative is update out-of-place, hence the input is assumed to be in <computeroutput>dxdt_in</computeroutput> and the output in <computeroutput>dxdt_out</computeroutput>. Access to this step functionality is provided by explicit_error_stepper_fsal_base and <computeroutput>do_step_impl</computeroutput> should not be called directly. </purpose><description><para>
</para></description></method>
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="DerivOut"/>
          <template-type-parameter name="Err"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt_in"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. dxdt_in is not modified by this method </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dxdt_out"><paramtype>DerivOut &amp;</paramtype><description><para>The result of the new derivative at time t+dt. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><parameter name="xerr"><paramtype>Err &amp;</paramtype><description><para>An estimation of the error. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt_in</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. The result is updated out-of-place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Furthermore, the derivative is update out-of-place, hence the input is assumed to be in <computeroutput>dxdt_in</computeroutput> and the output in <computeroutput>dxdt_out</computeroutput>. Access to this step functionality is provided by explicit_error_stepper_fsal_base and <computeroutput>do_step_impl</computeroutput> should not be called directly. An estimation of the error is calculated. </purpose><description><para>
</para></description></method>
<method name="calc_state" cv="const"><type>void</type><template>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="StateIn1"/>
          <template-type-parameter name="DerivIn1"/>
          <template-type-parameter name="StateIn2"/>
          <template-type-parameter name="DerivIn2"/>
        </template><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="x"><paramtype>StateOut &amp;</paramtype></parameter><parameter name="x_old"><paramtype>const StateIn1 &amp;</paramtype></parameter><parameter name="deriv_old"><paramtype>const DerivIn1 &amp;</paramtype></parameter><parameter name="t_old"><paramtype>time_type</paramtype></parameter><parameter name=""><paramtype>const StateIn2 &amp;</paramtype></parameter><parameter name="deriv_new"><paramtype>const DerivIn2 &amp;</paramtype></parameter><parameter name="t_new"><paramtype>time_type</paramtype></parameter><purpose>This method is used for continuous output and it calculates the state <computeroutput>x</computeroutput> at a time <computeroutput>t</computeroutput> from the knowledge of two states <computeroutput>old_state</computeroutput> and <computeroutput>current_state</computeroutput> at time points <computeroutput>t_old</computeroutput> and <computeroutput>t_new</computeroutput>. It also uses internal variables to calculate the result. Hence this method must be called after two successful <computeroutput>do_step</computeroutput> calls. </purpose></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored inside explicit_stepper_base. </para></description></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::runge_kutta_dopri5">runge_kutta_dopri5</classname> class. This constructor can be used as a default constructor if the algebra has a default constructor. </purpose><description><para>
</para></description></constructor>
<method-group name="private member functions">
<method name="resize_k_x_tmp_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="resize_dxdt_tmp_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/runge_kutta_fehlberg78.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="runge_kutta_fehlberg78"><template>
      <template-type-parameter name="State"><purpose><para>The state type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The value type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Deriv"><default>State</default><purpose><para>The type representing the time derivative of the state. </para></purpose></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The time representing the independent variable - the time. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; State &gt;::algebra_type</default><purpose><para>The algebra type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; State &gt;::operations_type</default><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><inherit access="public">boost::numeric::odeint::explicit_error_generic_rk&lt; StageCount, Order, StepperOrder, ErrorOrder, State, Value, Deriv, Time, Algebra, Operations, Resizer &gt;</inherit><purpose>The Runge-Kutta Fehlberg 78 method. </purpose><description><para>The Runge-Kutta Fehlberg 78 method is a standard method for high-precision applications. The method is explicit and fulfills the Error Stepper concept. Step size control is provided but continuous output is not available for this method.</para><para>This class derives from explicit_error_stepper_base and inherits its interface via CRTP (current recurring template pattern). Furthermore, it derivs from <classname alt="boost::numeric::odeint::explicit_error_generic_rk">explicit_error_generic_rk</classname> which is a generic Runge-Kutta algorithm with error estimation. For more details see explicit_error_stepper_base and <classname alt="boost::numeric::odeint::explicit_error_generic_rk">explicit_error_generic_rk</classname>.</para><para>
</para></description><typedef name="state_type"><type>stepper_base_type::state_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<typedef name="deriv_type"><type>stepper_base_type::deriv_type</type></typedef>
<typedef name="time_type"><type>stepper_base_type::time_type</type></typedef>
<typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>stepper_base_type::operations_type</type></typedef>
<typedef name="resizer_type"><type>stepper_base_type::resizer_type</type></typedef>
<method-group name="public member functions">
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
          <template-type-parameter name="Err"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><parameter name="xerr"><paramtype>Err &amp;</paramtype><description><para>The result of the error estimation is written in xerr. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. The result is updated out-of-place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Futhermore, an estimation of the error is stored in <computeroutput>xerr</computeroutput>. <computeroutput>do_step_impl</computeroutput> is used by explicit_error_stepper_base. </purpose><description><para>
</para></description></method>
<method name="do_step_impl"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateIn"/>
          <template-type-parameter name="DerivIn"/>
          <template-type-parameter name="StateOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ODE. It must fulfill the Simple System concept. </para></description></parameter><parameter name="in"><paramtype>const StateIn &amp;</paramtype><description><para>The state of the ODE which should be solved. in is not modified in this method </para></description></parameter><parameter name="dxdt"><paramtype>const DerivIn &amp;</paramtype><description><para>The derivative of x at t. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="out"><paramtype>StateOut &amp;</paramtype><description><para>The result of the step is written in out. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. The derivative <computeroutput>dxdt</computeroutput> of <computeroutput>in</computeroutput> at the time <computeroutput>t</computeroutput> is passed to the method. The result is updated out-of-place, hence the input is in <computeroutput>in</computeroutput> and the output in <computeroutput>out</computeroutput>. Access to this step functionality is provided by explicit_stepper_base and <computeroutput>do_step_impl</computeroutput> should not be called directly. </purpose><description><para>
</para></description></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored inside explicit_stepper_base. </para></description></parameter><purpose>Constructs the runge_kutta_cash_fehlberg78 class. This constructor can be used as a default constructor if the algebra has a default constructor. </purpose><description><para>
</para></description></constructor>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/stepper_categories.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<struct name="base_tag"><template>
      <template-type-parameter name="tag"/>
    </template></struct><struct-specialization name="base_tag"><template>
    </template><specialization><template-arg>controlled_stepper_tag</template-arg></specialization><typedef name="type"><type>controlled_stepper_tag</type></typedef>
</struct-specialization><struct-specialization name="base_tag"><template>
    </template><specialization><template-arg>dense_output_stepper_tag</template-arg></specialization><typedef name="type"><type>dense_output_stepper_tag</type></typedef>
</struct-specialization><struct-specialization name="base_tag"><template>
    </template><specialization><template-arg>error_stepper_tag</template-arg></specialization><typedef name="type"><type>stepper_tag</type></typedef>
</struct-specialization><struct-specialization name="base_tag"><template>
    </template><specialization><template-arg>explicit_controlled_stepper_fsal_tag</template-arg></specialization><typedef name="type"><type>controlled_stepper_tag</type></typedef>
</struct-specialization><struct-specialization name="base_tag"><template>
    </template><specialization><template-arg>explicit_controlled_stepper_tag</template-arg></specialization><typedef name="type"><type>controlled_stepper_tag</type></typedef>
</struct-specialization><struct-specialization name="base_tag"><template>
    </template><specialization><template-arg>explicit_error_stepper_fsal_tag</template-arg></specialization><typedef name="type"><type>stepper_tag</type></typedef>
</struct-specialization><struct-specialization name="base_tag"><template>
    </template><specialization><template-arg>explicit_error_stepper_tag</template-arg></specialization><typedef name="type"><type>stepper_tag</type></typedef>
</struct-specialization><struct-specialization name="base_tag"><template>
    </template><specialization><template-arg>stepper_tag</template-arg></specialization><typedef name="type"><type>stepper_tag</type></typedef>
</struct-specialization><struct name="controlled_stepper_tag"/><struct name="dense_output_stepper_tag"/><struct name="error_stepper_tag"><inherit access="public">boost::numeric::odeint::stepper_tag</inherit></struct><struct name="explicit_controlled_stepper_fsal_tag"><inherit access="public">boost::numeric::odeint::controlled_stepper_tag</inherit></struct><struct name="explicit_controlled_stepper_tag"><inherit access="public">boost::numeric::odeint::controlled_stepper_tag</inherit></struct><struct name="explicit_error_stepper_fsal_tag"><inherit access="public">boost::numeric::odeint::error_stepper_tag</inherit></struct><struct name="explicit_error_stepper_tag"><inherit access="public">boost::numeric::odeint::error_stepper_tag</inherit></struct><struct name="stepper_tag"/>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/symplectic_euler.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="symplectic_euler"><template>
      <template-type-parameter name="Coor"><purpose><para>The type representing the coordinates q. </para></purpose></template-type-parameter>
      <template-type-parameter name="Momentum"><default>Coor</default><purpose><para>The type representing the coordinates p. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The basic value type. Should be something like float, double or a high-precision type. </para></purpose></template-type-parameter>
      <template-type-parameter name="CoorDeriv"><default>Coor</default><purpose><para>The type representing the time derivative of the coordinate dq/dt. </para></purpose></template-type-parameter>
      <template-type-parameter name="MomentumDeriv"><default>Coor</default></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The type representing the time t. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; Coor &gt;::algebra_type</default><purpose><para>The algebra. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; Coor &gt;::operations_type</default><purpose><para>The operations. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy. </para></purpose></template-type-parameter>
    </template><inherit access="public">symplectic_nystroem_stepper_base</inherit><purpose>Implementation of the symplectic Euler method. </purpose><description><para>The method is of first order and has one stage. It is described HERE.</para><para>
</para></description><typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<method-group name="public member functions">
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored inside explicit_stepper_base. </para></description></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::symplectic_euler">symplectic_euler</classname>. This constructor can be used as a default constructor if the algebra has a default constructor. </purpose><description><para>
</para></description></constructor>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/symplectic_rkn_sb3a_m4_mclachlan.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="symplectic_rkn_sb3a_m4_mclachlan"><template>
      <template-type-parameter name="Coor"><purpose><para>The type representing the coordinates q. </para></purpose></template-type-parameter>
      <template-type-parameter name="Momentum"><default>Coor</default><purpose><para>The type representing the coordinates p. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The basic value type. Should be something like float, double or a high-precision type. </para></purpose></template-type-parameter>
      <template-type-parameter name="CoorDeriv"><default>Coor</default><purpose><para>The type representing the time derivative of the coordinate dq/dt. </para></purpose></template-type-parameter>
      <template-type-parameter name="MomentumDeriv"><default>Coor</default></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The type representing the time t. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; Coor &gt;::algebra_type</default><purpose><para>The algebra. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; Coor &gt;::operations_type</default><purpose><para>The operations. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy. </para></purpose></template-type-parameter>
    </template><inherit access="public">symplectic_nystroem_stepper_base</inherit><purpose>Implementation of the symmetric B3A Runge-Kutta Nystroem method of fifth order. </purpose><description><para>The method is of fourth order and has five stages. It is described HERE. This method can be used with multiprecision types since the coefficients are defined analytically.</para><para>ToDo: add reference to paper.</para><para>
</para></description><typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<method-group name="public member functions">
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored inside explicit_stepper_base. </para></description></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::symplectic_rkn_sb3a_m4_mclachlan">symplectic_rkn_sb3a_m4_mclachlan</classname>. This constructor can be used as a default constructor if the algebra has a default constructor. </purpose><description><para>
</para></description></constructor>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/symplectic_rkn_sb3a_mclachlan.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="symplectic_rkn_sb3a_mclachlan"><template>
      <template-type-parameter name="Coor"><purpose><para>The type representing the coordinates q. </para></purpose></template-type-parameter>
      <template-type-parameter name="Momentum"><default>Coor</default><purpose><para>The type representing the coordinates p. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The basic value type. Should be something like float, double or a high-precision type. </para></purpose></template-type-parameter>
      <template-type-parameter name="CoorDeriv"><default>Coor</default><purpose><para>The type representing the time derivative of the coordinate dq/dt. </para></purpose></template-type-parameter>
      <template-type-parameter name="MomentumDeriv"><default>Coor</default></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The type representing the time t. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; Coor &gt;::algebra_type</default><purpose><para>The algebra. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; Coor &gt;::operations_type</default><purpose><para>The operations. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy. </para></purpose></template-type-parameter>
    </template><inherit access="public">symplectic_nystroem_stepper_base</inherit><purpose>Implement of the symmetric B3A method of Runge-Kutta-Nystroem method of sixth order. </purpose><description><para>The method is of fourth order and has six stages. It is described HERE. This method cannot be used with multiprecision types since the coefficients are not defined analytically.</para><para>ToDo Add reference to the paper.</para><para>
</para></description><typedef name="algebra_type"><type>stepper_base_type::algebra_type</type></typedef>
<typedef name="value_type"><type>stepper_base_type::value_type</type></typedef>
<method-group name="public member functions">
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored inside explicit_stepper_base. </para></description></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::symplectic_rkn_sb3a_mclachlan">symplectic_rkn_sb3a_mclachlan</classname>. This constructor can be used as a default constructor if the algebra has a default constructor. </purpose><description><para>
</para></description></constructor>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
<header name="boost/numeric/odeint/stepper/velocity_verlet.hpp">
<namespace name="boost">
<namespace name="numeric">
<namespace name="odeint">
<class name="velocity_verlet"><template>
      <template-type-parameter name="Coor"><purpose><para>The type representing the coordinates. </para></purpose></template-type-parameter>
      <template-type-parameter name="Velocity"><default>Coor</default><purpose><para>The type representing the velocities. </para></purpose></template-type-parameter>
      <template-type-parameter name="Value"><default>double</default><purpose><para>The type value type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Acceleration"><default>Coor</default><purpose><para>The type representing the acceleration. </para></purpose></template-type-parameter>
      <template-type-parameter name="Time"><default>Value</default><purpose><para>The time representing the independent variable - the time. </para></purpose></template-type-parameter>
      <template-type-parameter name="TimeSq"><default>Time</default><purpose><para>The time representing the square of the time. </para></purpose></template-type-parameter>
      <template-type-parameter name="Algebra"><default>typename algebra_dispatcher&lt; Coor &gt;::algebra_type</default><purpose><para>The algebra. </para></purpose></template-type-parameter>
      <template-type-parameter name="Operations"><default>typename operations_dispatcher&lt; Coor &gt;::operations_type</default><purpose><para>The operations type. </para></purpose></template-type-parameter>
      <template-type-parameter name="Resizer"><default>initially_resizer</default><purpose><para>The resizer policy type. </para></purpose></template-type-parameter>
    </template><inherit access="public">algebra_stepper_base&lt; Algebra, Operations &gt;</inherit><purpose>The Velocity-Verlet algorithm. </purpose><description><para><ulink url="http://en.wikipedia.org/wiki/Verlet_integration">The Velocity-Verlet algorithm</ulink> is a method for simulation of molecular dynamics systems. It solves the ODE a=f(r,v',t) where r are the coordinates, v are the velocities and a are the accelerations, hence v = dr/dt, a=dv/dt.</para><para>
</para></description><typedef name="algebra_stepper_base_type"><type>algebra_stepper_base&lt; Algebra, Operations &gt;</type></typedef>
<typedef name="algebra_type"><type>algebra_stepper_base_type::algebra_type</type></typedef>
<typedef name="operations_type"><type>algebra_stepper_base_type::operations_type</type></typedef>
<typedef name="coor_type"><type>Coor</type></typedef>
<typedef name="velocity_type"><type>Velocity</type></typedef>
<typedef name="acceleration_type"><type>Acceleration</type></typedef>
<typedef name="state_type"><type>std::pair&lt; coor_type, velocity_type &gt;</type></typedef>
<typedef name="deriv_type"><type>std::pair&lt; velocity_type, acceleration_type &gt;</type></typedef>
<typedef name="wrapped_acceleration_type"><type>state_wrapper&lt; acceleration_type &gt;</type></typedef>
<typedef name="value_type"><type>Value</type></typedef>
<typedef name="time_type"><type>Time</type></typedef>
<typedef name="time_square_type"><type>TimeSq</type></typedef>
<typedef name="resizer_type"><type>Resizer</type></typedef>
<typedef name="stepper_category"><type>stepper_tag</type></typedef>
<typedef name="order_type"><type>unsigned short</type></typedef>
<data-member name="order_value" specifiers="static"><type>const order_type</type></data-member>
<method-group name="public member functions">
<method name="order" cv="const"><type>order_type</type><parameter name=""><paramtype>void</paramtype></parameter><description><para>
</para></description><returns><para>Returns the order of the stepper. </para>
</returns></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ordinary differential equation. It must fulfill the Second Order System concept. </para></description></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype><description><para>The state of the ODE which should be solved. The state is pair of Coor and Velocity. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. It transforms the result in-place. </purpose><description><para>It can be used like <programlisting language="c++">pair&lt; coordinates , velocities &gt; state;
stepper.do_step( sys , x , t , dt );
</programlisting></para><para>
</para></description></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ordinary differential equation. It must fulfill the Second Order System concept. </para></description></parameter><parameter name="x"><paramtype>const StateInOut &amp;</paramtype><description><para>The state of the ODE which should be solved. The state is pair of Coor and Velocity. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. It transforms the result in-place. </purpose><description><para>It can be used like <programlisting language="c++">pair&lt; coordinates , velocities &gt; state;
stepper.do_step( sys , x , t , dt );
</programlisting></para><para>
</para></description></method>
<method name="do_step"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="CoorIn"/>
          <template-type-parameter name="VelocityIn"/>
          <template-type-parameter name="AccelerationIn"/>
          <template-type-parameter name="CoorOut"/>
          <template-type-parameter name="VelocityOut"/>
          <template-type-parameter name="AccelerationOut"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function to solve, hence the r.h.s. of the ordinary differential equation. It must fulfill the Second Order System concept. </para></description></parameter><parameter name="qin"><paramtype>CoorIn const &amp;</paramtype></parameter><parameter name="pin"><paramtype>VelocityIn const &amp;</paramtype></parameter><parameter name="ain"><paramtype>AccelerationIn const &amp;</paramtype></parameter><parameter name="qout"><paramtype>CoorOut &amp;</paramtype></parameter><parameter name="pout"><paramtype>VelocityOut &amp;</paramtype></parameter><parameter name="aout"><paramtype>AccelerationOut &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The value of the time, at which the step should be performed. </para></description></parameter><parameter name="dt"><paramtype>time_type</paramtype><description><para>The step size. </para></description></parameter><purpose>This method performs one step. It transforms the result in-place. Additionally to the other methods the coordinates, velocities and accelerations are passed directly to do_step and they are transformed out-of-place. </purpose><description><para>It can be used like <programlisting language="c++">coordinates qin , qout;
velocities pin , pout;
accelerations ain, aout;
stepper.do_step( sys , qin , pin , ain , qout , pout , aout , t , dt );
</programlisting></para><para>
</para></description></method>
<method name="adjust_size"><type>void</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype><description><para>A state from which the size of the temporaries to be resized is deduced. </para></description></parameter><purpose>Adjust the size of all temporaries in the stepper manually. </purpose><description><para>
</para></description></method>
<method name="reset"><type>void</type><parameter name=""><paramtype>void</paramtype></parameter><purpose>Resets the internal state of this stepper. After calling this method it is safe to use all <computeroutput>do_step</computeroutput> method without explicitly initializing the stepper. </purpose></method>
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="AccelerationIn"/>
        </template><parameter name="ain"><paramtype>const AccelerationIn &amp;</paramtype></parameter><purpose>Initializes the internal state of the stepper. </purpose><description><para>
</para></description></method>
<method name="initialize"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="CoorIn"/>
          <template-type-parameter name="VelocityIn"/>
        </template><parameter name="system"><paramtype>System</paramtype><description><para>The system function for the next calls of <computeroutput>do_step</computeroutput>. </para></description></parameter><parameter name="qin"><paramtype>const CoorIn &amp;</paramtype><description><para>The current coordinates of the ODE. </para></description></parameter><parameter name="pin"><paramtype>const VelocityIn &amp;</paramtype><description><para>The current velocities of the ODE. </para></description></parameter><parameter name="t"><paramtype>time_type</paramtype><description><para>The current time of the ODE. </para></description></parameter><purpose>Initializes the internal state of the stepper. </purpose><description><para>This method is equivalent to <programlisting language="c++">Acceleration a;
system( qin , pin , a , t );
stepper.initialize( a );
</programlisting></para><para>
</para></description></method>
<method name="is_initialized" cv="const"><type>bool</type><parameter name=""><paramtype>void</paramtype></parameter><description><para>
</para></description><returns><para>Returns if the stepper is initialized. </para>
</returns></method>
</method-group>
<constructor><parameter name="algebra"><paramtype>const algebra_type &amp;</paramtype><default>algebra_type()</default><description><para>A copy of algebra is made and stored. </para></description></parameter><purpose>Constructs the <classname alt="boost::numeric::odeint::velocity_verlet">velocity_verlet</classname> class. This constructor can be used as a default constructor if the algebra has a default constructor. </purpose><description><para>
</para></description></constructor>
<method-group name="private member functions">
<method name="initialize_acc"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="CoorIn"/>
          <template-type-parameter name="VelocityIn"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="qin"><paramtype>const CoorIn &amp;</paramtype></parameter><parameter name="pin"><paramtype>const VelocityIn &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter></method>
<method name="do_step_v1"><type>void</type><template>
          <template-type-parameter name="System"/>
          <template-type-parameter name="StateInOut"/>
        </template><parameter name="system"><paramtype>System</paramtype></parameter><parameter name="x"><paramtype>StateInOut &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="dt"><paramtype>time_type</paramtype></parameter></method>
<method name="resize_impl"><type>bool</type><template>
          <template-type-parameter name="StateIn"/>
        </template><parameter name="x"><paramtype>const StateIn &amp;</paramtype></parameter></method>
<method name="get_current_acc"><type>acceleration_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_current_acc" cv="const"><type>const acceleration_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_acc"><type>acceleration_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="get_old_acc" cv="const"><type>const acceleration_type &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="toggle_current_acc"><type>void</type><parameter name=""><paramtype>void</paramtype></parameter></method>
</method-group>
</class>



















































</namespace>
</namespace>
</namespace>
</header>
</library-reference>