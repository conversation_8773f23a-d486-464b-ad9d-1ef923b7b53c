<shared_ptr>
	<id>1</id>
	<type>
		<string>reno_project</string>
	</type>
	<object>
		<reno_project>
			<config>
				<shared_ptr>
					<id>2</id>
					<type>
						<string>reno_config</string>
					</type>
					<object>
						<source>
							<string>../..;../../../../boost</string>
						</source>
						<match>
							<string>.*\.(cpp|hpp|h)$</string>
						</match>
					</object>
				</shared_ptr>
			</config>
			<layers>
				<shared_ptr>
					<id>3</id>
					<type>
						<string>reno_layer_map</string>
					</type>
					<object>
						<sorted>
							<size>7</size>
							<pair>
								<string>default</string>
								<shared_ptr>
									<id>4</id>
									<type>
										<string>reno_layer</string>
									</type>
									<object>
										<sorted>
											<size>75</size>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>5</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>AEDDD2FA4F47CEBD99444F1054D85AB8132748CF38D6634503D62E9C8AD5FE68</strong>
																		<weak>1378637100</weak>
																		<size>292</size>
																		<position>368</position>
																		<strong>892C0239798B84BA2E80DAA70BBEB7BE0B6086A1D0829D0E1937EC1D19E3FF20</strong>
																		<weak>3349881047</weak>
																		<size>89</size>
																		<position>197</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_api_function.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>errinfo_api_function</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>6</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost exception</string>
															</title>
															<file_name>
																<string>boost-exception</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>7</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>FFF4359EFC66EE6AA729B641F38B4020A55E83A1C099BCA59B1CA9A9875E7F79</strong>
																		<weak>366628170</weak>
																		<size>236</size>
																		<position>323</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_file_handle.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/errinfo_file_handle.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>8</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>Types</string>
															</title>
															<file_name>
																<string>types</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>9</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>3</size>
																		<strong>9516640DF38FC07A649AA4CAF21D4C4A6D6C2DF2B00E608F8D1C653C8D85E58B</strong>
																		<weak>406646287</weak>
																		<size>956</size>
																		<position>564</position>
																		<strong>8F508F9E7187AEA0E35A268B6F7B8E8A6C6588CCA01A2F3C5BBF1010699D8270</strong>
																		<weak>1555404133</weak>
																		<size>578</size>
																		<position>372</position>
																		<strong>38B566F2C6678B8724D18086A6F76E077DC2ADC1BB69A4B83BF0A2C3B7D31B50</strong>
																		<weak>2218658069</weak>
																		<size>31</size>
																		<position>143</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/detail/error_info_impl.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>error_info::value_type</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>10</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>Functions</string>
															</title>
															<file_name>
																<string>functions</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>11</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>BF7B46FEFA4E2DED7D652BFD40E94DD0B225ADA8D35E28FF4216F72812589835</strong>
																		<weak>422843600</weak>
																		<size>756</size>
																		<position>543</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/all.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/all.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>12</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>ADCD0B47BEBAA82DE5FDCACB0E9E8FF900527566EF9865ECD8C08B62067B4C66</strong>
																		<weak>1181168733</weak>
																		<size>14302</size>
																		<position>1027</position>
																		<strong>0066D4E6E6B189906E6DE04F08509F3737511701A1B1355B37511EC18E8371F4</strong>
																		<weak>2078296250</weak>
																		<size>305</size>
																		<position>1066</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>copy_exception</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>13</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>3</size>
																		<strong>2F10A76F9BA78353597A5E6F1373E8188DE7AEFDCE29BFD0105527B64B37D00E</strong>
																		<weak>1041541496</weak>
																		<size>4693</size>
																		<position>1606</position>
																		<strong>4FDA7B607488BB202B2AB72C17983031070085FB6B616F2B77320088BE08EB62</strong>
																		<weak>98930276</weak>
																		<size>3714</size>
																		<position>26</position>
																		<strong>28B2A7701322B20C8CF5D6074F9019FBEA2FB02F1A13E83632AA76C431798777</strong>
																		<weak>1206384617</weak>
																		<size>3087</size>
																		<position>628</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/diagnostic_information.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>diagnostic_information</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>14</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>790A065B0168021FAD180E996254E3CDBC0360D22D3FCB83C143416663D85402</strong>
																		<weak>1376868984</weak>
																		<size>262</size>
																		<position>323</position>
																		<strong>83E18B98A15DEF9418E38218D221D10DC093B915D630B51974CCD23A6E1EDC44</strong>
																		<weak>849683856</weak>
																		<size>98</size>
																		<position>158</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_nested_exception.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>errinfo_nested_exception</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>15</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>E0BE7EFCD5550582AB95C9EEDA6E68CA0F89B19838DA61876D42161E1EA4AE71</strong>
																		<weak>2587249979</weak>
																		<size>233</size>
																		<position>323</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_at_line.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/errinfo_at_line.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>16</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>15CF5BD93D20D62D659C11A69330B06E408398EA488BEF1FD45437AADCDB424E</strong>
																		<weak>1232553666</weak>
																		<size>214</size>
																		<position>345</position>
																		<strong>6262783847165581298EC9500031E6B7A97B2751A9CEF67C4794121A78142C58</strong>
																		<weak>3676119191</weak>
																		<size>90</size>
																		<position>118</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_file_handle.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>errinfo_file_handle</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>17</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>frequently asked questions</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>18</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>9A4ECF9A49A73AED83C1565CB8C67AE1519E8AFE6818F968B4C4733CB9E86CEF</strong>
																		<weak>1615599655</weak>
																		<size>68</size>
																		<position>321</position>
																		<strong>34F0583BC8DE767CE2D79721E1F956895E43E5397473B1050F59BE7E26C773DB</strong>
																		<weak>805836816</weak>
																		<size>66</size>
																		<position>1</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/error_info.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/error_info.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>19</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>3</size>
																		<strong>C6DDF7D02A058403B7BD295CF1561F167D92B7DA1DAC4EBE9F801955264180EB</strong>
																		<weak>1656366188</weak>
																		<size>5040</size>
																		<position>767</position>
																		<strong>507B2DA4184DD6A38FC6099F6454CDC96604C0C7B2C06A2955C78452F66526F8</strong>
																		<weak>457758605</weak>
																		<size>3872</size>
																		<position>889</position>
																		<strong>38AA79D330846BE1CF17285796F34A9DBB5A7E995963A55F9B46EB1DA6314610</strong>
																		<weak>542483318</weak>
																		<size>573</size>
																		<position>3084</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/info.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>exception/operator&lt;&lt;</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>20</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>3</size>
																		<strong>3EC508EAC996E7C8D4B1DDF49BF0B32096F8DEFD1BD1CD7FABBF0E0938255CA4</strong>
																		<weak>1239321455</weak>
																		<size>12162</size>
																		<position>543</position>
																		<strong>97095C7B5621CC7BAB278BE10B315FB44588DB1CF078F269E35499C0FD934AF0</strong>
																		<weak>2572216802</weak>
																		<size>2505</size>
																		<position>4436</position>
																		<strong>A94129EFD6ABBDDCD4CFDB671821F7DA103B2EA4455CF39E783D333C236D1C41</strong>
																		<weak>1035445969</weak>
																		<size>595</size>
																		<position>402</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/exception.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>exception::exception</string>
															</title>
															<file_name>
																<string>exception_constructors</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>21</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>Synopsis</string>
															</title>
															<file_name>
																<string>synopsis</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>22</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>8A5444CF9C854740F83F17EA2075478A983F7C0243DCE4E42551ECBF908C1392</strong>
																		<weak>4193409281</weak>
																		<size>322</size>
																		<position>992</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/throw_exception.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>configuration macros</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>23</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>ADCD0B47BEBAA82DE5FDCACB0E9E8FF900527566EF9865ECD8C08B62067B4C66</strong>
																		<weak>1181168733</weak>
																		<size>14302</size>
																		<position>1027</position>
																		<strong>10E31FFA267B250065A2630D0B7107862920D940AEA0A5499D5341A902AE01FF</strong>
																		<weak>1524325002</weak>
																		<size>368</size>
																		<position>13033</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>rethrow_exception</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>24</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>using virtual inheritance in exception types</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>25</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>56C5A51DE37A6E893DA3B25D69DB65E4593C7803C6E34112E1F95C93D6037A82</strong>
																		<weak>275305396</weak>
																		<size>5586</size>
																		<position>321</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/info.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/info.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>26</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>66E0BD9724AB83012F5B35D887E3313960DC0E69B94E0C03CA1F3C85A0D84A5C</strong>
																		<weak>2883671483</weak>
																		<size>311</size>
																		<position>306</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../example/logging.cpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>diagnostic information</string>
															</title>
															<file_name>
																<string>tutorial_diagnostic_information</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>27</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>1B4417301AE3C0338C22E6D497391F51ABD459E521E7DFCE59A6EEC1372D33C2</strong>
																		<weak>202224383</weak>
																		<size>1766</size>
																		<position>616</position>
																		<strong>E0A17503B42EE12F31548A7D20F89916D734CE88B30A1BF6F9FC2D1F83A8B6F4</strong>
																		<weak>3410340567</weak>
																		<size>1734</size>
																		<position>26</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/info_tuple.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>tuple/operator&lt;&lt;</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>28</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>3B52D5850D9664639CCF1D22FBD52F2EB99087BED704C3FE07FE185B38C0DD09</strong>
																		<weak>676740550</weak>
																		<size>15108</size>
																		<position>321</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception_ptr.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>29</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>ADCD0B47BEBAA82DE5FDCACB0E9E8FF900527566EF9865ECD8C08B62067B4C66</strong>
																		<weak>1181168733</weak>
																		<size>14302</size>
																		<position>1027</position>
																		<strong>EEDBDE62A278D2AF428D9D1ED2ABCFF06163BACD91E12DD033565C7043354B89</strong>
																		<weak>246173488</weak>
																		<size>248</size>
																		<position>1396</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>original_exception_type</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>30</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>transporting of exceptions between threads</string>
															</title>
															<file_name>
																<string>tutorial_exception_ptr</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>31</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>FC684D0DD5A9732B4130F2AB3DB6E0491D0F523E14B7FB738B2019EA2C7F8717</strong>
																		<weak>2229778754</weak>
																		<size>631</size>
																		<position>319</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../example/cloning_2.cpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>cloning and re-throwing an exception</string>
															</title>
															<file_name>
																<string>cloning_and_rethrowing</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>32</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>3EC508EAC996E7C8D4B1DDF49BF0B32096F8DEFD1BD1CD7FABBF0E0938255CA4</strong>
																		<weak>1239321455</weak>
																		<size>12162</size>
																		<position>543</position>
																		<strong>DF9EA87B0140AACF4422F1B76F6A6A409C15F32858BBBA85A35981A824C56BA9</strong>
																		<weak>1137981799</weak>
																		<size>192</size>
																		<position>11964</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/exception.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>enable_current_exception</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>33</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>197F3960CFF5CBDEF7BDA8D0DE60948A5328F229C6710FEDE656530A3116B29B</strong>
																		<weak>742102996</weak>
																		<size>475</size>
																		<position>1316</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/throw_exception.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>BOOST_THROW_EXCEPTION</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>34</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>3EC508EAC996E7C8D4B1DDF49BF0B32096F8DEFD1BD1CD7FABBF0E0938255CA4</strong>
																		<weak>1239321455</weak>
																		<size>12162</size>
																		<position>543</position>
																		<strong>17E691632123EB67BA67D590B49EB8094F462F5A10A66A1C5438E1867EF1478E</strong>
																		<weak>765399792</weak>
																		<size>77</size>
																		<position>7076</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/exception.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>exception::~exception</string>
															</title>
															<file_name>
																<string>exception_destructor</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>35</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>Headers</string>
															</title>
															<file_name>
																<string>headers</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>36</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/enable_current_exception.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>37</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>422CF2A57EA6763FBD2F319C4CDD8DD5ADF4493C699B50653015A362F71D4500</strong>
																		<weak>1282485161</weak>
																		<size>2161</size>
																		<position>321</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/info_tuple.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/info_tuple.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>38</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>2F10A76F9BA78353597A5E6F1373E8188DE7AEFDCE29BFD0105527B64B37D00E</strong>
																		<weak>1041541496</weak>
																		<size>4693</size>
																		<position>1606</position>
																		<strong>20B46D7510ED9F1F40CF3A80C97AE430628745D26173DE91E3D6CB6CEABDAA58</strong>
																		<weak>2572596214</weak>
																		<size>659</size>
																		<position>4028</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/diagnostic_information.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>diagnostic_information_what</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>39</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>F971041F60D19AFB8AA50440BC2A911633E5826FDED7B3E1CFC90D241D880C32</strong>
																		<weak>931174095</weak>
																		<size>3062</size>
																		<position>95</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/throw_exception.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/throw_exception.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>40</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>979343A73CAA7601AF159E6240A03038F47940F71F6DE85D6BA648B179921C35</strong>
																		<weak>2321681356</weak>
																		<size>939</size>
																		<position>321</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_errno.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/errinfo_errno.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>41</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>21027A2B73C9AA6FF083752A952D63BBA9B5FD68A3C8915965A7184EA62A5D61</strong>
																		<weak>1523356166</weak>
																		<size>537</size>
																		<position>623</position>
																		<strong>24256E1CE56594FB38D0630858B8947191827CFC57771E8727A6A56F76207454</strong>
																		<weak>665917505</weak>
																		<size>66</size>
																		<position>26</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_errno.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>errinfo_errno</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>42</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>F2E44174DE588C19C0172D82AD61322E6B6578ADBE2A631C6C8059CB84396D97</strong>
																		<weak>670214046</weak>
																		<size>684</size>
																		<position>321</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/current_exception_cast.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/current_exception_cast.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>43</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>756A81C65A938BEEAD9B576707145748A3DB3BF767CC77ADD5AACD549373856A</strong>
																		<weak>904132245</weak>
																		<size>744</size>
																		<position>363</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../example/info_tuple.cpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>adding grouped data to exceptions</string>
															</title>
															<file_name>
																<string>grouping_data</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>44</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>4EDD3DF2332B6D9D22AC9AD90B850ACC715A24DD466E675014CBED25C63C255F</strong>
																		<weak>4175717823</weak>
																		<size>328</size>
																		<position>323</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_type_info_name.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/errinfo_type_info_name.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>45</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>exception types as simple semantic tags</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>46</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>91CF203512705C8B2CDCBCD1439821CBF93CFC1A4C2EA2CA91F38DAA3F7720B2</strong>
																		<weak>1769665510</weak>
																		<size>1558</size>
																		<position>352</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../example/errinfos.cpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>errinfos example</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>47</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>8A8FAA48FF123031D5E51D50BC96D0AAC468112838058976B85AC6EED4A25C57</strong>
																		<weak>4201574956</weak>
																		<size>763</size>
																		<position>833</position>
																		<strong>AEA5C07CF015DDE792E061003F669239E7AADBD24BE554EB26706AD9B28B8C89</strong>
																		<weak>2503775994</weak>
																		<size>472</size>
																		<position>285</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/diagnostic_information.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>current_exception_diagnostic_information</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>48</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>307034E20863A00923777A60D9495B4076B7F917D5B97203025299060F0833E0</strong>
																		<weak>3948311309</weak>
																		<size>396</size>
																		<position>344</position>
																		<strong>F8ED2052577830AC0C515EC5932BB14445DD5DA714782281FCDB1776961FECB1</strong>
																		<weak>3880328768</weak>
																		<size>82</size>
																		<position>308</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_file_name.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>errinfo_file_name</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>49</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>transporting of arbitrary data to the catch site</string>
															</title>
															<file_name>
																<string>tutorial_transporting_data</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>50</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>3EC508EAC996E7C8D4B1DDF49BF0B32096F8DEFD1BD1CD7FABBF0E0938255CA4</strong>
																		<weak>1239321455</weak>
																		<size>12162</size>
																		<position>543</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/exception.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/exception.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>51</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>D32E0E4334CE0236B6EDB0EAC484B2DD595860E9FD53701EB5646D62C6A45D4E</strong>
																		<weak>1054670543</weak>
																		<size>866</size>
																		<position>306</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../example/error_info_2.cpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>adding of arbitrary data to active exception objects</string>
															</title>
															<file_name>
																<string>adding_data_later</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>52</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>3</size>
																		<strong>9516640DF38FC07A649AA4CAF21D4C4A6D6C2DF2B00E608F8D1C653C8D85E58B</strong>
																		<weak>406646287</weak>
																		<size>956</size>
																		<position>564</position>
																		<strong>8F508F9E7187AEA0E35A268B6F7B8E8A6C6588CCA01A2F3C5BBF1010699D8270</strong>
																		<weak>1555404133</weak>
																		<size>578</size>
																		<position>372</position>
																		<strong>98B33BE76679E3A4831241335CD5DFF6F634429F36BABF96C1D4DC2296C5ECC5</strong>
																		<weak>1584672077</weak>
																		<size>208</size>
																		<position>259</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/detail/error_info_impl.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>error_info::value</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>53</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>3EC508EAC996E7C8D4B1DDF49BF0B32096F8DEFD1BD1CD7FABBF0E0938255CA4</strong>
																		<weak>1239321455</weak>
																		<size>12162</size>
																		<position>543</position>
																		<strong>96DA9A7E983733685FB1D74C8ABF58EA409CCA42F8522A9775E15BC0C93DB87B</strong>
																		<weak>3668840260</weak>
																		<size>2638</size>
																		<position>4436</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/exception.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>exception</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;----&#10;!!!See Also:&#10;&#10;(:pagelist link=&quot;backlink&quot; except_tags=&quot;exception member noalso&quot; mod=&quot;w&quot; fmt=&quot;h&quot;:)</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>54</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>9516640DF38FC07A649AA4CAF21D4C4A6D6C2DF2B00E608F8D1C653C8D85E58B</strong>
																		<weak>406646287</weak>
																		<size>956</size>
																		<position>564</position>
																		<strong>8F508F9E7187AEA0E35A268B6F7B8E8A6C6588CCA01A2F3C5BBF1010699D8270</strong>
																		<weak>1555404133</weak>
																		<size>578</size>
																		<position>372</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/detail/error_info_impl.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>error_info</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>55</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>C6DDF7D02A058403B7BD295CF1561F167D92B7DA1DAC4EBE9F801955264180EB</strong>
																		<weak>1656366188</weak>
																		<size>5040</size>
																		<position>767</position>
																		<strong>6E325144EF4F41FA3A225EB30729101382C4E99B3D6160E307311E4B4E641010</strong>
																		<weak>1097215175</weak>
																		<size>161</size>
																		<position>422</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/info.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>error_info::error_info</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>56</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>790A065B0168021FAD180E996254E3CDBC0360D22D3FCB83C143416663D85402</strong>
																		<weak>1376868984</weak>
																		<size>262</size>
																		<position>323</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_nested_exception.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/errinfo_nested_exception.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>57</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>Motivation</string>
															</title>
															<file_name>
																<string>motivation</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also explicit=&quot;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-45</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>58</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>C95CEF2E9D0BAA1E950509471062916DB849A46A19F7692BA478030E79B338EB</strong>
																		<weak>1917376632</weak>
																		<size>706</size>
																		<position>408</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../example/enable_error_info.cpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>integrating boost exception in existing exception class hierarchies</string>
															</title>
															<file_name>
																<string>tutorial_enable_error_info</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-17</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&quot;:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-58</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>59</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>21A43755562CB78B3FFCC49F66B457C1FCD659EE98F25BBFA8DDE453EB89DF0E</strong>
																		<weak>2576704708</weak>
																		<size>337</size>
																		<position>323</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_api_function.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/errinfo_api_function.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>60</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>4D7009F0868C1DF4898EC6ECF9AD2CFEA98E8653B01B066106761807405D4C22</strong>
																		<weak>1416707852</weak>
																		<size>3107</size>
																		<position>543</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/get_error_info.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/get_error_info.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>61</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>Macros</string>
															</title>
															<file_name>
																<string>macros</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>62</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>3</size>
																		<strong>ADCD0B47BEBAA82DE5FDCACB0E9E8FF900527566EF9865ECD8C08B62067B4C66</strong>
																		<weak>1181168733</weak>
																		<size>14302</size>
																		<position>1027</position>
																		<strong>2482DDAF6A7E31CF75E93B993C86D9814A0B8899B68E555B23D411BD195FE270</strong>
																		<weak>1574307697</weak>
																		<size>8349</size>
																		<position>4068</position>
																		<strong>7E162EB263369C2C485D5F69CA1A4FADD3EEBC6EB78CE7A767A8615885178079</strong>
																		<weak>1179386730</weak>
																		<size>5404</size>
																		<position>2935</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>current_exception</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>63</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>3EC508EAC996E7C8D4B1DDF49BF0B32096F8DEFD1BD1CD7FABBF0E0938255CA4</strong>
																		<weak>1239321455</weak>
																		<size>12162</size>
																		<position>543</position>
																		<strong>F3FB15CD82336271C6E875BC620385322777D16F0B7C233300783CE35710CCBF</strong>
																		<weak>3292878997</weak>
																		<size>282</size>
																		<position>9470</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/exception.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>enable_error_info</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>64</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>0CA48A4674CA9C409FF164D9A1B261FB48B0916C0EA387DF2F00DC4637E769BD</strong>
																		<weak>348807582</weak>
																		<size>6078</size>
																		<position>321</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/diagnostic_information.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/diagnostic_information.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>65</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>27ED18F9B6131B084FEF0C9F932B7027AF449E378B5FD7973CD6642263FCAF27</strong>
																		<weak>2867102400</weak>
																		<size>404</size>
																		<position>307</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../example/cloning_1.cpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>using enable_current_exception at the time of the throw</string>
															</title>
															<file_name>
																<string>using_enable_cloning</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>66</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>66EFC83C830F0B00D0C9399B475099072E2674B3C694F9152645A33E3D7AC303</strong>
																		<weak>561674611</weak>
																		<size>417</size>
																		<position>323</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_file_name.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/errinfo_file_name.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>67</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>FD7792C2929DD7B6BD613636FD0C574D002286E33811BA109B57B9C4D790D340</strong>
																		<weak>1830643656</weak>
																		<size>1244</size>
																		<position>1793</position>
																		<strong>BAE73EEDFF4059A7561888B4BA054DFA033F0967727630270F2C0D4EB918B88D</strong>
																		<weak>3168166030</weak>
																		<size>1222</size>
																		<position>21</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/throw_exception.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>throw_exception</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>68</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>D58AD357499A5A09FB5D12397CFFC2FFD412AC8A307ABB59C9BC53ACCA3B959D</strong>
																		<weak>2209414553</weak>
																		<size>2926</size>
																		<position>724</position>
																		<strong>49F40FF20D66B205C908A8F10BC61DE1BC571E4917A5BD0B4115E3F7FE3923FA</strong>
																		<weak>638776689</weak>
																		<size>2894</size>
																		<position>26</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/get_error_info.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>get_error_info</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>69</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>ADCD0B47BEBAA82DE5FDCACB0E9E8FF900527566EF9865ECD8C08B62067B4C66</strong>
																		<weak>1181168733</weak>
																		<size>14302</size>
																		<position>1027</position>
																		<strong>1D5E771272B020A105B69E186517499873571F62AFF9D48F130C952CFAA12FA3</strong>
																		<weak>2841506107</weak>
																		<size>891</size>
																		<position>173</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>exception_ptr</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>70</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>EEA69AA1E84CB2B7C903A3F4C236D0A233D03DBA4BA1D3B97D959918F3B30E09</strong>
																		<weak>2728032055</weak>
																		<size>406</size>
																		<position>344</position>
																		<strong>EE695B95A2499B66980754857E184776F1DE7224372A5F5153B6DF94E621A89B</strong>
																		<weak>1009590890</weak>
																		<size>92</size>
																		<position>308</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_file_open_mode.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>errinfo_file_open_mode</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>71</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>6FB85B536F965F137409D5B5D34786DCBF0B9957A7C251D271B717A1156B823D</strong>
																		<weak>1090406464</weak>
																		<size>362</size>
																		<position>543</position>
																		<strong>D16DAEA8B1792A019AF7FCA362FDC6EFD381AF4C43C076A01C029ECE51F994A6</strong>
																		<weak>3172941848</weak>
																		<size>330</size>
																		<position>26</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/current_exception_cast.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>current_exception_cast</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>72</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>DB156E6A8ACB9FB90C8FB110FC25A5FEB14A619F82EEC47FF913373592E5CC3E</strong>
																		<weak>240075319</weak>
																		<size>6209</size>
																		<position>412</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../example/example_io.cpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>diagnostic_information example</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>73</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>E0BE7EFCD5550582AB95C9EEDA6E68CA0F89B19838DA61876D42161E1EA4AE71</strong>
																		<weak>2587249979</weak>
																		<size>233</size>
																		<position>323</position>
																		<strong>92AB508A6B1C2A62CB2ACED423BD04BB5A471674B5A51BFC1E6FB1F5C92AF9AA</strong>
																		<weak>2372475309</weak>
																		<size>70</size>
																		<position>157</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_at_line.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>errinfo_at_line</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>74</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>page index</string>
															</title>
															<file_name>
																<string>page_idx</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>75</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>977045132A532A0071B0B53F737D85367CE9A331402F96790E45B3B6F2FC88A6</strong>
																		<weak>1875939463</weak>
																		<size>529</size>
																		<position>382</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../example/error_info_1.cpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>adding of arbitrary data at the point of the throw</string>
															</title>
															<file_name>
																<string>adding_data_at_throw</string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>76</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>1</size>
																		<strong>DE766B811244919E8E1EA54FC747A8487BCE57F1AB592932640FC90428B617A5</strong>
																		<weak>414875037</weak>
																		<size>427</size>
																		<position>323</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_file_open_mode.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/errinfo_file_open_mode.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>77</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>3</size>
																		<strong>ADCD0B47BEBAA82DE5FDCACB0E9E8FF900527566EF9865ECD8C08B62067B4C66</strong>
																		<weak>1181168733</weak>
																		<size>14302</size>
																		<position>1027</position>
																		<strong>97DB2EDAA38019314BA1A582664F8950F5208310F14BAB94E1880AE2C5F00CD4</strong>
																		<weak>3076716310</weak>
																		<size>959</size>
																		<position>2974</position>
																		<strong>1760DA943E0DCAE6DDB000F3C08D6E6F5F8AEDBBEAC7CAA84A2ED60BFA4B0E1A</strong>
																		<weak>702729709</weak>
																		<size>815</size>
																		<position>145</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>unknown_exception</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>78</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>0</size>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>1</empty>
																	</path>
																</file>
															</hook>
															<title>
																<string>boost/exception/enable_error_info.hpp</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>79</id>
														<type>
															<string>reno_context</string>
														</type>
														<object>
															<hook>
																<hook>
																	<stream_hook_path>
																		<size>2</size>
																		<strong>126A895281064E2195458B8A47CD73DB7E3BE3608F250925E07AF4230CBDDE1D</strong>
																		<weak>4231421785</weak>
																		<size>307</size>
																		<position>344</position>
																		<strong>16179B125E2BC6D993FBE4BA5E9A96DBAE43CA1443C7D281B659D020B6725983</strong>
																		<weak>1126376090</weak>
																		<size>92</size>
																		<position>209</position>
																	</stream_hook_path>
																</hook>
																<file>
																	<path>
																		<empty>0</empty>
																		<string>../../../../boost/exception/errinfo_type_info_name.hpp</string>
																		<type>0</type>
																		<base>0</base>
																	</path>
																</file>
															</hook>
															<title>
																<string>errinfo_type_info_name</string>
															</title>
															<file_name>
																<string></string>
															</file_name>
														</object>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include include:)&#10;(:auto also:)&#10;</string>
											</pair>
										</sorted>
									</object>
								</shared_ptr>
							</pair>
							<pair>
								<string>def</string>
								<shared_ptr>
									<id>80</id>
									<type>
										<string>reno_layer</string>
									</type>
									<object>
										<sorted>
											<size>75</size>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-5</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@typedef (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct errinfo_api_function_,char const *&gt; (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-5</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:);@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-6</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-7</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-8</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-9</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-10</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-11</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-12</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-14</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>[@typedef (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct errinfo_nested_exception_,(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt; (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-14</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:);@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-15</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-16</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@typedef (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct errinfo_file_handle_,weak_ptr&lt;FILE&gt; &gt; (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-16</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:);@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-17</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-20</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-21</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-22</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-23</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-24</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-26</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-27</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-29</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-30</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-31</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-34</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-35</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-36</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-37</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-38</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-39</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@typedef </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&lt;struct errinfo_errno_,int&gt; </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>;@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-42</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-43</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-44</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-45</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-46</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-47</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-48</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@typedef (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct errinfo_file_name_,std::string&gt; (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-48</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:);@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-49</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-51</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-52</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>[@class&#10;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;    {&#10;    protected:&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-20</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl pre_indent=&quot;4&quot;:)&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-34</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl pre_indent=&quot;4&quot;:)&#10;    };@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<size>9</size>
												<variant>2</variant>
												<string>[@template &lt;class Tag,class T&gt;&#10;class&#10;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;    {&#10;    public:&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-9</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl pre_indent=&quot;4&quot;:)&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-55</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl pre_indent=&quot;4&quot;:)&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-52</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl pre_indent=&quot;4&quot;:)&#10;    };@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-55</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-56</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-57</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-58</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-59</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-60</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-61</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-64</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-65</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-66</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-70</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@typedef </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&lt;struct errinfo_file_open_mode_,std::string&gt; </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-70</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>;@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-71</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-72</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-73</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@typedef (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct errinfo_at_line_,int&gt; (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-73</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:);@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-74</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-75</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-76</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-77</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-78</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-79</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@typedef </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&lt;struct errinfo_type_info_name_,std::string&gt; </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-79</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>;@]&#10;</string>
											</pair>
										</sorted>
									</object>
								</shared_ptr>
							</pair>
							<pair>
								<string>api</string>
								<shared_ptr>
									<id>81</id>
									<type>
										<string>reno_layer</string>
									</type>
									<object>
										<sorted>
											<size>75</size>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-5</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-6</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-7</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-16</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> def:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-8</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-9</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-10</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-11</id>
													</shared_ptr>
												</weak_ptr>
												<size>33</size>
												<variant>2</variant>
												<string>[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-64</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-60</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-37</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-59</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-15</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-7</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-66</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-76</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-44</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#ifndef (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-22</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|BOOST_NO_EXCEPTIONS:)&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-56</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#endif@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-12</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-14</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-15</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-73</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> def:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-16</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-17</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-20</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-21</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-22</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-23</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-24</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> def:)&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-26</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-27</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<size>13</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-77</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-29</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-12</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-23</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-29</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-30</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-31</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-34</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-35</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-36</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-37</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-27</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-38</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-39</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> def:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-42</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-71</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-43</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-44</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-79</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> def:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-45</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-46</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-47</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-48</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-49</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<size>11</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> def:)&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)&#10;&#10;typedef (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct throw_function_,char const *&gt; throw_function;&#10;typedef (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct throw_file_,char const *&gt; throw_file;&#10;typedef (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct throw_line_,int&gt; throw_line;@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-51</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-52</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-55</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-56</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-14</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> def:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-57</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-58</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-59</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-5</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> def:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-60</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-61</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-64</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-38</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-47</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-65</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-66</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-48</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> def:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-70</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-71</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-72</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-73</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-74</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-75</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-76</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-70</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> def:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-77</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-78</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-79</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
										</sorted>
									</object>
								</shared_ptr>
							</pair>
							<pair>
								<string>decl</string>
								<shared_ptr>
									<id>82</id>
									<type>
										<string>reno_layer</string>
									</type>
									<object>
										<sorted>
											<size>75</size>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-5</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-6</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-7</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-8</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-9</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@typedef T (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-9</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:);@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-10</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-11</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-12</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@template &lt;class T&gt;&#10;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-12</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)( T const &amp; e );@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>[@template &lt;class E&gt;&#10;std::string (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)( E const &amp; e, bool verbose=true );&#10;&#10;std::string (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)( (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) const &amp; p, bool verbose=true );@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-14</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-15</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-16</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-17</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@template &lt;class E, class Tag, class T&gt;&#10;E const &amp; (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;/&quot;:)( E const &amp; x, (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;Tag,T&gt; const &amp; v );@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-20</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>[@(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-20</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:)();&#10;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-20</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:)( (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) const &amp; x );@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-21</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-22</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-23</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@void (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-23</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)( (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) const &amp; ep );</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-24</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-26</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-27</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>[@template &lt;class E, class Tag1, class T1, ..., class TagN, class TN&gt;&#10;E const &amp; (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-27</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;/&quot;:)( E const &amp; x,&#10;    (:link http://www.boost.org/libs/tuple/doc/tuple_users_guide.html|tuple:)&lt;&#10;        (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;Tag1,T1&gt;,&#10;        ...,&#10;        (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;TagN,TN&gt; &gt; const &amp; v );@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-29</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@typedef (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct tag_original_exception_type,std::type_info const *&gt; </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-29</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>;@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-30</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-31</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@template &lt;class T&gt;&#10;---unspecified--- (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)( T const &amp; e );@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<size>21</size>
												<variant>2</variant>
												<string>[@#if !defined( BOOST_EXCEPTION_DISABLE )&#10;    #include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;    #include &lt;boost/current_function.hpp&gt;&#10;    #define (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(x)\&#10;        ::boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)( ::boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(x) &lt;&lt;\&#10;        ::boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|throw_function:)((:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-22</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|BOOST_THROW_EXCEPTION_CURRENT_FUNCTION:)) &lt;&lt;\&#10;        ::boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|throw_file:)(__FILE__) &lt;&lt;\&#10;        ::boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|throw_line:)((int)__LINE__) )&#10;#else&#10;    #define (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(x) ::boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(x)&#10;#endif@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-34</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-34</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:)();@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-35</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-36</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-37</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-38</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@char const * (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-38</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)( boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) const &amp; e, bool verbose=true ) throw();@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-39</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-42</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-43</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-44</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-45</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-46</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-47</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@std::string (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-47</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)();@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-48</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-49</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-51</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-52</id>
													</shared_ptr>
												</weak_ptr>
												<size>9</size>
												<variant>2</variant>
												<string>[@(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-9</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:) const &amp; (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-52</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:)() const;&#10;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-9</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:) &amp; (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-52</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:)();@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@class (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:);@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@template &lt;class Tag,class T&gt;&#10;class (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:);@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-55</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-55</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:)( (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-9</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:) const &amp; v );@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-56</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-57</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-58</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-59</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-60</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-61</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)();@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@template &lt;class T&gt;&#10;---unspecified--- (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)( T const &amp; x );@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-64</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-65</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-66</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@#ifdef BOOST_NO_EXCEPTIONS&#10;    void (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)( std::exception const &amp; e ); // user defined&#10;#else&#10;    template &lt;class E&gt;&#10;    void (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)( E const &amp; e );&#10;#endif@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>[@template &lt;class ErrorInfo,class E&gt;&#10;typename ErrorInfo::(:link error_info::value_type mod=&quot;m&quot;:) const * (:link get_error_info:)( E const &amp; x );&#10;&#10;template &lt;class ErrorInfo,class E&gt;&#10;typename ErrorInfo::(:link error_info::value_type mod=&quot;m&quot;:) * (:link get_error_info:)( E &amp; x );@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@typedef ---unspecified--- (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:);@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-70</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-71</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@template &lt;class E&gt;&#10;E * </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-71</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>();@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-72</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-73</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-74</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-75</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-76</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-77</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@class&#10;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-77</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:):&#10;    public std::exception&#10;    public boost::</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&#10;    {&#10;    ---unspecified---&#10;    };@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-78</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-79</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
										</sorted>
									</object>
								</shared_ptr>
							</pair>
							<pair>
								<string>include</string>
								<shared_ptr>
									<id>83</id>
									<type>
										<string>reno_layer</string>
									</type>
									<object>
										<sorted>
											<size>75</size>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-5</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;This type is designed to be used as a standard (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) instance for transporting the name of a relevant API function (which does not use exceptions to report errors) in exceptions deriving from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;!!!Example:&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-46</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-6</id>
													</shared_ptr>
												</weak_ptr>
												<size>69</size>
												<variant>2</variant>
												<string>!!Introduction&#10;&#10;The purpose of Boost Exception is to ease the design of exception class hierarchies and to help write exception handling and error reporting code.&#10;&#10;It supports transporting of arbitrary data to the catch site, which is otherwise tricky due to the no-throw requirements (15.5.1) for exception types. Data can be added to any exception object, either directly in the throw-expression (15.1), or at a later time as the exception object propagates up the call stack.&#10;&#10;The ability to add data to exception objects after they have been passed to throw is important, because often some of the information needed to handle an exception is unavailable in the context where the failure is detected. &#10;&#10;Boost Exception also supports (:link http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2179.html|N2179:)-style (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-30</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|copying:) of exception objects, implemented non-intrusively and automatically by the boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) function.&#10;&#10;!!Contents&#10;&#10;#(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-57</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;#Tutorial&#10;##(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-49</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;w&quot;:)&#10;##(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-58</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;w&quot;:)&#10;##(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-30</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;w&quot;:)&#10;##(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-45</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;w&quot;:)&#10;##(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-24</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;w&quot;:)&#10;##(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-26</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;w&quot;:)&#10;#Documentation&#10;##Class (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;##Throwing Exceptions&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;##Transporting of Arbitrary Data to the Catch Site&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-27</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;##(:link http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2179.html|N2179:) Transporting of Exceptions between Threads&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-12</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-23</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-77</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;##Diagnostic Information&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;###(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-47</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;##(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-71</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;#API&#10;##(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-21</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;##(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-35</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;##(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-8</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;##(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-10</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;##(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-61</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;##(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-22</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;w&quot;:)&#10;#(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-17</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;w&quot;:)&#10;#(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-74</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;w&quot;:)&#10;&#10;!!!Acknowledgements&#10;&#10;Thanks to Peter Dimov for his continuing help. Also thanks to Tobias Schwinger, Tom Brinkman, Pavel Vozenilek and everyone who participated in the review process.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-7</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-8</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;(:pagelist fmt=&quot;index&quot; tags=&quot;type&quot;:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-9</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Definition:&#10;&#10;The expression </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&lt;Tag,T&gt;::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-9</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:) evaluates to T.</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-10</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;(:pagelist fmt=&quot;index&quot; tags=&quot;function&quot;:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-11</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-12</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Effects:&#10;&#10;As if&#10;&#10;[@try&#10;    {&#10;    throw </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>(e);&#10;    }&#10;catch(...)&#10;    {&#10;    return (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)();&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<size>39</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Returns:&#10;&#10;A string value that contains varying amount of diagnostic information about the passed object:&#10;&#10;* If E can be statically converted to either boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) or to std::exception, dynamic_cast is used to access both the boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) and std::exception subobjects of e; otherwise, the boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) template is not available.&#10;* The returned value contains the string representations of all (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) objects stored in the boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) subobject through (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;/&quot;:).&#10;* In addition, if verbose is true, it contains other diagnostic information relevant to the exception, including the string returned by std::exception::what().&#10;&#10;The string representation of each (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) object is deduced by an unqualified call to to_string(x), where x is of type (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;Tag,T&gt;, for which Boost Exception defines a generic overload. It converts x.(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-52</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:)() to string, attempting to bind (at the time the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;Tag,T&gt; template is instantiated) the following functions in order:&#10;&#10;#Unqualified call to to_string(x.(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-52</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:)()) (the return value is expected to be of type std::string.)&#10;#Unqualified call to s &lt;&lt; x.(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-52</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;m&quot;:)(), where s is a std::ostringstream.&#10;&#10;The first successfully bound function is used at the time (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) is called; if both overload resolutions are unsuccessful, the system is unable to convert the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) object to string, and &apos;&apos;an unspecified stub string value is used without issuing a compile error.&apos;&apos;&#10;&#10;The (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) overload of (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) is equivalent to:&#10;&#10;[@if( p )&#10;    try&#10;        {&#10;        (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-23</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(p);&#10;        }&#10;    catch(...)&#10;        {&#10;        return (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-47</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(verbose);&#10;        }&#10;else return &lt;unspecified-string-value&gt;;@]&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-72</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-14</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;This type is designed to be used as a standard (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) instance for transporting (in exceptions deriving from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)) an </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> to an exception nested into another exception.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-15</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-16</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;This type is designed to be used as a standard (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) instance for transporting a relevant FILE pointer managed by a boost::shared_ptr&lt;FILE&gt; in exceptions deriving from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;!!!Example:&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-46</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-17</id>
													</shared_ptr>
												</weak_ptr>
												<size>81</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!What is the cost of calling boost::throw_exception?&#10;&#10;The cost is that boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) is added as a base of the exception emitted by boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) (unless the passed type already derives from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).)&#10;&#10;Calling boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) does not cause dynamic memory allocations.&#10;&#10;!!!What is the cost of BOOST_THROW_EXCEPTION?&#10;&#10;In addition to calling boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:), </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> invokes __FILE__, __LINE__ and the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-22</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|BOOST_THROW_EXCEPTION_CURRENT_FUNCTION:) macros. The space required to store the information is already included in sizeof(boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)).&#10;&#10;Calling </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> does not cause dynamic memory allocations.&#10;&#10;!!!Should I use boost::throw_exception or BOOST_THROW_EXCEPTION or just throw?&#10;&#10;The benefit of calling boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) instead of using throw directly is that it ensures that the emitted exception derives from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) and that it is compatible with boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;The (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) macro also results in a call to boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:), but in addition it records in the exception object the __FILE__ and __LINE__ of the throw, as well as the pretty name of the function that throws. This enables boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) to compose a more useful, if not user-friendly message.&#10;&#10;Typical use of boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) is:&#10;&#10;[@catch(...)&#10;    {&#10;    std::cerr &lt;&lt;&#10;        &quot;Unexpected exception, diagnostic information follows:\n&quot; &lt;&lt;&#10;        </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-47</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>();&#10;    }@]&#10;&#10;This is a possible message it may display -- the information in the first line is only available if (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) was used to throw:&#10;&#10;[@example_io.cpp(70): Throw in function class boost::shared_ptr&lt;struct _iobuf&gt; __cdecl my_fopen(const char *,const char *)&#10;Dynamic exception type: class boost::exception_detail::clone_impl&lt;class fopen_error&gt;&#10;std::exception::what: example_io error&#10;[struct boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-5</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)_ *] = fopen&#10;[struct boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)_ *] = 2, &quot;No such file or directory&quot;&#10;[struct boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-48</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)_ *] = tmp1.txt&#10;[struct boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-70</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)_ *] = rb@]&#10;&#10;In some development environments, the first line in that message can be clicked to show the location of the throw in the debugger, so it&apos;s easy to set a break point and run again to see the unexpected throw in the context of its call stack.&#10;&#10;!!!Why doesn&apos;t boost::exception derive from std::exception?&#10;&#10;Despite that (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-24</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|virtual inheritance should be used in deriving from base exception types:), quite often exception types (including the ones defined in the standard library) don&apos;t derive from std::exception virtually.&#10;&#10;If boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) derives from std::exception, using the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) function with such user-defined types would introduce dangerous ambiguity which would break all catch(std::exception &amp;) statements.&#10;&#10;Of course, boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) should not be used to replace std::exception as a base type in exception type hierarchies. Instead, it should be included as a virtual base, in addition to std::exception (which should probably also be derived virtually.)&#10;&#10;!!!Why is boost::exception abstract?&#10;&#10;To prevent exception-neutral contexts from erroneously erasing the type of the original exception when adding (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) to an active exception object:&#10;&#10;[@catch( boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) &amp; e )&#10;    {&#10;    e (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|&lt;&lt;:) foo_info(foo);&#10;    throw e; //Compile error: boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) is abstract&#10;    }@]&#10;&#10;The correct code is:&#10;&#10;[@catch( boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) &amp; e )&#10;    {&#10;    e (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|&lt;&lt;:) foo_info(foo);&#10;    throw; //Okay, re-throwing the original exception object.&#10;    }@]&#10;&#10;!!!Why use operator&lt;&lt; overload for adding info to exceptions?&#10;&#10;Before throwing an object of type that derives from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:), it is often desirable to add one or more (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) objects in it. The syntactic sugar provided by (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;/&quot;:) allows this to be done directly in a throw expression:&#10;&#10;[@throw error() (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|&lt;&lt;:) foo_info(foo) (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|&lt;&lt;:) bar_info(bar);@]&#10;&#10;!!!Why is operator&lt;&lt; allowed to throw?&#10;&#10;This question is referring to the following issue. Consider this throw statement example:&#10;&#10;[@throw file_open_error() (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|&lt;&lt;:) file_name(fn);@]&#10;&#10;The intention here is to throw a file_open_error, however if (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;/&quot;:) fails to copy the std::string contained in the file_name (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) wrapper, a std::bad_alloc could propagate instead. This behavior seems undesirable to some programmers.&#10;&#10;Bjarne Stroustrup, The C++ Programming Language, 3rd Edition, page 371:&#10;&#10;-&gt;&apos;&apos;&quot;Throwing an exception requires an object to throw.  A C++ implementation is required to have enough spare memory to be able to throw bad_alloc in case of memory exhaustion.  However, it is possible that throwing some other exception will cause memory exhaustion.&quot;&apos;&apos;&#10;&#10;Therefore, the language itself does not guarantee that an attempt to throw an exception is guaranteed to throw an object of the specified type; propagating a std::bad_alloc seems to be a possibility even outside of the scope of Boost Exception.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Requirements:&#10;&#10;E must be boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:), or a type that derives (indirectly) from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;!!!!Postcondition:&#10;&#10;A copy of v is stored into x. If x already contains data of type (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;Tag,T&gt;, that data is overwritten. Basic exception safety guarantee.&#10;&#10;!!!!Returns:&#10;&#10;x.&#10;&#10;(:include throws:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-20</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include decl:)&#10;&#10;!!!!Effects:&#10;&#10;* Default constructor: initializes an empty boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) object.&#10;* Copy constructor: initializes a boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) object which shares ownership with x of all data added through (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;/&quot;:), including data that is added at a future time.&#10;&#10;!!!!Throws:&#10;&#10;Nothing.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-21</id>
													</shared_ptr>
												</weak_ptr>
												<size>77</size>
												<variant>2</variant>
												<string>!!Synopsis&#10;&#10;List of documented definitions, declarations and includes by header file:&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)@]&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)@]&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)@]&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-37</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-37</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)@]&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-78</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-78</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)@]&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-64</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-64</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)@]&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-42</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-42</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)@]&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)@]&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-36</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-36</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)@]&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-39</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-39</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)@]&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-59</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-59</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-15</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-15</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-7</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-7</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-66</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-66</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-76</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-76</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;&#10;`#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-56</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-56</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-44</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-44</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;&#10;`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-11</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-11</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-22</id>
													</shared_ptr>
												</weak_ptr>
												<size>21</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;Boost Exception responds to the following configuration macros:&#10;&#10;&apos;&apos;&apos;BOOST_NO_RTTI&apos;&apos;&apos;\\&#10;&apos;&apos;&apos;BOOST_NO_TYPEID&apos;&apos;&apos;&#10;&#10;The first macro prevents Boost Exception from using dynamic_cast and dynamic typeid. If the second macro is also defined, Boost Exception does not use static typeid either. There are no observable degrading effects on the library functionality, except for the following:&#10;&#10;-&gt;By default, the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) function template can be called with any exception type. If BOOST_NO_RTTI is defined, (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) can be used only with objects of type boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;!!!!Note:&#10;&#10;The library needs RTTI functionality. Disabling the language RTTI support enables an internal RTTI system, which may have more or less overhead depending on the platform.&#10;&#10;&apos;&apos;&apos;BOOST_EXCEPTION_DISABLE&apos;&apos;&apos;&#10;&#10;By default, (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) and (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) are integrated directly in the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) function. Defining BOOST_EXCEPTION_DISABLE disables this integration.&#10;&#10;Note that on some non-conformant compilers, for example MSVC 7.0 and older, as well as BCC, BOOST_EXCEPTION_DISABLE is implicitly defined in (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-39</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;&apos;&apos;&apos;BOOST_NO_EXCEPTIONS&apos;&apos;&apos;&#10;&#10;This macro disables exception handling in Boost, forwarding all exceptions to a user-defined non-template version of boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:). However, unless BOOST_EXCEPTION_DISABLE is also defined, users can still examine the exception object for any data added at the point of the throw, or use boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) (of course under BOOST_NO_EXCEPTIONS, the user-defined boost::throw_exception is not allowed to return to the caller.)&#10;&#10;&apos;&apos;&apos;BOOST_THROW_EXCEPTION_CURRENT_FUNCTION&apos;&apos;&apos;&#10;&#10;The </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> macro uses BOOST_THROW_EXCEPTION_CURRENT_FUNCTION to record the name of the current function in the exception object. Unless overridden by the user, BOOST_THROW_EXCEPTION_CURRENT_FUNCTION expands to BOOST_CURRENT_FUNCTION.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-23</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Precondition:&#10;&#10;ep shall not be null.&#10;&#10;!!!!Throws:&#10;&#10;&#10;&#10;The exception to which ep refers.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-24</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;Exception types should use virtual inheritance when deriving from other exception types. This insight is due to Andrew Koenig. Using virtual inheritance prevents ambiguity problems in the exception handler:&#10;&#10;[@#include &lt;iostream&gt;&#10;struct my_exc1 : std::exception { char const* what() const throw(); };&#10;struct my_exc2 : std::exception { char const* what() const throw(); };&#10;struct your_exc3 : my_exc1, my_exc2 {};&#10;&#10;int&#10;main()&#10;    {&#10;    try { throw your_exc3(); }&#10;    catch(std::exception const&amp; e) {}&#10;    catch(...) { std::cout &lt;&lt; &quot;whoops!&quot; &lt;&lt; std::endl; }&#10;    }@]&#10;&#10;The program above outputs &quot;whoops!&quot; because the conversion to std::exception is ambiguous.&#10;&#10;The overhead introduced by virtual inheritance is always negligible in the context of exception handling. Note that virtual bases are initialized directly by the constructor of the most-derived-type (the type passed to the throw statement, in case of exceptions.) However, typically this detail is of no concern when boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) is used, because it enables exception types to be trivial structs with no members (there&apos;s nothing to initialize.) See (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-45</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;w&quot;:).&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-26</id>
													</shared_ptr>
												</weak_ptr>
												<size>19</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;Boost Exception provides a namespace-scope function (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) which takes a boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:). The returned string contains:&#10;&#10;*the string representation of all data objects added to the boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) through (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;/&quot;:);&#10;*the output from std::exception::what;&#10;*additional platform-specific diagnostic information.&#10;&#10;The returned string is not presentable as a friendly user message, but because it is generated automatically, it is useful for debugging or logging purposes. Here is an example:&#10;&#10;[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-11</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;iostream&gt;&#10;&#10;void f(); //throws unknown types that derive from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;void&#10;g()&#10;    {&#10;    try&#10;        {&#10;        f();&#10;        }&#10;    catch(&#10;    boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) &amp; e )&#10;        {&#10;        std::cerr &lt;&lt; (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(e);&#10;        }&#10;    }@]&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-72</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-27</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Requirements:&#10;&#10;E must be boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:), or a type that derives (indirectly) from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;!!!!Effects:&#10;&#10;Equivalent to x &lt;&lt; v.(:link http://www.boost.org/libs/tuple/doc/tuple_users_guide.html#accessing_elements|get:)&lt;0&gt;() &lt;&lt; ... &lt;&lt; v.(:link http://www.boost.org/libs/tuple/doc/tuple_users_guide.html#accessing_elements|get:)&lt;N&gt;().&#10;&#10;!!!!Returns:&#10;&#10;x.&#10;&#10;(:include throws:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-29</id>
													</shared_ptr>
												</weak_ptr>
												<size>9</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;This </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> typedef is used by </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> if it defaults to returning an </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> that refers to an object of type </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-77</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>, to record in it the std::type_info of the original exception object.</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-30</id>
													</shared_ptr>
												</weak_ptr>
												<size>11</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;Boost Exception supports transporting of exception objects between threads through cloning. This system is similar to (:link http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2007/n2179.html|N2179:), but because Boost Exception can not rely on language support, the use of (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) at the time of the throw is required in order to use cloning.&#10;&#10;!!!!Note:&#10;&#10;All exceptions emitted by the familiar function boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) are guaranteed to derive from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) and to support cloning.&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-65</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-31</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-31</id>
													</shared_ptr>
												</weak_ptr>
												<size>37</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;When you catch an exception, you can call (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) to get an (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) object:&#10;&#10;[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;boost/thread.hpp&gt;&#10;#include &lt;boost/bind.hpp&gt;&#10;&#10;void do_work(); //throws cloning-enabled boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)s&#10;&#10;void&#10;worker_thread( boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) &amp; error )&#10;    {&#10;    try&#10;        {&#10;        do_work();&#10;        error = boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)();&#10;        }&#10;    catch(&#10;    ... )&#10;        {&#10;        error = boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)();&#10;        }&#10;    }@]&#10;&#10;In the above example, note that (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) captures the original type of the exception object. The exception can be thrown again using the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-23</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) function:&#10;&#10;[@// ...continued&#10;&#10;void&#10;work()&#10;    {&#10;    boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) error;&#10;    boost::(:link http://www.boost.org/doc/html/boost/thread.html|thread:) t( boost::(:link http://www.boost.org/libs/bind/bind.html|bind:)(worker_thread,boost::(:link http://www.boost.org/doc/html/ref.html|ref:)(error)) );&#10;    t.(:link http://www.boost.org/doc/html/boost/thread.html|join:)();&#10;    if( error )&#10;        boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-23</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(error);&#10;    }@]&#10;&#10;Note that (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) could fail to copy the original exception object in the following cases:&#10;&#10;* if there is not enough memory, in which case the returned (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) points to an instance of std::bad_alloc, or&#10;* if (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) was not used in the throw-expression passed to the original throw statement and the current implementation does not have the necessary compiler-specific support to copy the exception automatically, in which case the returned (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) points to an instance of (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-77</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;Regardless, the use of (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) and (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-23</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) in the above examples is well-formed.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<size>21</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Requirements:&#10;&#10;* T must be a class with an accessible no-throw copy constructor.&#10;* If T has any virtual base types, those types must have an accessible default constructor.&#10;&#10;!!!!Returns:&#10;&#10;An object of &apos;&apos;unspecified&apos;&apos; type which derives publicly from T. That is, the returned object can be intercepted by a catch(T &amp;).&#10;&#10;!!!!Description:&#10;&#10;&#10;&#10;This function is designed to be used directly in a throw-expression to enable the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) support in Boost Exception. For example:&#10;&#10;[@class&#10;my_exception:&#10;    public std::exception&#10;    {&#10;    };&#10;&#10;....&#10;throw boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(my_exception());@]&#10;&#10;Unless (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) is called at the time an exception object is used in a throw-expression, an attempt to copy it using (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) may return an (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) which refers to an instance of (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-77</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:). See (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) for details.&#10;&#10;!!!!Note:&#10;&#10;Instead of using the throw keyword directly, it is preferable to call boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:). This is guaranteed to throw an exception that derives from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) and supports the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) functionality.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;This macro takes an exception object, records the current function name, __FILE__ and __LINE__ in it, and forwards it to (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:). To recover this information at the catch site, use (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:); the information is also included in the message returned by (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-34</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include decl:)&#10;&#10;!!!!Effects:&#10;&#10;&#10;&#10;Frees all resources associated with a boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) object.&#10;&#10;!!!!Throws:&#10;&#10;&#10;&#10;Nothing.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-35</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;(:pagelist tags=&quot;hpp&quot; except_tags=&quot;noindex&quot;:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-36</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-37</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-38</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;The (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-38</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) function is intended to be called from a user-defined std::exception::what() override. This allows diagnostic information to be returned as the what() string.&#10;&#10;!!!!Returns:&#10;&#10;A pointer to a zero-terminated buffer that contains a string similar to the std::string returned by the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) function, or null to indicate a failure.&#10;&#10;!!!!Throws:&#10;&#10;Nothing.&#10;&#10;!!!!Note:&#10;&#10;The returned pointer becomes invalid if any (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) is modified or added to the exception object, or if another diagnostic information function is called.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-39</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;This type is designed to be used as a standard (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) instance for transporting a relevant errno value in exceptions deriving from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;!!!Example:&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-46</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-42</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-43</id>
													</shared_ptr>
												</weak_ptr>
												<size>19</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;The code snippet below demonstrates how boost::(:link http://www.boost.org/libs/tuple/doc/tuple_users_guide.html|tuple:) can be used to bundle the name of the function that failed, together with the reported errno so that they can be added to exception objects more conveniently together:&#10;&#10;[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-37</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-66</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-59</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;boost/shared_ptr.hpp&gt;&#10;#include &lt;stdio.h&gt;&#10;#include &lt;string&gt;&#10;#include &lt;errno.h&gt;&#10;&#10;typedef boost::(:link http://www.boost.org/libs/tuple/doc/tuple_users_guide.html|tuple:)&lt;boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-5</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:),boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt; clib_failure;&#10;&#10;struct file_open_error: virtual boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) { };&#10;&#10;boost::shared_ptr&lt;FILE&gt;&#10;file_open( char const * name, char const * mode )&#10;    {&#10;    if( FILE * f=fopen(name,mode) )&#10;        return boost::shared_ptr&lt;FILE&gt;(f,fclose);&#10;    else&#10;        throw file_open_error() &lt;&lt;&#10;            boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-48</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(name) &lt;&lt;&#10;            clib_failure(&quot;fopen&quot;,errno);&#10;    }@]&#10;&#10;Note that the members of a boost::(:link http://www.boost.org/libs/tuple/doc/tuple_users_guide.html|tuple:) are stored separately in exception objects; they can only be retrieved individually, using (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-44</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-45</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;Deriving from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) effectively decouples the semantics of a failure from the information that is relevant to each individual instance of reporting a failure with a given semantic.&#10;&#10;In other words: with boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:), what data a given exception object transports depends primarily on the context in which failures are reported (not on its type.) Since exception types need no members, it becomes very natural to throw exceptions that derive from more than one type to indicate multiple appropriate semantics:&#10;&#10;[@struct exception_base: virtual std::exception, virtual boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) { };&#10;struct io_error: virtual exception_base { };&#10;struct file_error: virtual io_error { };&#10;struct read_error: virtual io_error { };&#10;struct file_read_error: virtual file_error, virtual read_error { };@]&#10;&#10;Using this approach, exception types become a simple tagging system for categorizing errors and selecting failures in exception handlers.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-46</id>
													</shared_ptr>
												</weak_ptr>
												<size>37</size>
												<variant>2</variant>
												<string>[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-59</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-15</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-7</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-66</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-76</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-39</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;boost/shared_ptr.hpp&gt;&#10;#include &lt;boost/weak_ptr.hpp&gt;&#10;#include &lt;stdio.h&gt;&#10;#include &lt;errno.h&gt;&#10;#include &lt;exception&gt;&#10;&#10;struct error : virtual std::exception, virtual boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) { };&#10;struct file_error : virtual error { };&#10;struct file_open_error: virtual file_error { };&#10;struct file_read_error: virtual file_error { };&#10;&#10;boost::shared_ptr&lt;FILE&gt;&#10;open_file( char const * file, char const * mode )&#10;    {&#10;    if( FILE * f=fopen(file,mode) )&#10;        return boost::shared_ptr&lt;FILE&gt;(f,fclose);&#10;    else&#10;        (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(&#10;            file_open_error() &lt;&lt;&#10;            boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-5</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(&quot;fopen&quot;) &lt;&lt;&#10;            boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(errno) &lt;&lt;&#10;            boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-48</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(file) &lt;&lt;&#10;            boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-70</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(mode) );&#10;    }&#10;&#10;size_t&#10;read_file( boost::shared_ptr&lt;FILE&gt; const &amp; f, void * buf, size_t size )&#10;    {&#10;    size_t nr=fread(buf,1,size,f.get());&#10;    if( ferror(f.get()) )&#10;        (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(&#10;            file_read_error() &lt;&lt;&#10;            boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-5</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(&quot;fread&quot;) &lt;&lt;&#10;            boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(errno) &lt;&lt;&#10;            boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-16</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(f) );&#10;    return nr;&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-47</id>
													</shared_ptr>
												</weak_ptr>
												<size>9</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Requirements:&#10;&#10;This function must not be called outside of a catch block.&#10;&#10;!!!!Returns:&#10;&#10;If the current exception object can be converted to boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) or std::exception, this function returns the same string value returned by (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) for the current exception object. Otherwise, an unspecified non-empty string is returned.&#10;&#10;Typical use is to call </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-47</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> from a top-level function to output diagnostic information about unhandled exceptions:&#10;&#10;[@int&#10;main()&#10;    {&#10;    try&#10;        {&#10;        run_program();&#10;        }&#10;    catch(&#10;    error &amp; e )&#10;        {&#10;        //handle error&#10;        }&#10;    catch(&#10;    ...)&#10;        {&#10;        std::cerr &lt;&lt; &quot;Unhandled exception!&quot; &lt;&lt; std::endl &lt;&lt;&#10;            boost::</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-47</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>();&#10;        }&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-48</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;This type is designed to be used as a standard (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) instance for transporting a relevant file name in exceptions deriving from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;!!!Example:&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-46</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-49</id>
													</shared_ptr>
												</weak_ptr>
												<size>11</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;All exception types that derive from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) can be used as type-safe containers of arbitrary data objects, while complying with the no-throw requirements (15.5.1) of the ANSI C++ standard for exception types.&#10;&#10;When exceptions derive from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:), arbitrary data can be added to exception objects:&#10;&#10;*At the point of the throw;&#10;*At a later time as exceptions bubble up the call stack.&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-75</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-51</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-43</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-51</id>
													</shared_ptr>
												</weak_ptr>
												<size>19</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;Sometimes the throw site does not have all the information that is needed at the catch site to make sense of what went wrong. Let&apos;s say we have an exception type file_read_error, which takes a file name in its constructor. Consider the following function:&#10;&#10;[@void&#10;file_read( FILE * f, void * buffer, size_t size )&#10;    {&#10;    if( size!=fread(buffer,1,size,f) )&#10;        throw file_read_error(????);&#10;    }@]&#10;&#10;How can the file_read function pass a file name to the exception type constructor? All it has is a FILE handle.&#10;&#10;Using boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) allows us to free the file_read function from the burden of storing the file name in exceptions it throws:&#10;&#10;[@`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-11</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;boost/shared_ptr.hpp&gt;&#10;#include &lt;stdio.h&gt;&#10;#include &lt;errno.h&gt;&#10;&#10;struct file_read_error: virtual boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) { };&#10;&#10;void&#10;file_read( FILE * f, void * buffer, size_t size )&#10;    {&#10;    if( size!=fread(buffer,1,size,f) )&#10;        throw file_read_error() &lt;&lt; boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(errno);&#10;    }@]&#10;&#10;If file_read detects a failure, it throws an exception which contains the information that is available at the time, namely the errno. Other relevant information, such as the file name, can be added in a context higher up the call stack, where it is known naturally:&#10;&#10;[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-11</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;boost/shared_ptr.hpp&gt;&#10;#include &lt;stdio.h&gt;&#10;#include &lt;string&gt;&#10;&#10;boost::shared_ptr&lt;FILE&gt; file_open( char const * file_name, char const * mode );&#10;void file_read( FILE * f, void * buffer, size_t size );&#10;&#10;void&#10;parse_file( char const * file_name )&#10;    {&#10;    boost::shared_ptr&lt;FILE&gt; f = file_open(file_name,&quot;rb&quot;);&#10;    assert(f);&#10;    try&#10;        {&#10;        char buf[1024];&#10;        file_read( f.get(), buf, sizeof(buf) );&#10;        }&#10;    catch(&#10;    boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) &amp; e )&#10;        {&#10;        e &lt;&lt; boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-48</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(file_name);&#10;        throw;&#10;        }&#10;    }@]&#10;&#10;The above function is (almost) exception-neutral -- if an exception is emitted by any function call within the try block, parse_file does not need to do any real work, but it intercepts any boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) object, stores the file name, and re-throws using a throw-expression with no operand (15.1.6). The rationale for catching any boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) object is that the file name is relevant to any failure that occurs in parse_file, &apos;&apos;even if the failure is unrelated to file I/O&apos;&apos;.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-52</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Description:&#10;&#10;Returns a (const) reference to the copy of the value passed to (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&apos;s constructor stored in the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) object.&#10;&#10;!!!!Throws:&#10;&#10;Nothing.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<size>13</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;Class boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) is designed to be used as a universal base for user-defined exception types.&#10;&#10;An object of any type deriving from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) can store data of arbitrary types, using the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) wrapper and (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;/&quot;:).&#10;&#10;To retrieve data from a boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) object, use the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) function template.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<size>41</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Requirements:&#10;&#10;T must have accessible copy constructor and must not be a reference (there is no requirement that T&apos;s copy constructor does not throw.)&#10;&#10;!!!!Description:&#10;&#10;This class template is used to associate a Tag type with a value type T. Objects of type (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;Tag,T&gt; can be passed to (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;/&quot;:) to be stored in objects of type boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;!!!!Usage:&#10;&#10;The header &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt; provides a declaration of the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) template, which is sufficient for the purpose of typedefing an instance for specific Tag and T, for example:&#10;&#10;[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;struct tag_errno;&#10;typedef boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;tag_errno,int&gt; errno_info;@]&#10;&#10;Or, the shorter equivalent:&#10;&#10;[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;typedef boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct tag_errno,int&gt; errno_info;@]&#10;&#10;This errno_info typedef can be passed to (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;/&quot;:) (#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt; first) to store an int named tag_errno in exceptions of types that derive from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:):&#10;&#10;[@throw file_read_error() (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|&lt;&lt;:) errno_info(errno);@]&#10;&#10;It can also be passed to (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) (#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-60</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt; first) to retrieve the tag_errno int from a boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:):&#10;&#10;[@catch( boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) &amp; x )&#10;    {&#10;    if( int const * e=boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;errno_info&gt;(x) )&#10;        ....&#10;    }@]&#10;&#10;For convenience and uniformity, Boost Exception defines the following commonly used (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) typedefs, ready for use with (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;/&quot;:):&#10;&#10;(:pagelist tags=&quot;error_info_instance&quot;:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-55</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Effects:&#10;&#10;Stores a copy of v in the </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> object.&#10;&#10;(:include throws:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-56</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-57</id>
													</shared_ptr>
												</weak_ptr>
												<size>33</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;Traditionally, when using exceptions to report failures, the throw site:&#10;&#10;*creates an exception object of the appropriate type, and&#10;*stuffs it with data relevant to the detected error.&#10;&#10;A higher context in the program contains a catch statement which:&#10;&#10;*selects failures based on exception types, and&#10;*inspects exception objects for data required to deal with the problem.&#10;&#10;The main issue with this &quot;traditional&quot; approach is that often, the data available at the point of the throw is insufficient for the catch site to handle the failure.&#10;&#10;Here is an example of a catch statement:&#10;&#10;[@catch( file_read_error &amp; e )&#10;    {&#10;    std::cerr &lt;&lt; e.file_name();&#10;    }@]&#10;&#10;And here is a possible matching throw:&#10;&#10;[@void&#10;read_file( FILE * f )&#10;    {&#10;    ....&#10;    size_t nr=fread(buf,1,count,f);&#10;    if( ferror(f) )&#10;        throw file_read_error(???);&#10;    ....&#10;    }@]&#10;&#10;Clearly, the problem is that the handler requires a file name but the read_file function does not have a file name to put in the exception object; all it has is a FILE pointer!&#10;&#10;In an attempt to deal with this problem, we could modify read_file to accept a file name:&#10;&#10;[@void&#10;read_file( FILE * f, char const * name )&#10;    {&#10;    ....&#10;    size_t nr=fread(buf,1,count,f);&#10;    if( ferror(f) )&#10;        throw file_read_error(name);&#10;    ....&#10;    }@]&#10;&#10;This is not a real solution: it simply shifts the burden of supplying a file name to the immediate caller of the read_file function.&#10;&#10;-&gt;&apos;&apos;In general, the data required to handle a given library-emitted exception depends on the program that links to it. Many contexts between the throw and the catch may have relevant information which must be transported to the exception handler.&apos;&apos;&#10;&#10;!!!Exception wrapping&#10;&#10;The idea of exception wrapping is to catch an exception from a lower level function (such as the read_file function above), and throw a new exception object that contains the original exception (and also carries a file name.) This method seems to be particularly popular with C++ programmers with Java background.&#10;&#10;Exception wrapping leads to the following problems:&#10;&#10;*To wrap an exception object it must be copied, which may result in slicing.&#10;*Wrapping is practically impossible to use in generic contexts.&#10;&#10;The second point is actually special case of violating the exception neutrality principle. Most contexts in a program can not handle exceptions; such contexts should not interfere with the process of exception handling.&#10;&#10;!!!The boost::exception solution&#10;&#10;*Simply derive your exception types from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;*Confidently limit the throw site to provide only data that is available naturally.&#10;*Use exception-neutral contexts between the throw and the catch to augment exceptions with more relevant data as they bubble up.&#10;&#10;For example, in the throw statement below we only add the errno code, since this is the only failure-relevant information available in this context:&#10;&#10;[@struct exception_base: virtual std::exception, virtual boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) { };&#10;struct io_error: virtual exception_base { };&#10;struct file_read_error: virtual io_error { };&#10;&#10;typedef boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct tag_errno_code,int&gt; errno_code;&#10;&#10;void&#10;read_file( FILE * f )&#10;    {&#10;    ....&#10;    size_t nr=fread(buf,1,count,f);&#10;    if( ferror(f) )&#10;        throw file_read_error() (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|&lt;&lt;:) errno_code(errno);&#10;    ....&#10;    }@]&#10;&#10;In a higher exception-neutral context, we add the file name to &apos;&apos;any&apos;&apos; exception that derives from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:):&#10;&#10;[@typedef boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct tag_file_name,std::string&gt; file_name;&#10;&#10;....&#10;try&#10;    {&#10;    if( FILE * fp=fopen(&quot;foo.txt&quot;,&quot;rt&quot;) )&#10;        {&#10;        shared_ptr&lt;FILE&gt; f(fp,fclose);&#10;        ....&#10;        read_file(fp); //throws types deriving from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;        do_something();&#10;        ....&#10;        }&#10;    else&#10;        throw file_open_error() (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|&lt;&lt;:) errno_code(errno);&#10;    }&#10;catch( boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) &amp; e )&#10;    {&#10;    e (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|&lt;&lt;:) file_name(&quot;foo.txt&quot;);&#10;    throw;&#10;    }@]&#10;&#10;Finally here is how the handler retrieves data from exceptions that derive from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:):&#10;&#10;[@catch( io_error &amp; e )&#10;    {&#10;    std::cerr &lt;&lt; &quot;I/O Error!\n&quot;;&#10;&#10;    if( std::string const * fn=(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;file_name&gt;(e) )&#10;        std::cerr &lt;&lt; &quot;File name: &quot; &lt;&lt; *fn &lt;&lt; &quot;\n&quot;;&#10;&#10;    if( int const * c=(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;errno_code&gt;(e) )&#10;        std::cerr &lt;&lt; &quot;OS says: &quot; &lt;&lt; strerror(*c) &lt;&lt; &quot;\n&quot;;&#10;    }@]&#10;&#10;In addition, boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) can be used to compose an automatic (if not user-friendly) message that contains all of the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) objects added to a boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:). This is useful for inclusion in logs and other diagnostic objects.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-58</id>
													</shared_ptr>
												</weak_ptr>
												<size>27</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;Some exception hierarchies can not be modified to make boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) a base type. In this case, the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) function template can be used to make exception objects derive from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) anyway. Here is an example:&#10;&#10;[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-11</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;stdexcept&gt;&#10;&#10;typedef boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct tag_std_range_min,size_t&gt; std_range_min;&#10;typedef boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct tag_std_range_max,size_t&gt; std_range_max;&#10;typedef boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct tag_std_range_index,size_t&gt; std_range_index;&#10;&#10;template &lt;class T&gt;&#10;class&#10;my_container&#10;    {&#10;    public:&#10;&#10;    size_t size() const;&#10;&#10;    T const &amp;&#10;    operator[]( size_t i ) const&#10;        {&#10;        if( i &gt; size() )&#10;            throw boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(std::range_error(&quot;Index out of range&quot;)) &lt;&lt;&#10;                std_range_min(0) &lt;&lt;&#10;                std_range_max(size()) &lt;&lt;&#10;                std_range_index(i);&#10;        //....&#10;        }&#10;    };&#10;@]&#10;&#10;The call to (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;T&gt; gets us an object of &apos;&apos;unspecified type&apos;&apos; which is guaranteed to derive from both boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) and T. This makes it possible to use (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;/&quot;:) to store additional information in the exception object. The exception can be intercepted as T &amp;, so existing exception handling will not break. It can also be intercepted as boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) &amp;, so that (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-49</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|more information can be added to the exception at a later time:).&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-59</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-60</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-61</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;(:pagelist tags=&quot;macro&quot;:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<size>39</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Requirements:&#10;&#10;The (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) function must not be called outside of a catch block.&#10;&#10;In addition, to safely copy an exception from one thread to another, if the exception object is copied by </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> or </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-12</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>, the two copies must not have shared state. Exceptions that have value-type semantics (as well as the boost::</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> type itself) satisfy this requirement.&#10;&#10;!!!!Returns:&#10;&#10;* An (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) that refers to the currently handled exception or a copy of the currently handled exception.&#10;* If the function needs to allocate memory and the attempt fails, it returns an (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) that refers to an instance of std::bad_alloc.&#10;&#10;!!!!Throws:&#10;&#10;Nothing.&#10;&#10;!!!!Notes:&#10;&#10;* It is unspecified whether the return values of two successive calls to (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) refer to the same exception object.&#10;* Correct implementation of (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) may require compiler support, unless (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) was used at the time the currently handled exception object was passed to throw. Whenever (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) fails to properly copy the current exception object, it returns an (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) to an object of type that is as close as possible to the original exception type, using (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-77</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) as a final fallback. All such types derive from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:), and:&#10;** if the original exception object derives from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:), then the boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) sub-object of the object referred to by the returned (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) is initialized by the boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) copy constructor;&#10;** if available, the exception contains the std::type_info of the original exception object, accessible through (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-29</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Requirements:&#10;&#10;T must be a class with an accessible no-throw copy constructor as per (15.5.1).&#10;&#10;!!!!Returns:&#10;&#10;* If T derives from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:), the returned object is of type T and is a copy of x.&#10;* Otherwise, the returned object is of an unspecified type that derives publicly from both T and boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:). The T sub-object is initialized from x by the T copy constructor.&#10;&#10;!!!!Throws:&#10;&#10;Nothing.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-64</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-65</id>
													</shared_ptr>
												</weak_ptr>
												<size>15</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;Here is how cloning can be enabled in a throw-expression (15.1):&#10;&#10;[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;stdio.h&gt;&#10;#include &lt;errno.h&gt;&#10;&#10;struct file_read_error: virtual boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) { };&#10;&#10;void&#10;file_read( FILE * f, void * buffer, size_t size )&#10;    {&#10;    if( size!=fread(buffer,1,size,f) )&#10;        throw boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(file_read_error()) &lt;&lt;&#10;            boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(errno);&#10;    }@]&#10;&#10;Of course, (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) may be used with any exception type; there is no requirement that it should derive from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-66</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<size>15</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Effects:&#10;&#10;* If BOOST_NO_EXCEPTIONS is not defined, boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)(e) throws an exception of unspecified type that derives publicly from E and from boost::</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>.&#10;* If BOOST_NO_EXCEPTIONS is defined, the function is left undefined, and the user is expected to supply an appropriate definition. Callers of (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) are allowed to assume that the function never returns; therefore, if the user-defined (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) returns, the behavior is undefined.&#10;&#10;!!!!Requirements:&#10;&#10;E must derive publicly from std::exception. E may or may not derive from boost::</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>.&#10;&#10;!!!!Notes:&#10;&#10;* The emitted exception can be intercepted as E &amp;, std::exception &amp;, or boost::exception &amp;.&#10;* The emitted exception supports boost::</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>.&#10;* If BOOST_EXCEPTION_DISABLE is defined and BOOST_NO_EXCEPTIONS is not defined, boost::</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>(e) equivalent to throw e.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<size>13</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Requirements:&#10;&#10;* ErrorInfo must be an instance of the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) template.&#10;* E must be polymorphic.&#10;&#10;!!!!Returns:&#10;&#10;* If dynamic_cast&lt;boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) const *&gt;(&amp;x) is 0, or if x does not store an object of type ErrorInfo, the returned value is null.&#10;* Otherwise, the returned pointer points to the stored value (use (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> mod=&quot;/&quot;:) to store values in exception objects.) When x is destroyed, any pointers returned by (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) become invalid.&#10;&#10;!!!!Throws:&#10;&#10;Nothing.&#10;&#10;!!!!Note:&#10;&#10;The interface of (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) may be affected by the build (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-22</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<size>23</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;The (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) type can be used to refer to a copy of an exception object. It is Default Constructible, Copy Constructible, Assignable and Equality Comparable; (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&apos;s operations do not throw.&#10;&#10;The referenced object remains valid at least as long as there is an (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) object that refers to it.&#10;&#10;Two instances of (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) are equivalent and compare equal if and only if they refer to the same exception.&#10;&#10;The default constructor of (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) produces the null value of the type. The null value is equivalent only to itself.&#10;&#10;!!!!Thread safety:&#10;&#10;The </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> type is &quot;as thread-safe as built-in types&quot;:&#10;&#10;* An </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> instance can be &quot;read&quot; simultaneously by multiple threads&#10;* Different </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> instances can be &quot;written to&quot; simultaneously by multiple threads, even when these instances refer to the same exception object&#10;&#10;All other simultaneous accesses result in undefined behavior.&#10;&#10;!!!!Nesting of exceptions:&#10;&#10;An (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) can be added as (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) to any boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:). This is a convenient way to nest exceptions. There is no limit on the depth of the nesting, however cyclic references result in undefined behavior.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-70</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;This type is designed to be used as a standard (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) instance for transporting the string passed as a second argument to fopen in exceptions indicating fopen failures and deriving from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;!!!Example:&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-46</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-71</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;!!!!Requirements:&#10;&#10;This function must not be called outside of a catch block.&#10;&#10;!!!!Returns:&#10;&#10;A pointer of type E to the current exception object, or null if the current exception object can not be converted to E *.&#10;&#10;!!!!Throws:&#10;&#10;Nothing.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-72</id>
													</shared_ptr>
												</weak_ptr>
												<size>11</size>
												<variant>2</variant>
												<string>!!!!Example:&#10;&#10;this is a possible output from the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) function, as used in &apos;&apos;libs/exception/example/example_io.cpp:&apos;&apos;&#10;&#10;[@example_io.cpp(70): Throw in function class boost::shared_ptr&lt;struct _iobuf&gt; __cdecl my_fopen(const char *,const char *)&#10;Dynamic exception type: class boost::exception_detail::clone_impl&lt;struct fopen_error&gt;&#10;std::exception::what: example_io error&#10;[struct boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-5</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)_ *] = fopen&#10;[struct boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)_ *] = 2, &quot;No such file or directory&quot;&#10;[struct boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-48</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)_ *] = tmp1.txt&#10;[struct boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-70</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)_ *] = rb@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-73</id>
													</shared_ptr>
												</weak_ptr>
												<size>7</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;This type is designed to be used as a standard (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) instance for transporting a relevant text file line number, for example in parse error exceptions deriving from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;!!!Example:&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-46</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-74</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !:)&#10;&#10;This is an alphabetical list of all Boost Exception documentation pages.&#10;&#10;(:pagelist fmt=&quot;index&quot; except_tags=&quot;index noindex&quot; mod=&quot;w&quot;:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-75</id>
													</shared_ptr>
												</weak_ptr>
												<size>17</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;The following example demonstrates how errno can be stored in exception objects using Boost Exception:&#10;&#10;[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-11</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;iostream&gt;&#10;&#10;typedef boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;struct tag_my_info,int&gt; my_info; //(1)&#10;&#10;struct my_error: virtual boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:), virtual std::exception { }; //(2)&#10;&#10;void&#10;f()&#10;    {&#10;    throw my_error() &lt;&lt; my_info(42); //(3)&#10;    }@]&#10;&#10;First, we instantiate the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) template using a unique identifier -- tag_my_info, and the type of the info it identifies -- int. This provides compile-time type safety for the various values stored in exception objects.&#10;&#10;Second, we define class my_error, which derives from boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;&#10;Finally, (3) illustrates how the typedef from (1) can be used with (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>|operator&lt;&lt;:) to store values in exception objects at the point of the throw.&#10;&#10;The stored my_info value can be recovered at a later time like this:&#10;&#10;[@// ...continued&#10;&#10;void&#10;g()&#10;    {&#10;    try&#10;        {&#10;        f();&#10;        }&#10;    catch(&#10;    my_error &amp; x )&#10;        {&#10;        if( int const * mi=boost::(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&lt;my_info&gt;(x) )&#10;            std::cerr &lt;&lt; &quot;My info: &quot; &lt;&lt; *mi;&#10;        }&#10;    }@]&#10;&#10;The (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) function template is instantiated with the typedef from (1), and is passed an exception object of a polymorphic type. If the exception object contains the requested value, err will point to it; otherwise a null pointer is returned.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-76</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-77</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;This type is used by the (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:) support in Boost Exception. Please see (:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:).&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-78</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;!!!Synopsis&#10;&#10;(:include synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-79</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>(:auto !!!:)&#10;&#10;(:include synopsis:)&#10;&#10;This type is designed to be used as a standard </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> instance for transporting strings returned by std::type_info::name in exceptions deriving from boost::</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> objects.</string>
											</pair>
										</sorted>
									</object>
								</shared_ptr>
							</pair>
							<pair>
								<string>throws</string>
								<shared_ptr>
									<id>84</id>
									<type>
										<string>reno_layer</string>
									</type>
									<object>
										<sorted>
											<size>75</size>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-5</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-6</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-7</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-8</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-9</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-10</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-11</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-12</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-14</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-15</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-16</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-17</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>!!!!Throws:&#10;&#10;std::bad_alloc, or any exception emitted by the T copy constructor.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-20</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-21</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-22</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-23</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-24</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-26</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-27</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>!!!!Throws:&#10;&#10;std::bad_alloc, or any exception emitted by T1..TN copy constructor.&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-29</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-30</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-31</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-34</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-35</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-36</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-37</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-38</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-39</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-42</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-43</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-44</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-45</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-46</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-47</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-48</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-49</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-51</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-52</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-55</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>!!!!Throws:&#10;&#10;Any exception emitted by v&apos;s copy constructor.</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-56</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-57</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-58</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-59</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-60</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-61</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-64</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-65</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-66</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-70</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-71</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-72</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-73</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-74</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-75</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-76</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-77</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-78</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-79</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
										</sorted>
									</object>
								</shared_ptr>
							</pair>
							<pair>
								<string>synopsis</string>
								<shared_ptr>
									<id>85</id>
									<type>
										<string>reno_layer</string>
									</type>
									<object>
										<sorted>
											<size>75</size>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-5</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>`#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-59</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-59</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-6</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-7</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;    template &lt;class&gt; class weak_ptr;&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-8</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-9</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@(:include decl:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-10</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-11</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>[@(:include api:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-12</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-13</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-64</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;\\&#10;`#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;\\&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-14</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>`#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-56</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-56</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-15</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-16</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-7</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-7</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-17</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>[@namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-19</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;\\&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-20</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-21</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-22</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-23</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-24</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-26</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-27</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-37</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-27</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-29</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-30</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-31</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-32</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-36</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-33</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-39</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;(:include decl:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-34</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-35</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-36</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-37</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;#include &lt;boost/tuple/tuple.hpp&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-38</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-64</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;\\&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-39</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>(:include api:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;#include &lt;errno.h&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-41</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>`#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-40</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-42</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>[@namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-43</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-44</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;#include &lt;string&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-45</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-46</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-47</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-64</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;\\&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-48</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-66</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-66</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-49</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>[@namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-51</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-52</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@(:include decl:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include def pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-54</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include def pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-55</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-25</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@(:include decl:)@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-56</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl pre_indent=&quot;4&quot;:)&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-57</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-58</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-59</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-60</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>[@#include &lt;boost/shared_ptr.hpp&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-61</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-62</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-63</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-78</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-64</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@#include &lt;string&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-53</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> decl pre_indent=&quot;4&quot;:)&#10;&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-65</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-66</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;#include &lt;string&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-67</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-39</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-68</id>
													</shared_ptr>
												</weak_ptr>
												<size>1</size>
												<variant>2</variant>
												<string>[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-69</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-70</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>`#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-76</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-76</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-71</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-42</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-72</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-73</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-15</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-15</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-74</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-75</id>
													</shared_ptr>
												</weak_ptr>
												<size>0</size>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-76</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-18</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;#include &lt;string&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-77</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>`#include &lt;(:link </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-28</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>:)&gt;&#10;&#10;[@namespace&#10;boost&#10;    {&#10;(:include decl pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-78</id>
													</shared_ptr>
												</weak_ptr>
												<size>3</size>
												<variant>2</variant>
												<string>[@#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-50</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;&#10;namespace&#10;boost&#10;    {&#10;(:include api pre_indent=&quot;4&quot;:)&#10;    }@]&#10;</string>
											</pair>
											<pair>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-79</id>
													</shared_ptr>
												</weak_ptr>
												<size>5</size>
												<variant>2</variant>
												<string>`#include &lt;</string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-44</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string>&gt;&#10;&#10;(:include </string>
												<variant>1</variant>
												<weak_ptr>
													<expired>0</expired>
													<shared_ptr>
														<id>-44</id>
													</shared_ptr>
												</weak_ptr>
												<variant>2</variant>
												<string> synopsis:)&#10;</string>
											</pair>
										</sorted>
									</object>
								</shared_ptr>
							</pair>
						</sorted>
					</object>
				</shared_ptr>
			</layers>
			<contexts>
				<shared_ptr>
					<id>86</id>
					<type>
						<string>reno_context_map</string>
					</type>
					<object>
						<contexts>
							<sorted>
								<size>75</size>
								<shared_ptr>
									<id>-5</id>
								</shared_ptr>
								<shared_ptr>
									<id>-6</id>
								</shared_ptr>
								<shared_ptr>
									<id>-7</id>
								</shared_ptr>
								<shared_ptr>
									<id>-8</id>
								</shared_ptr>
								<shared_ptr>
									<id>-9</id>
								</shared_ptr>
								<shared_ptr>
									<id>-10</id>
								</shared_ptr>
								<shared_ptr>
									<id>-11</id>
								</shared_ptr>
								<shared_ptr>
									<id>-12</id>
								</shared_ptr>
								<shared_ptr>
									<id>-13</id>
								</shared_ptr>
								<shared_ptr>
									<id>-14</id>
								</shared_ptr>
								<shared_ptr>
									<id>-15</id>
								</shared_ptr>
								<shared_ptr>
									<id>-16</id>
								</shared_ptr>
								<shared_ptr>
									<id>-17</id>
								</shared_ptr>
								<shared_ptr>
									<id>-18</id>
								</shared_ptr>
								<shared_ptr>
									<id>-19</id>
								</shared_ptr>
								<shared_ptr>
									<id>-20</id>
								</shared_ptr>
								<shared_ptr>
									<id>-21</id>
								</shared_ptr>
								<shared_ptr>
									<id>-22</id>
								</shared_ptr>
								<shared_ptr>
									<id>-23</id>
								</shared_ptr>
								<shared_ptr>
									<id>-24</id>
								</shared_ptr>
								<shared_ptr>
									<id>-25</id>
								</shared_ptr>
								<shared_ptr>
									<id>-26</id>
								</shared_ptr>
								<shared_ptr>
									<id>-27</id>
								</shared_ptr>
								<shared_ptr>
									<id>-28</id>
								</shared_ptr>
								<shared_ptr>
									<id>-29</id>
								</shared_ptr>
								<shared_ptr>
									<id>-30</id>
								</shared_ptr>
								<shared_ptr>
									<id>-31</id>
								</shared_ptr>
								<shared_ptr>
									<id>-32</id>
								</shared_ptr>
								<shared_ptr>
									<id>-33</id>
								</shared_ptr>
								<shared_ptr>
									<id>-34</id>
								</shared_ptr>
								<shared_ptr>
									<id>-35</id>
								</shared_ptr>
								<shared_ptr>
									<id>-36</id>
								</shared_ptr>
								<shared_ptr>
									<id>-37</id>
								</shared_ptr>
								<shared_ptr>
									<id>-38</id>
								</shared_ptr>
								<shared_ptr>
									<id>-39</id>
								</shared_ptr>
								<shared_ptr>
									<id>-40</id>
								</shared_ptr>
								<shared_ptr>
									<id>-41</id>
								</shared_ptr>
								<shared_ptr>
									<id>-42</id>
								</shared_ptr>
								<shared_ptr>
									<id>-43</id>
								</shared_ptr>
								<shared_ptr>
									<id>-44</id>
								</shared_ptr>
								<shared_ptr>
									<id>-45</id>
								</shared_ptr>
								<shared_ptr>
									<id>-46</id>
								</shared_ptr>
								<shared_ptr>
									<id>-47</id>
								</shared_ptr>
								<shared_ptr>
									<id>-48</id>
								</shared_ptr>
								<shared_ptr>
									<id>-49</id>
								</shared_ptr>
								<shared_ptr>
									<id>-50</id>
								</shared_ptr>
								<shared_ptr>
									<id>-51</id>
								</shared_ptr>
								<shared_ptr>
									<id>-52</id>
								</shared_ptr>
								<shared_ptr>
									<id>-53</id>
								</shared_ptr>
								<shared_ptr>
									<id>-54</id>
								</shared_ptr>
								<shared_ptr>
									<id>-55</id>
								</shared_ptr>
								<shared_ptr>
									<id>-56</id>
								</shared_ptr>
								<shared_ptr>
									<id>-57</id>
								</shared_ptr>
								<shared_ptr>
									<id>-58</id>
								</shared_ptr>
								<shared_ptr>
									<id>-59</id>
								</shared_ptr>
								<shared_ptr>
									<id>-60</id>
								</shared_ptr>
								<shared_ptr>
									<id>-61</id>
								</shared_ptr>
								<shared_ptr>
									<id>-62</id>
								</shared_ptr>
								<shared_ptr>
									<id>-63</id>
								</shared_ptr>
								<shared_ptr>
									<id>-64</id>
								</shared_ptr>
								<shared_ptr>
									<id>-65</id>
								</shared_ptr>
								<shared_ptr>
									<id>-66</id>
								</shared_ptr>
								<shared_ptr>
									<id>-67</id>
								</shared_ptr>
								<shared_ptr>
									<id>-68</id>
								</shared_ptr>
								<shared_ptr>
									<id>-69</id>
								</shared_ptr>
								<shared_ptr>
									<id>-70</id>
								</shared_ptr>
								<shared_ptr>
									<id>-71</id>
								</shared_ptr>
								<shared_ptr>
									<id>-72</id>
								</shared_ptr>
								<shared_ptr>
									<id>-73</id>
								</shared_ptr>
								<shared_ptr>
									<id>-74</id>
								</shared_ptr>
								<shared_ptr>
									<id>-75</id>
								</shared_ptr>
								<shared_ptr>
									<id>-76</id>
								</shared_ptr>
								<shared_ptr>
									<id>-77</id>
								</shared_ptr>
								<shared_ptr>
									<id>-78</id>
								</shared_ptr>
								<shared_ptr>
									<id>-79</id>
								</shared_ptr>
							</sorted>
						</contexts>
						<index>
							<sorted>
								<size>75</size>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-6</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-30</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-49</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-74</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-78</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-36</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-57</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-24</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-45</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-17</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-10</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-35</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-8</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-61</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>0</size>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>1</empty>
										</path>
									</file>
									<shared_ptr>
										<id>-21</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>422CF2A57EA6763FBD2F319C4CDD8DD5ADF4493C699B50653015A362F71D4500</strong>
											<weak>1282485161</weak>
											<size>2161</size>
											<position>321</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/info_tuple.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-37</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>790A065B0168021FAD180E996254E3CDBC0360D22D3FCB83C143416663D85402</strong>
											<weak>1376868984</weak>
											<size>262</size>
											<position>323</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_nested_exception.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-56</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>790A065B0168021FAD180E996254E3CDBC0360D22D3FCB83C143416663D85402</strong>
											<weak>1376868984</weak>
											<size>262</size>
											<position>323</position>
											<strong>83E18B98A15DEF9418E38218D221D10DC093B915D630B51974CCD23A6E1EDC44</strong>
											<weak>849683856</weak>
											<size>98</size>
											<position>158</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_nested_exception.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-14</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>66EFC83C830F0B00D0C9399B475099072E2674B3C694F9152645A33E3D7AC303</strong>
											<weak>561674611</weak>
											<size>417</size>
											<position>323</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_file_name.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-66</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>EEA69AA1E84CB2B7C903A3F4C236D0A233D03DBA4BA1D3B97D959918F3B30E09</strong>
											<weak>2728032055</weak>
											<size>406</size>
											<position>344</position>
											<strong>EE695B95A2499B66980754857E184776F1DE7224372A5F5153B6DF94E621A89B</strong>
											<weak>1009590890</weak>
											<size>92</size>
											<position>308</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_file_open_mode.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-70</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>3B52D5850D9664639CCF1D22FBD52F2EB99087BED704C3FE07FE185B38C0DD09</strong>
											<weak>676740550</weak>
											<size>15108</size>
											<position>321</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-28</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>2F10A76F9BA78353597A5E6F1373E8188DE7AEFDCE29BFD0105527B64B37D00E</strong>
											<weak>1041541496</weak>
											<size>4693</size>
											<position>1606</position>
											<strong>20B46D7510ED9F1F40CF3A80C97AE430628745D26173DE91E3D6CB6CEABDAA58</strong>
											<weak>2572596214</weak>
											<size>659</size>
											<position>4028</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/diagnostic_information.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-38</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>3</size>
											<strong>2F10A76F9BA78353597A5E6F1373E8188DE7AEFDCE29BFD0105527B64B37D00E</strong>
											<weak>1041541496</weak>
											<size>4693</size>
											<position>1606</position>
											<strong>4FDA7B607488BB202B2AB72C17983031070085FB6B616F2B77320088BE08EB62</strong>
											<weak>98930276</weak>
											<size>3714</size>
											<position>26</position>
											<strong>28B2A7701322B20C8CF5D6074F9019FBEA2FB02F1A13E83632AA76C431798777</strong>
											<weak>1206384617</weak>
											<size>3087</size>
											<position>628</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/diagnostic_information.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-13</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>21A43755562CB78B3FFCC49F66B457C1FCD659EE98F25BBFA8DDE453EB89DF0E</strong>
											<weak>2576704708</weak>
											<size>337</size>
											<position>323</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_api_function.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-59</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>FC684D0DD5A9732B4130F2AB3DB6E0491D0F523E14B7FB738B2019EA2C7F8717</strong>
											<weak>2229778754</weak>
											<size>631</size>
											<position>319</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../example/cloning_2.cpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-31</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>126A895281064E2195458B8A47CD73DB7E3BE3608F250925E07AF4230CBDDE1D</strong>
											<weak>4231421785</weak>
											<size>307</size>
											<position>344</position>
											<strong>16179B125E2BC6D993FBE4BA5E9A96DBAE43CA1443C7D281B659D020B6725983</strong>
											<weak>1126376090</weak>
											<size>92</size>
											<position>209</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_type_info_name.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-79</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>4D7009F0868C1DF4898EC6ECF9AD2CFEA98E8653B01B066106761807405D4C22</strong>
											<weak>1416707852</weak>
											<size>3107</size>
											<position>543</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/get_error_info.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-60</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>27ED18F9B6131B084FEF0C9F932B7027AF449E378B5FD7973CD6642263FCAF27</strong>
											<weak>2867102400</weak>
											<size>404</size>
											<position>307</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../example/cloning_1.cpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-65</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>F971041F60D19AFB8AA50440BC2A911633E5826FDED7B3E1CFC90D241D880C32</strong>
											<weak>931174095</weak>
											<size>3062</size>
											<position>95</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/throw_exception.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-39</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>979343A73CAA7601AF159E6240A03038F47940F71F6DE85D6BA648B179921C35</strong>
											<weak>2321681356</weak>
											<size>939</size>
											<position>321</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_errno.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-40</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>BF7B46FEFA4E2DED7D652BFD40E94DD0B225ADA8D35E28FF4216F72812589835</strong>
											<weak>422843600</weak>
											<size>756</size>
											<position>543</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/all.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-11</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>6FB85B536F965F137409D5B5D34786DCBF0B9957A7C251D271B717A1156B823D</strong>
											<weak>1090406464</weak>
											<size>362</size>
											<position>543</position>
											<strong>D16DAEA8B1792A019AF7FCA362FDC6EFD381AF4C43C076A01C029ECE51F994A6</strong>
											<weak>3172941848</weak>
											<size>330</size>
											<position>26</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/current_exception_cast.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-71</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>DB156E6A8ACB9FB90C8FB110FC25A5FEB14A619F82EEC47FF913373592E5CC3E</strong>
											<weak>240075319</weak>
											<size>6209</size>
											<position>412</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../example/example_io.cpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-72</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>FD7792C2929DD7B6BD613636FD0C574D002286E33811BA109B57B9C4D790D340</strong>
											<weak>1830643656</weak>
											<size>1244</size>
											<position>1793</position>
											<strong>BAE73EEDFF4059A7561888B4BA054DFA033F0967727630270F2C0D4EB918B88D</strong>
											<weak>3168166030</weak>
											<size>1222</size>
											<position>21</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/throw_exception.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-67</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>15CF5BD93D20D62D659C11A69330B06E408398EA488BEF1FD45437AADCDB424E</strong>
											<weak>1232553666</weak>
											<size>214</size>
											<position>345</position>
											<strong>6262783847165581298EC9500031E6B7A97B2751A9CEF67C4794121A78142C58</strong>
											<weak>3676119191</weak>
											<size>90</size>
											<position>118</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_file_handle.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-16</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>D32E0E4334CE0236B6EDB0EAC484B2DD595860E9FD53701EB5646D62C6A45D4E</strong>
											<weak>1054670543</weak>
											<size>866</size>
											<position>306</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../example/error_info_2.cpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-51</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>8A8FAA48FF123031D5E51D50BC96D0AAC468112838058976B85AC6EED4A25C57</strong>
											<weak>4201574956</weak>
											<size>763</size>
											<position>833</position>
											<strong>AEA5C07CF015DDE792E061003F669239E7AADBD24BE554EB26706AD9B28B8C89</strong>
											<weak>2503775994</weak>
											<size>472</size>
											<position>285</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/diagnostic_information.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-47</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>66E0BD9724AB83012F5B35D887E3313960DC0E69B94E0C03CA1F3C85A0D84A5C</strong>
											<weak>2883671483</weak>
											<size>311</size>
											<position>306</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../example/logging.cpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-26</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>4EDD3DF2332B6D9D22AC9AD90B850ACC715A24DD466E675014CBED25C63C255F</strong>
											<weak>4175717823</weak>
											<size>328</size>
											<position>323</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_type_info_name.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-44</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>21027A2B73C9AA6FF083752A952D63BBA9B5FD68A3C8915965A7184EA62A5D61</strong>
											<weak>1523356166</weak>
											<size>537</size>
											<position>623</position>
											<strong>24256E1CE56594FB38D0630858B8947191827CFC57771E8727A6A56F76207454</strong>
											<weak>665917505</weak>
											<size>66</size>
											<position>26</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_errno.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-41</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>3</size>
											<strong>ADCD0B47BEBAA82DE5FDCACB0E9E8FF900527566EF9865ECD8C08B62067B4C66</strong>
											<weak>1181168733</weak>
											<size>14302</size>
											<position>1027</position>
											<strong>2482DDAF6A7E31CF75E93B993C86D9814A0B8899B68E555B23D411BD195FE270</strong>
											<weak>1574307697</weak>
											<size>8349</size>
											<position>4068</position>
											<strong>7E162EB263369C2C485D5F69CA1A4FADD3EEBC6EB78CE7A767A8615885178079</strong>
											<weak>1179386730</weak>
											<size>5404</size>
											<position>2935</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-62</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>ADCD0B47BEBAA82DE5FDCACB0E9E8FF900527566EF9865ECD8C08B62067B4C66</strong>
											<weak>1181168733</weak>
											<size>14302</size>
											<position>1027</position>
											<strong>EEDBDE62A278D2AF428D9D1ED2ABCFF06163BACD91E12DD033565C7043354B89</strong>
											<weak>246173488</weak>
											<size>248</size>
											<position>1396</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-29</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>ADCD0B47BEBAA82DE5FDCACB0E9E8FF900527566EF9865ECD8C08B62067B4C66</strong>
											<weak>1181168733</weak>
											<size>14302</size>
											<position>1027</position>
											<strong>1D5E771272B020A105B69E186517499873571F62AFF9D48F130C952CFAA12FA3</strong>
											<weak>2841506107</weak>
											<size>891</size>
											<position>173</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-69</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>3</size>
											<strong>ADCD0B47BEBAA82DE5FDCACB0E9E8FF900527566EF9865ECD8C08B62067B4C66</strong>
											<weak>1181168733</weak>
											<size>14302</size>
											<position>1027</position>
											<strong>97DB2EDAA38019314BA1A582664F8950F5208310F14BAB94E1880AE2C5F00CD4</strong>
											<weak>3076716310</weak>
											<size>959</size>
											<position>2974</position>
											<strong>1760DA943E0DCAE6DDB000F3C08D6E6F5F8AEDBBEAC7CAA84A2ED60BFA4B0E1A</strong>
											<weak>702729709</weak>
											<size>815</size>
											<position>145</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-77</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>ADCD0B47BEBAA82DE5FDCACB0E9E8FF900527566EF9865ECD8C08B62067B4C66</strong>
											<weak>1181168733</weak>
											<size>14302</size>
											<position>1027</position>
											<strong>0066D4E6E6B189906E6DE04F08509F3737511701A1B1355B37511EC18E8371F4</strong>
											<weak>2078296250</weak>
											<size>305</size>
											<position>1066</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-12</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>ADCD0B47BEBAA82DE5FDCACB0E9E8FF900527566EF9865ECD8C08B62067B4C66</strong>
											<weak>1181168733</weak>
											<size>14302</size>
											<position>1027</position>
											<strong>10E31FFA267B250065A2630D0B7107862920D940AEA0A5499D5341A902AE01FF</strong>
											<weak>1524325002</weak>
											<size>368</size>
											<position>13033</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/detail/exception_ptr.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-23</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>AEDDD2FA4F47CEBD99444F1054D85AB8132748CF38D6634503D62E9C8AD5FE68</strong>
											<weak>1378637100</weak>
											<size>292</size>
											<position>368</position>
											<strong>892C0239798B84BA2E80DAA70BBEB7BE0B6086A1D0829D0E1937EC1D19E3FF20</strong>
											<weak>3349881047</weak>
											<size>89</size>
											<position>197</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_api_function.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-5</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>756A81C65A938BEEAD9B576707145748A3DB3BF767CC77ADD5AACD549373856A</strong>
											<weak>904132245</weak>
											<size>744</size>
											<position>363</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../example/info_tuple.cpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-43</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>E0BE7EFCD5550582AB95C9EEDA6E68CA0F89B19838DA61876D42161E1EA4AE71</strong>
											<weak>2587249979</weak>
											<size>233</size>
											<position>323</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_at_line.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-15</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>E0BE7EFCD5550582AB95C9EEDA6E68CA0F89B19838DA61876D42161E1EA4AE71</strong>
											<weak>2587249979</weak>
											<size>233</size>
											<position>323</position>
											<strong>92AB508A6B1C2A62CB2ACED423BD04BB5A471674B5A51BFC1E6FB1F5C92AF9AA</strong>
											<weak>2372475309</weak>
											<size>70</size>
											<position>157</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_at_line.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-73</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>FFF4359EFC66EE6AA729B641F38B4020A55E83A1C099BCA59B1CA9A9875E7F79</strong>
											<weak>366628170</weak>
											<size>236</size>
											<position>323</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_file_handle.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-7</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>56C5A51DE37A6E893DA3B25D69DB65E4593C7803C6E34112E1F95C93D6037A82</strong>
											<weak>275305396</weak>
											<size>5586</size>
											<position>321</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/info.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-25</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>9516640DF38FC07A649AA4CAF21D4C4A6D6C2DF2B00E608F8D1C653C8D85E58B</strong>
											<weak>406646287</weak>
											<size>956</size>
											<position>564</position>
											<strong>8F508F9E7187AEA0E35A268B6F7B8E8A6C6588CCA01A2F3C5BBF1010699D8270</strong>
											<weak>1555404133</weak>
											<size>578</size>
											<position>372</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/detail/error_info_impl.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-54</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>3</size>
											<strong>9516640DF38FC07A649AA4CAF21D4C4A6D6C2DF2B00E608F8D1C653C8D85E58B</strong>
											<weak>406646287</weak>
											<size>956</size>
											<position>564</position>
											<strong>8F508F9E7187AEA0E35A268B6F7B8E8A6C6588CCA01A2F3C5BBF1010699D8270</strong>
											<weak>1555404133</weak>
											<size>578</size>
											<position>372</position>
											<strong>38B566F2C6678B8724D18086A6F76E077DC2ADC1BB69A4B83BF0A2C3B7D31B50</strong>
											<weak>2218658069</weak>
											<size>31</size>
											<position>143</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/detail/error_info_impl.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-9</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>3</size>
											<strong>9516640DF38FC07A649AA4CAF21D4C4A6D6C2DF2B00E608F8D1C653C8D85E58B</strong>
											<weak>406646287</weak>
											<size>956</size>
											<position>564</position>
											<strong>8F508F9E7187AEA0E35A268B6F7B8E8A6C6588CCA01A2F3C5BBF1010699D8270</strong>
											<weak>1555404133</weak>
											<size>578</size>
											<position>372</position>
											<strong>98B33BE76679E3A4831241335CD5DFF6F634429F36BABF96C1D4DC2296C5ECC5</strong>
											<weak>1584672077</weak>
											<size>208</size>
											<position>259</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/detail/error_info_impl.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-52</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>8A5444CF9C854740F83F17EA2075478A983F7C0243DCE4E42551ECBF908C1392</strong>
											<weak>4193409281</weak>
											<size>322</size>
											<position>992</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/throw_exception.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-22</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>F2E44174DE588C19C0172D82AD61322E6B6578ADBE2A631C6C8059CB84396D97</strong>
											<weak>670214046</weak>
											<size>684</size>
											<position>321</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/current_exception_cast.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-42</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>197F3960CFF5CBDEF7BDA8D0DE60948A5328F229C6710FEDE656530A3116B29B</strong>
											<weak>742102996</weak>
											<size>475</size>
											<position>1316</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/throw_exception.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-33</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>D58AD357499A5A09FB5D12397CFFC2FFD412AC8A307ABB59C9BC53ACCA3B959D</strong>
											<weak>2209414553</weak>
											<size>2926</size>
											<position>724</position>
											<strong>49F40FF20D66B205C908A8F10BC61DE1BC571E4917A5BD0B4115E3F7FE3923FA</strong>
											<weak>638776689</weak>
											<size>2894</size>
											<position>26</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/get_error_info.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-68</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>3EC508EAC996E7C8D4B1DDF49BF0B32096F8DEFD1BD1CD7FABBF0E0938255CA4</strong>
											<weak>1239321455</weak>
											<size>12162</size>
											<position>543</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/exception.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-50</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>3EC508EAC996E7C8D4B1DDF49BF0B32096F8DEFD1BD1CD7FABBF0E0938255CA4</strong>
											<weak>1239321455</weak>
											<size>12162</size>
											<position>543</position>
											<strong>96DA9A7E983733685FB1D74C8ABF58EA409CCA42F8522A9775E15BC0C93DB87B</strong>
											<weak>3668840260</weak>
											<size>2638</size>
											<position>4436</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/exception.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-53</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>3EC508EAC996E7C8D4B1DDF49BF0B32096F8DEFD1BD1CD7FABBF0E0938255CA4</strong>
											<weak>1239321455</weak>
											<size>12162</size>
											<position>543</position>
											<strong>17E691632123EB67BA67D590B49EB8094F462F5A10A66A1C5438E1867EF1478E</strong>
											<weak>765399792</weak>
											<size>77</size>
											<position>7076</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/exception.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-34</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>3EC508EAC996E7C8D4B1DDF49BF0B32096F8DEFD1BD1CD7FABBF0E0938255CA4</strong>
											<weak>1239321455</weak>
											<size>12162</size>
											<position>543</position>
											<strong>DF9EA87B0140AACF4422F1B76F6A6A409C15F32858BBBA85A35981A824C56BA9</strong>
											<weak>1137981799</weak>
											<size>192</size>
											<position>11964</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/exception.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-32</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>3EC508EAC996E7C8D4B1DDF49BF0B32096F8DEFD1BD1CD7FABBF0E0938255CA4</strong>
											<weak>1239321455</weak>
											<size>12162</size>
											<position>543</position>
											<strong>F3FB15CD82336271C6E875BC620385322777D16F0B7C233300783CE35710CCBF</strong>
											<weak>3292878997</weak>
											<size>282</size>
											<position>9470</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/exception.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-63</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>3</size>
											<strong>3EC508EAC996E7C8D4B1DDF49BF0B32096F8DEFD1BD1CD7FABBF0E0938255CA4</strong>
											<weak>1239321455</weak>
											<size>12162</size>
											<position>543</position>
											<strong>97095C7B5621CC7BAB278BE10B315FB44588DB1CF078F269E35499C0FD934AF0</strong>
											<weak>2572216802</weak>
											<size>2505</size>
											<position>4436</position>
											<strong>A94129EFD6ABBDDCD4CFDB671821F7DA103B2EA4455CF39E783D333C236D1C41</strong>
											<weak>1035445969</weak>
											<size>595</size>
											<position>402</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/exception.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-20</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>DE766B811244919E8E1EA54FC747A8487BCE57F1AB592932640FC90428B617A5</strong>
											<weak>414875037</weak>
											<size>427</size>
											<position>323</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_file_open_mode.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-76</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>977045132A532A0071B0B53F737D85367CE9A331402F96790E45B3B6F2FC88A6</strong>
											<weak>1875939463</weak>
											<size>529</size>
											<position>382</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../example/error_info_1.cpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-75</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>91CF203512705C8B2CDCBCD1439821CBF93CFC1A4C2EA2CA91F38DAA3F7720B2</strong>
											<weak>1769665510</weak>
											<size>1558</size>
											<position>352</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../example/errinfos.cpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-46</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>0CA48A4674CA9C409FF164D9A1B261FB48B0916C0EA387DF2F00DC4637E769BD</strong>
											<weak>348807582</weak>
											<size>6078</size>
											<position>321</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/diagnostic_information.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-64</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>1B4417301AE3C0338C22E6D497391F51ABD459E521E7DFCE59A6EEC1372D33C2</strong>
											<weak>202224383</weak>
											<size>1766</size>
											<position>616</position>
											<strong>E0A17503B42EE12F31548A7D20F89916D734CE88B30A1BF6F9FC2D1F83A8B6F4</strong>
											<weak>3410340567</weak>
											<size>1734</size>
											<position>26</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/info_tuple.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-27</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>307034E20863A00923777A60D9495B4076B7F917D5B97203025299060F0833E0</strong>
											<weak>3948311309</weak>
											<size>396</size>
											<position>344</position>
											<strong>F8ED2052577830AC0C515EC5932BB14445DD5DA714782281FCDB1776961FECB1</strong>
											<weak>3880328768</weak>
											<size>82</size>
											<position>308</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/errinfo_file_name.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-48</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>1</size>
											<strong>C95CEF2E9D0BAA1E950509471062916DB849A46A19F7692BA478030E79B338EB</strong>
											<weak>1917376632</weak>
											<size>706</size>
											<position>408</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../example/enable_error_info.cpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-58</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>C6DDF7D02A058403B7BD295CF1561F167D92B7DA1DAC4EBE9F801955264180EB</strong>
											<weak>1656366188</weak>
											<size>5040</size>
											<position>767</position>
											<strong>6E325144EF4F41FA3A225EB30729101382C4E99B3D6160E307311E4B4E641010</strong>
											<weak>1097215175</weak>
											<size>161</size>
											<position>422</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/info.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-55</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>3</size>
											<strong>C6DDF7D02A058403B7BD295CF1561F167D92B7DA1DAC4EBE9F801955264180EB</strong>
											<weak>1656366188</weak>
											<size>5040</size>
											<position>767</position>
											<strong>507B2DA4184DD6A38FC6099F6454CDC96604C0C7B2C06A2955C78452F66526F8</strong>
											<weak>457758605</weak>
											<size>3872</size>
											<position>889</position>
											<strong>38AA79D330846BE1CF17285796F34A9DBB5A7E995963A55F9B46EB1DA6314610</strong>
											<weak>542483318</weak>
											<size>573</size>
											<position>3084</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/info.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-19</id>
									</shared_ptr>
								</pair>
								<pair>
									<hook>
										<stream_hook_path>
											<size>2</size>
											<strong>9A4ECF9A49A73AED83C1565CB8C67AE1519E8AFE6818F968B4C4733CB9E86CEF</strong>
											<weak>1615599655</weak>
											<size>68</size>
											<position>321</position>
											<strong>34F0583BC8DE767CE2D79721E1F956895E43E5397473B1050F59BE7E26C773DB</strong>
											<weak>805836816</weak>
											<size>66</size>
											<position>1</position>
										</stream_hook_path>
									</hook>
									<file>
										<path>
											<empty>0</empty>
											<string>../../../../boost/exception/error_info.hpp</string>
											<type>0</type>
											<base>0</base>
										</path>
									</file>
									<shared_ptr>
										<id>-18</id>
									</shared_ptr>
								</pair>
							</sorted>
						</index>
					</object>
				</shared_ptr>
			</contexts>
			<index>
				<shared_ptr>
					<id>87</id>
					<type>
						<string>tag_index</string>
					</type>
					<object>
						<tag_index>
							<sorted>
								<size>56</size>
								<pair>
									<weak_ptr>
										<expired>1</expired>
									</weak_ptr>
									<string></string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-5</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info_instance noalso type</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-6</id>
										</shared_ptr>
									</weak_ptr>
									<string>noindex</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-9</id>
										</shared_ptr>
									</weak_ptr>
									<string>type</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-12</id>
										</shared_ptr>
									</weak_ptr>
									<string>exception_ptr free function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-13</id>
										</shared_ptr>
									</weak_ptr>
									<string>diagnostic_information free function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-14</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info_instance noalso type</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-16</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info_instance noalso type</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-18</id>
										</shared_ptr>
									</weak_ptr>
									<string></string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-19</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info free function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-20</id>
										</shared_ptr>
									</weak_ptr>
									<string>function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-23</id>
										</shared_ptr>
									</weak_ptr>
									<string>exception_ptr free function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-24</id>
										</shared_ptr>
									</weak_ptr>
									<string>tutorial</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-25</id>
										</shared_ptr>
									</weak_ptr>
									<string></string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-26</id>
										</shared_ptr>
									</weak_ptr>
									<string>diagnostic_information tutorial</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-27</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info free function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-28</id>
										</shared_ptr>
									</weak_ptr>
									<string></string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-29</id>
										</shared_ptr>
									</weak_ptr>
									<string>type</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-30</id>
										</shared_ptr>
									</weak_ptr>
									<string>tutorial</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-31</id>
										</shared_ptr>
									</weak_ptr>
									<string>noindex tutorial</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-32</id>
										</shared_ptr>
									</weak_ptr>
									<string>exception_ptr free function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-33</id>
										</shared_ptr>
									</weak_ptr>
									<string>macro</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-34</id>
										</shared_ptr>
									</weak_ptr>
									<string>function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-36</id>
										</shared_ptr>
									</weak_ptr>
									<string>exception_ptr</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-37</id>
										</shared_ptr>
									</weak_ptr>
									<string></string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-38</id>
										</shared_ptr>
									</weak_ptr>
									<string>free function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-39</id>
										</shared_ptr>
									</weak_ptr>
									<string></string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-41</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info_instance noalso type</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-42</id>
										</shared_ptr>
									</weak_ptr>
									<string></string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-43</id>
										</shared_ptr>
									</weak_ptr>
									<string>noalso noindex tutorial</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-47</id>
										</shared_ptr>
									</weak_ptr>
									<string>function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-48</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info_instance noalso type</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-49</id>
										</shared_ptr>
									</weak_ptr>
									<string>tutorial</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-50</id>
										</shared_ptr>
									</weak_ptr>
									<string></string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-51</id>
										</shared_ptr>
									</weak_ptr>
									<string>noalso noindex tutorial</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-52</id>
										</shared_ptr>
									</weak_ptr>
									<string>function member</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-53</id>
										</shared_ptr>
									</weak_ptr>
									<string>type</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-54</id>
										</shared_ptr>
									</weak_ptr>
									<string>type</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-55</id>
										</shared_ptr>
									</weak_ptr>
									<string>function member</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-58</id>
										</shared_ptr>
									</weak_ptr>
									<string>tutorial</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-60</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-62</id>
										</shared_ptr>
									</weak_ptr>
									<string>exception_ptr free function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-63</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info free function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-64</id>
										</shared_ptr>
									</weak_ptr>
									<string></string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-65</id>
										</shared_ptr>
									</weak_ptr>
									<string>noindex tutorial</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-67</id>
										</shared_ptr>
									</weak_ptr>
									<string>free function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-68</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info free function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-69</id>
										</shared_ptr>
									</weak_ptr>
									<string>type</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-70</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info_instance noalso type</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-71</id>
										</shared_ptr>
									</weak_ptr>
									<string>function</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-73</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info_instance noalso type</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-74</id>
										</shared_ptr>
									</weak_ptr>
									<string></string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-75</id>
										</shared_ptr>
									</weak_ptr>
									<string>noalso noindex tutorial</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-77</id>
										</shared_ptr>
									</weak_ptr>
									<string>exception_ptr type</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-78</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info</string>
								</pair>
								<pair>
									<weak_ptr>
										<expired>0</expired>
										<shared_ptr>
											<id>-79</id>
										</shared_ptr>
									</weak_ptr>
									<string>error_info_instance noalso type</string>
								</pair>
							</sorted>
						</tag_index>
					</object>
				</shared_ptr>
			</index>
		</reno_project>
	</object>
</shared_ptr>
