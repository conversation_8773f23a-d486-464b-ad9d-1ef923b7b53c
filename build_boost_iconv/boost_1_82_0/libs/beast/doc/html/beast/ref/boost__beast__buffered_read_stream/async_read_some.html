<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>buffered_read_stream::async_read_some</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Beast">
<link rel="up" href="../boost__beast__buffered_read_stream.html" title="buffered_read_stream">
<link rel="prev" href="read_some/overload2.html" title="buffered_read_stream::read_some (2 of 2 overloads)">
<link rel="next" href="write_some.html" title="buffered_read_stream::write_some">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="read_some/overload2.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__beast__buffered_read_stream.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="write_some.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="beast.ref.boost__beast__buffered_read_stream.async_read_some"></a><a class="link" href="async_read_some.html" title="buffered_read_stream::async_read_some">buffered_read_stream::async_read_some</a>
</h5></div></div></div>
<p>
          <a class="indexterm" name="idm18814"></a>
        </p>
<p>
          Start an asynchronous read.
        </p>
<h6>
<a name="beast.ref.boost__beast__buffered_read_stream.async_read_some.h0"></a>
          <span class="phrase"><a name="beast.ref.boost__beast__buffered_read_stream.async_read_some.synopsis"></a></span><a class="link" href="async_read_some.html#beast.ref.boost__beast__buffered_read_stream.async_read_some.synopsis">Synopsis</a>
        </h6>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">class</span> <a href="../../../../../../../doc/html/boost_asio/reference/MutableBufferSequence.html" target="_top"><span class="emphasis"><em>MutableBufferSequence</em></span></a><span class="special">,</span>
    <span class="keyword">class</span> <a href="../../../../../../../doc/html/boost_asio/reference/ReadHandler.html" target="_top"><span class="emphasis"><em>ReadHandler</em></span></a> <span class="special">=</span> <span class="identifier">net</span><span class="special">::</span><span class="identifier">default_completion_token_t</span><span class="special">&lt;</span><a class="link" href="executor_type.html" title="buffered_read_stream::executor_type">executor_type</a><span class="special">&gt;&gt;</span>
<a href="../../../../../../../doc/html/boost_asio/reference/asynchronous_operations.html#boost_asio.reference.asynchronous_operations.automatic_deduction_of_initiating_function_return_type" target="_top"><span class="emphasis"><em>DEDUCED</em></span></a>
<span class="identifier">async_read_some</span><span class="special">(</span>
    <span class="identifier">MutableBufferSequence</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">buffers</span><span class="special">,</span>
    <span class="identifier">ReadHandler</span><span class="special">&amp;&amp;</span> <span class="identifier">handler</span> <span class="special">=</span> <span class="identifier">net</span><span class="special">::</span><span class="identifier">default_completion_token_t</span><span class="special">&lt;</span> <a class="link" href="executor_type.html" title="buffered_read_stream::executor_type">executor_type</a> <span class="special">&gt;{});</span>
</pre>
<h6>
<a name="beast.ref.boost__beast__buffered_read_stream.async_read_some.h1"></a>
          <span class="phrase"><a name="beast.ref.boost__beast__buffered_read_stream.async_read_some.description"></a></span><a class="link" href="async_read_some.html#beast.ref.boost__beast__buffered_read_stream.async_read_some.description">Description</a>
        </h6>
<p>
          This function is used to asynchronously read data from the stream. The
          function call always returns immediately.
        </p>
<h6>
<a name="beast.ref.boost__beast__buffered_read_stream.async_read_some.h2"></a>
          <span class="phrase"><a name="beast.ref.boost__beast__buffered_read_stream.async_read_some.parameters"></a></span><a class="link" href="async_read_some.html#beast.ref.boost__beast__buffered_read_stream.async_read_some.parameters">Parameters</a>
        </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Name
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">buffers</span></code>
                  </p>
                </td>
<td>
                  <p>
                    One or more buffers into which the data will be read. Although
                    the buffers object may be copied as necessary, ownership of the
                    underlying memory blocks is retained by the caller, which must
                    guarantee that they remain valid until the handler is called.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">handler</span></code>
                  </p>
                </td>
<td>
                  <p>
                    The completion handler to invoke when the operation completes.
                    The implementation takes ownership of the handler by performing
                    a decay-copy. The equivalent function signature of the handler
                    must be:
                  </p>
<pre class="table-programlisting"><span class="keyword">void</span> <span class="identifier">handler</span><span class="special">(</span>
    <span class="identifier">error_code</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">error</span><span class="special">,</span>      <span class="comment">// result of operation</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <span class="identifier">bytes_transferred</span> <span class="comment">// number of bytes transferred</span>
<span class="special">);</span>
</pre>
                </td>
</tr>
</tbody>
</table></div>
<p>
          Regardless of whether the asynchronous operation completes immediately
          or not, the handler will not be invoked from within this function. Invocation
          of the handler will be performed in a manner equivalent to using <code class="computeroutput"><span class="identifier">net</span><span class="special">::</span><span class="identifier">post</span></code>.
        </p>
</div>
<div class="copyright-footer">Copyright © 2016-2019 Vinnie
      Falco<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="read_some/overload2.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__beast__buffered_read_stream.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="write_some.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
