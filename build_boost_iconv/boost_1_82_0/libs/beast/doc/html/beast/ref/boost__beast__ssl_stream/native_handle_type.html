<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ssl_stream::native_handle_type</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Boost.Beast">
<link rel="up" href="../boost__beast__ssl_stream.html" title="ssl_stream">
<link rel="prev" href="../boost__beast__ssl_stream.html" title="ssl_stream">
<link rel="next" href="impl_struct.html" title="ssl_stream::impl_struct">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../boost__beast__ssl_stream.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__beast__ssl_stream.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="impl_struct.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="beast.ref.boost__beast__ssl_stream.native_handle_type"></a><a class="link" href="native_handle_type.html" title="ssl_stream::native_handle_type">ssl_stream::native_handle_type</a>
</h5></div></div></div>
<p>
          <a class="indexterm" name="idm44715"></a>
        </p>
<p>
          The native handle type of the SSL stream.
        </p>
<h6>
<a name="beast.ref.boost__beast__ssl_stream.native_handle_type.h0"></a>
          <span class="phrase"><a name="beast.ref.boost__beast__ssl_stream.native_handle_type.synopsis"></a></span><a class="link" href="native_handle_type.html#beast.ref.boost__beast__ssl_stream.native_handle_type.synopsis">Synopsis</a>
        </h6>
<pre class="programlisting"><span class="keyword">using</span> <span class="identifier">native_handle_type</span> <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">ssl_stream_type</span><span class="special">::</span><span class="identifier">native_handle_type</span><span class="special">;</span>
</pre>
</div>
<div class="copyright-footer">Copyright © 2016-2019 Vinnie
      Falco<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../boost__beast__ssl_stream.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost__beast__ssl_stream.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="impl_struct.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
