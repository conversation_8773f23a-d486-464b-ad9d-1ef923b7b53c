<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>set_of Reference</title>
<link rel="stylesheet" href="../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Bimap">
<link rel="up" href="../reference.html" title="Reference">
<link rel="prev" href="bimap_reference.html" title="Bimap Reference">
<link rel="next" href="unordered_set_of_reference.html" title="unordered_set_of Reference">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="bimap_reference.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../reference.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="unordered_set_of_reference.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_bimap.reference.set_of_reference"></a><a class="link" href="set_of_reference.html" title="set_of Reference">set_of Reference</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.header__boost_bimap_set_of_hpp__synopsis">Header
        "boost/bimap/set_of.hpp" synopsis</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.header__boost_bimap_multiset_of_hpp__synopsis">Header
        "boost/bimap/multiset_of.hpp" synopsis</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.collection_type_specifiers_set_of_and_multiset_of">Collection
        type specifiers set_of and multiset_of</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views">[multi]set_of
        Views</a></span></dt>
<dd><dl>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.complexity_signature">Complexity
          signature</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.instantiation_types">Instantiation
          types</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.constructors__copy_and_assignment">Constructors,
          copy and assignment</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.modifiers">Modifiers</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.set_operations">Set
          operations</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.range_operations">Range
          operations</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.at____info_at___and_operator_______set_of_only">at(),
          info_at() and operator[] - set_of only</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.serialization">Serialization</a></span></dt>
</dl></dd>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_bimap.reference.set_of_reference.header__boost_bimap_set_of_hpp__synopsis"></a><a class="link" href="set_of_reference.html#boost_bimap.reference.set_of_reference.header__boost_bimap_set_of_hpp__synopsis" title='Header "boost/bimap/set_of.hpp" synopsis'>Header
        "boost/bimap/set_of.hpp" synopsis</a>
</h4></div></div></div>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
<span class="keyword">namespace</span> <span class="identifier">bimaps</span> <span class="special">{</span>


<span class="keyword">template</span>
<span class="special">&lt;</span>
    <span class="keyword">class</span> <span class="identifier">KeyType</span><span class="special">,</span>
    <span class="keyword">class</span> <span class="identifier">KeyCompare</span> <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span> <span class="identifier">KeyType</span> <span class="special">&gt;</span>
<span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">set_of</span><span class="special">;</span>


<span class="keyword">template</span>
<span class="special">&lt;</span>
    <span class="keyword">class</span> <span class="identifier">KeyCompare</span> <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span> <span class="identifier">_relation</span> <span class="special">&gt;</span>
<span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">set_of_relation</span><span class="special">;</span>


<span class="special">}</span> <span class="comment">// namespace bimap</span>
<span class="special">}</span> <span class="comment">// namespace boost</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_bimap.reference.set_of_reference.header__boost_bimap_multiset_of_hpp__synopsis"></a><a class="link" href="set_of_reference.html#boost_bimap.reference.set_of_reference.header__boost_bimap_multiset_of_hpp__synopsis" title='Header "boost/bimap/multiset_of.hpp" synopsis'>Header
        "boost/bimap/multiset_of.hpp" synopsis</a>
</h4></div></div></div>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
<span class="keyword">namespace</span> <span class="identifier">bimaps</span> <span class="special">{</span>


<span class="keyword">template</span>
<span class="special">&lt;</span>
    <span class="keyword">class</span> <span class="identifier">KeyType</span><span class="special">,</span>
    <span class="keyword">class</span> <span class="identifier">KeyCompare</span> <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span> <span class="identifier">KeyType</span> <span class="special">&gt;</span>
<span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">multiset_of</span><span class="special">;</span>


<span class="keyword">template</span>
<span class="special">&lt;</span>
    <span class="keyword">class</span> <span class="identifier">KeyCompare</span> <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span> <span class="identifier">_relation</span> <span class="special">&gt;</span>
<span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">multiset_of_relation</span><span class="special">;</span>


<span class="special">}</span> <span class="comment">// namespace bimap</span>
<span class="special">}</span> <span class="comment">// namespace boost</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_bimap.reference.set_of_reference.collection_type_specifiers_set_of_and_multiset_of"></a><a class="link" href="set_of_reference.html#boost_bimap.reference.set_of_reference.collection_type_specifiers_set_of_and_multiset_of" title="Collection type specifiers set_of and multiset_of">Collection
        type specifiers set_of and multiset_of</a>
</h4></div></div></div>
<p>
          These collection type specifiers allow for insertion of sets disallowing
          or allowing duplicate elements, respectively. The syntaxes of <code class="computeroutput"><span class="identifier">set_of</span></code> and <code class="computeroutput"><span class="identifier">multiset_of</span></code>
          coincide, so they are described together.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_bimap.reference.set_of_reference.__multi__set_of_views"></a><a class="link" href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views" title="[multi]set_of Views">[multi]set_of
        Views</a>
</h4></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.complexity_signature">Complexity
          signature</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.instantiation_types">Instantiation
          types</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.constructors__copy_and_assignment">Constructors,
          copy and assignment</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.modifiers">Modifiers</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.set_operations">Set
          operations</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.range_operations">Range
          operations</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.at____info_at___and_operator_______set_of_only">at(),
          info_at() and operator[] - set_of only</a></span></dt>
<dt><span class="section"><a href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.serialization">Serialization</a></span></dt>
</dl></div>
<p>
          A [multi]set_of set view is a std::[multi]set signature-compatible interface
          to the underlying heap of elements contained in a <code class="computeroutput"><span class="identifier">bimap</span></code>.
        </p>
<p>
          There are two variants: set_of, which does not allow duplicate elements
          (with respect to its associated comparison predicate) and multiset_of,
          which does accept those duplicates. The interface of these two variants
          is largely the same, so they are documented together with their differences
          explicitly noted where they exist.
        </p>
<p>
          If you look the bimap from a side, you will use a map view, and if you
          look at it as a whole, you will be using a set view.
        </p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
<span class="keyword">namespace</span> <span class="identifier">bimaps</span> <span class="special">{</span>
<span class="keyword">namespace</span> <span class="identifier">views</span> <span class="special">{</span>

<span class="keyword">template</span><span class="special">&lt;</span> <span class="emphasis"><em>-implementation defined parameter list-</em></span> <span class="special">&gt;</span>
<span class="keyword">class</span> <span class="emphasis"><em>-implementation defined view name-</em></span>
<span class="special">{</span>
    <span class="keyword">public</span><span class="special">:</span>

    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">key_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">value_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">key_compare</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">value_compare</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">allocator_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">reference</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">const_reference</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">iterator</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">const_iterator</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">size_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">difference_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">pointer</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">const_pointer</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">reverse_iterator</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">const_reverse_iterator</span><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">info_type</span><span class="special">;</span>

    <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="identifier">allocator_type</span> <span class="identifier">get_allocator</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="comment">// iterators</span>

    <span class="identifier">iterator</span>               <span class="identifier">begin</span><span class="special">();</span>
    <span class="identifier">const_iterator</span>         <span class="identifier">begin</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="identifier">iterator</span>               <span class="identifier">end</span><span class="special">();</span>
    <span class="identifier">const_iterator</span>         <span class="identifier">end</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="identifier">reverse_iterator</span>       <span class="identifier">rbegin</span><span class="special">();</span>
    <span class="identifier">const_reverse_iterator</span> <span class="identifier">rbegin</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="identifier">reverse_iterator</span>       <span class="identifier">rend</span><span class="special">();</span>
    <span class="identifier">const_reverse_iterator</span> <span class="identifier">rend</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="comment">// capacity</span>

    <span class="keyword">bool</span>      <span class="identifier">empty</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="identifier">size_type</span> <span class="identifier">size</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="identifier">size_type</span> <span class="identifier">max_size</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="comment">// modifiers</span>

    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">iterator</span><span class="special">,</span><span class="keyword">bool</span><span class="special">&gt;</span> <a class="link" href="set_of_reference.html#reference_set_of_insert_value">insert</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="identifier">iterator</span> <a class="link" href="set_of_reference.html#reference_set_of_insert_iterator_value">insert</a><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">value_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">InputIterator</span><span class="special">&gt;</span>
    <span class="keyword">void</span> <a class="link" href="set_of_reference.html#reference_set_of_insert_iterator_iterator">insert</a><span class="special">(</span><span class="identifier">InputIterator</span> <span class="identifier">first</span><span class="special">,</span>  <span class="identifier">InputIterator</span> <span class="identifier">last</span><span class="special">);</span>

    <span class="identifier">iterator</span> <a class="link" href="set_of_reference.html#reference_set_of_erase_iterator">erase</a><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">);</span>

    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
    <span class="identifier">size_type</span> <a class="link" href="set_of_reference.html#reference_set_of_erase_key">erase</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="identifier">iterator</span> <a class="link" href="set_of_reference.html#reference_set_of_erase_iterator_iterator">erase</a><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">first</span><span class="special">,</span>  <span class="identifier">iterator</span> <span class="identifier">last</span><span class="special">);</span>

    <span class="keyword">bool</span> <a class="link" href="set_of_reference.html#reference_set_of_replace_iterator_value">replace</a><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="comment">// Only in map views</span>
    <span class="comment">// {</span>

      <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
      <span class="keyword">bool</span> <a class="link" href="set_of_reference.html#reference_set_of_replace_key_iterator_key">replace_key</a><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

      <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleData</span> <span class="special">&gt;</span>
      <span class="keyword">bool</span> <a class="link" href="set_of_reference.html#reference_set_of_replace_data_iterator_data">replace_data</a><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CompatibleData</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

      <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">KeyModifier</span> <span class="special">&gt;</span>
      <span class="keyword">bool</span> <a class="link" href="set_of_reference.html#reference_set_of_modify_key_iterator_modifier">modify_key</a><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">,</span> <span class="identifier">KeyModifier</span> <span class="identifier">mod</span><span class="special">);</span>

      <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">DataModifier</span> <span class="special">&gt;</span>
      <span class="keyword">bool</span> <a class="link" href="set_of_reference.html#reference_set_of_modify_data_iterator_modifier">modify_data</a><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">,</span> <span class="identifier">DataModifier</span> <span class="identifier">mod</span><span class="special">);</span>

    <span class="comment">// }</span>

    <span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span><span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="keyword">void</span> <span class="identifier">clear</span><span class="special">();</span>

    <span class="comment">// observers</span>

    <span class="identifier">key_compare</span>    <span class="identifier">key_comp</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="identifier">value_compare</span>  <span class="identifier">value_comp</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="comment">// set operations</span>

    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
    <span class="identifier">iterator</span> <a class="link" href="set_of_reference.html#reference_set_of_find_key">find</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
    <span class="identifier">const_iterator</span> <a class="link" href="set_of_reference.html#reference_set_of_find_key">find</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>


    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
    <span class="identifier">size_type</span> <a class="link" href="set_of_reference.html#reference_set_of_count_key">count</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>


    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
    <span class="identifier">iterator</span> <a class="link" href="set_of_reference.html#reference_set_of_lower_bound_key">lower_bound</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
    <span class="identifier">const_iterator</span> <a class="link" href="set_of_reference.html#reference_set_of_lower_bound_key">lower_bound</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>


    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
    <span class="identifier">iterator</span> <a class="link" href="set_of_reference.html#reference_set_of_upper_bound_key">upper_bound</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
    <span class="identifier">const_iterator</span> <a class="link" href="set_of_reference.html#reference_set_of_upper_bound_key">upper_bound</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>


    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">iterator</span><span class="special">,</span><span class="identifier">iterator</span><span class="special">&gt;</span>
        <a class="link" href="set_of_reference.html#reference_set_of_equal_range_key">equal_range</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">const_iterator</span><span class="special">,</span><span class="identifier">const_iterator</span><span class="special">&gt;</span>
        <a class="link" href="set_of_reference.html#reference_set_of_equal_range_key">equal_range</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="comment">// Only in maps views</span>
    <span class="comment">// {</span>

    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">LowerBounder</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">UpperBounder</span><span class="special">&gt;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">iterator</span><span class="special">,</span><span class="identifier">iterator</span><span class="special">&gt;</span> <a class="link" href="set_of_reference.html#reference_set_of_range_lower_upper">range</a><span class="special">(</span>
        <span class="identifier">LowerBounder</span> <span class="identifier">lower</span><span class="special">,</span> <span class="identifier">UpperBounder</span> <span class="identifier">upper</span><span class="special">);</span>

    <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">LowerBounder</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">UpperBounder</span><span class="special">&gt;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">const_iterator</span><span class="special">,</span><span class="identifier">const_iterator</span><span class="special">&gt;</span> <a class="link" href="set_of_reference.html#reference_set_of_range_lower_upper">range</a><span class="special">(</span>
        <span class="identifier">LowerBounder</span> <span class="identifier">lower</span><span class="special">,</span> <span class="identifier">UpperBounder</span> <span class="identifier">upper</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">mapped_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="emphasis"><em>-unspecified-</em></span> <span class="identifier">data_type</span><span class="special">;</span> <span class="comment">// Equal to mapped_type</span>

      <span class="comment">// Only in for `set_of` collection type</span>
      <span class="comment">// {</span>

      <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
      <span class="keyword">const</span> <span class="identifier">mapped_type</span> <span class="special">&amp;</span> <a class="link" href="set_of_reference.html#reference_set_of_at_key_const">at</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">k</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>

        <span class="comment">// Only if the other collection type is mutable</span>
        <span class="comment">// {</span>

        <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
        <span class="identifier">mapped_type</span> <span class="special">&amp;</span> <a class="link" href="set_of_reference.html#reference_set_of_operator_bracket_key">operator[]</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">k</span><span class="special">);</span>

        <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
        <span class="identifier">mapped_type</span> <span class="special">&amp;</span> <a class="link" href="set_of_reference.html#reference_set_of_at_key">at</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">k</span><span class="special">);</span>

        <span class="comment">// }</span>

        <span class="comment">// Only if info_hook is used</span>
        <span class="comment">// {</span>

        <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
        <span class="identifier">info_type</span> <span class="special">&amp;</span> <a class="link" href="set_of_reference.html#reference_set_of_info_at_key">info_at</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">k</span><span class="special">);</span>

        <span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
        <span class="keyword">const</span> <span class="identifier">info_type</span> <span class="special">&amp;</span> <a class="link" href="set_of_reference.html#reference_set_of_info_at_key">info_at</a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">k</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>

        <span class="comment">// }</span>

      <span class="comment">// }</span>

    <span class="comment">// }</span>
<span class="special">};</span>

<span class="comment">// view comparison</span>

<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span><span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">v1</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">v2</span> <span class="special">);</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;</span> <span class="special">(</span><span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">v1</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">v2</span> <span class="special">);</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span><span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">v1</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">v2</span> <span class="special">);</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;</span> <span class="special">(</span><span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">v1</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">v2</span> <span class="special">);</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span><span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">v1</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">v2</span> <span class="special">);</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span><span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">v1</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">v2</span> <span class="special">);</span>

<span class="special">}</span> <span class="comment">// namespace views</span>
<span class="special">}</span> <span class="comment">// namespace bimap</span>
<span class="special">}</span> <span class="comment">// namespace boost</span>
</pre>
<p>
          In the case of a <code class="computeroutput"><span class="identifier">bimap</span><span class="special">&lt;</span> <span class="special">{</span><span class="identifier">multi</span><span class="special">}</span><span class="identifier">set_of</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;,</span> <span class="special">...</span> <span class="special">&gt;</span></code>
        </p>
<p>
          In the set view:
        </p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">signature</span><span class="special">-</span><span class="identifier">compatible</span> <span class="identifier">with</span> <span class="identifier">relation</span><span class="special">&lt;</span>       <span class="identifier">Left</span><span class="special">,</span> <span class="special">...</span> <span class="special">&gt;</span> <span class="identifier">key_type</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">signature</span><span class="special">-</span><span class="identifier">compatible</span> <span class="identifier">with</span> <span class="identifier">relation</span><span class="special">&lt;</span> <span class="keyword">const</span> <span class="identifier">Left</span><span class="special">,</span> <span class="special">...</span> <span class="special">&gt;</span> <span class="identifier">value_type</span><span class="special">;</span>
</pre>
<p>
          In the left map view:
        </p>
<pre class="programlisting"><span class="keyword">typedef</span>  <span class="identifier">Left</span>  <span class="identifier">key_type</span><span class="special">;</span>
<span class="keyword">typedef</span>  <span class="special">...</span>   <span class="identifier">mapped_type</span><span class="special">;</span>

<span class="keyword">typedef</span> <span class="identifier">signature</span><span class="special">-</span><span class="identifier">compatible</span> <span class="identifier">with</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="keyword">const</span> <span class="identifier">Left</span><span class="special">,</span> <span class="special">...</span> <span class="special">&gt;</span> <span class="identifier">value_type</span><span class="special">;</span>
</pre>
<p>
          In the right map view:
        </p>
<pre class="programlisting"><span class="keyword">typedef</span>  <span class="special">...</span>  <span class="identifier">key_type</span><span class="special">;</span>
<span class="keyword">typedef</span>  <span class="identifier">Left</span> <span class="identifier">mapped_type</span><span class="special">;</span>

<span class="keyword">typedef</span> <span class="identifier">signature</span><span class="special">-</span><span class="identifier">compatible</span> <span class="identifier">with</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="special">...</span> <span class="special">,</span><span class="keyword">const</span> <span class="identifier">Left</span> <span class="special">&gt;</span> <span class="identifier">value_type</span><span class="special">;</span>
</pre>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_bimap.reference.set_of_reference.__multi__set_of_views.complexity_signature"></a><a name="set_of_complexity_signature"></a><a class="link" href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.complexity_signature" title="Complexity signature">Complexity
          signature</a>
</h5></div></div></div>
<p>
            Here and in the descriptions of operations of this view, we adopt the
            scheme outlined in the <a class="link" href="bimap_reference.html#complexity_signature_explanation">complexity
            signature section</a>. The complexity signature of [multi]set_of view
            is:
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                copying: <code class="computeroutput"><span class="identifier">c</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span> <span class="special">=</span> <span class="identifier">n</span> <span class="special">*</span> <span class="identifier">log</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span></code>,
              </li>
<li class="listitem">
                insertion: <code class="computeroutput"><span class="identifier">i</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span> <span class="special">=</span> <span class="identifier">log</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span></code>,
              </li>
<li class="listitem">
                hinted insertion: <code class="computeroutput"><span class="identifier">h</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span> <span class="special">=</span> <span class="number">1</span></code> (constant) if the hint element precedes
                the point of insertion, <code class="computeroutput"><span class="identifier">h</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span> <span class="special">=</span> <span class="identifier">log</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span></code>
                otherwise,
              </li>
<li class="listitem">
                deletion: <code class="computeroutput"><span class="identifier">d</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span> <span class="special">=</span> <span class="number">1</span></code>
                (amortized constant),
              </li>
<li class="listitem">
                replacement: <code class="computeroutput"><span class="identifier">r</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span> <span class="special">=</span> <span class="number">1</span></code>
                (constant) if the element position does not change, <code class="computeroutput"><span class="identifier">r</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span> <span class="special">=</span> <span class="identifier">log</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span></code> otherwise,
              </li>
<li class="listitem">
                modifying: <code class="computeroutput"><span class="identifier">m</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span> <span class="special">=</span> <span class="number">1</span></code>
                (constant) if the element position does not change, <code class="computeroutput"><span class="identifier">m</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span> <span class="special">=</span> <span class="identifier">log</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span></code> otherwise.
              </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_bimap.reference.set_of_reference.__multi__set_of_views.instantiation_types"></a><a class="link" href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.instantiation_types" title="Instantiation types">Instantiation
          types</a>
</h5></div></div></div>
<p>
            Set views are instantiated internally to a <code class="computeroutput"><span class="identifier">bimap</span></code>.
            Instantiations are dependent on the following types:
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <code class="computeroutput"><span class="identifier">Value</span></code> from the set
                specifier,
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">Allocator</span></code> from <code class="computeroutput"><span class="identifier">bimap</span></code>,
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">Compare</span></code> from the
                set specifier.
              </li>
</ul></div>
<p>
            <code class="computeroutput"><span class="identifier">Compare</span></code> is a <a href="https://www.boost.org/sgi/stl/StrictWeakOrdering.html" target="_top">Strict
            Weak Ordering</a> on elements of <code class="computeroutput"><span class="identifier">Value</span></code>.
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_bimap.reference.set_of_reference.__multi__set_of_views.constructors__copy_and_assignment"></a><a class="link" href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.constructors__copy_and_assignment" title="Constructors, copy and assignment">Constructors,
          copy and assignment</a>
</h5></div></div></div>
<p>
            Set views do not have public constructors or destructors. Assignment,
            on the other hand, is provided.
          </p>
<pre class="programlisting"><span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="keyword">const</span> <span class="identifier">this_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Effects: </strong></span> <code class="computeroutput"><span class="identifier">a</span>
                <span class="special">=</span> <span class="identifier">b</span><span class="special">;</span></code> where a and b are the <code class="computeroutput"><span class="identifier">bimap</span></code> objects to which <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>
                and x belong, respectively.
              </li>
<li class="listitem">
                <span class="bold"><strong>Returns: </strong></span> <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>.
              </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_bimap.reference.set_of_reference.__multi__set_of_views.modifiers"></a><a class="link" href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.modifiers" title="Modifiers">Modifiers</a>
</h5></div></div></div>
<a name="reference_set_of_insert_value"></a><pre class="programlisting"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">iterator</span><span class="special">,</span><span class="keyword">bool</span><span class="special">&gt;</span> <span class="identifier">insert</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Inserts <code class="computeroutput"><span class="identifier">x</span></code>
                into the <code class="computeroutput"><span class="identifier">bimap</span></code> to
                which the set view belongs if
                <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                      the set view is non-unique OR no other element with equivalent
                      key exists,
                    </li>
<li class="listitem">
                      AND insertion is allowed by the other set specifications the
                      <code class="computeroutput"><span class="identifier">bimap</span></code>.
                    </li>
</ul></div>
              </li>
<li class="listitem">
                <span class="bold"><strong>Returns:</strong></span> The return value is a pair
                <code class="computeroutput"><span class="identifier">p</span></code>. <code class="computeroutput"><span class="identifier">p</span><span class="special">.</span><span class="identifier">second</span></code> is <code class="computeroutput"><span class="keyword">true</span></code>
                if and only if insertion took place. On successful insertion, <code class="computeroutput"><span class="identifier">p</span><span class="special">.</span><span class="identifier">first</span></code> points to the element inserted;
                otherwise, <code class="computeroutput"><span class="identifier">p</span><span class="special">.</span><span class="identifier">first</span></code> points to an element that
                caused the insertion to be banned. Note that more than one element
                can be causing insertion not to be allowed.
              </li>
<li class="listitem">
                <a class="link" href="set_of_reference.html#set_of_complexity_signature"><span class="bold"><strong>Complexity:</strong></span></a>
                O(I(n)).
              </li>
<li class="listitem">
                <span class="bold"><strong>Exception safety:</strong></span> Strong.
              </li>
</ul></div>
<a name="reference_set_of_insert_iterator_value"></a><pre class="programlisting"><span class="identifier">iterator</span> <span class="identifier">insert</span><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">value_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">position</span></code>
                is a valid iterator of the view.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects: </strong></span> <code class="computeroutput"><span class="identifier">position</span></code>
                is used as a hint to improve the efficiency of the operation. Inserts
                <code class="computeroutput"><span class="identifier">x</span></code> into the <code class="computeroutput"><span class="identifier">bimap</span></code> to which the view belongs
                if
                <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                      the set view is non-unique OR no other element with equivalent
                      key exists,
                    </li>
<li class="listitem">
                      AND insertion is allowed by all other views of the <code class="computeroutput"><span class="identifier">bimap</span></code>.
                    </li>
</ul></div>
              </li>
<li class="listitem">
                <span class="bold"><strong>Returns:</strong></span> On successful insertion,
                an iterator to the newly inserted element. Otherwise, an iterator
                to an element that caused the insertion to be banned. Note that more
                than one element can be causing insertion not to be allowed.
              </li>
<li class="listitem">
                <a class="link" href="set_of_reference.html#set_of_complexity_signature"><span class="bold"><strong>Complexity:</strong></span></a>
                O(H(n)).
              </li>
<li class="listitem">
                <span class="bold"><strong>Exception safety:</strong></span> Strong.
              </li>
</ul></div>
<a name="reference_set_of_insert_iterator_iterator"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">InputIterator</span> <span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">insert</span><span class="special">(</span><span class="identifier">InputIterator</span> <span class="identifier">first</span><span class="special">,</span> <span class="identifier">InputIterator</span> <span class="identifier">last</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">InputIterator</span></code>
                is a model of <a href="https://www.boost.org/sgi/stl/InputIterator.html" target="_top">Input
                Iterator</a> over elements of type <code class="computeroutput"><span class="identifier">value_type</span></code>
                or a type convertible to value_type. <code class="computeroutput"><span class="identifier">first</span></code>
                and <code class="computeroutput"><span class="identifier">last</span></code> are not
                iterators into any view of the <code class="computeroutput"><span class="identifier">bimap</span></code>
                to which this index belongs. <code class="computeroutput"><span class="identifier">last</span></code>
                is reachable from <code class="computeroutput"><span class="identifier">first</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects: </strong></span> <code class="computeroutput"><span class="identifier">iterator</span>
                <span class="identifier">hint</span> <span class="special">=</span>
                <span class="identifier">end</span><span class="special">()</span></code>;
                <code class="computeroutput"><span class="keyword">while</span><span class="special">(</span>
                <span class="identifier">first</span> <span class="special">!=</span>
                <span class="identifier">last</span> <span class="special">)</span>
                <span class="identifier">hint</span> <span class="special">=</span>
                <span class="identifier">insert</span><span class="special">(</span>
                <span class="identifier">hint</span><span class="special">,</span>
                <span class="special">*</span><span class="identifier">first</span><span class="special">++</span> <span class="special">);</span></code>
              </li>
<li class="listitem">
                <a class="link" href="set_of_reference.html#set_of_complexity_signature"><span class="bold"><strong>Complexity:</strong></span></a>
                O(m*H(n+m)), where m is the number of elements in <code class="computeroutput"><span class="special">[</span><span class="identifier">first</span><span class="special">,</span>
                <span class="identifier">last</span><span class="special">)</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Exception safety:</strong></span> Basic.
              </li>
</ul></div>
<a name="reference_set_of_erase_iterator"></a><pre class="programlisting"><span class="identifier">iterator</span> <span class="identifier">erase</span><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">position</span></code>
                is a valid dereferenceable iterator if the set view.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Deletes the element pointed
                to by <code class="computeroutput"><span class="identifier">position</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Returns:</strong></span> An iterator pointing to
                the element immediately following the one that was deleted, or <code class="computeroutput"><span class="identifier">end</span><span class="special">()</span></code>
                if no such element exists.
              </li>
<li class="listitem">
                <a class="link" href="set_of_reference.html#set_of_complexity_signature"><span class="bold"><strong>Complexity:</strong></span></a>
                O(D(n)).
              </li>
<li class="listitem">
                <span class="bold"><strong>Exception safety:</strong></span> nothrow.
              </li>
</ul></div>
<a name="reference_set_of_erase_key"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="identifier">size_type</span> <span class="identifier">erase</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">CompatibleKey</span></code>
                is a compatible key of <code class="computeroutput"><span class="identifier">key_compare</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Deletes the elements with
                key equivalent to <code class="computeroutput"><span class="identifier">x</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Returns:</strong></span> Number of elements deleted.
              </li>
<li class="listitem">
                <a class="link" href="set_of_reference.html#set_of_complexity_signature"><span class="bold"><strong>Complexity:</strong></span></a>
                O(log(n) + m*D(n)), where m is the number of elements deleted.
              </li>
<li class="listitem">
                <span class="bold"><strong>Exception safety:</strong></span> Basic.
              </li>
</ul></div>
<a name="reference_set_of_erase_iterator_iterator"></a><pre class="programlisting"><span class="identifier">iterator</span> <span class="identifier">erase</span><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">first</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="identifier">last</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="special">[</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">last</span><span class="special">)</span></code>
                is a valid range of the view.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Deletes the elements in
                <code class="computeroutput"><span class="special">[</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">last</span><span class="special">)</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Returns:</strong></span> last.
              </li>
<li class="listitem">
                <a class="link" href="set_of_reference.html#set_of_complexity_signature"><span class="bold"><strong>Complexity:</strong></span></a>
                O(log(n) + m*D(n)), where m is the number of elements in <code class="computeroutput"><span class="special">[</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">last</span><span class="special">)</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Exception safety:</strong></span> nothrow.
              </li>
</ul></div>
<a name="reference_set_of_replace_iterator_value"></a><pre class="programlisting"><span class="keyword">bool</span> <span class="identifier">replace</span><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">position</span></code>
                is a valid dereferenceable iterator of the set view.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Assigns the value <code class="computeroutput"><span class="identifier">x</span></code> to the element pointed to by
                <code class="computeroutput"><span class="identifier">position</span></code> into the
                <code class="computeroutput"><span class="identifier">bimap</span></code> to which the
                set view belongs if, for the value <code class="computeroutput"><span class="identifier">x</span></code>
                <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                      the set view is non-unique OR no other element with equivalent
                      key exists (except possibly <code class="computeroutput"><span class="special">*</span><span class="identifier">position</span></code>),
                    </li>
<li class="listitem">
                      AND replacing is allowed by all other views of the <code class="computeroutput"><span class="identifier">bimap</span></code>.
                    </li>
</ul></div>
              </li>
<li class="listitem">
                <span class="bold"><strong>Postconditions:</strong></span> Validity of position
                is preserved in all cases.
              </li>
<li class="listitem">
                <span class="bold"><strong>Returns: </strong></span> <code class="computeroutput"><span class="keyword">true</span></code>
                if the replacement took place, <code class="computeroutput"><span class="keyword">false</span></code>
                otherwise.
              </li>
<li class="listitem">
                <a class="link" href="set_of_reference.html#set_of_complexity_signature"><span class="bold"><strong>Complexity:</strong></span></a>
                O(R(n)).
              </li>
<li class="listitem">
                <span class="bold"><strong>Exception safety:</strong></span> Strong. If an
                exception is thrown by some user-provided operation, the <code class="computeroutput"><span class="identifier">bimap</span></code> to which the set view belongs
                remains in its original state.
              </li>
</ul></div>
<a name="reference_set_of_replace_key_iterator_key"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">replace_key</span><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">position</span></code>
                is a valid dereferenceable iterator of the set view. <code class="computeroutput"><span class="identifier">CompatibleKey</span></code> can be assigned to
                <code class="computeroutput"><span class="identifier">key_type</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Assigns the value <code class="computeroutput"><span class="identifier">x</span></code> to <code class="computeroutput"><span class="identifier">e</span><span class="special">.</span><span class="identifier">first</span></code>,
                where <code class="computeroutput"><span class="identifier">e</span></code> is the element
                pointed to by <code class="computeroutput"><span class="identifier">position</span></code>
                into the <code class="computeroutput"><span class="identifier">bimap</span></code> to
                which the set view belongs if,
                <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                      the map view is non-unique OR no other element with equivalent
                      key exists (except possibly <code class="computeroutput"><span class="special">*</span><span class="identifier">position</span></code>),
                    </li>
<li class="listitem">
                      AND replacing is allowed by all other views of the <code class="computeroutput"><span class="identifier">bimap</span></code>.
                    </li>
</ul></div>
              </li>
<li class="listitem">
                <span class="bold"><strong>Postconditions:</strong></span> Validity of position
                is preserved in all cases.
              </li>
<li class="listitem">
                <span class="bold"><strong>Returns: </strong></span> <code class="computeroutput"><span class="keyword">true</span></code>
                if the replacement took place, <code class="computeroutput"><span class="keyword">false</span></code>
                otherwise.
              </li>
<li class="listitem">
                <a class="link" href="set_of_reference.html#set_of_complexity_signature"><span class="bold"><strong>Complexity:</strong></span></a>
                O(R(n)).
              </li>
<li class="listitem">
                <span class="bold"><strong>Exception safety:</strong></span> Strong. If an
                exception is thrown by some user-provided operation, the <code class="computeroutput"><span class="identifier">bimap</span></code> to which the set view belongs
                remains in its original state.
              </li>
</ul></div>
<a name="reference_set_of_replace_data_iterator_data"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleData</span> <span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">replace_data</span><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CompatibleData</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">position</span></code>
                is a valid dereferenceable iterator of the set view. <code class="computeroutput"><span class="identifier">CompatibleKey</span></code> can be assigned to
                <code class="computeroutput"><span class="identifier">mapped_type</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Assigns the value <code class="computeroutput"><span class="identifier">x</span></code> to <code class="computeroutput"><span class="identifier">e</span><span class="special">.</span><span class="identifier">second</span></code>,
                where <code class="computeroutput"><span class="identifier">e</span></code> is the element
                pointed to by <code class="computeroutput"><span class="identifier">position</span></code>
                into the <code class="computeroutput"><span class="identifier">bimap</span></code> to
                which the set view belongs if,
                <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                      the map view is non-unique OR no other element with equivalent
                      key exists (except possibly <code class="computeroutput"><span class="special">*</span><span class="identifier">position</span></code>),
                    </li>
<li class="listitem">
                      AND replacing is allowed by all other views of the <code class="computeroutput"><span class="identifier">bimap</span></code>.
                    </li>
</ul></div>
              </li>
<li class="listitem">
                <span class="bold"><strong>Postconditions:</strong></span> Validity of position
                is preserved in all cases.
              </li>
<li class="listitem">
                <span class="bold"><strong>Returns: </strong></span> <code class="computeroutput"><span class="keyword">true</span></code>
                if the replacement took place, <code class="computeroutput"><span class="keyword">false</span></code>
                otherwise.
              </li>
<li class="listitem">
                <a class="link" href="set_of_reference.html#set_of_complexity_signature"><span class="bold"><strong>Complexity:</strong></span></a>
                O(R(n)).
              </li>
<li class="listitem">
                <span class="bold"><strong>Exception safety:</strong></span> Strong. If an
                exception is thrown by some user-provided operation, the <code class="computeroutput"><span class="identifier">bimap</span></code> to which the set view belongs
                remains in its original state.
              </li>
</ul></div>
<a name="reference_set_of_modify_key_iterator_modifier"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">KeyModifier</span> <span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">modify_key</span><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">,</span> <span class="identifier">KeyModifier</span> <span class="identifier">mod</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">KeyModifier</span></code>
                is a model of <a href="https://www.boost.org/sgi/stl/UnaryFunction.html" target="_top">Unary
                Function</a> accepting arguments of type: <code class="computeroutput"><span class="identifier">key_type</span><span class="special">&amp;</span></code>; <code class="computeroutput"><span class="identifier">position</span></code>
                is a valid dereferenceable iterator of the view.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Calls <code class="computeroutput"><span class="identifier">mod</span><span class="special">(</span><span class="identifier">e</span><span class="special">.</span><span class="identifier">first</span><span class="special">)</span></code> where e is the element pointed to
                by position and rearranges <code class="computeroutput"><span class="special">*</span><span class="identifier">position</span></code> into all the views of
                the <code class="computeroutput"><span class="identifier">bimap</span></code>. If the
                rearrangement fails, the element is erased. Rearrangement is successful
                if
                <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                      the map view is non-unique OR no other element with equivalent
                      key exists,
                    </li>
<li class="listitem">
                      AND rearrangement is allowed by all other views of the <code class="computeroutput"><span class="identifier">bimap</span></code>.
                    </li>
</ul></div>
              </li>
<li class="listitem">
                <span class="bold"><strong>Postconditions:</strong></span> Validity of <code class="computeroutput"><span class="identifier">position</span></code> is preserved if the operation
                succeeds.
              </li>
<li class="listitem">
                <span class="bold"><strong>Returns: </strong></span> <code class="computeroutput"><span class="keyword">true</span></code>
                if the operation succeeded, <code class="computeroutput"><span class="keyword">false</span></code>
                otherwise.
              </li>
<li class="listitem">
                <a class="link" href="set_of_reference.html#set_of_complexity_signature"><span class="bold"><strong>Complexity:</strong></span></a>
                O(M(n)).
              </li>
<li class="listitem">
                <span class="bold"><strong>Exception safety:</strong></span> Basic. If an exception
                is thrown by some user-provided operation (except possibly mod),
                then the element pointed to by position is erased.
              </li>
<li class="listitem">
                <span class="bold"><strong>Note:</strong></span> Only provided for map views.
              </li>
</ul></div>
<a name="reference_set_of_modify_data_iterator_modifier"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">DataModifier</span> <span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">modify_data</span><span class="special">(</span><span class="identifier">iterator</span> <span class="identifier">position</span><span class="special">,</span> <span class="identifier">DataModifier</span> <span class="identifier">mod</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">DataModifier</span></code>
                is a model of <a href="https://www.boost.org/sgi/stl/UnaryFunction.html" target="_top">Unary
                Function</a> accepting arguments of type: <code class="computeroutput"><span class="identifier">mapped_type</span><span class="special">&amp;</span></code>; <code class="computeroutput"><span class="identifier">position</span></code>
                is a valid dereferenceable iterator of the view.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Calls <code class="computeroutput"><span class="identifier">mod</span><span class="special">(</span><span class="identifier">e</span><span class="special">.</span><span class="identifier">second</span><span class="special">)</span></code> where e is the element pointed to
                by position and rearranges <code class="computeroutput"><span class="special">*</span><span class="identifier">position</span></code> into all the views of
                the <code class="computeroutput"><span class="identifier">bimap</span></code>. If the
                rearrangement fails, the element is erased. Rearrangement is successful
                if
                <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                      the oppositte map view is non-unique OR no other element with
                      equivalent key in that view exists,
                    </li>
<li class="listitem">
                      AND rearrangement is allowed by all other views of the <code class="computeroutput"><span class="identifier">bimap</span></code>.
                    </li>
</ul></div>
              </li>
<li class="listitem">
                <span class="bold"><strong>Postconditions:</strong></span> Validity of <code class="computeroutput"><span class="identifier">position</span></code> is preserved if the operation
                succeeds.
              </li>
<li class="listitem">
                <span class="bold"><strong>Returns: </strong></span> <code class="computeroutput"><span class="keyword">true</span></code>
                if the operation succeeded, <code class="computeroutput"><span class="keyword">false</span></code>
                otherwise.
              </li>
<li class="listitem">
                <a class="link" href="set_of_reference.html#set_of_complexity_signature"><span class="bold"><strong>Complexity:</strong></span></a>
                O(M(n)).
              </li>
<li class="listitem">
                <span class="bold"><strong>Exception safety:</strong></span> Basic. If an exception
                is thrown by some user-provided operation (except possibly mod),
                then the element pointed to by position is erased.
              </li>
<li class="listitem">
                <span class="bold"><strong>Note:</strong></span> Only provided for map views.
              </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_bimap.reference.set_of_reference.__multi__set_of_views.set_operations"></a><a class="link" href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.set_operations" title="Set operations">Set
          operations</a>
</h5></div></div></div>
<p>
            <code class="computeroutput"><span class="special">[</span><span class="identifier">multi</span><span class="special">]</span><span class="identifier">set_of</span></code>
            views provide the full lookup functionality required by <a href="https://www.boost.org/sgi/stl/SortedAssociativeContainer.html" target="_top">Sorted
            Associative Container</a> and <a href="https://www.boost.org/sgi/stl/UniqueAssociativeContainer.html" target="_top">Unique
            Associative Container</a>, namely <code class="computeroutput"><span class="identifier">find</span></code>,
            <code class="computeroutput"><span class="identifier">count</span></code>, <code class="computeroutput"><span class="identifier">lower_bound</span></code>, <code class="computeroutput"><span class="identifier">upper_bound</span></code>
            and <code class="computeroutput"><span class="identifier">equal_range</span></code>. Additionally,
            these member functions are templatized to allow for non-standard arguments,
            so extending the types of search operations allowed.
          </p>
<p>
            A type <code class="computeroutput"><span class="identifier">CompatibleKey</span></code>
            is said to be a <span class="emphasis"><em>compatible key</em></span> of <code class="computeroutput"><span class="identifier">Compare</span></code> if <code class="computeroutput"><span class="special">(</span><span class="identifier">CompatibleKey</span><span class="special">,</span>
            <span class="identifier">Compare</span><span class="special">)</span></code>
            is a compatible extension of <code class="computeroutput"><span class="identifier">Compare</span></code>.
            This implies that <code class="computeroutput"><span class="identifier">Compare</span></code>,
            as well as being a strict weak ordering, accepts arguments of type <code class="computeroutput"><span class="identifier">CompatibleKey</span></code>, which usually means
            it has several overloads of <code class="computeroutput"><span class="keyword">operator</span><span class="special">()</span></code>.
          </p>
<a name="reference_set_of_find_key"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="identifier">iterator</span> <span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="identifier">const_iterator</span> <span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">CompatibleKey</span></code>
                is a compatible key of <code class="computeroutput"><span class="identifier">key_compare</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Returns a pointer to an
                element whose key is equivalent to <code class="computeroutput"><span class="identifier">x</span></code>,
                or <code class="computeroutput"><span class="identifier">end</span><span class="special">()</span></code>
                if such an element does not exist.
              </li>
<li class="listitem">
                <span class="bold"><strong>Complexity:</strong></span> O(log(n)).
              </li>
</ul></div>
<a name="reference_set_of_count_key"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="identifier">size_type</span> <span class="identifier">count</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">CompatibleKey</span></code>
                is a compatible key of <code class="computeroutput"><span class="identifier">key_compare</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Returns the number of elements
                with key equivalent to <code class="computeroutput"><span class="identifier">x</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Complexity:</strong></span> O(log(n) + count(x)).
              </li>
</ul></div>
<a name="reference_set_of_lower_bound_key"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="identifier">iterator</span> <span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="identifier">const_iterator</span> <span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">CompatibleKey</span></code>
                is a compatible key of <code class="computeroutput"><span class="identifier">key_compare</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Returns an iterator pointing
                to the first element with key not less than <code class="computeroutput"><span class="identifier">x</span></code>,
                or <code class="computeroutput"><span class="identifier">end</span><span class="special">()</span></code>
                if such an element does not exist.
              </li>
<li class="listitem">
                <span class="bold"><strong>Complexity:</strong></span> O(log(n)).
              </li>
</ul></div>
<a name="reference_set_of_upper_bound_key"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="identifier">iterator</span> <span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="identifier">const_iterator</span> <span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">CompatibleKey</span></code>
                is a compatible key of <code class="computeroutput"><span class="identifier">key_compare</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Returns an iterator pointing
                to the first element with key greater than <code class="computeroutput"><span class="identifier">x</span></code>,
                or <code class="computeroutput"><span class="identifier">end</span><span class="special">()</span></code>
                if such an element does not exist.
              </li>
<li class="listitem">
                <span class="bold"><strong>Complexity:</strong></span> O(log(n)).
              </li>
</ul></div>
<a name="reference_set_of_equal_range_key"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">iterator</span><span class="special">,</span><span class="identifier">iterator</span><span class="special">&gt;</span>
    <span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">const_iterator</span><span class="special">,</span><span class="identifier">const_iterator</span><span class="special">&gt;</span>
    <span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">CompatibleKey</span></code>
                is a compatible key of <code class="computeroutput"><span class="identifier">key_compare</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Equivalent to <code class="computeroutput"><span class="identifier">make_pair</span><span class="special">(</span><span class="identifier">lower_bound</span><span class="special">(</span><span class="identifier">x</span><span class="special">),</span><span class="identifier">upper_bound</span><span class="special">(</span><span class="identifier">x</span><span class="special">))</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Complexity:</strong></span> O(log(n)).
              </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_bimap.reference.set_of_reference.__multi__set_of_views.range_operations"></a><a class="link" href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.range_operations" title="Range operations">Range
          operations</a>
</h5></div></div></div>
<p>
            The member function range is not defined for sorted associative containers,
            but <code class="computeroutput"><span class="special">[</span><span class="identifier">multi</span><span class="special">]</span><span class="identifier">set_of</span></code>
            map views provide it as a convenient utility. A range or interval is
            defined by two conditions for the lower and upper bounds, which are modelled
            after the following concepts.
          </p>
<p>
            Consider a <a href="https://www.boost.org/sgi/stl/StrictWeakOrdering.html" target="_top">Strict
            Weak Ordering</a> <code class="computeroutput"><span class="identifier">Compare</span></code>
            over values of type Key. A type <code class="computeroutput"><span class="identifier">LowerBounder</span></code>
            is said to be a lower bounder of <code class="computeroutput"><span class="identifier">Compare</span></code>
            if
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <code class="computeroutput"><span class="identifier">LowerBounder</span></code> is a
                <code class="computeroutput"><span class="identifier">Predicate</span></code> over <code class="computeroutput"><span class="identifier">Key</span></code>,
              </li>
<li class="listitem">
                if <code class="computeroutput"><span class="identifier">lower</span><span class="special">(</span><span class="identifier">k1</span><span class="special">)</span></code>
                and <code class="computeroutput"><span class="special">!</span><span class="identifier">comp</span><span class="special">(</span><span class="identifier">k2</span><span class="special">,</span><span class="identifier">k1</span><span class="special">)</span></code> then <code class="computeroutput"><span class="identifier">lower</span><span class="special">(</span><span class="identifier">k2</span><span class="special">)</span></code>,
              </li>
</ul></div>
<p>
            for every <code class="computeroutput"><span class="identifier">lower</span></code> of type
            <code class="computeroutput"><span class="identifier">LowerBounder</span></code>, <code class="computeroutput"><span class="identifier">comp</span></code> of type <code class="computeroutput"><span class="identifier">Compare</span></code>,
            and <code class="computeroutput"><span class="identifier">k1</span></code>, <code class="computeroutput"><span class="identifier">k2</span></code> of type <code class="computeroutput"><span class="identifier">Key</span></code>.
            Similarly, an upper bounder is a type <code class="computeroutput"><span class="identifier">UpperBounder</span></code>
            such that
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <code class="computeroutput"><span class="identifier">UpperBounder</span></code> is a
                <code class="computeroutput"><span class="identifier">Predicate</span></code> over <code class="computeroutput"><span class="identifier">Key</span></code>,
              </li>
<li class="listitem">
                if <code class="computeroutput"><span class="identifier">upper</span><span class="special">(</span><span class="identifier">k1</span><span class="special">)</span></code>
                and <code class="computeroutput"><span class="special">!</span><span class="identifier">comp</span><span class="special">(</span><span class="identifier">k1</span><span class="special">,</span><span class="identifier">k2</span><span class="special">)</span></code> then <code class="computeroutput"><span class="identifier">upper</span><span class="special">(</span><span class="identifier">k2</span><span class="special">)</span></code>,
              </li>
</ul></div>
<p>
            for every <code class="computeroutput"><span class="identifier">upper</span></code> of type
            <code class="computeroutput"><span class="identifier">UpperBounder</span></code>, <code class="computeroutput"><span class="identifier">comp</span></code> of type <code class="computeroutput"><span class="identifier">Compare</span></code>,
            and <code class="computeroutput"><span class="identifier">k1</span></code>, <code class="computeroutput"><span class="identifier">k2</span></code> of type <code class="computeroutput"><span class="identifier">Key</span></code>.
          </p>
<a name="reference_set_of_range_lower_upper"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">LowerBounder</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">UpperBounder</span><span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">const_iterator</span><span class="special">,</span><span class="identifier">const_iterator</span><span class="special">&gt;</span> <span class="identifier">range</span><span class="special">(</span>
    <span class="identifier">LowerBounder</span> <span class="identifier">lower</span><span class="special">,</span> <span class="identifier">UpperBounder</span> <span class="identifier">upper</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">LowerBounder</span></code>
                and <code class="computeroutput"><span class="identifier">UpperBounder</span></code>
                are a lower and upper bounder of <code class="computeroutput"><span class="identifier">key_compare</span></code>,
                respectively.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Returns a pair of iterators
                pointing to the beginning and one past the end of the subsequence
                of elements satisfying lower and upper simultaneously. If no such
                elements exist, the iterators both point to the first element satisfying
                lower, or else are equal to <code class="computeroutput"><span class="identifier">end</span><span class="special">()</span></code> if this latter element does not
                exist.
              </li>
<li class="listitem">
                <span class="bold"><strong>Complexity:</strong></span> O(log(n)).
              </li>
<li class="listitem">
                <span class="bold"><strong>Variants:</strong></span> In place of lower or upper
                (or both), the singular value <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">bimap</span><span class="special">::</span><span class="identifier">unbounded</span></code>
                can be provided. This acts as a predicate which all values of type
                <code class="computeroutput"><span class="identifier">key_type</span></code> satisfy.
              </li>
<li class="listitem">
                <span class="bold"><strong>Note:</strong></span> Only provided for map views.
              </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_bimap.reference.set_of_reference.__multi__set_of_views.at____info_at___and_operator_______set_of_only"></a><a class="link" href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.at____info_at___and_operator_______set_of_only" title="at(), info_at() and operator[] - set_of only">at(),
          info_at() and operator[] - set_of only</a>
</h5></div></div></div>
<a name="reference_set_of_at_key_const"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="keyword">const</span> <span class="identifier">mapped_type</span> <span class="special">&amp;</span> <span class="identifier">at</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">k</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">CompatibleKey</span></code>
                is a compatible key of <code class="computeroutput"><span class="identifier">key_compare</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Returns the <code class="computeroutput"><span class="identifier">mapped_type</span></code> reference that is associated
                with <code class="computeroutput"><span class="identifier">k</span></code>, or throws
                <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">out_of_range</span></code> if such key does not
                exist.
              </li>
<li class="listitem">
                <span class="bold"><strong>Complexity:</strong></span> O(log(n)).
              </li>
<li class="listitem">
                <span class="bold"><strong>Note:</strong></span> Only provided when <code class="computeroutput"><span class="identifier">set_of</span></code> is used.
              </li>
</ul></div>
<p>
            The symmetry of bimap imposes some constraints on <code class="computeroutput"><span class="keyword">operator</span><span class="special">[]</span></code> and the non constant version of at()
            that are not found in <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">maps</span></code>.
            They are only provided if the other collection type is mutable (<code class="computeroutput"><span class="identifier">list_of</span></code>, <code class="computeroutput"><span class="identifier">vector_of</span></code>
            and <code class="computeroutput"><span class="identifier">unconstrained_set_of</span></code>).
          </p>
<a name="reference_set_of_operator_bracket_key"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="identifier">mapped_type</span> <span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">[](</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">k</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">CompatibleKey</span></code>
                is a compatible key of <code class="computeroutput"><span class="identifier">key_compare</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects: </strong></span> <code class="computeroutput"><span class="keyword">return</span>
                <span class="identifier">insert</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">(</span><span class="identifier">k</span><span class="special">,</span><span class="identifier">mapped_type</span><span class="special">()))-&gt;</span><span class="identifier">second</span><span class="special">;</span></code>
              </li>
<li class="listitem">
                <span class="bold"><strong>Complexity:</strong></span> O(log(n)).
              </li>
<li class="listitem">
                <span class="bold"><strong>Note:</strong></span> Only provided when <code class="computeroutput"><span class="identifier">set_of</span></code> is used and the other collection
                type is mutable.
              </li>
</ul></div>
<a name="reference_set_of_at_key"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="identifier">mapped_type</span> <span class="special">&amp;</span> <span class="identifier">at</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">k</span><span class="special">);</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">CompatibleKey</span></code>
                is a compatible key of <code class="computeroutput"><span class="identifier">key_compare</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects: </strong></span> Returns the <code class="computeroutput"><span class="identifier">mapped_type</span></code> reference that is associated
                with <code class="computeroutput"><span class="identifier">k</span></code>, or throws
                <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">out_of_range</span></code> if such key does not
                exist.
              </li>
<li class="listitem">
                <span class="bold"><strong>Complexity:</strong></span> O(log(n)).
              </li>
<li class="listitem">
                <span class="bold"><strong>Note:</strong></span> Only provided when <code class="computeroutput"><span class="identifier">set_of</span></code> is used and the other collection
                type is mutable.
              </li>
</ul></div>
<a name="reference_set_of_info_at_key"></a><pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="identifier">info_type</span> <span class="special">&amp;</span> <span class="identifier">info_at</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">k</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="identifier">CompatibleKey</span> <span class="special">&gt;</span>
<span class="keyword">const</span> <span class="identifier">info_type</span> <span class="special">&amp;</span> <span class="identifier">info_at</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CompatibleKey</span> <span class="special">&amp;</span> <span class="identifier">k</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">CompatibleKey</span></code>
                is a compatible key of <code class="computeroutput"><span class="identifier">key_compare</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Effects:</strong></span> Returns the <code class="computeroutput"><span class="identifier">info_type</span></code> reference that is associated
                with <code class="computeroutput"><span class="identifier">k</span></code>, or throws
                <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">out_of_range</span></code> if such key does not
                exist.
              </li>
<li class="listitem">
                <span class="bold"><strong>Complexity:</strong></span> O(log(n)).
              </li>
<li class="listitem">
                <span class="bold"><strong>Note:</strong></span> Only provided when <code class="computeroutput"><span class="identifier">set_of</span></code> and <code class="computeroutput"><span class="identifier">info_hook</span></code>
                are used
              </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_bimap.reference.set_of_reference.__multi__set_of_views.serialization"></a><a class="link" href="set_of_reference.html#boost_bimap.reference.set_of_reference.__multi__set_of_views.serialization" title="Serialization">Serialization</a>
</h5></div></div></div>
<p>
            Views cannot be serialized on their own, but only as part of the <code class="computeroutput"><span class="identifier">bimap</span></code> into which they are embedded.
            In describing the additional preconditions and guarantees associated
            to <code class="computeroutput"><span class="special">[</span><span class="identifier">multi</span><span class="special">]</span><span class="identifier">set_of</span></code>
            views with respect to serialization of their embedding containers, we
            use the concepts defined in the <code class="computeroutput"><span class="identifier">bimap</span></code>
            serialization section.
          </p>
<div class="blurb">
<div class="titlepage"><div><div><p class="title"><b></b></p></div></div></div>
<p>
            <span class="bold"><strong>Operation:</strong></span> saving of a <code class="computeroutput"><span class="identifier">bimap</span></code> m to an output archive (XML archive)
            ar.
          </p>
</div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
                <span class="bold"><strong>Requires:</strong></span> No additional requirements
                to those imposed by the container.
              </li></ul></div>
<div class="blurb">
<div class="titlepage"><div><div><p class="title"><b></b></p></div></div></div>
<p>
            <span class="bold"><strong>Operation:</strong></span> loading of a <code class="computeroutput"><span class="identifier">bimap</span></code> m' from an input archive (XML
            archive) ar.
          </p>
</div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Requires:</strong></span> In addition to the general
                requirements, <code class="computeroutput"><span class="identifier">value_comp</span><span class="special">()</span></code> must be serialization-compatible
                with <code class="computeroutput"><span class="identifier">m</span><span class="special">.</span><span class="identifier">get</span><span class="special">&lt;</span><span class="identifier">i</span><span class="special">&gt;().</span><span class="identifier">value_comp</span><span class="special">()</span></code>,
                where i is the position of the ordered view in the container.
              </li>
<li class="listitem">
                <span class="bold"><strong>Postconditions:</strong></span> On successful loading,
                each of the elements of <code class="computeroutput"><span class="special">[</span><span class="identifier">begin</span><span class="special">(),</span>
                <span class="identifier">end</span><span class="special">())</span></code>
                is a restored copy of the corresponding element in <code class="computeroutput"><span class="special">[</span><span class="identifier">m</span><span class="special">.</span><span class="identifier">get</span><span class="special">&lt;</span><span class="identifier">i</span><span class="special">&gt;().</span><span class="identifier">begin</span><span class="special">(),</span> <span class="identifier">m</span><span class="special">.</span><span class="identifier">get</span><span class="special">&lt;</span><span class="identifier">i</span><span class="special">&gt;().</span><span class="identifier">end</span><span class="special">())</span></code>.
              </li>
</ul></div>
<div class="blurb">
<div class="titlepage"><div><div><p class="title"><b></b></p></div></div></div>
<p>
            <span class="bold"><strong>Operation:</strong></span> saving of an iterator or
            <code class="computeroutput"><span class="identifier">const_iterator</span></code> it to
            an output archive (XML archive) ar.
          </p>
</div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
                <span class="bold"><strong>Requires: </strong></span> <code class="computeroutput"><span class="identifier">it</span></code>
                is a valid iterator of the view. The associated <code class="computeroutput"><span class="identifier">bimap</span></code>
                has been previously saved.
              </li></ul></div>
<div class="blurb">
<div class="titlepage"><div><div><p class="title"><b></b></p></div></div></div>
<p>
            <span class="bold"><strong>Operation:</strong></span> loading of an <code class="computeroutput"><span class="identifier">iterator</span></code> or <code class="computeroutput"><span class="identifier">const_iterator</span></code>
            <code class="computeroutput"><span class="identifier">it</span></code>' from an input archive
            ( XML archive) ar.
          </p>
</div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <span class="bold"><strong>Postconditions:</strong></span> On successful loading,
                if it was dereferenceable then <code class="computeroutput"><span class="special">*</span><span class="identifier">it</span></code>' is the restored copy of <code class="computeroutput"><span class="special">*</span><span class="identifier">it</span></code>,
                otherwise <code class="computeroutput"><span class="identifier">it</span></code>'<code class="computeroutput">
                <span class="special">==</span> <span class="identifier">end</span><span class="special">()</span></code>.
              </li>
<li class="listitem">
                <span class="bold"><strong>Note:</strong></span> It is allowed that it be a
                <code class="computeroutput"><span class="identifier">const_iterator</span></code> and
                the restored <code class="computeroutput"><span class="identifier">it</span></code>'
                an iterator, or viceversa.
              </li>
</ul></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2006-2012 Matias Capeletto<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="bimap_reference.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../reference.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="unordered_set_of_reference.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
