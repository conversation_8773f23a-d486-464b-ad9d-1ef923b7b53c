<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>equal_range</title>
<link rel="stylesheet" href="../../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../index.html" title="Chapter 1. Range 2.0">
<link rel="up" href="../non_mutating.html" title="Non-mutating algorithms">
<link rel="prev" href="equal.html" title="equal">
<link rel="next" href="for_each.html" title="for_each">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="equal.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../non_mutating.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="for_each.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="range.reference.algorithms.non_mutating.equal_range"></a><a class="link" href="equal_range.html" title="equal_range">equal_range</a>
</h5></div></div></div>
<h6>
<a name="range.reference.algorithms.non_mutating.equal_range.h0"></a>
            <span class="phrase"><a name="range.reference.algorithms.non_mutating.equal_range.prototype"></a></span><a class="link" href="equal_range.html#range.reference.algorithms.non_mutating.equal_range.prototype">Prototype</a>
          </h6>
<p>
</p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">class</span> <span class="identifier">ForwardRange</span><span class="special">,</span>
    <span class="keyword">class</span> <span class="identifier">Value</span>
    <span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">range_iterator</span><span class="special">&lt;</span><span class="identifier">ForwardRange</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">,</span>
          <span class="keyword">typename</span> <span class="identifier">range_iterator</span><span class="special">&lt;</span><span class="identifier">ForwardRange</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">&gt;</span>
<span class="identifier">equal_range</span><span class="special">(</span><span class="identifier">ForwardRange</span><span class="special">&amp;</span> <span class="identifier">rng</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Value</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">class</span> <span class="identifier">ForwardRange</span><span class="special">,</span>
    <span class="keyword">class</span> <span class="identifier">Value</span>
    <span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">range_iterator</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="identifier">ForwardRange</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">,</span>
          <span class="keyword">typename</span> <span class="identifier">range_iterator</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="identifier">ForwardRange</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">&gt;</span>
<span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">ForwardRange</span><span class="special">&amp;</span> <span class="identifier">rng</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Value</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">class</span> <span class="identifier">ForwardRange</span><span class="special">,</span>
    <span class="keyword">class</span> <span class="identifier">Value</span><span class="special">,</span>
    <span class="keyword">class</span> <span class="identifier">SortPredicate</span>
    <span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">range_iterator</span><span class="special">&lt;</span><span class="identifier">ForwardRange</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">,</span>
          <span class="keyword">typename</span> <span class="identifier">range_iterator</span><span class="special">&lt;</span><span class="identifier">ForwardRange</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">&gt;</span>
<span class="identifier">equal_range</span><span class="special">(</span><span class="identifier">ForwardRange</span><span class="special">&amp;</span> <span class="identifier">rng</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Value</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">,</span> <span class="identifier">SortPredicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
    <span class="keyword">class</span> <span class="identifier">ForwardRange</span><span class="special">,</span>
    <span class="keyword">class</span> <span class="identifier">Value</span><span class="special">,</span>
    <span class="keyword">class</span> <span class="identifier">SortPredicate</span>
    <span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">range_iterator</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="identifier">ForwardRange</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">,</span>
          <span class="keyword">typename</span> <span class="identifier">range_iterator</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="identifier">ForwardRange</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">&gt;</span>
<span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">ForwardRange</span><span class="special">&amp;</span> <span class="identifier">rng</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Value</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">,</span> <span class="identifier">SortPredicate</span> <span class="identifier">pred</span><span class="special">);</span>
</pre>
<p>
          </p>
<h6>
<a name="range.reference.algorithms.non_mutating.equal_range.h1"></a>
            <span class="phrase"><a name="range.reference.algorithms.non_mutating.equal_range.description"></a></span><a class="link" href="equal_range.html#range.reference.algorithms.non_mutating.equal_range.description">Description</a>
          </h6>
<p>
            <code class="computeroutput"><span class="identifier">equal_range</span></code> returns a
            range in the form of a pair of iterators where all of the elements are
            equal to <code class="computeroutput"><span class="identifier">val</span></code>. If no values
            are found that are equal to <code class="computeroutput"><span class="identifier">val</span></code>,
            then an empty range is returned, hence <code class="computeroutput"><span class="identifier">result</span><span class="special">.</span><span class="identifier">first</span> <span class="special">==</span> <span class="identifier">result</span><span class="special">.</span><span class="identifier">second</span></code>.
            For the non-predicate versions of <code class="computeroutput"><span class="identifier">equal_range</span></code>
            the equality of elements is determined by <code class="computeroutput"><span class="keyword">operator</span><span class="special">&lt;</span></code>. For the predicate versions of <code class="computeroutput"><span class="identifier">equal_range</span></code> the equality of elements
            is determined by <code class="computeroutput"><span class="identifier">pred</span></code>.
          </p>
<h6>
<a name="range.reference.algorithms.non_mutating.equal_range.h2"></a>
            <span class="phrase"><a name="range.reference.algorithms.non_mutating.equal_range.definition"></a></span><a class="link" href="equal_range.html#range.reference.algorithms.non_mutating.equal_range.definition">Definition</a>
          </h6>
<p>
            Defined in the header file <code class="computeroutput"><span class="identifier">boost</span><span class="special">/</span><span class="identifier">range</span><span class="special">/</span><span class="identifier">algorithm</span><span class="special">/</span><span class="identifier">equal_range</span><span class="special">.</span><span class="identifier">hpp</span></code>
          </p>
<h6>
<a name="range.reference.algorithms.non_mutating.equal_range.h3"></a>
            <span class="phrase"><a name="range.reference.algorithms.non_mutating.equal_range.requirements"></a></span><a class="link" href="equal_range.html#range.reference.algorithms.non_mutating.equal_range.requirements">Requirements</a>
          </h6>
<p>
            <span class="bold"><strong>For the non-predicate versions:</strong></span>
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ForwardRange</span></code> is a
                model of the <a class="link" href="../../../concepts/forward_range.html" title="Forward Range">Forward
                Range</a> Concept.
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">Value</span></code> is a model
                of the <code class="computeroutput"><span class="identifier">LessThanComparableConcept</span></code>.
              </li>
<li class="listitem">
                The ordering of objects of type <code class="computeroutput"><span class="identifier">Value</span></code>
                is a <span class="bold"><strong><span class="emphasis"><em>strict weak ordering</em></span></strong></span>,
                as defined in the <code class="computeroutput"><span class="identifier">LessThanComparableConcept</span></code>
                requirements.
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ForwardRange</span></code>'s value
                type is the same type as <code class="computeroutput"><span class="identifier">Value</span></code>.
              </li>
</ul></div>
<p>
            <span class="bold"><strong>For the predicate versions:</strong></span>
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ForwardRange</span></code> is a
                model of the <a class="link" href="../../../concepts/forward_range.html" title="Forward Range">Forward
                Range</a> Concept.
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">SortPredicate</span></code> is
                a model of the <code class="computeroutput"><span class="identifier">StrictWeakOrderingConcept</span></code>.
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ForwardRange</span></code>'s value
                type is the same as <code class="computeroutput"><span class="identifier">Value</span></code>.
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ForwardRange</span></code>'s value
                type is convertible to both of <code class="computeroutput"><span class="identifier">SortPredicate</span></code>'s
                argument types.
              </li>
</ul></div>
<h6>
<a name="range.reference.algorithms.non_mutating.equal_range.h4"></a>
            <span class="phrase"><a name="range.reference.algorithms.non_mutating.equal_range.precondition_"></a></span><a class="link" href="equal_range.html#range.reference.algorithms.non_mutating.equal_range.precondition_">Precondition:</a>
          </h6>
<p>
            For the non-predicate versions: <code class="computeroutput"><span class="identifier">rng</span></code>
            is ordered in ascending order according to <code class="computeroutput"><span class="keyword">operator</span><span class="special">&lt;</span></code>.
          </p>
<p>
            For the predicate versions: <code class="computeroutput"><span class="identifier">rng</span></code>
            is ordered in ascending order according to <code class="computeroutput"><span class="identifier">pred</span></code>.
          </p>
<h6>
<a name="range.reference.algorithms.non_mutating.equal_range.h5"></a>
            <span class="phrase"><a name="range.reference.algorithms.non_mutating.equal_range.complexity"></a></span><a class="link" href="equal_range.html#range.reference.algorithms.non_mutating.equal_range.complexity">Complexity</a>
          </h6>
<p>
            For random-access ranges, the complexity is <code class="computeroutput"><span class="identifier">O</span><span class="special">(</span><span class="identifier">log</span> <span class="identifier">N</span><span class="special">)</span></code>,
            otherwise the complexity is <code class="computeroutput"><span class="identifier">O</span><span class="special">(</span><span class="identifier">N</span><span class="special">)</span></code>.
          </p>
</div>
<div class="copyright-footer">Copyright © 2003-2010 Thorsten Ottosen,
      Neil Groves<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="equal.html"><img src="../../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../non_mutating.html"><img src="../../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="for_each.html"><img src="../../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
