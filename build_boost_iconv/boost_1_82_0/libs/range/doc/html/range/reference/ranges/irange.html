<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>irange</title>
<link rel="stylesheet" href="../../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="Chapter 1. Range 2.0">
<link rel="up" href="../ranges.html" title="Provided Ranges">
<link rel="prev" href="istream_range.html" title="istream_range">
<link rel="next" href="../utilities.html" title="Utilities">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="istream_range.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../ranges.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../utilities.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="range.reference.ranges.irange"></a><a class="link" href="irange.html" title="irange">irange</a>
</h4></div></div></div>
<h6>
<a name="range.reference.ranges.irange.h0"></a>
          <span class="phrase"><a name="range.reference.ranges.irange.prototype"></a></span><a class="link" href="irange.html#range.reference.ranges.irange.prototype">Prototype</a>
        </h6>
<p>
</p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Integer</span><span class="special">&gt;</span>
<span class="identifier">iterator_range</span><span class="special">&lt;</span> <span class="identifier">range_detail</span><span class="special">::</span><span class="identifier">integer_iterator</span><span class="special">&lt;</span><span class="identifier">Integer</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="identifier">irange</span><span class="special">(</span><span class="identifier">Integer</span> <span class="identifier">last</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Integer</span><span class="special">&gt;</span>
<span class="identifier">iterator_range</span><span class="special">&lt;</span> <span class="identifier">range_detail</span><span class="special">::</span><span class="identifier">integer_iterator</span><span class="special">&lt;</span><span class="identifier">Integer</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="identifier">irange</span><span class="special">(</span><span class="identifier">Integer</span> <span class="identifier">first</span><span class="special">,</span> <span class="identifier">Integer</span> <span class="identifier">last</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Integer</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">StepSize</span><span class="special">&gt;</span>
<span class="identifier">iterator_range</span><span class="special">&lt;</span> <span class="identifier">range_detail</span><span class="special">::</span><span class="identifier">integer_iterator_with_step</span><span class="special">&lt;</span><span class="identifier">Integer</span><span class="special">,</span> <span class="identifier">StepSize</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="identifier">irange</span><span class="special">(</span><span class="identifier">Integer</span> <span class="identifier">first</span><span class="special">,</span> <span class="identifier">Integer</span> <span class="identifier">last</span><span class="special">,</span> <span class="identifier">StepSize</span> <span class="identifier">step_size</span><span class="special">);</span>
</pre>
<p>
        </p>
<h6>
<a name="range.reference.ranges.irange.h1"></a>
          <span class="phrase"><a name="range.reference.ranges.irange.description"></a></span><a class="link" href="irange.html#range.reference.ranges.irange.description">Description</a>
        </h6>
<p>
          <code class="computeroutput"><span class="identifier">irange</span></code> is a function to
          generate an Integer Range.
        </p>
<p>
          <code class="computeroutput"><span class="identifier">irange</span></code> allows treating
          integers as a model of the <a class="link" href="../../concepts/random_access_range.html" title="Random Access Range">Random
          Access Range</a> Concept. It should be noted that the <code class="computeroutput"><span class="identifier">first</span></code> and <code class="computeroutput"><span class="identifier">last</span></code>
          parameters denoted a half-open range.
        </p>
<h6>
<a name="range.reference.ranges.irange.h2"></a>
          <span class="phrase"><a name="range.reference.ranges.irange.definition"></a></span><a class="link" href="irange.html#range.reference.ranges.irange.definition">Definition</a>
        </h6>
<p>
          Defined in the header file <code class="computeroutput"><span class="identifier">boost</span><span class="special">/</span><span class="identifier">range</span><span class="special">/</span><span class="identifier">irange</span><span class="special">.</span><span class="identifier">hpp</span></code>
        </p>
<h6>
<a name="range.reference.ranges.irange.h3"></a>
          <span class="phrase"><a name="range.reference.ranges.irange.requirements"></a></span><a class="link" href="irange.html#range.reference.ranges.irange.requirements">Requirements</a>
        </h6>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
              <code class="computeroutput"><span class="identifier">Integer</span></code> is a model
              of the <code class="computeroutput"><span class="identifier">Integer</span></code> Concept.
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">StepSize</span></code> is a model
              of the <code class="computeroutput"><span class="identifier">SignedInteger</span></code>
              Concept.
            </li>
</ol></div>
<h6>
<a name="range.reference.ranges.irange.h4"></a>
          <span class="phrase"><a name="range.reference.ranges.irange.complexity"></a></span><a class="link" href="irange.html#range.reference.ranges.irange.complexity">Complexity</a>
        </h6>
<p>
          Constant. Since this function generates a new range the most significant
          performance cost is incurred through the iteration of the generated range.
        </p>
</div>
<div class="copyright-footer">Copyright © 2003-2010 Thorsten Ottosen,
      Neil Groves<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="istream_range.html"><img src="../../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../ranges.html"><img src="../../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../utilities.html"><img src="../../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
