<?xml version="1.0" standalone="yes"?>
<library-reference id="date_time_reference"><title>Date Time Reference</title><header name="boost/date_time/adjust_functors.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="day_functor"><template>
      <template-type-parameter name="date_type"/>
    </template><purpose>Functor to iterate a fixed number of days. </purpose><typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<method-group name="public member functions">
<method name="get_offset" cv="const"><type>duration_type</type><parameter name=""><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="get_neg_offset" cv="const"><type>duration_type</type><parameter name=""><paramtype>const date_type &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="f"><paramtype>int</paramtype></parameter></constructor>
</class><class name="month_functor"><template>
      <template-type-parameter name="date_type"/>
    </template><purpose>Provides calculation to find next nth month given a date. </purpose><description><para>This adjustment function provides the logic for 'month-based' advancement on a ymd based calendar. The policy it uses to handle the non existant end of month days is to back up to the last day of the month. Also, if the starting date is the last day of a month, this functor will attempt to adjust to the end of the month. </para></description><typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<typedef name="cal_type"><type>date_type::calendar_type</type></typedef>
<typedef name="ymd_type"><type>cal_type::ymd_type</type></typedef>
<typedef name="day_type"><type>cal_type::day_type</type></typedef>
<method-group name="public member functions">
<method name="get_offset" cv="const"><type>duration_type</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="get_neg_offset" cv="const"><type>duration_type</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter><purpose>Returns a negative duration_type. </purpose></method>
</method-group>
<constructor><parameter name="f"><paramtype>int</paramtype></parameter></constructor>
</class><class name="week_functor"><template>
      <template-type-parameter name="date_type"/>
    </template><purpose>Functor to iterate a over weeks. </purpose><typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<typedef name="calendar_type"><type>date_type::calendar_type</type></typedef>
<method-group name="public member functions">
<method name="get_offset" cv="const"><type>duration_type</type><parameter name=""><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="get_neg_offset" cv="const"><type>duration_type</type><parameter name=""><paramtype>const date_type &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="f"><paramtype>int</paramtype></parameter></constructor>
</class><class name="year_functor"><template>
      <template-type-parameter name="date_type"/>
    </template><purpose>Functor to iterate by a year adjusting for leap years. </purpose><typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<method-group name="public member functions">
<method name="get_offset" cv="const"><type>duration_type</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="get_neg_offset" cv="const"><type>duration_type</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="f"><paramtype>int</paramtype></parameter></constructor>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/c_local_time_adjustor.hpp">
<para>Time adjustment calculations based on machine </para><namespace name="boost">
<namespace name="date_time">
<class name="c_local_adjustor"><template>
      <template-type-parameter name="time_type"/>
    </template><purpose>Adjust to / from utc using the C API. </purpose><description><para>Warning!!! This class assumes that timezone settings of the machine are correct. This can be a very dangerous assumption. </para></description><typedef name="time_duration_type"><type>time_type::time_duration_type</type></typedef>
<typedef name="date_type"><type>time_type::date_type</type></typedef>
<typedef name="date_duration_type"><type>date_type::duration_type</type></typedef>
<method-group name="public static functions">
<method name="utc_to_local" specifiers="static"><type>time_type</type><parameter name="t"><paramtype>const time_type &amp;</paramtype></parameter><purpose>Convert a utc time to local time. </purpose></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/c_time.hpp">
<para>Provide workarounds related to the ctime header </para><namespace name="std">
</namespace>
<namespace name="boost">
<namespace name="date_time">
<struct name="c_time"><purpose>Provides a uniform interface to some 'ctime' functions. </purpose><description><para>Provides a uniform interface to some ctime functions and their '_r' counterparts. The '_r' functions require a pointer to a user created std::tm struct whereas the regular functions use a staticly created struct and return a pointer to that. These wrapper functions require the user to create a std::tm struct and send in a pointer to it. This struct may be used to store the resulting time. The returned pointer may or may not point to this struct, however, it will point to the result of the corresponding function. All functions do proper checking of the C function results and throw exceptions on error. Therefore the functions will never return NULL. </para></description><method-group name="public static functions">
<method name="localtime" specifiers="static"><type>std::tm *</type><parameter name="t"><paramtype>const std::time_t *</paramtype></parameter><parameter name="result"><paramtype>std::tm *</paramtype></parameter><purpose>requires a pointer to a user created std::tm struct </purpose></method>
<method name="gmtime" specifiers="static"><type>std::tm *</type><parameter name="t"><paramtype>const std::time_t *</paramtype></parameter><parameter name="result"><paramtype>std::tm *</paramtype></parameter><purpose>requires a pointer to a user created std::tm struct </purpose></method>
<method name="localtime" specifiers="static"><type>std::tm *</type><parameter name="t"><paramtype>const std::time_t *</paramtype></parameter><parameter name="result"><paramtype>std::tm *</paramtype></parameter><purpose>requires a pointer to a user created std::tm struct </purpose></method>
<method name="gmtime" specifiers="static"><type>std::tm *</type><parameter name="t"><paramtype>const std::time_t *</paramtype></parameter><parameter name="result"><paramtype>std::tm *</paramtype></parameter><purpose>requires a pointer to a user created std::tm struct </purpose></method>
</method-group>
</struct>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/compiler_config.hpp">
<namespace name="std">
</namespace>
</header>
<header name="boost/date_time/constrained_value.hpp">
<namespace name="boost">
<namespace name="CV">
<class name="constrained_value"><template>
      <template-type-parameter name="value_policies"/>
    </template><purpose>A template to specify a constrained basic value type. </purpose><description><para>This template provides a quick way to generate an integer type with a constrained range. The type provides for the ability to specify the min, max, and and error handling policy.</para><para><emphasis role="bold">value policies</emphasis> A class that provides the range limits via the min and max functions as well as a function on_error that determines how errors are handled. A common strategy would be to assert or throw and exception. The on_error is passed both the current value and the new value that is in error. </para></description><typedef name="value_type"><type>value_policies::value_type</type></typedef>
<method-group name="public member functions">
<method name="operator value_type" cv="const"><type>BOOST_CXX14_CONSTEXPR</type><purpose>Coerce into the representation type. </purpose></method>
</method-group>
<constructor><parameter name="value"><paramtype>value_type</paramtype></parameter></constructor>
<copy-assignment><type>BOOST_CXX14_CONSTEXPR <classname>constrained_value</classname> &amp;</type><parameter name="v"><paramtype>value_type</paramtype></parameter></copy-assignment>
<method-group name="public static functions">
<method name="BOOST_PREVENT_MACRO_SUBSTITUTION" specifiers="static"><type>BOOST_CONSTEXPR value_type max</type><purpose>Return the max allowed value (traits method) </purpose></method>
<method name="BOOST_PREVENT_MACRO_SUBSTITUTION" specifiers="static"><type>BOOST_CONSTEXPR value_type min</type><purpose>Return the min allowed value (traits method) </purpose></method>
</method-group>
<method-group name="private member functions">
<method name="assign"><type>BOOST_CXX14_CONSTEXPR void</type><parameter name="value"><paramtype>value_type</paramtype></parameter></method>
</method-group>
</class><class name="simple_exception_policy"><template>
      <template-type-parameter name="rep_type"/>
      <template-nontype-parameter name="min_value"><type>rep_type</type></template-nontype-parameter>
      <template-nontype-parameter name="max_value"><type>rep_type</type></template-nontype-parameter>
      <template-type-parameter name="exception_type"/>
    </template><purpose>Template to shortcut the <classname alt="boost::CV::constrained_value">constrained_value</classname> policy creation process. </purpose><struct name="exception_wrapper"><inherit access="public">exception_type</inherit><method-group name="public member functions">
<method name="conversion-operator" cv="const"><type>std::out_of_range</type></method>
</method-group>
</struct><typedef name="value_type"><type>rep_type</type></typedef>
<method-group name="public static functions">
<method name="BOOST_PREVENT_MACRO_SUBSTITUTION" specifiers="static"><type>BOOST_CONSTEXPR rep_type min</type></method>
<method name="BOOST_PREVENT_MACRO_SUBSTITUTION" specifiers="static"><type>BOOST_CONSTEXPR rep_type max</type></method>
<method name="on_error" specifiers="static"><type>void</type><parameter name=""><paramtype>rep_type</paramtype></parameter><parameter name=""><paramtype>rep_type</paramtype></parameter><parameter name=""><paramtype>violation_enum</paramtype></parameter></method>
</method-group>
</class><enum name="violation_enum"><enumvalue name="min_violation"/><enumvalue name="max_violation"/><purpose>Represent a min or max violation type. </purpose></enum>
</namespace>
</namespace>
</header>
<header name="boost/date_time/date.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="date"><template>
      <template-type-parameter name="T"/>
      <template-type-parameter name="calendar"/>
      <template-type-parameter name="duration_type_"/>
    </template><inherit access="private">boost::less_than_comparable&lt; T, boost::equality_comparable&lt; T &gt; &gt;</inherit><purpose>Representation of timepoint at the one day level resolution. </purpose><description><para>The date template represents an interface shell for a date class that is based on a year-month-day system such as the gregorian or ISO 8601 systems. It provides basic operations to enable calculation and comparisons.</para><para><emphasis role="bold">Theory</emphasis></para><para>This date representation fundamentally departs from the C tm struct approach. The goal for this type is to provide efficient date operations (add, subtract) and storage (minimize space to represent) in a concrete class. Thus, the date uses a count internally to represent a particular date. The calendar parameter defines the policies for converting the the year-month-day and internal counted form here. Applications that need to perform heavy formatting of the same date repeatedly will perform better by using the year-month-day representation.</para><para>Internally the date uses a day number to represent the date. This is a monotonic time representation. This representation allows for fast comparison as well as simplifying the creation of writing numeric operations. Essentially, the internal day number is like adjusted julian day. The adjustment is determined by the Epoch date which is represented as day 1 of the calendar. Day 0 is reserved for negative infinity so that any actual date is automatically greater than negative infinity. When a date is constructed from a date or formatted for output, the appropriate conversions are applied to create the year, month, day representations. </para></description><typedef name="date_type"><type>T</type></typedef>
<typedef name="calendar_type"><type>calendar</type></typedef>
<typedef name="traits_type"><type>calendar::date_traits_type</type></typedef>
<typedef name="duration_type"><type>duration_type_</type></typedef>
<typedef name="year_type"><type>calendar::year_type</type></typedef>
<typedef name="month_type"><type>calendar::month_type</type></typedef>
<typedef name="day_type"><type>calendar::day_type</type></typedef>
<typedef name="ymd_type"><type>calendar::ymd_type</type></typedef>
<typedef name="date_rep_type"><type>calendar::date_rep_type</type></typedef>
<typedef name="date_int_type"><type>calendar::date_int_type</type></typedef>
<typedef name="day_of_week_type"><type>calendar::day_of_week_type</type></typedef>
<method-group name="public member functions">
<method name="year" cv="const"><type>BOOST_CXX14_CONSTEXPR year_type</type></method>
<method name="month" cv="const"><type>BOOST_CXX14_CONSTEXPR month_type</type></method>
<method name="day" cv="const"><type>BOOST_CXX14_CONSTEXPR day_type</type></method>
<method name="day_of_week" cv="const"><type>BOOST_CXX14_CONSTEXPR day_of_week_type</type></method>
<method name="year_month_day" cv="const"><type>BOOST_CXX14_CONSTEXPR ymd_type</type></method>
<method name="operator&lt;" cv="const"><type>BOOST_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="operator==" cv="const"><type>BOOST_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="is_special" cv="const"><type>BOOST_CONSTEXPR bool</type><purpose>check to see if date is a special value </purpose></method>
<method name="is_not_a_date" cv="const"><type>BOOST_CONSTEXPR bool</type><purpose>check to see if date is not a value </purpose></method>
<method name="is_infinity" cv="const"><type>BOOST_CONSTEXPR bool</type><purpose>check to see if date is one of the infinity values </purpose></method>
<method name="is_pos_infinity" cv="const"><type>BOOST_CONSTEXPR bool</type><purpose>check to see if date is greater than all possible dates </purpose></method>
<method name="is_neg_infinity" cv="const"><type>BOOST_CONSTEXPR bool</type><purpose>check to see if date is greater than all possible dates </purpose></method>
<method name="as_special" cv="const"><type>BOOST_CXX14_CONSTEXPR special_values</type><purpose>return as a special value or a not_special if a normal date </purpose></method>
<method name="operator-" cv="const"><type>BOOST_CXX14_CONSTEXPR duration_type</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="operator-" cv="const"><type>BOOST_CXX14_CONSTEXPR date_type</type><parameter name="dd"><paramtype>const duration_type &amp;</paramtype></parameter></method>
<method name="operator-="><type>BOOST_CXX14_CONSTEXPR date_type</type><parameter name="dd"><paramtype>const duration_type &amp;</paramtype></parameter></method>
<method name="day_count" cv="const"><type>BOOST_CONSTEXPR date_rep_type</type></method>
<method name="operator+" cv="const"><type>BOOST_CXX14_CONSTEXPR date_type</type><parameter name="dd"><paramtype>const duration_type &amp;</paramtype></parameter></method>
<method name="operator+="><type>BOOST_CXX14_CONSTEXPR date_type</type><parameter name="dd"><paramtype>const duration_type &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="y"><paramtype>year_type</paramtype></parameter><parameter name="m"><paramtype>month_type</paramtype></parameter><parameter name="d"><paramtype>day_type</paramtype></parameter></constructor>
<constructor><parameter name="ymd"><paramtype>const ymd_type &amp;</paramtype></parameter></constructor>
<method-group name="protected member functions">
</method-group>
<constructor specifiers="explicit"><parameter name="days"><paramtype>date_int_type</paramtype></parameter><description><para>This is a private constructor which allows for the creation of new dates. It is not exposed to users since that would require class users to understand the inner workings of the date class. </para></description></constructor>
<constructor specifiers="explicit"><parameter name="days"><paramtype>date_rep_type</paramtype></parameter></constructor>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_clock_device.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="day_clock"><template>
      <template-type-parameter name="date_type"/>
    </template><purpose>A clock providing day level services based on C time_t capabilities. </purpose><description><para>This clock uses Posix interfaces as its implementation and hence uses the timezone settings of the operating system. Incorrect user settings will result in incorrect results for the calls to local_day. </para></description><typedef name="ymd_type"><type>date_type::ymd_type</type></typedef>
<method-group name="public static functions">
<method name="local_day" specifiers="static"><type>date_type</type><purpose>Get the local day as a date type. </purpose></method>
<method name="local_day_ymd" specifiers="static"><type>date_type::ymd_type</type><purpose>Get the local day as a ymd_type. </purpose></method>
<method name="universal_day_ymd" specifiers="static"><type>date_type::ymd_type</type><purpose>Get the current day in universal date as a ymd_type. </purpose></method>
<method name="universal_day" specifiers="static"><type>date_type</type><purpose>Get the UTC day as a date type. </purpose></method>
</method-group>
<method-group name="private static functions">
<method name="get_local_time" specifiers="static"><type>::std::tm *</type><parameter name="result"><paramtype>std::tm &amp;</paramtype></parameter></method>
<method name="get_universal_time" specifiers="static"><type>::std::tm *</type><parameter name="result"><paramtype>std::tm &amp;</paramtype></parameter></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_defs.hpp">
<namespace name="boost">
<namespace name="date_time">
<enum name="weekdays"><enumvalue name="Sunday"/><enumvalue name="Monday"/><enumvalue name="Tuesday"/><enumvalue name="Wednesday"/><enumvalue name="Thursday"/><enumvalue name="Friday"/><enumvalue name="Saturday"/><purpose>An enumeration of weekday names. </purpose></enum>
<enum name="months_of_year"><enumvalue name="Jan"><default>=1</default></enumvalue><enumvalue name="Feb"/><enumvalue name="Mar"/><enumvalue name="Apr"/><enumvalue name="May"/><enumvalue name="Jun"/><enumvalue name="Jul"/><enumvalue name="Aug"/><enumvalue name="Sep"/><enumvalue name="Oct"/><enumvalue name="Nov"/><enumvalue name="Dec"/><enumvalue name="NotAMonth"/><enumvalue name="NumMonths"/><purpose>Simple enum to allow for nice programming with Jan, Feb, etc. </purpose></enum>

































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_duration.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="date_duration"><template>
      <template-type-parameter name="duration_rep_traits"/>
    </template><inherit access="private">boost::less_than_comparable1&lt; date_duration&lt; duration_rep_traits &gt;, boost::equality_comparable1&lt; date_duration&lt; duration_rep_traits &gt;, boost::addable1&lt; date_duration&lt; duration_rep_traits &gt;, boost::subtractable1&lt; date_duration&lt; duration_rep_traits &gt;, boost::dividable2&lt; date_duration&lt; duration_rep_traits &gt;, int &gt; &gt; &gt; &gt; &gt;</inherit><purpose>Duration type with date level resolution. </purpose><typedef name="duration_rep_type"><type>duration_rep_traits::int_type</type></typedef>
<typedef name="duration_rep"><type>duration_rep_traits::impl_type</type></typedef>
<method-group name="public member functions">
<method name="get_rep" cv="const"><type>BOOST_CXX14_CONSTEXPR duration_rep</type><purpose>returns days_ as it's instantiated type - used for streaming </purpose></method>
<method name="as_special" cv="const"><type>BOOST_CXX14_CONSTEXPR special_values</type></method>
<method name="is_special" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type></method>
<method name="days" cv="const"><type>BOOST_CXX14_CONSTEXPR duration_rep_type</type><purpose>returns days as value, not object. </purpose></method>
<method name="operator==" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const <classname>date_duration</classname> &amp;</paramtype></parameter><purpose>Equality. </purpose></method>
<method name="operator&lt;" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const <classname>date_duration</classname> &amp;</paramtype></parameter><purpose>Less. </purpose></method>
<method name="operator-="><type>BOOST_CXX14_CONSTEXPR <classname>date_duration</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>date_duration</classname> &amp;</paramtype></parameter><purpose>Subtract another duration – result is signed. </purpose></method>
<method name="operator+="><type>BOOST_CXX14_CONSTEXPR <classname>date_duration</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>date_duration</classname> &amp;</paramtype></parameter><purpose>Add a duration – result is signed. </purpose></method>
<method name="operator-" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>date_duration</classname></type><purpose>unary- Allows for dd = -date_duration(2); -&gt; dd == -2 </purpose></method>
<method name="operator/="><type>BOOST_CXX14_CONSTEXPR <classname>date_duration</classname> &amp;</type><parameter name="divisor"><paramtype>int</paramtype></parameter><purpose>Division operations on a duration with an integer. </purpose></method>
<method name="is_negative" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>return sign information </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="day_count"><paramtype>duration_rep</paramtype></parameter><purpose>Construct from a day count. </purpose></constructor>
<constructor><parameter name="sv"><paramtype>special_values</paramtype></parameter><description><para>construct from special_values - only works when instantiated with <classname alt="boost::date_time::duration_traits_adapted">duration_traits_adapted</classname> </para></description></constructor>
<method-group name="public static functions">
<method name="unit" specifiers="static"><type>BOOST_CXX14_CONSTEXPR <classname>date_duration</classname></type><purpose>Returns the smallest duration – used by to calculate 'end'. </purpose></method>
</method-group>
</class><struct name="duration_traits_adapted"><description><para>Struct for instantiating <classname alt="boost::date_time::date_duration">date_duration</classname> <emphasis role="bold">WITH</emphasis> special values functionality. Allows for transparent implementation of either date_duration&lt;long&gt; or <classname alt="boost::date_time::date_duration">date_duration</classname>&lt;int_adapter&lt;long&gt; &gt; </para></description><typedef name="int_type"><type>long</type></typedef>
<typedef name="impl_type"><type><classname>boost::date_time::int_adapter</classname>&lt; long &gt;</type></typedef>
<method-group name="public static functions">
<method name="as_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR int_type</type><parameter name="i"><paramtype><classname>impl_type</classname></paramtype></parameter></method>
</method-group>
</struct><struct name="duration_traits_long"><description><para>Struct for instantiating <classname alt="boost::date_time::date_duration">date_duration</classname> with <emphasis role="bold">NO</emphasis> special values functionality. Allows for transparent implementation of either date_duration&lt;long&gt; or <classname alt="boost::date_time::date_duration">date_duration</classname>&lt;int_adapter&lt;long&gt; &gt; </para></description><typedef name="int_type"><type>long</type></typedef>
<typedef name="impl_type"><type>long</type></typedef>
<method-group name="public static functions">
<method name="as_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR int_type</type><parameter name="i"><paramtype>impl_type</paramtype></parameter></method>
</method-group>
</struct>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_duration_types.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="months_duration"><template>
      <template-type-parameter name="base_config"/>
    </template><purpose>additional duration type that represents a logical month </purpose><description><para>A logical month enables things like: "date(2002,Mar,2) + months(2) -&gt; 
2002-May2". If the date is a last day-of-the-month, the result will also be a last-day-of-the-month. </para></description><method-group name="public member functions">
<method name="number_of_months" cv="const"><type>int_rep</type></method>
<method name="get_neg_offset" cv="const"><type>BOOST_CXX14_CONSTEXPR duration_type</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter><purpose>returns a negative duration </purpose></method>
<method name="get_offset" cv="const"><type>BOOST_CXX14_CONSTEXPR duration_type</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="operator==" cv="const"><type>BOOST_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const <classname>months_type</classname> &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const"><type>BOOST_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const <classname>months_type</classname> &amp;</paramtype></parameter></method>
<method name="operator+" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname></type><parameter name="rhs"><paramtype>const <classname>months_type</classname> &amp;</paramtype></parameter></method>
<method name="operator+="><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>months_type</classname> &amp;</paramtype></parameter></method>
<method name="operator-" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname></type><parameter name="rhs"><paramtype>const <classname>months_type</classname> &amp;</paramtype></parameter></method>
<method name="operator-="><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>months_type</classname> &amp;</paramtype></parameter></method>
<method name="operator *" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname></type><parameter name="rhs"><paramtype>const int_type</paramtype></parameter></method>
<method name="operator *="><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname> &amp;</type><parameter name="rhs"><paramtype>const int_type</paramtype></parameter></method>
<method name="operator/" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname></type><parameter name="rhs"><paramtype>const int_type</paramtype></parameter></method>
<method name="operator/="><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname> &amp;</type><parameter name="rhs"><paramtype>const int_type</paramtype></parameter></method>
<method name="operator+" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname></type><parameter name="y"><paramtype>const <classname>years_type</classname> &amp;</paramtype></parameter></method>
<method name="operator+="><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname> &amp;</type><parameter name="y"><paramtype>const <classname>years_type</classname> &amp;</paramtype></parameter></method>
<method name="operator-" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname></type><parameter name="y"><paramtype>const <classname>years_type</classname> &amp;</paramtype></parameter></method>
<method name="operator-="><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname> &amp;</type><parameter name="y"><paramtype>const <classname>years_type</classname> &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="num"><paramtype>int_rep</paramtype></parameter></constructor>
<constructor><parameter name="sv"><paramtype>special_values</paramtype></parameter></constructor>
</class><class name="weeks_duration"><template>
      <template-type-parameter name="duration_config"/>
    </template><inherit access="public">boost::date_time::date_duration&lt; duration_config &gt;</inherit><purpose>Additional duration type that represents a number of n*7 days. </purpose><method-group name="public member functions">
</method-group>
<constructor><parameter name="w"><paramtype>typename duration_config::impl_type</paramtype></parameter></constructor>
<constructor><parameter name="sv"><paramtype>special_values</paramtype></parameter></constructor>
</class><class name="years_duration"><template>
      <template-type-parameter name="base_config"/>
    </template><purpose>additional duration type that represents a logical year </purpose><description><para>A logical year enables things like: "date(2002,Mar,2) + years(2) -&gt; 
2004-Mar-2". If the date is a last day-of-the-month, the result will also be a last-day-of-the-month (ie date(2001-Feb-28) + years(3) -&gt; 2004-Feb-29). </para></description><method-group name="public member functions">
<method name="number_of_years" cv="const"><type>BOOST_CXX14_CONSTEXPR int_rep</type></method>
<method name="get_neg_offset" cv="const"><type>BOOST_CXX14_CONSTEXPR duration_type</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter><purpose>returns a negative duration </purpose></method>
<method name="get_offset" cv="const"><type>BOOST_CXX14_CONSTEXPR duration_type</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="operator==" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const <classname>years_type</classname> &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const"><type>bool</type><parameter name="rhs"><paramtype>const <classname>years_type</classname> &amp;</paramtype></parameter></method>
<method name="operator+" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>years_type</classname></type><parameter name="rhs"><paramtype>const <classname>years_type</classname> &amp;</paramtype></parameter></method>
<method name="operator+="><type>BOOST_CXX14_CONSTEXPR <classname>years_type</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>years_type</classname> &amp;</paramtype></parameter></method>
<method name="operator-" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>years_type</classname></type><parameter name="rhs"><paramtype>const <classname>years_type</classname> &amp;</paramtype></parameter></method>
<method name="operator-="><type>BOOST_CXX14_CONSTEXPR <classname>years_type</classname> &amp;</type><parameter name="rhs"><paramtype>const <classname>years_type</classname> &amp;</paramtype></parameter></method>
<method name="operator *" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>years_type</classname></type><parameter name="rhs"><paramtype>const int_type</paramtype></parameter></method>
<method name="operator *="><type>BOOST_CXX14_CONSTEXPR <classname>years_type</classname> &amp;</type><parameter name="rhs"><paramtype>const int_type</paramtype></parameter></method>
<method name="operator/" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>years_type</classname></type><parameter name="rhs"><paramtype>const int_type</paramtype></parameter></method>
<method name="operator/="><type>BOOST_CXX14_CONSTEXPR <classname>years_type</classname> &amp;</type><parameter name="rhs"><paramtype>const int_type</paramtype></parameter></method>
<method name="operator+" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname></type><parameter name="m"><paramtype>const <classname>months_type</classname> &amp;</paramtype></parameter></method>
<method name="operator-" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>months_type</classname></type><parameter name="m"><paramtype>const <classname>months_type</classname> &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="num"><paramtype>int_rep</paramtype></parameter></constructor>
<constructor><parameter name="sv"><paramtype>special_values</paramtype></parameter></constructor>
</class><data-member name="years_duration"><type>class BOOST_SYMBOL_VISIBLE</type></data-member>

































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_facet.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="date_facet"><template>
      <template-type-parameter name="date_type"/>
      <template-type-parameter name="CharT"/>
      <template-type-parameter name="OutItrT"><default>std::ostreambuf_iterator&lt;CharT, std::char_traits&lt;CharT&gt; &gt;</default></template-type-parameter>
    </template><inherit access="public">facet</inherit><description><para>Class that provides format based I/O facet for date types.</para><para>This class allows the formatting of dates by using format string. Format strings are:</para><para><itemizedlist>
<listitem><para>A =&gt; long_weekday_format - Full name Ex: Tuesday</para>
</listitem><listitem><para>a =&gt; short_weekday_format - Three letter abbreviation Ex: Tue</para>
</listitem><listitem><para>B =&gt; long_month_format - Full name Ex: October</para>
</listitem><listitem><para>b =&gt; short_month_format - Three letter abbreviation Ex: Oct</para>
</listitem><listitem><para>x =&gt; standard_format_specifier - defined by the locale</para>
</listitem><listitem><para>Y-b-d =&gt; default_date_format - YYYY-Mon-dd</para>
</listitem></itemizedlist>
</para><para>Default month format == b Default weekday format == a </para></description><typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<typedef name="day_of_week_type"><type>date_type::day_of_week_type</type></typedef>
<typedef name="day_type"><type>date_type::day_type</type></typedef>
<typedef name="month_type"><type>date_type::month_type</type></typedef>
<typedef name="period_type"><type><classname>boost::date_time::period</classname>&lt; date_type, duration_type &gt;</type></typedef>
<typedef name="string_type"><type>std::basic_string&lt; CharT &gt;</type></typedef>
<typedef name="char_type"><type>CharT</type></typedef>
<typedef name="period_formatter_type"><type><classname>boost::date_time::period_formatter</classname>&lt; CharT &gt;</type></typedef>
<typedef name="special_values_formatter_type"><type><classname>boost::date_time::special_values_formatter</classname>&lt; CharT &gt;</type></typedef>
<typedef name="input_collection_type"><type>std::vector&lt; std::basic_string&lt; CharT &gt; &gt;</type></typedef>
<typedef name="date_gen_formatter_type"><type><classname>date_generator_formatter</classname>&lt; date_type, CharT &gt;</type></typedef>
<typedef name="partial_date_type"><type><classname>partial_date</classname>&lt; date_type &gt;</type></typedef>
<typedef name="nth_kday_type"><type><classname>nth_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="first_kday_type"><type><classname>first_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="last_kday_type"><type><classname>last_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="kday_after_type"><type><classname>first_kday_after</classname>&lt; date_type &gt;</type></typedef>
<typedef name="kday_before_type"><type><classname>first_kday_before</classname>&lt; date_type &gt;</type></typedef>
<data-member name="long_weekday_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="short_weekday_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="long_month_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="short_month_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="default_period_separator" specifiers="static"><type>const char_type</type></data-member>
<data-member name="standard_format_specifier" specifiers="static"><type>const char_type</type></data-member>
<data-member name="iso_format_specifier" specifiers="static"><type>const char_type</type></data-member>
<data-member name="iso_format_extended_specifier" specifiers="static"><type>const char_type</type></data-member>
<data-member name="default_date_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="id" specifiers="static"><type>std::locale::id</type></data-member>
<method-group name="public member functions">
<method name="__get_id" cv="const"><type>std::locale::id &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="format"><type>void</type><parameter name="format_str"><paramtype>const char_type *const</paramtype></parameter></method>
<method name="set_iso_format" specifiers="virtual"><type>void</type></method>
<method name="set_iso_extended_format" specifiers="virtual"><type>void</type></method>
<method name="month_format"><type>void</type><parameter name="format_str"><paramtype>const char_type *const</paramtype></parameter></method>
<method name="weekday_format"><type>void</type><parameter name="format_str"><paramtype>const char_type *const</paramtype></parameter></method>
<method name="period_formatter"><type>void</type><parameter name="per_formatter"><paramtype><classname>period_formatter_type</classname></paramtype></parameter></method>
<method name="special_values_formatter"><type>void</type><parameter name="svf"><paramtype>const <classname>special_values_formatter_type</classname> &amp;</paramtype></parameter></method>
<method name="short_weekday_names"><type>void</type><parameter name="short_names"><paramtype>const input_collection_type &amp;</paramtype></parameter></method>
<method name="long_weekday_names"><type>void</type><parameter name="long_names"><paramtype>const input_collection_type &amp;</paramtype></parameter></method>
<method name="short_month_names"><type>void</type><parameter name="short_names"><paramtype>const input_collection_type &amp;</paramtype></parameter></method>
<method name="long_month_names"><type>void</type><parameter name="long_names"><paramtype>const input_collection_type &amp;</paramtype></parameter></method>
<method name="date_gen_phrase_strings"><type>void</type><parameter name="new_strings"><paramtype>const input_collection_type &amp;</paramtype></parameter><parameter name="beg_pos"><paramtype>typename date_gen_formatter_type::phrase_elements</paramtype><default>date_gen_formatter_type::first</default></parameter></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_char"><paramtype>char_type</paramtype></parameter><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_char"><paramtype>char_type</paramtype></parameter><parameter name="dd"><paramtype>const duration_type &amp;</paramtype></parameter></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_char"><paramtype>char_type</paramtype></parameter><parameter name="m"><paramtype>const month_type &amp;</paramtype></parameter></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_char"><paramtype>char_type</paramtype></parameter><parameter name="day"><paramtype>const day_type &amp;</paramtype></parameter><purpose>puts the day of month </purpose></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_char"><paramtype>char_type</paramtype></parameter><parameter name="dow"><paramtype>const day_of_week_type &amp;</paramtype></parameter></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_char"><paramtype>char_type</paramtype></parameter><parameter name="p"><paramtype>const <classname>period_type</classname> &amp;</paramtype></parameter></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_char"><paramtype>char_type</paramtype></parameter><parameter name="pd"><paramtype>const <classname>partial_date_type</classname> &amp;</paramtype></parameter></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_char"><paramtype>char_type</paramtype></parameter><parameter name="nkd"><paramtype>const <classname>nth_kday_type</classname> &amp;</paramtype></parameter></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_char"><paramtype>char_type</paramtype></parameter><parameter name="fkd"><paramtype>const <classname>first_kday_type</classname> &amp;</paramtype></parameter></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_char"><paramtype>char_type</paramtype></parameter><parameter name="lkd"><paramtype>const <classname>last_kday_type</classname> &amp;</paramtype></parameter></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_char"><paramtype>char_type</paramtype></parameter><parameter name="fkb"><paramtype>const <classname>kday_before_type</classname> &amp;</paramtype></parameter></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_char"><paramtype>char_type</paramtype></parameter><parameter name="fka"><paramtype>const <classname>kday_after_type</classname> &amp;</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit"><parameter name="a_ref"><paramtype>::size_t</paramtype><default>0</default></parameter></constructor>
<constructor specifiers="explicit"><parameter name="format_str"><paramtype>const char_type *</paramtype></parameter><parameter name="short_names"><paramtype>const input_collection_type &amp;</paramtype></parameter><parameter name="ref_count"><paramtype>::size_t</paramtype><default>0</default></parameter></constructor>
<constructor specifiers="explicit"><parameter name="format_str"><paramtype>const char_type *</paramtype></parameter><parameter name="per_formatter"><paramtype><classname>period_formatter_type</classname></paramtype><default><classname alt="boost::date_time::period_formatter">period_formatter_type</classname>()</default></parameter><parameter name="sv_formatter"><paramtype><classname>special_values_formatter_type</classname></paramtype><default><classname alt="boost::date_time::special_values_formatter">special_values_formatter_type</classname>()</default></parameter><parameter name="dg_formatter"><paramtype><classname>date_gen_formatter_type</classname></paramtype><default><classname alt="boost::date_time::date_generator_formatter">date_gen_formatter_type</classname>()</default></parameter><parameter name="ref_count"><paramtype>::size_t</paramtype><default>0</default></parameter></constructor>
<method-group name="protected member functions">
<method name="do_put_special" cv="const" specifiers="virtual"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name=""><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name=""><paramtype>char_type</paramtype></parameter><parameter name="sv"><paramtype>const boost::date_time::special_values</paramtype></parameter></method>
<method name="do_put_tm" cv="const" specifiers="virtual"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_char"><paramtype>char_type</paramtype></parameter><parameter name="tm_value"><paramtype>const tm &amp;</paramtype></parameter><parameter name="a_format"><paramtype>string_type</paramtype></parameter></method>
</method-group>
</class><class name="date_input_facet"><template>
      <template-type-parameter name="date_type"/>
      <template-type-parameter name="CharT"/>
      <template-type-parameter name="InItrT"><default>std::istreambuf_iterator&lt;CharT, std::char_traits&lt;CharT&gt; &gt;</default></template-type-parameter>
    </template><inherit access="public">facet</inherit><purpose>Input facet. </purpose><typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<typedef name="day_of_week_type"><type>date_type::day_of_week_type</type></typedef>
<typedef name="day_type"><type>date_type::day_type</type></typedef>
<typedef name="month_type"><type>date_type::month_type</type></typedef>
<typedef name="year_type"><type>date_type::year_type</type></typedef>
<typedef name="period_type"><type><classname>boost::date_time::period</classname>&lt; date_type, duration_type &gt;</type></typedef>
<typedef name="string_type"><type>std::basic_string&lt; CharT &gt;</type></typedef>
<typedef name="char_type"><type>CharT</type></typedef>
<typedef name="period_parser_type"><type><classname>boost::date_time::period_parser</classname>&lt; date_type, CharT &gt;</type></typedef>
<typedef name="special_values_parser_type"><type><classname>boost::date_time::special_values_parser</classname>&lt; date_type, CharT &gt;</type></typedef>
<typedef name="input_collection_type"><type>std::vector&lt; std::basic_string&lt; CharT &gt; &gt;</type></typedef>
<typedef name="format_date_parser_type"><type><classname>format_date_parser</classname>&lt; date_type, CharT &gt;</type></typedef>
<typedef name="date_gen_parser_type"><type><classname>date_generator_parser</classname>&lt; date_type, CharT &gt;</type></typedef>
<typedef name="partial_date_type"><type><classname>partial_date</classname>&lt; date_type &gt;</type></typedef>
<typedef name="nth_kday_type"><type><classname>nth_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="first_kday_type"><type><classname>first_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="last_kday_type"><type><classname>last_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="kday_after_type"><type><classname>first_kday_after</classname>&lt; date_type &gt;</type></typedef>
<typedef name="kday_before_type"><type><classname>first_kday_before</classname>&lt; date_type &gt;</type></typedef>
<data-member name="long_weekday_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="short_weekday_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="long_month_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="short_month_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="four_digit_year_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="two_digit_year_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="default_period_separator" specifiers="static"><type>const char_type</type></data-member>
<data-member name="standard_format_specifier" specifiers="static"><type>const char_type</type></data-member>
<data-member name="iso_format_specifier" specifiers="static"><type>const char_type</type></data-member>
<data-member name="iso_format_extended_specifier" specifiers="static"><type>const char_type</type></data-member>
<data-member name="default_date_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="id" specifiers="static"><type>std::locale::id</type></data-member>
<method-group name="public member functions">
<method name="format"><type>void</type><parameter name="format_str"><paramtype>const char_type *const</paramtype></parameter></method>
<method name="set_iso_format" specifiers="virtual"><type>void</type></method>
<method name="set_iso_extended_format" specifiers="virtual"><type>void</type></method>
<method name="month_format"><type>void</type><parameter name="format_str"><paramtype>const char_type *const</paramtype></parameter></method>
<method name="weekday_format"><type>void</type><parameter name="format_str"><paramtype>const char_type *const</paramtype></parameter></method>
<method name="year_format"><type>void</type><parameter name="format_str"><paramtype>const char_type *const</paramtype></parameter></method>
<method name="period_parser"><type>void</type><parameter name="per_parser"><paramtype><classname>period_parser_type</classname></paramtype></parameter></method>
<method name="short_weekday_names"><type>void</type><parameter name="weekday_names"><paramtype>const input_collection_type &amp;</paramtype></parameter></method>
<method name="long_weekday_names"><type>void</type><parameter name="weekday_names"><paramtype>const input_collection_type &amp;</paramtype></parameter></method>
<method name="short_month_names"><type>void</type><parameter name="month_names"><paramtype>const input_collection_type &amp;</paramtype></parameter></method>
<method name="long_month_names"><type>void</type><parameter name="month_names"><paramtype>const input_collection_type &amp;</paramtype></parameter></method>
<method name="date_gen_element_strings"><type>void</type><parameter name="col"><paramtype>const input_collection_type &amp;</paramtype></parameter></method>
<method name="date_gen_element_strings"><type>void</type><parameter name="first"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="second"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="third"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="fourth"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="fifth"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="last"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="before"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="after"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="of"><paramtype>const string_type &amp;</paramtype></parameter></method>
<method name="special_values_parser"><type>void</type><parameter name="sv_parser"><paramtype><classname>special_values_parser_type</classname></paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="from"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="to"><paramtype>InItrT &amp;</paramtype></parameter><parameter name=""><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="d"><paramtype>date_type &amp;</paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="from"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="to"><paramtype>InItrT &amp;</paramtype></parameter><parameter name=""><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="m"><paramtype>month_type &amp;</paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="from"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="to"><paramtype>InItrT &amp;</paramtype></parameter><parameter name=""><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="wd"><paramtype>day_of_week_type &amp;</paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="from"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="to"><paramtype>InItrT &amp;</paramtype></parameter><parameter name=""><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="d"><paramtype>day_type &amp;</paramtype></parameter><purpose>Expects 1 or 2 digit day range: 1-31. </purpose></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="from"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="to"><paramtype>InItrT &amp;</paramtype></parameter><parameter name=""><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="y"><paramtype>year_type &amp;</paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="from"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="to"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="dd"><paramtype>duration_type &amp;</paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="from"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="to"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="p"><paramtype><classname>period_type</classname> &amp;</paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="from"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="to"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="nkd"><paramtype><classname>nth_kday_type</classname> &amp;</paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="from"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="to"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="pd"><paramtype><classname>partial_date_type</classname> &amp;</paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="from"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="to"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fkd"><paramtype><classname>first_kday_type</classname> &amp;</paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="from"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="to"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="lkd"><paramtype><classname>last_kday_type</classname> &amp;</paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="from"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="to"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fkb"><paramtype><classname>kday_before_type</classname> &amp;</paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="from"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="to"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fka"><paramtype><classname>kday_after_type</classname> &amp;</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit"><parameter name="a_ref"><paramtype>::size_t</paramtype><default>0</default></parameter></constructor>
<constructor specifiers="explicit"><parameter name="format_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="a_ref"><paramtype>::size_t</paramtype><default>0</default></parameter></constructor>
<constructor specifiers="explicit"><parameter name="format_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="date_parser"><paramtype>const <classname>format_date_parser_type</classname> &amp;</paramtype></parameter><parameter name="sv_parser"><paramtype>const <classname>special_values_parser_type</classname> &amp;</paramtype></parameter><parameter name="per_parser"><paramtype>const <classname>period_parser_type</classname> &amp;</paramtype></parameter><parameter name="date_gen_parser"><paramtype>const <classname>date_gen_parser_type</classname> &amp;</paramtype></parameter><parameter name="ref_count"><paramtype>::size_t</paramtype><default>0</default></parameter></constructor>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_format_simple.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="simple_format"><template>
      <template-type-parameter name="charT"/>
    </template><purpose>Class to provide simple basic formatting rules. </purpose><method-group name="public static functions">
<method name="not_a_date" specifiers="static"><type>const charT *</type><purpose>String used printed is date is invalid. </purpose></method>
<method name="pos_infinity" specifiers="static"><type>const charT *</type><purpose>String used to for positive infinity value. </purpose></method>
<method name="neg_infinity" specifiers="static"><type>const charT *</type><purpose>String used to for positive infinity value. </purpose></method>
<method name="month_format" specifiers="static"><type>month_format_spec</type><purpose>Describe month format. </purpose></method>
<method name="date_order" specifiers="static"><type>ymd_order_spec</type></method>
<method name="has_date_sep_chars" specifiers="static"><type>bool</type><purpose>This format uses '-' to separate date elements. </purpose></method>
<method name="year_sep_char" specifiers="static"><type>charT</type><purpose>Char to sep? </purpose></method>
<method name="month_sep_char" specifiers="static"><type>charT</type><purpose>char between year-month </purpose></method>
<method name="day_sep_char" specifiers="static"><type>charT</type><purpose>Char to separate month-day. </purpose></method>
<method name="hour_sep_char" specifiers="static"><type>charT</type><purpose>char between date-hours </purpose></method>
<method name="minute_sep_char" specifiers="static"><type>charT</type><purpose>char between hour and minute </purpose></method>
<method name="second_sep_char" specifiers="static"><type>charT</type><purpose>char for second </purpose></method>
</method-group>
</class><class-specialization name="simple_format"><template>
    </template><specialization><template-arg>wchar_t</template-arg></specialization><purpose>Specialization of formmating rules for wchar_t. </purpose><method-group name="public static functions">
<method name="not_a_date" specifiers="static"><type>const wchar_t *</type><purpose>String used printed is date is invalid. </purpose></method>
<method name="pos_infinity" specifiers="static"><type>const wchar_t *</type><purpose>String used to for positive infinity value. </purpose></method>
<method name="neg_infinity" specifiers="static"><type>const wchar_t *</type><purpose>String used to for positive infinity value. </purpose></method>
<method name="month_format" specifiers="static"><type>month_format_spec</type><purpose>Describe month format. </purpose></method>
<method name="date_order" specifiers="static"><type>ymd_order_spec</type></method>
<method name="has_date_sep_chars" specifiers="static"><type>bool</type><purpose>This format uses '-' to separate date elements. </purpose></method>
<method name="year_sep_char" specifiers="static"><type>wchar_t</type><purpose>Char to sep? </purpose></method>
<method name="month_sep_char" specifiers="static"><type>wchar_t</type><purpose>char between year-month </purpose></method>
<method name="day_sep_char" specifiers="static"><type>wchar_t</type><purpose>Char to separate month-day. </purpose></method>
<method name="hour_sep_char" specifiers="static"><type>wchar_t</type><purpose>char between date-hours </purpose></method>
<method name="minute_sep_char" specifiers="static"><type>wchar_t</type><purpose>char between hour and minute </purpose></method>
<method name="second_sep_char" specifiers="static"><type>wchar_t</type><purpose>char for second </purpose></method>
</method-group>
</class-specialization>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_formatting.hpp">
<namespace name="boost">
<namespace name="date_time">

































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_formatting_limited.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="date_formatter"><template>
      <template-type-parameter name="date_type"/>
      <template-type-parameter name="format_type"/>
    </template><purpose>Convert a date to string using format policies. </purpose><method-group name="public static functions">
<method name="date_to_string" specifiers="static"><type>std::string</type><parameter name="d"><paramtype>date_type</paramtype></parameter><purpose>Convert to a date to standard string using format policies. </purpose></method>
<method name="date_to_string" specifiers="static"><type>string_type</type><parameter name="d"><paramtype>date_type</paramtype></parameter><purpose>Convert to a date to standard string using format policies. </purpose></method>
</method-group>
</class><class name="month_formatter"><template>
      <template-type-parameter name="month_type"/>
      <template-type-parameter name="format_type"/>
    </template><purpose>Formats a month as as string into an ostream. </purpose><method-group name="public static functions">
<method name="format_month" specifiers="static"><type>std::ostream &amp;</type><parameter name="month"><paramtype>const month_type &amp;</paramtype></parameter><parameter name="os"><paramtype>std::ostream &amp;</paramtype></parameter><purpose>Formats a month as as string into an ostream. </purpose><description><para>This function demands that month_type provide functions for converting to short and long strings if that capability is used. </para></description></method>
<method name="format_month" specifiers="static"><type>ostream_type &amp;</type><parameter name="month"><paramtype>const month_type &amp;</paramtype></parameter><parameter name="os"><paramtype>ostream_type &amp;</paramtype></parameter><purpose>Formats a month as as string into an ostream. </purpose><description><para>This function demands that month_type provide functions for converting to short and long strings if that capability is used. </para></description></method>
</method-group>
</class><class name="ymd_formatter"><template>
      <template-type-parameter name="ymd_type"/>
      <template-type-parameter name="format_type"/>
    </template><purpose>Convert ymd to a standard string formatting policies. </purpose><method-group name="public static functions">
<method name="ymd_to_string" specifiers="static"><type>std::string</type><parameter name="ymd"><paramtype>ymd_type</paramtype></parameter><purpose>Convert ymd to a standard string formatting policies. </purpose><description><para>This is standard code for handling date formatting with year-month-day based date information. This function uses the format_type to control whether the string will contain separator characters, and if so what the character will be. In addtion, it can format the month as either an integer or a string as controled by the formatting policy </para></description></method>
<method name="ymd_to_string" specifiers="static"><type>std::basic_string&lt; charT &gt;</type><parameter name="ymd"><paramtype>ymd_type</paramtype></parameter><purpose>Convert ymd to a standard string formatting policies. </purpose><description><para>This is standard code for handling date formatting with year-month-day based date information. This function uses the format_type to control whether the string will contain separator characters, and if so what the character will be. In addtion, it can format the month as either an integer or a string as controled by the formatting policy </para></description></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_formatting_locales.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="ostream_date_formatter"><template>
      <template-type-parameter name="date_type"/>
      <template-type-parameter name="facet_type"/>
      <template-type-parameter name="charT"><default>char</default></template-type-parameter>
    </template><purpose>Convert a date to string using format policies. </purpose><typedef name="ostream_type"><type>std::basic_ostream&lt; charT &gt;</type></typedef>
<typedef name="ymd_type"><type>date_type::ymd_type</type></typedef>
<method-group name="public static functions">
<method name="date_put" specifiers="static"><type>void</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter><parameter name="os"><paramtype>ostream_type &amp;</paramtype></parameter><parameter name="f"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>Put date into an ostream. </purpose></method>
<method name="date_put" specifiers="static"><type>void</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter><parameter name="os"><paramtype>ostream_type &amp;</paramtype></parameter><purpose>Put date into an ostream. </purpose></method>
</method-group>
</class><class name="ostream_month_formatter"><template>
      <template-type-parameter name="facet_type"/>
      <template-type-parameter name="charT"><default>char</default></template-type-parameter>
    </template><purpose>Formats a month as as string into an ostream. </purpose><typedef name="month_type"><type>facet_type::month_type</type></typedef>
<typedef name="ostream_type"><type>std::basic_ostream&lt; charT &gt;</type></typedef>
<method-group name="public static functions">
<method name="format_month" specifiers="static"><type>void</type><parameter name="month"><paramtype>const month_type &amp;</paramtype></parameter><parameter name="os"><paramtype>ostream_type &amp;</paramtype></parameter><parameter name="f"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>Formats a month as as string into an output iterator. </purpose></method>
</method-group>
</class><class name="ostream_weekday_formatter"><template>
      <template-type-parameter name="weekday_type"/>
      <template-type-parameter name="facet_type"/>
      <template-type-parameter name="charT"><default>char</default></template-type-parameter>
    </template><purpose>Formats a weekday. </purpose><typedef name="month_type"><type>facet_type::month_type</type></typedef>
<typedef name="ostream_type"><type>std::basic_ostream&lt; charT &gt;</type></typedef>
<method-group name="public static functions">
<method name="format_weekday" specifiers="static"><type>void</type><parameter name="wd"><paramtype>const weekday_type &amp;</paramtype></parameter><parameter name="os"><paramtype>ostream_type &amp;</paramtype></parameter><parameter name="f"><paramtype>const facet_type &amp;</paramtype></parameter><parameter name="as_long_string"><paramtype>bool</paramtype></parameter><purpose>Formats a month as as string into an output iterator. </purpose></method>
</method-group>
</class><class name="ostream_ymd_formatter"><template>
      <template-type-parameter name="ymd_type"/>
      <template-type-parameter name="facet_type"/>
      <template-type-parameter name="charT"><default>char</default></template-type-parameter>
    </template><purpose>Convert ymd to a standard string formatting policies. </purpose><typedef name="month_type"><type>ymd_type::month_type</type></typedef>
<typedef name="month_formatter_type"><type><classname>ostream_month_formatter</classname>&lt; facet_type, charT &gt;</type></typedef>
<typedef name="ostream_type"><type>std::basic_ostream&lt; charT &gt;</type></typedef>
<typedef name="foo_type"><type>std::basic_string&lt; charT &gt;</type></typedef>
<method-group name="public static functions">
<method name="ymd_put" specifiers="static"><type>void</type><parameter name="ymd"><paramtype>ymd_type</paramtype></parameter><parameter name="os"><paramtype>ostream_type &amp;</paramtype></parameter><parameter name="f"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>Convert ymd to a standard string formatting policies. </purpose><description><para>This is standard code for handling date formatting with year-month-day based date information. This function uses the format_type to control whether the string will contain separator characters, and if so what the character will be. In addtion, it can format the month as either an integer or a string as controled by the formatting policy </para></description></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_generator_formatter.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="date_generator_formatter"><template>
      <template-type-parameter name="date_type"/>
      <template-type-parameter name="CharT"/>
      <template-type-parameter name="OutItrT"><default>std::ostreambuf_iterator&lt;CharT, std::char_traits&lt;CharT&gt; &gt;</default></template-type-parameter>
    </template><purpose>Formats date_generators for output. </purpose><description><para>Formatting of date_generators follows specific orders for the various types of date_generators.<itemizedlist>
<listitem><para><classname alt="boost::date_time::partial_date">partial_date</classname> =&gt; "dd Month"</para>
</listitem><listitem><para>nth_day_of_the_week_in_month =&gt; "nth weekday of month"</para>
</listitem><listitem><para>first_day_of_the_week_in_month =&gt; "first weekday of month"</para>
</listitem><listitem><para>last_day_of_the_week_in_month =&gt; "last weekday of month"</para>
</listitem><listitem><para>first_day_of_the_week_after =&gt; "weekday after"</para>
</listitem><listitem><para>first_day_of_the_week_before =&gt; "weekday before" While the order of the elements in these phrases cannot be changed, the elements themselves can be. Weekday and Month get their formats and names from the <classname alt="boost::date_time::date_facet">date_facet</classname>. The remaining elements are stored in the <classname alt="boost::date_time::date_generator_formatter">date_generator_formatter</classname> and can be customized upon construction or via a member function. The default elements are those shown in the examples above. </para>
</listitem></itemizedlist>
</para></description><enum name="phrase_elements"><enumvalue name="first"><default>=0</default></enumvalue><enumvalue name="second"/><enumvalue name="third"/><enumvalue name="fourth"/><enumvalue name="fifth"/><enumvalue name="last"/><enumvalue name="before"/><enumvalue name="after"/><enumvalue name="of"/><enumvalue name="number_of_phrase_elements"/></enum>
<typedef name="partial_date_type"><type><classname>partial_date</classname>&lt; date_type &gt;</type></typedef>
<typedef name="nth_kday_type"><type><classname>nth_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="first_kday_type"><type><classname>first_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="last_kday_type"><type><classname>last_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="kday_after_type"><type><classname>first_kday_after</classname>&lt; date_type &gt;</type></typedef>
<typedef name="kday_before_type"><type><classname>first_kday_before</classname>&lt; date_type &gt;</type></typedef>
<typedef name="char_type"><type>CharT</type></typedef>
<typedef name="string_type"><type>std::basic_string&lt; char_type &gt;</type></typedef>
<typedef name="collection_type"><type>std::vector&lt; string_type &gt;</type></typedef>
<data-member name="first_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="second_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="third_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="fourth_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="fifth_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="last_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="before_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="after_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="of_string" specifiers="static"><type>const char_type</type></data-member>
<method-group name="public member functions">
<method name="elements"><type>void</type><parameter name="new_strings"><paramtype>const collection_type &amp;</paramtype></parameter><parameter name="beg_pos"><paramtype>phrase_elements</paramtype><default>first</default></parameter><purpose>Replace the set of phrase elements with those contained in new_strings. </purpose><description><para>The order of the strings in the given collection is important. They must follow:<itemizedlist>
<listitem><para>first, second, third, fourth, fifth, last, before, after, of.</para>
</listitem></itemizedlist>
</para><para>It is not necessary to send in a complete set if only a few elements are to be replaced as long as the correct beg_pos is used.</para><para>Ex: To keep the default first through fifth elements, but replace the rest with a collection of:<itemizedlist>
<listitem><para>"final", "prior", "following", "in". The beg_pos of date_generator_formatter::last would be used. </para>
</listitem></itemizedlist>
</para></description></method>
<method name="put_partial_date" cv="const"><type>OutItrT</type><template>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="a_fill"><paramtype>CharT</paramtype></parameter><parameter name="pd"><paramtype>const <classname>partial_date_type</classname> &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>Put a <classname alt="boost::date_time::partial_date">partial_date</classname> =&gt; "dd Month". </purpose></method>
<method name="put_nth_kday" cv="const"><type>OutItrT</type><template>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="a_fill"><paramtype>CharT</paramtype></parameter><parameter name="nkd"><paramtype>const <classname>nth_kday_type</classname> &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>Put an nth_day_of_the_week_in_month =&gt; "nth weekday of month". </purpose></method>
<method name="put_first_kday" cv="const"><type>OutItrT</type><template>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="a_fill"><paramtype>CharT</paramtype></parameter><parameter name="fkd"><paramtype>const <classname>first_kday_type</classname> &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>Put a first_day_of_the_week_in_month =&gt; "first weekday of month". </purpose></method>
<method name="put_last_kday" cv="const"><type>OutItrT</type><template>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="a_fill"><paramtype>CharT</paramtype></parameter><parameter name="lkd"><paramtype>const <classname>last_kday_type</classname> &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>Put a last_day_of_the_week_in_month =&gt; "last weekday of month". </purpose></method>
<method name="put_kday_before" cv="const"><type>OutItrT</type><template>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="a_fill"><paramtype>CharT</paramtype></parameter><parameter name="fkb"><paramtype>const <classname>kday_before_type</classname> &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>Put a first_day_of_the_week_before =&gt; "weekday before". </purpose></method>
<method name="put_kday_after" cv="const"><type>OutItrT</type><template>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="a_fill"><paramtype>CharT</paramtype></parameter><parameter name="fka"><paramtype>const <classname>kday_after_type</classname> &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>Put a first_day_of_the_week_after =&gt; "weekday after". </purpose></method>
</method-group>
<constructor><purpose>Default format elements used. </purpose></constructor>
<constructor><parameter name="first_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="second_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="third_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="fourth_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="fifth_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="last_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="before_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="after_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="of_str"><paramtype>const string_type &amp;</paramtype></parameter><purpose>Constructor that allows for a custom set of phrase elements. </purpose></constructor>
<method-group name="private member functions">
<method name="put_string" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="str"><paramtype>const string_type &amp;</paramtype></parameter><purpose>helper function to put the various member string into stream </purpose></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_generator_parser.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="date_generator_parser"><template>
      <template-type-parameter name="date_type"/>
      <template-type-parameter name="charT"/>
    </template><purpose>Class for date_generator parsing. </purpose><description><para>The elements of a date_generator "phrase" are parsed from the input stream in a particular order. All elements are required and the order in which they appear cannot change, however, the elements themselves can be changed. The default elements and their order are as follows:</para><para><itemizedlist>
<listitem><para><classname alt="boost::date_time::partial_date">partial_date</classname> =&gt; "dd Month"</para>
</listitem><listitem><para>nth_day_of_the_week_in_month =&gt; "nth weekday of month"</para>
</listitem><listitem><para>first_day_of_the_week_in_month =&gt; "first weekday of month"</para>
</listitem><listitem><para>last_day_of_the_week_in_month =&gt; "last weekday of month"</para>
</listitem><listitem><para>first_day_of_the_week_after =&gt; "weekday after"</para>
</listitem><listitem><para>first_day_of_the_week_before =&gt; "weekday before"</para>
</listitem></itemizedlist>
</para><para>Weekday and Month names and formats are handled via the <classname alt="boost::date_time::date_input_facet">date_input_facet</classname>. </para></description><enum name="phrase_elements"><enumvalue name="first"><default>=0</default></enumvalue><enumvalue name="second"/><enumvalue name="third"/><enumvalue name="fourth"/><enumvalue name="fifth"/><enumvalue name="last"/><enumvalue name="before"/><enumvalue name="after"/><enumvalue name="of"/><enumvalue name="number_of_phrase_elements"/></enum>
<typedef name="string_type"><type>std::basic_string&lt; charT &gt;</type></typedef>
<typedef name="stream_itr_type"><type>std::istreambuf_iterator&lt; charT &gt;</type></typedef>
<typedef name="month_type"><type>date_type::month_type</type></typedef>
<typedef name="day_of_week_type"><type>date_type::day_of_week_type</type></typedef>
<typedef name="day_type"><type>date_type::day_type</type></typedef>
<typedef name="parse_tree_type"><type><classname>string_parse_tree</classname>&lt; charT &gt;</type></typedef>
<typedef name="match_results"><type><classname>parse_tree_type::parse_match_result_type</classname></type></typedef>
<typedef name="collection_type"><type>std::vector&lt; std::basic_string&lt; charT &gt; &gt;</type></typedef>
<typedef name="partial_date_type"><type><classname>partial_date</classname>&lt; date_type &gt;</type></typedef>
<typedef name="nth_kday_type"><type><classname>nth_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="first_kday_type"><type><classname>first_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="last_kday_type"><type><classname>last_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="kday_after_type"><type><classname>first_kday_after</classname>&lt; date_type &gt;</type></typedef>
<typedef name="kday_before_type"><type><classname>first_kday_before</classname>&lt; date_type &gt;</type></typedef>
<typedef name="char_type"><type>charT</type></typedef>
<data-member name="first_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="second_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="third_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="fourth_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="fifth_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="last_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="before_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="after_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="of_string" specifiers="static"><type>const char_type</type></data-member>
<method-group name="public member functions">
<method name="element_strings"><type>void</type><parameter name="first_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="second_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="third_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="fourth_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="fifth_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="last_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="before_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="after_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="of_str"><paramtype>const string_type &amp;</paramtype></parameter><purpose>Replace strings that determine nth week for generator. </purpose></method>
<method name="element_strings"><type>void</type><parameter name="col"><paramtype>const collection_type &amp;</paramtype></parameter></method>
<method name="get_partial_date_type" cv="const"><type><classname>partial_date_type</classname></type><template>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="sitr"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>returns <classname alt="boost::date_time::partial_date">partial_date</classname> parsed from stream </purpose></method>
<method name="get_nth_kday_type" cv="const"><type><classname>nth_kday_type</classname></type><template>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="sitr"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>returns nth_kday_of_week parsed from stream </purpose></method>
<method name="get_first_kday_type" cv="const"><type><classname>first_kday_type</classname></type><template>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="sitr"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>returns first_kday_of_week parsed from stream </purpose></method>
<method name="get_last_kday_type" cv="const"><type><classname>last_kday_type</classname></type><template>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="sitr"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>returns last_kday_of_week parsed from stream </purpose></method>
<method name="get_kday_before_type" cv="const"><type><classname>kday_before_type</classname></type><template>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="sitr"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>returns first_kday_of_week parsed from stream </purpose></method>
<method name="get_kday_after_type" cv="const"><type><classname>kday_after_type</classname></type><template>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="sitr"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>returns first_kday_of_week parsed from stream </purpose></method>
</method-group>
<constructor><purpose>Creates a <classname alt="boost::date_time::date_generator_parser">date_generator_parser</classname> with the default set of "element_strings". </purpose></constructor>
<constructor><parameter name="first_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="second_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="third_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="fourth_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="fifth_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="last_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="before_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="after_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="of_str"><paramtype>const string_type &amp;</paramtype></parameter><purpose>Creates a <classname alt="boost::date_time::date_generator_parser">date_generator_parser</classname> using a user defined set of element strings. </purpose></constructor>
<method-group name="private member functions">
<method name="extract_element" cv="const"><type>void</type><parameter name="sitr"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="ele"><paramtype>typename date_generator_parser::phrase_elements</paramtype></parameter><purpose>Extracts phrase element from input. Throws ios_base::failure on error. </purpose></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_generators.hpp">
<para>Definition and implementation of date algorithm templates </para><namespace name="boost">
<namespace name="date_time">
<class name="first_kday_after"><template>
      <template-type-parameter name="date_type"/>
    </template><purpose>Calculate something like "First Sunday after Jan 1,2002. </purpose><description><para>Date generator that takes a date and finds kday after <programlisting language="c++">typedef boost::date_time::first_kday_after&lt;date&gt; firstkdayafter;
firstkdayafter fkaf(Monday);
fkaf.get_date(date(2002,Feb,1));
</programlisting> </para></description><typedef name="calendar_type"><type>date_type::calendar_type</type></typedef>
<typedef name="day_of_week_type"><type>calendar_type::day_of_week_type</type></typedef>
<typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<method-group name="public member functions">
<method name="get_date" cv="const"><type>date_type</type><parameter name="start_day"><paramtype>date_type</paramtype></parameter><purpose>Return next kday given. </purpose></method>
<method name="day_of_week" cv="const"><type>day_of_week_type</type></method>
</method-group>
<constructor><parameter name="dow"><paramtype>day_of_week_type</paramtype></parameter></constructor>
</class><class name="first_kday_before"><template>
      <template-type-parameter name="date_type"/>
    </template><purpose>Calculate something like "First Sunday before Jan 1,2002. </purpose><description><para>Date generator that takes a date and finds kday after <programlisting language="c++">typedef boost::date_time::first_kday_before&lt;date&gt; firstkdaybefore;
firstkdaybefore fkbf(Monday);
fkbf.get_date(date(2002,Feb,1));
</programlisting> </para></description><typedef name="calendar_type"><type>date_type::calendar_type</type></typedef>
<typedef name="day_of_week_type"><type>calendar_type::day_of_week_type</type></typedef>
<typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<method-group name="public member functions">
<method name="get_date" cv="const"><type>date_type</type><parameter name="start_day"><paramtype>date_type</paramtype></parameter><purpose>Return next kday given. </purpose></method>
<method name="day_of_week" cv="const"><type>day_of_week_type</type></method>
</method-group>
<constructor><parameter name="dow"><paramtype>day_of_week_type</paramtype></parameter></constructor>
</class><class name="first_kday_of_month"><template>
      <template-type-parameter name="date_type"/>
    </template><inherit access="public">boost::date_time::year_based_generator&lt; date_type &gt;</inherit><purpose>Useful generator functor for finding holidays and daylight savings. </purpose><description><para>Similar to <classname alt="boost::date_time::nth_kday_of_month">nth_kday_of_month</classname>, but requires less paramters </para></description><typedef name="calendar_type"><type>date_type::calendar_type</type></typedef>
<typedef name="day_of_week_type"><type>calendar_type::day_of_week_type</type></typedef>
<typedef name="month_type"><type>calendar_type::month_type</type></typedef>
<typedef name="year_type"><type>calendar_type::year_type</type></typedef>
<typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<method-group name="public member functions">
<method name="get_date" cv="const" specifiers="virtual"><type>date_type</type><parameter name="year"><paramtype>year_type</paramtype></parameter><purpose>Return a concrete date when provided with a year specific year. </purpose></method>
<method name="month" cv="const"><type>month_type</type></method>
<method name="day_of_week" cv="const"><type>day_of_week_type</type></method>
<method name="to_string" cv="const" specifiers="virtual"><type>std::string</type><purpose>Returns string suitable for use in POSIX time zone string. </purpose><description><para>Returns a string formatted as "M4.1.0" ==&gt; 1st Sunday in April. </para></description></method>
</method-group>
<constructor><parameter name="dow"><paramtype>day_of_week_type</paramtype><description><para>The day of week, eg: Sunday, Monday, etc </para></description></parameter><parameter name="m"><paramtype>month_type</paramtype><description><para>The month of the year, eg: Jan, Feb, Mar, etc </para></description></parameter><purpose>Specify the first 'Sunday' in 'April' spec. </purpose><description><para>
</para></description></constructor>
</class><class name="last_kday_of_month"><template>
      <template-type-parameter name="date_type"><purpose><para>A date class that exports day_of_week, month_type, etc. </para></purpose></template-type-parameter>
    </template><inherit access="public">boost::date_time::year_based_generator&lt; date_type &gt;</inherit><purpose>Calculate something like Last Sunday of January. </purpose><description><para>Useful generator functor for finding holidays and daylight savings Get the last day of the month and then calculate the difference to the last previous day. 
</para></description><typedef name="calendar_type"><type>date_type::calendar_type</type></typedef>
<typedef name="day_of_week_type"><type>calendar_type::day_of_week_type</type></typedef>
<typedef name="month_type"><type>calendar_type::month_type</type></typedef>
<typedef name="year_type"><type>calendar_type::year_type</type></typedef>
<typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<method-group name="public member functions">
<method name="get_date" cv="const" specifiers="virtual"><type>date_type</type><parameter name="year"><paramtype>year_type</paramtype></parameter><purpose>Return a concrete date when provided with a year specific year. </purpose></method>
<method name="month" cv="const"><type>month_type</type></method>
<method name="day_of_week" cv="const"><type>day_of_week_type</type></method>
<method name="to_string" cv="const" specifiers="virtual"><type>std::string</type><purpose>Returns string suitable for use in POSIX time zone string. </purpose><description><para>Returns a string formatted as "M4.5.0" ==&gt; last Sunday in April. </para></description></method>
</method-group>
<constructor><parameter name="dow"><paramtype>day_of_week_type</paramtype><description><para>The day of week, eg: Sunday, Monday, etc </para></description></parameter><parameter name="m"><paramtype>month_type</paramtype><description><para>The month of the year, eg: Jan, Feb, Mar, etc </para></description></parameter><purpose>Specify the date spec like last 'Sunday' in 'April' spec. </purpose><description><para>
</para></description></constructor>
</class><class name="nth_kday_of_month"><template>
      <template-type-parameter name="date_type"/>
    </template><inherit access="public">boost::date_time::year_based_generator&lt; date_type &gt;</inherit><purpose>Useful generator functor for finding holidays. </purpose><description><para>Based on the idea in Cal. Calc. for finding holidays that are the 'first Monday of September'. When instantiated with 'fifth' kday of month, the result will be the last kday of month which can be the fourth or fifth depending on the structure of the month.</para><para>The algorithm here basically guesses for the first day of the month. Then finds the first day of the correct type. That is, if the first of the month is a Tuesday and it needs Wednesday then we simply increment by a day and then we can add the length of a week until we get to the 'nth kday'. There are probably more efficient algorithms based on using a mod 7, but this one works reasonably well for basic applications. </para></description><enum name="week_num"><enumvalue name="first"><default>=1</default></enumvalue><enumvalue name="second"/><enumvalue name="third"/><enumvalue name="fourth"/><enumvalue name="fifth"/></enum>
<typedef name="calendar_type"><type>date_type::calendar_type</type></typedef>
<typedef name="day_of_week_type"><type>calendar_type::day_of_week_type</type></typedef>
<typedef name="month_type"><type>calendar_type::month_type</type></typedef>
<typedef name="year_type"><type>calendar_type::year_type</type></typedef>
<typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<method-group name="public member functions">
<method name="get_date" cv="const" specifiers="virtual"><type>date_type</type><parameter name="y"><paramtype>year_type</paramtype></parameter><purpose>Return a concrete date when provided with a year specific year. </purpose></method>
<method name="month" cv="const"><type>month_type</type></method>
<method name="nth_week" cv="const"><type>week_num</type></method>
<method name="day_of_week" cv="const"><type>day_of_week_type</type></method>
<method name="nth_week_as_str" cv="const"><type>const char *</type></method>
<method name="to_string" cv="const" specifiers="virtual"><type>std::string</type><purpose>Returns string suitable for use in POSIX time zone string. </purpose><description><para>Returns a string formatted as "M4.3.0" ==&gt; 3rd Sunday in April. </para></description></method>
</method-group>
<constructor><parameter name="week_no"><paramtype>week_num</paramtype></parameter><parameter name="dow"><paramtype>day_of_week_type</paramtype></parameter><parameter name="m"><paramtype>month_type</paramtype></parameter></constructor>
</class><class name="partial_date"><template>
      <template-type-parameter name="date_type"/>
    </template><inherit access="public">boost::date_time::year_based_generator&lt; date_type &gt;</inherit><purpose>Generates a date by applying the year to the given month and day. </purpose><description><para>Example usage: <programlisting language="c++">partial_date pd(1, Jan);
partial_date pd2(70);
date d = pd.get_date(2002); //2002-Jan-01
date d2 = pd2.get_date(2002); //2002-Mar-10
</programlisting> </para></description><typedef name="calendar_type"><type>date_type::calendar_type</type></typedef>
<typedef name="day_type"><type>calendar_type::day_type</type></typedef>
<typedef name="month_type"><type>calendar_type::month_type</type></typedef>
<typedef name="year_type"><type>calendar_type::year_type</type></typedef>
<typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<typedef name="duration_rep"><type>duration_type::duration_rep</type></typedef>
<method-group name="public member functions">
<method name="get_date" cv="const" specifiers="virtual"><type>date_type</type><parameter name="y"><paramtype>year_type</paramtype></parameter><purpose>Return a concrete date when provided with a year specific year. </purpose><description><para>Will throw an 'invalid_argument' exception if a <classname alt="boost::date_time::partial_date">partial_date</classname> object, instantiated with Feb-29, has get_date called with a non-leap year. Example: <programlisting language="c++">partial_date pd(29, Feb);
pd.get_date(2003); // throws invalid_argument exception
pg.get_date(2000); // returns 2000-2-29
</programlisting> </para></description></method>
<method name="operator()" cv="const"><type>date_type</type><parameter name="y"><paramtype>year_type</paramtype></parameter></method>
<method name="operator==" cv="const"><type>bool</type><parameter name="rhs"><paramtype>const <classname>partial_date</classname> &amp;</paramtype></parameter></method>
<method name="operator&lt;" cv="const"><type>bool</type><parameter name="rhs"><paramtype>const <classname>partial_date</classname> &amp;</paramtype></parameter></method>
<method name="month" cv="const"><type>month_type</type></method>
<method name="day" cv="const"><type>day_type</type></method>
<method name="to_string" cv="const" specifiers="virtual"><type>std::string</type><purpose>Returns string suitable for use in POSIX time zone string. </purpose><description><para>Returns string formatted with up to 3 digits: Jan-01 == "0" Feb-29 == "58" Dec-31 == "365" </para></description></method>
</method-group>
<constructor><parameter name="d"><paramtype>day_type</paramtype></parameter><parameter name="m"><paramtype>month_type</paramtype></parameter></constructor>
<constructor><parameter name="days"><paramtype>duration_rep</paramtype></parameter><purpose>Partial date created from number of days into year. Range 1-366. </purpose><description><para>Allowable values range from 1 to 366. 1=Jan1, 366=Dec31. If argument exceeds range, <classname alt="boost::date_time::partial_date">partial_date</classname> will be created with closest in-range value. 60 will always be Feb29, if get_date() is called with a non-leap year an exception will be thrown </para></description></constructor>
</class><class name="year_based_generator"><template>
      <template-type-parameter name="date_type"><purpose><para>The type representing a date. This type must export a calender_type which defines a year_type. </para></purpose></template-type-parameter>
    </template><purpose>Base class for all generators that take a year and produce a date. </purpose><description><para>This class is a base class for polymorphic function objects that take a year and produce a concrete date. 
</para></description><typedef name="calendar_type"><type>date_type::calendar_type</type></typedef>
<typedef name="year_type"><type>calendar_type::year_type</type></typedef>
<method-group name="public member functions">
<method name="get_date" cv="const = 0" specifiers="virtual"><type>date_type</type><parameter name="y"><paramtype>year_type</paramtype></parameter></method>
<method name="to_string" cv="const = 0" specifiers="virtual"><type>std::string</type><purpose>Returns a string for use in a POSIX time_zone string. </purpose></method>
</method-group>
<constructor/>
<destructor/>
</class>



























<function name="nth_as_str"><type>const char *</type><parameter name="ele"><paramtype>int</paramtype></parameter><purpose>Returns nth arg as string. 1 -&gt; "first", 2 -&gt; "second", max is 5. </purpose></function>
<function name="days_until_weekday"><type>date_type::duration_type</type><template>
          <template-type-parameter name="date_type"/>
          <template-type-parameter name="weekday_type"/>
        </template><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter><parameter name="wd"><paramtype>const weekday_type &amp;</paramtype></parameter><purpose>Calculates the number of days until the next weekday. </purpose><description><para>Calculates the number of days until the next weekday. If the date given falls on a Sunday and the given weekday is Tuesday the result will be 2 days </para></description></function>
<function name="days_before_weekday"><type>date_type::duration_type</type><template>
          <template-type-parameter name="date_type"/>
          <template-type-parameter name="weekday_type"/>
        </template><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter><parameter name="wd"><paramtype>const weekday_type &amp;</paramtype></parameter><purpose>Calculates the number of days since the previous weekday. </purpose><description><para>Calculates the number of days since the previous weekday If the date given falls on a Sunday and the given weekday is Tuesday the result will be 5 days. The answer will be a positive number because Tuesday is 5 days before Sunday, not -5 days before. </para></description></function>
<function name="next_weekday"><type>date_type</type><template>
          <template-type-parameter name="date_type"/>
          <template-type-parameter name="weekday_type"/>
        </template><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter><parameter name="wd"><paramtype>const weekday_type &amp;</paramtype></parameter><purpose>Generates a date object representing the date of the following weekday from the given date. </purpose><description><para>Generates a date object representing the date of the following weekday from the given date. If the date given is 2004-May-9 (a Sunday) and the given weekday is Tuesday then the resulting date will be 2004-May-11. </para></description></function>
<function name="previous_weekday"><type>date_type</type><template>
          <template-type-parameter name="date_type"/>
          <template-type-parameter name="weekday_type"/>
        </template><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter><parameter name="wd"><paramtype>const weekday_type &amp;</paramtype></parameter><purpose>Generates a date object representing the date of the previous weekday from the given date. </purpose><description><para>Generates a date object representing the date of the previous weekday from the given date. If the date given is 2004-May-9 (a Sunday) and the given weekday is Tuesday then the resulting date will be 2004-May-4. </para></description></function>
</namespace>
</namespace>
</header>
<header name="boost/date_time/date_iterator.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="date_itr"><template>
      <template-type-parameter name="offset_functor"/>
      <template-type-parameter name="date_type"/>
    </template><inherit access="public">boost::date_time::date_itr_base&lt; date_type &gt;</inherit><purpose>Overrides the base date iterator providing hook for functors. </purpose><typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<method-group name="public member functions">
</method-group>
<constructor><parameter name="d"><paramtype>date_type</paramtype></parameter><parameter name="factor"><paramtype>int</paramtype><default>1</default></parameter></constructor>
<method-group name="private member functions">
<method name="get_offset" cv="const" specifiers="virtual"><type>duration_type</type><parameter name="current"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="get_neg_offset" cv="const" specifiers="virtual"><type>duration_type</type><parameter name="current"><paramtype>const date_type &amp;</paramtype></parameter></method>
</method-group>
</class><class name="date_itr_base"><template>
      <template-type-parameter name="date_type"/>
    </template><purpose>Base date iterator type. </purpose><description><para>This class provides the skeleton for the creation of iterators. New and interesting interators can be created by plugging in a new function that derives the next value from the current state. generation of various types of -based information.</para><para><emphasis role="bold">Template Parameters</emphasis></para><para><emphasis role="bold">date_type</emphasis></para><para>The date_type is a concrete date_type. The date_type must define a duration_type and a calendar_type. </para></description><typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<typedef name="value_type"><type>date_type</type></typedef>
<typedef name="iterator_category"><type>std::input_iterator_tag</type></typedef>
<method-group name="public member functions">
<method name="operator++"><type><classname>date_itr_base</classname> &amp;</type></method>
<method name="operator--"><type><classname>date_itr_base</classname> &amp;</type></method>
<method name="get_offset" cv="const = 0" specifiers="virtual"><type>duration_type</type><parameter name="current"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="get_neg_offset" cv="const = 0" specifiers="virtual"><type>duration_type</type><parameter name="current"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="operator *" cv="const"><type>const date_type &amp;</type></method>
<method name="operator-&gt;" cv="const"><type>const date_type *</type></method>
<method name="operator&lt;" cv="const"><type>bool</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="operator&lt;=" cv="const"><type>bool</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="operator&gt;" cv="const"><type>bool</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="operator&gt;=" cv="const"><type>bool</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="operator==" cv="const"><type>bool</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const"><type>bool</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="d"><paramtype>date_type</paramtype></parameter></constructor>
<destructor/>
</class><enum name="date_resolutions"><enumvalue name="day"/><enumvalue name="week"/><enumvalue name="months"/><enumvalue name="year"/><enumvalue name="decade"/><enumvalue name="century"/><enumvalue name="NumDateResolutions"/><purpose>An iterator over dates with varying resolution (day, week, month, year, etc) </purpose></enum>

































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_names_put.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="all_date_names_put"><template>
      <template-type-parameter name="Config"/>
      <template-type-parameter name="charT"><default>char</default></template-type-parameter>
      <template-type-parameter name="OutputIterator"><default>std::ostreambuf_iterator&lt;charT&gt;</default></template-type-parameter>
    </template><inherit access="public">boost::date_time::date_names_put&lt; Config, charT, OutputIterator &gt;</inherit><purpose>A date name output facet that takes an array of char* to define strings. </purpose><typedef name="iter_type"><type>OutputIterator</type></typedef>
<typedef name="month_enum"><type>Config::month_enum</type></typedef>
<typedef name="weekday_enum"><type>Config::weekday_enum</type></typedef>
<typedef name="special_value_enum"><type>Config::special_value_enum</type></typedef>
<method-group name="public member functions">
<method name="get_short_month_names" cv="const"><type>const charT *const  *</type></method>
<method name="get_long_month_names" cv="const"><type>const charT *const  *</type></method>
<method name="get_special_value_names" cv="const"><type>const charT *const  *</type></method>
<method name="get_short_weekday_names" cv="const"><type>const charT *const  *</type></method>
<method name="get_long_weekday_names" cv="const"><type>const charT *const  *</type></method>
</method-group>
<constructor><parameter name="month_short_names"><paramtype>const charT *const</paramtype></parameter><parameter name="month_long_names"><paramtype>const charT *const</paramtype></parameter><parameter name="special_value_names"><paramtype>const charT *const</paramtype></parameter><parameter name="weekday_short_names"><paramtype>const charT *const</paramtype></parameter><parameter name="weekday_long_names"><paramtype>const charT *const</paramtype></parameter><parameter name="separator_char"><paramtype>charT</paramtype><default>'-'</default></parameter><parameter name="order_spec"><paramtype>ymd_order_spec</paramtype><default>ymd_order_iso</default></parameter><parameter name="month_format"><paramtype>month_format_spec</paramtype><default>month_as_short_string</default></parameter></constructor>
<method-group name="protected member functions">
<method name="do_put_month_short" cv="const" specifiers="virtual"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="moy"><paramtype>month_enum</paramtype></parameter><purpose>Generic facet that takes array of chars. </purpose></method>
<method name="do_put_month_long" cv="const" specifiers="virtual"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="moy"><paramtype>month_enum</paramtype></parameter><purpose>Long month names. </purpose></method>
<method name="do_put_special_value" cv="const" specifiers="virtual"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="sv"><paramtype>special_value_enum</paramtype></parameter><purpose>Special values names. </purpose></method>
<method name="do_put_weekday_short" cv="const" specifiers="virtual"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="wd"><paramtype>weekday_enum</paramtype></parameter></method>
<method name="do_put_weekday_long" cv="const" specifiers="virtual"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="wd"><paramtype>weekday_enum</paramtype></parameter></method>
<method name="do_month_sep_char" cv="const" specifiers="virtual"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><purpose>char between year-month </purpose></method>
<method name="do_day_sep_char" cv="const" specifiers="virtual"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><purpose>Char to separate month-day. </purpose></method>
<method name="do_date_order" cv="const" specifiers="virtual"><type>ymd_order_spec</type><purpose>Set the date ordering. </purpose></method>
<method name="do_month_format" cv="const" specifiers="virtual"><type>month_format_spec</type><purpose>Set the date ordering. </purpose></method>
</method-group>
</class><class name="date_names_put"><template>
      <template-type-parameter name="Config"/>
      <template-type-parameter name="charT"><default>char</default></template-type-parameter>
      <template-type-parameter name="OutputIterator"><default>std::ostreambuf_iterator&lt;charT&gt;</default></template-type-parameter>
    </template><inherit access="public">facet</inherit><purpose>Output facet base class for gregorian dates. </purpose><description><para>This class is a base class for date facets used to localize the names of months and the names of days in the week.</para><para>Requirements of Config<itemizedlist>
<listitem><para>define an enumeration month_enum that enumerates the months. The enumeration should be '1' based eg: Jan==1</para>
</listitem><listitem><para>define as_short_string and as_long_string</para>
</listitem></itemizedlist>
</para><para>(see langer &amp; kreft p334). </para></description><typedef name="iter_type"><type>OutputIterator</type></typedef>
<typedef name="month_type"><type>Config::month_type</type></typedef>
<typedef name="month_enum"><type>Config::month_enum</type></typedef>
<typedef name="weekday_enum"><type>Config::weekday_enum</type></typedef>
<typedef name="special_value_enum"><type>Config::special_value_enum</type></typedef>
<typedef name="string_type"><type>std::basic_string&lt; charT &gt;</type></typedef>
<typedef name="char_type"><type>charT</type></typedef>
<data-member name="default_special_value_names" specifiers="static"><type>const char_type</type></data-member>
<data-member name="separator" specifiers="static"><type>const char_type</type></data-member>
<data-member name="id" specifiers="static"><type>std::locale::id</type><purpose>Generate storage location for a std::locale::id. </purpose></data-member>
<method-group name="public member functions">
<method name="__get_id" cv="const"><type>std::locale::id &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="put_special_value" cv="const"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="sv"><paramtype>special_value_enum</paramtype></parameter></method>
<method name="put_month_short" cv="const"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="moy"><paramtype>month_enum</paramtype></parameter></method>
<method name="put_month_long" cv="const"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="moy"><paramtype>month_enum</paramtype></parameter></method>
<method name="put_weekday_short" cv="const"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="wd"><paramtype>weekday_enum</paramtype></parameter></method>
<method name="put_weekday_long" cv="const"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="wd"><paramtype>weekday_enum</paramtype></parameter></method>
<method name="has_date_sep_chars" cv="const"><type>bool</type></method>
<method name="year_sep_char" cv="const"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter></method>
<method name="month_sep_char" cv="const"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><purpose>char between year-month </purpose></method>
<method name="day_sep_char" cv="const"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><purpose>Char to separate month-day. </purpose></method>
<method name="date_order" cv="const"><type>ymd_order_spec</type><purpose>Determines the order to put the date elements. </purpose></method>
<method name="month_format" cv="const"><type>month_format_spec</type><purpose>Determines if month is displayed as integer, short or long string. </purpose></method>
</method-group>
<constructor/>
<method-group name="protected member functions">
<method name="do_put_month_short" cv="const" specifiers="virtual"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="moy"><paramtype>month_enum</paramtype></parameter><purpose>Default facet implementation uses month_type defaults. </purpose></method>
<method name="do_put_month_long" cv="const" specifiers="virtual"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="moy"><paramtype>month_enum</paramtype></parameter><purpose>Default facet implementation uses month_type defaults. </purpose></method>
<method name="do_put_special_value" cv="const" specifiers="virtual"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="sv"><paramtype>special_value_enum</paramtype></parameter><purpose>Default facet implementation for special value types. </purpose></method>
<method name="do_put_weekday_short" cv="const" specifiers="virtual"><type>void</type><parameter name=""><paramtype>iter_type &amp;</paramtype></parameter><parameter name=""><paramtype>weekday_enum</paramtype></parameter></method>
<method name="do_put_weekday_long" cv="const" specifiers="virtual"><type>void</type><parameter name=""><paramtype>iter_type &amp;</paramtype></parameter><parameter name=""><paramtype>weekday_enum</paramtype></parameter></method>
<method name="do_has_date_sep_chars" cv="const" specifiers="virtual"><type>bool</type></method>
<method name="do_year_sep_char" cv="const" specifiers="virtual"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter></method>
<method name="do_month_sep_char" cv="const" specifiers="virtual"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><purpose>char between year-month </purpose></method>
<method name="do_day_sep_char" cv="const" specifiers="virtual"><type>void</type><parameter name="oitr"><paramtype>iter_type &amp;</paramtype></parameter><purpose>Char to separate month-day. </purpose></method>
<method name="do_date_order" cv="const" specifiers="virtual"><type>ymd_order_spec</type><purpose>Default for date order. </purpose></method>
<method name="do_month_format" cv="const" specifiers="virtual"><type>month_format_spec</type><purpose>Default month format. </purpose></method>
<method name="put_string" cv="const"><type>void</type><parameter name="oi"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="s"><paramtype>const charT *const</paramtype></parameter></method>
<method name="put_string" cv="const"><type>void</type><parameter name="oi"><paramtype>iter_type &amp;</paramtype></parameter><parameter name="s1"><paramtype>const string_type &amp;</paramtype></parameter></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/date_parsing.hpp">
<namespace name="boost">
<namespace name="date_time">



















<function name="convert_to_lower"><type>std::string</type><parameter name="inp"><paramtype>std::string</paramtype></parameter><purpose>A function to replace the std::transform( , , ,tolower) construct. </purpose><description><para>This function simply takes a string, and changes all the characters in that string to lowercase (according to the default system locale). In the event that a compiler does not support locales, the old C style tolower() is used. </para></description></function>
<function name="month_str_to_ushort"><type>unsigned short</type><template>
          <template-type-parameter name="month_type"/>
        </template><parameter name="s"><paramtype>std::string const &amp;</paramtype></parameter><purpose>Helper function for parse_date. </purpose></function>
<function name="parse_date"><type>date_type</type><template>
          <template-type-parameter name="date_type"/>
        </template><parameter name="s"><paramtype>const std::string &amp;</paramtype></parameter><parameter name="order_spec"><paramtype>int</paramtype><default>ymd_order_iso</default></parameter><purpose>Generic function to parse a delimited date (eg: 2002-02-10) </purpose><description><para>Accepted formats are: "2003-02-10" or " 2003-Feb-10" or "2003-Feburary-10" The order in which the Month, Day, &amp; Year appear in the argument string can be accomodated by passing in the appropriate ymd_order_spec </para></description></function>
<function name="parse_undelimited_date"><type>date_type</type><template>
          <template-type-parameter name="date_type"/>
        </template><parameter name="s"><paramtype>const std::string &amp;</paramtype></parameter><purpose>Generic function to parse undelimited date (eg: 20020201) </purpose></function>
<function name="from_stream_type"><type>date_type</type><template>
          <template-type-parameter name="date_type"/>
          <template-type-parameter name="iterator_type"/>
        </template><parameter name="beg"><paramtype>iterator_type &amp;</paramtype></parameter><parameter name="end"><paramtype>iterator_type const &amp;</paramtype></parameter><parameter name=""><paramtype>char</paramtype></parameter><purpose>Helper function for 'date gregorian::from_stream()'. </purpose><description><para>Creates a string from the iterators that reference the begining &amp; end of a char[] or string. All elements are used in output string </para></description></function>
<function name="from_stream_type"><type>date_type</type><template>
          <template-type-parameter name="date_type"/>
          <template-type-parameter name="iterator_type"/>
        </template><parameter name="beg"><paramtype>iterator_type &amp;</paramtype></parameter><parameter name=""><paramtype>iterator_type const &amp;</paramtype></parameter><parameter name=""><paramtype>std::string const &amp;</paramtype></parameter><purpose>Helper function for 'date gregorian::from_stream()'. </purpose><description><para>Returns the first string found in the stream referenced by the begining &amp; end iterators </para></description></function>
<function name="from_stream_type"><type>date_type</type><template>
          <template-type-parameter name="date_type"/>
          <template-type-parameter name="iterator_type"/>
        </template><parameter name="beg"><paramtype>iterator_type &amp;</paramtype></parameter><parameter name="end"><paramtype>iterator_type const &amp;</paramtype></parameter><parameter name=""><paramtype>wchar_t</paramtype></parameter><purpose>Helper function for 'date gregorian::from_stream()'. </purpose><description><para>Creates a string from the iterators that reference the begining &amp; end of a wstring. All elements are used in output string </para></description></function>
<function name="from_stream_type"><type>date_type</type><template>
          <template-type-parameter name="date_type"/>
          <template-type-parameter name="iterator_type"/>
        </template><parameter name="beg"><paramtype>iterator_type &amp;</paramtype></parameter><parameter name=""><paramtype>iterator_type const &amp;</paramtype></parameter><parameter name=""><paramtype>std::wstring const &amp;</paramtype></parameter><purpose>Helper function for 'date gregorian::from_stream()'. </purpose><description><para>Creates a string from the first wstring found in the stream referenced by the begining &amp; end iterators </para></description></function>
<function name="from_simple_string_type"><type><classname>period</classname>&lt; date_type, typename date_type::duration_type &gt;</type><template>
          <template-type-parameter name="date_type"/>
          <template-type-parameter name="charT"/>
        </template><parameter name="s"><paramtype>const std::basic_string&lt; charT &gt; &amp;</paramtype></parameter><purpose>function called by wrapper functions: date_period_from_(w)string() </purpose></function>





</namespace>
</namespace>
</header>
<header name="boost/date_time/dst_rules.hpp">
<para>Contains template class to provide static dst rule calculations </para><namespace name="boost">
<namespace name="date_time">
<class name="dst_calc_engine"><template>
      <template-type-parameter name="date_type"/>
      <template-type-parameter name="time_duration_type"/>
      <template-type-parameter name="dst_traits"/>
    </template><purpose>Compile-time configurable daylight savings time calculation engine. </purpose><typedef name="year_type"><type>date_type::year_type</type></typedef>
<typedef name="calendar_type"><type>date_type::calendar_type</type></typedef>
<typedef name="dstcalc"><type><classname>dst_calculator</classname>&lt; date_type, time_duration_type &gt;</type></typedef>
<method-group name="public static functions">
<method name="local_is_dst" specifiers="static"><type>time_is_dst_result</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter><parameter name="td"><paramtype>const time_duration_type &amp;</paramtype></parameter><purpose>Calculates if the given local time is dst or not. </purpose><description><para>Determines if the time is really in DST or not. Also checks for invalid and ambiguous. 
</para></description></method>
<method name="is_dst_boundary_day" specifiers="static"><type>bool</type><parameter name="d"><paramtype>date_type</paramtype></parameter></method>
<method name="dst_offset" specifiers="static"><type>time_duration_type</type><purpose>The time of day for the dst transition (eg: typically 01:00:00 or 02:00:00) </purpose></method>
<method name="local_dst_start_day" specifiers="static"><type>date_type</type><parameter name="year"><paramtype>year_type</paramtype></parameter></method>
<method name="local_dst_end_day" specifiers="static"><type>date_type</type><parameter name="year"><paramtype>year_type</paramtype></parameter></method>
</method-group>
</class><class name="dst_calculator"><template>
      <template-type-parameter name="date_type_"/>
      <template-type-parameter name="time_duration_type_"/>
    </template><purpose>Dynamic class used to caluclate dst transition information. </purpose><typedef name="time_duration_type"><type>time_duration_type_</type></typedef>
<typedef name="date_type"><type>date_type_</type></typedef>
<method-group name="public static functions">
<method name="process_local_dst_start_day" specifiers="static"><type>time_is_dst_result</type><parameter name="time_of_day"><paramtype>const time_duration_type &amp;</paramtype><description><para>Time offset in the day for the local time </para></description></parameter><parameter name="dst_start_offset_minutes"><paramtype>unsigned int</paramtype><description><para>Local day offset for start of dst </para></description></parameter><parameter name="dst_length_minutes"><paramtype>long</paramtype><description><para>Number of minutes to adjust clock forward </para></description></parameter><purpose>Check the local time offset when on dst start day. </purpose><description><para>On this dst transition, the time label between the transition boundary and the boudary + the offset are invalid times. If before the boundary then still not in dst. 

</para></description></method>
<method name="process_local_dst_end_day" specifiers="static"><type>time_is_dst_result</type><parameter name="time_of_day"><paramtype>const time_duration_type &amp;</paramtype><description><para>Time offset in the day for the local time </para></description></parameter><parameter name="dst_end_offset_minutes"><paramtype>unsigned int</paramtype><description><para>Local time of day for end of dst </para></description></parameter><parameter name="dst_length_minutes"><paramtype>long</paramtype></parameter><purpose>Check the local time offset when on the last day of dst. </purpose><description><para>This is the calculation for the DST end day. On that day times prior to the conversion time - dst_length (1 am in US) are still in dst. Times between the above and the switch time are ambiguous. Times after the start_offset are not in dst. 

</para></description></method>
<method name="local_is_dst" specifiers="static"><type>time_is_dst_result</type><parameter name="current_day"><paramtype>const date_type &amp;</paramtype><description><para>The day to check for dst </para></description></parameter><parameter name="time_of_day"><paramtype>const time_duration_type &amp;</paramtype><description><para>Time offset within the day to check </para></description></parameter><parameter name="dst_start_day"><paramtype>const date_type &amp;</paramtype><description><para>Starting day of dst for the given locality </para></description></parameter><parameter name="dst_start_offset"><paramtype>const time_duration_type &amp;</paramtype><description><para>Time offset within day for dst boundary </para></description></parameter><parameter name="dst_end_day"><paramtype>const date_type &amp;</paramtype><description><para>Ending day of dst for the given locality </para></description></parameter><parameter name="dst_end_offset"><paramtype>const time_duration_type &amp;</paramtype><description><para>Time offset within day given in dst for dst boundary </para></description></parameter><parameter name="dst_length"><paramtype>const time_duration_type &amp;</paramtype></parameter><purpose>Calculates if the given local time is dst or not. </purpose><description><para>Determines if the time is really in DST or not. Also checks for invalid and ambiguous. 

</para></description></method>
<method name="local_is_dst" specifiers="static"><type>time_is_dst_result</type><parameter name="current_day"><paramtype>const date_type &amp;</paramtype><description><para>The day to check for dst </para></description></parameter><parameter name="time_of_day"><paramtype>const time_duration_type &amp;</paramtype><description><para>Time offset within the day to check </para></description></parameter><parameter name="dst_start_day"><paramtype>const date_type &amp;</paramtype><description><para>Starting day of dst for the given locality </para></description></parameter><parameter name="dst_start_offset_minutes"><paramtype>unsigned int</paramtype><description><para>Offset within day for dst boundary (eg 120 for US which is 02:00:00) </para></description></parameter><parameter name="dst_end_day"><paramtype>const date_type &amp;</paramtype><description><para>Ending day of dst for the given locality </para></description></parameter><parameter name="dst_end_offset_minutes"><paramtype>unsigned int</paramtype><description><para>Offset within day given in dst for dst boundary (eg 120 for US which is 02:00:00) </para></description></parameter><parameter name="dst_length_minutes"><paramtype>long</paramtype><description><para>Length of dst adjusment (eg: 60 for US) </para></description></parameter><purpose>Calculates if the given local time is dst or not. </purpose><description><para>Determines if the time is really in DST or not. Also checks for invalid and ambiguous. 

</para></description></method>
</method-group>
</class><class name="null_dst_rules"><template>
      <template-type-parameter name="date_type_"/>
      <template-type-parameter name="time_duration_type_"/>
    </template><purpose>Used for local time adjustments in places that don't use dst. </purpose><typedef name="time_duration_type"><type>time_duration_type_</type></typedef>
<typedef name="date_type"><type>date_type_</type></typedef>
<method-group name="public static functions">
<method name="local_is_dst" specifiers="static"><type>time_is_dst_result</type><parameter name=""><paramtype>const date_type &amp;</paramtype></parameter><parameter name=""><paramtype>const time_duration_type &amp;</paramtype></parameter><purpose>Calculates if the given local time is dst or not. </purpose><description><para>
</para></description></method>
<method name="utc_is_dst" specifiers="static"><type>time_is_dst_result</type><parameter name=""><paramtype>const date_type &amp;</paramtype></parameter><parameter name=""><paramtype>const time_duration_type &amp;</paramtype></parameter><purpose>Calculates if the given utc time is in dst. </purpose></method>
<method name="is_dst_boundary_day" specifiers="static"><type>bool</type><parameter name=""><paramtype>date_type</paramtype></parameter></method>
<method name="dst_offset" specifiers="static"><type>time_duration_type</type></method>
</method-group>
</class><class name="us_dst_rules"><template>
      <template-type-parameter name="date_type_"/>
      <template-type-parameter name="time_duration_type_"/>
      <template-nontype-parameter name="dst_start_offset_minutes"><type>unsigned int</type><default>120</default></template-nontype-parameter>
      <template-nontype-parameter name="dst_length_minutes"><type>short</type><default>60</default></template-nontype-parameter>
    </template><purpose>Depricated: Class to calculate dst boundaries for US time zones. </purpose><typedef name="time_duration_type"><type>time_duration_type_</type></typedef>
<typedef name="date_type"><type>date_type_</type></typedef>
<typedef name="year_type"><type>date_type::year_type</type></typedef>
<typedef name="calendar_type"><type>date_type::calendar_type</type></typedef>
<typedef name="lkday"><type><classname>date_time::last_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="fkday"><type><classname>date_time::first_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="nkday"><type><classname>date_time::nth_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="dstcalc"><type><classname>dst_calculator</classname>&lt; date_type, time_duration_type &gt;</type></typedef>
<method-group name="public static functions">
<method name="local_is_dst" specifiers="static"><type>time_is_dst_result</type><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter><parameter name="td"><paramtype>const time_duration_type &amp;</paramtype></parameter><purpose>Calculates if the given local time is dst or not. </purpose><description><para>Determines if the time is really in DST or not. Also checks for invalid and ambiguous. 
</para></description></method>
<method name="is_dst_boundary_day" specifiers="static"><type>bool</type><parameter name="d"><paramtype>date_type</paramtype></parameter></method>
<method name="local_dst_start_day" specifiers="static"><type>date_type</type><parameter name="year"><paramtype>year_type</paramtype></parameter></method>
<method name="local_dst_end_day" specifiers="static"><type>date_type</type><parameter name="year"><paramtype>year_type</paramtype></parameter></method>
<method name="dst_offset" specifiers="static"><type>time_duration_type</type></method>
</method-group>
</class><enum name="time_is_dst_result"><enumvalue name="is_not_in_dst"/><enumvalue name="is_in_dst"/><enumvalue name="ambiguous"/><enumvalue name="invalid_time_label"/></enum>

































</namespace>
</namespace>
</header>
<header name="boost/date_time/dst_transition_generators.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="day_calc_dst_rule"><template>
      <template-type-parameter name="spec"><purpose><para>Provides a specifiction of the function object types used to generate start and end days of daylight savings as well as the date type. </para></purpose></template-type-parameter>
    </template><inherit access="public">boost::date_time::dst_day_calc_rule&lt; spec::date_type &gt;</inherit><purpose>Canonical form for a class that provides day rule calculation. </purpose><description><para>This class is used to generate specific sets of dst rules</para><para>
</para></description><typedef name="date_type"><type>spec::date_type</type></typedef>
<typedef name="year_type"><type>date_type::year_type</type></typedef>
<typedef name="start_rule"><type>spec::start_rule</type></typedef>
<typedef name="end_rule"><type>spec::end_rule</type></typedef>
<method-group name="public member functions">
<method name="start_day" cv="const" specifiers="virtual"><type>date_type</type><parameter name="y"><paramtype>year_type</paramtype></parameter></method>
<method name="start_rule_as_string" cv="const" specifiers="virtual"><type>std::string</type></method>
<method name="end_day" cv="const" specifiers="virtual"><type>date_type</type><parameter name="y"><paramtype>year_type</paramtype></parameter></method>
<method name="end_rule_as_string" cv="const" specifiers="virtual"><type>std::string</type></method>
</method-group>
<constructor><parameter name="dst_start"><paramtype>start_rule</paramtype></parameter><parameter name="dst_end"><paramtype>end_rule</paramtype></parameter></constructor>
</class><class name="dst_day_calc_rule"><template>
      <template-type-parameter name="date_type"/>
    </template><purpose>Defines base interface for calculating start and end date of daylight savings. </purpose><typedef name="year_type"><type>date_type::year_type</type></typedef>
<method-group name="public member functions">
<method name="start_day" cv="const = 0" specifiers="virtual"><type>date_type</type><parameter name="y"><paramtype>year_type</paramtype></parameter></method>
<method name="start_rule_as_string" cv="const = 0" specifiers="virtual"><type>std::string</type></method>
<method name="end_day" cv="const = 0" specifiers="virtual"><type>date_type</type><parameter name="y"><paramtype>year_type</paramtype></parameter></method>
<method name="end_rule_as_string" cv="const = 0" specifiers="virtual"><type>std::string</type></method>
</method-group>
<destructor/>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/filetime_functions.hpp">
<para>Function(s) for converting between a FILETIME structure and a time object. This file is only available on systems that have BOOST_HAS_FTIME defined. </para><namespace name="boost">
<namespace name="date_time">


















<function name="time_from_ftime"><type>TimeT</type><template>
          <template-type-parameter name="TimeT"/>
          <template-type-parameter name="FileTimeT"/>
        </template><parameter name="ft"><paramtype>const FileTimeT &amp;</paramtype></parameter><purpose>Create a time object from an initialized FILETIME struct. </purpose><description><para>Create a time object from an initialized FILETIME struct. A FILETIME struct holds 100-nanosecond units (0.0000001). When built with microsecond resolution the file_time's sub second value will be truncated. Nanosecond resolution has no truncation.</para><para><note><para>The function is templated on the FILETIME type, so that it can be used with both native FILETIME and the ad-hoc boost::detail::winapi::FILETIME_ type. </para>
</note>
</para></description></function>














</namespace>
</namespace>
</header>
<header name="boost/date_time/find_match.hpp">
<namespace name="boost">
<namespace name="date_time">

















<function name="find_match"><type>short</type><template>
          <template-type-parameter name="charT"/>
        </template><parameter name="short_names"><paramtype>const charT *const *</paramtype></parameter><parameter name="long_names"><paramtype>const charT *const *</paramtype></parameter><parameter name="size"><paramtype>short</paramtype></parameter><parameter name="s"><paramtype>const std::basic_string&lt; charT &gt; &amp;</paramtype></parameter><purpose>Find index of a string in either of 2 arrays. </purpose><description><para>find_match searches both arrays for a match to 's'. Both arrays must contain 'size' elements. The index of the match is returned. If no match is found, 'size' is returned. Ex. "Jan" returns 0, "Dec" returns 11, "Tue" returns 2. 'size' can be sent in with: (greg_month::max)() (which 12), (greg_weekday::max)() + 1 (which is 7) or date_time::NumSpecialValues </para></description></function>















</namespace>
</namespace>
</header>
<header name="boost/date_time/format_date_parser.hpp">
<namespace name="std">
</namespace>
<namespace name="boost">
<namespace name="date_time">
<class name="format_date_parser"><template>
      <template-type-parameter name="date_type"/>
      <template-type-parameter name="charT"/>
    </template><purpose>Class with generic date parsing using a format string. </purpose><description><para>The following is the set of recognized format specifiers<itemizedlist>
<listitem><para>a - Short weekday name</para>
</listitem><listitem><para>A - Long weekday name</para>
</listitem><listitem><para>b - Abbreviated month name</para>
</listitem><listitem><para>B - Full month name</para>
</listitem><listitem><para>d - Day of the month as decimal 01 to 31</para>
</listitem><listitem><para>j - Day of year as decimal from 001 to 366</para>
</listitem><listitem><para>m - Month name as a decimal 01 to 12</para>
</listitem><listitem><para>U - Week number 00 to 53 with first Sunday as the first day of week 1?</para>
</listitem><listitem><para>w - Weekday as decimal number 0 to 6 where Sunday == 0</para>
</listitem><listitem><para>W - Week number 00 to 53 where Monday is first day of week 1</para>
</listitem><listitem><para>x - facet default date representation</para>
</listitem><listitem><para>y - Year without the century - eg: 04 for 2004</para>
</listitem><listitem><para>Y - Year with century</para>
</listitem></itemizedlist>
</para><para>The weekday specifiers (a and A) do not add to the date construction, but they provide a way to skip over the weekday names for formats that provide them.</para><para>todo – Another interesting feature that this approach could provide is an option to fill in any missing fields with the current values from the clock. So if you have m-d the parser would detect the missing year value and fill it in using the clock.</para><para>todo – What to do with the x. x in the classic facet is just bad... </para></description><typedef name="string_type"><type>std::basic_string&lt; charT &gt;</type></typedef>
<typedef name="stringstream_type"><type>std::basic_istringstream&lt; charT &gt;</type></typedef>
<typedef name="stream_itr_type"><type>std::istreambuf_iterator&lt; charT &gt;</type></typedef>
<typedef name="const_itr"><type>string_type::const_iterator</type></typedef>
<typedef name="year_type"><type>date_type::year_type</type></typedef>
<typedef name="month_type"><type>date_type::month_type</type></typedef>
<typedef name="day_type"><type>date_type::day_type</type></typedef>
<typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<typedef name="day_of_week_type"><type>date_type::day_of_week_type</type></typedef>
<typedef name="day_of_year_type"><type>date_type::day_of_year_type</type></typedef>
<typedef name="parse_tree_type"><type><classname>string_parse_tree</classname>&lt; charT &gt;</type></typedef>
<typedef name="match_results"><type><classname>parse_tree_type::parse_match_result_type</classname></type></typedef>
<typedef name="input_collection_type"><type>std::vector&lt; std::basic_string&lt; charT &gt; &gt;</type></typedef>
<method-group name="public member functions">
<method name="format" cv="const"><type>string_type</type></method>
<method name="format"><type>void</type><parameter name="format_str"><paramtype>string_type</paramtype></parameter></method>
<method name="short_month_names"><type>void</type><parameter name="month_names"><paramtype>const input_collection_type &amp;</paramtype></parameter></method>
<method name="long_month_names"><type>void</type><parameter name="month_names"><paramtype>const input_collection_type &amp;</paramtype></parameter></method>
<method name="short_weekday_names"><type>void</type><parameter name="weekday_names"><paramtype>const input_collection_type &amp;</paramtype></parameter></method>
<method name="long_weekday_names"><type>void</type><parameter name="weekday_names"><paramtype>const input_collection_type &amp;</paramtype></parameter></method>
<method name="parse_date" cv="const"><type>date_type</type><parameter name="value"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="format_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="sv_parser"><paramtype>const <classname>special_values_parser</classname>&lt; date_type, charT &gt; &amp;</paramtype></parameter></method>
<method name="parse_date" cv="const"><type>date_type</type><parameter name="sitr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="sv_parser"><paramtype>const <classname>special_values_parser</classname>&lt; date_type, charT &gt; &amp;</paramtype></parameter></method>
<method name="parse_date" cv="const"><type>date_type</type><parameter name="sitr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="format_str"><paramtype>string_type</paramtype></parameter><parameter name="sv_parser"><paramtype>const <classname>special_values_parser</classname>&lt; date_type, charT &gt; &amp;</paramtype></parameter><description><para>Of all the objects that the <classname alt="boost::date_time::format_date_parser">format_date_parser</classname> can parse, only a date can be a special value. Therefore, only parse_date checks for special_values. </para></description></method>
<method name="parse_month" cv="const"><type>month_type</type><parameter name="sitr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="format_str"><paramtype>string_type</paramtype></parameter><purpose>Throws bad_month if unable to parse. </purpose></method>
<method name="parse_month" cv="const"><type>month_type</type><parameter name="sitr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="format_str"><paramtype>string_type</paramtype></parameter><parameter name="mr"><paramtype><classname>match_results</classname> &amp;</paramtype></parameter><purpose>Throws bad_month if unable to parse. </purpose></method>
<method name="parse_var_day_of_month" cv="const"><type>day_type</type><parameter name="sitr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><purpose>Expects 1 or 2 digits 1-31. Throws bad_day_of_month if unable to parse. </purpose></method>
<method name="parse_day_of_month" cv="const"><type>day_type</type><parameter name="sitr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><purpose>Expects 2 digits 01-31. Throws bad_day_of_month if unable to parse. </purpose></method>
<method name="parse_weekday" cv="const"><type>day_of_week_type</type><parameter name="sitr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="format_str"><paramtype>string_type</paramtype></parameter></method>
<method name="parse_weekday" cv="const"><type>day_of_week_type</type><parameter name="sitr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="format_str"><paramtype>string_type</paramtype></parameter><parameter name="mr"><paramtype><classname>match_results</classname> &amp;</paramtype></parameter></method>
<method name="parse_year" cv="const"><type>year_type</type><parameter name="sitr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="format_str"><paramtype>string_type</paramtype></parameter><purpose>throws bad_year if unable to parse </purpose></method>
<method name="parse_year" cv="const"><type>year_type</type><parameter name="sitr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="format_str"><paramtype>string_type</paramtype></parameter><parameter name="mr"><paramtype><classname>match_results</classname> &amp;</paramtype></parameter><purpose>throws bad_year if unable to parse </purpose></method>
</method-group>
<constructor><parameter name="format_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="month_short_names"><paramtype>const input_collection_type &amp;</paramtype></parameter><parameter name="month_long_names"><paramtype>const input_collection_type &amp;</paramtype></parameter><parameter name="weekday_short_names"><paramtype>const input_collection_type &amp;</paramtype></parameter><parameter name="weekday_long_names"><paramtype>const input_collection_type &amp;</paramtype></parameter></constructor>
<constructor><parameter name="format_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="locale"><paramtype>const std::locale &amp;</paramtype></parameter></constructor>
<constructor><parameter name="fdp"><paramtype>const <classname>format_date_parser</classname>&lt; date_type, charT &gt; &amp;</paramtype></parameter></constructor>
</class>













<function name="fixed_string_to_int"><type>int_type</type><template>
          <template-type-parameter name="int_type"/>
          <template-type-parameter name="charT"/>
        </template><parameter name="itr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="mr"><paramtype><classname>parse_match_result</classname>&lt; charT &gt; &amp;</paramtype></parameter><parameter name="length"><paramtype>unsigned int</paramtype></parameter><parameter name="fill_char"><paramtype>const charT &amp;</paramtype></parameter><purpose>Helper function for parsing fixed length strings into integers. </purpose><description><para>Will consume 'length' number of characters from stream. Consumed character are transfered to <classname alt="boost::date_time::parse_match_result">parse_match_result</classname> struct. Returns '-1' if no number can be parsed or incorrect number of digits in stream. </para></description></function>
<function name="fixed_string_to_int"><type>int_type</type><template>
          <template-type-parameter name="int_type"/>
          <template-type-parameter name="charT"/>
        </template><parameter name="itr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="mr"><paramtype><classname>parse_match_result</classname>&lt; charT &gt; &amp;</paramtype></parameter><parameter name="length"><paramtype>unsigned int</paramtype></parameter><purpose>Helper function for parsing fixed length strings into integers. </purpose><description><para>Will consume 'length' number of characters from stream. Consumed character are transfered to <classname alt="boost::date_time::parse_match_result">parse_match_result</classname> struct. Returns '-1' if no number can be parsed or incorrect number of digits in stream. </para></description></function>
<function name="var_string_to_int"><type>int_type</type><template>
          <template-type-parameter name="int_type"/>
          <template-type-parameter name="charT"/>
        </template><parameter name="itr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>const std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="max_length"><paramtype>unsigned int</paramtype></parameter><purpose>Helper function for parsing varied length strings into integers. </purpose><description><para>Will consume 'max_length' characters from stream only if those characters are digits. Returns '-1' if no number can be parsed. Will not parse a number preceeded by a '+' or '-'. </para></description></function>
















</namespace>
</namespace>
</header>
<header name="boost/date_time/gregorian_calendar.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="gregorian_calendar_base"><template>
      <template-type-parameter name="ymd_type_"><purpose><para>Struct type representing the year, month, day. The ymd_type must define a of types for the year, month, and day. These types need to be arithmetic types. </para></purpose></template-type-parameter>
      <template-type-parameter name="date_int_type_"><purpose><para>Underlying type for the date count. Must be an arithmetic type. </para></purpose></template-type-parameter>
    </template><purpose>An implementation of the Gregorian calendar. </purpose><description><para>This is a parameterized implementation of a proleptic Gregorian Calendar that can be used in the creation of date systems or just to perform calculations. All the methods of this class are static functions, so the intent is to never create instances of this class. 
</para></description><typedef name="ymd_type"><purpose>define a type a date split into components </purpose><type>ymd_type_</type></typedef>
<typedef name="month_type"><purpose>define a type for representing months </purpose><type>ymd_type::month_type</type></typedef>
<typedef name="day_type"><purpose>define a type for representing days </purpose><type>ymd_type::day_type</type></typedef>
<typedef name="year_type"><purpose>Type to hold a stand alone year value (eg: 2002) </purpose><type>ymd_type::year_type</type></typedef>
<typedef name="date_int_type"><purpose>Define the integer type to use for internal calculations. </purpose><type>date_int_type_</type></typedef>
<method-group name="public static functions">
<method name="day_of_week" specifiers="static"><type>BOOST_CXX14_CONSTEXPR unsigned short</type><parameter name="ymd"><paramtype>const ymd_type &amp;</paramtype></parameter></method>
<method name="week_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR int</type><parameter name="ymd"><paramtype>const ymd_type &amp;</paramtype></parameter></method>
<method name="day_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR date_int_type</type><parameter name="ymd"><paramtype>const ymd_type &amp;</paramtype></parameter></method>
<method name="julian_day_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR date_int_type</type><parameter name="ymd"><paramtype>const ymd_type &amp;</paramtype></parameter></method>
<method name="modjulian_day_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR date_int_type</type><parameter name="ymd"><paramtype>const ymd_type &amp;</paramtype></parameter></method>
<method name="from_day_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR ymd_type</type><parameter name=""><paramtype>date_int_type</paramtype></parameter></method>
<method name="from_julian_day_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR ymd_type</type><parameter name=""><paramtype>date_int_type</paramtype></parameter></method>
<method name="from_modjulian_day_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR ymd_type</type><parameter name=""><paramtype>date_int_type</paramtype></parameter></method>
<method name="is_leap_year" specifiers="static"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name=""><paramtype>year_type</paramtype></parameter></method>
<method name="end_of_month_day" specifiers="static"><type>BOOST_CXX14_CONSTEXPR unsigned short</type><parameter name="y"><paramtype>year_type</paramtype></parameter><parameter name="m"><paramtype>month_type</paramtype></parameter></method>
<method name="epoch" specifiers="static"><type>BOOST_CXX14_CONSTEXPR ymd_type</type></method>
<method name="days_in_week" specifiers="static"><type>BOOST_CXX14_CONSTEXPR unsigned short</type></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/int_adapter.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="int_adapter"><template>
      <template-type-parameter name="int_type_"/>
    </template><purpose>Adapter to create integer types with +-infinity, and not a value. </purpose><description><para>This class is used internally in counted date/time representations. It adds the floating point like features of infinities and not a number. It also provides mathmatical operations with consideration to special values following these rules: <programlisting language="c++">+infinity  -  infinity  == Not A Number (NAN)
 infinity  *  non-zero  == infinity
 infinity  *  zero      == NAN
+infinity  * -integer   == -infinity
 infinity  /  infinity  == NAN
 infinity  *  infinity  == infinity 
</programlisting> </para></description><typedef name="int_type"><type>int_type_</type></typedef>
<method-group name="public member functions">
<method name="is_infinity" cv="const"><type>BOOST_CONSTEXPR bool</type></method>
<method name="is_pos_infinity" cv="const"><type>BOOST_CONSTEXPR bool</type></method>
<method name="is_neg_infinity" cv="const"><type>BOOST_CONSTEXPR bool</type></method>
<method name="is_nan" cv="const"><type>BOOST_CONSTEXPR bool</type></method>
<method name="is_special" cv="const"><type>BOOST_CONSTEXPR bool</type></method>
<method name="operator==" cv="const"><type>BOOST_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const <classname>int_adapter</classname> &amp;</paramtype></parameter></method>
<method name="operator==" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const int &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const"><type>BOOST_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const <classname>int_adapter</classname> &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const int &amp;</paramtype></parameter></method>
<method name="operator&lt;" cv="const"><type>BOOST_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const <classname>int_adapter</classname> &amp;</paramtype></parameter></method>
<method name="operator&lt;" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const int &amp;</paramtype></parameter></method>
<method name="operator&gt;" cv="const"><type>BOOST_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const <classname>int_adapter</classname> &amp;</paramtype></parameter></method>
<method name="as_number" cv="const"><type>BOOST_CONSTEXPR int_type</type></method>
<method name="as_special" cv="const"><type>BOOST_CONSTEXPR special_values</type><purpose>Returns either special value type or is_not_special. </purpose></method>
<method name="operator+" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>int_adapter</classname></type><template>
          <template-type-parameter name="rhs_type"/>
        </template><parameter name="rhs"><paramtype>const <classname>int_adapter</classname>&lt; rhs_type &gt; &amp;</paramtype></parameter><description><para>Operator allows for adding dissimilar <classname alt="boost::date_time::int_adapter">int_adapter</classname> types. The return type will match that of the the calling object's type </para></description></method>
<method name="operator+" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>int_adapter</classname></type><parameter name="rhs"><paramtype>const int_type</paramtype></parameter></method>
<method name="operator-" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>int_adapter</classname></type><template>
          <template-type-parameter name="rhs_type"/>
        </template><parameter name="rhs"><paramtype>const <classname>int_adapter</classname>&lt; rhs_type &gt; &amp;</paramtype></parameter><description><para>Operator allows for subtracting dissimilar <classname alt="boost::date_time::int_adapter">int_adapter</classname> types. The return type will match that of the the calling object's type </para></description></method>
<method name="operator-" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>int_adapter</classname></type><parameter name="rhs"><paramtype>const int_type</paramtype></parameter></method>
<method name="operator *" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>int_adapter</classname></type><parameter name="rhs"><paramtype>const <classname>int_adapter</classname> &amp;</paramtype></parameter></method>
<method name="operator *" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>int_adapter</classname></type><parameter name="rhs"><paramtype>const int</paramtype></parameter><description><para>Provided for cases when automatic conversion from 'int' to '<classname alt="boost::date_time::int_adapter">int_adapter</classname>' causes incorrect results. </para></description></method>
<method name="operator/" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>int_adapter</classname></type><parameter name="rhs"><paramtype>const <classname>int_adapter</classname> &amp;</paramtype></parameter></method>
<method name="operator/" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>int_adapter</classname></type><parameter name="rhs"><paramtype>const int</paramtype></parameter><description><para>Provided for cases when automatic conversion from 'int' to '<classname alt="boost::date_time::int_adapter">int_adapter</classname>' causes incorrect results. </para></description></method>
<method name="operator%" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>int_adapter</classname></type><parameter name="rhs"><paramtype>const <classname>int_adapter</classname> &amp;</paramtype></parameter></method>
<method name="operator%" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>int_adapter</classname></type><parameter name="rhs"><paramtype>const int</paramtype></parameter><description><para>Provided for cases when automatic conversion from 'int' to '<classname alt="boost::date_time::int_adapter">int_adapter</classname>' causes incorrect results. </para></description></method>
</method-group>
<constructor><parameter name="v"><paramtype>int_type</paramtype></parameter></constructor>
<method-group name="public static functions">
<method name="has_infinity" specifiers="static"><type>BOOST_CONSTEXPR bool</type></method>
<method name="pos_infinity" specifiers="static"><type>BOOST_CONSTEXPR <classname>int_adapter</classname></type></method>
<method name="neg_infinity" specifiers="static"><type>BOOST_CONSTEXPR <classname>int_adapter</classname></type></method>
<method name="not_a_number" specifiers="static"><type>BOOST_CONSTEXPR <classname>int_adapter</classname></type></method>
<method name="BOOST_PREVENT_MACRO_SUBSTITUTION" specifiers="static"><type>BOOST_CONSTEXPR <classname>int_adapter</classname> max</type></method>
<method name="BOOST_PREVENT_MACRO_SUBSTITUTION" specifiers="static"><type>BOOST_CONSTEXPR <classname>int_adapter</classname> min</type></method>
<method name="from_special" specifiers="static"><type>BOOST_CXX14_CONSTEXPR <classname>int_adapter</classname></type><parameter name="sv"><paramtype>special_values</paramtype></parameter></method>
<method name="is_inf" specifiers="static"><type>BOOST_CONSTEXPR bool</type><parameter name="v"><paramtype>int_type</paramtype></parameter></method>
<method name="is_neg_inf" specifiers="static"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="v"><paramtype>int_type</paramtype></parameter></method>
<method name="is_pos_inf" specifiers="static"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="v"><paramtype>int_type</paramtype></parameter></method>
<method name="is_not_a_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="v"><paramtype>int_type</paramtype></parameter></method>
<method name="to_special" specifiers="static"><type>BOOST_CXX14_CONSTEXPR special_values</type><parameter name="v"><paramtype>int_type</paramtype></parameter><purpose>Returns either special value type or is_not_special. </purpose></method>
<method name="maxcount" specifiers="static"><type>BOOST_CONSTEXPR int_type</type></method>
</method-group>
<method-group name="private member functions">
<method name="compare" cv="const"><type>BOOST_CXX14_CONSTEXPR int</type><parameter name="rhs"><paramtype>const <classname>int_adapter</classname> &amp;</paramtype></parameter><purpose>returns -1, 0, 1, or 2 if 'this' is &lt;, ==, &gt;, or 'nan comparison' rhs </purpose></method>
<method name="mult_div_specials" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>int_adapter</classname></type><parameter name="rhs"><paramtype>const <classname>int_adapter</classname> &amp;</paramtype></parameter><purpose>Assumes at least 'this' or 'rhs' is a special value. </purpose></method>
<method name="mult_div_specials" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>int_adapter</classname></type><parameter name="rhs"><paramtype>const int &amp;</paramtype></parameter><purpose>Assumes 'this' is a special value. </purpose></method>
</method-group>
</class>












<function name="operator&lt;&lt;"><type>std::basic_ostream&lt; charT, traits &gt; &amp;</type><template>
          <template-type-parameter name="charT"/>
          <template-type-parameter name="traits"/>
          <template-type-parameter name="int_type"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; charT, traits &gt; &amp;</paramtype></parameter><parameter name="ia"><paramtype>const <classname>int_adapter</classname>&lt; int_type &gt; &amp;</paramtype></parameter><description><para>Expected output is either a numeric representation or a special values representation.<sbr/>
 Ex. "12", "+infinity", "not-a-number", etc. </para></description></function>



















</namespace>
</namespace>
</header>
<header name="boost/date_time/iso_format.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="iso_extended_format"><template>
      <template-type-parameter name="charT"/>
    </template><inherit access="public">boost::date_time::iso_format_base&lt; charT &gt;</inherit><purpose>Extended format uses seperators YYYY-MM-DD. </purpose><method-group name="public static functions">
<method name="has_date_sep_chars" specifiers="static"><type>bool</type><purpose>Extended format needs char separators. </purpose></method>
</method-group>
</class><class name="iso_format"><template>
      <template-type-parameter name="charT"/>
    </template><inherit access="public">boost::date_time::iso_format_base&lt; charT &gt;</inherit><purpose>Format description for ISO 8601 normal YYYYMMDD. </purpose><method-group name="public static functions">
<method name="has_date_sep_chars" specifiers="static"><type>bool</type><purpose>The ios standard format doesn't use char separators. </purpose></method>
</method-group>
</class><class name="iso_format_base"><template>
      <template-type-parameter name="charT"/>
    </template><purpose>Class to provide common ISO 8601 formatting spec. </purpose><method-group name="public static functions">
<method name="month_format" specifiers="static"><type>month_format_spec</type><purpose>Describe month format – its an integer in ISO 8601 format. </purpose></method>
<method name="not_a_date" specifiers="static"><type>const charT *</type><purpose>String used printed is date is invalid. </purpose></method>
<method name="pos_infinity" specifiers="static"><type>const charT *</type><purpose>String used to for positive infinity value. </purpose></method>
<method name="neg_infinity" specifiers="static"><type>const charT *</type><purpose>String used to for positive infinity value. </purpose></method>
<method name="year_sep_char" specifiers="static"><type>charT</type><purpose>ISO 8601 char for a year – used in durations. </purpose></method>
<method name="month_sep_char" specifiers="static"><type>charT</type><purpose>ISO 8601 char for a month. </purpose></method>
<method name="day_sep_char" specifiers="static"><type>charT</type><purpose>ISO 8601 char for a day. </purpose></method>
<method name="hour_sep_char" specifiers="static"><type>charT</type><purpose>char for minute </purpose></method>
<method name="minute_sep_char" specifiers="static"><type>charT</type><purpose>char for minute </purpose></method>
<method name="second_sep_char" specifiers="static"><type>charT</type><purpose>char for second </purpose></method>
<method name="period_start_char" specifiers="static"><type>charT</type><purpose>ISO 8601 char for a period. </purpose></method>
<method name="time_start_char" specifiers="static"><type>charT</type><purpose>Used in time in mixed strings to set start of time. </purpose></method>
<method name="week_start_char" specifiers="static"><type>charT</type><purpose>Used in mixed strings to identify start of a week number. </purpose></method>
<method name="period_sep_char" specifiers="static"><type>charT</type><purpose>Separators for periods. </purpose></method>
<method name="time_sep_char" specifiers="static"><type>charT</type><purpose>Separator for hh:mm:ss. </purpose></method>
<method name="fractional_time_sep_char" specifiers="static"><type>charT</type><purpose>Preferred Separator for hh:mm:ss,decimal_fraction. </purpose></method>
<method name="is_component_sep" specifiers="static"><type>bool</type><parameter name="sep"><paramtype>charT</paramtype></parameter></method>
<method name="is_fractional_time_sep" specifiers="static"><type>bool</type><parameter name="sep"><paramtype>charT</paramtype></parameter></method>
<method name="is_timezone_sep" specifiers="static"><type>bool</type><parameter name="sep"><paramtype>charT</paramtype></parameter></method>
<method name="element_sep_char" specifiers="static"><type>charT</type></method>
</method-group>
</class><class-specialization name="iso_format_base"><template>
    </template><specialization><template-arg>wchar_t</template-arg></specialization><purpose>Class to provide common ISO 8601 formatting spec. </purpose><method-group name="public static functions">
<method name="month_format" specifiers="static"><type>month_format_spec</type><purpose>Describe month format – its an integer in ISO 8601 format. </purpose></method>
<method name="not_a_date" specifiers="static"><type>const wchar_t *</type><purpose>String used printed is date is invalid. </purpose></method>
<method name="pos_infinity" specifiers="static"><type>const wchar_t *</type><purpose>String used to for positive infinity value. </purpose></method>
<method name="neg_infinity" specifiers="static"><type>const wchar_t *</type><purpose>String used to for positive infinity value. </purpose></method>
<method name="year_sep_char" specifiers="static"><type>wchar_t</type><purpose>ISO 8601 char for a year – used in durations. </purpose></method>
<method name="month_sep_char" specifiers="static"><type>wchar_t</type><purpose>ISO 8601 char for a month. </purpose></method>
<method name="day_sep_char" specifiers="static"><type>wchar_t</type><purpose>ISO 8601 char for a day. </purpose></method>
<method name="hour_sep_char" specifiers="static"><type>wchar_t</type><purpose>char for minute </purpose></method>
<method name="minute_sep_char" specifiers="static"><type>wchar_t</type><purpose>char for minute </purpose></method>
<method name="second_sep_char" specifiers="static"><type>wchar_t</type><purpose>char for second </purpose></method>
<method name="period_start_char" specifiers="static"><type>wchar_t</type><purpose>ISO 8601 char for a period. </purpose></method>
<method name="time_start_char" specifiers="static"><type>wchar_t</type><purpose>Used in time in mixed strings to set start of time. </purpose></method>
<method name="week_start_char" specifiers="static"><type>wchar_t</type><purpose>Used in mixed strings to identify start of a week number. </purpose></method>
<method name="period_sep_char" specifiers="static"><type>wchar_t</type><purpose>Separators for periods. </purpose></method>
<method name="time_sep_char" specifiers="static"><type>wchar_t</type><purpose>Separator for hh:mm:ss. </purpose></method>
<method name="fractional_time_sep_char" specifiers="static"><type>wchar_t</type><purpose>Preferred Separator for hh:mm:ss,decimal_fraction. </purpose></method>
<method name="is_component_sep" specifiers="static"><type>bool</type><parameter name="sep"><paramtype>wchar_t</paramtype></parameter></method>
<method name="is_fractional_time_sep" specifiers="static"><type>bool</type><parameter name="sep"><paramtype>wchar_t</paramtype></parameter></method>
<method name="is_timezone_sep" specifiers="static"><type>bool</type><parameter name="sep"><paramtype>wchar_t</paramtype></parameter></method>
<method name="element_sep_char" specifiers="static"><type>wchar_t</type></method>
</method-group>
</class-specialization>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/local_time_adjustor.hpp">
<para>Time adjustment calculations for local times </para><namespace name="boost">
<namespace name="date_time">
<class name="dynamic_local_time_adjustor"><template>
      <template-type-parameter name="time_type"/>
      <template-type-parameter name="dst_rules"/>
    </template><inherit access="public">dst_rules</inherit><purpose>Allow sliding utc adjustment with fixed dst rules. </purpose><typedef name="time_duration_type"><type>time_type::time_duration_type</type></typedef>
<typedef name="date_type"><type>time_type::date_type</type></typedef>
<method-group name="public member functions">
<method name="utc_offset"><type>time_duration_type</type><parameter name="is_dst"><paramtype>bool</paramtype></parameter><purpose>Presumes local time. </purpose></method>
</method-group>
<constructor><parameter name="utc_offset"><paramtype>time_duration_type</paramtype></parameter></constructor>
</class><class name="local_adjustor"><template>
      <template-type-parameter name="time_type"/>
      <template-nontype-parameter name="utc_offset"><type>short</type></template-nontype-parameter>
      <template-type-parameter name="dst_rule"/>
    </template><purpose>Template that simplifies the creation of local time calculator. </purpose><description><para>Use this template to create the timezone to utc convertors as required.</para><para>This class will also work for other regions that don't use dst and have a utc offset which is an integral number of hours.</para><para><emphasis role="bold">Template Parameters</emphasis> -time_type – Time class to use -utc_offset – Number hours local time is adjust from utc -use_dst – true (default) if region uses dst, false otherwise For example: <programlisting language="c++">   //eastern timezone is utc-5
typedef date_time::local_adjustor&lt;ptime, -5, us_dst&gt; us_eastern;
typedef date_time::local_adjustor&lt;ptime, -6, us_dst&gt; us_central;
typedef date_time::local_adjustor&lt;ptime, -7, us_dst&gt; us_mountain;
typedef date_time::local_adjustor&lt;ptime, -8, us_dst&gt; us_pacific;
typedef date_time::local_adjustor&lt;ptime, -7, no_dst&gt; us_arizona;
</programlisting> </para></description><typedef name="time_duration_type"><type>time_type::time_duration_type</type></typedef>
<typedef name="date_type"><type>time_type::date_type</type></typedef>
<typedef name="dst_adjustor"><type><classname>static_local_time_adjustor</classname>&lt; time_type, dst_rule, <classname>utc_adjustment</classname>&lt; time_duration_type, utc_offset &gt; &gt;</type></typedef>
<method-group name="public static functions">
<method name="utc_to_local" specifiers="static"><type>time_type</type><parameter name="t"><paramtype>const time_type &amp;</paramtype></parameter><purpose>Convert a utc time to local time. </purpose></method>
<method name="local_to_utc" specifiers="static"><type>time_type</type><parameter name="t"><paramtype>const time_type &amp;</paramtype></parameter><parameter name="dst"><paramtype>date_time::dst_flags</paramtype><default>date_time::calculate</default></parameter><purpose>Convert a local time to utc. </purpose></method>
</method-group>
</class><class name="static_local_time_adjustor"><template>
      <template-type-parameter name="time_type"/>
      <template-type-parameter name="dst_rules"/>
      <template-type-parameter name="utc_offset_rules"/>
    </template><inherit access="public">dst_rules</inherit><inherit access="public">utc_offset_rules</inherit><purpose>Embed the rules for local time adjustments at compile time. </purpose><typedef name="time_duration_type"><type>time_type::time_duration_type</type></typedef>
<typedef name="date_type"><type>time_type::date_type</type></typedef>
<method-group name="public static functions">
<method name="utc_to_local_offset" specifiers="static"><type>time_duration_type</type><parameter name="t"><paramtype>const time_type &amp;</paramtype><description><para>UTC time to calculate offset to local time This adjustment depends on the following observations about the workings of the DST boundary offset. Since UTC time labels are monotonically increasing we can determine if a given local time is in DST or not and therefore adjust the offset appropriately.</para></description></parameter><purpose>Calculates the offset from a utc time to local based on dst and utc offset. </purpose><description><para>
The logic is as follows. Starting with UTC time use the offset to create a label for an non-dst adjusted local time. Then call dst_rules::local_is_dst with the non adjust local time. The results of this function will either unabiguously decide that the initial local time is in dst or return an illegal or ambiguous result. An illegal result only occurs at the end of dst (where labels are skipped) and indicates that dst has ended. An ambiguous result means that we need to recheck by making a dst adjustment and then rechecking. If the dst offset is added to the utc time and the recheck proves non-ambiguous then we are past the boundary. If it is still ambiguous then we are ahead of the boundary and dst is still in effect.</para><para>TODO – check if all dst offsets are positive. If not then the algorithm needs to check for this and reverse the illegal/ambiguous logic. </para></description></method>
<method name="local_to_utc_offset" specifiers="static"><type>time_duration_type</type><parameter name="t"><paramtype>const time_type &amp;</paramtype></parameter><parameter name="dst"><paramtype>date_time::dst_flags</paramtype><default>date_time::calculate</default></parameter><purpose>Get the offset to UTC given a local time. </purpose></method>
</method-group>
</class><class name="utc_adjustment"><template>
      <template-type-parameter name="time_duration_type"/>
      <template-nontype-parameter name="hours"><type>short</type></template-nontype-parameter>
      <template-nontype-parameter name="minutes"><type>unsigned short</type><default>0</default></template-nontype-parameter>
    </template><purpose>Provides a base offset adjustment from utc. </purpose><method-group name="public static functions">
<method name="local_to_utc_base_offset" specifiers="static"><type>time_duration_type</type></method>
<method name="utc_to_local_base_offset" specifiers="static"><type>time_duration_type</type></method>
</method-group>
</class>











<function name="dummy_to_prevent_msvc6_ice"><type>void</type></function>




















</namespace>
</namespace>
</header>
<header name="boost/date_time/local_timezone_defs.hpp">
<namespace name="boost">
<namespace name="date_time">
<struct name="acst_dst_trait"><template>
      <template-type-parameter name="date_type"/>
    </template><typedef name="day_of_week_type"><type>date_type::day_of_week_type</type></typedef>
<typedef name="month_type"><type>date_type::month_type</type></typedef>
<typedef name="year_type"><type>date_type::year_type</type></typedef>
<typedef name="start_rule_functor"><type><classname>date_time::last_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="end_rule_functor"><type><classname>date_time::last_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<method-group name="public static functions">
<method name="start_day" specifiers="static"><type>day_of_week_type</type><parameter name=""><paramtype>year_type</paramtype></parameter></method>
<method name="start_month" specifiers="static"><type>month_type</type><parameter name=""><paramtype>year_type</paramtype></parameter></method>
<method name="end_day" specifiers="static"><type>day_of_week_type</type><parameter name=""><paramtype>year_type</paramtype></parameter></method>
<method name="end_month" specifiers="static"><type>month_type</type><parameter name=""><paramtype>year_type</paramtype></parameter></method>
<method name="dst_start_offset_minutes" specifiers="static"><type>int</type></method>
<method name="dst_end_offset_minutes" specifiers="static"><type>int</type></method>
<method name="dst_shift_length_minutes" specifiers="static"><type>int</type></method>
<method name="local_dst_start_day" specifiers="static"><type>date_type</type><parameter name="year"><paramtype>year_type</paramtype></parameter></method>
<method name="local_dst_end_day" specifiers="static"><type>date_type</type><parameter name="year"><paramtype>year_type</paramtype></parameter></method>
</method-group>
</struct><struct name="eu_dst_trait"><template>
      <template-type-parameter name="date_type"/>
    </template><purpose>Rules for daylight savings start in the EU (Last Sun in Mar) </purpose><description><para>These amount to the following:<itemizedlist>
<listitem><para>Start of dst day is last Sunday in March</para>
</listitem><listitem><para>End day of dst is last Sunday in Oct</para>
</listitem><listitem><para>Going forward switch time is 2:00 am (offset 120 minutes)</para>
</listitem><listitem><para>Going back switch time is 3:00 am (off set 180 minutes)</para>
</listitem><listitem><para>Shift duration is one hour (60 minutes) </para>
</listitem></itemizedlist>
</para></description><typedef name="day_of_week_type"><type>date_type::day_of_week_type</type></typedef>
<typedef name="month_type"><type>date_type::month_type</type></typedef>
<typedef name="year_type"><type>date_type::year_type</type></typedef>
<typedef name="start_rule_functor"><type><classname>date_time::last_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="end_rule_functor"><type><classname>date_time::last_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<method-group name="public static functions">
<method name="start_day" specifiers="static"><type>day_of_week_type</type><parameter name=""><paramtype>year_type</paramtype></parameter></method>
<method name="start_month" specifiers="static"><type>month_type</type><parameter name=""><paramtype>year_type</paramtype></parameter></method>
<method name="end_day" specifiers="static"><type>day_of_week_type</type><parameter name=""><paramtype>year_type</paramtype></parameter></method>
<method name="end_month" specifiers="static"><type>month_type</type><parameter name=""><paramtype>year_type</paramtype></parameter></method>
<method name="dst_start_offset_minutes" specifiers="static"><type>int</type></method>
<method name="dst_end_offset_minutes" specifiers="static"><type>int</type></method>
<method name="dst_shift_length_minutes" specifiers="static"><type>int</type></method>
<method name="local_dst_start_day" specifiers="static"><type>date_type</type><parameter name="year"><paramtype>year_type</paramtype></parameter></method>
<method name="local_dst_end_day" specifiers="static"><type>date_type</type><parameter name="year"><paramtype>year_type</paramtype></parameter></method>
</method-group>
</struct><struct name="uk_dst_trait"><template>
      <template-type-parameter name="date_type"/>
    </template><inherit access="public">boost::date_time::eu_dst_trait&lt; date_type &gt;</inherit><purpose>Alternative dst traits for some parts of the United Kingdom. </purpose><method-group name="public static functions">
<method name="dst_start_offset_minutes" specifiers="static"><type>int</type></method>
<method name="dst_end_offset_minutes" specifiers="static"><type>int</type></method>
<method name="dst_shift_length_minutes" specifiers="static"><type>int</type></method>
</method-group>
</struct><struct name="us_dst_trait"><template>
      <template-type-parameter name="date_type"/>
    </template><purpose>Specification for daylight savings start rules in US. </purpose><description><para>This class is used to configure <classname alt="boost::date_time::dst_calc_engine">dst_calc_engine</classname> template typically as follows: <programlisting language="c++">using namespace boost::gregorian;
using namespace boost::posix_time;
typedef us_dst_trait&lt;date&gt; us_dst_traits;
typedef boost::date_time::dst_calc_engine&lt;date, time_duration, 
                                          us_dst_traits&gt;  
                                          us_dst_calc;
//calculate the 2002 transition day of USA April 7 2002
date dst_start = us_dst_calc::local_dst_start_day(2002); 

//calculate the 2002 transition day of USA Oct 27 2002
date dst_end = us_dst_calc::local_dst_end_day(2002); 
                                          
//check if a local time is in dst or not -- posible answers
//are yes, no, invalid time label, ambiguous
ptime t(...some time...);  
if (us_dst::local_is_dst(t.date(), t.time_of_day()) 
    == boost::date_time::is_not_in_dst) 
{

}
</programlisting> This generates a type suitable for the calculation of dst transitions for the United States. Of course other templates can be used for other locales. </para></description><typedef name="day_of_week_type"><type>date_type::day_of_week_type</type></typedef>
<typedef name="month_type"><type>date_type::month_type</type></typedef>
<typedef name="year_type"><type>date_type::year_type</type></typedef>
<typedef name="start_rule_functor"><type><classname>date_time::nth_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="end_rule_functor"><type><classname>date_time::first_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="start_rule_functor_pre2007"><type><classname>date_time::first_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<typedef name="end_rule_functor_pre2007"><type><classname>date_time::last_kday_of_month</classname>&lt; date_type &gt;</type></typedef>
<method-group name="public static functions">
<method name="start_day" specifiers="static"><type>day_of_week_type</type><parameter name=""><paramtype>year_type</paramtype></parameter></method>
<method name="start_month" specifiers="static"><type>month_type</type><parameter name="y"><paramtype>year_type</paramtype></parameter></method>
<method name="end_day" specifiers="static"><type>day_of_week_type</type><parameter name=""><paramtype>year_type</paramtype></parameter></method>
<method name="end_month" specifiers="static"><type>month_type</type><parameter name="y"><paramtype>year_type</paramtype></parameter></method>
<method name="local_dst_start_day" specifiers="static"><type>date_type</type><parameter name="year"><paramtype>year_type</paramtype></parameter></method>
<method name="local_dst_end_day" specifiers="static"><type>date_type</type><parameter name="year"><paramtype>year_type</paramtype></parameter></method>
<method name="dst_start_offset_minutes" specifiers="static"><type>int</type></method>
<method name="dst_end_offset_minutes" specifiers="static"><type>int</type></method>
<method name="dst_shift_length_minutes" specifiers="static"><type>int</type></method>
</method-group>
</struct>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/locale_config.hpp">
</header>
<header name="boost/date_time/microsec_time_clock.hpp">
<para>This file contains a high resolution time clock implementation. </para><namespace name="boost">
<namespace name="date_time">
<class name="microsec_clock"><template>
      <template-type-parameter name="time_type"/>
    </template><purpose>A clock providing microsecond level resolution. </purpose><description><para>A high precision clock that measures the local time at a resolution up to microseconds and adjusts to the resolution of the time system. For example, for the a library configuration with nano second resolution, the last 3 places of the fractional seconds will always be 000 since there are 1000 nano-seconds in a micro second. </para></description><typedef name="date_type"><type>time_type::date_type</type></typedef>
<typedef name="time_duration_type"><type>time_type::time_duration_type</type></typedef>
<typedef name="resolution_traits_type"><type>time_duration_type::rep_type</type></typedef>
<method-group name="public static functions">
<method name="local_time" specifiers="static"><type>time_type</type><template>
          <template-type-parameter name="time_zone_type"/>
        </template><parameter name="tz_ptr"><paramtype>shared_ptr&lt; time_zone_type &gt;</paramtype></parameter><purpose>return a local time object for the given zone, based on computer clock </purpose></method>
<method name="local_time" specifiers="static"><type>time_type</type><purpose>Returns the local time based on computer clock settings. </purpose></method>
<method name="universal_time" specifiers="static"><type>time_type</type><purpose>Returns the UTC time based on computer settings. </purpose></method>
</method-group>
<method-group name="private static functions">
<method name="create_time" specifiers="static"><type>time_type</type><parameter name="converter"><paramtype>time_converter</paramtype></parameter></method>
<method name="file_time_to_microseconds" specifiers="static"><type>boost::uint64_t</type><parameter name="ft"><paramtype>boost::winapi::FILETIME_ const &amp;</paramtype></parameter><description><para>The function converts file_time into number of microseconds elapsed since 1970-Jan-01</para><para><note><para>Only dates after 1970-Jan-01 are supported. Dates before will be wrapped. </para>
</note>
</para></description></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/parse_format_base.hpp">
<namespace name="boost">
<namespace name="date_time">
<enum name="month_format_spec"><enumvalue name="month_as_integer"/><enumvalue name="month_as_short_string"/><enumvalue name="month_as_long_string"/><purpose>Enum for distinguishing parsing and formatting options. </purpose></enum>
<enum name="ymd_order_spec"><enumvalue name="ymd_order_iso"/><enumvalue name="ymd_order_dmy"/><enumvalue name="ymd_order_us"/><purpose>Enum for distinguishing the order of Month, Day, &amp; Year. </purpose><description><para>Enum for distinguishing the order in which Month, Day, &amp; Year will appear in a date string </para></description></enum>

































</namespace>
</namespace>
</header>
<header name="boost/date_time/period.hpp">
<para>This file contain the implementation of the period abstraction. This is basically the same idea as a range. Although this class is intended for use in the time library, it is pretty close to general enough for other numeric uses. </para><namespace name="boost">
<namespace name="date_time">
<class name="period"><template>
      <template-type-parameter name="point_rep"/>
      <template-type-parameter name="duration_rep"/>
    </template><inherit access="private">boost::less_than_comparable&lt; period&lt; point_rep, duration_rep &gt;, boost::equality_comparable&lt; period&lt; point_rep, duration_rep &gt; &gt; &gt;</inherit><purpose>Provides generalized period type useful in date-time systems. </purpose><description><para>This template uses a class to represent a time point within the period and another class to represent a duration. As a result, this class is not appropriate for use when the number and duration representation are the same (eg: in the regular number domain).</para><para>A period can be specified by providing either the begining point and a duration or the begining point and the end point( end is NOT part of the period but 1 unit past it. A period will be "invalid" if either end_point &lt;= begin_point or the given duration is &lt;= 0. Any valid period will return false for is_null().</para><para>Zero length periods are also considered invalid. Zero length periods are periods where the begining and end points are the same, or, the given duration is zero. For a zero length period, the last point will be one unit less than the begining point.</para><para>In the case that the begin and last are the same, the period has a length of one unit.</para><para>The best way to handle periods is usually to provide a begining point and a duration. So, day1 + 7 days is a week period which includes all of the first day and 6 more days (eg: Sun to Sat). </para></description><typedef name="point_type"><type>point_rep</type></typedef>
<typedef name="duration_type"><type>duration_rep</type></typedef>
<method-group name="public member functions">
<method name="begin" cv="const"><type>BOOST_CXX14_CONSTEXPR point_rep</type><purpose>Return the first element in the period. </purpose></method>
<method name="end" cv="const"><type>BOOST_CXX14_CONSTEXPR point_rep</type><purpose>Return one past the last element. </purpose></method>
<method name="last" cv="const"><type>BOOST_CXX14_CONSTEXPR point_rep</type><purpose>Return the last item in the period. </purpose></method>
<method name="length" cv="const"><type>BOOST_CXX14_CONSTEXPR duration_rep</type><purpose>Return the length of the period. </purpose></method>
<method name="is_null" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>True if period is ill formed (length is zero or less) </purpose></method>
<method name="operator==" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const <classname>period</classname> &amp;</paramtype></parameter><purpose>Equality operator. </purpose></method>
<method name="operator&lt;" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const <classname>period</classname> &amp;</paramtype></parameter><purpose>Strict as defined by rhs.last &lt;= lhs.last. </purpose></method>
<method name="shift"><type>BOOST_CXX14_CONSTEXPR void</type><parameter name="d"><paramtype>const duration_rep &amp;</paramtype></parameter><purpose>Shift the start and end by the specified amount. </purpose></method>
<method name="expand"><type>BOOST_CXX14_CONSTEXPR void</type><parameter name="d"><paramtype>const duration_rep &amp;</paramtype></parameter><description><para>Expands the size of the period by the duration on both ends.</para><para>So before expand <programlisting language="c++">        [-------]
^   ^   ^   ^   ^   ^  ^
1   2   3   4   5   6  7
</programlisting> After expand(2) <programlisting language="c++">[----------------------]
^   ^   ^   ^   ^   ^  ^
1   2   3   4   5   6  7
</programlisting> </para></description></method>
<method name="contains" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="point"><paramtype>const point_rep &amp;</paramtype></parameter><purpose>True if the point is inside the period, zero length periods contain no points. </purpose></method>
<method name="contains" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="other"><paramtype>const <classname>period</classname> &amp;</paramtype></parameter><purpose>True if this period fully contains (or equals) the other period. </purpose></method>
<method name="intersects" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="other"><paramtype>const <classname>period</classname> &amp;</paramtype></parameter><purpose>True if the periods overlap in any way. </purpose></method>
<method name="is_adjacent" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="other"><paramtype>const <classname>period</classname> &amp;</paramtype></parameter><purpose>True if periods are next to each other without a gap. </purpose></method>
<method name="is_before" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="point"><paramtype>const point_rep &amp;</paramtype></parameter><purpose>True if all of the period is prior to the passed point or end &lt;= t. </purpose></method>
<method name="is_after" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="point"><paramtype>const point_rep &amp;</paramtype></parameter><purpose>True if all of the period is prior or t &lt; start. </purpose></method>
<method name="intersection" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>period</classname></type><parameter name="other"><paramtype>const <classname>period</classname> &amp;</paramtype></parameter><purpose>Returns the period of intersection or invalid range no intersection. </purpose></method>
<method name="merge" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>period</classname></type><parameter name="other"><paramtype>const <classname>period</classname> &amp;</paramtype></parameter><purpose>Returns the union of intersecting periods – or null period. </purpose></method>
<method name="span" cv="const"><type>BOOST_CXX14_CONSTEXPR <classname>period</classname></type><parameter name="other"><paramtype>const <classname>period</classname> &amp;</paramtype></parameter><purpose>Combine two periods with earliest start and latest end. </purpose><description><para>Combines two periods and any gap between them such that start = min(p1.start, p2.start) end = max(p1.end , p2.end) <programlisting language="c++">       [---p1---)
                      [---p2---)
result:
       [-----------p3----------) 
</programlisting> </para></description></method>
</method-group>
<constructor><parameter name="first_point"><paramtype>point_rep</paramtype></parameter><parameter name="end_point"><paramtype>point_rep</paramtype></parameter><purpose>create a period from begin to last eg: [begin,end) </purpose><description><para>If end &lt;= begin then the period will be invalid </para></description></constructor>
<constructor><parameter name="first_point"><paramtype>point_rep</paramtype></parameter><parameter name="len"><paramtype>duration_rep</paramtype></parameter><purpose>create a period as [begin, begin+len) </purpose><description><para>If len is &lt;= 0 then the period will be invalid </para></description></constructor>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/period_formatter.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="period_formatter"><template>
      <template-type-parameter name="CharT"/>
      <template-type-parameter name="OutItrT"><default>std::ostreambuf_iterator&lt;CharT, std::char_traits&lt;CharT&gt; &gt;</default></template-type-parameter>
    </template><purpose>Not a facet, but a class used to specify and control period formats. </purpose><description><para>Provides settings for the following:<itemizedlist>
<listitem><para>period_separator – default '/'</para>
</listitem><listitem><para>period_open_start_delimeter – default '['</para>
</listitem><listitem><para>period_open_range_end_delimeter – default ')'</para>
</listitem><listitem><para>period_closed_range_end_delimeter – default ']'</para>
</listitem><listitem><para>display_as_open_range, display_as_closed_range – default closed_range</para>
</listitem></itemizedlist>
</para><para>Thus the default formatting for a period is as follows: <programlisting language="c++">[period.start()/period.last()]
</programlisting> So for a typical date_period this would be <programlisting language="c++">[2004-Jan-04/2004-Feb-01]
</programlisting> where the date formatting is controlled by the date facet </para></description><enum name="range_display_options"><enumvalue name="AS_OPEN_RANGE"/><enumvalue name="AS_CLOSED_RANGE"/></enum>
<typedef name="string_type"><type>std::basic_string&lt; CharT &gt;</type></typedef>
<typedef name="char_type"><type>CharT</type></typedef>
<typedef name="const_itr_type"><type>std::basic_string&lt; char_type &gt;::const_iterator</type></typedef>
<typedef name="collection_type"><type>std::vector&lt; std::basic_string&lt; CharT &gt; &gt;</type></typedef>
<data-member name="default_period_separator" specifiers="static"><type>const char_type</type></data-member>
<data-member name="default_period_start_delimeter" specifiers="static"><type>const char_type</type></data-member>
<data-member name="default_period_open_range_end_delimeter" specifiers="static"><type>const char_type</type></data-member>
<data-member name="default_period_closed_range_end_delimeter" specifiers="static"><type>const char_type</type></data-member>
<method-group name="public member functions">
<method name="put_period_separator" cv="const"><type>OutItrT</type><parameter name="oitr"><paramtype>OutItrT &amp;</paramtype></parameter><purpose>Puts the characters between period elements into stream – default is /. </purpose></method>
<method name="put_period_start_delimeter" cv="const"><type>OutItrT</type><parameter name="oitr"><paramtype>OutItrT &amp;</paramtype></parameter><purpose>Puts the period start characters into stream – default is [. </purpose></method>
<method name="put_period_end_delimeter" cv="const"><type>OutItrT</type><parameter name="oitr"><paramtype>OutItrT &amp;</paramtype></parameter><purpose>Puts the period end characters into stream as controled by open/closed range setting. </purpose></method>
<method name="range_option" cv="const"><type>range_display_options</type></method>
<method name="range_option" cv="const"><type>void</type><parameter name="option"><paramtype>range_display_options</paramtype></parameter><purpose>Reset the range_option control. </purpose></method>
<method name="delimiter_strings"><type>void</type><parameter name="separator"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="start_delim"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="open_end_delim"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="closed_end_delim"><paramtype>const string_type &amp;</paramtype></parameter><purpose>Change the delimiter strings. </purpose></method>
<method name="put_period" cv="const"><type>OutItrT</type><template>
          <template-type-parameter name="period_type"/>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="a_fill"><paramtype>char_type</paramtype></parameter><parameter name="p"><paramtype>const period_type &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>Generic code to output a period – no matter the period type. </purpose><description><para>This generic code will output any period using a facet to to output the 'elements'. For example, in the case of a date_period the elements will be instances of a date which will be formatted according the to setup in the passed facet parameter.</para><para>The steps for formatting a period are always the same:<itemizedlist>
<listitem><para>put the start delimiter</para>
</listitem><listitem><para>put start element</para>
</listitem><listitem><para>put the separator</para>
</listitem><listitem><para>put either last or end element depending on range settings</para>
</listitem><listitem><para>put end delimeter depending on range settings</para>
</listitem></itemizedlist>
</para><para>Thus for a typical date period the result might look like this: <programlisting language="c++">[March 01, 2004/June 07, 2004]   &lt;-- closed range
[March 01, 2004/June 08, 2004)   &lt;-- open range
</programlisting> </para></description></method>
</method-group>
<constructor><parameter name="range_option_in"><paramtype>range_display_options</paramtype><default>AS_CLOSED_RANGE</default></parameter><parameter name="period_separator"><paramtype>const char_type *const</paramtype><default>default_period_separator</default></parameter><parameter name="period_start_delimeter"><paramtype>const char_type *const</paramtype><default>default_period_start_delimeter</default></parameter><parameter name="period_open_range_end_delimeter"><paramtype>const char_type *const</paramtype><default>default_period_open_range_end_delimeter</default></parameter><parameter name="period_closed_range_end_delimeter"><paramtype>const char_type *const</paramtype><default>default_period_closed_range_end_delimeter</default></parameter><purpose>Constructor that sets up period formatter options – default should suffice most cases. </purpose></constructor>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/period_parser.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="period_parser"><template>
      <template-type-parameter name="date_type"/>
      <template-type-parameter name="CharT"/>
    </template><purpose>Not a facet, but a class used to specify and control period parsing. </purpose><description><para>Provides settings for the following:<itemizedlist>
<listitem><para>period_separator – default '/'</para>
</listitem><listitem><para>period_open_start_delimeter – default '['</para>
</listitem><listitem><para>period_open_range_end_delimeter – default ')'</para>
</listitem><listitem><para>period_closed_range_end_delimeter – default ']'</para>
</listitem><listitem><para>display_as_open_range, display_as_closed_range – default closed_range</para>
</listitem></itemizedlist>
</para><para>For a typical date_period, the contents of the input stream would be <programlisting language="c++">[2004-Jan-04/2004-Feb-01]
</programlisting> where the date format is controlled by the date facet </para></description><enum name="period_range_option"><enumvalue name="AS_OPEN_RANGE"/><enumvalue name="AS_CLOSED_RANGE"/></enum>
<typedef name="string_type"><type>std::basic_string&lt; CharT &gt;</type></typedef>
<typedef name="char_type"><type>CharT</type></typedef>
<typedef name="stream_itr_type"><type>std::istreambuf_iterator&lt; CharT &gt;</type></typedef>
<typedef name="parse_tree_type"><type><classname>string_parse_tree</classname>&lt; CharT &gt;</type></typedef>
<typedef name="match_results"><type><classname>parse_tree_type::parse_match_result_type</classname></type></typedef>
<typedef name="collection_type"><type>std::vector&lt; std::basic_string&lt; CharT &gt; &gt;</type></typedef>
<data-member name="default_period_separator" specifiers="static"><type>const char_type</type></data-member>
<data-member name="default_period_start_delimeter" specifiers="static"><type>const char_type</type></data-member>
<data-member name="default_period_open_range_end_delimeter" specifiers="static"><type>const char_type</type></data-member>
<data-member name="default_period_closed_range_end_delimeter" specifiers="static"><type>const char_type</type></data-member>
<method-group name="public member functions">
<method name="range_option" cv="const"><type>period_range_option</type></method>
<method name="range_option"><type>void</type><parameter name="option"><paramtype>period_range_option</paramtype></parameter></method>
<method name="delimiter_strings" cv="const"><type>collection_type</type></method>
<method name="delimiter_strings"><type>void</type><parameter name="separator"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="start_delim"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="open_end_delim"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="closed_end_delim"><paramtype>const string_type &amp;</paramtype></parameter></method>
<method name="get_period" cv="const"><type>period_type</type><template>
          <template-type-parameter name="period_type"/>
          <template-type-parameter name="duration_type"/>
          <template-type-parameter name="facet_type"/>
        </template><parameter name="sitr"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="a_ios"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name=""><paramtype>const period_type &amp;</paramtype></parameter><parameter name="dur_unit"><paramtype>const duration_type &amp;</paramtype></parameter><parameter name="facet"><paramtype>const facet_type &amp;</paramtype></parameter><purpose>Generic code to parse a period – no matter the period type. </purpose><description><para>This generic code will parse any period using a facet to to get the 'elements'. For example, in the case of a date_period the elements will be instances of a date which will be parsed according the to setup in the passed facet parameter.</para><para>The steps for parsing a period are always the same:<itemizedlist>
<listitem><para>consume the start delimiter</para>
</listitem><listitem><para>get start element</para>
</listitem><listitem><para>consume the separator</para>
</listitem><listitem><para>get either last or end element depending on range settings</para>
</listitem><listitem><para>consume the end delimeter depending on range settings</para>
</listitem></itemizedlist>
</para><para>Thus for a typical date period the contents of the input stream might look like this: <programlisting language="c++">[March 01, 2004/June 07, 2004]   &lt;-- closed range
[March 01, 2004/June 08, 2004)   &lt;-- open range
</programlisting> </para></description></method>
</method-group>
<constructor><parameter name="range_opt"><paramtype>period_range_option</paramtype><default>AS_CLOSED_RANGE</default></parameter><parameter name="period_separator"><paramtype>const char_type *const</paramtype><default>default_period_separator</default></parameter><parameter name="period_start_delimeter"><paramtype>const char_type *const</paramtype><default>default_period_start_delimeter</default></parameter><parameter name="period_open_range_end_delimeter"><paramtype>const char_type *const</paramtype><default>default_period_open_range_end_delimeter</default></parameter><parameter name="period_closed_range_end_delimeter"><paramtype>const char_type *const</paramtype><default>default_period_closed_range_end_delimeter</default></parameter><purpose>Constructor that sets up period parser options. </purpose></constructor>
<method-group name="private member functions">
<method name="consume_delim" cv="const"><type>void</type><parameter name="sitr"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>stream_itr_type &amp;</paramtype></parameter><parameter name="delim"><paramtype>const string_type &amp;</paramtype></parameter><purpose>throws ios_base::failure if delimiter and parsed data do not match </purpose></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/special_defs.hpp">
<namespace name="boost">
<namespace name="date_time">
<enum name="special_values"><enumvalue name="not_a_date_time"/><enumvalue name="neg_infin"/><enumvalue name="pos_infin"/><enumvalue name="min_date_time"/><enumvalue name="max_date_time"/><enumvalue name="not_special"/><enumvalue name="NumSpecialValues"/></enum>

































</namespace>
</namespace>
</header>
<header name="boost/date_time/special_values_formatter.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="special_values_formatter"><template>
      <template-type-parameter name="CharT"/>
      <template-type-parameter name="OutItrT"><default>std::ostreambuf_iterator&lt;CharT, std::char_traits&lt;CharT&gt; &gt;</default></template-type-parameter>
    </template><purpose>Class that provides generic formmatting ostream formatting for special values. </purpose><description><para>This class provides for the formmating of special values to an output stream. In particular, it produces strings for the values of negative and positive infinity as well as not_a_date_time.</para><para>While not a facet, this class is used by the date and time facets for formatting special value types. </para></description><typedef name="string_type"><type>std::basic_string&lt; CharT &gt;</type></typedef>
<typedef name="char_type"><type>CharT</type></typedef>
<typedef name="collection_type"><type>std::vector&lt; string_type &gt;</type></typedef>
<data-member name="default_special_value_names" specifiers="static"><type>const char_type</type><purpose>Storage for the strings used to indicate special values. </purpose></data-member>
<method-group name="public member functions">
<method name="put_special" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="value"><paramtype>const boost::date_time::special_values &amp;</paramtype></parameter></method>
</method-group>
<constructor><purpose>Construct special values formatter using default strings. </purpose><description><para>Default strings are not-a-date-time -infinity +infinity </para></description></constructor>
<constructor><parameter name="begin"><paramtype>const char_type *const *</paramtype></parameter><parameter name="end"><paramtype>const char_type *const *</paramtype></parameter><purpose>Construct special values formatter from array of strings. </purpose><description><para>This constructor will take pair of iterators from an array of strings that represent the special values and copy them for use in formatting special values. <programlisting language="c++">const char* const special_value_names[]={"nadt","-inf","+inf" };

special_value_formatter svf(&amp;special_value_names[0], &amp;special_value_names[3]);
</programlisting> </para></description></constructor>
<constructor><parameter name="beg"><paramtype>typename collection_type::iterator</paramtype></parameter><parameter name="end"><paramtype>typename collection_type::iterator</paramtype></parameter></constructor>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/special_values_parser.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="special_values_parser"><template>
      <template-type-parameter name="date_type"/>
      <template-type-parameter name="charT"/>
    </template><purpose>Class for special_value parsing. </purpose><description><para>TODO: add doc-comments for which elements can be changed Parses input stream for strings representing special_values. Special values parsed are:<itemizedlist>
<listitem><para>not_a_date_time</para>
</listitem><listitem><para>neg_infin</para>
</listitem><listitem><para>pod_infin</para>
</listitem><listitem><para>min_date_time</para>
</listitem><listitem><para>max_date_time </para>
</listitem></itemizedlist>
</para></description><typedef name="string_type"><type>std::basic_string&lt; charT &gt;</type></typedef>
<typedef name="stringstream_type"><type>std::basic_stringstream&lt; charT &gt;</type></typedef>
<typedef name="stream_itr_type"><type>std::istreambuf_iterator&lt; charT &gt;</type></typedef>
<typedef name="duration_type"><type>date_type::duration_type</type></typedef>
<typedef name="parse_tree_type"><type><classname>string_parse_tree</classname>&lt; charT &gt;</type></typedef>
<typedef name="match_results"><type><classname>parse_tree_type::parse_match_result_type</classname></type></typedef>
<typedef name="collection_type"><type>std::vector&lt; std::basic_string&lt; charT &gt; &gt;</type></typedef>
<typedef name="char_type"><type>charT</type></typedef>
<data-member name="nadt_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="neg_inf_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="pos_inf_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="min_date_time_string" specifiers="static"><type>const char_type</type></data-member>
<data-member name="max_date_time_string" specifiers="static"><type>const char_type</type></data-member>
<method-group name="public member functions">
<method name="sv_strings"><type>void</type><parameter name="nadt_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="neg_inf_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="pos_inf_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="min_dt_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="max_dt_str"><paramtype>const string_type &amp;</paramtype></parameter><purpose>Replace special value strings. </purpose></method>
<method name="match" cv="const"><type>bool</type><parameter name="sitr"><paramtype>stream_itr_type &amp;</paramtype><description><para>the start iterator </para></description></parameter><parameter name="str_end"><paramtype>stream_itr_type &amp;</paramtype><description><para>the end iterator </para></description></parameter><parameter name="mr"><paramtype><classname>match_results</classname> &amp;</paramtype><description><para>the match result: mr.current_match is set to the corresponding special_value or -1 </para></description></parameter><description><para>Given an input iterator, attempt to match it to a known special value 

</para></description><returns><para>whether something matched </para>
</returns></method>
</method-group>
<constructor><purpose>Creates a <classname alt="boost::date_time::special_values_parser">special_values_parser</classname> with the default set of "sv_strings". </purpose></constructor>
<constructor><parameter name="nadt_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="neg_inf_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="pos_inf_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="min_dt_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="max_dt_str"><paramtype>const string_type &amp;</paramtype></parameter><purpose>Creates a <classname alt="boost::date_time::special_values_parser">special_values_parser</classname> using a user defined set of element strings. </purpose></constructor>
<constructor><parameter name="beg"><paramtype>typename collection_type::iterator</paramtype></parameter><parameter name="end"><paramtype>typename collection_type::iterator</paramtype></parameter></constructor>
<method-group name="public static functions">
<method name="should_call_match" specifiers="static"><type>bool</type><parameter name="str"><paramtype>const string_type &amp;</paramtype><description><para>the string to check </para></description></parameter><description><para>The parser is expensive to create, and not thread-safe so it cannot be static therefore given a string, determine if it is likely to be a special value. A negative response is a definite no, whereas a positive is only likely and match() should be called and return value checked. 

</para></description><returns><para>false if it is definitely not a special value </para>
</returns></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/string_convert.hpp">
<namespace name="boost">
<namespace name="date_time">











<function name="convert_string_type"><type>std::basic_string&lt; OutputT &gt;</type><template>
          <template-type-parameter name="InputT"/>
          <template-type-parameter name="OutputT"/>
        </template><parameter name="inp_str"><paramtype>const std::basic_string&lt; InputT &gt; &amp;</paramtype></parameter><purpose>Converts a string from one value_type to another. </purpose><description><para>Converts a wstring to a string (or a string to wstring). If both template parameters are of same type, a copy of the input string is returned. </para></description></function>





















</namespace>
</namespace>
</header>
<header name="boost/date_time/string_parse_tree.hpp">
<namespace name="boost">
<namespace name="date_time">
<struct name="parse_match_result"><template>
      <template-type-parameter name="charT"/>
    </template><enum name="PARSE_STATE"><enumvalue name="PARSE_ERROR"><default>= -1</default></enumvalue></enum>
<typedef name="string_type"><type>std::basic_string&lt; charT &gt;</type></typedef>
<data-member name="cache"><type>string_type</type></data-member>
<data-member name="match_depth"><type>unsigned short</type></data-member>
<data-member name="current_match"><type>short</type></data-member>
<method-group name="public member functions">
<method name="remaining" cv="const"><type>string_type</type></method>
<method name="last_char" cv="const"><type>charT</type></method>
<method name="has_remaining" cv="const"><type>bool</type><purpose>Returns true if more characters were parsed than was necessary. </purpose><description><para>Should be used in conjunction with last_char() to get the remaining character. </para></description></method>
</method-group>
<constructor/>
</struct><struct name="string_parse_tree"><template>
      <template-type-parameter name="charT"/>
    </template><purpose>Recursive data structure to allow efficient parsing of various strings. </purpose><description><para>This class provides a quick lookup by building what amounts to a tree data structure. It also features a match function which can can handle nasty input interators by caching values as it recurses the tree so that it can backtrack as needed. </para></description><typedef name="ptree_coll"><type>std::multimap&lt; charT, <classname>string_parse_tree</classname>&lt; charT &gt; &gt;</type></typedef>
<typedef name="ptree_coll"><type>std::multimap&lt; charT, <classname>string_parse_tree</classname> &gt;</type></typedef>
<typedef name="value_type"><type>ptree_coll::value_type</type></typedef>
<typedef name="iterator"><type>ptree_coll::iterator</type></typedef>
<typedef name="const_iterator"><type>ptree_coll::const_iterator</type></typedef>
<typedef name="string_type"><type>std::basic_string&lt; charT &gt;</type></typedef>
<typedef name="collection_type"><type>std::vector&lt; std::basic_string&lt; charT &gt; &gt;</type></typedef>
<typedef name="parse_match_result_type"><type><classname>parse_match_result</classname>&lt; charT &gt;</type></typedef>
<data-member name="m_next_chars"><type>ptree_coll</type></data-member>
<data-member name="m_value"><type>short</type></data-member>
<method-group name="public member functions">
<method name="insert"><type>void</type><parameter name="s"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="value"><paramtype>unsigned short</paramtype></parameter></method>
<method name="match" cv="const"><type>short</type><parameter name="sitr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="result"><paramtype><classname>parse_match_result_type</classname> &amp;</paramtype></parameter><parameter name="level"><paramtype>unsigned int &amp;</paramtype></parameter><purpose>Recursive function that finds a matching string in the tree. </purpose><description><para>Must check match_results::has_remaining() after match() is called. This is required so the user can determine if stream iterator is already pointing to the expected character or not (match() might advance sitr to next char in stream).</para><para>A <classname alt="boost::date_time::parse_match_result">parse_match_result</classname> that has been returned from a failed match attempt can be sent in to the match function of a different <classname alt="boost::date_time::string_parse_tree">string_parse_tree</classname> to attempt a match there. Use the iterators for the partially consumed stream, the <classname alt="boost::date_time::parse_match_result">parse_match_result</classname> object, and '0' for the level parameter. </para></description></method>
<method name="match" cv="const"><type><classname>parse_match_result_type</classname></type><parameter name="sitr"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>std::istreambuf_iterator&lt; charT &gt; &amp;</paramtype></parameter><description><para>Must check match_results::has_remaining() after match() is called. This is required so the user can determine if stream iterator is already pointing to the expected character or not (match() might advance sitr to next char in stream). </para></description></method>
<method name="printme"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype></parameter><parameter name="level"><paramtype>int &amp;</paramtype></parameter></method>
<method name="print"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype></parameter></method>
<method name="printmatch"><type>void</type><parameter name="os"><paramtype>std::ostream &amp;</paramtype></parameter><parameter name="c"><paramtype>charT</paramtype></parameter></method>
</method-group>
<constructor><parameter name="names"><paramtype>collection_type</paramtype></parameter><parameter name="starting_point"><paramtype>unsigned int</paramtype><default>0</default></parameter><description><para>Parameter "starting_point" designates where the numbering begins. A starting_point of zero will start the numbering at zero (Sun=0, Mon=1, ...) were a starting_point of one starts the numbering at one (Jan=1, Feb=2, ...). The default is zero, negative vaules are not allowed </para></description></constructor>
<constructor><parameter name="value"><paramtype>short</paramtype><default>parse_match_result_type::PARSE_ERROR</default></parameter></constructor>
</struct>









<function name="operator&lt;&lt;"><type>std::basic_ostream&lt; charT &gt; &amp;</type><template>
          <template-type-parameter name="charT"/>
        </template><parameter name="os"><paramtype>std::basic_ostream&lt; charT &gt; &amp;</paramtype></parameter><parameter name="mr"><paramtype><classname>parse_match_result</classname>&lt; charT &gt; &amp;</paramtype></parameter></function>






















</namespace>
</namespace>
</header>
<header name="boost/date_time/strings_from_facet.hpp">
<namespace name="boost">
<namespace name="date_time">








<function name="gather_month_strings"><type>std::vector&lt; std::basic_string&lt; charT &gt; &gt;</type><template>
          <template-type-parameter name="charT"/>
        </template><parameter name="locale"><paramtype>const std::locale &amp;</paramtype><description><para>The locale to use when gathering the strings </para></description></parameter><parameter name="short_strings"><paramtype>bool</paramtype><default>true</default><description><para>True(default) to gather short strings, false for long strings. </para></description></parameter><purpose>This function gathers up all the month strings from a std::locale. </purpose><description><para>Using the time_put facet, this function creates a collection of all the month strings from a locale. This is handy when building custom date parsers or formatters that need to be localized.</para><para>

</para></description><returns><para>A vector of strings containing the strings in order. eg: Jan, Feb, Mar, etc. </para>
</returns></function>
<function name="gather_weekday_strings"><type>std::vector&lt; std::basic_string&lt; charT &gt; &gt;</type><template>
          <template-type-parameter name="charT"/>
        </template><parameter name="locale"><paramtype>const std::locale &amp;</paramtype><description><para>The locale to use when gathering the strings </para></description></parameter><parameter name="short_strings"><paramtype>bool</paramtype><default>true</default><description><para>True(default) to gather short strings, false for long strings. </para></description></parameter><purpose>This function gathers up all the weekday strings from a std::locale. </purpose><description><para>Using the time_put facet, this function creates a collection of all the weekday strings from a locale starting with the string for 'Sunday'. This is handy when building custom date parsers or formatters that need to be localized.</para><para>

</para></description><returns><para>A vector of strings containing the weekdays in order. eg: Sun, Mon, Tue, Wed, Thu, Fri, Sat </para>
</returns></function>























</namespace>
</namespace>
</header>
<header name="boost/date_time/time.hpp">
<para>This file contains the interface for the time associated classes. </para><namespace name="boost">
<namespace name="date_time">
<class name="base_time"><template>
      <template-type-parameter name="T"/>
      <template-type-parameter name="time_system"/>
    </template><inherit access="private">boost::less_than_comparable&lt; T, boost::equality_comparable&lt; T &gt; &gt;</inherit><purpose>Representation of a precise moment in time, including the date. </purpose><description><para>This class is a skeleton for the interface of a temporal type with a resolution that is higher than a day. It is intended that this class be the base class and that the actual time class be derived using the BN pattern. In this way, the derived class can make decisions such as 'should there be a default constructor' and what should it set its value to, should there be optional constructors say allowing only an time_durations that generate a time from a clock,etc. So, in fact multiple time types can be created for a time_system with different construction policies, and all of them can perform basic operations by only writing a copy constructor. Finally, compiler errors are also shorter.</para><para>The real behavior of the time class is provided by the time_system template parameter. This class must provide all the logic for addition, subtraction, as well as define all the interface types. </para></description><typedef name="_is_boost_date_time_time_point"><type>void</type></typedef>
<typedef name="time_type"><type>T</type></typedef>
<typedef name="time_rep_type"><type>time_system::time_rep_type</type></typedef>
<typedef name="date_type"><type>time_system::date_type</type></typedef>
<typedef name="date_duration_type"><type>time_system::date_duration_type</type></typedef>
<typedef name="time_duration_type"><type>time_system::time_duration_type</type></typedef>
<method-group name="public member functions">
<method name="date" cv="const"><type>BOOST_CXX14_CONSTEXPR date_type</type></method>
<method name="time_of_day" cv="const"><type>BOOST_CXX14_CONSTEXPR time_duration_type</type></method>
<method name="zone_name" cv="const"><type>std::string</type><parameter name=""><paramtype>bool</paramtype><default>false</default></parameter><description><para>Optional bool parameter will return time zone as an offset (ie "+07:00"). Empty string is returned for classes that do not use a time_zone </para></description></method>
<method name="zone_abbrev" cv="const"><type>std::string</type><parameter name=""><paramtype>bool</paramtype><default>false</default></parameter><description><para>Optional bool parameter will return time zone as an offset (ie "+07:00"). Empty string is returned for classes that do not use a time_zone </para></description></method>
<method name="zone_as_posix_string" cv="const"><type>std::string</type><purpose>An empty string is returned for classes that do not use a time_zone. </purpose></method>
<method name="is_not_a_date_time" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>check to see if date is not a value </purpose></method>
<method name="is_infinity" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>check to see if date is one of the infinity values </purpose></method>
<method name="is_pos_infinity" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>check to see if date is greater than all possible dates </purpose></method>
<method name="is_neg_infinity" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>check to see if date is greater than all possible dates </purpose></method>
<method name="is_special" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>check to see if time is a special value </purpose></method>
<method name="operator==" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const time_type &amp;</paramtype></parameter><purpose>Equality operator – others generated by boost::equality_comparable. </purpose></method>
<method name="operator&lt;" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const time_type &amp;</paramtype></parameter><purpose>Equality operator – others generated by boost::less_than_comparable. </purpose></method>
<method name="operator-" cv="const"><type>BOOST_CXX14_CONSTEXPR time_duration_type</type><parameter name="rhs"><paramtype>const time_type &amp;</paramtype></parameter><purpose>difference between two times </purpose></method>
<method name="operator+" cv="const"><type>BOOST_CXX14_CONSTEXPR time_type</type><parameter name="dd"><paramtype>const date_duration_type &amp;</paramtype></parameter><purpose>add date durations </purpose></method>
<method name="operator+="><type>BOOST_CXX14_CONSTEXPR time_type</type><parameter name="dd"><paramtype>const date_duration_type &amp;</paramtype></parameter></method>
<method name="operator-" cv="const"><type>BOOST_CXX14_CONSTEXPR time_type</type><parameter name="dd"><paramtype>const date_duration_type &amp;</paramtype></parameter><purpose>subtract date durations </purpose></method>
<method name="operator-="><type>BOOST_CXX14_CONSTEXPR time_type</type><parameter name="dd"><paramtype>const date_duration_type &amp;</paramtype></parameter></method>
<method name="operator+" cv="const"><type>BOOST_CXX14_CONSTEXPR time_type</type><parameter name="td"><paramtype>const time_duration_type &amp;</paramtype></parameter><purpose>add time durations </purpose></method>
<method name="operator+="><type>BOOST_CXX14_CONSTEXPR time_type</type><parameter name="td"><paramtype>const time_duration_type &amp;</paramtype></parameter></method>
<method name="operator-" cv="const"><type>BOOST_CXX14_CONSTEXPR time_type</type><parameter name="rhs"><paramtype>const time_duration_type &amp;</paramtype></parameter><purpose>subtract time durations </purpose></method>
<method name="operator-="><type>BOOST_CXX14_CONSTEXPR time_type</type><parameter name="td"><paramtype>const time_duration_type &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="day"><paramtype>const date_type &amp;</paramtype></parameter><parameter name="td"><paramtype>const time_duration_type &amp;</paramtype></parameter><parameter name="dst"><paramtype>dst_flags</paramtype><default>not_dst</default></parameter></constructor>
<constructor><parameter name="sv"><paramtype>special_values</paramtype></parameter></constructor>
<constructor><parameter name="rhs"><paramtype>const time_rep_type &amp;</paramtype></parameter></constructor>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/time_clock.hpp">
<para>This file contains the interface for clock devices. </para><namespace name="boost">
<namespace name="date_time">
<class name="second_clock"><template>
      <template-type-parameter name="time_type"/>
    </template><purpose>A clock providing time level services based on C time_t capabilities. </purpose><description><para>This clock provides resolution to the 1 second level </para></description><typedef name="date_type"><type>time_type::date_type</type></typedef>
<typedef name="time_duration_type"><type>time_type::time_duration_type</type></typedef>
<method-group name="public static functions">
<method name="local_time" specifiers="static"><type>time_type</type></method>
<method name="universal_time" specifiers="static"><type>time_type</type><purpose>Get the current day in universal date as a ymd_type. </purpose></method>
<method name="local_time" specifiers="static"><type>time_type</type><template>
          <template-type-parameter name="time_zone_type"/>
        </template><parameter name="tz_ptr"><paramtype>boost::shared_ptr&lt; time_zone_type &gt;</paramtype></parameter></method>
</method-group>
<method-group name="private static functions">
<method name="create_time" specifiers="static"><type>time_type</type><parameter name="current"><paramtype>::std::tm *</paramtype></parameter></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/time_defs.hpp">
<para>This file contains nice definitions for handling the resoluion of various time reprsentations. </para><namespace name="boost">
<namespace name="date_time">
<enum name="time_resolutions"><enumvalue name="sec"/><enumvalue name="tenth"/><enumvalue name="hundreth"/><enumvalue name="hundredth"><default>= hundreth</default></enumvalue><enumvalue name="milli"/><enumvalue name="ten_thousandth"/><enumvalue name="micro"/><enumvalue name="nano"/><enumvalue name="NumResolutions"/><purpose>Defines some nice types for handling time level resolutions. </purpose></enum>
<enum name="dst_flags"><enumvalue name="not_dst"/><enumvalue name="is_dst"/><enumvalue name="calculate"/><purpose>Flags for daylight savings or summer time. </purpose></enum>

































</namespace>
</namespace>
</header>
<header name="boost/date_time/time_duration.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="subsecond_duration"><template>
      <template-type-parameter name="base_duration"/>
      <template-nontype-parameter name="frac_of_second"><type>boost::int64_t</type></template-nontype-parameter>
    </template><inherit access="public">base_duration</inherit><purpose>Template for instantiating derived adjusting durations. </purpose><typedef name="impl_type"><type>base_duration::impl_type</type></typedef>
<typedef name="traits_type"><type>base_duration::traits_type</type></typedef>
<method-group name="private member functions">
<method name="BOOST_STATIC_ASSERT_MSG"><type/><parameter name=""><paramtype>(traits_type::ticks_per_second &gt;=frac_of_second ? traits_type::ticks_per_second % frac_of_second :frac_of_second % traits_type::ticks_per_second)</paramtype><default>=0</default></parameter><parameter name=""><paramtype>\ "The base duration resolution must be a multiple of the subsecond duration resolution"</paramtype></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>boost::int64_t</paramtype></parameter><parameter name=""><paramtype>adjustment_ratio</paramtype><default>(traits_type::ticks_per_second &gt;=frac_of_second ? traits_type::ticks_per_second/frac_of_second :frac_of_second/traits_type::ticks_per_second)</default></parameter></method>
</method-group>
<method-group name="public member functions">
</method-group>
<constructor specifiers="explicit"><template>
          <template-type-parameter name="T"/>
        </template><parameter name="ss"><paramtype>T const &amp;</paramtype></parameter><parameter name=""><paramtype>typename boost::enable_if&lt; boost::is_integral&lt; T &gt;, void &gt;::type *</paramtype><default>BOOST_DATE_TIME_NULLPTR</default></parameter></constructor>
</class><class name="time_duration"><template>
      <template-type-parameter name="T"><purpose><para>The subclass type </para></purpose></template-type-parameter>
      <template-type-parameter name="rep_type"><purpose><para>The time resolution traits for this duration type. </para></purpose></template-type-parameter>
    </template><inherit access="private">boost::less_than_comparable&lt; T, boost::equality_comparable&lt; T &gt; &gt;</inherit><purpose>Represents some amount of elapsed time measure to a given resolution. </purpose><description><para>This class represents a standard set of capabilities for all counted time durations. Time duration implementations should derive from this class passing their type as the first template parameter. This design allows the subclass duration types to provide custom construction policies or other custom features not provided here.</para><para>
</para></description><typedef name="_is_boost_date_time_duration"><type>void</type></typedef>
<typedef name="duration_type"><type>T</type></typedef>
<typedef name="traits_type"><type>rep_type</type></typedef>
<typedef name="day_type"><type>rep_type::day_type</type></typedef>
<typedef name="hour_type"><type>rep_type::hour_type</type></typedef>
<typedef name="min_type"><type>rep_type::min_type</type></typedef>
<typedef name="sec_type"><type>rep_type::sec_type</type></typedef>
<typedef name="fractional_seconds_type"><type>rep_type::fractional_seconds_type</type></typedef>
<typedef name="tick_type"><type>rep_type::tick_type</type></typedef>
<typedef name="impl_type"><type>rep_type::impl_type</type></typedef>
<method-group name="public member functions">
<method name="hours" cv="const"><type>BOOST_CXX14_CONSTEXPR hour_type</type><purpose>Returns number of hours in the duration. </purpose></method>
<method name="minutes" cv="const"><type>BOOST_CXX14_CONSTEXPR min_type</type><purpose>Returns normalized number of minutes. </purpose></method>
<method name="seconds" cv="const"><type>BOOST_CXX14_CONSTEXPR sec_type</type><purpose>Returns normalized number of seconds (0..60) </purpose></method>
<method name="total_seconds" cv="const"><type>BOOST_CXX14_CONSTEXPR sec_type</type><purpose>Returns total number of seconds truncating any fractional seconds. </purpose></method>
<method name="total_milliseconds" cv="const"><type>BOOST_CXX14_CONSTEXPR tick_type</type><purpose>Returns total number of milliseconds truncating any fractional seconds. </purpose></method>
<method name="total_nanoseconds" cv="const"><type>BOOST_CXX14_CONSTEXPR tick_type</type><purpose>Returns total number of nanoseconds truncating any sub millisecond values. </purpose></method>
<method name="total_microseconds" cv="const"><type>BOOST_CXX14_CONSTEXPR tick_type</type><purpose>Returns total number of microseconds truncating any sub microsecond values. </purpose></method>
<method name="fractional_seconds" cv="const"><type>BOOST_CXX14_CONSTEXPR fractional_seconds_type</type><purpose>Returns count of fractional seconds at given resolution. </purpose></method>
<method name="invert_sign" cv="const"><type>BOOST_CXX14_CONSTEXPR duration_type</type></method>
<method name="abs" cv="const"><type>BOOST_CXX14_CONSTEXPR duration_type</type></method>
<method name="is_negative" cv="const"><type>BOOST_CONSTEXPR bool</type></method>
<method name="is_zero" cv="const"><type>BOOST_CONSTEXPR bool</type></method>
<method name="is_positive" cv="const"><type>BOOST_CONSTEXPR bool</type></method>
<method name="operator&lt;" cv="const"><type>BOOST_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const <classname>time_duration</classname> &amp;</paramtype></parameter></method>
<method name="operator==" cv="const"><type>BOOST_CONSTEXPR bool</type><parameter name="rhs"><paramtype>const <classname>time_duration</classname> &amp;</paramtype></parameter></method>
<method name="operator-" cv="const"><type>BOOST_CONSTEXPR duration_type</type><purpose>unary- Allows for <classname alt="boost::date_time::time_duration">time_duration</classname> td = -td1 </purpose></method>
<method name="operator-" cv="const"><type>BOOST_CONSTEXPR duration_type</type><parameter name="d"><paramtype>const duration_type &amp;</paramtype></parameter></method>
<method name="operator+" cv="const"><type>BOOST_CONSTEXPR duration_type</type><parameter name="d"><paramtype>const duration_type &amp;</paramtype></parameter></method>
<method name="operator/" cv="const"><type>BOOST_CONSTEXPR duration_type</type><parameter name="divisor"><paramtype>int</paramtype></parameter></method>
<method name="operator-="><type>BOOST_CXX14_CONSTEXPR duration_type</type><parameter name="d"><paramtype>const duration_type &amp;</paramtype></parameter></method>
<method name="operator+="><type>BOOST_CXX14_CONSTEXPR duration_type</type><parameter name="d"><paramtype>const duration_type &amp;</paramtype></parameter></method>
<method name="operator/="><type>BOOST_CXX14_CONSTEXPR duration_type</type><parameter name="divisor"><paramtype>int</paramtype></parameter><purpose>Division operations on a duration with an integer. </purpose></method>
<method name="operator *" cv="const"><type>BOOST_CXX14_CONSTEXPR duration_type</type><parameter name="rhs"><paramtype>int</paramtype></parameter><purpose>Multiplication operations an a duration with an integer. </purpose></method>
<method name="operator *="><type>BOOST_CXX14_CONSTEXPR duration_type</type><parameter name="divisor"><paramtype>int</paramtype></parameter></method>
<method name="ticks" cv="const"><type>BOOST_CXX14_CONSTEXPR tick_type</type></method>
<method name="is_special" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>Is ticks_ a special value? </purpose></method>
<method name="is_pos_infinity" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>Is duration pos-infinity. </purpose></method>
<method name="is_neg_infinity" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>Is duration neg-infinity. </purpose></method>
<method name="is_not_a_date_time" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>Is duration not-a-date-time. </purpose></method>
<method name="get_rep" cv="const"><type>BOOST_CONSTEXPR impl_type</type><purpose>Used for special_values output. </purpose></method>
</method-group>
<constructor/>
<constructor><parameter name="hours_in"><paramtype>hour_type</paramtype></parameter><parameter name="minutes_in"><paramtype>min_type</paramtype></parameter><parameter name="seconds_in"><paramtype>sec_type</paramtype><default>0</default></parameter><parameter name="frac_sec_in"><paramtype>fractional_seconds_type</paramtype><default>0</default></parameter></constructor>
<constructor><parameter name="sv"><paramtype>special_values</paramtype></parameter><purpose>Construct from special_values. </purpose></constructor>
<method-group name="public static functions">
<method name="unit" specifiers="static"><type>BOOST_CXX14_CONSTEXPR duration_type</type><purpose>Returns smallest representable duration. </purpose></method>
<method name="ticks_per_second" specifiers="static"><type>BOOST_CXX14_CONSTEXPR tick_type</type><purpose>Return the number of ticks in a second. </purpose></method>
<method name="resolution" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_resolutions</type><purpose>Provide the resolution of this duration type. </purpose></method>
<method name="num_fractional_digits" specifiers="static"><type>BOOST_CXX14_CONSTEXPR unsigned short</type><purpose>Returns number of possible digits in fractional seconds. </purpose></method>
</method-group>
<method-group name="protected member functions">
</method-group>
<constructor specifiers="explicit"><parameter name="in"><paramtype>impl_type</paramtype></parameter></constructor>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/time_facet.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="time_facet"><template>
      <template-type-parameter name="time_type"/>
      <template-type-parameter name="CharT"/>
      <template-type-parameter name="OutItrT"><default>std::ostreambuf_iterator&lt;CharT, std::char_traits&lt;CharT&gt; &gt;</default></template-type-parameter>
    </template><inherit access="public">boost::date_time::date_facet&lt; time_type::date_type, CharT, OutItrT &gt;</inherit><description><para>Facet used for format-based output of time types This class provides for the use of format strings to output times. In addition to the flags for formatting date elements, the following are the allowed format flags:<itemizedlist>
<listitem><para>x X =&gt; default format - enables addition of more flags to default (ie. "%x %X %z")</para>
</listitem><listitem><para>f =&gt; fractional seconds ".123456"</para>
</listitem><listitem><para>F =&gt; fractional seconds or none: like frac sec but empty if frac sec == 0</para>
</listitem><listitem><para>s =&gt; seconds w/ fractional sec "02.123" (this is the same as "%S%f)
 - %S =&gt; seconds "02"
 - %z =&gt; abbreviated time zone "EDT"
 - %Z =&gt; full time zone name "Eastern Daylight Time" </para>
</listitem></itemizedlist>
</para></description><typedef name="date_type"><type>time_type::date_type</type></typedef>
<typedef name="time_duration_type"><type>time_type::time_duration_type</type></typedef>
<typedef name="period_type"><type><classname>boost::date_time::period</classname>&lt; time_type, time_duration_type &gt;</type></typedef>
<typedef name="base_type"><type><classname>boost::date_time::date_facet</classname>&lt; typename time_type::date_type, CharT, OutItrT &gt;</type></typedef>
<typedef name="string_type"><type>base_type::string_type</type></typedef>
<typedef name="char_type"><type>base_type::char_type</type></typedef>
<typedef name="period_formatter_type"><type><classname>base_type::period_formatter_type</classname></type></typedef>
<typedef name="special_values_formatter_type"><type><classname>base_type::special_values_formatter_type</classname></type></typedef>
<typedef name="date_gen_formatter_type"><type><classname>base_type::date_gen_formatter_type</classname></type></typedef>
<data-member name="fractional_seconds_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="fractional_seconds_or_none_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="seconds_with_fractional_seconds_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="seconds_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="hours_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="unrestricted_hours_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="standard_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="zone_abbrev_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="zone_name_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="zone_iso_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="zone_iso_extended_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="posix_zone_string_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="duration_seperator" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="duration_sign_always" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="duration_sign_negative_only" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="negative_sign" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="positive_sign" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="iso_time_format_specifier" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="iso_time_format_extended_specifier" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="default_time_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="default_time_duration_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="id" specifiers="static"><type>std::locale::id</type></data-member>
<method-group name="public member functions">
<method name="__get_id" cv="const"><type>std::locale::id &amp;</type><parameter name=""><paramtype>void</paramtype></parameter></method>
<method name="time_duration_format"><type>void</type><parameter name="format"><paramtype>const char_type *const</paramtype></parameter><purpose>Changes format for <classname alt="boost::date_time::time_duration">time_duration</classname>. </purpose></method>
<method name="set_iso_format" specifiers="virtual"><type>void</type></method>
<method name="set_iso_extended_format" specifiers="virtual"><type>void</type></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next_arg"><paramtype>OutItrT</paramtype></parameter><parameter name="ios_arg"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_arg"><paramtype>char_type</paramtype></parameter><parameter name="time_arg"><paramtype>const time_type &amp;</paramtype></parameter></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next_arg"><paramtype>OutItrT</paramtype></parameter><parameter name="ios_arg"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill_arg"><paramtype>char_type</paramtype></parameter><parameter name="time_dur_arg"><paramtype>const time_duration_type &amp;</paramtype></parameter><purpose>put function for <classname alt="boost::date_time::time_duration">time_duration</classname> </purpose></method>
<method name="put" cv="const"><type>OutItrT</type><parameter name="next"><paramtype>OutItrT</paramtype></parameter><parameter name="ios_arg"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="fill"><paramtype>char_type</paramtype></parameter><parameter name="p"><paramtype>const <classname>period_type</classname> &amp;</paramtype></parameter></method>
</method-group>
<constructor specifiers="explicit"><parameter name="ref_arg"><paramtype>::size_t</paramtype><default>0</default></parameter><purpose>sets default formats for ptime, local_date_time, and <classname alt="boost::date_time::time_duration">time_duration</classname> </purpose></constructor>
<constructor specifiers="explicit"><parameter name="format_arg"><paramtype>const char_type *</paramtype></parameter><parameter name="period_formatter_arg"><paramtype><classname>period_formatter_type</classname></paramtype><default><classname alt="boost::date_time::period_formatter">period_formatter_type</classname>()</default></parameter><parameter name="special_value_formatter"><paramtype>const <classname>special_values_formatter_type</classname> &amp;</paramtype><default><classname alt="boost::date_time::special_values_formatter">special_values_formatter_type</classname>()</default></parameter><parameter name="dg_formatter"><paramtype><classname>date_gen_formatter_type</classname></paramtype><default><classname alt="boost::date_time::date_generator_formatter">date_gen_formatter_type</classname>()</default></parameter><parameter name="ref_arg"><paramtype>::size_t</paramtype><default>0</default></parameter><purpose>Construct the facet with an explicitly specified format. </purpose></constructor>
<method-group name="protected static functions">
<method name="fractional_seconds_as_string" specifiers="static"><type>string_type</type><parameter name="time_arg"><paramtype>const time_duration_type &amp;</paramtype></parameter><parameter name="null_when_zero"><paramtype>bool</paramtype></parameter></method>
<method name="hours_as_string" specifiers="static"><type>string_type</type><parameter name="time_arg"><paramtype>const time_duration_type &amp;</paramtype></parameter><parameter name="width"><paramtype>int</paramtype><default>2</default></parameter></method>
<method name="integral_as_string" specifiers="static"><type>string_type</type><template>
          <template-type-parameter name="IntT"/>
        </template><parameter name="val"><paramtype>IntT</paramtype></parameter><parameter name="width"><paramtype>int</paramtype><default>2</default></parameter></method>
</method-group>
</class><struct name="time_formats"><template>
      <template-type-parameter name="CharT"/>
    </template><typedef name="char_type"><type>CharT</type></typedef>
<data-member name="fractional_seconds_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="fractional_seconds_or_none_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="seconds_with_fractional_seconds_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="seconds_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="hours_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="unrestricted_hours_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="full_24_hour_time_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="full_24_hour_time_expanded_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="short_24_hour_time_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="short_24_hour_time_expanded_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="standard_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="zone_abbrev_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="zone_name_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="zone_iso_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="zone_iso_extended_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="posix_zone_string_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="duration_sign_negative_only" specifiers="static"><type>const char_type</type></data-member>
<data-member name="duration_sign_always" specifiers="static"><type>const char_type</type></data-member>
<data-member name="duration_seperator" specifiers="static"><type>const char_type</type></data-member>
<data-member name="negative_sign" specifiers="static"><type>const char_type</type></data-member>
<data-member name="positive_sign" specifiers="static"><type>const char_type</type></data-member>
<data-member name="iso_time_format_specifier" specifiers="static"><type>const char_type</type></data-member>
<data-member name="iso_time_format_extended_specifier" specifiers="static"><type>const char_type</type></data-member>
<data-member name="default_time_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="default_time_input_format" specifiers="static"><type>const char_type</type></data-member>
<data-member name="default_time_duration_format" specifiers="static"><type>const char_type</type></data-member>
</struct><class name="time_input_facet"><template>
      <template-type-parameter name="time_type"/>
      <template-type-parameter name="CharT"/>
      <template-type-parameter name="InItrT"><default>std::istreambuf_iterator&lt;CharT, std::char_traits&lt;CharT&gt; &gt;</default></template-type-parameter>
    </template><inherit access="public">boost::date_time::date_input_facet&lt; time_type::date_type, CharT, InItrT &gt;</inherit><purpose>Facet for format-based input. </purpose><typedef name="date_type"><type>time_type::date_type</type></typedef>
<typedef name="time_duration_type"><type>time_type::time_duration_type</type></typedef>
<typedef name="fracional_seconds_type"><type>time_duration_type::fractional_seconds_type</type></typedef>
<typedef name="period_type"><type><classname>boost::date_time::period</classname>&lt; time_type, time_duration_type &gt;</type></typedef>
<typedef name="base_type"><type><classname>boost::date_time::date_input_facet</classname>&lt; typename time_type::date_type, CharT, InItrT &gt;</type></typedef>
<typedef name="date_duration_type"><type>base_type::duration_type</type></typedef>
<typedef name="year_type"><type>base_type::year_type</type></typedef>
<typedef name="month_type"><type>base_type::month_type</type></typedef>
<typedef name="day_type"><type>base_type::day_type</type></typedef>
<typedef name="string_type"><type>base_type::string_type</type></typedef>
<typedef name="const_itr"><type>string_type::const_iterator</type></typedef>
<typedef name="char_type"><type>base_type::char_type</type></typedef>
<typedef name="format_date_parser_type"><type><classname>base_type::format_date_parser_type</classname></type></typedef>
<typedef name="period_parser_type"><type><classname>base_type::period_parser_type</classname></type></typedef>
<typedef name="special_values_parser_type"><type><classname>base_type::special_values_parser_type</classname></type></typedef>
<typedef name="date_gen_parser_type"><type><classname>base_type::date_gen_parser_type</classname></type></typedef>
<typedef name="match_results"><type>base_type::special_values_parser_type::match_results</type></typedef>
<data-member name="fractional_seconds_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="fractional_seconds_or_none_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="seconds_with_fractional_seconds_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="seconds_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="standard_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="zone_abbrev_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="zone_name_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="zone_iso_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="zone_iso_extended_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="duration_seperator" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="iso_time_format_specifier" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="iso_time_format_extended_specifier" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="default_time_input_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="default_time_duration_format" specifiers="static"><type>const char_type *</type></data-member>
<data-member name="id" specifiers="static"><type>std::locale::id</type></data-member>
<method-group name="public member functions">
<method name="time_duration_format"><type>void</type><parameter name="format"><paramtype>const char_type *const</paramtype></parameter><purpose>Set the format for <classname alt="boost::date_time::time_duration">time_duration</classname>. </purpose></method>
<method name="set_iso_format" specifiers="virtual"><type>void</type></method>
<method name="set_iso_extended_format" specifiers="virtual"><type>void</type></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="sitr"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="ios_arg"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="p"><paramtype><classname>period_type</classname> &amp;</paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="sitr"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="ios_arg"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="td"><paramtype>time_duration_type &amp;</paramtype></parameter></method>
<method name="get" cv="const"><type>InItrT</type><parameter name="sitr"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="ios_arg"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><purpose>Parses a time object from the input stream. </purpose></method>
<method name="get_local_time" cv="const"><type>InItrT</type><parameter name="sitr"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="ios_arg"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="tz_str"><paramtype>string_type &amp;</paramtype></parameter><purpose>Expects a time_zone in the input stream. </purpose></method>
</method-group>
<constructor specifiers="explicit"><parameter name="format"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="ref_arg"><paramtype>::size_t</paramtype><default>0</default></parameter><purpose>Constructor that takes a format string for a ptime. </purpose></constructor>
<constructor specifiers="explicit"><parameter name="format"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="date_parser"><paramtype>const <classname>format_date_parser_type</classname> &amp;</paramtype></parameter><parameter name="sv_parser"><paramtype>const <classname>special_values_parser_type</classname> &amp;</paramtype></parameter><parameter name="per_parser"><paramtype>const <classname>period_parser_type</classname> &amp;</paramtype></parameter><parameter name="date_gen_parser"><paramtype>const <classname>date_gen_parser_type</classname> &amp;</paramtype></parameter><parameter name="ref_arg"><paramtype>::size_t</paramtype><default>0</default></parameter></constructor>
<constructor specifiers="explicit"><parameter name="ref_arg"><paramtype>::size_t</paramtype><default>0</default></parameter><purpose>sets default formats for ptime, local_date_time, and <classname alt="boost::date_time::time_duration">time_duration</classname> </purpose></constructor>
<method-group name="protected member functions">
<method name="get" cv="const"><type>InItrT</type><parameter name="sitr"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="ios_arg"><paramtype>std::ios_base &amp;</paramtype></parameter><parameter name="t"><paramtype>time_type &amp;</paramtype></parameter><parameter name="tz_str"><paramtype>string_type &amp;</paramtype></parameter><parameter name="time_is_local"><paramtype>bool</paramtype></parameter></method>
<method name="check_special_value" cv="const"><type>InItrT</type><template>
          <template-type-parameter name="temporal_type"/>
        </template><parameter name="sitr"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="tt"><paramtype>temporal_type &amp;</paramtype></parameter><parameter name="c"><paramtype>char_type</paramtype><default>'\0'</default></parameter><purpose>Helper function to check for special_value. </purpose><description><para>First character may have been consumed during original parse attempt. Parameter 'c' should be a copy of that character. Throws ios_base::failure if parse fails. </para></description></method>
<method name="parse_frac_type" cv="const"><type>void</type><parameter name="sitr"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="stream_end"><paramtype>InItrT &amp;</paramtype></parameter><parameter name="frac"><paramtype>fracional_seconds_type &amp;</paramtype></parameter><purpose>Helper function for parsing a fractional second type from the stream. </purpose></method>
</method-group>
<method-group name="private member functions">
<method name="decimal_adjust" cv="const"><type>int_type</type><template>
          <template-type-parameter name="int_type"/>
        </template><parameter name="val"><paramtype>int_type</paramtype></parameter><parameter name="places"><paramtype>const unsigned short</paramtype></parameter><purpose>Helper function to adjust trailing zeros when parsing fractional digits. </purpose></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/time_formatting_streams.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="ostream_time_duration_formatter"><template>
      <template-type-parameter name="time_duration_type"/>
      <template-type-parameter name="charT"><default>char</default></template-type-parameter>
    </template><purpose>Put a time type into a stream using appropriate facets. </purpose><typedef name="ostream_type"><type>std::basic_ostream&lt; charT &gt;</type></typedef>
<typedef name="fractional_seconds_type"><type>time_duration_type::fractional_seconds_type</type></typedef>
<method-group name="public static functions">
<method name="duration_put" specifiers="static"><type>void</type><parameter name="td"><paramtype>const time_duration_type &amp;</paramtype></parameter><parameter name="os"><paramtype>ostream_type &amp;</paramtype></parameter><purpose>Put time into an ostream. </purpose></method>
</method-group>
</class><class name="ostream_time_formatter"><template>
      <template-type-parameter name="time_type"/>
      <template-type-parameter name="charT"><default>char</default></template-type-parameter>
    </template><purpose>Put a time type into a stream using appropriate facets. </purpose><typedef name="ostream_type"><type>std::basic_ostream&lt; charT &gt;</type></typedef>
<typedef name="date_type"><type>time_type::date_type</type></typedef>
<typedef name="time_duration_type"><type>time_type::time_duration_type</type></typedef>
<typedef name="duration_formatter"><type><classname>ostream_time_duration_formatter</classname>&lt; time_duration_type, charT &gt;</type></typedef>
<method-group name="public static functions">
<method name="time_put" specifiers="static"><type>void</type><parameter name="t"><paramtype>const time_type &amp;</paramtype></parameter><parameter name="os"><paramtype>ostream_type &amp;</paramtype></parameter><purpose>Put time into an ostream. </purpose></method>
</method-group>
</class><class name="ostream_time_period_formatter"><template>
      <template-type-parameter name="time_period_type"/>
      <template-type-parameter name="charT"><default>char</default></template-type-parameter>
    </template><purpose>Put a time period into a stream using appropriate facets. </purpose><typedef name="ostream_type"><type>std::basic_ostream&lt; charT &gt;</type></typedef>
<typedef name="time_type"><type>time_period_type::point_type</type></typedef>
<typedef name="time_formatter"><type><classname>ostream_time_formatter</classname>&lt; time_type, charT &gt;</type></typedef>
<method-group name="public static functions">
<method name="period_put" specifiers="static"><type>void</type><parameter name="tp"><paramtype>const time_period_type &amp;</paramtype></parameter><parameter name="os"><paramtype>ostream_type &amp;</paramtype></parameter><purpose>Put time into an ostream. </purpose></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/time_iterator.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="time_itr"><template>
      <template-type-parameter name="time_type"/>
    </template><purpose>Simple time iterator skeleton class. </purpose><typedef name="time_duration_type"><type>time_type::time_duration_type</type></typedef>
<method-group name="public member functions">
<method name="operator++"><type><classname>time_itr</classname> &amp;</type></method>
<method name="operator--"><type><classname>time_itr</classname> &amp;</type></method>
<method name="operator *" cv="const"><type>const time_type &amp;</type></method>
<method name="operator-&gt;" cv="const"><type>const time_type *</type></method>
<method name="operator&lt;" cv="const"><type>bool</type><parameter name="t"><paramtype>const time_type &amp;</paramtype></parameter></method>
<method name="operator&lt;=" cv="const"><type>bool</type><parameter name="t"><paramtype>const time_type &amp;</paramtype></parameter></method>
<method name="operator!=" cv="const"><type>bool</type><parameter name="t"><paramtype>const time_type &amp;</paramtype></parameter></method>
<method name="operator==" cv="const"><type>bool</type><parameter name="t"><paramtype>const time_type &amp;</paramtype></parameter></method>
<method name="operator&gt;" cv="const"><type>bool</type><parameter name="t"><paramtype>const time_type &amp;</paramtype></parameter></method>
<method name="operator&gt;=" cv="const"><type>bool</type><parameter name="t"><paramtype>const time_type &amp;</paramtype></parameter></method>
</method-group>
<constructor><parameter name="t"><paramtype>time_type</paramtype></parameter><parameter name="d"><paramtype>time_duration_type</paramtype></parameter></constructor>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/time_parsing.hpp">
<namespace name="boost">
<namespace name="date_time">

<function name="power"><type>int_type</type><template>
          <template-type-parameter name="int_type"/>
        </template><parameter name="base"><paramtype>int_type</paramtype></parameter><parameter name="exponent"><paramtype>int_type</paramtype></parameter><purpose>computes exponential math like 2^8 =&gt; 256, only works with positive integers </purpose></function>
<function name="str_from_delimited_time_duration"><type><classname>time_duration</classname></type><template>
          <template-type-parameter name="time_duration"/>
          <template-type-parameter name="char_type"/>
        </template><parameter name="s"><paramtype>const std::basic_string&lt; char_type &gt; &amp;</paramtype></parameter><purpose>Creates a <classname alt="boost::date_time::time_duration">time_duration</classname> object from a delimited string. </purpose><description><para>Expected format for string is "[-]h[h][:mm][:ss][.fff]". If the number of fractional digits provided is greater than the precision of the time duration type then the extra digits are truncated.</para><para>A negative duration will be created if the first character in string is a '-', all other '-' will be treated as delimiters. Accepted delimiters are "-:,.". </para></description></function>
<function name="parse_delimited_time_duration"><type><classname>time_duration</classname></type><template>
          <template-type-parameter name="time_duration"/>
        </template><parameter name="s"><paramtype>const std::string &amp;</paramtype></parameter><purpose>Creates a <classname alt="boost::date_time::time_duration">time_duration</classname> object from a delimited string. </purpose><description><para>Expected format for string is "[-]h[h][:mm][:ss][.fff]". If the number of fractional digits provided is greater than the precision of the time duration type then the extra digits are truncated.</para><para>A negative duration will be created if the first character in string is a '-', all other '-' will be treated as delimiters. Accepted delimiters are "-:,.". </para></description></function>
<function name="split"><type>bool</type><parameter name="s"><paramtype>const std::string &amp;</paramtype></parameter><parameter name="sep"><paramtype>char</paramtype></parameter><parameter name="first"><paramtype>std::string &amp;</paramtype></parameter><parameter name="second"><paramtype>std::string &amp;</paramtype></parameter><purpose>Utility function to split appart string. </purpose></function>
<function name="parse_delimited_time"><type>time_type</type><template>
          <template-type-parameter name="time_type"/>
        </template><parameter name="s"><paramtype>const std::string &amp;</paramtype></parameter><parameter name="sep"><paramtype>char</paramtype></parameter></function>
<function name="parse_undelimited_time_duration"><type><classname>time_duration</classname></type><template>
          <template-type-parameter name="time_duration"/>
        </template><parameter name="s"><paramtype>const std::string &amp;</paramtype></parameter><purpose>Parse time duration part of an ISO 8601 time of form: [-]hhmmss<ulink url="eg: 120259.123 is 12 hours, 2 min, 59 seconds, 123000 microseconds">.fff...</ulink> </purpose></function>
<function name="parse_iso_time"><type>time_type</type><template>
          <template-type-parameter name="time_type"/>
        </template><parameter name="s"><paramtype>const std::string &amp;</paramtype></parameter><parameter name="sep"><paramtype>char</paramtype></parameter><purpose>Parse time string of form YYYYMMDDThhmmss where T is delimeter between date and time. </purpose></function>

























</namespace>
</namespace>
</header>
<header name="boost/date_time/time_resolution_traits.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="time_resolution_traits"><template>
      <template-type-parameter name="frac_sec_type"/>
      <template-nontype-parameter name="res"><type>time_resolutions</type></template-nontype-parameter>
      <template-nontype-parameter name="resolution_adjust"><type>#if(defined(BOOST_MSVC) &amp;&amp;(_MSC_VER&lt; 1300)) boost::int64_t</type></template-nontype-parameter>
      <template-nontype-parameter name="resolution_adjust"><type>#else typename frac_sec_type::int_type</type></template-nontype-parameter>
      <template-nontype-parameter name="frac_digits"><type>#endif unsigned short</type></template-nontype-parameter>
      <template-type-parameter name="var_type"><default>boost::int64_t</default></template-type-parameter>
    </template><typedef name="fractional_seconds_type"><type>frac_sec_type::int_type</type></typedef>
<typedef name="tick_type"><type>frac_sec_type::int_type</type></typedef>
<typedef name="impl_type"><type>frac_sec_type::impl_type</type></typedef>
<typedef name="day_type"><type>var_type</type></typedef>
<typedef name="hour_type"><type>var_type</type></typedef>
<typedef name="min_type"><type>var_type</type></typedef>
<typedef name="sec_type"><type>var_type</type></typedef>
<method-group name="public static functions">
<method name="as_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR fractional_seconds_type</type><parameter name="i"><paramtype>impl_type</paramtype></parameter></method>
<method name="is_adapted" specifiers="static"><type>BOOST_CXX14_CONSTEXPR bool</type></method>
<method name="resolution" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_resolutions</type></method>
<method name="num_fractional_digits" specifiers="static"><type>BOOST_CXX14_CONSTEXPR unsigned short</type></method>
<method name="res_adjust" specifiers="static"><type>BOOST_CXX14_CONSTEXPR fractional_seconds_type</type></method>
<method name="to_tick_count" specifiers="static"><type>BOOST_CXX14_CONSTEXPR tick_type</type><parameter name="hours"><paramtype>hour_type</paramtype></parameter><parameter name="minutes"><paramtype>min_type</paramtype></parameter><parameter name="seconds"><paramtype>sec_type</paramtype></parameter><parameter name="fs"><paramtype>fractional_seconds_type</paramtype></parameter><purpose>Any negative argument results in a negative tick_count. </purpose></method>
</method-group>
<method-group name="public member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>boost::int64_t</paramtype></parameter><parameter name=""><paramtype>ticks_per_second</paramtype><default>resolution_adjust</default></parameter></method>
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>fractional_seconds_type</paramtype></parameter><parameter name=""><paramtype>ticks_per_second</paramtype><default>resolution_adjust</default></parameter></method>
</method-group>
</class><struct name="time_resolution_traits_adapted32_impl"><purpose>traits struct for <classname alt="boost::date_time::time_resolution_traits">time_resolution_traits</classname> implementation type </purpose><typedef name="int_type"><type>boost::int32_t</type></typedef>
<typedef name="impl_type"><type><classname>boost::date_time::int_adapter</classname>&lt; boost::int32_t &gt;</type></typedef>
<method-group name="public static functions">
<method name="as_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR int_type</type><parameter name="i"><paramtype><classname>impl_type</classname></paramtype></parameter></method>
<method name="is_adapted" specifiers="static"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>Used to determine if implemented type is <classname alt="boost::date_time::int_adapter">int_adapter</classname> or int. </purpose></method>
</method-group>
</struct><struct name="time_resolution_traits_adapted64_impl"><purpose>traits struct for <classname alt="boost::date_time::time_resolution_traits">time_resolution_traits</classname> implementation type </purpose><typedef name="int_type"><type>boost::int64_t</type></typedef>
<typedef name="impl_type"><type><classname>boost::date_time::int_adapter</classname>&lt; boost::int64_t &gt;</type></typedef>
<method-group name="public static functions">
<method name="as_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR int_type</type><parameter name="i"><paramtype><classname>impl_type</classname></paramtype></parameter></method>
<method name="is_adapted" specifiers="static"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>Used to determine if implemented type is <classname alt="boost::date_time::int_adapter">int_adapter</classname> or int. </purpose></method>
</method-group>
</struct><struct name="time_resolution_traits_bi32_impl"><purpose>traits struct for <classname alt="boost::date_time::time_resolution_traits">time_resolution_traits</classname> implementation type </purpose><typedef name="int_type"><type>boost::int32_t</type></typedef>
<typedef name="impl_type"><type>boost::int32_t</type></typedef>
<method-group name="public static functions">
<method name="as_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR int_type</type><parameter name="i"><paramtype>impl_type</paramtype></parameter></method>
<method name="is_adapted" specifiers="static"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>Used to determine if implemented type is <classname alt="boost::date_time::int_adapter">int_adapter</classname> or int. </purpose></method>
</method-group>
</struct><struct name="time_resolution_traits_bi64_impl"><purpose>traits struct for <classname alt="boost::date_time::time_resolution_traits">time_resolution_traits</classname> implementation type </purpose><typedef name="int_type"><type>boost::int64_t</type></typedef>
<typedef name="impl_type"><type>boost::int64_t</type></typedef>
<method-group name="public static functions">
<method name="as_number" specifiers="static"><type>BOOST_CXX14_CONSTEXPR int_type</type><parameter name="i"><paramtype>impl_type</paramtype></parameter></method>
<method name="is_adapted" specifiers="static"><type>BOOST_CXX14_CONSTEXPR bool</type><purpose>Used to determine if implemented type is <classname alt="boost::date_time::int_adapter">int_adapter</classname> or int. </purpose></method>
</method-group>
</struct><typedef name="milli_res"><type><classname>time_resolution_traits</classname>&lt; <classname>time_resolution_traits_adapted32_impl</classname>, milli, 1000, 3 &gt;</type></typedef>
<typedef name="micro_res"><type><classname>time_resolution_traits</classname>&lt; <classname>time_resolution_traits_adapted64_impl</classname>, micro, 1000000, 6 &gt;</type></typedef>
<typedef name="nano_res"><type><classname>time_resolution_traits</classname>&lt; <classname>time_resolution_traits_adapted64_impl</classname>, nano, 1000000000, 9 &gt;</type></typedef>
<function name="absolute_value"><type>BOOST_CXX14_CONSTEXPR T</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name="x"><paramtype>T</paramtype></parameter><purpose>Simple function to calculate absolute value of a numeric type. </purpose></function>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/time_system_counted.hpp">
<namespace name="boost">
<namespace name="date_time">
<struct name="counted_time_rep"><template>
      <template-type-parameter name="config"/>
    </template><purpose>Time representation that uses a single integer count. </purpose><typedef name="int_type"><type>config::int_type</type></typedef>
<typedef name="date_type"><type>config::date_type</type></typedef>
<typedef name="impl_type"><type>config::impl_type</type></typedef>
<typedef name="date_duration_type"><type>date_type::duration_type</type></typedef>
<typedef name="calendar_type"><type>date_type::calendar_type</type></typedef>
<typedef name="ymd_type"><type>date_type::ymd_type</type></typedef>
<typedef name="time_duration_type"><type>config::time_duration_type</type></typedef>
<typedef name="resolution_traits"><type>config::resolution_traits</type></typedef>
<method-group name="public member functions">
<method name="date" cv="const"><type>BOOST_CXX14_CONSTEXPR date_type</type></method>
<method name="day_count" cv="const"><type>BOOST_CXX14_CONSTEXPR unsigned long</type></method>
<method name="time_count" cv="const"><type>BOOST_CXX14_CONSTEXPR int_type</type></method>
<method name="tod" cv="const"><type>BOOST_CXX14_CONSTEXPR int_type</type></method>
<method name="is_pos_infinity" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type></method>
<method name="is_neg_infinity" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type></method>
<method name="is_not_a_date_time" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type></method>
<method name="is_special" cv="const"><type>BOOST_CXX14_CONSTEXPR bool</type></method>
<method name="get_rep" cv="const"><type>BOOST_CXX14_CONSTEXPR impl_type</type></method>
</method-group>
<constructor><parameter name="d"><paramtype>const date_type &amp;</paramtype></parameter><parameter name="time_of_day"><paramtype>const time_duration_type &amp;</paramtype></parameter></constructor>
<constructor specifiers="explicit"><parameter name="count"><paramtype>int_type</paramtype></parameter></constructor>
<constructor specifiers="explicit"><parameter name="count"><paramtype>impl_type</paramtype></parameter></constructor>
<method-group name="public static functions">
<method name="frac_sec_per_day" specifiers="static"><type>BOOST_CXX14_CONSTEXPR int_type</type></method>
</method-group>
</struct><class name="counted_time_system"><template>
      <template-type-parameter name="time_rep"/>
    </template><purpose>An unadjusted time system implementation. </purpose><typedef name="time_rep_type"><type>time_rep</type></typedef>
<typedef name="impl_type"><type>time_rep_type::impl_type</type></typedef>
<typedef name="time_duration_type"><type>time_rep_type::time_duration_type</type></typedef>
<typedef name="fractional_seconds_type"><type>time_duration_type::fractional_seconds_type</type></typedef>
<typedef name="date_type"><type>time_rep_type::date_type</type></typedef>
<typedef name="date_duration_type"><type>time_rep_type::date_duration_type</type></typedef>
<method-group name="public static functions">
<method name="unused_var" specifiers="static"><type>BOOST_CXX14_CONSTEXPR void</type><template>
          <template-type-parameter name="T"/>
        </template><parameter name=""><paramtype>const T &amp;</paramtype></parameter></method>
<method name="get_time_rep" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_rep_type</type><parameter name="day"><paramtype>const date_type &amp;</paramtype></parameter><parameter name="tod"><paramtype>const time_duration_type &amp;</paramtype></parameter><parameter name="dst"><paramtype>date_time::dst_flags</paramtype><default>not_dst</default></parameter></method>
<method name="get_time_rep" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_rep_type</type><parameter name="sv"><paramtype>special_values</paramtype></parameter></method>
<method name="get_date" specifiers="static"><type>BOOST_CXX14_CONSTEXPR date_type</type><parameter name="val"><paramtype>const time_rep_type &amp;</paramtype></parameter></method>
<method name="get_time_of_day" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_duration_type</type><parameter name="val"><paramtype>const time_rep_type &amp;</paramtype></parameter></method>
<method name="zone_name" specifiers="static"><type>std::string</type><parameter name=""><paramtype>const time_rep_type &amp;</paramtype></parameter></method>
<method name="is_equal" specifiers="static"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="lhs"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const time_rep_type &amp;</paramtype></parameter></method>
<method name="is_less" specifiers="static"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="lhs"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const time_rep_type &amp;</paramtype></parameter></method>
<method name="add_days" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_rep_type</type><parameter name="base"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="dd"><paramtype>const date_duration_type &amp;</paramtype></parameter></method>
<method name="subtract_days" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_rep_type</type><parameter name="base"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="dd"><paramtype>const date_duration_type &amp;</paramtype></parameter></method>
<method name="subtract_time_duration" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_rep_type</type><parameter name="base"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="td"><paramtype>const time_duration_type &amp;</paramtype></parameter></method>
<method name="add_time_duration" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_rep_type</type><parameter name="base"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="td"><paramtype>time_duration_type</paramtype></parameter></method>
<method name="subtract_times" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_duration_type</type><parameter name="lhs"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const time_rep_type &amp;</paramtype></parameter></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/time_system_split.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="split_timedate_system"><template>
      <template-type-parameter name="config"/>
      <template-nontype-parameter name="ticks_per_second"><type>boost::int32_t</type></template-nontype-parameter>
    </template><purpose>An unadjusted time system implementation. </purpose><typedef name="time_rep_type"><type>config::time_rep_type</type></typedef>
<typedef name="date_type"><type>config::date_type</type></typedef>
<typedef name="time_duration_type"><type>config::time_duration_type</type></typedef>
<typedef name="date_duration_type"><type>config::date_duration_type</type></typedef>
<typedef name="int_type"><type>config::int_type</type></typedef>
<typedef name="resolution_traits"><type>config::resolution_traits</type></typedef>
<typedef name="wrap_int_type"><type><classname>date_time::wrapping_int</classname>&lt; int_type, INT64_C(86400) *ticks_per_second &gt;</type></typedef>
<typedef name="wrap_int_type"><type><classname>date_time::wrapping_int</classname>&lt; split_timedate_system::int_type, split_timedate_system::ticks_per_day &gt;</type></typedef>
<typedef name="wrap_int_type"><type><classname>date_time::wrapping_int</classname>&lt; int_type, ticks_per_day &gt;</type></typedef>
<method-group name="private member functions">
<method name="BOOST_STATIC_CONSTANT"><type/><parameter name=""><paramtype>int_type</paramtype></parameter><parameter name=""><paramtype>ticks_per_day</paramtype><default>INT64_C(86400) *config::tick_per_second</default></parameter></method>
</method-group>
<method-group name="public static functions">
<method name="get_time_rep" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_rep_type</type><parameter name="sv"><paramtype>special_values</paramtype></parameter></method>
<method name="get_time_rep" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_rep_type</type><parameter name="day"><paramtype>const date_type &amp;</paramtype></parameter><parameter name="tod"><paramtype>const time_duration_type &amp;</paramtype></parameter><parameter name=""><paramtype>date_time::dst_flags</paramtype><default>not_dst</default></parameter></method>
<method name="get_date" specifiers="static"><type>BOOST_CONSTEXPR date_type</type><parameter name="val"><paramtype>const time_rep_type &amp;</paramtype></parameter></method>
<method name="get_time_of_day" specifiers="static"><type>BOOST_CONSTEXPR time_duration_type</type><parameter name="val"><paramtype>const time_rep_type &amp;</paramtype></parameter></method>
<method name="zone_name" specifiers="static"><type>std::string</type><parameter name=""><paramtype>const time_rep_type &amp;</paramtype></parameter></method>
<method name="is_equal" specifiers="static"><type>BOOST_CONSTEXPR bool</type><parameter name="lhs"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const time_rep_type &amp;</paramtype></parameter></method>
<method name="is_less" specifiers="static"><type>BOOST_CXX14_CONSTEXPR bool</type><parameter name="lhs"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const time_rep_type &amp;</paramtype></parameter></method>
<method name="add_days" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_rep_type</type><parameter name="base"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="dd"><paramtype>const date_duration_type &amp;</paramtype></parameter></method>
<method name="subtract_days" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_rep_type</type><parameter name="base"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="dd"><paramtype>const date_duration_type &amp;</paramtype></parameter></method>
<method name="subtract_time_duration" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_rep_type</type><parameter name="base"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="td"><paramtype>const time_duration_type &amp;</paramtype></parameter></method>
<method name="add_time_duration" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_rep_type</type><parameter name="base"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="td"><paramtype>time_duration_type</paramtype></parameter></method>
<method name="subtract_times" specifiers="static"><type>BOOST_CXX14_CONSTEXPR time_duration_type</type><parameter name="lhs"><paramtype>const time_rep_type &amp;</paramtype></parameter><parameter name="rhs"><paramtype>const time_rep_type &amp;</paramtype></parameter></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/time_zone_base.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="dst_adjustment_offsets"><template>
      <template-type-parameter name="time_duration_type"><purpose><para>A type used to represent the offset </para></purpose></template-type-parameter>
    </template><purpose>Structure which holds the time offsets associated with daylight savings time. </purpose><description><para>
</para></description><data-member name="dst_adjust_"><type>time_duration_type</type><purpose>Amount DST adjusts the clock eg: plus one hour. </purpose></data-member>
<data-member name="dst_start_offset_"><type>time_duration_type</type><purpose>Time past midnight on start transition day that dst starts. </purpose></data-member>
<data-member name="dst_end_offset_"><type>time_duration_type</type><purpose>Time past midnight on end transition day that dst ends. </purpose></data-member>
<method-group name="public member functions">
</method-group>
<constructor><parameter name="dst_adjust"><paramtype>const time_duration_type &amp;</paramtype></parameter><parameter name="dst_start_offset"><paramtype>const time_duration_type &amp;</paramtype></parameter><parameter name="dst_end_offset"><paramtype>const time_duration_type &amp;</paramtype></parameter></constructor>
</class><class name="time_zone_base"><template>
      <template-type-parameter name="time_type"/>
      <template-type-parameter name="CharT"/>
    </template><purpose>Interface class for dynamic time zones. </purpose><description><para>This class represents the base interface for all timezone representations. Subclasses may provide different systems for identifying a particular zone. For example some may provide a geographical based zone construction while others may specify the offset from GMT. Another possible implementation would be to convert from POSIX timezone strings. Regardless of the construction technique, this is the interface that these time zone types must provide.</para><para>Note that this class is intended to be used as a shared resource (hence the derivation from boost::counted_base. </para></description><typedef name="char_type"><type>CharT</type></typedef>
<typedef name="string_type"><type>std::basic_string&lt; CharT &gt;</type></typedef>
<typedef name="stringstream_type"><type>std::basic_ostringstream&lt; CharT &gt;</type></typedef>
<typedef name="year_type"><type>time_type::date_type::year_type</type></typedef>
<typedef name="time_duration_type"><type>time_type::time_duration_type</type></typedef>
<method-group name="public member functions">
<method name="dst_zone_abbrev" cv="const = 0" specifiers="virtual"><type>string_type</type><purpose>String for the timezone when in daylight savings (eg: EDT) </purpose></method>
<method name="std_zone_abbrev" cv="const = 0" specifiers="virtual"><type>string_type</type><purpose>String for the zone when not in daylight savings (eg: EST) </purpose></method>
<method name="dst_zone_name" cv="const = 0" specifiers="virtual"><type>string_type</type><purpose>String for the timezone when in daylight savings (eg: Eastern Daylight Time) </purpose></method>
<method name="std_zone_name" cv="const = 0" specifiers="virtual"><type>string_type</type><purpose>String for the zone when not in daylight savings (eg: Eastern Standard Time) </purpose></method>
<method name="has_dst" cv="const = 0" specifiers="virtual"><type>bool</type><purpose>True if zone uses daylight savings adjustments otherwise false. </purpose></method>
<method name="dst_local_start_time" cv="const = 0" specifiers="virtual"><type>time_type</type><parameter name="y"><paramtype>year_type</paramtype></parameter><purpose>Local time that DST starts – undefined if has_dst is false. </purpose></method>
<method name="dst_local_end_time" cv="const = 0" specifiers="virtual"><type>time_type</type><parameter name="y"><paramtype>year_type</paramtype></parameter><purpose>Local time that DST ends – undefined if has_dst is false. </purpose></method>
<method name="base_utc_offset" cv="const = 0" specifiers="virtual"><type>time_duration_type</type><purpose>Base offset from UTC for zone (eg: -07:30:00) </purpose></method>
<method name="dst_offset" cv="const = 0" specifiers="virtual"><type>time_duration_type</type><purpose>Adjustment forward or back made while DST is in effect. </purpose></method>
<method name="to_posix_string" cv="const = 0" specifiers="virtual"><type>string_type</type><purpose>Returns a POSIX time_zone string for this object. </purpose></method>
</method-group>
<constructor/>
<destructor/>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/time_zone_names.hpp">
<namespace name="boost">
<namespace name="date_time">
<struct name="default_zone_names"><template>
      <template-type-parameter name="CharT"/>
    </template><typedef name="char_type"><type>CharT</type></typedef>
<data-member name="standard_name" specifiers="static"><type>const char_type</type></data-member>
<data-member name="standard_abbrev" specifiers="static"><type>const char_type</type></data-member>
<data-member name="non_dst_identifier" specifiers="static"><type>const char_type</type></data-member>
</struct><class name="time_zone_names_base"><template>
      <template-type-parameter name="CharT"><purpose><para>Allows class to support different character types </para></purpose></template-type-parameter>
    </template><purpose>Base type that holds various string names for timezone output. </purpose><description><para>Class that holds various types of strings used for timezones. For example, for the western United States there is the full name: Pacific Standard Time and the abbreviated name: PST. During daylight savings there are additional names: Pacific Daylight Time and PDT. 
</para></description><typedef name="string_type"><type>std::basic_string&lt; CharT &gt;</type></typedef>
<method-group name="public member functions">
<method name="dst_zone_abbrev" cv="const"><type>string_type</type></method>
<method name="std_zone_abbrev" cv="const"><type>string_type</type></method>
<method name="dst_zone_name" cv="const"><type>string_type</type></method>
<method name="std_zone_name" cv="const"><type>string_type</type></method>
</method-group>
<constructor/>
<constructor><parameter name="std_zone_name_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="std_zone_abbrev_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="dst_zone_name_str"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="dst_zone_abbrev_str"><paramtype>const string_type &amp;</paramtype></parameter></constructor>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/tz_db_base.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="bad_field_count"><inherit access="public">out_of_range</inherit><purpose>Exception thrown when tz database locates incorrect field structure in data file. </purpose><method-group name="public member functions">
</method-group>
<constructor><parameter name="s"><paramtype>const std::string &amp;</paramtype></parameter></constructor>
</class><class name="data_not_accessible"><inherit access="public">logic_error</inherit><purpose>Exception thrown when tz database cannot locate requested data file. </purpose><method-group name="public member functions">
</method-group>
<constructor/>
<constructor><parameter name="filespec"><paramtype>const std::string &amp;</paramtype></parameter></constructor>
</class><class name="tz_db_base"><template>
      <template-type-parameter name="time_zone_type"/>
      <template-type-parameter name="rule_type"/>
    </template><purpose>Creates a database of time_zones from csv datafile. </purpose><description><para>The csv file containing the zone_specs used by the <classname alt="boost::date_time::tz_db_base">tz_db_base</classname> is intended to be customized by the library user. When customizing this file (or creating your own) the file must follow a specific format.</para><para>This first line is expected to contain column headings and is therefore not processed by the <classname alt="boost::date_time::tz_db_base">tz_db_base</classname>.</para><para>Each record (line) must have eleven fields. Some of those fields can be empty. Every field (even empty ones) must be enclosed in double-quotes. Ex: <programlisting language="c++">"America/Phoenix" &lt;- string enclosed in quotes
""                &lt;- empty field
</programlisting></para><para>Some fields represent a length of time. The format of these fields must be: <programlisting language="c++">"{+|-}hh:mm[:ss]" &lt;- length-of-time format
</programlisting> Where the plus or minus is mandatory and the seconds are optional.</para><para>Since some time zones do not use daylight savings it is not always necessary for every field in a zone_spec to contain a value. All zone_specs must have at least ID and GMT offset. Zones that use daylight savings must have all fields filled except: STD ABBR, STD NAME, DST NAME. You should take note that DST ABBR is mandatory for zones that use daylight savings (see field descriptions for further details).</para><para>******* Fields and their description/details *********</para><para>ID: Contains the identifying string for the zone_spec. Any string will do as long as it's unique. No two ID's can be the same.</para><para>STD ABBR: STD NAME: DST ABBR: DST NAME: These four are all the names and abbreviations used by the time zone being described. While any string will do in these fields, care should be taken. These fields hold the strings that will be used in the output of many of the local_time classes. Ex: <programlisting language="c++">time_zone nyc = tz_db.time_zone_from_region("America/New_York");
local_time ny_time(date(2004, Aug, 30), IS_DST, nyc);
cout &lt;&lt; ny_time.to_long_string() &lt;&lt; endl;
// 2004-Aug-30 00:00:00 Eastern Daylight Time
cout &lt;&lt; ny_time.to_short_string() &lt;&lt; endl;
// 2004-Aug-30 00:00:00 EDT
</programlisting></para><para>NOTE: The exact format/function names may vary - see local_time documentation for further details.</para><para>GMT offset: This is the number of hours added to utc to get the local time before any daylight savings adjustments are made. Some examples are: America/New_York offset -5 hours, &amp; Africa/Cairo offset +2 hours. The format must follow the length-of-time format described above.</para><para>DST adjustment: The amount of time added to gmt_offset when daylight savings is in effect. The format must follow the length-of-time format described above.</para><para>DST Start Date rule: This is a specially formatted string that describes the day of year in which the transition take place. It holds three fields of it's own, separated by semicolons. The first field indicates the "nth" weekday of the month. The possible values are: 1 (first), 2 (second), 3 (third), 4 (fourth), 5 (fifth), and -1 (last). The second field indicates the day-of-week from 0-6 (Sun=0). The third field indicates the month from 1-12 (Jan=1).</para><para>Examples are: "-1;5;9"="Last Friday of September", "2;1;3"="Second Monday of March"</para><para>Start time: Start time is the number of hours past midnight, on the day of the start transition, the transition takes place. More simply put, the time of day the transition is made (in 24 hours format). The format must follow the length-of-time format described above with the exception that it must always be positive.</para><para>DST End date rule: See DST Start date rule. The difference here is this is the day daylight savings ends (transition to STD).</para><para>End time: Same as Start time. </para></description><typedef name="char_type"><type>char</type></typedef>
<typedef name="time_zone_base_type"><type>time_zone_type::base_type</type></typedef>
<typedef name="time_duration_type"><type>time_zone_type::time_duration_type</type></typedef>
<typedef name="time_zone_names"><type><classname>time_zone_names_base</classname>&lt; char_type &gt;</type></typedef>
<typedef name="dst_adjustment_offsets"><type><classname>boost::date_time::dst_adjustment_offsets</classname>&lt; time_duration_type &gt;</type></typedef>
<typedef name="string_type"><type>std::basic_string&lt; char_type &gt;</type></typedef>
<method-group name="public member functions">
<method name="load_from_stream"><type>void</type><parameter name="in"><paramtype>std::istream &amp;</paramtype></parameter><purpose>Process csv data file, may throw exceptions. </purpose><description><para>May throw <classname alt="boost::date_time::bad_field_count">bad_field_count</classname> exceptions </para></description></method>
<method name="load_from_file"><type>void</type><parameter name="pathspec"><paramtype>const std::string &amp;</paramtype></parameter><purpose>Process csv data file, may throw exceptions. </purpose><description><para>May throw <classname alt="boost::date_time::data_not_accessible">data_not_accessible</classname>, or <classname alt="boost::date_time::bad_field_count">bad_field_count</classname> exceptions </para></description></method>
<method name="add_record"><type>bool</type><parameter name="region"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="tz"><paramtype>boost::shared_ptr&lt; time_zone_base_type &gt;</paramtype></parameter><purpose>returns true if record successfully added to map </purpose><description><para>Takes a region name in the form of "America/Phoenix", and a time_zone object for that region. The id string must be a unique name that does not already exist in the database. </para></description></method>
<method name="time_zone_from_region" cv="const"><type>boost::shared_ptr&lt; time_zone_base_type &gt;</type><parameter name="region"><paramtype>const string_type &amp;</paramtype></parameter><purpose>Returns a time_zone object built from the specs for the given region. </purpose><description><para>Returns a time_zone object built from the specs for the given region. If region does not exist a local_time::record_not_found exception will be thrown </para></description></method>
<method name="region_list" cv="const"><type>std::vector&lt; std::string &gt;</type><purpose>Returns a vector of strings holding the time zone regions in the database. </purpose></method>
</method-group>
<constructor><purpose>Constructs an empty database. </purpose></constructor>
<method-group name="private member functions">
<method name="parse_rules" cv="const"><type>rule_type *</type><parameter name="sr"><paramtype>const string_type &amp;</paramtype></parameter><parameter name="er"><paramtype>const string_type &amp;</paramtype></parameter><purpose>parses rule specs for transition day rules </purpose></method>
<method name="get_week_num" cv="const"><type>week_num</type><parameter name="nth"><paramtype>int</paramtype></parameter><purpose>helper function for parse_rules() </purpose></method>
<method name="split_rule_spec" cv="const"><type>void</type><parameter name="nth"><paramtype>int &amp;</paramtype></parameter><parameter name="d"><paramtype>int &amp;</paramtype></parameter><parameter name="m"><paramtype>int &amp;</paramtype></parameter><parameter name="rule"><paramtype>string_type</paramtype></parameter><purpose>splits the [start|end]_date_rule string into 3 ints </purpose></method>
<method name="parse_string"><type>bool</type><parameter name="s"><paramtype>string_type &amp;</paramtype></parameter><purpose>Take a line from the csv, turn it into a time_zone_type. </purpose><description><para>Take a line from the csv, turn it into a time_zone_type, and add it to the map. Zone_specs in csv file are expected to have eleven fields that describe the time zone. Returns true if zone_spec successfully added to database </para></description></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/wrapping_int.hpp">
<namespace name="boost">
<namespace name="date_time">
<class name="wrapping_int"><template>
      <template-type-parameter name="int_type_"/>
      <template-nontype-parameter name="wrap_val"><type>int_type_</type></template-nontype-parameter>
    </template><purpose>A wrapping integer used to support time durations (WARNING: only instantiate with a signed type) </purpose><description><para>In composite date and time types this type is used to wrap at the day boundary. Ex: A wrapping_int&lt;short, 10&gt; will roll over after nine, and roll under below zero. This gives a range of [0,9]</para><para>NOTE: it is strongly recommended that <classname alt="boost::date_time::wrapping_int2">wrapping_int2</classname> be used instead of <classname alt="boost::date_time::wrapping_int">wrapping_int</classname> as <classname alt="boost::date_time::wrapping_int">wrapping_int</classname> is to be depricated at some point soon.</para><para>Also Note that warnings will occur if instantiated with an unsigned type. Only a signed type should be used! </para></description><typedef name="int_type"><type>int_type_</type></typedef>
<method-group name="public static functions">
<method name="wrap_value" specifiers="static"><type>BOOST_CONSTEXPR int_type</type></method>
</method-group>
<method-group name="public member functions">
<method name="as_int" cv="const"><type>BOOST_CONSTEXPR int_type</type><purpose>Explicit converion method. </purpose></method>
<method name="operator int_type" cv="const"><type>BOOST_CONSTEXPR</type></method>
<method name="add"><type>BOOST_CXX14_CONSTEXPR IntT</type><template>
          <template-type-parameter name="IntT"/>
        </template><parameter name="v"><paramtype>IntT</paramtype></parameter><purpose>Add, return number of wraps performed. </purpose><description><para>The sign of the returned value will indicate which direction the wraps went. Ex: add a negative number and wrapping under could occur, this would be indicated by a negative return value. If wrapping over took place, a positive value would be returned </para></description></method>
<method name="subtract"><type>BOOST_CXX14_CONSTEXPR IntT</type><template>
          <template-type-parameter name="IntT"/>
        </template><parameter name="v"><paramtype>IntT</paramtype></parameter><purpose>Subtract will return '+d' if wrapping under took place ('d' is the number of wraps) </purpose><description><para>The sign of the returned value will indicate which direction the wraps went (positive indicates wrap under, negative indicates wrap over). Ex: subtract a negative number and wrapping over could occur, this would be indicated by a negative return value. If wrapping under took place, a positive value would be returned. </para></description></method>
</method-group>
<constructor><parameter name="v"><paramtype>int_type</paramtype></parameter><purpose>Add, return true if wrapped. </purpose></constructor>
<method-group name="private member functions">
<method name="calculate_wrap"><type>BOOST_CXX14_CONSTEXPR IntT</type><template>
          <template-type-parameter name="IntT"/>
        </template><parameter name="wrap"><paramtype>IntT</paramtype></parameter></method>
</method-group>
</class><class name="wrapping_int2"><template>
      <template-type-parameter name="int_type_"/>
      <template-nontype-parameter name="wrap_min"><type>int_type_</type></template-nontype-parameter>
      <template-nontype-parameter name="wrap_max"><type>int_type_</type></template-nontype-parameter>
    </template><purpose>A wrapping integer used to wrap around at the top (WARNING: only instantiate with a signed type) </purpose><description><para>Bad name, quick impl to fix a bug – fix later!! This allows the wrap to restart at a value other than 0. </para></description><typedef name="int_type"><type>int_type_</type></typedef>
<method-group name="public static functions">
<method name="wrap_value" specifiers="static"><type>BOOST_CONSTEXPR int_type</type></method>
<method name="min_value" specifiers="static"><type>BOOST_CONSTEXPR int_type</type></method>
</method-group>
<method-group name="public member functions">
<method name="as_int" cv="const"><type>BOOST_CONSTEXPR int_type</type><purpose>Explicit converion method. </purpose></method>
<method name="operator int_type" cv="const"><type>BOOST_CONSTEXPR</type></method>
<method name="add"><type>BOOST_CXX14_CONSTEXPR IntT</type><template>
          <template-type-parameter name="IntT"/>
        </template><parameter name="v"><paramtype>IntT</paramtype></parameter><purpose>Add, return number of wraps performed. </purpose><description><para>The sign of the returned value will indicate which direction the wraps went. Ex: add a negative number and wrapping under could occur, this would be indicated by a negative return value. If wrapping over took place, a positive value would be returned </para></description></method>
<method name="subtract"><type>BOOST_CXX14_CONSTEXPR IntT</type><template>
          <template-type-parameter name="IntT"/>
        </template><parameter name="v"><paramtype>IntT</paramtype></parameter><purpose>Subtract will return '-d' if wrapping under took place ('d' is the number of wraps) </purpose><description><para>The sign of the returned value will indicate which direction the wraps went. Ex: subtract a negative number and wrapping over could occur, this would be indicated by a positive return value. If wrapping under took place, a negative value would be returned </para></description></method>
</method-group>
<constructor><parameter name="v"><paramtype>int_type</paramtype></parameter><description><para>If initializing value is out of range of [wrap_min, wrap_max], value will be initialized to closest of min or max </para></description></constructor>
<method-group name="private member functions">
<method name="calculate_wrap"><type>BOOST_CXX14_CONSTEXPR IntT</type><template>
          <template-type-parameter name="IntT"/>
        </template><parameter name="wrap"><paramtype>IntT</paramtype></parameter></method>
</method-group>
</class>
































</namespace>
</namespace>
</header>
<header name="boost/date_time/year_month_day.hpp">
<namespace name="boost">
<namespace name="date_time">
<struct name="year_month_day_base"><template>
      <template-type-parameter name="YearType"/>
      <template-type-parameter name="MonthType"/>
      <template-type-parameter name="DayType"/>
    </template><purpose>Allow rapid creation of ymd triples of different types. </purpose><typedef name="year_type"><type>YearType</type></typedef>
<typedef name="month_type"><type>MonthType</type></typedef>
<typedef name="day_type"><type>DayType</type></typedef>
<data-member name="year"><type>YearType</type></data-member>
<data-member name="month"><type>MonthType</type></data-member>
<data-member name="day"><type>DayType</type></data-member>
<method-group name="public member functions">
</method-group>
<constructor><parameter name="year"><paramtype>YearType</paramtype></parameter><parameter name="month"><paramtype>MonthType</paramtype></parameter><parameter name="day"><paramtype>DayType</paramtype></parameter><purpose>A basic constructor. </purpose></constructor>
</struct>
































</namespace>
</namespace>
</header>
</library-reference>