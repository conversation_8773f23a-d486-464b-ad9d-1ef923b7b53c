

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    
    <title>A simple tutorial on Arrays - Boost.Python NumPy extension 1.0 documentation</title>
    <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
    <link rel="stylesheet" href="../_static/style.css" type="text/css" />
    <script type="text/javascript">
      var DOCUMENTATION_OPTIONS = {
          URL_ROOT:    '../',
          VERSION:     '1.0',
          COLLAPSE_MODINDEX: false,
          FILE_SUFFIX: '.html'
      };
    </script>
    <script type="text/javascript" src="../_static/jquery.js"></script>
    <script type="text/javascript" src="../_static/underscore.js"></script>
    <script type="text/javascript" src="../_static/doctools.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="top" title="Boost.Python NumPy extension 1.0 documentation" href="../index.html" />
    <link rel="up" title="Boost.Python NumPy extension Tutorial" href="index.html" />
    <link rel="next" title="How to use dtypes" href="dtype.html" />
    <link rel="prev" title="Boost.Python NumPy extension Tutorial" href="index.html" /> 
  </head>
  <body>
    <div class="header">
    <table border="0" cellpadding="7" cellspacing="0" width="100%" summary=
    "header">
      <tr>
        <td valign="top" width="300">
          <h3><a href="../index.html"><img 
          alt="C++ Boost" src="../_static/bpl.png" border="0"></a></h3>
        </td>

        <td >
          <h1 align="center"><a href="../index.html">(NumPy)</a></h1>
<!--          <h2 align="center">CallPolicies Concept</h2>-->
        </td>
	<td>
      <div id="searchbox" style="display: none">
        <form class="search" action="../search.html" method="get">
          <input type="text" name="q" size="18" />
          <input type="submit" value="Search" />
          <input type="hidden" name="check_keywords" value="yes" />
          <input type="hidden" name="area" value="default" />
        </form>
      </div>
      <script type="text/javascript">$('#searchbox').show(0);</script>
	</td>
      </tr>
    </table>
    </div>
    <hr/>
    <div class="content">
    <div class="navbar" style="text-align:right;">
      
      
      <a class="prev" title="Boost.Python NumPy extension Tutorial" href="index.html"><img src="../_static/prev.png" alt="prev"/></a>
      <a class="up" title="Boost.Python NumPy extension Tutorial" href="index.html"><img src="../_static/up.png" alt="up"/></a>
      <a class="next" title="How to use dtypes" href="dtype.html"><img src="../_static/next.png" alt="next"/></a>
      
    </div>
      
  <div class="section" id="a-simple-tutorial-on-arrays">
<h1>A simple tutorial on Arrays</h1>
<p>Let&#8217;s start with a simple tutorial to create and modify arrays.</p>
<p>Get the necessary headers for numpy components and set up necessary namespaces:</p>
<div class="highlight-c++"><div class="highlight"><pre><span></span><span class="cp">#include</span> <span class="cpf">&lt;boost/python/numpy.hpp&gt;</span><span class="cp"></span>
<span class="cp">#include</span> <span class="cpf">&lt;iostream&gt;</span><span class="cp"></span>

<span class="k">namespace</span> <span class="n">p</span> <span class="o">=</span> <span class="n">boost</span><span class="o">::</span><span class="n">python</span><span class="p">;</span>
<span class="k">namespace</span> <span class="n">np</span> <span class="o">=</span> <span class="n">boost</span><span class="o">::</span><span class="n">python</span><span class="o">::</span><span class="n">numpy</span><span class="p">;</span>
</pre></div>
</div>
<p>Initialise the Python runtime, and the numpy module. Failure to call these results in segmentation errors:</p>
<div class="highlight-c++"><div class="highlight"><pre><span></span><span class="kt">int</span> <span class="nf">main</span><span class="p">(</span><span class="kt">int</span> <span class="n">argc</span><span class="p">,</span> <span class="kt">char</span> <span class="o">**</span><span class="n">argv</span><span class="p">)</span>
<span class="p">{</span>
  <span class="n">Py_Initialize</span><span class="p">();</span>
  <span class="n">np</span><span class="o">::</span><span class="n">initialize</span><span class="p">();</span>
</pre></div>
</div>
<p>Zero filled n-dimensional arrays can be created using the shape and data-type of the array as a parameter. Here, the shape is 3x3 and the datatype is the built-in float type:</p>
<div class="highlight-c++"><div class="highlight"><pre><span></span><span class="n">p</span><span class="o">::</span><span class="n">tuple</span> <span class="n">shape</span> <span class="o">=</span> <span class="n">p</span><span class="o">::</span><span class="n">make_tuple</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">3</span><span class="p">);</span>
<span class="n">np</span><span class="o">::</span><span class="n">dtype</span> <span class="n">dtype</span> <span class="o">=</span> <span class="n">np</span><span class="o">::</span><span class="n">dtype</span><span class="o">::</span><span class="n">get_builtin</span><span class="o">&lt;</span><span class="kt">float</span><span class="o">&gt;</span><span class="p">();</span>
<span class="n">np</span><span class="o">::</span><span class="n">ndarray</span> <span class="n">a</span> <span class="o">=</span> <span class="n">np</span><span class="o">::</span><span class="n">zeros</span><span class="p">(</span><span class="n">shape</span><span class="p">,</span> <span class="n">dtype</span><span class="p">);</span>
</pre></div>
</div>
<p>You can also create an empty array like this</p>
<div class="highlight-c++"><div class="highlight"><pre><span></span><span class="n">np</span><span class="o">::</span><span class="n">ndarray</span> <span class="n">b</span> <span class="o">=</span> <span class="n">np</span><span class="o">::</span><span class="n">empty</span><span class="p">(</span><span class="n">shape</span><span class="p">,</span><span class="n">dtype</span><span class="p">);</span>
</pre></div>
</div>
<p>Print the original and reshaped array. The array a which is a list is first converted to a string, and each value in the list is extracted using extract&lt; &gt;:</p>
<div class="highlight-c++"><div class="highlight"><pre><span></span>  <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">&quot;Original array:</span><span class="se">\n</span><span class="s">&quot;</span> <span class="o">&lt;&lt;</span> <span class="n">p</span><span class="o">::</span><span class="n">extract</span><span class="o">&lt;</span><span class="kt">char</span> <span class="k">const</span> <span class="o">*&gt;</span><span class="p">(</span><span class="n">p</span><span class="o">::</span><span class="n">str</span><span class="p">(</span><span class="n">a</span><span class="p">))</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>

  <span class="c1">// Reshape the array into a 1D array</span>
  <span class="n">a</span> <span class="o">=</span> <span class="n">a</span><span class="p">.</span><span class="n">reshape</span><span class="p">(</span><span class="n">p</span><span class="o">::</span><span class="n">make_tuple</span><span class="p">(</span><span class="mi">9</span><span class="p">));</span>
  <span class="c1">// Print it again.</span>
  <span class="n">std</span><span class="o">::</span><span class="n">cout</span> <span class="o">&lt;&lt;</span> <span class="s">&quot;Reshaped array:</span><span class="se">\n</span><span class="s">&quot;</span> <span class="o">&lt;&lt;</span> <span class="n">p</span><span class="o">::</span><span class="n">extract</span><span class="o">&lt;</span><span class="kt">char</span> <span class="k">const</span> <span class="o">*&gt;</span><span class="p">(</span><span class="n">p</span><span class="o">::</span><span class="n">str</span><span class="p">(</span><span class="n">a</span><span class="p">))</span> <span class="o">&lt;&lt;</span> <span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</div>


    <div class="navbar" style="text-align:right;">
      
      
      <a class="prev" title="Boost.Python NumPy extension Tutorial" href="index.html"><img src="../_static/prev.png" alt="prev"/></a>
      <a class="up" title="Boost.Python NumPy extension Tutorial" href="index.html"><img src="../_static/up.png" alt="up"/></a>
      <a class="next" title="How to use dtypes" href="dtype.html"><img src="../_static/next.png" alt="next"/></a>
      
    </div>
    </div>
  </body>
</html>