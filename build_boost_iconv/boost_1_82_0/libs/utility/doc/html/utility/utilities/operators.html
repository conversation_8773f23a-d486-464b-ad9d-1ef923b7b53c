<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Operators</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="Chapter 1. Boost.Utility">
<link rel="up" href="../utilities.html" title="Utilities">
<link rel="prev" href="../../boost/typed_in_place_fac_idm4015.html" title="Class typed_in_place_factory_base">
<link rel="next" href="result_of.html" title="Result of">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../boost/typed_in_place_fac_idm4015.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../utilities.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="result_of.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="utility.utilities.operators"></a><a name="sec:operators"></a><a class="link" href="operators.html" title="Operators">Operators</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="operators.html#utility.utilities.operators.introduction">Introduction</a></span></dt>
<dt><span class="section"><a href="operators.html#utility.utilities.operators.rationale">Rationale</a></span></dt>
<dt><span class="section"><a href="operators.html#utility.utilities.operators.example">Example</a></span></dt>
<dt><span class="section"><a href="operators.html#utility.utilities.operators.usage">Usage</a></span></dt>
<dt><span class="section"><a href="operators.html#utility.utilities.operators.arithmetic_operators">Arithmetic
        Operators</a></span></dt>
<dt><span class="section"><a href="operators.html#utility.utilities.operators.dereference_operators_and_iterat">Dereference
        Operators and Iterator Helpers</a></span></dt>
<dt><span class="section"><a href="operators.html#utility.utilities.operators.note_for_users_of_older_versions">Note
        for Users of Older Versions</a></span></dt>
<dt><span class="section"><a href="operators.html#utility.utilities.operators.acknowledgments">Acknowledgments</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="utility.utilities.operators.introduction"></a><a class="link" href="operators.html#utility.utilities.operators.introduction" title="Introduction">Introduction</a>
</h4></div></div></div>
<p>
          The header <a href="../../../../../../boost/operators.hpp" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">operators</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code></a>
          supplies several sets of class templates in <code class="computeroutput"><span class="keyword">namespace</span>
          <span class="identifier">boost</span></code>. These templates define
          operators at namespace scope in terms of a minimal number of fundamental
          operators provided by the class.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="utility.utilities.operators.rationale"></a><a name="sec:rationale"></a><a class="link" href="operators.html#utility.utilities.operators.rationale" title="Rationale">Rationale</a>
</h4></div></div></div>
<p>
          Overloaded operators for class types typically occur in groups. If you
          can write <code class="computeroutput"><span class="identifier">x</span> <span class="special">+</span>
          <span class="identifier">y</span></code>, you probably also want to
          be able to write <code class="computeroutput"><span class="identifier">x</span> <span class="special">+=</span>
          <span class="identifier">y</span></code>. If you can write <code class="computeroutput"><span class="identifier">x</span> <span class="special">&lt;</span> <span class="identifier">y</span><span class="special">,</span></code> you
          also want <code class="computeroutput"><span class="identifier">x</span> <span class="special">&gt;</span>
          <span class="identifier">y</span></code>, <code class="computeroutput"><span class="identifier">x</span>
          <span class="special">&gt;=</span> <span class="identifier">y</span><span class="special">,</span></code> and <code class="computeroutput"><span class="identifier">x</span>
          <span class="special">&lt;=</span> <span class="identifier">y</span></code>.
        </p>
<p>
          Moreover, unless your class has really surprising behavior, some of these
          related operators can be defined in terms of others (e.g. <code class="computeroutput"><span class="identifier">x</span> <span class="special">&gt;=</span> <span class="identifier">y</span></code> is equivalent to <code class="computeroutput"><span class="special">!(</span><span class="identifier">x</span> <span class="special">&lt;</span> <span class="identifier">y</span><span class="special">)</span></code>).
        </p>
<p>
          Replicating this boilerplate for multiple classes is both tedious and error-prone.
          The <a href="../../../../../../boost/operators.hpp" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">operators</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span></code></a>
          templates help by generating operators for you at namespace scope based
          on other operators you have defined in your class.
        </p>
<p>
          If, for example, you declare a class like this:
        </p>
<pre class="programlisting"><span class="keyword">class</span> <span class="identifier">MyInt</span>
    <span class="special">:</span> <span class="identifier">boost</span><span class="special">::</span><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">operators</span></code></a><span class="special">&lt;</span><span class="identifier">MyInt</span><span class="special">&gt;</span>
<span class="special">{</span>
    <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;(</span><span class="keyword">const</span> <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span><span class="keyword">const</span> <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">+=(</span><span class="keyword">const</span> <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">-=(</span><span class="keyword">const</span> <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">*=(</span><span class="keyword">const</span> <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">/=(</span><span class="keyword">const</span> <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">%=(</span><span class="keyword">const</span> <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">|=(</span><span class="keyword">const</span> <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">&amp;=(</span><span class="keyword">const</span> <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">^=(</span><span class="keyword">const</span> <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">++();</span>
    <span class="identifier">MyInt</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">--();</span>
<span class="special">};</span>
</pre>
<p>
          then the <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">operators</span></code></a>&lt;&gt;
          template adds more than a dozen additional operators, such as <code class="computeroutput"><span class="keyword">operator</span><span class="special">&gt;</span></code>,
          <code class="computeroutput"><span class="keyword">operator</span><span class="special">&lt;=</span></code>,
          <code class="computeroutput"><span class="keyword">operator</span><span class="special">&gt;=</span></code>,
          and the binary <code class="computeroutput"><span class="keyword">operator</span><span class="special">+</span></code>.
        </p>
<p>
          <a class="link" href="operators.html#sec:two_arg">Two-argument forms</a> of the templates
          are also provided to allow interaction with other types.
        </p>
<p>
          <a name="sec:semantics"></a>This is a <span class="emphasis"><em>Summary of Template Semantics</em></span>:
        </p>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
              Each operator template completes the concept(s) it describes by defining
              overloaded operators for its target class.
            </li>
<li class="listitem">
              The name of an operator class template indicates the <a class="link" href="operators.html#sec:concepts_note">concept</a>
              that its target class will model.
            </li>
<li class="listitem">
              Usually, the target class uses an instantiation of the operator class
              template as a base class. Some operator templates support an <a class="link" href="operators.html#sec:explicit_instantiation">alternate method</a>.
            </li>
<li class="listitem">
              The concept can be compound, i.e. it may represent a common combination
              of other, simpler concepts.
            </li>
<li class="listitem">
              Most operator templates require their target class to support operations
              related to the operators supplied by the template. In accordance with
              widely accepted <a href="http://www.gotw.ca/gotw/004.htm" target="_top">coding
              style recommendations</a>, the target class is often required to
              supply the assignment counterpart operator of the concept's "main
              operator." For example, the <code class="computeroutput"><span class="identifier">addable</span></code>
              template requires <code class="computeroutput"><span class="keyword">operator</span><span class="special">+=(</span><span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;)</span></code>
              and in turn supplies <code class="computeroutput"><span class="keyword">operator</span><span class="special">+(</span><span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;,</span>
              <span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;)</span></code>.
            </li>
</ol></div>
<p>
          <a name="sec:concepts_note"></a><span class="emphasis"><em>Note on the use of concepts</em></span>:
          The discussed concepts are not necessarily the standard library's concepts,
          such as <a href="https://en.cppreference.com/w/cpp/named_req/CopyConstructible" target="_top"><span class="emphasis"><em>CopyConstructible</em></span></a>,
          although some of them could be; they are what we call <span class="emphasis"><em>concepts
          with a small 'c'</em></span>. In particular, they are different from the
          former ones in that they <span class="emphasis"><em>do not</em></span> describe precise semantics
          of the operators they require to be defined, except the requirements that
          (a) the semantics of the operators grouped in one concept should be consistent
          (e.g. effects of evaluating of <code class="computeroutput"><span class="identifier">a</span>
          <span class="special">+=</span> <span class="identifier">b</span></code>
          and <code class="computeroutput"><span class="identifier">a</span> <span class="special">=</span>
          <span class="identifier">a</span> <span class="special">+</span>
          <span class="identifier">b</span></code> expressions should be the same),
          and (b) that the return types of the operators should follow semantics
          of return types of corresponding operators for built-in types (e.g. <code class="computeroutput"><span class="keyword">operator</span><span class="special">&lt;</span></code>
          should return a type convertible to <code class="computeroutput"><span class="keyword">bool</span></code>,
          and <code class="computeroutput"><span class="identifier">T</span><span class="special">::</span><span class="keyword">operator</span><span class="special">-=</span></code>
          should return type convertible to <code class="computeroutput"><span class="identifier">T</span></code>).
          Such "loose" requirements make <code class="computeroutput"><span class="identifier">operators</span></code>
          library applicable to broader set of target classes from different domains,
          i.e. eventually more useful.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="utility.utilities.operators.example"></a><a name="sec:example"></a><a class="link" href="operators.html#utility.utilities.operators.example" title="Example">Example</a>
</h4></div></div></div>
<p>
          This example shows how some of the <a class="link" href="operators.html#sec:arithmetic">arithmetic
          operator templates</a> can be used with a geometric point class template.
        </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">point</span>    <span class="comment">// note: private inheritance is OK here!</span>
    <span class="special">:</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">addable</span><span class="special">&lt;</span> <span class="identifier">point</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span>          <span class="comment">// point + point</span>
    <span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">subtractable</span><span class="special">&lt;</span> <span class="identifier">point</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span>     <span class="comment">// point - point</span>
    <span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">dividable2</span><span class="special">&lt;</span> <span class="identifier">point</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;,</span> <span class="identifier">T</span>    <span class="comment">// point / T</span>
    <span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">multipliable2</span><span class="special">&lt;</span> <span class="identifier">point</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;,</span> <span class="identifier">T</span> <span class="comment">// point * T, T * point</span>
      <span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
    <span class="identifier">point</span><span class="special">(</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">T</span><span class="special">);</span>
    <span class="identifier">T</span> <span class="identifier">x</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="identifier">T</span> <span class="identifier">y</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="identifier">point</span> <span class="keyword">operator</span><span class="special">+=(</span><span class="keyword">const</span> <span class="identifier">point</span><span class="special">&amp;);</span>
    <span class="comment">// point operator+(point, const point&amp;) automatically</span>
    <span class="comment">// generated by addable.</span>

    <span class="identifier">point</span> <span class="keyword">operator</span><span class="special">-=(</span><span class="keyword">const</span> <span class="identifier">point</span><span class="special">&amp;);</span>
    <span class="comment">// point operator-(point, const point&amp;) automatically</span>
    <span class="comment">// generated by subtractable.</span>

    <span class="identifier">point</span> <span class="keyword">operator</span><span class="special">*=(</span><span class="identifier">T</span><span class="special">);</span>
    <span class="comment">// point operator*(point, const T&amp;) and</span>
    <span class="comment">// point operator*(const T&amp;, point) auto-generated</span>
    <span class="comment">// by multipliable.</span>

    <span class="identifier">point</span> <span class="keyword">operator</span><span class="special">/=(</span><span class="identifier">T</span><span class="special">);</span>
    <span class="comment">// point operator/(point, const T&amp;) auto-generated</span>
    <span class="comment">// by dividable.</span>
<span class="keyword">private</span><span class="special">:</span>
    <span class="identifier">T</span> <span class="identifier">x_</span><span class="special">;</span>
    <span class="identifier">T</span> <span class="identifier">y_</span><span class="special">;</span>
<span class="special">};</span>

<span class="comment">// now use the point&lt;&gt; class:</span>
<span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">T</span> <span class="identifier">length</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">point</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">p</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">return</span> <span class="identifier">sqrt</span><span class="special">(</span><span class="identifier">p</span><span class="special">.</span><span class="identifier">x</span><span class="special">()*</span><span class="identifier">p</span><span class="special">.</span><span class="identifier">x</span><span class="special">()</span> <span class="special">+</span> <span class="identifier">p</span><span class="special">.</span><span class="identifier">y</span><span class="special">()*</span><span class="identifier">p</span><span class="special">.</span><span class="identifier">y</span><span class="special">());</span>
<span class="special">}</span>

<span class="keyword">const</span> <span class="identifier">point</span><span class="special">&lt;</span><span class="keyword">float</span><span class="special">&gt;</span> <span class="identifier">right</span><span class="special">(</span><span class="number">0</span><span class="special">,</span> <span class="number">1</span><span class="special">);</span>
<span class="keyword">const</span> <span class="identifier">point</span><span class="special">&lt;</span><span class="keyword">float</span><span class="special">&gt;</span> <span class="identifier">up</span><span class="special">(</span><span class="number">1</span><span class="special">,</span> <span class="number">0</span><span class="special">);</span>
<span class="keyword">const</span> <span class="identifier">point</span><span class="special">&lt;</span><span class="keyword">float</span><span class="special">&gt;</span> <span class="identifier">pi_over_4</span> <span class="special">=</span> <span class="identifier">up</span> <span class="special">+</span> <span class="identifier">right</span><span class="special">;</span>
<span class="keyword">const</span> <span class="identifier">point</span><span class="special">&lt;</span><span class="keyword">float</span><span class="special">&gt;</span> <span class="identifier">pi_over_4_normalized</span> <span class="special">=</span> <span class="identifier">pi_over_4</span> <span class="special">/</span> <span class="identifier">length</span><span class="special">(</span><span class="identifier">pi_over_4</span><span class="special">);</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="utility.utilities.operators.usage"></a><a name="sec:usage"></a><a class="link" href="operators.html#utility.utilities.operators.usage" title="Usage">Usage</a>
</h4></div></div></div>
<a name="sec:two_arg"></a><h6>
<a name="utility.utilities.operators.usage.h0"></a>
          <span class="phrase"><a name="utility.utilities.operators.usage.two_argument_template_forms"></a></span><a class="link" href="operators.html#utility.utilities.operators.usage.two_argument_template_forms">Two-Argument
          Template Forms</a>
        </h6>
<p>
          <a name="sec:two_arg_gen"></a>The arguments to a binary operator commonly
          have identical types, but it is not unusual to want to define operators
          which combine different types. For <a class="link" href="operators.html#sec:example">example</a>,
          one might want to multiply a mathematical vector by a scalar. The two-argument
          template forms of the arithmetic operator templates are supplied for this
          purpose. When applying the two-argument form of a template, the desired
          return type of the operators typically determines which of the two types
          in question should be derived from the operator template.
        </p>
<p>
          For example, if the result of <code class="computeroutput"><span class="identifier">T</span>
          <span class="special">+</span> <span class="identifier">U</span></code>
          is of type <code class="computeroutput"><span class="identifier">T</span></code>, then <code class="computeroutput"><span class="identifier">T</span></code> (not <code class="computeroutput"><span class="identifier">U</span></code>)
          should be derived from <a class="link" href="operators.html#table:addable2"><code class="computeroutput"><span class="identifier">addable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a>.
          The comparison templates <a class="link" href="operators.html#table:less_than_comparable2"><code class="computeroutput"><span class="identifier">less_than_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>, <a class="link" href="operators.html#table:equality_comparable2"><code class="computeroutput"><span class="identifier">equality_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a>, <a class="link" href="operators.html#table:equivalent2"><code class="computeroutput"><span class="identifier">equivalent</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a>, and <a class="link" href="operators.html#table:partially_ordered2"><code class="computeroutput"><span class="identifier">partially_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a> are exceptions to this guideline,
          since the return type of the operators they define is <code class="computeroutput"><span class="keyword">bool</span></code>.
        </p>
<p>
          On compilers which do not support partial specialization, the two-argument
          forms must be specified by using the names shown below with the trailing
          <code class="computeroutput"><span class="char">'2'</span></code>. The single-argument forms
          with the trailing <code class="computeroutput"><span class="char">'1'</span></code> are provided
          for symmetry and to enable certain applications of the <a class="link" href="operators.html#sec:chaining">base
          class chaining</a> technique.
        </p>
<p>
          <a name="sec:mixed_arithmetics"></a><span class="emphasis"><em>Mixed Arithmetics</em></span>:
          Another application of the two-argument template forms is for mixed arithmetics
          between a type <code class="computeroutput"><span class="identifier">T</span></code> and a
          type <code class="computeroutput"><span class="identifier">U</span></code> that is convertible
          to <code class="computeroutput"><span class="identifier">T</span></code>. In this case there
          are two ways where the two-argument template forms are helpful: one is
          to provide the respective signatures for operator overloading, the second
          is performance.
        </p>
<p>
          With respect to the operator overloading assume e.g. that <code class="computeroutput"><span class="identifier">U</span></code> is <code class="computeroutput"><span class="keyword">int</span></code>,
          that <code class="computeroutput"><span class="identifier">T</span></code> is an user-defined
          unlimited integer type, and that <code class="computeroutput"><span class="keyword">double</span>
          <span class="keyword">operator</span><span class="special">-(</span><span class="keyword">double</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code> exists.
        </p>
<p>
          If one wants to compute <code class="computeroutput"><span class="keyword">int</span> <span class="special">-</span> <span class="identifier">T</span></code> and
          does not provide <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">-(</span><span class="keyword">int</span><span class="special">,</span>
          <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>, the compiler will consider <code class="computeroutput"><span class="keyword">double</span> <span class="keyword">operator</span><span class="special">-(</span><span class="keyword">double</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
          to be a better match than <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">-(</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>,
          which will probably be different from the user's intention.
        </p>
<p>
          To define a complete set of operator signatures, additional 'left' forms
          of the two-argument template forms are provided <a class="link" href="operators.html#table:subtractable2_left"><code class="computeroutput"><span class="identifier">subtractable2_left</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a>, <a class="link" href="operators.html#table:dividable2_left"><code class="computeroutput"><span class="identifier">dividable2_left</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a>, and <a class="link" href="operators.html#table:modable2_left"><code class="computeroutput"><span class="identifier">modable2_left</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a> that define the signatures for
          non-commutative operators where <code class="computeroutput"><span class="identifier">U</span></code>
          appears on the left hand side (<code class="computeroutput"><span class="keyword">operator</span><span class="special">-(</span><span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>, <code class="computeroutput"><span class="keyword">operator</span><span class="special">/(</span><span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>, <code class="computeroutput"><span class="keyword">operator</span><span class="special">%(</span><span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>).
        </p>
<p>
          With respect to the performance observe that when one uses the single type
          binary operator for mixed type arithmetics, the type <code class="computeroutput"><span class="identifier">U</span></code>
          argument has to be converted to type <code class="computeroutput"><span class="identifier">T</span></code>.
          In practice, however, there are often more efficient implementations of,
          say <code class="computeroutput"><span class="identifier">T</span><span class="special">::</span><span class="keyword">operator</span><span class="special">-=(</span><span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code> that avoid unnecessary conversions
          from <code class="computeroutput"><span class="identifier">U</span></code> to <code class="computeroutput"><span class="identifier">T</span></code>.
        </p>
<p>
          The two-argument template forms of the arithmetic operator create additional
          operator interfaces that use these more efficient implementations. There
          is, however, no performance gain in the 'left' forms: they still need a
          conversion from <code class="computeroutput"><span class="identifier">U</span></code> to <code class="computeroutput"><span class="identifier">T</span></code> and have an implementation equivalent
          to the code that would be automatically created by the compiler if it considered
          the single type binary operator to be the best match.
        </p>
<a name="sec:chaining"></a><h6>
<a name="utility.utilities.operators.usage.h1"></a>
          <span class="phrase"><a name="utility.utilities.operators.usage.base_class_chaining_and_object_s"></a></span><a class="link" href="operators.html#utility.utilities.operators.usage.base_class_chaining_and_object_s">Base
          Class Chaining and Object Size</a>
        </h6>
<p>
          Every operator class template, except the <a class="link" href="operators.html#sec:ex_oprs">arithmetic
          examples</a> and the <a class="link" href="operators.html#sec:iterator">iterator helpers</a>,
          has an additional, but optional, template type parameter <code class="computeroutput"><span class="identifier">B</span></code>. This parameter will be a publicly-derived
          base class of the instantiated template. This means it must be a class
          type. It can be used to avoid the bloating of object sizes that is commonly
          associated with multiple-inheritance from several empty base classes. See
          the <a class="link" href="operators.html#sec:old_lib_note">note for users of older versions</a>
          for more details.
        </p>
<p>
          To provide support for a group of operators, use the <code class="computeroutput"><span class="identifier">B</span></code>
          parameter to chain operator templates into a single-base class hierarchy,
          demostrated in the <a class="link" href="operators.html#sec:example">usage example</a>. The
          technique is also used by the composite operator templates to group operator
          definitions. If a chain becomes too long for the compiler to support, try
          replacing some of the operator templates with a single grouped operator
          template that chains the old templates together; the length limit only
          applies to the number of templates directly in the chain, not those hidden
          in group templates.
        </p>
<p>
          <span class="emphasis"><em>Caveat</em></span>: to chain to a base class which is <span class="emphasis"><em>not</em></span>
          a Boost operator template when using the <a class="link" href="operators.html#sec:two_arg">single-argument
          form</a> of a Boost operator template, you must specify the operator
          template with the trailing <code class="computeroutput"><span class="char">'1'</span></code>
          in its name. Otherwise the library will assume you mean to define a binary
          operation combining the class you intend to use as a base class and the
          class you're deriving.
        </p>
<a name="sec:explicit_instantiation"></a><h6>
<a name="utility.utilities.operators.usage.h2"></a>
          <span class="phrase"><a name="utility.utilities.operators.usage.separate_explicit_instantiation"></a></span><a class="link" href="operators.html#utility.utilities.operators.usage.separate_explicit_instantiation">Separate
          Explicit Instantiation</a>
        </h6>
<p>
          On some compilers (e.g. Borland, GCC) even single-inheritance seems to
          cause an increase in object size in some cases. If you are not defining
          a class template, you may get better object-size performance by avoiding
          derivation altogether, and instead explicitly instantiating the operator
          template as follows:
        </p>
<pre class="programlisting"><span class="keyword">class</span> <span class="identifier">my_class</span> <span class="comment">// lose the inheritance...</span>
<span class="special">{</span>
    <span class="comment">//...</span>
<span class="special">};</span>

<span class="comment">// explicitly instantiate the operators I need.</span>
<span class="keyword">template</span> <span class="keyword">struct</span> <span class="identifier">less_than_comparable</span><span class="special">&lt;</span><span class="identifier">my_class</span><span class="special">&gt;;</span>
<span class="keyword">template</span> <span class="keyword">struct</span> <span class="identifier">equality_comparable</span><span class="special">&lt;</span><span class="identifier">my_class</span><span class="special">&gt;;</span>
<span class="keyword">template</span> <span class="keyword">struct</span> <span class="identifier">incrementable</span><span class="special">&lt;</span><span class="identifier">my_class</span><span class="special">&gt;;</span>
<span class="keyword">template</span> <span class="keyword">struct</span> <span class="identifier">decrementable</span><span class="special">&lt;</span><span class="identifier">my_class</span><span class="special">&gt;;</span>
<span class="keyword">template</span> <span class="keyword">struct</span> <span class="identifier">addable</span><span class="special">&lt;</span><span class="identifier">my_class</span><span class="special">,</span><span class="keyword">long</span><span class="special">&gt;;</span>
<span class="keyword">template</span> <span class="keyword">struct</span> <span class="identifier">subtractable</span><span class="special">&lt;</span><span class="identifier">my_class</span><span class="special">,</span><span class="keyword">long</span><span class="special">&gt;;</span>
</pre>
<p>
          Note that some operator templates cannot use this workaround and must be
          a base class of their primary operand type. Those templates define operators
          which must be member functions, and the workaround needs the operators
          to be independent <code class="computeroutput"><span class="keyword">friend</span></code> functions.
          The relevant templates are:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              <a class="link" href="operators.html#table:dereferenceable"><code class="computeroutput"><span class="identifier">dereferenceable</span><span class="special">&lt;&gt;</span></code></a>
            </li>
<li class="listitem">
              <a class="link" href="operators.html#table:indexable"><code class="computeroutput"><span class="identifier">indexable</span><span class="special">&lt;&gt;</span></code></a>
            </li>
<li class="listitem">
              Any composite operator template that includes at least one of the above
            </li>
</ul></div>
<p>
          As Daniel Krugler pointed out, this technique violates C++11 §14.6.5/2 [temp.inject]
          and is thus non-portable. The reasoning is, that the operators injected
          by the instantiation of e.g. <code class="computeroutput"><span class="identifier">less_than_comparable</span><span class="special">&lt;</span><span class="identifier">my_class</span><span class="special">&gt;</span></code> can not be found by ADL according to
          the rules given by C++11 §3.4.2/2 [basic.lookup.argdep], since <code class="computeroutput"><span class="identifier">my_class</span></code> is not an associated class of
          <code class="computeroutput"><span class="identifier">less_than_comparable</span><span class="special">&lt;</span><span class="identifier">my_class</span><span class="special">&gt;</span></code>.
          Thus only use this technique if all else fails.
        </p>
<a name="sec:portability"></a><h6>
<a name="utility.utilities.operators.usage.h3"></a>
          <span class="phrase"><a name="utility.utilities.operators.usage.requirement_portability"></a></span><a class="link" href="operators.html#utility.utilities.operators.usage.requirement_portability">Requirement
          Portability</a>
        </h6>
<p>
          Many compilers (e.g. MSVC 6.3, GCC 2.95.2) will not enforce the requirements
          in the operator template tables unless the operations which depend on them
          are actually used. This is not standard-conforming behavior. In particular,
          although it would be convenient to derive all your classes which need binary
          operators from the <a class="link" href="operators.html#table:operators1"><code class="computeroutput"><span class="identifier">operators</span><span class="special">&lt;&gt;</span></code></a> and <a class="link" href="operators.html#table:operators2"><code class="computeroutput"><span class="identifier">operators2</span><span class="special">&lt;&gt;</span></code></a>
          templates, regardless of whether they implement all the requirements of
          those templates, this shortcut is not portable. Even if this currently
          works with your compiler, it may not work later.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="utility.utilities.operators.arithmetic_operators"></a><a name="sec:arithmetic"></a><a class="link" href="operators.html#utility.utilities.operators.arithmetic_operators" title="Arithmetic Operators">Arithmetic
        Operators</a>
</h4></div></div></div>
<p>
          The arithmetic operator templates ease the task of creating a custom numeric
          type. Given a core set of operators, the templates add related operators
          to the numeric class. These operations are like the ones the standard arithmetic
          types have, and may include comparisons, adding, incrementing, logical
          and bitwise manipulations, etc. Further, since most numeric types need
          more than one of these operators, some templates are provided to combine
          several of the basic operator templates in one declaration.
        </p>
<p>
          The requirements for the types used to instantiate the simple operator
          templates are specified in terms of expressions which must be valid and
          the expression's return type. The composite operator templates only list
          what other templates they use. The supplied operations and requirements
          of the composite operator templates can be inferred from the operations
          and requirements of the listed components.
        </p>
<a name="sec:smpl_oprs"></a><h6>
<a name="utility.utilities.operators.arithmetic_operators.h0"></a>
          <span class="phrase"><a name="utility.utilities.operators.arithmetic_operators.simple_arithmetic_operators"></a></span><a class="link" href="operators.html#utility.utilities.operators.arithmetic_operators.simple_arithmetic_operators">Simple
          Arithmetic Operators</a>
        </h6>
<p>
          These templates are "simple" since they provide operators based
          on a single operation the base type has to provide. They have an additional
          optional template parameter <code class="computeroutput"><span class="identifier">B</span></code>,
          which is not shown, for the <a class="link" href="operators.html#sec:chaining">base class chaining</a>
          technique.
        </p>
<p>
          The primary operand type <code class="computeroutput"><span class="identifier">T</span></code>
          needs to be of class type, built-in types are not supported.
        </p>
<div class="table">
<a name="utility.utilities.operators.arithmetic_operators.notation"></a><p class="title"><b>Table 1.6. Notation</b></p>
<div class="table-contents"><table class="table" summary="Notation">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Key
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span></code>
                  </p>
                </td>
<td>
                  <p>
                    primary operand type
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">t</span><span class="special">,</span><span class="identifier">t1</span></code>
                  </p>
                </td>
<td>
                  <p>
                    values of type <code class="computeroutput"><span class="identifier">T</span></code>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">U</span></code>
                  </p>
                </td>
<td>
                  <p>
                    alternate operand type
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">u</span></code>
                  </p>
                </td>
<td>
                  <p>
                    value of type <code class="computeroutput"><span class="identifier">U</span></code>
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="utility.utilities.operators.arithmetic_operators.simple_arithmetic_operator_templ"></a><p class="title"><b>Table 1.7. Simple Arithmetic Operator Template Classes</b></p>
<div class="table-contents"><table class="table" summary="Simple Arithmetic Operator Template Classes">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Template
                  </p>
                </th>
<th>
                  <p>
                    Supplied Operations
                  </p>
                </th>
<th>
                  <p>
                    Requirements
                  </p>
                </th>
<th>
                  <p>
                    Propagates constexpr
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <a name="table:less_than_comparable1"></a><code class="computeroutput"><span class="identifier">less_than_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">less_than_comparable1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">t</span> <span class="special">&lt;</span>
                    <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="keyword">bool</span></code>.
                    See the <a class="link" href="operators.html#sec:ordering">Ordering Note</a>
                  </p>
                </td>
<td>
                  <p>
                    Since <code class="computeroutput"><span class="identifier">C</span><span class="special">++</span><span class="number">11</span></code>, except <a href="https://developercommunity.visualstudio.com/content/problem/414193/rejects-valid-constexpr-marked-friend-function-def.html" target="_top">MSVC
                    &lt; v19.22</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:less_than_comparable2"></a><code class="computeroutput"><span class="identifier">less_than_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">less_than_comparable2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">t</span> <span class="special">&lt;</span>
                    <span class="identifier">u</span></code>. <code class="computeroutput"><span class="identifier">t</span>
                    <span class="special">&gt;</span> <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Returns convertible to <code class="computeroutput"><span class="keyword">bool</span></code>.
                    See the <a class="link" href="operators.html#sec:ordering">Ordering Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    Since <code class="computeroutput"><span class="identifier">C</span><span class="special">++</span><span class="number">11</span></code>, except <a href="https://developercommunity.visualstudio.com/content/problem/414193/rejects-valid-constexpr-marked-friend-function-def.html" target="_top">MSVC
                    &lt; v19.22</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:equality_comparable1"></a><code class="computeroutput"><span class="identifier">equality_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">equality_comparable1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">t</span> <span class="special">==</span>
                    <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="keyword">bool</span></code>.
                  </p>
                </td>
<td>
                  <p>
                    Since <code class="computeroutput"><span class="identifier">C</span><span class="special">++</span><span class="number">11</span></code>, except <a href="https://developercommunity.visualstudio.com/content/problem/414193/rejects-valid-constexpr-marked-friend-function-def.html" target="_top">MSVC
                    &lt; v19.22</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:equality_comparable2"></a><code class="computeroutput"><span class="identifier">equality_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">equality_comparable2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">t</span> <span class="special">==</span>
                    <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="keyword">bool</span></code>.
                  </p>
                </td>
<td>
                  <p>
                    Since <code class="computeroutput"><span class="identifier">C</span><span class="special">++</span><span class="number">11</span></code>, except <a href="https://developercommunity.visualstudio.com/content/problem/414193/rejects-valid-constexpr-marked-friend-function-def.html" target="_top">MSVC
                    &lt; v19.22</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:addable1"></a><code class="computeroutput"><span class="identifier">addable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">addable1</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">+(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">+=</span> <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:addable2"></a><code class="computeroutput"><span class="identifier">addable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">addable2</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span>
                    <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">+(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">+(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="special">)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">+=</span> <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:subtractable1"></a><code class="computeroutput"><span class="identifier">subtractable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">subtractable1</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">-(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">-=</span> <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:subtractable2"></a><code class="computeroutput"><span class="identifier">subtractable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">subtractable2</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">-(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">-=</span> <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:subtractable2_left"></a><code class="computeroutput"><span class="identifier">subtractable2_left</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">-(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">u</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">-=</span> <span class="identifier">t</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:multipliable1"></a><code class="computeroutput"><span class="identifier">multipliable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">multipliable1</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">*(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">*=</span> <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:multipliable2"></a><code class="computeroutput"><span class="identifier">multipliable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">multipliable2</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">*(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">*(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">*=</span> <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:dividable1"></a><code class="computeroutput"><span class="identifier">dividable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">dividable1</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">/(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">/=</span> <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:dividable2"></a><code class="computeroutput"><span class="identifier">dividable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">dividable2</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span>
                    <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">/(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">/=</span> <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:dividable2_left"></a><code class="computeroutput"><span class="identifier">dividable2_left</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">/(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">u</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">/=</span> <span class="identifier">t</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:modable1"></a><code class="computeroutput"><span class="identifier">modable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">modable1</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">%(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">%=</span> <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:modable2"></a><code class="computeroutput"><span class="identifier">modable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">modable2</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span>
                    <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">%(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">%=</span> <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:modable2_left"></a><code class="computeroutput"><span class="identifier">modable2_left</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">%(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">u</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">%=</span> <span class="identifier">t</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:orable1"></a><code class="computeroutput"><span class="identifier">orable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">orable1</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">|(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">|=</span> <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:orable2"></a><code class="computeroutput"><span class="identifier">orable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">orable2</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span>
                    <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">|(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">|(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">|=</span> <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:andable1"></a><code class="computeroutput"><span class="identifier">andable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">andable1</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">&amp;(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">&amp;=</span> <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:andable2"></a><code class="computeroutput"><span class="identifier">andable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">andable2</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span>
                    <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">&amp;(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">&amp;(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">&amp;=</span> <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:xorable1"></a><code class="computeroutput"><span class="identifier">xorable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">xorable1</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">^(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">^=</span> <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:xorable2"></a><code class="computeroutput"><span class="identifier">xorable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">xorable2</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span>
                    <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">^(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">^(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">^=</span> <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:incrementable"></a><code class="computeroutput"><span class="identifier">incrementable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">++(</span><span class="identifier">T</span><span class="special">&amp;,</span> <span class="keyword">int</span><span class="special">)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="special">++</span><span class="identifier">t</span></code>
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:decrementable"></a><code class="computeroutput"><span class="identifier">decrementable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">--(</span><span class="identifier">T</span><span class="special">&amp;,</span> <span class="keyword">int</span><span class="special">)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="special">--</span><span class="identifier">t</span><span class="special">;</span></code>
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:left_shiftable1"></a><code class="computeroutput"><span class="identifier">left_shiftable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">left_shiftable1</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">&lt;&lt;(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">&lt;&lt;=</span> <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:left_shiftable2"></a><code class="computeroutput"><span class="identifier">left_shiftable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">left_shiftable2</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">&lt;&lt;(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">&lt;&lt;=</span> <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:right_shiftable1"></a><code class="computeroutput"><span class="identifier">right_shiftable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">right_shiftable1</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">&gt;&gt;(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">&gt;&gt;=</span> <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:right_shiftable2"></a><code class="computeroutput"><span class="identifier">right_shiftable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="identifier">right_shiftable2</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">&gt;&gt;(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span> <span class="identifier">temp</span><span class="special">(</span><span class="identifier">t</span><span class="special">);</span> <span class="identifier">temp</span>
                    <span class="special">&gt;&gt;=</span> <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="identifier">T</span></code>.
                    See the <a class="link" href="operators.html#sec:symmetry">Symmetry Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    No
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:equivalent1"></a><code class="computeroutput"><span class="identifier">equivalent</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">equivalent1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">t</span> <span class="special">&lt;</span>
                    <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Return convertible to <code class="computeroutput"><span class="keyword">bool</span></code>.
                    See the <a class="link" href="operators.html#sec:ordering">Ordering Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    Since <code class="computeroutput"><span class="identifier">C</span><span class="special">++</span><span class="number">11</span></code>, except <a href="https://developercommunity.visualstudio.com/content/problem/414193/rejects-valid-constexpr-marked-friend-function-def.html" target="_top">MSVC
                    &lt; v19.22</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:equivalent2"></a><code class="computeroutput"><span class="identifier">equivalent</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">equivalent2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">t</span> <span class="special">&lt;</span>
                    <span class="identifier">u</span></code>. <code class="computeroutput"><span class="identifier">t</span>
                    <span class="special">&gt;</span> <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Returns convertible to <code class="computeroutput"><span class="keyword">bool</span></code>.
                    See the <a class="link" href="operators.html#sec:ordering">Ordering Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    Since <code class="computeroutput"><span class="identifier">C</span><span class="special">++</span><span class="number">11</span></code>, except <a href="https://developercommunity.visualstudio.com/content/problem/414193/rejects-valid-constexpr-marked-friend-function-def.html" target="_top">MSVC
                    &lt; v19.22</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:partially_ordered1"></a><code class="computeroutput"><span class="identifier">partially_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">partially_ordered1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">t</span> <span class="special">&lt;</span>
                    <span class="identifier">t1</span></code>. <code class="computeroutput"><span class="identifier">t</span>
                    <span class="special">==</span> <span class="identifier">t1</span></code>.
                  </p>
                  <p>
                    Returns convertible to <code class="computeroutput"><span class="keyword">bool</span></code>.
                    See the <a class="link" href="operators.html#sec:ordering">Ordering Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    Since <code class="computeroutput"><span class="identifier">C</span><span class="special">++</span><span class="number">11</span></code>, except <a href="https://developercommunity.visualstudio.com/content/problem/414193/rejects-valid-constexpr-marked-friend-function-def.html" target="_top">MSVC
                    &lt; v19.22</a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:partially_ordered2"></a><code class="computeroutput"><span class="identifier">partially_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">partially_ordered2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span><span class="keyword">const</span>
                    <span class="identifier">T</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                  <p>
                    <code class="computeroutput"><span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span><span class="keyword">const</span>
                    <span class="identifier">U</span><span class="special">&amp;,</span>
                    <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;)</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">t</span> <span class="special">&lt;</span>
                    <span class="identifier">u</span></code>. <code class="computeroutput"><span class="identifier">t</span>
                    <span class="special">&gt;</span> <span class="identifier">u</span></code>.
                    <code class="computeroutput"><span class="identifier">t</span> <span class="special">==</span>
                    <span class="identifier">u</span></code>.
                  </p>
                  <p>
                    Returns convertible to <code class="computeroutput"><span class="keyword">bool</span></code>.
                    See the <a class="link" href="operators.html#sec:ordering">Ordering Note</a>.
                  </p>
                </td>
<td>
                  <p>
                    Since <code class="computeroutput"><span class="identifier">C</span><span class="special">++</span><span class="number">11</span></code>, except <a href="https://developercommunity.visualstudio.com/content/problem/414193/rejects-valid-constexpr-marked-friend-function-def.html" target="_top">MSVC
                    &lt; v19.22</a>
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><p>
          <a name="sec:ordering"></a><span class="emphasis"><em>Ordering Note</em></span>: The <a class="link" href="operators.html#table:less_than_comparable1"><code class="computeroutput"><span class="identifier">less_than_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a> and <a class="link" href="operators.html#table:partially_ordered1"><code class="computeroutput"><span class="identifier">partially_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
          templates provide the same set of operations. However, the workings of
          <a class="link" href="operators.html#table:less_than_comparable1"><code class="computeroutput"><span class="identifier">less_than_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a> assume that all values of type
          <code class="computeroutput"><span class="identifier">T</span></code> can be placed in a total
          order. If that is not true (e.g. Not-a-Number values in IEEE floating point
          arithmetic), then <a class="link" href="operators.html#table:partially_ordered1"><code class="computeroutput"><span class="identifier">partially_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
          should be used. The <a class="link" href="operators.html#table:partially_ordered1"><code class="computeroutput"><span class="identifier">partially_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
          template can be used for a totally-ordered type, but it is not as efficient
          as <a class="link" href="operators.html#table:less_than_comparable1"><code class="computeroutput"><span class="identifier">less_than_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>. This rule also applies for
          <a class="link" href="operators.html#table:less_than_comparable2"><code class="computeroutput"><span class="identifier">less_than_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a>
          and <a class="link" href="operators.html#table:partially_ordered2"><code class="computeroutput"><span class="identifier">partially_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
          with respect to the ordering of all <code class="computeroutput"><span class="identifier">T</span></code>
          and <code class="computeroutput"><span class="identifier">U</span></code> values, and for both
          versions of <a class="link" href="operators.html#table:equivalent1"><code class="computeroutput"><span class="identifier">equivalent</span><span class="special">&lt;&gt;</span></code></a>. The solution for <a class="link" href="operators.html#table:equivalent1"><code class="computeroutput"><span class="identifier">equivalent</span><span class="special">&lt;&gt;</span></code></a> is to write a custom <code class="computeroutput"><span class="keyword">operator</span><span class="special">==</span></code>
          for the target class.
        </p>
<p>
          <a name="sec:symmetry"></a><span class="emphasis"><em>Symmetry Note</em></span>: Before talking
          about symmetry, we need to talk about optimizations to understand the reasons
          for the different implementation styles of operators. Let's have a look
          at <code class="computeroutput"><span class="keyword">operator</span><span class="special">+</span></code>
          for a class <code class="computeroutput"><span class="identifier">T</span></code> as an example:
        </p>
<pre class="programlisting"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">+(</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">rhs</span> <span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">return</span> <span class="identifier">T</span><span class="special">(</span> <span class="identifier">lhs</span> <span class="special">)</span> <span class="special">+=</span> <span class="identifier">rhs</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
          This would be a normal implementation of <code class="computeroutput"><span class="keyword">operator</span><span class="special">+</span></code>, but it is not an efficient one. An unnamed
          local copy of <code class="computeroutput"><span class="identifier">lhs</span></code> is created,
          <code class="computeroutput"><span class="keyword">operator</span><span class="special">+=</span></code>
          is called on it and it is copied to the function return value (which is
          another unnamed object of type <code class="computeroutput"><span class="identifier">T</span></code>).
          The standard doesn't generally allow the intermediate object to be optimized
          away:
        </p>
<div class="blockquote"><blockquote class="blockquote"><p>
            <span class="emphasis"><em>C++11 §3.7.3/3 [basic.stc.auto]: Automatic storage duration:</em></span>
            If a variable with automatic storage duration has initialization or a
            destructor with side effects, it shall not be destroyed before the end
            of its block, nor shall it be eliminated as an optimization even if it
            appears to be unused, except that a class object or its copy/move may
            be eliminated as specified in 12.8.
          </p></blockquote></div>
<p>
          The reference to §12.8 is important for us:
        </p>
<div class="blockquote"><blockquote class="blockquote">
<p>
            <span class="emphasis"><em>C++11 §12.8/31 [class.copy]: Copying and moving class objects:</em></span>
            When certain criteria are met, an implementation is allowed to omit the
            copy/move construction of a class object, even if the copy/move constructor
            and/or destructor for the object have side effects. (…) This elision of
            copy/move operations, called <span class="emphasis"><em>copy elision</em></span>, is permitted
            in the following circumstances (which may be combined to eliminate multiple
            copies):
          </p>
<p>
            — in a <code class="computeroutput"><span class="keyword">return</span></code> statement in
            a function with a class return type, when the expression is the name
            of a non-volatile automatic object (other than a function or catch-clause
            parameter) with the same cv- unqualified type as the function return
            type, the copy/move operation can be omitted by constructing the automatic
            object directly into the function's return value
          </p>
<p>
            (…)
          </p>
</blockquote></div>
<p>
          This optimization is known as the named return value optimization (<a href="https://en.cppreference.com/w/cpp/language/copy_elision#Non-mandatory_elision_of_copy.2Fmove_.28since_C.2B.2B11.29_operations" target="_top">NRVO</a>),
          which leads us to the following implementation for <code class="computeroutput"><span class="keyword">operator</span><span class="special">+</span></code>:
        </p>
<pre class="programlisting"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">+(</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">rhs</span> <span class="special">)</span>
<span class="special">{</span>
    <span class="identifier">T</span> <span class="identifier">nrv</span><span class="special">(</span> <span class="identifier">lhs</span> <span class="special">);</span>
    <span class="identifier">nrv</span> <span class="special">+=</span> <span class="identifier">rhs</span><span class="special">;</span>
    <span class="keyword">return</span> <span class="identifier">nrv</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
          Given this implementation, the compiler is allowed to remove the intermediate
          object. Sadly, not all compilers implement the NRVO, some even implement
          it in an incorrect way which makes it useless here. Without the NRVO, the
          NRVO-friendly code is no worse than the original code showed above, but
          there is another possible implementation, which has some very special properties:
        </p>
<pre class="programlisting"><span class="identifier">T</span> <span class="keyword">operator</span><span class="special">+(</span> <span class="identifier">T</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">rhs</span> <span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">return</span> <span class="identifier">lhs</span> <span class="special">+=</span> <span class="identifier">rhs</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
          The difference to the first implementation is that <code class="computeroutput"><span class="identifier">lhs</span></code>
          is not taken as a constant reference used to create a copy; instead, <code class="computeroutput"><span class="identifier">lhs</span></code> is a by-value parameter, thus it
          is already the copy needed. This allows another optimization (C++11 §12.2/2
          [class.temporary]) for some cases.
        </p>
<p>
          Consider <code class="computeroutput"><span class="identifier">a</span> <span class="special">+</span>
          <span class="identifier">b</span> <span class="special">+</span>
          <span class="identifier">c</span></code> where the result of <code class="computeroutput"><span class="identifier">a</span> <span class="special">+</span> <span class="identifier">b</span></code> is not copied when used as <code class="computeroutput"><span class="identifier">lhs</span></code> when adding <code class="computeroutput"><span class="identifier">c</span></code>.
          This is more efficient than the original code, but not as efficient as
          a compiler using the NRVO. For most people, it is still preferable for
          compilers that don't implement the NRVO, but the <code class="computeroutput"><span class="keyword">operator</span><span class="special">+</span></code> now has a different function signature.
          Also, the number of objects created differs for <code class="computeroutput"><span class="special">(</span><span class="identifier">a</span> <span class="special">+</span> <span class="identifier">b</span><span class="special">)</span> <span class="special">+</span>
          <span class="identifier">c</span></code> and <code class="computeroutput"><span class="identifier">a</span>
          <span class="special">+</span> <span class="special">(</span><span class="identifier">b</span> <span class="special">+</span> <span class="identifier">c</span><span class="special">)</span></code>.
        </p>
<p>
          Most probably, this won't be a problem for you, but if your code relies
          on the function signature or a strict symmetric behaviour, you should set
          <code class="computeroutput"><span class="identifier">BOOST_FORCE_SYMMETRIC_OPERATORS</span></code>
          in your user-config. This will force the NRVO-friendly implementation to
          be used even for compilers that do not implement the NRVO.
        </p>
<a name="sec:grpd_oprs"></a><h6>
<a name="utility.utilities.operators.arithmetic_operators.h1"></a>
          <span class="phrase"><a name="utility.utilities.operators.arithmetic_operators.grouped_arithmetic_operators"></a></span><a class="link" href="operators.html#utility.utilities.operators.arithmetic_operators.grouped_arithmetic_operators">Grouped
          Arithmetic Operators</a>
        </h6>
<p>
          The following templates provide common groups of related operations. For
          example, since a type which is addable is usually also subtractable, the
          <a class="link" href="operators.html#table:additive1"><code class="computeroutput"><span class="identifier">additive</span></code></a>
          template provides the combined operators of both. The grouped operator
          templates have an additional optional template parameter <code class="computeroutput"><span class="identifier">B</span></code>, which is not shown, for the <a class="link" href="operators.html#sec:chaining">base class chaining</a> technique.
        </p>
<div class="table">
<a name="utility.utilities.operators.arithmetic_operators.notation0"></a><p class="title"><b>Table 1.8. Notation</b></p>
<div class="table-contents"><table class="table" summary="Notation">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Key
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span></code>
                  </p>
                </td>
<td>
                  <p>
                    primary operand type
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">U</span></code>
                  </p>
                </td>
<td>
                  <p>
                    alternate operand type
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="utility.utilities.operators.arithmetic_operators.grouped_arithmetic_operator_temp"></a><p class="title"><b>Table 1.9. Grouped Arithmetic Operator Template Classes</b></p>
<div class="table-contents"><table class="table" summary="Grouped Arithmetic Operator Template Classes">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Template
                  </p>
                </th>
<th>
                  <p>
                    Component Operator Templates
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <a name="table:totally_ordered1"></a><code class="computeroutput"><span class="identifier">totally_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">totally_ordered1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:less_than_comparable1"><code class="computeroutput"><span class="identifier">less_than_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:equality_comparable1"><code class="computeroutput"><span class="identifier">equality_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:totally_ordered2"></a><code class="computeroutput"><span class="identifier">totally_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">totally_ordered2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:less_than_comparable2"><code class="computeroutput"><span class="identifier">less_than_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:equality_comparable2"><code class="computeroutput"><span class="identifier">equality_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:additive1"></a><code class="computeroutput"><span class="identifier">additive</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">additive1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:addable1"><code class="computeroutput"><span class="identifier">addable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:subtractable1"><code class="computeroutput"><span class="identifier">subtractable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:additive2"></a><code class="computeroutput"><span class="identifier">additive</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">additive2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:addable2"><code class="computeroutput"><span class="identifier">addable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:subtractable2"><code class="computeroutput"><span class="identifier">subtractable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:multiplicative1"></a><code class="computeroutput"><span class="identifier">multiplicative</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">multiplicative1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:multipliable1"><code class="computeroutput"><span class="identifier">multipliable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:dividable1"><code class="computeroutput"><span class="identifier">dividable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:multiplicative2"></a><code class="computeroutput"><span class="identifier">multiplicative</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">multiplicative2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:multipliable2"><code class="computeroutput"><span class="identifier">multipliable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:dividable2"><code class="computeroutput"><span class="identifier">dividable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:integer_multiplicative1"></a><code class="computeroutput"><span class="identifier">integer_multiplicative</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">integer_multiplicative1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:multiplicative1"><code class="computeroutput"><span class="identifier">multiplicative</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:modable1"><code class="computeroutput"><span class="identifier">modable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:integer_multiplicative2"></a><code class="computeroutput"><span class="identifier">integer_multiplicative</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">integer_multiplicative2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:multiplicative2"><code class="computeroutput"><span class="identifier">multiplicative</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:modable2"><code class="computeroutput"><span class="identifier">modable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:arithmetic1"></a><code class="computeroutput"><span class="identifier">arithmetic</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">arithmetic1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:additive1"><code class="computeroutput"><span class="identifier">additive</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:multiplicative1"><code class="computeroutput"><span class="identifier">multiplicative</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:arithmetic2"></a><code class="computeroutput"><span class="identifier">arithmetic</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">arithmetic2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:additive2"><code class="computeroutput"><span class="identifier">additive</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:multiplicative2"><code class="computeroutput"><span class="identifier">multiplicative</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:integer_arithmetic1"></a><code class="computeroutput"><span class="identifier">integer_arithmetic</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">integer_arithmetic1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:additive1"><code class="computeroutput"><span class="identifier">additive</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:integer_multiplicative1"><code class="computeroutput"><span class="identifier">integer_multiplicative</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:integer_arithmetic2"></a><code class="computeroutput"><span class="identifier">integer_arithmetic</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">integer_arithmetic2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:additive2"><code class="computeroutput"><span class="identifier">additive</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:integer_multiplicative2"><code class="computeroutput"><span class="identifier">integer_multiplicative</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:bitwise1"></a><code class="computeroutput"><span class="identifier">bitwise</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">bitwise1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:xorable1"><code class="computeroutput"><span class="identifier">xorable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:andable1"><code class="computeroutput"><span class="identifier">andable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:orable1"><code class="computeroutput"><span class="identifier">orable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:bitwise2"></a><code class="computeroutput"><span class="identifier">bitwise</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">bitwise2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:xorable2"><code class="computeroutput"><span class="identifier">xorable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:andable2"><code class="computeroutput"><span class="identifier">andable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:orable2"><code class="computeroutput"><span class="identifier">orable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:unit_steppable"></a><code class="computeroutput"><span class="identifier">unit_steppable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:incrementable"><code class="computeroutput"><span class="identifier">incrementable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:decrementable"><code class="computeroutput"><span class="identifier">decrementable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:shiftable1"></a><code class="computeroutput"><span class="identifier">shiftable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">shiftable1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:left_shiftable1"><code class="computeroutput"><span class="identifier">left_shiftable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:right_shiftable1"><code class="computeroutput"><span class="identifier">right_shiftable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:shiftable2"></a><code class="computeroutput"><span class="identifier">shiftable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">shiftable2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:left_shiftable2"><code class="computeroutput"><span class="identifier">left_shiftable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:right_shiftable2"><code class="computeroutput"><span class="identifier">right_shiftable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:ring_operators1"></a><code class="computeroutput"><span class="identifier">ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">ring_operators1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:additive1"><code class="computeroutput"><span class="identifier">additive</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:multipliable1"><code class="computeroutput"><span class="identifier">multipliable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:ring_operators2"></a><code class="computeroutput"><span class="identifier">ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">ring_operators2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:additive2"><code class="computeroutput"><span class="identifier">additive</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:subtractable2_left"><code class="computeroutput"><span class="identifier">subtractable2_left</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:multipliable2"><code class="computeroutput"><span class="identifier">multipliable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:ordered_ring_operators1"></a><code class="computeroutput"><span class="identifier">ordered_ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">ordered_ring_operators1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:ring_operators1"><code class="computeroutput"><span class="identifier">ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:totally_ordered1"><code class="computeroutput"><span class="identifier">totally_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:ordered_ring_operators2"></a><code class="computeroutput"><span class="identifier">ordered_ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">ordered_ring_operators2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:ring_operators2"><code class="computeroutput"><span class="identifier">ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:totally_ordered2"><code class="computeroutput"><span class="identifier">totally_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:field_operators1"></a><code class="computeroutput"><span class="identifier">field_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">field_operators1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:ring_operators1"><code class="computeroutput"><span class="identifier">ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:dividable1"><code class="computeroutput"><span class="identifier">dividable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:field_operators2"></a><code class="computeroutput"><span class="identifier">field_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">field_operators2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:ring_operators2"><code class="computeroutput"><span class="identifier">ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:dividable2"><code class="computeroutput"><span class="identifier">dividable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:dividable2_left"><code class="computeroutput"><span class="identifier">dividable2_left</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:ordered_field_operators1"></a><code class="computeroutput"><span class="identifier">ordered_field_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">ordered_field_operators1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:field_operators1"><code class="computeroutput"><span class="identifier">field_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:totally_ordered1"><code class="computeroutput"><span class="identifier">totally_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:ordered_field_operators2"></a><code class="computeroutput"><span class="identifier">ordered_field_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">ordered_field_operators2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:field_operators2"><code class="computeroutput"><span class="identifier">field_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:totally_ordered2"><code class="computeroutput"><span class="identifier">totally_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:euclidean_ring_operators1"></a><code class="computeroutput"><span class="identifier">euclidean_ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">euclidean_ring_operators1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:ring_operators1"><code class="computeroutput"><span class="identifier">ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:dividable1"><code class="computeroutput"><span class="identifier">dividable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:modable1"><code class="computeroutput"><span class="identifier">modable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:euclidean_ring_operators2"></a><code class="computeroutput"><span class="identifier">euclidean_ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">euclidean_ring_operators2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:ring_operators2"><code class="computeroutput"><span class="identifier">ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:dividable2"><code class="computeroutput"><span class="identifier">dividable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:dividable2_left"><code class="computeroutput"><span class="identifier">dividable2_left</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:modable2"><code class="computeroutput"><span class="identifier">modable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:modable2_left"><code class="computeroutput"><span class="identifier">modable2_left</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:ordered_euclidean_ring_operators1"></a><code class="computeroutput"><span class="identifier">ordered_euclidean_ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">ordered_euclidean_ring_operators1</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:euclidean_ring_operators1"><code class="computeroutput"><span class="identifier">euclidean_ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:totally_ordered1"><code class="computeroutput"><span class="identifier">totally_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:ordered_euclidean_ring_operators2"></a><code class="computeroutput"><span class="identifier">ordered_euclidean_ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">ordered_euclidean_ring_operators2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:euclidean_ring_operators2"><code class="computeroutput"><span class="identifier">euclidean_ring_operators</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:totally_ordered2"><code class="computeroutput"><span class="identifier">totally_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><p>
          <span class="emphasis"><em>Spelling: euclidean vs. euclidian</em></span>: Older versions
          of the Boost.Operators library used "<code class="computeroutput"><span class="identifier">euclidian</span></code>",
          but it was pointed out that "<code class="computeroutput"><span class="identifier">euclidean</span></code>"
          is the more common spelling. To be compatible with older version, the library
          now supports both spellings.
        </p>
<a name="sec:ex_oprs"></a><h6>
<a name="utility.utilities.operators.arithmetic_operators.h2"></a>
          <span class="phrase"><a name="utility.utilities.operators.arithmetic_operators.example_templates"></a></span><a class="link" href="operators.html#utility.utilities.operators.arithmetic_operators.example_templates">Example
          Templates</a>
        </h6>
<p>
          The arithmetic operator class templates <a class="link" href="operators.html#table:operators1"><code class="computeroutput"><span class="identifier">operators</span><span class="special">&lt;&gt;</span></code></a>
          and <a class="link" href="operators.html#table:operators2"><code class="computeroutput"><span class="identifier">operators2</span><span class="special">&lt;&gt;</span></code></a> are examples of non-extensible
          operator grouping classes. These legacy class templates, from previous
          versions of the header, cannot be used for <a class="link" href="operators.html#sec:chaining">base
          class chaining</a>.
        </p>
<div class="table">
<a name="utility.utilities.operators.arithmetic_operators.notation1"></a><p class="title"><b>Table 1.10. Notation</b></p>
<div class="table-contents"><table class="table" summary="Notation">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Key
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span></code>
                  </p>
                </td>
<td>
                  <p>
                    primary operand type
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">U</span></code>
                  </p>
                </td>
<td>
                  <p>
                    alternate operand type
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="utility.utilities.operators.arithmetic_operators.final_arithmetic_operator_templa"></a><p class="title"><b>Table 1.11. Final Arithmetic Operator Template Classes</b></p>
<div class="table-contents"><table class="table" summary="Final Arithmetic Operator Template Classes">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Template
                  </p>
                </th>
<th>
                  <p>
                    Component Operator Templates
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <a name="table:operators1"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">operators</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:totally_ordered1"><code class="computeroutput"><span class="identifier">totally_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:integer_arithmetic1"><code class="computeroutput"><span class="identifier">integer_arithmetic</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:bitwise1"><code class="computeroutput"><span class="identifier">bitwise</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:unit_steppable"><code class="computeroutput"><span class="identifier">unit_steppable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:operators2"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">operators</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                  <p>
                    <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">operators2</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:totally_ordered2"><code class="computeroutput"><span class="identifier">totally_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:integer_arithmetic2"><code class="computeroutput"><span class="identifier">integer_arithmetic</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:bitwise2"><code class="computeroutput"><span class="identifier">bitwise</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><p>
          <a name="sec:a_demo"></a><span class="emphasis"><em>Arithmetic Operators Demonstration and
          Test Program</em></span>: The <a href="../../../../../test/operators_test.cpp" target="_top"><code class="computeroutput"><span class="identifier">operators_test</span><span class="special">.</span><span class="identifier">cpp</span></code></a> program demonstrates the
          use of the arithmetic operator templates, and can also be used to verify
          correct operation. Check the compiler status report for the test results
          with selected platforms.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="utility.utilities.operators.dereference_operators_and_iterat"></a><a name="sec:deref"></a><a class="link" href="operators.html#utility.utilities.operators.dereference_operators_and_iterat" title="Dereference Operators and Iterator Helpers">Dereference
        Operators and Iterator Helpers</a>
</h4></div></div></div>
<p>
          The <a class="link" href="operators.html#sec:iterator">iterator helper</a> templates ease
          the task of creating a custom iterator. Similar to arithmetic types, a
          complete iterator has many operators that are "redundant" and
          can be implemented in terms of the core set of operators.
        </p>
<p>
          The <a class="link" href="operators.html#sec:dereference">dereference operators</a> were motivated
          by the <a class="link" href="operators.html#sec:iterator">iterator helpers</a>, but are often
          useful in non-iterator contexts as well. Many of the redundant iterator
          operators are also arithmetic operators, so the iterator helper classes
          borrow many of the operators defined above. In fact, only two new operators
          need to be defined: the pointer-to-member <code class="computeroutput"><span class="keyword">operator</span><span class="special">-&gt;</span></code> and the subscript <code class="computeroutput"><span class="keyword">operator</span><span class="special">[]</span></code>.
        </p>
<p>
          The requirements for the types used to instantiate the dereference operators
          are specified in terms of expressions which must be valid and their return
          type. The composite operator templates list their component templates,
          which the instantiating type must support, and possibly other requirements.
        </p>
<a name="sec:dereference"></a><h6>
<a name="utility.utilities.operators.dereference_operators_and_iterat.h0"></a>
          <span class="phrase"><a name="utility.utilities.operators.dereference_operators_and_iterat.dereference_operators"></a></span><a class="link" href="operators.html#utility.utilities.operators.dereference_operators_and_iterat.dereference_operators">Dereference
          Operators</a>
        </h6>
<p>
          All the dereference operator templates in this table accept an optional
          template parameter (not shown) to be used for <a class="link" href="operators.html#sec:chaining">base
          class chaining</a>.
        </p>
<div class="table">
<a name="utility.utilities.operators.dereference_operators_and_iterat.notation"></a><p class="title"><b>Table 1.12. Notation</b></p>
<div class="table-contents"><table class="table" summary="Notation">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Key
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span></code>
                  </p>
                </td>
<td>
                  <p>
                    operand type
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">D</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">difference_type</span></code>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">i</span></code>
                  </p>
                </td>
<td>
                  <p>
                    object of type <code class="computeroutput"><span class="identifier">T</span></code>
                    (an iterator)
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">P</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">pointer</span></code> type
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">R</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">reference</span></code> type
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">n</span></code>
                  </p>
                </td>
<td>
                  <p>
                    object of type <code class="computeroutput"><span class="identifier">D</span></code>
                    (an index)
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="utility.utilities.operators.dereference_operators_and_iterat.dereference_operator_template_cl"></a><p class="title"><b>Table 1.13. Dereference Operator Template Classes</b></p>
<div class="table-contents"><table class="table" summary="Dereference Operator Template Classes">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Template
                  </p>
                </th>
<th>
                  <p>
                    Supplied Operations
                  </p>
                </th>
<th>
                  <p>
                    Requirements
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <a name="table:dereferenceable"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">dereferenceable</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">P</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">P</span> <span class="keyword">operator</span><span class="special">-&gt;()</span> <span class="keyword">const</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="special">*</span><span class="identifier">i</span></code>.
                    Address of the returned value convertible to <code class="computeroutput"><span class="identifier">P</span></code>.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:indexable"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">indexable</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">D</span><span class="special">,</span> <span class="identifier">R</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">R</span> <span class="keyword">operator</span><span class="special">[](</span><span class="identifier">D</span>
                    <span class="identifier">n</span><span class="special">)</span>
                    <span class="keyword">const</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="special">*(</span><span class="identifier">i</span>
                    <span class="special">+</span> <span class="identifier">n</span><span class="special">)</span></code>. Return of type <code class="computeroutput"><span class="identifier">R</span></code>.
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><a name="sec:grpd_iter_oprs"></a><h6>
<a name="utility.utilities.operators.dereference_operators_and_iterat.h1"></a>
          <span class="phrase"><a name="utility.utilities.operators.dereference_operators_and_iterat.grouped_iterator_operators"></a></span><a class="link" href="operators.html#utility.utilities.operators.dereference_operators_and_iterat.grouped_iterator_operators">Grouped
          Iterator Operators</a>
        </h6>
<p>
          There are five iterator operator class templates, each for a different
          category of iterator. The following table shows the operator groups for
          any category that a custom iterator could define. These class templates
          have an additional optional template parameter <code class="computeroutput"><span class="identifier">B</span></code>,
          which is not shown, to support <a class="link" href="operators.html#sec:chaining">base class
          chaining</a>.
        </p>
<div class="table">
<a name="utility.utilities.operators.dereference_operators_and_iterat.notation0"></a><p class="title"><b>Table 1.14. Notation</b></p>
<div class="table-contents"><table class="table" summary="Notation">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Key
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span></code>
                  </p>
                </td>
<td>
                  <p>
                    operand type
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">D</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">difference_type</span></code>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">V</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">value_type</span></code>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">P</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">pointer</span></code> type
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">R</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">reference</span></code> type
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="utility.utilities.operators.dereference_operators_and_iterat.iterator_operator_class_template"></a><p class="title"><b>Table 1.15. Iterator Operator Class Templates</b></p>
<div class="table-contents"><table class="table" summary="Iterator Operator Class Templates">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Template
                  </p>
                </th>
<th>
                  <p>
                    Component Operator Templates
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <a name="table:input_iteratable"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">input_iteratable</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">P</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:equality_comparable1"><code class="computeroutput"><span class="identifier">equality_comparable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:incrementable"><code class="computeroutput"><span class="identifier">incrementable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:dereferenceable"><code class="computeroutput"><span class="identifier">dereferenceable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">P</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:output_iteratable"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">output_iteratable</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:incrementable"><code class="computeroutput"><span class="identifier">incrementable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:forward_iteratable"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">forward_iteratable</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">P</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:input_iteratable"><code class="computeroutput"><span class="identifier">input_iteratable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">P</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:bidirectional_iteratable"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">bidirectional_iteratable</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">P</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:forward_iteratable"><code class="computeroutput"><span class="identifier">forward_iteratable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">P</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:decrementable"><code class="computeroutput"><span class="identifier">decrementable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:random_access_iteratable"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">random_access_iteratable</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">P</span><span class="special">,</span> <span class="identifier">D</span><span class="special">,</span> <span class="identifier">R</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <a class="link" href="operators.html#table:bidirectional_iteratable"><code class="computeroutput"><span class="identifier">bidirectional_iteratable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span>
                    <span class="identifier">P</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:totally_ordered1"><code class="computeroutput"><span class="identifier">totally_ordered</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:additive2"><code class="computeroutput"><span class="identifier">additive</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">D</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    <a class="link" href="operators.html#table:indexable"><code class="computeroutput"><span class="identifier">indexable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">D</span><span class="special">,</span> <span class="identifier">R</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><a name="sec:iterator"></a><h6>
<a name="utility.utilities.operators.dereference_operators_and_iterat.h2"></a>
          <span class="phrase"><a name="utility.utilities.operators.dereference_operators_and_iterat.iterator_helpers"></a></span><a class="link" href="operators.html#utility.utilities.operators.dereference_operators_and_iterat.iterator_helpers">Iterator
          Helpers</a>
        </h6>
<p>
          There are also five iterator helper class templates, each corresponding
          to a different iterator category. These classes cannot be used for <a class="link" href="operators.html#sec:chaining">base class chaining</a>. The following summaries
          show that these class templates supply both the iterator operators from
          the <a class="link" href="operators.html#sec:grpd_iter_oprs">iterator operator class templates</a>
          and the iterator <code class="computeroutput"><span class="keyword">typedef</span></code>s
          required by the C++ standard, such as <code class="computeroutput"><span class="identifier">iterator_category</span></code>
          and <code class="computeroutput"><span class="identifier">value_type</span></code>.
        </p>
<div class="table">
<a name="utility.utilities.operators.dereference_operators_and_iterat.notation1"></a><p class="title"><b>Table 1.16. Notation</b></p>
<div class="table-contents"><table class="table" summary="Notation">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Key
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">T</span></code>
                  </p>
                </td>
<td>
                  <p>
                    operand type
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">D</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">difference_type</span></code>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">V</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">value_type</span></code>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">P</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">pointer</span></code> type
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">R</span></code>
                  </p>
                </td>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">reference</span></code> type
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <code class="computeroutput"><span class="identifier">x1</span></code>, <code class="computeroutput"><span class="identifier">x2</span></code>
                  </p>
                </td>
<td>
                  <p>
                    objects of type <code class="computeroutput"><span class="identifier">T</span></code>
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><div class="table">
<a name="utility.utilities.operators.dereference_operators_and_iterat.helper_class_templates"></a><p class="title"><b>Table 1.17. Helper Class Templates</b></p>
<div class="table-contents"><table class="table" summary="Helper Class Templates">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Template
                  </p>
                </th>
<th>
                  <p>
                    Operations and Requirements
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <a name="table:input_iterator_helper"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">input_iterator_helper</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">D</span><span class="special">,</span> <span class="identifier">P</span><span class="special">,</span> <span class="identifier">R</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    Supports the operations and has the requirements of <a class="link" href="operators.html#table:input_iteratable"><code class="computeroutput"><span class="identifier">input_iteratable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span>
                    <span class="identifier">P</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:output_iterator_helper"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">output_iterator_helper</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    Supports the operations and has the requirements of <a class="link" href="operators.html#table:output_iteratable"><code class="computeroutput"><span class="identifier">output_iteratable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    See also [<a class="link" href="operators.html#note:1">1</a>], [<a class="link" href="operators.html#note:2">2</a>].
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:forward_iterator_helper"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">forward_iterator_helper</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">D</span><span class="special">,</span> <span class="identifier">P</span><span class="special">,</span> <span class="identifier">R</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    Supports the operations and has the requirements of <a class="link" href="operators.html#table:forward_iteratable"><code class="computeroutput"><span class="identifier">forward_iteratable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span>
                    <span class="identifier">P</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:bidirectional_iterator_helper"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">bidirectional_iterator_helper</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">D</span><span class="special">,</span> <span class="identifier">P</span><span class="special">,</span> <span class="identifier">R</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    Supports the operations and has the requirements of <a class="link" href="operators.html#table:bidirectional_iteratable"><code class="computeroutput"><span class="identifier">bidirectional_iteratable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span>
                    <span class="identifier">P</span><span class="special">&gt;</span></code></a>
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a name="table:random_access_iterator_helper"></a><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">random_access_iterator_helper</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">V</span><span class="special">,</span> <span class="identifier">D</span><span class="special">,</span> <span class="identifier">P</span><span class="special">,</span> <span class="identifier">R</span><span class="special">&gt;</span></code>
                  </p>
                </td>
<td>
                  <p>
                    Supports the operations and has the requirements of <a class="link" href="operators.html#table:random_access_iteratable"><code class="computeroutput"><span class="identifier">random_access_iteratable</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span>
                    <span class="identifier">P</span><span class="special">,</span>
                    <span class="identifier">D</span><span class="special">,</span>
                    <span class="identifier">R</span><span class="special">&gt;</span></code></a>
                  </p>
                  <p>
                    To satisfy <a href="https://en.cppreference.com/w/cpp/named_req/RandomAccessIterator" target="_top"><span class="emphasis"><em>RandomAccessIterator</em></span></a>,
                    <code class="computeroutput"><span class="identifier">x1</span> <span class="special">-</span>
                    <span class="identifier">x2</span></code> with return convertible
                    to <code class="computeroutput"><span class="identifier">D</span></code> is also
                    required.
                  </p>
                </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><p>
          <a name="sec:iterator_helpers_notes"></a><span class="emphasis"><em>Iterator Helper Notes</em></span>:
        </p>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
              <a name="note:1"></a>Unlike other iterator helpers templates, <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">output_iterator_helper</span></code></a>
              takes only one template parameter - the type of its target class. Although
              to some it might seem like an unnecessary restriction, the standard
              requires <code class="computeroutput"><span class="identifier">difference_type</span></code>
              and <code class="computeroutput"><span class="identifier">value_type</span></code> of any
              output iterator to be <code class="computeroutput"><span class="keyword">void</span></code>
              (C++11 §24.4.1 [lib.iterator.traits]), and <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">output_iterator_helper</span></code></a> template
              respects this requirement. Also, output iterators in the standard have
              void <code class="computeroutput"><span class="identifier">pointer</span></code> and <code class="computeroutput"><span class="identifier">reference</span></code> types, so the <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">output_iterator_helper</span></code></a> does
              the same.
            </li>
<li class="listitem">
              <a name="note:2"></a>As self-proxying is the easiest and most common
              way to implement output iterators (see, for example, insert (C++11
              §24.5.2 [insert.iterators]) and stream iterators (C++11 §24.6 [stream.iterators])
              in the standard library), <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">output_iterator_helper</span></code></a> supports
              the idiom by defining <code class="computeroutput"><span class="keyword">operator</span><span class="special">*</span></code> and <code class="computeroutput"><span class="keyword">operator</span><span class="special">++</span></code> member functions which just return
              a non-const reference to the iterator itself. Support for self-proxying
              allows us, in many cases, to reduce the task of writing an output iterator
              to writing just two member functions - an appropriate constructor and
              a copy-assignment operator. For example, here is a possible implementation
              of <a href="../../../../../../libs/iterator/doc/function_output_iterator.html" target="_top"><code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">function_output_iterator</span></code></a>
              adaptor:
            </li>
</ol></div>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">UnaryFunction</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">function_output_iterator</span>
    <span class="special">:</span> <span class="identifier">boost</span><span class="special">::</span><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">output_iterator_helper</span></code></a><span class="special">&lt;</span> <span class="identifier">function_output_iterator</span><span class="special">&lt;</span><span class="identifier">UnaryFunction</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="special">{</span>
    <span class="keyword">explicit</span> <span class="identifier">function_output_iterator</span><span class="special">(</span><span class="identifier">UnaryFunction</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">f</span> <span class="special">=</span> <span class="identifier">UnaryFunction</span><span class="special">())</span>
        <span class="special">:</span> <span class="identifier">func</span><span class="special">(</span><span class="identifier">f</span><span class="special">)</span> <span class="special">{}</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
    <span class="identifier">function_output_iterator</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">value</span><span class="special">)</span>
    <span class="special">{</span>
        <span class="keyword">this</span><span class="special">-&gt;</span><span class="identifier">func</span><span class="special">(</span><span class="identifier">value</span><span class="special">);</span>
        <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>
    <span class="special">}</span>

<span class="keyword">private</span><span class="special">:</span>
    <span class="identifier">UnaryFunction</span> <span class="identifier">func</span><span class="special">;</span>
<span class="special">};</span>
</pre>
<p>
          Note that support for self-proxying does not prevent you from using <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">output_iterator_helper</span></code></a>
          to ease any other, different kind of output iterator's implementation.
          If <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">output_iterator_helper</span></code></a>'s
          target type provides its own definition of <code class="computeroutput"><span class="keyword">operator</span><span class="special">*</span></code> or/and <code class="computeroutput"><span class="keyword">operator</span><span class="special">++</span></code>, then these operators will get used and
          the ones supplied by <a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">output_iterator_helper</span></code></a>
          will never be instantiated.
        </p>
<a name="sec:i_demo"></a><h6>
<a name="utility.utilities.operators.dereference_operators_and_iterat.h3"></a>
          <span class="phrase"><a name="utility.utilities.operators.dereference_operators_and_iterat.iterator_demonstration_and_test_"></a></span><a class="link" href="operators.html#utility.utilities.operators.dereference_operators_and_iterat.iterator_demonstration_and_test_">Iterator
          Demonstration and Test Program</a>
        </h6>
<p>
          The <a href="../../../../../test/iterators_test.cpp" target="_top"><code class="computeroutput"><span class="identifier">iterators_test</span><span class="special">.</span><span class="identifier">cpp</span></code></a>
          program demonstrates the use of the iterator templates, and can also be
          used to verify correct operation. The following is the custom iterator
          defined in the test program. It demonstrates a correct (though trivial)
          implementation of the core operations that must be defined in order for
          the iterator helpers to "fill in" the rest of the iterator operations.
        </p>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">R</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">P</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">test_iter</span>
    <span class="special">:</span> <span class="keyword">public</span> <span class="identifier">boost</span><span class="special">::</span><a class="link" href="operators.html#sec:arithmetic"><code class="computeroutput"><span class="identifier">random_access_iterator_helper</span></code></a><span class="special">&lt;</span>
                <span class="identifier">test_iter</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">R</span><span class="special">,</span> <span class="identifier">P</span><span class="special">&gt;,</span> <span class="identifier">T</span><span class="special">,</span> <a href="https://en.cppreference.com/w/cpp/types/ptrdiff_t" target="_top"><code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">ptrdiff_t</span></code></a><span class="special">,</span> <span class="identifier">P</span><span class="special">,</span> <span class="identifier">R</span>
             <span class="special">&gt;</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="identifier">test_iter</span> <span class="identifier">self</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">R</span> <span class="identifier">Reference</span><span class="special">;</span>
    <span class="keyword">typedef</span> <a href="https://en.cppreference.com/w/cpp/types/ptrdiff_t" target="_top"><code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">ptrdiff_t</span></code></a> <span class="identifier">Distance</span><span class="special">;</span>

<span class="keyword">public</span><span class="special">:</span>
    <span class="keyword">explicit</span> <span class="identifier">test_iter</span><span class="special">(</span><span class="identifier">T</span><span class="special">*</span> <span class="identifier">i</span> <span class="special">=</span><span class="number">0</span><span class="special">);</span>
    <span class="identifier">test_iter</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">self</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">self</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="keyword">const</span> <span class="identifier">self</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">Reference</span> <span class="keyword">operator</span><span class="special">*()</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="identifier">self</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">++();</span>
    <span class="identifier">self</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">--();</span>
    <span class="identifier">self</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">+=(</span><span class="identifier">Distance</span> <span class="identifier">n</span><span class="special">);</span>
    <span class="identifier">self</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">-=(</span><span class="identifier">Distance</span> <span class="identifier">n</span><span class="special">);</span>
    <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span><span class="keyword">const</span> <span class="identifier">self</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;(</span><span class="keyword">const</span> <span class="identifier">self</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="keyword">friend</span> <span class="identifier">Distance</span> <span class="keyword">operator</span><span class="special">-(</span><span class="keyword">const</span> <span class="identifier">self</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">self</span><span class="special">&amp;</span> <span class="identifier">y</span><span class="special">);</span>
<span class="special">};</span>
</pre>
<p>
          Check the <a href="http://www.boost.org/development/testing.html" target="_top">compiler
          status report</a> for the test results with selected platforms.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="utility.utilities.operators.note_for_users_of_older_versions"></a><a name="sec:old_lib_note"></a><a class="link" href="operators.html#utility.utilities.operators.note_for_users_of_older_versions" title="Note for Users of Older Versions">Note
        for Users of Older Versions</a>
</h4></div></div></div>
<p>
          The <a class="link" href="operators.html#sec:chaining">changes in the library interface and recommended
          usage</a> were motivated by some practical issues described below. The
          new version of the library is still backward-compatible with the former
          one, so you are not <span class="emphasis"><em>forced</em></span> to change any existing
          code, but the old usage is deprecated.
        </p>
<p>
          Though it was arguably simpler and more intuitive than using <a class="link" href="operators.html#sec:chaining">base
          class chaining</a>, it has been discovered that the old practice of
          deriving from multiple operator templates can cause the resulting classes
          to be much larger than they should be. Most modern C++ compilers significantly
          bloat the size of classes derived from multiple empty base classes, even
          though the base classes themselves have no state. For instance, the size
          of <code class="computeroutput"><span class="identifier">point</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;</span></code>
          from the <a class="link" href="operators.html#sec:example">example</a> above was 12-24 bytes
          on various compilers for the Win32 platform, instead of the expected 8
          bytes.
        </p>
<p>
          Strictly speaking, it was not the library's fault – the language rules allow
          the compiler to apply the <a href="https://en.cppreference.com/w/cpp/language/ebo" target="_top">empty
          base class optimization</a> in that situation. In principle an arbitrary
          number of empty base classes can be allocated at the same offset, provided
          that none of them have a common ancestor (see §10 [class.derived] paragraph
          8 of the C++11 standard).
        </p>
<p>
          But the language definition also does not <span class="emphasis"><em>require</em></span>
          implementations to do the optimization, and few if any of today's compilers
          implement it when multiple inheritance is involved. What's worse, it is
          very unlikely that implementors will adopt it as a future enhancement to
          existing compilers, because it would break binary compatibility between
          code generated by two different versions of the same compiler. As Matt
          Austern said, "One of the few times when you have the freedom to do
          this sort of thing is when you are targeting a new architecture…".
          On the other hand, many common compilers will use the empty base optimization
          for single inheritance hierarchies.
        </p>
<p>
          Given the importance of the issue for the users of the library (which aims
          to be useful for writing light-weight classes like <code class="computeroutput"><span class="identifier">MyInt</span></code>
          or <code class="computeroutput"><span class="identifier">point</span><span class="special">&lt;&gt;</span></code>),
          and the forces described above, we decided to change the library interface
          so that the object size bloat could be eliminated even on compilers that
          support only the simplest form of the empty base class optimization. The
          current library interface is the result of those changes. Though the new
          usage is a bit more complicated than the old one, we think it's worth it
          to make the library more useful in real world. Alexy Gurtovoy contributed
          the code which supports the new usage idiom while allowing the library
          to remain backward-compatible.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="utility.utilities.operators.acknowledgments"></a><a name="sec:contributors"></a><a class="link" href="operators.html#utility.utilities.operators.acknowledgments" title="Acknowledgments">Acknowledgments</a>
</h4></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              <a href="http://www.boost.org/people/dave_abrahams.htm" target="_top">Dave Abrahams</a>:
              Started the library and contributed the arithmetic operators in <a href="../../../../../../boost/operators.hpp" target="_top"><code class="computeroutput"><span class="identifier">boost</span><span class="special">/</span><span class="identifier">operators</span><span class="special">.</span><span class="identifier">hpp</span></code></a>.
            </li>
<li class="listitem">
              <a href="http://www.boost.org/people/jeremy_siek.htm" target="_top">Jeremy Siek</a>:
              Contributed the <a class="link" href="operators.html#sec:deref">dereference operators and
              iterator helpers</a> in <a href="../../../../../../boost/operators.hpp" target="_top">boost/operators.hpp</a>.
              Also contributed <a href="../../../../../test/iterators_test.cpp" target="_top">iterators_test.cpp</a>.
            </li>
<li class="listitem">
              <a href="http://www.boost.org/people/aleksey_gurtovoy.htm" target="_top">Aleksey
              Gurtovoy</a>: Contributed the code to support <a class="link" href="operators.html#sec:chaining">base
              class chaining</a> while remaining backward-compatible with old
              versions of the library.
            </li>
<li class="listitem">
              <a href="http://www.boost.org/people/beman_dawes.html" target="_top">Beman Dawes</a>:
              Contributed <a href="../../../../../test/operators_test.cpp" target="_top"><code class="computeroutput"><span class="identifier">operators_test</span><span class="special">.</span><span class="identifier">cpp</span></code></a>.
            </li>
<li class="listitem">
              <a href="http://www.boost.org/people/daryle_walker.html" target="_top">Daryle
              Walker</a>: Contributed classes for the shift operators, equivalence,
              partial ordering, and arithmetic conversions. Added the grouped operator
              classes. Added helper classes for input and output iterators.
            </li>
<li class="listitem">
              Helmut Zeisel: Contributed the 'left' operators and added some grouped
              operator classes.
            </li>
<li class="listitem">
              Daniel Frey: Contributed the NRVO-friendly and symmetric implementation
              of arithmetic operators.
            </li>
</ul></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2001 Beman Dawes<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../../boost/typed_in_place_fac_idm4015.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../utilities.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="result_of.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
