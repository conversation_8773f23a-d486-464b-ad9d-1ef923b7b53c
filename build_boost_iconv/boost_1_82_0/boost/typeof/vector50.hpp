
// Copyright (C) 2005 Arkadiy Vertleyb
// Copyright (C) 2005 <PERSON><PERSON><PERSON>
//
// Use modification and distribution are subject to the boost Software License,
// Version 1.0. (See http://www.boost.org/LICENSE_1_0.txt).

// Preprocessed code, do not edit manually !


namespace boost { namespace type_of {
    template<class V, class Increase_BOOST_TYPEOF_LIMIT_SIZE> struct v_iter;
        template<class V> struct v_iter<V, constant<int,0> > { typedef typename V::item0 type; typedef v_iter<V, constant<int,0 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,1> > { typedef typename V::item1 type; typedef v_iter<V, constant<int,1 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,2> > { typedef typename V::item2 type; typedef v_iter<V, constant<int,2 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,3> > { typedef typename V::item3 type; typedef v_iter<V, constant<int,3 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,4> > { typedef typename V::item4 type; typedef v_iter<V, constant<int,4 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,5> > { typedef typename V::item5 type; typedef v_iter<V, constant<int,5 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,6> > { typedef typename V::item6 type; typedef v_iter<V, constant<int,6 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,7> > { typedef typename V::item7 type; typedef v_iter<V, constant<int,7 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,8> > { typedef typename V::item8 type; typedef v_iter<V, constant<int,8 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,9> > { typedef typename V::item9 type; typedef v_iter<V, constant<int,9 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,10> > { typedef typename V::item10 type; typedef v_iter<V, constant<int,10 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,11> > { typedef typename V::item11 type; typedef v_iter<V, constant<int,11 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,12> > { typedef typename V::item12 type; typedef v_iter<V, constant<int,12 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,13> > { typedef typename V::item13 type; typedef v_iter<V, constant<int,13 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,14> > { typedef typename V::item14 type; typedef v_iter<V, constant<int,14 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,15> > { typedef typename V::item15 type; typedef v_iter<V, constant<int,15 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,16> > { typedef typename V::item16 type; typedef v_iter<V, constant<int,16 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,17> > { typedef typename V::item17 type; typedef v_iter<V, constant<int,17 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,18> > { typedef typename V::item18 type; typedef v_iter<V, constant<int,18 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,19> > { typedef typename V::item19 type; typedef v_iter<V, constant<int,19 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,20> > { typedef typename V::item20 type; typedef v_iter<V, constant<int,20 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,21> > { typedef typename V::item21 type; typedef v_iter<V, constant<int,21 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,22> > { typedef typename V::item22 type; typedef v_iter<V, constant<int,22 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,23> > { typedef typename V::item23 type; typedef v_iter<V, constant<int,23 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,24> > { typedef typename V::item24 type; typedef v_iter<V, constant<int,24 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,25> > { typedef typename V::item25 type; typedef v_iter<V, constant<int,25 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,26> > { typedef typename V::item26 type; typedef v_iter<V, constant<int,26 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,27> > { typedef typename V::item27 type; typedef v_iter<V, constant<int,27 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,28> > { typedef typename V::item28 type; typedef v_iter<V, constant<int,28 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,29> > { typedef typename V::item29 type; typedef v_iter<V, constant<int,29 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,30> > { typedef typename V::item30 type; typedef v_iter<V, constant<int,30 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,31> > { typedef typename V::item31 type; typedef v_iter<V, constant<int,31 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,32> > { typedef typename V::item32 type; typedef v_iter<V, constant<int,32 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,33> > { typedef typename V::item33 type; typedef v_iter<V, constant<int,33 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,34> > { typedef typename V::item34 type; typedef v_iter<V, constant<int,34 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,35> > { typedef typename V::item35 type; typedef v_iter<V, constant<int,35 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,36> > { typedef typename V::item36 type; typedef v_iter<V, constant<int,36 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,37> > { typedef typename V::item37 type; typedef v_iter<V, constant<int,37 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,38> > { typedef typename V::item38 type; typedef v_iter<V, constant<int,38 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,39> > { typedef typename V::item39 type; typedef v_iter<V, constant<int,39 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,40> > { typedef typename V::item40 type; typedef v_iter<V, constant<int,40 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,41> > { typedef typename V::item41 type; typedef v_iter<V, constant<int,41 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,42> > { typedef typename V::item42 type; typedef v_iter<V, constant<int,42 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,43> > { typedef typename V::item43 type; typedef v_iter<V, constant<int,43 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,44> > { typedef typename V::item44 type; typedef v_iter<V, constant<int,44 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,45> > { typedef typename V::item45 type; typedef v_iter<V, constant<int,45 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,46> > { typedef typename V::item46 type; typedef v_iter<V, constant<int,46 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,47> > { typedef typename V::item47 type; typedef v_iter<V, constant<int,47 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,48> > { typedef typename V::item48 type; typedef v_iter<V, constant<int,48 + 1> > next; };
        template<class V> struct v_iter<V, constant<int,49> > { typedef typename V::item49 type; typedef v_iter<V, constant<int,49 + 1> > next; };
}}
namespace boost { namespace type_of {
        template< class T = void> struct vector0 { typedef v_iter<vector0<>, boost::type_of::constant<int,0> > begin; typedef constant<int,1> item0; typedef constant<int,1> item1; typedef constant<int,1> item2; typedef constant<int,1> item3; typedef constant<int,1> item4; typedef constant<int,1> item5; typedef constant<int,1> item6; typedef constant<int,1> item7; typedef constant<int,1> item8; typedef constant<int,1> item9; typedef constant<int,1> item10; typedef constant<int,1> item11; typedef constant<int,1> item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 > struct vector1 { typedef v_iter<vector1< P0>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef constant<int,1> item1; typedef constant<int,1> item2; typedef constant<int,1> item3; typedef constant<int,1> item4; typedef constant<int,1> item5; typedef constant<int,1> item6; typedef constant<int,1> item7; typedef constant<int,1> item8; typedef constant<int,1> item9; typedef constant<int,1> item10; typedef constant<int,1> item11; typedef constant<int,1> item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 > struct vector2 { typedef v_iter<vector2< P0 , P1>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef constant<int,1> item2; typedef constant<int,1> item3; typedef constant<int,1> item4; typedef constant<int,1> item5; typedef constant<int,1> item6; typedef constant<int,1> item7; typedef constant<int,1> item8; typedef constant<int,1> item9; typedef constant<int,1> item10; typedef constant<int,1> item11; typedef constant<int,1> item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 > struct vector3 { typedef v_iter<vector3< P0 , P1 , P2>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef constant<int,1> item3; typedef constant<int,1> item4; typedef constant<int,1> item5; typedef constant<int,1> item6; typedef constant<int,1> item7; typedef constant<int,1> item8; typedef constant<int,1> item9; typedef constant<int,1> item10; typedef constant<int,1> item11; typedef constant<int,1> item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 > struct vector4 { typedef v_iter<vector4< P0 , P1 , P2 , P3>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef constant<int,1> item4; typedef constant<int,1> item5; typedef constant<int,1> item6; typedef constant<int,1> item7; typedef constant<int,1> item8; typedef constant<int,1> item9; typedef constant<int,1> item10; typedef constant<int,1> item11; typedef constant<int,1> item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 > struct vector5 { typedef v_iter<vector5< P0 , P1 , P2 , P3 , P4>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef constant<int,1> item5; typedef constant<int,1> item6; typedef constant<int,1> item7; typedef constant<int,1> item8; typedef constant<int,1> item9; typedef constant<int,1> item10; typedef constant<int,1> item11; typedef constant<int,1> item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 > struct vector6 { typedef v_iter<vector6< P0 , P1 , P2 , P3 , P4 , P5>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef constant<int,1> item6; typedef constant<int,1> item7; typedef constant<int,1> item8; typedef constant<int,1> item9; typedef constant<int,1> item10; typedef constant<int,1> item11; typedef constant<int,1> item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 > struct vector7 { typedef v_iter<vector7< P0 , P1 , P2 , P3 , P4 , P5 , P6>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef constant<int,1> item7; typedef constant<int,1> item8; typedef constant<int,1> item9; typedef constant<int,1> item10; typedef constant<int,1> item11; typedef constant<int,1> item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 > struct vector8 { typedef v_iter<vector8< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef constant<int,1> item8; typedef constant<int,1> item9; typedef constant<int,1> item10; typedef constant<int,1> item11; typedef constant<int,1> item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 > struct vector9 { typedef v_iter<vector9< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef constant<int,1> item9; typedef constant<int,1> item10; typedef constant<int,1> item11; typedef constant<int,1> item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 > struct vector10 { typedef v_iter<vector10< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef constant<int,1> item10; typedef constant<int,1> item11; typedef constant<int,1> item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 > struct vector11 { typedef v_iter<vector11< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef constant<int,1> item11; typedef constant<int,1> item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 > struct vector12 { typedef v_iter<vector12< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef constant<int,1> item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 > struct vector13 { typedef v_iter<vector13< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef constant<int,1> item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 > struct vector14 { typedef v_iter<vector14< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef constant<int,1> item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 > struct vector15 { typedef v_iter<vector15< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef constant<int,1> item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 > struct vector16 { typedef v_iter<vector16< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef constant<int,1> item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 > struct vector17 { typedef v_iter<vector17< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef constant<int,1> item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 > struct vector18 { typedef v_iter<vector18< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef constant<int,1> item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 > struct vector19 { typedef v_iter<vector19< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef constant<int,1> item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 > struct vector20 { typedef v_iter<vector20< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef constant<int,1> item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 > struct vector21 { typedef v_iter<vector21< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef constant<int,1> item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 > struct vector22 { typedef v_iter<vector22< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef constant<int,1> item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 > struct vector23 { typedef v_iter<vector23< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef constant<int,1> item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 > struct vector24 { typedef v_iter<vector24< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef constant<int,1> item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 > struct vector25 { typedef v_iter<vector25< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef constant<int,1> item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 > struct vector26 { typedef v_iter<vector26< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef constant<int,1> item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 > struct vector27 { typedef v_iter<vector27< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef constant<int,1> item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 > struct vector28 { typedef v_iter<vector28< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef constant<int,1> item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 > struct vector29 { typedef v_iter<vector29< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef constant<int,1> item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 > struct vector30 { typedef v_iter<vector30< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef constant<int,1> item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 > struct vector31 { typedef v_iter<vector31< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef constant<int,1> item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 > struct vector32 { typedef v_iter<vector32< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef constant<int,1> item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 > struct vector33 { typedef v_iter<vector33< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef constant<int,1> item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 > struct vector34 { typedef v_iter<vector34< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef constant<int,1> item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 > struct vector35 { typedef v_iter<vector35< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef constant<int,1> item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 > struct vector36 { typedef v_iter<vector36< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef constant<int,1> item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 > struct vector37 { typedef v_iter<vector37< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef constant<int,1> item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 > struct vector38 { typedef v_iter<vector38< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef P37 item37; typedef constant<int,1> item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 > struct vector39 { typedef v_iter<vector39< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef P37 item37; typedef P38 item38; typedef constant<int,1> item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 > struct vector40 { typedef v_iter<vector40< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef P37 item37; typedef P38 item38; typedef P39 item39; typedef constant<int,1> item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 > struct vector41 { typedef v_iter<vector41< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef P37 item37; typedef P38 item38; typedef P39 item39; typedef P40 item40; typedef constant<int,1> item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 > struct vector42 { typedef v_iter<vector42< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef P37 item37; typedef P38 item38; typedef P39 item39; typedef P40 item40; typedef P41 item41; typedef constant<int,1> item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 > struct vector43 { typedef v_iter<vector43< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef P37 item37; typedef P38 item38; typedef P39 item39; typedef P40 item40; typedef P41 item41; typedef P42 item42; typedef constant<int,1> item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class P43 > struct vector44 { typedef v_iter<vector44< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef P37 item37; typedef P38 item38; typedef P39 item39; typedef P40 item40; typedef P41 item41; typedef P42 item42; typedef P43 item43; typedef constant<int,1> item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class P43 , class P44 > struct vector45 { typedef v_iter<vector45< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef P37 item37; typedef P38 item38; typedef P39 item39; typedef P40 item40; typedef P41 item41; typedef P42 item42; typedef P43 item43; typedef P44 item44; typedef constant<int,1> item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class P43 , class P44 , class P45 > struct vector46 { typedef v_iter<vector46< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , P45>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef P37 item37; typedef P38 item38; typedef P39 item39; typedef P40 item40; typedef P41 item41; typedef P42 item42; typedef P43 item43; typedef P44 item44; typedef P45 item45; typedef constant<int,1> item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class P43 , class P44 , class P45 , class P46 > struct vector47 { typedef v_iter<vector47< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , P45 , P46>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef P37 item37; typedef P38 item38; typedef P39 item39; typedef P40 item40; typedef P41 item41; typedef P42 item42; typedef P43 item43; typedef P44 item44; typedef P45 item45; typedef P46 item46; typedef constant<int,1> item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class P43 , class P44 , class P45 , class P46 , class P47 > struct vector48 { typedef v_iter<vector48< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , P45 , P46 , P47>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef P37 item37; typedef P38 item38; typedef P39 item39; typedef P40 item40; typedef P41 item41; typedef P42 item42; typedef P43 item43; typedef P44 item44; typedef P45 item45; typedef P46 item46; typedef P47 item47; typedef constant<int,1> item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class P43 , class P44 , class P45 , class P46 , class P47 , class P48 > struct vector49 { typedef v_iter<vector49< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , P45 , P46 , P47 , P48>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef P37 item37; typedef P38 item38; typedef P39 item39; typedef P40 item40; typedef P41 item41; typedef P42 item42; typedef P43 item43; typedef P44 item44; typedef P45 item45; typedef P46 item46; typedef P47 item47; typedef P48 item48; typedef constant<int,1> item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class P43 , class P44 , class P45 , class P46 , class P47 , class P48 , class P49 > struct vector50 { typedef v_iter<vector50< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , P45 , P46 , P47 , P48 , P49>, boost::type_of::constant<int,0> > begin; typedef P0 item0; typedef P1 item1; typedef P2 item2; typedef P3 item3; typedef P4 item4; typedef P5 item5; typedef P6 item6; typedef P7 item7; typedef P8 item8; typedef P9 item9; typedef P10 item10; typedef P11 item11; typedef P12 item12; typedef P13 item13; typedef P14 item14; typedef P15 item15; typedef P16 item16; typedef P17 item17; typedef P18 item18; typedef P19 item19; typedef P20 item20; typedef P21 item21; typedef P22 item22; typedef P23 item23; typedef P24 item24; typedef P25 item25; typedef P26 item26; typedef P27 item27; typedef P28 item28; typedef P29 item29; typedef P30 item30; typedef P31 item31; typedef P32 item32; typedef P33 item33; typedef P34 item34; typedef P35 item35; typedef P36 item36; typedef P37 item37; typedef P38 item38; typedef P39 item39; typedef P40 item40; typedef P41 item41; typedef P42 item42; typedef P43 item43; typedef P44 item44; typedef P45 item45; typedef P46 item46; typedef P47 item47; typedef P48 item48; typedef P49 item49; typedef constant<int,1> item50; typedef constant<int,1> item51; typedef constant<int,1> item52; typedef constant<int,1> item53; typedef constant<int,1> item54; typedef constant<int,1> item55; typedef constant<int,1> item56; typedef constant<int,1> item57; typedef constant<int,1> item58; typedef constant<int,1> item59; typedef constant<int,1> item60; typedef constant<int,1> item61; typedef constant<int,1> item62; typedef constant<int,1> item63; typedef constant<int,1> item64; typedef constant<int,1> item65; typedef constant<int,1> item66; typedef constant<int,1> item67; typedef constant<int,1> item68; typedef constant<int,1> item69; typedef constant<int,1> item70; typedef constant<int,1> item71; typedef constant<int,1> item72; typedef constant<int,1> item73; typedef constant<int,1> item74; typedef constant<int,1> item75; typedef constant<int,1> item76; typedef constant<int,1> item77; typedef constant<int,1> item78; typedef constant<int,1> item79; typedef constant<int,1> item80; typedef constant<int,1> item81; typedef constant<int,1> item82; typedef constant<int,1> item83; typedef constant<int,1> item84; typedef constant<int,1> item85; typedef constant<int,1> item86; typedef constant<int,1> item87; typedef constant<int,1> item88; typedef constant<int,1> item89; typedef constant<int,1> item90; typedef constant<int,1> item91; typedef constant<int,1> item92; typedef constant<int,1> item93; typedef constant<int,1> item94; typedef constant<int,1> item95; typedef constant<int,1> item96; typedef constant<int,1> item97; typedef constant<int,1> item98; typedef constant<int,1> item99; };
}}
namespace boost { namespace type_of {
    template<class V, class T> struct push_back {
        typedef V type;
    };
        template< class T> struct push_back<boost::type_of::vector0<>, T> { typedef boost::type_of::vector1< T > type; };
        template< class P0 , class T> struct push_back<boost::type_of::vector1< P0>, T> { typedef boost::type_of::vector2< P0 , T > type; };
        template< class P0 , class P1 , class T> struct push_back<boost::type_of::vector2< P0 , P1>, T> { typedef boost::type_of::vector3< P0 , P1 , T > type; };
        template< class P0 , class P1 , class P2 , class T> struct push_back<boost::type_of::vector3< P0 , P1 , P2>, T> { typedef boost::type_of::vector4< P0 , P1 , P2 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class T> struct push_back<boost::type_of::vector4< P0 , P1 , P2 , P3>, T> { typedef boost::type_of::vector5< P0 , P1 , P2 , P3 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class T> struct push_back<boost::type_of::vector5< P0 , P1 , P2 , P3 , P4>, T> { typedef boost::type_of::vector6< P0 , P1 , P2 , P3 , P4 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class T> struct push_back<boost::type_of::vector6< P0 , P1 , P2 , P3 , P4 , P5>, T> { typedef boost::type_of::vector7< P0 , P1 , P2 , P3 , P4 , P5 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class T> struct push_back<boost::type_of::vector7< P0 , P1 , P2 , P3 , P4 , P5 , P6>, T> { typedef boost::type_of::vector8< P0 , P1 , P2 , P3 , P4 , P5 , P6 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class T> struct push_back<boost::type_of::vector8< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7>, T> { typedef boost::type_of::vector9< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class T> struct push_back<boost::type_of::vector9< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8>, T> { typedef boost::type_of::vector10< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class T> struct push_back<boost::type_of::vector10< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9>, T> { typedef boost::type_of::vector11< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class T> struct push_back<boost::type_of::vector11< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10>, T> { typedef boost::type_of::vector12< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class T> struct push_back<boost::type_of::vector12< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11>, T> { typedef boost::type_of::vector13< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class T> struct push_back<boost::type_of::vector13< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12>, T> { typedef boost::type_of::vector14< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class T> struct push_back<boost::type_of::vector14< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13>, T> { typedef boost::type_of::vector15< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class T> struct push_back<boost::type_of::vector15< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14>, T> { typedef boost::type_of::vector16< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class T> struct push_back<boost::type_of::vector16< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15>, T> { typedef boost::type_of::vector17< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class T> struct push_back<boost::type_of::vector17< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16>, T> { typedef boost::type_of::vector18< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class T> struct push_back<boost::type_of::vector18< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17>, T> { typedef boost::type_of::vector19< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class T> struct push_back<boost::type_of::vector19< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18>, T> { typedef boost::type_of::vector20< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class T> struct push_back<boost::type_of::vector20< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19>, T> { typedef boost::type_of::vector21< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class T> struct push_back<boost::type_of::vector21< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20>, T> { typedef boost::type_of::vector22< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class T> struct push_back<boost::type_of::vector22< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21>, T> { typedef boost::type_of::vector23< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class T> struct push_back<boost::type_of::vector23< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22>, T> { typedef boost::type_of::vector24< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class T> struct push_back<boost::type_of::vector24< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23>, T> { typedef boost::type_of::vector25< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class T> struct push_back<boost::type_of::vector25< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24>, T> { typedef boost::type_of::vector26< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class T> struct push_back<boost::type_of::vector26< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25>, T> { typedef boost::type_of::vector27< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class T> struct push_back<boost::type_of::vector27< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26>, T> { typedef boost::type_of::vector28< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class T> struct push_back<boost::type_of::vector28< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27>, T> { typedef boost::type_of::vector29< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class T> struct push_back<boost::type_of::vector29< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28>, T> { typedef boost::type_of::vector30< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class T> struct push_back<boost::type_of::vector30< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29>, T> { typedef boost::type_of::vector31< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class T> struct push_back<boost::type_of::vector31< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30>, T> { typedef boost::type_of::vector32< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class T> struct push_back<boost::type_of::vector32< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31>, T> { typedef boost::type_of::vector33< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class T> struct push_back<boost::type_of::vector33< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32>, T> { typedef boost::type_of::vector34< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class T> struct push_back<boost::type_of::vector34< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33>, T> { typedef boost::type_of::vector35< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class T> struct push_back<boost::type_of::vector35< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34>, T> { typedef boost::type_of::vector36< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class T> struct push_back<boost::type_of::vector36< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35>, T> { typedef boost::type_of::vector37< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class T> struct push_back<boost::type_of::vector37< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36>, T> { typedef boost::type_of::vector38< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class T> struct push_back<boost::type_of::vector38< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37>, T> { typedef boost::type_of::vector39< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class T> struct push_back<boost::type_of::vector39< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38>, T> { typedef boost::type_of::vector40< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class T> struct push_back<boost::type_of::vector40< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39>, T> { typedef boost::type_of::vector41< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class T> struct push_back<boost::type_of::vector41< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40>, T> { typedef boost::type_of::vector42< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class T> struct push_back<boost::type_of::vector42< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41>, T> { typedef boost::type_of::vector43< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class T> struct push_back<boost::type_of::vector43< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42>, T> { typedef boost::type_of::vector44< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class P43 , class T> struct push_back<boost::type_of::vector44< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43>, T> { typedef boost::type_of::vector45< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class P43 , class P44 , class T> struct push_back<boost::type_of::vector45< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44>, T> { typedef boost::type_of::vector46< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class P43 , class P44 , class P45 , class T> struct push_back<boost::type_of::vector46< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , P45>, T> { typedef boost::type_of::vector47< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , P45 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class P43 , class P44 , class P45 , class P46 , class T> struct push_back<boost::type_of::vector47< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , P45 , P46>, T> { typedef boost::type_of::vector48< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , P45 , P46 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class P43 , class P44 , class P45 , class P46 , class P47 , class T> struct push_back<boost::type_of::vector48< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , P45 , P46 , P47>, T> { typedef boost::type_of::vector49< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , P45 , P46 , P47 , T > type; };
        template< class P0 , class P1 , class P2 , class P3 , class P4 , class P5 , class P6 , class P7 , class P8 , class P9 , class P10 , class P11 , class P12 , class P13 , class P14 , class P15 , class P16 , class P17 , class P18 , class P19 , class P20 , class P21 , class P22 , class P23 , class P24 , class P25 , class P26 , class P27 , class P28 , class P29 , class P30 , class P31 , class P32 , class P33 , class P34 , class P35 , class P36 , class P37 , class P38 , class P39 , class P40 , class P41 , class P42 , class P43 , class P44 , class P45 , class P46 , class P47 , class P48 , class T> struct push_back<boost::type_of::vector49< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , P45 , P46 , P47 , P48>, T> { typedef boost::type_of::vector50< P0 , P1 , P2 , P3 , P4 , P5 , P6 , P7 , P8 , P9 , P10 , P11 , P12 , P13 , P14 , P15 , P16 , P17 , P18 , P19 , P20 , P21 , P22 , P23 , P24 , P25 , P26 , P27 , P28 , P29 , P30 , P31 , P32 , P33 , P34 , P35 , P36 , P37 , P38 , P39 , P40 , P41 , P42 , P43 , P44 , P45 , P46 , P47 , P48 , T > type; };
}}
