#ifndef BOOST_QVM_GEN_MAT_OPERATIONS4_HPP_INCLUDED
#define BOOST_QVM_GEN_MAT_OPERATIONS4_HPP_INCLUDED

// Copyright 2008-2022 <PERSON> and Reverge Studios, Inc.

// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

// This file was generated by a program. Do not edit manually.

#include <boost/qvm/assert.hpp>
#include <boost/qvm/deduce_mat.hpp>
#include <boost/qvm/deduce_vec.hpp>
#include <boost/qvm/error.hpp>
#include <boost/qvm/gen/mat_assign4.hpp>
#include <boost/qvm/quat_traits.hpp>
#include <boost/qvm/scalar_traits.hpp>
#include <boost/qvm/throw_exception.hpp>

namespace boost { namespace qvm {

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    deduce_mat2<A,B,4,4> >::type
operator+( A const & a, B const & b )
    {
    typedef typename deduce_mat2<A,B,4,4>::type R;
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==4);
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==4);
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a)+mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<0,1>(r,mat_traits<A>::template read_element<0,1>(a)+mat_traits<B>::template read_element<0,1>(b));
    write_mat_element<0,2>(r,mat_traits<A>::template read_element<0,2>(a)+mat_traits<B>::template read_element<0,2>(b));
    write_mat_element<0,3>(r,mat_traits<A>::template read_element<0,3>(a)+mat_traits<B>::template read_element<0,3>(b));
    write_mat_element<1,0>(r,mat_traits<A>::template read_element<1,0>(a)+mat_traits<B>::template read_element<1,0>(b));
    write_mat_element<1,1>(r,mat_traits<A>::template read_element<1,1>(a)+mat_traits<B>::template read_element<1,1>(b));
    write_mat_element<1,2>(r,mat_traits<A>::template read_element<1,2>(a)+mat_traits<B>::template read_element<1,2>(b));
    write_mat_element<1,3>(r,mat_traits<A>::template read_element<1,3>(a)+mat_traits<B>::template read_element<1,3>(b));
    write_mat_element<2,0>(r,mat_traits<A>::template read_element<2,0>(a)+mat_traits<B>::template read_element<2,0>(b));
    write_mat_element<2,1>(r,mat_traits<A>::template read_element<2,1>(a)+mat_traits<B>::template read_element<2,1>(b));
    write_mat_element<2,2>(r,mat_traits<A>::template read_element<2,2>(a)+mat_traits<B>::template read_element<2,2>(b));
    write_mat_element<2,3>(r,mat_traits<A>::template read_element<2,3>(a)+mat_traits<B>::template read_element<2,3>(b));
    write_mat_element<3,0>(r,mat_traits<A>::template read_element<3,0>(a)+mat_traits<B>::template read_element<3,0>(b));
    write_mat_element<3,1>(r,mat_traits<A>::template read_element<3,1>(a)+mat_traits<B>::template read_element<3,1>(b));
    write_mat_element<3,2>(r,mat_traits<A>::template read_element<3,2>(a)+mat_traits<B>::template read_element<3,2>(b));
    write_mat_element<3,3>(r,mat_traits<A>::template read_element<3,3>(a)+mat_traits<B>::template read_element<3,3>(b));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator+;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct plus_mm_defined;

    template <>
    struct
    plus_mm_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==1 && mat_traits<B>::cols==1,
    deduce_mat2<A,B,4,1> >::type
operator+( A const & a, B const & b )
    {
    typedef typename deduce_mat2<A,B,4,1>::type R;
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==4);
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==1);
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a)+mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<1,0>(r,mat_traits<A>::template read_element<1,0>(a)+mat_traits<B>::template read_element<1,0>(b));
    write_mat_element<2,0>(r,mat_traits<A>::template read_element<2,0>(a)+mat_traits<B>::template read_element<2,0>(b));
    write_mat_element<3,0>(r,mat_traits<A>::template read_element<3,0>(a)+mat_traits<B>::template read_element<3,0>(b));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator+;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct plus_mm_defined;

    template <>
    struct
    plus_mm_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==1 && mat_traits<B>::rows==1 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    deduce_mat2<A,B,1,4> >::type
operator+( A const & a, B const & b )
    {
    typedef typename deduce_mat2<A,B,1,4>::type R;
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==1);
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==4);
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a)+mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<0,1>(r,mat_traits<A>::template read_element<0,1>(a)+mat_traits<B>::template read_element<0,1>(b));
    write_mat_element<0,2>(r,mat_traits<A>::template read_element<0,2>(a)+mat_traits<B>::template read_element<0,2>(b));
    write_mat_element<0,3>(r,mat_traits<A>::template read_element<0,3>(a)+mat_traits<B>::template read_element<0,3>(b));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator+;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct plus_mm_defined;

    template <>
    struct
    plus_mm_defined<1,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    deduce_mat2<A,B,4,4> >::type
operator-( A const & a, B const & b )
    {
    typedef typename deduce_mat2<A,B,4,4>::type R;
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==4);
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==4);
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a)-mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<0,1>(r,mat_traits<A>::template read_element<0,1>(a)-mat_traits<B>::template read_element<0,1>(b));
    write_mat_element<0,2>(r,mat_traits<A>::template read_element<0,2>(a)-mat_traits<B>::template read_element<0,2>(b));
    write_mat_element<0,3>(r,mat_traits<A>::template read_element<0,3>(a)-mat_traits<B>::template read_element<0,3>(b));
    write_mat_element<1,0>(r,mat_traits<A>::template read_element<1,0>(a)-mat_traits<B>::template read_element<1,0>(b));
    write_mat_element<1,1>(r,mat_traits<A>::template read_element<1,1>(a)-mat_traits<B>::template read_element<1,1>(b));
    write_mat_element<1,2>(r,mat_traits<A>::template read_element<1,2>(a)-mat_traits<B>::template read_element<1,2>(b));
    write_mat_element<1,3>(r,mat_traits<A>::template read_element<1,3>(a)-mat_traits<B>::template read_element<1,3>(b));
    write_mat_element<2,0>(r,mat_traits<A>::template read_element<2,0>(a)-mat_traits<B>::template read_element<2,0>(b));
    write_mat_element<2,1>(r,mat_traits<A>::template read_element<2,1>(a)-mat_traits<B>::template read_element<2,1>(b));
    write_mat_element<2,2>(r,mat_traits<A>::template read_element<2,2>(a)-mat_traits<B>::template read_element<2,2>(b));
    write_mat_element<2,3>(r,mat_traits<A>::template read_element<2,3>(a)-mat_traits<B>::template read_element<2,3>(b));
    write_mat_element<3,0>(r,mat_traits<A>::template read_element<3,0>(a)-mat_traits<B>::template read_element<3,0>(b));
    write_mat_element<3,1>(r,mat_traits<A>::template read_element<3,1>(a)-mat_traits<B>::template read_element<3,1>(b));
    write_mat_element<3,2>(r,mat_traits<A>::template read_element<3,2>(a)-mat_traits<B>::template read_element<3,2>(b));
    write_mat_element<3,3>(r,mat_traits<A>::template read_element<3,3>(a)-mat_traits<B>::template read_element<3,3>(b));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator-;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct minus_mm_defined;

    template <>
    struct
    minus_mm_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==1 && mat_traits<B>::cols==1,
    deduce_mat2<A,B,4,1> >::type
operator-( A const & a, B const & b )
    {
    typedef typename deduce_mat2<A,B,4,1>::type R;
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==4);
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==1);
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a)-mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<1,0>(r,mat_traits<A>::template read_element<1,0>(a)-mat_traits<B>::template read_element<1,0>(b));
    write_mat_element<2,0>(r,mat_traits<A>::template read_element<2,0>(a)-mat_traits<B>::template read_element<2,0>(b));
    write_mat_element<3,0>(r,mat_traits<A>::template read_element<3,0>(a)-mat_traits<B>::template read_element<3,0>(b));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator-;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct minus_mm_defined;

    template <>
    struct
    minus_mm_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==1 && mat_traits<B>::rows==1 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    deduce_mat2<A,B,1,4> >::type
operator-( A const & a, B const & b )
    {
    typedef typename deduce_mat2<A,B,1,4>::type R;
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==1);
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==4);
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a)-mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<0,1>(r,mat_traits<A>::template read_element<0,1>(a)-mat_traits<B>::template read_element<0,1>(b));
    write_mat_element<0,2>(r,mat_traits<A>::template read_element<0,2>(a)-mat_traits<B>::template read_element<0,2>(b));
    write_mat_element<0,3>(r,mat_traits<A>::template read_element<0,3>(a)-mat_traits<B>::template read_element<0,3>(b));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator-;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct minus_mm_defined;

    template <>
    struct
    minus_mm_defined<1,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    A &>::type
operator+=( A & a, B const & b )
    {
    write_mat_element<0,0>(a,mat_traits<A>::template read_element<0,0>(a)+mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<0,1>(a,mat_traits<A>::template read_element<0,1>(a)+mat_traits<B>::template read_element<0,1>(b));
    write_mat_element<0,2>(a,mat_traits<A>::template read_element<0,2>(a)+mat_traits<B>::template read_element<0,2>(b));
    write_mat_element<0,3>(a,mat_traits<A>::template read_element<0,3>(a)+mat_traits<B>::template read_element<0,3>(b));
    write_mat_element<1,0>(a,mat_traits<A>::template read_element<1,0>(a)+mat_traits<B>::template read_element<1,0>(b));
    write_mat_element<1,1>(a,mat_traits<A>::template read_element<1,1>(a)+mat_traits<B>::template read_element<1,1>(b));
    write_mat_element<1,2>(a,mat_traits<A>::template read_element<1,2>(a)+mat_traits<B>::template read_element<1,2>(b));
    write_mat_element<1,3>(a,mat_traits<A>::template read_element<1,3>(a)+mat_traits<B>::template read_element<1,3>(b));
    write_mat_element<2,0>(a,mat_traits<A>::template read_element<2,0>(a)+mat_traits<B>::template read_element<2,0>(b));
    write_mat_element<2,1>(a,mat_traits<A>::template read_element<2,1>(a)+mat_traits<B>::template read_element<2,1>(b));
    write_mat_element<2,2>(a,mat_traits<A>::template read_element<2,2>(a)+mat_traits<B>::template read_element<2,2>(b));
    write_mat_element<2,3>(a,mat_traits<A>::template read_element<2,3>(a)+mat_traits<B>::template read_element<2,3>(b));
    write_mat_element<3,0>(a,mat_traits<A>::template read_element<3,0>(a)+mat_traits<B>::template read_element<3,0>(b));
    write_mat_element<3,1>(a,mat_traits<A>::template read_element<3,1>(a)+mat_traits<B>::template read_element<3,1>(b));
    write_mat_element<3,2>(a,mat_traits<A>::template read_element<3,2>(a)+mat_traits<B>::template read_element<3,2>(b));
    write_mat_element<3,3>(a,mat_traits<A>::template read_element<3,3>(a)+mat_traits<B>::template read_element<3,3>(b));
    return a;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator+=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct plus_eq_mm_defined;

    template <>
    struct
    plus_eq_mm_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==1 && mat_traits<B>::cols==1,
    A &>::type
operator+=( A & a, B const & b )
    {
    write_mat_element<0,0>(a,mat_traits<A>::template read_element<0,0>(a)+mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<1,0>(a,mat_traits<A>::template read_element<1,0>(a)+mat_traits<B>::template read_element<1,0>(b));
    write_mat_element<2,0>(a,mat_traits<A>::template read_element<2,0>(a)+mat_traits<B>::template read_element<2,0>(b));
    write_mat_element<3,0>(a,mat_traits<A>::template read_element<3,0>(a)+mat_traits<B>::template read_element<3,0>(b));
    return a;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator+=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct plus_eq_mm_defined;

    template <>
    struct
    plus_eq_mm_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==1 && mat_traits<B>::rows==1 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    A &>::type
operator+=( A & a, B const & b )
    {
    write_mat_element<0,0>(a,mat_traits<A>::template read_element<0,0>(a)+mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<0,1>(a,mat_traits<A>::template read_element<0,1>(a)+mat_traits<B>::template read_element<0,1>(b));
    write_mat_element<0,2>(a,mat_traits<A>::template read_element<0,2>(a)+mat_traits<B>::template read_element<0,2>(b));
    write_mat_element<0,3>(a,mat_traits<A>::template read_element<0,3>(a)+mat_traits<B>::template read_element<0,3>(b));
    return a;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator+=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct plus_eq_mm_defined;

    template <>
    struct
    plus_eq_mm_defined<1,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    A &>::type
operator-=( A & a, B const & b )
    {
    write_mat_element<0,0>(a,mat_traits<A>::template read_element<0,0>(a)-mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<0,1>(a,mat_traits<A>::template read_element<0,1>(a)-mat_traits<B>::template read_element<0,1>(b));
    write_mat_element<0,2>(a,mat_traits<A>::template read_element<0,2>(a)-mat_traits<B>::template read_element<0,2>(b));
    write_mat_element<0,3>(a,mat_traits<A>::template read_element<0,3>(a)-mat_traits<B>::template read_element<0,3>(b));
    write_mat_element<1,0>(a,mat_traits<A>::template read_element<1,0>(a)-mat_traits<B>::template read_element<1,0>(b));
    write_mat_element<1,1>(a,mat_traits<A>::template read_element<1,1>(a)-mat_traits<B>::template read_element<1,1>(b));
    write_mat_element<1,2>(a,mat_traits<A>::template read_element<1,2>(a)-mat_traits<B>::template read_element<1,2>(b));
    write_mat_element<1,3>(a,mat_traits<A>::template read_element<1,3>(a)-mat_traits<B>::template read_element<1,3>(b));
    write_mat_element<2,0>(a,mat_traits<A>::template read_element<2,0>(a)-mat_traits<B>::template read_element<2,0>(b));
    write_mat_element<2,1>(a,mat_traits<A>::template read_element<2,1>(a)-mat_traits<B>::template read_element<2,1>(b));
    write_mat_element<2,2>(a,mat_traits<A>::template read_element<2,2>(a)-mat_traits<B>::template read_element<2,2>(b));
    write_mat_element<2,3>(a,mat_traits<A>::template read_element<2,3>(a)-mat_traits<B>::template read_element<2,3>(b));
    write_mat_element<3,0>(a,mat_traits<A>::template read_element<3,0>(a)-mat_traits<B>::template read_element<3,0>(b));
    write_mat_element<3,1>(a,mat_traits<A>::template read_element<3,1>(a)-mat_traits<B>::template read_element<3,1>(b));
    write_mat_element<3,2>(a,mat_traits<A>::template read_element<3,2>(a)-mat_traits<B>::template read_element<3,2>(b));
    write_mat_element<3,3>(a,mat_traits<A>::template read_element<3,3>(a)-mat_traits<B>::template read_element<3,3>(b));
    return a;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator-=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct minus_eq_mm_defined;

    template <>
    struct
    minus_eq_mm_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==1 && mat_traits<B>::cols==1,
    A &>::type
operator-=( A & a, B const & b )
    {
    write_mat_element<0,0>(a,mat_traits<A>::template read_element<0,0>(a)-mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<1,0>(a,mat_traits<A>::template read_element<1,0>(a)-mat_traits<B>::template read_element<1,0>(b));
    write_mat_element<2,0>(a,mat_traits<A>::template read_element<2,0>(a)-mat_traits<B>::template read_element<2,0>(b));
    write_mat_element<3,0>(a,mat_traits<A>::template read_element<3,0>(a)-mat_traits<B>::template read_element<3,0>(b));
    return a;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator-=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct minus_eq_mm_defined;

    template <>
    struct
    minus_eq_mm_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==1 && mat_traits<B>::rows==1 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    A &>::type
operator-=( A & a, B const & b )
    {
    write_mat_element<0,0>(a,mat_traits<A>::template read_element<0,0>(a)-mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<0,1>(a,mat_traits<A>::template read_element<0,1>(a)-mat_traits<B>::template read_element<0,1>(b));
    write_mat_element<0,2>(a,mat_traits<A>::template read_element<0,2>(a)-mat_traits<B>::template read_element<0,2>(b));
    write_mat_element<0,3>(a,mat_traits<A>::template read_element<0,3>(a)-mat_traits<B>::template read_element<0,3>(b));
    return a;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator-=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct minus_eq_mm_defined;

    template <>
    struct
    minus_eq_mm_defined<1,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<A>::cols==4 && is_scalar<B>::value,
    deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols> >::type
operator*( A const & a, B b )
    {
    typedef typename deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols>::type R;
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a)*b);
    write_mat_element<0,1>(r,mat_traits<A>::template read_element<0,1>(a)*b);
    write_mat_element<0,2>(r,mat_traits<A>::template read_element<0,2>(a)*b);
    write_mat_element<0,3>(r,mat_traits<A>::template read_element<0,3>(a)*b);
    write_mat_element<1,0>(r,mat_traits<A>::template read_element<1,0>(a)*b);
    write_mat_element<1,1>(r,mat_traits<A>::template read_element<1,1>(a)*b);
    write_mat_element<1,2>(r,mat_traits<A>::template read_element<1,2>(a)*b);
    write_mat_element<1,3>(r,mat_traits<A>::template read_element<1,3>(a)*b);
    write_mat_element<2,0>(r,mat_traits<A>::template read_element<2,0>(a)*b);
    write_mat_element<2,1>(r,mat_traits<A>::template read_element<2,1>(a)*b);
    write_mat_element<2,2>(r,mat_traits<A>::template read_element<2,2>(a)*b);
    write_mat_element<2,3>(r,mat_traits<A>::template read_element<2,3>(a)*b);
    write_mat_element<3,0>(r,mat_traits<A>::template read_element<3,0>(a)*b);
    write_mat_element<3,1>(r,mat_traits<A>::template read_element<3,1>(a)*b);
    write_mat_element<3,2>(r,mat_traits<A>::template read_element<3,2>(a)*b);
    write_mat_element<3,3>(r,mat_traits<A>::template read_element<3,3>(a)*b);
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator*;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct mul_ms_defined;

    template <>
    struct
    mul_ms_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    is_scalar<A>::value && mat_traits<B>::rows==4 && mat_traits<B>::cols==4,
    deduce_mat2<A,B,mat_traits<B>::rows,mat_traits<B>::cols> >::type
operator*( A a, B const & b )
    {
    typedef typename deduce_mat2<A,B,mat_traits<B>::rows,mat_traits<B>::cols>::type R;
    R r;
    write_mat_element<0,0>(r,a*mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<0,1>(r,a*mat_traits<B>::template read_element<0,1>(b));
    write_mat_element<0,2>(r,a*mat_traits<B>::template read_element<0,2>(b));
    write_mat_element<0,3>(r,a*mat_traits<B>::template read_element<0,3>(b));
    write_mat_element<1,0>(r,a*mat_traits<B>::template read_element<1,0>(b));
    write_mat_element<1,1>(r,a*mat_traits<B>::template read_element<1,1>(b));
    write_mat_element<1,2>(r,a*mat_traits<B>::template read_element<1,2>(b));
    write_mat_element<1,3>(r,a*mat_traits<B>::template read_element<1,3>(b));
    write_mat_element<2,0>(r,a*mat_traits<B>::template read_element<2,0>(b));
    write_mat_element<2,1>(r,a*mat_traits<B>::template read_element<2,1>(b));
    write_mat_element<2,2>(r,a*mat_traits<B>::template read_element<2,2>(b));
    write_mat_element<2,3>(r,a*mat_traits<B>::template read_element<2,3>(b));
    write_mat_element<3,0>(r,a*mat_traits<B>::template read_element<3,0>(b));
    write_mat_element<3,1>(r,a*mat_traits<B>::template read_element<3,1>(b));
    write_mat_element<3,2>(r,a*mat_traits<B>::template read_element<3,2>(b));
    write_mat_element<3,3>(r,a*mat_traits<B>::template read_element<3,3>(b));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator*;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct mul_sm_defined;

    template <>
    struct
    mul_sm_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<A>::cols==1 && is_scalar<B>::value,
    deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols> >::type
operator*( A const & a, B b )
    {
    typedef typename deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols>::type R;
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a)*b);
    write_mat_element<1,0>(r,mat_traits<A>::template read_element<1,0>(a)*b);
    write_mat_element<2,0>(r,mat_traits<A>::template read_element<2,0>(a)*b);
    write_mat_element<3,0>(r,mat_traits<A>::template read_element<3,0>(a)*b);
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator*;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct mul_ms_defined;

    template <>
    struct
    mul_ms_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    is_scalar<A>::value && mat_traits<B>::rows==4 && mat_traits<B>::cols==1,
    deduce_mat2<A,B,mat_traits<B>::rows,mat_traits<B>::cols> >::type
operator*( A a, B const & b )
    {
    typedef typename deduce_mat2<A,B,mat_traits<B>::rows,mat_traits<B>::cols>::type R;
    R r;
    write_mat_element<0,0>(r,a*mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<1,0>(r,a*mat_traits<B>::template read_element<1,0>(b));
    write_mat_element<2,0>(r,a*mat_traits<B>::template read_element<2,0>(b));
    write_mat_element<3,0>(r,a*mat_traits<B>::template read_element<3,0>(b));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator*;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct mul_sm_defined;

    template <>
    struct
    mul_sm_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==1 && mat_traits<A>::cols==4 && is_scalar<B>::value,
    deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols> >::type
operator*( A const & a, B b )
    {
    typedef typename deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols>::type R;
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a)*b);
    write_mat_element<0,1>(r,mat_traits<A>::template read_element<0,1>(a)*b);
    write_mat_element<0,2>(r,mat_traits<A>::template read_element<0,2>(a)*b);
    write_mat_element<0,3>(r,mat_traits<A>::template read_element<0,3>(a)*b);
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator*;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct mul_ms_defined;

    template <>
    struct
    mul_ms_defined<1,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    is_scalar<A>::value && mat_traits<B>::rows==1 && mat_traits<B>::cols==4,
    deduce_mat2<A,B,mat_traits<B>::rows,mat_traits<B>::cols> >::type
operator*( A a, B const & b )
    {
    typedef typename deduce_mat2<A,B,mat_traits<B>::rows,mat_traits<B>::cols>::type R;
    R r;
    write_mat_element<0,0>(r,a*mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<0,1>(r,a*mat_traits<B>::template read_element<0,1>(b));
    write_mat_element<0,2>(r,a*mat_traits<B>::template read_element<0,2>(b));
    write_mat_element<0,3>(r,a*mat_traits<B>::template read_element<0,3>(b));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator*;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct mul_sm_defined;

    template <>
    struct
    mul_sm_defined<1,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<A>::cols==4 && is_scalar<B>::value,
    A &>::type
operator*=( A & a, B b )
    {
    write_mat_element<0,0>(a,mat_traits<A>::template read_element<0,0>(a)*b);
    write_mat_element<0,1>(a,mat_traits<A>::template read_element<0,1>(a)*b);
    write_mat_element<0,2>(a,mat_traits<A>::template read_element<0,2>(a)*b);
    write_mat_element<0,3>(a,mat_traits<A>::template read_element<0,3>(a)*b);
    write_mat_element<1,0>(a,mat_traits<A>::template read_element<1,0>(a)*b);
    write_mat_element<1,1>(a,mat_traits<A>::template read_element<1,1>(a)*b);
    write_mat_element<1,2>(a,mat_traits<A>::template read_element<1,2>(a)*b);
    write_mat_element<1,3>(a,mat_traits<A>::template read_element<1,3>(a)*b);
    write_mat_element<2,0>(a,mat_traits<A>::template read_element<2,0>(a)*b);
    write_mat_element<2,1>(a,mat_traits<A>::template read_element<2,1>(a)*b);
    write_mat_element<2,2>(a,mat_traits<A>::template read_element<2,2>(a)*b);
    write_mat_element<2,3>(a,mat_traits<A>::template read_element<2,3>(a)*b);
    write_mat_element<3,0>(a,mat_traits<A>::template read_element<3,0>(a)*b);
    write_mat_element<3,1>(a,mat_traits<A>::template read_element<3,1>(a)*b);
    write_mat_element<3,2>(a,mat_traits<A>::template read_element<3,2>(a)*b);
    write_mat_element<3,3>(a,mat_traits<A>::template read_element<3,3>(a)*b);
    return a;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator*=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct mul_eq_ms_defined;

    template <>
    struct
    mul_eq_ms_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<A>::cols==1 && is_scalar<B>::value,
    A &>::type
operator*=( A & a, B b )
    {
    write_mat_element<0,0>(a,mat_traits<A>::template read_element<0,0>(a)*b);
    write_mat_element<1,0>(a,mat_traits<A>::template read_element<1,0>(a)*b);
    write_mat_element<2,0>(a,mat_traits<A>::template read_element<2,0>(a)*b);
    write_mat_element<3,0>(a,mat_traits<A>::template read_element<3,0>(a)*b);
    return a;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator*=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct mul_eq_ms_defined;

    template <>
    struct
    mul_eq_ms_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==1 && mat_traits<A>::cols==4 && is_scalar<B>::value,
    A &>::type
operator*=( A & a, B b )
    {
    write_mat_element<0,0>(a,mat_traits<A>::template read_element<0,0>(a)*b);
    write_mat_element<0,1>(a,mat_traits<A>::template read_element<0,1>(a)*b);
    write_mat_element<0,2>(a,mat_traits<A>::template read_element<0,2>(a)*b);
    write_mat_element<0,3>(a,mat_traits<A>::template read_element<0,3>(a)*b);
    return a;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator*=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct mul_eq_ms_defined;

    template <>
    struct
    mul_eq_ms_defined<1,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<A>::cols==4 && is_scalar<B>::value,
    deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols> >::type
operator/( A const & a, B b )
    {
    typedef typename deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols>::type R;
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a)/b);
    write_mat_element<0,1>(r,mat_traits<A>::template read_element<0,1>(a)/b);
    write_mat_element<0,2>(r,mat_traits<A>::template read_element<0,2>(a)/b);
    write_mat_element<0,3>(r,mat_traits<A>::template read_element<0,3>(a)/b);
    write_mat_element<1,0>(r,mat_traits<A>::template read_element<1,0>(a)/b);
    write_mat_element<1,1>(r,mat_traits<A>::template read_element<1,1>(a)/b);
    write_mat_element<1,2>(r,mat_traits<A>::template read_element<1,2>(a)/b);
    write_mat_element<1,3>(r,mat_traits<A>::template read_element<1,3>(a)/b);
    write_mat_element<2,0>(r,mat_traits<A>::template read_element<2,0>(a)/b);
    write_mat_element<2,1>(r,mat_traits<A>::template read_element<2,1>(a)/b);
    write_mat_element<2,2>(r,mat_traits<A>::template read_element<2,2>(a)/b);
    write_mat_element<2,3>(r,mat_traits<A>::template read_element<2,3>(a)/b);
    write_mat_element<3,0>(r,mat_traits<A>::template read_element<3,0>(a)/b);
    write_mat_element<3,1>(r,mat_traits<A>::template read_element<3,1>(a)/b);
    write_mat_element<3,2>(r,mat_traits<A>::template read_element<3,2>(a)/b);
    write_mat_element<3,3>(r,mat_traits<A>::template read_element<3,3>(a)/b);
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator/;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct div_ms_defined;

    template <>
    struct
    div_ms_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    is_scalar<A>::value && mat_traits<B>::rows==4 && mat_traits<B>::cols==4,
    deduce_mat2<A,B,mat_traits<B>::rows,mat_traits<B>::cols> >::type
operator/( A a, B const & b )
    {
    typedef typename deduce_mat2<A,B,mat_traits<B>::rows,mat_traits<B>::cols>::type R;
    R r;
    write_mat_element<0,0>(r,a/mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<0,1>(r,a/mat_traits<B>::template read_element<0,1>(b));
    write_mat_element<0,2>(r,a/mat_traits<B>::template read_element<0,2>(b));
    write_mat_element<0,3>(r,a/mat_traits<B>::template read_element<0,3>(b));
    write_mat_element<1,0>(r,a/mat_traits<B>::template read_element<1,0>(b));
    write_mat_element<1,1>(r,a/mat_traits<B>::template read_element<1,1>(b));
    write_mat_element<1,2>(r,a/mat_traits<B>::template read_element<1,2>(b));
    write_mat_element<1,3>(r,a/mat_traits<B>::template read_element<1,3>(b));
    write_mat_element<2,0>(r,a/mat_traits<B>::template read_element<2,0>(b));
    write_mat_element<2,1>(r,a/mat_traits<B>::template read_element<2,1>(b));
    write_mat_element<2,2>(r,a/mat_traits<B>::template read_element<2,2>(b));
    write_mat_element<2,3>(r,a/mat_traits<B>::template read_element<2,3>(b));
    write_mat_element<3,0>(r,a/mat_traits<B>::template read_element<3,0>(b));
    write_mat_element<3,1>(r,a/mat_traits<B>::template read_element<3,1>(b));
    write_mat_element<3,2>(r,a/mat_traits<B>::template read_element<3,2>(b));
    write_mat_element<3,3>(r,a/mat_traits<B>::template read_element<3,3>(b));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator/;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct div_sm_defined;

    template <>
    struct
    div_sm_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<A>::cols==1 && is_scalar<B>::value,
    deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols> >::type
operator/( A const & a, B b )
    {
    typedef typename deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols>::type R;
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a)/b);
    write_mat_element<1,0>(r,mat_traits<A>::template read_element<1,0>(a)/b);
    write_mat_element<2,0>(r,mat_traits<A>::template read_element<2,0>(a)/b);
    write_mat_element<3,0>(r,mat_traits<A>::template read_element<3,0>(a)/b);
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator/;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct div_ms_defined;

    template <>
    struct
    div_ms_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    is_scalar<A>::value && mat_traits<B>::rows==4 && mat_traits<B>::cols==1,
    deduce_mat2<A,B,mat_traits<B>::rows,mat_traits<B>::cols> >::type
operator/( A a, B const & b )
    {
    typedef typename deduce_mat2<A,B,mat_traits<B>::rows,mat_traits<B>::cols>::type R;
    R r;
    write_mat_element<0,0>(r,a/mat_traits<B>::template read_element<0,0>(b));
    write_mat_element<1,0>(r,a/mat_traits<B>::template read_element<1,0>(b));
    write_mat_element<2,0>(r,a/mat_traits<B>::template read_element<2,0>(b));
    write_mat_element<3,0>(r,a/mat_traits<B>::template read_element<3,0>(b));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator/;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct div_sm_defined;

    template <>
    struct
    div_sm_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==1 && mat_traits<A>::cols==4 && is_scalar<B>::value,
    deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols> >::type
operator/( A const & a, B b )
    {
    typedef typename deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols>::type R;
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a)/b);
    write_mat_element<0,1>(r,mat_traits<A>::template read_element<0,1>(a)/b);
    write_mat_element<0,2>(r,mat_traits<A>::template read_element<0,2>(a)/b);
    write_mat_element<0,3>(r,mat_traits<A>::template read_element<0,3>(a)/b);
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator/;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct div_ms_defined;

    template <>
    struct
    div_ms_defined<1,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<A>::cols==4 && is_scalar<B>::value,
    A &>::type
operator/=( A & a, B b )
    {
    write_mat_element<0,0>(a,mat_traits<A>::template read_element<0,0>(a)/b);
    write_mat_element<0,1>(a,mat_traits<A>::template read_element<0,1>(a)/b);
    write_mat_element<0,2>(a,mat_traits<A>::template read_element<0,2>(a)/b);
    write_mat_element<0,3>(a,mat_traits<A>::template read_element<0,3>(a)/b);
    write_mat_element<1,0>(a,mat_traits<A>::template read_element<1,0>(a)/b);
    write_mat_element<1,1>(a,mat_traits<A>::template read_element<1,1>(a)/b);
    write_mat_element<1,2>(a,mat_traits<A>::template read_element<1,2>(a)/b);
    write_mat_element<1,3>(a,mat_traits<A>::template read_element<1,3>(a)/b);
    write_mat_element<2,0>(a,mat_traits<A>::template read_element<2,0>(a)/b);
    write_mat_element<2,1>(a,mat_traits<A>::template read_element<2,1>(a)/b);
    write_mat_element<2,2>(a,mat_traits<A>::template read_element<2,2>(a)/b);
    write_mat_element<2,3>(a,mat_traits<A>::template read_element<2,3>(a)/b);
    write_mat_element<3,0>(a,mat_traits<A>::template read_element<3,0>(a)/b);
    write_mat_element<3,1>(a,mat_traits<A>::template read_element<3,1>(a)/b);
    write_mat_element<3,2>(a,mat_traits<A>::template read_element<3,2>(a)/b);
    write_mat_element<3,3>(a,mat_traits<A>::template read_element<3,3>(a)/b);
    return a;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator/=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct div_eq_ms_defined;

    template <>
    struct
    div_eq_ms_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<A>::cols==1 && is_scalar<B>::value,
    A &>::type
operator/=( A & a, B b )
    {
    write_mat_element<0,0>(a,mat_traits<A>::template read_element<0,0>(a)/b);
    write_mat_element<1,0>(a,mat_traits<A>::template read_element<1,0>(a)/b);
    write_mat_element<2,0>(a,mat_traits<A>::template read_element<2,0>(a)/b);
    write_mat_element<3,0>(a,mat_traits<A>::template read_element<3,0>(a)/b);
    return a;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator/=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct div_eq_ms_defined;

    template <>
    struct
    div_eq_ms_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==1 && mat_traits<A>::cols==4 && is_scalar<B>::value,
    A &>::type
operator/=( A & a, B b )
    {
    write_mat_element<0,0>(a,mat_traits<A>::template read_element<0,0>(a)/b);
    write_mat_element<0,1>(a,mat_traits<A>::template read_element<0,1>(a)/b);
    write_mat_element<0,2>(a,mat_traits<A>::template read_element<0,2>(a)/b);
    write_mat_element<0,3>(a,mat_traits<A>::template read_element<0,3>(a)/b);
    return a;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator/=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct div_eq_ms_defined;

    template <>
    struct
    div_eq_ms_defined<1,4>
        {
        static bool const value=true;
        };
    }

template <class R,class A>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<R>::rows==4 && mat_traits<A>::rows==4 &&
    mat_traits<R>::cols==4 && mat_traits<A>::cols==4,
    R>::type
convert_to( A const & a )
    {
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a));
    write_mat_element<0,1>(r,mat_traits<A>::template read_element<0,1>(a));
    write_mat_element<0,2>(r,mat_traits<A>::template read_element<0,2>(a));
    write_mat_element<0,3>(r,mat_traits<A>::template read_element<0,3>(a));
    write_mat_element<1,0>(r,mat_traits<A>::template read_element<1,0>(a));
    write_mat_element<1,1>(r,mat_traits<A>::template read_element<1,1>(a));
    write_mat_element<1,2>(r,mat_traits<A>::template read_element<1,2>(a));
    write_mat_element<1,3>(r,mat_traits<A>::template read_element<1,3>(a));
    write_mat_element<2,0>(r,mat_traits<A>::template read_element<2,0>(a));
    write_mat_element<2,1>(r,mat_traits<A>::template read_element<2,1>(a));
    write_mat_element<2,2>(r,mat_traits<A>::template read_element<2,2>(a));
    write_mat_element<2,3>(r,mat_traits<A>::template read_element<2,3>(a));
    write_mat_element<3,0>(r,mat_traits<A>::template read_element<3,0>(a));
    write_mat_element<3,1>(r,mat_traits<A>::template read_element<3,1>(a));
    write_mat_element<3,2>(r,mat_traits<A>::template read_element<3,2>(a));
    write_mat_element<3,3>(r,mat_traits<A>::template read_element<3,3>(a));
    return r;
    }

template <class R,class A>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE
typename enable_if_c<
    is_mat<R>::value && is_quat<A>::value &&
    mat_traits<R>::rows==4 && mat_traits<R>::cols==4,
    R>::type
convert_to( A const & q )
    {
    typedef typename mat_traits<R>::scalar_type T;
    T const a=quat_traits<A>::template read_element<0>(q);
    T const b=quat_traits<A>::template read_element<1>(q);
    T const c=quat_traits<A>::template read_element<2>(q);
    T const d=quat_traits<A>::template read_element<3>(q);
    T const bb = b*b;
    T const cc = c*c;
    T const dd = d*d;
    T const bc = b*c;
    T const bd = b*d;
    T const cd = c*d;
    T const ab = a*b;
    T const ac = a*c;
    T const ad = a*d;
    T const zero = scalar_traits<T>::value(0);
    T const one = scalar_traits<T>::value(1);
    T const two = one+one;
    R r;
    write_mat_element<0,0>(r,one - two*(cc+dd));
    write_mat_element<0,1>(r,two*(bc-ad));
    write_mat_element<0,2>(r,two*(bd+ac));
    write_mat_element<0,3>(r,zero);
    write_mat_element<1,0>(r,two*(bc+ad));
    write_mat_element<1,1>(r,one - two*(bb+dd));
    write_mat_element<1,2>(r,two*(cd-ab));
    write_mat_element<1,3>(r,zero);
    write_mat_element<2,0>(r,two*(bd-ac));
    write_mat_element<2,1>(r,two*(cd+ab));
    write_mat_element<2,2>(r,one - two*(bb+cc));
    write_mat_element<2,3>(r,zero);
    write_mat_element<3,0>(r,zero);
    write_mat_element<3,1>(r,zero);
    write_mat_element<3,2>(r,zero);
    write_mat_element<3,3>(r,one);
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::convert_to;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct convert_to_m_defined;

    template <>
    struct
    convert_to_m_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class R,class A>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<R>::rows==4 && mat_traits<A>::rows==4 &&
    mat_traits<R>::cols==1 && mat_traits<A>::cols==1,
    R>::type
convert_to( A const & a )
    {
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a));
    write_mat_element<1,0>(r,mat_traits<A>::template read_element<1,0>(a));
    write_mat_element<2,0>(r,mat_traits<A>::template read_element<2,0>(a));
    write_mat_element<3,0>(r,mat_traits<A>::template read_element<3,0>(a));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::convert_to;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct convert_to_m_defined;

    template <>
    struct
    convert_to_m_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class R,class A>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<R>::rows==1 && mat_traits<A>::rows==1 &&
    mat_traits<R>::cols==4 && mat_traits<A>::cols==4,
    R>::type
convert_to( A const & a )
    {
    R r;
    write_mat_element<0,0>(r,mat_traits<A>::template read_element<0,0>(a));
    write_mat_element<0,1>(r,mat_traits<A>::template read_element<0,1>(a));
    write_mat_element<0,2>(r,mat_traits<A>::template read_element<0,2>(a));
    write_mat_element<0,3>(r,mat_traits<A>::template read_element<0,3>(a));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::convert_to;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct convert_to_m_defined;

    template <>
    struct
    convert_to_m_defined<1,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    bool>::type
operator==( A const & a, B const & b )
    {
    return
        mat_traits<A>::template read_element<0,0>(a)==mat_traits<B>::template read_element<0,0>(b) &&
        mat_traits<A>::template read_element<0,1>(a)==mat_traits<B>::template read_element<0,1>(b) &&
        mat_traits<A>::template read_element<0,2>(a)==mat_traits<B>::template read_element<0,2>(b) &&
        mat_traits<A>::template read_element<0,3>(a)==mat_traits<B>::template read_element<0,3>(b) &&
        mat_traits<A>::template read_element<1,0>(a)==mat_traits<B>::template read_element<1,0>(b) &&
        mat_traits<A>::template read_element<1,1>(a)==mat_traits<B>::template read_element<1,1>(b) &&
        mat_traits<A>::template read_element<1,2>(a)==mat_traits<B>::template read_element<1,2>(b) &&
        mat_traits<A>::template read_element<1,3>(a)==mat_traits<B>::template read_element<1,3>(b) &&
        mat_traits<A>::template read_element<2,0>(a)==mat_traits<B>::template read_element<2,0>(b) &&
        mat_traits<A>::template read_element<2,1>(a)==mat_traits<B>::template read_element<2,1>(b) &&
        mat_traits<A>::template read_element<2,2>(a)==mat_traits<B>::template read_element<2,2>(b) &&
        mat_traits<A>::template read_element<2,3>(a)==mat_traits<B>::template read_element<2,3>(b) &&
        mat_traits<A>::template read_element<3,0>(a)==mat_traits<B>::template read_element<3,0>(b) &&
        mat_traits<A>::template read_element<3,1>(a)==mat_traits<B>::template read_element<3,1>(b) &&
        mat_traits<A>::template read_element<3,2>(a)==mat_traits<B>::template read_element<3,2>(b) &&
        mat_traits<A>::template read_element<3,3>(a)==mat_traits<B>::template read_element<3,3>(b);
    }

namespace
sfinae
    {
    using ::boost::qvm::operator==;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct eq_mm_defined;

    template <>
    struct
    eq_mm_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==1 && mat_traits<B>::cols==1,
    bool>::type
operator==( A const & a, B const & b )
    {
    return
        mat_traits<A>::template read_element<0,0>(a)==mat_traits<B>::template read_element<0,0>(b) &&
        mat_traits<A>::template read_element<1,0>(a)==mat_traits<B>::template read_element<1,0>(b) &&
        mat_traits<A>::template read_element<2,0>(a)==mat_traits<B>::template read_element<2,0>(b) &&
        mat_traits<A>::template read_element<3,0>(a)==mat_traits<B>::template read_element<3,0>(b);
    }

namespace
sfinae
    {
    using ::boost::qvm::operator==;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct eq_mm_defined;

    template <>
    struct
    eq_mm_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==1 && mat_traits<B>::rows==1 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    bool>::type
operator==( A const & a, B const & b )
    {
    return
        mat_traits<A>::template read_element<0,0>(a)==mat_traits<B>::template read_element<0,0>(b) &&
        mat_traits<A>::template read_element<0,1>(a)==mat_traits<B>::template read_element<0,1>(b) &&
        mat_traits<A>::template read_element<0,2>(a)==mat_traits<B>::template read_element<0,2>(b) &&
        mat_traits<A>::template read_element<0,3>(a)==mat_traits<B>::template read_element<0,3>(b);
    }

namespace
sfinae
    {
    using ::boost::qvm::operator==;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct eq_mm_defined;

    template <>
    struct
    eq_mm_defined<1,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    bool>::type
operator!=( A const & a, B const & b )
    {
    return
        !(mat_traits<A>::template read_element<0,0>(a)==mat_traits<B>::template read_element<0,0>(b)) ||
        !(mat_traits<A>::template read_element<0,1>(a)==mat_traits<B>::template read_element<0,1>(b)) ||
        !(mat_traits<A>::template read_element<0,2>(a)==mat_traits<B>::template read_element<0,2>(b)) ||
        !(mat_traits<A>::template read_element<0,3>(a)==mat_traits<B>::template read_element<0,3>(b)) ||
        !(mat_traits<A>::template read_element<1,0>(a)==mat_traits<B>::template read_element<1,0>(b)) ||
        !(mat_traits<A>::template read_element<1,1>(a)==mat_traits<B>::template read_element<1,1>(b)) ||
        !(mat_traits<A>::template read_element<1,2>(a)==mat_traits<B>::template read_element<1,2>(b)) ||
        !(mat_traits<A>::template read_element<1,3>(a)==mat_traits<B>::template read_element<1,3>(b)) ||
        !(mat_traits<A>::template read_element<2,0>(a)==mat_traits<B>::template read_element<2,0>(b)) ||
        !(mat_traits<A>::template read_element<2,1>(a)==mat_traits<B>::template read_element<2,1>(b)) ||
        !(mat_traits<A>::template read_element<2,2>(a)==mat_traits<B>::template read_element<2,2>(b)) ||
        !(mat_traits<A>::template read_element<2,3>(a)==mat_traits<B>::template read_element<2,3>(b)) ||
        !(mat_traits<A>::template read_element<3,0>(a)==mat_traits<B>::template read_element<3,0>(b)) ||
        !(mat_traits<A>::template read_element<3,1>(a)==mat_traits<B>::template read_element<3,1>(b)) ||
        !(mat_traits<A>::template read_element<3,2>(a)==mat_traits<B>::template read_element<3,2>(b)) ||
        !(mat_traits<A>::template read_element<3,3>(a)==mat_traits<B>::template read_element<3,3>(b));
    }

namespace
sfinae
    {
    using ::boost::qvm::operator!=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct neq_mm_defined;

    template <>
    struct
    neq_mm_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==1 && mat_traits<B>::cols==1,
    bool>::type
operator!=( A const & a, B const & b )
    {
    return
        !(mat_traits<A>::template read_element<0,0>(a)==mat_traits<B>::template read_element<0,0>(b)) ||
        !(mat_traits<A>::template read_element<1,0>(a)==mat_traits<B>::template read_element<1,0>(b)) ||
        !(mat_traits<A>::template read_element<2,0>(a)==mat_traits<B>::template read_element<2,0>(b)) ||
        !(mat_traits<A>::template read_element<3,0>(a)==mat_traits<B>::template read_element<3,0>(b));
    }

namespace
sfinae
    {
    using ::boost::qvm::operator!=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct neq_mm_defined;

    template <>
    struct
    neq_mm_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==1 && mat_traits<B>::rows==1 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    bool>::type
operator!=( A const & a, B const & b )
    {
    return
        !(mat_traits<A>::template read_element<0,0>(a)==mat_traits<B>::template read_element<0,0>(b)) ||
        !(mat_traits<A>::template read_element<0,1>(a)==mat_traits<B>::template read_element<0,1>(b)) ||
        !(mat_traits<A>::template read_element<0,2>(a)==mat_traits<B>::template read_element<0,2>(b)) ||
        !(mat_traits<A>::template read_element<0,3>(a)==mat_traits<B>::template read_element<0,3>(b));
    }

namespace
sfinae
    {
    using ::boost::qvm::operator!=;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct neq_mm_defined;

    template <>
    struct
    neq_mm_defined<1,4>
        {
        static bool const value=true;
        };
    }

template <class A>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<A>::cols==4,
    deduce_mat<A> >::type
operator-( A const & a )
    {
    typedef typename deduce_mat<A>::type R;
    R r;
    write_mat_element<0,0>(r,-mat_traits<A>::template read_element<0,0>(a));
    write_mat_element<0,1>(r,-mat_traits<A>::template read_element<0,1>(a));
    write_mat_element<0,2>(r,-mat_traits<A>::template read_element<0,2>(a));
    write_mat_element<0,3>(r,-mat_traits<A>::template read_element<0,3>(a));
    write_mat_element<1,0>(r,-mat_traits<A>::template read_element<1,0>(a));
    write_mat_element<1,1>(r,-mat_traits<A>::template read_element<1,1>(a));
    write_mat_element<1,2>(r,-mat_traits<A>::template read_element<1,2>(a));
    write_mat_element<1,3>(r,-mat_traits<A>::template read_element<1,3>(a));
    write_mat_element<2,0>(r,-mat_traits<A>::template read_element<2,0>(a));
    write_mat_element<2,1>(r,-mat_traits<A>::template read_element<2,1>(a));
    write_mat_element<2,2>(r,-mat_traits<A>::template read_element<2,2>(a));
    write_mat_element<2,3>(r,-mat_traits<A>::template read_element<2,3>(a));
    write_mat_element<3,0>(r,-mat_traits<A>::template read_element<3,0>(a));
    write_mat_element<3,1>(r,-mat_traits<A>::template read_element<3,1>(a));
    write_mat_element<3,2>(r,-mat_traits<A>::template read_element<3,2>(a));
    write_mat_element<3,3>(r,-mat_traits<A>::template read_element<3,3>(a));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator-;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct minus_m_defined;

    template <>
    struct
    minus_m_defined<4,4>
        {
        static bool const value=true;
        };
    }

template <class A>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<A>::cols==1,
    deduce_mat<A> >::type
operator-( A const & a )
    {
    typedef typename deduce_mat<A>::type R;
    R r;
    write_mat_element<0,0>(r,-mat_traits<A>::template read_element<0,0>(a));
    write_mat_element<1,0>(r,-mat_traits<A>::template read_element<1,0>(a));
    write_mat_element<2,0>(r,-mat_traits<A>::template read_element<2,0>(a));
    write_mat_element<3,0>(r,-mat_traits<A>::template read_element<3,0>(a));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator-;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct minus_m_defined;

    template <>
    struct
    minus_m_defined<4,1>
        {
        static bool const value=true;
        };
    }

template <class A>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==1 && mat_traits<A>::cols==4,
    deduce_mat<A> >::type
operator-( A const & a )
    {
    typedef typename deduce_mat<A>::type R;
    R r;
    write_mat_element<0,0>(r,-mat_traits<A>::template read_element<0,0>(a));
    write_mat_element<0,1>(r,-mat_traits<A>::template read_element<0,1>(a));
    write_mat_element<0,2>(r,-mat_traits<A>::template read_element<0,2>(a));
    write_mat_element<0,3>(r,-mat_traits<A>::template read_element<0,3>(a));
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator-;
    }

namespace
qvm_detail
    {
    template <int R,int C>
    struct minus_m_defined;

    template <>
    struct
    minus_m_defined<1,4>
        {
        static bool const value=true;
        };
    }

template <class A>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<A>::cols==4,
    typename mat_traits<A>::scalar_type>::type
determinant( A const & a )
    {
    typedef typename mat_traits<A>::scalar_type T;
    T const a00=mat_traits<A>::template read_element<0,0>(a);
    T const a01=mat_traits<A>::template read_element<0,1>(a);
    T const a02=mat_traits<A>::template read_element<0,2>(a);
    T const a03=mat_traits<A>::template read_element<0,3>(a);
    T const a10=mat_traits<A>::template read_element<1,0>(a);
    T const a11=mat_traits<A>::template read_element<1,1>(a);
    T const a12=mat_traits<A>::template read_element<1,2>(a);
    T const a13=mat_traits<A>::template read_element<1,3>(a);
    T const a20=mat_traits<A>::template read_element<2,0>(a);
    T const a21=mat_traits<A>::template read_element<2,1>(a);
    T const a22=mat_traits<A>::template read_element<2,2>(a);
    T const a23=mat_traits<A>::template read_element<2,3>(a);
    T const a30=mat_traits<A>::template read_element<3,0>(a);
    T const a31=mat_traits<A>::template read_element<3,1>(a);
    T const a32=mat_traits<A>::template read_element<3,2>(a);
    T const a33=mat_traits<A>::template read_element<3,3>(a);
    T det=(a00*(a11*(a22*a33-a23*a32)-a12*(a21*a33-a23*a31)+a13*(a21*a32-a22*a31))-a01*(a10*(a22*a33-a23*a32)-a12*(a20*a33-a23*a30)+a13*(a20*a32-a22*a30))+a02*(a10*(a21*a33-a23*a31)-a11*(a20*a33-a23*a30)+a13*(a20*a31-a21*a30))-a03*(a10*(a21*a32-a22*a31)-a11*(a20*a32-a22*a30)+a12*(a20*a31-a21*a30)));
    return det;
    }

namespace
sfinae
    {
    using ::boost::qvm::determinant;
    }

namespace
qvm_detail
    {
    template <int D>
    struct determinant_defined;

    template <>
    struct
    determinant_defined<4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<A>::cols==4 && is_scalar<B>::value,
    deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols> >::type
inverse( A const & a, B det )
    {
    typedef typename mat_traits<A>::scalar_type T;
    BOOST_QVM_ASSERT(det!=scalar_traits<B>::value(0));
    T const a00=mat_traits<A>::template read_element<0,0>(a);
    T const a01=mat_traits<A>::template read_element<0,1>(a);
    T const a02=mat_traits<A>::template read_element<0,2>(a);
    T const a03=mat_traits<A>::template read_element<0,3>(a);
    T const a10=mat_traits<A>::template read_element<1,0>(a);
    T const a11=mat_traits<A>::template read_element<1,1>(a);
    T const a12=mat_traits<A>::template read_element<1,2>(a);
    T const a13=mat_traits<A>::template read_element<1,3>(a);
    T const a20=mat_traits<A>::template read_element<2,0>(a);
    T const a21=mat_traits<A>::template read_element<2,1>(a);
    T const a22=mat_traits<A>::template read_element<2,2>(a);
    T const a23=mat_traits<A>::template read_element<2,3>(a);
    T const a30=mat_traits<A>::template read_element<3,0>(a);
    T const a31=mat_traits<A>::template read_element<3,1>(a);
    T const a32=mat_traits<A>::template read_element<3,2>(a);
    T const a33=mat_traits<A>::template read_element<3,3>(a);
    T const f=scalar_traits<T>::value(1)/det;
    typedef typename deduce_mat2<A,B,mat_traits<A>::rows,mat_traits<A>::cols>::type R;
    R r;
    write_mat_element<0,0>(r, f*(a11*(a22*a33-a23*a32)-a12*(a21*a33-a23*a31)+a13*(a21*a32-a22*a31)));
    write_mat_element<0,1>(r,-f*(a01*(a22*a33-a23*a32)-a02*(a21*a33-a23*a31)+a03*(a21*a32-a22*a31)));
    write_mat_element<0,2>(r, f*(a01*(a12*a33-a13*a32)-a02*(a11*a33-a13*a31)+a03*(a11*a32-a12*a31)));
    write_mat_element<0,3>(r,-f*(a01*(a12*a23-a13*a22)-a02*(a11*a23-a13*a21)+a03*(a11*a22-a12*a21)));
    write_mat_element<1,0>(r,-f*(a10*(a22*a33-a23*a32)-a12*(a20*a33-a23*a30)+a13*(a20*a32-a22*a30)));
    write_mat_element<1,1>(r, f*(a00*(a22*a33-a23*a32)-a02*(a20*a33-a23*a30)+a03*(a20*a32-a22*a30)));
    write_mat_element<1,2>(r,-f*(a00*(a12*a33-a13*a32)-a02*(a10*a33-a13*a30)+a03*(a10*a32-a12*a30)));
    write_mat_element<1,3>(r, f*(a00*(a12*a23-a13*a22)-a02*(a10*a23-a13*a20)+a03*(a10*a22-a12*a20)));
    write_mat_element<2,0>(r, f*(a10*(a21*a33-a23*a31)-a11*(a20*a33-a23*a30)+a13*(a20*a31-a21*a30)));
    write_mat_element<2,1>(r,-f*(a00*(a21*a33-a23*a31)-a01*(a20*a33-a23*a30)+a03*(a20*a31-a21*a30)));
    write_mat_element<2,2>(r, f*(a00*(a11*a33-a13*a31)-a01*(a10*a33-a13*a30)+a03*(a10*a31-a11*a30)));
    write_mat_element<2,3>(r,-f*(a00*(a11*a23-a13*a21)-a01*(a10*a23-a13*a20)+a03*(a10*a21-a11*a20)));
    write_mat_element<3,0>(r,-f*(a10*(a21*a32-a22*a31)-a11*(a20*a32-a22*a30)+a12*(a20*a31-a21*a30)));
    write_mat_element<3,1>(r, f*(a00*(a21*a32-a22*a31)-a01*(a20*a32-a22*a30)+a02*(a20*a31-a21*a30)));
    write_mat_element<3,2>(r,-f*(a00*(a11*a32-a12*a31)-a01*(a10*a32-a12*a30)+a02*(a10*a31-a11*a30)));
    write_mat_element<3,3>(r, f*(a00*(a11*a22-a12*a21)-a01*(a10*a22-a12*a20)+a02*(a10*a21-a11*a20)));
    return r;
    }

template <class A>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<A>::cols==4,
    deduce_mat<A> >::type
inverse( A const & a )
    {
    typedef typename mat_traits<A>::scalar_type T;
    T det=determinant(a);
    if( det==scalar_traits<T>::value(0) )
        BOOST_QVM_THROW_EXCEPTION(zero_determinant_error());
    return inverse(a,det);
    }

namespace
sfinae
    {
    using ::boost::qvm::inverse;
    }

namespace
qvm_detail
    {
    template <int D>
    struct inverse_m_defined;

    template <>
    struct
    inverse_m_defined<4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    deduce_mat2<A,B,4,4> >::type
operator*( A const & a, B const & b )
    {
    typedef typename mat_traits<A>::scalar_type Ta;
    typedef typename mat_traits<B>::scalar_type Tb;
    Ta const a00 = mat_traits<A>::template read_element<0,0>(a);
    Ta const a01 = mat_traits<A>::template read_element<0,1>(a);
    Ta const a02 = mat_traits<A>::template read_element<0,2>(a);
    Ta const a03 = mat_traits<A>::template read_element<0,3>(a);
    Ta const a10 = mat_traits<A>::template read_element<1,0>(a);
    Ta const a11 = mat_traits<A>::template read_element<1,1>(a);
    Ta const a12 = mat_traits<A>::template read_element<1,2>(a);
    Ta const a13 = mat_traits<A>::template read_element<1,3>(a);
    Ta const a20 = mat_traits<A>::template read_element<2,0>(a);
    Ta const a21 = mat_traits<A>::template read_element<2,1>(a);
    Ta const a22 = mat_traits<A>::template read_element<2,2>(a);
    Ta const a23 = mat_traits<A>::template read_element<2,3>(a);
    Ta const a30 = mat_traits<A>::template read_element<3,0>(a);
    Ta const a31 = mat_traits<A>::template read_element<3,1>(a);
    Ta const a32 = mat_traits<A>::template read_element<3,2>(a);
    Ta const a33 = mat_traits<A>::template read_element<3,3>(a);
    Tb const b00 = mat_traits<B>::template read_element<0,0>(b);
    Tb const b01 = mat_traits<B>::template read_element<0,1>(b);
    Tb const b02 = mat_traits<B>::template read_element<0,2>(b);
    Tb const b03 = mat_traits<B>::template read_element<0,3>(b);
    Tb const b10 = mat_traits<B>::template read_element<1,0>(b);
    Tb const b11 = mat_traits<B>::template read_element<1,1>(b);
    Tb const b12 = mat_traits<B>::template read_element<1,2>(b);
    Tb const b13 = mat_traits<B>::template read_element<1,3>(b);
    Tb const b20 = mat_traits<B>::template read_element<2,0>(b);
    Tb const b21 = mat_traits<B>::template read_element<2,1>(b);
    Tb const b22 = mat_traits<B>::template read_element<2,2>(b);
    Tb const b23 = mat_traits<B>::template read_element<2,3>(b);
    Tb const b30 = mat_traits<B>::template read_element<3,0>(b);
    Tb const b31 = mat_traits<B>::template read_element<3,1>(b);
    Tb const b32 = mat_traits<B>::template read_element<3,2>(b);
    Tb const b33 = mat_traits<B>::template read_element<3,3>(b);
    typedef typename deduce_mat2<A,B,4,4>::type R;
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==4);
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==4);
    R r;
    write_mat_element<0,0>(r,a00*b00+a01*b10+a02*b20+a03*b30);
    write_mat_element<0,1>(r,a00*b01+a01*b11+a02*b21+a03*b31);
    write_mat_element<0,2>(r,a00*b02+a01*b12+a02*b22+a03*b32);
    write_mat_element<0,3>(r,a00*b03+a01*b13+a02*b23+a03*b33);
    write_mat_element<1,0>(r,a10*b00+a11*b10+a12*b20+a13*b30);
    write_mat_element<1,1>(r,a10*b01+a11*b11+a12*b21+a13*b31);
    write_mat_element<1,2>(r,a10*b02+a11*b12+a12*b22+a13*b32);
    write_mat_element<1,3>(r,a10*b03+a11*b13+a12*b23+a13*b33);
    write_mat_element<2,0>(r,a20*b00+a21*b10+a22*b20+a23*b30);
    write_mat_element<2,1>(r,a20*b01+a21*b11+a22*b21+a23*b31);
    write_mat_element<2,2>(r,a20*b02+a21*b12+a22*b22+a23*b32);
    write_mat_element<2,3>(r,a20*b03+a21*b13+a22*b23+a23*b33);
    write_mat_element<3,0>(r,a30*b00+a31*b10+a32*b20+a33*b30);
    write_mat_element<3,1>(r,a30*b01+a31*b11+a32*b21+a33*b31);
    write_mat_element<3,2>(r,a30*b02+a31*b12+a32*b22+a33*b32);
    write_mat_element<3,3>(r,a30*b03+a31*b13+a32*b23+a33*b33);
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator*;
    }

namespace
qvm_detail
    {
    template <int R,int /*CR*/,int C>
    struct mul_mm_defined;

    template <>
    struct
    mul_mm_defined<4,4,4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    A &>::type
operator*=( A & a, B const & b )
    {
    typedef typename mat_traits<A>::scalar_type Ta;
    typedef typename mat_traits<B>::scalar_type Tb;
    Ta const a00 = mat_traits<A>::template read_element<0,0>(a);
    Ta const a01 = mat_traits<A>::template read_element<0,1>(a);
    Ta const a02 = mat_traits<A>::template read_element<0,2>(a);
    Ta const a03 = mat_traits<A>::template read_element<0,3>(a);
    Ta const a10 = mat_traits<A>::template read_element<1,0>(a);
    Ta const a11 = mat_traits<A>::template read_element<1,1>(a);
    Ta const a12 = mat_traits<A>::template read_element<1,2>(a);
    Ta const a13 = mat_traits<A>::template read_element<1,3>(a);
    Ta const a20 = mat_traits<A>::template read_element<2,0>(a);
    Ta const a21 = mat_traits<A>::template read_element<2,1>(a);
    Ta const a22 = mat_traits<A>::template read_element<2,2>(a);
    Ta const a23 = mat_traits<A>::template read_element<2,3>(a);
    Ta const a30 = mat_traits<A>::template read_element<3,0>(a);
    Ta const a31 = mat_traits<A>::template read_element<3,1>(a);
    Ta const a32 = mat_traits<A>::template read_element<3,2>(a);
    Ta const a33 = mat_traits<A>::template read_element<3,3>(a);
    Tb const b00 = mat_traits<B>::template read_element<0,0>(b);
    Tb const b01 = mat_traits<B>::template read_element<0,1>(b);
    Tb const b02 = mat_traits<B>::template read_element<0,2>(b);
    Tb const b03 = mat_traits<B>::template read_element<0,3>(b);
    Tb const b10 = mat_traits<B>::template read_element<1,0>(b);
    Tb const b11 = mat_traits<B>::template read_element<1,1>(b);
    Tb const b12 = mat_traits<B>::template read_element<1,2>(b);
    Tb const b13 = mat_traits<B>::template read_element<1,3>(b);
    Tb const b20 = mat_traits<B>::template read_element<2,0>(b);
    Tb const b21 = mat_traits<B>::template read_element<2,1>(b);
    Tb const b22 = mat_traits<B>::template read_element<2,2>(b);
    Tb const b23 = mat_traits<B>::template read_element<2,3>(b);
    Tb const b30 = mat_traits<B>::template read_element<3,0>(b);
    Tb const b31 = mat_traits<B>::template read_element<3,1>(b);
    Tb const b32 = mat_traits<B>::template read_element<3,2>(b);
    Tb const b33 = mat_traits<B>::template read_element<3,3>(b);
    write_mat_element<0,0>(a,a00*b00+a01*b10+a02*b20+a03*b30);
    write_mat_element<0,1>(a,a00*b01+a01*b11+a02*b21+a03*b31);
    write_mat_element<0,2>(a,a00*b02+a01*b12+a02*b22+a03*b32);
    write_mat_element<0,3>(a,a00*b03+a01*b13+a02*b23+a03*b33);
    write_mat_element<1,0>(a,a10*b00+a11*b10+a12*b20+a13*b30);
    write_mat_element<1,1>(a,a10*b01+a11*b11+a12*b21+a13*b31);
    write_mat_element<1,2>(a,a10*b02+a11*b12+a12*b22+a13*b32);
    write_mat_element<1,3>(a,a10*b03+a11*b13+a12*b23+a13*b33);
    write_mat_element<2,0>(a,a20*b00+a21*b10+a22*b20+a23*b30);
    write_mat_element<2,1>(a,a20*b01+a21*b11+a22*b21+a23*b31);
    write_mat_element<2,2>(a,a20*b02+a21*b12+a22*b22+a23*b32);
    write_mat_element<2,3>(a,a20*b03+a21*b13+a22*b23+a23*b33);
    write_mat_element<3,0>(a,a30*b00+a31*b10+a32*b20+a33*b30);
    write_mat_element<3,1>(a,a30*b01+a31*b11+a32*b21+a33*b31);
    write_mat_element<3,2>(a,a30*b02+a31*b12+a32*b22+a33*b32);
    write_mat_element<3,3>(a,a30*b03+a31*b13+a32*b23+a33*b33);
    return a;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator*=;
    }

namespace
qvm_detail
    {
    template <int D>
    struct mul_eq_mm_defined;

    template <>
    struct
    mul_eq_mm_defined<4>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==4 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==1,
    deduce_mat2<A,B,4,1> >::type
operator*( A const & a, B const & b )
    {
    typedef typename mat_traits<A>::scalar_type Ta;
    typedef typename mat_traits<B>::scalar_type Tb;
    Ta const a00 = mat_traits<A>::template read_element<0,0>(a);
    Ta const a01 = mat_traits<A>::template read_element<0,1>(a);
    Ta const a02 = mat_traits<A>::template read_element<0,2>(a);
    Ta const a03 = mat_traits<A>::template read_element<0,3>(a);
    Ta const a10 = mat_traits<A>::template read_element<1,0>(a);
    Ta const a11 = mat_traits<A>::template read_element<1,1>(a);
    Ta const a12 = mat_traits<A>::template read_element<1,2>(a);
    Ta const a13 = mat_traits<A>::template read_element<1,3>(a);
    Ta const a20 = mat_traits<A>::template read_element<2,0>(a);
    Ta const a21 = mat_traits<A>::template read_element<2,1>(a);
    Ta const a22 = mat_traits<A>::template read_element<2,2>(a);
    Ta const a23 = mat_traits<A>::template read_element<2,3>(a);
    Ta const a30 = mat_traits<A>::template read_element<3,0>(a);
    Ta const a31 = mat_traits<A>::template read_element<3,1>(a);
    Ta const a32 = mat_traits<A>::template read_element<3,2>(a);
    Ta const a33 = mat_traits<A>::template read_element<3,3>(a);
    Tb const b00 = mat_traits<B>::template read_element<0,0>(b);
    Tb const b10 = mat_traits<B>::template read_element<1,0>(b);
    Tb const b20 = mat_traits<B>::template read_element<2,0>(b);
    Tb const b30 = mat_traits<B>::template read_element<3,0>(b);
    typedef typename deduce_mat2<A,B,4,1>::type R;
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==4);
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==1);
    R r;
    write_mat_element<0,0>(r,a00*b00+a01*b10+a02*b20+a03*b30);
    write_mat_element<1,0>(r,a10*b00+a11*b10+a12*b20+a13*b30);
    write_mat_element<2,0>(r,a20*b00+a21*b10+a22*b20+a23*b30);
    write_mat_element<3,0>(r,a30*b00+a31*b10+a32*b20+a33*b30);
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator*;
    }

namespace
qvm_detail
    {
    template <int R,int /*CR*/,int C>
    struct mul_mm_defined;

    template <>
    struct
    mul_mm_defined<4,4,1>
        {
        static bool const value=true;
        };
    }

template <class A,class B>
BOOST_QVM_CONSTEXPR BOOST_QVM_INLINE_OPERATIONS
typename lazy_enable_if_c<
    mat_traits<A>::rows==1 && mat_traits<B>::rows==4 &&
    mat_traits<A>::cols==4 && mat_traits<B>::cols==4,
    deduce_mat2<A,B,1,4> >::type
operator*( A const & a, B const & b )
    {
    typedef typename mat_traits<A>::scalar_type Ta;
    typedef typename mat_traits<B>::scalar_type Tb;
    Ta const a00 = mat_traits<A>::template read_element<0,0>(a);
    Ta const a01 = mat_traits<A>::template read_element<0,1>(a);
    Ta const a02 = mat_traits<A>::template read_element<0,2>(a);
    Ta const a03 = mat_traits<A>::template read_element<0,3>(a);
    Tb const b00 = mat_traits<B>::template read_element<0,0>(b);
    Tb const b01 = mat_traits<B>::template read_element<0,1>(b);
    Tb const b02 = mat_traits<B>::template read_element<0,2>(b);
    Tb const b03 = mat_traits<B>::template read_element<0,3>(b);
    Tb const b10 = mat_traits<B>::template read_element<1,0>(b);
    Tb const b11 = mat_traits<B>::template read_element<1,1>(b);
    Tb const b12 = mat_traits<B>::template read_element<1,2>(b);
    Tb const b13 = mat_traits<B>::template read_element<1,3>(b);
    Tb const b20 = mat_traits<B>::template read_element<2,0>(b);
    Tb const b21 = mat_traits<B>::template read_element<2,1>(b);
    Tb const b22 = mat_traits<B>::template read_element<2,2>(b);
    Tb const b23 = mat_traits<B>::template read_element<2,3>(b);
    Tb const b30 = mat_traits<B>::template read_element<3,0>(b);
    Tb const b31 = mat_traits<B>::template read_element<3,1>(b);
    Tb const b32 = mat_traits<B>::template read_element<3,2>(b);
    Tb const b33 = mat_traits<B>::template read_element<3,3>(b);
    typedef typename deduce_mat2<A,B,1,4>::type R;
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::rows==1);
    BOOST_QVM_STATIC_ASSERT(mat_traits<R>::cols==4);
    R r;
    write_mat_element<0,0>(r,a00*b00+a01*b10+a02*b20+a03*b30);
    write_mat_element<0,1>(r,a00*b01+a01*b11+a02*b21+a03*b31);
    write_mat_element<0,2>(r,a00*b02+a01*b12+a02*b22+a03*b32);
    write_mat_element<0,3>(r,a00*b03+a01*b13+a02*b23+a03*b33);
    return r;
    }

namespace
sfinae
    {
    using ::boost::qvm::operator*;
    }

namespace
qvm_detail
    {
    template <int R,int /*CR*/,int C>
    struct mul_mm_defined;

    template <>
    struct
    mul_mm_defined<1,4,4>
        {
        static bool const value=true;
        };
    }

} }

#endif
