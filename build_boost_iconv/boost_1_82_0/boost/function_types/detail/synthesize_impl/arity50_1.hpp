
// (C) Copyright <PERSON>
//
// Use modification and distribution are subject to the boost Software License,
// Version 1.0. (See http://www.boost.org/LICENSE_1_0.txt).

//------------------------------------------------------------------------------

// no include guards, this file is intended for multiple inclusion

// input:  BOOST_FT_syntax     type macro to use
// input:  BOOST_FT_cc         empty or cc specifier 
// input:  BOOST_FT_ell        empty or "..."
// input:  BOOST_FT_cv         empty or cv qualifiers
// input:  BOOST_FT_flags      single decimal integer encoding the flags
// output: BOOST_FT_n          number of component types (arity+1)
// output: BOOST_FT_arity      current arity
// output: BOOST_FT_type       macro that expands to the type
// output: BOOST_FT_tplargs(p) template arguments with given prefix
// output: BOOST_FT_params(p)  parameters with given prefix

# include <boost/function_types/detail/synthesize_impl/arity40_1.hpp>
# define BOOST_FT_make_type(flags,cc,arity) BOOST_FT_make_type_impl(flags,cc,arity)
# define BOOST_FT_make_type_impl(flags,cc,arity) make_type_ ## flags ## _ ## cc ## _ ## arity
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 , typename T27 , typename T28 , typename T29 , typename T30 , typename T31 , typename T32 , typename T33 , typename T34 , typename T35 , typename T36 , typename T37 , typename T38 , typename T39 , typename T40 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,41)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 , T27 , T28 , T29 , T30 , T31 , T32 , T33 , T34 , T35 , T36 , T37 , T38 , T39 , T40 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 42 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
typedef typename mpl::next< iter_27 > ::type iter_28;
typedef typename mpl::next< iter_28 > ::type iter_29;
typedef typename mpl::next< iter_29 > ::type iter_30;
typedef typename mpl::next< iter_30 > ::type iter_31;
typedef typename mpl::next< iter_31 > ::type iter_32;
typedef typename mpl::next< iter_32 > ::type iter_33;
typedef typename mpl::next< iter_33 > ::type iter_34;
typedef typename mpl::next< iter_34 > ::type iter_35;
typedef typename mpl::next< iter_35 > ::type iter_36;
typedef typename mpl::next< iter_36 > ::type iter_37;
typedef typename mpl::next< iter_37 > ::type iter_38;
typedef typename mpl::next< iter_38 > ::type iter_39;
typedef typename mpl::next< iter_39 > ::type iter_40;
typedef typename mpl::next< iter_40 > ::type iter_41;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,41) 
< typename mpl::deref< iter_0 > ::type 
, typename detail::cv_traits< 
typename mpl::deref< iter_1 > ::type > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
, typename mpl::deref< iter_28 > ::type
, typename mpl::deref< iter_29 > ::type
, typename mpl::deref< iter_30 > ::type
, typename mpl::deref< iter_31 > ::type
, typename mpl::deref< iter_32 > ::type
, typename mpl::deref< iter_33 > ::type
, typename mpl::deref< iter_34 > ::type
, typename mpl::deref< iter_35 > ::type
, typename mpl::deref< iter_36 > ::type
, typename mpl::deref< iter_37 > ::type
, typename mpl::deref< iter_38 > ::type
, typename mpl::deref< iter_39 > ::type
, typename mpl::deref< iter_40 > ::type
, typename mpl::deref< iter_41 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 , typename T27 , typename T28 , typename T29 , typename T30 , typename T31 , typename T32 , typename T33 , typename T34 , typename T35 , typename T36 , typename T37 , typename T38 , typename T39 , typename T40 , typename T41 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,42)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 , T27 , T28 , T29 , T30 , T31 , T32 , T33 , T34 , T35 , T36 , T37 , T38 , T39 , T40 , T41 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 43 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
typedef typename mpl::next< iter_27 > ::type iter_28;
typedef typename mpl::next< iter_28 > ::type iter_29;
typedef typename mpl::next< iter_29 > ::type iter_30;
typedef typename mpl::next< iter_30 > ::type iter_31;
typedef typename mpl::next< iter_31 > ::type iter_32;
typedef typename mpl::next< iter_32 > ::type iter_33;
typedef typename mpl::next< iter_33 > ::type iter_34;
typedef typename mpl::next< iter_34 > ::type iter_35;
typedef typename mpl::next< iter_35 > ::type iter_36;
typedef typename mpl::next< iter_36 > ::type iter_37;
typedef typename mpl::next< iter_37 > ::type iter_38;
typedef typename mpl::next< iter_38 > ::type iter_39;
typedef typename mpl::next< iter_39 > ::type iter_40;
typedef typename mpl::next< iter_40 > ::type iter_41;
typedef typename mpl::next< iter_41 > ::type iter_42;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,42) 
< typename mpl::deref< iter_0 > ::type 
, typename detail::cv_traits< 
typename mpl::deref< iter_1 > ::type > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
, typename mpl::deref< iter_28 > ::type
, typename mpl::deref< iter_29 > ::type
, typename mpl::deref< iter_30 > ::type
, typename mpl::deref< iter_31 > ::type
, typename mpl::deref< iter_32 > ::type
, typename mpl::deref< iter_33 > ::type
, typename mpl::deref< iter_34 > ::type
, typename mpl::deref< iter_35 > ::type
, typename mpl::deref< iter_36 > ::type
, typename mpl::deref< iter_37 > ::type
, typename mpl::deref< iter_38 > ::type
, typename mpl::deref< iter_39 > ::type
, typename mpl::deref< iter_40 > ::type
, typename mpl::deref< iter_41 > ::type
, typename mpl::deref< iter_42 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 , typename T27 , typename T28 , typename T29 , typename T30 , typename T31 , typename T32 , typename T33 , typename T34 , typename T35 , typename T36 , typename T37 , typename T38 , typename T39 , typename T40 , typename T41 , typename T42 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,43)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 , T27 , T28 , T29 , T30 , T31 , T32 , T33 , T34 , T35 , T36 , T37 , T38 , T39 , T40 , T41 , T42 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 44 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
typedef typename mpl::next< iter_27 > ::type iter_28;
typedef typename mpl::next< iter_28 > ::type iter_29;
typedef typename mpl::next< iter_29 > ::type iter_30;
typedef typename mpl::next< iter_30 > ::type iter_31;
typedef typename mpl::next< iter_31 > ::type iter_32;
typedef typename mpl::next< iter_32 > ::type iter_33;
typedef typename mpl::next< iter_33 > ::type iter_34;
typedef typename mpl::next< iter_34 > ::type iter_35;
typedef typename mpl::next< iter_35 > ::type iter_36;
typedef typename mpl::next< iter_36 > ::type iter_37;
typedef typename mpl::next< iter_37 > ::type iter_38;
typedef typename mpl::next< iter_38 > ::type iter_39;
typedef typename mpl::next< iter_39 > ::type iter_40;
typedef typename mpl::next< iter_40 > ::type iter_41;
typedef typename mpl::next< iter_41 > ::type iter_42;
typedef typename mpl::next< iter_42 > ::type iter_43;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,43) 
< typename mpl::deref< iter_0 > ::type 
, typename detail::cv_traits< 
typename mpl::deref< iter_1 > ::type > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
, typename mpl::deref< iter_28 > ::type
, typename mpl::deref< iter_29 > ::type
, typename mpl::deref< iter_30 > ::type
, typename mpl::deref< iter_31 > ::type
, typename mpl::deref< iter_32 > ::type
, typename mpl::deref< iter_33 > ::type
, typename mpl::deref< iter_34 > ::type
, typename mpl::deref< iter_35 > ::type
, typename mpl::deref< iter_36 > ::type
, typename mpl::deref< iter_37 > ::type
, typename mpl::deref< iter_38 > ::type
, typename mpl::deref< iter_39 > ::type
, typename mpl::deref< iter_40 > ::type
, typename mpl::deref< iter_41 > ::type
, typename mpl::deref< iter_42 > ::type
, typename mpl::deref< iter_43 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 , typename T27 , typename T28 , typename T29 , typename T30 , typename T31 , typename T32 , typename T33 , typename T34 , typename T35 , typename T36 , typename T37 , typename T38 , typename T39 , typename T40 , typename T41 , typename T42 , typename T43 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,44)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 , T27 , T28 , T29 , T30 , T31 , T32 , T33 , T34 , T35 , T36 , T37 , T38 , T39 , T40 , T41 , T42 , T43 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 45 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
typedef typename mpl::next< iter_27 > ::type iter_28;
typedef typename mpl::next< iter_28 > ::type iter_29;
typedef typename mpl::next< iter_29 > ::type iter_30;
typedef typename mpl::next< iter_30 > ::type iter_31;
typedef typename mpl::next< iter_31 > ::type iter_32;
typedef typename mpl::next< iter_32 > ::type iter_33;
typedef typename mpl::next< iter_33 > ::type iter_34;
typedef typename mpl::next< iter_34 > ::type iter_35;
typedef typename mpl::next< iter_35 > ::type iter_36;
typedef typename mpl::next< iter_36 > ::type iter_37;
typedef typename mpl::next< iter_37 > ::type iter_38;
typedef typename mpl::next< iter_38 > ::type iter_39;
typedef typename mpl::next< iter_39 > ::type iter_40;
typedef typename mpl::next< iter_40 > ::type iter_41;
typedef typename mpl::next< iter_41 > ::type iter_42;
typedef typename mpl::next< iter_42 > ::type iter_43;
typedef typename mpl::next< iter_43 > ::type iter_44;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,44) 
< typename mpl::deref< iter_0 > ::type 
, typename detail::cv_traits< 
typename mpl::deref< iter_1 > ::type > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
, typename mpl::deref< iter_28 > ::type
, typename mpl::deref< iter_29 > ::type
, typename mpl::deref< iter_30 > ::type
, typename mpl::deref< iter_31 > ::type
, typename mpl::deref< iter_32 > ::type
, typename mpl::deref< iter_33 > ::type
, typename mpl::deref< iter_34 > ::type
, typename mpl::deref< iter_35 > ::type
, typename mpl::deref< iter_36 > ::type
, typename mpl::deref< iter_37 > ::type
, typename mpl::deref< iter_38 > ::type
, typename mpl::deref< iter_39 > ::type
, typename mpl::deref< iter_40 > ::type
, typename mpl::deref< iter_41 > ::type
, typename mpl::deref< iter_42 > ::type
, typename mpl::deref< iter_43 > ::type
, typename mpl::deref< iter_44 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 , typename T27 , typename T28 , typename T29 , typename T30 , typename T31 , typename T32 , typename T33 , typename T34 , typename T35 , typename T36 , typename T37 , typename T38 , typename T39 , typename T40 , typename T41 , typename T42 , typename T43 , typename T44 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,45)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 , T27 , T28 , T29 , T30 , T31 , T32 , T33 , T34 , T35 , T36 , T37 , T38 , T39 , T40 , T41 , T42 , T43 , T44 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 46 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
typedef typename mpl::next< iter_27 > ::type iter_28;
typedef typename mpl::next< iter_28 > ::type iter_29;
typedef typename mpl::next< iter_29 > ::type iter_30;
typedef typename mpl::next< iter_30 > ::type iter_31;
typedef typename mpl::next< iter_31 > ::type iter_32;
typedef typename mpl::next< iter_32 > ::type iter_33;
typedef typename mpl::next< iter_33 > ::type iter_34;
typedef typename mpl::next< iter_34 > ::type iter_35;
typedef typename mpl::next< iter_35 > ::type iter_36;
typedef typename mpl::next< iter_36 > ::type iter_37;
typedef typename mpl::next< iter_37 > ::type iter_38;
typedef typename mpl::next< iter_38 > ::type iter_39;
typedef typename mpl::next< iter_39 > ::type iter_40;
typedef typename mpl::next< iter_40 > ::type iter_41;
typedef typename mpl::next< iter_41 > ::type iter_42;
typedef typename mpl::next< iter_42 > ::type iter_43;
typedef typename mpl::next< iter_43 > ::type iter_44;
typedef typename mpl::next< iter_44 > ::type iter_45;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,45) 
< typename mpl::deref< iter_0 > ::type 
, typename detail::cv_traits< 
typename mpl::deref< iter_1 > ::type > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
, typename mpl::deref< iter_28 > ::type
, typename mpl::deref< iter_29 > ::type
, typename mpl::deref< iter_30 > ::type
, typename mpl::deref< iter_31 > ::type
, typename mpl::deref< iter_32 > ::type
, typename mpl::deref< iter_33 > ::type
, typename mpl::deref< iter_34 > ::type
, typename mpl::deref< iter_35 > ::type
, typename mpl::deref< iter_36 > ::type
, typename mpl::deref< iter_37 > ::type
, typename mpl::deref< iter_38 > ::type
, typename mpl::deref< iter_39 > ::type
, typename mpl::deref< iter_40 > ::type
, typename mpl::deref< iter_41 > ::type
, typename mpl::deref< iter_42 > ::type
, typename mpl::deref< iter_43 > ::type
, typename mpl::deref< iter_44 > ::type
, typename mpl::deref< iter_45 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 , typename T27 , typename T28 , typename T29 , typename T30 , typename T31 , typename T32 , typename T33 , typename T34 , typename T35 , typename T36 , typename T37 , typename T38 , typename T39 , typename T40 , typename T41 , typename T42 , typename T43 , typename T44 , typename T45 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,46)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 , T27 , T28 , T29 , T30 , T31 , T32 , T33 , T34 , T35 , T36 , T37 , T38 , T39 , T40 , T41 , T42 , T43 , T44 , T45 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 47 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
typedef typename mpl::next< iter_27 > ::type iter_28;
typedef typename mpl::next< iter_28 > ::type iter_29;
typedef typename mpl::next< iter_29 > ::type iter_30;
typedef typename mpl::next< iter_30 > ::type iter_31;
typedef typename mpl::next< iter_31 > ::type iter_32;
typedef typename mpl::next< iter_32 > ::type iter_33;
typedef typename mpl::next< iter_33 > ::type iter_34;
typedef typename mpl::next< iter_34 > ::type iter_35;
typedef typename mpl::next< iter_35 > ::type iter_36;
typedef typename mpl::next< iter_36 > ::type iter_37;
typedef typename mpl::next< iter_37 > ::type iter_38;
typedef typename mpl::next< iter_38 > ::type iter_39;
typedef typename mpl::next< iter_39 > ::type iter_40;
typedef typename mpl::next< iter_40 > ::type iter_41;
typedef typename mpl::next< iter_41 > ::type iter_42;
typedef typename mpl::next< iter_42 > ::type iter_43;
typedef typename mpl::next< iter_43 > ::type iter_44;
typedef typename mpl::next< iter_44 > ::type iter_45;
typedef typename mpl::next< iter_45 > ::type iter_46;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,46) 
< typename mpl::deref< iter_0 > ::type 
, typename detail::cv_traits< 
typename mpl::deref< iter_1 > ::type > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
, typename mpl::deref< iter_28 > ::type
, typename mpl::deref< iter_29 > ::type
, typename mpl::deref< iter_30 > ::type
, typename mpl::deref< iter_31 > ::type
, typename mpl::deref< iter_32 > ::type
, typename mpl::deref< iter_33 > ::type
, typename mpl::deref< iter_34 > ::type
, typename mpl::deref< iter_35 > ::type
, typename mpl::deref< iter_36 > ::type
, typename mpl::deref< iter_37 > ::type
, typename mpl::deref< iter_38 > ::type
, typename mpl::deref< iter_39 > ::type
, typename mpl::deref< iter_40 > ::type
, typename mpl::deref< iter_41 > ::type
, typename mpl::deref< iter_42 > ::type
, typename mpl::deref< iter_43 > ::type
, typename mpl::deref< iter_44 > ::type
, typename mpl::deref< iter_45 > ::type
, typename mpl::deref< iter_46 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 , typename T27 , typename T28 , typename T29 , typename T30 , typename T31 , typename T32 , typename T33 , typename T34 , typename T35 , typename T36 , typename T37 , typename T38 , typename T39 , typename T40 , typename T41 , typename T42 , typename T43 , typename T44 , typename T45 , typename T46 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,47)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 , T27 , T28 , T29 , T30 , T31 , T32 , T33 , T34 , T35 , T36 , T37 , T38 , T39 , T40 , T41 , T42 , T43 , T44 , T45 , T46 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 48 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
typedef typename mpl::next< iter_27 > ::type iter_28;
typedef typename mpl::next< iter_28 > ::type iter_29;
typedef typename mpl::next< iter_29 > ::type iter_30;
typedef typename mpl::next< iter_30 > ::type iter_31;
typedef typename mpl::next< iter_31 > ::type iter_32;
typedef typename mpl::next< iter_32 > ::type iter_33;
typedef typename mpl::next< iter_33 > ::type iter_34;
typedef typename mpl::next< iter_34 > ::type iter_35;
typedef typename mpl::next< iter_35 > ::type iter_36;
typedef typename mpl::next< iter_36 > ::type iter_37;
typedef typename mpl::next< iter_37 > ::type iter_38;
typedef typename mpl::next< iter_38 > ::type iter_39;
typedef typename mpl::next< iter_39 > ::type iter_40;
typedef typename mpl::next< iter_40 > ::type iter_41;
typedef typename mpl::next< iter_41 > ::type iter_42;
typedef typename mpl::next< iter_42 > ::type iter_43;
typedef typename mpl::next< iter_43 > ::type iter_44;
typedef typename mpl::next< iter_44 > ::type iter_45;
typedef typename mpl::next< iter_45 > ::type iter_46;
typedef typename mpl::next< iter_46 > ::type iter_47;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,47) 
< typename mpl::deref< iter_0 > ::type 
, typename detail::cv_traits< 
typename mpl::deref< iter_1 > ::type > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
, typename mpl::deref< iter_28 > ::type
, typename mpl::deref< iter_29 > ::type
, typename mpl::deref< iter_30 > ::type
, typename mpl::deref< iter_31 > ::type
, typename mpl::deref< iter_32 > ::type
, typename mpl::deref< iter_33 > ::type
, typename mpl::deref< iter_34 > ::type
, typename mpl::deref< iter_35 > ::type
, typename mpl::deref< iter_36 > ::type
, typename mpl::deref< iter_37 > ::type
, typename mpl::deref< iter_38 > ::type
, typename mpl::deref< iter_39 > ::type
, typename mpl::deref< iter_40 > ::type
, typename mpl::deref< iter_41 > ::type
, typename mpl::deref< iter_42 > ::type
, typename mpl::deref< iter_43 > ::type
, typename mpl::deref< iter_44 > ::type
, typename mpl::deref< iter_45 > ::type
, typename mpl::deref< iter_46 > ::type
, typename mpl::deref< iter_47 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 , typename T27 , typename T28 , typename T29 , typename T30 , typename T31 , typename T32 , typename T33 , typename T34 , typename T35 , typename T36 , typename T37 , typename T38 , typename T39 , typename T40 , typename T41 , typename T42 , typename T43 , typename T44 , typename T45 , typename T46 , typename T47 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,48)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 , T27 , T28 , T29 , T30 , T31 , T32 , T33 , T34 , T35 , T36 , T37 , T38 , T39 , T40 , T41 , T42 , T43 , T44 , T45 , T46 , T47 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 49 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
typedef typename mpl::next< iter_27 > ::type iter_28;
typedef typename mpl::next< iter_28 > ::type iter_29;
typedef typename mpl::next< iter_29 > ::type iter_30;
typedef typename mpl::next< iter_30 > ::type iter_31;
typedef typename mpl::next< iter_31 > ::type iter_32;
typedef typename mpl::next< iter_32 > ::type iter_33;
typedef typename mpl::next< iter_33 > ::type iter_34;
typedef typename mpl::next< iter_34 > ::type iter_35;
typedef typename mpl::next< iter_35 > ::type iter_36;
typedef typename mpl::next< iter_36 > ::type iter_37;
typedef typename mpl::next< iter_37 > ::type iter_38;
typedef typename mpl::next< iter_38 > ::type iter_39;
typedef typename mpl::next< iter_39 > ::type iter_40;
typedef typename mpl::next< iter_40 > ::type iter_41;
typedef typename mpl::next< iter_41 > ::type iter_42;
typedef typename mpl::next< iter_42 > ::type iter_43;
typedef typename mpl::next< iter_43 > ::type iter_44;
typedef typename mpl::next< iter_44 > ::type iter_45;
typedef typename mpl::next< iter_45 > ::type iter_46;
typedef typename mpl::next< iter_46 > ::type iter_47;
typedef typename mpl::next< iter_47 > ::type iter_48;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,48) 
< typename mpl::deref< iter_0 > ::type 
, typename detail::cv_traits< 
typename mpl::deref< iter_1 > ::type > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
, typename mpl::deref< iter_28 > ::type
, typename mpl::deref< iter_29 > ::type
, typename mpl::deref< iter_30 > ::type
, typename mpl::deref< iter_31 > ::type
, typename mpl::deref< iter_32 > ::type
, typename mpl::deref< iter_33 > ::type
, typename mpl::deref< iter_34 > ::type
, typename mpl::deref< iter_35 > ::type
, typename mpl::deref< iter_36 > ::type
, typename mpl::deref< iter_37 > ::type
, typename mpl::deref< iter_38 > ::type
, typename mpl::deref< iter_39 > ::type
, typename mpl::deref< iter_40 > ::type
, typename mpl::deref< iter_41 > ::type
, typename mpl::deref< iter_42 > ::type
, typename mpl::deref< iter_43 > ::type
, typename mpl::deref< iter_44 > ::type
, typename mpl::deref< iter_45 > ::type
, typename mpl::deref< iter_46 > ::type
, typename mpl::deref< iter_47 > ::type
, typename mpl::deref< iter_48 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 , typename T27 , typename T28 , typename T29 , typename T30 , typename T31 , typename T32 , typename T33 , typename T34 , typename T35 , typename T36 , typename T37 , typename T38 , typename T39 , typename T40 , typename T41 , typename T42 , typename T43 , typename T44 , typename T45 , typename T46 , typename T47 , typename T48 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,49)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 , T27 , T28 , T29 , T30 , T31 , T32 , T33 , T34 , T35 , T36 , T37 , T38 , T39 , T40 , T41 , T42 , T43 , T44 , T45 , T46 , T47 , T48 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 50 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
typedef typename mpl::next< iter_27 > ::type iter_28;
typedef typename mpl::next< iter_28 > ::type iter_29;
typedef typename mpl::next< iter_29 > ::type iter_30;
typedef typename mpl::next< iter_30 > ::type iter_31;
typedef typename mpl::next< iter_31 > ::type iter_32;
typedef typename mpl::next< iter_32 > ::type iter_33;
typedef typename mpl::next< iter_33 > ::type iter_34;
typedef typename mpl::next< iter_34 > ::type iter_35;
typedef typename mpl::next< iter_35 > ::type iter_36;
typedef typename mpl::next< iter_36 > ::type iter_37;
typedef typename mpl::next< iter_37 > ::type iter_38;
typedef typename mpl::next< iter_38 > ::type iter_39;
typedef typename mpl::next< iter_39 > ::type iter_40;
typedef typename mpl::next< iter_40 > ::type iter_41;
typedef typename mpl::next< iter_41 > ::type iter_42;
typedef typename mpl::next< iter_42 > ::type iter_43;
typedef typename mpl::next< iter_43 > ::type iter_44;
typedef typename mpl::next< iter_44 > ::type iter_45;
typedef typename mpl::next< iter_45 > ::type iter_46;
typedef typename mpl::next< iter_46 > ::type iter_47;
typedef typename mpl::next< iter_47 > ::type iter_48;
typedef typename mpl::next< iter_48 > ::type iter_49;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,49) 
< typename mpl::deref< iter_0 > ::type 
, typename detail::cv_traits< 
typename mpl::deref< iter_1 > ::type > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
, typename mpl::deref< iter_28 > ::type
, typename mpl::deref< iter_29 > ::type
, typename mpl::deref< iter_30 > ::type
, typename mpl::deref< iter_31 > ::type
, typename mpl::deref< iter_32 > ::type
, typename mpl::deref< iter_33 > ::type
, typename mpl::deref< iter_34 > ::type
, typename mpl::deref< iter_35 > ::type
, typename mpl::deref< iter_36 > ::type
, typename mpl::deref< iter_37 > ::type
, typename mpl::deref< iter_38 > ::type
, typename mpl::deref< iter_39 > ::type
, typename mpl::deref< iter_40 > ::type
, typename mpl::deref< iter_41 > ::type
, typename mpl::deref< iter_42 > ::type
, typename mpl::deref< iter_43 > ::type
, typename mpl::deref< iter_44 > ::type
, typename mpl::deref< iter_45 > ::type
, typename mpl::deref< iter_46 > ::type
, typename mpl::deref< iter_47 > ::type
, typename mpl::deref< iter_48 > ::type
, typename mpl::deref< iter_49 > ::type
> ::type type;
};
};
template< typename R , typename T0 , typename T1 , typename T2 , typename T3 , typename T4 , typename T5 , typename T6 , typename T7 , typename T8 , typename T9 , typename T10 , typename T11 , typename T12 , typename T13 , typename T14 , typename T15 , typename T16 , typename T17 , typename T18 , typename T19 , typename T20 , typename T21 , typename T22 , typename T23 , typename T24 , typename T25 , typename T26 , typename T27 , typename T28 , typename T29 , typename T30 , typename T31 , typename T32 , typename T33 , typename T34 , typename T35 , typename T36 , typename T37 , typename T38 , typename T39 , typename T40 , typename T41 , typename T42 , typename T43 , typename T44 , typename T45 , typename T46 , typename T47 , typename T48 , typename T49 >
struct BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,50)
{
typedef BOOST_FT_syntax(BOOST_FT_cc,type BOOST_PP_EMPTY) (T1 , T2 , T3 , T4 , T5 , T6 , T7 , T8 , T9 , T10 , T11 , T12 , T13 , T14 , T15 , T16 , T17 , T18 , T19 , T20 , T21 , T22 , T23 , T24 , T25 , T26 , T27 , T28 , T29 , T30 , T31 , T32 , T33 , T34 , T35 , T36 , T37 , T38 , T39 , T40 , T41 , T42 , T43 , T44 , T45 , T46 , T47 , T48 , T49 BOOST_FT_ell) BOOST_FT_cv ;
};
template< > 
struct synthesize_impl_o< BOOST_FT_flags, BOOST_FT_cc_id, 51 > 
{ 
template<typename S> struct synthesize_impl_i
{
private:
typedef typename mpl::begin<S> ::type iter_0;
typedef typename mpl::next< iter_0 > ::type iter_1;
typedef typename mpl::next< iter_1 > ::type iter_2;
typedef typename mpl::next< iter_2 > ::type iter_3;
typedef typename mpl::next< iter_3 > ::type iter_4;
typedef typename mpl::next< iter_4 > ::type iter_5;
typedef typename mpl::next< iter_5 > ::type iter_6;
typedef typename mpl::next< iter_6 > ::type iter_7;
typedef typename mpl::next< iter_7 > ::type iter_8;
typedef typename mpl::next< iter_8 > ::type iter_9;
typedef typename mpl::next< iter_9 > ::type iter_10;
typedef typename mpl::next< iter_10 > ::type iter_11;
typedef typename mpl::next< iter_11 > ::type iter_12;
typedef typename mpl::next< iter_12 > ::type iter_13;
typedef typename mpl::next< iter_13 > ::type iter_14;
typedef typename mpl::next< iter_14 > ::type iter_15;
typedef typename mpl::next< iter_15 > ::type iter_16;
typedef typename mpl::next< iter_16 > ::type iter_17;
typedef typename mpl::next< iter_17 > ::type iter_18;
typedef typename mpl::next< iter_18 > ::type iter_19;
typedef typename mpl::next< iter_19 > ::type iter_20;
typedef typename mpl::next< iter_20 > ::type iter_21;
typedef typename mpl::next< iter_21 > ::type iter_22;
typedef typename mpl::next< iter_22 > ::type iter_23;
typedef typename mpl::next< iter_23 > ::type iter_24;
typedef typename mpl::next< iter_24 > ::type iter_25;
typedef typename mpl::next< iter_25 > ::type iter_26;
typedef typename mpl::next< iter_26 > ::type iter_27;
typedef typename mpl::next< iter_27 > ::type iter_28;
typedef typename mpl::next< iter_28 > ::type iter_29;
typedef typename mpl::next< iter_29 > ::type iter_30;
typedef typename mpl::next< iter_30 > ::type iter_31;
typedef typename mpl::next< iter_31 > ::type iter_32;
typedef typename mpl::next< iter_32 > ::type iter_33;
typedef typename mpl::next< iter_33 > ::type iter_34;
typedef typename mpl::next< iter_34 > ::type iter_35;
typedef typename mpl::next< iter_35 > ::type iter_36;
typedef typename mpl::next< iter_36 > ::type iter_37;
typedef typename mpl::next< iter_37 > ::type iter_38;
typedef typename mpl::next< iter_38 > ::type iter_39;
typedef typename mpl::next< iter_39 > ::type iter_40;
typedef typename mpl::next< iter_40 > ::type iter_41;
typedef typename mpl::next< iter_41 > ::type iter_42;
typedef typename mpl::next< iter_42 > ::type iter_43;
typedef typename mpl::next< iter_43 > ::type iter_44;
typedef typename mpl::next< iter_44 > ::type iter_45;
typedef typename mpl::next< iter_45 > ::type iter_46;
typedef typename mpl::next< iter_46 > ::type iter_47;
typedef typename mpl::next< iter_47 > ::type iter_48;
typedef typename mpl::next< iter_48 > ::type iter_49;
typedef typename mpl::next< iter_49 > ::type iter_50;
public:
typedef typename detail::BOOST_FT_make_type(BOOST_FT_flags,BOOST_FT_cc_id,50) 
< typename mpl::deref< iter_0 > ::type 
, typename detail::cv_traits< 
typename mpl::deref< iter_1 > ::type > ::type
, typename mpl::deref< iter_2 > ::type
, typename mpl::deref< iter_3 > ::type
, typename mpl::deref< iter_4 > ::type
, typename mpl::deref< iter_5 > ::type
, typename mpl::deref< iter_6 > ::type
, typename mpl::deref< iter_7 > ::type
, typename mpl::deref< iter_8 > ::type
, typename mpl::deref< iter_9 > ::type
, typename mpl::deref< iter_10 > ::type
, typename mpl::deref< iter_11 > ::type
, typename mpl::deref< iter_12 > ::type
, typename mpl::deref< iter_13 > ::type
, typename mpl::deref< iter_14 > ::type
, typename mpl::deref< iter_15 > ::type
, typename mpl::deref< iter_16 > ::type
, typename mpl::deref< iter_17 > ::type
, typename mpl::deref< iter_18 > ::type
, typename mpl::deref< iter_19 > ::type
, typename mpl::deref< iter_20 > ::type
, typename mpl::deref< iter_21 > ::type
, typename mpl::deref< iter_22 > ::type
, typename mpl::deref< iter_23 > ::type
, typename mpl::deref< iter_24 > ::type
, typename mpl::deref< iter_25 > ::type
, typename mpl::deref< iter_26 > ::type
, typename mpl::deref< iter_27 > ::type
, typename mpl::deref< iter_28 > ::type
, typename mpl::deref< iter_29 > ::type
, typename mpl::deref< iter_30 > ::type
, typename mpl::deref< iter_31 > ::type
, typename mpl::deref< iter_32 > ::type
, typename mpl::deref< iter_33 > ::type
, typename mpl::deref< iter_34 > ::type
, typename mpl::deref< iter_35 > ::type
, typename mpl::deref< iter_36 > ::type
, typename mpl::deref< iter_37 > ::type
, typename mpl::deref< iter_38 > ::type
, typename mpl::deref< iter_39 > ::type
, typename mpl::deref< iter_40 > ::type
, typename mpl::deref< iter_41 > ::type
, typename mpl::deref< iter_42 > ::type
, typename mpl::deref< iter_43 > ::type
, typename mpl::deref< iter_44 > ::type
, typename mpl::deref< iter_45 > ::type
, typename mpl::deref< iter_46 > ::type
, typename mpl::deref< iter_47 > ::type
, typename mpl::deref< iter_48 > ::type
, typename mpl::deref< iter_49 > ::type
, typename mpl::deref< iter_50 > ::type
> ::type type;
};
};
# undef BOOST_FT_make_type
# undef BOOST_FT_make_type_impl

