/* Generated by re2c 1.0.2 on Mon Nov  7 21:56:07 2022 */
#line 1 "cpp.re"
/*=============================================================================
    Boost.Wave: A Standard compliant C++ preprocessor library

    Copyright (c) 2001 Daniel C. Nuffer
    Copyright (c) 2001-2013 Hart<PERSON><PERSON> Kaiser.
    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

    This is a lexer conforming to the Standard with a few exceptions.
    So it does allow the '$' to be part of identifiers. If you need strict
    Standards conforming behaviour, please include the lexer definition
    provided in the file strict_cpp.re.

    TODO:
        handle errors better.
=============================================================================*/

#line 41 "cpp.re"



#line 25 "cpp_re.inc"
{
    YYCTYPE yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
        /* table 1 .. 8: 0 */
          0,   0,   0,   0,   0,   0,   0,   0, 
          0, 203,   8, 203, 203,   8,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
        203, 203, 138, 203, 203, 203, 203, 201, 
        203, 203, 227, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203,  75, 204, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 216, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        203, 203, 203, 203, 203, 203, 203, 203, 
        /* table 9 .. 10: 256 */
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,  64,   0,  64,  64,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
         64,   0,   0,   0, 128,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
        128, 128, 128, 128, 128, 128, 128, 128, 
        128, 128,   0,   0,   0,   0,   0,   0, 
          0, 128, 128, 128, 128, 128, 128, 128, 
        128, 128, 128, 128, 128, 128, 128, 128, 
        128, 128, 128, 128, 128, 128, 128, 128, 
        128, 128, 128,   0,   0,   0,   0, 128, 
          0, 128, 128, 128, 128, 128, 128, 128, 
        128, 128, 128, 128, 128, 128, 128, 128, 
        128, 128, 128, 128, 128, 128, 128, 128, 
        128, 128, 128,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
    };
    if ((YYLIMIT - YYCURSOR) < 17) YYFILL(17);
    yych = *YYCURSOR;
    if (yybm[256+yych] & 64) {
        goto yy6;
    }
    switch (yych) {
    case 0x00:    goto yy2;
    case 0x01:
    case 0x02:
    case 0x03:
    case 0x04:
    case 0x05:
    case 0x06:
    case 0x07:
    case 0x08:
    case 0x0E:
    case 0x0F:
    case 0x10:
    case 0x11:
    case 0x12:
    case 0x13:
    case 0x14:
    case 0x15:
    case 0x16:
    case 0x17:
    case 0x18:
    case 0x19:
    case 0x1A:
    case 0x1B:
    case 0x1C:
    case 0x1D:
    case 0x1E:
    case 0x1F:    goto yy4;
    case '\t':
    case '\n':    goto yy9;
    case '\v':
    case '\f':
    case '\r':    goto yy11;
    case ' ':
    case '!':    goto yy12;
    case '"':    goto yy14;
    case '#':    goto yy16;
    case '$':
    case 'A':
    case 'B':
    case 'C':
    case 'D':
    case 'E':
    case 'F':
    case 'G':
    case 'H':
    case 'I':
    case 'J':
    case 'K':
    case 'M':
    case 'N':
    case 'O':
    case 'P':
    case 'Q':
    case 'S':
    case 'T':
    case 'V':
    case 'W':
    case 'X':
    case 'Y':
    case 'Z':
    case 'h':
    case 'j':
    case 'k':
    case 'q':
    case 'y':
    case 'z':    goto yy18;
    case '%':    goto yy21;
    case '&':    goto yy23;
    case '\'':    goto yy25;
    case '(':    goto yy26;
    case ')':    goto yy28;
    case '*':    goto yy30;
    case '+':    goto yy32;
    case ',':    goto yy34;
    case '-':    goto yy36;
    case '.':    goto yy38;
    case '/':    goto yy40;
    case '0':
    case '1':
    case '2':
    case '3':
    case '4':
    case '5':
    case '6':
    case '7':
    case '8':
    case '9':    goto yy42;
    case ':':    goto yy44;
    case ';':    goto yy46;
    case '<':    goto yy48;
    case '=':    goto yy50;
    case '>':    goto yy52;
    case '?':    goto yy54;
    case 'L':    goto yy57;
    case 'R':    goto yy58;
    case 'U':    goto yy59;
    case '[':    goto yy60;
    case '\\':    goto yy62;
    case ']':    goto yy63;
    case '^':    goto yy65;
    case '_':    goto yy67;
    case 'a':    goto yy68;
    case 'b':    goto yy69;
    case 'c':    goto yy70;
    case 'd':    goto yy71;
    case 'e':    goto yy72;
    case 'f':    goto yy73;
    case 'g':    goto yy74;
    case 'i':    goto yy75;
    case 'l':    goto yy76;
    case 'm':    goto yy77;
    case 'n':    goto yy78;
    case 'o':    goto yy79;
    case 'p':    goto yy80;
    case 'r':    goto yy81;
    case 's':    goto yy82;
    case 't':    goto yy83;
    case 'u':    goto yy84;
    case 'v':    goto yy85;
    case 'w':    goto yy86;
    case 'x':    goto yy87;
    case '{':    goto yy88;
    case '|':    goto yy90;
    case '}':    goto yy92;
    case '~':    goto yy94;
    default:    goto yy56;
    }
yy2:
    ++YYCURSOR;
#line 354 "cpp.re"
    {
        if (s->eof && cursor != s->eof)
        {
            BOOST_WAVE_UPDATE_CURSOR();     // adjust the input cursor
            (*s->error_proc)(s, lexing_exception::generic_lexing_error,
                "invalid character '\\000' in input stream");
        }
        BOOST_WAVE_RET(T_EOF);
    }
#line 242 "cpp_re.inc"
yy4:
    ++YYCURSOR;
#line 367 "cpp.re"
    {
        // flag the error
        BOOST_WAVE_UPDATE_CURSOR();     // adjust the input cursor
        (*s->error_proc)(s, lexing_exception::generic_lexing_error,
            "invalid character '\\%03o' in input stream", *--YYCURSOR);
    }
#line 252 "cpp_re.inc"
yy6:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[256+yych] & 64) {
        goto yy6;
    }
#line 344 "cpp.re"
    { BOOST_WAVE_RET(T_SPACE); }
#line 262 "cpp_re.inc"
yy9:
    ++YYCURSOR;
yy10:
#line 347 "cpp.re"
    {
        s->line++;
        cursor.column = 1;
        BOOST_WAVE_RET(T_NEWLINE);
    }
#line 272 "cpp_re.inc"
yy11:
    yych = *++YYCURSOR;
    if (yych == '\n') goto yy9;
    goto yy10;
yy12:
    yych = *++YYCURSOR;
    if (yych == '=') goto yy96;
#line 210 "cpp.re"
    { BOOST_WAVE_RET(T_NOT); }
#line 282 "cpp_re.inc"
yy14:
    yyaccept = 0;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '\n') {
        if (yych == '\t') goto yy99;
    } else {
        if (yych <= '\f') goto yy99;
        if (yych >= ' ') goto yy99;
    }
yy15:
#line 364 "cpp.re"
    { BOOST_WAVE_RET(TOKEN_FROM_ID(*s->tok, UnknownTokenType)); }
#line 295 "cpp_re.inc"
yy16:
    yyaccept = 1;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'c') {
        if (yych <= ' ') {
            if (yych <= '\n') {
                if (yych == '\t') goto yy106;
            } else {
                if (yych <= '\f') goto yy106;
                if (yych >= ' ') goto yy106;
            }
        } else {
            if (yych <= '.') {
                if (yych == '#') goto yy107;
            } else {
                if (yych <= '/') goto yy106;
                if (yych == '?') goto yy110;
            }
        }
    } else {
        if (yych <= 'p') {
            if (yych <= 'i') {
                if (yych <= 'e') goto yy106;
                if (yych >= 'i') goto yy106;
            } else {
                if (yych == 'l') goto yy106;
                if (yych >= 'p') goto yy106;
            }
        } else {
            if (yych <= 't') {
                if (yych == 'r') goto yy106;
            } else {
                if (yych == 'v') goto yy17;
                if (yych <= 'w') goto yy106;
            }
        }
    }
yy17:
#line 159 "cpp.re"
    { BOOST_WAVE_RET(T_POUND); }
#line 336 "cpp_re.inc"
yy18:
    yyaccept = 2;
    YYMARKER = ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
yy19:
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy20:
#line 315 "cpp.re"
    { BOOST_WAVE_RET(T_IDENTIFIER); }
#line 351 "cpp_re.inc"
yy21:
    yych = *++YYCURSOR;
    if (yych <= '<') {
        if (yych == ':') goto yy121;
    } else {
        if (yych <= '=') goto yy123;
        if (yych <= '>') goto yy125;
    }
#line 198 "cpp.re"
    { BOOST_WAVE_RET(T_PERCENT); }
#line 362 "cpp_re.inc"
yy23:
    yych = *++YYCURSOR;
    if (yych == '&') goto yy127;
    if (yych == '=') goto yy129;
#line 202 "cpp.re"
    { BOOST_WAVE_RET(T_AND); }
#line 369 "cpp_re.inc"
yy25:
    yyaccept = 0;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '\f') {
        if (yych == '\t') goto yy132;
        if (yych <= '\n') goto yy15;
        goto yy132;
    } else {
        if (yych <= 0x1F) goto yy15;
        if (yych == '\'') goto yy15;
        goto yy132;
    }
yy26:
    ++YYCURSOR;
#line 167 "cpp.re"
    { BOOST_WAVE_RET(T_LEFTPAREN); }
#line 386 "cpp_re.inc"
yy28:
    ++YYCURSOR;
#line 168 "cpp.re"
    { BOOST_WAVE_RET(T_RIGHTPAREN); }
#line 391 "cpp_re.inc"
yy30:
    yych = *++YYCURSOR;
    if (yych == '=') goto yy135;
#line 196 "cpp.re"
    { BOOST_WAVE_RET(T_STAR); }
#line 397 "cpp_re.inc"
yy32:
    yych = *++YYCURSOR;
    if (yych == '+') goto yy137;
    if (yych == '=') goto yy139;
#line 194 "cpp.re"
    { BOOST_WAVE_RET(T_PLUS); }
#line 404 "cpp_re.inc"
yy34:
    ++YYCURSOR;
#line 256 "cpp.re"
    { BOOST_WAVE_RET(T_COMMA); }
#line 409 "cpp_re.inc"
yy36:
    yych = *++YYCURSOR;
    if (yych <= '<') {
        if (yych == '-') goto yy141;
    } else {
        if (yych <= '=') goto yy143;
        if (yych <= '>') goto yy145;
    }
#line 195 "cpp.re"
    { BOOST_WAVE_RET(T_MINUS); }
#line 420 "cpp_re.inc"
yy38:
    yyaccept = 3;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '-') {
        if (yych == '*') goto yy147;
    } else {
        if (yych <= '.') goto yy149;
        if (yych <= '/') goto yy39;
        if (yych <= '9') goto yy42;
    }
yy39:
#line 183 "cpp.re"
    { BOOST_WAVE_RET(T_DOT); }
#line 434 "cpp_re.inc"
yy40:
    yych = *++YYCURSOR;
    if (yych <= '.') {
        if (yych == '*') goto yy150;
    } else {
        if (yych <= '/') goto yy152;
        if (yych == '=') goto yy154;
    }
#line 197 "cpp.re"
    { BOOST_WAVE_RET(T_DIVIDE); }
#line 445 "cpp_re.inc"
yy42:
    ++YYCURSOR;
#line 46 "cpp.re"
    { goto pp_number; }
#line 450 "cpp_re.inc"
yy44:
    yych = *++YYCURSOR;
    if (yych == ':') goto yy156;
    if (yych == '>') goto yy158;
#line 170 "cpp.re"
    { BOOST_WAVE_RET(T_COLON); }
#line 457 "cpp_re.inc"
yy46:
    ++YYCURSOR;
#line 169 "cpp.re"
    { BOOST_WAVE_RET(T_SEMICOLON); }
#line 462 "cpp_re.inc"
yy48:
    yych = *++YYCURSOR;
    if (yych <= ':') {
        if (yych == '%') goto yy160;
        if (yych >= ':') goto yy162;
    } else {
        if (yych <= ';') goto yy49;
        if (yych <= '<') goto yy164;
        if (yych <= '=') goto yy166;
    }
yy49:
#line 213 "cpp.re"
    { BOOST_WAVE_RET(T_LESS); }
#line 476 "cpp_re.inc"
yy50:
    yych = *++YYCURSOR;
    if (yych == '=') goto yy168;
#line 212 "cpp.re"
    { BOOST_WAVE_RET(T_ASSIGN); }
#line 482 "cpp_re.inc"
yy52:
    yych = *++YYCURSOR;
    if (yych <= '<') goto yy53;
    if (yych <= '=') goto yy170;
    if (yych <= '>') goto yy172;
yy53:
#line 214 "cpp.re"
    { BOOST_WAVE_RET(T_GREATER); }
#line 491 "cpp_re.inc"
yy54:
    yyaccept = 4;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '?') goto yy174;
yy55:
#line 172 "cpp.re"
    { BOOST_WAVE_RET(T_QUESTION_MARK); }
#line 499 "cpp_re.inc"
yy56:
    ++YYCURSOR;
    goto yy15;
yy57:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '&') {
        if (yych == '"') goto yy98;
        goto yy19;
    } else {
        if (yych <= '\'') goto yy175;
        if (yych != 'R') goto yy19;
    }
yy58:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '"') goto yy176;
    goto yy19;
yy59:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '&') {
        if (yych == '"') goto yy178;
        goto yy19;
    } else {
        if (yych <= '\'') goto yy180;
        if (yych == 'R') goto yy182;
        goto yy19;
    }
yy60:
    ++YYCURSOR;
#line 153 "cpp.re"
    { BOOST_WAVE_RET(T_LEFTBRACKET); }
#line 533 "cpp_re.inc"
yy62:
    yyaccept = 0;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'U') goto yy183;
    if (yych == 'u') goto yy184;
    goto yy15;
yy63:
    ++YYCURSOR;
#line 156 "cpp.re"
    { BOOST_WAVE_RET(T_RIGHTBRACKET); }
#line 544 "cpp_re.inc"
yy65:
    yych = *++YYCURSOR;
    if (yych == '=') goto yy185;
#line 199 "cpp.re"
    { BOOST_WAVE_RET(T_XOR); }
#line 550 "cpp_re.inc"
yy67:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    switch (yych) {
    case '_':    goto yy187;
    case 'a':    goto yy188;
    case 'b':    goto yy189;
    case 'c':    goto yy190;
    case 'd':    goto yy191;
    case 'f':    goto yy192;
    case 'i':    goto yy193;
    case 's':    goto yy194;
    default:    goto yy19;
    }
yy68:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    switch (yych) {
    case 'l':    goto yy195;
    case 'n':    goto yy196;
    case 's':    goto yy197;
    case 'u':    goto yy198;
    default:    goto yy19;
    }
yy69:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'n') {
        if (yych == 'i') goto yy199;
        goto yy19;
    } else {
        if (yych <= 'o') goto yy200;
        if (yych == 'r') goto yy201;
        goto yy19;
    }
yy70:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    switch (yych) {
    case 'a':    goto yy202;
    case 'h':    goto yy203;
    case 'l':    goto yy204;
    case 'o':    goto yy205;
    default:    goto yy19;
    }
yy71:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'n') {
        if (yych == 'e') goto yy206;
        goto yy19;
    } else {
        if (yych <= 'o') goto yy207;
        if (yych == 'y') goto yy209;
        goto yy19;
    }
yy72:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'm') {
        if (yych == 'l') goto yy210;
        goto yy19;
    } else {
        if (yych <= 'n') goto yy211;
        if (yych == 'x') goto yy212;
        goto yy19;
    }
yy73:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    switch (yych) {
    case 'a':    goto yy213;
    case 'l':    goto yy214;
    case 'o':    goto yy215;
    case 'r':    goto yy216;
    default:    goto yy19;
    }
yy74:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'o') goto yy217;
    goto yy19;
yy75:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'l') {
        if (yych == 'f') goto yy218;
        goto yy19;
    } else {
        if (yych <= 'm') goto yy220;
        if (yych <= 'n') goto yy221;
        goto yy19;
    }
yy76:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'o') goto yy222;
    goto yy19;
yy77:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'u') goto yy223;
    goto yy19;
yy78:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    switch (yych) {
    case 'a':    goto yy224;
    case 'e':    goto yy225;
    case 'o':    goto yy226;
    case 'u':    goto yy227;
    default:    goto yy19;
    }
yy79:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy228;
    if (yych == 'r') goto yy229;
    goto yy19;
yy80:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy231;
    if (yych == 'u') goto yy232;
    goto yy19;
yy81:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy233;
    goto yy19;
yy82:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 's') {
        if (yych <= 'g') goto yy19;
        if (yych <= 'h') goto yy234;
        if (yych <= 'i') goto yy235;
        goto yy19;
    } else {
        if (yych <= 't') goto yy236;
        if (yych == 'w') goto yy237;
        goto yy19;
    }
yy83:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    switch (yych) {
    case 'e':    goto yy238;
    case 'h':    goto yy239;
    case 'r':    goto yy240;
    case 'y':    goto yy241;
    default:    goto yy19;
    }
yy84:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '8') {
        if (yych <= '&') {
            if (yych == '"') goto yy178;
            goto yy19;
        } else {
            if (yych <= '\'') goto yy180;
            if (yych <= '7') goto yy19;
            goto yy242;
        }
    } else {
        if (yych <= 'm') {
            if (yych == 'R') goto yy182;
            goto yy19;
        } else {
            if (yych <= 'n') goto yy243;
            if (yych == 's') goto yy244;
            goto yy19;
        }
    }
yy85:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy245;
    if (yych == 'o') goto yy246;
    goto yy19;
yy86:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy247;
    if (yych == 'h') goto yy248;
    goto yy19;
yy87:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'o') goto yy249;
    goto yy19;
yy88:
    ++YYCURSOR;
#line 147 "cpp.re"
    { BOOST_WAVE_RET(T_LEFTBRACE); }
#line 747 "cpp_re.inc"
yy90:
    yyaccept = 5;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '>') {
        if (yych == '=') goto yy250;
    } else {
        if (yych <= '?') goto yy252;
        if (yych == '|') goto yy253;
    }
yy91:
#line 204 "cpp.re"
    { BOOST_WAVE_RET(T_OR); }
#line 760 "cpp_re.inc"
yy92:
    ++YYCURSOR;
#line 150 "cpp.re"
    { BOOST_WAVE_RET(T_RIGHTBRACE); }
#line 765 "cpp_re.inc"
yy94:
    ++YYCURSOR;
#line 207 "cpp.re"
    { BOOST_WAVE_RET(T_COMPL); }
#line 770 "cpp_re.inc"
yy96:
    ++YYCURSOR;
#line 233 "cpp.re"
    { BOOST_WAVE_RET(T_NOTEQUAL); }
#line 775 "cpp_re.inc"
yy98:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
yy99:
    if (yybm[0+yych] & 1) {
        goto yy98;
    }
    if (yych <= 0x1F) goto yy100;
    if (yych <= '"') goto yy101;
    if (yych <= '?') goto yy103;
    goto yy104;
yy100:
    YYCURSOR = YYMARKER;
    if (yyaccept <= 60) {
        if (yyaccept <= 30) {
            if (yyaccept <= 15) {
                if (yyaccept <= 7) {
                    if (yyaccept <= 3) {
                        if (yyaccept <= 1) {
                            if (yyaccept == 0) {
                                goto yy15;
                            } else {
                                goto yy17;
                            }
                        } else {
                            if (yyaccept == 2) {
                                goto yy20;
                            } else {
                                goto yy39;
                            }
                        }
                    } else {
                        if (yyaccept <= 5) {
                            if (yyaccept == 4) {
                                goto yy55;
                            } else {
                                goto yy91;
                            }
                        } else {
                            if (yyaccept == 6) {
                                goto yy122;
                            } else {
                                goto yy208;
                            }
                        }
                    }
                } else {
                    if (yyaccept <= 11) {
                        if (yyaccept <= 9) {
                            if (yyaccept == 8) {
                                goto yy219;
                            } else {
                                goto yy230;
                            }
                        } else {
                            if (yyaccept == 10) {
                                goto yy268;
                            } else {
                                goto yy295;
                            }
                        }
                    } else {
                        if (yyaccept <= 13) {
                            if (yyaccept == 12) {
                                goto yy305;
                            } else {
                                goto yy309;
                            }
                        } else {
                            if (yyaccept == 14) {
                                goto yy330;
                            } else {
                                goto yy332;
                            }
                        }
                    }
                }
            } else {
                if (yyaccept <= 23) {
                    if (yyaccept <= 19) {
                        if (yyaccept <= 17) {
                            if (yyaccept == 16) {
                                goto yy356;
                            } else {
                                goto yy362;
                            }
                        } else {
                            if (yyaccept == 18) {
                                goto yy367;
                            } else {
                                goto yy370;
                            }
                        }
                    } else {
                        if (yyaccept <= 21) {
                            if (yyaccept == 20) {
                                goto yy392;
                            } else {
                                goto yy403;
                            }
                        } else {
                            if (yyaccept == 22) {
                                goto yy450;
                            } else {
                                goto yy460;
                            }
                        }
                    }
                } else {
                    if (yyaccept <= 27) {
                        if (yyaccept <= 25) {
                            if (yyaccept == 24) {
                                goto yy464;
                            } else {
                                goto yy467;
                            }
                        } else {
                            if (yyaccept == 26) {
                                goto yy470;
                            } else {
                                goto yy485;
                            }
                        }
                    } else {
                        if (yyaccept <= 29) {
                            if (yyaccept == 28) {
                                goto yy487;
                            } else {
                                goto yy495;
                            }
                        } else {
                            goto yy499;
                        }
                    }
                }
            }
        } else {
            if (yyaccept <= 45) {
                if (yyaccept <= 38) {
                    if (yyaccept <= 34) {
                        if (yyaccept <= 32) {
                            if (yyaccept == 31) {
                                goto yy522;
                            } else {
                                goto yy526;
                            }
                        } else {
                            if (yyaccept == 33) {
                                goto yy533;
                            } else {
                                goto yy102;
                            }
                        }
                    } else {
                        if (yyaccept <= 36) {
                            if (yyaccept == 35) {
                                goto yy278;
                            } else {
                                goto yy572;
                            }
                        } else {
                            if (yyaccept == 37) {
                                goto yy583;
                            } else {
                                goto yy585;
                            }
                        }
                    }
                } else {
                    if (yyaccept <= 42) {
                        if (yyaccept <= 40) {
                            if (yyaccept == 39) {
                                goto yy587;
                            } else {
                                goto yy592;
                            }
                        } else {
                            if (yyaccept == 41) {
                                goto yy597;
                            } else {
                                goto yy600;
                            }
                        }
                    } else {
                        if (yyaccept <= 44) {
                            if (yyaccept == 43) {
                                goto yy611;
                            } else {
                                goto yy613;
                            }
                        } else {
                            goto yy624;
                        }
                    }
                }
            } else {
                if (yyaccept <= 53) {
                    if (yyaccept <= 49) {
                        if (yyaccept <= 47) {
                            if (yyaccept == 46) {
                                goto yy633;
                            } else {
                                goto yy642;
                            }
                        } else {
                            if (yyaccept == 48) {
                                goto yy647;
                            } else {
                                goto yy650;
                            }
                        }
                    } else {
                        if (yyaccept <= 51) {
                            if (yyaccept == 50) {
                                goto yy655;
                            } else {
                                goto yy684;
                            }
                        } else {
                            if (yyaccept == 52) {
                                goto yy687;
                            } else {
                                goto yy689;
                            }
                        }
                    }
                } else {
                    if (yyaccept <= 57) {
                        if (yyaccept <= 55) {
                            if (yyaccept == 54) {
                                goto yy697;
                            } else {
                                goto yy699;
                            }
                        } else {
                            if (yyaccept == 56) {
                                goto yy714;
                            } else {
                                goto yy716;
                            }
                        }
                    } else {
                        if (yyaccept <= 59) {
                            if (yyaccept == 58) {
                                goto yy720;
                            } else {
                                goto yy722;
                            }
                        } else {
                            goto yy724;
                        }
                    }
                }
            }
        }
    } else {
        if (yyaccept <= 91) {
            if (yyaccept <= 76) {
                if (yyaccept <= 68) {
                    if (yyaccept <= 64) {
                        if (yyaccept <= 62) {
                            if (yyaccept == 61) {
                                goto yy726;
                            } else {
                                goto yy728;
                            }
                        } else {
                            if (yyaccept == 63) {
                                goto yy733;
                            } else {
                                goto yy739;
                            }
                        }
                    } else {
                        if (yyaccept <= 66) {
                            if (yyaccept == 65) {
                                goto yy744;
                            } else {
                                goto yy746;
                            }
                        } else {
                            if (yyaccept == 67) {
                                goto yy748;
                            } else {
                                goto yy750;
                            }
                        }
                    }
                } else {
                    if (yyaccept <= 72) {
                        if (yyaccept <= 70) {
                            if (yyaccept == 69) {
                                goto yy752;
                            } else {
                                goto yy754;
                            }
                        } else {
                            if (yyaccept == 71) {
                                goto yy759;
                            } else {
                                goto yy766;
                            }
                        }
                    } else {
                        if (yyaccept <= 74) {
                            if (yyaccept == 73) {
                                goto yy781;
                            } else {
                                goto yy783;
                            }
                        } else {
                            if (yyaccept == 75) {
                                goto yy785;
                            } else {
                                goto yy787;
                            }
                        }
                    }
                }
            } else {
                if (yyaccept <= 84) {
                    if (yyaccept <= 80) {
                        if (yyaccept <= 78) {
                            if (yyaccept == 77) {
                                goto yy791;
                            } else {
                                goto yy794;
                            }
                        } else {
                            if (yyaccept == 79) {
                                goto yy796;
                            } else {
                                goto yy800;
                            }
                        }
                    } else {
                        if (yyaccept <= 82) {
                            if (yyaccept == 81) {
                                goto yy805;
                            } else {
                                goto yy813;
                            }
                        } else {
                            if (yyaccept == 83) {
                                goto yy817;
                            } else {
                                goto yy821;
                            }
                        }
                    }
                } else {
                    if (yyaccept <= 88) {
                        if (yyaccept <= 86) {
                            if (yyaccept == 85) {
                                goto yy824;
                            } else {
                                goto yy833;
                            }
                        } else {
                            if (yyaccept == 87) {
                                goto yy837;
                            } else {
                                goto yy840;
                            }
                        }
                    } else {
                        if (yyaccept <= 90) {
                            if (yyaccept == 89) {
                                goto yy843;
                            } else {
                                goto yy847;
                            }
                        } else {
                            goto yy852;
                        }
                    }
                }
            }
        } else {
            if (yyaccept <= 106) {
                if (yyaccept <= 99) {
                    if (yyaccept <= 95) {
                        if (yyaccept <= 93) {
                            if (yyaccept == 92) {
                                goto yy854;
                            } else {
                                goto yy856;
                            }
                        } else {
                            if (yyaccept == 94) {
                                goto yy858;
                            } else {
                                goto yy861;
                            }
                        }
                    } else {
                        if (yyaccept <= 97) {
                            if (yyaccept == 96) {
                                goto yy867;
                            } else {
                                goto yy869;
                            }
                        } else {
                            if (yyaccept == 98) {
                                goto yy872;
                            } else {
                                goto yy875;
                            }
                        }
                    }
                } else {
                    if (yyaccept <= 103) {
                        if (yyaccept <= 101) {
                            if (yyaccept == 100) {
                                goto yy877;
                            } else {
                                goto yy880;
                            }
                        } else {
                            if (yyaccept == 102) {
                                goto yy883;
                            } else {
                                goto yy887;
                            }
                        }
                    } else {
                        if (yyaccept <= 105) {
                            if (yyaccept == 104) {
                                goto yy890;
                            } else {
                                goto yy892;
                            }
                        } else {
                            goto yy894;
                        }
                    }
                }
            } else {
                if (yyaccept <= 114) {
                    if (yyaccept <= 110) {
                        if (yyaccept <= 108) {
                            if (yyaccept == 107) {
                                goto yy903;
                            } else {
                                goto yy905;
                            }
                        } else {
                            if (yyaccept == 109) {
                                goto yy907;
                            } else {
                                goto yy909;
                            }
                        }
                    } else {
                        if (yyaccept <= 112) {
                            if (yyaccept == 111) {
                                goto yy912;
                            } else {
                                goto yy914;
                            }
                        } else {
                            if (yyaccept == 113) {
                                goto yy916;
                            } else {
                                goto yy919;
                            }
                        }
                    }
                } else {
                    if (yyaccept <= 118) {
                        if (yyaccept <= 116) {
                            if (yyaccept == 115) {
                                goto yy921;
                            } else {
                                goto yy936;
                            }
                        } else {
                            if (yyaccept == 117) {
                                goto yy953;
                            } else {
                                goto yy957;
                            }
                        }
                    } else {
                        if (yyaccept <= 120) {
                            if (yyaccept == 119) {
                                goto yy961;
                            } else {
                                goto yy964;
                            }
                        } else {
                            goto yy968;
                        }
                    }
                }
            }
        }
    }
yy101:
    ++YYCURSOR;
yy102:
#line 274 "cpp.re"
    { BOOST_WAVE_RET(T_STRINGLIT); }
#line 1280 "cpp_re.inc"
yy103:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 1) {
        goto yy98;
    }
    if (yych <= 0x1F) goto yy100;
    if (yych <= '"') goto yy101;
    if (yych <= '?') goto yy255;
yy104:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '\\') {
        if (yych <= '>') {
            if (yych <= '&') {
                if (yych == '"') goto yy98;
                goto yy100;
            } else {
                if (yych <= '\'') goto yy98;
                if (yych <= '/') goto yy100;
                if (yych <= '7') goto yy98;
                goto yy100;
            }
        } else {
            if (yych <= 'E') {
                if (yych <= '?') goto yy98;
                if (yych <= 'D') goto yy100;
                goto yy98;
            } else {
                if (yych == 'U') goto yy257;
                if (yych <= '[') goto yy100;
                goto yy98;
            }
        }
    } else {
        if (yych <= 'q') {
            if (yych <= 'd') {
                if (yych <= '`') goto yy100;
                if (yych <= 'b') goto yy98;
                goto yy100;
            } else {
                if (yych <= 'f') goto yy98;
                if (yych == 'n') goto yy98;
                goto yy100;
            }
        } else {
            if (yych <= 'u') {
                if (yych == 's') goto yy100;
                if (yych <= 't') goto yy98;
                goto yy258;
            } else {
                if (yych <= 'v') goto yy98;
                if (yych == 'x') goto yy259;
                goto yy100;
            }
        }
    }
yy105:
    ++YYCURSOR;
    if ((YYLIMIT - YYCURSOR) < 12) YYFILL(12);
    yych = *YYCURSOR;
yy106:
    if (yych <= 'h') {
        if (yych <= ' ') {
            if (yych <= '\n') {
                if (yych == '\t') goto yy105;
                goto yy100;
            } else {
                if (yych <= '\f') goto yy105;
                if (yych <= 0x1F) goto yy100;
                goto yy105;
            }
        } else {
            if (yych <= 'c') {
                if (yych == '/') goto yy109;
                goto yy100;
            } else {
                if (yych <= 'd') goto yy111;
                if (yych <= 'e') goto yy112;
                goto yy100;
            }
        }
    } else {
        if (yych <= 'q') {
            if (yych <= 'l') {
                if (yych <= 'i') goto yy113;
                if (yych <= 'k') goto yy100;
                goto yy114;
            } else {
                if (yych == 'p') goto yy115;
                goto yy100;
            }
        } else {
            if (yych <= 'u') {
                if (yych <= 'r') goto yy116;
                if (yych <= 't') goto yy100;
                goto yy117;
            } else {
                if (yych == 'w') goto yy118;
                goto yy100;
            }
        }
    }
yy107:
    ++YYCURSOR;
#line 162 "cpp.re"
    { BOOST_WAVE_RET(T_POUND_POUND); }
#line 1390 "cpp_re.inc"
yy109:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych == '*') goto yy260;
    goto yy100;
yy110:
    yych = *++YYCURSOR;
    if (yych == '?') goto yy262;
    goto yy100;
yy111:
    yych = *++YYCURSOR;
    if (yych == 'e') goto yy263;
    goto yy100;
yy112:
    yych = *++YYCURSOR;
    if (yych <= 'm') {
        if (yych == 'l') goto yy264;
        goto yy100;
    } else {
        if (yych <= 'n') goto yy265;
        if (yych == 'r') goto yy266;
        goto yy100;
    }
yy113:
    yych = *++YYCURSOR;
    if (yych == 'f') goto yy267;
    if (yych == 'n') goto yy269;
    goto yy100;
yy114:
    yych = *++YYCURSOR;
    if (yych == 'i') goto yy270;
    goto yy100;
yy115:
    yych = *++YYCURSOR;
    if (yych == 'r') goto yy271;
    goto yy100;
yy116:
    yych = *++YYCURSOR;
    if (yych == 'e') goto yy272;
    goto yy100;
yy117:
    yych = *++YYCURSOR;
    if (yych == 'n') goto yy273;
    goto yy100;
yy118:
    yych = *++YYCURSOR;
    if (yych == 'a') goto yy274;
    goto yy100;
yy119:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych == '?') goto yy275;
    goto yy100;
yy120:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych == 'U') goto yy183;
    if (yych == 'u') goto yy184;
    goto yy100;
yy121:
    yyaccept = 6;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'e') {
        if (yych <= ' ') {
            if (yych <= '\n') {
                if (yych == '\t') goto yy106;
            } else {
                if (yych <= '\f') goto yy106;
                if (yych >= ' ') goto yy106;
            }
        } else {
            if (yych <= '.') {
                if (yych == '%') goto yy276;
            } else {
                if (yych <= '/') goto yy106;
                if (yych >= 'd') goto yy106;
            }
        }
    } else {
        if (yych <= 'p') {
            if (yych <= 'k') {
                if (yych == 'i') goto yy106;
            } else {
                if (yych <= 'l') goto yy106;
                if (yych >= 'p') goto yy106;
            }
        } else {
            if (yych <= 't') {
                if (yych == 'r') goto yy106;
            } else {
                if (yych == 'v') goto yy122;
                if (yych <= 'w') goto yy106;
            }
        }
    }
yy122:
#line 160 "cpp.re"
    { BOOST_WAVE_RET(T_POUND_ALT); }
#line 1492 "cpp_re.inc"
yy123:
    ++YYCURSOR;
#line 219 "cpp.re"
    { BOOST_WAVE_RET(T_PERCENTASSIGN); }
#line 1497 "cpp_re.inc"
yy125:
    ++YYCURSOR;
#line 152 "cpp.re"
    { BOOST_WAVE_RET(T_RIGHTBRACE_ALT); }
#line 1502 "cpp_re.inc"
yy127:
    ++YYCURSOR;
#line 247 "cpp.re"
    { BOOST_WAVE_RET(T_ANDAND); }
#line 1507 "cpp_re.inc"
yy129:
    ++YYCURSOR;
#line 223 "cpp.re"
    { BOOST_WAVE_RET(T_ANDASSIGN); }
#line 1512 "cpp_re.inc"
yy131:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
yy132:
    if (yybm[0+yych] & 2) {
        goto yy131;
    }
    if (yych <= 0x1F) goto yy100;
    if (yych <= '\'') goto yy277;
    if (yych >= '@') goto yy134;
yy133:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 2) {
        goto yy131;
    }
    if (yych <= 0x1F) goto yy100;
    if (yych <= '\'') goto yy277;
    if (yych <= '?') goto yy279;
yy134:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '\\') {
        if (yych <= '>') {
            if (yych <= '&') {
                if (yych == '"') goto yy131;
                goto yy100;
            } else {
                if (yych <= '\'') goto yy131;
                if (yych <= '/') goto yy100;
                if (yych <= '7') goto yy131;
                goto yy100;
            }
        } else {
            if (yych <= 'E') {
                if (yych <= '?') goto yy131;
                if (yych <= 'D') goto yy100;
                goto yy131;
            } else {
                if (yych == 'U') goto yy281;
                if (yych <= '[') goto yy100;
                goto yy131;
            }
        }
    } else {
        if (yych <= 'q') {
            if (yych <= 'd') {
                if (yych <= '`') goto yy100;
                if (yych <= 'b') goto yy131;
                goto yy100;
            } else {
                if (yych <= 'f') goto yy131;
                if (yych == 'n') goto yy131;
                goto yy100;
            }
        } else {
            if (yych <= 'u') {
                if (yych == 's') goto yy100;
                if (yych <= 't') goto yy131;
                goto yy282;
            } else {
                if (yych <= 'v') goto yy131;
                if (yych == 'x') goto yy283;
                goto yy100;
            }
        }
    }
yy135:
    ++YYCURSOR;
#line 217 "cpp.re"
    { BOOST_WAVE_RET(T_STARASSIGN); }
#line 1587 "cpp_re.inc"
yy137:
    ++YYCURSOR;
#line 254 "cpp.re"
    { BOOST_WAVE_RET(T_PLUSPLUS); }
#line 1592 "cpp_re.inc"
yy139:
    ++YYCURSOR;
#line 215 "cpp.re"
    { BOOST_WAVE_RET(T_PLUSASSIGN); }
#line 1597 "cpp_re.inc"
yy141:
    ++YYCURSOR;
#line 255 "cpp.re"
    { BOOST_WAVE_RET(T_MINUSMINUS); }
#line 1602 "cpp_re.inc"
yy143:
    ++YYCURSOR;
#line 216 "cpp.re"
    { BOOST_WAVE_RET(T_MINUSASSIGN); }
#line 1607 "cpp_re.inc"
yy145:
    yych = *++YYCURSOR;
    if (yych == '*') goto yy284;
#line 267 "cpp.re"
    { BOOST_WAVE_RET(T_ARROW); }
#line 1613 "cpp_re.inc"
yy147:
    ++YYCURSOR;
#line 185 "cpp.re"
    {
            if (s->act_in_c99_mode) {
                --YYCURSOR;
                BOOST_WAVE_RET(T_DOT);
            }
            else {
                BOOST_WAVE_RET(T_DOTSTAR);
            }
        }
#line 1626 "cpp_re.inc"
yy149:
    yych = *++YYCURSOR;
    if (yych == '.') goto yy286;
    goto yy100;
yy150:
    ++YYCURSOR;
#line 44 "cpp.re"
    { goto ccomment; }
#line 1635 "cpp_re.inc"
yy152:
    ++YYCURSOR;
#line 45 "cpp.re"
    { goto cppcomment; }
#line 1640 "cpp_re.inc"
yy154:
    ++YYCURSOR;
#line 218 "cpp.re"
    { BOOST_WAVE_RET(T_DIVIDEASSIGN); }
#line 1645 "cpp_re.inc"
yy156:
    ++YYCURSOR;
#line 174 "cpp.re"
    {
            if (s->act_in_c99_mode) {
                --YYCURSOR;
                BOOST_WAVE_RET(T_COLON);
            }
            else {
                BOOST_WAVE_RET(T_COLON_COLON);
            }
        }
#line 1658 "cpp_re.inc"
yy158:
    ++YYCURSOR;
#line 158 "cpp.re"
    { BOOST_WAVE_RET(T_RIGHTBRACKET_ALT); }
#line 1663 "cpp_re.inc"
yy160:
    ++YYCURSOR;
#line 149 "cpp.re"
    { BOOST_WAVE_RET(T_LEFTBRACE_ALT); }
#line 1668 "cpp_re.inc"
yy162:
    ++YYCURSOR;
#line 155 "cpp.re"
    { BOOST_WAVE_RET(T_LEFTBRACKET_ALT); }
#line 1673 "cpp_re.inc"
yy164:
    yych = *++YYCURSOR;
    if (yych == '=') goto yy288;
#line 228 "cpp.re"
    { BOOST_WAVE_RET(T_SHIFTLEFT); }
#line 1679 "cpp_re.inc"
yy166:
    yych = *++YYCURSOR;
    if (yych == '>') goto yy290;
#line 245 "cpp.re"
    { BOOST_WAVE_RET(T_LESSEQUAL); }
#line 1685 "cpp_re.inc"
yy168:
    ++YYCURSOR;
#line 232 "cpp.re"
    { BOOST_WAVE_RET(T_EQUAL); }
#line 1690 "cpp_re.inc"
yy170:
    ++YYCURSOR;
#line 246 "cpp.re"
    { BOOST_WAVE_RET(T_GREATEREQUAL); }
#line 1695 "cpp_re.inc"
yy172:
    yych = *++YYCURSOR;
    if (yych == '=') goto yy292;
#line 229 "cpp.re"
    { BOOST_WAVE_RET(T_SHIFTRIGHT); }
#line 1701 "cpp_re.inc"
yy174:
    yych = *++YYCURSOR;
    switch (yych) {
    case '!':    goto yy294;
    case '\'':    goto yy296;
    case '(':    goto yy298;
    case ')':    goto yy300;
    case '-':    goto yy302;
    case '/':    goto yy304;
    case '<':    goto yy306;
    case '=':    goto yy308;
    case '>':    goto yy310;
    default:    goto yy100;
    }
yy175:
    yych = *++YYCURSOR;
    if (yych == '\'') goto yy100;
    goto yy132;
yy176:
    ++YYCURSOR;
#line 277 "cpp.re"
    {
            if (s->act_in_cpp0x_mode)
            {
                rawstringdelim = "";
                goto extrawstringlit;
            }
            --YYCURSOR;
            BOOST_WAVE_RET(T_IDENTIFIER);
        }
#line 1732 "cpp_re.inc"
yy178:
    ++YYCURSOR;
#line 296 "cpp.re"
    {
            if (s->act_in_cpp0x_mode)
                goto extstringlit;
            --YYCURSOR;
            BOOST_WAVE_RET(T_IDENTIFIER);
        }
#line 1742 "cpp_re.inc"
yy180:
    ++YYCURSOR;
#line 288 "cpp.re"
    {
            if (s->act_in_cpp0x_mode)
                goto extcharlit;
            --YYCURSOR;
            BOOST_WAVE_RET(T_IDENTIFIER);
        }
#line 1752 "cpp_re.inc"
yy182:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '"') goto yy312;
    goto yy19;
yy183:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy314;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy314;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy314;
        goto yy100;
    }
yy184:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy315;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy315;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy315;
        goto yy100;
    }
yy185:
    ++YYCURSOR;
#line 220 "cpp.re"
    { BOOST_WAVE_RET(T_XORASSIGN); }
#line 1790 "cpp_re.inc"
yy187:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    switch (yych) {
    case 'a':    goto yy188;
    case 'b':    goto yy189;
    case 'c':    goto yy190;
    case 'd':    goto yy191;
    case 'e':    goto yy316;
    case 'f':    goto yy317;
    case 'i':    goto yy318;
    case 'l':    goto yy319;
    case 's':    goto yy194;
    case 't':    goto yy320;
    default:    goto yy19;
    }
yy188:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy321;
    goto yy19;
yy189:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy322;
    goto yy19;
yy190:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy323;
    goto yy19;
yy191:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy324;
    goto yy19;
yy192:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy325;
    goto yy19;
yy193:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy326;
    goto yy19;
yy194:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy327;
    goto yy19;
yy195:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy328;
    goto yy19;
yy196:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy329;
    goto yy19;
yy197:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'm') goto yy331;
    goto yy19;
yy198:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy333;
    goto yy19;
yy199:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy334;
    goto yy19;
yy200:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'o') goto yy335;
    goto yy19;
yy201:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy336;
    goto yy19;
yy202:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'r') goto yy19;
    if (yych <= 's') goto yy337;
    if (yych <= 't') goto yy338;
    goto yy19;
yy203:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy339;
    goto yy19;
yy204:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy340;
    goto yy19;
yy205:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'l') {
        if (yych == '_') goto yy341;
        goto yy19;
    } else {
        if (yych <= 'm') goto yy342;
        if (yych <= 'n') goto yy343;
        goto yy19;
    }
yy206:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'e') {
        if (yych == 'c') goto yy344;
        goto yy19;
    } else {
        if (yych <= 'f') goto yy345;
        if (yych == 'l') goto yy346;
        goto yy19;
    }
yy207:
    yyaccept = 7;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'Z') {
        if (yych <= '9') {
            if (yych == '$') goto yy18;
            if (yych >= '0') goto yy18;
        } else {
            if (yych == '?') goto yy119;
            if (yych >= 'A') goto yy18;
        }
    } else {
        if (yych <= '_') {
            if (yych == '\\') goto yy120;
            if (yych >= '_') goto yy18;
        } else {
            if (yych <= 't') {
                if (yych >= 'a') goto yy18;
            } else {
                if (yych <= 'u') goto yy347;
                if (yych <= 'z') goto yy18;
            }
        }
    }
yy208:
#line 74 "cpp.re"
    { BOOST_WAVE_RET(T_DO); }
#line 1943 "cpp_re.inc"
yy209:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy348;
    goto yy19;
yy210:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy349;
    goto yy19;
yy211:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'u') goto yy350;
    goto yy19;
yy212:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy351;
    if (yych == 't') goto yy352;
    goto yy19;
yy213:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy353;
    goto yy19;
yy214:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'o') goto yy354;
    goto yy19;
yy215:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy355;
    goto yy19;
yy216:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy357;
    goto yy19;
yy217:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy358;
    goto yy19;
yy218:
    yyaccept = 8;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy219:
#line 87 "cpp.re"
    { BOOST_WAVE_RET(T_IF); }
#line 2001 "cpp_re.inc"
yy220:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy359;
    goto yy19;
yy221:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy360;
    if (yych == 't') goto yy361;
    goto yy19;
yy222:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy363;
    goto yy19;
yy223:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy364;
    goto yy19;
yy224:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'm') goto yy365;
    goto yy19;
yy225:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'w') goto yy366;
    goto yy19;
yy226:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy368;
    if (yych == 't') goto yy369;
    goto yy19;
yy227:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy371;
    goto yy19;
yy228:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy372;
    goto yy19;
yy229:
    yyaccept = 9;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '@') {
        if (yych <= '/') {
            if (yych == '$') goto yy18;
        } else {
            if (yych <= '9') goto yy18;
            if (yych == '?') goto yy119;
        }
    } else {
        if (yych <= '^') {
            if (yych <= 'Z') goto yy18;
            if (yych == '\\') goto yy120;
        } else {
            if (yych <= '_') goto yy373;
            if (yych <= '`') goto yy230;
            if (yych <= 'z') goto yy18;
        }
    }
yy230:
#line 252 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_c99_mode ? T_IDENTIFIER : T_OROR_ALT); }
#line 2072 "cpp_re.inc"
yy231:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy374;
    if (yych == 'o') goto yy375;
    goto yy19;
yy232:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'b') goto yy376;
    goto yy19;
yy233:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    switch (yych) {
    case 'g':    goto yy377;
    case 'i':    goto yy378;
    case 'q':    goto yy379;
    case 't':    goto yy380;
    default:    goto yy19;
    }
yy234:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'o') goto yy381;
    goto yy19;
yy235:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'g') goto yy382;
    if (yych == 'z') goto yy383;
    goto yy19;
yy236:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy384;
    if (yych == 'r') goto yy385;
    goto yy19;
yy237:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy386;
    goto yy19;
yy238:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'm') goto yy387;
    goto yy19;
yy239:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy388;
    if (yych == 'r') goto yy389;
    goto yy19;
yy240:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'u') goto yy390;
    if (yych == 'y') goto yy391;
    goto yy19;
yy241:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy393;
    goto yy19;
yy242:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '"') goto yy178;
    if (yych == 'R') goto yy182;
    goto yy19;
yy243:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy394;
    if (yych == 's') goto yy395;
    goto yy19;
yy244:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy396;
    goto yy19;
yy245:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy397;
    goto yy19;
yy246:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy398;
    if (yych == 'l') goto yy399;
    goto yy19;
yy247:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'h') goto yy400;
    goto yy19;
yy248:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy401;
    goto yy19;
yy249:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy402;
    goto yy19;
yy250:
    ++YYCURSOR;
#line 225 "cpp.re"
    { BOOST_WAVE_RET(T_ORASSIGN); }
#line 2185 "cpp_re.inc"
yy252:
    yych = *++YYCURSOR;
    if (yych == '?') goto yy404;
    goto yy100;
yy253:
    ++YYCURSOR;
#line 249 "cpp.re"
    { BOOST_WAVE_RET(T_OROR); }
#line 2194 "cpp_re.inc"
yy255:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 4) {
        goto yy255;
    }
    if (yych <= '!') {
        if (yych <= '\n') {
            if (yych == '\t') goto yy98;
            goto yy100;
        } else {
            if (yych <= '\f') goto yy98;
            if (yych <= 0x1F) goto yy100;
            goto yy98;
        }
    } else {
        if (yych <= '/') {
            if (yych <= '"') goto yy101;
            if (yych <= '.') goto yy98;
            goto yy405;
        } else {
            if (yych == '\\') goto yy104;
            goto yy98;
        }
    }
yy257:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy407;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy407;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy407;
        goto yy100;
    }
yy258:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy408;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy408;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy408;
        goto yy100;
    }
yy259:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy98;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy98;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy98;
        goto yy100;
    }
yy260:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 8) {
        goto yy260;
    }
    if (yych <= 0x1F) goto yy100;
    goto yy409;
yy262:
    yych = *++YYCURSOR;
    if (yych == '=') goto yy411;
    goto yy100;
yy263:
    yych = *++YYCURSOR;
    if (yych == 'f') goto yy413;
    goto yy100;
yy264:
    yych = *++YYCURSOR;
    if (yych == 'i') goto yy414;
    if (yych == 's') goto yy415;
    goto yy100;
yy265:
    yych = *++YYCURSOR;
    if (yych == 'd') goto yy416;
    goto yy100;
yy266:
    yych = *++YYCURSOR;
    if (yych == 'r') goto yy417;
    goto yy100;
yy267:
    yyaccept = 10;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy418;
    if (yych == 'n') goto yy419;
yy268:
#line 326 "cpp.re"
    { BOOST_WAVE_RET(T_PP_IF); }
#line 2301 "cpp_re.inc"
yy269:
    yych = *++YYCURSOR;
    if (yych == 'c') goto yy420;
    goto yy100;
yy270:
    yych = *++YYCURSOR;
    if (yych == 'n') goto yy421;
    goto yy100;
yy271:
    yych = *++YYCURSOR;
    if (yych == 'a') goto yy422;
    goto yy100;
yy272:
    yych = *++YYCURSOR;
    if (yych == 'g') goto yy423;
    goto yy100;
yy273:
    yych = *++YYCURSOR;
    if (yych == 'd') goto yy424;
    goto yy100;
yy274:
    yych = *++YYCURSOR;
    if (yych == 'r') goto yy425;
    goto yy100;
yy275:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych == '/') goto yy120;
    goto yy100;
yy276:
    yych = *++YYCURSOR;
    if (yych == ':') goto yy426;
    goto yy100;
yy277:
    ++YYCURSOR;
yy278:
#line 271 "cpp.re"
    { BOOST_WAVE_RET(T_CHARLIT); }
#line 2341 "cpp_re.inc"
yy279:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '\'') {
        if (yych <= '\n') {
            if (yych == '\t') goto yy131;
            goto yy100;
        } else {
            if (yych <= '\f') goto yy131;
            if (yych <= 0x1F) goto yy100;
            if (yych <= '&') goto yy131;
            goto yy277;
        }
    } else {
        if (yych <= '>') {
            if (yych == '/') goto yy428;
            goto yy131;
        } else {
            if (yych <= '?') goto yy279;
            if (yych == '\\') goto yy134;
            goto yy131;
        }
    }
yy281:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy430;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy430;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy430;
        goto yy100;
    }
yy282:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy431;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy431;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy431;
        goto yy100;
    }
yy283:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy131;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy131;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy131;
        goto yy100;
    }
yy284:
    ++YYCURSOR;
#line 258 "cpp.re"
    {
            if (s->act_in_c99_mode) {
                --YYCURSOR;
                BOOST_WAVE_RET(T_ARROW);
            }
            else {
                BOOST_WAVE_RET(T_ARROWSTAR);
            }
        }
#line 2420 "cpp_re.inc"
yy286:
    ++YYCURSOR;
#line 171 "cpp.re"
    { BOOST_WAVE_RET(T_ELLIPSIS); }
#line 2425 "cpp_re.inc"
yy288:
    ++YYCURSOR;
#line 231 "cpp.re"
    { BOOST_WAVE_RET(T_SHIFTLEFTASSIGN); }
#line 2430 "cpp_re.inc"
yy290:
    ++YYCURSOR;
#line 236 "cpp.re"
    {
            if (s->act_in_cpp2a_mode) {
                BOOST_WAVE_RET(T_SPACESHIP);
            }
            else {
                --YYCURSOR;
                BOOST_WAVE_RET(T_LESSEQUAL);
            }
        }
#line 2443 "cpp_re.inc"
yy292:
    ++YYCURSOR;
#line 230 "cpp.re"
    { BOOST_WAVE_RET(T_SHIFTRIGHTASSIGN); }
#line 2448 "cpp_re.inc"
yy294:
    yyaccept = 11;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '>') {
        if (yych == '=') goto yy432;
    } else {
        if (yych <= '?') goto yy434;
        if (yych == '|') goto yy435;
    }
yy295:
#line 206 "cpp.re"
    { BOOST_WAVE_RET(T_OR_TRIGRAPH); }
#line 2461 "cpp_re.inc"
yy296:
    yych = *++YYCURSOR;
    if (yych == '=') goto yy437;
#line 200 "cpp.re"
    { BOOST_WAVE_RET(T_XOR_TRIGRAPH); }
#line 2467 "cpp_re.inc"
yy298:
    ++YYCURSOR;
#line 154 "cpp.re"
    { BOOST_WAVE_RET(T_LEFTBRACKET_TRIGRAPH); }
#line 2472 "cpp_re.inc"
yy300:
    ++YYCURSOR;
#line 157 "cpp.re"
    { BOOST_WAVE_RET(T_RIGHTBRACKET_TRIGRAPH); }
#line 2477 "cpp_re.inc"
yy302:
    ++YYCURSOR;
#line 208 "cpp.re"
    { BOOST_WAVE_RET(T_COMPL_TRIGRAPH); }
#line 2482 "cpp_re.inc"
yy304:
    yyaccept = 12;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'U') goto yy183;
    if (yych == 'u') goto yy184;
yy305:
#line 268 "cpp.re"
    { BOOST_WAVE_RET(T_ANY_TRIGRAPH); }
#line 2491 "cpp_re.inc"
yy306:
    ++YYCURSOR;
#line 148 "cpp.re"
    { BOOST_WAVE_RET(T_LEFTBRACE_TRIGRAPH); }
#line 2496 "cpp_re.inc"
yy308:
    yyaccept = 13;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'c') {
        if (yych <= ' ') {
            if (yych <= '\n') {
                if (yych == '\t') goto yy106;
            } else {
                if (yych <= '\f') goto yy106;
                if (yych >= ' ') goto yy106;
            }
        } else {
            if (yych <= '.') {
                if (yych == '#') goto yy439;
            } else {
                if (yych <= '/') goto yy106;
                if (yych == '?') goto yy441;
            }
        }
    } else {
        if (yych <= 'p') {
            if (yych <= 'i') {
                if (yych <= 'e') goto yy106;
                if (yych >= 'i') goto yy106;
            } else {
                if (yych == 'l') goto yy106;
                if (yych >= 'p') goto yy106;
            }
        } else {
            if (yych <= 't') {
                if (yych == 'r') goto yy106;
            } else {
                if (yych == 'v') goto yy309;
                if (yych <= 'w') goto yy106;
            }
        }
    }
yy309:
#line 161 "cpp.re"
    { BOOST_WAVE_RET(T_POUND_TRIGRAPH); }
#line 2537 "cpp_re.inc"
yy310:
    ++YYCURSOR;
#line 151 "cpp.re"
    { BOOST_WAVE_RET(T_RIGHTBRACE_TRIGRAPH); }
#line 2542 "cpp_re.inc"
yy312:
    ++YYCURSOR;
#line 304 "cpp.re"
    {
            if (s->act_in_cpp0x_mode)
            {
                rawstringdelim = "";
                goto extrawstringlit;
            }
            --YYCURSOR;
            BOOST_WAVE_RET(T_IDENTIFIER);
        }
#line 2555 "cpp_re.inc"
yy314:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy442;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy442;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy442;
        goto yy100;
    }
yy315:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy443;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy443;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy443;
        goto yy100;
    }
yy316:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'x') goto yy444;
    goto yy19;
yy317:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy325;
    if (yych == 'i') goto yy445;
    goto yy19;
yy318:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy446;
    goto yy19;
yy319:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy447;
    goto yy19;
yy320:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy448;
    goto yy19;
yy321:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'm') goto yy449;
    goto yy19;
yy322:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy451;
    goto yy19;
yy323:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy452;
    goto yy19;
yy324:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy453;
    goto yy19;
yy325:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy454;
    goto yy19;
yy326:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy455;
    goto yy19;
yy327:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy456;
    goto yy19;
yy328:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'g') goto yy457;
    goto yy19;
yy329:
    yyaccept = 14;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '@') {
        if (yych <= '/') {
            if (yych == '$') goto yy18;
        } else {
            if (yych <= '9') goto yy18;
            if (yych == '?') goto yy119;
        }
    } else {
        if (yych <= '^') {
            if (yych <= 'Z') goto yy18;
            if (yych == '\\') goto yy120;
        } else {
            if (yych <= '_') goto yy458;
            if (yych <= '`') goto yy330;
            if (yych <= 'z') goto yy18;
        }
    }
yy330:
#line 248 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_c99_mode ? T_IDENTIFIER : T_ANDAND_ALT); }
#line 2673 "cpp_re.inc"
yy331:
    yyaccept = 15;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy332:
#line 50 "cpp.re"
    { BOOST_WAVE_RET(T_ASM); }
#line 2685 "cpp_re.inc"
yy333:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'o') goto yy459;
    goto yy19;
yy334:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy461;
    if (yych == 'o') goto yy462;
    goto yy19;
yy335:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy463;
    goto yy19;
yy336:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy465;
    goto yy19;
yy337:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy466;
    goto yy19;
yy338:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy468;
    goto yy19;
yy339:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy469;
    goto yy19;
yy340:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy471;
    goto yy19;
yy341:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'q') {
        if (yych == 'a') goto yy472;
        goto yy19;
    } else {
        if (yych <= 'r') goto yy473;
        if (yych == 'y') goto yy474;
        goto yy19;
    }
yy342:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy475;
    goto yy19;
yy343:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'r') {
        if (yych == 'c') goto yy476;
        goto yy19;
    } else {
        if (yych <= 's') goto yy477;
        if (yych <= 't') goto yy478;
        goto yy19;
    }
yy344:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy479;
    goto yy19;
yy345:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy480;
    goto yy19;
yy346:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy481;
    goto yy19;
yy347:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'b') goto yy482;
    goto yy19;
yy348:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy483;
    goto yy19;
yy349:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy484;
    goto yy19;
yy350:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'm') goto yy486;
    goto yy19;
yy351:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy488;
    if (yych == 'o') goto yy489;
    goto yy19;
yy352:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy490;
    goto yy19;
yy353:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy491;
    goto yy19;
yy354:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy492;
    goto yy19;
yy355:
    yyaccept = 16;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy356:
#line 84 "cpp.re"
    { BOOST_WAVE_RET(T_FOR); }
#line 2821 "cpp_re.inc"
yy357:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy493;
    goto yy19;
yy358:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'o') goto yy494;
    goto yy19;
yy359:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'o') goto yy496;
    goto yy19;
yy360:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy497;
    goto yy19;
yy361:
    yyaccept = 17;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy362:
#line 90 "cpp.re"
    { BOOST_WAVE_RET(T_INT); }
#line 2853 "cpp_re.inc"
yy363:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'g') goto yy498;
    goto yy19;
yy364:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy500;
    goto yy19;
yy365:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy501;
    goto yy19;
yy366:
    yyaccept = 18;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy367:
#line 94 "cpp.re"
    { BOOST_WAVE_RET(T_NEW); }
#line 2880 "cpp_re.inc"
yy368:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'x') goto yy502;
    goto yy19;
yy369:
    yyaccept = 19;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '@') {
        if (yych <= '/') {
            if (yych == '$') goto yy18;
        } else {
            if (yych <= '9') goto yy18;
            if (yych == '?') goto yy119;
        }
    } else {
        if (yych <= '^') {
            if (yych <= 'Z') goto yy18;
            if (yych == '\\') goto yy120;
        } else {
            if (yych <= '_') goto yy503;
            if (yych <= '`') goto yy370;
            if (yych <= 'z') goto yy18;
        }
    }
yy370:
#line 211 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_c99_mode ? T_IDENTIFIER : T_NOT_ALT); }
#line 2909 "cpp_re.inc"
yy371:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy504;
    goto yy19;
yy372:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy505;
    goto yy19;
yy373:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy506;
    goto yy19;
yy374:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'v') goto yy507;
    goto yy19;
yy375:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy508;
    goto yy19;
yy376:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy509;
    goto yy19;
yy377:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy510;
    goto yy19;
yy378:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy511;
    goto yy19;
yy379:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'u') goto yy512;
    goto yy19;
yy380:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'u') goto yy513;
    goto yy19;
yy381:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy514;
    goto yy19;
yy382:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy515;
    goto yy19;
yy383:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy516;
    goto yy19;
yy384:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy517;
    goto yy19;
yy385:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'u') goto yy518;
    goto yy19;
yy386:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy519;
    goto yy19;
yy387:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy520;
    goto yy19;
yy388:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy521;
    goto yy19;
yy389:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy523;
    if (yych == 'o') goto yy524;
    goto yy19;
yy390:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy525;
    goto yy19;
yy391:
    yyaccept = 20;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy392:
#line 118 "cpp.re"
    { BOOST_WAVE_RET(T_TRY); }
#line 3022 "cpp_re.inc"
yy393:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy527;
    goto yy19;
yy394:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'o') goto yy528;
    goto yy19;
yy395:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy529;
    goto yy19;
yy396:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy530;
    goto yy19;
yy397:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy531;
    goto yy19;
yy398:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy532;
    goto yy19;
yy399:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy534;
    goto yy19;
yy400:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy535;
    goto yy19;
yy401:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy536;
    goto yy19;
yy402:
    yyaccept = 21;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '@') {
        if (yych <= '/') {
            if (yych == '$') goto yy18;
        } else {
            if (yych <= '9') goto yy18;
            if (yych == '?') goto yy119;
        }
    } else {
        if (yych <= '^') {
            if (yych <= 'Z') goto yy18;
            if (yych == '\\') goto yy120;
        } else {
            if (yych <= '_') goto yy537;
            if (yych <= '`') goto yy403;
            if (yych <= 'z') goto yy18;
        }
    }
yy403:
#line 201 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_c99_mode ? T_IDENTIFIER : T_XOR_ALT); }
#line 3091 "cpp_re.inc"
yy404:
    yych = *++YYCURSOR;
    if (yych == '!') goto yy538;
    goto yy100;
yy405:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 1) {
        goto yy98;
    }
    if (yych <= 0x1F) goto yy100;
    if (yych <= '"') goto yy540;
    if (yych <= '?') goto yy103;
    goto yy405;
yy407:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy541;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy541;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy541;
        goto yy100;
    }
yy408:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy542;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy542;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy542;
        goto yy100;
    }
yy409:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 32) {
        goto yy409;
    }
    if (yych <= 0x1F) {
        if (yych <= 0x08) goto yy100;
        if (yych <= '\r') goto yy260;
        goto yy100;
    } else {
        if (yych == '/') goto yy105;
        goto yy260;
    }
yy411:
    ++YYCURSOR;
#line 163 "cpp.re"
    { BOOST_WAVE_RET(T_POUND_POUND_TRIGRAPH); }
#line 3154 "cpp_re.inc"
yy413:
    yych = *++YYCURSOR;
    if (yych == 'i') goto yy543;
    goto yy100;
yy414:
    yych = *++YYCURSOR;
    if (yych == 'f') goto yy544;
    goto yy100;
yy415:
    yych = *++YYCURSOR;
    if (yych == 'e') goto yy546;
    goto yy100;
yy416:
    yych = *++YYCURSOR;
    if (yych == 'i') goto yy548;
    if (yych == 'r') goto yy549;
    goto yy100;
yy417:
    yych = *++YYCURSOR;
    if (yych == 'o') goto yy550;
    goto yy100;
yy418:
    yych = *++YYCURSOR;
    if (yych == 'e') goto yy551;
    goto yy100;
yy419:
    yych = *++YYCURSOR;
    if (yych == 'd') goto yy552;
    goto yy100;
yy420:
    yych = *++YYCURSOR;
    if (yych == 'l') goto yy553;
    goto yy100;
yy421:
    yych = *++YYCURSOR;
    if (yych == 'e') goto yy554;
    goto yy100;
yy422:
    yych = *++YYCURSOR;
    if (yych == 'g') goto yy556;
    goto yy100;
yy423:
    yych = *++YYCURSOR;
    if (yych == 'i') goto yy557;
    goto yy100;
yy424:
    yych = *++YYCURSOR;
    if (yych == 'e') goto yy558;
    goto yy100;
yy425:
    yych = *++YYCURSOR;
    if (yych == 'n') goto yy559;
    goto yy100;
yy426:
    ++YYCURSOR;
#line 166 "cpp.re"
    { BOOST_WAVE_RET(T_POUND_POUND_ALT); }
#line 3212 "cpp_re.inc"
yy428:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 2) {
        goto yy131;
    }
    if (yych <= 0x1F) goto yy100;
    if (yych <= '\'') goto yy560;
    if (yych <= '?') goto yy133;
    goto yy428;
yy430:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy561;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy561;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy561;
        goto yy100;
    }
yy431:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy562;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy562;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy562;
        goto yy100;
    }
yy432:
    ++YYCURSOR;
#line 227 "cpp.re"
    { BOOST_WAVE_RET(T_ORASSIGN_TRIGRAPH); }
#line 3256 "cpp_re.inc"
yy434:
    yych = *++YYCURSOR;
    if (yych == '?') goto yy563;
    goto yy100;
yy435:
    ++YYCURSOR;
#line 250 "cpp.re"
    { BOOST_WAVE_RET(T_OROR_TRIGRAPH); }
#line 3265 "cpp_re.inc"
yy437:
    ++YYCURSOR;
#line 222 "cpp.re"
    { BOOST_WAVE_RET(T_XORASSIGN_TRIGRAPH); }
#line 3270 "cpp_re.inc"
yy439:
    ++YYCURSOR;
#line 164 "cpp.re"
    { BOOST_WAVE_RET(T_POUND_POUND_TRIGRAPH); }
#line 3275 "cpp_re.inc"
yy441:
    yych = *++YYCURSOR;
    if (yych == '?') goto yy564;
    goto yy100;
yy442:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy565;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy565;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy565;
        goto yy100;
    }
yy443:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy566;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy566;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy566;
        goto yy100;
    }
yy444:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy567;
    goto yy19;
yy445:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy568;
    goto yy19;
yy446:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy455;
    if (yych == 't') goto yy569;
    goto yy19;
yy447:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy570;
    goto yy19;
yy448:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'y') goto yy571;
    goto yy19;
yy449:
    yyaccept = 22;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy450:
#line 145 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_ASM : T_IDENTIFIER); }
#line 3345 "cpp_re.inc"
yy451:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy573;
    goto yy19;
yy452:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy574;
    goto yy19;
yy453:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy575;
    goto yy19;
yy454:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy576;
    goto yy19;
yy455:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy577;
    goto yy19;
yy456:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy578;
    goto yy19;
yy457:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy579;
    goto yy19;
yy458:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy580;
    goto yy19;
yy459:
    yyaccept = 23;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy460:
#line 51 "cpp.re"
    { BOOST_WAVE_RET(T_AUTO); }
#line 3397 "cpp_re.inc"
yy461:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy581;
    goto yy19;
yy462:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy582;
    goto yy19;
yy463:
    yyaccept = 24;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy464:
#line 52 "cpp.re"
    { BOOST_WAVE_RET(T_BOOL); }
#line 3419 "cpp_re.inc"
yy465:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'k') goto yy584;
    goto yy19;
yy466:
    yyaccept = 25;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy467:
#line 54 "cpp.re"
    { BOOST_WAVE_RET(T_CASE); }
#line 3436 "cpp_re.inc"
yy468:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'h') goto yy586;
    goto yy19;
yy469:
    yyaccept = 26;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '9') {
        if (yych <= '1') {
            if (yych <= '$') {
                if (yych >= '$') goto yy18;
            } else {
                if (yych <= '/') goto yy470;
                if (yych <= '0') goto yy18;
                goto yy588;
            }
        } else {
            if (yych <= '3') {
                if (yych <= '2') goto yy18;
                goto yy589;
            } else {
                if (yych == '8') goto yy590;
                goto yy18;
            }
        }
    } else {
        if (yych <= '[') {
            if (yych <= '?') {
                if (yych >= '?') goto yy119;
            } else {
                if (yych <= '@') goto yy470;
                if (yych <= 'Z') goto yy18;
            }
        } else {
            if (yych <= '_') {
                if (yych <= '\\') goto yy120;
                if (yych >= '_') goto yy18;
            } else {
                if (yych <= '`') goto yy470;
                if (yych <= 'z') goto yy18;
            }
        }
    }
yy470:
#line 56 "cpp.re"
    { BOOST_WAVE_RET(T_CHAR); }
#line 3484 "cpp_re.inc"
yy471:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy591;
    goto yy19;
yy472:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'w') goto yy593;
    goto yy19;
yy473:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy594;
    goto yy19;
yy474:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy595;
    goto yy19;
yy475:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy596;
    goto yy19;
yy476:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy598;
    goto yy19;
yy477:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy599;
    goto yy19;
yy478:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy601;
    goto yy19;
yy479:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy602;
    goto yy19;
yy480:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'u') goto yy603;
    goto yy19;
yy481:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy604;
    goto yy19;
yy482:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy605;
    goto yy19;
yy483:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'm') goto yy606;
    goto yy19;
yy484:
    yyaccept = 27;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy485:
#line 77 "cpp.re"
    { BOOST_WAVE_RET(T_ELSE); }
#line 3561 "cpp_re.inc"
yy486:
    yyaccept = 28;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy487:
#line 78 "cpp.re"
    { BOOST_WAVE_RET(T_ENUM); }
#line 3573 "cpp_re.inc"
yy488:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy607;
    goto yy19;
yy489:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy608;
    goto yy19;
yy490:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy609;
    goto yy19;
yy491:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy610;
    goto yy19;
yy492:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy612;
    goto yy19;
yy493:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy614;
    goto yy19;
yy494:
    yyaccept = 29;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy495:
#line 86 "cpp.re"
    { BOOST_WAVE_RET(T_GOTO); }
#line 3615 "cpp_re.inc"
yy496:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy615;
    goto yy19;
yy497:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy616;
    goto yy19;
yy498:
    yyaccept = 30;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy499:
#line 91 "cpp.re"
    { BOOST_WAVE_RET(T_LONG); }
#line 3637 "cpp_re.inc"
yy500:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'b') goto yy617;
    goto yy19;
yy501:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy618;
    goto yy19;
yy502:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy619;
    goto yy19;
yy503:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy620;
    goto yy19;
yy504:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy621;
    goto yy19;
yy505:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy622;
    goto yy19;
yy506:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'q') goto yy623;
    goto yy19;
yy507:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy625;
    goto yy19;
yy508:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy626;
    goto yy19;
yy509:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy627;
    goto yy19;
yy510:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy628;
    goto yy19;
yy511:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy629;
    goto yy19;
yy512:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy630;
    goto yy19;
yy513:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy631;
    goto yy19;
yy514:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy632;
    goto yy19;
yy515:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy634;
    goto yy19;
yy516:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'o') goto yy635;
    goto yy19;
yy517:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy636;
    goto yy19;
yy518:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy637;
    goto yy19;
yy519:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy638;
    goto yy19;
yy520:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy639;
    goto yy19;
yy521:
    yyaccept = 31;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy522:
#line 114 "cpp.re"
    { BOOST_WAVE_RET(T_THIS); }
#line 3754 "cpp_re.inc"
yy523:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy640;
    goto yy19;
yy524:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'w') goto yy641;
    goto yy19;
yy525:
    yyaccept = 32;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy526:
#line 117 "cpp.re"
    { BOOST_WAVE_RET(T_TRUE); }
#line 3776 "cpp_re.inc"
yy527:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'h') {
        if (yych == 'd') goto yy643;
        goto yy19;
    } else {
        if (yych <= 'i') goto yy644;
        if (yych == 'n') goto yy645;
        goto yy19;
    }
yy528:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy646;
    goto yy19;
yy529:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'g') goto yy648;
    goto yy19;
yy530:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'g') goto yy649;
    goto yy19;
yy531:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'u') goto yy651;
    goto yy19;
yy532:
    yyaccept = 33;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy533:
#line 126 "cpp.re"
    { BOOST_WAVE_RET(T_VOID); }
#line 3819 "cpp_re.inc"
yy534:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy652;
    goto yy19;
yy535:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy653;
    goto yy19;
yy536:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy654;
    goto yy19;
yy537:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy656;
    goto yy19;
yy538:
    ++YYCURSOR;
#line 251 "cpp.re"
    { BOOST_WAVE_RET(T_OROR_TRIGRAPH); }
#line 3844 "cpp_re.inc"
yy540:
    yyaccept = 34;
    YYMARKER = ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 1) {
        goto yy98;
    }
    if (yych <= 0x1F) goto yy102;
    if (yych <= '"') goto yy101;
    if (yych <= '?') goto yy103;
    goto yy104;
yy541:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy657;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy657;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy657;
        goto yy100;
    }
yy542:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy259;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy259;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy259;
        goto yy100;
    }
yy543:
    yych = *++YYCURSOR;
    if (yych == 'n') goto yy658;
    goto yy100;
yy544:
    ++YYCURSOR;
#line 330 "cpp.re"
    { BOOST_WAVE_RET(T_PP_ELIF); }
#line 3893 "cpp_re.inc"
yy546:
    ++YYCURSOR;
#line 329 "cpp.re"
    { BOOST_WAVE_RET(T_PP_ELSE); }
#line 3898 "cpp_re.inc"
yy548:
    yych = *++YYCURSOR;
    if (yych == 'f') goto yy659;
    goto yy100;
yy549:
    yych = *++YYCURSOR;
    if (yych == 'e') goto yy661;
    goto yy100;
yy550:
    yych = *++YYCURSOR;
    if (yych == 'r') goto yy662;
    goto yy100;
yy551:
    yych = *++YYCURSOR;
    if (yych == 'f') goto yy664;
    goto yy100;
yy552:
    yych = *++YYCURSOR;
    if (yych == 'e') goto yy666;
    goto yy100;
yy553:
    yych = *++YYCURSOR;
    if (yych == 'u') goto yy667;
    goto yy100;
yy554:
    ++YYCURSOR;
#line 334 "cpp.re"
    { BOOST_WAVE_RET(T_PP_LINE); }
#line 3927 "cpp_re.inc"
yy556:
    yych = *++YYCURSOR;
    if (yych == 'm') goto yy668;
    goto yy100;
yy557:
    yych = *++YYCURSOR;
    if (yych == 'o') goto yy669;
    goto yy100;
yy558:
    yych = *++YYCURSOR;
    if (yych == 'f') goto yy670;
    goto yy100;
yy559:
    yych = *++YYCURSOR;
    if (yych == 'i') goto yy672;
    goto yy100;
yy560:
    yyaccept = 35;
    YYMARKER = ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 2) {
        goto yy131;
    }
    if (yych <= 0x1F) goto yy278;
    if (yych <= '\'') goto yy277;
    if (yych <= '?') goto yy133;
    goto yy134;
yy561:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy673;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy673;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy673;
        goto yy100;
    }
yy562:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy283;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy283;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy283;
        goto yy100;
    }
yy563:
    yych = *++YYCURSOR;
    if (yych == '!') goto yy674;
    goto yy100;
yy564:
    yych = *++YYCURSOR;
    if (yych == '=') goto yy676;
    goto yy100;
yy565:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy184;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy184;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy184;
        goto yy100;
    }
yy566:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy18;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy18;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy18;
        goto yy100;
    }
yy567:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy678;
    goto yy19;
yy568:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy679;
    goto yy19;
yy569:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    switch (yych) {
    case '1':    goto yy680;
    case '3':    goto yy681;
    case '6':    goto yy682;
    case '8':    goto yy683;
    default:    goto yy19;
    }
yy570:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'v') goto yy685;
    goto yy19;
yy571:
    yyaccept = 36;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy572:
#line 140 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_TRY : T_IDENTIFIER); }
#line 4056 "cpp_re.inc"
yy573:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy686;
    goto yy19;
yy574:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy688;
    goto yy19;
yy575:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy690;
    goto yy19;
yy576:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy691;
    goto yy19;
yy577:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy692;
    goto yy19;
yy578:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy693;
    goto yy19;
yy579:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy694;
    if (yych == 'o') goto yy695;
    goto yy19;
yy580:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'q') goto yy696;
    goto yy19;
yy581:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy698;
    goto yy19;
yy582:
    yyaccept = 37;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy583:
#line 205 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_c99_mode ? T_IDENTIFIER : T_OR_ALT); }
#line 4114 "cpp_re.inc"
yy584:
    yyaccept = 38;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy585:
#line 53 "cpp.re"
    { BOOST_WAVE_RET(T_BREAK); }
#line 4126 "cpp_re.inc"
yy586:
    yyaccept = 39;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy587:
#line 55 "cpp.re"
    { BOOST_WAVE_RET(T_CATCH); }
#line 4138 "cpp_re.inc"
yy588:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '6') goto yy700;
    goto yy19;
yy589:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '2') goto yy701;
    goto yy19;
yy590:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '_') goto yy702;
    goto yy19;
yy591:
    yyaccept = 40;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy592:
#line 60 "cpp.re"
    { BOOST_WAVE_RET(T_CLASS); }
#line 4165 "cpp_re.inc"
yy593:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy703;
    goto yy19;
yy594:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy704;
    goto yy19;
yy595:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy705;
    goto yy19;
yy596:
    yyaccept = 41;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy597:
#line 209 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_c99_mode ? T_IDENTIFIER : T_COMPL_ALT); }
#line 4192 "cpp_re.inc"
yy598:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy706;
    goto yy19;
yy599:
    yyaccept = 42;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '[') {
        if (yych <= '9') {
            if (yych == '$') goto yy18;
            if (yych >= '0') goto yy18;
        } else {
            if (yych <= '?') {
                if (yych >= '?') goto yy119;
            } else {
                if (yych <= '@') goto yy600;
                if (yych <= 'Z') goto yy18;
            }
        }
    } else {
        if (yych <= 'd') {
            if (yych <= '^') {
                if (yych <= '\\') goto yy120;
            } else {
                if (yych <= '_') goto yy707;
                if (yych >= 'a') goto yy18;
            }
        } else {
            if (yych <= 'h') {
                if (yych <= 'e') goto yy708;
                goto yy18;
            } else {
                if (yych <= 'i') goto yy709;
                if (yych <= 'z') goto yy18;
            }
        }
    }
yy600:
#line 62 "cpp.re"
    { BOOST_WAVE_RET(T_CONST); }
#line 4234 "cpp_re.inc"
yy601:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy710;
    goto yy19;
yy602:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'y') goto yy711;
    goto yy19;
yy603:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy712;
    goto yy19;
yy604:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy713;
    goto yy19;
yy605:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy715;
    goto yy19;
yy606:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy717;
    goto yy19;
yy607:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy718;
    goto yy19;
yy608:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy719;
    goto yy19;
yy609:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy721;
    goto yy19;
yy610:
    yyaccept = 43;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy611:
#line 82 "cpp.re"
    { BOOST_WAVE_RET(T_FALSE); }
#line 4291 "cpp_re.inc"
yy612:
    yyaccept = 44;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy613:
#line 83 "cpp.re"
    { BOOST_WAVE_RET(T_FLOAT); }
#line 4303 "cpp_re.inc"
yy614:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy723;
    goto yy19;
yy615:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy725;
    goto yy19;
yy616:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy727;
    goto yy19;
yy617:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy729;
    goto yy19;
yy618:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy730;
    goto yy19;
yy619:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy731;
    goto yy19;
yy620:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'q') goto yy732;
    goto yy19;
yy621:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy734;
    goto yy19;
yy622:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy735;
    goto yy19;
yy623:
    yyaccept = 45;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy624:
#line 226 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_c99_mode ? T_IDENTIFIER : T_ORASSIGN_ALT); }
#line 4360 "cpp_re.inc"
yy625:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy736;
    goto yy19;
yy626:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy737;
    goto yy19;
yy627:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy738;
    goto yy19;
yy628:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy740;
    goto yy19;
yy629:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy741;
    goto yy19;
yy630:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy742;
    goto yy19;
yy631:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy743;
    goto yy19;
yy632:
    yyaccept = 46;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy633:
#line 105 "cpp.re"
    { BOOST_WAVE_RET(T_SHORT); }
#line 4407 "cpp_re.inc"
yy634:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy745;
    goto yy19;
yy635:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'f') goto yy747;
    goto yy19;
yy636:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy749;
    goto yy19;
yy637:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy751;
    goto yy19;
yy638:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'h') goto yy753;
    goto yy19;
yy639:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy755;
    goto yy19;
yy640:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy756;
    goto yy19;
yy641:
    yyaccept = 47;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy642:
#line 116 "cpp.re"
    { BOOST_WAVE_RET(T_THROW); }
#line 4454 "cpp_re.inc"
yy643:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy757;
    goto yy19;
yy644:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy758;
    goto yy19;
yy645:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy760;
    goto yy19;
yy646:
    yyaccept = 48;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy647:
#line 122 "cpp.re"
    { BOOST_WAVE_RET(T_UNION); }
#line 4481 "cpp_re.inc"
yy648:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy761;
    goto yy19;
yy649:
    yyaccept = 49;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy650:
#line 124 "cpp.re"
    { BOOST_WAVE_RET(T_USING); }
#line 4498 "cpp_re.inc"
yy651:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy762;
    goto yy19;
yy652:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy763;
    goto yy19;
yy653:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '_') goto yy764;
    goto yy19;
yy654:
    yyaccept = 50;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy655:
#line 129 "cpp.re"
    { BOOST_WAVE_RET(T_WHILE); }
#line 4525 "cpp_re.inc"
yy656:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'q') goto yy765;
    goto yy19;
yy657:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy258;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy258;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy258;
        goto yy100;
    }
yy658:
    yych = *++YYCURSOR;
    if (yych == 'e') goto yy767;
    goto yy100;
yy659:
    ++YYCURSOR;
#line 331 "cpp.re"
    { BOOST_WAVE_RET(T_PP_ENDIF); }
#line 4553 "cpp_re.inc"
yy661:
    yych = *++YYCURSOR;
    if (yych == 'g') goto yy769;
    goto yy100;
yy662:
    ++YYCURSOR;
#line 335 "cpp.re"
    { BOOST_WAVE_RET(T_PP_ERROR); }
#line 4562 "cpp_re.inc"
yy664:
    ++YYCURSOR;
#line 327 "cpp.re"
    { BOOST_WAVE_RET(T_PP_IFDEF); }
#line 4567 "cpp_re.inc"
yy666:
    yych = *++YYCURSOR;
    if (yych == 'f') goto yy770;
    goto yy100;
yy667:
    yych = *++YYCURSOR;
    if (yych == 'd') goto yy772;
    goto yy100;
yy668:
    yych = *++YYCURSOR;
    if (yych == 'a') goto yy773;
    goto yy100;
yy669:
    yych = *++YYCURSOR;
    if (yych == 'n') goto yy775;
    goto yy100;
yy670:
    ++YYCURSOR;
#line 333 "cpp.re"
    { BOOST_WAVE_RET(T_PP_UNDEF); }
#line 4588 "cpp_re.inc"
yy672:
    yych = *++YYCURSOR;
    if (yych == 'n') goto yy777;
    goto yy100;
yy673:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy100;
        if (yych <= '9') goto yy282;
        goto yy100;
    } else {
        if (yych <= 'F') goto yy282;
        if (yych <= '`') goto yy100;
        if (yych <= 'f') goto yy282;
        goto yy100;
    }
yy674:
    ++YYCURSOR;
#line 253 "cpp.re"
    { BOOST_WAVE_RET(T_OROR_TRIGRAPH); }
#line 4611 "cpp_re.inc"
yy676:
    ++YYCURSOR;
#line 165 "cpp.re"
    { BOOST_WAVE_RET(T_POUND_POUND_TRIGRAPH); }
#line 4616 "cpp_re.inc"
yy678:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy778;
    goto yy19;
yy679:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy779;
    goto yy19;
yy680:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '6') goto yy780;
    goto yy19;
yy681:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '2') goto yy782;
    goto yy19;
yy682:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '4') goto yy784;
    goto yy19;
yy683:
    yyaccept = 51;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy684:
#line 131 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_INT8 : T_IDENTIFIER); }
#line 4653 "cpp_re.inc"
yy685:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy786;
    goto yy19;
yy686:
    yyaccept = 52;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy687:
#line 135 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_BASED : T_IDENTIFIER); }
#line 4670 "cpp_re.inc"
yy688:
    yyaccept = 53;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy689:
#line 137 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_CDECL : T_IDENTIFIER); }
#line 4682 "cpp_re.inc"
yy690:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy788;
    goto yy19;
yy691:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy789;
    goto yy19;
yy692:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy790;
    goto yy19;
yy693:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy792;
    goto yy19;
yy694:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy793;
    goto yy19;
yy695:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'f') goto yy795;
    goto yy19;
yy696:
    yyaccept = 54;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy697:
#line 224 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_c99_mode ? T_IDENTIFIER : T_ANDASSIGN_ALT); }
#line 4724 "cpp_re.inc"
yy698:
    yyaccept = 55;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy699:
#line 203 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_c99_mode ? T_IDENTIFIER : T_AND_ALT); }
#line 4736 "cpp_re.inc"
yy700:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '_') goto yy797;
    goto yy19;
yy701:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '_') goto yy798;
    goto yy19;
yy702:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy799;
    goto yy19;
yy703:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy801;
    goto yy19;
yy704:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'u') goto yy802;
    goto yy19;
yy705:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy803;
    goto yy19;
yy706:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy804;
    goto yy19;
yy707:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy806;
    goto yy19;
yy708:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'v') goto yy807;
    if (yych == 'x') goto yy808;
    goto yy19;
yy709:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy809;
    goto yy19;
yy710:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'u') goto yy810;
    goto yy19;
yy711:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy811;
    goto yy19;
yy712:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy812;
    goto yy19;
yy713:
    yyaccept = 56;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy714:
#line 73 "cpp.re"
    { BOOST_WAVE_RET(T_DELETE); }
#line 4814 "cpp_re.inc"
yy715:
    yyaccept = 57;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy716:
#line 75 "cpp.re"
    { BOOST_WAVE_RET(T_DOUBLE); }
#line 4826 "cpp_re.inc"
yy717:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy814;
    goto yy19;
yy718:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy815;
    goto yy19;
yy719:
    yyaccept = 58;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy720:
#line 80 "cpp.re"
    { BOOST_WAVE_RET(T_EXPORT); }
#line 4848 "cpp_re.inc"
yy721:
    yyaccept = 59;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy722:
#line 81 "cpp.re"
    { BOOST_WAVE_RET(T_EXTERN); }
#line 4860 "cpp_re.inc"
yy723:
    yyaccept = 60;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy724:
#line 85 "cpp.re"
    { BOOST_WAVE_RET(T_FRIEND); }
#line 4872 "cpp_re.inc"
yy725:
    yyaccept = 61;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy726:
#line 88 "cpp.re"
    { BOOST_WAVE_RET(s->enable_import_keyword ? T_IMPORT : T_IDENTIFIER); }
#line 4884 "cpp_re.inc"
yy727:
    yyaccept = 62;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy728:
#line 89 "cpp.re"
    { BOOST_WAVE_RET(T_INLINE); }
#line 4896 "cpp_re.inc"
yy729:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy816;
    goto yy19;
yy730:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy818;
    goto yy19;
yy731:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy819;
    goto yy19;
yy732:
    yyaccept = 63;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy733:
#line 234 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_c99_mode ? T_IDENTIFIER : T_NOTEQUAL_ALT); }
#line 4923 "cpp_re.inc"
yy734:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy820;
    goto yy19;
yy735:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'o') goto yy822;
    goto yy19;
yy736:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy823;
    goto yy19;
yy737:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy825;
    goto yy19;
yy738:
    yyaccept = 64;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy739:
#line 100 "cpp.re"
    { BOOST_WAVE_RET(T_PUBLIC); }
#line 4955 "cpp_re.inc"
yy740:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy826;
    goto yy19;
yy741:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy827;
    goto yy19;
yy742:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy828;
    goto yy19;
yy743:
    yyaccept = 65;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy744:
#line 104 "cpp.re"
    { BOOST_WAVE_RET(T_RETURN); }
#line 4982 "cpp_re.inc"
yy745:
    yyaccept = 66;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy746:
#line 106 "cpp.re"
    { BOOST_WAVE_RET(T_SIGNED); }
#line 4994 "cpp_re.inc"
yy747:
    yyaccept = 67;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy748:
#line 107 "cpp.re"
    { BOOST_WAVE_RET(T_SIZEOF); }
#line 5006 "cpp_re.inc"
yy749:
    yyaccept = 68;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '@') {
        if (yych <= '/') {
            if (yych == '$') goto yy18;
        } else {
            if (yych <= '9') goto yy18;
            if (yych == '?') goto yy119;
        }
    } else {
        if (yych <= '^') {
            if (yych <= 'Z') goto yy18;
            if (yych == '\\') goto yy120;
        } else {
            if (yych <= '_') goto yy829;
            if (yych <= '`') goto yy750;
            if (yych <= 'z') goto yy18;
        }
    }
yy750:
#line 108 "cpp.re"
    { BOOST_WAVE_RET(T_STATIC); }
#line 5030 "cpp_re.inc"
yy751:
    yyaccept = 69;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy752:
#line 111 "cpp.re"
    { BOOST_WAVE_RET(T_STRUCT); }
#line 5042 "cpp_re.inc"
yy753:
    yyaccept = 70;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy754:
#line 112 "cpp.re"
    { BOOST_WAVE_RET(T_SWITCH); }
#line 5054 "cpp_re.inc"
yy755:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy830;
    goto yy19;
yy756:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '_') goto yy831;
    goto yy19;
yy757:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'f') goto yy832;
    goto yy19;
yy758:
    yyaccept = 71;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy759:
#line 120 "cpp.re"
    { BOOST_WAVE_RET(T_TYPEID); }
#line 5081 "cpp_re.inc"
yy760:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'm') goto yy834;
    goto yy19;
yy761:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy835;
    goto yy19;
yy762:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy836;
    goto yy19;
yy763:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy838;
    goto yy19;
yy764:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy839;
    goto yy19;
yy765:
    yyaccept = 72;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy766:
#line 221 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_c99_mode ? T_IDENTIFIER : T_XORASSIGN_ALT); }
#line 5118 "cpp_re.inc"
yy767:
    ++YYCURSOR;
#line 332 "cpp.re"
    { BOOST_WAVE_RET(T_PP_DEFINE); }
#line 5123 "cpp_re.inc"
yy769:
    yych = *++YYCURSOR;
    if (yych == 'i') goto yy841;
    goto yy100;
yy770:
    ++YYCURSOR;
#line 328 "cpp.re"
    { BOOST_WAVE_RET(T_PP_IFNDEF); }
#line 5132 "cpp_re.inc"
yy772:
    yych = *++YYCURSOR;
    if (yych == 'e') goto yy842;
    goto yy100;
yy773:
    ++YYCURSOR;
#line 336 "cpp.re"
    { BOOST_WAVE_RET(T_PP_PRAGMA); }
#line 5141 "cpp_re.inc"
yy775:
    ++YYCURSOR;
#line 340 "cpp.re"
    { BOOST_WAVE_RET(T_MSEXT_PP_REGION); }
#line 5146 "cpp_re.inc"
yy777:
    yych = *++YYCURSOR;
    if (yych == 'g') goto yy844;
    goto yy100;
yy778:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy846;
    goto yy19;
yy779:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy848;
    goto yy19;
yy780:
    yyaccept = 73;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy781:
#line 132 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_INT16 : T_IDENTIFIER); }
#line 5172 "cpp_re.inc"
yy782:
    yyaccept = 74;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy783:
#line 133 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_INT32 : T_IDENTIFIER); }
#line 5184 "cpp_re.inc"
yy784:
    yyaccept = 75;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy785:
#line 134 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_INT64 : T_IDENTIFIER); }
#line 5196 "cpp_re.inc"
yy786:
    yyaccept = 76;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy787:
#line 143 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_LEAVE : T_IDENTIFIER); }
#line 5208 "cpp_re.inc"
yy788:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy849;
    goto yy19;
yy789:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy850;
    goto yy19;
yy790:
    yyaccept = 77;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy791:
#line 144 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_INLINE : T_IDENTIFIER); }
#line 5230 "cpp_re.inc"
yy792:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy851;
    goto yy19;
yy793:
    yyaccept = 78;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy794:
#line 48 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp0x_mode ? T_ALIGNAS : T_IDENTIFIER); }
#line 5247 "cpp_re.inc"
yy795:
    yyaccept = 79;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy796:
#line 49 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp0x_mode ? T_ALIGNOF : T_IDENTIFIER); }
#line 5259 "cpp_re.inc"
yy797:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy853;
    goto yy19;
yy798:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy855;
    goto yy19;
yy799:
    yyaccept = 80;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy800:
#line 57 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp2a_mode ? T_CHAR8_T : T_IDENTIFIER); }
#line 5281 "cpp_re.inc"
yy801:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy857;
    goto yy19;
yy802:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy859;
    goto yy19;
yy803:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy860;
    goto yy19;
yy804:
    yyaccept = 81;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy805:
#line 61 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp2a_mode ? T_CONCEPT : T_IDENTIFIER); }
#line 5308 "cpp_re.inc"
yy806:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy862;
    goto yy19;
yy807:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy863;
    goto yy19;
yy808:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy864;
    goto yy19;
yy809:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'i') goto yy865;
    goto yy19;
yy810:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy866;
    goto yy19;
yy811:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy868;
    goto yy19;
yy812:
    yyaccept = 82;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy813:
#line 72 "cpp.re"
    { BOOST_WAVE_RET(T_DEFAULT); }
#line 5350 "cpp_re.inc"
yy814:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '_') goto yy870;
    goto yy19;
yy815:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy871;
    goto yy19;
yy816:
    yyaccept = 83;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy817:
#line 92 "cpp.re"
    { BOOST_WAVE_RET(T_MUTABLE); }
#line 5372 "cpp_re.inc"
yy818:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy873;
    goto yy19;
yy819:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy874;
    goto yy19;
yy820:
    yyaccept = 84;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy821:
#line 96 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp0x_mode ? T_NULLPTR : T_IDENTIFIER); }
#line 5394 "cpp_re.inc"
yy822:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy876;
    goto yy19;
yy823:
    yyaccept = 85;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy824:
#line 98 "cpp.re"
    { BOOST_WAVE_RET(T_PRIVATE); }
#line 5411 "cpp_re.inc"
yy825:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy878;
    goto yy19;
yy826:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy879;
    goto yy19;
yy827:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'p') goto yy881;
    goto yy19;
yy828:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy882;
    goto yy19;
yy829:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy884;
    if (yych == 'c') goto yy885;
    goto yy19;
yy830:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy886;
    goto yy19;
yy831:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy888;
    goto yy19;
yy832:
    yyaccept = 86;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy833:
#line 119 "cpp.re"
    { BOOST_WAVE_RET(T_TYPEDEF); }
#line 5459 "cpp_re.inc"
yy834:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy889;
    goto yy19;
yy835:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy891;
    goto yy19;
yy836:
    yyaccept = 87;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy837:
#line 125 "cpp.re"
    { BOOST_WAVE_RET(T_VIRTUAL); }
#line 5481 "cpp_re.inc"
yy838:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy893;
    goto yy19;
yy839:
    yyaccept = 88;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy840:
#line 128 "cpp.re"
    { BOOST_WAVE_RET(T_WCHART); }
#line 5498 "cpp_re.inc"
yy841:
    yych = *++YYCURSOR;
    if (yych == 'o') goto yy895;
    goto yy100;
yy842:
    yyaccept = 89;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '_') goto yy901;
    goto yy897;
yy843:
#line 324 "cpp.re"
    { BOOST_WAVE_RET(T_PP_INCLUDE); }
#line 5511 "cpp_re.inc"
yy844:
    ++YYCURSOR;
#line 338 "cpp.re"
    { BOOST_WAVE_RET(T_PP_WARNING); }
#line 5516 "cpp_re.inc"
yy846:
    yyaccept = 90;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy847:
#line 141 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_EXCEPT : T_IDENTIFIER); }
#line 5528 "cpp_re.inc"
yy848:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'y') goto yy902;
    goto yy19;
yy849:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy904;
    goto yy19;
yy850:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy906;
    goto yy19;
yy851:
    yyaccept = 91;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy852:
#line 139 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_STDCALL : T_IDENTIFIER); }
#line 5555 "cpp_re.inc"
yy853:
    yyaccept = 92;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy854:
#line 58 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp0x_mode ? T_CHAR16_T : T_IDENTIFIER); }
#line 5567 "cpp_re.inc"
yy855:
    yyaccept = 93;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy856:
#line 59 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp0x_mode ? T_CHAR32_T : T_IDENTIFIER); }
#line 5579 "cpp_re.inc"
yy857:
    yyaccept = 94;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy858:
#line 68 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp2a_mode ? T_CO_AWAIT : T_IDENTIFIER); }
#line 5591 "cpp_re.inc"
yy859:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'n') goto yy908;
    goto yy19;
yy860:
    yyaccept = 95;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy861:
#line 70 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp2a_mode ? T_CO_YIELD : T_IDENTIFIER); }
#line 5608 "cpp_re.inc"
yy862:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy910;
    goto yy19;
yy863:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy911;
    goto yy19;
yy864:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy913;
    goto yy19;
yy865:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy915;
    goto yy19;
yy866:
    yyaccept = 96;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy867:
#line 67 "cpp.re"
    { BOOST_WAVE_RET(T_CONTINUE); }
#line 5640 "cpp_re.inc"
yy868:
    yyaccept = 97;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy869:
#line 71 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp0x_mode ? T_DECLTYPE : T_IDENTIFIER); }
#line 5652 "cpp_re.inc"
yy870:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy917;
    goto yy19;
yy871:
    yyaccept = 98;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy872:
#line 79 "cpp.re"
    { BOOST_WAVE_RET(T_EXPLICIT); }
#line 5669 "cpp_re.inc"
yy873:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy918;
    goto yy19;
yy874:
    yyaccept = 99;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy875:
#line 95 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp0x_mode ? T_NOEXCEPT : T_IDENTIFIER); }
#line 5686 "cpp_re.inc"
yy876:
    yyaccept = 100;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy877:
#line 97 "cpp.re"
    { BOOST_WAVE_RET(T_OPERATOR); }
#line 5698 "cpp_re.inc"
yy878:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'd') goto yy920;
    goto yy19;
yy879:
    yyaccept = 101;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy880:
#line 101 "cpp.re"
    { BOOST_WAVE_RET(T_REGISTER); }
#line 5715 "cpp_re.inc"
yy881:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy922;
    goto yy19;
yy882:
    yyaccept = 102;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy883:
#line 103 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp2a_mode ? T_REQUIRES : T_IDENTIFIER); }
#line 5732 "cpp_re.inc"
yy884:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy923;
    goto yy19;
yy885:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy924;
    goto yy19;
yy886:
    yyaccept = 103;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy887:
#line 113 "cpp.re"
    { BOOST_WAVE_RET(T_TEMPLATE); }
#line 5754 "cpp_re.inc"
yy888:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'o') goto yy925;
    goto yy19;
yy889:
    yyaccept = 104;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy890:
#line 121 "cpp.re"
    { BOOST_WAVE_RET(T_TYPENAME); }
#line 5771 "cpp_re.inc"
yy891:
    yyaccept = 105;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy892:
#line 123 "cpp.re"
    { BOOST_WAVE_RET(T_UNSIGNED); }
#line 5783 "cpp_re.inc"
yy893:
    yyaccept = 106;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy894:
#line 127 "cpp.re"
    { BOOST_WAVE_RET(T_VOLATILE); }
#line 5795 "cpp_re.inc"
yy895:
    yych = *++YYCURSOR;
    if (yych == 'n') goto yy926;
    goto yy100;
yy896:
    yyaccept = 89;
    YYMARKER = ++YYCURSOR;
    if ((YYLIMIT - YYCURSOR) < 2) YYFILL(2);
    yych = *YYCURSOR;
yy897:
    if (yych <= ' ') {
        if (yych <= '\n') {
            if (yych == '\t') goto yy896;
            goto yy843;
        } else {
            if (yych <= '\f') goto yy896;
            if (yych <= 0x1F) goto yy843;
            goto yy896;
        }
    } else {
        if (yych <= '.') {
            if (yych != '"') goto yy843;
        } else {
            if (yych <= '/') goto yy899;
            if (yych == '<') goto yy900;
            goto yy843;
        }
    }
    yych = *++YYCURSOR;
    if (yych == '"') goto yy100;
    goto yy929;
yy899:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych == '*') goto yy930;
    goto yy100;
yy900:
    yych = *++YYCURSOR;
    if (yych == '>') goto yy100;
    goto yy933;
yy901:
    yych = *++YYCURSOR;
    if (yych == 'n') goto yy934;
    goto yy100;
yy902:
    yyaccept = 107;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy903:
#line 142 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_FINALLY : T_IDENTIFIER); }
#line 5852 "cpp_re.inc"
yy904:
    yyaccept = 108;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy905:
#line 136 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_DECLSPEC : T_IDENTIFIER); }
#line 5864 "cpp_re.inc"
yy906:
    yyaccept = 109;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy907:
#line 138 "cpp.re"
    { BOOST_WAVE_RET(s->enable_ms_extensions ? T_MSEXT_FASTCALL : T_IDENTIFIER); }
#line 5876 "cpp_re.inc"
yy908:
    yyaccept = 110;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy909:
#line 69 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp2a_mode ? T_CO_RETURN : T_IDENTIFIER); }
#line 5888 "cpp_re.inc"
yy910:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy935;
    goto yy19;
yy911:
    yyaccept = 111;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy912:
#line 63 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp2a_mode ? T_CONSTEVAL : T_IDENTIFIER); }
#line 5905 "cpp_re.inc"
yy913:
    yyaccept = 112;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy914:
#line 64 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp0x_mode ? T_CONSTEXPR : T_IDENTIFIER); }
#line 5917 "cpp_re.inc"
yy915:
    yyaccept = 113;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy916:
#line 65 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp2a_mode ? T_CONSTINIT : T_IDENTIFIER); }
#line 5929 "cpp_re.inc"
yy917:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy937;
    goto yy19;
yy918:
    yyaccept = 114;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy919:
#line 93 "cpp.re"
    { BOOST_WAVE_RET(T_NAMESPACE); }
#line 5946 "cpp_re.inc"
yy920:
    yyaccept = 115;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy921:
#line 99 "cpp.re"
    { BOOST_WAVE_RET(T_PROTECTED); }
#line 5958 "cpp_re.inc"
yy922:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy938;
    goto yy19;
yy923:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy939;
    goto yy19;
yy924:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy940;
    goto yy19;
yy925:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy941;
    goto yy19;
yy926:
    ++YYCURSOR;
#line 341 "cpp.re"
    { BOOST_WAVE_RET(T_MSEXT_PP_ENDREGION); }
#line 5983 "cpp_re.inc"
yy928:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
yy929:
    if (yybm[0+yych] & 64) {
        goto yy928;
    }
    if (yych <= 0x1F) goto yy100;
    goto yy942;
yy930:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= 0x1F) {
        if (yych <= 0x08) goto yy100;
        if (yych <= '\r') goto yy930;
        goto yy100;
    } else {
        if (yych == '*') goto yy944;
        goto yy930;
    }
yy932:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
yy933:
    if (yybm[0+yych] & 128) {
        goto yy932;
    }
    if (yych <= 0x1F) goto yy100;
    goto yy946;
yy934:
    yych = *++YYCURSOR;
    if (yych == 'e') goto yy948;
    goto yy100;
yy935:
    yyaccept = 116;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy936:
#line 66 "cpp.re"
    { BOOST_WAVE_RET(T_CONSTCAST); }
#line 6031 "cpp_re.inc"
yy937:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 's') goto yy949;
    goto yy19;
yy938:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy950;
    goto yy19;
yy939:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'e') goto yy951;
    goto yy19;
yy940:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy952;
    goto yy19;
yy941:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy954;
    goto yy19;
yy942:
    ++YYCURSOR;
#line 321 "cpp.re"
    { BOOST_WAVE_RET(T_PP_QHEADER); }
#line 6061 "cpp_re.inc"
yy944:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= ')') {
        if (yych <= 0x08) goto yy100;
        if (yych <= '\r') goto yy930;
        if (yych <= 0x1F) goto yy100;
        goto yy930;
    } else {
        if (yych <= '*') goto yy944;
        if (yych == '/') goto yy896;
        goto yy930;
    }
yy946:
    ++YYCURSOR;
#line 318 "cpp.re"
    { BOOST_WAVE_RET(T_PP_HHEADER); }
#line 6080 "cpp_re.inc"
yy948:
    yych = *++YYCURSOR;
    if (yych == 'x') goto yy955;
    goto yy100;
yy949:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy956;
    goto yy19;
yy950:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '_') goto yy958;
    goto yy19;
yy951:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'r') goto yy959;
    goto yy19;
yy952:
    yyaccept = 117;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy953:
#line 109 "cpp.re"
    { BOOST_WAVE_RET(T_STATICCAST); }
#line 6111 "cpp_re.inc"
yy954:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'l') goto yy960;
    goto yy19;
yy955:
    yych = *++YYCURSOR;
    if (yych == 't') goto yy896;
    goto yy100;
yy956:
    yyaccept = 118;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy957:
#line 76 "cpp.re"
    { BOOST_WAVE_RET(T_DYNAMICCAST); }
#line 6132 "cpp_re.inc"
yy958:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'c') goto yy962;
    goto yy19;
yy959:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 't') goto yy963;
    goto yy19;
yy960:
    yyaccept = 119;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy961:
#line 115 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp0x_mode ? T_THREADLOCAL : T_IDENTIFIER); }
#line 6154 "cpp_re.inc"
yy962:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'a') goto yy965;
    goto yy19;
yy963:
    yyaccept = 120;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy964:
#line 110 "cpp.re"
    { BOOST_WAVE_RET(s->act_in_cpp0x_mode ? T_STATICASSERT : T_IDENTIFIER); }
#line 6171 "cpp_re.inc"
yy965:
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych != 's') goto yy19;
    yyaccept = 2;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych != 't') goto yy19;
    yyaccept = 121;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[256+yych] & 128) {
        goto yy18;
    }
    if (yych == '?') goto yy119;
    if (yych == '\\') goto yy120;
yy968:
#line 102 "cpp.re"
    { BOOST_WAVE_RET(T_REINTERPRETCAST); }
#line 6189 "cpp_re.inc"
}
#line 373 "cpp.re"


ccomment:

#line 6196 "cpp_re.inc"
{
    YYCTYPE yych;
    if ((YYLIMIT - YYCURSOR) < 2) YYFILL(2);
    yych = *YYCURSOR;
    if (yych <= '\f') {
        if (yych <= 0x08) {
            if (yych >= 0x01) goto yy973;
        } else {
            if (yych == '\n') goto yy977;
            goto yy975;
        }
    } else {
        if (yych <= 0x1F) {
            if (yych <= '\r') goto yy979;
            goto yy973;
        } else {
            if (yych == '*') goto yy980;
            goto yy975;
        }
    }
    ++YYCURSOR;
#line 391 "cpp.re"
    {
        if(cursor == s->eof)
        {
            BOOST_WAVE_UPDATE_CURSOR();   // adjust the input cursor
            (*s->error_proc)(s, lexing_exception::generic_lexing_warning,
                "Unterminated 'C' style comment");
        }
        else
        {
            --YYCURSOR;                   // next call returns T_EOF
            BOOST_WAVE_UPDATE_CURSOR();   // adjust the input cursor
            (*s->error_proc)(s, lexing_exception::generic_lexing_error,
                "invalid character: '\\000' in input stream");
        }
    }
#line 6234 "cpp_re.inc"
yy973:
    ++YYCURSOR;
#line 408 "cpp.re"
    {
        // flag the error
        BOOST_WAVE_UPDATE_CURSOR();     // adjust the input cursor
        (*s->error_proc)(s, lexing_exception::generic_lexing_error,
            "invalid character '\\%03o' in input stream", *--YYCURSOR);
    }
#line 6244 "cpp_re.inc"
yy975:
    ++YYCURSOR;
yy976:
#line 388 "cpp.re"
    { goto ccomment; }
#line 6250 "cpp_re.inc"
yy977:
    ++YYCURSOR;
yy978:
#line 380 "cpp.re"
    {
        /*if(cursor == s->eof) BOOST_WAVE_RET(T_EOF);*/
        /*s->tok = cursor; */
        s->line += count_backslash_newlines(s, cursor) +1;
        cursor.column = 1;
        goto ccomment;
    }
#line 6262 "cpp_re.inc"
yy979:
    yych = *++YYCURSOR;
    if (yych == '\n') goto yy977;
    goto yy978;
yy980:
    yych = *++YYCURSOR;
    if (yych != '/') goto yy976;
    ++YYCURSOR;
#line 377 "cpp.re"
    { BOOST_WAVE_RET(T_CCOMMENT); }
#line 6273 "cpp_re.inc"
}
#line 414 "cpp.re"


cppcomment:

#line 6280 "cpp_re.inc"
{
    YYCTYPE yych;
    if ((YYLIMIT - YYCURSOR) < 2) YYFILL(2);
    yych = *YYCURSOR;
    if (yych <= '\n') {
        if (yych <= 0x00) goto yy985;
        if (yych <= 0x08) goto yy987;
        if (yych <= '\t') goto yy989;
        goto yy991;
    } else {
        if (yych <= '\f') goto yy989;
        if (yych <= '\r') goto yy993;
        if (yych <= 0x1F) goto yy987;
        goto yy989;
    }
yy985:
    ++YYCURSOR;
#line 430 "cpp.re"
    {
        if (s->eof && cursor != s->eof)
        {
            --YYCURSOR;                     // next call returns T_EOF
            BOOST_WAVE_UPDATE_CURSOR();     // adjust the input cursor
            (*s->error_proc)(s, lexing_exception::generic_lexing_error,
                "invalid character '\\000' in input stream");
        }

        --YYCURSOR;                         // next call returns T_EOF
        if (!s->single_line_only)
        {
            BOOST_WAVE_UPDATE_CURSOR();     // adjust the input cursor
            (*s->error_proc)(s, lexing_exception::generic_lexing_warning,
                "Unterminated 'C++' style comment");
        }
        BOOST_WAVE_RET(T_CPPCOMMENT);
    }
#line 6317 "cpp_re.inc"
yy987:
    ++YYCURSOR;
#line 450 "cpp.re"
    {
        // flag the error
        BOOST_WAVE_UPDATE_CURSOR();     // adjust the input cursor
        (*s->error_proc)(s, lexing_exception::generic_lexing_error,
            "invalid character '\\%03o' in input stream", *--YYCURSOR);
    }
#line 6327 "cpp_re.inc"
yy989:
    ++YYCURSOR;
#line 427 "cpp.re"
    { goto cppcomment; }
#line 6332 "cpp_re.inc"
yy991:
    ++YYCURSOR;
yy992:
#line 419 "cpp.re"
    {
        /*if(cursor == s->eof) BOOST_WAVE_RET(T_EOF); */
        /*s->tok = cursor; */
        s->line++;
        cursor.column = 1;
        BOOST_WAVE_RET(T_CPPCOMMENT);
    }
#line 6344 "cpp_re.inc"
yy993:
    yych = *++YYCURSOR;
    if (yych == '\n') goto yy991;
    goto yy992;
}
#line 456 "cpp.re"


/* this subscanner is called whenever a pp_number has been started */
pp_number:
{
    cursor = uchar_wrapper(s->tok = s->cur, s->column = s->curr_column);
    marker = uchar_wrapper(s->ptr);
    limit = uchar_wrapper(s->lim);

    if (s->detect_pp_numbers) {
    
#line 6362 "cpp_re.inc"
{
    YYCTYPE yych;
    static const unsigned char yybm[] = {
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,  64,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,  64,   0, 
         64,  64,  64,  64,  64,  64,  64,  64, 
         64,  64,   0,   0,   0,   0,   0,   0, 
          0,  64,  64,  64,  64, 128,  64,  64, 
         64,  64,  64,  64,  64,  64,  64,  64, 
         64,  64,  64,  64,  64,  64,  64,  64, 
         64,  64,  64,   0,   0,   0,   0,  64, 
          0,  64,  64,  64,  64, 128,  64,  64, 
         64,  64,  64,  64,  64,  64,  64,  64, 
         64,  64,  64,  64,  64,  64,  64,  64, 
         64,  64,  64,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
    };
    if ((YYLIMIT - YYCURSOR) < 2) YYFILL(2);
    yych = *YYCURSOR;
    if (yych == '.') goto yy998;
    if (yych <= '/') goto yy996;
    if (yych <= '9') goto yy999;
yy996:
    ++YYCURSOR;
yy997:
#line 472 "cpp.re"
    { BOOST_ASSERT(false); }
#line 6409 "cpp_re.inc"
yy998:
    yych = *++YYCURSOR;
    if (yych <= '/') goto yy997;
    if (yych >= ':') goto yy997;
yy999:
    YYMARKER = ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 64) {
        goto yy999;
    }
    if (yych <= 'E') {
        if (yych == '?') goto yy1002;
        if (yych >= 'A') goto yy1004;
    } else {
        if (yych <= '\\') {
            if (yych >= '\\') goto yy1006;
        } else {
            if (yych <= '`') goto yy1001;
            if (yych <= 'e') goto yy1004;
        }
    }
yy1001:
#line 468 "cpp.re"
    { BOOST_WAVE_RET(T_PP_NUMBER); }
#line 6435 "cpp_re.inc"
yy1002:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych == '?') goto yy1007;
yy1003:
    YYCURSOR = YYMARKER;
    goto yy1001;
yy1004:
    YYMARKER = ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 128) {
        goto yy1004;
    }
    if (yych <= '>') {
        if (yych <= '+') {
            if (yych == '$') goto yy999;
            if (yych <= '*') goto yy1001;
            goto yy999;
        } else {
            if (yych <= '.') {
                if (yych <= ',') goto yy1001;
                goto yy999;
            } else {
                if (yych <= '/') goto yy1001;
                if (yych <= '9') goto yy999;
                goto yy1001;
            }
        }
    } else {
        if (yych <= '\\') {
            if (yych <= '@') {
                if (yych <= '?') goto yy1002;
                goto yy1001;
            } else {
                if (yych <= 'Z') goto yy999;
                if (yych <= '[') goto yy1001;
            }
        } else {
            if (yych <= '_') {
                if (yych <= '^') goto yy1001;
                goto yy999;
            } else {
                if (yych <= '`') goto yy1001;
                if (yych <= 'z') goto yy999;
                goto yy1001;
            }
        }
    }
yy1006:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych == 'U') goto yy1008;
    if (yych == 'u') goto yy1009;
    goto yy1003;
yy1007:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych == '/') goto yy1006;
    goto yy1003;
yy1008:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1003;
        if (yych <= '9') goto yy1010;
        goto yy1003;
    } else {
        if (yych <= 'F') goto yy1010;
        if (yych <= '`') goto yy1003;
        if (yych <= 'f') goto yy1010;
        goto yy1003;
    }
yy1009:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1003;
        if (yych <= '9') goto yy1011;
        goto yy1003;
    } else {
        if (yych <= 'F') goto yy1011;
        if (yych <= '`') goto yy1003;
        if (yych <= 'f') goto yy1011;
        goto yy1003;
    }
yy1010:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1003;
        if (yych <= '9') goto yy1012;
        goto yy1003;
    } else {
        if (yych <= 'F') goto yy1012;
        if (yych <= '`') goto yy1003;
        if (yych <= 'f') goto yy1012;
        goto yy1003;
    }
yy1011:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1003;
        if (yych <= '9') goto yy1013;
        goto yy1003;
    } else {
        if (yych <= 'F') goto yy1013;
        if (yych <= '`') goto yy1003;
        if (yych <= 'f') goto yy1013;
        goto yy1003;
    }
yy1012:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1003;
        if (yych <= '9') goto yy1014;
        goto yy1003;
    } else {
        if (yych <= 'F') goto yy1014;
        if (yych <= '`') goto yy1003;
        if (yych <= 'f') goto yy1014;
        goto yy1003;
    }
yy1013:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1003;
        if (yych <= '9') goto yy1015;
        goto yy1003;
    } else {
        if (yych <= 'F') goto yy1015;
        if (yych <= '`') goto yy1003;
        if (yych <= 'f') goto yy1015;
        goto yy1003;
    }
yy1014:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1003;
        if (yych <= '9') goto yy1009;
        goto yy1003;
    } else {
        if (yych <= 'F') goto yy1009;
        if (yych <= '`') goto yy1003;
        if (yych <= 'f') goto yy1009;
        goto yy1003;
    }
yy1015:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1003;
        if (yych <= '9') goto yy999;
        goto yy1003;
    } else {
        if (yych <= 'F') goto yy999;
        if (yych <= '`') goto yy1003;
        if (yych <= 'f') goto yy999;
        goto yy1003;
    }
}
#line 473 "cpp.re"

    }
    else {
    
#line 6617 "cpp_re.inc"
{
    YYCTYPE yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
        224, 224, 224, 224, 224, 224, 224, 224, 
        160, 160,   0,   0,   0,   0,   0,   0, 
          0, 128, 128, 128, 128, 128, 128,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0, 128, 128, 128, 128, 128, 128,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
    };
    if ((YYLIMIT - YYCURSOR) < 4) YYFILL(4);
    yych = *YYCURSOR;
    if (yych <= '/') {
        if (yych == '.') goto yy1020;
    } else {
        if (yych <= '0') goto yy1021;
        if (yych <= '9') goto yy1023;
    }
    ++YYCURSOR;
yy1019:
#line 483 "cpp.re"
    { BOOST_ASSERT(false); }
#line 6667 "cpp_re.inc"
yy1020:
    yych = *++YYCURSOR;
    if (yych <= '/') goto yy1019;
    if (yych <= '9') goto yy1025;
    goto yy1019;
yy1021:
    yyaccept = 0;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == 'X') goto yy1034;
    if (yych == 'x') goto yy1034;
    goto yy1029;
yy1022:
#line 480 "cpp.re"
    { goto integer_suffix; }
#line 6682 "cpp_re.inc"
yy1023:
    yyaccept = 0;
    YYMARKER = ++YYCURSOR;
    if ((YYLIMIT - YYCURSOR) < 3) YYFILL(3);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 32) {
        goto yy1023;
    }
    if (yych <= 'D') {
        if (yych != '.') goto yy1022;
    } else {
        if (yych <= 'E') goto yy1033;
        if (yych == 'e') goto yy1033;
        goto yy1022;
    }
yy1025:
    yyaccept = 1;
    YYMARKER = ++YYCURSOR;
    if ((YYLIMIT - YYCURSOR) < 3) YYFILL(3);
    yych = *YYCURSOR;
    if (yych <= 'K') {
        if (yych <= 'D') {
            if (yych <= '/') goto yy1027;
            if (yych <= '9') goto yy1025;
        } else {
            if (yych <= 'E') goto yy1033;
            if (yych <= 'F') goto yy1035;
        }
    } else {
        if (yych <= 'e') {
            if (yych <= 'L') goto yy1036;
            if (yych >= 'e') goto yy1033;
        } else {
            if (yych <= 'f') goto yy1035;
            if (yych == 'l') goto yy1036;
        }
    }
yy1027:
#line 478 "cpp.re"
    { BOOST_WAVE_RET(T_FLOATLIT); }
#line 6723 "cpp_re.inc"
yy1028:
    yyaccept = 0;
    YYMARKER = ++YYCURSOR;
    if ((YYLIMIT - YYCURSOR) < 3) YYFILL(3);
    yych = *YYCURSOR;
yy1029:
    if (yybm[0+yych] & 64) {
        goto yy1028;
    }
    if (yych <= '9') {
        if (yych == '.') goto yy1025;
        if (yych <= '/') goto yy1022;
    } else {
        if (yych <= 'E') {
            if (yych <= 'D') goto yy1022;
            goto yy1033;
        } else {
            if (yych == 'e') goto yy1033;
            goto yy1022;
        }
    }
yy1030:
    ++YYCURSOR;
    if ((YYLIMIT - YYCURSOR) < 3) YYFILL(3);
    yych = *YYCURSOR;
    if (yych <= '9') {
        if (yych == '.') goto yy1025;
        if (yych >= '0') goto yy1030;
    } else {
        if (yych <= 'E') {
            if (yych >= 'E') goto yy1033;
        } else {
            if (yych == 'e') goto yy1033;
        }
    }
yy1032:
    YYCURSOR = YYMARKER;
    if (yyaccept == 0) {
        goto yy1022;
    } else {
        goto yy1027;
    }
yy1033:
    yych = *++YYCURSOR;
    if (yych <= ',') {
        if (yych == '+') goto yy1037;
        goto yy1032;
    } else {
        if (yych <= '-') goto yy1037;
        if (yych <= '/') goto yy1032;
        if (yych <= '9') goto yy1038;
        goto yy1032;
    }
yy1034:
    yych = *++YYCURSOR;
    if (yybm[0+yych] & 128) {
        goto yy1040;
    }
    goto yy1032;
yy1035:
    yych = *++YYCURSOR;
    if (yych == 'L') goto yy1042;
    if (yych == 'l') goto yy1042;
    goto yy1027;
yy1036:
    yych = *++YYCURSOR;
    if (yych == 'F') goto yy1042;
    if (yych == 'f') goto yy1042;
    goto yy1027;
yy1037:
    yych = *++YYCURSOR;
    if (yych <= '/') goto yy1032;
    if (yych >= ':') goto yy1032;
yy1038:
    ++YYCURSOR;
    if ((YYLIMIT - YYCURSOR) < 2) YYFILL(2);
    yych = *YYCURSOR;
    if (yych <= 'K') {
        if (yych <= '9') {
            if (yych <= '/') goto yy1027;
            goto yy1038;
        } else {
            if (yych == 'F') goto yy1035;
            goto yy1027;
        }
    } else {
        if (yych <= 'f') {
            if (yych <= 'L') goto yy1036;
            if (yych <= 'e') goto yy1027;
            goto yy1035;
        } else {
            if (yych == 'l') goto yy1036;
            goto yy1027;
        }
    }
yy1040:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 128) {
        goto yy1040;
    }
    goto yy1022;
yy1042:
    ++YYCURSOR;
    goto yy1027;
}
#line 484 "cpp.re"

    }
}

/* this subscanner is called, whenever an Integer was recognized */
integer_suffix:
{
    if (s->enable_ms_extensions) {
    
#line 6841 "cpp_re.inc"
{
    YYCTYPE yych;
    if ((YYLIMIT - YYCURSOR) < 4) YYFILL(4);
    yych = *(YYMARKER = YYCURSOR);
    if (yych <= 'h') {
        if (yych <= 'L') {
            if (yych >= 'L') goto yy1046;
        } else {
            if (yych == 'U') goto yy1047;
        }
    } else {
        if (yych <= 'l') {
            if (yych <= 'i') goto yy1048;
            if (yych >= 'l') goto yy1050;
        } else {
            if (yych == 'u') goto yy1051;
        }
    }
yy1045:
#line 497 "cpp.re"
    { BOOST_WAVE_RET(T_INTLIT); }
#line 6863 "cpp_re.inc"
yy1046:
    yych = *++YYCURSOR;
    if (yych <= 'T') {
        if (yych == 'L') goto yy1052;
        goto yy1045;
    } else {
        if (yych <= 'U') goto yy1054;
        if (yych == 'u') goto yy1054;
        goto yy1045;
    }
yy1047:
    yych = *++YYCURSOR;
    if (yych == 'L') goto yy1055;
    if (yych == 'l') goto yy1056;
    goto yy1045;
yy1048:
    yych = *++YYCURSOR;
    if (yych == '6') goto yy1057;
yy1049:
    YYCURSOR = YYMARKER;
    goto yy1045;
yy1050:
    yych = *++YYCURSOR;
    if (yych <= 'k') {
        if (yych == 'U') goto yy1054;
        goto yy1045;
    } else {
        if (yych <= 'l') goto yy1052;
        if (yych == 'u') goto yy1054;
        goto yy1045;
    }
yy1051:
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= 'h') {
        if (yych == 'L') goto yy1055;
        goto yy1045;
    } else {
        if (yych <= 'i') goto yy1048;
        if (yych == 'l') goto yy1056;
        goto yy1045;
    }
yy1052:
    yych = *++YYCURSOR;
    if (yych == 'U') goto yy1058;
    if (yych == 'u') goto yy1058;
yy1053:
#line 494 "cpp.re"
    { BOOST_WAVE_RET(T_LONGINTLIT); }
#line 6912 "cpp_re.inc"
yy1054:
    ++YYCURSOR;
    goto yy1045;
yy1055:
    yych = *++YYCURSOR;
    if (yych == 'L') goto yy1058;
    goto yy1045;
yy1056:
    yych = *++YYCURSOR;
    if (yych == 'l') goto yy1058;
    goto yy1045;
yy1057:
    yych = *++YYCURSOR;
    if (yych != '4') goto yy1049;
yy1058:
    ++YYCURSOR;
    goto yy1053;
}
#line 498 "cpp.re"

    }
    else {
    
#line 6936 "cpp_re.inc"
{
    YYCTYPE yych;
    if ((YYLIMIT - YYCURSOR) < 3) YYFILL(3);
    yych = *YYCURSOR;
    if (yych <= 'U') {
        if (yych == 'L') goto yy1062;
        if (yych >= 'U') goto yy1063;
    } else {
        if (yych <= 'l') {
            if (yych >= 'l') goto yy1064;
        } else {
            if (yych == 'u') goto yy1063;
        }
    }
yy1061:
#line 506 "cpp.re"
    { BOOST_WAVE_RET(T_INTLIT); }
#line 6954 "cpp_re.inc"
yy1062:
    yych = *++YYCURSOR;
    if (yych <= 'T') {
        if (yych == 'L') goto yy1065;
        goto yy1061;
    } else {
        if (yych <= 'U') goto yy1067;
        if (yych == 'u') goto yy1067;
        goto yy1061;
    }
yy1063:
    yych = *++YYCURSOR;
    if (yych == 'L') goto yy1068;
    if (yych == 'l') goto yy1069;
    goto yy1061;
yy1064:
    yych = *++YYCURSOR;
    if (yych <= 'k') {
        if (yych == 'U') goto yy1067;
        goto yy1061;
    } else {
        if (yych <= 'l') goto yy1065;
        if (yych == 'u') goto yy1067;
        goto yy1061;
    }
yy1065:
    yych = *++YYCURSOR;
    if (yych == 'U') goto yy1070;
    if (yych == 'u') goto yy1070;
yy1066:
#line 503 "cpp.re"
    { BOOST_WAVE_RET(T_LONGINTLIT); }
#line 6987 "cpp_re.inc"
yy1067:
    ++YYCURSOR;
    goto yy1061;
yy1068:
    yych = *++YYCURSOR;
    if (yych == 'L') goto yy1070;
    goto yy1061;
yy1069:
    yych = *++YYCURSOR;
    if (yych != 'l') goto yy1061;
yy1070:
    ++YYCURSOR;
    goto yy1066;
}
#line 507 "cpp.re"

    }

    // re2c will complain about -Wmatch-empty-string above
    // it's OK because we've already matched an integer
    // and will return T_INTLIT
}

/* this subscanner is invoked for C++0x extended character literals */
extcharlit:
{
    
#line 7015 "cpp_re.inc"
{
    YYCTYPE yych;
    static const unsigned char yybm[] = {
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
        128, 128, 128, 128, 128, 128, 128, 128, 
        128, 128,   0,   0,   0,   0,   0,   0, 
          0, 128, 128, 128, 128, 128, 128,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0, 128, 128, 128, 128, 128, 128,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
    };
    if ((YYLIMIT - YYCURSOR) < 13) YYFILL(13);
    yych = *YYCURSOR;
    if (yych <= 0x1F) {
        if (yych <= '\n') {
            if (yych <= 0x08) goto yy1073;
            if (yych <= '\t') goto yy1075;
            goto yy1077;
        } else {
            if (yych <= '\f') goto yy1075;
            if (yych <= '\r') goto yy1077;
        }
    } else {
        if (yych <= '>') {
            if (yych == '\'') goto yy1077;
            goto yy1075;
        } else {
            if (yych <= '?') goto yy1078;
            if (yych == '\\') goto yy1079;
            goto yy1075;
        }
    }
yy1073:
    ++YYCURSOR;
#line 519 "cpp.re"
    {
            (*s->error_proc)(s, lexing_exception::generic_lexing_error,
                "Invalid character in raw string delimiter ('%c')", yych);
        }
#line 7080 "cpp_re.inc"
yy1075:
    yych = *++YYCURSOR;
    if (yych == '\'') goto yy1080;
yy1076:
#line 528 "cpp.re"
    { BOOST_WAVE_RET(TOKEN_FROM_ID(*s->tok, UnknownTokenType)); }
#line 7087 "cpp_re.inc"
yy1077:
    ++YYCURSOR;
    goto yy1076;
yy1078:
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '\'') goto yy1080;
    if (yych == '?') goto yy1082;
    goto yy1076;
yy1079:
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '\\') {
        if (yych <= '>') {
            if (yych <= '&') {
                if (yych == '"') goto yy1084;
                goto yy1076;
            } else {
                if (yych <= '\'') goto yy1084;
                if (yych <= '/') goto yy1076;
                if (yych <= '7') goto yy1085;
                goto yy1076;
            }
        } else {
            if (yych <= 'E') {
                if (yych <= '?') goto yy1086;
                if (yych <= 'D') goto yy1076;
                goto yy1084;
            } else {
                if (yych == 'U') goto yy1087;
                if (yych <= '[') goto yy1076;
                goto yy1084;
            }
        }
    } else {
        if (yych <= 'q') {
            if (yych <= 'd') {
                if (yych <= '`') goto yy1076;
                if (yych <= 'b') goto yy1084;
                goto yy1076;
            } else {
                if (yych <= 'f') goto yy1084;
                if (yych == 'n') goto yy1084;
                goto yy1076;
            }
        } else {
            if (yych <= 'u') {
                if (yych == 's') goto yy1076;
                if (yych <= 't') goto yy1084;
                goto yy1088;
            } else {
                if (yych <= 'v') goto yy1084;
                if (yych == 'x') goto yy1089;
                goto yy1076;
            }
        }
    }
yy1080:
    ++YYCURSOR;
#line 525 "cpp.re"
    { BOOST_WAVE_RET(T_CHARLIT); }
#line 7147 "cpp_re.inc"
yy1082:
    yych = *++YYCURSOR;
    if (yych == '/') goto yy1090;
yy1083:
    YYCURSOR = YYMARKER;
    goto yy1076;
yy1084:
    yych = *++YYCURSOR;
    if (yych == '\'') goto yy1080;
    goto yy1083;
yy1085:
    yych = *++YYCURSOR;
    if (yych == '\'') goto yy1080;
    if (yych <= '/') goto yy1083;
    if (yych <= '7') goto yy1091;
    goto yy1083;
yy1086:
    yych = *++YYCURSOR;
    if (yych == '\'') goto yy1080;
    if (yych == '?') goto yy1092;
    goto yy1083;
yy1087:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1083;
        if (yych <= '9') goto yy1093;
        goto yy1083;
    } else {
        if (yych <= 'F') goto yy1093;
        if (yych <= '`') goto yy1083;
        if (yych <= 'f') goto yy1093;
        goto yy1083;
    }
yy1088:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1083;
        if (yych <= '9') goto yy1094;
        goto yy1083;
    } else {
        if (yych <= 'F') goto yy1094;
        if (yych <= '`') goto yy1083;
        if (yych <= 'f') goto yy1094;
        goto yy1083;
    }
yy1089:
    yych = *++YYCURSOR;
    if (yych == '\'') goto yy1083;
    goto yy1096;
yy1090:
    yych = *++YYCURSOR;
    if (yych <= '\\') {
        if (yych <= '>') {
            if (yych <= '&') {
                if (yych == '"') goto yy1084;
                goto yy1083;
            } else {
                if (yych <= '\'') goto yy1084;
                if (yych <= '/') goto yy1083;
                if (yych <= '7') goto yy1085;
                goto yy1083;
            }
        } else {
            if (yych <= 'E') {
                if (yych <= '?') goto yy1086;
                if (yych <= 'D') goto yy1083;
                goto yy1084;
            } else {
                if (yych == 'U') goto yy1087;
                if (yych <= '[') goto yy1083;
                goto yy1084;
            }
        }
    } else {
        if (yych <= 'q') {
            if (yych <= 'd') {
                if (yych <= '`') goto yy1083;
                if (yych <= 'b') goto yy1084;
                goto yy1083;
            } else {
                if (yych <= 'f') goto yy1084;
                if (yych == 'n') goto yy1084;
                goto yy1083;
            }
        } else {
            if (yych <= 'u') {
                if (yych == 's') goto yy1083;
                if (yych <= 't') goto yy1084;
                goto yy1088;
            } else {
                if (yych <= 'v') goto yy1084;
                if (yych == 'x') goto yy1089;
                goto yy1083;
            }
        }
    }
yy1091:
    yych = *++YYCURSOR;
    if (yych == '\'') goto yy1080;
    if (yych <= '/') goto yy1083;
    if (yych <= '7') goto yy1084;
    goto yy1083;
yy1092:
    yych = *++YYCURSOR;
    if (yych == '/') goto yy1084;
    goto yy1083;
yy1093:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1083;
        if (yych <= '9') goto yy1097;
        goto yy1083;
    } else {
        if (yych <= 'F') goto yy1097;
        if (yych <= '`') goto yy1083;
        if (yych <= 'f') goto yy1097;
        goto yy1083;
    }
yy1094:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1083;
        if (yych <= '9') goto yy1098;
        goto yy1083;
    } else {
        if (yych <= 'F') goto yy1098;
        if (yych <= '`') goto yy1083;
        if (yych <= 'f') goto yy1098;
        goto yy1083;
    }
yy1095:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
yy1096:
    if (yybm[0+yych] & 128) {
        goto yy1095;
    }
    if (yych == '\'') goto yy1080;
    goto yy1083;
yy1097:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1083;
        if (yych <= '9') goto yy1099;
        goto yy1083;
    } else {
        if (yych <= 'F') goto yy1099;
        if (yych <= '`') goto yy1083;
        if (yych <= 'f') goto yy1099;
        goto yy1083;
    }
yy1098:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1083;
        if (yych <= '9') goto yy1100;
        goto yy1083;
    } else {
        if (yych <= 'F') goto yy1100;
        if (yych <= '`') goto yy1083;
        if (yych <= 'f') goto yy1100;
        goto yy1083;
    }
yy1099:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1083;
        if (yych <= '9') goto yy1088;
        goto yy1083;
    } else {
        if (yych <= 'F') goto yy1088;
        if (yych <= '`') goto yy1083;
        if (yych <= 'f') goto yy1088;
        goto yy1083;
    }
yy1100:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1083;
        if (yych <= '9') goto yy1084;
        goto yy1083;
    } else {
        if (yych <= 'F') goto yy1084;
        if (yych <= '`') goto yy1083;
        if (yych <= 'f') goto yy1084;
        goto yy1083;
    }
}
#line 529 "cpp.re"

}

/* this subscanner is invoked for C++0x extended character string literals */
extstringlit:
{
    
#line 7345 "cpp_re.inc"
{
    YYCTYPE yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,  32,   0,  32,  32,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
         32,  32,   0,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  64, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32, 128,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
         32,  32,  32,  32,  32,  32,  32,  32, 
    };
    if ((YYLIMIT - YYCURSOR) < 2) YYFILL(2);
    yych = *YYCURSOR;
    if (yych <= 0x1F) {
        if (yych <= '\n') {
            if (yych <= 0x08) goto yy1103;
            if (yych <= '\t') goto yy1105;
            goto yy1107;
        } else {
            if (yych <= '\f') goto yy1105;
            if (yych <= '\r') goto yy1107;
        }
    } else {
        if (yych <= '>') {
            if (yych == '"') goto yy1108;
            goto yy1105;
        } else {
            if (yych <= '?') goto yy1110;
            if (yych == '\\') goto yy1111;
            goto yy1105;
        }
    }
yy1103:
    ++YYCURSOR;
#line 536 "cpp.re"
    {
            (*s->error_proc)(s, lexing_exception::generic_lexing_error,
                "Invalid character in raw string delimiter ('%c')", yych);
        }
#line 7411 "cpp_re.inc"
yy1105:
    yyaccept = 0;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '\n') {
        if (yych == '\t') goto yy1113;
    } else {
        if (yych <= '\f') goto yy1113;
        if (yych >= ' ') goto yy1113;
    }
yy1106:
#line 545 "cpp.re"
    { BOOST_WAVE_RET(TOKEN_FROM_ID(*s->tok, UnknownTokenType)); }
#line 7424 "cpp_re.inc"
yy1107:
    ++YYCURSOR;
    goto yy1106;
yy1108:
    ++YYCURSOR;
yy1109:
#line 542 "cpp.re"
    { BOOST_WAVE_RET(T_STRINGLIT); }
#line 7433 "cpp_re.inc"
yy1110:
    yyaccept = 0;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yybm[0+yych] & 64) {
        goto yy1117;
    }
    if (yych <= '\n') {
        if (yych == '\t') goto yy1113;
        goto yy1106;
    } else {
        if (yych <= '\f') goto yy1113;
        if (yych <= 0x1F) goto yy1106;
        goto yy1113;
    }
yy1111:
    yyaccept = 0;
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '\\') {
        if (yych <= '>') {
            if (yych <= '&') {
                if (yych != '"') goto yy1106;
            } else {
                if (yych <= '\'') goto yy1112;
                if (yych <= '/') goto yy1106;
                if (yych >= '8') goto yy1106;
            }
        } else {
            if (yych <= 'E') {
                if (yych <= '?') goto yy1112;
                if (yych <= 'D') goto yy1106;
            } else {
                if (yych == 'U') goto yy1119;
                if (yych <= '[') goto yy1106;
            }
        }
    } else {
        if (yych <= 'q') {
            if (yych <= 'd') {
                if (yych <= '`') goto yy1106;
                if (yych >= 'c') goto yy1106;
            } else {
                if (yych <= 'f') goto yy1112;
                if (yych != 'n') goto yy1106;
            }
        } else {
            if (yych <= 'u') {
                if (yych == 's') goto yy1106;
                if (yych >= 'u') goto yy1120;
            } else {
                if (yych <= 'v') goto yy1112;
                if (yych == 'x') goto yy1121;
                goto yy1106;
            }
        }
    }
yy1112:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
yy1113:
    if (yybm[0+yych] & 32) {
        goto yy1112;
    }
    if (yych <= 0x1F) goto yy1114;
    if (yych <= '"') goto yy1108;
    if (yych <= '?') goto yy1115;
    goto yy1116;
yy1114:
    YYCURSOR = YYMARKER;
    if (yyaccept == 0) {
        goto yy1106;
    } else {
        goto yy1109;
    }
yy1115:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 32) {
        goto yy1112;
    }
    if (yych <= 0x1F) goto yy1114;
    if (yych <= '"') goto yy1108;
    if (yych <= '?') goto yy1117;
yy1116:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '\\') {
        if (yych <= '>') {
            if (yych <= '&') {
                if (yych == '"') goto yy1112;
                goto yy1114;
            } else {
                if (yych <= '\'') goto yy1112;
                if (yych <= '/') goto yy1114;
                if (yych <= '7') goto yy1112;
                goto yy1114;
            }
        } else {
            if (yych <= 'E') {
                if (yych <= '?') goto yy1112;
                if (yych <= 'D') goto yy1114;
                goto yy1112;
            } else {
                if (yych == 'U') goto yy1119;
                if (yych <= '[') goto yy1114;
                goto yy1112;
            }
        }
    } else {
        if (yych <= 'q') {
            if (yych <= 'd') {
                if (yych <= '`') goto yy1114;
                if (yych <= 'b') goto yy1112;
                goto yy1114;
            } else {
                if (yych <= 'f') goto yy1112;
                if (yych == 'n') goto yy1112;
                goto yy1114;
            }
        } else {
            if (yych <= 'u') {
                if (yych == 's') goto yy1114;
                if (yych <= 't') goto yy1112;
                goto yy1120;
            } else {
                if (yych <= 'v') goto yy1112;
                if (yych == 'x') goto yy1121;
                goto yy1114;
            }
        }
    }
yy1117:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 64) {
        goto yy1117;
    }
    if (yych <= '!') {
        if (yych <= '\n') {
            if (yych == '\t') goto yy1112;
            goto yy1114;
        } else {
            if (yych <= '\f') goto yy1112;
            if (yych <= 0x1F) goto yy1114;
            goto yy1112;
        }
    } else {
        if (yych <= '/') {
            if (yych <= '"') goto yy1108;
            if (yych <= '.') goto yy1112;
            goto yy1122;
        } else {
            if (yych == '\\') goto yy1116;
            goto yy1112;
        }
    }
yy1119:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1114;
        if (yych <= '9') goto yy1124;
        goto yy1114;
    } else {
        if (yych <= 'F') goto yy1124;
        if (yych <= '`') goto yy1114;
        if (yych <= 'f') goto yy1124;
        goto yy1114;
    }
yy1120:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1114;
        if (yych <= '9') goto yy1125;
        goto yy1114;
    } else {
        if (yych <= 'F') goto yy1125;
        if (yych <= '`') goto yy1114;
        if (yych <= 'f') goto yy1125;
        goto yy1114;
    }
yy1121:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1114;
        if (yych <= '9') goto yy1112;
        goto yy1114;
    } else {
        if (yych <= 'F') goto yy1112;
        if (yych <= '`') goto yy1114;
        if (yych <= 'f') goto yy1112;
        goto yy1114;
    }
yy1122:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 32) {
        goto yy1112;
    }
    if (yych <= 0x1F) goto yy1114;
    if (yych <= '"') goto yy1126;
    if (yych <= '?') goto yy1115;
    goto yy1122;
yy1124:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1114;
        if (yych <= '9') goto yy1127;
        goto yy1114;
    } else {
        if (yych <= 'F') goto yy1127;
        if (yych <= '`') goto yy1114;
        if (yych <= 'f') goto yy1127;
        goto yy1114;
    }
yy1125:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1114;
        if (yych <= '9') goto yy1128;
        goto yy1114;
    } else {
        if (yych <= 'F') goto yy1128;
        if (yych <= '`') goto yy1114;
        if (yych <= 'f') goto yy1128;
        goto yy1114;
    }
yy1126:
    yyaccept = 1;
    YYMARKER = ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 32) {
        goto yy1112;
    }
    if (yych <= 0x1F) goto yy1109;
    if (yych <= '"') goto yy1108;
    if (yych <= '?') goto yy1115;
    goto yy1116;
yy1127:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1114;
        if (yych <= '9') goto yy1129;
        goto yy1114;
    } else {
        if (yych <= 'F') goto yy1129;
        if (yych <= '`') goto yy1114;
        if (yych <= 'f') goto yy1129;
        goto yy1114;
    }
yy1128:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1114;
        if (yych <= '9') goto yy1121;
        goto yy1114;
    } else {
        if (yych <= 'F') goto yy1121;
        if (yych <= '`') goto yy1114;
        if (yych <= 'f') goto yy1121;
        goto yy1114;
    }
yy1129:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1114;
        if (yych <= '9') goto yy1120;
        goto yy1114;
    } else {
        if (yych <= 'F') goto yy1120;
        if (yych <= '`') goto yy1114;
        if (yych <= 'f') goto yy1120;
        goto yy1114;
    }
}
#line 546 "cpp.re"

}

extrawstringlit:
{
    // we have consumed the double quote but not the lparen
    // at this point we may see a delimiter

    
#line 7739 "cpp_re.inc"
{
    YYCTYPE yych;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yych <= '(') {
        if (yych <= 0x1F) goto yy1132;
        if (yych <= '\'') goto yy1134;
        goto yy1136;
    } else {
        if (yych <= ')') goto yy1132;
        if (yych != '\\') goto yy1134;
    }
yy1132:
    ++YYCURSOR;
#line 555 "cpp.re"
    {
            (*s->error_proc)(s, lexing_exception::generic_lexing_error,
                "Invalid character in raw string delimiter ('%c')", yych);
        }
#line 7759 "cpp_re.inc"
yy1134:
    ++YYCURSOR;
#line 562 "cpp.re"
    {
            rawstringdelim += yych;
            if (rawstringdelim.size() > 16)
            {
                (*s->error_proc)(s, lexing_exception::generic_lexing_error,
                    "Raw string delimiter of excessive length (\"%s\") in input stream",
                    rawstringdelim.c_str());
            }
            goto extrawstringlit;
        }
#line 7773 "cpp_re.inc"
yy1136:
    ++YYCURSOR;
#line 574 "cpp.re"
    {
            rawstringdelim = ")" + rawstringdelim;
            goto extrawstringbody;
        }
#line 7781 "cpp_re.inc"
}
#line 579 "cpp.re"

}

extrawstringbody:
{
    
#line 7790 "cpp_re.inc"
{
    YYCTYPE yych;
    static const unsigned char yybm[] = {
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
        128, 128, 128, 128, 128, 128, 128, 128, 
        128, 128,   0,   0,   0,   0,   0,   0, 
          0, 128, 128, 128, 128, 128, 128,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0, 128, 128, 128, 128, 128, 128,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
          0,   0,   0,   0,   0,   0,   0,   0, 
    };
    if ((YYLIMIT - YYCURSOR) < 12) YYFILL(12);
    yych = *YYCURSOR;
    if (yych <= 0x1F) {
        if (yych <= '\n') {
            if (yych <= 0x08) goto yy1140;
            if (yych <= '\t') goto yy1142;
            goto yy1144;
        } else {
            if (yych <= '\f') goto yy1142;
            if (yych <= '\r') goto yy1146;
        }
    } else {
        if (yych <= '>') {
            if (yych == '"') goto yy1147;
            goto yy1142;
        } else {
            if (yych <= '?') goto yy1149;
            if (yych == '\\') goto yy1150;
            goto yy1142;
        }
    }
yy1140:
    ++YYCURSOR;
#line 586 "cpp.re"
    {
            (*s->error_proc)(s, lexing_exception::generic_lexing_error,
                "Invalid character in raw string body ('%c')", yych);
        }
#line 7855 "cpp_re.inc"
yy1142:
    ++YYCURSOR;
yy1143:
#line 599 "cpp.re"
    {
            goto extrawstringbody;
        }
#line 7863 "cpp_re.inc"
yy1144:
    ++YYCURSOR;
yy1145:
#line 592 "cpp.re"
    {
            s->line += count_backslash_newlines(s, cursor) +1;
            cursor.column = 1;
            goto extrawstringbody;
        }
#line 7873 "cpp_re.inc"
yy1146:
    yych = *++YYCURSOR;
    if (yych == '\n') goto yy1144;
    goto yy1145;
yy1147:
    ++YYCURSOR;
#line 604 "cpp.re"
    {
            // check to see if we have completed a delimiter
            if (string_type((char *)(YYCURSOR - rawstringdelim.size() - 1),
                             (char *)(YYCURSOR - 1)) == rawstringdelim)
            {
                 BOOST_WAVE_RET(T_RAWSTRINGLIT);
            } else {
                goto extrawstringbody;
            }
        }
#line 7891 "cpp_re.inc"
yy1149:
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '?') goto yy1151;
    goto yy1143;
yy1150:
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych <= '\\') {
        if (yych <= '>') {
            if (yych <= '&') {
                if (yych == '"') goto yy1142;
                goto yy1143;
            } else {
                if (yych <= '\'') goto yy1142;
                if (yych <= '/') goto yy1143;
                if (yych <= '7') goto yy1153;
                goto yy1143;
            }
        } else {
            if (yych <= 'E') {
                if (yych <= '?') goto yy1154;
                if (yych <= 'D') goto yy1143;
                goto yy1142;
            } else {
                if (yych == 'U') goto yy1155;
                if (yych <= '[') goto yy1143;
                goto yy1142;
            }
        }
    } else {
        if (yych <= 'q') {
            if (yych <= 'd') {
                if (yych <= '`') goto yy1143;
                if (yych <= 'b') goto yy1142;
                goto yy1143;
            } else {
                if (yych <= 'f') goto yy1142;
                if (yych == 'n') goto yy1142;
                goto yy1143;
            }
        } else {
            if (yych <= 'u') {
                if (yych == 's') goto yy1143;
                if (yych <= 't') goto yy1142;
                goto yy1156;
            } else {
                if (yych <= 'v') goto yy1142;
                if (yych == 'x') goto yy1157;
                goto yy1143;
            }
        }
    }
yy1151:
    yych = *++YYCURSOR;
    if (yych == '/') goto yy1158;
yy1152:
    YYCURSOR = YYMARKER;
    goto yy1143;
yy1153:
    yych = *++YYCURSOR;
    if (yych <= '/') goto yy1143;
    if (yych <= '7') goto yy1159;
    goto yy1143;
yy1154:
    yych = *(YYMARKER = ++YYCURSOR);
    if (yych == '?') goto yy1160;
    goto yy1143;
yy1155:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1152;
        if (yych <= '9') goto yy1161;
        goto yy1152;
    } else {
        if (yych <= 'F') goto yy1161;
        if (yych <= '`') goto yy1152;
        if (yych <= 'f') goto yy1161;
        goto yy1152;
    }
yy1156:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1152;
        if (yych <= '9') goto yy1162;
        goto yy1152;
    } else {
        if (yych <= 'F') goto yy1162;
        if (yych <= '`') goto yy1152;
        if (yych <= 'f') goto yy1162;
        goto yy1152;
    }
yy1157:
    yych = *++YYCURSOR;
    if (yybm[0+yych] & 128) {
        goto yy1163;
    }
    goto yy1152;
yy1158:
    yych = *++YYCURSOR;
    if (yych <= '\\') {
        if (yych <= '>') {
            if (yych <= '&') {
                if (yych == '"') goto yy1142;
                goto yy1152;
            } else {
                if (yych <= '\'') goto yy1142;
                if (yych <= '/') goto yy1152;
                if (yych <= '7') goto yy1153;
                goto yy1152;
            }
        } else {
            if (yych <= 'E') {
                if (yych <= '?') goto yy1154;
                if (yych <= 'D') goto yy1152;
                goto yy1142;
            } else {
                if (yych == 'U') goto yy1155;
                if (yych <= '[') goto yy1152;
                goto yy1142;
            }
        }
    } else {
        if (yych <= 'q') {
            if (yych <= 'd') {
                if (yych <= '`') goto yy1152;
                if (yych <= 'b') goto yy1142;
                goto yy1152;
            } else {
                if (yych <= 'f') goto yy1142;
                if (yych == 'n') goto yy1142;
                goto yy1152;
            }
        } else {
            if (yych <= 'u') {
                if (yych == 's') goto yy1152;
                if (yych <= 't') goto yy1142;
                goto yy1156;
            } else {
                if (yych <= 'v') goto yy1142;
                if (yych == 'x') goto yy1157;
                goto yy1152;
            }
        }
    }
yy1159:
    yych = *++YYCURSOR;
    if (yych <= '/') goto yy1143;
    if (yych <= '7') goto yy1142;
    goto yy1143;
yy1160:
    yych = *++YYCURSOR;
    if (yych == '/') goto yy1142;
    goto yy1152;
yy1161:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1152;
        if (yych <= '9') goto yy1165;
        goto yy1152;
    } else {
        if (yych <= 'F') goto yy1165;
        if (yych <= '`') goto yy1152;
        if (yych <= 'f') goto yy1165;
        goto yy1152;
    }
yy1162:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1152;
        if (yych <= '9') goto yy1166;
        goto yy1152;
    } else {
        if (yych <= 'F') goto yy1166;
        if (yych <= '`') goto yy1152;
        if (yych <= 'f') goto yy1166;
        goto yy1152;
    }
yy1163:
    ++YYCURSOR;
    if (YYLIMIT <= YYCURSOR) YYFILL(1);
    yych = *YYCURSOR;
    if (yybm[0+yych] & 128) {
        goto yy1163;
    }
    goto yy1143;
yy1165:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1152;
        if (yych <= '9') goto yy1167;
        goto yy1152;
    } else {
        if (yych <= 'F') goto yy1167;
        if (yych <= '`') goto yy1152;
        if (yych <= 'f') goto yy1167;
        goto yy1152;
    }
yy1166:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1152;
        if (yych <= '9') goto yy1168;
        goto yy1152;
    } else {
        if (yych <= 'F') goto yy1168;
        if (yych <= '`') goto yy1152;
        if (yych <= 'f') goto yy1168;
        goto yy1152;
    }
yy1167:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1152;
        if (yych <= '9') goto yy1156;
        goto yy1152;
    } else {
        if (yych <= 'F') goto yy1156;
        if (yych <= '`') goto yy1152;
        if (yych <= 'f') goto yy1156;
        goto yy1152;
    }
yy1168:
    yych = *++YYCURSOR;
    if (yych <= '@') {
        if (yych <= '/') goto yy1152;
        if (yych <= '9') goto yy1142;
        goto yy1152;
    } else {
        if (yych <= 'F') goto yy1142;
        if (yych <= '`') goto yy1152;
        if (yych <= 'f') goto yy1142;
        goto yy1152;
    }
}
#line 614 "cpp.re"

}
