// Copyright <PERSON>, 2017
// Use, modification and distribution are subject to the
// Boost Software License, Version 1.0.
// (See accompanying file LICENSE_1_0.txt
// or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_MATH_QUADRATURE_DETAIL_TANH_SINH_DETAIL_HPP
#define BOOST_MATH_QUADRATURE_DETAIL_TANH_SINH_DETAIL_HPP

#include <cmath>
#include <vector>
#include <typeinfo>
#include <boost/math/tools/atomic.hpp>
#include <boost/math/constants/constants.hpp>
#include <boost/math/special_functions/next.hpp>
#include <boost/math/tools/config.hpp>

#ifdef BOOST_HAS_THREADS
#include <mutex>
#endif

namespace boost{ namespace math{ namespace quadrature { namespace detail{


// Returns the tanh-sinh quadrature of a function f over the open interval (-1, 1)

template<class Real, class Policy>
class tanh_sinh_detail
{
   static const int initializer_selector =
      !std::numeric_limits<Real>::is_specialized || (std::numeric_limits<Real>::radix != 2) ?
      0 :
      (std::numeric_limits<Real>::digits < 30) && (std::numeric_limits<Real>::max_exponent <= 128) ?
      1 :
      (std::numeric_limits<Real>::digits <= std::numeric_limits<double>::digits) && (std::numeric_limits<Real>::max_exponent <= std::numeric_limits<double>::max_exponent) ?
      2 :
      (std::numeric_limits<Real>::digits <= std::numeric_limits<long double>::digits) && (std::numeric_limits<Real>::max_exponent <= 16384) ?
      3 :
#ifdef BOOST_HAS_FLOAT128
      (std::numeric_limits<Real>::digits <= 113) && (std::numeric_limits<Real>::max_exponent <= 16384) ?
      4 :
#endif
      0;
public:
    tanh_sinh_detail(size_t max_refinements, const Real& min_complement) : m_max_refinements(max_refinements)
    {
       typedef std::integral_constant<int, initializer_selector> tag_type;
       init(min_complement, tag_type());
    }

    template<class F>
    decltype(std::declval<F>()(std::declval<Real>(), std::declval<Real>())) integrate(const F f, Real* error, Real* L1, const char* function, Real left_min_complement, Real right_min_complement, Real tolerance, std::size_t* levels) const;

private:
   const std::vector<Real>& get_abscissa_row(std::size_t n)const
   {
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
      if (m_committed_refinements.load() < n)
         extend_refinements();
      BOOST_MATH_ASSERT(m_committed_refinements.load() >= n);
#else
      if (m_committed_refinements < n)
         extend_refinements();
      BOOST_MATH_ASSERT(m_committed_refinements >= n);
#endif
      return m_abscissas[n];
   }
   const std::vector<Real>& get_weight_row(std::size_t n)const
   {
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
      if (m_committed_refinements.load() < n)
         extend_refinements();
      BOOST_MATH_ASSERT(m_committed_refinements.load() >= n);
#else
      if (m_committed_refinements < n)
         extend_refinements();
      BOOST_MATH_ASSERT(m_committed_refinements >= n);
#endif
      return m_weights[n];
   }
   std::size_t get_first_complement_index(std::size_t n)const
   {
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
      if (m_committed_refinements.load() < n)
         extend_refinements();
      BOOST_MATH_ASSERT(m_committed_refinements.load() >= n);
#else
      if (m_committed_refinements < n)
         extend_refinements();
      BOOST_MATH_ASSERT(m_committed_refinements >= n);
#endif
      return m_first_complements[n];
   }

   void init(const Real& min_complement, const std::integral_constant<int, 0>&);
   void init(const Real& min_complement, const std::integral_constant<int, 1>&);
   void init(const Real& min_complement, const std::integral_constant<int, 2>&);
   void init(const Real& min_complement, const std::integral_constant<int, 3>&);
#ifdef BOOST_HAS_FLOAT128
   void init(const Real& min_complement, const std::integral_constant<int, 4>&);
#endif
   void prune_to_min_complement(const Real& m);
   void extend_refinements()const
   {
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
      std::lock_guard<std::mutex> guard(m_mutex);
#endif
      //
      // Check some other thread hasn't got here after we read the atomic variable, but before we got here:
      //
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
      if (m_committed_refinements.load() >= m_max_refinements)
         return;
#else
      if (m_committed_refinements >= m_max_refinements)
         return;
#endif

      using std::ldexp;
      using std::ceil;
      ++m_committed_refinements;
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
      std::size_t row = m_committed_refinements.load();
#else
      std::size_t row = m_committed_refinements;
#endif
      Real h = ldexp(static_cast<Real>(1), -static_cast<int>(row));
      std::size_t first_complement = 0;
      std::size_t n = boost::math::itrunc(ceil((m_t_max - h) / (2 * h)));
      m_abscissas[row].reserve(n);
      m_weights[row].reserve(n);
      for (Real pos = h; pos < m_t_max; pos += 2 * h)
      {
         if (pos < m_t_crossover)
            ++first_complement;
         m_abscissas[row].push_back(pos < m_t_crossover ? abscissa_at_t(pos) : -abscissa_complement_at_t(pos));
      }
      m_first_complements[row] = first_complement;
      for (Real pos = h; pos < m_t_max; pos += 2 * h)
         m_weights[row].push_back(weight_at_t(pos));
   }

   static inline Real abscissa_at_t(const Real& t)
   {
      using std::tanh;
      using std::sinh;
      using boost::math::constants::half_pi;
      return tanh(half_pi<Real>()*sinh(t));
   }
   static inline Real weight_at_t(const Real& t)
   {
      using std::cosh;
      using std::sinh;
      using boost::math::constants::half_pi;
      Real cs = cosh(half_pi<Real>() * sinh(t));
      return half_pi<Real>() * cosh(t) / (cs * cs);
   }
   static inline Real abscissa_complement_at_t(const Real& t)
   {
      using std::cosh;
      using std::exp;
      using std::sinh;
      using boost::math::constants::half_pi;
      Real u2 = half_pi<Real>() * sinh(t);
      return 1 / (exp(u2) *cosh(u2));
   }
   static inline Real t_from_abscissa_complement(const Real& x)
   {
      using std::log;
      using std::sqrt;
      using boost::math::constants::pi;
      Real l = log(2-x) - log(x);
      return log((sqrt(l * l + pi<Real>() * pi<Real>()) + l) / pi<Real>());
   };


   mutable std::vector<std::vector<Real>> m_abscissas;
   mutable std::vector<std::vector<Real>> m_weights;
   mutable std::vector<std::size_t>       m_first_complements;
   std::size_t                       m_max_refinements, m_inital_row_length{};
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
   mutable boost::math::detail::atomic_unsigned_type      m_committed_refinements{};
   mutable std::mutex m_mutex;
#else
   mutable unsigned                  m_committed_refinements;
#endif
   Real m_t_max, m_t_crossover;
};

template<class Real, class Policy>
template<class F>
decltype(std::declval<F>()(std::declval<Real>(), std::declval<Real>())) tanh_sinh_detail<Real, Policy>::integrate(const F f, Real* error, Real* L1, const char* function, Real left_min_complement, Real right_min_complement, Real tolerance, std::size_t* levels) const
{
    using std::abs;
    using std::fabs;
    using std::floor;
    using std::tanh;
    using std::sinh;
    using std::sqrt;
    using boost::math::constants::half;
    using boost::math::constants::half_pi;

    //
    // The type of the result:
    typedef decltype(std::declval<F>()(std::declval<Real>(), std::declval<Real>())) result_type;

    Real h = m_t_max / m_inital_row_length;
    result_type I0 = half_pi<Real>() * f(0, 1);
    Real L1_I0 = abs(I0);
    //
    // We maintain 4 integer values:
    // max_left_position is the logical index of the abscissa value closest to the
    // left endpoint of the range that we can call f(x_i) on without rounding error
    // inside f(x_i) causing evaluation at the endpoint.
    // max_left_index is the actual position in the current row that has a logical index
    // no higher than max_left_position.  Remember that since we only store odd numbered
    // indexes in each row, this may actually be one position to the left of max_left_position
    // in the case that is even.  Then, if we only evaluate f(-x_i) for abscissa values
    // i <= max_left_index we will never evaluate f(-x_i) at the left endpoint.
    // max_right_position and max_right_index are defined similarly for the right boundary
    // and are used to guard evaluation of f(x_i).
    //
    // max_left_position and max_right_position start off as the last element in row zero:
    //
    std::size_t max_left_position(m_abscissas[0].size() - 1);
    std::size_t max_left_index, max_right_position(max_left_position), max_right_index;
    //
    // Decrement max_left_position and max_right_position until the complement
    // of the abscissa value is greater than the smallest permitted (as specified
    // by the function caller):
    //
    while ((max_left_position > 1) && fabs(m_abscissas[0][max_left_position]) < left_min_complement)
       --max_left_position;
    while ((max_right_position > 1) && fabs(m_abscissas[0][max_right_position]) < right_min_complement)
       --max_right_position;
    //
    // Check for non-finite values at the end points:
    // 
    result_type yp{ f(-1 - m_abscissas[0][max_left_position], m_abscissas[0][max_left_position]) };
    result_type ym{ f(1 + m_abscissas[0][max_right_position], -m_abscissas[0][max_right_position]) };
    result_type tail_tolerance{ (std::max)(boost::math::tools::epsilon<Real>(), Real(tolerance * tolerance)) };
    while (max_left_position)
    {
       if ((boost::math::isfinite)(yp))
          break;
       --max_left_position;
       yp = f(-1 - m_abscissas[0][max_left_position], m_abscissas[0][max_left_position]);
    }
    //
    // Also remove points which are insignificant or zero:
    //
    while (max_left_position > 1)
    {
       if (abs(yp * m_weights[0][max_left_position]) > abs(L1_I0 * tail_tolerance))
          break;
       --max_left_position;
       yp = f(-1 - m_abscissas[0][max_left_position], m_abscissas[0][max_left_position]);
    }
    //
    // Over again for the right hand side:
    //
    while (max_right_position)
    {
       if ((boost::math::isfinite)(ym))
          break;
       --max_right_position;
       ym = f(1 + m_abscissas[0][max_right_position], -m_abscissas[0][max_right_position]);
    }
    while (max_right_position > 1)
    {
       if (abs(ym * m_weights[0][max_right_position]) > abs(L1_I0 * tail_tolerance))
          break;
       --max_right_position;
       ym = f(1 + m_abscissas[0][max_right_position], -m_abscissas[0][max_right_position]);
    }

    if ((max_left_position == 0) || (max_right_position == 0))
    {
       return policies::raise_evaluation_error(function, "The tanh_sinh quadrature found your function to be non-finite everywhere! Please check your function for singularities.", ym, Policy());
    }

    I0 += yp * m_weights[0][max_left_position] + ym * m_weights[0][max_right_position];
    L1_I0 += abs(yp * m_weights[0][max_left_position]) + abs(ym * m_weights[0][max_right_position]);
    //
    // Assumption: left_min_complement/right_min_complement are sufficiently small that we only
    // ever decrement through the stored values that are complements (the negative ones), and
    // never ever hit the true abscissa values (positive stored values).
    //
    BOOST_MATH_ASSERT(m_abscissas[0][max_left_position] < 0);
    BOOST_MATH_ASSERT(m_abscissas[0][max_right_position] < 0);

    for(size_t i = 1; i < m_abscissas[0].size(); ++i)
    {
        if ((i >= max_right_position) && (i >= max_left_position))
            break;
        Real x = m_abscissas[0][i];
        Real xc = x;
        Real w = m_weights[0][i];
        if ((boost::math::signbit)(x))
        {
           // We have stored x - 1:
           x = 1 + xc;
        }
        else
           xc = x - 1;
        yp = i < max_right_position ? f(x, -xc) : 0;
        ym = i < max_left_position ? f(-x, xc) : 0;
        I0 += (yp + ym)*w;
        L1_I0 += (abs(yp) + abs(ym))*w;
    }
    //
    // We have:
    // k = current row.
    // I0 = last integral value.
    // I1 = current integral value.
    // L1_I0 and L1_I1 are the absolute integral values.
    //
    size_t k = 1;
    result_type I1 = I0;
    Real L1_I1 = L1_I0;
    Real err = 0;
    //
    // thrash_count is a heuristic - it counts how many time the error has actually increased
    // rather than decreased, if this gets too high we abort...
    //
    unsigned thrash_count = 0;

    while (k < 4 || (k < m_weights.size() && k < m_max_refinements) )
    {
        I0 = I1;
        L1_I0 = L1_I1;

        I1 = half<Real>()*I0;
        L1_I1 = half<Real>()*L1_I0;
        h *= half<Real>();
        result_type sum = 0;
        Real absum = 0;
        Real endpoint_error = 0;
        auto const& abscissa_row = this->get_abscissa_row(k);
        auto const& weight_row = this->get_weight_row(k);
        std::size_t first_complement_index = this->get_first_complement_index(k);
        //
        // At the start of each new row we need to update the max left/right indexes
        // at which we can evaluate f(x_i).  The new logical position is simply twice
        // the old value.  The new max index is one position to the left of the new
        // logical value (remember each row contains only odd numbered positions).
        // Then we have to make a single check, to see if one position to the right
        // is also in bounds (this is the new abscissa value in this row which is
        // known to be in between a value known to be in bounds, and one known to be
        // not in bounds).
        // Thus, we filter which abscissa values generate a call to f(x_i), with a single
        // floating point comparison per loop.  Everything else is integer logic.
        //
        BOOST_MATH_ASSERT(max_left_position);
        max_left_index = max_left_position - 1;
        max_left_position *= 2;
        BOOST_MATH_ASSERT(max_right_position);
        max_right_index = max_right_position - 1;
        max_right_position *= 2;
        if ((abscissa_row.size() > max_left_index + 1) && (fabs(abscissa_row[max_left_index + 1]) > left_min_complement))
        {
           ++max_left_position;
           ++max_left_index;
        }
        if ((abscissa_row.size() > max_right_index + 1) && (fabs(abscissa_row[max_right_index + 1]) > right_min_complement))
        {
           ++max_right_position;
           ++max_right_index;
        }
        //
        // We also check that our endpoints don't hit singularities:
        //
        do
        {
           yp = f(-1 - abscissa_row[max_left_index], abscissa_row[max_left_index]);
           if ((boost::math::isfinite)(yp))
              break;
           if(max_left_position <= 2)
           {
              return policies::raise_evaluation_error(function, "The tanh_sinh quadrature found your function to be non-finite everywhere! Please check your function for singularities.", ym, Policy());
           }
           max_left_position -= 2;
           --max_left_index;
        } while (abscissa_row[max_left_index] < 0);
        bool truncate_left(false), truncate_right(false);
        if (abs(L1_I1 * tail_tolerance) > abs(yp * weight_row[max_left_index]))
           truncate_left = true;
        do
        {
           ym = f(1 + abscissa_row[max_right_index], -abscissa_row[max_right_index]);
           if ((boost::math::isfinite)(ym))
              break;
           if (max_right_position <= 2)
           {
              return policies::raise_evaluation_error(function, "The tanh_sinh quadrature found your function to be non-finite everywhere! Please check your function for singularities.", ym, Policy());
           }
           --max_right_index;
           max_right_position -= 2;
        } while (abscissa_row[max_right_index] < 0);
        if (abs(L1_I1 * tail_tolerance) > abs(ym * weight_row[max_right_index]))
           truncate_right = true;

        sum += yp * weight_row[max_left_index] + ym * weight_row[max_right_index];
        absum += abs(yp * weight_row[max_left_index]) + abs(ym * weight_row[max_right_index]);
        //
        // We estimate the error due to truncation as the value contributed by the two most extreme points.
        // In most cases this is tiny and can be ignored, if it is significant then either the area of the
        // integral is so far our in the tails that our exponent range can't reach it (example x^-p at double
        // precision and p ~ 1), or our function is truncated near epsilon, and we have had to narrow our endpoints.
        // In this latter case we may over-estimate the error, but this is the best we can do.
        // In any event, we do not add endpoint_error to the error estimate until we terminate the main loop,
        // otherwise it can make things appear to be non-converged, when in reality, they are as converged as they
        // will ever be.
        //
        endpoint_error = absum;

        for(size_t j = 0; j < weight_row.size(); ++j)
        {
            // If both left and right abscissa values are out of bounds at this step
            // we can just stop this loop right now:
            if ((j >= max_left_index) && (j >= max_right_index))
                break;
            Real x = abscissa_row[j];
            Real xc = x;
            Real w = weight_row[j];
            if (j >= first_complement_index)
            {
               // We have stored x - 1:
               BOOST_MATH_ASSERT(x < 0);
               x = 1 + xc;
            }
            else
            {
               BOOST_MATH_ASSERT(x >= 0);
               xc = x - 1;
            }

            yp = j >= max_right_index ? 0 : f(x, -xc);
            ym = j >= max_left_index ? 0 : f(-x, xc);
            result_type term = (yp + ym)*w;
            sum += term;

            // A question arises as to how accurately we actually need to estimate the L1 integral.
            // For simple integrands, computing the L1 norm makes the integration 20% slower,
            // but for more complicated integrands, this calculation is not noticeable.
            Real abterm = (abs(yp) + abs(ym))*w;
            absum += abterm;
        }

        I1 += sum*h;
        L1_I1 += absum*h;

        ++k;
        Real last_err = err;
        err = abs(I0 - I1);

        if (!(boost::math::isfinite)(I1))
        {
            return policies::raise_evaluation_error(function, "The tanh_sinh quadrature evaluated your function at a singular point and got %1%. Please narrow the bounds of integration or check your function for singularities.", I1, Policy());
        }
        //
        // If the error is increasing, and we're past level 4, something bad is very likely happening:
        //
        if ((err * 1.5 > last_err) && (k > 4))
        {
           bool terminate = false;
           if ((++thrash_count > 1) && (last_err < 1e-3))
              // Probably just thrashing, abort:
              terminate = true;
           else if(thrash_count > 2)
              // OK, terrible error, but giving up anyway!
              terminate = true;
           else if (last_err < boost::math::tools::root_epsilon<Real>())
              // Trying to squeeze precision that probably isn't there, abort:
              terminate = true;
           else
           {
              // Take a look at the end points, if there's significant new area being
              // discovered, then we're not able to get close enough to the endpoints
              // to ever find the integral:
              if (abs(endpoint_error / sum) > err)
                 terminate = true;
           }

           if (terminate)
           {
              // We could raise an evaluation_error, but since we likely have some sort of result, just return the last one
              // (ie before the error started going up)
              I1 = I0;
              L1_I1 = L1_I0;
              --k;
              err = last_err + endpoint_error;
              break;
           }
           // Fall through and keep going, assume we've discovered a new feature of f(x)....
        }
        //
        // Termination condition:
        // No more levels are considered once the error is less than the specified tolerance.
        // Note however, that we always go down at least 4 levels, otherwise we risk missing
        // features of interest in f() - imagine for example a function which flatlines, except
        // for a very small "spike". An example would be the incomplete beta integral with large
        // parameters.  We could keep hunting until we find something, but that would handicap
        // integrals which really are zero.... so a compromise then!
        //
        if ((err <= abs(tolerance*L1_I1)) && (k >= 4))
        {
            //
            // A quick sanity check: have we at some point narrowed our boundaries as a result
            // of non-finite values?  If so let's check that the area isn't on an increasing
            // trajectory at our new end point, and increase our error estimate by the last
            // good value as an estimate for what we may have discarded.
            //
            if (max_left_index && (max_left_index < abscissa_row.size() - 1) && (abs(abscissa_row[max_left_index + 1]) > left_min_complement))
            {
               yp = f(-1 - abscissa_row[max_left_index], abscissa_row[max_left_index]) * weight_row[max_left_index];
               ym = f(-1 - abscissa_row[max_left_index - 1], abscissa_row[max_left_index - 1]) * weight_row[max_left_index - 1];
               if (abs(yp) > abs(ym))
               {
                  return policies::raise_evaluation_error(function, "The tanh_sinh quadrature evaluated your function at a singular point and got %1%. Integration bounds were automatically narrowed, but the integral was found to be increasing at the new endpoint.  Please check your function, and consider providing a 2-argument functor.", I1, Policy());
               }
            }
            if (max_right_index && (max_right_index < abscissa_row.size() - 1) && (abs(abscissa_row[max_right_index + 1]) > right_min_complement))
            {
               yp = f(1 + abscissa_row[max_right_index], -abscissa_row[max_right_index]) * weight_row[max_right_index];
               ym = f(1 + abscissa_row[max_right_index - 1], -abscissa_row[max_right_index - 1]) * weight_row[max_right_index - 1];
               if (abs(yp) > abs(ym))
               {
                  return policies::raise_evaluation_error(function, "The tanh_sinh quadrature evaluated your function at a singular point and got %1%. Integration bounds were automatically narrowed, but the integral was found to be increasing at the new endpoint.  Please check your function, and consider providing a 2-argument functor.", I1, Policy());
               }
            }
            err += endpoint_error;
            break;
        }

        if ((truncate_left) && (max_left_position > 1))
           --max_left_position;
        if ((truncate_right) && (max_right_position > 1))
           --max_right_position;

    }
    if (error)
    {
        *error = err;
    }

    if (L1)
    {
        *L1 = L1_I1;
    }

    if (levels)
    {
       *levels = k;
    }

    return I1;
}

template<class Real, class Policy>
void tanh_sinh_detail<Real, Policy>::init(const Real& min_complement, const std::integral_constant<int, 0>&)
{
   using std::tanh;
   using std::sinh;
   using std::asinh;
   using std::atanh;
   using std::ceil;
   using boost::math::constants::half_pi;
   using boost::math::constants::pi;
   using boost::math::constants::two_div_pi;
   using boost::math::lltrunc;

   m_committed_refinements = 4;
   //
   // Initial row length is one step to the right of the abscissa value
   // that would go to the full extent of the requested range, we need this
   // to ensure full precision as otherwise we chop off quite a chunk of the
   // range in *subsequent* rows.
   //
   m_inital_row_length = lltrunc(ceil(t_from_abscissa_complement(min_complement)));
   std::size_t first_complement = 0;
   m_t_max = m_inital_row_length;
   m_t_crossover = t_from_abscissa_complement(Real(0.5f));

   m_abscissas.assign(m_max_refinements + 1, std::vector<Real>());
   m_weights.assign(m_max_refinements + 1, std::vector<Real>());
   m_first_complements.assign(m_max_refinements + 1, 0);
   //
   // First row is special:
   //
   Real h = m_t_max / m_inital_row_length;

   std::vector<Real> temp(m_inital_row_length + 1, Real(0));
   for (std::size_t i = 0; i < m_inital_row_length; ++i)
   {
      Real t = h * i;
      if (t < m_t_crossover)
         ++first_complement;
      temp[i] = t < m_t_crossover ? abscissa_at_t(t) : -abscissa_complement_at_t(t);
   }
   temp[m_inital_row_length] = -abscissa_complement_at_t(m_t_max);
   m_abscissas[0].swap(temp);
   m_first_complements[0] = first_complement;
   temp.assign(m_inital_row_length + 1, Real(0));
   for (std::size_t i = 0; i < m_inital_row_length; ++i)
      temp[i] = weight_at_t(Real(h * i));
   temp[m_inital_row_length] = weight_at_t(m_t_max);
   m_weights[0].swap(temp);

#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
   for (std::size_t row = 1; row <= m_committed_refinements.load(); ++row)
#else
   for (std::size_t row = 1; row <= m_committed_refinements; ++row)
#endif
   {
      h /= 2;
      first_complement = 0;

      for (Real pos = h; pos < m_t_max; pos += 2 * h)
      {
         if (pos < m_t_crossover)
            ++first_complement;
         temp.push_back(pos < m_t_crossover ? abscissa_at_t(pos) : -abscissa_complement_at_t(pos));
      }
      m_abscissas[row].swap(temp);
      m_first_complements[row] = first_complement;
      for (Real pos = h; pos < m_t_max; pos += 2 * h)
         temp.push_back(weight_at_t(pos));
      m_weights[row].swap(temp);
   }
}

#ifdef __GNUC__
// Selective warning disabling via:
// #pragma GCC diagnostic ignored "-Wliteral-range"
// #pragma GCC diagnostic ignored "-Woverflow"
// Seems not to work, so we're left with this:
#pragma GCC system_header
#endif

template<class Real, class Policy>
void tanh_sinh_detail<Real, Policy>::init(const Real& min_complement, const std::integral_constant<int, 1>&)
{
   m_inital_row_length = 4;
   m_abscissas.reserve(m_max_refinements + 1);
   m_weights.reserve(m_max_refinements + 1);
   m_first_complements.reserve(m_max_refinements + 1);
   m_abscissas = {
      { 0.0f, -0.04863203593f, -2.252280754e-05f, -4.294161056e-14f, -1.167648898e-37f, },
      { -0.3257285078f, -0.002485143543f, -1.112433512e-08f, -5.378491591e-23f, },
      { 0.3772097382f, -0.1404309413f, -0.01295943949f, -0.0003117359716f, -7.952628853e-07f, -4.714355182e-11f, -5.415222824e-18f, -2.040300394e-29f, },
      { 0.1943570033f, -0.4608532946f, -0.219392561f, -0.08512073674f, -0.0260331318f, -0.005944493369f, -0.0009348035442f, -9.061530486e-05f, -4.683958779e-06f, -1.072183876e-07f, -8.572949078e-10f, -1.767834693e-12f, -6.374878495e-16f, -2.442937279e-20f, -5.251546473e-26f, -2.789467162e-33f, },
      { 0.09792388529f, 0.2878799327f, 0.4612535439f, -0.3897263425f, -0.2689819652f, -0.1766829945f, -0.1101085972f, -0.06483914248f, -0.03588783578f, -0.01854517332f, -0.008873007558f, -0.003891334562f, -0.001545791232f, -0.0005485655647f, -0.0001711779271f, -4.612899437e-05f, -1.051798518e-05f, -1.982859405e-06f, -3.011058474e-07f, -3.576091908e-08f, -3.212800902e-09f, -2.102671378e-10f, -9.606066479e-12f, -2.919026641e-13f, -5.586116639e-15f, -6.328207135e-17f, -3.956461339e-19f, -1.260975845e-21f, -1.872492175e-24f, -1.170030294e-27f, -2.740986178e-31f, -2.112282721e-35f, },
      { 0.04905596731f, 0.1464179843f, 0.2415663195f, 0.3331422646f, 0.4199521113f, -0.4989866106f, -0.4244155094f, -0.356823241f, -0.2964499949f, -0.2433060914f, -0.1972012587f, -0.1577807536f, -0.1245646024f, -0.09698671849f, -0.07443136593f, -0.05626521395f, -0.04186397729f, -0.0306332671f, -0.02202376481f, -0.01554116883f, -0.01075156891f, -0.007283002803f, -0.004823973845f, -0.003119681872f, -0.001966663685f, -0.001206465701f, -0.0007188880782f, -0.0004152496485f, -0.0002320284004f, -0.0001251349512f, -6.498007492e-05f, -3.240693206e-05f, -1.548009773e-05f, -7.062123337e-06f, -3.06755081e-06f, -1.264528134e-06f, -4.929942806e-07f, -1.811062872e-07f, -6.244592163e-08f, -2.01254968e-08f, -6.035865798e-09f, -1.676638052e-09f, -4.292122274e-10f, -1.007222767e-10f, -2.154466259e-11f, -4.175393124e-12f, -7.284737264e-13f, -1.136386985e-13f, -1.57355351e-14f, -1.91921891e-15f, -2.044959541e-16f, -1.886958307e-17f, -1.493871649e-18f, -1.00469381e-19f, -5.679929178e-21f, -2.669103953e-22f, -1.030178138e-23f, -3.224484876e-25f, -8.0747829e-27f, -1.594654602e-28f, -2.445723975e-30f, -2.865919772e-32f, -2.521682525e-34f, -1.63551444e-36f, },
      { 0.02453976357f, 0.07352512299f, 0.1222291222f, 0.1704679724f, 0.2180634735f, 0.2648450766f, 0.3106517806f, 0.3553338252f, 0.3987541505f, 0.440789599f, 0.4813318461f, -0.4797119493f, -0.4424187717f, -0.4068496464f, -0.3730497919f, -0.3410490083f, -0.3108622749f, -0.2824905325f, -0.2559216165f, -0.2311313132f, -0.2080845076f, -0.1867363915f, -0.1670337061f, -0.148915992f, -0.1323168242f, -0.1171650118f, -0.1033857457f, -0.09090168184f, -0.07963394697f, -0.069503062f, -0.06042977607f, -0.05233580938f, -0.04514450419f, -0.03878138485f, -0.03317462969f, -0.02825545843f, -0.02395843974f, -0.0202217242f, -0.01698720852f, -0.01420063697f, -0.0118116462f, -0.009773759532f, -0.008044336997f, -0.006584486831f, -0.005358944287f, -0.004335923183f, -0.00348694536f, -0.002786652957f, -0.002212608041f, -0.001745083828f, -0.001366851359f, -0.001062965166f, -0.0008205510651f, -0.0006285988591f, -0.0004777623488f, -0.0003601686544f, -0.0002692384802f, -0.0001995185689f, -0.0001465272269f, -0.0001066134524f, -7.682987071e-05f, -5.481938554e-05f, -3.871519214e-05f, -2.705357477e-05f, -1.869872988e-05f, -1.2778718e-05f, -8.631551655e-06f, -5.760372383e-06f, -3.796652834e-06f, -2.470376195e-06f, -1.586189035e-06f, -1.00458931e-06f, -6.272926646e-07f, -3.860114498e-07f, -2.339766676e-07f, -1.396287854e-07f, -8.199520529e-08f, -4.735733554e-08f, -2.688676406e-08f, -1.499692369e-08f, -8.213543901e-09f, -4.414366384e-09f, -2.326763262e-09f, -1.2020165e-09f, -6.082231242e-10f, -3.012456307e-10f, -1.459438845e-10f, -6.911160499e-11f, -3.196678326e-11f, -1.443120992e-11f, -6.353676128e-12f, -2.725950519e-12f, -1.138734572e-12f, -4.627734622e-13f, -1.827990225e-13f, -7.012046738e-14f, -2.609605366e-14f, -9.413361335e-15f, -3.287925695e-15f, -1.11086294e-15f, -3.626602264e-16f, -1.142787653e-16f, -3.471902269e-17f, -1.015781907e-17f, -2.858532825e-18f, -7.727834787e-19f, -2.004423992e-19f, -4.981568376e-20f, -1.184668492e-20f, -2.691984904e-21f, -5.836667442e-22f, -1.205661589e-22f, -2.369109351e-23f, -4.421339462e-24f, -7.823835004e-25f, -1.310533144e-25f, -2.074345013e-26f, -3.096968326e-27f, -4.353200528e-28f, -5.749955668e-29f, -7.122715927e-30f, -8.25783542e-31f, -8.941541007e-32f, -9.02279665e-33f, -8.46603012e-34f, -7.369272807e-35f, -5.936632356e-36f, -4.415277839e-37f, },
      { 0.01227135512f, 0.03680228095f, 0.06129788941f, 0.08573475488f, 0.1100896299f, 0.1343395153f, 0.1584617283f, 0.1824339697f, 0.2062343883f, 0.2298416433f, 0.2532349634f, 0.2763942036f, 0.2992998981f, 0.3219333097f, 0.3442764756f, 0.3663122492f, 0.3880243378f, 0.4093973357f, 0.4304167537f, 0.4510690435f, 0.4713416183f, 0.4912228687f, -0.489297826f, -0.4702300899f, -0.4515825479f, -0.4333628262f, -0.4155775577f, -0.39823239f, -0.3813319982f, -0.3648801001f, -0.3488794756f, -0.3333319877f, -0.318238608f, -0.3035994429f, -0.2894137636f, -0.275680037f, -0.2623959593f, -0.2495584902f, -0.2371638893f, -0.2252077531f, -0.2136850525f, -0.2025901721f, -0.1919169483f, -0.1816587092f, -0.1718083133f, -0.1623581887f, -0.1533003716f, -0.1446265448f, -0.1363280753f, -0.1283960505f, -0.120821315f, -0.113594505f, -0.1067060822f, -0.1001463668f, -0.09390556883f, -0.08797381831f, -0.08234119416f, -0.07699775172f, -0.07193354881f, -0.06713867034f, -0.0626032516f, -0.0583175f, -0.0542717154f, -0.050456309f, -0.0468618209f, -0.0434789361f, -0.0402984993f, -0.03731152826f, -0.0345092259f, -0.03188299107f, -0.02942442821f, -0.02712535566f, -0.02497781299f, -0.02297406711f, -0.02110661737f, -0.01936819969f, -0.01775178968f, -0.01625060483f, -0.01485810598f, -0.01356799776f, -0.01237422849f, -0.01127098916f, -0.01025271192f, -0.009314067826f, -0.008449964034f, -0.007655540506f, -0.006926166179f, -0.006257434709f, -0.005645159804f, -0.005085370197f, -0.004574304294f, -0.004108404533f, -0.003684311494f, -0.003298857801f, -0.002949061834f, -0.002632121306f, -0.002345406714f, -0.002086454715f, -0.001852961444f, -0.001642775797f, -0.001453892723f, -0.001284446528f, -0.001132704236f, -0.0009970590063f, -0.0008760236476f, -0.0007682242321f, -0.0006723938372f, -0.000587366425f, -0.0005120708762f, -0.0004455251889f, -0.0003868308567f, -0.0003351674323f, -0.0002897872882f, -0.0002500105784f, -0.0002152204083f, -0.0001848582177f, -0.0001584193765f, -0.0001354489984f, -0.0001155379702f, -9.8319198e-05f, -8.346406605e-05f, -7.067910812e-05f, -5.970288552e-05f, -5.030306831e-05f, -4.227371392e-05f, -3.543273737e-05f, -2.961956641e-05f, -2.469297451e-05f, -2.052908425e-05f, -1.701953324e-05f, -1.406979453e-05f, -1.159764317e-05f, -9.531760786e-06f, -7.810469566e-06f, -6.380587461e-06f, -5.196396351e-06f, -4.218715072e-06f, -3.414069429e-06f, -2.753951574e-06f, -2.214161365e-06f, -1.774222689e-06f, -1.416868016e-06f, -1.127584838e-06f, -8.942180042e-07f, -7.066223315e-07f, -5.563602711e-07f, -4.364397681e-07f, -3.410878407e-07f, -2.65555767e-07f, -2.059521239e-07f, -1.591002641e-07f, -1.224171449e-07f, -9.381073151e-08f, -7.159348586e-08f, -5.440972705e-08f, -4.117489851e-08f, -3.102500976e-08f, -2.327473287e-08f, -1.738282654e-08f, -1.292373523e-08f, -9.564367214e-09f, -7.045195508e-09f, -5.164949276e-09f, -3.768272997e-09f, -2.735826254e-09f, -1.976380568e-09f, -1.420541964e-09f, -1.015790099e-09f, -7.225780068e-10f, -5.112816947e-10f, -3.598270551e-10f, -2.518536224e-10f, -1.753014775e-10f, -1.213298105e-10f, -8.349395075e-11f, -5.712266027e-11f, -3.8849686e-11f, -2.626342738e-11f, -1.764649978e-11f, -1.178329832e-11f, -7.818680628e-12f, -5.154836404e-12f, -3.376501461e-12f, -2.19707447e-12f, -1.420047367e-12f, -9.115800793e-13f, -5.811306251e-13f, -3.67867911e-13f, -2.312068904e-13f, -1.442617249e-13f, -8.934966665e-14f, -5.492567772e-14f, -3.35078831e-14f, -2.02840764e-14f, -1.218279513e-14f, -7.258853736e-15f, -4.290062787e-15f, -2.514652539e-15f, -1.461687113e-15f, -8.424330958e-16f, -4.81351759e-16f, -2.726317943e-16f, -1.530442297e-16f, -8.513785725e-17f, -4.692795039e-17f, -2.562597595e-17f, -1.386133467e-17f, -7.425750192e-18f, -3.939309402e-18f, -2.069079146e-18f, -1.075828622e-18f, -5.536671017e-19f, -2.819834004e-19f, -1.421006375e-19f, -7.084243878e-20f, -3.493350557e-20f, -1.703597751e-20f, -8.214706044e-21f, -3.915973438e-21f, -1.845149816e-21f, -8.591874581e-22f, -3.953006958e-22f, -1.7966698e-22f, -8.065408821e-23f, -3.575337212e-23f, -1.564782132e-23f, -6.760041764e-24f, -2.882136323e-24f, -1.212436233e-24f, -5.031421831e-25f, -2.059286312e-25f, -8.310787798e-26f, -3.306518068e-26f, -1.296597346e-26f, -5.010091032e-27f, -1.907179385e-27f, -7.150567334e-28f, -2.639906037e-28f, -9.594664542e-29f, -3.432078099e-29f, -1.207985066e-29f, -4.182464721e-30f, -1.424153707e-30f, -4.767833382e-31f, -1.568949178e-31f, -5.073438855e-32f, -1.611691916e-32f, -5.028356694e-33f, -1.540320437e-33f, -4.631392443e-34f, -1.366470225e-34f, -3.955008527e-35f, -1.122591825e-35f, -3.123848743e-36f, -8.519540247e-37f, -2.276473481e-37f, },
   };
   m_weights = {
      { 1.570796327f, 0.2300223945f, 0.0002662005138f, 1.358178427e-12f, 1.001741678e-35f, },
      { 0.9659765794f, 0.01834316699f, 2.143120456e-07f, 2.800315102e-21f, },
      { 1.389614759f, 0.5310782754f, 0.07638574357f, 0.002902517748f, 1.198370136e-05f, 1.163116581e-09f, 2.197079236e-16f, 1.363510331e-27f, },
      { 1.523283719f, 1.193463026f, 0.7374378484f, 0.3604614185f, 0.1374221077f, 0.03917500549f, 0.007742601026f, 0.0009499468043f, 6.248255924e-05f, 1.826332059e-06f, 1.868728227e-08f, 4.937853878e-11f, 2.28349267e-14f, 1.122753143e-18f, 3.09765397e-24f, 2.112123344e-31f, },
      { 1.558773356f, 1.466014427f, 1.29747575f, 1.081634985f, 0.8501728565f, 0.6304051352f, 0.4408332363f, 0.2902406793f, 0.1793244121f, 0.1034321542f, 0.05528968374f, 0.02713351001f, 0.0120835436f, 0.004816298144f, 0.001690873998f, 0.0005133938241f, 0.0001320523413f, 2.811016433e-05f, 4.823718203e-06f, 6.477756604e-07f, 6.583518513e-08f, 4.876006097e-09f, 2.521634792e-10f, 8.675931415e-12f, 1.880207173e-13f, 2.412423038e-15f, 1.708453277e-17f, 6.168256849e-20f, 1.037679724e-22f, 7.345984103e-26f, 1.949783362e-29f, 1.702438776e-33f, },
      { 1.567781431f, 1.543881116f, 1.497226223f, 1.430008355f, 1.345278885f, 1.246701207f, 1.138272243f, 1.024044933f, 0.9078793792f, 0.7932427008f, 0.6830685163f, 0.5796781031f, 0.4847580912f, 0.3993847415f, 0.3240825396f, 0.2589046395f, 0.2035239989f, 0.1573262035f, 0.1194974113f, 0.08910313924f, 0.06515553343f, 0.04666820805f, 0.03269873273f, 0.02237947106f, 0.0149378351f, 0.009707223739f, 0.006130037632f, 0.003754250977f, 0.002225082706f, 0.001273327945f, 0.0007018595157f, 0.0003716669362f, 0.0001885644298f, 9.139081749e-05f, 4.218318384e-05f, 1.84818136e-05f, 7.659575853e-06f, 2.991661588e-06f, 1.096883513e-06f, 3.759541186e-07f, 1.199244278e-07f, 3.543477717e-08f, 9.649888896e-09f, 2.409177326e-09f, 5.48283578e-10f, 1.130605535e-10f, 2.09893354e-11f, 3.484193767e-12f, 5.134127525e-13f, 6.663992283e-14f, 7.556721776e-15f, 7.420993231e-16f, 6.252804845e-17f, 4.475759507e-18f, 2.693120661e-19f, 1.346994157e-20f, 5.533583499e-22f, 1.843546975e-23f, 4.913936871e-25f, 1.032939131e-26f, 1.686277004e-28f, 2.103305749e-30f, 1.96992098e-32f, 1.359989462e-34f, },
      { 1.570042029f, 1.564021404f, 1.55205317f, 1.534281738f, 1.510919723f, 1.482243298f, 1.448586255f, 1.410332971f, 1.367910512f, 1.321780117f, 1.272428346f, 1.22035811f, 1.16607987f, 1.110103194f, 1.05292888f, 0.995041804f, 0.9369046127f, 0.8789523456f, 0.8215880353f, 0.7651792989f, 0.7100559012f, 0.6565082461f, 0.6047867306f, 0.555101878f, 0.5076251588f, 0.4624903981f, 0.4197956684f, 0.3796055694f, 0.3419537959f, 0.3068459094f, 0.2742622297f, 0.2441607779f, 0.2164802091f, 0.1911426841f, 0.1680566379f, 0.1471194133f, 0.1282197336f, 0.111239999f, 0.09605839187f, 0.08255078811f, 0.07059246991f, 0.06005964236f, 0.05083075757f, 0.04278765216f, 0.0358165056f, 0.02980862812f, 0.02466108731f, 0.02027718382f, 0.01656678625f, 0.01344653661f, 0.01083993717f, 0.00867733075f, 0.006895785969f, 0.005438899798f, 0.004256529599f, 0.003304466994f, 0.002544065768f, 0.001941835776f, 0.00146901436f, 0.001101126113f, 0.0008175410133f, 0.0006010398799f, 0.0004373949562f, 0.0003149720919f, 0.0002243596521f, 0.000158027884f, 0.0001100211285f, 7.568399659e-05f, 5.142149745e-05f, 3.449212476e-05f, 2.283211811e-05f, 1.490851403e-05f, 9.598194128e-06f, 6.089910032e-06f, 3.806198326e-06f, 2.342166721e-06f, 1.418306716e-06f, 8.447375638e-07f, 4.94582887e-07f, 2.844992366e-07f, 1.606939458e-07f, 8.907139514e-08f, 4.84209502e-08f, 2.579956823e-08f, 1.346464552e-08f, 6.878461096e-09f, 3.437185674e-09f, 1.678889768e-09f, 8.009978448e-10f, 3.729950184e-10f, 1.693945779e-10f, 7.496739757e-11f, 3.230446433e-11f, 1.354251291e-11f, 5.518236947e-12f, 2.18359221e-12f, 8.383128961e-13f, 3.119497729e-13f, 1.124020896e-13f, 3.917679451e-14f, 1.319434223e-14f, 4.289196222e-15f, 1.344322288e-15f, 4.057557702e-16f, 1.177981213e-16f, 3.285386163e-17f, 8.791316559e-18f, 2.25407483e-18f, 5.530176913e-19f, 1.296452714e-19f, 2.899964556e-20f, 6.180143249e-21f, 1.252867643e-21f, 2.412250547e-22f, 4.4039067e-23f, 7.610577808e-24f, 1.242805165e-24f, 1.91431069e-25f, 2.776125103e-26f, 3.783124073e-27f, 4.834910155e-28f, 5.783178697e-29f, 6.460575703e-30f, 6.72603739e-31f, 6.511153451e-32f, 5.847409075e-33f, 4.860046055e-34f, 3.72923953e-35f, },
      { 1.570607717f, 1.569099695f, 1.566088239f, 1.561582493f, 1.555596115f, 1.548147191f, 1.539258145f, 1.528955608f, 1.517270275f, 1.504236738f, 1.489893298f, 1.474281762f, 1.457447221f, 1.439437815f, 1.420304486f, 1.400100716f, 1.378882264f, 1.35670689f, 1.333634075f, 1.309724744f, 1.285040985f, 1.259645765f, 1.233602657f, 1.206975567f, 1.179828472f, 1.152225159f, 1.124228984f, 1.09590263f, 1.067307886f, 1.038505436f, 1.00955466f, 0.9805134517f, 0.951438051f, 0.9223828892f, 0.8934004523f, 0.8645411596f, 0.8358532563f, 0.807382723f, 0.7791731997f, 0.7512659245f, 0.723699687f, 0.6965107951f, 0.6697330554f, 0.6433977657f, 0.6175337199f, 0.5921672237f, 0.5673221206f, 0.5430198278f, 0.5192793805f, 0.4961174844f, 0.4735485755f, 0.4515848861f, 0.4302365164f, 0.4095115109f, 0.3894159397f, 0.3699539819f, 0.3511280132f, 0.3329386948f, 0.3153850641f, 0.2984646265f, 0.2821734476f, 0.2665062456f, 0.2514564831f, 0.2370164583f, 0.2231773949f, 0.2099295305f, 0.1972622032f, 0.1851639366f, 0.1736225217f, 0.1626250975f, 0.1521582278f, 0.1422079761f, 0.1327599774f, 0.1237995069f, 0.1153115463f, 0.1072808458f, 0.09969198461f, 0.09252942711f, 0.08577757654f, 0.0794208254f, 0.07344360286f, 0.06783041903f, 0.06256590638f, 0.05763485811f, 0.05302226366f, 0.04871334138f, 0.04469356846f, 0.04094870813f, 0.0374648342f, 0.03422835312f, 0.03122602351f, 0.02844497325f, 0.02587271434f, 0.02349715546f, 0.02130661237f, 0.01928981624f, 0.01743592007f, 0.01573450311f, 0.01417557353f, 0.01274956936f, 0.01144735783f, 0.01026023317f, 0.009179912924f, 0.008198533005f, 0.007308641451f, 0.006503191044f, 0.005775530877f, 0.005119396961f, 0.004528901979f, 0.003998524263f, 0.00352309611f, 0.003097791523f, 0.002718113458f, 0.002379880688f, 0.002079214354f, 0.001812524299f, 0.001576495262f, 0.00136807301f, 0.001184450486f, 0.001023054043f, 0.0008815298242f, 0.0007577303578f, 0.0006497014187f, 0.0005556692074f, 0.000474027894f, 0.0004033275645f, 0.0003422626065f, 0.0002896605611f, 0.0002444714673f, 0.0002057577147f, 0.0001726844199f, 0.0001445103343f, 0.0001205792873f, 0.0001003121646f, 8.319941724e-05f, 6.879409311e-05f, 5.670537985e-05f, 4.659264463e-05f, 3.815995412e-05f, 3.115105568e-05f, 2.534479897e-05f, 2.055097594e-05f, 1.660655598e-05f, 1.337229228e-05f, 1.072967541e-05f, 8.578209354e-06f, 6.832986277e-06f, 5.422535892e-06f, 4.286926494e-06f, 3.376095235e-06f, 2.648386225e-06f, 2.069276126e-06f, 1.610268009e-06f, 1.247935512e-06f, 9.631005212e-07f, 7.401289349e-07f, 5.66330284e-07f, 4.314482559e-07f, 3.272303733e-07f, 2.470662451e-07f, 1.856849137e-07f, 1.3890287e-07f, 1.034152804e-07f, 7.662387397e-08f, 5.649576387e-08f, 4.144823356e-08f, 3.025519646e-08f, 2.197164892e-08f, 1.587297809e-08f, 1.140646555e-08f, 8.152746483e-09f, 5.795349573e-09f, 4.096757914e-09f, 2.879701346e-09f, 2.012621022e-09f, 1.398441431e-09f, 9.659485186e-10f, 6.632086347e-10f, 4.52575761e-10f, 3.069270208e-10f, 2.068420354e-10f, 1.385028753e-10f, 9.214056423e-11f, 6.089338706e-11f, 3.997338952e-11f, 2.60619605e-11f, 1.687451934e-11f, 1.084916183e-11f, 6.925528015e-12f, 4.38886519e-12f, 2.760858767e-12f, 1.723764404e-12f, 1.068075044e-12f, 6.56694435e-13f, 4.00598538e-13f, 2.424296605e-13f, 1.455249916e-13f, 8.663812725e-14f, 5.114974901e-14f, 2.99421776e-14f, 1.737681695e-14f, 9.99642401e-15f, 5.699626666e-15f, 3.220432513e-15f, 1.802958964e-15f, 9.999957344e-16f, 5.493978397e-16f, 2.989420886e-16f, 1.610765424e-16f, 8.593209748e-17f, 4.538246827e-17f, 2.372253167e-17f, 1.227167167e-17f, 6.281229049e-18f, 3.180614714e-18f, 1.593049257e-18f, 7.890855159e-19f, 3.864733103e-19f, 1.87127733e-19f, 8.955739455e-20f, 4.235742852e-20f, 1.979436202e-20f, 9.138078558e-21f, 4.166641158e-21f, 1.876075055e-21f, 8.339901949e-22f, 3.659575236e-22f, 1.584785218e-22f, 6.771575694e-23f, 2.854281708e-23f, 1.186583858e-23f, 4.864069936e-24f, 1.965643419e-24f, 7.829165625e-25f, 3.072789229e-25f, 1.188107615e-25f, 4.524619749e-26f, 1.696710187e-26f, 6.263641003e-27f, 2.275790793e-27f, 8.136077716e-28f, 2.861306549e-28f, 9.896184197e-29f, 3.365200893e-29f, 1.124807055e-29f, 3.694460433e-30f, 1.192093301e-30f, 3.777757876e-31f, 1.175436379e-31f, 3.589879078e-32f, 1.075842686e-32f, 3.162835126e-33f, 9.118674189e-34f, 2.577393168e-34f, 7.139829504e-35f, 1.937828921e-35f, },
   };
   m_first_complements = {
      1, 0, 1, 1, 3, 5, 11, 22,
   };
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
   m_committed_refinements = static_cast<boost::math::detail::atomic_unsigned_integer_type>(m_abscissas.size() - 1);
#else
   m_committed_refinements = m_abscissas.size() - 1;
#endif

   if (m_max_refinements >= m_abscissas.size())
   {
      m_abscissas.resize(m_max_refinements + 1);
      m_weights.resize(m_max_refinements + 1);
      m_first_complements.resize(m_max_refinements + 1);
   }
   else
   {
      m_max_refinements = m_abscissas.size() - 1;
   }
   m_t_max = static_cast<Real>(m_inital_row_length);
   m_t_crossover = t_from_abscissa_complement(Real(0.5f));

   prune_to_min_complement(min_complement);

}

template<class Real, class Policy>
void tanh_sinh_detail<Real, Policy>::init(const Real& min_complement, const std::integral_constant<int, 2>&)
{
   m_inital_row_length = 6;
   m_abscissas.reserve(m_max_refinements + 1);
   m_weights.reserve(m_max_refinements + 1);
   m_first_complements.reserve(m_max_refinements + 1);
   m_abscissas = {
      { 0, -0.0486320359272530543, -2.25228075384071351e-05, -4.29416105587824078e-14, -1.16764889750986093e-37, -1.14795299162938991e-101, -1.22565381365848647e-275, },
      { -0.325728507751564174, -0.00248514354277561317, -1.1124335118015332e-08, -5.37849159139368877e-23, -7.9430213192221161e-62, -2.38712281858192662e-167, },
      { 0.377209738164034174, -0.140430941310103365, -0.0129594394926231083, -0.000311735971646790949, -7.95262885287335534e-07, -4.71435518232225751e-11, -5.41522282380723085e-18, -2.04030039435249433e-29, -3.05969013536445003e-48, -2.86219841925875116e-79, -2.00703306915332264e-130, -9.24799327395281672e-215, },
      { 0.194357003324935432, -0.460853294612032231, -0.219392561016799701, -0.0851207367354253891, -0.0260331318043225514, -0.00594449336859785671, -0.000934803544214153575, -9.06153048560001614e-05, -4.68395877947156992e-06, -1.07218387581618094e-07, -8.57294907821677329e-10, -1.76783469283872037e-12, -6.37487849504443965e-16, -2.44293727908521732e-20, -5.25154647301957486e-26, -2.78946716228941774e-33, -1.2781108980938045e-42, -1.3082723368531808e-54, -5.27997812261025438e-70, -9.06191040541810149e-90, -3.79031527880246479e-115, -9.83017699648704421e-148, -1.41780355464723683e-189, -2.67381847626328947e-243, },
      { 0.0979238852878323333, 0.287879932742715915, 0.461253543939585704, -0.389726342499361055, -0.268981965207438489, -0.17668299449359763, -0.110108597215739802, -0.0648391424780153168, -0.0358878357764527081, -0.01854517332266483, -0.00887300755830119777, -0.00389133456249145746, -0.00154579123230226249, -0.000548565564725394158, -0.00017117792712505834, -4.61289943720392527e-05, -1.05179851814963885e-05, -1.98285940456792063e-06, -3.01105847388779655e-07, -3.57609190846577144e-08, -3.2128009016957605e-09, -2.10267137764114881e-10, -9.606066478508465e-12, -2.91902664101751393e-13, -5.58611663883822752e-15, -6.32820713468316824e-17, -3.95646133946764783e-19, -1.26097584471654847e-21, -1.87249217452760828e-24, -1.17003029391311908e-27, -2.74098617835124095e-31, -2.11228272144761198e-35, -4.61722394825950171e-40, -2.42036219762624008e-45, -2.5155618638019452e-51, -4.17863906970219404e-58, -8.6898180082116723e-66, -1.71543577650024789e-74, -2.34930324121953381e-84, -1.56454323889425122e-95, -3.38734020478683648e-108, -1.51081664710078465e-122, -8.27800009567041668e-139, -3.10160298195808184e-157, -4.09173469287244044e-178, -8.9581681449485487e-202, -1.3878968774299745e-228, -5.79257421293503894e-259, },
      { 0.0490559673050778863, 0.146417984290587941, 0.241566319538883658, 0.333142264577638092, 0.419952111278447158, -0.498986610620690898, -0.42441550936484834, -0.356823241014795299, -0.296449994852857984, -0.243306091366270051, -0.197201258656758734, -0.157780753649243136, -0.124564602369591322, -0.0969867184864261294, -0.0744313659313873335, -0.0562652139472428431, -0.0418639772897863099, -0.0306332671030826648, -0.022023764813335027, -0.0155411688325691691, -0.010751568909866104, -0.00728300280317271462, -0.00482397384467264574, -0.00311968187180812628, -0.00196666368456624598, -0.00120646570119410071, -0.000718888078208044591, -0.000415249648482412683, -0.000232028400439164939, -0.000125134951219653515, -6.49800749175763138e-05, -3.24069320565402357e-05, -1.5480097729175578e-05, -7.06212333711435226e-06, -3.06755080964249425e-06, -1.26452813409045827e-06, -4.92994280563111161e-07, -1.81106287232991369e-07, -6.24459216262249756e-08, -2.01254967982457957e-08, -6.03586579835227638e-09, -1.67663805174219429e-09, -4.29212227386388064e-10, -1.00722276743730848e-10, -2.15446625890420237e-11, -4.1753931241965133e-12, -7.28473726428773404e-13, -1.13638698543921915e-13, -1.57355350979265302e-14, -1.91921890971076214e-15, -2.04495954084673556e-16, -1.88695830672934097e-17, -1.4938716488357229e-18, -1.00469381000375765e-19, -5.6799291780731967e-21, -2.66910395261351514e-22, -1.03017813818941147e-23, -3.22448487639124621e-25, -8.07478290042168868e-27, -1.59465460199328555e-28, -2.44572397527528259e-30, -2.86591977190786557e-32, -2.52168252518289941e-34, -1.63551444031007274e-36, -7.66665909906783572e-39, -2.54357712881376935e-41, -5.84094722807164823e-44, -9.06586956479397026e-47, -9.27356981605885259e-50, -6.08571480420522776e-53, -2.48980897367221466e-56, -6.15986208631705443e-60, -8.92141270722267119e-64, -7.30722432600404514e-68, -3.26256086255138585e-72, -7.6357851449836225e-77, -8.9855174690288197e-82, -5.085871851548729e-87, -1.3207396001984761e-92, -1.49648649356105213e-98, -7.01292336693108804e-105, -1.2839990161964175e-111, -8.64456838410520022e-119, -2.00636948968315553e-126, -1.49877407961844478e-134, -3.34937621495896108e-143, -2.07152943814344815e-152, -3.26388325933199651e-162, -1.19947375988972346e-172, -9.36020855840271015e-184, -1.40350944280078664e-195, -3.63561076394912477e-208, -1.45274399574122646e-221, -7.93772358209223086e-236, -5.21635362297544998e-251, -3.59647437525778163e-267, },
      { 0.0245397635746491604, 0.0735251229856712945, 0.122229122201557642, 0.170467972382010518, 0.218063473469712005, 0.26484507658344795, 0.310651780552845961, 0.355333825165074533, 0.398754150467237756, 0.440789599033900866, 0.481331846116905044, -0.47971194930876984, -0.442418771739221769, -0.406849646408046841, -0.373049791948957121, -0.341049008256649876, -0.310862274938332328, -0.282490532512675873, -0.255921616452652601, -0.231131313231753415, -0.208084507623857886, -0.186736391497026148, -0.167033706080589124, -0.148915992012151267, -0.132316824224354013, -0.117165011755331045, -0.103385745719923974, -0.0909016818369795649, -0.0796339469680471977, -0.0695030620028465937, -0.0604297760667252446, -0.0523358093848469027, -0.0451445041949773146, -0.0387813848488835925, -0.0331746296876441472, -0.0282554584345126911, -0.0239584397434232607, -0.0202217241993842374, -0.0169872085188988944, -0.014200636974716564, -0.0118116461992573576, -0.00977375953247225306, -0.00804433699732238438, -0.006584486830735961, -0.00535894428748880328, -0.00433592318304683035, -0.00348694535974622683, -0.00278665295653129776, -0.00221260804109346917, -0.00174508382800370656, -0.00136685135932252238, -0.00106296516648782627, -0.000820551065114082842, -0.000628598859062313104, -0.000477762348782795776, -0.00036016865439963481, -0.00026923848019151737, -0.000199518568861613698, -0.000146527226888588285, -0.000106613452407435744, -7.68298707106713057e-05, -5.48193855413069104e-05, -3.87151921433338684e-05, -2.70535747677634357e-05, -1.86987298792732083e-05, -1.27787179993718896e-05, -8.63155165512655592e-06, -5.76037238336521799e-06, -3.79665283382324692e-06, -2.47037619483206967e-06, -1.5861890352645758e-06, -1.00458931003037651e-06, -6.27292664630529592e-07, -3.86011449757251023e-07, -2.33976667566879521e-07, -1.39628785400592289e-07, -8.19952052894377486e-08, -4.7357335538151974e-08, -2.68867640563769168e-08, -1.49969236882666325e-08, -8.21354390092801904e-09, -4.4143663841612464e-09, -2.32676326210046123e-09, -1.20201649960114077e-09, -6.08223124167786151e-10, -3.01245630748323226e-10, -1.45943884500128571e-10, -6.91116049852310787e-11, -3.19667832644453697e-11, -1.44312099240426525e-11, -6.35367612849118143e-12, -2.72595051887251972e-12, -1.13873457224078497e-12, -4.62773462176931343e-13, -1.82799022484311048e-13, -7.01204673849056861e-14, -2.60960536613795464e-14, -9.41336133528885558e-15, -3.28792569493649342e-15, -1.11086294018218984e-15, -3.6266022642032122e-16, -1.14278765250526699e-16, -3.47190226924949368e-17, -1.01578190683051246e-17, -2.85853282450814817e-18, -7.72783478738925526e-19, -2.00442399221751722e-19, -4.98156837646991149e-20, -1.18466849242234024e-20, -2.69198490361039057e-21, -5.83666744193394141e-22, -1.20566158865487235e-22, -2.36910935080780677e-23, -4.42133946172800178e-24, -7.82383500410868582e-25, -1.31053314412547815e-25, -2.07434501307351507e-26, -3.09696832555074958e-27, -4.35320052819675598e-28, -5.74995566807039229e-29, -7.12271592663817274e-30, -8.25783542035580458e-31, -8.94154100662495554e-32, -9.02279665003086697e-33, -8.46603011953159718e-34, -7.36927280656452982e-35, -5.93663235600887498e-36, -4.41527783869469595e-37, -3.02396013586353149e-38, -1.90220379165770488e-39, -1.09604332860345779e-40, -5.76869559800406948e-42, -2.76539938173771022e-43, -1.20387095285625741e-44, -4.74476232533549872e-46, -1.68767912810533091e-47, -5.39996699829972848e-49, -1.54902404944808261e-50, -3.96994725525613498e-52, -9.05767492652393586e-54, -1.83295043348369314e-55, -3.27742233239736439e-57, -5.15768715308393636e-59, -7.114715221408198e-61, -8.56688901712102493e-63, -8.96555855421136186e-65, -8.11869379690547438e-67, -6.33219388590773392e-69, -4.23372634246032809e-71, -2.41472604643767902e-73, -1.16895559109482145e-75, -4.77806689855264461e-78, -1.6402035716952159e-80, -4.70248914876421051e-83, -1.11959046700779807e-85, -2.2005433507330025e-88, -3.54892402406326365e-91, -4.66694093688365186e-94, -4.97190667459303363e-97, -4.26251200323736193e-100, -2.92055254102694012e-103, -1.58792835205206735e-106, -6.80103287742115697e-110, -2.2772324163917692e-113, -5.91472228162959818e-117, -1.18209982177688408e-120, -1.80282622739513989e-124, -2.08020413351101242e-128, -1.79996701563969356e-132, -1.15734196606825454e-136, -5.477766654144099e-141, -1.8900190812332373e-145, -4.70643183557466755e-150, -8.37112913643764166e-155, -1.05221390088176585e-159, -9.2441193400981258e-165, -5.61216501813263789e-170, -2.32703984243702941e-175, -6.5107211867430156e-181, -1.2138997008699256e-186, -1.48891299935269587e-192, -1.18553781867001071e-198, -6.04455846579323287e-205, -1.94567931886735567e-211, -3.8966805747095114e-218, -4.78292200095461437e-225, -3.54255959173241182e-232, -1.55812601911741406e-239, -4.00280716389675087e-247, -5.90460003827621713e-255, -4.91396180352002352e-263, -2.26567446980557423e-271, },
      { 0.012271355118082202, 0.036802280950025085, 0.0612978894136599758, 0.0857347548776510558, 0.110089629932628013, 0.134339515287672237, 0.158461728289299504, 0.18243396969028915, 0.206234388311028769, 0.229841643254360754, 0.253234963356000236, 0.276394203576178614, 0.299299898063960473, 0.321933309653369166, 0.344276475579704919, 0.366312249234904082, 0.388024337812117748, 0.409397335721529489, 0.430416753691437064, 0.451069043500451995, 0.471341618317998466, 0.491222868660811487, -0.489297825997441858, -0.470230089898225442, -0.451582547860207645, -0.433362826214981142, -0.415577557677336058, -0.398232389990366541, -0.381331998167271939, -0.364880100135578164, -0.34887947557569577, -0.333331987734262319, -0.318238607983572687, -0.303599442891540793, -0.289413763561994213, -0.27568003700259252, -0.262395959277174755, -0.249558490200759565, -0.237163889338607631, -0.22520775307556463, -0.213685052528180394, -0.202590172079687753, -0.191916948326661462, -0.181658709235902172, -0.171808313320642901, -0.162358188656399975, -0.153300371568536304, -0.144626544835728469, -0.136328075265894765, -0.128396050513622281, -0.12082131502061063, -0.113594504973021298, -0.106706082181789018, -0.100146366803829869, -0.0939055688335957994, -0.0879738183055131808, -0.0823411941584506602, -0.0769977517234455965, -0.0719335488054448738, -0.0671386703387599459, -0.0626032516042808619, -0.0583175000042306645, -0.0542717153973677599, -0.0504563090040636404, -0.0468618208966061973, -0.0434789360954190696, -0.0402984992966633908, -0.03731152826092173, -0.0345092258963795269, -0.0318829910731436838, -0.0294244282080995797, -0.0271253556620361184, -0.0249778129926936505, -0.0229740671089420425, -0.0211066173725059709, -0.019368199694551295, -0.0177517896750589349, -0.0162506048332687547, -0.0148581059776019712, -0.0135679977633915537, -0.0123742284864893156, -0.0112709891603976381, -0.0102527119240129037, -0.00931406782638492783, -0.00844996403410826525, -0.00765554050608182456, -0.00692616617941556874, -0.00625743470923838919, -0.00564515980407910996, -0.0050853701973611284, -0.00457430429437719331, -0.00410840453289979854, -0.00368431149433907478, -0.00329885780108739487, -0.00294906183439087047, -0.00263212130576456563, -0.0023454067136221407, -0.00208645471542297342, -0.0018529614442515744, -0.00164277579733623578, -0.00145389272258728331, -0.00128444652779115701, -0.0011327042356361947, -0.000997059006271141487, -0.000876023647610171022, -0.000768224232101148019, -0.00067239383717004608, -0.000587366425047420931, -0.000512070876176692876, -0.0004455251889032602, -0.00038683085665314189, -0.00033516743233532264, -0.000289787288248965581, -0.000250010578351141274, -0.000215220408348834122, -0.000184858217726938954, -0.000158419376517931266, -0.000135448998364931342, -0.000115537970233795639, -9.83191979971305535e-05, -8.3464066048770978e-05, -7.06791081158112189e-05, -5.97028855206253867e-05, -5.03030683106995953e-05, -4.22737139220181204e-05, -3.54327373739233618e-05, -2.96195664107243162e-05, -2.46929745079623587e-05, -2.05290842484436199e-05, -1.7019533243403755e-05, -1.40697945252075011e-05, -1.1597643166806137e-05, -9.53176078612253345e-06, -7.8104695663539219e-06, -6.38058746110851941e-06, -5.19639635112362769e-06, -4.21871507150727724e-06, -3.41406942921033546e-06, -2.75395157382161437e-06, -2.21416136477003485e-06, -1.77422268862384164e-06, -1.41686801551279912e-06, -1.12758483806512337e-06, -8.94218004200267166e-07, -7.066223315196142e-07, -5.56360271123123226e-07, -4.36439768090839962e-07, -3.41087840680835234e-07, -2.65555767043837624e-07, -2.0595212394180991e-07, -1.59100264054130123e-07, -1.22417144893368773e-07, -9.38107315112986677e-08, -7.15934858568207488e-08, -5.44097270488153164e-08, -4.11748985097450346e-08, -3.10250097588474058e-08, -2.32747328650517169e-08, -1.7382826537542006e-08, -1.29237352250289432e-08, -9.5643672141012476e-09, -7.04519550806302257e-09, -5.1649492757248395e-09, -3.76827299743367321e-09, -2.73582625416470426e-09, -1.97638056812945901e-09, -1.42054196354815056e-09, -1.01579009878587221e-09, -7.22578006796989773e-10, -5.11281694668026368e-10, -3.59827055120401645e-10, -2.51853622445542102e-10, -1.75301477503092556e-10, -1.21329810513247054e-10, -8.34939507523206686e-11, -5.71226602674819584e-11, -3.88496860027937659e-11, -2.62634273799497214e-11, -1.76464997763789762e-11, -1.17832983201206309e-11, -7.81868062832536264e-12, -5.15483640427513504e-12, -3.37650146080002947e-12, -2.19707446995923426e-12, -1.42004736695181269e-12, -9.11580079335777287e-13, -5.81130625055868083e-13, -3.6786791102075634e-13, -2.31206890357644477e-13, -1.44261724859223088e-13, -8.93496666512989392e-14, -5.49256777212567905e-14, -3.35078830951587706e-14, -2.02840764024440399e-14, -1.21827951289796445e-14, -7.25885373620523418e-15, -4.29006278660366691e-15, -2.51465253948077134e-15, -1.46168711284753358e-15, -8.42433095837469831e-16, -4.81351759040153145e-16, -2.7263179430684055e-16, -1.53044229739659322e-16, -8.5137857250384434e-17, -4.69279503896114271e-17, -2.56259759503200895e-17, -1.38613346679503967e-17, -7.42575019213846423e-18, -3.93930940244456018e-18, -2.06907914597742282e-18, -1.07582862164931383e-18, -5.53667101730119995e-19, -2.81983400378589843e-19, -1.42100637503135938e-19, -7.08424387814173802e-20, -3.49335055730950445e-20, -1.70359775108200994e-20, -8.21470604421218778e-21, -3.91597343841569897e-21, -1.84514981562718714e-21, -8.59187458062354013e-22, -3.95300695770657649e-22, -1.79666979951342614e-22, -8.06540882083892199e-23, -3.57533721226205606e-23, -1.5647821323387364e-23, -6.76004176421736964e-24, -2.88213632330255471e-24, -1.21243623290624178e-24, -5.03142183078805999e-25, -2.05928631224500678e-25, -8.31078779763998548e-26, -3.30651806761459413e-26, -1.29659734624673994e-26, -5.01009103239855231e-27, -1.90717938473556322e-27, -7.15056733398109736e-28, -2.63990603736826823e-28, -9.59466454215915903e-29, -3.43207809913106496e-29, -1.20798506630670813e-29, -4.18246472148817528e-30, -1.42415370668448176e-30, -4.76783338169881336e-31, -1.56894917819580623e-31, -5.07343885484964435e-32, -1.61169191609392457e-32, -5.02835669425437807e-33, -1.54032043714714164e-33, -4.63139244348179017e-34, -1.36647022505741948e-34, -3.95500852699898458e-35, -1.12259182536440082e-35, -3.12384874336168341e-36, -8.51954024720649026e-37, -2.27647348137758297e-37, -5.95784738583498423e-38, -1.5267023004586064e-38, -3.82924523738836913e-39, -9.39764841233127425e-40, -2.25591870233912475e-40, -5.29510577400702735e-41, -1.21484005139195805e-41, -2.72333290575638595e-42, -5.96294747245500166e-43, -1.27479045632084017e-43, -2.65993246477400726e-44, -5.41489757055003388e-45, -1.07505067837929222e-45, -2.0807277332937164e-46, -3.9244163140436388e-47, -7.20993781004693903e-48, -1.28974823309181237e-48, -2.24549897001720529e-49, -3.80338177519323738e-50, -6.2645207667921989e-51, -1.00294108349246457e-51, -1.5600495091009387e-52, -2.35656502929127819e-53, -3.45539861530099585e-54, -4.91576367443963002e-55, -6.78189690060837789e-56, -9.06919402024436437e-57, -1.17497924435643381e-57, -1.47407303120625938e-58, -1.78984329859422798e-59, -2.1022998433631042e-60, -2.3874311701754331e-61, -2.61994971132693976e-62, -2.77681312539698556e-63, -2.84088709628858008e-64, -2.80396888834653826e-65, -2.66844816022605403e-66, -2.44715408087085735e-67, -2.16136193986109549e-68, -1.83738434241422263e-69, -1.50251039384538056e-70, -1.18117553050091616e-71, -8.92115779194300367e-73, -6.469398720148732e-74, -4.50157318845664738e-75, -3.00358422099143309e-76, -1.92045159974146096e-77, -1.17588108496782645e-78, -6.89007215715517382e-80, -3.86085633874422728e-81, -2.06746164750454894e-82, -1.05724310335546675e-83, -5.159178839138868e-85, -2.40068073513892583e-86, -1.06441521301761484e-87, -4.49345292323654518e-89, -1.80470487227992129e-90, -6.89045955067325497e-92, -2.49896371014878691e-93, -8.60180884698632756e-95, -2.8078924862967821e-96, -8.68498675197961289e-98, -2.54323961092475611e-99, -7.04466689786197244e-101, -1.84420182558676709e-102, -4.55874167252535593e-104, -1.06310885552045921e-105, -2.33672817489640356e-107, -4.83650426311308612e-109, -9.41752356208775621e-111, -1.72347894616941773e-112, -2.96152337973418694e-114, -4.77346095185853133e-116, -7.20979528228986404e-118, -1.0193874335777637e-119, -1.34781669991634323e-121, -1.66471130073245645e-123, -1.91865839504763309e-125, -2.06126480152885691e-127, -2.06189537886634895e-129, -1.91826195552363599e-131, -1.65791287785192226e-133, -1.32960990700944959e-135, -9.8828871549374804e-138, -6.80018519366801829e-140, -4.32620427633799334e-142, -2.54159552852297217e-144, -1.37712796594958211e-146, -6.87317396243845786e-149, -3.15568448417183025e-151, -1.33110460150070095e-153, -5.15149580050109184e-156, -1.82670376280564422e-158, -5.92677285398580171e-161, -1.75701790578206368e-163, -4.75252736446443541e-166, -1.17121511806343118e-168, -2.62588387222030548e-171, -5.34803615842019053e-174, -9.87952606199786798e-177, -1.65285015577763281e-179, -2.50039475650869583e-182, -3.41485961919321622e-185, -4.20365360545913323e-188, -4.65650825702166728e-191, -4.63395309965355254e-194, -4.13587819260496962e-197, -3.30494472949500722e-200, -2.36039562085899995e-203, -1.5040470140688354e-206, -8.53520219585080464e-210, -4.3057568200632215e-213, -1.92736281851211248e-216, -7.64078684884150441e-220, -2.6775820096831777e-223, -8.27816250430073493e-227, -2.2534850368391124e-230, -5.39056859020767355e-234, -1.13080972768156503e-237, -2.07597218137054105e-241, -3.32826876045839753e-245, -4.65000671354536966e-249, -5.64918736922395969e-253, -5.95469666263319233e-257, -5.43379904255349279e-261, -4.28283635376386341e-265, -2.90898542515205486e-269, -1.69869798350729535e-273, },
   };
   m_weights = {
      { 1.57079632679489662, 0.230022394514788685, 0.000266200513752716909, 1.35817842745390908e-12, 1.0017416784066253e-35, 2.6763080920617461e-99, 7.76707068863340629e-273, },
      { 0.965976579412301148, 0.0183431669899278421, 2.14312045569430394e-07, 2.80031510197758896e-21, 1.12327053454869188e-59, 9.17532687500178413e-165, },
      { 1.38961475924725632, 0.531078275428053975, 0.0763857435708323042, 0.00290251774790131359, 1.198370136317072e-05, 1.16311658142557828e-09, 2.19707923629797992e-16, 1.36351033076376154e-27, 3.3700568540419265e-46, 5.19697838008985521e-77, 6.00803417057135015e-128, 4.5642040563555991e-212, },
      { 1.52328371863470521, 1.19346302584915696, 0.737437848361547841, 0.360461418469343674, 0.137422107733167723, 0.0391750054936007791, 0.00774260102606424071, 0.000949946804283468717, 6.24825592407440829e-05, 1.82633205937106597e-06, 1.86872822687364101e-08, 4.9378538776631927e-11, 2.2834926702613954e-14, 1.12275314281815515e-18, 3.09765397011735437e-24, 2.11212334353722559e-31, 1.24241475706160524e-40, 1.63277073317994932e-52, 8.4606887310962138e-68, 1.86444920795886503e-87, 1.00131284686664302e-112, 3.33444354118689902e-145, 6.17515762254877653e-187, 1.49532400222570759e-240, },
      { 1.55877335553333015, 1.46601442671696578, 1.297475750424978, 1.08163498549007041, 0.850172856456620069, 0.630405135164743691, 0.440833236273858237, 0.290240679312454185, 0.179324412110728293, 0.103432154223332901, 0.0552896837422405838, 0.0271335100137120032, 0.0120835435991579535, 0.00481629814392846302, 0.00169087399814263965, 0.00051339382406790336, 0.000132052341256099749, 2.81101643279401347e-05, 4.82371820326155021e-06, 6.47775660359297199e-07, 6.58351851271833967e-08, 4.87600609742406259e-09, 2.52163479185301486e-10, 8.67593141497960465e-12, 1.88020717307506498e-13, 2.41242303843087864e-15, 1.70845327724057017e-17, 6.16825684907623826e-20, 1.03767972385287062e-22, 7.34598410322269356e-26, 1.94978336243351748e-29, 1.70243877612575472e-33, 4.21648637094842789e-38, 2.50442771162752843e-43, 2.94936014574619331e-49, 5.55133235696537106e-56, 1.30811651202108216e-63, 2.92608244638239753e-72, 4.54077346699978241e-82, 3.42656356920869877e-93, 8.406435948883177e-106, 4.24861926881986944e-120, 2.63782082635045215e-136, 1.11992914558412798e-154, 1.67415937867108893e-175, 4.15330608101949222e-199, 7.29151266090566043e-226, 3.44840283588682197e-256, },
      { 1.56778143130722186, 1.54388111617695922, 1.49722622254103629, 1.43000835487229967, 1.34527888476625166, 1.2467012074518577, 1.13827224337630537, 1.02404493311181145, 0.907879379154895317, 0.793242700820516718, 0.683068516344263755, 0.579678103087787647, 0.484758091214755393, 0.399384741525717135, 0.324082539611528904, 0.258904639514053516, 0.203523998858601745, 0.15732620348436615, 0.119497411288695924, 0.0891031392409414628, 0.065155533432536205, 0.0466682080548466136, 0.0326987327266090311, 0.0223794710636484765, 0.0149378350960501297, 0.00970722373939168927, 0.00613003763208303013, 0.0037542509774318343, 0.0022250827064786427, 0.0012733279447082382, 0.000701859515684242271, 0.000371666936216777603, 0.000188564429767003186, 9.13908174907101227e-05, 4.21831838417576006e-05, 1.84818135998792171e-05, 7.65957585252031626e-06, 2.99166158781387871e-06, 1.09688351259012647e-06, 3.75954118623606301e-07, 1.19924427829027702e-07, 3.5434777171421953e-08, 9.64988889610896336e-09, 2.40917732564759408e-09, 5.48283577970949776e-10, 1.13060553474946805e-10, 2.09893354045114691e-11, 3.48419376702610597e-12, 5.13412752450142075e-13, 6.66399228330876532e-14, 7.55672177578056519e-15, 7.42099323099221676e-16, 6.25280484461045536e-17, 4.47575950666909697e-18, 2.69312066148696951e-19, 1.34699415695422861e-20, 5.53358349941557115e-22, 1.84354697471814938e-23, 4.91393687126490401e-25, 1.03293913069285754e-26, 1.68627700384926065e-28, 2.10330574900180895e-30, 1.96992097962323433e-32, 1.35998946163037957e-34, 6.78597883755924791e-37, 2.39650636994432174e-39, 5.85795694830842108e-42, 9.67839277557170956e-45, 1.05383611325642088e-47, 7.36158583097876492e-51, 3.20597853528338661e-54, 8.44308926618642561e-58, 1.3016694874428173e-61, 1.13489850483720466e-65, 5.39388136951158084e-70, 1.3438019476233711e-74, 1.68330959366332403e-79, 1.01420590744681585e-84, 2.80361361934498745e-90, 3.38153878938366947e-96, 1.68686995979731349e-102, 3.28767160546365504e-109, 2.35618039758663049e-116, 5.82127142688641827e-124, 4.62897694169420155e-132, 1.10117124175309668e-140, 7.2497708296024464e-150, 1.2159344493950079e-159, 4.75673322877850563e-170, 3.95135613192899529e-181, 6.3069398037899023e-193, 1.73909581157725484e-205, 7.39738244904451982e-219, 4.3025693007354699e-233, 3.00982956958324877e-248, 2.20899573159075297e-264, },
      { 1.57004202927959315, 1.5640214037732321, 1.55205316984541212, 1.53428173815430343, 1.51091972307416971, 1.48224329788553807, 1.44858625496132259, 1.41033297144625901, 1.36791051168089649, 1.32178011744377286, 1.27242834553786271, 1.22035810957935822, 1.16607986993243458, 1.11010319396534038, 1.05292887995526666, 0.995041804046132715, 0.936904612745667934, 0.87895234555278212, 0.821588035266964703, 0.765179298908956137, 0.710055901205468984, 0.656508246131627531, 0.604786730578403622, 0.55510187800363351, 0.5076251588319081, 0.462490398055367761, 0.419795668445015481, 0.37960556938665161, 0.341953795923016832, 0.306845909417916949, 0.274262229689068106, 0.244160777869839909, 0.21648020911729617, 0.191142684133427495, 0.168056637948269162, 0.147119413257856932, 0.128219733631200987, 0.11123999898874453, 0.0960583918651894678, 0.0825507881107017377, 0.0705924699068669994, 0.0600596423586363003, 0.0508307575725704711, 0.042787652157725676, 0.0358165056041964365, 0.029808628117310127, 0.0246610873147532825, 0.0202771838175001239, 0.0165667862542475754, 0.0134465366052857307, 0.0108399371682559072, 0.00867733074953918159, 0.00689578596906600353, 0.00543889979762399843, 0.00425652959901785802, 0.00330446699403483024, 0.00254406576752917297, 0.00194183577598436758, 0.00146901435994297911, 0.00110112611345193839, 0.000817541013324694931, 0.000601039879911474226, 0.000437394956159116878, 0.000314972091860212003, 0.000224359652050085491, 0.000158027884007011919, 0.000110021128466666972, 7.56839965862014778e-05, 5.14214974476588021e-05, 3.44921247593431977e-05, 2.28321181090361466e-05, 1.49085140318706084e-05, 9.59819412837847108e-06, 6.08991003209490393e-06, 3.8061983264644899e-06, 2.34216672085280968e-06, 1.41830671554939175e-06, 8.44737563848598635e-07, 4.94582887027541985e-07, 2.84499236591598063e-07, 1.60693945790762249e-07, 8.90713951402423871e-08, 4.84209501980723697e-08, 2.57995682295358924e-08, 1.34646455223020388e-08, 6.87846109558990011e-09, 3.43718567446500905e-09, 1.67888976821619068e-09, 8.00997844797296654e-10, 3.729950184305279e-10, 1.69394577894116469e-10, 7.49673975738182245e-11, 3.23044643332523658e-11, 1.35425129123362744e-11, 5.51823694681748858e-12, 2.18359220992336091e-12, 8.38312896050266709e-13, 3.11949772868480812e-13, 1.12402089599228615e-13, 3.91767945060164679e-14, 1.31943422319679894e-14, 4.289196222067908e-15, 1.34432228753952215e-15, 4.05755770226285762e-16, 1.17798121272483481e-16, 3.28538616288470066e-17, 8.79131655891989009e-18, 2.25407483043688194e-18, 5.53017691284033759e-19, 1.29645271406893694e-19, 2.89996455643157199e-20, 6.18014324933998845e-21, 1.25286764322732104e-21, 2.41225054683610101e-22, 4.40390669993986809e-23, 7.61057780758206258e-24, 1.24280516521231649e-24, 1.91431069022397607e-25, 2.77612510258505832e-26, 3.78312407281377971e-27, 4.83491015481884767e-28, 5.78317869722905296e-29, 6.46057570344172199e-30, 6.72603738958794054e-31, 6.51115345113745165e-32, 5.84740907454810199e-33, 4.86004605514227334e-34, 3.72923952984360242e-35, 2.63512306168036669e-36, 1.71019264014906972e-37, 1.01666853095521272e-38, 5.52069094434276257e-40, 2.73047551166050082e-41, 1.22638096665251285e-42, 4.98683929835361257e-44, 1.8300654157711633e-45, 6.04135032250826404e-47, 1.78800030852571268e-48, 4.72782066352906336e-50, 1.1129101718049772e-51, 2.32360072879360546e-53, 4.28657921354384008e-55, 6.95987352255375155e-57, 9.90539981286746442e-59, 1.23056903223701726e-60, 1.32870554636824381e-62, 1.24138446090017621e-64, 9.9894889830989163e-67, 6.89097928902098062e-69, 4.05504128376793915e-71, 2.02532547805183379e-73, 8.54119386081712901e-76, 3.02505849584780752e-78, 8.94815749837378473e-81, 2.19803659571925209e-83, 4.45733908425182132e-86, 7.41673167121007253e-89, 1.00627894464704291e-91, 1.10606170248612031e-94, 9.78345817231037531e-98, 6.91611604032706014e-101, 3.87970491925780374e-104, 1.71440443580946886e-107, 5.92265513981439625e-111, 1.58713616826240749e-114, 3.2726893534998181e-118, 5.14962203948078777e-122, 6.13053461759876059e-126, 5.47303084654380852e-130, 3.63074753096835834e-134, 1.77300075376963467e-138, 6.31164645321273548e-143, 1.62158369391922019e-147, 2.97579351328298596e-152, 3.85917057654421516e-157, 3.49805556185897821e-162, 2.19110324175719591e-167, 9.37362347961089205e-173, 2.70585229333265743e-178, 5.20510049827160481e-184, 6.58698972266196046e-190, 5.41133533489527735e-196, 2.84659058487511908e-202, 9.45372790667779433e-209, 1.95343097467687228e-215, 2.47381948587608999e-222, 1.89044188887636326e-229, 8.57867513484860174e-237, 2.27380804498007095e-244, 3.46059771526558578e-252, 2.97141917521293704e-260, 1.41351747260411324e-268, },
      { 1.57060771653827522, 1.56909969535166913, 1.56608823891746137, 1.56158249349181062, 1.55559611463166042, 1.54814719123555733, 1.53925814531188183, 1.52895560835458072, 1.51727027540505468, 1.50423673806367721, 1.48989329788329712, 1.47428176172807973, 1.45744722081254867, 1.43943781524640698, 1.42030448599969118, 1.40010071626944465, 1.37888226427313752, 1.35670688951560547, 1.33363407457575614, 1.30972474443743974, 1.28504098534672723, 1.2596457651166706, 1.23360265672194671, 1.20697556693130823, 1.17982847161733374, 1.15222515926254743, 1.12422898405060326, 1.09590262979297221, 1.06730788579750387, 1.03850543563739233, 1.00955465962942982, 0.980513451680855025, 0.951438051016352709, 0.922382889152455146, 0.893400452347195998, 0.864541159619668999, 0.835853256308267131, 0.807382723018763038, 0.779173199704797981, 0.751265924524356859, 0.723699687026830293, 0.696510795146546885, 0.669733055410290998, 0.643397765708252856, 0.6175337199299089, 0.592167223728206949, 0.567322120646738718, 0.543019827824842867, 0.519279380484246331, 0.496117484397316214, 0.473548575540614005, 0.451584886147544914, 0.430236516389788978, 0.409511510938193634, 0.389415939679212028, 0.369953981892113872, 0.351128013224425247, 0.332938694837747795, 0.315385064132678135, 0.298464626499444984, 0.282173447579596105, 0.266506245563135161, 0.251456483084510398, 0.237016458319418989, 0.223177394922184588, 0.209929530480207537, 0.197262203197437199, 0.185163936552775515, 0.173622521711631303, 0.162625097499384324, 0.15215822777419972, 0.142207976063401831, 0.132759977352445526, 0.123799506938412961, 0.115311546280937337, 0.107280845802556434, 0.0996919846077885777, 0.0925294271057784639, 0.0857775765352681898, 0.0794208254030081551, 0.0734436028576387748, 0.0678304190306579665, 0.0625659063844551083, 0.057634858114654727, 0.0530222636602871787, 0.0487133413807014315, 0.0446935684627655335, 0.0409487081258673025, 0.0374648341956289728, 0.0342283531201756966, 0.0312260235053317411, 0.0284449732473342374, 0.0258727143436176445, 0.0234971554639885273, 0.0213066123661260705, 0.0192898162408456015, 0.0174359200739774613, 0.0157345031130597965, 0.0141755735283304602, 0.0127495693587311627, 0.0114473578347997563, 0.0102602331714107689, 0.00917991292431090168, 0.00819853300526119991, 0.00730864145131324803, 0.00650319104428257946, 0.00577553087680654914, 0.00511939696145378187, 0.00452890197915628912, 0.00399852426273353105, 0.00352309611044298632, 0.00309779152330082066, 0.00271811345835022642, 0.00237988068810043825, 0.00207921435400866615, 0.00181252429912886427, 0.00157649526191055605, 0.00136807300960970396, 0.00118445048589027722, 0.00102305404297454109, 0.000881529824172970023, 0.000757730357827359211, 0.000649701418674290374, 0.000555669207425785582, 0.000474027894018167196, 0.000403327564549540929, 0.000342262606462976881, 0.000289660561088771743, 0.000244471467286891624, 0.000205757714679988186, 0.000172684419885929228, 0.000144510334290945865, 0.000120579287290569961, 0.000100312164601124202, 8.31994172400364807e-05, 6.8794093113496377e-05, 5.67053798539328775e-05, 4.65926446304946187e-05, 3.81599541202457603e-05, 3.11510556774483529e-05, 2.534479896885011e-05, 2.05509759449342446e-05, 1.66065559765083359e-05, 1.33722922845323726e-05, 1.07296754068523407e-05, 8.57820935370790825e-06, 6.83298627742185068e-06, 5.42253589182403319e-06, 4.28692649399982268e-06, 3.37609523480551097e-06, 2.64838622540434106e-06, 2.06927612573646709e-06, 1.61026800945076506e-06, 1.24793551211774519e-06, 9.63100521176592526e-07, 7.40128934909086142e-07, 5.66330284020507752e-07, 4.31448255871653806e-07, 3.27230373305175589e-07, 2.47066245112497007e-07, 1.85684913700251324e-07, 1.3890286996035953e-07, 1.03415280407456071e-07, 7.66238739748175847e-08, 5.64957638716706215e-08, 4.14482335585053839e-08, 3.02551964648994672e-08, 2.19716489170893499e-08, 1.58729780909682326e-08, 1.14064655548504821e-08, 8.15274648285765377e-09, 5.79534957309660641e-09, 4.0967579139685198e-09, 2.87970134644711047e-09, 2.01262102188671729e-09, 1.3984414312565442e-09, 9.65948518580993005e-10, 6.63208634701620998e-10, 4.5257576104153822e-10, 3.06927020787233287e-10, 2.06842035390292175e-10, 1.38502875258341451e-10, 9.21405642265188841e-11, 6.08933870643806705e-11, 3.99733895199302696e-11, 2.60619605028052933e-11, 1.68745193439150252e-11, 1.08491618343371201e-11, 6.92552801526813823e-12, 4.3888651899779304e-12, 2.76085876713982836e-12, 1.72376440360427176e-12, 1.06807504365414595e-12, 6.56694434965737702e-13, 4.00598537995324327e-13, 2.42429660454424091e-13, 1.45524991577718379e-13, 8.66381272543578617e-14, 5.11497490112370359e-14, 2.99421775950314841e-14, 1.73768169460742936e-14, 9.99642400989942931e-15, 5.69962666605779177e-15, 3.22043251327094293e-15, 1.80295896389489942e-15, 9.9999573441511328e-16, 5.49397839735005513e-16, 2.98942088568360808e-16, 1.6107654243893339e-16, 8.59320974827080203e-17, 4.53824682671384867e-17, 2.37225316709140862e-17, 1.22716716699576843e-17, 6.28122904855397546e-18, 3.18061471372875766e-18, 1.59304925747919683e-18, 7.89085515934558414e-19, 3.86473310301407342e-19, 1.87127732997090861e-19, 8.95573945523139025e-20, 4.23574285194563761e-20, 1.97943620153417501e-20, 9.1380785584698727e-21, 4.16664115847730492e-21, 1.87607505526684708e-21, 8.33990194865253314e-22, 3.65957523624367309e-22, 1.58478521833259782e-22, 6.77157569443439638e-23, 2.85428170793711408e-23, 1.18658385809475687e-23, 4.86406993570804503e-24, 1.96564341924698522e-24, 7.82916562469586207e-25, 3.07278922881727027e-25, 1.18810761472137628e-25, 4.52461974876349341e-26, 1.69671018684788546e-26, 6.26364100322911832e-27, 2.27579079285739671e-27, 8.13607771590443142e-28, 2.86130654929539103e-28, 9.89618419694378756e-29, 3.36520089316413987e-29, 1.12480705460763553e-29, 3.69446043271393344e-30, 1.19209330134653648e-30, 3.77775787601831979e-31, 1.17543637867784723e-31, 3.58987907782846159e-32, 1.07584268621427998e-32, 3.16283512594659586e-33, 9.11867418907406595e-34, 2.57739316844162499e-34, 7.1398295043464041e-35, 1.93782892101998192e-35, 5.15137910137900834e-36, 1.34081832632372396e-36, 3.41594078513204744e-37, 8.51526274133751428e-38, 2.07627148236909967e-38, 4.95013783834481588e-39, 1.15356967695693221e-39, 2.62668296485366097e-40, 5.8418474765546355e-41, 1.26855835228178392e-41, 2.68859122451013341e-42, 5.5593886486694507e-43, 1.12111057861687088e-43, 2.20403043717381214e-44, 4.22240917613642503e-45, 7.87952006236087725e-46, 1.4317140234575382e-46, 2.53190480341727998e-47, 4.35599819582008424e-48, 7.28767439600814101e-49, 1.18511349577795095e-49, 1.87243345837735946e-50, 2.872969414982913e-51, 4.27891206602476651e-52, 6.1831517263855102e-53, 8.66470653206476218e-54, 1.17694237323367861e-54, 1.54881873834810793e-55, 1.97366460880734193e-56, 2.43418383962132076e-57, 2.90413694228353275e-58, 3.34994299201193299e-59, 3.73408060580539564e-60, 4.01995891804574233e-61, 4.1774687856674289e-62, 4.18809723670882773e-63, 4.0484306973548958e-64, 3.77114883236106507e-65, 3.38317388825833663e-66, 2.92133483455321543e-67, 2.4265171108768021e-68, 1.93760346159320028e-69, 1.48647054250017705e-70, 1.09492322559900645e-71, 7.7387134447881401e-73, 5.24480082054261184e-74, 3.40625629278303564e-75, 2.11846924392585922e-76, 1.2608614434809477e-77, 7.17649031262087557e-79, 3.90346976562660287e-80, 2.02755734043105449e-81, 1.0049948164305939e-82, 4.75009864715140751e-84, 2.13926360994608328e-85, 9.17314929286727873e-87, 3.74222173296279272e-88, 1.4512980036440355e-89, 5.34630175749911307e-91, 1.8692545071988721e-92, 6.19789589312738535e-94, 1.94723396955242345e-95, 5.79190528725131719e-97, 1.62959562674195777e-98, 4.33324326423632619e-100, 1.08801494300863546e-101, 2.57722826273521138e-103, 5.75398380064511214e-105, 1.20969900172483152e-106, 2.39258726643450458e-108, 4.44756592826448755e-110, 7.76277384222654792e-112, 1.27092673030838632e-113, 1.9498242131608832e-115, 2.80025255319740608e-117, 3.76074748562275474e-119, 4.7181060260584104e-121, 5.5234683214250331e-123, 6.02744769044586333e-125, 6.1242336828836774e-127, 5.78733363277456218e-129, 5.08063271088400746e-131, 4.13871801736087986e-133, 3.12471881752233827e-135, 2.18390311640240775e-137, 1.41125398621265234e-139, 8.42151329455492477e-142, 4.63493394973768902e-144, 2.34969854645219935e-146, 1.09580671068190257e-148, 4.69502753712715875e-151, 1.84563108198977552e-153, 6.64760587741882562e-156, 2.19079138839584829e-158, 6.59697046136299986e-161, 1.81250242223442328e-163, 4.53707818939648398e-166, 1.03323888357107399e-168, 2.13749544976958983e-171, 4.0108149042055623e-174, 6.81578124552971753e-177, 1.04731276526762e-179, 1.45286859403218762e-182, 1.81662828371734628e-185, 2.04402028828248565e-188, 2.06615138595524586e-191, 1.87311279320577987e-194, 1.52035876371838628e-197, 1.10294124238752463e-200, 7.1386269709902758e-204, 4.11483850807611072e-207, 2.10850283455308121e-210, 9.58680472859715381e-214, 3.86041689304663802e-217, 1.37411989131828493e-220, 4.3152058575378643e-224, 1.19318553486394228e-227, 2.89916952664044891e-231, 6.17752210069930949e-235, 1.15194579122953133e-238, 1.87592143430152533e-242, 2.66216877009057987e-246, 3.28513888332404399e-250, 3.51733028987600599e-254, 3.26018945745222711e-258, 2.61009614898002005e-262, 1.80074547979629024e-266, 1.06810199062668946e-270, },
   };
   m_first_complements = {
      1, 0, 1, 1, 3, 5, 11, 22,
   };
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
   m_committed_refinements = static_cast<boost::math::detail::atomic_unsigned_integer_type>(m_abscissas.size() - 1);
#else
   m_committed_refinements = m_abscissas.size() - 1;
#endif

   if (m_max_refinements >= m_abscissas.size())
   {
      m_abscissas.resize(m_max_refinements + 1);
      m_weights.resize(m_max_refinements + 1);
      m_first_complements.resize(m_max_refinements + 1);
   }
   else
   {
      m_max_refinements = m_abscissas.size() - 1;
   }
   m_t_max = static_cast<Real>(m_inital_row_length);
   m_t_crossover = t_from_abscissa_complement(Real(0.5));

   prune_to_min_complement(min_complement);
}

template<class Real, class Policy>
void tanh_sinh_detail<Real, Policy>::init(const Real& min_complement, const std::integral_constant<int, 3>&)
{
   m_inital_row_length = 9;
   m_abscissas.reserve(m_max_refinements + 1);
   m_weights.reserve(m_max_refinements + 1);
   m_first_complements.reserve(m_max_refinements + 1);
   m_abscissas = {
      { 0.0L, -0.048632035927253054272944637095360333L, -2.2522807538407135100363691311150714e-05L, -4.2941610558782407776948098746194498e-14L, -1.1676488975098609327433648963732896e-37L, -1.1479529916293899121630752460973831e-101L, -1.2256538136584864685624805213855318e-275L, -1.5540928823936461440049038613030612e-748L, -5.3329091650553293055512604419528931e-2034L, -2.9720916290005160834428657415764727e-5528L, },
      { -0.32572850775156417391957990936794786L, -0.0024851435427756131672825807611796319L, -1.1124335118015331984966677262985097e-08L, -5.3784915913936887745567608333252923e-23L, -7.9430213192221161033965793076252082e-62L, -2.3871228185819266205711383850686192e-167L, -3.5505659459518255844385870776118831e-454L, -7.5205359182648710139026226942624688e-1234L, -3.1913442054759083418654684997091651e-3353L, },
      { 0.37720973816403417379147863762593439L, -0.14043094131010336482533980273793048L, -0.012959439492623108312614281385864999L, -0.00031173597164679094947882131119246211L, -7.9526288528733553445705754617783867e-07L, -4.7143551823222575055542137870412504e-11L, -5.4152228238072308459654515228487702e-18L, -2.0403003943524943294070815498839042e-29L, -3.0596901353644500298194434206758382e-48L, -2.8621984192587511612196829791231642e-79L, -2.0070330691533226369447382138318798e-130L, -9.24799327395281672235465186719e-215L, -8.3199831454007319575241484332973316e-354L, -4.7101915049286172051527740304785445e-583L, -5.1172326001331791544221573365516138e-961L, -3.565091902092693101170960484903703e-1584L, -1.380788494593788864141714339603113e-2611L, -1.676187209363792948538214808034106e-4305L, },
      { 0.19435700332493543161464358543736564L, -0.46085329461203223095024473969116941L, -0.21939256101679970074520301166037226L, -0.085120736735425389092624476255089179L, -0.026033131804322551436697210776387341L, -0.0059444933685978567073105981025861939L, -0.00093480354421415357524054329032500553L, -9.0615304856000161419338663865283889e-05L, -4.6839587794715699213295541472938628e-06L, -1.0721838758161809434047961979486614e-07L, -8.572949078216773291904741005771376e-10L, -1.7678346928387203697450793420511691e-12L, -6.374878495044439647822814562601604e-16L, -2.4429372790852173177460412747369906e-20L, -5.2515464730195748562913138330508827e-26L, -2.7894671622894177442056826817468893e-33L, -1.2781108980938044979748713346336601e-42L, -1.3082723368531808046381743164626179e-54L, -5.2799781226102543764849911449215675e-70L, -9.0619104054181014912823874637681869e-90L, -3.7903152788024647936360099425967319e-115L, -9.8301769964870442146689481371228979e-148L, -1.4178035546472368325653350149379565e-189L, -2.6738184762632894703182151426789471e-243L, -2.7784573988900744903717061581976306e-312L, -7.3734573357629727800590803405661863e-401L, -1.3609194136463888140125728088459369e-514L, -1.2497483783949474937966696885440387e-660L, -3.8167291133270025322500901161738939e-848L, -6.4205995373383705446145927875493146e-1089L, -4.4417348039463271683501804024826735e-1398L, -4.7673068229426388972968294463896211e-1795L, -9.1130922352573668488349663479480507e-2305L, -2.9379714111577752802646622386752463e-2959L, -1.2139403773313257256483385510112958e-3799L, -1.0232729473573403414331717146692674e-4878L, },
      { 0.097923885287832333262426257841800739L, 0.28787993274271591456404741264058453L, 0.46125354393958570440308960547891476L, -0.38972634249936105511770480385767707L, -0.26898196520743848851375682587352696L, -0.17668299449359762993747017866341472L, -0.11010859721573980192313081408407809L, -0.064839142478015316771395646494440585L, -0.035887835776452708068531865802344445L, -0.018545173322664829973291436321723213L, -0.0088730075583011977688563205451530858L, -0.0038913345624914574643783628537015109L, -0.0015457912323022624891555460019043471L, -0.00054856556472539415808492760717674295L, -0.00017117792712505834023311917084994073L, -4.6128994372039252659199558313379019e-05L, -1.0517985181496388528104100992279397e-05L, -1.9828594045679206280184825675885662e-06L, -3.0110584738877965497930108986179822e-07L, -3.5760919084657714373617064589353927e-08L, -3.2128009016957604999656591474001583e-09L, -2.1026713776411488104386696975012214e-10L, -9.606066478508465003238769873504248e-12L, -2.9190266410175139338365873758536822e-13L, -5.5861166388382275217411050967087276e-15L, -6.328207134683168236714250292398626e-17L, -3.9564613394676478284814817479958896e-19L, -1.2609758447165484726150111345292941e-21L, -1.8724921745276082830417983528796313e-24L, -1.1700302939131190810173673171853244e-27L, -2.7409861783512409524809986743233587e-31L, -2.1122827214476119769953891899502657e-35L, -4.6172239482595017143038368975612786e-40L, -2.4203621976262400766076976463254527e-45L, -2.5155618638019452025824211444543239e-51L, -4.1786390697021940369390798235100086e-58L, -8.6898180082116723025562943397702972e-66L, -1.7154357765002478902943183846995354e-74L, -2.3493032412195338067008854292230121e-84L, -1.5645432388942512177369950120860268e-95L, -3.3873402047868364753040883051129579e-108L, -1.5108166471007846474189907630676839e-122L, -8.2780000956704166841985762886452778e-139L, -3.1016029819580818393688194490134506e-157L, -4.0917346928724404440872286063293528e-178L, -8.9581681449485486979882444847795217e-202L, -1.3878968774299744972242822597861929e-228L, -5.7925742129350389434060147129950123e-259L, -2.1800405760909812340138469200688552e-293L, -2.1406911761156887130351179719470661e-332L, -1.3453872213865718752685710282193859e-376L, -1.101014385623064504263959341239785e-426L, -1.9309068614975016262992988189610055e-483L, -9.3925603630660026565835909833509362e-548L, -1.2492852059051478595312298290165577e-620L, -3.2901922810889194483613206478029049e-703L, -8.7594800494412338094846019359419219e-797L, -8.0988360915906760478163297865939266e-903L, -5.7031344694656922604093410430637095e-1023L, -4.0339055664976041957619784717611254e-1159L, -2.1239178549857743261952555619757156e-1313L, -3.2107478383582521299381622502244136e-1488L, -2.5644945785388919263865858785287637e-1686L, -8.6102745649577746230102198336971101e-1911L, -3.7383203767208973356368595261534058e-2165L, -2.1998398074691126029946216118342674e-2453L, -5.4286931050493856204731826640809476e-2780L, -4.3621004458123770532320189625677295e-3150L, -1.8497978727098997950183603712476515e-3569L, -1.1369676358500293654883467577244006e-4044L, -3.7214839695831726545775627850809865e-4583L, -2.4391504445900085306499460471723674e-5193L, },
      { 0.049055967305077886314518733812558024L, 0.14641798429058794052721556654753264L, 0.24156631953888365837930785879296225L, 0.33314226457763809243847911788190042L, 0.41995211127844715849198982824847239L, -0.49898661062069089848349381875489034L, -0.42441550936484834004641698479390102L, -0.35682324101479529871858133934192696L, -0.2964499948528579843445043147096805L, -0.24330609136627005059281117751078325L, -0.19720125865675873423611581820317215L, -0.15778075364924313618002648351129378L, -0.12456460236959132162770016658853563L, -0.096986718486426129363622040287916613L, -0.074431365931387333547858011705581591L, -0.056265213947242843145098876381950312L, -0.041863977289786309881823549732911528L, -0.030633267103082664829845578114893065L, -0.022023764813335027020322387941507847L, -0.015541168832569169131789346286900099L, -0.010751568909866103993013400883081643L, -0.0072830028031727146206149733302275483L, -0.004823973844672645743610027178935428L, -0.0031196818718081262793524354157388492L, -0.0019666636845662459778601079868649985L, -0.0012064657011941007112693927041613496L, -0.00071888807820804459144864726303695018L, -0.00041524964848241268314681568037451433L, -0.00023202840043916493872468513410105539L, -0.00012513495121965351530204004794322354L, -6.4980074917576313794386569166441844e-05L, -3.2406932056540235696910972894671255e-05L, -1.5480097729175578034178680349142798e-05L, -7.0621233371143522557906813506099499e-06L, -3.0675508096424942465066236512329746e-06L, -1.2645281340904582699398919033969316e-06L, -4.9299428056311116061802876221616736e-07L, -1.8110628723299136916197821516601534e-07L, -6.2445921626224975560761130514661379e-08L, -2.0125496798245795667718397386179161e-08L, -6.0358657983522763759573155859882131e-09L, -1.6766380517421942876239492441262369e-09L, -4.292122273863880635543266425012844e-10L, -1.0072227674373084849456997974551022e-10L, -2.154466258904202365369932629331438e-11L, -4.1753931241965133013258464583874422e-12L, -7.2847372642877340424635554069698771e-13L, -1.1363869854392191510522700311409067e-13L, -1.5735535097926530184856977764147682e-14L, -1.9192189097107621395219494827869742e-15L, -2.0449595408467355635051956217352212e-16L, -1.8869583067293409682109390382712203e-17L, -1.4938716488357228966449278404247075e-18L, -1.0046938100037576480510308414445084e-19L, -5.6799291780731967001285171683592406e-21L, -2.6691039526135151405184691859723367e-22L, -1.0301781381894114683480515283399996e-23L, -3.2244848763912462050246763811724513e-25L, -8.074782900421688680511360232922118e-27L, -1.5946546019932855470728505192034342e-28L, -2.4457239752752825879158721953606166e-30L, -2.8659197719078655681373680421610036e-32L, -2.5216825251828994098262358831122367e-34L, -1.6355144403100727446739341400788963e-36L, -7.6666590990678357226674242484094458e-39L, -2.5435771288137693474957657676429463e-41L, -5.8409472280716482316816237196206469e-44L, -9.0658695647939702552111019446179921e-47L, -9.2735698160588525904827667013324314e-50L, -6.0857148042052277604035466987369298e-53L, -2.4898089736722146606232085397625384e-56L, -6.1598620863170544290033299759018975e-60L, -8.9214127072226711903218542175348322e-64L, -7.3072243260040451389778049479673039e-68L, -3.2625608625513858480451119539754283e-72L, -7.635785144983622496088267971984613e-77L, -8.9855174690288197015625761302053014e-82L, -5.0858718515487289997949540667405475e-87L, -1.3207396001984761034417395177214725e-92L, -1.4964864935610521348123903353455889e-98L, -7.0129233669310880373145304240642606e-105L, -1.2839990161964174973579406866396101e-111L, -8.6445683841052002170413994851536565e-119L, -2.0063694896831555279614790770285332e-126L, -1.4987740796184447811135706015205434e-134L, -3.3493762149589610757447357005516387e-143L, -2.0715294381434481475176795645059666e-152L, -3.2638832593319965098529982282696874e-162L, -1.199473759889723458030222815758102e-172L, -9.3602085584027101517846387151893588e-184L, -1.4035094428007866386369967794759254e-195L, -3.6356107639491247696753307742986431e-208L, -1.452743995741226464980668915527686e-221L, -7.9377235820922308566742845525117486e-236L, -5.2163536229754499795136126103956784e-251L, -3.5964743752577816321270751238997225e-267L, -2.2494401025357803105700529744520824e-284L, -1.0932895122313467863909046216907507e-302L, -3.5018653663752920075194010313564247e-322L, -6.2028644584939181030730328594330175e-343L, -5.0411076779076593359744388314869213e-365L, -1.5409305985666377552321629186300452e-388L, -1.4337691700836957601785568351756479e-413L, -3.2419333785971000532922906392702739e-440L, -1.4016483469785082716612467122698103e-468L, -8.9774004157156246399741480681783078e-499L, -6.491677776744900396848653659323143e-531L, -3.968873851900238121124736680679859e-565L, -1.5079636812339879861399394077308116e-601L, -2.5657520984549883455506649942306167e-640L, -1.3792637739621433541997914448762973e-681L, -1.6159468091505698350217369718439518e-725L, -2.7790201117578232055517503438743301e-772L, -4.6058286485158911464074200874638533e-822L, -4.7006358553201903008219637676007764e-875L, -1.8338935012250262258997114428219912e-931L, -1.6464242001502528395088533240159331e-991L, -1.9816419848867601423745192888068803e-1055L, -1.7991094041844603532543957306249989e-1123L, -6.6797992361969494675858665124683032e-1196L, -5.2859098848402526633383865829249954e-1273L, -4.4550261847470794285196049926646754e-1355L, -1.9109438945105886059290808709178883e-1442L, -1.9007367698294334902014013473010713e-1535L, -1.8987324425640464476346787762173242e-1634L, -7.8167618149826855518169886771423511e-1740L, -5.1382521738908379784587542986497343e-1852L, -1.9655086052442019324819644975934904e-1971L, -1.4940998787039296124957637312415828e-2098L, -7.1913235695994009755126799768473976e-2234L, -6.4864369300175247147155547975053653e-2378L, -2.999946728125704672445599927700136e-2531L, -1.7904880802749365558709955426180586e-2694L, -3.1752504880744458148875772810912828e-2868L, -3.5042626554286023022035386575033303e-3053L, -4.5572536942770532680494362978923935e-3250L, -1.1878504044248058672204846884752448e-3459L, -9.4149599587707022465535580134518114e-3683L, -3.0486081328912260971936320312995529e-3920L, -4.7600871041282300101380551251356845e-4173L, -3.6856282214670996203899028095741989e-4442L, -1.2567068832608339131427238307321809e-4728L, -1.43352474798599180975125045215869e-5033L, -3.5192846690056568230804624618996538e-5358L, },
      { 0.024539763574649160378815204133417875L, 0.073525122985671294475493956399705179L, 0.12222912220155764235135543647484308L, 0.17046797238201051810697458795462942L, 0.21806347346971200463019772812275949L, 0.26484507658344795046121266511868619L, 0.31065178055284596083122357022827612L, 0.35533382516507453329875421158518394L, 0.39875415046723775644258197210456498L, 0.44078959903390086626728024419415105L, 0.48133184611690504421849248706653226L, -0.47971194930876984042437695635737165L, -0.44241877173922176919985653200857201L, -0.40684964640804684120174768961798095L, -0.37304979194895712050380997331064448L, -0.34104900825664987561685714614383599L, -0.3108622749383323282394733757479914L, -0.28249053251267587278787801637860204L, -0.25592161645265260087450443652530469L, -0.23113131323175341540544311864007538L, -0.20808450762385788552523390287482686L, -0.18673639149702614832471241636026165L, -0.16703370608058912436398702772140703L, -0.14891599201215126738569202826103709L, -0.13231682422435401330546108424048627L, -0.11716501175533104486690697891318309L, -0.10338574571992397421289522925754423L, -0.090901681836979564888397733057112044L, -0.07963394696804719765304016834176825L, -0.069503062002846593688614128282605538L, -0.06042977606672524461492007773726152L, -0.052335809384846902663067556129352578L, -0.045144504194977314593201232656013596L, -0.038781384848883592468460207166535186L, -0.033174629687644147159978329404393879L, -0.02825545843451269107668693235164557L, -0.02395843974342326066584697335177699L, -0.020221724199384237353497637367719603L, -0.016987208518898894423060324544850119L, -0.014200636974716564032303584519259449L, -0.011811646199257357569283265398947793L, -0.0097737595324722530639024866802767264L, -0.0080443369973223843798692318964963564L, -0.0065844868307359609987086057921138135L, -0.0053589442874888032779151584247851277L, -0.0043359231830468303521751643564073944L, -0.0034869453597462268271205586183706831L, -0.0027866529565312977632015610760207467L, -0.0022126080410934691734971443584643208L, -0.0017450838280037065615903399860968624L, -0.001366851359322522377124006197840537L, -0.0010629651664878262735108241097275379L, -0.00082055106511408284244272347987383953L, -0.00062859885906231310357940554216565142L, -0.00047776234878279577588863291489417448L, -0.00036016865439963480978268344017946398L, -0.00026923848019151736959880088820900265L, -0.00019951856886161369803218944988361524L, -0.00014652722688858828517464005147727694L, -0.00010661345240743574350860189665463426L, -7.6829870710671305672093252530903342e-05L, -5.4819385541306910394346187661030795e-05L, -3.8715192143333868407124859677345036e-05L, -2.7053574767763435703448594138071512e-05L, -1.8698729879273208281214887386151327e-05L, -1.2778717999371889596874911057724209e-05L, -8.631551655126555923485540179705975e-06L, -5.7603723833652179866412085097651011e-06L, -3.796652833823246921587494391038503e-06L, -2.4703761948320696741670218229372071e-06L, -1.5861890352645758023485712079757291e-06L, -1.0045893100303765084297472484854592e-06L, -6.27292664630529591934671730489378e-07L, -3.8601144975725102288841415591950667e-07L, -2.339766675668795211736366707138927e-07L, -1.396287854005922888135986291920763e-07L, -8.199520528943774856188619843122174e-08L, -4.7357335538151973999955685632054965e-08L, -2.6886764056376916830145130602254872e-08L, -1.4996923688266632451823595335560501e-08L, -8.2135439009280190432084662337190249e-09L, -4.4143663841612464008735244035118061e-09L, -2.3267632621004612298287053849704746e-09L, -1.2020164996011407668894632352766295e-09L, -6.0822312416778615105389374826677738e-10L, -3.0124563074832322562803735802769698e-10L, -1.4594388450012857143914599919653952e-10L, -6.9111604985231078666967248228438538e-11L, -3.1966783264445369722850429235278972e-11L, -1.4431209924042652494507345459464675e-11L, -6.3536761284911814276716019591237312e-12L, -2.7259505188725197201090431322689677e-12L, -1.1387345722407849659711071645809649e-12L, -4.6277346217693134290561172417433117e-13L, -1.8279902248431104840099026765769527e-13L, -7.0120467384905686087185698802558123e-14L, -2.6096053661379546374618820988626935e-14L, -9.4133613352888555760685465264560536e-15L, -3.2879256949364934203905795056053052e-15L, -1.1108629401821898431250973198964316e-15L, -3.6266022642032122019717830419062828e-16L, -1.1427876525052669863279877707280153e-16L, -3.4719022692494936795924113147604942e-17L, -1.0157819068305124587449297554375856e-17L, -2.8585328245081481739274807557448345e-18L, -7.7278347873892552568443785071813692e-19L, -2.0044239922175172204979314952837677e-19L, -4.9815683764699114931038226437577286e-20L, -1.184668492422340236764487382599448e-20L, -2.6919849036103905707012031384082434e-21L, -5.8366674419339414129894129738889481e-22L, -1.2056615886548723513354371860285751e-22L, -2.3691093508078067650593562318134397e-23L, -4.4213394617280017751359368167258193e-24L, -7.8238350041086858182189611432466241e-25L, -1.3105331441254781548552012055519121e-25L, -2.0743450130735150739962743364500278e-26L, -3.0969683255507495828369632528319965e-27L, -4.3532005281967559757740065310127852e-28L, -5.7499556680703922900693138751854555e-29L, -7.1227159266381727412068631384345687e-30L, -8.2578354203558045768004451321024521e-31L, -8.9415410066249555437647937669966689e-32L, -9.022796650030866974265443296162775e-33L, -8.4660301195315971752688615420135191e-34L, -7.3692728065645298168617836618124666e-35L, -5.9366323560088749776278557840220755e-36L, -4.4152778386946959545381237143455402e-37L, -3.0239601358635314916964716581634168e-38L, -1.9022037916577048792943667867798758e-39L, -1.0960433286034577882548963198643229e-40L, -5.7686955980040694827320797355280285e-42L, -2.7653993817377102211887943260200603e-43L, -1.2038709528562574051108458784307785e-44L, -4.7447623253354987177832150988795395e-46L, -1.6876791281053309141043851984778909e-47L, -5.3999669982997284808706183365024646e-49L, -1.549024049448082614346506969629423e-50L, -3.9699472552561349765465127201129129e-52L, -9.0576749265239358552559531050991564e-54L, -1.8329504334836931380757353284221911e-55L, -3.2774223323973643896776906795254206e-57L, -5.1576871530839363640889147624270212e-59L, -7.1147152214081979999932108600174644e-61L, -8.5668890171210249323533230713364891e-63L, -8.9655585542113618588233770236885955e-65L, -8.1186937969054743805236701211415782e-67L, -6.3321938859077339155825561128906228e-69L, -4.233726342460328087225876085667347e-71L, -2.4147260464376790196845134616759282e-73L, -1.1689555910948214525778546914101199e-75L, -4.7780668985526446079787318493898922e-78L, -1.6402035716952158990309656043758773e-80L, -4.7024891487642105081472609988004035e-83L, -1.1195904670077980724005196979590736e-85L, -2.2005433507330024992345876236265894e-88L, -3.5489240240632636451154177125584074e-91L, -4.666940936883651858001572120481125e-94L, -4.9719066745930336256015501390182822e-97L, -4.2625120032373619347908954864791867e-100L, -2.9205525410269401160683450458899041e-103L, -1.5879283520520673524672134768919763e-106L, -6.8010328774211569659959576053742516e-110L, -2.2772324163917691950189307132002393e-113L, -5.914722281629598175795438531560773e-117L, -1.1820998217768840832984056598978546e-120L, -1.8028262273951398891886168129927287e-124L, -2.0802041335110124193794970445396975e-128L, -1.7999670156396935596364355347029528e-132L, -1.1573419660682545435402292296632314e-136L, -5.4777666541440989994081254549149894e-141L, -1.8900190812332372959031760309750191e-145L, -4.7064318355746675546808068910291556e-150L, -8.3711291364376416570212981885601003e-155L, -1.0522139008817658534084763162543628e-159L, -9.2441193400981258043604322474285494e-165L, -5.6121650181326378940737135740792016e-170L, -2.3270398424370294077224375501608811e-175L, -6.510721186743015604234587537579044e-181L, -1.2138997008699255952666017192674735e-186L, -1.488912999352695867344311698561554e-192L, -1.1855378186700107100267028475666946e-198L, -6.0445584657932328676234962000989188e-205L, -1.9456793188673556729031520231103262e-211L, -3.8966805747095113950686230057410173e-218L, -4.7829220009546143710431038737748715e-225L, -3.5425595917324118172041028901087466e-232L, -1.5581260191174140607938473801301532e-239L, -4.0028071638967508676664829682660027e-247L, -5.9046000382762171283315920864186447e-255L, -4.9139618035200235196744932808885781e-263L, -2.2656744698055742315406349324984012e-271L, -5.679971707464259613069663431618014e-280L, -7.5941261723746109223402306020806232e-289L, -5.3079378188778939332566204780962363e-298L, -1.8999778173084169834715816327210827e-307L, -3.4097331110687592869362387676073128e-317L, -3.0013940393884085654083505783337316e-327L, -1.2668812065800974411227932737003665e-337L, -2.5051097865973858424963978994105679e-348L, -2.2653835286828392416879577941979414e-359L, -9.1389245527311981300218270087623242e-371L, -1.6030930250662823526442537954011133e-382L, -1.1908341901916029371925944757292412e-394L, -3.6452473980708905026093050166205199e-407L, -4.470581799337985564576477205041097e-420L, -2.1337946766915608745979583351759322e-433L, -3.8466502387371814478842876774875617e-447L, -2.539400648443551278595315008859673e-461L, -5.9463310120354055195604938583173969e-476L, -4.7791154462039743101535253283672216e-491L, -1.2743368238366444127980835826074082e-506L, -1.0885513756896925921766278125740167e-522L, -2.8730852174104725878401965901098976e-539L, -2.2573125874160702024258126565183851e-556L, -5.0801120877688108677556596022448958e-574L, -3.147442313458971094095441973453258e-592L, -5.1530284704707320518750575728196905e-611L, -2.1371769888513739266688394459294633e-630L, -2.1496221573881902238982422615825289e-650L, -5.0129928677987666360823382488243001e-671L, -2.5875873756026443959521949570534425e-692L, -2.8181620714067187431385929758736621e-714L, -6.1639625895204988083360964770898867e-737L, -2.5730385846926571409465774477581834e-760L, -1.9448744127295675669050254780849454e-784L, -2.5213762798933993822025230917884045e-809L, -5.3012546029922448380589857164083814e-835L, -1.7062280322013794538523703018620566e-861L, -7.920258584822147757302618208276618e-889L, -4.9864409544476492068060565707730923e-917L, -3.9962146024411971515483669014007722e-946L, -3.8185388383865818601310492618971832e-976L, -4.0664680142829823697989791072662443e-1007L, -4.5015287529024162764736783373823692e-1039L, -4.8207630344098447587536323292213823e-1072L, -4.6375061995611002272610193821937293e-1106L, -3.7123073076807703361397549566488692e-1141L, -2.2851629967300820983086928617773692e-1177L, -9.9710151392993821636927693831761909e-1215L, -2.8354564442635450405718863241322006e-1253L, -4.8186151533506508630367161348238245e-1293L, -4.4750346397724178706383092717200537e-1334L, -2.07096024490079588158927973820168e-1376L, -4.342127308884323517312819302082545e-1420L, -3.7387854726872903857603805318252205e-1465L, -1.1946574634186879393921342144260135e-1511L, -1.2759441657028066718211573051988164e-1559L, -4.0892575258067523665855842405306752e-1609L, -3.5183875354871145031995454351438789e-1660L, -7.2453069573873944421690184084196628e-1713L, -3.1719634253495420624848681187846285e-1767L, -2.6125752080330097261369292566212969e-1823L, -3.568653966381994484787922096951618e-1881L, -7.0977732480251336872758023077199559e-1941L, -1.7972768141068333989152413556564914e-2002L, -5.0445828237798979207114411387487754e-2066L, -1.3604595412453945762407996412329254e-2131L, -3.0419951659449528528023254177983519e-2199L, -4.8436592014037520285667566761754706e-2269L, -4.6942170473823597199914323005808658e-2341L, -2.3550263329098316285032485977513203e-2415L, -5.1749636831144475403049010950928176e-2492L, -4.1920868885180451051347682697190622e-2571L, -1.0479018787402586343779450171950524e-2652L, -6.7279428066803227116834111695132562e-2737L, -9.1809880505079118230638356660916729e-2824L, -2.1903121606778754894644610552460375e-2913L, -7.4679789664508998469723121247024705e-3006L, -2.9557873617341136266023224346917197e-3101L, -1.0958275239557229340575496309183988e-3199L, -3.0498491351714004349547292302889343e-3301L, -5.0710549490564658785365657190857463e-3406L, -3.9798833012314432652288251577412562e-3514L, -1.1561503564308970430488476029765637e-3625L, -9.6738350948812768619787679276120515e-3741L, -1.7998403785619766408556530850576782e-3859L, -5.7011595100947685691764189814097789e-3982L, -2.3342553026008523985584602356036335e-4108L, -9.2972354921660563358009254715497299e-4239L, -2.6867404349734172649970964993288437e-4373L, -4.1626263982921541967696302677355217e-4512L, -2.5305298537002028475606439113724333e-4655L, -4.374081747920579431517607591486263e-4803L, -1.5419945401212975573693263841002279e-4955L, -7.8687813783350283818994253864974705e-5113L, -4.0807753246747505208452672626580765e-5275L, -1.4931173692359951169542729406105663e-5442L, },
      { 0.01227135511808220203174030407830294L, 0.036802280950025085014502801868003454L, 0.061297889413659975774096037893250044L, 0.085734754877651055755695461536205953L, 0.11008962993262801258027823870142222L, 0.13433951528767223660361301319755489L, 0.15846172828929950397368299153321965L, 0.18243396969028915021484585689969531L, 0.20623438831102876939480559034293115L, 0.229841643254360753698787981399908L, 0.25323496335600023568199079816688852L, 0.27639420357617861412235821455333865L, 0.29929989806396047268181013419640459L, 0.32193330965336916623041220093195644L, 0.34427647557970491862490100675820811L, 0.36631224923490408185795345459447966L, 0.38802433781211774758659161520378721L, 0.40939733572152948912988463410721733L, 0.43041675369143706432580814661825066L, 0.45106904350045199476872844990592136L, 0.47134161831799846568675411549444037L, 0.49122286866081148740164998519670023L, -0.48929782599744185793989666371765164L, -0.47023008989822544160728474574480754L, -0.45158254786020764535461909038883739L, -0.43336282621498114195428483532083889L, -0.41557755767733605834276062792428588L, -0.39823238999036654097849262723755211L, -0.38133199816727193874162067315553426L, -0.36488010013557816387677409414947586L, -0.34887947557569577047526159126703565L, -0.33333198773426231868095335844342399L, -0.31823860798357268745316074354464261L, -0.3035994428915407927086292160567468L, -0.28941376356199421334543326775454705L, -0.27568003700259251963348266210052998L, -0.26239595927717475510166267033301246L, -0.24955849020075956493410868722363431L, -0.2371638893386076311859247952179354L, -0.22520775307556463019220672947952605L, -0.21368505252818039398938452749375662L, -0.20259017207968775297648115336201576L, -0.19191694832666146249702413953519866L, -0.1816587092359021720484117453151821L, -0.17180831332064290072992681330484534L, -0.16235818865639997533663276272992352L, -0.15330037156853630364404549106194642L, -0.14462654483572846910471674072111206L, -0.13632807526589476511715224673350004L, -0.12839605051362228117619033241835825L, -0.12082131502061062975401342241486234L, -0.11359450497302129819348705755904515L, -0.10670608218178901767108607508338614L, -0.10014636680382986857859380586600253L, -0.093905568833595799431451407446515619L, -0.087973818305513180839892673963966241L, -0.082341194158450660178436474882477694L, -0.076997751723445596499983664621487996L, -0.07193354880544487378738311657933671L, -0.067138670338759945862672351268719341L, -0.06260325160428086186052507865402345L, -0.058317500004230664509200379313199518L, -0.054271715397367759935086056389293555L, -0.050456309004063640355911746580908904L, -0.046861820896606197349996098751978814L, -0.043478936095419069610563231210812888L, -0.040298499296663390818673202062521126L, -0.037311528260921730030179369385366989L, -0.03450922589637952691206152682222172L, -0.031882991073143683811495499895307175L, -0.029424428208099579660135740086865642L, -0.0271253556620361184294276412754116L, -0.024977812992693650455170906326981392L, -0.022974067108942042492683430416652371L, -0.021106617372505970898624990276714523L, -0.019368199694551294992954740677403882L, -0.017751789675058934941049112986218163L, -0.016250604833268754660069071624504775L, -0.014858105977601971243228436688712421L, -0.013567997763391553658409398729735747L, -0.01237422848648931564729341766812544L, -0.011270989160397638106454864191015908L, -0.010252711924012903699062738821083185L, -0.0093140678263849278340314349076327353L, -0.008449964034108265248268117472003357L, -0.0076555405060818245590132017863229384L, -0.0069261661794155687412632471018391501L, -0.0062574347092383891891208397242483686L, -0.0056451598040791099606160787309638234L, -0.005085370197361128404963242365624777L, -0.0045743042943771933124154458548517366L, -0.0041084045328997985361611689079751989L, -0.003684311494339074782205923019899892L, -0.0032988578010873948724575222465114151L, -0.0029490618343908704733333873759063219L, -0.0026321213057645656305833925217639785L, -0.0023454067136221407000743728815804873L, -0.0020864547154229734242597171162514598L, -0.0018529614442515743960802739125590691L, -0.0016427757973362357767337812404644128L, -0.0014538927225872833069713654109330111L, -0.0012844465277911570122332123844688455L, -0.0011327042356361946956708854910717802L, -0.00099705900627114148706364731704457709L, -0.00087602364761017102176873850865987398L, -0.00076822423210114801927833212686428566L, -0.00067239383717004607961542674246453911L, -0.00058736642504742093080174568452091253L, -0.00051207087617669287572188257954358842L, -0.00044552518890326020005629570593666048L, -0.00038683085665314188976467566548793199L, -0.00033516743233532263998791108562179822L, -0.00028978728824896558062724060959821788L, -0.00025001057835114127416442774611755005L, -0.00021522040834883412191823299277438955L, -0.00018485821772693895358504217864611182L, -0.00015841937651793126628162990110989201L, -0.00013544899836493134204129687787204532L, -0.00011553797023379563893736144870607114L, -9.8319197997130553498037999641991487e-05L, -8.3464066048770978005196778706600799e-05L, -7.0679108115811218906095134416154613e-05L, -5.9702885520625386671791420435362921e-05L, -5.0303068310699595343932915440924077e-05L, -4.2273713922018120404243909635658711e-05L, -3.5432737373923361833214162324345645e-05L, -2.9619566410724316247858190357851524e-05L, -2.4692974507962358657498919760422057e-05L, -2.0529084248443619898457582669713841e-05L, -1.7019533243403755023062694063359981e-05L, -1.4069794525207501119432380528847431e-05L, -1.1597643166806136977541673876760615e-05L, -9.5317607861225334492481477207976067e-06L, -7.8104695663539218978433615931904191e-06L, -6.3805874611085194148542316667027636e-06L, -5.1963963511236276943307284768443108e-06L, -4.2187150715072772351343816215627715e-06L, -3.4140694292103354602072245113222152e-06L, -2.7539515738216143655929212361430556e-06L, -2.2141613647700348500196752438228238e-06L, -1.7742226886238416354636940930406071e-06L, -1.4168680155127991206906010386776301e-06L, -1.1275848380651233667302406751686147e-06L, -8.9421800420026716637883889021333717e-07L, -7.0662233151961419975116851767251182e-07L, -5.5636027112312322628504802544396849e-07L, -4.3643976809083996211611047940700884e-07L, -3.410878406808352344192770488162828e-07L, -2.6555576704383762368205974553664254e-07L, -2.0595212394180990975114609016580268e-07L, -1.5910026405413012262681483592072574e-07L, -1.2241714489336877307229490096356811e-07L, -9.3810731511298667657736474465916103e-08L, -7.1593485856820748829676007409030264e-08L, -5.440972704881531639917700413368612e-08L, -4.1174898509745034636641901499637178e-08L, -3.1025009758847405806336107058366646e-08L, -2.3274732865051716886351304700372955e-08L, -1.7382826537542006005418736935360821e-08L, -1.2923735225028943169773355461941788e-08L, -9.5643672141012475959697164491130902e-09L, -7.0451955080630225653421587634231792e-09L, -5.1649492757248395049197216720378741e-09L, -3.7682729974336732148768443187202036e-09L, -2.7358262541647042643058722243498035e-09L, -1.9763805681294590078061949445779507e-09L, -1.4205419635481505647172465852581089e-09L, -1.0157900987858722132374807163656952e-09L, -7.2257800679698977337008871109952299e-10L, -5.1128169466802636753557604076122797e-10L, -3.5982705512040164549431243047583567e-10L, -2.5185362244554210229589301236053854e-10L, -1.7530147750309255605734608915901604e-10L, -1.2132981051324705363357105718720941e-10L, -8.3493950752320668610647319362559637e-11L, -5.7122660267481958430713410881095016e-11L, -3.8849686002793765883588257767026493e-11L, -2.6263427379949721354783426839588953e-11L, -1.7646499776378976222245772521934802e-11L, -1.1783298320120630850539029584309311e-11L, -7.818680628325362638575521404267539e-12L, -5.1548364042751350429076854858018077e-12L, -3.3765014608000294669628289654725219e-12L, -2.1970744699592342642466439966825033e-12L, -1.4200473669518126870628306394495551e-12L, -9.1158007933577728676134065579326132e-13L, -5.8113062505586808310528553401168741e-13L, -3.6786791102075633999543555381361719e-13L, -2.312068903576444768213986586668492e-13L, -1.4426172485922308843621431499934686e-13L, -8.9349666651298939216456676750565829e-14L, -5.4925677721256790529564920614679325e-14L, -3.350788309515877062958811342787956e-14L, -2.0284076402444039895928235924589244e-14L, -1.2182795128979644452912696549518128e-14L, -7.2588537362052341752692102123089101e-15L, -4.2900627866036669128285279549303027e-15L, -2.514652539480771341683046756355483e-15L, -1.4616871128475335847732485959402402e-15L, -8.4243309583746983103209365972381765e-16L, -4.8135175904015314514321104340303263e-16L, -2.7263179430684054979795995181696999e-16L, -1.5304422973965932233677561227408377e-16L, -8.5137857250384433997363815737881969e-17L, -4.6927950389611427068741326969599031e-17L, -2.5625975950320089549856176296445119e-17L, -1.3861334667950396744809652330676689e-17L, -7.4257501921384642292801069180083189e-18L, -3.9393094024445601833019849790813999e-18L, -2.0690791459774228199242128751668536e-18L, -1.0758286216493138282150652273565844e-18L, -5.5366710173011999504598745894506669e-19L, -2.819834003785898432921699582824987e-19L, -1.4210063750313593796726974809925319e-19L, -7.0842438781417380233044623416891119e-20L, -3.4933505573095044526616590274445578e-20L, -1.7035977510820099431072120902417317e-20L, -8.2147060442121877800848286483420634e-21L, -3.9159734384156989690338293870539541e-21L, -1.8451498156271871363001830659526856e-21L, -8.591874580623540129167786895666899e-22L, -3.9530069577065764915635262899095704e-22L, -1.7966697995134261385398916631645912e-22L, -8.0654088208389219862969348472697735e-23L, -3.5753372122620560623243551587275021e-23L, -1.5647821323387363967354776667279086e-23L, -6.760041764217369644940988147273804e-24L, -2.8821363233025547133119762203866565e-24L, -1.2124362329062417813651773042222047e-24L, -5.0314218307880599929168429579030218e-25L, -2.0592863122450067780181162680466505e-25L, -8.310787797639985481399920298561774e-26L, -3.3065180676145941342573597126851257e-26L, -1.2965973462467399413826706323859068e-26L, -5.010091032398552312889055575499255e-27L, -1.9071793847355632162575945250968826e-27L, -7.1505673339810973649967867585100117e-28L, -2.6399060373682682300772738413986419e-28L, -9.5946645421591590272737060192787035e-29L, -3.4320780991310649618469641664780934e-29L, -1.2079850663067081309863651387341263e-29L, -4.1824647214881752797125541962674838e-30L, -1.4241537066844817617919365428875337e-30L, -4.7678333816988133611599026436806048e-31L, -1.5689491781958062283731284050971648e-31L, -5.0734388548496443499288192319785528e-32L, -1.6116919160939245706339600215608967e-32L, -5.0283566942543780691617432163678683e-33L, -1.540320437147141644686176099192533e-33L, -4.6313924434817901733319954389626016e-34L, -1.3664702250574194828136706303116849e-34L, -3.9550085269989845845237677023544696e-35L, -1.1225918253644008151328041096174431e-35L, -3.1238487433616834059785879847016762e-36L, -8.5195402472064902632510499781952803e-37L, -2.2764734813775829740707091909282255e-37L, -5.9578473858349842310952826931317941e-38L, -1.5267023004586063974485430859996629e-38L, -3.8292452373883691255032248372279562e-39L, -9.3976484123312742513645018653353223e-40L, -2.2559187023391247540357057603608445e-40L, -5.2951057740070273455910884012728247e-41L, -1.21484005139195805370253212205352e-41L, -2.7233329057563859457781652894773382e-42L, -5.9629474724550016648059026687365969e-43L, -1.2747904563208401684090170257821763e-43L, -2.6599324647740072572781740009475107e-44L, -5.4148975705500338800900730270033864e-45L, -1.0750506783792922201321365237207054e-45L, -2.0807277332937164031945232093695697e-46L, -3.9244163140436388041993251545689042e-47L, -7.2099378100469390278450504220931707e-48L, -1.2897482330918123669983337886382095e-48L, -2.2454989700172052890162893888242529e-49L, -3.8033817751932373827258462682395899e-50L, -6.2645207667921988981079909974995822e-51L, -1.0029410834924645721226614397473029e-51L, -1.5600495091009386993988441513950677e-52L, -2.3565650292912781888129806450540921e-53L, -3.4553986153009958542696149938952582e-54L, -4.9157636744396300209125008789737936e-55L, -6.781896900608377888472479708032872e-56L, -9.0691940202443643747199815355477207e-57L, -1.1749792443564338073595948464279018e-57L, -1.4740730312062593807916913990480482e-58L, -1.78984329859422798255692388149323e-59L, -2.1022998433631042036525885470222986e-60L, -2.3874311701754330999376394091254256e-61L, -2.6199497113269397601815392049767616e-62L, -2.7768131253969855575381129113408787e-63L, -2.8408870962885800781023815369817483e-64L, -2.8039688883465382638228147703461371e-65L, -2.6684481602260540322238895901981613e-66L, -2.447154080870857351995438102206883e-67L, -2.1613619398610954911634747438934054e-68L, -1.8373843424142226299889606750473108e-69L, -1.5025103938453805607949990395131202e-70L, -1.181175530500916159109188924367755e-71L, -8.9211577919430036730810594588108188e-73L, -6.4693987201487320046787049136480794e-74L, -4.5015731884566473782551022645592059e-75L, -3.0035842209914330912805318957189823e-76L, -1.9204515997414609573256145075523711e-77L, -1.1758810849678264495241195391760596e-78L, -6.8900721571551738196701537405845152e-80L, -3.8608563387442272849431335779050044e-81L, -2.067461647504548936055359104346581e-82L, -1.0572431033554667452601572946421337e-83L, -5.1591788391388680011549213529878416e-85L, -2.4006807351389258318746737135895233e-86L, -1.0644152130176148388326772258538689e-87L, -4.4934529232365451766727877769969084e-89L, -1.8047048722799212873442491872423271e-90L, -6.8904595506732549720140200562795596e-92L, -2.4989637101487869067392480430274382e-93L, -8.6018088469863275574189994840620463e-95L, -2.8078924862967821005632597982452309e-96L, -8.6849867519796128933390271698765342e-98L, -2.5432396109247561096829294407826966e-99L, -7.0446668978619724438762397385539258e-101L, -1.8442018255867670937882313596001159e-102L, -4.5587416725253559276425131397074849e-104L, -1.0631088555204592085750091746032982e-105L, -2.3367281748964035585738800969158434e-107L, -4.8365042631130861220456426369022544e-109L, -9.4175235620877562064839942442771856e-111L, -1.7234789461694177262894389768372263e-112L, -2.961523379734186944047236046386456e-114L, -4.7734609518585313301965336240248189e-116L, -7.2097952822898640408181240050284012e-118L, -1.0193874335777637006841936637337494e-119L, -1.3478166999163432272267137132118888e-121L, -1.664711300732456447679638084338044e-123L, -1.9186583950476330921591900813216499e-125L, -2.0612648015288569084831497840721453e-127L, -2.0618953788663489533345425995316527e-129L, -1.9182619555236359873955912398757981e-131L, -1.6579128778519222560290325566834437e-133L, -1.3296099070094495947742421798539638e-135L, -9.8828871549374803987116212190790475e-138L, -6.8001851936680182899829217843093739e-140L, -4.326204276337993340016401440114041e-142L, -2.541595528522972170757701141043585e-144L, -1.3771279659495821106478129347836407e-146L, -6.8731739624384578591538313057486772e-149L, -3.1556844841718302469140036866580175e-151L, -1.3311046015007009547835866736304136e-153L, -5.1514958005010918440961548583793399e-156L, -1.8267037628056442215789990487980127e-158L, -5.9267728539858017097499689403281403e-161L, -1.7570179057820636782453206639159588e-163L, -4.7525273644644354076088841580861234e-166L, -1.1712151180634311771192081451244071e-168L, -2.6258838722203054808989693856617573e-171L, -5.348036158420190530754215189932846e-174L, -9.8795260619978679822138888646044246e-177L, -1.6528501557776328068085680725354548e-179L, -2.5003947565086958266446692572037419e-182L, -3.414859619193216220277979880289186e-185L, -4.203653605459133226010277835528306e-188L, -4.656508257021667283525304276478024e-191L, -4.6339530996535525354074849617025537e-194L, -4.1358781926049696182200878642464924e-197L, -3.3049447294950072208241398344574307e-200L, -2.3603956208589999494122184847510332e-203L, -1.504047014068835401149660217771386e-206L, -8.5352021958508046391358762569456352e-210L, -4.3057568200632214990724928361534485e-213L, -1.9273628185121124753795720186090423e-216L, -7.6407868488415044113016735817166107e-220L, -2.677582009683177696032426651320389e-223L, -8.2781625043007349263821040953856809e-227L, -2.2534850368391124035175853226989187e-230L, -5.3905685902076735531578346280529237e-234L, -1.1308097276815650308355193143392165e-237L, -2.0759721813705410476824570327172646e-241L, -3.3282687604583975257076029809432931e-245L, -4.6500067135453696640703738061616968e-249L, -5.6491873692239596907540663618927003e-253L, -5.9546966626331923325761672881695778e-257L, -5.4337990425534927929347405657826422e-261L, -4.2828363537638634149994842306436067e-265L, -2.9089854251520548595336724777976923e-269L, -1.6986979835072953491948167874994245e-273L, -8.5078874736023215648629448861985246e-278L, -3.6459301611001785382074525089193986e-282L, -1.3335508003947114825747939690317958e-286L, -4.1528110309781799510004618371028852e-291L, -1.0982617987267680462918374327390562e-295L, -2.4602717457871291167756856167595572e-300L, -4.656274386497415739809125734407304e-305L, -7.4253896458167870494540985632704167e-310L, -9.9507057756639922437150285707977622e-315L, -1.1175135227544318386694717687968224e-319L, -1.048835867763772555877163638261832e-324L, -8.2033390786966798682011027925866189e-330L, -5.3315735131149564162074506318716251e-335L, -2.8710141330746883154448723258150022e-340L, -1.2771564172267081609509990200434409e-345L, -4.679237231988855089392537021634395e-351L, -1.4076706654157792032739797667966913e-356L, -3.4663625720057878756572289263095777e-362L, -6.965054539543846678373236132189486e-368L, -1.1383132196432715526434302555043402e-373L, -1.5082487448329479128870155332131625e-379L, -1.614816076667686258793882820784337e-385L, -1.3923692408287144362457422616397937e-391L, -9.6357705989726650954351553689911432e-398L, -5.3335362887861762022108923833857644e-404L, -2.3529529322735497728866304144280939e-410L, -8.2438250199447939690869259289572704e-417L, -2.2855238208173770201725378922268636e-423L, -4.9955499211081324148253281714818585e-430L, -8.5762081046608095383619080676476731e-437L, -1.1520466338982341020319294100361546e-443L, -1.2062291598612346324429863879453543e-450L, -9.8055160740422284599044523336543234e-458L, -6.1639765190753631642664330172075405e-465L, -2.9843045493133686661298629930164973e-472L, -1.10823285826398300175507717773449e-479L, -3.1434790244922162638267628745063788e-487L, -6.7817090417200778282332735946819014e-495L, -1.1080137352329998556662834370761201e-502L, -1.3649835831781010441492893639688094e-510L, -1.2622778335796144231373603506854352e-518L, -8.7230079476014413312144088584965706e-527L, -4.4840352485865235615809427931860355e-535L, -1.7066304212439665043951442710341263e-543L, -4.7865441809026242359826342326024693e-552L, -9.8452970343625269250999633839131245e-561L, -1.4778790578536059527306608667934244e-569L, -1.6110092952268207636105891878784886e-578L, -1.2688728710128977236628155987138424e-587L, -7.1841678295696924944920506687608424e-597L, -2.9088109305034413675847131931270509e-606L, -8.3780318645305679545954425238306175e-616L, -1.7073663715760515568257862032253151e-625L, -2.4485200806811154999909042531599294e-635L, -2.4573647256627145606915141874939106e-645L, -1.7162616532700922219515556951255632e-655L, -8.2940336462928562559347382857731944e-666L, -2.7573883713227551117703313812478972e-676L, -6.2693384642795198624500212806556514e-687L, -9.6903310752055252797176389760001952e-698L, -1.0120668627816313397657974563178381e-708L, -7.0982696439175036135044214112413552e-720L, -3.3223490979680344536621296663739436e-731L, -1.0311489910731877825037981367801772e-742L, -2.1084888364587342412345374253683016e-754L, -2.8218960738946335538305090801367889e-766L, -2.4554534404272344111007341068959851e-778L, -1.3797451463824018308120568764941013e-790L, -4.9722352119016468411483808073270903e-803L, -1.141174632389420312821039197546226e-815L, -1.6562061873208393415939125326414615e-828L, -1.5090517973695404620062521847072836e-841L, -8.5691467995207824570732116071300356e-855L, -3.0100992462861676532185696320551197e-868L, -6.4915591914227985933992989098330185e-882L, -8.5291480289311605733387922175211764e-896L, -6.7742561345607759008586516480118676e-910L, -3.2268177845742615217916640601654205e-924L, -9.1442367835386354685297667090946292e-939L, -1.5290755306231257347546172504187466e-953L, -1.4962742317658249855750971462991461e-968L, -8.4962983613273098681909435342233043e-984L, -2.7756354317962233287713988508361331e-999L, -5.1716412864088598574901876510481632e-1015L, -5.4473756239469544256829106354679121e-1031L, -3.2146847397534380657713310981627524e-1047L, -1.0532205872897567235295617558264118e-1063L, -1.8980407364210960924624682689327296e-1080L, -1.8638413013078585989318566769757098e-1097L, -9.878184707808045512821781472334627e-1115L, -2.7982877400590951555125003865089596e-1132L, -4.1953712551999192397935862947005203e-1150L, -3.2957901195530958784930344030449164e-1168L, -1.3428874261296622720955452657433921e-1186L, -2.8087996369552811844908096236163199e-1205L, -2.9843000347844479031722692316121269e-1224L, -1.5935747451543053300901499796204061e-1243L, -4.2306328294965009362324717933882302e-1263L, -5.5228314338471652594276889712636454e-1283L, -3.5058000799426290470482678197659356e-1303L, -1.0699175488609417213076569958022624e-1323L, -1.5518242259547372491845055361631511e-1344L, -1.0572462818562698193769229028835047e-1365L, -3.3433625974012960853422590535670218e-1387L, -4.8485886276278601232637821173583126e-1409L, -3.1852238059952872764052353398865978e-1431L, -9.361416530125180143871854771737577e-1454L, -1.2153997660730855590852157017720519e-1476L, -6.8815302334269492601957883930533625e-1500L, -1.6771176516091137975239852271122166e-1523L, -1.7361595430709725552633953103091938e-1547L, -7.5319512846392125534714956610857413e-1572L, -1.3507317243577349648175397566286196e-1596L, -9.8749002116827156793758713498543268e-1622L, -2.9017600444094134408965714990369305e-1647L, -3.3784846386968340746897973924953046e-1673L, -1.5359688750531830898643386942465616e-1699L, -2.6866527047176469998545109651176216e-1726L, -1.7810521996687791658708980830532171e-1753L, -4.4069918722394069695147388355206565e-1781L, -4.0074519225536368666632735822098734e-1809L, -1.3182831725717259025871898011803224e-1837L, -1.5438654487729921345949019930471491e-1866L, -6.332964675016807525269001303450335e-1896L, -8.9500787015064447527716544282145449e-1926L, -4.2852989232796780762796817666627905e-1956L, -6.8338809894056785809735926537843468e-1987L, -3.5675222855473848333391077011021476e-2018L, -5.9902029406713556414824109568504936e-2050L, -3.1778560977483191067544886656870642e-2082L, -5.2307612099795388778239926501377221e-2115L, -2.622585004749696786707859063948337e-2148L, -3.9309566721122472026471743065924237e-2182L, -1.7282752365752259330631889016659569e-2216L, -2.1861776117108222661355073061235227e-2251L, -7.8018157093958177389763723299669248e-2287L, -7.6999746209936272927151686536635957e-2323L, -2.0595655884579572710261610678442846e-2359L, -1.4626001721502482969651469854201664e-2396L, -2.7006580242811967764786623573354003e-2434L, -1.2693899148553691566069699600055988e-2472L, -1.4864297410986916900605959894904808e-2511L, -4.2424104158612592858897827763816223e-2551L, -2.886325476737890820535159860850169e-2591L, -4.5765223979102481270954678544866293e-2632L, -1.6528081850614599523381524655646053e-2673L, -1.328274798653407243855327660531847e-2715L, -2.3198223770931618563520410588946674e-2758L, -8.5957488899967850735558558018957728e-2802L, -6.5943404261286890309954636455911131e-2846L, -1.0217558991001370161903847475864815e-2890L, -3.1179630912672502182141115573865598e-2936L, -1.8265451376704665882357813599889357e-2982L, -2.0014192526716340395460104532447404e-3029L, -3.9951106676925283777648320420658919e-3077L, -1.4143501265341338386948746898388465e-3125L, -8.6415836958539035920266027922282148e-3175L, -8.8638553988044639042147624946934825e-3225L, -1.4840219096599721556289490714622718e-3275L, -3.9413909056885523526277851486916637e-3327L, -1.6130887141803206541250840542937335e-3379L, -9.8781642524919349085295532219577414e-3433L, -8.7843931949057976239401506792380659e-3487L, -1.1004454490075535460405971636548993e-3541L, -1.8829655853829584691138469084815955e-3597L, -4.2649913833758498152779971325727165e-3654L, -1.2387002826718376930423594688980877e-3711L, -4.4662108799989251681924501424182082e-3770L, -1.9344990293606773314152567555369947e-3829L, -9.7355570095915342874542008807254313e-3890L, -5.5029370896508671666190160013096548e-3951L, -3.3753310533018114196752016778611603e-4013L, -2.1693889909489136218749502032440802e-4076L, -1.4100342946015435859763977543837031e-4140L, -8.9396827455669883373190732493946026e-4206L, -5.3296285690324414372171445344916662e-4272L, -2.8786292587718554179132949540534825e-4339L, -1.3563301511005327997304553445712977e-4407L, -5.3648093624713363852431573541370394e-4477L, -1.713199655224600838900218899825865e-4547L, -4.245367036181261378573090733274636e-4619L, -7.8414039345064814928186445860311876e-4692L, -1.036301574835369426138520680597679e-4765L, -9.4005882200264089643362665220611737e-4841L, -5.6115028230432295314938587541868893e-4917L, -2.1117808872654504815205210716165957e-4994L, -4.7969059512296546218382678391152525e-5073L, -6.2923913054777813860480394257938674e-5153L, -4.557320132561115296699430714437428e-5234L, -1.7411358133348974154538037089867482e-5316L, -3.3501410753353566412413655174778596e-5400L, -3.097148592957890545441753638107994e-5485L, },
   };
   m_weights = {
      { 1.5707963267948966192313216916397514L, 0.23002239451478868500041247042232167L, 0.00026620051375271690865701015937223316L, 1.3581784274539090834221967874745002e-12L, 1.001741678406625296380989561316704e-35L, 2.6763080920617460968679410949198166e-99L, 7.7670706886334062872146243844453043e-273L, 2.6770629459428179490864940513366914e-745L, 2.497123188557279400555877663164169e-2030L, 3.7829658019347837822103381908509695e-5524L, },
      { 0.96597657941230114801208692453802948L, 0.018343166989927842087331266912053799L, 2.1431204556943039357697233307232118e-07L, 2.8003151019775889582580016992170153e-21L, 1.123270534548691878982747435678734e-59L, 9.1753268750017841272445320853712195e-165L, 3.7096469071314047287189555701359196e-451L, 2.1358827779704788581082183250272886e-1230L, 2.4637500105830058174530832607403982e-3349L, },
      { 1.3896147592472563228608191295320513L, 0.53107827542805397476113231761531408L, 0.0763857435708323041883405748316428L, 0.0029025177479013135935932948904580215L, 1.198370136317072004690126421734261e-05L, 1.1631165814255782765597155262382926e-09L, 2.197079236297979917409204112478354e-16L, 1.3635103307637615413724747655081585e-27L, 3.3700568540419264989934173928550566e-46L, 5.1969783800898552138641449339116869e-77L, 6.0080341705713501485949327691027087e-128L, 4.564204056355599097208981999351872e-212L, 6.769934297924075481071769868728861e-351L, 6.3189772257105317991231885214248323e-580L, 1.1318535773666956837787222565063026e-957L, 1.300088515992481474129980450433913e-1580L, 8.3018817290920015915795630666821309e-2608L, 1.6615718540023971313123206033705184e-4301L, },
      { 1.5232837186347052131949627901588453L, 1.193463025849156963909371648222972L, 0.73743784836154784136450085848526681L, 0.36046141846934367416541940530511192L, 0.13742210773316772341110281600075763L, 0.039175005493600779071814125724013544L, 0.0077426010260642407123309111640668157L, 0.00094994680428346871690539180358829065L, 6.2482559240744082890784437584871091e-05L, 1.8263320593710659699109280974494727e-06L, 1.8687282268736410131523743935312467e-08L, 4.9378538776631926963708240981686386e-11L, 2.2834926702613953995564216678996858e-14L, 1.1227531428181551500942554523402945e-18L, 3.0976539701173543715458514327631078e-24L, 2.1121233435372255913526811977623162e-31L, 1.2424147570616052367007700396344415e-40L, 1.6327707331799493237812947626932747e-52L, 8.460688731096213796022276271481337e-68L, 1.8644492079588650273068038890475311e-87L, 1.0013128468666430206628687242136761e-112L, 3.3344435411868990213060767519646709e-145L, 6.1751576225487765303410376146008819e-187L, 1.4953240022257075856966174469213765e-240L, 1.995167671391421283850079422966012e-309L, 6.7986022131150125295165555843516628e-398L, 1.6112168443011532782661941324936245e-511L, 1.8998420056198456993096891152100817e-657L, 7.4500584401117916806764514072363403e-845L, 1.6092274599867451896713421807328385e-1085L, 1.4294469100875426784155324297970964e-1394L, 1.9699812322172327797426511042200449e-1791L, 4.8353547995000100603312087832016511e-2301L, 2.0016297694765957880783422493672502e-2955L, 1.0619575437766938720176582011701185e-3795L, 1.1494098249640889641399434474174788e-4874L, },
      { 1.5587733555333301450624222553039117L, 1.4660144267169657810275411936658895L, 1.2974757504249779979885383085284353L, 1.0816349854900704074448532749336471L, 0.8501728564566200689527310980522113L, 0.63040513516474369106015058023920241L, 0.44083323627385823706771270610993222L, 0.29024067931245418500061231479966878L, 0.17932441211072829296345978397121765L, 0.10343215422333290062482385052951418L, 0.055289683742240583845301977440481557L, 0.027133510013712003218708018921405436L, 0.012083543599157953493134951286413131L, 0.0048162981439284630172757660071387715L, 0.0016908739981426396472155417510249034L, 0.00051339382406790336016588906448858883L, 0.00013205234125609974878680402074340758L, 2.8110164327940134748736157546988307e-05L, 4.8237182032615502124025440343943169e-06L, 6.4777566035929719907733987417697432e-07L, 6.5835185127183396672340995882066949e-08L, 4.8760060974240625868904606426809347e-09L, 2.5216347918530148571826656491854398e-10L, 8.6759314149796046501956077624778843e-12L, 1.8802071730750649809476255843771975e-13L, 2.4124230384308786393899307730550295e-15L, 1.7084532772405701711664118263431986e-17L, 6.1682568490762382593952567143955272e-20L, 1.0376797238528706160610863270915827e-22L, 7.3459841032226935608549262812034877e-26L, 1.9497833624335174811763249596143101e-29L, 1.7024387761257547218675324410867209e-33L, 4.2164863709484278882204462399998679e-38L, 2.5044277116275284317811847533784782e-43L, 2.9493601457461933137195159567099426e-49L, 5.5513323569653710605229337238942258e-56L, 1.3081165120210821643686110907899211e-63L, 2.9260824463823975328629659452367614e-72L, 4.5407734669997824148941045053504133e-82L, 3.4265635692086987660673772586083229e-93L, 8.4064359488831769989074631453464805e-106L, 4.2486192688198694407012327873632857e-120L, 2.6378208263504521457685936139412266e-136L, 1.1199291455841279757620547508811484e-154L, 1.6741593786710889281267003484320699e-175L, 4.1533060810194922183660114746657927e-199L, 7.291512660905660427014583724901483e-226L, 3.4484028358868219730957875473275039e-256L, 1.470608613733186341839899819856118e-290L, 1.6363373245553192894384271318647291e-329L, 1.1653396430451048019810427186628332e-373L, 1.0806491346819681711718251969578419e-423L, 2.1475318157612117601623532784345252e-480L, 1.1837197513952188698387978224315556e-544L, 1.7840752052025753295716828917620433e-617L, 5.3242634124221408997248254448704286e-700L, 1.6062136117391783778620623735552911e-793L, 1.6828070526919983211589685788034629e-899L, 1.3428023135061849684751687999574317e-1019L, 1.0762445467071890806353267455766392e-1155L, 6.4211043768224511497561388149947945e-1310L, 1.0999298719684690155223457966483755e-1484L, 9.9551400313164741067089535072684105e-1683L, 3.7874715825152699680401002242066869e-1907L, 1.8633554093895907210742600678288691e-2161L, 1.2425018321859109200647073828888711e-2449L, 3.4744660691878314268031415759661457e-2776L, 3.1635539874917104171786237474568114e-3146L, 1.520164792309349080207015729831993e-3565L, 1.0587692108054511977730210493803509e-4040L, 3.9269571376984945676082618447700578e-4579L, 2.9165229925801338811576465571535852e-5189L, },
      { 1.56778143130722185718457839560813L, 1.5438811161769592204120195652304554L, 1.4972262225410362896175121106253706L, 1.4300083548722996676294145121698845L, 1.3452788847662516614631881421588284L, 1.2467012074518577048171373166756783L, 1.1382722433763053733718328301717509L, 1.0240449331118114482594022454942541L, 0.90787937915489531693097972059732257L, 0.79324270082051671787385259862995589L, 0.68306851634426375464118893187202369L, 0.5796781030877876470811045242977012L, 0.48475809121475539286590775419868886L, 0.39938474152571713514696619655275183L, 0.32408253961152890401776015000060852L, 0.25890463951405351600251387258910213L, 0.20352399885860174518647884913232324L, 0.15732620348436615026738563376259734L, 0.11949741128869592427594275905822144L, 0.089103139240941462840959442033942474L, 0.065155533432536205042255277931864103L, 0.046668208054846613643791300289316396L, 0.032698732726609031112522702100248949L, 0.022379471063648476483258477281403216L, 0.014937835096050129695520628452448085L, 0.0097072237393916892692355786307589937L, 0.0061300376320830301252445771940873246L, 0.0037542509774318343022967144602791306L, 0.0022250827064786427021584620411703896L, 0.0012733279447082382026740903535577353L, 0.00070185951568424227080474304718567332L, 0.00037166693621677760301295760218968355L, 0.00018856442976700318571529922115575474L, 9.1390817490710122732277133049597672e-05L, 4.2183183841757600604161049520839395e-05L, 1.8481813599879217116302847163026167e-05L, 7.6595758525203162561568606199506296e-06L, 2.991661587813878709443954037223499e-06L, 1.0968835125901264731967931401061751e-06L, 3.7595411862360630091183817668146341e-07L, 1.1992442782902770218679992206070689e-07L, 3.5434777171421953042822362946016499e-08L, 9.6498888961089633609206181967630592e-09L, 2.4091773256475940778596088439332178e-09L, 5.482835779709497755012456926288581e-10L, 1.1306055347494680535789879497045831e-10L, 2.0989335404511469108589724179284344e-11L, 3.4841937670261059685298917826974968e-12L, 5.1341275245014207474276938761808301e-13L, 6.6639922833087653243643099189129361e-14L, 7.5567217757805651894455261838657557e-15L, 7.4209932309922167588538662071098972e-16L, 6.2528048446104553564782731960667282e-17L, 4.4757595066690969713939566842636666e-18L, 2.6931206614869695057714525810417824e-19L, 1.3469941569542286092134858963015562e-20L, 5.5335834994155711455633228489644644e-22L, 1.843546974718149378096179376158747e-23L, 4.9139368712649040083308200957748574e-25L, 1.0329391306928575387668383646132905e-26L, 1.686277003849260652771901316947161e-28L, 2.1033057490018089523843332100004934e-30L, 1.9699209796232343253581550830906289e-32L, 1.3599894616303795697940905597197162e-34L, 6.7859788375592479085540582968820347e-37L, 2.3965063699443217406067231991536888e-39L, 5.8579569483084210846386345961131375e-42L, 9.6783927755717095571366983570964434e-45L, 1.0538361132564208838364959652125833e-47L, 7.3615858309787649210787574154381217e-51L, 3.2059785352833866133968160706885294e-54L, 8.4430892661864256082234111135836121e-58L, 1.301669487442817299199572925297743e-61L, 1.1348985048372046635260385887339235e-65L, 5.393881369511580841024486229556929e-70L, 1.3438019476233711028911671531262384e-74L, 1.6833095936633240263022508251714738e-79L, 1.0142059074468158538185578436642225e-84L, 2.8036136193449874498478125381332892e-90L, 3.3815387893836694650191340608232241e-96L, 1.6868699597973134851614068554314712e-102L, 3.2876716054636550397600067563177134e-109L, 2.3561803975866304919776634857937977e-116L, 5.8212714268864182676007475006940935e-124L, 4.6289769416942015466154791282940135e-132L, 1.1011712417530966759846558629187253e-140L, 7.2497708296024463973360079596584377e-150L, 1.2159344493950079010263214501254854e-159L, 4.7567332287785056252666937863445785e-170L, 3.9513561319289952903145090015894379e-181L, 6.3069398037899023031641976078084843e-193L, 1.7390958115772548420234336819215406e-205L, 7.3973824490445198187310257149181993e-219L, 4.3025693007354699025906405899973485e-233L, 3.0098295695832487716215316168466764e-248L, 2.2089957315907529731668895943677893e-264L, 1.4707383302537016829517267662493224e-281L, 7.6092047493292429607812922938663979e-300L, 2.5944582029845183791860228756757574e-319L, 4.8919577835343219860639698905291437e-340L, 4.2321357135722486316129327838961609e-362L, 1.377082367391413218915156512362392e-385L, 1.363952742750262175587655506596678e-410L, 3.282973957767873844612670506024268e-437L, 1.510934727927437884800902577518545e-465L, 1.0301501430760404101952742972682365e-495L, 7.9295801879971179076172461167077394e-528L, 5.1606434722064335409175586840009083e-562L, 2.0872322362104964031726231732635154e-598L, 3.7804014749166388131317806793058942e-637L, 2.1632858420984952938030309932161571e-678L, 2.697969237867160734724398915290777e-722L, 4.939067732330739558253971083984646e-769L, 8.7137367268607555569549275663836614e-819L, 9.4666568790742115790866970449104673e-872L, 3.9314930560193364712728646099681646e-928L, 3.7572363915786555422592370375561427e-988L, 4.8138810542737654527596947066774263e-1052L, 4.6523364803809138193102091214841167e-1120L, 1.8387400682879078406702806989874382e-1192L, 1.5488882086634896755007358131079033e-1269L, 1.3896133256118102918631384214217437e-1351L, 6.3450498372554790778619758345952674e-1439L, 6.718192925085413277150654870462718e-1532L, 7.1439377408604826875747242429241183e-1631L, 3.1307195483169831435149812630603435e-1736L, 2.1906656484419255825224621505729532e-1848L, 8.9202911849845695393803303860897414e-1968L, 7.2181700833126514821590990431204877e-2095L, 3.69827933239331943547271842529346e-2230L, 3.5509166675222604652849078491152928e-2374L, 1.7482004370958645792029686167921119e-2527L, 1.1106891190500999916896792610406226e-2690L, 2.0967298422181990469075373625054175e-2864L, 2.4632275747625679891658308849723178e-3049L, 3.4100009144092401643961247399658256e-3246L, 9.461422635018898458311235983749575e-3456L, 7.9828243452163492698389979442314978e-3679L, 2.751586075619233132202409409236628e-3916L, 4.573406478327970954594749406297331e-4169L, 3.7694660070555500868572517438534528e-4438L, 1.3681877706178551350653622286560967e-4724L, 1.6613468385141710911219854343435161e-5029L, 4.3416310672295866779590889330326041e-5354L, },
      { 1.5700420292795931467492986437735064L, 1.564021403773232099877114326609257L, 1.5520531698454121192208571607383634L, 1.5342817381543034316353772145375646L, 1.5109197230741697127061791873634288L, 1.4822432978855380699918515557505545L, 1.4485862549613225916106262684975582L, 1.410332971446259012949044799400295L, 1.3679105116808964880788896668731933L, 1.3217801174437728578945834791848425L, 1.2724283455378627082346213018713573L, 1.2203581095793582207386389532998567L, 1.1660798699324345766327658650910729L, 1.1101031939653403795568765186098518L, 1.0529288799552666555511877000344537L, 0.99504180404613271513656897365437693L, 0.93690461274566793365666309492163748L, 0.87895234555278212039056482196455926L, 0.82158803526696470334184690990337618L, 0.76517929890895613670160403610671453L, 0.7100559012054689838533558330696999L, 0.65650824613162753075637187406975179L, 0.60478673057840362157663695141011708L, 0.55510187800363350959266471517172014L, 0.50762515883190809969744129508607212L, 0.46249039805536776129564056825534108L, 0.41979566844501548065589545493380249L, 0.37960556938665160999227127370288764L, 0.34195379592301683230370363663700268L, 0.30684590941791694932077002517250017L, 0.27426222968906810637090960121437003L, 0.24416077786983990867520612813967892L, 0.21648020911729617038131524528780352L, 0.19114268413342749532225095746508781L, 0.16805663794826916233031413596643794L, 0.14711941325785693247796311096185123L, 0.12821973363120098675278273881295278L, 0.11123999898874453034874054225116091L, 0.096058391865189467849178662072631058L, 0.08255078811070173765353752357300814L, 0.070592469906866999351840896875301286L, 0.060059642358636300319399035772689021L, 0.050830757572570471070050742610585547L, 0.042787652157725676034469840927028069L, 0.035816505604196436523119991586426561L, 0.029808628117310126968601751537697736L, 0.024661087314753282510573737140694345L, 0.020277183817500123925718353350832364L, 0.016566786254247575375293749008565117L, 0.013446536605285730674148393318910799L, 0.010839937168255907210869762269731544L, 0.0086773307495391815853559138369297225L, 0.0068957859690660035329120415695881464L, 0.0054388997976239984331249261778682035L, 0.0042565295990178580164922070775372331L, 0.0033044669940348302363413922293941345L, 0.0025440657675291729677737674183717872L, 0.0019418357759843675814337234877431187L, 0.0014690143599429791058440789865212569L, 0.0011011261134519383861925290686638441L, 0.00081754101332469493114796258629778861L, 0.00060103987991147422573401527665731798L, 0.00043739495615911687786086941160964625L, 0.00031497209186021200273816328980956318L, 0.00022435965205008549104107147410537586L, 0.00015802788400701191949230617575051454L, 0.00011002112846666697224455022994264118L, 7.5683996586201477788165374446595019e-05L, 5.1421497447658802091816641948792101e-05L, 3.4492124759343197699875355951697823e-05L, 2.2832118109036146591351441225788429e-05L, 1.4908514031870608449073273504082825e-05L, 9.5981941283784710776464665022285646e-06L, 6.089910032094903925589071885768365e-06L, 3.806198326464489904501471179191111e-06L, 2.3421667208528096842717147966610279e-06L, 1.4183067155493917523149875336299115e-06L, 8.4473756384859863469243774523267053e-07L, 4.9458288702754198508296104158884319e-07L, 2.8449923659159806339254761906866073e-07L, 1.6069394579076224910854372243815059e-07L, 8.907139514024238712375244371846641e-08L, 4.8420950198072369668651027805560416e-08L, 2.5799568229535892380414249079124995e-08L, 1.3464645522302038795727293982948898e-08L, 6.878461095589900111136392082034183e-09L, 3.4371856744650090511359612982214485e-09L, 1.6788897682161906806903450478834801e-09L, 8.0099784479729665355704238927483163e-10L, 3.7299501843052790038025133658401989e-10L, 1.6939457789411646875651299532593942e-10L, 7.4967397573818224522463983518852213e-11L, 3.2304464333252365759775564205312505e-11L, 1.3542512912336274431500361753892705e-11L, 5.5182369468174885820640573102698266e-12L, 2.1835922099233609052099969606827855e-12L, 8.3831289605026670935474089966848491e-13L, 3.1194977286848081234778051650691241e-13L, 1.1240208959922861475529576279412917e-13L, 3.9176794506016467939451671035436525e-14L, 1.3194342231967989407842390664342776e-14L, 4.2891962220679080018159948630799523e-15L, 1.3443222875395221462210806176730482e-15L, 4.0575577022628576213784976604019497e-16L, 1.1779812127248348143079542926522782e-16L, 3.2853861628847006575461912014493979e-17L, 8.791316558919890092526902998792709e-18L, 2.2540748304368819446158103388678448e-18L, 5.5301769128403375894905317866280607e-19L, 1.2964527140689369428189650628176523e-19L, 2.8999645564315719864785175397007916e-20L, 6.1801432493399884472690523294170016e-21L, 1.2528676432273210433625630071013181e-21L, 2.4122505468361010054762467348044815e-22L, 4.4039066999398680900169681674054685e-23L, 7.6105778075820625755732634136772293e-24L, 1.2428051652123164945236765528871363e-24L, 1.9143106902239760676488034727771211e-25L, 2.7761251025850583169467065718383927e-26L, 3.7831240728137797052283562714381636e-27L, 4.8349101548188476652448113863661033e-28L, 5.7831786972290529603663947121744896e-29L, 6.4605757034417219896312763235825947e-30L, 6.7260373895879405356485977108634093e-31L, 6.5111534511374516546657070065569591e-32L, 5.8474090745481019884619654259625198e-33L, 4.8600460551422733352577609809902488e-34L, 3.7292395298436024198774848925631642e-35L, 2.6351230616803666949611566230315689e-36L, 1.7101926401490697241365293331985362e-37L, 1.0166685309552127228580017693019009e-38L, 5.5206909443427625675929025278647327e-40L, 2.7304755116605008208950089540576932e-41L, 1.226380966652512847885874020657185e-42L, 4.9868392983536125725869145787511357e-44L, 1.8300654157711633015675688730645846e-45L, 6.0413503225082640435207520715838788e-47L, 1.7880003085257126775048837670283103e-48L, 4.7278206635290633557485891499143011e-50L, 1.1129101718049771950203375840916927e-51L, 2.3236007287936054634490409808175661e-53L, 4.2865792135438400818864832679645293e-55L, 6.9598735225537515535247172084789902e-57L, 9.905399812867464416784441328019894e-59L, 1.2305690322370172647654483736261024e-60L, 1.3287055463682438112745418077294776e-62L, 1.2413844609001762091919818631042531e-64L, 9.9894889830989163005162036230629025e-67L, 6.8909792890209806239542239718708282e-69L, 4.0550412837679391530484571616360602e-71L, 2.025325478051833791855348665597344e-73L, 8.541193860817129005635611395334028e-76L, 3.0250584958478075188394058484674014e-78L, 8.9481574983737847286009199268691343e-81L, 2.1980365957192520850759686078382165e-83L, 4.457339084251821318983583351230449e-86L, 7.4167316712100725345384814229429518e-89L, 1.0062789446470429122974386215670469e-91L, 1.1060617024861203148604052551620709e-94L, 9.7834581723103753096719332058265664e-98L, 6.9161160403270601376643047091198938e-101L, 3.8797049192578037362892459079447827e-104L, 1.7144044358094688607777762897347039e-107L, 5.9226551398143962456841752185003654e-111L, 1.5871361682624074916136417159312335e-114L, 3.2726893534998181040216294108876001e-118L, 5.1496220394807877679493913965250783e-122L, 6.1305346175987605932476180523733874e-126L, 5.473030846543808520009167994615492e-130L, 3.6307475309683583384111588486160093e-134L, 1.7730007537696346688915570033698743e-138L, 6.3116464532127354831381913256985002e-143L, 1.621583693919220194566929433171311e-147L, 2.9757935132829859613289387859126523e-152L, 3.8591705765442151638142119206858087e-157L, 3.4980555618589782147605871793646301e-162L, 2.1911032417571959066795311563828556e-167L, 9.3736234796108920474724971094564098e-173L, 2.7058522933326574274299142121455261e-178L, 5.2051004982716048091082896972654894e-184L, 6.5869897226619604622569213699928695e-190L, 5.4113353348952773541650437440862393e-196L, 2.8465905848751190783736476660662487e-202L, 9.4537279066777943316631814110533656e-209L, 1.9534309746768722817386272635097574e-215L, 2.4738194858760899904808482337660157e-222L, 1.8904418888763632604075097028006049e-229L, 8.5786751348486017374921228218675884e-237L, 2.273808044980070953576133599904938e-244L, 3.4605977152655857787870383972443829e-252L, 2.9714191752129370394435745626764138e-260L, 1.4135174726041132417612082341294276e-268L, 3.6561278873359158194812461400887331e-277L, 5.0434133108713706852575809257966216e-286L, 3.6370069739853174907232860458211075e-295L, 1.3431929449017207014618114298104235e-304L, 2.4870346219017639693976883745872467e-314L, 2.2586869384020337841470045671934569e-324L, 9.8364979621195023902799369645695391e-335L, 2.0067948188168259586571751290590148e-345L, 1.8723608312818058521936506625581294e-356L, 7.7931764302539272294837164978121943e-368L, 1.4104242105367751421756306559719189e-379L, 1.0809707526373134180302026203191734e-391L, 3.413982422671325929147843223533059e-404L, 4.3198627266120931546631293969877099e-417L, 2.1273069601360357294162339387648629e-430L, 3.9566885872209056027370422510981332e-444L, 2.6949582298096968115536767242364878e-458L, 6.5109076931010576854739908270299986e-473L, 5.3989788614177770156022874990436434e-488L, 1.4853198669149487016912694923346188e-503L, 1.3090503251521584428930352496581121e-519L, 3.5647376837004158357511297570921346e-536L, 2.8896313028497521005627356654844694e-553L, 6.7095854399465738026181868874992309e-571L, 4.2889584337257558657454644184109877e-589L, 7.2448311439514538923291336946008664e-608L, 3.1001155749336353171177254469615123e-627L, 3.2171491615676533889663223546563542e-647L, 7.7406570695117283869992601856060778e-668L, 4.1223744692343859730646543586548491e-689L, 4.6322294010052851367822679106298139e-711L, 1.045335659900684312871171221243375e-733L, 4.5020856241223108492475174506019875e-757L, 3.5109989183708310084991820318792658e-781L, 4.6962204836998229475507359520919518e-806L, 1.0187348472076847046857363810773889e-831L, 3.3829163365599114939619605636207329e-858L, 1.6201873291670617651278341356078551e-885L, 1.052417929633641351707580294560769e-913L, 8.7019793416937745490332209560882093e-943L, 8.5790292111351760552267505428090183e-973L, 9.4260553303166607465714226577573373e-1004L, 1.0765751303003708312349678449534298e-1035L, 1.1895201137046793178631747385045816e-1068L, 1.180625613342395519601533530260917e-1102L, 9.7508687447891759767569844552344533e-1138L, 6.1928172812184124261845708258990883e-1174L, 2.7879318459600816091819660853849943e-1211L, 8.1797014327573742781475939847205791e-1250L, 1.4341956738449380463933781088853353e-1289L, 1.3742136648085548142182437043687567e-1330L, 6.5614725919712462874880606553304539e-1373L, 1.4193967474068871671305637677852339e-1416L, 1.260966346895598155305400631354573e-1461L, 4.1570767913697591845636153183534885e-1508L, 4.5808704947181685782204593123998747e-1556L, 1.5147204987797220696721543058866571e-1605L, 1.3446319068528240765599796700835479e-1656L, 2.8568551875090842255902159455984979e-1709L, 1.290420656637587734011142529706619e-1763L, 1.0965883695419688652509296670806449e-1819L, 1.5454358117029190110066857968185965e-1877L, 3.1713220921953507873149502965607843e-1937L, 8.2852363729842743064713740610323336e-1999L, 2.399312892921620764854020931161764e-2062L, 6.6760404808499701150348715300290141e-2128L, 1.5401518790175723653421594673899941e-2195L, 2.5301735371980282112359832727046555e-2265L, 2.5299480120770166824316075523796362e-2337L, 1.3095313462113548374850512340351666e-2411L, 2.9689243920149770430661749727744362e-2488L, 2.4813830291085919140036931759581715e-2567L, 6.3996445311970094107659804037592786e-2649L, 4.239251909781851582408879081262827e-2733L, 5.96853928335010438943934366176163e-2820L, 1.4691168838129215779634944240558196e-2909L, 5.1680311504236953911307750231831853e-3002L, 2.1104104354704386557845763111476113e-3097L, 8.0724923764466854235161222774323272e-3196L, 2.3180110258875131728741090820512076e-3297L, 3.9765565093255272984730390679523982e-3402L, 3.2199629992549034490326426922965119e-3510L, 9.6508721511747631002418659479792544e-3622L, 8.3314890242471693358884470423786766e-3737L, 1.5992989019877436694379940011582504e-3855L, 5.2267357291195312826488098055916878e-3978L, 2.2079407038664800796624257814276463e-4104L, 9.0732856778373185742790691459304111e-4235L, 2.7052547418764871675136904567273848e-4369L, 4.3243575109951310431228833829026739e-4508L, 2.7122975060794559909023695709869724e-4651L, 4.8370932158845122662637153824534517e-4799L, 1.7593494088080091822518592461420403e-4951L, 9.2629313370158738463326647490577837e-5109L, 4.9562745065321184922933588793911493e-5271L, 1.8710195150434747289751408668152425e-5438L, },
      { 1.5706077165382752220570950499753154L, 1.5690996953516691278086267057102919L, 1.5660882389174613671811758395264413L, 1.5615824934918106190924913174771039L, 1.555596114631660422166342990902623L, 1.5481471912355573327293783013569808L, 1.5392581453118818324345492520631963L, 1.5289556083545807159292699900112433L, 1.517270275405054680346497720954261L, 1.5042367380636772129345718219774013L, 1.4898932978832971219484878984168431L, 1.4742817617280797281467109946343097L, 1.4574472208125486663314082491076236L, 1.4394378152464069775161779990628486L, 1.4203044859996911837480782185431865L, 1.4001007162694446513758901419788661L, 1.3788822642731375176779450629832807L, 1.3567068895156054674526777181888561L, 1.333634074575756135382992612713127L, 1.3097247444374397383128243464895219L, 1.2850409853467272312421857577496357L, 1.2596457651166706020800416735545885L, 1.2336026567219467107844443624642869L, 1.2069755669313082254213358903568052L, 1.1798284716173337422807600081541025L, 1.1522251592625474295137962600991148L, 1.1242289840506032580653098282178496L, 1.0959026297929722120123658782081807L, 1.0673078857975038741099002717210955L, 1.0385054356373923292061824367921921L, 1.0095546596294298182250582016592233L, 0.98051345168085502517972727278507908L, 0.95143805101635270871239123838108163L, 0.92238288915245514551412028761663168L, 0.8934004523471959982711625120192134L, 0.86454115961966899850156197132807295L, 0.83585325630826713062996261999715574L, 0.80738272301876303826323728461609798L, 0.77917319970479798076341140529382576L, 0.75126592452435685914062625329355162L, 0.72369968702683029319553573992504406L, 0.69651079514654688482101628454709857L, 0.66973305541029099783625527361406845L, 0.6433977657082528556344204308635143L, 0.61753371992990889983731720244260268L, 0.59216722372820694935702418127019L, 0.5673221206467387178357679693601802L, 0.54301982782484286720274321665461387L, 0.51927938048424633084628868358974812L, 0.49611748439731621394870318175376878L, 0.47354857554061400457926695177211335L, 0.45158488614754491438064885165412276L, 0.43023651638978897817371690879635701L, 0.4095115109381936343721381503891959L, 0.38941593967921202782661222737925564L, 0.36995398189211387219291724401952693L, 0.35112801322442524717390619924264052L, 0.33293869483774779538113623624821976L, 0.31538506413267813525698287867505706L, 0.29846462649944498394951741316395666L, 0.28217344757959610491269401677566302L, 0.26650624556313516141887797351312194L, 0.25145648308451039819955104711453138L, 0.23701645831941898915989673276556716L, 0.22317739492218458776987874976238264L, 0.20992953048020753663062039840873861L, 0.19726220319743719884124330734112999L, 0.18516393655277551537189585427079996L, 0.17362252171163130317128688392273587L, 0.16262509749938432420455409981591821L, 0.15215822777419972032806536862862315L, 0.14220797606340183123695433555896409L, 0.13275997735244552637840297048350341L, 0.12379950693841296100725547935434371L, 0.11531154628093733739002418825877215L, 0.10728084580255643428531503966925542L, 0.099691984607788577667458210957830992L, 0.092529427105778463900867988199890184L, 0.085777576535268189772833000772228023L, 0.079420825403008155143300459369002492L, 0.073443602857638774779152196546779989L, 0.067830419030657966460701735648315918L, 0.062565906384455108312883097272720571L, 0.057634858114654726990360882355019006L, 0.053022263660287178654865336510427632L, 0.048713341380701431454862729860209054L, 0.044693568462765533522590231699541224L, 0.040948708125867302497552393088112476L, 0.037464834195628972796376547170838841L, 0.034228353120175696600101652094083332L, 0.031226023505331741095045556858942863L, 0.028444973247334237420578959776412296L, 0.025872714343617644485216127636544335L, 0.023497155463988527331246316453605283L, 0.021306612366126070465592025046447108L, 0.019289816240845601467614148418736892L, 0.017435920073977461259931130790681214L, 0.015734503113059796501938486945359929L, 0.014175573528330460201833138862417195L, 0.012749569358731162695972946581614495L, 0.011447357834799756252759767428888751L, 0.010260233171410768904966921400699131L, 0.0091799129243109016820036013086815458L, 0.0081985330052611999146716526049152539L, 0.0073086414513132480272928751787482011L, 0.0065031910442825794569735904252798084L, 0.0057755308768065491405505517951108403L, 0.0051193969614537818669843222017295048L, 0.0045289019791562891246666236654351803L, 0.0039985242627335310486699760330762132L, 0.0035230961104429863210055440392477764L, 0.003097791523300820656719025216576771L, 0.0027181134583502264180047688832185696L, 0.00237988068810043824649673132220802L, 0.0020792143540086661490040088456472881L, 0.0018125242991288642651054139496748517L, 0.0015764952619105560466714983901735372L, 0.001368073009609703955236179563862531L, 0.0011844504858902772190681005760231091L, 0.0010230540429745410945605352201833147L, 0.00088152982417297002262517617153839107L, 0.00075773035782735921074324212296621353L, 0.00064970141867429037384583446669345298L, 0.00055566920742578558188343231541026958L, 0.00047402789401816719609357067930453704L, 0.00040332756454954092910822407611191225L, 0.0003422626064629768809457896368129693L, 0.00028966056108877174342801893880690422L, 0.00024447146728689162442142144970348683L, 0.00020575771467998818628501233394779612L, 0.00017268441988592922811041420156552324L, 0.00014451033429094586468260075495740334L, 0.00012057928729056996115421473619828627L, 0.0001003121646011242017105871905771618L, 8.3199417240036480655327139421155229e-05L, 6.8794093113496376994935071214960655e-05L, 5.6705379853932877470011678973870605e-05L, 4.6592644630494618702241456703037532e-05L, 3.8159954120245760316298139014562893e-05L, 3.1151055677448352880855786865941099e-05L, 2.5344798968850110019503595997329621e-05L, 2.0550975944934244592401576964828471e-05L, 1.6606555976508335934243673648199596e-05L, 1.3372292284532372637222480551805329e-05L, 1.0729675406852340659025097863229453e-05L, 8.5782093537079082475098159798479489e-06L, 6.8329862774218506793384467861049682e-06L, 5.4225358918240331874795406776236123e-06L, 4.2869264939998226801201888531480146e-06L, 3.3760952348055109687047948553034167e-06L, 2.648386225404341059332347382668934e-06L, 2.0692761257364670911197722058327534e-06L, 1.6102680094507650586919500255804135e-06L, 1.2479355121177451927952866165256362e-06L, 9.6310052117659252623275411195037422e-07L, 7.4012893490908614244527654085271701e-07L, 5.6633028402050775174949474283673891e-07L, 4.3144825587165380566889409128276242e-07L, 3.2723037330517558940458575985214918e-07L, 2.4706624511249700700814105972385083e-07L, 1.8568491370025132363780817403060483e-07L, 1.3890286996035952957423247999266266e-07L, 1.0341528040745607124994759911720107e-07L, 7.6623873974817584699067130031657306e-08L, 5.6495763871670621475281493521314709e-08L, 4.1448233558505383939707940899336768e-08L, 3.0255196464899467188973440250947218e-08L, 2.1971648917089349870913690980235634e-08L, 1.5872978090968232625950436800137184e-08L, 1.1406465554850482080861078708713322e-08L, 8.1527464828576537674941124925699748e-09L, 5.7953495730966064129686023678242569e-09L, 4.0967579139685198004841429173893772e-09L, 2.8797013464471104655049488102491186e-09L, 2.0126210218867172874496187582569718e-09L, 1.3984414312565442025515753185221259e-09L, 9.6594851858099300536090988590771494e-10L, 6.6320863470162099803938291638680053e-10L, 4.5257576104153821951813027371570715e-10L, 3.0692702078723328679138233160912672e-10L, 2.0684203539029217544821684017571545e-10L, 1.3850287525834145108955800004991698e-10L, 9.214056422651888408320635035510389e-11L, 6.0893387064380670508776147108848386e-11L, 3.9973389519930269589286591598258413e-11L, 2.6061960502805293348144397281679947e-11L, 1.6874519343915025228826792012273533e-11L, 1.0849161834337120075418713193749676e-11L, 6.925528015268138234276418201069421e-12L, 4.3888651899779304012906781620222308e-12L, 2.7608587671398283608193689290767215e-12L, 1.7237644036042717645352410466671951e-12L, 1.0680750436541459501472279340038532e-12L, 6.5669443496573770247087892915106603e-13L, 4.0059853799532432743143892523313549e-13L, 2.4242966045442409054601213028173084e-13L, 1.455249915777183791487150796402817e-13L, 8.6638127254357861703205617203908732e-14L, 5.1149749011237035858347608326433163e-14L, 2.994217759503148409824465166785657e-14L, 1.7376816946074293584520159998670905e-14L, 9.9964240098994293054396937904035808e-15L, 5.6996266660577917745539527915605509e-15L, 3.2204325132709429326443049307963174e-15L, 1.8029589638948994224704223595333813e-15L, 9.9999573441511328003764710559432247e-16L, 5.4939783973500551313304111867037353e-16L, 2.9894208856836080833460170608635085e-16L, 1.610765424389333897595412933702905e-16L, 8.5932097482708020320234894915603507e-17L, 4.5382468267138486711718165281416612e-17L, 2.3722531670914086186505578503296188e-17L, 1.2271671669957684251768746309727359e-17L, 6.2812290485539754593993025086049784e-18L, 3.1806147137287576603303714266387884e-18L, 1.5930492574791968327078642420897427e-18L, 7.8908551593455841363543904697547233e-19L, 3.8647331030140734169770289169399394e-19L, 1.8712773299709086118483416095592492e-19L, 8.9557394552313902528032623648079256e-20L, 4.2357428519456376102962467454513856e-20L, 1.979436201534175012643985224780424e-20L, 9.1380785584698727034273276491713887e-21L, 4.1666411584773049229385926804715087e-21L, 1.8760750552668470771305003442308049e-21L, 8.3399019486525331443195597764274931e-22L, 3.6595752362436730932932611770341911e-22L, 1.5847852183325978194205652538004105e-22L, 6.7715756944343963844212791596730701e-23L, 2.8542817079371140817224171432389793e-23L, 1.1865838580947568695752893868758282e-23L, 4.8640699357080450262013571667032291e-24L, 1.9656434192469852200073765210097077e-24L, 7.8291656246958620676899939812206199e-25L, 3.0727892288172702707668669968129923e-25L, 1.1881076147213762784564203492918474e-25L, 4.5246197487634934114352468991264689e-26L, 1.6967101868478854591740026523544294e-26L, 6.2636410032291183199621894495108545e-27L, 2.275790792857396714851032797332432e-27L, 8.1360777159044314206100175821194793e-28L, 2.8613065492953910272350038532776635e-28L, 9.8961841969437875600155257339935786e-29L, 3.3652008931641398734060087635568974e-29L, 1.124807054607635533524732113721643e-29L, 3.6944604327139334398530256817969063e-30L, 1.1920933013465364813127655880555969e-30L, 3.7777578760183197857216889284069537e-31L, 1.1754363786778472289935929233399097e-31L, 3.5898790778284615893833448994196406e-32L, 1.0758426862142799782307176268359752e-32L, 3.1628351259465958616106447958915727e-33L, 9.1186741890740659476367230179943904e-34L, 2.5773931684416249884030668079887807e-34L, 7.1398295043464040965283248909128753e-35L, 1.9378289210199819221125431141075043e-35L, 5.1513791013790083352767878745680313e-36L, 1.3408183263237239554032545670003467e-36L, 3.4159407851320474430217223083201431e-37L, 8.5152627413375142768339982799164144e-38L, 2.076271482369099670696465965718329e-38L, 4.9501378383448158752628326041586328e-39L, 1.1535696769569322062307767078682896e-39L, 2.6266829648536609656499385130095636e-40L, 5.8418474765546354958505600321922284e-41L, 1.2685583522817839163209325300191513e-41L, 2.6885912245101334092484585567288326e-42L, 5.5593886486694506967808090446058169e-43L, 1.1211105786168708837628837423551506e-43L, 2.204030437173812137094585242452846e-44L, 4.2224091761364250309210368326791673e-45L, 7.879520062360877249284818876749499e-46L, 1.4317140234575381985434267597963095e-46L, 2.5319048034172799761988113239574673e-47L, 4.3559981958200842427093791079007571e-48L, 7.2876743960081410061189361036751606e-49L, 1.1851134957779509548750104934682377e-49L, 1.872433458377359457763352131002218e-50L, 2.8729694149829129971079440142039839e-51L, 4.2789120660247665069210975132651194e-52L, 6.1831517263855101991333477522333856e-53L, 8.664706532064762178060555848235158e-54L, 1.1769423732336786099340369561810161e-54L, 1.5488187383481079319128553087281025e-55L, 1.9736646088073419346394015708589866e-56L, 2.4341838396213207647077873905717686e-57L, 2.9041369422835327495394798923780893e-58L, 3.3499429920119329938613749830054234e-59L, 3.7340806058053956442103094318030179e-60L, 4.0199589180457423324670244391460705e-61L, 4.1774687856674289017944418799754548e-62L, 4.1880972367088277279311490203747192e-63L, 4.0484306973548957998328137903090132e-64L, 3.77114883236106507191241601008861e-65L, 3.3831738882583366253452262404438761e-66L, 2.9213348345532154319786866459214566e-67L, 2.4265171108768020980951563482987534e-68L, 1.9376034615932002776264780070086131e-69L, 1.4864705425001770514685771802938584e-70L, 1.0949232255990064493457806009226565e-71L, 7.7387134447881401018887497774386554e-73L, 5.2448008205426118383234631723504239e-74L, 3.4062562927830356374440099107130253e-75L, 2.1184692439258592242177714552745419e-76L, 1.2608614434809477030102983673195673e-77L, 7.1764903126208755695723110547276813e-79L, 3.9034697656266028736188479360578122e-80L, 2.0275573404310544862715347770507338e-81L, 1.004994816430593904290660376649901e-82L, 4.7500986471514075093717143999280406e-84L, 2.1392636099460832836078416112710222e-85L, 9.173149292867278730161085643245862e-87L, 3.7422217329627927169047643424111024e-88L, 1.4512980036440354977046772500061822e-89L, 5.3463017574991130746397399989936882e-91L, 1.8692545071988721039716330965097833e-92L, 6.1978958931273853497246435906829052e-94L, 1.9472339695524234456610961876127122e-95L, 5.7919052872513171909291273369047611e-97L, 1.6295956267419577729908767823766263e-98L, 4.3332432642363261903550403983227406e-100L, 1.0880149430086354606207846327564807e-101L, 2.5772282627352113781815039093907765e-103L, 5.7539838006451121430858484531452556e-105L, 1.2096990017248315153784188752260699e-106L, 2.3925872664345045807315831521649824e-108L, 4.4475659282644875495932738704595289e-110L, 7.7627738422265479186699876924736519e-112L, 1.2709267303083863208341290697181042e-113L, 1.9498242131608832039698501560236386e-115L, 2.8002525531974060834525812862103801e-117L, 3.7607474856227547424556497710309557e-119L, 4.7181060260584103957700726731938295e-121L, 5.5234683214250331048701341097289187e-123L, 6.0274476904458633271417170323324512e-125L, 6.1242336828836773956273877493935834e-127L, 5.7873336327745621779130244175524495e-129L, 5.080632710884007461562439620052701e-131L, 4.1387180173608798644427799558698992e-133L, 3.1247188175223382722031520705749413e-135L, 2.1839031164024077549981710942515192e-137L, 1.4112539862126523413412548851519319e-139L, 8.4215132945549247707528420294855683e-142L, 4.6349339497376890248654482771898709e-144L, 2.3496985464521993504910835710336684e-146L, 1.095806710681902573252914390685236e-148L, 4.6950275371271587533706139425003387e-151L, 1.8456310819897755187059215917442304e-153L, 6.6476058774188256202611396416535206e-156L, 2.1907913883958482863705726011811944e-158L, 6.5969704613629998580324088255164448e-161L, 1.8125024222344232769814833021310009e-163L, 4.5370781893964839845073953864812105e-166L, 1.0332388835710739936372904447767308e-168L, 2.1374954497695898284726118552680548e-171L, 4.0108149042055622979938927508096981e-174L, 6.8157812455297175262972900652817266e-177L, 1.0473127652676199964496156413213892e-179L, 1.4528685940321876156376494017441343e-182L, 1.8166282837173462776489744689123018e-185L, 2.0440202882824856548036164467448088e-188L, 2.0661513859552458565266153108297534e-191L, 1.8731127932057798663856916994067048e-194L, 1.5203587637183862829799380406495058e-197L, 1.1029412423875246289335781867943994e-200L, 7.138626970990275795307217364262657e-204L, 4.1148385080761107172311325770404465e-207L, 2.1085028345530812052701929614749437e-210L, 9.586804728597153811540457301738394e-214L, 3.8604168930466380230648935566836905e-217L, 1.3741198913182849308419643845423577e-220L, 4.3152058575378643032664917818412533e-224L, 1.19318553486394227968739928824324e-227L, 2.8991695266404489121362655770787829e-231L, 6.1775221006993094876500875992969792e-235L, 1.1519457912295313262297054573241955e-238L, 1.8759214343015253296548435854195451e-242L, 2.6621687700905798726983482845704724e-246L, 3.2851388833240439850794784627378447e-250L, 3.5173302898760059862233242135090423e-254L, 3.260189457452227113017033626415114e-258L, 2.6100961489800200467379180863184362e-262L, 1.8007454797962902444457099319194981e-266L, 1.0681019906266894643271137718146545e-270L, 5.4338051160125203325306044546291973e-275L, 2.3652466547636417935201205492175566e-279L, 8.7874613708003418754944028581674092e-284L, 2.7795968403545195493312606588081939e-288L, 7.4667445765219841782970695464816343e-293L, 1.6990035121087781757668989612218036e-297L, 3.2661456244114182842789617411036552e-302L, 5.2905644824269341726292739407836284e-307L, 7.2014912174357894815487914007866659e-312L, 8.2149916381513794689686855840667286e-317L, 7.8315488217791735530193073164613925e-322L, 6.221807701348355377240172152359918e-327L, 4.1074011155154758472047101692258134e-332L, 2.2466366589127674529456245569217313e-337L, 1.0151434061771573997492847262735324e-342L, 3.7778453114026807637246195807729534e-348L, 1.1543990703305213109241662519678786e-353L, 2.8874514917596574178755044320075352e-359L, 5.8931980361396154630354121875276487e-365L, 9.7830460697139640980628824283457061e-371L, 1.3166522887017193888073217487220663e-376L, 1.4318812372702150835656599374620546e-382L, 1.2540768871672304974301195995718396e-388L, 8.8153996483879828169726259431129063e-395L, 4.9562889772003524717252843516553902e-401L, 2.2209583723340796264599763080008776e-407L, 7.903905845967360394872219425003336e-414L, 2.2257919152191142215565812315993462e-420L, 4.9416038684875140142672214621735078e-427L, 8.6171916788075114925145119819509171e-434L, 1.1757806822258262957023704296248014e-440L, 1.2504660464052680899003468814266606e-447L, 1.0325197258632347378121850864959173e-454L, 6.5928728293837187402797552969580767e-462L, 3.2422215378060561710679179733872247e-469L, 1.222971654709343975246423698805888e-476L, 3.523560719191273512100160441884497e-484L, 7.7214021325433144875052949047234787e-492L, 1.2814096433117550022825846412075423e-499L, 1.6034524983169066795932277083952198e-507L, 1.506154257333325578480885384144555e-515L, 1.0572229793982283970011093126796893e-523L, 5.5202045762026741431740525809554222e-532L, 2.1340837910128019442948018148493991e-540L, 6.0796683172016911238300290124661526e-549L, 1.2702010542787853060770224333562558e-557L, 1.9367268433132755704840764854736682e-566L, 2.1444373126935426310796432391730652e-575L, 1.7156127051237753338771752429597998e-584L, 9.866507362005302302229219896627392e-594L, 4.0577781777778825723169578502780851e-603L, 1.1871364801794013747840938511563031e-612L, 2.4573736732127578624297116105185871e-622L, 3.5795957466771145363085369419550411e-632L, 3.6491000343716609306595204093204503e-642L, 2.5887224173643746039322073618863653e-652L, 1.2707309714711704379079743917253552e-662L, 4.2911292837382087644451178350840055e-673L, 9.9101724991114880676267115929463864e-684L, 1.5559081312551687762049592042215559e-694L, 1.6505944447381619620675148844917786e-705L, 1.1758976044388560589646808736426135e-716L, 5.5904673599123524828590221558073227e-728L, 1.762422841281411916279811438897522e-739L, 3.660545736359787919552147533680784e-751L, 4.9762407942794836638678732794521407e-763L, 4.3982297935392846258826361101417004e-775L, 2.5103306493196863847807186768995234e-787L, 9.1890276778950746130428977933070467e-800L, 2.1421794336828190981917187030902949e-812L, 3.1579407773552118084939940851249037e-825L, 2.9226685587591509131252404660323457e-838L, 1.6857720383594173922437511994184956e-851L, 6.0148932059792413237874599630717323e-865L, 1.3175950977896871810836083428540193e-878L, 1.7584274071402706626129462062711265e-892L, 1.4186205574627811155197950438077455e-906L, 6.863804756553040536363703712029114e-921L, 1.9757126782016829801994589969078639e-935L, 3.3557616466388148224311943077895109e-950L, 3.3354866661158784873247273739595471e-965L, 1.9238163515428741014100444007200552e-980L, 6.3838419214869081664291315367530963e-996L, 1.208186393746817780221157077221556e-1011L, 1.2926433460770675227136395965601267e-1027L, 7.7484641126890628613399212918871834e-1044L, 2.578590762872741885363873428172601e-1060L, 4.720135411440959292309368252688105e-1077L, 4.7080786123051904219070581932647786e-1094L, 2.5345319096404707885383043943244978e-1111L, 7.2928761242789730396354678181188802e-1129L, 1.1106128952895418090598971812669904e-1146L, 8.8621216538366976598099979452808551e-1165L, 3.6677828388640988980975786646419682e-1183L, 7.7923888084439739305704880307980387e-1202L, 8.4096550976573708334084140027496576e-1221L, 4.5613561814429013831962003215267279e-1240L, 1.2300215778004431684442502009102409e-1259L, 1.6310040159866857782581696492106591e-1279L, 1.0516379174444742948762886500817157e-1299L, 3.2599823473651125257811218258666689e-1320L, 4.8027868480449172060158102541122388e-1341L, 3.3236309884819155458562664594667437e-1362L, 1.0675935976034168796912624171598076e-1383L, 1.5726195400977448490722891999643084e-1405L, 1.0493832555753684651126599489805473e-1427L, 3.1327201084154872489458088843708842e-1450L, 4.1312837255709912970140480142041816e-1473L, 2.3759469769680296617336666937339053e-1496L, 5.8816760470052203616229940275916193e-1520L, 6.1846204357986408200352274197037853e-1544L, 2.7253154006156158167192213817170292e-1568L, 4.964370850871644112480158952518382e-1593L, 3.6864952720610418491857518517405997e-1618L, 1.1003435724966953469136035841615193e-1643L, 1.3012915572536198178305157209959287e-1669L, 6.0092590718277568469858434192698668e-1696L, 1.0676671777328477051443719591612721e-1722L, 7.1893041788265042697820383343649581e-1750L, 1.8069174401475778125395048319028868e-1777L, 1.6689765331128672749329764129278041e-1805L, 5.5766895369980043818737558535999006e-1834L, 6.6338104205654511592967377535571654e-1863L, 2.7640539994999072399732281044814252e-1892L, 3.9678218215367472586686997493459426e-1922L, 1.9297110274588854079208153981267479e-1952L, 3.125823255660491143824024380405163e-1983L, 1.6574847870654916983076674690989236e-2014L, 2.8268986732520512350429266473306993e-2046L, 1.5233117185863507338781156113800939e-2078L, 2.546861334196453858326476740489303e-2111L, 1.2970473455790653045919178951673388e-2144L, 1.9747419969447016216735762205746422e-2178L, 8.8188278044353709778558661887540083e-2213L, 1.1331026308914886857015956789021835e-2247L, 4.1073843396377054383734821718609159e-2283L, 4.1176061068991316842526396776090883e-2319L, 1.1187086154165438557157295454514512e-2355L, 8.0696152044407520449588862080186105e-2393L, 1.5135007846133660639825213215103939e-2430L, 7.2259333243684066847653432848351411e-2469L, 8.5946687461833096509693248289938412e-2508L, 2.4916284852956646502671815296350685e-2547L, 1.7218755528601187283427831366854425e-2587L, 2.7731789802471774097405514299103397e-2628L, 1.0173036737175759305843452255817661e-2669L, 8.3042791798168477802920353914502218e-2712L, 1.473175854535708068279125062330391e-2754L, 5.5445898944436653916624336507572418e-2798L, 4.320588676830861227044616106347197e-2842L, 6.7999319106199079525777026656783353e-2887L, 2.1077263780365503485840282493709679e-2932L, 1.2541789874677469018547003272238725e-2978L, 1.3958959110384407791740617210399902e-3025L, 2.8302814609077924874798718558506843e-3073L, 1.0177558252358910503169620579616733e-3121L, 6.316345161489586703263200702377878e-3171L, 6.5808353794152592529153513818570413e-3221L, 1.1191402820754794725611968922105955e-3271L, 3.0191144446114914489317417615052391e-3323L, 1.2550879768384736567252096901362895e-3375L, 7.8068891083719869927416696059040226e-3429L, 7.0517902304991510532607166162881123e-3483L, 8.9730892510839231589218006993975962e-3538L, 1.5595586152851619317721078977043233e-3593L, 3.5880900047081973177436521469002167e-3650L, 1.0585155878906962124317498469363852e-3707L, 3.8766455756851727310799307162918377e-3766L, 1.7055766393620807246392140051588691e-3825L, 8.7186526945824113614692955827617228e-3886L, 5.0057478847913665022916271652366648e-3947L, 3.118721903551705673676508466953291e-4009L, 2.0360271187838855169643637345546437e-4072L, 1.344193072571113944443469925533764e-4136L, 8.6564521926545774914724471102499854e-4202L, 5.2420435234912004640647252129478902e-4268L, 2.8759098307799421718470860096954611e-4335L, 1.3763877452472741048491095385711433e-4403L, 5.5298776937738818735087266649244396e-4473L, 1.7937216962986389293716315095996859e-4543L, 4.5149004338871753808860898565339903e-4615L, 8.4705695199542943533046216376760939e-4688L, 1.1370794062105632021349671352107651e-4761L, 1.0477206804639390072354388690482908e-4836L, 6.3526587059143165049479765478742544e-4913L, 2.4283486680552345908529800038916585e-4990L, 5.6028531999416699789095738832933157e-5069L, 7.4653403674710397422097751397913229e-5149L, 5.4919841992020489039553213694177019e-5230L, 2.1312688707736268785590779947753119e-5312L, 4.1653791598980828217930742787672032e-5396L, 3.9114639992058990826656399856975019e-5481L, },
   };
   m_first_complements = {
      1, 0, 1, 1, 3, 5, 11, 22,
   };
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
   m_committed_refinements = static_cast<boost::math::detail::atomic_unsigned_integer_type>(m_abscissas.size() - 1);
#else
   m_committed_refinements = m_abscissas.size() - 1;
#endif

   if (m_max_refinements >= m_abscissas.size())
   {
      m_abscissas.resize(m_max_refinements + 1);
      m_weights.resize(m_max_refinements + 1);
      m_first_complements.resize(m_max_refinements + 1);
   }
   else
   {
      m_max_refinements = m_abscissas.size() - 1;
   }
   m_t_max = static_cast<Real>(m_inital_row_length);
   m_t_crossover = t_from_abscissa_complement(Real(0.5));

   prune_to_min_complement(min_complement);
}

#ifdef BOOST_HAS_FLOAT128

template<class Real, class Policy>
void tanh_sinh_detail<Real, Policy>::init(const Real& min_complement, const std::integral_constant<int, 4>&)
{
   m_inital_row_length = 9;
   m_abscissas.reserve(m_max_refinements + 1);
   m_weights.reserve(m_max_refinements + 1);
   m_first_complements.reserve(m_max_refinements + 1);
   m_abscissas = {
      { 0.0Q, -0.048632035927253054272944637095360333Q, -2.2522807538407135100363691311150714e-05Q, -4.2941610558782407776948098746194498e-14Q, -1.1676488975098609327433648963732896e-37Q, -1.1479529916293899121630752460973831e-101Q, -1.2256538136584864685624805213855318e-275Q, -1.5540928823936461440049038613030612e-748Q, -5.3329091650553293055512604419528931e-2034Q, -2.9720916290005160834428657415764727e-5528Q, },
      { -0.32572850775156417391957990936794786Q, -0.0024851435427756131672825807611796319Q, -1.1124335118015331984966677262985097e-08Q, -5.3784915913936887745567608333252923e-23Q, -7.9430213192221161033965793076252082e-62Q, -2.3871228185819266205711383850686192e-167Q, -3.5505659459518255844385870776118831e-454Q, -7.5205359182648710139026226942624688e-1234Q, -3.1913442054759083418654684997091651e-3353Q, },
      { 0.37720973816403417379147863762593439Q, -0.14043094131010336482533980273793048Q, -0.012959439492623108312614281385864999Q, -0.00031173597164679094947882131119246211Q, -7.9526288528733553445705754617783867e-07Q, -4.7143551823222575055542137870412504e-11Q, -5.4152228238072308459654515228487702e-18Q, -2.0403003943524943294070815498839042e-29Q, -3.0596901353644500298194434206758382e-48Q, -2.8621984192587511612196829791231642e-79Q, -2.0070330691533226369447382138318798e-130Q, -9.24799327395281672235465186719e-215Q, -8.3199831454007319575241484332973316e-354Q, -4.7101915049286172051527740304785445e-583Q, -5.1172326001331791544221573365516138e-961Q, -3.565091902092693101170960484903703e-1584Q, -1.380788494593788864141714339603113e-2611Q, -1.676187209363792948538214808034106e-4305Q, },
      { 0.19435700332493543161464358543736564Q, -0.46085329461203223095024473969116941Q, -0.21939256101679970074520301166037226Q, -0.085120736735425389092624476255089179Q, -0.026033131804322551436697210776387341Q, -0.0059444933685978567073105981025861939Q, -0.00093480354421415357524054329032500553Q, -9.0615304856000161419338663865283889e-05Q, -4.6839587794715699213295541472938628e-06Q, -1.0721838758161809434047961979486614e-07Q, -8.572949078216773291904741005771376e-10Q, -1.7678346928387203697450793420511691e-12Q, -6.374878495044439647822814562601604e-16Q, -2.4429372790852173177460412747369906e-20Q, -5.2515464730195748562913138330508827e-26Q, -2.7894671622894177442056826817468893e-33Q, -1.2781108980938044979748713346336601e-42Q, -1.3082723368531808046381743164626179e-54Q, -5.2799781226102543764849911449215675e-70Q, -9.0619104054181014912823874637681869e-90Q, -3.7903152788024647936360099425967319e-115Q, -9.8301769964870442146689481371228979e-148Q, -1.4178035546472368325653350149379565e-189Q, -2.6738184762632894703182151426789471e-243Q, -2.7784573988900744903717061581976306e-312Q, -7.3734573357629727800590803405661863e-401Q, -1.3609194136463888140125728088459369e-514Q, -1.2497483783949474937966696885440387e-660Q, -3.8167291133270025322500901161738939e-848Q, -6.4205995373383705446145927875493146e-1089Q, -4.4417348039463271683501804024826735e-1398Q, -4.7673068229426388972968294463896211e-1795Q, -9.1130922352573668488349663479480507e-2305Q, -2.9379714111577752802646622386752463e-2959Q, -1.2139403773313257256483385510112958e-3799Q, -1.0232729473573403414331717146692674e-4878Q, },
      { 0.097923885287832333262426257841800739Q, 0.28787993274271591456404741264058453Q, 0.46125354393958570440308960547891476Q, -0.38972634249936105511770480385767707Q, -0.26898196520743848851375682587352696Q, -0.17668299449359762993747017866341472Q, -0.11010859721573980192313081408407809Q, -0.064839142478015316771395646494440585Q, -0.035887835776452708068531865802344445Q, -0.018545173322664829973291436321723213Q, -0.0088730075583011977688563205451530858Q, -0.0038913345624914574643783628537015109Q, -0.0015457912323022624891555460019043471Q, -0.00054856556472539415808492760717674295Q, -0.00017117792712505834023311917084994073Q, -4.6128994372039252659199558313379019e-05Q, -1.0517985181496388528104100992279397e-05Q, -1.9828594045679206280184825675885662e-06Q, -3.0110584738877965497930108986179822e-07Q, -3.5760919084657714373617064589353927e-08Q, -3.2128009016957604999656591474001583e-09Q, -2.1026713776411488104386696975012214e-10Q, -9.606066478508465003238769873504248e-12Q, -2.9190266410175139338365873758536822e-13Q, -5.5861166388382275217411050967087276e-15Q, -6.328207134683168236714250292398626e-17Q, -3.9564613394676478284814817479958896e-19Q, -1.2609758447165484726150111345292941e-21Q, -1.8724921745276082830417983528796313e-24Q, -1.1700302939131190810173673171853244e-27Q, -2.7409861783512409524809986743233587e-31Q, -2.1122827214476119769953891899502657e-35Q, -4.6172239482595017143038368975612786e-40Q, -2.4203621976262400766076976463254527e-45Q, -2.5155618638019452025824211444543239e-51Q, -4.1786390697021940369390798235100086e-58Q, -8.6898180082116723025562943397702972e-66Q, -1.7154357765002478902943183846995354e-74Q, -2.3493032412195338067008854292230121e-84Q, -1.5645432388942512177369950120860268e-95Q, -3.3873402047868364753040883051129579e-108Q, -1.5108166471007846474189907630676839e-122Q, -8.2780000956704166841985762886452778e-139Q, -3.1016029819580818393688194490134506e-157Q, -4.0917346928724404440872286063293528e-178Q, -8.9581681449485486979882444847795217e-202Q, -1.3878968774299744972242822597861929e-228Q, -5.7925742129350389434060147129950123e-259Q, -2.1800405760909812340138469200688552e-293Q, -2.1406911761156887130351179719470661e-332Q, -1.3453872213865718752685710282193859e-376Q, -1.101014385623064504263959341239785e-426Q, -1.9309068614975016262992988189610055e-483Q, -9.3925603630660026565835909833509362e-548Q, -1.2492852059051478595312298290165577e-620Q, -3.2901922810889194483613206478029049e-703Q, -8.7594800494412338094846019359419219e-797Q, -8.0988360915906760478163297865939266e-903Q, -5.7031344694656922604093410430637095e-1023Q, -4.0339055664976041957619784717611254e-1159Q, -2.1239178549857743261952555619757156e-1313Q, -3.2107478383582521299381622502244136e-1488Q, -2.5644945785388919263865858785287637e-1686Q, -8.6102745649577746230102198336971101e-1911Q, -3.7383203767208973356368595261534058e-2165Q, -2.1998398074691126029946216118342674e-2453Q, -5.4286931050493856204731826640809476e-2780Q, -4.3621004458123770532320189625677295e-3150Q, -1.8497978727098997950183603712476515e-3569Q, -1.1369676358500293654883467577244006e-4044Q, -3.7214839695831726545775627850809865e-4583Q, -2.4391504445900085306499460471723674e-5193Q, },
      { 0.049055967305077886314518733812558024Q, 0.14641798429058794052721556654753264Q, 0.24156631953888365837930785879296225Q, 0.33314226457763809243847911788190042Q, 0.41995211127844715849198982824847239Q, -0.49898661062069089848349381875489034Q, -0.42441550936484834004641698479390102Q, -0.35682324101479529871858133934192696Q, -0.2964499948528579843445043147096805Q, -0.24330609136627005059281117751078325Q, -0.19720125865675873423611581820317215Q, -0.15778075364924313618002648351129378Q, -0.12456460236959132162770016658853563Q, -0.096986718486426129363622040287916613Q, -0.074431365931387333547858011705581591Q, -0.056265213947242843145098876381950312Q, -0.041863977289786309881823549732911528Q, -0.030633267103082664829845578114893065Q, -0.022023764813335027020322387941507847Q, -0.015541168832569169131789346286900099Q, -0.010751568909866103993013400883081643Q, -0.0072830028031727146206149733302275483Q, -0.004823973844672645743610027178935428Q, -0.0031196818718081262793524354157388492Q, -0.0019666636845662459778601079868649985Q, -0.0012064657011941007112693927041613496Q, -0.00071888807820804459144864726303695018Q, -0.00041524964848241268314681568037451433Q, -0.00023202840043916493872468513410105539Q, -0.00012513495121965351530204004794322354Q, -6.4980074917576313794386569166441844e-05Q, -3.2406932056540235696910972894671255e-05Q, -1.5480097729175578034178680349142798e-05Q, -7.0621233371143522557906813506099499e-06Q, -3.0675508096424942465066236512329746e-06Q, -1.2645281340904582699398919033969316e-06Q, -4.9299428056311116061802876221616736e-07Q, -1.8110628723299136916197821516601534e-07Q, -6.2445921626224975560761130514661379e-08Q, -2.0125496798245795667718397386179161e-08Q, -6.0358657983522763759573155859882131e-09Q, -1.6766380517421942876239492441262369e-09Q, -4.292122273863880635543266425012844e-10Q, -1.0072227674373084849456997974551022e-10Q, -2.154466258904202365369932629331438e-11Q, -4.1753931241965133013258464583874422e-12Q, -7.2847372642877340424635554069698771e-13Q, -1.1363869854392191510522700311409067e-13Q, -1.5735535097926530184856977764147682e-14Q, -1.9192189097107621395219494827869742e-15Q, -2.0449595408467355635051956217352212e-16Q, -1.8869583067293409682109390382712203e-17Q, -1.4938716488357228966449278404247075e-18Q, -1.0046938100037576480510308414445084e-19Q, -5.6799291780731967001285171683592406e-21Q, -2.6691039526135151405184691859723367e-22Q, -1.0301781381894114683480515283399996e-23Q, -3.2244848763912462050246763811724513e-25Q, -8.074782900421688680511360232922118e-27Q, -1.5946546019932855470728505192034342e-28Q, -2.4457239752752825879158721953606166e-30Q, -2.8659197719078655681373680421610036e-32Q, -2.5216825251828994098262358831122367e-34Q, -1.6355144403100727446739341400788963e-36Q, -7.6666590990678357226674242484094458e-39Q, -2.5435771288137693474957657676429463e-41Q, -5.8409472280716482316816237196206469e-44Q, -9.0658695647939702552111019446179921e-47Q, -9.2735698160588525904827667013324314e-50Q, -6.0857148042052277604035466987369298e-53Q, -2.4898089736722146606232085397625384e-56Q, -6.1598620863170544290033299759018975e-60Q, -8.9214127072226711903218542175348322e-64Q, -7.3072243260040451389778049479673039e-68Q, -3.2625608625513858480451119539754283e-72Q, -7.635785144983622496088267971984613e-77Q, -8.9855174690288197015625761302053014e-82Q, -5.0858718515487289997949540667405475e-87Q, -1.3207396001984761034417395177214725e-92Q, -1.4964864935610521348123903353455889e-98Q, -7.0129233669310880373145304240642606e-105Q, -1.2839990161964174973579406866396101e-111Q, -8.6445683841052002170413994851536565e-119Q, -2.0063694896831555279614790770285332e-126Q, -1.4987740796184447811135706015205434e-134Q, -3.3493762149589610757447357005516387e-143Q, -2.0715294381434481475176795645059666e-152Q, -3.2638832593319965098529982282696874e-162Q, -1.199473759889723458030222815758102e-172Q, -9.3602085584027101517846387151893588e-184Q, -1.4035094428007866386369967794759254e-195Q, -3.6356107639491247696753307742986431e-208Q, -1.452743995741226464980668915527686e-221Q, -7.9377235820922308566742845525117486e-236Q, -5.2163536229754499795136126103956784e-251Q, -3.5964743752577816321270751238997225e-267Q, -2.2494401025357803105700529744520824e-284Q, -1.0932895122313467863909046216907507e-302Q, -3.5018653663752920075194010313564247e-322Q, -6.2028644584939181030730328594330175e-343Q, -5.0411076779076593359744388314869213e-365Q, -1.5409305985666377552321629186300452e-388Q, -1.4337691700836957601785568351756479e-413Q, -3.2419333785971000532922906392702739e-440Q, -1.4016483469785082716612467122698103e-468Q, -8.9774004157156246399741480681783078e-499Q, -6.491677776744900396848653659323143e-531Q, -3.968873851900238121124736680679859e-565Q, -1.5079636812339879861399394077308116e-601Q, -2.5657520984549883455506649942306167e-640Q, -1.3792637739621433541997914448762973e-681Q, -1.6159468091505698350217369718439518e-725Q, -2.7790201117578232055517503438743301e-772Q, -4.6058286485158911464074200874638533e-822Q, -4.7006358553201903008219637676007764e-875Q, -1.8338935012250262258997114428219912e-931Q, -1.6464242001502528395088533240159331e-991Q, -1.9816419848867601423745192888068803e-1055Q, -1.7991094041844603532543957306249989e-1123Q, -6.6797992361969494675858665124683032e-1196Q, -5.2859098848402526633383865829249954e-1273Q, -4.4550261847470794285196049926646754e-1355Q, -1.9109438945105886059290808709178883e-1442Q, -1.9007367698294334902014013473010713e-1535Q, -1.8987324425640464476346787762173242e-1634Q, -7.8167618149826855518169886771423511e-1740Q, -5.1382521738908379784587542986497343e-1852Q, -1.9655086052442019324819644975934904e-1971Q, -1.4940998787039296124957637312415828e-2098Q, -7.1913235695994009755126799768473976e-2234Q, -6.4864369300175247147155547975053653e-2378Q, -2.999946728125704672445599927700136e-2531Q, -1.7904880802749365558709955426180586e-2694Q, -3.1752504880744458148875772810912828e-2868Q, -3.5042626554286023022035386575033303e-3053Q, -4.5572536942770532680494362978923935e-3250Q, -1.1878504044248058672204846884752448e-3459Q, -9.4149599587707022465535580134518114e-3683Q, -3.0486081328912260971936320312995529e-3920Q, -4.7600871041282300101380551251356845e-4173Q, -3.6856282214670996203899028095741989e-4442Q, -1.2567068832608339131427238307321809e-4728Q, -1.43352474798599180975125045215869e-5033Q, -3.5192846690056568230804624618996538e-5358Q, },
      { 0.024539763574649160378815204133417875Q, 0.073525122985671294475493956399705179Q, 0.12222912220155764235135543647484308Q, 0.17046797238201051810697458795462942Q, 0.21806347346971200463019772812275949Q, 0.26484507658344795046121266511868619Q, 0.31065178055284596083122357022827612Q, 0.35533382516507453329875421158518394Q, 0.39875415046723775644258197210456498Q, 0.44078959903390086626728024419415105Q, 0.48133184611690504421849248706653226Q, -0.47971194930876984042437695635737165Q, -0.44241877173922176919985653200857201Q, -0.40684964640804684120174768961798095Q, -0.37304979194895712050380997331064448Q, -0.34104900825664987561685714614383599Q, -0.3108622749383323282394733757479914Q, -0.28249053251267587278787801637860204Q, -0.25592161645265260087450443652530469Q, -0.23113131323175341540544311864007538Q, -0.20808450762385788552523390287482686Q, -0.18673639149702614832471241636026165Q, -0.16703370608058912436398702772140703Q, -0.14891599201215126738569202826103709Q, -0.13231682422435401330546108424048627Q, -0.11716501175533104486690697891318309Q, -0.10338574571992397421289522925754423Q, -0.090901681836979564888397733057112044Q, -0.07963394696804719765304016834176825Q, -0.069503062002846593688614128282605538Q, -0.06042977606672524461492007773726152Q, -0.052335809384846902663067556129352578Q, -0.045144504194977314593201232656013596Q, -0.038781384848883592468460207166535186Q, -0.033174629687644147159978329404393879Q, -0.02825545843451269107668693235164557Q, -0.02395843974342326066584697335177699Q, -0.020221724199384237353497637367719603Q, -0.016987208518898894423060324544850119Q, -0.014200636974716564032303584519259449Q, -0.011811646199257357569283265398947793Q, -0.0097737595324722530639024866802767264Q, -0.0080443369973223843798692318964963564Q, -0.0065844868307359609987086057921138135Q, -0.0053589442874888032779151584247851277Q, -0.0043359231830468303521751643564073944Q, -0.0034869453597462268271205586183706831Q, -0.0027866529565312977632015610760207467Q, -0.0022126080410934691734971443584643208Q, -0.0017450838280037065615903399860968624Q, -0.001366851359322522377124006197840537Q, -0.0010629651664878262735108241097275379Q, -0.00082055106511408284244272347987383953Q, -0.00062859885906231310357940554216565142Q, -0.00047776234878279577588863291489417448Q, -0.00036016865439963480978268344017946398Q, -0.00026923848019151736959880088820900265Q, -0.00019951856886161369803218944988361524Q, -0.00014652722688858828517464005147727694Q, -0.00010661345240743574350860189665463426Q, -7.6829870710671305672093252530903342e-05Q, -5.4819385541306910394346187661030795e-05Q, -3.8715192143333868407124859677345036e-05Q, -2.7053574767763435703448594138071512e-05Q, -1.8698729879273208281214887386151327e-05Q, -1.2778717999371889596874911057724209e-05Q, -8.631551655126555923485540179705975e-06Q, -5.7603723833652179866412085097651011e-06Q, -3.796652833823246921587494391038503e-06Q, -2.4703761948320696741670218229372071e-06Q, -1.5861890352645758023485712079757291e-06Q, -1.0045893100303765084297472484854592e-06Q, -6.27292664630529591934671730489378e-07Q, -3.8601144975725102288841415591950667e-07Q, -2.339766675668795211736366707138927e-07Q, -1.396287854005922888135986291920763e-07Q, -8.199520528943774856188619843122174e-08Q, -4.7357335538151973999955685632054965e-08Q, -2.6886764056376916830145130602254872e-08Q, -1.4996923688266632451823595335560501e-08Q, -8.2135439009280190432084662337190249e-09Q, -4.4143663841612464008735244035118061e-09Q, -2.3267632621004612298287053849704746e-09Q, -1.2020164996011407668894632352766295e-09Q, -6.0822312416778615105389374826677738e-10Q, -3.0124563074832322562803735802769698e-10Q, -1.4594388450012857143914599919653952e-10Q, -6.9111604985231078666967248228438538e-11Q, -3.1966783264445369722850429235278972e-11Q, -1.4431209924042652494507345459464675e-11Q, -6.3536761284911814276716019591237312e-12Q, -2.7259505188725197201090431322689677e-12Q, -1.1387345722407849659711071645809649e-12Q, -4.6277346217693134290561172417433117e-13Q, -1.8279902248431104840099026765769527e-13Q, -7.0120467384905686087185698802558123e-14Q, -2.6096053661379546374618820988626935e-14Q, -9.4133613352888555760685465264560536e-15Q, -3.2879256949364934203905795056053052e-15Q, -1.1108629401821898431250973198964316e-15Q, -3.6266022642032122019717830419062828e-16Q, -1.1427876525052669863279877707280153e-16Q, -3.4719022692494936795924113147604942e-17Q, -1.0157819068305124587449297554375856e-17Q, -2.8585328245081481739274807557448345e-18Q, -7.7278347873892552568443785071813692e-19Q, -2.0044239922175172204979314952837677e-19Q, -4.9815683764699114931038226437577286e-20Q, -1.184668492422340236764487382599448e-20Q, -2.6919849036103905707012031384082434e-21Q, -5.8366674419339414129894129738889481e-22Q, -1.2056615886548723513354371860285751e-22Q, -2.3691093508078067650593562318134397e-23Q, -4.4213394617280017751359368167258193e-24Q, -7.8238350041086858182189611432466241e-25Q, -1.3105331441254781548552012055519121e-25Q, -2.0743450130735150739962743364500278e-26Q, -3.0969683255507495828369632528319965e-27Q, -4.3532005281967559757740065310127852e-28Q, -5.7499556680703922900693138751854555e-29Q, -7.1227159266381727412068631384345687e-30Q, -8.2578354203558045768004451321024521e-31Q, -8.9415410066249555437647937669966689e-32Q, -9.022796650030866974265443296162775e-33Q, -8.4660301195315971752688615420135191e-34Q, -7.3692728065645298168617836618124666e-35Q, -5.9366323560088749776278557840220755e-36Q, -4.4152778386946959545381237143455402e-37Q, -3.0239601358635314916964716581634168e-38Q, -1.9022037916577048792943667867798758e-39Q, -1.0960433286034577882548963198643229e-40Q, -5.7686955980040694827320797355280285e-42Q, -2.7653993817377102211887943260200603e-43Q, -1.2038709528562574051108458784307785e-44Q, -4.7447623253354987177832150988795395e-46Q, -1.6876791281053309141043851984778909e-47Q, -5.3999669982997284808706183365024646e-49Q, -1.549024049448082614346506969629423e-50Q, -3.9699472552561349765465127201129129e-52Q, -9.0576749265239358552559531050991564e-54Q, -1.8329504334836931380757353284221911e-55Q, -3.2774223323973643896776906795254206e-57Q, -5.1576871530839363640889147624270212e-59Q, -7.1147152214081979999932108600174644e-61Q, -8.5668890171210249323533230713364891e-63Q, -8.9655585542113618588233770236885955e-65Q, -8.1186937969054743805236701211415782e-67Q, -6.3321938859077339155825561128906228e-69Q, -4.233726342460328087225876085667347e-71Q, -2.4147260464376790196845134616759282e-73Q, -1.1689555910948214525778546914101199e-75Q, -4.7780668985526446079787318493898922e-78Q, -1.6402035716952158990309656043758773e-80Q, -4.7024891487642105081472609988004035e-83Q, -1.1195904670077980724005196979590736e-85Q, -2.2005433507330024992345876236265894e-88Q, -3.5489240240632636451154177125584074e-91Q, -4.666940936883651858001572120481125e-94Q, -4.9719066745930336256015501390182822e-97Q, -4.2625120032373619347908954864791867e-100Q, -2.9205525410269401160683450458899041e-103Q, -1.5879283520520673524672134768919763e-106Q, -6.8010328774211569659959576053742516e-110Q, -2.2772324163917691950189307132002393e-113Q, -5.914722281629598175795438531560773e-117Q, -1.1820998217768840832984056598978546e-120Q, -1.8028262273951398891886168129927287e-124Q, -2.0802041335110124193794970445396975e-128Q, -1.7999670156396935596364355347029528e-132Q, -1.1573419660682545435402292296632314e-136Q, -5.4777666541440989994081254549149894e-141Q, -1.8900190812332372959031760309750191e-145Q, -4.7064318355746675546808068910291556e-150Q, -8.3711291364376416570212981885601003e-155Q, -1.0522139008817658534084763162543628e-159Q, -9.2441193400981258043604322474285494e-165Q, -5.6121650181326378940737135740792016e-170Q, -2.3270398424370294077224375501608811e-175Q, -6.510721186743015604234587537579044e-181Q, -1.2138997008699255952666017192674735e-186Q, -1.488912999352695867344311698561554e-192Q, -1.1855378186700107100267028475666946e-198Q, -6.0445584657932328676234962000989188e-205Q, -1.9456793188673556729031520231103262e-211Q, -3.8966805747095113950686230057410173e-218Q, -4.7829220009546143710431038737748715e-225Q, -3.5425595917324118172041028901087466e-232Q, -1.5581260191174140607938473801301532e-239Q, -4.0028071638967508676664829682660027e-247Q, -5.9046000382762171283315920864186447e-255Q, -4.9139618035200235196744932808885781e-263Q, -2.2656744698055742315406349324984012e-271Q, -5.679971707464259613069663431618014e-280Q, -7.5941261723746109223402306020806232e-289Q, -5.3079378188778939332566204780962363e-298Q, -1.8999778173084169834715816327210827e-307Q, -3.4097331110687592869362387676073128e-317Q, -3.0013940393884085654083505783337316e-327Q, -1.2668812065800974411227932737003665e-337Q, -2.5051097865973858424963978994105679e-348Q, -2.2653835286828392416879577941979414e-359Q, -9.1389245527311981300218270087623242e-371Q, -1.6030930250662823526442537954011133e-382Q, -1.1908341901916029371925944757292412e-394Q, -3.6452473980708905026093050166205199e-407Q, -4.470581799337985564576477205041097e-420Q, -2.1337946766915608745979583351759322e-433Q, -3.8466502387371814478842876774875617e-447Q, -2.539400648443551278595315008859673e-461Q, -5.9463310120354055195604938583173969e-476Q, -4.7791154462039743101535253283672216e-491Q, -1.2743368238366444127980835826074082e-506Q, -1.0885513756896925921766278125740167e-522Q, -2.8730852174104725878401965901098976e-539Q, -2.2573125874160702024258126565183851e-556Q, -5.0801120877688108677556596022448958e-574Q, -3.147442313458971094095441973453258e-592Q, -5.1530284704707320518750575728196905e-611Q, -2.1371769888513739266688394459294633e-630Q, -2.1496221573881902238982422615825289e-650Q, -5.0129928677987666360823382488243001e-671Q, -2.5875873756026443959521949570534425e-692Q, -2.8181620714067187431385929758736621e-714Q, -6.1639625895204988083360964770898867e-737Q, -2.5730385846926571409465774477581834e-760Q, -1.9448744127295675669050254780849454e-784Q, -2.5213762798933993822025230917884045e-809Q, -5.3012546029922448380589857164083814e-835Q, -1.7062280322013794538523703018620566e-861Q, -7.920258584822147757302618208276618e-889Q, -4.9864409544476492068060565707730923e-917Q, -3.9962146024411971515483669014007722e-946Q, -3.8185388383865818601310492618971832e-976Q, -4.0664680142829823697989791072662443e-1007Q, -4.5015287529024162764736783373823692e-1039Q, -4.8207630344098447587536323292213823e-1072Q, -4.6375061995611002272610193821937293e-1106Q, -3.7123073076807703361397549566488692e-1141Q, -2.2851629967300820983086928617773692e-1177Q, -9.9710151392993821636927693831761909e-1215Q, -2.8354564442635450405718863241322006e-1253Q, -4.8186151533506508630367161348238245e-1293Q, -4.4750346397724178706383092717200537e-1334Q, -2.07096024490079588158927973820168e-1376Q, -4.342127308884323517312819302082545e-1420Q, -3.7387854726872903857603805318252205e-1465Q, -1.1946574634186879393921342144260135e-1511Q, -1.2759441657028066718211573051988164e-1559Q, -4.0892575258067523665855842405306752e-1609Q, -3.5183875354871145031995454351438789e-1660Q, -7.2453069573873944421690184084196628e-1713Q, -3.1719634253495420624848681187846285e-1767Q, -2.6125752080330097261369292566212969e-1823Q, -3.568653966381994484787922096951618e-1881Q, -7.0977732480251336872758023077199559e-1941Q, -1.7972768141068333989152413556564914e-2002Q, -5.0445828237798979207114411387487754e-2066Q, -1.3604595412453945762407996412329254e-2131Q, -3.0419951659449528528023254177983519e-2199Q, -4.8436592014037520285667566761754706e-2269Q, -4.6942170473823597199914323005808658e-2341Q, -2.3550263329098316285032485977513203e-2415Q, -5.1749636831144475403049010950928176e-2492Q, -4.1920868885180451051347682697190622e-2571Q, -1.0479018787402586343779450171950524e-2652Q, -6.7279428066803227116834111695132562e-2737Q, -9.1809880505079118230638356660916729e-2824Q, -2.1903121606778754894644610552460375e-2913Q, -7.4679789664508998469723121247024705e-3006Q, -2.9557873617341136266023224346917197e-3101Q, -1.0958275239557229340575496309183988e-3199Q, -3.0498491351714004349547292302889343e-3301Q, -5.0710549490564658785365657190857463e-3406Q, -3.9798833012314432652288251577412562e-3514Q, -1.1561503564308970430488476029765637e-3625Q, -9.6738350948812768619787679276120515e-3741Q, -1.7998403785619766408556530850576782e-3859Q, -5.7011595100947685691764189814097789e-3982Q, -2.3342553026008523985584602356036335e-4108Q, -9.2972354921660563358009254715497299e-4239Q, -2.6867404349734172649970964993288437e-4373Q, -4.1626263982921541967696302677355217e-4512Q, -2.5305298537002028475606439113724333e-4655Q, -4.374081747920579431517607591486263e-4803Q, -1.5419945401212975573693263841002279e-4955Q, -7.8687813783350283818994253864974705e-5113Q, -4.0807753246747505208452672626580765e-5275Q, -1.4931173692359951169542729406105663e-5442Q, },
      { 0.01227135511808220203174030407830294Q, 0.036802280950025085014502801868003454Q, 0.061297889413659975774096037893250044Q, 0.085734754877651055755695461536205953Q, 0.11008962993262801258027823870142222Q, 0.13433951528767223660361301319755489Q, 0.15846172828929950397368299153321965Q, 0.18243396969028915021484585689969531Q, 0.20623438831102876939480559034293115Q, 0.229841643254360753698787981399908Q, 0.25323496335600023568199079816688852Q, 0.27639420357617861412235821455333865Q, 0.29929989806396047268181013419640459Q, 0.32193330965336916623041220093195644Q, 0.34427647557970491862490100675820811Q, 0.36631224923490408185795345459447966Q, 0.38802433781211774758659161520378721Q, 0.40939733572152948912988463410721733Q, 0.43041675369143706432580814661825066Q, 0.45106904350045199476872844990592136Q, 0.47134161831799846568675411549444037Q, 0.49122286866081148740164998519670023Q, -0.48929782599744185793989666371765164Q, -0.47023008989822544160728474574480754Q, -0.45158254786020764535461909038883739Q, -0.43336282621498114195428483532083889Q, -0.41557755767733605834276062792428588Q, -0.39823238999036654097849262723755211Q, -0.38133199816727193874162067315553426Q, -0.36488010013557816387677409414947586Q, -0.34887947557569577047526159126703565Q, -0.33333198773426231868095335844342399Q, -0.31823860798357268745316074354464261Q, -0.3035994428915407927086292160567468Q, -0.28941376356199421334543326775454705Q, -0.27568003700259251963348266210052998Q, -0.26239595927717475510166267033301246Q, -0.24955849020075956493410868722363431Q, -0.2371638893386076311859247952179354Q, -0.22520775307556463019220672947952605Q, -0.21368505252818039398938452749375662Q, -0.20259017207968775297648115336201576Q, -0.19191694832666146249702413953519866Q, -0.1816587092359021720484117453151821Q, -0.17180831332064290072992681330484534Q, -0.16235818865639997533663276272992352Q, -0.15330037156853630364404549106194642Q, -0.14462654483572846910471674072111206Q, -0.13632807526589476511715224673350004Q, -0.12839605051362228117619033241835825Q, -0.12082131502061062975401342241486234Q, -0.11359450497302129819348705755904515Q, -0.10670608218178901767108607508338614Q, -0.10014636680382986857859380586600253Q, -0.093905568833595799431451407446515619Q, -0.087973818305513180839892673963966241Q, -0.082341194158450660178436474882477694Q, -0.076997751723445596499983664621487996Q, -0.07193354880544487378738311657933671Q, -0.067138670338759945862672351268719341Q, -0.06260325160428086186052507865402345Q, -0.058317500004230664509200379313199518Q, -0.054271715397367759935086056389293555Q, -0.050456309004063640355911746580908904Q, -0.046861820896606197349996098751978814Q, -0.043478936095419069610563231210812888Q, -0.040298499296663390818673202062521126Q, -0.037311528260921730030179369385366989Q, -0.03450922589637952691206152682222172Q, -0.031882991073143683811495499895307175Q, -0.029424428208099579660135740086865642Q, -0.0271253556620361184294276412754116Q, -0.024977812992693650455170906326981392Q, -0.022974067108942042492683430416652371Q, -0.021106617372505970898624990276714523Q, -0.019368199694551294992954740677403882Q, -0.017751789675058934941049112986218163Q, -0.016250604833268754660069071624504775Q, -0.014858105977601971243228436688712421Q, -0.013567997763391553658409398729735747Q, -0.01237422848648931564729341766812544Q, -0.011270989160397638106454864191015908Q, -0.010252711924012903699062738821083185Q, -0.0093140678263849278340314349076327353Q, -0.008449964034108265248268117472003357Q, -0.0076555405060818245590132017863229384Q, -0.0069261661794155687412632471018391501Q, -0.0062574347092383891891208397242483686Q, -0.0056451598040791099606160787309638234Q, -0.005085370197361128404963242365624777Q, -0.0045743042943771933124154458548517366Q, -0.0041084045328997985361611689079751989Q, -0.003684311494339074782205923019899892Q, -0.0032988578010873948724575222465114151Q, -0.0029490618343908704733333873759063219Q, -0.0026321213057645656305833925217639785Q, -0.0023454067136221407000743728815804873Q, -0.0020864547154229734242597171162514598Q, -0.0018529614442515743960802739125590691Q, -0.0016427757973362357767337812404644128Q, -0.0014538927225872833069713654109330111Q, -0.0012844465277911570122332123844688455Q, -0.0011327042356361946956708854910717802Q, -0.00099705900627114148706364731704457709Q, -0.00087602364761017102176873850865987398Q, -0.00076822423210114801927833212686428566Q, -0.00067239383717004607961542674246453911Q, -0.00058736642504742093080174568452091253Q, -0.00051207087617669287572188257954358842Q, -0.00044552518890326020005629570593666048Q, -0.00038683085665314188976467566548793199Q, -0.00033516743233532263998791108562179822Q, -0.00028978728824896558062724060959821788Q, -0.00025001057835114127416442774611755005Q, -0.00021522040834883412191823299277438955Q, -0.00018485821772693895358504217864611182Q, -0.00015841937651793126628162990110989201Q, -0.00013544899836493134204129687787204532Q, -0.00011553797023379563893736144870607114Q, -9.8319197997130553498037999641991487e-05Q, -8.3464066048770978005196778706600799e-05Q, -7.0679108115811218906095134416154613e-05Q, -5.9702885520625386671791420435362921e-05Q, -5.0303068310699595343932915440924077e-05Q, -4.2273713922018120404243909635658711e-05Q, -3.5432737373923361833214162324345645e-05Q, -2.9619566410724316247858190357851524e-05Q, -2.4692974507962358657498919760422057e-05Q, -2.0529084248443619898457582669713841e-05Q, -1.7019533243403755023062694063359981e-05Q, -1.4069794525207501119432380528847431e-05Q, -1.1597643166806136977541673876760615e-05Q, -9.5317607861225334492481477207976067e-06Q, -7.8104695663539218978433615931904191e-06Q, -6.3805874611085194148542316667027636e-06Q, -5.1963963511236276943307284768443108e-06Q, -4.2187150715072772351343816215627715e-06Q, -3.4140694292103354602072245113222152e-06Q, -2.7539515738216143655929212361430556e-06Q, -2.2141613647700348500196752438228238e-06Q, -1.7742226886238416354636940930406071e-06Q, -1.4168680155127991206906010386776301e-06Q, -1.1275848380651233667302406751686147e-06Q, -8.9421800420026716637883889021333717e-07Q, -7.0662233151961419975116851767251182e-07Q, -5.5636027112312322628504802544396849e-07Q, -4.3643976809083996211611047940700884e-07Q, -3.410878406808352344192770488162828e-07Q, -2.6555576704383762368205974553664254e-07Q, -2.0595212394180990975114609016580268e-07Q, -1.5910026405413012262681483592072574e-07Q, -1.2241714489336877307229490096356811e-07Q, -9.3810731511298667657736474465916103e-08Q, -7.1593485856820748829676007409030264e-08Q, -5.440972704881531639917700413368612e-08Q, -4.1174898509745034636641901499637178e-08Q, -3.1025009758847405806336107058366646e-08Q, -2.3274732865051716886351304700372955e-08Q, -1.7382826537542006005418736935360821e-08Q, -1.2923735225028943169773355461941788e-08Q, -9.5643672141012475959697164491130902e-09Q, -7.0451955080630225653421587634231792e-09Q, -5.1649492757248395049197216720378741e-09Q, -3.7682729974336732148768443187202036e-09Q, -2.7358262541647042643058722243498035e-09Q, -1.9763805681294590078061949445779507e-09Q, -1.4205419635481505647172465852581089e-09Q, -1.0157900987858722132374807163656952e-09Q, -7.2257800679698977337008871109952299e-10Q, -5.1128169466802636753557604076122797e-10Q, -3.5982705512040164549431243047583567e-10Q, -2.5185362244554210229589301236053854e-10Q, -1.7530147750309255605734608915901604e-10Q, -1.2132981051324705363357105718720941e-10Q, -8.3493950752320668610647319362559637e-11Q, -5.7122660267481958430713410881095016e-11Q, -3.8849686002793765883588257767026493e-11Q, -2.6263427379949721354783426839588953e-11Q, -1.7646499776378976222245772521934802e-11Q, -1.1783298320120630850539029584309311e-11Q, -7.818680628325362638575521404267539e-12Q, -5.1548364042751350429076854858018077e-12Q, -3.3765014608000294669628289654725219e-12Q, -2.1970744699592342642466439966825033e-12Q, -1.4200473669518126870628306394495551e-12Q, -9.1158007933577728676134065579326132e-13Q, -5.8113062505586808310528553401168741e-13Q, -3.6786791102075633999543555381361719e-13Q, -2.312068903576444768213986586668492e-13Q, -1.4426172485922308843621431499934686e-13Q, -8.9349666651298939216456676750565829e-14Q, -5.4925677721256790529564920614679325e-14Q, -3.350788309515877062958811342787956e-14Q, -2.0284076402444039895928235924589244e-14Q, -1.2182795128979644452912696549518128e-14Q, -7.2588537362052341752692102123089101e-15Q, -4.2900627866036669128285279549303027e-15Q, -2.514652539480771341683046756355483e-15Q, -1.4616871128475335847732485959402402e-15Q, -8.4243309583746983103209365972381765e-16Q, -4.8135175904015314514321104340303263e-16Q, -2.7263179430684054979795995181696999e-16Q, -1.5304422973965932233677561227408377e-16Q, -8.5137857250384433997363815737881969e-17Q, -4.6927950389611427068741326969599031e-17Q, -2.5625975950320089549856176296445119e-17Q, -1.3861334667950396744809652330676689e-17Q, -7.4257501921384642292801069180083189e-18Q, -3.9393094024445601833019849790813999e-18Q, -2.0690791459774228199242128751668536e-18Q, -1.0758286216493138282150652273565844e-18Q, -5.5366710173011999504598745894506669e-19Q, -2.819834003785898432921699582824987e-19Q, -1.4210063750313593796726974809925319e-19Q, -7.0842438781417380233044623416891119e-20Q, -3.4933505573095044526616590274445578e-20Q, -1.7035977510820099431072120902417317e-20Q, -8.2147060442121877800848286483420634e-21Q, -3.9159734384156989690338293870539541e-21Q, -1.8451498156271871363001830659526856e-21Q, -8.591874580623540129167786895666899e-22Q, -3.9530069577065764915635262899095704e-22Q, -1.7966697995134261385398916631645912e-22Q, -8.0654088208389219862969348472697735e-23Q, -3.5753372122620560623243551587275021e-23Q, -1.5647821323387363967354776667279086e-23Q, -6.760041764217369644940988147273804e-24Q, -2.8821363233025547133119762203866565e-24Q, -1.2124362329062417813651773042222047e-24Q, -5.0314218307880599929168429579030218e-25Q, -2.0592863122450067780181162680466505e-25Q, -8.310787797639985481399920298561774e-26Q, -3.3065180676145941342573597126851257e-26Q, -1.2965973462467399413826706323859068e-26Q, -5.010091032398552312889055575499255e-27Q, -1.9071793847355632162575945250968826e-27Q, -7.1505673339810973649967867585100117e-28Q, -2.6399060373682682300772738413986419e-28Q, -9.5946645421591590272737060192787035e-29Q, -3.4320780991310649618469641664780934e-29Q, -1.2079850663067081309863651387341263e-29Q, -4.1824647214881752797125541962674838e-30Q, -1.4241537066844817617919365428875337e-30Q, -4.7678333816988133611599026436806048e-31Q, -1.5689491781958062283731284050971648e-31Q, -5.0734388548496443499288192319785528e-32Q, -1.6116919160939245706339600215608967e-32Q, -5.0283566942543780691617432163678683e-33Q, -1.540320437147141644686176099192533e-33Q, -4.6313924434817901733319954389626016e-34Q, -1.3664702250574194828136706303116849e-34Q, -3.9550085269989845845237677023544696e-35Q, -1.1225918253644008151328041096174431e-35Q, -3.1238487433616834059785879847016762e-36Q, -8.5195402472064902632510499781952803e-37Q, -2.2764734813775829740707091909282255e-37Q, -5.9578473858349842310952826931317941e-38Q, -1.5267023004586063974485430859996629e-38Q, -3.8292452373883691255032248372279562e-39Q, -9.3976484123312742513645018653353223e-40Q, -2.2559187023391247540357057603608445e-40Q, -5.2951057740070273455910884012728247e-41Q, -1.21484005139195805370253212205352e-41Q, -2.7233329057563859457781652894773382e-42Q, -5.9629474724550016648059026687365969e-43Q, -1.2747904563208401684090170257821763e-43Q, -2.6599324647740072572781740009475107e-44Q, -5.4148975705500338800900730270033864e-45Q, -1.0750506783792922201321365237207054e-45Q, -2.0807277332937164031945232093695697e-46Q, -3.9244163140436388041993251545689042e-47Q, -7.2099378100469390278450504220931707e-48Q, -1.2897482330918123669983337886382095e-48Q, -2.2454989700172052890162893888242529e-49Q, -3.8033817751932373827258462682395899e-50Q, -6.2645207667921988981079909974995822e-51Q, -1.0029410834924645721226614397473029e-51Q, -1.5600495091009386993988441513950677e-52Q, -2.3565650292912781888129806450540921e-53Q, -3.4553986153009958542696149938952582e-54Q, -4.9157636744396300209125008789737936e-55Q, -6.781896900608377888472479708032872e-56Q, -9.0691940202443643747199815355477207e-57Q, -1.1749792443564338073595948464279018e-57Q, -1.4740730312062593807916913990480482e-58Q, -1.78984329859422798255692388149323e-59Q, -2.1022998433631042036525885470222986e-60Q, -2.3874311701754330999376394091254256e-61Q, -2.6199497113269397601815392049767616e-62Q, -2.7768131253969855575381129113408787e-63Q, -2.8408870962885800781023815369817483e-64Q, -2.8039688883465382638228147703461371e-65Q, -2.6684481602260540322238895901981613e-66Q, -2.447154080870857351995438102206883e-67Q, -2.1613619398610954911634747438934054e-68Q, -1.8373843424142226299889606750473108e-69Q, -1.5025103938453805607949990395131202e-70Q, -1.181175530500916159109188924367755e-71Q, -8.9211577919430036730810594588108188e-73Q, -6.4693987201487320046787049136480794e-74Q, -4.5015731884566473782551022645592059e-75Q, -3.0035842209914330912805318957189823e-76Q, -1.9204515997414609573256145075523711e-77Q, -1.1758810849678264495241195391760596e-78Q, -6.8900721571551738196701537405845152e-80Q, -3.8608563387442272849431335779050044e-81Q, -2.067461647504548936055359104346581e-82Q, -1.0572431033554667452601572946421337e-83Q, -5.1591788391388680011549213529878416e-85Q, -2.4006807351389258318746737135895233e-86Q, -1.0644152130176148388326772258538689e-87Q, -4.4934529232365451766727877769969084e-89Q, -1.8047048722799212873442491872423271e-90Q, -6.8904595506732549720140200562795596e-92Q, -2.4989637101487869067392480430274382e-93Q, -8.6018088469863275574189994840620463e-95Q, -2.8078924862967821005632597982452309e-96Q, -8.6849867519796128933390271698765342e-98Q, -2.5432396109247561096829294407826966e-99Q, -7.0446668978619724438762397385539258e-101Q, -1.8442018255867670937882313596001159e-102Q, -4.5587416725253559276425131397074849e-104Q, -1.0631088555204592085750091746032982e-105Q, -2.3367281748964035585738800969158434e-107Q, -4.8365042631130861220456426369022544e-109Q, -9.4175235620877562064839942442771856e-111Q, -1.7234789461694177262894389768372263e-112Q, -2.961523379734186944047236046386456e-114Q, -4.7734609518585313301965336240248189e-116Q, -7.2097952822898640408181240050284012e-118Q, -1.0193874335777637006841936637337494e-119Q, -1.3478166999163432272267137132118888e-121Q, -1.664711300732456447679638084338044e-123Q, -1.9186583950476330921591900813216499e-125Q, -2.0612648015288569084831497840721453e-127Q, -2.0618953788663489533345425995316527e-129Q, -1.9182619555236359873955912398757981e-131Q, -1.6579128778519222560290325566834437e-133Q, -1.3296099070094495947742421798539638e-135Q, -9.8828871549374803987116212190790475e-138Q, -6.8001851936680182899829217843093739e-140Q, -4.326204276337993340016401440114041e-142Q, -2.541595528522972170757701141043585e-144Q, -1.3771279659495821106478129347836407e-146Q, -6.8731739624384578591538313057486772e-149Q, -3.1556844841718302469140036866580175e-151Q, -1.3311046015007009547835866736304136e-153Q, -5.1514958005010918440961548583793399e-156Q, -1.8267037628056442215789990487980127e-158Q, -5.9267728539858017097499689403281403e-161Q, -1.7570179057820636782453206639159588e-163Q, -4.7525273644644354076088841580861234e-166Q, -1.1712151180634311771192081451244071e-168Q, -2.6258838722203054808989693856617573e-171Q, -5.348036158420190530754215189932846e-174Q, -9.8795260619978679822138888646044246e-177Q, -1.6528501557776328068085680725354548e-179Q, -2.5003947565086958266446692572037419e-182Q, -3.414859619193216220277979880289186e-185Q, -4.203653605459133226010277835528306e-188Q, -4.656508257021667283525304276478024e-191Q, -4.6339530996535525354074849617025537e-194Q, -4.1358781926049696182200878642464924e-197Q, -3.3049447294950072208241398344574307e-200Q, -2.3603956208589999494122184847510332e-203Q, -1.504047014068835401149660217771386e-206Q, -8.5352021958508046391358762569456352e-210Q, -4.3057568200632214990724928361534485e-213Q, -1.9273628185121124753795720186090423e-216Q, -7.6407868488415044113016735817166107e-220Q, -2.677582009683177696032426651320389e-223Q, -8.2781625043007349263821040953856809e-227Q, -2.2534850368391124035175853226989187e-230Q, -5.3905685902076735531578346280529237e-234Q, -1.1308097276815650308355193143392165e-237Q, -2.0759721813705410476824570327172646e-241Q, -3.3282687604583975257076029809432931e-245Q, -4.6500067135453696640703738061616968e-249Q, -5.6491873692239596907540663618927003e-253Q, -5.9546966626331923325761672881695778e-257Q, -5.4337990425534927929347405657826422e-261Q, -4.2828363537638634149994842306436067e-265Q, -2.9089854251520548595336724777976923e-269Q, -1.6986979835072953491948167874994245e-273Q, -8.5078874736023215648629448861985246e-278Q, -3.6459301611001785382074525089193986e-282Q, -1.3335508003947114825747939690317958e-286Q, -4.1528110309781799510004618371028852e-291Q, -1.0982617987267680462918374327390562e-295Q, -2.4602717457871291167756856167595572e-300Q, -4.656274386497415739809125734407304e-305Q, -7.4253896458167870494540985632704167e-310Q, -9.9507057756639922437150285707977622e-315Q, -1.1175135227544318386694717687968224e-319Q, -1.048835867763772555877163638261832e-324Q, -8.2033390786966798682011027925866189e-330Q, -5.3315735131149564162074506318716251e-335Q, -2.8710141330746883154448723258150022e-340Q, -1.2771564172267081609509990200434409e-345Q, -4.679237231988855089392537021634395e-351Q, -1.4076706654157792032739797667966913e-356Q, -3.4663625720057878756572289263095777e-362Q, -6.965054539543846678373236132189486e-368Q, -1.1383132196432715526434302555043402e-373Q, -1.5082487448329479128870155332131625e-379Q, -1.614816076667686258793882820784337e-385Q, -1.3923692408287144362457422616397937e-391Q, -9.6357705989726650954351553689911432e-398Q, -5.3335362887861762022108923833857644e-404Q, -2.3529529322735497728866304144280939e-410Q, -8.2438250199447939690869259289572704e-417Q, -2.2855238208173770201725378922268636e-423Q, -4.9955499211081324148253281714818585e-430Q, -8.5762081046608095383619080676476731e-437Q, -1.1520466338982341020319294100361546e-443Q, -1.2062291598612346324429863879453543e-450Q, -9.8055160740422284599044523336543234e-458Q, -6.1639765190753631642664330172075405e-465Q, -2.9843045493133686661298629930164973e-472Q, -1.10823285826398300175507717773449e-479Q, -3.1434790244922162638267628745063788e-487Q, -6.7817090417200778282332735946819014e-495Q, -1.1080137352329998556662834370761201e-502Q, -1.3649835831781010441492893639688094e-510Q, -1.2622778335796144231373603506854352e-518Q, -8.7230079476014413312144088584965706e-527Q, -4.4840352485865235615809427931860355e-535Q, -1.7066304212439665043951442710341263e-543Q, -4.7865441809026242359826342326024693e-552Q, -9.8452970343625269250999633839131245e-561Q, -1.4778790578536059527306608667934244e-569Q, -1.6110092952268207636105891878784886e-578Q, -1.2688728710128977236628155987138424e-587Q, -7.1841678295696924944920506687608424e-597Q, -2.9088109305034413675847131931270509e-606Q, -8.3780318645305679545954425238306175e-616Q, -1.7073663715760515568257862032253151e-625Q, -2.4485200806811154999909042531599294e-635Q, -2.4573647256627145606915141874939106e-645Q, -1.7162616532700922219515556951255632e-655Q, -8.2940336462928562559347382857731944e-666Q, -2.7573883713227551117703313812478972e-676Q, -6.2693384642795198624500212806556514e-687Q, -9.6903310752055252797176389760001952e-698Q, -1.0120668627816313397657974563178381e-708Q, -7.0982696439175036135044214112413552e-720Q, -3.3223490979680344536621296663739436e-731Q, -1.0311489910731877825037981367801772e-742Q, -2.1084888364587342412345374253683016e-754Q, -2.8218960738946335538305090801367889e-766Q, -2.4554534404272344111007341068959851e-778Q, -1.3797451463824018308120568764941013e-790Q, -4.9722352119016468411483808073270903e-803Q, -1.141174632389420312821039197546226e-815Q, -1.6562061873208393415939125326414615e-828Q, -1.5090517973695404620062521847072836e-841Q, -8.5691467995207824570732116071300356e-855Q, -3.0100992462861676532185696320551197e-868Q, -6.4915591914227985933992989098330185e-882Q, -8.5291480289311605733387922175211764e-896Q, -6.7742561345607759008586516480118676e-910Q, -3.2268177845742615217916640601654205e-924Q, -9.1442367835386354685297667090946292e-939Q, -1.5290755306231257347546172504187466e-953Q, -1.4962742317658249855750971462991461e-968Q, -8.4962983613273098681909435342233043e-984Q, -2.7756354317962233287713988508361331e-999Q, -5.1716412864088598574901876510481632e-1015Q, -5.4473756239469544256829106354679121e-1031Q, -3.2146847397534380657713310981627524e-1047Q, -1.0532205872897567235295617558264118e-1063Q, -1.8980407364210960924624682689327296e-1080Q, -1.8638413013078585989318566769757098e-1097Q, -9.878184707808045512821781472334627e-1115Q, -2.7982877400590951555125003865089596e-1132Q, -4.1953712551999192397935862947005203e-1150Q, -3.2957901195530958784930344030449164e-1168Q, -1.3428874261296622720955452657433921e-1186Q, -2.8087996369552811844908096236163199e-1205Q, -2.9843000347844479031722692316121269e-1224Q, -1.5935747451543053300901499796204061e-1243Q, -4.2306328294965009362324717933882302e-1263Q, -5.5228314338471652594276889712636454e-1283Q, -3.5058000799426290470482678197659356e-1303Q, -1.0699175488609417213076569958022624e-1323Q, -1.5518242259547372491845055361631511e-1344Q, -1.0572462818562698193769229028835047e-1365Q, -3.3433625974012960853422590535670218e-1387Q, -4.8485886276278601232637821173583126e-1409Q, -3.1852238059952872764052353398865978e-1431Q, -9.361416530125180143871854771737577e-1454Q, -1.2153997660730855590852157017720519e-1476Q, -6.8815302334269492601957883930533625e-1500Q, -1.6771176516091137975239852271122166e-1523Q, -1.7361595430709725552633953103091938e-1547Q, -7.5319512846392125534714956610857413e-1572Q, -1.3507317243577349648175397566286196e-1596Q, -9.8749002116827156793758713498543268e-1622Q, -2.9017600444094134408965714990369305e-1647Q, -3.3784846386968340746897973924953046e-1673Q, -1.5359688750531830898643386942465616e-1699Q, -2.6866527047176469998545109651176216e-1726Q, -1.7810521996687791658708980830532171e-1753Q, -4.4069918722394069695147388355206565e-1781Q, -4.0074519225536368666632735822098734e-1809Q, -1.3182831725717259025871898011803224e-1837Q, -1.5438654487729921345949019930471491e-1866Q, -6.332964675016807525269001303450335e-1896Q, -8.9500787015064447527716544282145449e-1926Q, -4.2852989232796780762796817666627905e-1956Q, -6.8338809894056785809735926537843468e-1987Q, -3.5675222855473848333391077011021476e-2018Q, -5.9902029406713556414824109568504936e-2050Q, -3.1778560977483191067544886656870642e-2082Q, -5.2307612099795388778239926501377221e-2115Q, -2.622585004749696786707859063948337e-2148Q, -3.9309566721122472026471743065924237e-2182Q, -1.7282752365752259330631889016659569e-2216Q, -2.1861776117108222661355073061235227e-2251Q, -7.8018157093958177389763723299669248e-2287Q, -7.6999746209936272927151686536635957e-2323Q, -2.0595655884579572710261610678442846e-2359Q, -1.4626001721502482969651469854201664e-2396Q, -2.7006580242811967764786623573354003e-2434Q, -1.2693899148553691566069699600055988e-2472Q, -1.4864297410986916900605959894904808e-2511Q, -4.2424104158612592858897827763816223e-2551Q, -2.886325476737890820535159860850169e-2591Q, -4.5765223979102481270954678544866293e-2632Q, -1.6528081850614599523381524655646053e-2673Q, -1.328274798653407243855327660531847e-2715Q, -2.3198223770931618563520410588946674e-2758Q, -8.5957488899967850735558558018957728e-2802Q, -6.5943404261286890309954636455911131e-2846Q, -1.0217558991001370161903847475864815e-2890Q, -3.1179630912672502182141115573865598e-2936Q, -1.8265451376704665882357813599889357e-2982Q, -2.0014192526716340395460104532447404e-3029Q, -3.9951106676925283777648320420658919e-3077Q, -1.4143501265341338386948746898388465e-3125Q, -8.6415836958539035920266027922282148e-3175Q, -8.8638553988044639042147624946934825e-3225Q, -1.4840219096599721556289490714622718e-3275Q, -3.9413909056885523526277851486916637e-3327Q, -1.6130887141803206541250840542937335e-3379Q, -9.8781642524919349085295532219577414e-3433Q, -8.7843931949057976239401506792380659e-3487Q, -1.1004454490075535460405971636548993e-3541Q, -1.8829655853829584691138469084815955e-3597Q, -4.2649913833758498152779971325727165e-3654Q, -1.2387002826718376930423594688980877e-3711Q, -4.4662108799989251681924501424182082e-3770Q, -1.9344990293606773314152567555369947e-3829Q, -9.7355570095915342874542008807254313e-3890Q, -5.5029370896508671666190160013096548e-3951Q, -3.3753310533018114196752016778611603e-4013Q, -2.1693889909489136218749502032440802e-4076Q, -1.4100342946015435859763977543837031e-4140Q, -8.9396827455669883373190732493946026e-4206Q, -5.3296285690324414372171445344916662e-4272Q, -2.8786292587718554179132949540534825e-4339Q, -1.3563301511005327997304553445712977e-4407Q, -5.3648093624713363852431573541370394e-4477Q, -1.713199655224600838900218899825865e-4547Q, -4.245367036181261378573090733274636e-4619Q, -7.8414039345064814928186445860311876e-4692Q, -1.036301574835369426138520680597679e-4765Q, -9.4005882200264089643362665220611737e-4841Q, -5.6115028230432295314938587541868893e-4917Q, -2.1117808872654504815205210716165957e-4994Q, -4.7969059512296546218382678391152525e-5073Q, -6.2923913054777813860480394257938674e-5153Q, -4.557320132561115296699430714437428e-5234Q, -1.7411358133348974154538037089867482e-5316Q, -3.3501410753353566412413655174778596e-5400Q, -3.097148592957890545441753638107994e-5485Q, },
   };
   m_weights = {
      { 1.5707963267948966192313216916397514Q, 0.23002239451478868500041247042232167Q, 0.00026620051375271690865701015937223316Q, 1.3581784274539090834221967874745002e-12Q, 1.001741678406625296380989561316704e-35Q, 2.6763080920617460968679410949198166e-99Q, 7.7670706886334062872146243844453043e-273Q, 2.6770629459428179490864940513366914e-745Q, 2.497123188557279400555877663164169e-2030Q, 3.7829658019347837822103381908509695e-5524Q, },
      { 0.96597657941230114801208692453802948Q, 0.018343166989927842087331266912053799Q, 2.1431204556943039357697233307232118e-07Q, 2.8003151019775889582580016992170153e-21Q, 1.123270534548691878982747435678734e-59Q, 9.1753268750017841272445320853712195e-165Q, 3.7096469071314047287189555701359196e-451Q, 2.1358827779704788581082183250272886e-1230Q, 2.4637500105830058174530832607403982e-3349Q, },
      { 1.3896147592472563228608191295320513Q, 0.53107827542805397476113231761531408Q, 0.0763857435708323041883405748316428Q, 0.0029025177479013135935932948904580215Q, 1.198370136317072004690126421734261e-05Q, 1.1631165814255782765597155262382926e-09Q, 2.197079236297979917409204112478354e-16Q, 1.3635103307637615413724747655081585e-27Q, 3.3700568540419264989934173928550566e-46Q, 5.1969783800898552138641449339116869e-77Q, 6.0080341705713501485949327691027087e-128Q, 4.564204056355599097208981999351872e-212Q, 6.769934297924075481071769868728861e-351Q, 6.3189772257105317991231885214248323e-580Q, 1.1318535773666956837787222565063026e-957Q, 1.300088515992481474129980450433913e-1580Q, 8.3018817290920015915795630666821309e-2608Q, 1.6615718540023971313123206033705184e-4301Q, },
      { 1.5232837186347052131949627901588453Q, 1.193463025849156963909371648222972Q, 0.73743784836154784136450085848526681Q, 0.36046141846934367416541940530511192Q, 0.13742210773316772341110281600075763Q, 0.039175005493600779071814125724013544Q, 0.0077426010260642407123309111640668157Q, 0.00094994680428346871690539180358829065Q, 6.2482559240744082890784437584871091e-05Q, 1.8263320593710659699109280974494727e-06Q, 1.8687282268736410131523743935312467e-08Q, 4.9378538776631926963708240981686386e-11Q, 2.2834926702613953995564216678996858e-14Q, 1.1227531428181551500942554523402945e-18Q, 3.0976539701173543715458514327631078e-24Q, 2.1121233435372255913526811977623162e-31Q, 1.2424147570616052367007700396344415e-40Q, 1.6327707331799493237812947626932747e-52Q, 8.460688731096213796022276271481337e-68Q, 1.8644492079588650273068038890475311e-87Q, 1.0013128468666430206628687242136761e-112Q, 3.3344435411868990213060767519646709e-145Q, 6.1751576225487765303410376146008819e-187Q, 1.4953240022257075856966174469213765e-240Q, 1.995167671391421283850079422966012e-309Q, 6.7986022131150125295165555843516628e-398Q, 1.6112168443011532782661941324936245e-511Q, 1.8998420056198456993096891152100817e-657Q, 7.4500584401117916806764514072363403e-845Q, 1.6092274599867451896713421807328385e-1085Q, 1.4294469100875426784155324297970964e-1394Q, 1.9699812322172327797426511042200449e-1791Q, 4.8353547995000100603312087832016511e-2301Q, 2.0016297694765957880783422493672502e-2955Q, 1.0619575437766938720176582011701185e-3795Q, 1.1494098249640889641399434474174788e-4874Q, },
      { 1.5587733555333301450624222553039117Q, 1.4660144267169657810275411936658895Q, 1.2974757504249779979885383085284353Q, 1.0816349854900704074448532749336471Q, 0.8501728564566200689527310980522113Q, 0.63040513516474369106015058023920241Q, 0.44083323627385823706771270610993222Q, 0.29024067931245418500061231479966878Q, 0.17932441211072829296345978397121765Q, 0.10343215422333290062482385052951418Q, 0.055289683742240583845301977440481557Q, 0.027133510013712003218708018921405436Q, 0.012083543599157953493134951286413131Q, 0.0048162981439284630172757660071387715Q, 0.0016908739981426396472155417510249034Q, 0.00051339382406790336016588906448858883Q, 0.00013205234125609974878680402074340758Q, 2.8110164327940134748736157546988307e-05Q, 4.8237182032615502124025440343943169e-06Q, 6.4777566035929719907733987417697432e-07Q, 6.5835185127183396672340995882066949e-08Q, 4.8760060974240625868904606426809347e-09Q, 2.5216347918530148571826656491854398e-10Q, 8.6759314149796046501956077624778843e-12Q, 1.8802071730750649809476255843771975e-13Q, 2.4124230384308786393899307730550295e-15Q, 1.7084532772405701711664118263431986e-17Q, 6.1682568490762382593952567143955272e-20Q, 1.0376797238528706160610863270915827e-22Q, 7.3459841032226935608549262812034877e-26Q, 1.9497833624335174811763249596143101e-29Q, 1.7024387761257547218675324410867209e-33Q, 4.2164863709484278882204462399998679e-38Q, 2.5044277116275284317811847533784782e-43Q, 2.9493601457461933137195159567099426e-49Q, 5.5513323569653710605229337238942258e-56Q, 1.3081165120210821643686110907899211e-63Q, 2.9260824463823975328629659452367614e-72Q, 4.5407734669997824148941045053504133e-82Q, 3.4265635692086987660673772586083229e-93Q, 8.4064359488831769989074631453464805e-106Q, 4.2486192688198694407012327873632857e-120Q, 2.6378208263504521457685936139412266e-136Q, 1.1199291455841279757620547508811484e-154Q, 1.6741593786710889281267003484320699e-175Q, 4.1533060810194922183660114746657927e-199Q, 7.291512660905660427014583724901483e-226Q, 3.4484028358868219730957875473275039e-256Q, 1.470608613733186341839899819856118e-290Q, 1.6363373245553192894384271318647291e-329Q, 1.1653396430451048019810427186628332e-373Q, 1.0806491346819681711718251969578419e-423Q, 2.1475318157612117601623532784345252e-480Q, 1.1837197513952188698387978224315556e-544Q, 1.7840752052025753295716828917620433e-617Q, 5.3242634124221408997248254448704286e-700Q, 1.6062136117391783778620623735552911e-793Q, 1.6828070526919983211589685788034629e-899Q, 1.3428023135061849684751687999574317e-1019Q, 1.0762445467071890806353267455766392e-1155Q, 6.4211043768224511497561388149947945e-1310Q, 1.0999298719684690155223457966483755e-1484Q, 9.9551400313164741067089535072684105e-1683Q, 3.7874715825152699680401002242066869e-1907Q, 1.8633554093895907210742600678288691e-2161Q, 1.2425018321859109200647073828888711e-2449Q, 3.4744660691878314268031415759661457e-2776Q, 3.1635539874917104171786237474568114e-3146Q, 1.520164792309349080207015729831993e-3565Q, 1.0587692108054511977730210493803509e-4040Q, 3.9269571376984945676082618447700578e-4579Q, 2.9165229925801338811576465571535852e-5189Q, },
      { 1.56778143130722185718457839560813Q, 1.5438811161769592204120195652304554Q, 1.4972262225410362896175121106253706Q, 1.4300083548722996676294145121698845Q, 1.3452788847662516614631881421588284Q, 1.2467012074518577048171373166756783Q, 1.1382722433763053733718328301717509Q, 1.0240449331118114482594022454942541Q, 0.90787937915489531693097972059732257Q, 0.79324270082051671787385259862995589Q, 0.68306851634426375464118893187202369Q, 0.5796781030877876470811045242977012Q, 0.48475809121475539286590775419868886Q, 0.39938474152571713514696619655275183Q, 0.32408253961152890401776015000060852Q, 0.25890463951405351600251387258910213Q, 0.20352399885860174518647884913232324Q, 0.15732620348436615026738563376259734Q, 0.11949741128869592427594275905822144Q, 0.089103139240941462840959442033942474Q, 0.065155533432536205042255277931864103Q, 0.046668208054846613643791300289316396Q, 0.032698732726609031112522702100248949Q, 0.022379471063648476483258477281403216Q, 0.014937835096050129695520628452448085Q, 0.0097072237393916892692355786307589937Q, 0.0061300376320830301252445771940873246Q, 0.0037542509774318343022967144602791306Q, 0.0022250827064786427021584620411703896Q, 0.0012733279447082382026740903535577353Q, 0.00070185951568424227080474304718567332Q, 0.00037166693621677760301295760218968355Q, 0.00018856442976700318571529922115575474Q, 9.1390817490710122732277133049597672e-05Q, 4.2183183841757600604161049520839395e-05Q, 1.8481813599879217116302847163026167e-05Q, 7.6595758525203162561568606199506296e-06Q, 2.991661587813878709443954037223499e-06Q, 1.0968835125901264731967931401061751e-06Q, 3.7595411862360630091183817668146341e-07Q, 1.1992442782902770218679992206070689e-07Q, 3.5434777171421953042822362946016499e-08Q, 9.6498888961089633609206181967630592e-09Q, 2.4091773256475940778596088439332178e-09Q, 5.482835779709497755012456926288581e-10Q, 1.1306055347494680535789879497045831e-10Q, 2.0989335404511469108589724179284344e-11Q, 3.4841937670261059685298917826974968e-12Q, 5.1341275245014207474276938761808301e-13Q, 6.6639922833087653243643099189129361e-14Q, 7.5567217757805651894455261838657557e-15Q, 7.4209932309922167588538662071098972e-16Q, 6.2528048446104553564782731960667282e-17Q, 4.4757595066690969713939566842636666e-18Q, 2.6931206614869695057714525810417824e-19Q, 1.3469941569542286092134858963015562e-20Q, 5.5335834994155711455633228489644644e-22Q, 1.843546974718149378096179376158747e-23Q, 4.9139368712649040083308200957748574e-25Q, 1.0329391306928575387668383646132905e-26Q, 1.686277003849260652771901316947161e-28Q, 2.1033057490018089523843332100004934e-30Q, 1.9699209796232343253581550830906289e-32Q, 1.3599894616303795697940905597197162e-34Q, 6.7859788375592479085540582968820347e-37Q, 2.3965063699443217406067231991536888e-39Q, 5.8579569483084210846386345961131375e-42Q, 9.6783927755717095571366983570964434e-45Q, 1.0538361132564208838364959652125833e-47Q, 7.3615858309787649210787574154381217e-51Q, 3.2059785352833866133968160706885294e-54Q, 8.4430892661864256082234111135836121e-58Q, 1.301669487442817299199572925297743e-61Q, 1.1348985048372046635260385887339235e-65Q, 5.393881369511580841024486229556929e-70Q, 1.3438019476233711028911671531262384e-74Q, 1.6833095936633240263022508251714738e-79Q, 1.0142059074468158538185578436642225e-84Q, 2.8036136193449874498478125381332892e-90Q, 3.3815387893836694650191340608232241e-96Q, 1.6868699597973134851614068554314712e-102Q, 3.2876716054636550397600067563177134e-109Q, 2.3561803975866304919776634857937977e-116Q, 5.8212714268864182676007475006940935e-124Q, 4.6289769416942015466154791282940135e-132Q, 1.1011712417530966759846558629187253e-140Q, 7.2497708296024463973360079596584377e-150Q, 1.2159344493950079010263214501254854e-159Q, 4.7567332287785056252666937863445785e-170Q, 3.9513561319289952903145090015894379e-181Q, 6.3069398037899023031641976078084843e-193Q, 1.7390958115772548420234336819215406e-205Q, 7.3973824490445198187310257149181993e-219Q, 4.3025693007354699025906405899973485e-233Q, 3.0098295695832487716215316168466764e-248Q, 2.2089957315907529731668895943677893e-264Q, 1.4707383302537016829517267662493224e-281Q, 7.6092047493292429607812922938663979e-300Q, 2.5944582029845183791860228756757574e-319Q, 4.8919577835343219860639698905291437e-340Q, 4.2321357135722486316129327838961609e-362Q, 1.377082367391413218915156512362392e-385Q, 1.363952742750262175587655506596678e-410Q, 3.282973957767873844612670506024268e-437Q, 1.510934727927437884800902577518545e-465Q, 1.0301501430760404101952742972682365e-495Q, 7.9295801879971179076172461167077394e-528Q, 5.1606434722064335409175586840009083e-562Q, 2.0872322362104964031726231732635154e-598Q, 3.7804014749166388131317806793058942e-637Q, 2.1632858420984952938030309932161571e-678Q, 2.697969237867160734724398915290777e-722Q, 4.939067732330739558253971083984646e-769Q, 8.7137367268607555569549275663836614e-819Q, 9.4666568790742115790866970449104673e-872Q, 3.9314930560193364712728646099681646e-928Q, 3.7572363915786555422592370375561427e-988Q, 4.8138810542737654527596947066774263e-1052Q, 4.6523364803809138193102091214841167e-1120Q, 1.8387400682879078406702806989874382e-1192Q, 1.5488882086634896755007358131079033e-1269Q, 1.3896133256118102918631384214217437e-1351Q, 6.3450498372554790778619758345952674e-1439Q, 6.718192925085413277150654870462718e-1532Q, 7.1439377408604826875747242429241183e-1631Q, 3.1307195483169831435149812630603435e-1736Q, 2.1906656484419255825224621505729532e-1848Q, 8.9202911849845695393803303860897414e-1968Q, 7.2181700833126514821590990431204877e-2095Q, 3.69827933239331943547271842529346e-2230Q, 3.5509166675222604652849078491152928e-2374Q, 1.7482004370958645792029686167921119e-2527Q, 1.1106891190500999916896792610406226e-2690Q, 2.0967298422181990469075373625054175e-2864Q, 2.4632275747625679891658308849723178e-3049Q, 3.4100009144092401643961247399658256e-3246Q, 9.461422635018898458311235983749575e-3456Q, 7.9828243452163492698389979442314978e-3679Q, 2.751586075619233132202409409236628e-3916Q, 4.573406478327970954594749406297331e-4169Q, 3.7694660070555500868572517438534528e-4438Q, 1.3681877706178551350653622286560967e-4724Q, 1.6613468385141710911219854343435161e-5029Q, 4.3416310672295866779590889330326041e-5354Q, },
      { 1.5700420292795931467492986437735064Q, 1.564021403773232099877114326609257Q, 1.5520531698454121192208571607383634Q, 1.5342817381543034316353772145375646Q, 1.5109197230741697127061791873634288Q, 1.4822432978855380699918515557505545Q, 1.4485862549613225916106262684975582Q, 1.410332971446259012949044799400295Q, 1.3679105116808964880788896668731933Q, 1.3217801174437728578945834791848425Q, 1.2724283455378627082346213018713573Q, 1.2203581095793582207386389532998567Q, 1.1660798699324345766327658650910729Q, 1.1101031939653403795568765186098518Q, 1.0529288799552666555511877000344537Q, 0.99504180404613271513656897365437693Q, 0.93690461274566793365666309492163748Q, 0.87895234555278212039056482196455926Q, 0.82158803526696470334184690990337618Q, 0.76517929890895613670160403610671453Q, 0.7100559012054689838533558330696999Q, 0.65650824613162753075637187406975179Q, 0.60478673057840362157663695141011708Q, 0.55510187800363350959266471517172014Q, 0.50762515883190809969744129508607212Q, 0.46249039805536776129564056825534108Q, 0.41979566844501548065589545493380249Q, 0.37960556938665160999227127370288764Q, 0.34195379592301683230370363663700268Q, 0.30684590941791694932077002517250017Q, 0.27426222968906810637090960121437003Q, 0.24416077786983990867520612813967892Q, 0.21648020911729617038131524528780352Q, 0.19114268413342749532225095746508781Q, 0.16805663794826916233031413596643794Q, 0.14711941325785693247796311096185123Q, 0.12821973363120098675278273881295278Q, 0.11123999898874453034874054225116091Q, 0.096058391865189467849178662072631058Q, 0.08255078811070173765353752357300814Q, 0.070592469906866999351840896875301286Q, 0.060059642358636300319399035772689021Q, 0.050830757572570471070050742610585547Q, 0.042787652157725676034469840927028069Q, 0.035816505604196436523119991586426561Q, 0.029808628117310126968601751537697736Q, 0.024661087314753282510573737140694345Q, 0.020277183817500123925718353350832364Q, 0.016566786254247575375293749008565117Q, 0.013446536605285730674148393318910799Q, 0.010839937168255907210869762269731544Q, 0.0086773307495391815853559138369297225Q, 0.0068957859690660035329120415695881464Q, 0.0054388997976239984331249261778682035Q, 0.0042565295990178580164922070775372331Q, 0.0033044669940348302363413922293941345Q, 0.0025440657675291729677737674183717872Q, 0.0019418357759843675814337234877431187Q, 0.0014690143599429791058440789865212569Q, 0.0011011261134519383861925290686638441Q, 0.00081754101332469493114796258629778861Q, 0.00060103987991147422573401527665731798Q, 0.00043739495615911687786086941160964625Q, 0.00031497209186021200273816328980956318Q, 0.00022435965205008549104107147410537586Q, 0.00015802788400701191949230617575051454Q, 0.00011002112846666697224455022994264118Q, 7.5683996586201477788165374446595019e-05Q, 5.1421497447658802091816641948792101e-05Q, 3.4492124759343197699875355951697823e-05Q, 2.2832118109036146591351441225788429e-05Q, 1.4908514031870608449073273504082825e-05Q, 9.5981941283784710776464665022285646e-06Q, 6.089910032094903925589071885768365e-06Q, 3.806198326464489904501471179191111e-06Q, 2.3421667208528096842717147966610279e-06Q, 1.4183067155493917523149875336299115e-06Q, 8.4473756384859863469243774523267053e-07Q, 4.9458288702754198508296104158884319e-07Q, 2.8449923659159806339254761906866073e-07Q, 1.6069394579076224910854372243815059e-07Q, 8.907139514024238712375244371846641e-08Q, 4.8420950198072369668651027805560416e-08Q, 2.5799568229535892380414249079124995e-08Q, 1.3464645522302038795727293982948898e-08Q, 6.878461095589900111136392082034183e-09Q, 3.4371856744650090511359612982214485e-09Q, 1.6788897682161906806903450478834801e-09Q, 8.0099784479729665355704238927483163e-10Q, 3.7299501843052790038025133658401989e-10Q, 1.6939457789411646875651299532593942e-10Q, 7.4967397573818224522463983518852213e-11Q, 3.2304464333252365759775564205312505e-11Q, 1.3542512912336274431500361753892705e-11Q, 5.5182369468174885820640573102698266e-12Q, 2.1835922099233609052099969606827855e-12Q, 8.3831289605026670935474089966848491e-13Q, 3.1194977286848081234778051650691241e-13Q, 1.1240208959922861475529576279412917e-13Q, 3.9176794506016467939451671035436525e-14Q, 1.3194342231967989407842390664342776e-14Q, 4.2891962220679080018159948630799523e-15Q, 1.3443222875395221462210806176730482e-15Q, 4.0575577022628576213784976604019497e-16Q, 1.1779812127248348143079542926522782e-16Q, 3.2853861628847006575461912014493979e-17Q, 8.791316558919890092526902998792709e-18Q, 2.2540748304368819446158103388678448e-18Q, 5.5301769128403375894905317866280607e-19Q, 1.2964527140689369428189650628176523e-19Q, 2.8999645564315719864785175397007916e-20Q, 6.1801432493399884472690523294170016e-21Q, 1.2528676432273210433625630071013181e-21Q, 2.4122505468361010054762467348044815e-22Q, 4.4039066999398680900169681674054685e-23Q, 7.6105778075820625755732634136772293e-24Q, 1.2428051652123164945236765528871363e-24Q, 1.9143106902239760676488034727771211e-25Q, 2.7761251025850583169467065718383927e-26Q, 3.7831240728137797052283562714381636e-27Q, 4.8349101548188476652448113863661033e-28Q, 5.7831786972290529603663947121744896e-29Q, 6.4605757034417219896312763235825947e-30Q, 6.7260373895879405356485977108634093e-31Q, 6.5111534511374516546657070065569591e-32Q, 5.8474090745481019884619654259625198e-33Q, 4.8600460551422733352577609809902488e-34Q, 3.7292395298436024198774848925631642e-35Q, 2.6351230616803666949611566230315689e-36Q, 1.7101926401490697241365293331985362e-37Q, 1.0166685309552127228580017693019009e-38Q, 5.5206909443427625675929025278647327e-40Q, 2.7304755116605008208950089540576932e-41Q, 1.226380966652512847885874020657185e-42Q, 4.9868392983536125725869145787511357e-44Q, 1.8300654157711633015675688730645846e-45Q, 6.0413503225082640435207520715838788e-47Q, 1.7880003085257126775048837670283103e-48Q, 4.7278206635290633557485891499143011e-50Q, 1.1129101718049771950203375840916927e-51Q, 2.3236007287936054634490409808175661e-53Q, 4.2865792135438400818864832679645293e-55Q, 6.9598735225537515535247172084789902e-57Q, 9.905399812867464416784441328019894e-59Q, 1.2305690322370172647654483736261024e-60Q, 1.3287055463682438112745418077294776e-62Q, 1.2413844609001762091919818631042531e-64Q, 9.9894889830989163005162036230629025e-67Q, 6.8909792890209806239542239718708282e-69Q, 4.0550412837679391530484571616360602e-71Q, 2.025325478051833791855348665597344e-73Q, 8.541193860817129005635611395334028e-76Q, 3.0250584958478075188394058484674014e-78Q, 8.9481574983737847286009199268691343e-81Q, 2.1980365957192520850759686078382165e-83Q, 4.457339084251821318983583351230449e-86Q, 7.4167316712100725345384814229429518e-89Q, 1.0062789446470429122974386215670469e-91Q, 1.1060617024861203148604052551620709e-94Q, 9.7834581723103753096719332058265664e-98Q, 6.9161160403270601376643047091198938e-101Q, 3.8797049192578037362892459079447827e-104Q, 1.7144044358094688607777762897347039e-107Q, 5.9226551398143962456841752185003654e-111Q, 1.5871361682624074916136417159312335e-114Q, 3.2726893534998181040216294108876001e-118Q, 5.1496220394807877679493913965250783e-122Q, 6.1305346175987605932476180523733874e-126Q, 5.473030846543808520009167994615492e-130Q, 3.6307475309683583384111588486160093e-134Q, 1.7730007537696346688915570033698743e-138Q, 6.3116464532127354831381913256985002e-143Q, 1.621583693919220194566929433171311e-147Q, 2.9757935132829859613289387859126523e-152Q, 3.8591705765442151638142119206858087e-157Q, 3.4980555618589782147605871793646301e-162Q, 2.1911032417571959066795311563828556e-167Q, 9.3736234796108920474724971094564098e-173Q, 2.7058522933326574274299142121455261e-178Q, 5.2051004982716048091082896972654894e-184Q, 6.5869897226619604622569213699928695e-190Q, 5.4113353348952773541650437440862393e-196Q, 2.8465905848751190783736476660662487e-202Q, 9.4537279066777943316631814110533656e-209Q, 1.9534309746768722817386272635097574e-215Q, 2.4738194858760899904808482337660157e-222Q, 1.8904418888763632604075097028006049e-229Q, 8.5786751348486017374921228218675884e-237Q, 2.273808044980070953576133599904938e-244Q, 3.4605977152655857787870383972443829e-252Q, 2.9714191752129370394435745626764138e-260Q, 1.4135174726041132417612082341294276e-268Q, 3.6561278873359158194812461400887331e-277Q, 5.0434133108713706852575809257966216e-286Q, 3.6370069739853174907232860458211075e-295Q, 1.3431929449017207014618114298104235e-304Q, 2.4870346219017639693976883745872467e-314Q, 2.2586869384020337841470045671934569e-324Q, 9.8364979621195023902799369645695391e-335Q, 2.0067948188168259586571751290590148e-345Q, 1.8723608312818058521936506625581294e-356Q, 7.7931764302539272294837164978121943e-368Q, 1.4104242105367751421756306559719189e-379Q, 1.0809707526373134180302026203191734e-391Q, 3.413982422671325929147843223533059e-404Q, 4.3198627266120931546631293969877099e-417Q, 2.1273069601360357294162339387648629e-430Q, 3.9566885872209056027370422510981332e-444Q, 2.6949582298096968115536767242364878e-458Q, 6.5109076931010576854739908270299986e-473Q, 5.3989788614177770156022874990436434e-488Q, 1.4853198669149487016912694923346188e-503Q, 1.3090503251521584428930352496581121e-519Q, 3.5647376837004158357511297570921346e-536Q, 2.8896313028497521005627356654844694e-553Q, 6.7095854399465738026181868874992309e-571Q, 4.2889584337257558657454644184109877e-589Q, 7.2448311439514538923291336946008664e-608Q, 3.1001155749336353171177254469615123e-627Q, 3.2171491615676533889663223546563542e-647Q, 7.7406570695117283869992601856060778e-668Q, 4.1223744692343859730646543586548491e-689Q, 4.6322294010052851367822679106298139e-711Q, 1.045335659900684312871171221243375e-733Q, 4.5020856241223108492475174506019875e-757Q, 3.5109989183708310084991820318792658e-781Q, 4.6962204836998229475507359520919518e-806Q, 1.0187348472076847046857363810773889e-831Q, 3.3829163365599114939619605636207329e-858Q, 1.6201873291670617651278341356078551e-885Q, 1.052417929633641351707580294560769e-913Q, 8.7019793416937745490332209560882093e-943Q, 8.5790292111351760552267505428090183e-973Q, 9.4260553303166607465714226577573373e-1004Q, 1.0765751303003708312349678449534298e-1035Q, 1.1895201137046793178631747385045816e-1068Q, 1.180625613342395519601533530260917e-1102Q, 9.7508687447891759767569844552344533e-1138Q, 6.1928172812184124261845708258990883e-1174Q, 2.7879318459600816091819660853849943e-1211Q, 8.1797014327573742781475939847205791e-1250Q, 1.4341956738449380463933781088853353e-1289Q, 1.3742136648085548142182437043687567e-1330Q, 6.5614725919712462874880606553304539e-1373Q, 1.4193967474068871671305637677852339e-1416Q, 1.260966346895598155305400631354573e-1461Q, 4.1570767913697591845636153183534885e-1508Q, 4.5808704947181685782204593123998747e-1556Q, 1.5147204987797220696721543058866571e-1605Q, 1.3446319068528240765599796700835479e-1656Q, 2.8568551875090842255902159455984979e-1709Q, 1.290420656637587734011142529706619e-1763Q, 1.0965883695419688652509296670806449e-1819Q, 1.5454358117029190110066857968185965e-1877Q, 3.1713220921953507873149502965607843e-1937Q, 8.2852363729842743064713740610323336e-1999Q, 2.399312892921620764854020931161764e-2062Q, 6.6760404808499701150348715300290141e-2128Q, 1.5401518790175723653421594673899941e-2195Q, 2.5301735371980282112359832727046555e-2265Q, 2.5299480120770166824316075523796362e-2337Q, 1.3095313462113548374850512340351666e-2411Q, 2.9689243920149770430661749727744362e-2488Q, 2.4813830291085919140036931759581715e-2567Q, 6.3996445311970094107659804037592786e-2649Q, 4.239251909781851582408879081262827e-2733Q, 5.96853928335010438943934366176163e-2820Q, 1.4691168838129215779634944240558196e-2909Q, 5.1680311504236953911307750231831853e-3002Q, 2.1104104354704386557845763111476113e-3097Q, 8.0724923764466854235161222774323272e-3196Q, 2.3180110258875131728741090820512076e-3297Q, 3.9765565093255272984730390679523982e-3402Q, 3.2199629992549034490326426922965119e-3510Q, 9.6508721511747631002418659479792544e-3622Q, 8.3314890242471693358884470423786766e-3737Q, 1.5992989019877436694379940011582504e-3855Q, 5.2267357291195312826488098055916878e-3978Q, 2.2079407038664800796624257814276463e-4104Q, 9.0732856778373185742790691459304111e-4235Q, 2.7052547418764871675136904567273848e-4369Q, 4.3243575109951310431228833829026739e-4508Q, 2.7122975060794559909023695709869724e-4651Q, 4.8370932158845122662637153824534517e-4799Q, 1.7593494088080091822518592461420403e-4951Q, 9.2629313370158738463326647490577837e-5109Q, 4.9562745065321184922933588793911493e-5271Q, 1.8710195150434747289751408668152425e-5438Q, },
      { 1.5706077165382752220570950499753154Q, 1.5690996953516691278086267057102919Q, 1.5660882389174613671811758395264413Q, 1.5615824934918106190924913174771039Q, 1.555596114631660422166342990902623Q, 1.5481471912355573327293783013569808Q, 1.5392581453118818324345492520631963Q, 1.5289556083545807159292699900112433Q, 1.517270275405054680346497720954261Q, 1.5042367380636772129345718219774013Q, 1.4898932978832971219484878984168431Q, 1.4742817617280797281467109946343097Q, 1.4574472208125486663314082491076236Q, 1.4394378152464069775161779990628486Q, 1.4203044859996911837480782185431865Q, 1.4001007162694446513758901419788661Q, 1.3788822642731375176779450629832807Q, 1.3567068895156054674526777181888561Q, 1.333634074575756135382992612713127Q, 1.3097247444374397383128243464895219Q, 1.2850409853467272312421857577496357Q, 1.2596457651166706020800416735545885Q, 1.2336026567219467107844443624642869Q, 1.2069755669313082254213358903568052Q, 1.1798284716173337422807600081541025Q, 1.1522251592625474295137962600991148Q, 1.1242289840506032580653098282178496Q, 1.0959026297929722120123658782081807Q, 1.0673078857975038741099002717210955Q, 1.0385054356373923292061824367921921Q, 1.0095546596294298182250582016592233Q, 0.98051345168085502517972727278507908Q, 0.95143805101635270871239123838108163Q, 0.92238288915245514551412028761663168Q, 0.8934004523471959982711625120192134Q, 0.86454115961966899850156197132807295Q, 0.83585325630826713062996261999715574Q, 0.80738272301876303826323728461609798Q, 0.77917319970479798076341140529382576Q, 0.75126592452435685914062625329355162Q, 0.72369968702683029319553573992504406Q, 0.69651079514654688482101628454709857Q, 0.66973305541029099783625527361406845Q, 0.6433977657082528556344204308635143Q, 0.61753371992990889983731720244260268Q, 0.59216722372820694935702418127019Q, 0.5673221206467387178357679693601802Q, 0.54301982782484286720274321665461387Q, 0.51927938048424633084628868358974812Q, 0.49611748439731621394870318175376878Q, 0.47354857554061400457926695177211335Q, 0.45158488614754491438064885165412276Q, 0.43023651638978897817371690879635701Q, 0.4095115109381936343721381503891959Q, 0.38941593967921202782661222737925564Q, 0.36995398189211387219291724401952693Q, 0.35112801322442524717390619924264052Q, 0.33293869483774779538113623624821976Q, 0.31538506413267813525698287867505706Q, 0.29846462649944498394951741316395666Q, 0.28217344757959610491269401677566302Q, 0.26650624556313516141887797351312194Q, 0.25145648308451039819955104711453138Q, 0.23701645831941898915989673276556716Q, 0.22317739492218458776987874976238264Q, 0.20992953048020753663062039840873861Q, 0.19726220319743719884124330734112999Q, 0.18516393655277551537189585427079996Q, 0.17362252171163130317128688392273587Q, 0.16262509749938432420455409981591821Q, 0.15215822777419972032806536862862315Q, 0.14220797606340183123695433555896409Q, 0.13275997735244552637840297048350341Q, 0.12379950693841296100725547935434371Q, 0.11531154628093733739002418825877215Q, 0.10728084580255643428531503966925542Q, 0.099691984607788577667458210957830992Q, 0.092529427105778463900867988199890184Q, 0.085777576535268189772833000772228023Q, 0.079420825403008155143300459369002492Q, 0.073443602857638774779152196546779989Q, 0.067830419030657966460701735648315918Q, 0.062565906384455108312883097272720571Q, 0.057634858114654726990360882355019006Q, 0.053022263660287178654865336510427632Q, 0.048713341380701431454862729860209054Q, 0.044693568462765533522590231699541224Q, 0.040948708125867302497552393088112476Q, 0.037464834195628972796376547170838841Q, 0.034228353120175696600101652094083332Q, 0.031226023505331741095045556858942863Q, 0.028444973247334237420578959776412296Q, 0.025872714343617644485216127636544335Q, 0.023497155463988527331246316453605283Q, 0.021306612366126070465592025046447108Q, 0.019289816240845601467614148418736892Q, 0.017435920073977461259931130790681214Q, 0.015734503113059796501938486945359929Q, 0.014175573528330460201833138862417195Q, 0.012749569358731162695972946581614495Q, 0.011447357834799756252759767428888751Q, 0.010260233171410768904966921400699131Q, 0.0091799129243109016820036013086815458Q, 0.0081985330052611999146716526049152539Q, 0.0073086414513132480272928751787482011Q, 0.0065031910442825794569735904252798084Q, 0.0057755308768065491405505517951108403Q, 0.0051193969614537818669843222017295048Q, 0.0045289019791562891246666236654351803Q, 0.0039985242627335310486699760330762132Q, 0.0035230961104429863210055440392477764Q, 0.003097791523300820656719025216576771Q, 0.0027181134583502264180047688832185696Q, 0.00237988068810043824649673132220802Q, 0.0020792143540086661490040088456472881Q, 0.0018125242991288642651054139496748517Q, 0.0015764952619105560466714983901735372Q, 0.001368073009609703955236179563862531Q, 0.0011844504858902772190681005760231091Q, 0.0010230540429745410945605352201833147Q, 0.00088152982417297002262517617153839107Q, 0.00075773035782735921074324212296621353Q, 0.00064970141867429037384583446669345298Q, 0.00055566920742578558188343231541026958Q, 0.00047402789401816719609357067930453704Q, 0.00040332756454954092910822407611191225Q, 0.0003422626064629768809457896368129693Q, 0.00028966056108877174342801893880690422Q, 0.00024447146728689162442142144970348683Q, 0.00020575771467998818628501233394779612Q, 0.00017268441988592922811041420156552324Q, 0.00014451033429094586468260075495740334Q, 0.00012057928729056996115421473619828627Q, 0.0001003121646011242017105871905771618Q, 8.3199417240036480655327139421155229e-05Q, 6.8794093113496376994935071214960655e-05Q, 5.6705379853932877470011678973870605e-05Q, 4.6592644630494618702241456703037532e-05Q, 3.8159954120245760316298139014562893e-05Q, 3.1151055677448352880855786865941099e-05Q, 2.5344798968850110019503595997329621e-05Q, 2.0550975944934244592401576964828471e-05Q, 1.6606555976508335934243673648199596e-05Q, 1.3372292284532372637222480551805329e-05Q, 1.0729675406852340659025097863229453e-05Q, 8.5782093537079082475098159798479489e-06Q, 6.8329862774218506793384467861049682e-06Q, 5.4225358918240331874795406776236123e-06Q, 4.2869264939998226801201888531480146e-06Q, 3.3760952348055109687047948553034167e-06Q, 2.648386225404341059332347382668934e-06Q, 2.0692761257364670911197722058327534e-06Q, 1.6102680094507650586919500255804135e-06Q, 1.2479355121177451927952866165256362e-06Q, 9.6310052117659252623275411195037422e-07Q, 7.4012893490908614244527654085271701e-07Q, 5.6633028402050775174949474283673891e-07Q, 4.3144825587165380566889409128276242e-07Q, 3.2723037330517558940458575985214918e-07Q, 2.4706624511249700700814105972385083e-07Q, 1.8568491370025132363780817403060483e-07Q, 1.3890286996035952957423247999266266e-07Q, 1.0341528040745607124994759911720107e-07Q, 7.6623873974817584699067130031657306e-08Q, 5.6495763871670621475281493521314709e-08Q, 4.1448233558505383939707940899336768e-08Q, 3.0255196464899467188973440250947218e-08Q, 2.1971648917089349870913690980235634e-08Q, 1.5872978090968232625950436800137184e-08Q, 1.1406465554850482080861078708713322e-08Q, 8.1527464828576537674941124925699748e-09Q, 5.7953495730966064129686023678242569e-09Q, 4.0967579139685198004841429173893772e-09Q, 2.8797013464471104655049488102491186e-09Q, 2.0126210218867172874496187582569718e-09Q, 1.3984414312565442025515753185221259e-09Q, 9.6594851858099300536090988590771494e-10Q, 6.6320863470162099803938291638680053e-10Q, 4.5257576104153821951813027371570715e-10Q, 3.0692702078723328679138233160912672e-10Q, 2.0684203539029217544821684017571545e-10Q, 1.3850287525834145108955800004991698e-10Q, 9.214056422651888408320635035510389e-11Q, 6.0893387064380670508776147108848386e-11Q, 3.9973389519930269589286591598258413e-11Q, 2.6061960502805293348144397281679947e-11Q, 1.6874519343915025228826792012273533e-11Q, 1.0849161834337120075418713193749676e-11Q, 6.925528015268138234276418201069421e-12Q, 4.3888651899779304012906781620222308e-12Q, 2.7608587671398283608193689290767215e-12Q, 1.7237644036042717645352410466671951e-12Q, 1.0680750436541459501472279340038532e-12Q, 6.5669443496573770247087892915106603e-13Q, 4.0059853799532432743143892523313549e-13Q, 2.4242966045442409054601213028173084e-13Q, 1.455249915777183791487150796402817e-13Q, 8.6638127254357861703205617203908732e-14Q, 5.1149749011237035858347608326433163e-14Q, 2.994217759503148409824465166785657e-14Q, 1.7376816946074293584520159998670905e-14Q, 9.9964240098994293054396937904035808e-15Q, 5.6996266660577917745539527915605509e-15Q, 3.2204325132709429326443049307963174e-15Q, 1.8029589638948994224704223595333813e-15Q, 9.9999573441511328003764710559432247e-16Q, 5.4939783973500551313304111867037353e-16Q, 2.9894208856836080833460170608635085e-16Q, 1.610765424389333897595412933702905e-16Q, 8.5932097482708020320234894915603507e-17Q, 4.5382468267138486711718165281416612e-17Q, 2.3722531670914086186505578503296188e-17Q, 1.2271671669957684251768746309727359e-17Q, 6.2812290485539754593993025086049784e-18Q, 3.1806147137287576603303714266387884e-18Q, 1.5930492574791968327078642420897427e-18Q, 7.8908551593455841363543904697547233e-19Q, 3.8647331030140734169770289169399394e-19Q, 1.8712773299709086118483416095592492e-19Q, 8.9557394552313902528032623648079256e-20Q, 4.2357428519456376102962467454513856e-20Q, 1.979436201534175012643985224780424e-20Q, 9.1380785584698727034273276491713887e-21Q, 4.1666411584773049229385926804715087e-21Q, 1.8760750552668470771305003442308049e-21Q, 8.3399019486525331443195597764274931e-22Q, 3.6595752362436730932932611770341911e-22Q, 1.5847852183325978194205652538004105e-22Q, 6.7715756944343963844212791596730701e-23Q, 2.8542817079371140817224171432389793e-23Q, 1.1865838580947568695752893868758282e-23Q, 4.8640699357080450262013571667032291e-24Q, 1.9656434192469852200073765210097077e-24Q, 7.8291656246958620676899939812206199e-25Q, 3.0727892288172702707668669968129923e-25Q, 1.1881076147213762784564203492918474e-25Q, 4.5246197487634934114352468991264689e-26Q, 1.6967101868478854591740026523544294e-26Q, 6.2636410032291183199621894495108545e-27Q, 2.275790792857396714851032797332432e-27Q, 8.1360777159044314206100175821194793e-28Q, 2.8613065492953910272350038532776635e-28Q, 9.8961841969437875600155257339935786e-29Q, 3.3652008931641398734060087635568974e-29Q, 1.124807054607635533524732113721643e-29Q, 3.6944604327139334398530256817969063e-30Q, 1.1920933013465364813127655880555969e-30Q, 3.7777578760183197857216889284069537e-31Q, 1.1754363786778472289935929233399097e-31Q, 3.5898790778284615893833448994196406e-32Q, 1.0758426862142799782307176268359752e-32Q, 3.1628351259465958616106447958915727e-33Q, 9.1186741890740659476367230179943904e-34Q, 2.5773931684416249884030668079887807e-34Q, 7.1398295043464040965283248909128753e-35Q, 1.9378289210199819221125431141075043e-35Q, 5.1513791013790083352767878745680313e-36Q, 1.3408183263237239554032545670003467e-36Q, 3.4159407851320474430217223083201431e-37Q, 8.5152627413375142768339982799164144e-38Q, 2.076271482369099670696465965718329e-38Q, 4.9501378383448158752628326041586328e-39Q, 1.1535696769569322062307767078682896e-39Q, 2.6266829648536609656499385130095636e-40Q, 5.8418474765546354958505600321922284e-41Q, 1.2685583522817839163209325300191513e-41Q, 2.6885912245101334092484585567288326e-42Q, 5.5593886486694506967808090446058169e-43Q, 1.1211105786168708837628837423551506e-43Q, 2.204030437173812137094585242452846e-44Q, 4.2224091761364250309210368326791673e-45Q, 7.879520062360877249284818876749499e-46Q, 1.4317140234575381985434267597963095e-46Q, 2.5319048034172799761988113239574673e-47Q, 4.3559981958200842427093791079007571e-48Q, 7.2876743960081410061189361036751606e-49Q, 1.1851134957779509548750104934682377e-49Q, 1.872433458377359457763352131002218e-50Q, 2.8729694149829129971079440142039839e-51Q, 4.2789120660247665069210975132651194e-52Q, 6.1831517263855101991333477522333856e-53Q, 8.664706532064762178060555848235158e-54Q, 1.1769423732336786099340369561810161e-54Q, 1.5488187383481079319128553087281025e-55Q, 1.9736646088073419346394015708589866e-56Q, 2.4341838396213207647077873905717686e-57Q, 2.9041369422835327495394798923780893e-58Q, 3.3499429920119329938613749830054234e-59Q, 3.7340806058053956442103094318030179e-60Q, 4.0199589180457423324670244391460705e-61Q, 4.1774687856674289017944418799754548e-62Q, 4.1880972367088277279311490203747192e-63Q, 4.0484306973548957998328137903090132e-64Q, 3.77114883236106507191241601008861e-65Q, 3.3831738882583366253452262404438761e-66Q, 2.9213348345532154319786866459214566e-67Q, 2.4265171108768020980951563482987534e-68Q, 1.9376034615932002776264780070086131e-69Q, 1.4864705425001770514685771802938584e-70Q, 1.0949232255990064493457806009226565e-71Q, 7.7387134447881401018887497774386554e-73Q, 5.2448008205426118383234631723504239e-74Q, 3.4062562927830356374440099107130253e-75Q, 2.1184692439258592242177714552745419e-76Q, 1.2608614434809477030102983673195673e-77Q, 7.1764903126208755695723110547276813e-79Q, 3.9034697656266028736188479360578122e-80Q, 2.0275573404310544862715347770507338e-81Q, 1.004994816430593904290660376649901e-82Q, 4.7500986471514075093717143999280406e-84Q, 2.1392636099460832836078416112710222e-85Q, 9.173149292867278730161085643245862e-87Q, 3.7422217329627927169047643424111024e-88Q, 1.4512980036440354977046772500061822e-89Q, 5.3463017574991130746397399989936882e-91Q, 1.8692545071988721039716330965097833e-92Q, 6.1978958931273853497246435906829052e-94Q, 1.9472339695524234456610961876127122e-95Q, 5.7919052872513171909291273369047611e-97Q, 1.6295956267419577729908767823766263e-98Q, 4.3332432642363261903550403983227406e-100Q, 1.0880149430086354606207846327564807e-101Q, 2.5772282627352113781815039093907765e-103Q, 5.7539838006451121430858484531452556e-105Q, 1.2096990017248315153784188752260699e-106Q, 2.3925872664345045807315831521649824e-108Q, 4.4475659282644875495932738704595289e-110Q, 7.7627738422265479186699876924736519e-112Q, 1.2709267303083863208341290697181042e-113Q, 1.9498242131608832039698501560236386e-115Q, 2.8002525531974060834525812862103801e-117Q, 3.7607474856227547424556497710309557e-119Q, 4.7181060260584103957700726731938295e-121Q, 5.5234683214250331048701341097289187e-123Q, 6.0274476904458633271417170323324512e-125Q, 6.1242336828836773956273877493935834e-127Q, 5.7873336327745621779130244175524495e-129Q, 5.080632710884007461562439620052701e-131Q, 4.1387180173608798644427799558698992e-133Q, 3.1247188175223382722031520705749413e-135Q, 2.1839031164024077549981710942515192e-137Q, 1.4112539862126523413412548851519319e-139Q, 8.4215132945549247707528420294855683e-142Q, 4.6349339497376890248654482771898709e-144Q, 2.3496985464521993504910835710336684e-146Q, 1.095806710681902573252914390685236e-148Q, 4.6950275371271587533706139425003387e-151Q, 1.8456310819897755187059215917442304e-153Q, 6.6476058774188256202611396416535206e-156Q, 2.1907913883958482863705726011811944e-158Q, 6.5969704613629998580324088255164448e-161Q, 1.8125024222344232769814833021310009e-163Q, 4.5370781893964839845073953864812105e-166Q, 1.0332388835710739936372904447767308e-168Q, 2.1374954497695898284726118552680548e-171Q, 4.0108149042055622979938927508096981e-174Q, 6.8157812455297175262972900652817266e-177Q, 1.0473127652676199964496156413213892e-179Q, 1.4528685940321876156376494017441343e-182Q, 1.8166282837173462776489744689123018e-185Q, 2.0440202882824856548036164467448088e-188Q, 2.0661513859552458565266153108297534e-191Q, 1.8731127932057798663856916994067048e-194Q, 1.5203587637183862829799380406495058e-197Q, 1.1029412423875246289335781867943994e-200Q, 7.138626970990275795307217364262657e-204Q, 4.1148385080761107172311325770404465e-207Q, 2.1085028345530812052701929614749437e-210Q, 9.586804728597153811540457301738394e-214Q, 3.8604168930466380230648935566836905e-217Q, 1.3741198913182849308419643845423577e-220Q, 4.3152058575378643032664917818412533e-224Q, 1.19318553486394227968739928824324e-227Q, 2.8991695266404489121362655770787829e-231Q, 6.1775221006993094876500875992969792e-235Q, 1.1519457912295313262297054573241955e-238Q, 1.8759214343015253296548435854195451e-242Q, 2.6621687700905798726983482845704724e-246Q, 3.2851388833240439850794784627378447e-250Q, 3.5173302898760059862233242135090423e-254Q, 3.260189457452227113017033626415114e-258Q, 2.6100961489800200467379180863184362e-262Q, 1.8007454797962902444457099319194981e-266Q, 1.0681019906266894643271137718146545e-270Q, 5.4338051160125203325306044546291973e-275Q, 2.3652466547636417935201205492175566e-279Q, 8.7874613708003418754944028581674092e-284Q, 2.7795968403545195493312606588081939e-288Q, 7.4667445765219841782970695464816343e-293Q, 1.6990035121087781757668989612218036e-297Q, 3.2661456244114182842789617411036552e-302Q, 5.2905644824269341726292739407836284e-307Q, 7.2014912174357894815487914007866659e-312Q, 8.2149916381513794689686855840667286e-317Q, 7.8315488217791735530193073164613925e-322Q, 6.221807701348355377240172152359918e-327Q, 4.1074011155154758472047101692258134e-332Q, 2.2466366589127674529456245569217313e-337Q, 1.0151434061771573997492847262735324e-342Q, 3.7778453114026807637246195807729534e-348Q, 1.1543990703305213109241662519678786e-353Q, 2.8874514917596574178755044320075352e-359Q, 5.8931980361396154630354121875276487e-365Q, 9.7830460697139640980628824283457061e-371Q, 1.3166522887017193888073217487220663e-376Q, 1.4318812372702150835656599374620546e-382Q, 1.2540768871672304974301195995718396e-388Q, 8.8153996483879828169726259431129063e-395Q, 4.9562889772003524717252843516553902e-401Q, 2.2209583723340796264599763080008776e-407Q, 7.903905845967360394872219425003336e-414Q, 2.2257919152191142215565812315993462e-420Q, 4.9416038684875140142672214621735078e-427Q, 8.6171916788075114925145119819509171e-434Q, 1.1757806822258262957023704296248014e-440Q, 1.2504660464052680899003468814266606e-447Q, 1.0325197258632347378121850864959173e-454Q, 6.5928728293837187402797552969580767e-462Q, 3.2422215378060561710679179733872247e-469Q, 1.222971654709343975246423698805888e-476Q, 3.523560719191273512100160441884497e-484Q, 7.7214021325433144875052949047234787e-492Q, 1.2814096433117550022825846412075423e-499Q, 1.6034524983169066795932277083952198e-507Q, 1.506154257333325578480885384144555e-515Q, 1.0572229793982283970011093126796893e-523Q, 5.5202045762026741431740525809554222e-532Q, 2.1340837910128019442948018148493991e-540Q, 6.0796683172016911238300290124661526e-549Q, 1.2702010542787853060770224333562558e-557Q, 1.9367268433132755704840764854736682e-566Q, 2.1444373126935426310796432391730652e-575Q, 1.7156127051237753338771752429597998e-584Q, 9.866507362005302302229219896627392e-594Q, 4.0577781777778825723169578502780851e-603Q, 1.1871364801794013747840938511563031e-612Q, 2.4573736732127578624297116105185871e-622Q, 3.5795957466771145363085369419550411e-632Q, 3.6491000343716609306595204093204503e-642Q, 2.5887224173643746039322073618863653e-652Q, 1.2707309714711704379079743917253552e-662Q, 4.2911292837382087644451178350840055e-673Q, 9.9101724991114880676267115929463864e-684Q, 1.5559081312551687762049592042215559e-694Q, 1.6505944447381619620675148844917786e-705Q, 1.1758976044388560589646808736426135e-716Q, 5.5904673599123524828590221558073227e-728Q, 1.762422841281411916279811438897522e-739Q, 3.660545736359787919552147533680784e-751Q, 4.9762407942794836638678732794521407e-763Q, 4.3982297935392846258826361101417004e-775Q, 2.5103306493196863847807186768995234e-787Q, 9.1890276778950746130428977933070467e-800Q, 2.1421794336828190981917187030902949e-812Q, 3.1579407773552118084939940851249037e-825Q, 2.9226685587591509131252404660323457e-838Q, 1.6857720383594173922437511994184956e-851Q, 6.0148932059792413237874599630717323e-865Q, 1.3175950977896871810836083428540193e-878Q, 1.7584274071402706626129462062711265e-892Q, 1.4186205574627811155197950438077455e-906Q, 6.863804756553040536363703712029114e-921Q, 1.9757126782016829801994589969078639e-935Q, 3.3557616466388148224311943077895109e-950Q, 3.3354866661158784873247273739595471e-965Q, 1.9238163515428741014100444007200552e-980Q, 6.3838419214869081664291315367530963e-996Q, 1.208186393746817780221157077221556e-1011Q, 1.2926433460770675227136395965601267e-1027Q, 7.7484641126890628613399212918871834e-1044Q, 2.578590762872741885363873428172601e-1060Q, 4.720135411440959292309368252688105e-1077Q, 4.7080786123051904219070581932647786e-1094Q, 2.5345319096404707885383043943244978e-1111Q, 7.2928761242789730396354678181188802e-1129Q, 1.1106128952895418090598971812669904e-1146Q, 8.8621216538366976598099979452808551e-1165Q, 3.6677828388640988980975786646419682e-1183Q, 7.7923888084439739305704880307980387e-1202Q, 8.4096550976573708334084140027496576e-1221Q, 4.5613561814429013831962003215267279e-1240Q, 1.2300215778004431684442502009102409e-1259Q, 1.6310040159866857782581696492106591e-1279Q, 1.0516379174444742948762886500817157e-1299Q, 3.2599823473651125257811218258666689e-1320Q, 4.8027868480449172060158102541122388e-1341Q, 3.3236309884819155458562664594667437e-1362Q, 1.0675935976034168796912624171598076e-1383Q, 1.5726195400977448490722891999643084e-1405Q, 1.0493832555753684651126599489805473e-1427Q, 3.1327201084154872489458088843708842e-1450Q, 4.1312837255709912970140480142041816e-1473Q, 2.3759469769680296617336666937339053e-1496Q, 5.8816760470052203616229940275916193e-1520Q, 6.1846204357986408200352274197037853e-1544Q, 2.7253154006156158167192213817170292e-1568Q, 4.964370850871644112480158952518382e-1593Q, 3.6864952720610418491857518517405997e-1618Q, 1.1003435724966953469136035841615193e-1643Q, 1.3012915572536198178305157209959287e-1669Q, 6.0092590718277568469858434192698668e-1696Q, 1.0676671777328477051443719591612721e-1722Q, 7.1893041788265042697820383343649581e-1750Q, 1.8069174401475778125395048319028868e-1777Q, 1.6689765331128672749329764129278041e-1805Q, 5.5766895369980043818737558535999006e-1834Q, 6.6338104205654511592967377535571654e-1863Q, 2.7640539994999072399732281044814252e-1892Q, 3.9678218215367472586686997493459426e-1922Q, 1.9297110274588854079208153981267479e-1952Q, 3.125823255660491143824024380405163e-1983Q, 1.6574847870654916983076674690989236e-2014Q, 2.8268986732520512350429266473306993e-2046Q, 1.5233117185863507338781156113800939e-2078Q, 2.546861334196453858326476740489303e-2111Q, 1.2970473455790653045919178951673388e-2144Q, 1.9747419969447016216735762205746422e-2178Q, 8.8188278044353709778558661887540083e-2213Q, 1.1331026308914886857015956789021835e-2247Q, 4.1073843396377054383734821718609159e-2283Q, 4.1176061068991316842526396776090883e-2319Q, 1.1187086154165438557157295454514512e-2355Q, 8.0696152044407520449588862080186105e-2393Q, 1.5135007846133660639825213215103939e-2430Q, 7.2259333243684066847653432848351411e-2469Q, 8.5946687461833096509693248289938412e-2508Q, 2.4916284852956646502671815296350685e-2547Q, 1.7218755528601187283427831366854425e-2587Q, 2.7731789802471774097405514299103397e-2628Q, 1.0173036737175759305843452255817661e-2669Q, 8.3042791798168477802920353914502218e-2712Q, 1.473175854535708068279125062330391e-2754Q, 5.5445898944436653916624336507572418e-2798Q, 4.320588676830861227044616106347197e-2842Q, 6.7999319106199079525777026656783353e-2887Q, 2.1077263780365503485840282493709679e-2932Q, 1.2541789874677469018547003272238725e-2978Q, 1.3958959110384407791740617210399902e-3025Q, 2.8302814609077924874798718558506843e-3073Q, 1.0177558252358910503169620579616733e-3121Q, 6.316345161489586703263200702377878e-3171Q, 6.5808353794152592529153513818570413e-3221Q, 1.1191402820754794725611968922105955e-3271Q, 3.0191144446114914489317417615052391e-3323Q, 1.2550879768384736567252096901362895e-3375Q, 7.8068891083719869927416696059040226e-3429Q, 7.0517902304991510532607166162881123e-3483Q, 8.9730892510839231589218006993975962e-3538Q, 1.5595586152851619317721078977043233e-3593Q, 3.5880900047081973177436521469002167e-3650Q, 1.0585155878906962124317498469363852e-3707Q, 3.8766455756851727310799307162918377e-3766Q, 1.7055766393620807246392140051588691e-3825Q, 8.7186526945824113614692955827617228e-3886Q, 5.0057478847913665022916271652366648e-3947Q, 3.118721903551705673676508466953291e-4009Q, 2.0360271187838855169643637345546437e-4072Q, 1.344193072571113944443469925533764e-4136Q, 8.6564521926545774914724471102499854e-4202Q, 5.2420435234912004640647252129478902e-4268Q, 2.8759098307799421718470860096954611e-4335Q, 1.3763877452472741048491095385711433e-4403Q, 5.5298776937738818735087266649244396e-4473Q, 1.7937216962986389293716315095996859e-4543Q, 4.5149004338871753808860898565339903e-4615Q, 8.4705695199542943533046216376760939e-4688Q, 1.1370794062105632021349671352107651e-4761Q, 1.0477206804639390072354388690482908e-4836Q, 6.3526587059143165049479765478742544e-4913Q, 2.4283486680552345908529800038916585e-4990Q, 5.6028531999416699789095738832933157e-5069Q, 7.4653403674710397422097751397913229e-5149Q, 5.4919841992020489039553213694177019e-5230Q, 2.1312688707736268785590779947753119e-5312Q, 4.1653791598980828217930742787672032e-5396Q, 3.9114639992058990826656399856975019e-5481Q, },
   };
   m_first_complements = {
      1, 0, 1, 1, 3, 5, 11, 22,
   };
#if !defined(BOOST_MATH_NO_ATOMIC_INT) && defined(BOOST_HAS_THREADS)
   m_committed_refinements = static_cast<boost::math::detail::atomic_unsigned_integer_type>(m_abscissas.size() - 1);
#else
   m_committed_refinements = m_abscissas.size() - 1;
#endif

   if (m_max_refinements >= m_abscissas.size())
   {
      m_abscissas.resize(m_max_refinements + 1);
      m_weights.resize(m_max_refinements + 1);
      m_first_complements.resize(m_max_refinements + 1);
   }
   else
   {
      m_max_refinements = m_abscissas.size() - 1;
   }
   m_t_max = static_cast<Real>(m_inital_row_length);
   m_t_crossover = t_from_abscissa_complement(Real(0.5));

   prune_to_min_complement(min_complement);
}

#endif // BOOST_HAS_FLOAT128

template<class Real, class Policy>
void tanh_sinh_detail<Real, Policy>::prune_to_min_complement(const Real& m)
{
   //
   // If our tables were constructed from pre-computed data, then they will have more values stored than we can ever use,
   // and although the table size at this stage won't be too large, if we calculate down to m_max_levels then they will
   // grow by a huge amount - doubling in size at each step - so lets prune them down, removing values which will never
   // be used:
   //
   if (m > tools::min_value<Real>() * 4)
   {
      for (unsigned row = 0; (row < m_abscissas.size()) && m_abscissas[row].size(); ++row)
      {
         typename std::vector<Real>::iterator pos = std::lower_bound(m_abscissas[row].begin(), m_abscissas[row].end(), m, [](const Real& a, const Real& b) { using std::fabs; return fabs(a) > fabs(b); });
         if (pos != m_abscissas[row].end())
         {
            m_abscissas[row].erase(pos, m_abscissas[row].end());
            m_weights[row].erase(m_weights[row].begin() + m_abscissas[row].size(), m_weights[row].end());
         }
      }
   }
}

}}}}  // namespaces

#endif
