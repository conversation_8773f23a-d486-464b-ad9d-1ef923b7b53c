<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Design Rationale</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../process.html" title="Chapter 28. Boost.Process">
<link rel="prev" href="tutorial.html" title="Tutorial">
<link rel="next" href="extend.html" title="Extensions">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="tutorial.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../process.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="extend.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_process.design"></a><a class="link" href="design.html" title="Design Rationale">Design Rationale</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="design.html#boost_process.design.scope">Scope</a></span></dt>
<dt><span class="section"><a href="design.html#boost_process.design.interface_style">Interface Style</a></span></dt>
<dt><span class="section"><a href="design.html#boost_process.design.arg_cmd_style">Arguments/Command
      Style</a></span></dt>
<dt><span class="section"><a href="design.html#boost_process.design.plat_ext">Extensions</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_process.design.scope"></a><a class="link" href="design.html#boost_process.design.scope" title="Scope">Scope</a>
</h3></div></div></div>
<p>
        This library is meant to give a wrapper around the different OS-specific
        methods to launch processes. Its aim is to provide all functionality that
        is available on those systems and allow the user to do all related things,
        which require using the OS APIs.
      </p>
<p>
        <span class="bold"><strong>This library does not try to provide a full library
        for everything process related.</strong></span> In many discussions the proposal
        was made to build boost.process into a DSEL <a href="#ftn.boost_process.design.scope.f0" class="footnote" name="boost_process.design.scope.f0"><sup class="footnote">[28]</sup></a> of some sort. This is not the goal, it rather provides the facilities
        to build such a DSEL-library on top of it. Therefore the library also does
        <span class="bold"><strong>not</strong></span> force any particular use (such as only
        asynchronous communication) on its user. It rather could be integrated with
        such a library.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_process.design.interface_style"></a><a class="link" href="design.html#boost_process.design.interface_style" title="Interface Style">Interface Style</a>
</h3></div></div></div>
<p>
        Boost.Process does use a very particular style when constructing a process.
        This is because a process holds many properties, which are not members of
        the actual child class. Those properties are in many cases not accessible
        by the father process, for example when using environments. Here the child
        process can modify its own environment, but there is no way for the father
        process to know. That means, that a child process has properties that cannot
        be accessed in C++.
      </p>
<p>
        This now leads to the two styles supported and mixed by this library. Overloading
        and properties. Consider that you may want to launch a process passing a
        number of arguments. This is supported in both styles, and would look like
        this:
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">system</span><span class="special">(</span><span class="string">"gcc"</span><span class="special">,</span> <span class="string">"--version"</span><span class="special">);</span> <span class="comment">//overloading</span>
<span class="identifier">system</span><span class="special">(</span><span class="string">"gcc"</span><span class="special">,</span> <span class="identifier">args</span><span class="special">={</span><span class="string">"--version"</span><span class="special">});</span> <span class="comment">//property style.</span>
</pre>
<p>
      </p>
<p>
        Both styles can also be mixed in some cases.
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">system</span><span class="special">(</span><span class="string">"gcc"</span><span class="special">,</span> <span class="string">"-c"</span><span class="special">,</span> <span class="identifier">args</span><span class="special">+={</span><span class="string">"main.cpp"</span><span class="special">});</span>
</pre>
<p>
      </p>
<p>
        In the following section the available styles will be described. Note that
        the overload style is implemented via type traits, so the types will be listed.
      </p>
<div class="caution"><table border="0" summary="Caution">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Caution]" src="../../../doc/src/images/caution.png"></td>
<th align="left">Caution</th>
</tr>
<tr><td align="left" valign="top"><p>
          There is no guarantee in which order the arguments will be applied! There
          is however a guarantee for arguments belonging together, i.e. the string
          argument and the args property will be evaluated in the order given.
        </p></td></tr>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_process.design.arg_cmd_style"></a><a class="link" href="design.html#boost_process.design.arg_cmd_style" title="Arguments/Command Style">Arguments/Command
      Style</a>
</h3></div></div></div>
<p>
        When passing arguments to the process, two styles are provided, the cmd-style
        and the exe-/args-style.
      </p>
<p>
        The cmd style will interpret the string as a sequence of the exe and arguments
        and parse them as such, while the exe-/args-style will interpret each string
        as an argument.
      </p>
<div class="table">
<a name="boost_process.design.arg_cmd_style.id"></a><p class="title"><b>Table 28.1. Cmd vs Exe/Args</b></p>
<div class="table-contents"><table class="table" summary="Cmd vs Exe/Args">
<colgroup>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  String
                </p>
              </th>
<th>
                <p>
                  Cmd
                </p>
              </th>
<th>
                <p>
                  Exe/Args
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  "gcc --version"
                </p>
              </td>
<td>
                <p>
                  {"gcc", "--version"}
                </p>
              </td>
<td>
                <p>
                  {"\"gcc --version\""}
                </p>
              </td>
</tr></tbody>
</table></div>
</div>
<br class="table-break"><p>
        When using the overloading variant, a single string will result in a cmd
        interpretation, several strings will yield a exe-args interpretation. Both
        versions can be set explicitly:
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">system</span><span class="special">(</span><span class="string">"grep -c false /etc/passwd"</span><span class="special">);</span> <span class="comment">//cmd style</span>
<span class="identifier">system</span><span class="special">(</span><span class="string">"grep"</span><span class="special">,</span> <span class="string">"-c"</span><span class="special">,</span> <span class="string">"false"</span><span class="special">,</span> <span class="string">"/etc/passwd"</span><span class="special">);</span> <span class="comment">//exe-/args-</span>

<span class="identifier">system</span><span class="special">(</span><span class="identifier">cmd</span><span class="special">=</span><span class="string">"grep -c false /etc/passwd"</span><span class="special">);</span> <span class="comment">//cmd style</span>
<span class="identifier">system</span><span class="special">(</span><span class="identifier">exe</span><span class="special">=</span><span class="string">"grep"</span><span class="special">,</span> <span class="identifier">args</span><span class="special">={</span><span class="string">"-c"</span><span class="special">,</span> <span class="string">"false"</span><span class="special">,</span> <span class="string">"/etc/passwd"</span><span class="special">});</span> <span class="comment">//exe-/args-</span>
</pre>
<p>
      </p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
          If a '"' sign is used in the argument style, it will be passed as
          part of the argument. If the same effect is wanted with the cmd syntax,
          it ought to be escaped, i.e. '\"'.
        </p></td></tr>
</table></div>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
          The <code class="computeroutput"><span class="identifier">PATH</span></code> variable will
          automatically be searched in the command style, but the one of the launching
          process, not the one passed to the child process.
        </p></td></tr>
</table></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_process.design.plat_ext"></a><a class="link" href="design.html#boost_process.design.plat_ext" title="Extensions">Extensions</a>
</h3></div></div></div>
<p>
        The simplest form to extend functionality is to provide another handler,
        which will be called on the respective events on process launching. The names
        are:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">process</span><span class="special">::</span><span class="identifier">on_setup</span></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">process</span><span class="special">::</span><span class="identifier">on_error</span></code>
          </li>
<li class="listitem">
            <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">process</span><span class="special">::</span><span class="identifier">on_success</span></code>
          </li>
</ul></div>
<p>
        As an example:
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">child</span> <span class="identifier">c</span><span class="special">(</span><span class="string">"ls"</span><span class="special">,</span> <span class="identifier">on_setup</span><span class="special">([](){</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"On Setup"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;});</span>
</pre>
<p>
      </p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
          On posix all those callbacks will be handled by this process, not the created
          one. This is different for the posix extensions, which can be executed
          on the forked process.
        </p></td></tr>
</table></div>
</div>
<div class="footnotes">
<br><hr style="width:100; text-align:left;margin-left: 0">
<div id="ftn.boost_process.design.scope.f0" class="footnote"><p><a href="#boost_process.design.scope.f0" class="para"><sup class="para">[28] </sup></a>
          Domain Specific Embedded Language
        </p></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2006-2012 Julio M. Merino Vidal, Ilya Sokolov,
      Felipe Tanus, Jeff Flinn, Boris Schaeling<br>Copyright © 2016 Klemens D. Morgenstern<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="tutorial.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../process.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="extend.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
