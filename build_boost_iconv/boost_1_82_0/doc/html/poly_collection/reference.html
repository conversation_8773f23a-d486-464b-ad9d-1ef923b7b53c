<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Reference</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../poly_collection.html" title="Chapter 27. Boost.PolyCollection">
<link rel="prev" href="performance.html" title="Performance">
<link rel="next" href="future_work.html" title="Future work">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="performance.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../poly_collection.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="future_work.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="poly_collection.reference"></a><a class="link" href="reference.html" title="Reference">Reference</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#poly_collection.reference.polymorphism_models">Polymorphism
      models</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.polymorphic_containers">Polymorphic
      containers</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_exc">Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/exception.hpp"</span></code>
      synopsis</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_bas">Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/base_collection_fwd.hpp"</span></code>
      synopsis</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_ba0">Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/base_collection.hpp"</span></code>
      synopsis</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_fun">Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/function_collection_fwd.hpp"</span></code>
      synopsis</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0">Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/function_collection.hpp"</span></code>
      synopsis</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_any">Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/any_collection_fwd.hpp"</span></code>
      synopsis</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_an0">Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/any_collection.hpp"</span></code>
      synopsis</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_alg">Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/algorithm.hpp"</span></code>
      synopsis</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="poly_collection.reference.polymorphism_models"></a><a class="link" href="reference.html#poly_collection.reference.polymorphism_models" title="Polymorphism models">Polymorphism
      models</a>
</h3></div></div></div>
<p>
        The key aspect of dynamic polymorphism is the ability for a value of type
        <code class="computeroutput"><span class="identifier">T</span></code> to internally use another
        value of a possibily different type <code class="computeroutput"><span class="identifier">U</span></code>
        for the implementation of a given interface. Base/derived polymorphism is
        the classic model of dynamic polymorphism in C++, but not the only possible
        one.
      </p>
<p>
        Formally, a <span class="emphasis"><em>polymorphism model</em></span> is defined by
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            A family <span class="bold"><strong>Interface</strong></span> of permissible interface
            types and, for each <code class="computeroutput"><span class="identifier">I</span></code>
            ∈ <span class="bold"><strong>Interface</strong></span>, the family <span class="bold"><strong>Implementation</strong></span>(<code class="computeroutput"><span class="identifier">I</span></code>)
            of types satisfying <code class="computeroutput"><span class="identifier">I</span></code>.
          </li>
<li class="listitem">
            For a given interface type <code class="computeroutput"><span class="identifier">I</span></code>,
            an operation <span class="bold"><strong>subobject</strong></span>(<code class="computeroutput"><span class="identifier">x</span></code>) that maps each value of an implementation
            type to its internally used value <code class="computeroutput"><span class="identifier">y</span></code>
            of a possibly different implementation type <a href="#ftn.poly_collection.reference.polymorphism_models.f0" class="footnote" name="poly_collection.reference.polymorphism_models.f0"><sup class="footnote">[21]</sup></a>.
          </li>
</ul></div>
<p>
        Static polymorphism is the trivial case where <span class="bold"><strong>subobject</strong></span>(<code class="computeroutput"><span class="identifier">x</span></code>) = <code class="computeroutput"><span class="identifier">x</span></code>
        for all <code class="computeroutput"><span class="identifier">x</span></code>. Base/derived polymorphism
        is characterized by:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <span class="bold"><strong>Interface</strong></span> = { <code class="computeroutput"><span class="identifier">Base</span></code>
            : <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">is_polymorphic_v</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">&gt;</span></code>
            }.
          </li>
<li class="listitem">
            <span class="bold"><strong>Implementation</strong></span>(<code class="computeroutput"><span class="identifier">Base</span></code>)
            = { <code class="computeroutput"><span class="identifier">Derived</span></code> : <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">is_base_of_v</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Derived</span><span class="special">&gt;</span></code>
            }.
          </li>
<li class="listitem">
            <span class="bold"><strong>subobject</strong></span>(<code class="computeroutput"><span class="identifier">x</span></code>)
            = <code class="computeroutput"><span class="keyword">static_cast</span><span class="special">&lt;</span><span class="identifier">Derived</span><span class="special">&amp;&gt;(</span><span class="identifier">x</span><span class="special">)</span></code>
            with <code class="computeroutput"><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">x</span><span class="special">)==</span><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">Derived</span><span class="special">)</span></code>.
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="poly_collection.reference.polymorphic_containers"></a><a class="link" href="reference.html#poly_collection.reference.polymorphic_containers" title="Polymorphic containers">Polymorphic
      containers</a>
</h3></div></div></div>
<div class="toc"><dl class="toc"><dt><span class="section"><a href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections">Polymorphic
        collections</a></span></dt></dl></div>
<p>
        A <span class="emphasis"><em>polymorphic container</em></span> is an object that stores objects
        of some type <code class="computeroutput"><span class="identifier">T</span></code> implementing
        a given interface <code class="computeroutput"><span class="identifier">I</span></code> under
        an implicitly associated polymorphism model. Polymorphic containers satisfy
        the requirements for <a href="http://en.cppreference.com/w/cpp/named_req/Container" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">Container</span></code></strong></span></a>
        and <a href="http://en.cppreference.com/w/cpp/named_req/AllocatorAwareContainer" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">AllocatorAwareContainer</span></code></strong></span></a>
        with the following modifications:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Where it occurs, replace the requirement that <code class="computeroutput"><span class="identifier">T</span></code>
            be <a href="http://en.cppreference.com/w/cpp/named_req/CopyInsertable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyInsertable</span></code></strong></span></a>,
            <a href="http://en.cppreference.com/w/cpp/named_req/CopyAssignable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyAssignable</span></code></strong></span></a>,
            <a href="http://en.cppreference.com/w/cpp/named_req/MoveInsertable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">MoveInsertable</span></code></strong></span></a>,
            <a href="http://en.cppreference.com/w/cpp/named_req/MoveAssignable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">MoveAssignable</span></code></strong></span></a>
            or <a href="http://en.cppreference.com/w/cpp/named_req/EqualityComparable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">EqualityComparable</span></code></strong></span></a>,
            with the following semantic clause: may throw if some subobject in the
            container is not <a href="http://en.cppreference.com/w/cpp/named_req/CopyConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyConstructible</span></code></strong></span></a>
            (respectively, <a href="http://en.cppreference.com/w/cpp/named_req/CopyAssignable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyAssignable</span></code></strong></span></a>,
            <a href="http://en.cppreference.com/w/cpp/named_req/MoveConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">MoveConstructible</span></code></strong></span></a>,
            <a href="http://en.cppreference.com/w/cpp/named_req/MoveAssignable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">MoveAssignable</span></code></strong></span></a>,
            <a href="http://en.cppreference.com/w/cpp/named_req/EqualityComparable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">EqualityComparable</span></code></strong></span></a>).
          </li>
<li class="listitem">
            Replace [container.requirements.general]/3 with: <code class="computeroutput"><span class="identifier">allocator_type</span></code>
            must have the property that for any type <code class="computeroutput"><span class="identifier">U</span></code>
            implementing <code class="computeroutput"><span class="identifier">I</span></code> and the
            associated type <code class="computeroutput"><span class="identifier">A</span></code> =
            <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator_traits</span><span class="special">&lt;</span><span class="identifier">allocator_type</span><span class="special">&gt;::</span><span class="identifier">rebind_alloc</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>,
            <code class="computeroutput"><span class="identifier">U</span></code> is <a href="http://en.cppreference.com/w/cpp/named_req/CopyInsertable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyInsertable</span></code></strong></span></a>
            (respectively <a href="http://en.cppreference.com/w/cpp/named_req/MoveInsertable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">MoveInsertable</span></code></strong></span></a>)
            with respect to <code class="computeroutput"><span class="identifier">A</span></code> if
            and only if <code class="computeroutput"><span class="identifier">U</span></code> is <a href="http://en.cppreference.com/w/cpp/named_req/CopyConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyConstructible</span></code></strong></span></a>
            (respectively <a href="http://en.cppreference.com/w/cpp/named_req/MoveConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">MoveConstructible</span></code></strong></span></a>);
            all subobjects of type <code class="computeroutput"><span class="identifier">U</span></code>
            stored in these containers shall be constructed using the <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator_traits</span><span class="special">&lt;</span><span class="identifier">A</span><span class="special">&gt;::</span><span class="identifier">construct</span></code> function and destroyed using
            the <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator_traits</span><span class="special">&lt;</span><span class="identifier">A</span><span class="special">&gt;::</span><span class="identifier">destroy</span></code> function; these functions (or
            their equivalents for a rebound allocator) are called only for the types
            of the stored subobjects, not for any other type (internal or public)
            used by the container.
          </li>
</ul></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="poly_collection.reference.polymorphic_containers.polymorphic_collections"></a><a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections" title="Polymorphic collections">Polymorphic
        collections</a>
</h4></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types">Types</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.construct_copy_destroy">Construct/copy/destroy</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration">Type
          registration</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators">Iterators</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity">Capacity</a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers">Modifiers</a></span></dt>
</dl></div>
<p>
          <span class="emphasis"><em>Polymorphic collections</em></span> store their objects of type
          <code class="computeroutput"><span class="identifier">value_type</span></code> in <span class="emphasis"><em>segments</em></span>
          dedicated to each of the types of the contained subojects. Only objects
          whose subobjects are of an <span class="emphasis"><em>acceptable</em></span> type are allowed,
          where a type <code class="computeroutput"><span class="identifier">U</span></code> is said
          to be acceptable if
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              it implements the interface associated to the container,
            </li>
<li class="listitem">
              it is <a href="http://en.cppreference.com/w/cpp/named_req/MoveConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">MoveConstructible</span></code></strong></span></a>,
            </li>
<li class="listitem">
              it is <a href="http://en.cppreference.com/w/cpp/named_req/MoveAssignable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">MoveAssignable</span></code></strong></span></a>
              or <a href="http://en.cppreference.com/w/cpp/types/is_move_constructible" target="_top"><code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">is_nothrow_move_constructible</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;::</span><span class="identifier">value</span></code></a> is <code class="computeroutput"><span class="keyword">true</span></code>.
            </li>
</ul></div>
<p>
          Polymorphic collections conform to the requirements of <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers" title="Polymorphic containers"><span class="bold"><strong><code class="computeroutput"><span class="identifier">PolymorphicContainer</span></code></strong></span></a>
          with the following modfications and extra guarantees:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              The complexity of <code class="computeroutput"><span class="identifier">empty</span><span class="special">()</span></code> and <code class="computeroutput"><span class="identifier">size</span><span class="special">()</span></code> is linear on the number of segments
              of the collection.
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">max_size</span><span class="special">()</span></code>
              is not provided.
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">a</span><span class="special">==</span><span class="identifier">b</span></code> evaluates to <code class="computeroutput"><span class="keyword">true</span></code>
              iff for each non-empty segment of subojects of type <code class="computeroutput"><span class="identifier">U</span></code>
              in <code class="computeroutput"><span class="identifier">a</span></code> there is a segment
              of <code class="computeroutput"><span class="identifier">U</span></code> in <code class="computeroutput"><span class="identifier">b</span></code> with the same size and equal elements
              in the same order, and vice versa.
            </li>
<li class="listitem">
              No exceptions are thrown associated to some subobject type not being
              <a href="http://en.cppreference.com/w/cpp/named_req/CopyAssignable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyAssignable</span></code></strong></span></a>,
              <a href="http://en.cppreference.com/w/cpp/named_req/MoveConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">MoveConstructible</span></code></strong></span></a>
              or <a href="http://en.cppreference.com/w/cpp/named_req/MoveAssignable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">MoveAssignable</span></code></strong></span></a>.
            </li>
</ul></div>
<p>
          A type <code class="computeroutput"><span class="identifier">U</span></code> is said to be
          <span class="emphasis"><em>registered</em></span> into the collection if a (possibly empty)
          segment for <code class="computeroutput"><span class="identifier">U</span></code> has been
          created. Registered types continue to stay so for the duration of the container
          except if it is moved from, assigned to, or swapped.
        </p>
<p>
          Each segment has an associated capacity indicating the maximum size that
          it can attain without reallocation. When the limit is exceeded (or explicitly
          through <code class="computeroutput"><span class="identifier">reserve</span></code>) new storage
          space is allocated with greater capacity and elements are moved.
        </p>
<p>
          Collection traversal goes through the elements of the first segment, then
          the second, etc. The order in which segments are visited is unspecified
          but remains stable until a new segment is created.
        </p>
<p>
          Besides <code class="computeroutput"><span class="identifier">iterator</span></code> and <code class="computeroutput"><span class="identifier">const_iterator</span></code>, there are iterator types
          <code class="computeroutput"><span class="identifier">local_base_iterator</span></code> and
          <code class="computeroutput"><span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>
          (and their <code class="computeroutput"><span class="identifier">const_</span></code> counterparts)
          whose objects can be used to iterate over the segment for <code class="computeroutput"><span class="identifier">U</span></code> (in the same order followed by global
          traversal). Local base iterators refer to <code class="computeroutput"><span class="identifier">value_type</span></code>,
          whereas (<code class="computeroutput"><span class="identifier">const_</span></code>)<code class="computeroutput"><span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>
          refers to <code class="computeroutput"><span class="identifier">U</span></code>. All local
          iterators model <a href="http://en.cppreference.com/w/cpp/named_req/RandomAccessIterator" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">RandomAccessIterator</span></code></strong></span></a>.
          Local base iterators may not be used to iterate across segments, and comparing
          local base iterators associated to different segments is undefined behavior.
          A (const) local base iterator to a segment for <code class="computeroutput"><span class="identifier">U</span></code>
          can be explicitly converted to (<code class="computeroutput"><span class="identifier">const_</span></code>)<code class="computeroutput"><span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>
          pointing to the same position, and vice versa.
        </p>
<p>
          Insertion and erasure do not invalidate iterators (global or local) except
          those from the insertion/erasure point to the end of the affected segment,
          if its capacity is not exceeded, or all iterators/references to the segment
          otherwise <a href="#ftn.poly_collection.reference.polymorphic_containers.polymorphic_collections.f0" class="footnote" name="poly_collection.reference.polymorphic_containers.polymorphic_collections.f0"><sup class="footnote">[22]</sup></a>.
        </p>
<p>
          For the description of the remaining requirements of polymorphic collections,
          we use the following notation:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              <code class="computeroutput"><span class="identifier">C</span></code> is a polymorphic
              collection type,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">c</span></code> is an object of type
              <code class="computeroutput"><span class="identifier">C</span></code>, <code class="computeroutput"><span class="identifier">cc</span></code>
              is a possibly <code class="computeroutput"><span class="keyword">const</span></code> object
              of type <code class="computeroutput"><span class="identifier">C</span></code>,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">al</span></code> is a value of type
              <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">allocator_type</span></code>,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">info</span></code> is a <code class="computeroutput"><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span></code>,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">U</span></code> is an acceptable
              type, <code class="computeroutput"><span class="identifier">Us</span><span class="special">...</span></code>
              is a template parameter pack of acceptable types,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">n</span></code> is a value of <code class="computeroutput"><span class="identifier">size_type</span></code>,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">x</span></code> is a value of a type
              <code class="computeroutput"><span class="identifier">T</span></code> implementing the
              interface associated to the collection,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">args</span><span class="special">...</span></code>
              is a function parameter pack of types <code class="computeroutput"><span class="identifier">Args</span><span class="special">&amp;&amp;...</span></code>,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">it</span></code> is a possibly const
              global iterator of <code class="computeroutput"><span class="identifier">c</span></code>,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">it1</span></code> and <code class="computeroutput"><span class="identifier">it2</span></code> are (same-typed) possibly const
              global iterators of a <code class="computeroutput"><span class="identifier">C</span></code>
              collection other than <code class="computeroutput"><span class="identifier">c</span></code>
              such that [<code class="computeroutput"><span class="identifier">it1</span></code>, <code class="computeroutput"><span class="identifier">it2</span></code>) is a valid range.
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">lbit</span></code> is a possibly
              const local base iterator of <code class="computeroutput"><span class="identifier">c</span></code>,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">lbit1</span></code> and <code class="computeroutput"><span class="identifier">lbit2</span></code> are (same-typed) possibly const
              local base iterators of a <code class="computeroutput"><span class="identifier">C</span></code>
              collection other than <code class="computeroutput"><span class="identifier">c</span></code>
              such that [<code class="computeroutput"><span class="identifier">lbit1</span></code>,
              <code class="computeroutput"><span class="identifier">lbit2</span></code>) is a valid range.
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">lit</span></code> is a (<code class="computeroutput"><span class="identifier">const_</span></code>)<code class="computeroutput"><span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code> of <code class="computeroutput"><span class="identifier">c</span></code>,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">lit1</span></code> and <code class="computeroutput"><span class="identifier">lit2</span></code> are (same-typed) (<code class="computeroutput"><span class="identifier">const_</span></code>)<code class="computeroutput"><span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>s of a <code class="computeroutput"><span class="identifier">C</span></code>
              collection other than <code class="computeroutput"><span class="identifier">c</span></code>
              such that [<code class="computeroutput"><span class="identifier">lit1</span></code>, <code class="computeroutput"><span class="identifier">lit2</span></code>) is a valid range,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">i1</span></code> and <code class="computeroutput"><span class="identifier">i2</span></code> are iterators external to <code class="computeroutput"><span class="identifier">c</span></code> referring to <code class="computeroutput"><span class="identifier">T</span></code>
              such that [<code class="computeroutput"><span class="identifier">i1</span></code>, <code class="computeroutput"><span class="identifier">i2</span></code>) is a valid range,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">j1</span></code> and <code class="computeroutput"><span class="identifier">j2</span></code> are iterators external to <code class="computeroutput"><span class="identifier">c</span></code> such that [<code class="computeroutput"><span class="identifier">j1</span></code>,
              <code class="computeroutput"><span class="identifier">j2</span></code>) is a valid range,
            </li>
<li class="listitem">
              <code class="computeroutput"><span class="identifier">xit1</span></code> and <code class="computeroutput"><span class="identifier">xit2</span></code> are (same-typed) possibly const
              iterators (global or local) of <code class="computeroutput"><span class="identifier">c</span></code>
              such that [<code class="computeroutput"><span class="identifier">xit1</span></code>, <code class="computeroutput"><span class="identifier">xit2</span></code>) is a valid range.
            </li>
</ul></div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.types"></a><a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types" title="Types">Types</a>
</h5></div></div></div>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.types.local_base_iterator"></a><code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">local_base_iterator</span></code>
          </p>
<p>
            <a href="http://en.cppreference.com/w/cpp/named_req/RandomAccessIterator" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">RandomAccessIterator</span></code></strong></span></a>
            with same value type, difference type and pointer and reference types
            as <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">iterator</span></code>, valid for accessing elements
            of a given segment. Implicily convertible to <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_local_base_iterator</span></code>,
            explicitly convertible to <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code> if the segment it points to is actually
            that for <code class="computeroutput"><span class="identifier">U</span></code>.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_local_base_iterator"></a><code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_local_base_iterator</span></code>
          </p>
<p>
            <a href="http://en.cppreference.com/w/cpp/named_req/RandomAccessIterator" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">RandomAccessIterator</span></code></strong></span></a>
            with same value type, difference type and pointer and reference types
            as <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_iterator</span></code>, valid for accessing
            elements of a given segment. Explicitly convertible to <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>
            if the segment it points to is actually that for <code class="computeroutput"><span class="identifier">U</span></code>.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.types.local_iterator"></a><code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>
          </p>
<p>
            <a href="http://en.cppreference.com/w/cpp/named_req/RandomAccessIterator" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">RandomAccessIterator</span></code></strong></span></a>
            with value type <code class="computeroutput"><span class="identifier">U</span></code>, reference
            type <code class="computeroutput"><span class="identifier">U</span><span class="special">&amp;</span></code>,
            pointer type <code class="computeroutput"><span class="identifier">U</span><span class="special">*</span></code>
            and the same difference type as <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">iterator</span></code>,
            valid for accessing elements of the segment for <code class="computeroutput"><span class="identifier">U</span></code>.
            Implicily convertible to <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>, explicitly convertible to <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">local_base_iterator</span></code>.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_local_iterator"></a><code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>
          </p>
<p>
            <a href="http://en.cppreference.com/w/cpp/named_req/RandomAccessIterator" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">RandomAccessIterator</span></code></strong></span></a>
            with value type <code class="computeroutput"><span class="identifier">U</span></code>, reference
            type <code class="computeroutput"><span class="keyword">const</span> <span class="identifier">U</span><span class="special">&amp;</span></code>, pointer type <code class="computeroutput"><span class="keyword">const</span>
            <span class="identifier">U</span><span class="special">*</span></code>
            and the same difference type as <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">iterator</span></code>,
            valid for accessing elements of the segment for <code class="computeroutput"><span class="identifier">U</span></code>.
            Explicitly convertible to <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_local_base_iterator</span></code>.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_base_segment_info"></a><code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_base_segment_info</span></code>
          </p>
<p>
            <a href="http://en.cppreference.com/w/cpp/named_req/CopyConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyConstructible</span></code></strong></span></a>
            and <a href="http://en.cppreference.com/w/cpp/named_req/CopyAssignable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyAssignable</span></code></strong></span></a>
            type with information about a given segment of a collection. If <code class="computeroutput"><span class="identifier">ci</span></code> is a possibly <code class="computeroutput"><span class="keyword">const</span></code>
            object of type <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_base_segment_info</span></code> associated
            to the segment of <code class="computeroutput"><span class="identifier">c</span></code> for
            <code class="computeroutput"><span class="identifier">U</span></code>, then
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ci</span><span class="special">.</span><span class="identifier">begin</span><span class="special">()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">cbegin</span><span class="special">(</span><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">U</span><span class="special">))</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ci</span><span class="special">.</span><span class="identifier">cbegin</span><span class="special">()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">cbegin</span><span class="special">(</span><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">U</span><span class="special">))</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ci</span><span class="special">.</span><span class="identifier">begin</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">cbegin</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ci</span><span class="special">.</span><span class="identifier">cbegin</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">cbegin</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ci</span><span class="special">.</span><span class="identifier">end</span><span class="special">()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">cend</span><span class="special">(</span><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">U</span><span class="special">))</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ci</span><span class="special">.</span><span class="identifier">cend</span><span class="special">()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">cend</span><span class="special">(</span><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">U</span><span class="special">))</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ci</span><span class="special">.</span><span class="identifier">end</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">cend</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ci</span><span class="special">.</span><span class="identifier">cend</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">cend</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ci</span><span class="special">.</span><span class="identifier">type_info</span><span class="special">()==</span><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">U</span><span class="special">)</span></code>
              </li>
</ul></div>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.types.base_segment_info"></a><code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">base_segment_info</span></code>
          </p>
<p>
            <a href="http://en.cppreference.com/w/cpp/named_req/CopyConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyConstructible</span></code></strong></span></a>
            and <a href="http://en.cppreference.com/w/cpp/named_req/CopyAssignable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyAssignable</span></code></strong></span></a>
            type publicly derived from <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_base_segment_info</span></code>
            and exposing its public interface. Additionally, if <code class="computeroutput"><span class="identifier">i</span></code>
            is an object of type <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">base_segment_info</span></code>
            associated to the segment of <code class="computeroutput"><span class="identifier">c</span></code>
            for <code class="computeroutput"><span class="identifier">U</span></code>, then
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <code class="computeroutput"><span class="identifier">i</span><span class="special">.</span><span class="identifier">begin</span><span class="special">()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">begin</span><span class="special">(</span><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">U</span><span class="special">))</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">i</span><span class="special">.</span><span class="identifier">begin</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">begin</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">i</span><span class="special">.</span><span class="identifier">end</span><span class="special">()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">end</span><span class="special">(</span><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">U</span><span class="special">))</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">i</span><span class="special">.</span><span class="identifier">end</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">end</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
              </li>
</ul></div>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_segment_info"></a><code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_segment_info</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>
          </p>
<p>
            <a href="http://en.cppreference.com/w/cpp/named_req/CopyConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyConstructible</span></code></strong></span></a>
            and <a href="http://en.cppreference.com/w/cpp/named_req/CopyAssignable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyAssignable</span></code></strong></span></a>
            type with information about the segment for <code class="computeroutput"><span class="identifier">U</span></code>.
            If <code class="computeroutput"><span class="identifier">ci</span></code> is a possibly
            <code class="computeroutput"><span class="keyword">const</span></code> object of type <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_segment_info</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>
            associated to the collection <code class="computeroutput"><span class="identifier">c</span></code>,
            then
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ci</span><span class="special">.</span><span class="identifier">begin</span><span class="special">()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">cbegin</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ci</span><span class="special">.</span><span class="identifier">cbegin</span><span class="special">()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">cbegin</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ci</span><span class="special">.</span><span class="identifier">end</span><span class="special">()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">cend</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">ci</span><span class="special">.</span><span class="identifier">cend</span><span class="special">()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">cend</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
              </li>
</ul></div>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.types.segment_info"></a><code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">segment_info</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>
          </p>
<p>
            <a href="http://en.cppreference.com/w/cpp/named_req/CopyConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyConstructible</span></code></strong></span></a>
            and <a href="http://en.cppreference.com/w/cpp/named_req/CopyAssignable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyAssignable</span></code></strong></span></a>
            type publicly derived from <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_segment_info</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code> and exposing its public interface.
            Additionally, if <code class="computeroutput"><span class="identifier">i</span></code> is
            an object of type <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">segment_info</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>
            associated to the collection <code class="computeroutput"><span class="identifier">c</span></code>,
            then
          </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                <code class="computeroutput"><span class="identifier">i</span><span class="special">.</span><span class="identifier">begin</span><span class="special">()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">begin</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
              </li>
<li class="listitem">
                <code class="computeroutput"><span class="identifier">i</span><span class="special">.</span><span class="identifier">end</span><span class="special">()==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">end</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
              </li>
</ul></div>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.types.base_segment_info_iterator"></a><code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">base_segment_info_iterator</span></code>
          </p>
<p>
            <a href="http://en.cppreference.com/w/cpp/named_req/InputIterator" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">InputIterator</span></code></strong></span></a>
            with value type and reference type <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">base_segment_info</span></code>.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_base_segment_info_iterator"></a><code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_base_segment_info_iterator</span></code>
          </p>
<p>
            <a href="http://en.cppreference.com/w/cpp/named_req/InputIterator" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">InputIterator</span></code></strong></span></a>
            with value type and reference type <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_base_segment_info</span></code>.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_segment_traversal_info"></a><code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_segment_traversal_info</span></code>
          </p>
<p>
            <a href="http://en.cppreference.com/w/cpp/named_req/CopyConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyConstructible</span></code></strong></span></a>
            and <a href="http://en.cppreference.com/w/cpp/named_req/CopyAssignable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyAssignable</span></code></strong></span></a>
            type with <code class="computeroutput"><span class="keyword">const</span></code> member functions
            <code class="computeroutput"><span class="identifier">begin</span></code>/<code class="computeroutput"><span class="identifier">cbegin</span></code>
            and <code class="computeroutput"><span class="identifier">end</span></code>/<code class="computeroutput"><span class="identifier">cend</span></code> returning <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_base_segment_info_iterator</span></code>
            objects that span over a range of <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_base_segment_info</span></code>
            objects.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.types.segment_traversal_info"></a><code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">segment_traversal_info</span></code>
          </p>
<p>
            <a href="http://en.cppreference.com/w/cpp/named_req/CopyConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyConstructible</span></code></strong></span></a>
            and <a href="http://en.cppreference.com/w/cpp/named_req/CopyAssignable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyAssignable</span></code></strong></span></a>
            type publicly derived from with <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">const_segment_traversal_info</span></code>
            and exposing its public interface. Additionally, provides non-const member
            functions <code class="computeroutput"><span class="identifier">begin</span></code> and
            <code class="computeroutput"><span class="identifier">end</span></code> returning <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">base_segment_info_iterator</span></code> objects
            that span over an equivalent range of <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">base_segment_info</span></code>
            objects.
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.construct_copy_destroy"></a><a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.construct_copy_destroy" title="Construct/copy/destroy">Construct/copy/destroy</a>
</h5></div></div></div>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.construct_copy_destroy.range_construction"></a><code class="computeroutput"><span class="identifier">C</span><span class="special">(</span><span class="identifier">j1</span><span class="special">,</span><span class="identifier">j2</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">C</span> <span class="identifier">d</span><span class="special">(</span><span class="identifier">j1</span><span class="special">,</span><span class="identifier">j2</span><span class="special">)</span></code>
          </p>
<p>
            <span class="bold"><strong>Requires:</strong></span> <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">allocator_type</span></code>
            is <a href="http://en.cppreference.com/w/cpp/named_req/DefaultConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">DefaultConstructible</span></code></strong></span></a>.
            [<code class="computeroutput"><span class="identifier">j1</span></code>, <code class="computeroutput"><span class="identifier">j2</span></code>)
            can be inserted into <code class="computeroutput"><span class="identifier">C</span></code>.<br>
            <span class="bold"><strong>Effects:</strong></span> Copy constructs the internal
            allocator from <code class="computeroutput"><span class="identifier">C</span><span class="special">::</span><span class="identifier">allocator_type</span><span class="special">()</span></code>.
            Internally calls <code class="computeroutput"><span class="keyword">this</span><span class="special">-&gt;</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">j1</span><span class="special">,</span><span class="identifier">j2</span><span class="special">)</span></code>
            on construction.
          </p>
<p>
            <code class="computeroutput"><span class="identifier">C</span><span class="special">(</span><span class="identifier">j1</span><span class="special">,</span><span class="identifier">j2</span><span class="special">,</span><span class="identifier">al</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">C</span> <span class="identifier">d</span><span class="special">(</span><span class="identifier">j1</span><span class="special">,</span><span class="identifier">j2</span><span class="special">,</span><span class="identifier">al</span><span class="special">)</span></code>
          </p>
<p>
            <span class="bold"><strong>Requires:</strong></span> [<code class="computeroutput"><span class="identifier">j1</span></code>,
            <code class="computeroutput"><span class="identifier">j2</span></code>) can be inserted into
            <code class="computeroutput"><span class="identifier">C</span></code>.<br> <span class="bold"><strong>Effects:</strong></span>
            Copy constructs the internal allocator from <code class="computeroutput"><span class="identifier">al</span></code>.
            Internally calls <code class="computeroutput"><span class="keyword">this</span><span class="special">-&gt;</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">j1</span><span class="special">,</span><span class="identifier">j2</span><span class="special">)</span></code>
            on construction.
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration"></a><a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration" title="Type registration">Type
          registration</a>
</h5></div></div></div>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration.register_types"></a><code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">register_types</span><span class="special">&lt;</span><span class="identifier">Us</span><span class="special">...&gt;()</span></code>
          </p>
<p>
            <span class="bold"><strong>Effects:</strong></span> Registers (if needed) each
            of the indicated types in the collection.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration.is_registered"></a><code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">is_registered</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">is_registered</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
          </p>
<p>
            <span class="bold"><strong>Returns:</strong></span> <code class="computeroutput"><span class="keyword">true</span></code>
            iff the indicated type is registered in the collection.
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators"></a><a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators" title="Iterators">Iterators</a>
</h5></div></div></div>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"></a>(1)
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">begin</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br>
            (2) <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">begin</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code><br>
            (3) <code class="computeroutput"><span class="keyword">const_cast</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="identifier">C</span><span class="special">&amp;&gt;(</span><span class="identifier">c</span><span class="special">).</span><span class="identifier">begin</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br> (4) <code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">cbegin</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br> (5) <code class="computeroutput"><span class="keyword">const_cast</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="identifier">C</span><span class="special">&amp;&gt;(</span><span class="identifier">c</span><span class="special">).</span><span class="identifier">begin</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code><br>
            (6) <code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">cbegin</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
          </p>
<p>
            <span class="bold"><strong>Returns:</strong></span> A <code class="computeroutput"><span class="identifier">local_base_iterator</span></code>
            (1) or <code class="computeroutput"><span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code> (2) or <code class="computeroutput"><span class="identifier">const_local_base_iterator</span></code>
            (3,4) or <code class="computeroutput"><span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code> (5,6) to the beginning of the segment
            for the indicated type.<br> <span class="bold"><strong>Throws:</strong></span>
            If the indicated type is not registered.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"></a>(1)
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">end</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br>
            (2) <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">end</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code><br>
            (3) <code class="computeroutput"><span class="keyword">const_cast</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="identifier">C</span><span class="special">&amp;&gt;(</span><span class="identifier">c</span><span class="special">).</span><span class="identifier">end</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br> (4) <code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">cend</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br> (5) <code class="computeroutput"><span class="keyword">const_cast</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="identifier">C</span><span class="special">&amp;&gt;(</span><span class="identifier">c</span><span class="special">).</span><span class="identifier">end</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code><br>
            (6) <code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">cend</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
          </p>
<p>
            <span class="bold"><strong>Returns:</strong></span> A <code class="computeroutput"><span class="identifier">local_base_iterator</span></code>
            (1) or <code class="computeroutput"><span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code> (2) or <code class="computeroutput"><span class="identifier">const_local_base_iterator</span></code>
            (3,4) or <code class="computeroutput"><span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code> (5,6) to the end of the segment for
            the indicated type.<br> <span class="bold"><strong>Throws:</strong></span> If
            the indicated type is not registered.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment"></a>(1)
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">segment</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br>
            (2) <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">segment</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code><br>
            (3) <code class="computeroutput"><span class="keyword">const_cast</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="identifier">C</span><span class="special">&amp;&gt;(</span><span class="identifier">c</span><span class="special">).</span><span class="identifier">segment</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br> (4) <code class="computeroutput"><span class="keyword">const_cast</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="identifier">C</span><span class="special">&amp;&gt;(</span><span class="identifier">c</span><span class="special">).</span><span class="identifier">segment</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code><br>
          </p>
<p>
            <span class="bold"><strong>Returns:</strong></span> A <code class="computeroutput"><span class="identifier">base_segment_info</span></code>
            (1) or <code class="computeroutput"><span class="identifier">segment_info</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>
            (2) or <code class="computeroutput"><span class="identifier">const_base_segment_info</span></code>
            (3) or <code class="computeroutput"><span class="identifier">const_segment_info</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code> (4) object referring to the segment
            for the indicated type.<br> <span class="bold"><strong>Throws:</strong></span>
            If the indicated type is not registered.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment_traversal"></a>(1)
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">segment_traversal</span><span class="special">()</span></code><br>
            (2) <code class="computeroutput"><span class="keyword">const_cast</span><span class="special">&lt;</span><span class="keyword">const</span> <span class="identifier">C</span><span class="special">&amp;&gt;(</span><span class="identifier">c</span><span class="special">).</span><span class="identifier">segment_traversal</span><span class="special">()</span></code>
          </p>
<p>
            <span class="bold"><strong>Returns:</strong></span> A <code class="computeroutput"><span class="identifier">segment_traversal_info</span></code>
            (1) or <code class="computeroutput"><span class="identifier">const_segment_traversal_info</span></code>
            (2) object spanning over a range of segment descriptors for the collection.
            The order in which segments are visited matches that of [<code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">begin</span><span class="special">()</span></code>,
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">end</span><span class="special">()</span></code>).
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity"></a><a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity" title="Capacity">Capacity</a>
</h5></div></div></div>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.empty"></a><code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">empty</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">empty</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
          </p>
<p>
            <span class="bold"><strong>Returns:</strong></span> <code class="computeroutput"><span class="keyword">true</span></code>
            iff the segment for the indicated type exists and is empty.<br> <span class="bold"><strong>Throws:</strong></span> If the indicated type is not registered.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.size"></a><code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">size</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">size</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
          </p>
<p>
            <span class="bold"><strong>Returns:</strong></span> The size of the segment for
            the indicated type.<br> <span class="bold"><strong>Throws:</strong></span> If
            the indicated type is not registered.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.max_size"></a><code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">max_size</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">max_size</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
          </p>
<p>
            <span class="bold"><strong>Returns:</strong></span> The maximum size attainable
            by the segment for the indicated type.<br> <span class="bold"><strong>Throws:</strong></span>
            If the indicated type is not registered.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.capacity"></a><code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">capacity</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">cc</span><span class="special">.</span><span class="identifier">capacity</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code><br>
          </p>
<p>
            <span class="bold"><strong>Returns:</strong></span> The maximum size that the segment
            for the indicated type can attain without requiring reallocation.<br>
            <span class="bold"><strong>Throws:</strong></span> If the indicated type is not
            registered.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.reserve"></a><code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">reserve</span><span class="special">(</span><span class="identifier">n</span><span class="special">)</span></code>
          </p>
<p>
            <span class="bold"><strong>Effects:</strong></span> Calls <code class="computeroutput"><span class="identifier">reserve</span></code>
            with <code class="computeroutput"><span class="identifier">n</span></code> for each of the
            segments of the collection.
          </p>
<p>
            (1) <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">reserve</span><span class="special">(</span><span class="identifier">info</span><span class="special">,</span><span class="identifier">n</span><span class="special">)</span></code><br>
            (2) <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">reserve</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;(</span><span class="identifier">n</span><span class="special">)</span></code>
          </p>
<p>
            <span class="bold"><strong>Effects:</strong></span> Throws if the type indicated
            by <code class="computeroutput"><span class="identifier">info</span></code> is not registered
            (1) or registers <code class="computeroutput"><span class="identifier">U</span></code> if
            needed (2). If <code class="computeroutput"><span class="identifier">n</span></code> is greater
            than the current capacity of the segment for the indicated type, new
            storage space is allocated with a capacity of at least <code class="computeroutput"><span class="identifier">n</span></code> and elements are moved there.<br>
            <span class="bold"><strong>Complexity:</strong></span> Linear in the size of the
            segment if reallocation happens, constant otherwise.<br> <span class="bold"><strong>Throws:</strong></span> <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">length_error</span></code>
            if <code class="computeroutput"><span class="identifier">n</span></code> is greater than
            the return value of <code class="computeroutput"><span class="identifier">max_size</span></code>
            for the segment.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.shrink_to_fit"></a><code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">shrink_to_fit</span><span class="special">()</span></code>
          </p>
<p>
            <span class="bold"><strong>Effects:</strong></span> Calls <code class="computeroutput"><span class="identifier">shrink_to_fit</span></code>
            for each of the segments of the collection.
          </p>
<p>
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">shrink_to_fit</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">shrink_to_fit</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
          </p>
<p>
            <span class="bold"><strong>Effects:</strong></span> Non-binding request to reduce
            memory usage while preserving the sequence of elements of the segment
            for the indicated type. May invalidate all iterators and references to
            the segment.<br> <span class="bold"><strong>Throws:</strong></span> If the indicated
            type is not registered.
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers"></a><a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers" title="Modifiers">Modifiers</a>
</h5></div></div></div>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.emplace"></a>(1)
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">emplace</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;(</span><span class="identifier">args</span><span class="special">...)</span></code><br>
            (2) <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">emplace_hint</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;(</span><span class="identifier">it</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code>
          </p>
<p>
            <span class="bold"><strong>Requires:</strong></span> <code class="computeroutput"><span class="identifier">U</span></code>
            is constructible from <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">forward</span><span class="special">&lt;</span><span class="identifier">Args</span><span class="special">&gt;(</span><span class="identifier">args</span><span class="special">)...</span></code>.<br> <span class="bold"><strong>Effects:</strong></span>
            Registers <code class="computeroutput"><span class="identifier">U</span></code> (if needed)
            and inserts a new element with a subobject constructed from <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">forward</span><span class="special">&lt;</span><span class="identifier">Args</span><span class="special">&gt;(</span><span class="identifier">args</span><span class="special">)...</span></code>:
            (1) at the end of the segment for <code class="computeroutput"><span class="identifier">U</span></code>;
            (2) just before the position indicated by <code class="computeroutput"><span class="identifier">it</span></code>,
            if it points to the segment for <code class="computeroutput"><span class="identifier">U</span></code>,
            or at the end of the segment for <code class="computeroutput"><span class="identifier">U</span></code>
            otherwise.<br> <span class="bold"><strong>Returns:</strong></span> An <code class="computeroutput"><span class="identifier">iterator</span></code> to the newly inserted element.<br>
            <span class="bold"><strong>Complexity:</strong></span> Amortized constant time
            plus linear in the distance from the insertion position to the end of
            the segment.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.emplace_pos"></a>(1)
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">emplace_pos</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;(</span><span class="identifier">lbit</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code><br>
            (2) <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">emplace_pos</span><span class="special">(</span><span class="identifier">lit</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code>
          </p>
<p>
            <span class="bold"><strong>Requires:</strong></span> <code class="computeroutput"><span class="identifier">U</span></code>
            is constructible from <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">forward</span><span class="special">&lt;</span><span class="identifier">Args</span><span class="special">&gt;(</span><span class="identifier">args</span><span class="special">)...</span></code>. (1) <code class="computeroutput"><span class="identifier">lbit</span></code>
            points to the segment for <code class="computeroutput"><span class="identifier">U</span></code>.<br>
            <span class="bold"><strong>Effects:</strong></span> Inserts a new element with
            a subobject constructed from <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">forward</span><span class="special">&lt;</span><span class="identifier">Args</span><span class="special">&gt;(</span><span class="identifier">args</span><span class="special">)...</span></code> just before the position indicated.<br>
            <span class="bold"><strong>Returns:</strong></span> A <code class="computeroutput"><span class="identifier">local_base_iterator</span></code>
            (1) or <code class="computeroutput"><span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code> (2) to the newly inserted element.<br>
            <span class="bold"><strong>Complexity:</strong></span> Amortized constant time
            plus linear in the distance from the insertion position to the end of
            the segment.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert"></a>(1)
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">x</span><span class="special">)</span></code><br>
            (2) <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">it</span><span class="special">,</span><span class="identifier">x</span><span class="special">)</span></code>
          </p>
<p>
            <span class="bold"><strong>Effects:</strong></span> Let <code class="computeroutput"><span class="identifier">Q</span></code>
            be the type of the subobject of <code class="computeroutput"><span class="identifier">x</span></code>.
            If <code class="computeroutput"><span class="identifier">Q</span></code> = <code class="computeroutput"><span class="identifier">T</span></code> and <code class="computeroutput"><span class="identifier">T</span></code>
            is acceptable, registers <code class="computeroutput"><span class="identifier">T</span></code>
            if needed. If <code class="computeroutput"><span class="identifier">Q</span></code> = <code class="computeroutput"><span class="identifier">T</span></code> and <code class="computeroutput"><span class="identifier">T</span></code>
            is not acceptable, throws. If <code class="computeroutput"><span class="identifier">Q</span></code>
            ≠ <code class="computeroutput"><span class="identifier">T</span></code> and <code class="computeroutput"><span class="identifier">Q</span></code> is not registered, throws. If <code class="computeroutput"><span class="identifier">x</span></code> is not a non-const rvalue expression
            and <code class="computeroutput"><span class="identifier">Q</span></code> is not <a href="http://en.cppreference.com/w/cpp/named_req/CopyConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyConstructible</span></code></strong></span></a>,
            throws. Inserts an element with a subobject move constructed or copy
            constructed from the subobject of <code class="computeroutput"><span class="identifier">x</span></code>:
            (1) at the end of the corresponding segment; (2) just before the position
            indicated by <code class="computeroutput"><span class="identifier">it</span></code>, if it
            points to the corresponding segment, or at the end of the segment otherwise.<br>
            <span class="bold"><strong>Returns:</strong></span> An <code class="computeroutput"><span class="identifier">iterator</span></code>
            to the newly inserted element.<br> <span class="bold"><strong>Complexity:</strong></span>
            Amortized constant time plus linear in the distance from the insertion
            position to the end of the segment.
          </p>
<p>
            (1) <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">lbit</span><span class="special">,</span><span class="identifier">x</span><span class="special">)</span></code><br>
            (2) <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">lit</span><span class="special">,</span><span class="identifier">x</span><span class="special">)</span></code>
          </p>
<p>
            <span class="bold"><strong>Requires:</strong></span> The type of the subobject
            of <code class="computeroutput"><span class="identifier">x</span></code> corresponds to the
            indicated segment.<br> <span class="bold"><strong>Effects:</strong></span> Inserts
            an element with a subobject move constructed or copy constructed from
            the subobject of <code class="computeroutput"><span class="identifier">x</span></code> just
            before the position indicated.<br> <span class="bold"><strong>Returns:</strong></span>
            A <code class="computeroutput"><span class="identifier">local_base_iterator</span></code>
            (1) or <code class="computeroutput"><span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code> (2) to the newly inserted element.<br>
            <span class="bold"><strong>Complexity:</strong></span> Amortized constant time
            plus linear in the distance from the insertion position to the end of
            the segment.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert_range"></a><code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">i1</span><span class="special">,</span><span class="identifier">i2</span><span class="special">)</span></code>
          </p>
<p>
            <span class="bold"><strong>Effects:</strong></span> Equivalent to <code class="computeroutput"><span class="keyword">while</span><span class="special">(</span><span class="identifier">i1</span><span class="special">!=</span><span class="identifier">i2</span><span class="special">)</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(*</span><span class="identifier">i1</span><span class="special">++)</span></code>.
          </p>
<p>
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">it1</span><span class="special">,</span><span class="identifier">it2</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">lbit1</span><span class="special">,</span><span class="identifier">lbit2</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">lit1</span><span class="special">,</span><span class="identifier">lit2</span><span class="special">)</span></code>
          </p>
<p>
            <span class="bold"><strong>Effects:</strong></span> For each of the elements of
            the range in succession, registers the type of its subobject if needed
            and inserts it into the collection <a href="#ftn.poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.f0" class="footnote" name="poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.f0"><sup class="footnote">[23]</sup></a>.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert_hint_range"></a><code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">it</span><span class="special">,</span><span class="identifier">i1</span><span class="special">,</span><span class="identifier">i2</span><span class="special">)</span></code>
          </p>
<p>
            <span class="bold"><strong>Effects:</strong></span> If <code class="computeroutput"><span class="identifier">it</span><span class="special">==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">end</span><span class="special">()</span></code>, equivalent to <code class="computeroutput"><span class="keyword">while</span><span class="special">(</span><span class="identifier">i1</span><span class="special">!=</span><span class="identifier">i2</span><span class="special">)</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">it</span><span class="special">,*</span><span class="identifier">i1</span><span class="special">++)</span></code>, otherwise inserts each of the elements
            in [<code class="computeroutput"><span class="identifier">i1</span></code>, <code class="computeroutput"><span class="identifier">i2</span></code>) in succession with a hint pointing
            to <code class="computeroutput"><span class="special">*</span><span class="identifier">it</span></code>
            <a href="#ftn.poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.f1" class="footnote" name="poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.f1"><sup class="footnote">[24]</sup></a>.
          </p>
<p>
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">it</span><span class="special">,</span><span class="identifier">it1</span><span class="special">,</span><span class="identifier">it2</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">it</span><span class="special">,</span><span class="identifier">lbit1</span><span class="special">,</span><span class="identifier">lbit2</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">it</span><span class="special">,</span><span class="identifier">lit1</span><span class="special">,</span><span class="identifier">lit2</span><span class="special">)</span></code>
          </p>
<p>
            <span class="bold"><strong>Effects:</strong></span> If <code class="computeroutput"><span class="identifier">it</span><span class="special">==</span><span class="identifier">c</span><span class="special">.</span><span class="identifier">end</span><span class="special">()</span></code>, equivalent to the corresponding hint-less
            version, otherwise for each of the elements in [<code class="computeroutput"><span class="identifier">i1</span></code>,
            <code class="computeroutput"><span class="identifier">i2</span></code>) in succession registers
            the type of its subobject if needed and inserts it into the collection
            with a hint pointing to <code class="computeroutput"><span class="special">*</span><span class="identifier">it</span></code> <a href="#ftn.poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.f2" class="footnote" name="poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.f2"><sup class="footnote">[25]</sup></a>.
          </p>
<p>
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">lbit</span><span class="special">,</span><span class="identifier">i1</span><span class="special">,</span><span class="identifier">i2</span><span class="special">)</span></code>
          </p>
<p>
            <span class="bold"><strong>Requires:</strong></span> The subojects of elements
            in [<code class="computeroutput"><span class="identifier">i1</span></code>, <code class="computeroutput"><span class="identifier">i2</span></code>) are all of the type corresponding
            to the indicated segment.<br> <span class="bold"><strong>Effects:</strong></span>
            Inserts a range of elements with subobjects copy constructed from those
            in [<code class="computeroutput"><span class="identifier">i1</span></code>, <code class="computeroutput"><span class="identifier">i2</span></code>) just before <code class="computeroutput"><span class="identifier">lbit</span></code>.<br>
            <span class="bold"><strong>Returns:</strong></span> A <code class="computeroutput"><span class="identifier">local_base_iterator</span></code>
            to the beginning of the inserted range.
          </p>
<p>
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">lit</span><span class="special">,</span><span class="identifier">j1</span><span class="special">,</span><span class="identifier">j2</span><span class="special">)</span></code>
          </p>
<p>
            <span class="bold"><strong>Requires:</strong></span> For each value <code class="computeroutput"><span class="identifier">x</span></code> in [<code class="computeroutput"><span class="identifier">j1</span></code>,
            <code class="computeroutput"><span class="identifier">j2</span></code>) either (a) <code class="computeroutput"><span class="identifier">x</span></code> is of a type implementing the interface
            associated to the collection and the subobject of <code class="computeroutput"><span class="identifier">x</span></code>
            is of type <code class="computeroutput"><span class="identifier">U</span></code> or (b)
            <code class="computeroutput"><span class="identifier">U</span></code> is constructible from
            <code class="computeroutput"><span class="identifier">x</span></code>.<br> <span class="bold"><strong>Effects:</strong></span>
            Inserts a range of elements with subobjects copy constructed (a) or constructed
            (b) from the values in [<code class="computeroutput"><span class="identifier">j1</span></code>,
            <code class="computeroutput"><span class="identifier">j2</span></code>) just before <code class="computeroutput"><span class="identifier">lit</span></code>.<br> <span class="bold"><strong>Returns:</strong></span>
            A <code class="computeroutput"><span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;</span></code>
            to the beginning of the inserted range.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.erase"></a><code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">erase</span><span class="special">(</span><span class="identifier">xit1</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">erase</span><span class="special">(</span><span class="identifier">xit1</span><span class="special">,</span><span class="identifier">xit2</span><span class="special">)</span></code>
          </p>
<p>
            <span class="bold"><strong>Effects:</strong></span> Erases the indicated element(s).<br>
            <span class="bold"><strong>Returns:</strong></span> A non-const iterator of the
            same category as <code class="computeroutput"><span class="identifier">xit</span></code>
            pointing to the position just after the erased element(s).<br> <span class="bold"><strong>Complexity:</strong></span> Linear on the number of elements erased
            plus the distance from the last one to the end of its segment.
          </p>
<p>
            <a name="poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.clear"></a><code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">clear</span><span class="special">()</span></code>
          </p>
<p>
            <span class="bold"><strong>Effects:</strong></span> Erases all the elements of
            the container.<br> <span class="bold"><strong>Complexity:</strong></span> Linear.
          </p>
<p>
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">clear</span><span class="special">(</span><span class="identifier">info</span><span class="special">)</span></code><br>
            <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">clear</span><span class="special">&lt;</span><span class="identifier">U</span><span class="special">&gt;()</span></code>
          </p>
<p>
            <span class="bold"><strong>Effects:</strong></span> Erases all the elements of
            the segment for the indicated type.<br> <span class="bold"><strong>Complexity:</strong></span>
            Linear in the size of the segment.<br> <span class="bold"><strong>Throws:</strong></span>
            If the indicated type is not registered.
          </p>
</div>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_exc"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_exc" title='Header "boost/poly_collection/exception.hpp" synopsis'>Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/exception.hpp"</span></code>
      synopsis</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_exc.class_unregistered_type">Class
        <code class="computeroutput"><span class="identifier">unregistered_type</span></code></a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_exc.class_not_copy_constructible">Class
        <code class="computeroutput"><span class="identifier">not_copy_constructible</span></code></a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_exc.class_not_equality_comparable">Class
        <code class="computeroutput"><span class="identifier">not_equality_comparable</span></code></a></span></dt>
</dl></div>
<p>
        All the collections in Boost.PolyCollection use the following exceptions
        (and only these) to signal various run-time problems with contained types:
      </p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span>

<span class="keyword">namespace</span> <span class="identifier">poly_collection</span><span class="special">{</span>

<span class="keyword">struct</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_exc.class_unregistered_type" title="Class unregistered_type">unregistered_type</a><span class="special">;</span>
<span class="keyword">struct</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_exc.class_not_copy_constructible" title="Class not_copy_constructible">not_copy_constructible</a><span class="special">;</span>
<span class="keyword">struct</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_exc.class_not_equality_comparable" title="Class not_equality_comparable">not_equality_comparable</a><span class="special">;</span>

<span class="special">}</span> <span class="comment">/* namespace poly_collection */</span>

<span class="special">}</span> <span class="comment">/* namespace boost */</span>
</pre>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_exc.class_unregistered_type"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_exc.class_unregistered_type" title="Class unregistered_type">Class
        <code class="computeroutput"><span class="identifier">unregistered_type</span></code></a>
</h4></div></div></div>
<pre class="programlisting"><span class="keyword">struct</span> <span class="identifier">unregistered_type</span><span class="special">:</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">logic_error</span>
<span class="special">{</span>
  <span class="identifier">unregistered_type</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>

  <span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">*</span> <span class="identifier">pinfo</span><span class="special">;</span>
<span class="special">};</span>
</pre>
<p>
          <code class="computeroutput"><span class="identifier">unregistered_type</span></code> is thrown
          when an operation is requested on a type which does not yet have an associated
          segment.
        </p>
<p>
          <code class="computeroutput"><span class="identifier">unregistered_type</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span></code>
        </p>
<p>
          <span class="bold"><strong>Effects:</strong></span> Constructs an <code class="computeroutput"><span class="identifier">unregistered_type</span></code>
          object with the specified type information.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_exc.class_not_copy_constructible"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_exc.class_not_copy_constructible" title="Class not_copy_constructible">Class
        <code class="computeroutput"><span class="identifier">not_copy_constructible</span></code></a>
</h4></div></div></div>
<pre class="programlisting"><span class="keyword">struct</span> <span class="identifier">not_copy_constructible</span><span class="special">:</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">logic_error</span>
<span class="special">{</span>
  <span class="identifier">not_copy_constructible</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>

  <span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">*</span> <span class="identifier">pinfo</span><span class="special">;</span>
<span class="special">};</span>
</pre>
<p>
          <code class="computeroutput"><span class="identifier">not_copy_constructible</span></code>
          is thrown when a copy operation is tried that involves a non-<a href="http://en.cppreference.com/w/cpp/named_req/CopyConstructible" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">CopyConstructible</span></code></strong></span></a>
          type.
        </p>
<p>
          <code class="computeroutput"><span class="identifier">not_copy_constructible</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span>
          <span class="identifier">info</span><span class="special">);</span></code>
        </p>
<p>
          <span class="bold"><strong>Effects:</strong></span> Constructs a <code class="computeroutput"><span class="identifier">not_copy_constructible</span></code>
          object with the specified type information.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_exc.class_not_equality_comparable"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_exc.class_not_equality_comparable" title="Class not_equality_comparable">Class
        <code class="computeroutput"><span class="identifier">not_equality_comparable</span></code></a>
</h4></div></div></div>
<pre class="programlisting"><span class="keyword">struct</span> <span class="identifier">not_equality_comparable</span><span class="special">:</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">logic_error</span>
<span class="special">{</span>
  <span class="identifier">not_equality_comparable</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>

  <span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">*</span> <span class="identifier">pinfo</span><span class="special">;</span>
<span class="special">};</span>
</pre>
<p>
          <code class="computeroutput"><span class="identifier">not_equality_comparable</span></code>
          is thrown when comparing two collections for (in)equality involves a non-<a href="http://en.cppreference.com/w/cpp/named_req/EqualityComparable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">EqualityComparable</span></code></strong></span></a>
          type.
        </p>
<p>
          <code class="computeroutput"><span class="identifier">not_equality_comparable</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span>
          <span class="identifier">info</span><span class="special">);</span></code>
        </p>
<p>
          <span class="bold"><strong>Effects:</strong></span> Constructs a <code class="computeroutput"><span class="identifier">not_equality_comparable</span></code>
          object with the specified type information.
        </p>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_bas"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_bas" title='Header "boost/poly_collection/base_collection_fwd.hpp" synopsis'>Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/base_collection_fwd.hpp"</span></code>
      synopsis</a>
</h3></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">memory</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span>

<span class="keyword">namespace</span> <span class="identifier">poly_collection</span><span class="special">{</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Base</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">=</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">&gt;&gt;</span>
<span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_ba0.class_template_base_collection" title="Class template base_collection"><code class="computeroutput"><span class="identifier">base_collection</span></code></a><span class="special">;</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Base</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span>
  <span class="keyword">const</span> <span class="identifier">base_collection</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="keyword">const</span> <span class="identifier">base_collection</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Base</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span>
  <span class="keyword">const</span> <span class="identifier">base_collection</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="keyword">const</span> <span class="identifier">base_collection</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Base</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span>
  <span class="identifier">base_collection</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="identifier">base_collection</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="special">}</span> <span class="comment">/* namespace poly_collection */</span>

<span class="keyword">using</span> <span class="identifier">poly_collection</span><span class="special">::</span><span class="identifier">base_collection</span><span class="special">;</span>

<span class="special">}</span> <span class="comment">/* namespace boost */</span>
</pre>
<p>
        Forward declares the class template <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_ba0.class_template_base_collection" title="Class template base_collection"><code class="computeroutput"><span class="identifier">base_collection</span></code></a> and specifies its
        default template arguments. Forward declares associated free functions and
        brings <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">poly_collection</span><span class="special">::</span><span class="identifier">base_collection</span></code> to the <code class="computeroutput"><span class="identifier">boost</span></code>
        namespace.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_ba0"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_ba0" title='Header "boost/poly_collection/base_collection.hpp" synopsis'>Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/base_collection.hpp"</span></code>
      synopsis</a>
</h3></div></div></div>
<div class="toc"><dl class="toc"><dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_ba0.class_template_base_collection">Class
        template <code class="computeroutput"><span class="identifier">base_collection</span></code></a></span></dt></dl></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">poly_collection</span><span class="special">/</span><span class="identifier">base_collection_fwd</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span>

<span class="keyword">namespace</span> <span class="identifier">poly_collection</span><span class="special">{</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Base</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_ba0.class_template_base_collection" title="Class template base_collection"><code class="computeroutput"><span class="identifier">base_collection</span></code></a><span class="special">;</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Base</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span>
  <span class="keyword">const</span> <span class="identifier">base_collection</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="keyword">const</span> <span class="identifier">base_collection</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Base</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span>
  <span class="keyword">const</span> <span class="identifier">base_collection</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="keyword">const</span> <span class="identifier">base_collection</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Base</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span>
  <span class="identifier">base_collection</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="identifier">base_collection</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="special">}</span> <span class="comment">/* namespace poly_collection */</span>

<span class="special">}</span> <span class="comment">/* namespace boost */</span>
</pre>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_ba0.class_template_base_collection"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_ba0.class_template_base_collection" title="Class template base_collection">Class
        template <code class="computeroutput"><span class="identifier">base_collection</span></code></a>
</h4></div></div></div>
<p>
          <code class="computeroutput"><span class="identifier">base_collection</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;</span></code>
          is a <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections" title="Polymorphic collections"><span class="bold"><strong><code class="computeroutput"><span class="identifier">PolymorphicCollection</span></code></strong></span></a>
          associated to the classic base/derived <a class="link" href="reference.html#poly_collection.reference.polymorphism_models" title="Polymorphism models">polymorphism
          model</a>:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              <span class="bold"><strong>Interface</strong></span> = { <code class="computeroutput"><span class="identifier">Base</span></code>
              : <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">is_polymorphic_v</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">&gt;</span></code>
              }.
            </li>
<li class="listitem">
              <span class="bold"><strong>Implementation</strong></span>(<code class="computeroutput"><span class="identifier">Base</span></code>)
              = { <code class="computeroutput"><span class="identifier">Derived</span></code> : <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">is_base_of_v</span><span class="special">&lt;</span><span class="identifier">Base</span><span class="special">,</span><span class="identifier">Derived</span><span class="special">&gt;</span></code>
              }.
            </li>
<li class="listitem">
              <span class="bold"><strong>subobject</strong></span>(<code class="computeroutput"><span class="identifier">x</span></code>)
              = <code class="computeroutput"><span class="keyword">static_cast</span><span class="special">&lt;</span><span class="identifier">Derived</span><span class="special">&amp;&gt;(</span><span class="identifier">x</span><span class="special">)</span></code>
              with <code class="computeroutput"><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">x</span><span class="special">)==</span><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">Derived</span><span class="special">)</span></code>.
            </li>
</ul></div>
<pre class="programlisting"><code class="computeroutput"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Base</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span></code>
<span class="keyword">class</span> <code class="computeroutput"><span class="identifier">base_collection</span></code>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types" title="Types"><span class="emphasis"><em>// types:</em></span></a>

  <span class="keyword">using</span> <span class="identifier">value_type</span><span class="special">=</span><code class="computeroutput"><span class="identifier">Base</span></code><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">allocator_type</span><span class="special">=</span><span class="identifier">Allocator</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">size_type</span><span class="special">=</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">difference_type</span><span class="special">=</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">ptrdiff_t</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">reference</span><span class="special">=</span><span class="identifier">value_type</span><span class="special">&amp;;</span>
  <span class="keyword">using</span> <span class="identifier">const_reference</span><span class="special">=</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;;</span>
  <span class="keyword">using</span> <span class="identifier">pointer</span><span class="special">=</span><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator_traits</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;::</span><span class="identifier">pointer</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">const_pointer</span><span class="special">=</span><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator_traits</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;::</span><span class="identifier">const_pointer</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">iterator</span><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">const_iterator</span><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.local_base_iterator"><code class="computeroutput"><span class="identifier">local_base_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_local_base_iterator"><code class="computeroutput"><span class="identifier">const_local_base_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.local_iterator"><code class="computeroutput"><span class="identifier">local_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_local_iterator"><code class="computeroutput"><span class="identifier">const_local_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_base_segment_info"><code class="computeroutput"><span class="identifier">const_base_segment_info</span></code></a><span class="special">;</span>
  <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.base_segment_info"><code class="computeroutput"><span class="identifier">base_segment_info</span></code></a><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_segment_info"><code class="computeroutput"><span class="identifier">const_segment_info</span></code></a><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.segment_info"><code class="computeroutput"><span class="identifier">segment_info</span></code></a><span class="special">;</span>
  <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.base_segment_info_iterator"><code class="computeroutput"><span class="identifier">base_segment_info_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_base_segment_info_iterator"><code class="computeroutput"><span class="identifier">const_base_segment_info_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_segment_traversal_info"><code class="computeroutput"><span class="identifier">const_segment_traversal_info</span></code></a><span class="special">;</span>
  <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.segment_traversal_info"><code class="computeroutput"><span class="identifier">segment_traversal_info</span></code></a><span class="special">;</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.construct_copy_destroy" title="Construct/copy/destroy"><span class="emphasis"><em>// construct/destroy/copy:</em></span></a>

  <code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">();</span>
  <code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">(</span><span class="keyword">const</span> <code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">&amp;);</span>
  <code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">(</span><code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">&amp;&amp;);</span>
  <span class="keyword">explicit</span> <code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">(</span><span class="keyword">const</span> <span class="identifier">allocator_type</span><span class="special">&amp;</span> <span class="identifier">al</span><span class="special">);</span>
  <code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">(</span><span class="keyword">const</span> <code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">allocator_type</span><span class="special">&amp;</span> <span class="identifier">al</span><span class="special">);</span>
  <code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">(</span><code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">allocator_type</span><span class="special">&amp;</span> <span class="identifier">al</span><span class="special">);</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">&gt;</span>
  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.construct_copy_destroy.range_construction"><code class="computeroutput"><span class="identifier">base_collection</span></code></a><span class="special">(</span>
    <span class="identifier">InputIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">last</span><span class="special">,</span>
    <span class="keyword">const</span> <span class="identifier">allocator_type</span><span class="special">&amp;</span> <span class="identifier">al</span><span class="special">=</span><span class="identifier">allocator_type</span><span class="special">{});</span>

  <code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="keyword">const</span> <code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">&amp;);</span>
  <code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">&amp;&amp;);</span>

  <span class="identifier">allocator_type</span> <span class="identifier">get_allocator</span><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration" title="Type registration"><span class="emphasis"><em>// type registration:</em></span></a>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">T</span><span class="special">&gt;</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration.register_types"><code class="computeroutput"><span class="identifier">register_types</span></code></a><span class="special">();</span>

  <span class="keyword">bool</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration.is_registered"><code class="computeroutput"><span class="identifier">is_registered</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">bool</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration.is_registered"><code class="computeroutput"><span class="identifier">is_registered</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators" title="Iterators"><span class="emphasis"><em>// iterators:</em></span></a>

  <span class="identifier">iterator</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">cbegin</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">cend</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="identifier">local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="identifier">local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="identifier">const_local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">cbegin</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">cend</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">();</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">();</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">cbegin</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">cend</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">base_segment_info</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment"><code class="computeroutput"><span class="identifier">segment</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="identifier">const_base_segment_info</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment"><code class="computeroutput"><span class="identifier">segment</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">segment_info</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment"><code class="computeroutput"><span class="identifier">segment</span></code></a><span class="special">();</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_segment_info</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment"><code class="computeroutput"><span class="identifier">segment</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">segment_traversal_info</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment_traversal"><code class="computeroutput"><span class="identifier">segment_traversal</span></code></a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_segment_traversal_info</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment_traversal"><code class="computeroutput"><span class="identifier">segment_traversal</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity" title="Capacity"><span class="emphasis"><em>// capacity:</em></span></a>

  <span class="keyword">bool</span> <span class="identifier">empty</span><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.empty"><code class="computeroutput"><span class="identifier">empty</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">bool</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.empty"><code class="computeroutput"><span class="identifier">empty</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">size_type</span> <span class="identifier">size</span><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.size"><code class="computeroutput"><span class="identifier">size</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.size"><code class="computeroutput"><span class="identifier">size</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.max_size"><code class="computeroutput"><span class="identifier">max_size</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.max_size"><code class="computeroutput"><span class="identifier">max_size</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.capacity"><code class="computeroutput"><span class="identifier">capacity</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.capacity"><code class="computeroutput"><span class="identifier">capacity</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.reserve"><code class="computeroutput"><span class="identifier">reserve</span></code></a><span class="special">(</span><span class="identifier">size_type</span> <span class="identifier">n</span><span class="special">);</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.reserve"><code class="computeroutput"><span class="identifier">reserve</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">,</span><span class="identifier">size_type</span> <span class="identifier">n</span><span class="special">);</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span><span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.reserve"><code class="computeroutput"><span class="identifier">reserve</span></code></a><span class="special">(</span><span class="identifier">size_type</span> <span class="identifier">n</span><span class="special">);</span>

  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.shrink_to_fit"><code class="computeroutput"><span class="identifier">shrink_to_fit</span></code></a><span class="special">();</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.shrink_to_fit"><code class="computeroutput"><span class="identifier">shrink_to_fit</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.shrink_to_fit"><code class="computeroutput"><span class="identifier">shrink_to_fit</span></code></a><span class="special">();</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers" title="Modifiers"><span class="emphasis"><em>// modifiers:</em></span></a>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span><span class="special">&gt;</span>
  <span class="identifier">iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.emplace"><code class="computeroutput"><span class="identifier">emplace</span></code></a><span class="special">(</span><span class="identifier">Args</span><span class="special">&amp;&amp;...</span> <span class="identifier">args</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span><span class="special">&gt;</span>
  <span class="identifier">iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.emplace"><code class="computeroutput"><span class="identifier">emplace_hint</span></code></a><span class="special">(</span><span class="identifier">const_iterator</span> <span class="identifier">hint</span><span class="special">,</span><span class="identifier">Args</span><span class="special">&amp;&amp;...</span> <span class="identifier">args</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">LocalIterator</span><span class="special">,</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span><span class="special">&gt;</span>
  <span class="keyword">auto</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.emplace_pos"><code class="computeroutput"><span class="identifier">emplace_pos</span></code></a><span class="special">(</span><span class="identifier">LocalIterator</span> <span class="identifier">pos</span><span class="special">,</span><span class="identifier">Args</span><span class="special">&amp;&amp;...</span> <span class="identifier">args</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
  <span class="identifier">iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert"><code class="computeroutput"><span class="identifier">insert</span></code></a><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">CollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
  <span class="keyword">auto</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert"><code class="computeroutput"><span class="identifier">insert</span></code></a><span class="special">(</span><span class="identifier">CollectionIterator</span> <span class="identifier">hint</span><span class="special">,</span><span class="identifier">T</span><span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">&gt;</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert_range"><code class="computeroutput"><span class="identifier">insert</span></code></a><span class="special">(</span><span class="identifier">InputIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">last</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">CollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">&gt;</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert_hint_range"><code class="computeroutput"><span class="identifier">insert</span></code></a><span class="special">(</span><span class="identifier">CollectionIterator</span> <span class="identifier">hint</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">last</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">CollectionIterator</span><span class="special">&gt;</span>
  <span class="keyword">auto</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.erase"><code class="computeroutput"><span class="identifier">erase</span></code></a><span class="special">(</span><span class="identifier">CollectionIterator</span> <span class="identifier">pos</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">CollectionIterator</span><span class="special">&gt;</span>
  <span class="keyword">auto</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.erase"><code class="computeroutput"><span class="identifier">erase</span></code></a><span class="special">(</span><span class="identifier">CollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">CollectionIterator</span> <span class="identifier">last</span><span class="special">);</span>

  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.clear"><code class="computeroutput"><span class="identifier">clear</span></code></a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.clear"><code class="computeroutput"><span class="identifier">clear</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.clear"><code class="computeroutput"><span class="identifier">clear</span></code></a><span class="special">();</span>

  <span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span><code class="computeroutput"><span class="identifier">base_collection</span></code><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
<span class="special">};</span>
</pre>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_fun"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fun" title='Header "boost/poly_collection/function_collection_fwd.hpp" synopsis'>Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/function_collection_fwd.hpp"</span></code>
      synopsis</a>
</h3></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">memory</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span>

<span class="keyword">namespace</span> <span class="identifier">poly_collection</span><span class="special">{</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Signature</span><span class="special">&gt;</span>
<span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti" title="Alias template function_collection_value_type"><code class="computeroutput"><span class="identifier">function_collection_value_type</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span> <span class="identifier">Signature</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">=</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator</span><span class="special">&lt;</span><span class="identifier">function_collection_value_type</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;&gt;</span>
<span class="special">&gt;</span>
<span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.class_template_function_collecti" title="Class template function_collection"><code class="computeroutput"><span class="identifier">function_collection</span></code></a><span class="special">;</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Signature</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span>
  <span class="keyword">const</span> <span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="keyword">const</span> <span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Signature</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span>
  <span class="keyword">const</span> <span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="keyword">const</span> <span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Signature</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span>
  <span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="special">}</span> <span class="comment">/* namespace poly_collection */</span>

<span class="keyword">using</span> <span class="identifier">poly_collection</span><span class="special">::</span><span class="identifier">function_collection</span><span class="special">;</span>

<span class="special">}</span> <span class="comment">/* namespace boost */</span>
</pre>
<p>
        Defines the alias template <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti" title="Alias template function_collection_value_type"><code class="computeroutput"><span class="identifier">function_collection_value_type</span></code></a> (the
        actual type it refers to, though, is merely forward declared). Forward declares
        the class template <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.class_template_function_collecti" title="Class template function_collection"><code class="computeroutput"><span class="identifier">function_collection</span></code></a> and specifies
        its default template arguments. Forward declares associated free functions
        and brings <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">poly_collection</span><span class="special">::</span><span class="identifier">function_collection</span></code> to the <code class="computeroutput"><span class="identifier">boost</span></code> namespace.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_fu0"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0" title='Header "boost/poly_collection/function_collection.hpp" synopsis'>Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/function_collection.hpp"</span></code>
      synopsis</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti">Alias
        template <code class="computeroutput"><span class="identifier">function_collection_value_type</span></code></a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.class_template_function_collecti">Class
        template <code class="computeroutput"><span class="identifier">function_collection</span></code></a></span></dt>
</dl></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">poly_collection</span><span class="special">/</span><span class="identifier">function_collection_fwd</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span>

<span class="keyword">namespace</span> <span class="identifier">poly_collection</span><span class="special">{</span>

<span class="comment">// defines the type <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti" title="Alias template function_collection_value_type"><code class="computeroutput"><span class="identifier">function_collection_value_type</span></code></a> refers to</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Signature</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.class_template_function_collecti" title="Class template function_collection"><code class="computeroutput"><span class="identifier">function_collection</span></code></a><span class="special">;</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Signature</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span>
  <span class="keyword">const</span> <span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="keyword">const</span> <span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Signature</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span>
  <span class="keyword">const</span> <span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="keyword">const</span> <span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Signature</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span>
  <span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="special">}</span> <span class="comment">/* namespace poly_collection */</span>

<span class="special">}</span> <span class="comment">/* namespace boost */</span>
</pre>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti" title="Alias template function_collection_value_type">Alias
        template <code class="computeroutput"><span class="identifier">function_collection_value_type</span></code></a>
</h4></div></div></div>
<p>
          <code class="computeroutput"><span class="identifier">function_collection_value_type</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;</span></code> is the <code class="computeroutput"><span class="identifier">value_type</span></code>
          of <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;</span></code>,
          where <code class="computeroutput"><span class="identifier">Signature</span></code> must be
          a type of the form <code class="computeroutput"><span class="identifier">R</span><span class="special">(</span><span class="identifier">Args</span><span class="special">...)</span></code>.
          <code class="computeroutput"><span class="identifier">function_collection_value_type</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;</span></code> wraps a reference to an object modeling
          <a href="http://en.cppreference.com/w/cpp/named_req/Callable" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">Callable</span></code></strong></span></a>
          for the given <code class="computeroutput"><span class="identifier">Signature</span></code>.
          The interface provided partially replicates that of <a href="http://en.cppreference.com/w/cpp/utility/functional/function" target="_top"><code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">function</span></code></a> and adds some extra
          facilities.
        </p>
<p>
          In what follows, the name <span class="emphasis"><em><code class="computeroutput"><span class="identifier">function_collection_value_type_impl</span></code></em></span>
          is used just for explanatory purposes in place of the actual class template
          name, which is implementation defined.
        </p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Signature</span><span class="special">&gt;</span>
<span class="keyword">using</span> <span class="identifier">function_collection_value_type</span><span class="special">=</span>
  <span class="emphasis"><em>function_collection_value_type_impl</em></span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;;</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Signature</span><span class="special">&gt;</span>
<span class="keyword">class</span> <span class="emphasis"><em>function_collection_value_type_impl</em></span><span class="special">;</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">R</span><span class="special">,</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span><span class="special">&gt;</span>
<span class="keyword">class</span> <span class="emphasis"><em>function_collection_value_type_impl</em></span><span class="special">&lt;</span><span class="identifier">R</span><span class="special">(</span><span class="identifier">Args</span><span class="special">...)&gt;</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="keyword">explicit</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.operator_bool">operator bool</a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="identifier">R</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.operator_call">operator()</a><span class="special">(</span><span class="identifier">Args</span><span class="special">...</span> <span class="identifier">args</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>

  <span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.target_type">target_type</a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">T</span><span class="special">*</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.target">target</a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">*</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.target">target</a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="keyword">operator</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.operator_std_function">std::function&lt;R(Args...)&gt;</a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="keyword">void</span><span class="special">*</span>       <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.data">data</a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">const</span> <span class="keyword">void</span><span class="special">*</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.data">data</a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
<span class="special">};</span>
</pre>
<p>
          <a name="poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.operator_bool"></a><code class="computeroutput"><span class="keyword">explicit</span> <span class="keyword">operator</span>
          <span class="keyword">bool</span><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></code>
        </p>
<p>
          <span class="bold"><strong>Returns:</strong></span> <code class="computeroutput"><span class="keyword">true</span></code>.
        </p>
<p>
          <a name="poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.operator_call"></a><code class="computeroutput"><span class="identifier">R</span> <span class="keyword">operator</span><span class="special">()(</span><span class="identifier">Args</span><span class="special">...</span> <span class="identifier">args</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span></code>
        </p>
<p>
          <span class="bold"><strong>Effects:</strong></span> <code class="computeroutput"><a href="http://en.cppreference.com/w/cpp/utility/functional/invoke" target="_top"><span class="emphasis"><em><span class="bold"><strong><code class="computeroutput"><span class="identifier">INVOKE</span></code></strong></span></em></span></a><span class="special">(</span><span class="identifier">f</span><span class="special">,</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">forward</span><span class="special">&lt;</span><span class="identifier">Args</span><span class="special">&gt;(</span><span class="identifier">args</span><span class="special">)...,</span><span class="identifier">R</span><span class="special">)</span></code>, where
          f is the wrapped callable object.<br> <span class="bold"><strong>Returns:</strong></span>
          Nothing if <code class="computeroutput"><span class="identifier">R</span></code> is <code class="computeroutput"><span class="keyword">void</span></code>, otherwise the return value of <code class="computeroutput"><a href="http://en.cppreference.com/w/cpp/utility/functional/invoke" target="_top"><span class="emphasis"><em><span class="bold"><strong><code class="computeroutput"><span class="identifier">INVOKE</span></code></strong></span></em></span></a><span class="special">(</span><span class="identifier">f</span><span class="special">,</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">forward</span><span class="special">&lt;</span><span class="identifier">Args</span><span class="special">&gt;(</span><span class="identifier">args</span><span class="special">)...,</span><span class="identifier">R</span><span class="special">)</span></code>.
        </p>
<p>
          <a name="poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.target_type"></a><code class="computeroutput"><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">target_type</span><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></code>
        </p>
<p>
          <span class="bold"><strong>Returns:</strong></span> <code class="computeroutput"><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">T</span><span class="special">)</span></code>
          where <code class="computeroutput"><span class="identifier">T</span></code> is the type of
          the wrapped callable object.
        </p>
<p>
          <a name="poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.target"></a><code class="computeroutput"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">T</span><span class="special">*</span> <span class="identifier">target</span><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span></code><br> <code class="computeroutput"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">*</span> <span class="identifier">target</span><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></code>
        </p>
<p>
          <span class="bold"><strong>Returns:</strong></span> If <code class="computeroutput"><span class="identifier">target_type</span><span class="special">()==</span><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">T</span><span class="special">)</span></code>
          a pointer to the wrapped callable object, otherwise <code class="computeroutput"><span class="keyword">nullptr</span></code>.
        </p>
<p>
          <a name="poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.operator_std_function"></a><code class="computeroutput"><span class="keyword">operator</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">function</span><span class="special">&lt;</span><span class="identifier">R</span><span class="special">(</span><span class="identifier">Args</span><span class="special">...)&gt;()</span><span class="keyword">const</span>
          <span class="keyword">noexcept</span><span class="special">;</span></code>
        </p>
<p>
          <span class="bold"><strong>Returns:</strong></span> A <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">function</span><span class="special">&lt;</span><span class="identifier">R</span><span class="special">(</span><span class="identifier">Args</span><span class="special">...)&gt;</span></code> object holding a reference to the
          wrapped callable object.
        </p>
<p>
          <a name="poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti.data"></a><code class="computeroutput"><span class="keyword">void</span><span class="special">*</span> <span class="identifier">data</span><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span></code><br> <code class="computeroutput"><span class="keyword">const</span>
          <span class="keyword">void</span><span class="special">*</span>
          <span class="identifier">data</span><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></code>
        </p>
<p>
          <span class="bold"><strong>Returns:</strong></span> The address of the wrapped callable
          object.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_fu0.class_template_function_collecti"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.class_template_function_collecti" title="Class template function_collection">Class
        template <code class="computeroutput"><span class="identifier">function_collection</span></code></a>
</h4></div></div></div>
<p>
          <code class="computeroutput"><span class="identifier">function_collection</span><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;</span></code>
          is a <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections" title="Polymorphic collections"><span class="bold"><strong><code class="computeroutput"><span class="identifier">PolymorphicCollection</span></code></strong></span></a>
          associated to a dynamic <a class="link" href="reference.html#poly_collection.reference.polymorphism_models" title="Polymorphism models">polymorphism
          model</a> based on call signature compatibility:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              <span class="bold"><strong>Interface</strong></span> = { <code class="computeroutput"><span class="identifier">Signature</span></code>
              : <code class="computeroutput"><span class="identifier">Signature</span></code> = <code class="computeroutput"><span class="identifier">R</span><span class="special">(</span><span class="identifier">Args</span><span class="special">...)</span></code>
              }.
            </li>
<li class="listitem">
              <span class="bold"><strong>Implementation</strong></span>(<code class="computeroutput"><span class="identifier">Signature</span></code>)
              = { <code class="computeroutput"><span class="identifier">Callable</span></code> : <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">is_invocable_r_v</span><span class="special">&lt;</span><span class="identifier">R</span><span class="special">,</span><span class="identifier">Callable</span><span class="special">,</span><span class="identifier">Args</span><span class="special">...&gt;</span></code>
              }.
            </li>
<li class="listitem">
              <span class="bold"><strong>subobject</strong></span>(<code class="computeroutput"><span class="identifier">x</span></code>)
              =<br> <code class="computeroutput"><span class="identifier">x</span><span class="special">.</span><span class="identifier">target</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;()</span></code>
              with <code class="computeroutput"><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">T</span><span class="special">)==</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">target_type</span><span class="special">()</span></code>,
              if <code class="computeroutput"><span class="identifier">x</span></code> is an instantiation
              of <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti" title="Alias template function_collection_value_type"><code class="computeroutput"><span class="identifier">function_collection_value_type</span></code></a>,<br>
              <code class="computeroutput"><span class="identifier">x</span></code>, otherwise.
            </li>
</ul></div>
<pre class="programlisting"><code class="computeroutput"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Signature</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span></code>
<span class="keyword">class</span> <code class="computeroutput"><span class="identifier">function_collection</span></code>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types" title="Types"><span class="emphasis"><em>// types:</em></span></a>

  <span class="keyword">using</span> <span class="identifier">value_type</span><span class="special">=</span><code class="computeroutput"><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_fu0.alias_template_function_collecti" title="Alias template function_collection_value_type"><code class="computeroutput"><span class="identifier">function_collection_value_type</span></code></a><span class="special">&lt;</span><span class="identifier">Signature</span><span class="special">&gt;</span></code><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">allocator_type</span><span class="special">=</span><span class="identifier">Allocator</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">size_type</span><span class="special">=</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">difference_type</span><span class="special">=</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">ptrdiff_t</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">reference</span><span class="special">=</span><span class="identifier">value_type</span><span class="special">&amp;;</span>
  <span class="keyword">using</span> <span class="identifier">const_reference</span><span class="special">=</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;;</span>
  <span class="keyword">using</span> <span class="identifier">pointer</span><span class="special">=</span><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator_traits</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;::</span><span class="identifier">pointer</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">const_pointer</span><span class="special">=</span><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator_traits</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;::</span><span class="identifier">const_pointer</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">iterator</span><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">const_iterator</span><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.local_base_iterator"><code class="computeroutput"><span class="identifier">local_base_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_local_base_iterator"><code class="computeroutput"><span class="identifier">const_local_base_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.local_iterator"><code class="computeroutput"><span class="identifier">local_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_local_iterator"><code class="computeroutput"><span class="identifier">const_local_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_base_segment_info"><code class="computeroutput"><span class="identifier">const_base_segment_info</span></code></a><span class="special">;</span>
  <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.base_segment_info"><code class="computeroutput"><span class="identifier">base_segment_info</span></code></a><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_segment_info"><code class="computeroutput"><span class="identifier">const_segment_info</span></code></a><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.segment_info"><code class="computeroutput"><span class="identifier">segment_info</span></code></a><span class="special">;</span>
  <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.base_segment_info_iterator"><code class="computeroutput"><span class="identifier">base_segment_info_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_base_segment_info_iterator"><code class="computeroutput"><span class="identifier">const_base_segment_info_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_segment_traversal_info"><code class="computeroutput"><span class="identifier">const_segment_traversal_info</span></code></a><span class="special">;</span>
  <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.segment_traversal_info"><code class="computeroutput"><span class="identifier">segment_traversal_info</span></code></a><span class="special">;</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.construct_copy_destroy" title="Construct/copy/destroy"><span class="emphasis"><em>// construct/destroy/copy:</em></span></a>

  <code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">();</span>
  <code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">(</span><span class="keyword">const</span> <code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">&amp;);</span>
  <code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">(</span><code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">&amp;&amp;);</span>
  <span class="keyword">explicit</span> <code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">(</span><span class="keyword">const</span> <span class="identifier">allocator_type</span><span class="special">&amp;</span> <span class="identifier">al</span><span class="special">);</span>
  <code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">(</span><span class="keyword">const</span> <code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">allocator_type</span><span class="special">&amp;</span> <span class="identifier">al</span><span class="special">);</span>
  <code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">(</span><code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">allocator_type</span><span class="special">&amp;</span> <span class="identifier">al</span><span class="special">);</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">&gt;</span>
  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.construct_copy_destroy.range_construction"><code class="computeroutput"><span class="identifier">function_collection</span></code></a><span class="special">(</span>
    <span class="identifier">InputIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">last</span><span class="special">,</span>
    <span class="keyword">const</span> <span class="identifier">allocator_type</span><span class="special">&amp;</span> <span class="identifier">al</span><span class="special">=</span><span class="identifier">allocator_type</span><span class="special">{});</span>

  <code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="keyword">const</span> <code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">&amp;);</span>
  <code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">&amp;&amp;);</span>

  <span class="identifier">allocator_type</span> <span class="identifier">get_allocator</span><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration" title="Type registration"><span class="emphasis"><em>// type registration:</em></span></a>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">T</span><span class="special">&gt;</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration.register_types"><code class="computeroutput"><span class="identifier">register_types</span></code></a><span class="special">();</span>

  <span class="keyword">bool</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration.is_registered"><code class="computeroutput"><span class="identifier">is_registered</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">bool</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration.is_registered"><code class="computeroutput"><span class="identifier">is_registered</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators" title="Iterators"><span class="emphasis"><em>// iterators:</em></span></a>

  <span class="identifier">iterator</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">cbegin</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">cend</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="identifier">local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="identifier">local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="identifier">const_local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">cbegin</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">cend</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">();</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">();</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">cbegin</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">cend</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">base_segment_info</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment"><code class="computeroutput"><span class="identifier">segment</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="identifier">const_base_segment_info</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment"><code class="computeroutput"><span class="identifier">segment</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">segment_info</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment"><code class="computeroutput"><span class="identifier">segment</span></code></a><span class="special">();</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_segment_info</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment"><code class="computeroutput"><span class="identifier">segment</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">segment_traversal_info</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment_traversal"><code class="computeroutput"><span class="identifier">segment_traversal</span></code></a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_segment_traversal_info</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment_traversal"><code class="computeroutput"><span class="identifier">segment_traversal</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity" title="Capacity"><span class="emphasis"><em>// capacity:</em></span></a>

  <span class="keyword">bool</span> <span class="identifier">empty</span><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.empty"><code class="computeroutput"><span class="identifier">empty</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">bool</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.empty"><code class="computeroutput"><span class="identifier">empty</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">size_type</span> <span class="identifier">size</span><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.size"><code class="computeroutput"><span class="identifier">size</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.size"><code class="computeroutput"><span class="identifier">size</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.max_size"><code class="computeroutput"><span class="identifier">max_size</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.max_size"><code class="computeroutput"><span class="identifier">max_size</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.capacity"><code class="computeroutput"><span class="identifier">capacity</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.capacity"><code class="computeroutput"><span class="identifier">capacity</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.reserve"><code class="computeroutput"><span class="identifier">reserve</span></code></a><span class="special">(</span><span class="identifier">size_type</span> <span class="identifier">n</span><span class="special">);</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.reserve"><code class="computeroutput"><span class="identifier">reserve</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">,</span><span class="identifier">size_type</span> <span class="identifier">n</span><span class="special">);</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span><span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.reserve"><code class="computeroutput"><span class="identifier">reserve</span></code></a><span class="special">(</span><span class="identifier">size_type</span> <span class="identifier">n</span><span class="special">);</span>

  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.shrink_to_fit"><code class="computeroutput"><span class="identifier">shrink_to_fit</span></code></a><span class="special">();</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.shrink_to_fit"><code class="computeroutput"><span class="identifier">shrink_to_fit</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.shrink_to_fit"><code class="computeroutput"><span class="identifier">shrink_to_fit</span></code></a><span class="special">();</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers" title="Modifiers"><span class="emphasis"><em>// modifiers:</em></span></a>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span><span class="special">&gt;</span>
  <span class="identifier">iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.emplace"><code class="computeroutput"><span class="identifier">emplace</span></code></a><span class="special">(</span><span class="identifier">Args</span><span class="special">&amp;&amp;...</span> <span class="identifier">args</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span><span class="special">&gt;</span>
  <span class="identifier">iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.emplace"><code class="computeroutput"><span class="identifier">emplace_hint</span></code></a><span class="special">(</span><span class="identifier">const_iterator</span> <span class="identifier">hint</span><span class="special">,</span><span class="identifier">Args</span><span class="special">&amp;&amp;...</span> <span class="identifier">args</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">LocalIterator</span><span class="special">,</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span><span class="special">&gt;</span>
  <span class="keyword">auto</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.emplace_pos"><code class="computeroutput"><span class="identifier">emplace_pos</span></code></a><span class="special">(</span><span class="identifier">LocalIterator</span> <span class="identifier">pos</span><span class="special">,</span><span class="identifier">Args</span><span class="special">&amp;&amp;...</span> <span class="identifier">args</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
  <span class="identifier">iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert"><code class="computeroutput"><span class="identifier">insert</span></code></a><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">CollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
  <span class="keyword">auto</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert"><code class="computeroutput"><span class="identifier">insert</span></code></a><span class="special">(</span><span class="identifier">CollectionIterator</span> <span class="identifier">hint</span><span class="special">,</span><span class="identifier">T</span><span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">&gt;</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert_range"><code class="computeroutput"><span class="identifier">insert</span></code></a><span class="special">(</span><span class="identifier">InputIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">last</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">CollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">&gt;</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert_hint_range"><code class="computeroutput"><span class="identifier">insert</span></code></a><span class="special">(</span><span class="identifier">CollectionIterator</span> <span class="identifier">hint</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">last</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">CollectionIterator</span><span class="special">&gt;</span>
  <span class="keyword">auto</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.erase"><code class="computeroutput"><span class="identifier">erase</span></code></a><span class="special">(</span><span class="identifier">CollectionIterator</span> <span class="identifier">pos</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">CollectionIterator</span><span class="special">&gt;</span>
  <span class="keyword">auto</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.erase"><code class="computeroutput"><span class="identifier">erase</span></code></a><span class="special">(</span><span class="identifier">CollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">CollectionIterator</span> <span class="identifier">last</span><span class="special">);</span>

  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.clear"><code class="computeroutput"><span class="identifier">clear</span></code></a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.clear"><code class="computeroutput"><span class="identifier">clear</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.clear"><code class="computeroutput"><span class="identifier">clear</span></code></a><span class="special">();</span>

  <span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span><code class="computeroutput"><span class="identifier">function_collection</span></code><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
<span class="special">};</span>
</pre>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_any"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_any" title='Header "boost/poly_collection/any_collection_fwd.hpp" synopsis'>Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/any_collection_fwd.hpp"</span></code>
      synopsis</a>
</h3></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">memory</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span>

<span class="keyword">namespace</span> <span class="identifier">poly_collection</span><span class="special">{</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Concept</span><span class="special">&gt;</span>
<span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_an0.alias_template_any_collection_va" title="Alias template any_collection_value_type"><code class="computeroutput"><span class="identifier">any_collection_value_type</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span> <span class="identifier">Concept</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">=</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator</span><span class="special">&lt;</span><span class="identifier">any_collection_value_type</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">&gt;&gt;</span>
<span class="special">&gt;</span>
<span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_an0.class_template_any_collection" title="Class template any_collection"><code class="computeroutput"><span class="identifier">any_collection</span></code></a><span class="special">;</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Concept</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span>
  <span class="keyword">const</span> <span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="keyword">const</span> <span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Concept</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span>
  <span class="keyword">const</span> <span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="keyword">const</span> <span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Concept</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span>
  <span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="special">}</span> <span class="comment">/* namespace poly_collection */</span>

<span class="keyword">using</span> <span class="identifier">poly_collection</span><span class="special">::</span><span class="identifier">any_collection</span><span class="special">;</span>

<span class="special">}</span> <span class="comment">/* namespace boost */</span>
</pre>
<p>
        Defines the alias template <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_an0.alias_template_any_collection_va" title="Alias template any_collection_value_type"><code class="computeroutput"><span class="identifier">any_collection_value_type</span></code></a> (the actual
        type it refers to, though, is merely forward declared). Forward declares
        the class template <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_an0.class_template_any_collection" title="Class template any_collection"><code class="computeroutput"><span class="identifier">any_collection</span></code></a> and specifies its
        default template arguments. Forward declares associated free functions and
        brings <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">poly_collection</span><span class="special">::</span><span class="identifier">any_collection</span></code> to the <code class="computeroutput"><span class="identifier">boost</span></code>
        namespace.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_an0"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_an0" title='Header "boost/poly_collection/any_collection.hpp" synopsis'>Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/any_collection.hpp"</span></code>
      synopsis</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_an0.alias_template_any_collection_va">Alias
        template <code class="computeroutput"><span class="identifier">any_collection_value_type</span></code></a></span></dt>
<dt><span class="section"><a href="reference.html#poly_collection.reference.header_boost_poly_collection_an0.class_template_any_collection">Class
        template <code class="computeroutput"><span class="identifier">any_collection</span></code></a></span></dt>
</dl></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">poly_collection</span><span class="special">/</span><span class="identifier">any_collection_fwd</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span>

<span class="keyword">namespace</span> <span class="identifier">poly_collection</span><span class="special">{</span>

<span class="comment">// defines the type <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_an0.alias_template_any_collection_va" title="Alias template any_collection_value_type"><code class="computeroutput"><span class="identifier">any_collection_value_type</span></code></a> refers to</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Concept</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_an0.class_template_any_collection" title="Class template any_collection"><code class="computeroutput"><span class="identifier">any_collection</span></code></a><span class="special">;</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Concept</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span>
  <span class="keyword">const</span> <span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="keyword">const</span> <span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Concept</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span>
  <span class="keyword">const</span> <span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span>
  <span class="keyword">const</span> <span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Concept</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span>
<span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span>
  <span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">);</span>

<span class="special">}</span> <span class="comment">/* namespace poly_collection */</span>

<span class="special">}</span> <span class="comment">/* namespace boost */</span>
</pre>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_an0.alias_template_any_collection_va"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_an0.alias_template_any_collection_va" title="Alias template any_collection_value_type">Alias
        template <code class="computeroutput"><span class="identifier">any_collection_value_type</span></code></a>
</h4></div></div></div>
<p>
          <code class="computeroutput"><span class="identifier">any_collection_value_type</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">&gt;</span></code> is the <code class="computeroutput"><span class="identifier">value_type</span></code>
          of <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;</span></code>,
          where <code class="computeroutput"><span class="identifier">Concept</span></code> is defined
          according to the <a href="../../../doc/html/boost_typeerasure/conceptdef.html" target="_top">requisites</a>
          of <a href="../../../libs/type_erasure" target="_top">Boost.TypeErasure</a> using
          <a href="../../../doc/html/boost/type_erasure/_self.html" target="_top"><code class="computeroutput"><span class="identifier">_self</span></code></a> as its <a href="../../../doc/html/boost/type_erasure/placeholder.html" target="_top">placeholder</a>.
          The alias template definition has the form
        </p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Concept</span><span class="special">&gt;</span>
<span class="keyword">using</span> <span class="identifier">any_collection_value_type</span><span class="special">=</span>
  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">type_erasure</span><span class="special">::</span><a href="../../../doc/html/boost/type_erasure/any.html" target="_top">any</a><span class="special">&lt;</span><span class="identifier">Concept2</span><span class="special">,</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">type_erasure</span><span class="special">::</span><span class="identifier">_self</span><span class="special">&amp;&gt;;</span>
</pre>
<p>
          with <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">type_erasure</span><span class="special">::</span></code><a href="../../../doc/html/boost/type_erasure/is_subconcept.html" target="_top"><code class="computeroutput"><span class="identifier">is_subconcept</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Concept2</span><span class="special">&gt;::</span><span class="identifier">value</span><span class="special">==</span><span class="keyword">true</span></code>. The exact definition of <code class="computeroutput"><span class="identifier">Concept2</span></code> is implementation defined.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_an0.class_template_any_collection"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_an0.class_template_any_collection" title="Class template any_collection">Class
        template <code class="computeroutput"><span class="identifier">any_collection</span></code></a>
</h4></div></div></div>
<p>
          <code class="computeroutput"><span class="identifier">any_collection</span><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">,</span><span class="identifier">Allocator</span><span class="special">&gt;</span></code>
          is a <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections" title="Polymorphic collections"><span class="bold"><strong><code class="computeroutput"><span class="identifier">PolymorphicCollection</span></code></strong></span></a>
          associated to a dynamic <a class="link" href="reference.html#poly_collection.reference.polymorphism_models" title="Polymorphism models">polymorphism
          model</a> based on <a href="https://en.wikipedia.org/wiki/Duck_typing" target="_top"><span class="emphasis"><em>duck
          typing</em></span></a> as implemented by <a href="../../../libs/type_erasure" target="_top">Boost.TypeErasure</a>:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              <span class="bold"><strong>Interface</strong></span> = { <code class="computeroutput"><span class="identifier">Concept</span></code>
              : as <a href="../../../doc/html/boost_typeerasure/conceptdef.html" target="_top">specified</a>
              by <a href="../../../libs/type_erasure" target="_top">Boost.TypeErasure</a>,
              using the <a href="../../../doc/html/boost/type_erasure/_self.html" target="_top"><code class="computeroutput"><span class="identifier">_self</span></code></a> <a href="../../../doc/html/boost/type_erasure/placeholder.html" target="_top">placeholder</a>
              }.
            </li>
<li class="listitem">
              <span class="bold"><strong>Implementation</strong></span>(<code class="computeroutput"><span class="identifier">Concept</span></code>)
              = { <code class="computeroutput"><span class="identifier">Concrete</span></code> : <code class="computeroutput"><span class="identifier">Concrete</span></code> satisfies <code class="computeroutput"><span class="identifier">Concept</span></code> }.
            </li>
<li class="listitem">
              <span class="bold"><strong>subobject</strong></span>(<code class="computeroutput"><span class="identifier">x</span></code>)
              =<br> <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">type_erasure</span><span class="special">::</span></code><a href="../../../doc/html/boost/type_erasure/any_cast.html" target="_top"><code class="computeroutput"><span class="identifier">any_cast</span></code></a><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&amp;&gt;(</span><span class="identifier">x</span><span class="special">)</span></code>
              with <code class="computeroutput"><span class="keyword">typeid</span><span class="special">(</span><span class="identifier">T</span><span class="special">)==</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">type_erasure</span><span class="special">::</span></code><a href="../../../doc/html/boost/type_erasure/typeid_of.html" target="_top"><code class="computeroutput"><span class="identifier">typeid_of</span></code></a><code class="computeroutput"><span class="special">(</span><span class="identifier">x</span><span class="special">)</span></code>,
              if <code class="computeroutput"><span class="identifier">x</span></code> is an instantiation
              of <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">type_erasure</span><span class="special">::</span></code><a href="../../../doc/html/boost/type_erasure/any.html" target="_top"><code class="computeroutput"><span class="identifier">any</span></code></a>
              including <a href="../../../doc/html/boost/type_erasure/typeid_.html" target="_top"><code class="computeroutput"><span class="identifier">typeid_</span></code></a><code class="computeroutput"><span class="special">&lt;&gt;</span></code>,<br>
              <code class="computeroutput"><span class="identifier">x</span></code>, otherwise.
            </li>
</ul></div>
<pre class="programlisting"><code class="computeroutput"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Concept</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Allocator</span><span class="special">&gt;</span></code>
<span class="keyword">class</span> <code class="computeroutput"><span class="identifier">any_collection</span></code>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types" title="Types"><span class="emphasis"><em>// types:</em></span></a>

  <span class="keyword">using</span> <span class="identifier">value_type</span><span class="special">=</span><code class="computeroutput"><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_an0.alias_template_any_collection_va" title="Alias template any_collection_value_type"><code class="computeroutput"><span class="identifier">any_collection_value_type</span></code></a><span class="special">&lt;</span><span class="identifier">Concept</span><span class="special">&gt;</span></code><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">allocator_type</span><span class="special">=</span><span class="identifier">Allocator</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">size_type</span><span class="special">=</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">difference_type</span><span class="special">=</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">ptrdiff_t</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">reference</span><span class="special">=</span><span class="identifier">value_type</span><span class="special">&amp;;</span>
  <span class="keyword">using</span> <span class="identifier">const_reference</span><span class="special">=</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;;</span>
  <span class="keyword">using</span> <span class="identifier">pointer</span><span class="special">=</span><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator_traits</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;::</span><span class="identifier">pointer</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">const_pointer</span><span class="special">=</span><span class="keyword">typename</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">allocator_traits</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;::</span><span class="identifier">const_pointer</span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">iterator</span><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">using</span> <span class="identifier">const_iterator</span><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.local_base_iterator"><code class="computeroutput"><span class="identifier">local_base_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_local_base_iterator"><code class="computeroutput"><span class="identifier">const_local_base_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.local_iterator"><code class="computeroutput"><span class="identifier">local_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_local_iterator"><code class="computeroutput"><span class="identifier">const_local_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_base_segment_info"><code class="computeroutput"><span class="identifier">const_base_segment_info</span></code></a><span class="special">;</span>
  <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.base_segment_info"><code class="computeroutput"><span class="identifier">base_segment_info</span></code></a><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_segment_info"><code class="computeroutput"><span class="identifier">const_segment_info</span></code></a><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.segment_info"><code class="computeroutput"><span class="identifier">segment_info</span></code></a><span class="special">;</span>
  <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.base_segment_info_iterator"><code class="computeroutput"><span class="identifier">base_segment_info_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">using</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_base_segment_info_iterator"><code class="computeroutput"><span class="identifier">const_base_segment_info_iterator</span></code></a><span class="special">=</span><span class="emphasis"><em>implementation-defined</em></span><span class="special">;</span>
  <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.const_segment_traversal_info"><code class="computeroutput"><span class="identifier">const_segment_traversal_info</span></code></a><span class="special">;</span>
  <span class="keyword">class</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.types.segment_traversal_info"><code class="computeroutput"><span class="identifier">segment_traversal_info</span></code></a><span class="special">;</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.construct_copy_destroy" title="Construct/copy/destroy"><span class="emphasis"><em>// construct/destroy/copy:</em></span></a>

  <code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">();</span>
  <code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">(</span><span class="keyword">const</span> <code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">&amp;);</span>
  <code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">(</span><code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">&amp;&amp;);</span>
  <span class="keyword">explicit</span> <code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">(</span><span class="keyword">const</span> <span class="identifier">allocator_type</span><span class="special">&amp;</span> <span class="identifier">al</span><span class="special">);</span>
  <code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">(</span><span class="keyword">const</span> <code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">allocator_type</span><span class="special">&amp;</span> <span class="identifier">al</span><span class="special">);</span>
  <code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">(</span><code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">allocator_type</span><span class="special">&amp;</span> <span class="identifier">al</span><span class="special">);</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">&gt;</span>
  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.construct_copy_destroy.range_construction"><code class="computeroutput"><span class="identifier">any_collection</span></code></a><span class="special">(</span>
    <span class="identifier">InputIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">last</span><span class="special">,</span>
    <span class="keyword">const</span> <span class="identifier">allocator_type</span><span class="special">&amp;</span> <span class="identifier">al</span><span class="special">=</span><span class="identifier">allocator_type</span><span class="special">{});</span>

  <code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="keyword">const</span> <code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">&amp;);</span>
  <code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">&amp;&amp;);</span>

  <span class="identifier">allocator_type</span> <span class="identifier">get_allocator</span><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration" title="Type registration"><span class="emphasis"><em>// type registration:</em></span></a>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">T</span><span class="special">&gt;</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration.register_types"><code class="computeroutput"><span class="identifier">register_types</span></code></a><span class="special">();</span>

  <span class="keyword">bool</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration.is_registered"><code class="computeroutput"><span class="identifier">is_registered</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">bool</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.type_registration.is_registered"><code class="computeroutput"><span class="identifier">is_registered</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators" title="Iterators"><span class="emphasis"><em>// iterators:</em></span></a>

  <span class="identifier">iterator</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">cbegin</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">cend</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="identifier">local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="identifier">local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="identifier">const_local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">cbegin</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">const_local_base_iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">cend</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">();</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">();</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">begin</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">end</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.begin"><code class="computeroutput"><span class="identifier">cbegin</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_local_iterator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.end"><code class="computeroutput"><span class="identifier">cend</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">base_segment_info</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment"><code class="computeroutput"><span class="identifier">segment</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="identifier">const_base_segment_info</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment"><code class="computeroutput"><span class="identifier">segment</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">segment_info</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment"><code class="computeroutput"><span class="identifier">segment</span></code></a><span class="special">();</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">const_segment_info</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment"><code class="computeroutput"><span class="identifier">segment</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">segment_traversal_info</span>       <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment_traversal"><code class="computeroutput"><span class="identifier">segment_traversal</span></code></a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_segment_traversal_info</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.iterators.segment_traversal"><code class="computeroutput"><span class="identifier">segment_traversal</span></code></a><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity" title="Capacity"><span class="emphasis"><em>// capacity:</em></span></a>

  <span class="keyword">bool</span> <span class="identifier">empty</span><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.empty"><code class="computeroutput"><span class="identifier">empty</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">bool</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.empty"><code class="computeroutput"><span class="identifier">empty</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">size_type</span> <span class="identifier">size</span><span class="special">()</span><span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.size"><code class="computeroutput"><span class="identifier">size</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.size"><code class="computeroutput"><span class="identifier">size</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.max_size"><code class="computeroutput"><span class="identifier">max_size</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.max_size"><code class="computeroutput"><span class="identifier">max_size</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.capacity"><code class="computeroutput"><span class="identifier">capacity</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">)</span><span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="identifier">size_type</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.capacity"><code class="computeroutput"><span class="identifier">capacity</span></code></a><span class="special">()</span><span class="keyword">const</span><span class="special">;</span>

  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.reserve"><code class="computeroutput"><span class="identifier">reserve</span></code></a><span class="special">(</span><span class="identifier">size_type</span> <span class="identifier">n</span><span class="special">);</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.reserve"><code class="computeroutput"><span class="identifier">reserve</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">,</span><span class="identifier">size_type</span> <span class="identifier">n</span><span class="special">);</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span><span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.reserve"><code class="computeroutput"><span class="identifier">reserve</span></code></a><span class="special">(</span><span class="identifier">size_type</span> <span class="identifier">n</span><span class="special">);</span>

  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.shrink_to_fit"><code class="computeroutput"><span class="identifier">shrink_to_fit</span></code></a><span class="special">();</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.shrink_to_fit"><code class="computeroutput"><span class="identifier">shrink_to_fit</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.capacity.shrink_to_fit"><code class="computeroutput"><span class="identifier">shrink_to_fit</span></code></a><span class="special">();</span>

  <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers" title="Modifiers"><span class="emphasis"><em>// modifiers:</em></span></a>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span><span class="special">&gt;</span>
  <span class="identifier">iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.emplace"><code class="computeroutput"><span class="identifier">emplace</span></code></a><span class="special">(</span><span class="identifier">Args</span><span class="special">&amp;&amp;...</span> <span class="identifier">args</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span><span class="special">&gt;</span>
  <span class="identifier">iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.emplace"><code class="computeroutput"><span class="identifier">emplace_hint</span></code></a><span class="special">(</span><span class="identifier">const_iterator</span> <span class="identifier">hint</span><span class="special">,</span><span class="identifier">Args</span><span class="special">&amp;&amp;...</span> <span class="identifier">args</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">LocalIterator</span><span class="special">,</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Args</span><span class="special">&gt;</span>
  <span class="keyword">auto</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.emplace_pos"><code class="computeroutput"><span class="identifier">emplace_pos</span></code></a><span class="special">(</span><span class="identifier">LocalIterator</span> <span class="identifier">pos</span><span class="special">,</span><span class="identifier">Args</span><span class="special">&amp;&amp;...</span> <span class="identifier">args</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
  <span class="identifier">iterator</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert"><code class="computeroutput"><span class="identifier">insert</span></code></a><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">CollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
  <span class="keyword">auto</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert"><code class="computeroutput"><span class="identifier">insert</span></code></a><span class="special">(</span><span class="identifier">CollectionIterator</span> <span class="identifier">hint</span><span class="special">,</span><span class="identifier">T</span><span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">&gt;</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert_range"><code class="computeroutput"><span class="identifier">insert</span></code></a><span class="special">(</span><span class="identifier">InputIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">last</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">CollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">&gt;</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.insert_hint_range"><code class="computeroutput"><span class="identifier">insert</span></code></a><span class="special">(</span><span class="identifier">CollectionIterator</span> <span class="identifier">hint</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">last</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">CollectionIterator</span><span class="special">&gt;</span>
  <span class="keyword">auto</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.erase"><code class="computeroutput"><span class="identifier">erase</span></code></a><span class="special">(</span><span class="identifier">CollectionIterator</span> <span class="identifier">pos</span><span class="special">);</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">CollectionIterator</span><span class="special">&gt;</span>
  <span class="keyword">auto</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.erase"><code class="computeroutput"><span class="identifier">erase</span></code></a><span class="special">(</span><span class="identifier">CollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">CollectionIterator</span> <span class="identifier">last</span><span class="special">);</span>

  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.clear"><code class="computeroutput"><span class="identifier">clear</span></code></a><span class="special">()</span><span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.clear"><code class="computeroutput"><span class="identifier">clear</span></code></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">type_info</span><span class="special">&amp;</span> <span class="identifier">info</span><span class="special">);</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="reference.html#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.clear"><code class="computeroutput"><span class="identifier">clear</span></code></a><span class="special">();</span>

  <span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span><code class="computeroutput"><span class="identifier">any_collection</span></code><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
<span class="special">};</span>
</pre>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="poly_collection.reference.header_boost_poly_collection_alg"></a><a class="link" href="reference.html#poly_collection.reference.header_boost_poly_collection_alg" title='Header "boost/poly_collection/algorithm.hpp" synopsis'>Header
      <code class="computeroutput"><span class="string">"boost/poly_collection/algorithm.hpp"</span></code>
      synopsis</a>
</h3></div></div></div>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">{</span>

<span class="keyword">namespace</span> <span class="identifier">poly_collection</span><span class="special">{</span>

<span class="emphasis"><em><code class="computeroutput"><span class="comment">// non-modifying sequence operations:</span></code></em></span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Predicate</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">all_of</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span><span class="identifier">Predicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Predicate</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">any_of</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span><span class="identifier">Predicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Predicate</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">none_of</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span><span class="identifier">Predicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Function</span><span class="special">&gt;</span>
<span class="identifier">Function</span> <span class="identifier">for_each</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span><span class="identifier">Function</span> <span class="identifier">f</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">Size</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Function</span>
<span class="special">&gt;</span>
<span class="identifier">Iterator</span> <span class="identifier">for_each_n</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">Size</span> <span class="identifier">n</span><span class="special">,</span><span class="identifier">Function</span> <span class="identifier">f</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">find</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Predicate</span><span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">find_if</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span><span class="identifier">Predicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Predicate</span><span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">find_if_not</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span><span class="identifier">Predicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">ForwardIterator</span>
<span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">find_end</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">ForwardIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">ForwardIterator</span> <span class="identifier">last2</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">ForwardIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">BinaryPredicate</span>
<span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">find_end</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">ForwardIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">ForwardIterator</span> <span class="identifier">last2</span><span class="special">,</span><span class="identifier">BinaryPredicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">ForwardIterator</span>
<span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">find_first_of</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">ForwardIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">ForwardIterator</span> <span class="identifier">last2</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">ForwardIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">BinaryPredicate</span>
<span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">find_first_of</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">ForwardIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">ForwardIterator</span> <span class="identifier">last2</span><span class="special">,</span><span class="identifier">BinaryPredicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">adjacent_find</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">BinaryPredicate</span>
<span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">adjacent_find</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span>
  <span class="identifier">BinaryPredicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">ptrdiff_t</span> <span class="identifier">count</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Predicate</span><span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">ptrdiff_t</span> <span class="identifier">count_if</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span><span class="identifier">Predicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">InputIterator</span>
<span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="identifier">InputIterator</span><span class="special">&gt;</span> <span class="identifier">mismatch</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">InputIterator</span> <span class="identifier">first2</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">BinaryPredicate</span>
<span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="identifier">InputIterator</span><span class="special">&gt;</span> <span class="identifier">mismatch</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">InputIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">BinaryPredicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">InputIterator</span>
<span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="identifier">InputIterator</span><span class="special">&gt;</span> <span class="identifier">mismatch</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">InputIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">last2</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">BinaryPredicate</span>
<span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="identifier">InputIterator</span><span class="special">&gt;</span> <span class="identifier">mismatch</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">InputIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">last2</span><span class="special">,</span><span class="identifier">BinaryPredicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">InputIterator</span>
<span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">equal</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">InputIterator</span> <span class="identifier">first2</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">BinaryPredicate</span>
<span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">equal</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">InputIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">BinaryPredicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">InputIterator</span>
<span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">equal</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">InputIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">last2</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">BinaryPredicate</span>
<span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">equal</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">InputIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">InputIterator</span> <span class="identifier">last2</span><span class="special">,</span><span class="identifier">BinaryPredicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">ForwardIterator</span>
<span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">is_permutation</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">ForwardIterator</span> <span class="identifier">first2</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">ForwardIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">BinaryPredicate</span>
<span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">is_permutation</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">ForwardIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">BinaryPredicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">ForwardIterator</span>
<span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">is_permutation</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">ForwardIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">ForwardIterator</span> <span class="identifier">last2</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">ForwardIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">BinaryPredicate</span>
<span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">is_permutation</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">ForwardIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">ForwardIterator</span> <span class="identifier">last2</span><span class="special">,</span><span class="identifier">BinaryPredicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">ForwardIterator</span>
<span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">search</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">ForwardIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">ForwardIterator</span> <span class="identifier">last2</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">ForwardIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">BinaryPredicate</span>
<span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">search</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">ForwardIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">ForwardIterator</span> <span class="identifier">last2</span><span class="special">,</span><span class="identifier">BinaryPredicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Size</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">T</span>
<span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">search_n</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">Size</span> <span class="identifier">count</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">Size</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">BinaryPredicate</span>
<span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">search_n</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">Size</span> <span class="identifier">count</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="identifier">BinaryPredicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="emphasis"><em><code class="computeroutput"><span class="comment">// modifying sequence operations:</span></code></em></span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">OutputIterator</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">copy</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span>
  <span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">Size</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">OutputIterator</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">copy_n</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">Size</span> <span class="identifier">count</span><span class="special">,</span><span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">OutputIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Predicate</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">copy_if</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span>
  <span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">,</span><span class="identifier">Predicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">OutputIterator</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">move</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span>
  <span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">OutputIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">UnaryOperation</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">transform</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span>
  <span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">,</span><span class="identifier">UnaryOperation</span> <span class="identifier">op</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">InputIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">OutputIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">BinaryOperation</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">transform</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first1</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last1</span><span class="special">,</span>
  <span class="identifier">InputIterator</span> <span class="identifier">first2</span><span class="special">,</span><span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">,</span><span class="identifier">BinaryOperation</span> <span class="identifier">op</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">OutputIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">T</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">replace_copy</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span>
  <span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">old_x</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">new_x</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">OutputIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Predicate</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">T</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">replace_copy_if</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span>
  <span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">,</span><span class="identifier">Predicate</span> <span class="identifier">pred</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">new_x</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">OutputIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">T</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">remove_copy</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span>
  <span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">OutputIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Predicate</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">remove_copy_if</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span>
  <span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">,</span><span class="identifier">Predicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">OutputIterator</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">unique_copy</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span>
  <span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">OutputIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">BinaryPredicate</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">unique_copy</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span>
  <span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">,</span><span class="identifier">BinaryPredicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">OutputIterator</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">rotate_copy</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">middle</span><span class="special">,</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span><span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">OutputIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">Distance</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">UniformRandomBitGenerator</span>
<span class="special">&gt;</span>
<span class="identifier">OutputIterator</span> <span class="identifier">sample</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span>
  <span class="identifier">OutputIterator</span> <span class="identifier">res</span><span class="special">,</span><span class="identifier">Distance</span> <span class="identifier">n</span><span class="special">,</span><span class="identifier">UniformRandomBitGenerator</span><span class="special">&amp;&amp;</span> <span class="identifier">g</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Predicate</span><span class="special">&gt;</span>
<span class="keyword">bool</span> <span class="identifier">is_partitioned</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span><span class="identifier">Predicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span>
  <span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span>
  <span class="keyword">typename</span> <span class="identifier">OutputIterator1</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">OutputIterator2</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Predicate</span>
<span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span><span class="identifier">OutputIterator1</span><span class="special">,</span><span class="identifier">OutputIterator2</span><span class="special">&gt;</span> <span class="identifier">partition_copy</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span>
  <span class="identifier">OutputIterator1</span> <span class="identifier">rest</span><span class="special">,</span><span class="identifier">OutputIterator2</span> <span class="identifier">resf</span><span class="special">,</span><span class="identifier">Predicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span><span class="special">...</span> <span class="identifier">Ts</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">PolyCollectionIterator</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">Predicate</span><span class="special">&gt;</span>
<span class="identifier">PolyCollectionIterator</span> <span class="identifier">partition_point</span><span class="special">(</span>
  <span class="identifier">PolyCollectionIterator</span> <span class="identifier">first</span><span class="special">,</span><span class="identifier">PolyCollectionIterator</span> <span class="identifier">last</span><span class="special">,</span><span class="identifier">Predicate</span> <span class="identifier">pred</span><span class="special">);</span>

<span class="special">}</span> <span class="comment">/* namespace poly_collection */</span>

<span class="special">}</span> <span class="comment">/* namespace boost */</span>
</pre>
<p>
        The algorithms provided mimic the functionality of their homonyms in <a href="http://en.cppreference.com/w/cpp/algorithm" target="_top"><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">algorithm</span><span class="special">&gt;</span></code></a>
        but take advantage of the segmented nature of Boost.PolyCollection (global)
        iterators to deliver better performance. Additionally, concrete types can
        be passed to these algorithms for <span class="emphasis"><em>type restitution</em></span>.
      </p>
<p>
        For the description of the algorithms we use the following notation:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <span class="emphasis"><em><code class="computeroutput"><span class="identifier">alg</span></code></em></span>
            is the (unqualified) name of any of the algorithms in <code class="computeroutput"><span class="string">"boost/poly_collection/algorithm.hpp"</span></code>
            except <code class="computeroutput"><span class="identifier">copy_n</span></code> and <code class="computeroutput"><span class="identifier">rotate_copy</span></code>.
          </li>
<li class="listitem">
            <code class="computeroutput"><span class="identifier">first</span></code>, <code class="computeroutput"><span class="identifier">middle</span></code> and <code class="computeroutput"><span class="identifier">last</span></code>
            are (same-typed) possibly const global iterators of a collection of Boost.PolyCollection
            such that [<code class="computeroutput"><span class="identifier">first</span></code>, <code class="computeroutput"><span class="identifier">middle</span></code>) and [<code class="computeroutput"><span class="identifier">middle</span></code>,
            <code class="computeroutput"><span class="identifier">last</span></code>) are valid ranges.
          </li>
<li class="listitem">
            <code class="computeroutput"><span class="identifier">args</span><span class="special">...</span></code>
            is a function parameter pack of types <code class="computeroutput"><span class="identifier">Args</span><span class="special">&amp;&amp;...</span></code>,
          </li>
<li class="listitem">
            <code class="computeroutput"><span class="identifier">Ts</span><span class="special">...</span></code>
            is a template parameter pack of arbitrary types.
          </li>
</ul></div>
<p>
        (1) <span class="emphasis"><em><code class="computeroutput"><span class="identifier">alg</span></code></em></span><code class="computeroutput"><span class="special">(</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">last</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code><br>
        (2) <code class="computeroutput"><span class="identifier">for_each_n</span><span class="special">(</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code><br>
        (3) <code class="computeroutput"><span class="identifier">copy_n</span><span class="special">(</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code><br>
        (4) <code class="computeroutput"><span class="identifier">rotate_copy</span><span class="special">(</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">middle</span><span class="special">,</span><span class="identifier">last</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code>
      </p>
<p>
        <span class="bold"><strong>Requires:</strong></span> The expression <code class="computeroutput"><span class="identifier">expr</span></code>
        is well-formed, where <code class="computeroutput"><span class="identifier">expr</span></code>
        is defined as:<br> (1) <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span></code><span class="emphasis"><em><code class="computeroutput"><span class="identifier">alg</span></code></em></span><code class="computeroutput"><span class="special">(</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">last</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code>,<br>
        (2) <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">for_each_n</span><span class="special">(</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code>,<br>
        (3) <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">copy_n</span><span class="special">(</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code>,<br>
        (4) <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">rotate_copy</span><span class="special">(</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">middle</span><span class="special">,</span><span class="identifier">last</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code>.<br> <span class="bold"><strong>Effects:</strong></span>
        Equivalent to <code class="computeroutput"><span class="identifier">expr</span></code>.<br>
        <span class="bold"><strong>Returns:</strong></span> <code class="computeroutput"><span class="identifier">expr</span></code>.<br>
        <span class="bold"><strong>Complexity:</strong></span> That of <code class="computeroutput"><span class="identifier">expr</span></code>.
      </p>
<p>
        (1) <span class="emphasis"><em><code class="computeroutput"><span class="identifier">alg</span></code></em></span><code class="computeroutput"><span class="special">&lt;</span><span class="identifier">Ts</span><span class="special">...&gt;(</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">last</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code><br>
        (2) <code class="computeroutput"><span class="identifier">for_each_n</span><span class="special">&lt;</span><span class="identifier">Ts</span><span class="special">...&gt;(</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code><br>
        (3) <code class="computeroutput"><span class="identifier">copy_n</span><span class="special">&lt;</span><span class="identifier">Ts</span><span class="special">...&gt;(</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code><br>
        (4) <code class="computeroutput"><span class="identifier">rotate_copy</span><span class="special">&lt;</span><span class="identifier">Ts</span><span class="special">...&gt;(</span><span class="identifier">first</span><span class="special">,</span><span class="identifier">middle</span><span class="special">,</span><span class="identifier">last</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code>
      </p>
<p>
        <span class="bold"><strong>Requires:</strong></span> The expression <code class="computeroutput"><span class="identifier">expr</span></code>
        is well-formed, where <code class="computeroutput"><span class="identifier">expr</span></code>
        is defined as:<br> (1) <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span></code><span class="emphasis"><em><code class="computeroutput"><span class="identifier">alg</span></code></em></span><code class="computeroutput"><span class="special">(</span><span class="identifier">rfirst</span><span class="special">,</span><span class="identifier">rlast</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code>,<br>
        (2) <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">for_each_n</span><span class="special">(</span><span class="identifier">rfirst</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code>,<br>
        (3) <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">copy_n</span><span class="special">(</span><span class="identifier">rfirst</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code>,<br>
        (4) <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">rotate_copy</span><span class="special">(</span><span class="identifier">rfirst</span><span class="special">,</span><span class="identifier">rmiddle</span><span class="special">,</span><span class="identifier">rlast</span><span class="special">,</span><span class="identifier">args</span><span class="special">...)</span></code>,<br>
        and <code class="computeroutput"><span class="identifier">rfirst</span></code>, <code class="computeroutput"><span class="identifier">rmiddle</span></code> and <code class="computeroutput"><span class="identifier">rlast</span></code>
        are iterator-like objects behaving like their <code class="computeroutput"><span class="identifier">first</span></code>,
        <code class="computeroutput"><span class="identifier">middle</span></code> and <code class="computeroutput"><span class="identifier">last</span></code> counterparts except that they dereference
        to the corresponding subobject (<code class="computeroutput"><span class="keyword">const</span></code>)
        <code class="computeroutput"><span class="identifier">T</span><span class="special">&amp;</span></code>
        if pointing to a segment for <code class="computeroutput"><span class="identifier">T</span></code>
        and <code class="computeroutput"><span class="identifier">T</span></code> is in <code class="computeroutput"><span class="identifier">Ts</span><span class="special">...</span></code>
        <a href="#ftn.poly_collection.reference.header_boost_poly_collection_alg.f0" class="footnote" name="poly_collection.reference.header_boost_poly_collection_alg.f0"><sup class="footnote">[26]</sup></a>.<br> <span class="bold"><strong>Effects:</strong></span> Equivalent to
        <code class="computeroutput"><span class="identifier">expr</span></code>.<br> <span class="bold"><strong>Returns:</strong></span>
        <code class="computeroutput"><span class="identifier">expr</span></code>.<br> <span class="bold"><strong>Complexity:</strong></span>
        That of <code class="computeroutput"><span class="identifier">expr</span></code>.
      </p>
</div>
<div class="footnotes">
<br><hr style="width:100; text-align:left;margin-left: 0">
<div id="ftn.poly_collection.reference.polymorphism_models.f0" class="footnote"><p><a href="#poly_collection.reference.polymorphism_models.f0" class="para"><sup class="para">[21] </sup></a>
              This is a metalinguistic definition not directly expressible in C++.
              There are equivalent formulations that can indeed be realized in C++,
              but they add little to the comprehension of the concepts.
            </p></div>
<div id="ftn.poly_collection.reference.polymorphic_containers.polymorphic_collections.f0" class="footnote"><p><a href="#poly_collection.reference.polymorphic_containers.polymorphic_collections.f0" class="para"><sup class="para">[22] </sup></a>
            The global <code class="computeroutput"><span class="identifier">end</span><span class="special">()</span></code>
            iterator lies outside any segment, hence it always remain valid.
          </p></div>
<div id="ftn.poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.f0" class="footnote"><p><a href="#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.f0" class="para"><sup class="para">[23] </sup></a>
              Note that, unlike <code class="computeroutput"><span class="identifier">c</span><span class="special">.</span><span class="identifier">insert</span><span class="special">(</span><span class="identifier">i1</span><span class="special">,</span><span class="identifier">i2</span><span class="special">)</span></code>, these versions do not throw due to
              type registration problems.
            </p></div>
<div id="ftn.poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.f1" class="footnote"><p><a href="#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.f1" class="para"><sup class="para">[24] </sup></a>
              That is, the hint remains stable even if <code class="computeroutput"><span class="identifier">it</span></code>
              may become invalid due to reallocations.
            </p></div>
<div id="ftn.poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.f2" class="footnote"><p><a href="#poly_collection.reference.polymorphic_containers.polymorphic_collections.modifiers.f2" class="para"><sup class="para">[25] </sup></a>
              The two previous notes apply here.
            </p></div>
<div id="ftn.poly_collection.reference.header_boost_poly_collection_alg.f0" class="footnote"><p><a href="#poly_collection.reference.header_boost_poly_collection_alg.f0" class="para"><sup class="para">[26] </sup></a>
          Strictly speaking a proper <a href="http://en.cppreference.com/w/cpp/named_req/ForwardIterator" target="_top"><span class="bold"><strong><code class="computeroutput"><span class="identifier">ForwardIterator</span></code></strong></span></a>
          cannot behave like this as dereferencing must yield <span class="emphasis"><em>exactly</em></span>
          a (<code class="computeroutput"><span class="keyword">const</span></code>) <code class="computeroutput"><span class="identifier">value_type</span><span class="special">&amp;</span></code> value, which disallows this type of
          polymorphism.
        </p></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2016-2021 Joaquín M López Muñoz<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="performance.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../poly_collection.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="future_work.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
