<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Reference</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../intrusive.html" title="Chapter 17. Boost.Intrusive">
<link rel="prev" href="index.html" title="Indexes">
<link rel="next" href="../boost/intrusive/any_base_hook.html" title="Class template any_base_hook">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="index.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../intrusive.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost/intrusive/any_base_hook.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="intrusive.reference"></a>Reference</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#header.boost.intrusive.any_hook_hpp">Header &lt;boost/intrusive/any_hook.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.avl_set_hpp">Header &lt;boost/intrusive/avl_set.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.avl_set_hook_hpp">Header &lt;boost/intrusive/avl_set_hook.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.avltree_hpp">Header &lt;boost/intrusive/avltree.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.avltree_algorithms_hpp">Header &lt;boost/intrusive/avltree_algorithms.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.bs_set_hpp">Header &lt;boost/intrusive/bs_set.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.bs_set_hook_hpp">Header &lt;boost/intrusive/bs_set_hook.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.bstree_hpp">Header &lt;boost/intrusive/bstree.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.bstree_algorithms_hpp">Header &lt;boost/intrusive/bstree_algorithms.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.circular_list_algorithms_hpp">Header &lt;boost/intrusive/circular_list_algorithms.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.circular_slist_algorithms_hpp">Header &lt;boost/intrusive/circular_slist_algorithms.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.derivation_value_traits_hpp">Header &lt;boost/intrusive/derivation_value_traits.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.hashtable_hpp">Header &lt;boost/intrusive/hashtable.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.intrusive_fwd_hpp">Header &lt;boost/intrusive/intrusive_fwd.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.linear_slist_algorithms_hpp">Header &lt;boost/intrusive/linear_slist_algorithms.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.link_mode_hpp">Header &lt;boost/intrusive/link_mode.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.list_hpp">Header &lt;boost/intrusive/list.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.list_hook_hpp">Header &lt;boost/intrusive/list_hook.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.member_value_traits_hpp">Header &lt;boost/intrusive/member_value_traits.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.options_hpp">Header &lt;boost/intrusive/options.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.pack_options_hpp">Header &lt;boost/intrusive/pack_options.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.parent_from_member_hpp">Header &lt;boost/intrusive/parent_from_member.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.pointer_plus_bits_hpp">Header &lt;boost/intrusive/pointer_plus_bits.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.pointer_rebind_hpp">Header &lt;boost/intrusive/pointer_rebind.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.pointer_traits_hpp">Header &lt;boost/intrusive/pointer_traits.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.priority_compare_hpp">Header &lt;boost/intrusive/priority_compare.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.rbtree_hpp">Header &lt;boost/intrusive/rbtree.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.rbtree_algorithms_hpp">Header &lt;boost/intrusive/rbtree_algorithms.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.set_hpp">Header &lt;boost/intrusive/set.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.set_hook_hpp">Header &lt;boost/intrusive/set_hook.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.sg_set_hpp">Header &lt;boost/intrusive/sg_set.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.sgtree_hpp">Header &lt;boost/intrusive/sgtree.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.sgtree_algorithms_hpp">Header &lt;boost/intrusive/sgtree_algorithms.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.slist_hpp">Header &lt;boost/intrusive/slist.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.slist_hook_hpp">Header &lt;boost/intrusive/slist_hook.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.splay_set_hpp">Header &lt;boost/intrusive/splay_set.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.splaytree_hpp">Header &lt;boost/intrusive/splaytree.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.splaytree_algorithms_hpp">Header &lt;boost/intrusive/splaytree_algorithms.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.treap_hpp">Header &lt;boost/intrusive/treap.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.treap_algorithms_hpp">Header &lt;boost/intrusive/treap_algorithms.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.treap_set_hpp">Header &lt;boost/intrusive/treap_set.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.trivial_value_traits_hpp">Header &lt;boost/intrusive/trivial_value_traits.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.unordered_set_hpp">Header &lt;boost/intrusive/unordered_set.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.intrusive.unordered_set_hook_hpp">Header &lt;boost/intrusive/unordered_set_hook.hpp&gt;</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.any_hook_hpp"></a>Header &lt;<a href="../../../boost/intrusive/any_hook.hpp" target="_top">boost/intrusive/any_hook.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/any_base_hook.html" title="Class template any_base_hook">any_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/any_member_hook.html" title="Class template any_member_hook">any_member_hook</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BasicHook<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/any_to_avl_set_hook.html" title="Struct template any_to_avl_set_hook">any_to_avl_set_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BasicHook<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/any_to_bs_set_hook.html" title="Struct template any_to_bs_set_hook">any_to_bs_set_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BasicHook<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/any_to_list_hook.html" title="Struct template any_to_list_hook">any_to_list_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BasicHook<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/any_to_set_hook.html" title="Struct template any_to_set_hook">any_to_set_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BasicHook<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/any_to_slist_hook.html" title="Struct template any_to_slist_hook">any_to_slist_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BasicHook<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/any_to_unordered_set_hook.html" title="Struct template any_to_unordered_set_hook">any_to_unordered_set_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_any_base_hook.html" title="Struct template make_any_base_hook">make_any_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_any_member_hook.html" title="Struct template make_any_member_hook">make_any_member_hook</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.avl_set_hpp"></a>Header &lt;<a href="../../../boost/intrusive/avl_set.hpp" target="_top">boost/intrusive/avl_set.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/avl_multiset.html" title="Class template avl_multiset">avl_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/avl_set.html" title="Class template avl_set">avl_set</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_avl_multiset.html" title="Struct template make_avl_multiset">make_avl_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_avl_set.html" title="Struct template make_avl_set">make_avl_set</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator!=_idm22684"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_set.html" title="Class template avl_set">avl_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_set.html" title="Class template avl_set">avl_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_idm22696"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_set.html" title="Class template avl_set">avl_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_set.html" title="Class template avl_set">avl_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm22708"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_set.html" title="Class template avl_set">avl_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_set.html" title="Class template avl_set">avl_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm22720"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_set.html" title="Class template avl_set">avl_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_set.html" title="Class template avl_set">avl_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.intrusive.swap_idm22732"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="../boost/intrusive/avl_set.html" title="Class template avl_set">avl_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> <a class="link" href="../boost/intrusive/avl_set.html" title="Class template avl_set">avl_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator!=_idm22744"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_multiset.html" title="Class template avl_multiset">avl_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_multiset.html" title="Class template avl_multiset">avl_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_idm22756"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_multiset.html" title="Class template avl_multiset">avl_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_multiset.html" title="Class template avl_multiset">avl_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm22768"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_multiset.html" title="Class template avl_multiset">avl_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_multiset.html" title="Class template avl_multiset">avl_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm22780"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_multiset.html" title="Class template avl_multiset">avl_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/avl_multiset.html" title="Class template avl_multiset">avl_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.intrusive.swap_idm22792"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="../boost/intrusive/avl_multiset.html" title="Class template avl_multiset">avl_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                <a class="link" href="../boost/intrusive/avl_multiset.html" title="Class template avl_multiset">avl_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.avl_set_hook_hpp"></a>Header &lt;<a href="../../../boost/intrusive/avl_set_hook.hpp" target="_top">boost/intrusive/avl_set_hook.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/avl_set_base_hook.html" title="Class template avl_set_base_hook">avl_set_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/avl_set_member_hook.html" title="Class template avl_set_member_hook">avl_set_member_hook</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_avl_set_base_hook.html" title="Struct template make_avl_set_base_hook">make_avl_set_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_avl_set_member_hook.html" title="Struct template make_avl_set_member_hook">make_avl_set_member_hook</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.avltree_hpp"></a>Header &lt;<a href="../../../boost/intrusive/avltree.hpp" target="_top">boost/intrusive/avltree.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/avltree.html" title="Class template avltree">avltree</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_avltree.html" title="Struct template make_avltree">make_avltree</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.avltree_algorithms_hpp"></a>Header &lt;<a href="../../../boost/intrusive/avltree_algorithms.hpp" target="_top">boost/intrusive/avltree_algorithms.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> NodeTraits<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/avltree_algorithms.html" title="Class template avltree_algorithms">avltree_algorithms</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.bs_set_hpp"></a>Header &lt;<a href="../../../boost/intrusive/bs_set.hpp" target="_top">boost/intrusive/bs_set.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/bs_multiset.html" title="Class template bs_multiset">bs_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/bs_set.html" title="Class template bs_set">bs_set</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_bs_multiset.html" title="Struct template make_bs_multiset">make_bs_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_bs_set.html" title="Struct template make_bs_set">make_bs_set</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator!=_idm27951"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_set.html" title="Class template bs_set">bs_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_set.html" title="Class template bs_set">bs_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_idm27963"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_set.html" title="Class template bs_set">bs_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_set.html" title="Class template bs_set">bs_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm27975"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_set.html" title="Class template bs_set">bs_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_set.html" title="Class template bs_set">bs_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm27987"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_set.html" title="Class template bs_set">bs_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_set.html" title="Class template bs_set">bs_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.intrusive.swap_idm27999"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="../boost/intrusive/bs_set.html" title="Class template bs_set">bs_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> <a class="link" href="../boost/intrusive/bs_set.html" title="Class template bs_set">bs_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator!=_idm28011"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_multiset.html" title="Class template bs_multiset">bs_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_multiset.html" title="Class template bs_multiset">bs_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_idm28023"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_multiset.html" title="Class template bs_multiset">bs_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_multiset.html" title="Class template bs_multiset">bs_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm28035"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_multiset.html" title="Class template bs_multiset">bs_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_multiset.html" title="Class template bs_multiset">bs_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm28047"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_multiset.html" title="Class template bs_multiset">bs_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/bs_multiset.html" title="Class template bs_multiset">bs_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.intrusive.swap_idm28059"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="../boost/intrusive/bs_multiset.html" title="Class template bs_multiset">bs_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                <a class="link" href="../boost/intrusive/bs_multiset.html" title="Class template bs_multiset">bs_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.bs_set_hook_hpp"></a>Header &lt;<a href="../../../boost/intrusive/bs_set_hook.hpp" target="_top">boost/intrusive/bs_set_hook.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/bs_set_base_hook.html" title="Class template bs_set_base_hook">bs_set_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/bs_set_member_hook.html" title="Class template bs_set_member_hook">bs_set_member_hook</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_bs_set_base_hook.html" title="Struct template make_bs_set_base_hook">make_bs_set_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_bs_set_member_hook.html" title="Struct template make_bs_set_member_hook">make_bs_set_member_hook</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.bstree_hpp"></a>Header &lt;<a href="../../../boost/intrusive/bstree.hpp" target="_top">boost/intrusive/bstree.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/bstree.html" title="Class template bstree">bstree</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_bstree.html" title="Struct template make_bstree">make_bstree</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.bstree_algorithms_hpp"></a>Header &lt;<a href="../../../boost/intrusive/bstree_algorithms.hpp" target="_top">boost/intrusive/bstree_algorithms.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> NodeTraits<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/bstree_algorithms.html" title="Class template bstree_algorithms">bstree_algorithms</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.circular_list_algorithms_hpp"></a>Header &lt;<a href="../../../boost/intrusive/circular_list_algorithms.hpp" target="_top">boost/intrusive/circular_list_algorithms.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> NodeTraits<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/circular_list_algorithms.html" title="Class template circular_list_algorithms">circular_list_algorithms</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.circular_slist_algorithms_hpp"></a>Header &lt;<a href="../../../boost/intrusive/circular_slist_algorithms.hpp" target="_top">boost/intrusive/circular_slist_algorithms.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> NodeTraits<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/circular_slist_algorithms.html" title="Class template circular_slist_algorithms">circular_slist_algorithms</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.derivation_value_traits_hpp"></a>Header &lt;<a href="../../../boost/intrusive/derivation_value_traits.hpp" target="_top">boost/intrusive/derivation_value_traits.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> NodeTraits<span class="special">,</span> 
             <span class="identifier">link_mode_type</span> LinkMode <span class="special">=</span> <span class="identifier">safe_link</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/derivation_value_traits.html" title="Struct template derivation_value_traits">derivation_value_traits</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.hashtable_hpp"></a>Header &lt;<a href="../../../boost/intrusive/hashtable.hpp" target="_top">boost/intrusive/hashtable.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueTraits<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyOfValue<span class="special">,</span> 
             <span class="keyword">typename</span> VoidOrKeyHash<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyEqual<span class="special">,</span> 
             <span class="keyword">typename</span> BucketTraits<span class="special">,</span> <span class="keyword">bool</span> LinearBuckets<span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/bucket_hash_equal_t.html" title="Struct template bucket_hash_equal_t">bucket_hash_equal_t</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueTraits<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyOfValue<span class="special">,</span> 
             <span class="keyword">typename</span> VoidOrKeyHash<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyEqual<span class="special">,</span> 
             <span class="keyword">typename</span> BucketTraits<span class="special">,</span> <span class="keyword">bool</span> LinearBuckets<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/bucket_hash_equal_idm31853.html" title="Struct template bucket_hash_equal_t&lt;ValueTraits, VoidOrKeyOfValue, VoidOrKeyHash, VoidOrKeyEqual, BucketTraits, LinearBuckets, true&gt;">bucket_hash_equal_t</a><span class="special">&lt;</span><span class="identifier">ValueTraits</span><span class="special">,</span> <span class="identifier">VoidOrKeyOfValue</span><span class="special">,</span> <span class="identifier">VoidOrKeyHash</span><span class="special">,</span> <span class="identifier">VoidOrKeyEqual</span><span class="special">,</span> <span class="identifier">BucketTraits</span><span class="special">,</span> <span class="identifier">LinearBuckets</span><span class="special">,</span> <span class="keyword">true</span><span class="special">&gt;</span><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueTraits<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyOfValue<span class="special">,</span> 
             <span class="keyword">typename</span> VoidOrKeyHash<span class="special">,</span> <span class="keyword">typename</span> BucketTraits<span class="special">,</span> 
             <span class="keyword">bool</span> LinearBuckets<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/bucket_hash_t.html" title="Struct template bucket_hash_t">bucket_hash_t</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueTraits<span class="special">,</span> <span class="keyword">typename</span> BucketTraits<span class="special">,</span> <span class="keyword">bool</span> LinearBuckets<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/bucket_plus_vtraits.html" title="Struct template bucket_plus_vtraits">bucket_plus_vtraits</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BucketPtr<span class="special">,</span> <span class="keyword">typename</span> SizeType<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/bucket_traits_impl.html" title="Struct template bucket_traits_impl">bucket_traits_impl</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/default_hashtable_idm32482.html" title="Struct default_hashtable_hook_applier">default_hashtable_hook_applier</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueTraits<span class="special">,</span> <span class="keyword">bool</span> IsConst<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/downcast_node_to_value_t.html" title="Struct template downcast_node_to_value_t">downcast_node_to_value_t</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Bucket<span class="special">,</span> <span class="keyword">typename</span> Algo<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">,</span> 
             <span class="keyword">typename</span> SizeType<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/intrusive/exception_bucket_disposer.html" title="Class template exception_bucket_disposer">exception_bucket_disposer</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> EqualTo<span class="special">,</span> <span class="keyword">typename</span> <span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/get_equal_to.html" title="Struct template get_equal_to">get_equal_to</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/get_equal_to_void_idm32550.html" title="Struct template get_equal_to&lt;void, T&gt;">get_equal_to</a><span class="special">&lt;</span><span class="keyword">void</span><span class="special">,</span> <span class="identifier">T</span><span class="special">&gt;</span><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Hash<span class="special">,</span> <span class="keyword">typename</span> <span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/get_hash.html" title="Struct template get_hash">get_hash</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/get_hash_void__T_idm32564.html" title="Struct template get_hash&lt;void, T&gt;">get_hash</a><span class="special">&lt;</span><span class="keyword">void</span><span class="special">,</span> <span class="identifier">T</span><span class="special">&gt;</span><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyOfValue<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/get_hash_key_of_value.html" title="Struct template get_hash_key_of_value">get_hash_key_of_value</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/get_hash_key_of_v_idm32578.html" title="Struct template get_hash_key_of_value&lt;void, T&gt;">get_hash_key_of_value</a><span class="special">&lt;</span><span class="keyword">void</span><span class="special">,</span> <span class="identifier">T</span><span class="special">&gt;</span><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueTraits<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyOfValue<span class="special">,</span> 
             <span class="keyword">typename</span> VoidOrKeyHash<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyEqual<span class="special">,</span> 
             <span class="keyword">typename</span> BucketTraits<span class="special">,</span> <span class="keyword">typename</span> SizeType<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> BoolFlags<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/get_hashtable_siz_idm32587.html" title="Struct template get_hashtable_size_wrapper_bucket">get_hashtable_size_wrapper_bucket</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueTraits<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyOfValue<span class="special">,</span> 
             <span class="keyword">typename</span> VoidOrKeyHash<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyEqual<span class="special">,</span> 
             <span class="keyword">typename</span> BucketTraits<span class="special">,</span> <span class="keyword">typename</span> SizeType<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> BoolFlags<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/get_hashtable_siz_idm32599.html" title="Struct template get_hashtable_size_wrapper_internal">get_hashtable_size_wrapper_internal</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> NodeTraits<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/group_functions.html" title="Struct template group_functions">group_functions</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/hash_bool_flags.html" title="Struct hash_bool_flags">hash_bool_flags</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyOfValue<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyEqual<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/hash_key_equal.html" title="Struct template hash_key_equal">hash_key_equal</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyOfValue<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyHash<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/hash_key_hash.html" title="Struct template hash_key_hash">hash_key_hash</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyOfValue<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/hash_key_types_base.html" title="Struct template hash_key_types_base">hash_key_types_base</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueTraits<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyOfValue<span class="special">,</span> 
             <span class="keyword">typename</span> VoidOrKeyHash<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyEqual<span class="special">,</span> 
             <span class="keyword">typename</span> BucketTraits<span class="special">,</span> <span class="keyword">typename</span> SizeType<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> BoolFlags<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/hashdata_internal.html" title="Struct template hashdata_internal">hashdata_internal</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/hashtable.html" title="Class template hashtable">hashtable</a><span class="special">;</span>

    <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/hashtable_defaults.html" title="Struct hashtable_defaults">hashtable_defaults</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueTraits<span class="special">,</span> <span class="keyword">typename</span> BucketTraits<span class="special">,</span> 
             <span class="keyword">typename</span> VoidOrKeyOfValue<span class="special">,</span> <span class="keyword">typename</span> VoidOrKeyEqual<span class="special">,</span> 
             <span class="keyword">bool</span> LinearBuckets<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/hashtable_equal_holder.html" title="Struct template hashtable_equal_holder">hashtable_equal_holder</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> DeriveFrom<span class="special">,</span> <span class="keyword">typename</span> SizeType<span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/hashtable_size_wrapper.html" title="Struct template hashtable_size_wrapper">hashtable_size_wrapper</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> DeriveFrom<span class="special">,</span> <span class="keyword">typename</span> SizeType<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/hashtable_size_wr_idm34366.html" title="Struct template hashtable_size_wrapper&lt;DeriveFrom, SizeType, false&gt;">hashtable_size_wrapper</a><span class="special">&lt;</span><span class="identifier">DeriveFrom</span><span class="special">,</span> <span class="identifier">SizeType</span><span class="special">,</span> <span class="keyword">false</span><span class="special">&gt;</span><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BucketType<span class="special">,</span> <span class="keyword">typename</span> SplitTraits<span class="special">,</span> 
             <span class="keyword">typename</span> SlistNodeAlgorithms<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/intrusive/incremental_rehas_idm34412.html" title="Class template incremental_rehash_rollback">incremental_rehash_rollback</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> StoreHash<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/insert_commit_data_impl.html" title="Struct template insert_commit_data_impl">insert_commit_data_impl</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/insert_commit_dat_idm34452.html" title="Struct insert_commit_data_impl&lt;false&gt;">insert_commit_data_impl</a><span class="special">&lt;</span><span class="keyword">false</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/is_default_hook_t_idm34465.html" title="Struct is_default_hook_tag&lt;default_hashtable_hook_applier&gt;">is_default_hook_tag</a><span class="special">&lt;</span><span class="identifier">default_hashtable_hook_applier</span><span class="special">&gt;</span><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_hashtable.html" title="Struct template make_hashtable">make_hashtable</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> F<span class="special">,</span> <span class="keyword">typename</span> SlistNodePtr<span class="special">,</span> <span class="keyword">typename</span> NodePtr<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/node_cast_adaptor.html" title="Struct template node_cast_adaptor">node_cast_adaptor</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> NodeTraits<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/node_functions.html" title="Struct template node_functions">node_functions</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/optimize_multikey_is_true.html" title="Struct template optimize_multikey_is_true">optimize_multikey_is_true</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/store_hash_is_true.html" title="Struct template store_hash_is_true">store_hash_is_true</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueTraitsOrHookOption<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/unordered_bucket.html" title="Struct template unordered_bucket">unordered_bucket</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> SupposedValueTraits<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/unordered_bucket_impl.html" title="Struct template unordered_bucket_impl">unordered_bucket_impl</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueTraitsOrHookOption<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/unordered_bucket_ptr.html" title="Struct template unordered_bucket_ptr">unordered_bucket_ptr</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> SupposedValueTraits<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/unordered_bucket_ptr_impl.html" title="Struct template unordered_bucket_ptr_impl">unordered_bucket_ptr_impl</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueTraitsOrHookOption<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/unordered_default_idm34618.html" title="Struct template unordered_default_bucket_traits">unordered_default_bucket_traits</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIt<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="identifier">InputIt</span> <a name="boost.intrusive.priv_algo_find"></a><span class="identifier">priv_algo_find</span><span class="special">(</span><span class="identifier">InputIt</span> first<span class="special">,</span> <span class="identifier">InputIt</span> last<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> value<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIt<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="identifier">boost</span><span class="special">::</span><span class="identifier">intrusive</span><span class="special">::</span><span class="identifier">iterator_traits</span><span class="special">&lt;</span> <span class="identifier">InputIt</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">difference_type</span> 
      <a name="boost.intrusive.priv_algo_count"></a><span class="identifier">priv_algo_count</span><span class="special">(</span><span class="identifier">InputIt</span> first<span class="special">,</span> <span class="identifier">InputIt</span> last<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> value<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ForwardIterator1<span class="special">,</span> <span class="keyword">typename</span> ForwardIterator2<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.priv_algo_is_permutation"></a><span class="identifier">priv_algo_is_permutation</span><span class="special">(</span><span class="identifier">ForwardIterator1</span> first1<span class="special">,</span> 
                                    <span class="identifier">ForwardIterator1</span> last1<span class="special">,</span> 
                                    <span class="identifier">ForwardIterator2</span> first2<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Node<span class="special">,</span> <span class="keyword">typename</span> SlistNodePtr<span class="special">&gt;</span> 
      <a class="link" href="../boost/intrusive/pointer_traits.html" title="Struct template pointer_traits">pointer_traits</a><span class="special">&lt;</span> <span class="identifier">SlistNodePtr</span> <span class="special">&gt;</span><span class="special">::</span><span class="keyword">template</span> <span class="identifier">rebind_pointer</span><span class="special">&lt;</span> <span class="identifier">Node</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> 
      <a name="boost.intrusive.dcast_bucket_ptr"></a><span class="identifier">dcast_bucket_ptr</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">SlistNodePtr</span> <span class="special">&amp;</span> p<span class="special">)</span><span class="special">;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <a name="boost.intrusive.hash_to_bucket_idm34669"></a><span class="identifier">hash_to_bucket</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> hash_value<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> bucket_cnt<span class="special">,</span> 
                               <span class="emphasis"><em><span class="identifier">unspecified</span></em></span><span class="special">)</span><span class="special">;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <a name="boost.intrusive.hash_to_bucket_idm34678"></a><span class="identifier">hash_to_bucket</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> hash_value<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> bucket_cnt<span class="special">,</span> 
                               <span class="emphasis"><em><span class="identifier">unspecified</span></em></span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// <a class="link" href="../boost/intrusive/fastmod_buckets.html" title="Struct template fastmod_buckets">fastmod_buckets</a></span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Power2Buckets<span class="special">,</span> <span class="keyword">bool</span> Incremental<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <a name="boost.intrusive.hash_to_bucket_sp_idm34687"></a><span class="identifier">hash_to_bucket_split</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> hash_value<span class="special">,</span> 
                                       <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> bucket_cnt<span class="special">,</span> 
                                       <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> split<span class="special">,</span> <span class="emphasis"><em><span class="identifier">unspecified</span></em></span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Power2Buckets<span class="special">,</span> <span class="keyword">bool</span> Incremental<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <a name="boost.intrusive.hash_to_bucket_sp_idm34705"></a><span class="identifier">hash_to_bucket_split</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> hash_value<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span><span class="special">,</span> 
                                       <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> split<span class="special">,</span> <span class="emphasis"><em><span class="identifier">unspecified</span></em></span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.intrusive_fwd_hpp"></a>Header &lt;<a href="../../../boost/intrusive/intrusive_fwd.hpp" target="_top">boost/intrusive/intrusive_fwd.hpp</a>&gt;</h3></div></div></div>
<p>This header file forward declares most Intrusive classes.</p>
<p>It forward declares the following containers and hooks:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem"><p><a class="link" href="../boost/intrusive/slist.html" title="Class template slist">boost::intrusive::slist</a> / <a class="link" href="../boost/intrusive/slist_base_hook.html" title="Class template slist_base_hook">boost::intrusive::slist_base_hook</a> / <a class="link" href="../boost/intrusive/slist_member_hook.html" title="Class template slist_member_hook">boost::intrusive::slist_member_hook</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/list.html" title="Class template list">boost::intrusive::list</a> / <a class="link" href="../boost/intrusive/list_base_hook.html" title="Class template list_base_hook">boost::intrusive::list_base_hook</a> / <a class="link" href="../boost/intrusive/list_member_hook.html" title="Class template list_member_hook">boost::intrusive::list_member_hook</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/bstree.html" title="Class template bstree">boost::intrusive::bstree</a> / <a class="link" href="../boost/intrusive/bs_set.html" title="Class template bs_set">boost::intrusive::bs_set</a> / <a class="link" href="../boost/intrusive/bs_multiset.html" title="Class template bs_multiset">boost::intrusive::bs_multiset</a> / <a class="link" href="../boost/intrusive/bs_set_base_hook.html" title="Class template bs_set_base_hook">boost::intrusive::bs_set_base_hook</a> / <a class="link" href="../boost/intrusive/bs_set_member_hook.html" title="Class template bs_set_member_hook">boost::intrusive::bs_set_member_hook</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/rbtree.html" title="Class template rbtree">boost::intrusive::rbtree</a> / <a class="link" href="../boost/intrusive/set.html" title="Class template set">boost::intrusive::set</a> / <a class="link" href="../boost/intrusive/multiset.html" title="Class template multiset">boost::intrusive::multiset</a> / <a class="link" href="../boost/intrusive/set_base_hook.html" title="Class template set_base_hook">boost::intrusive::set_base_hook</a> / <a class="link" href="../boost/intrusive/set_member_hook.html" title="Class template set_member_hook">boost::intrusive::set_member_hook</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/avltree.html" title="Class template avltree">boost::intrusive::avltree</a> / <a class="link" href="../boost/intrusive/avl_set.html" title="Class template avl_set">boost::intrusive::avl_set</a> / <a class="link" href="../boost/intrusive/avl_multiset.html" title="Class template avl_multiset">boost::intrusive::avl_multiset</a> / <a class="link" href="../boost/intrusive/avl_set_base_hook.html" title="Class template avl_set_base_hook">boost::intrusive::avl_set_base_hook</a> / <a class="link" href="../boost/intrusive/avl_set_member_hook.html" title="Class template avl_set_member_hook">boost::intrusive::avl_set_member_hook</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/splaytree.html" title="Class template splaytree">boost::intrusive::splaytree</a> / <a class="link" href="../boost/intrusive/splay_set.html" title="Class template splay_set">boost::intrusive::splay_set</a> / <a class="link" href="../boost/intrusive/splay_multiset.html" title="Class template splay_multiset">boost::intrusive::splay_multiset</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/sgtree.html" title="Class template sgtree">boost::intrusive::sgtree</a> / <a class="link" href="../boost/intrusive/sg_set.html" title="Class template sg_set">boost::intrusive::sg_set</a> / <a class="link" href="../boost/intrusive/sg_multiset.html" title="Class template sg_multiset">boost::intrusive::sg_multiset</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/treap.html" title="Class template treap">boost::intrusive::treap</a> / <a class="link" href="../boost/intrusive/treap_set.html" title="Class template treap_set">boost::intrusive::treap_set</a> / <a class="link" href="../boost/intrusive/treap_multiset.html" title="Class template treap_multiset">boost::intrusive::treap_multiset</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/hashtable.html" title="Class template hashtable">boost::intrusive::hashtable</a> / <a class="link" href="../boost/intrusive/unordered_set.html" title="Class template unordered_set">boost::intrusive::unordered_set</a> / <a class="link" href="../boost/intrusive/unordered_multiset.html" title="Class template unordered_multiset">boost::intrusive::unordered_multiset</a> / <a class="link" href="../boost/intrusive/unordered_set_base_hook.html" title="Class template unordered_set_base_hook">boost::intrusive::unordered_set_base_hook</a> / <a class="link" href="../boost/intrusive/unordered_set_member_hook.html" title="Class template unordered_set_member_hook">boost::intrusive::unordered_set_member_hook</a> /</p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/any_base_hook.html" title="Class template any_base_hook">boost::intrusive::any_base_hook</a> / <a class="link" href="../boost/intrusive/any_member_hook.html" title="Class template any_member_hook">boost::intrusive::any_member_hook</a></p></li>
</ul></div>
<p>
</p>
<p>It forward declares the following container or hook options:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem"><p><a class="link" href="../boost/intrusive/constant_time_size.html" title="Struct template constant_time_size">boost::intrusive::constant_time_size</a> / <a class="link" href="../boost/intrusive/size_type.html" title="Struct template size_type">boost::intrusive::size_type</a> / <a class="link" href="../boost/intrusive/compare.html" title="Struct template compare">boost::intrusive::compare</a> / <a class="link" href="../boost/intrusive/equal.html" title="Struct template equal">boost::intrusive::equal</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/floating_point.html" title="Struct template floating_point">boost::intrusive::floating_point</a> / <a class="link" href="../boost/intrusive/priority.html" title="Struct template priority">boost::intrusive::priority</a> / <a class="link" href="../boost/intrusive/hash.html" title="Struct template hash">boost::intrusive::hash</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/value_traits.html" title="Struct template value_traits">boost::intrusive::value_traits</a> / <a class="link" href="../boost/intrusive/member_hook.html" title="Struct template member_hook">boost::intrusive::member_hook</a> / <a class="link" href="../boost/intrusive/function_hook.html" title="Struct template function_hook">boost::intrusive::function_hook</a> / <a class="link" href="../boost/intrusive/base_hook.html" title="Struct template base_hook">boost::intrusive::base_hook</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/void_pointer.html" title="Struct template void_pointer">boost::intrusive::void_pointer</a> / <a class="link" href="../boost/intrusive/tag.html" title="Struct template tag">boost::intrusive::tag</a> / <a class="link" href="../boost/intrusive/link_mode.html" title="Struct template link_mode">boost::intrusive::link_mode</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/optimize_size.html" title="Struct template optimize_size">boost::intrusive::optimize_size</a> / <a class="link" href="../boost/intrusive/linear.html" title="Struct template linear">boost::intrusive::linear</a> / <a class="link" href="../boost/intrusive/cache_last.html" title="Struct template cache_last">boost::intrusive::cache_last</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/bucket_traits.html" title="Struct template bucket_traits">boost::intrusive::bucket_traits</a> / <a class="link" href="../boost/intrusive/store_hash.html" title="Struct template store_hash">boost::intrusive::store_hash</a> / <a class="link" href="../boost/intrusive/optimize_multikey.html" title="Struct template optimize_multikey">boost::intrusive::optimize_multikey</a></p></li>
<li class="listitem"><p><a class="link" href="../boost/intrusive/power_2_buckets.html" title="Struct template power_2_buckets">boost::intrusive::power_2_buckets</a> / <a class="link" href="../boost/intrusive/cache_begin.html" title="Struct template cache_begin">boost::intrusive::cache_begin</a> / <a class="link" href="../boost/intrusive/compare_hash.html" title="Struct template compare_hash">boost::intrusive::compare_hash</a> / <a class="link" href="../boost/intrusive/incremental.html" title="Struct template incremental">boost::intrusive::incremental</a></p></li>
</ul></div>
<p>
</p>
<p>It forward declares the following value traits utilities:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><a class="link" href="../boost/intrusive/value_traits.html" title="Struct template value_traits">boost::intrusive::value_traits</a> / <a class="link" href="../boost/intrusive/derivation_value_traits.html" title="Struct template derivation_value_traits">boost::intrusive::derivation_value_traits</a> / <a class="link" href="../boost/intrusive/trivial_value_traits.html" title="Struct template trivial_value_traits">boost::intrusive::trivial_value_traits</a></p></li></ul></div>
<p>
</p>
<p>Finally it forward declares the following general purpose utilities:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><a class="link" href="../boost/intrusive/pointer_plus_bits.html" title="Struct template pointer_plus_bits">boost::intrusive::pointer_plus_bits</a> / boost::intrusive::priority_compare. </p></li></ul></div>
<p>
</p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.linear_slist_algorithms_hpp"></a>Header &lt;<a href="../../../boost/intrusive/linear_slist_algorithms.hpp" target="_top">boost/intrusive/linear_slist_algorithms.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> NodeTraits<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/linear_slist_algorithms.html" title="Class template linear_slist_algorithms">linear_slist_algorithms</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.link_mode_hpp"></a>Header &lt;<a href="../../../boost/intrusive/link_mode.hpp" target="_top">boost/intrusive/link_mode.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">enum</span> <a class="link" href="../boost/intrusive/link_mode_type.html" title="Type link_mode_type">link_mode_type</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.list_hpp"></a>Header &lt;<a href="../../../boost/intrusive/list.hpp" target="_top">boost/intrusive/list.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/list.html" title="Class template list">list</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_list.html" title="Struct template make_list">make_list</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.list_hook_hpp"></a>Header &lt;<a href="../../../boost/intrusive/list_hook.hpp" target="_top">boost/intrusive/list_hook.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/list_base_hook.html" title="Class template list_base_hook">list_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/list_member_hook.html" title="Class template list_member_hook">list_member_hook</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_list_base_hook.html" title="Struct template make_list_base_hook">make_list_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_list_member_hook.html" title="Struct template make_list_member_hook">make_list_member_hook</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.member_value_traits_hpp"></a>Header &lt;<a href="../../../boost/intrusive/member_value_traits.hpp" target="_top">boost/intrusive/member_value_traits.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> NodeTraits<span class="special">,</span> 
             <span class="keyword">typename</span> <span class="identifier">NodeTraits</span><span class="special">::</span><span class="identifier">node</span> <span class="identifier">T</span><span class="special">::</span><span class="special">*</span> PtrToMember<span class="special">,</span> 
             <span class="identifier">link_mode_type</span> LinkMode <span class="special">=</span> <span class="identifier">safe_link</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/member_value_traits.html" title="Struct template member_value_traits">member_value_traits</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.options_hpp"></a>Header &lt;<a href="../../../boost/intrusive/options.hpp" target="_top">boost/intrusive/options.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BaseHook<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/base_hook.html" title="Struct template base_hook">base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BucketTraits<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/bucket_traits.html" title="Struct template bucket_traits">bucket_traits</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/cache_begin.html" title="Struct template cache_begin">cache_begin</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/cache_last.html" title="Struct template cache_last">cache_last</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Compare<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/compare.html" title="Struct template compare">compare</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/compare_hash.html" title="Struct template compare_hash">compare_hash</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/constant_time_size.html" title="Struct template constant_time_size">constant_time_size</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Equal<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/equal.html" title="Struct template equal">equal</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/fastmod_buckets.html" title="Struct template fastmod_buckets">fastmod_buckets</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/floating_point.html" title="Struct template floating_point">floating_point</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Functor<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/function_hook.html" title="Struct template function_hook">function_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Hash<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/hash.html" title="Struct template hash">hash</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> HeaderHolder<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/header_holder_type.html" title="Struct template header_holder_type">header_holder_type</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/incremental.html" title="Struct template incremental">incremental</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyOfValue<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/key_of_value.html" title="Struct template key_of_value">key_of_value</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/linear.html" title="Struct template linear">linear</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/linear_buckets.html" title="Struct template linear_buckets">linear_buckets</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="identifier">link_mode_type</span> LinkType<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/link_mode.html" title="Struct template link_mode">link_mode</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Parent<span class="special">,</span> <span class="keyword">typename</span> MemberHook<span class="special">,</span> 
             <span class="identifier">MemberHook</span> <span class="identifier">Parent</span><span class="special">::</span><span class="special">*</span> PtrToMember<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/member_hook.html" title="Struct template member_hook">member_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/optimize_multikey.html" title="Struct template optimize_multikey">optimize_multikey</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/optimize_size.html" title="Struct template optimize_size">optimize_size</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/power_2_buckets.html" title="Struct template power_2_buckets">power_2_buckets</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Priority<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/priority.html" title="Struct template priority">priority</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> PrioOfValue<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/priority_of_value.html" title="Struct template priority_of_value">priority_of_value</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> SizeType<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/size_type.html" title="Struct template size_type">size_type</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">bool</span> Enabled<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/store_hash.html" title="Struct template store_hash">store_hash</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/tag.html" title="Struct template tag">tag</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueTraits<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/value_traits.html" title="Struct template value_traits">value_traits</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VoidPointer<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/void_pointer.html" title="Struct template void_pointer">void_pointer</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.pack_options_hpp"></a>Header &lt;<a href="../../../boost/intrusive/pack_options.hpp" target="_top">boost/intrusive/pack_options.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis">

<a class="link" href="../BOOST_INTRUSIVE_O_idm36739.html" title="Macro BOOST_INTRUSIVE_OPTION_TYPE">BOOST_INTRUSIVE_OPTION_TYPE</a>(OPTION_NAME, TYPE, TYPEDEF_EXPR, TYPEDEF_NAME)
<a class="link" href="../BOOST_INTRUSIVE_O_idm36753.html" title="Macro BOOST_INTRUSIVE_OPTION_CONSTANT">BOOST_INTRUSIVE_OPTION_CONSTANT</a>(OPTION_NAME, TYPE, VALUE, CONSTANT_NAME)</pre>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> DefaultOptions<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pack_options.html" title="Struct template pack_options">pack_options</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.parent_from_member_hpp"></a>Header &lt;<a href="../../../boost/intrusive/parent_from_member.hpp" target="_top">boost/intrusive/parent_from_member.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Parent<span class="special">,</span> <span class="keyword">typename</span> Member<span class="special">&gt;</span> 
      <span class="identifier">Parent</span> <span class="special">*</span> <a class="link" href="../boost/intrusive/get_parent_from_m_idm36770.html" title="Function template get_parent_from_member"><span class="identifier">get_parent_from_member</span></a><span class="special">(</span><span class="identifier">Member</span> <span class="special">*</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Member</span> <span class="identifier">Parent</span><span class="special">::</span><span class="special">*</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Parent<span class="special">,</span> <span class="keyword">typename</span> Member<span class="special">&gt;</span> 
      <span class="keyword">const</span> <span class="identifier">Parent</span> <span class="special">*</span> 
      <a class="link" href="../boost/intrusive/get_parent_from_m_idm36781.html" title="Function template get_parent_from_member"><span class="identifier">get_parent_from_member</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Member</span> <span class="special">*</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Member</span> <span class="identifier">Parent</span><span class="special">::</span><span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.pointer_plus_bits_hpp"></a>Header &lt;<a href="../../../boost/intrusive/pointer_plus_bits.hpp" target="_top">boost/intrusive/pointer_plus_bits.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VoidPointer<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> Alignment<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/max_pointer_plus_bits.html" title="Struct template max_pointer_plus_bits">max_pointer_plus_bits</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> Alignment<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/max_pointer_plus__idm36804.html" title="Struct template max_pointer_plus_bits&lt;void *, Alignment&gt;">max_pointer_plus_bits</a><span class="special">&lt;</span><span class="keyword">void</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">Alignment</span><span class="special">&gt;</span><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Pointer<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> NumBits<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_plus_bits.html" title="Struct template pointer_plus_bits">pointer_plus_bits</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> NumBits<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_plus_bits_idm36823.html" title="Struct template pointer_plus_bits&lt;T *, NumBits&gt;">pointer_plus_bits</a><span class="special">&lt;</span><span class="identifier">T</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">NumBits</span><span class="special">&gt;</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.pointer_rebind_hpp"></a>Header &lt;<a href="../../../boost/intrusive/pointer_rebind.hpp" target="_top">boost/intrusive/pointer_rebind.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Ptr<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_has_rebind.html" title="Struct template pointer_has_rebind">pointer_has_rebind</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Ptr<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_has_rebind_other.html" title="Struct template pointer_has_rebind_other">pointer_has_rebind_other</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Ptr<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_rebind.html" title="Struct template pointer_rebind">pointer_rebind</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_rebind_T__idm36927.html" title="Struct template pointer_rebind&lt;T *, U&gt;">pointer_rebind</a><span class="special">&lt;</span><span class="identifier">T</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">U</span><span class="special">&gt;</span><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Ptr<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_rebind_mode.html" title="Struct template pointer_rebind_mode">pointer_rebind_mode</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Ptr<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span> RebindMode<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_rebinder.html" title="Struct template pointer_rebinder">pointer_rebinder</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Ptr<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_rebinder__idm36952.html" title="Struct template pointer_rebinder&lt;Ptr, U, 1u&gt;">pointer_rebinder</a><span class="special">&lt;</span><span class="identifier">Ptr</span><span class="special">,</span> <span class="identifier">U</span><span class="special">,</span> <span class="number">1u</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Ptr<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_rebinder__idm36962.html" title="Struct template pointer_rebinder&lt;Ptr, U, 2u&gt;">pointer_rebinder</a><span class="special">&lt;</span><span class="identifier">Ptr</span><span class="special">,</span> <span class="identifier">U</span><span class="special">,</span> <span class="number">2u</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> Ptr<span class="special">,</span> <span class="keyword">typename</span> A<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_rebinder__idm36972.html" title="Struct template pointer_rebinder&lt;Ptr&lt; A &gt;, U, 0u&gt;">pointer_rebinder</a><span class="special">&lt;</span><span class="identifier">Ptr</span><span class="special">&lt;</span> <span class="identifier">A</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">U</span><span class="special">,</span> <span class="number">0u</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> <span class="special">&gt;</span> <span class="keyword">class</span> Ptr<span class="special">,</span> <span class="keyword">typename</span> A<span class="special">,</span> <span class="keyword">class</span><span class="special">...</span> An<span class="special">,</span> 
             <span class="keyword">typename</span> U<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_rebinder__idm36984.html" title="Struct template pointer_rebinder&lt;Ptr&lt; A, An... &gt;, U, 0u&gt;">pointer_rebinder</a><span class="special">&lt;</span><span class="identifier">Ptr</span><span class="special">&lt;</span> <span class="identifier">A</span><span class="special">,</span> <span class="identifier">An</span><span class="special">...</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">U</span><span class="special">,</span> <span class="number">0u</span><span class="special">&gt;</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.pointer_traits_hpp"></a>Header &lt;<a href="../../../boost/intrusive/pointer_traits.hpp" target="_top">boost/intrusive/pointer_traits.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Ptr<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_traits.html" title="Struct template pointer_traits">pointer_traits</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/pointer_traits_T_idm37097.html" title="Struct template pointer_traits&lt;T *&gt;">pointer_traits</a><span class="special">&lt;</span><span class="identifier">T</span> <span class="special">*</span><span class="special">&gt;</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.priority_compare_hpp"></a>Header &lt;<a href="../../../boost/intrusive/priority_compare.hpp" target="_top">boost/intrusive/priority_compare.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/priority_compare.html" title="Struct template priority_compare">priority_compare</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/priority_compare__idm37182.html" title="Struct priority_compare&lt;void&gt;">priority_compare</a><span class="special">&lt;</span><span class="keyword">void</span><span class="special">&gt;</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.rbtree_hpp"></a>Header &lt;<a href="../../../boost/intrusive/rbtree.hpp" target="_top">boost/intrusive/rbtree.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_rbtree.html" title="Struct template make_rbtree">make_rbtree</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/rbtree.html" title="Class template rbtree">rbtree</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.rbtree_algorithms_hpp"></a>Header &lt;<a href="../../../boost/intrusive/rbtree_algorithms.hpp" target="_top">boost/intrusive/rbtree_algorithms.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> NodeTraits<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/rbtree_algorithms.html" title="Class template rbtree_algorithms">rbtree_algorithms</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.set_hpp"></a>Header &lt;<a href="../../../boost/intrusive/set.hpp" target="_top">boost/intrusive/set.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_multiset.html" title="Struct template make_multiset">make_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_set.html" title="Struct template make_set">make_set</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/multiset.html" title="Class template multiset">multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/set.html" title="Class template set">set</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator!=_idm42076"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/set.html" title="Class template set">set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/set.html" title="Class template set">set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_idm42088"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/set.html" title="Class template set">set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="../boost/intrusive/set.html" title="Class template set">set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm42100"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/set.html" title="Class template set">set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/set.html" title="Class template set">set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm42112"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/set.html" title="Class template set">set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/set.html" title="Class template set">set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.intrusive.swap_idm42124"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="../boost/intrusive/set.html" title="Class template set">set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> <a class="link" href="../boost/intrusive/set.html" title="Class template set">set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator!=_idm42136"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/multiset.html" title="Class template multiset">multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/multiset.html" title="Class template multiset">multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_idm42148"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/multiset.html" title="Class template multiset">multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="../boost/intrusive/multiset.html" title="Class template multiset">multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm42160"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/multiset.html" title="Class template multiset">multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/multiset.html" title="Class template multiset">multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm42172"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/multiset.html" title="Class template multiset">multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/multiset.html" title="Class template multiset">multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.intrusive.swap_idm42184"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="../boost/intrusive/multiset.html" title="Class template multiset">multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> <a class="link" href="../boost/intrusive/multiset.html" title="Class template multiset">multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.set_hook_hpp"></a>Header &lt;<a href="../../../boost/intrusive/set_hook.hpp" target="_top">boost/intrusive/set_hook.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_set_base_hook.html" title="Struct template make_set_base_hook">make_set_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_set_member_hook.html" title="Struct template make_set_member_hook">make_set_member_hook</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/set_base_hook.html" title="Class template set_base_hook">set_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/set_member_hook.html" title="Class template set_member_hook">set_member_hook</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.sg_set_hpp"></a>Header &lt;<a href="../../../boost/intrusive/sg_set.hpp" target="_top">boost/intrusive/sg_set.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_sg_multiset.html" title="Struct template make_sg_multiset">make_sg_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_sg_set.html" title="Struct template make_sg_set">make_sg_set</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/sg_multiset.html" title="Class template sg_multiset">sg_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/sg_set.html" title="Class template sg_set">sg_set</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator!=_idm45242"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_set.html" title="Class template sg_set">sg_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_set.html" title="Class template sg_set">sg_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_idm45254"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_set.html" title="Class template sg_set">sg_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_set.html" title="Class template sg_set">sg_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm45266"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_set.html" title="Class template sg_set">sg_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_set.html" title="Class template sg_set">sg_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm45278"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_set.html" title="Class template sg_set">sg_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_set.html" title="Class template sg_set">sg_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.intrusive.swap_idm45290"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="../boost/intrusive/sg_set.html" title="Class template sg_set">sg_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> <a class="link" href="../boost/intrusive/sg_set.html" title="Class template sg_set">sg_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator!=_idm45302"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_multiset.html" title="Class template sg_multiset">sg_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_multiset.html" title="Class template sg_multiset">sg_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_idm45314"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_multiset.html" title="Class template sg_multiset">sg_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_multiset.html" title="Class template sg_multiset">sg_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm45326"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_multiset.html" title="Class template sg_multiset">sg_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_multiset.html" title="Class template sg_multiset">sg_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm45338"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_multiset.html" title="Class template sg_multiset">sg_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/sg_multiset.html" title="Class template sg_multiset">sg_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.intrusive.swap_idm45350"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="../boost/intrusive/sg_multiset.html" title="Class template sg_multiset">sg_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                <a class="link" href="../boost/intrusive/sg_multiset.html" title="Class template sg_multiset">sg_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.sgtree_hpp"></a>Header &lt;<a href="../../../boost/intrusive/sgtree.hpp" target="_top">boost/intrusive/sgtree.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_sgtree.html" title="Struct template make_sgtree">make_sgtree</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/sgtree.html" title="Class template sgtree">sgtree</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.sgtree_algorithms_hpp"></a>Header &lt;<a href="../../../boost/intrusive/sgtree_algorithms.hpp" target="_top">boost/intrusive/sgtree_algorithms.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> NodeTraits<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/sgtree_algorithms.html" title="Class template sgtree_algorithms">sgtree_algorithms</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.slist_hpp"></a>Header &lt;<a href="../../../boost/intrusive/slist.hpp" target="_top">boost/intrusive/slist.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_slist.html" title="Struct template make_slist">make_slist</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/slist.html" title="Class template slist">slist</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.slist_hook_hpp"></a>Header &lt;<a href="../../../boost/intrusive/slist_hook.hpp" target="_top">boost/intrusive/slist_hook.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_slist_base_hook.html" title="Struct template make_slist_base_hook">make_slist_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_slist_member_hook.html" title="Struct template make_slist_member_hook">make_slist_member_hook</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/slist_base_hook.html" title="Class template slist_base_hook">slist_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/slist_member_hook.html" title="Class template slist_member_hook">slist_member_hook</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.splay_set_hpp"></a>Header &lt;<a href="../../../boost/intrusive/splay_set.hpp" target="_top">boost/intrusive/splay_set.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_splay_multiset.html" title="Struct template make_splay_multiset">make_splay_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_splay_set.html" title="Struct template make_splay_set">make_splay_set</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/splay_multiset.html" title="Class template splay_multiset">splay_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/splay_set.html" title="Class template splay_set">splay_set</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator!=_idm52261"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_set.html" title="Class template splay_set">splay_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_set.html" title="Class template splay_set">splay_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_idm52273"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_set.html" title="Class template splay_set">splay_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_set.html" title="Class template splay_set">splay_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm52285"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_set.html" title="Class template splay_set">splay_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_set.html" title="Class template splay_set">splay_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm52297"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_set.html" title="Class template splay_set">splay_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_set.html" title="Class template splay_set">splay_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.intrusive.swap_idm52309"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="../boost/intrusive/splay_set.html" title="Class template splay_set">splay_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                <a class="link" href="../boost/intrusive/splay_set.html" title="Class template splay_set">splay_set</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator!=_idm52321"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_multiset.html" title="Class template splay_multiset">splay_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_multiset.html" title="Class template splay_multiset">splay_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_idm52333"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_multiset.html" title="Class template splay_multiset">splay_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_multiset.html" title="Class template splay_multiset">splay_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm52345"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_multiset.html" title="Class template splay_multiset">splay_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_multiset.html" title="Class template splay_multiset">splay_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">bool</span> <a name="boost.intrusive.operator_=_idm52357"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_multiset.html" title="Class template splay_multiset">splay_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                      <span class="keyword">const</span> <a class="link" href="../boost/intrusive/splay_multiset.html" title="Class template splay_multiset">splay_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.intrusive.swap_idm52369"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="../boost/intrusive/splay_multiset.html" title="Class template splay_multiset">splay_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> x<span class="special">,</span> 
                <a class="link" href="../boost/intrusive/splay_multiset.html" title="Class template splay_multiset">splay_multiset</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.splaytree_hpp"></a>Header &lt;<a href="../../../boost/intrusive/splaytree.hpp" target="_top">boost/intrusive/splaytree.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_splaytree.html" title="Struct template make_splaytree">make_splaytree</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/splaytree.html" title="Class template splaytree">splaytree</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.splaytree_algorithms_hpp"></a>Header &lt;<a href="../../../boost/intrusive/splaytree_algorithms.hpp" target="_top">boost/intrusive/splaytree_algorithms.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> NodeTraits<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/splaytree_algorithms.html" title="Class template splaytree_algorithms">splaytree_algorithms</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.treap_hpp"></a>Header &lt;<a href="../../../boost/intrusive/treap.hpp" target="_top">boost/intrusive/treap.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_treap.html" title="Struct template make_treap">make_treap</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/treap.html" title="Class template treap">treap</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.treap_algorithms_hpp"></a>Header &lt;<a href="../../../boost/intrusive/treap_algorithms.hpp" target="_top">boost/intrusive/treap_algorithms.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> NodeTraits<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/treap_algorithms.html" title="Class template treap_algorithms">treap_algorithms</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.treap_set_hpp"></a>Header &lt;<a href="../../../boost/intrusive/treap_set.hpp" target="_top">boost/intrusive/treap_set.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_treap_multiset.html" title="Struct template make_treap_multiset">make_treap_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_treap_set.html" title="Struct template make_treap_set">make_treap_set</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/treap_multiset.html" title="Class template treap_multiset">treap_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/treap_set.html" title="Class template treap_set">treap_set</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.trivial_value_traits_hpp"></a>Header &lt;<a href="../../../boost/intrusive/trivial_value_traits.hpp" target="_top">boost/intrusive/trivial_value_traits.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> NodeTraits<span class="special">,</span> <span class="identifier">link_mode_type</span> LinkMode <span class="special">=</span> <span class="identifier">safe_link</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/trivial_value_traits.html" title="Struct template trivial_value_traits">trivial_value_traits</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.unordered_set_hpp"></a>Header &lt;<a href="../../../boost/intrusive/unordered_set.hpp" target="_top">boost/intrusive/unordered_set.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_unordered_multiset.html" title="Struct template make_unordered_multiset">make_unordered_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_unordered_set.html" title="Struct template make_unordered_set">make_unordered_set</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/unordered_multiset.html" title="Class template unordered_multiset">unordered_multiset</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/unordered_set.html" title="Class template unordered_set">unordered_set</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="header.boost.intrusive.unordered_set_hook_hpp"></a>Header &lt;<a href="../../../boost/intrusive/unordered_set_hook.hpp" target="_top">boost/intrusive/unordered_set_hook.hpp</a>&gt;</h3></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">intrusive</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_unordered_se_idm62420.html" title="Struct template make_unordered_set_base_hook">make_unordered_set_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/intrusive/make_unordered_se_idm62430.html" title="Struct template make_unordered_set_member_hook">make_unordered_set_member_hook</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/unordered_set_base_hook.html" title="Class template unordered_set_base_hook">unordered_set_base_hook</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/intrusive/unordered_set_member_hook.html" title="Class template unordered_set_member_hook">unordered_set_member_hook</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
</div>
<div class="copyright-footer">Copyright © 2005 Olaf Krzikalla<br>Copyright © 2006-2015 Ion Gaztanaga<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="index.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../intrusive.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost/intrusive/any_base_hook.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
