<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Support for Other Protocols</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../networking.html" title="Networking">
<link rel="prev" href="protocols.html" title="TCP, UDP and ICMP">
<link rel="next" href="iostreams.html" title="Socket Iostreams">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="protocols.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../networking.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="iostreams.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_asio.overview.networking.other_protocols"></a><a class="link" href="other_protocols.html" title="Support for Other Protocols">Support
        for Other Protocols</a>
</h4></div></div></div>
<p>
          Support for other socket protocols (such as Bluetooth or IRCOMM sockets)
          can be added by implementing the <a class="link" href="../../reference/Protocol.html" title="Protocol requirements">protocol
          type requirements</a>. However, in many cases these protocols may also
          be used with Boost.Asio's generic protocol support. For this, Boost.Asio
          provides the following four classes:
        </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
              <a class="link" href="../../reference/generic__datagram_protocol.html" title="generic::datagram_protocol"><code class="computeroutput"><span class="identifier">generic</span><span class="special">::</span><span class="identifier">datagram_protocol</span></code></a>
            </li>
<li class="listitem">
              <a class="link" href="../../reference/generic__raw_protocol.html" title="generic::raw_protocol"><code class="computeroutput"><span class="identifier">generic</span><span class="special">::</span><span class="identifier">raw_protocol</span></code></a>
            </li>
<li class="listitem">
              <a class="link" href="../../reference/generic__seq_packet_protocol.html" title="generic::seq_packet_protocol"><code class="computeroutput"><span class="identifier">generic</span><span class="special">::</span><span class="identifier">seq_packet_protocol</span></code></a>
            </li>
<li class="listitem">
              <a class="link" href="../../reference/generic__stream_protocol.html" title="generic::stream_protocol"><code class="computeroutput"><span class="identifier">generic</span><span class="special">::</span><span class="identifier">stream_protocol</span></code></a>
            </li>
</ul></div>
<p>
          These classes implement the <a class="link" href="../../reference/Protocol.html" title="Protocol requirements">protocol
          type requirements</a>, but allow the user to specify the address family
          (e.g. <code class="computeroutput"><span class="identifier">AF_INET</span></code>) and protocol
          type (e.g. <code class="computeroutput"><span class="identifier">IPPROTO_TCP</span></code>)
          at runtime. For example:
        </p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">generic</span><span class="special">::</span><span class="identifier">stream_protocol</span><span class="special">::</span><span class="identifier">socket</span> <span class="identifier">my_socket</span><span class="special">(</span><span class="identifier">my_io_context</span><span class="special">);</span>
<span class="identifier">my_socket</span><span class="special">.</span><span class="identifier">open</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">generic</span><span class="special">::</span><span class="identifier">stream_protocol</span><span class="special">(</span><span class="identifier">AF_INET</span><span class="special">,</span> <span class="identifier">IPPROTO_TCP</span><span class="special">));</span>
<span class="special">...</span>
</pre>
<p>
          An endpoint class template, <a class="link" href="../../reference/generic__basic_endpoint.html" title="generic::basic_endpoint"><code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">generic</span><span class="special">::</span><span class="identifier">basic_endpoint</span></code></a>, is included to
          support these protocol classes. This endpoint can hold any other endpoint
          type, provided its native representation fits into a <code class="computeroutput"><span class="identifier">sockaddr_storage</span></code>
          object. This class will also convert from other types that implement the
          <a class="link" href="../../reference/Endpoint.html" title="Endpoint requirements">endpoint</a> type requirements:
        </p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">ip</span><span class="special">::</span><span class="identifier">tcp</span><span class="special">::</span><span class="identifier">endpoint</span> <span class="identifier">my_endpoint1</span> <span class="special">=</span> <span class="special">...;</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">generic</span><span class="special">::</span><span class="identifier">stream_protocol</span><span class="special">::</span><span class="identifier">endpoint</span> <span class="identifier">my_endpoint2</span><span class="special">(</span><span class="identifier">my_endpoint1</span><span class="special">);</span>
</pre>
<p>
          The conversion is implicit, so as to support the following use cases:
        </p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">generic</span><span class="special">::</span><span class="identifier">stream_protocol</span><span class="special">::</span><span class="identifier">socket</span> <span class="identifier">my_socket</span><span class="special">(</span><span class="identifier">my_io_context</span><span class="special">);</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">ip</span><span class="special">::</span><span class="identifier">tcp</span><span class="special">::</span><span class="identifier">endpoint</span> <span class="identifier">my_endpoint</span> <span class="special">=</span> <span class="special">...;</span>
<span class="identifier">my_socket</span><span class="special">.</span><span class="identifier">connect</span><span class="special">(</span><span class="identifier">my_endpoint</span><span class="special">);</span>
</pre>
<h6>
<a name="boost_asio.overview.networking.other_protocols.h0"></a>
          <span class="phrase"><a name="boost_asio.overview.networking.other_protocols.c__11_move_construction"></a></span><a class="link" href="other_protocols.html#boost_asio.overview.networking.other_protocols.c__11_move_construction">C++11
          Move Construction</a>
        </h6>
<p>
          When using C++11, it is possible to perform move construction from a socket
          (or acceptor) object to convert to the more generic protocol's socket (or
          acceptor) type. If the protocol conversion is valid:
        </p>
<pre class="programlisting"><span class="identifier">Protocol1</span> <span class="identifier">p1</span> <span class="special">=</span> <span class="special">...;</span>
<span class="identifier">Protocol2</span> <span class="identifier">p2</span><span class="special">(</span><span class="identifier">p1</span><span class="special">);</span>
</pre>
<p>
          then the corresponding socket conversion is allowed:
        </p>
<pre class="programlisting"><span class="identifier">Protocol1</span><span class="special">::</span><span class="identifier">socket</span> <span class="identifier">my_socket1</span><span class="special">(</span><span class="identifier">my_io_context</span><span class="special">);</span>
<span class="special">...</span>
<span class="identifier">Protocol2</span><span class="special">::</span><span class="identifier">socket</span> <span class="identifier">my_socket2</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">move</span><span class="special">(</span><span class="identifier">my_socket1</span><span class="special">));</span>
</pre>
<p>
          For example, one possible conversion is from a TCP socket to a generic
          stream-oriented socket:
        </p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">ip</span><span class="special">::</span><span class="identifier">tcp</span><span class="special">::</span><span class="identifier">socket</span> <span class="identifier">my_socket1</span><span class="special">(</span><span class="identifier">my_io_context</span><span class="special">);</span>
<span class="special">...</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">generic</span><span class="special">::</span><span class="identifier">stream_protocol</span><span class="special">::</span><span class="identifier">socket</span> <span class="identifier">my_socket2</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">move</span><span class="special">(</span><span class="identifier">my_socket1</span><span class="special">));</span>
</pre>
<p>
          These conversions are also available for move-assignment.
        </p>
<p>
          These conversions are not limited to the above generic protocol classes.
          User-defined protocols may take advantage of this feature by similarly
          ensuring the conversion from <code class="computeroutput"><span class="identifier">Protocol1</span></code>
          to <code class="computeroutput"><span class="identifier">Protocol2</span></code> is valid,
          as above.
        </p>
<h6>
<a name="boost_asio.overview.networking.other_protocols.h1"></a>
          <span class="phrase"><a name="boost_asio.overview.networking.other_protocols.accepting_generic_sockets"></a></span><a class="link" href="other_protocols.html#boost_asio.overview.networking.other_protocols.accepting_generic_sockets">Accepting
          Generic Sockets</a>
        </h6>
<p>
          As a convenience, a socket acceptor's <code class="computeroutput"><span class="identifier">accept</span><span class="special">()</span></code> and <code class="computeroutput"><span class="identifier">async_accept</span><span class="special">()</span></code> functions can directly accept into a
          different protocol's socket type, provided the corresponding protocol conversion
          is valid. For example, the following is supported because the protocol
          <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">ip</span><span class="special">::</span><span class="identifier">tcp</span></code> is convertible to <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">generic</span><span class="special">::</span><span class="identifier">stream_protocol</span></code>:
        </p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">ip</span><span class="special">::</span><span class="identifier">tcp</span><span class="special">::</span><span class="identifier">acceptor</span> <span class="identifier">my_acceptor</span><span class="special">(</span><span class="identifier">my_io_context</span><span class="special">);</span>
<span class="special">...</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">generic</span><span class="special">::</span><span class="identifier">stream_protocol</span><span class="special">::</span><span class="identifier">socket</span> <span class="identifier">my_socket</span><span class="special">(</span><span class="identifier">my_io_context</span><span class="special">);</span>
<span class="identifier">my_acceptor</span><span class="special">.</span><span class="identifier">accept</span><span class="special">(</span><span class="identifier">my_socket</span><span class="special">);</span>
</pre>
<h6>
<a name="boost_asio.overview.networking.other_protocols.h2"></a>
          <span class="phrase"><a name="boost_asio.overview.networking.other_protocols.see_also"></a></span><a class="link" href="other_protocols.html#boost_asio.overview.networking.other_protocols.see_also">See Also</a>
        </h6>
<p>
          <a class="link" href="../../reference/generic__datagram_protocol.html" title="generic::datagram_protocol"><code class="computeroutput"><span class="identifier">generic</span><span class="special">::</span><span class="identifier">datagram_protocol</span></code></a>, <a class="link" href="../../reference/generic__raw_protocol.html" title="generic::raw_protocol"><code class="computeroutput"><span class="identifier">generic</span><span class="special">::</span><span class="identifier">raw_protocol</span></code></a>, <a class="link" href="../../reference/generic__seq_packet_protocol.html" title="generic::seq_packet_protocol"><code class="computeroutput"><span class="identifier">generic</span><span class="special">::</span><span class="identifier">seq_packet_protocol</span></code></a>, <a class="link" href="../../reference/generic__stream_protocol.html" title="generic::stream_protocol"><code class="computeroutput"><span class="identifier">generic</span><span class="special">::</span><span class="identifier">stream_protocol</span></code></a>, <a class="link" href="../../reference/Protocol.html" title="Protocol requirements">protocol
          type requirements</a>.
        </p>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="protocols.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../networking.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="iostreams.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
