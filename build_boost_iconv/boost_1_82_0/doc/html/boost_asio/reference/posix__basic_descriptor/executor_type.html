<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>posix::basic_descriptor::executor_type</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../posix__basic_descriptor.html" title="posix::basic_descriptor">
<link rel="prev" href="close/overload2.html" title="posix::basic_descriptor::close (2 of 2 overloads)">
<link rel="next" href="get_executor.html" title="posix::basic_descriptor::get_executor">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="close/overload2.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../posix__basic_descriptor.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="get_executor.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_asio.reference.posix__basic_descriptor.executor_type"></a><a class="link" href="executor_type.html" title="posix::basic_descriptor::executor_type">posix::basic_descriptor::executor_type</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="boost_asio.indexterm.posix__basic_descriptor.executor_type"></a> 
The
          type of the executor associated with the object.
        </p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">Executor</span> <span class="identifier">executor_type</span><span class="special">;</span>
</pre>
<h6>
<a name="boost_asio.reference.posix__basic_descriptor.executor_type.h0"></a>
          <span class="phrase"><a name="boost_asio.reference.posix__basic_descriptor.executor_type.requirements"></a></span><a class="link" href="executor_type.html#boost_asio.reference.posix__basic_descriptor.executor_type.requirements">Requirements</a>
        </h6>
<p>
          <span class="emphasis"><em>Header: </em></span><code class="literal">boost/asio/posix/basic_descriptor.hpp</code>
        </p>
<p>
          <span class="emphasis"><em>Convenience header: </em></span><code class="literal">boost/asio.hpp</code>
        </p>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="close/overload2.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../posix__basic_descriptor.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="get_executor.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
