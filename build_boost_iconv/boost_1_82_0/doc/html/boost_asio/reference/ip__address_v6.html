<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ip::address_v6</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../reference.html" title="Reference">
<link rel="prev" href="ip__address_v4_range.html" title="ip::address_v4_range">
<link rel="next" href="ip__address_v6/address_v6.html" title="ip::address_v6::address_v6">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="ip__address_v4_range.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../reference.html"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../boost_asio.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="ip__address_v6/address_v6.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_asio.reference.ip__address_v6"></a><a class="link" href="ip__address_v6.html" title="ip::address_v6">ip::address_v6</a>
</h3></div></div></div>
<p>
        <a class="indexterm" name="boost_asio.indexterm.ip__address_v6"></a>
      </p>
<p>
        Implements IP version 6 style addresses.
      </p>
<pre class="programlisting"><span class="keyword">class</span> <span class="identifier">address_v6</span>
</pre>
<h5>
<a name="boost_asio.reference.ip__address_v6.h0"></a>
        <span class="phrase"><a name="boost_asio.reference.ip__address_v6.types"></a></span><a class="link" href="ip__address_v6.html#boost_asio.reference.ip__address_v6.types">Types</a>
      </h5>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Name
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody><tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/bytes_type.html" title="ip::address_v6::bytes_type"><span class="bold"><strong>bytes_type</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  The type used to represent an address as an array of bytes.
                </p>
              </td>
</tr></tbody>
</table></div>
<h5>
<a name="boost_asio.reference.ip__address_v6.h1"></a>
        <span class="phrase"><a name="boost_asio.reference.ip__address_v6.member_functions"></a></span><a class="link" href="ip__address_v6.html#boost_asio.reference.ip__address_v6.member_functions">Member Functions</a>
      </h5>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Name
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/address_v6.html" title="ip::address_v6::address_v6"><span class="bold"><strong>address_v6</strong></span></a> <span class="silver">[constructor]</span>
                </p>
              </td>
<td>
                <p>
                  Default constructor. <br> <span class="silver"> —</span><br> Construct an address from raw
                  bytes and scope ID. <br> <span class="silver"> —</span><br> Copy constructor.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/any.html" title="ip::address_v6::any"><span class="bold"><strong>any</strong></span></a> <span class="silver">[static]</span>
                </p>
              </td>
<td>
                <p>
                  Obtain an address object that represents any address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/from_string.html" title="ip::address_v6::from_string"><span class="bold"><strong>from_string</strong></span></a> <span class="silver">[static]</span>
                </p>
              </td>
<td>
                <p>
                  (Deprecated: Use make_address_v6().) Create an IPv6 address from
                  an IP address string.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/is_link_local.html" title="ip::address_v6::is_link_local"><span class="bold"><strong>is_link_local</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Determine whether the address is link local.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/is_loopback.html" title="ip::address_v6::is_loopback"><span class="bold"><strong>is_loopback</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Determine whether the address is a loopback address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/is_multicast.html" title="ip::address_v6::is_multicast"><span class="bold"><strong>is_multicast</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Determine whether the address is a multicast address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/is_multicast_global.html" title="ip::address_v6::is_multicast_global"><span class="bold"><strong>is_multicast_global</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Determine whether the address is a global multicast address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/is_multicast_link_local.html" title="ip::address_v6::is_multicast_link_local"><span class="bold"><strong>is_multicast_link_local</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Determine whether the address is a link-local multicast address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/is_multicast_node_local.html" title="ip::address_v6::is_multicast_node_local"><span class="bold"><strong>is_multicast_node_local</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Determine whether the address is a node-local multicast address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/is_multicast_org_local.html" title="ip::address_v6::is_multicast_org_local"><span class="bold"><strong>is_multicast_org_local</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Determine whether the address is a org-local multicast address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/is_multicast_site_local.html" title="ip::address_v6::is_multicast_site_local"><span class="bold"><strong>is_multicast_site_local</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Determine whether the address is a site-local multicast address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/is_site_local.html" title="ip::address_v6::is_site_local"><span class="bold"><strong>is_site_local</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Determine whether the address is site local.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/is_unspecified.html" title="ip::address_v6::is_unspecified"><span class="bold"><strong>is_unspecified</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Determine whether the address is unspecified.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/is_v4_compatible.html" title="ip::address_v6::is_v4_compatible"><span class="bold"><strong>is_v4_compatible</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  (Deprecated: No replacement.) Determine whether the address is
                  an IPv4-compatible address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/is_v4_mapped.html" title="ip::address_v6::is_v4_mapped"><span class="bold"><strong>is_v4_mapped</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Determine whether the address is a mapped IPv4 address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/loopback.html" title="ip::address_v6::loopback"><span class="bold"><strong>loopback</strong></span></a> <span class="silver">[static]</span>
                </p>
              </td>
<td>
                <p>
                  Obtain an address object that represents the loopback address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/operator_eq_.html" title="ip::address_v6::operator="><span class="bold"><strong>operator=</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Assign from another address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/scope_id.html" title="ip::address_v6::scope_id"><span class="bold"><strong>scope_id</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  The scope ID of the address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/to_bytes.html" title="ip::address_v6::to_bytes"><span class="bold"><strong>to_bytes</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Get the address in bytes, in network byte order.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/to_string.html" title="ip::address_v6::to_string"><span class="bold"><strong>to_string</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Get the address as a string. <br> <span class="silver"> —</span><br> (Deprecated: Use other
                  overload.) Get the address as a string.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/to_v4.html" title="ip::address_v6::to_v4"><span class="bold"><strong>to_v4</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  (Deprecated: Use make_address_v4().) Converts an IPv4-mapped or
                  IPv4-compatible address to an IPv4 address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/v4_compatible.html" title="ip::address_v6::v4_compatible"><span class="bold"><strong>v4_compatible</strong></span></a> <span class="silver">[static]</span>
                </p>
              </td>
<td>
                <p>
                  (Deprecated: No replacement.) Create an IPv4-compatible IPv6 address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/v4_mapped.html" title="ip::address_v6::v4_mapped"><span class="bold"><strong>v4_mapped</strong></span></a> <span class="silver">[static]</span>
                </p>
              </td>
<td>
                <p>
                  (Deprecated: Use make_address_v6().) Create an IPv4-mapped IPv6
                  address.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<h5>
<a name="boost_asio.reference.ip__address_v6.h2"></a>
        <span class="phrase"><a name="boost_asio.reference.ip__address_v6.friends"></a></span><a class="link" href="ip__address_v6.html#boost_asio.reference.ip__address_v6.friends">Friends</a>
      </h5>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Name
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/operator_not__eq_.html" title="ip::address_v6::operator!="><span class="bold"><strong>operator!=</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Compare two addresses for inequality.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/operator_lt_.html" title="ip::address_v6::operator&lt;"><span class="bold"><strong>operator&lt;</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Compare addresses for ordering.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/operator_lt__eq_.html" title="ip::address_v6::operator&lt;="><span class="bold"><strong>operator&lt;=</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Compare addresses for ordering.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/operator_eq__eq_.html" title="ip::address_v6::operator=="><span class="bold"><strong>operator==</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Compare two addresses for equality.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/operator_gt_.html" title="ip::address_v6::operator&gt;"><span class="bold"><strong>operator&gt;</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Compare addresses for ordering.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/operator_gt__eq_.html" title="ip::address_v6::operator&gt;="><span class="bold"><strong>operator&gt;=</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Compare addresses for ordering.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<h5>
<a name="boost_asio.reference.ip__address_v6.h3"></a>
        <span class="phrase"><a name="boost_asio.reference.ip__address_v6.related_functions"></a></span><a class="link" href="ip__address_v6.html#boost_asio.reference.ip__address_v6.related_functions">Related Functions</a>
      </h5>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Name
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/make_address_v6.html" title="ip::address_v6::make_address_v6"><span class="bold"><strong>make_address_v6</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Create an IPv6 address from raw bytes and scope ID.
                </p>
                <p>
                  Create an IPv6 address from an IP address string.
                </p>
                <p>
                  Createan IPv6 address from an IP address string.
                </p>
                <p>
                  Create an IPv4-mapped IPv6 address from an IPv4 address.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/make_network_v6.html" title="ip::address_v6::make_network_v6"><span class="bold"><strong>make_network_v6</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Create an IPv6 network from an address and prefix length.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__address_v6/operator_lt__lt_.html" title="ip::address_v6::operator&lt;&lt;"><span class="bold"><strong>operator&lt;&lt;</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Output an address as a string.
                </p>
                <p>
                  Output a network as a string.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<p>
        The <a class="link" href="ip__address_v6.html" title="ip::address_v6"><code class="computeroutput"><span class="identifier">ip</span><span class="special">::</span><span class="identifier">address_v6</span></code></a>
        class provides the ability to use and manipulate IP version 6 addresses.
      </p>
<h5>
<a name="boost_asio.reference.ip__address_v6.h4"></a>
        <span class="phrase"><a name="boost_asio.reference.ip__address_v6.thread_safety"></a></span><a class="link" href="ip__address_v6.html#boost_asio.reference.ip__address_v6.thread_safety">Thread
        Safety</a>
      </h5>
<p>
        <span class="emphasis"><em>Distinct</em></span> <span class="emphasis"><em>objects:</em></span> Safe.
      </p>
<p>
        <span class="emphasis"><em>Shared</em></span> <span class="emphasis"><em>objects:</em></span> Unsafe.
      </p>
<h5>
<a name="boost_asio.reference.ip__address_v6.h5"></a>
        <span class="phrase"><a name="boost_asio.reference.ip__address_v6.requirements"></a></span><a class="link" href="ip__address_v6.html#boost_asio.reference.ip__address_v6.requirements">Requirements</a>
      </h5>
<p>
        <span class="emphasis"><em>Header: </em></span><code class="literal">boost/asio/ip/address_v6.hpp</code>
      </p>
<p>
        <span class="emphasis"><em>Convenience header: </em></span><code class="literal">boost/asio.hpp</code>
      </p>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="ip__address_v4_range.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../reference.html"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../boost_asio.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="ip__address_v6/address_v6.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
