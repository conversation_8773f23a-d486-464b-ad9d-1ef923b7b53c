<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ip::icmp</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../reference.html" title="Reference">
<link rel="prev" href="ip__host_name/overload2.html" title="ip::host_name (2 of 2 overloads)">
<link rel="next" href="ip__icmp/endpoint.html" title="ip::icmp::endpoint">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="ip__host_name/overload2.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../reference.html"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../boost_asio.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="ip__icmp/endpoint.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_asio.reference.ip__icmp"></a><a class="link" href="ip__icmp.html" title="ip::icmp">ip::icmp</a>
</h3></div></div></div>
<p>
        <a class="indexterm" name="boost_asio.indexterm.ip__icmp"></a>
      </p>
<p>
        Encapsulates the flags needed for ICMP.
      </p>
<pre class="programlisting"><span class="keyword">class</span> <span class="identifier">icmp</span>
</pre>
<h5>
<a name="boost_asio.reference.ip__icmp.h0"></a>
        <span class="phrase"><a name="boost_asio.reference.ip__icmp.types"></a></span><a class="link" href="ip__icmp.html#boost_asio.reference.ip__icmp.types">Types</a>
      </h5>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Name
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <a class="link" href="ip__icmp/endpoint.html" title="ip::icmp::endpoint"><span class="bold"><strong>endpoint</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  The type of a ICMP endpoint.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__icmp/resolver.html" title="ip::icmp::resolver"><span class="bold"><strong>resolver</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  The ICMP resolver type.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__icmp/socket.html" title="ip::icmp::socket"><span class="bold"><strong>socket</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  The ICMP socket type.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<h5>
<a name="boost_asio.reference.ip__icmp.h1"></a>
        <span class="phrase"><a name="boost_asio.reference.ip__icmp.member_functions"></a></span><a class="link" href="ip__icmp.html#boost_asio.reference.ip__icmp.member_functions">Member
        Functions</a>
      </h5>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Name
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <a class="link" href="ip__icmp/family.html" title="ip::icmp::family"><span class="bold"><strong>family</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Obtain an identifier for the protocol family.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__icmp/protocol.html" title="ip::icmp::protocol"><span class="bold"><strong>protocol</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Obtain an identifier for the protocol.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__icmp/type.html" title="ip::icmp::type"><span class="bold"><strong>type</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Obtain an identifier for the type of the protocol.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__icmp/v4.html" title="ip::icmp::v4"><span class="bold"><strong>v4</strong></span></a>
                  <span class="silver">[static]</span>
                </p>
              </td>
<td>
                <p>
                  Construct to represent the IPv4 ICMP protocol.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__icmp/v6.html" title="ip::icmp::v6"><span class="bold"><strong>v6</strong></span></a>
                  <span class="silver">[static]</span>
                </p>
              </td>
<td>
                <p>
                  Construct to represent the IPv6 ICMP protocol.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<h5>
<a name="boost_asio.reference.ip__icmp.h2"></a>
        <span class="phrase"><a name="boost_asio.reference.ip__icmp.friends"></a></span><a class="link" href="ip__icmp.html#boost_asio.reference.ip__icmp.friends">Friends</a>
      </h5>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Name
                </p>
              </th>
<th>
                <p>
                  Description
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  <a class="link" href="ip__icmp/operator_not__eq_.html" title="ip::icmp::operator!="><span class="bold"><strong>operator!=</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Compare two protocols for inequality.
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  <a class="link" href="ip__icmp/operator_eq__eq_.html" title="ip::icmp::operator=="><span class="bold"><strong>operator==</strong></span></a>
                </p>
              </td>
<td>
                <p>
                  Compare two protocols for equality.
                </p>
              </td>
</tr>
</tbody>
</table></div>
<p>
        The <a class="link" href="ip__icmp.html" title="ip::icmp"><code class="computeroutput"><span class="identifier">ip</span><span class="special">::</span><span class="identifier">icmp</span></code></a>
        class contains flags necessary for ICMP sockets.
      </p>
<h5>
<a name="boost_asio.reference.ip__icmp.h3"></a>
        <span class="phrase"><a name="boost_asio.reference.ip__icmp.thread_safety"></a></span><a class="link" href="ip__icmp.html#boost_asio.reference.ip__icmp.thread_safety">Thread
        Safety</a>
      </h5>
<p>
        <span class="emphasis"><em>Distinct</em></span> <span class="emphasis"><em>objects:</em></span> Safe.
      </p>
<p>
        <span class="emphasis"><em>Shared</em></span> <span class="emphasis"><em>objects:</em></span> Safe.
      </p>
<h5>
<a name="boost_asio.reference.ip__icmp.h4"></a>
        <span class="phrase"><a name="boost_asio.reference.ip__icmp.requirements"></a></span><a class="link" href="ip__icmp.html#boost_asio.reference.ip__icmp.requirements">Requirements</a>
      </h5>
<p>
        <span class="emphasis"><em>Header: </em></span><code class="literal">boost/asio/ip/icmp.hpp</code>
      </p>
<p>
        <span class="emphasis"><em>Convenience header: </em></span><code class="literal">boost/asio.hpp</code>
      </p>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="ip__host_name/overload2.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../reference.html"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../boost_asio.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="ip__icmp/endpoint.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
