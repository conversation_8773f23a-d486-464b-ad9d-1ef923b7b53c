<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_writable_pipe::basic_writable_pipe (1 of 6 overloads)</title>
<link rel="stylesheet" href="../../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../basic_writable_pipe.html" title="basic_writable_pipe::basic_writable_pipe">
<link rel="prev" href="../basic_writable_pipe.html" title="basic_writable_pipe::basic_writable_pipe">
<link rel="next" href="overload2.html" title="basic_writable_pipe::basic_writable_pipe (2 of 6 overloads)">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../../boost.png"></td>
<td align="center"><a href="../../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../basic_writable_pipe.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../basic_writable_pipe.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../boost_asio.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="boost_asio.reference.basic_writable_pipe.basic_writable_pipe.overload1"></a><a class="link" href="overload1.html" title="basic_writable_pipe::basic_writable_pipe (1 of 6 overloads)">basic_writable_pipe::basic_writable_pipe
          (1 of 6 overloads)</a>
</h5></div></div></div>
<p>
            Construct a <a class="link" href="../../basic_writable_pipe.html" title="basic_writable_pipe"><code class="computeroutput"><span class="identifier">basic_writable_pipe</span></code></a> without
            opening it.
          </p>
<pre class="programlisting"><span class="identifier">basic_writable_pipe</span><span class="special">(</span>
    <span class="keyword">const</span> <span class="identifier">executor_type</span> <span class="special">&amp;</span> <span class="identifier">ex</span><span class="special">);</span>
</pre>
<p>
            This constructor creates a pipe without opening it.
          </p>
<h6>
<a name="boost_asio.reference.basic_writable_pipe.basic_writable_pipe.overload1.h0"></a>
            <span class="phrase"><a name="boost_asio.reference.basic_writable_pipe.basic_writable_pipe.overload1.parameters"></a></span><a class="link" href="overload1.html#boost_asio.reference.basic_writable_pipe.basic_writable_pipe.overload1.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">ex</span></dt>
<dd><p>
                  The I/O executor that the pipe will use, by default, to dispatch
                  handlers for any asynchronous operations performed on the pipe.
                </p></dd>
</dl>
</div>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../basic_writable_pipe.html"><img src="../../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../basic_writable_pipe.html"><img src="../../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../../boost_asio.html"><img src="../../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
