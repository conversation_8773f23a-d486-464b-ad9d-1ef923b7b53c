<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_stream_socket::receive_low_watermark</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../basic_stream_socket.html" title="basic_stream_socket">
<link rel="prev" href="receive_buffer_size.html" title="basic_stream_socket::receive_buffer_size">
<link rel="next" href="release.html" title="basic_stream_socket::release">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="receive_buffer_size.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../basic_stream_socket.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="release.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_asio.reference.basic_stream_socket.receive_low_watermark"></a><a class="link" href="receive_low_watermark.html" title="basic_stream_socket::receive_low_watermark">basic_stream_socket::receive_low_watermark</a>
</h4></div></div></div>
<p>
          <span class="emphasis"><em>Inherited from socket_base.</em></span>
        </p>
<p>
          <a class="indexterm" name="boost_asio.indexterm.basic_stream_socket.receive_low_watermark"></a> 
Socket
          option for the receive low watermark.
        </p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">implementation_defined</span> <span class="identifier">receive_low_watermark</span><span class="special">;</span>
</pre>
<p>
          Implements the SOL_SOCKET/SO_RCVLOWAT socket option.
        </p>
<h6>
<a name="boost_asio.reference.basic_stream_socket.receive_low_watermark.h0"></a>
          <span class="phrase"><a name="boost_asio.reference.basic_stream_socket.receive_low_watermark.examples"></a></span><a class="link" href="receive_low_watermark.html#boost_asio.reference.basic_stream_socket.receive_low_watermark.examples">Examples</a>
        </h6>
<p>
          Setting the option:
        </p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">ip</span><span class="special">::</span><span class="identifier">tcp</span><span class="special">::</span><span class="identifier">socket</span> <span class="identifier">socket</span><span class="special">(</span><span class="identifier">my_context</span><span class="special">);</span>
<span class="special">...</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">socket_base</span><span class="special">::</span><span class="identifier">receive_low_watermark</span> <span class="identifier">option</span><span class="special">(</span><span class="number">1024</span><span class="special">);</span>
<span class="identifier">socket</span><span class="special">.</span><span class="identifier">set_option</span><span class="special">(</span><span class="identifier">option</span><span class="special">);</span>
</pre>
<p>
          Getting the current option value:
        </p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">ip</span><span class="special">::</span><span class="identifier">tcp</span><span class="special">::</span><span class="identifier">socket</span> <span class="identifier">socket</span><span class="special">(</span><span class="identifier">my_context</span><span class="special">);</span>
<span class="special">...</span>
<span class="identifier">boost</span><span class="special">::</span><span class="identifier">asio</span><span class="special">::</span><span class="identifier">socket_base</span><span class="special">::</span><span class="identifier">receive_low_watermark</span> <span class="identifier">option</span><span class="special">;</span>
<span class="identifier">socket</span><span class="special">.</span><span class="identifier">get_option</span><span class="special">(</span><span class="identifier">option</span><span class="special">);</span>
<span class="keyword">int</span> <span class="identifier">size</span> <span class="special">=</span> <span class="identifier">option</span><span class="special">.</span><span class="identifier">value</span><span class="special">();</span>
</pre>
<h6>
<a name="boost_asio.reference.basic_stream_socket.receive_low_watermark.h1"></a>
          <span class="phrase"><a name="boost_asio.reference.basic_stream_socket.receive_low_watermark.requirements"></a></span><a class="link" href="receive_low_watermark.html#boost_asio.reference.basic_stream_socket.receive_low_watermark.requirements">Requirements</a>
        </h6>
<p>
          <span class="emphasis"><em>Header: </em></span><code class="literal">boost/asio/basic_stream_socket.hpp</code>
        </p>
<p>
          <span class="emphasis"><em>Convenience header: </em></span><code class="literal">boost/asio.hpp</code>
        </p>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="receive_buffer_size.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../basic_stream_socket.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="release.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
