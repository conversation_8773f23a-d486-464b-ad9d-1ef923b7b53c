<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>execution_context::fork_event</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../boost_asio.html" title="Boost.Asio">
<link rel="up" href="../execution_context.html" title="execution_context">
<link rel="prev" href="execution_context.html" title="execution_context::execution_context">
<link rel="next" href="has_service.html" title="execution_context::has_service">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="execution_context.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../execution_context.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="has_service.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_asio.reference.execution_context.fork_event"></a><a class="link" href="fork_event.html" title="execution_context::fork_event">execution_context::fork_event</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="boost_asio.indexterm.execution_context.fork_event"></a> 
Fork-related
          event notifications.
        </p>
<pre class="programlisting"><span class="keyword">enum</span> <span class="identifier">fork_event</span>
</pre>
<p>
          <a class="indexterm" name="boost_asio.indexterm.execution_context.fork_event.fork_prepare"></a>
 <a class="indexterm" name="boost_asio.indexterm.execution_context.fork_event.fork_parent"></a>
 <a class="indexterm" name="boost_asio.indexterm.execution_context.fork_event.fork_child"></a>
        </p>
<h6>
<a name="boost_asio.reference.execution_context.fork_event.h0"></a>
          <span class="phrase"><a name="boost_asio.reference.execution_context.fork_event.values"></a></span><a class="link" href="fork_event.html#boost_asio.reference.execution_context.fork_event.values">Values</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">fork_prepare</span></dt>
<dd><p>
                Notify the context that the process is about to fork.
              </p></dd>
<dt><span class="term">fork_parent</span></dt>
<dd><p>
                Notify the context that the process has forked and is the parent.
              </p></dd>
<dt><span class="term">fork_child</span></dt>
<dd><p>
                Notify the context that the process has forked and is the child.
              </p></dd>
</dl>
</div>
</div>
<div class="copyright-footer">Copyright © 2003-2023 Christopher M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="execution_context.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../execution_context.html"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../boost_asio.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="has_service.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
