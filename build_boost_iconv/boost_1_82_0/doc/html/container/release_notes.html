<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Release Notes</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../container.html" title="Chapter 8. Boost.Container">
<link rel="prev" href="acknowledgements_notes.html" title="Acknowledgements, notes and links">
<link rel="next" href="../conversion.html" title="Chapter 9. The Conversion Library 1.7">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="acknowledgements_notes.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../container.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../conversion.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="container.release_notes"></a><a class="link" href="release_notes.html" title="Release Notes">Release Notes</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_82_00">Boost
      1.82 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_81_00">Boost
      1.81 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_80_00">Boost
      1.80 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_79_00">Boost
      1.79 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_78_00">Boost
      1.78 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_77_00">Boost
      1.77 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_76_00">Boost
      1.76 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_75_00">Boost
      1.75 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_74_00">Boost
      1.74 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_72_00">Boost
      1.72 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_71_00">Boost
      1.71 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_70_00">Boost
      1.70 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_69_00">Boost
      1.69 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_68_00">Boost
      1.68 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_67_00">Boost
      1.67 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_66_00">Boost
      1.66 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_65_00">Boost
      1.65 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_64_00">Boost
      1.64 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_63_00">Boost
      1.63 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_62_00">Boost
      1.62 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_61_00">Boost
      1.61 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_60_00">Boost
      1.60 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_59_00">Boost
      1.59 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_58_00">Boost
      1.58 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_57_00">Boost
      1.57 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_56_00">Boost
      1.56 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_55_00">Boost
      1.55 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_54_00">Boost
      1.54 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_53_00">Boost
      1.53 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_52_00">Boost
      1.52 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_51_00">Boost
      1.51 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_50_00">Boost
      1.50 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_49_00">Boost
      1.49 Release</a></span></dt>
<dt><span class="section"><a href="release_notes.html#container.release_notes.release_notes_boost_1_48_00">Boost
      1.48 Release</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_82_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_82_00" title="Boost 1.82 Release">Boost
      1.82 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Fixed bugs/issues:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/232" target="_top">GitHub
                  #232: <span class="emphasis"><em>"Fix using pmr::polymorphic_allocator in pre-main"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/233" target="_top">GitHub
                  #233: <span class="emphasis"><em>"Can't std::move small_vector with move-only
                  type"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/238" target="_top">GitHub
                  #238: <span class="emphasis"><em>"Containers should not be using memset to
                  value-initialize POD types"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/236" target="_top">GitHub
                  #236: <span class="emphasis"><em>"flat_tree::erase_unique uses wrong iterator"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/240" target="_top">GitHub
                  #240: <span class="emphasis"><em>"_GLIBCXX_DEBUG detects issues in flat_set/map"</em></span></a>.
                </li>
</ul></div>
          </li></ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_81_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_81_00" title="Boost 1.81 Release">Boost
      1.81 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/container/devector.html" title="Class template devector">devector</a></code>'s insertion
            logic has been reimplemented to move elements to the center of the devector
            if an insertion at one end has no free capacity but there is free capacity
            on the other end. Current implementation keeps reallocating memory when
            only inserting at one end and poping from the other, provoking very high
            memory usage. The new strategy is based on the article <a href="http://larshagencpp.github.io/blog/2016/05/22/devector" target="_top">Double-ended
            vector - is it useful?</a> by Lars Greger Nordland Hagen.
          </li></ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_80_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_80_00" title="Boost 1.80 Release">Boost
      1.80 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Fixed bugs/issues:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/209" target="_top">GitHub
                  #209: <span class="emphasis"><em>"Some boost warnings with my R package (Wclass-memaccess
                  warnings with std::pair)"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/211" target="_top">GitHub
                  #211: <span class="emphasis"><em>"Use atomics for pmr get/set default resource"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/210" target="_top">GitHub
                  #210: <span class="emphasis"><em>"Use sized delete in boost::container::new_allocator
                  if __cpp_sized_deallocation is defined"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/221" target="_top">GitHub
                  #218: <span class="emphasis"><em>"small_vector static capacity is too small
                  when not a multiple of 8 bytes"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/221" target="_top">GitHub
                  #221: <span class="emphasis"><em>"flat_set and friends should offer a const
                  sequence_type&amp; sequence() const method (...)"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/222" target="_top">GitHub
                  #222: <span class="emphasis"><em>"Fix incomplete type error when using list
                  with pair"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/223" target="_top">GitHub
                  #223: <span class="emphasis"><em>"Possible copypaste typo"</em></span></a>.
                </li>
</ul></div>
          </li></ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_79_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_79_00" title="Boost 1.79 Release">Boost
      1.79 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            The library now compiles without warnings with GCC's -Wcast-align=strict
          </li>
<li class="listitem">
            Fixed bugs/issues:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/195" target="_top">GitHub
                  #195: <span class="emphasis"><em>"vec_iterator member typedef iterator_concept
                  should only be defined in C++20"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/199" target="_top">GitHub
                  #199: <span class="emphasis"><em>"Apply LWG issue 3471 to memory_resource"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/204" target="_top">GitHub
                  #204: <span class="emphasis"><em>"Inconsistent noexcept-ness of static_vector::reserve"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/206" target="_top">GitHub
                  #206: <span class="emphasis"><em>"operator-&gt; on static_vector::iterator
                  causes cast alignment warning"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/207" target="_top">GitHub
                  #207: <span class="emphasis"><em>"boost.vector doesn't work with common_iterator"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/214" target="_top">GitHub
                  #214: <span class="emphasis"><em>"string is not properly null-terminated in
                  assignments"</em></span></a>.
                </li>
</ul></div>
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_78_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_78_00" title="Boost 1.78 Release">Boost
      1.78 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Fixed bugs/issues:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/186" target="_top">GitHub
                  #186: <span class="emphasis"><em>"Warnings out the wazoo"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/187" target="_top">GitHub
                  #187: <span class="emphasis"><em>"flat_map::erase and unique keys"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/188" target="_top">GitHub
                  #188: <span class="emphasis"><em>"Build fails when RTTI is disabled"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/192" target="_top">GitHub
                  #192: <span class="emphasis"><em>"basic_string::clear() has poor codegen compared
                  to STL implementations"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/197" target="_top">GitHub
                  #197: <span class="emphasis"><em>"small_vector::swap causes spurious allocations
                  and suboptimal performance"</em></span></a>.
                </li>
</ul></div>
          </li></ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_77_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_77_00" title="Boost 1.77 Release">Boost
      1.77 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Fixed bugs/issues:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/150" target="_top">GitHub
                  #150: <span class="emphasis"><em>"Use std::contiguous_iterator_tag if available"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/180" target="_top">GitHub
                  #180: <span class="emphasis"><em>"polymorphic_allocator's copy special member
                  functions are not noexcept"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/184" target="_top">GitHub
                  #184: <span class="emphasis"><em>"Issues with custom exceptions implementation"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/185" target="_top">GitHub
                  #185: <span class="emphasis"><em>"Including headers adds exports"</em></span></a>.
                </li>
</ul></div>
          </li></ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_76_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_76_00" title="Boost 1.76 Release">Boost
      1.76 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Added [[no-discard]] attribute in all containers to catch bugs related
            to unused return values.
          </li>
<li class="listitem">
            Replaced default standard exception classes with Boost.Container own
            classes, reducing considerably the included files overhead. Example:
            in MSVC 19 <code class="computeroutput"><span class="identifier">boost</span><span class="special">/</span><span class="identifier">container</span><span class="special">/</span><span class="identifier">vector</span><span class="special">.</span><span class="identifier">hpp</span></code> preprocessed file size reduces
            from 1,5MB to 930KB. If you still want to use standard exception classes,
            you can define <code class="computeroutput"><span class="identifier">BOOST_CONTAINER_USE_STD_EXCEPTIONS</span></code>
            before using any Boost.Container class.
          </li>
<li class="listitem">
            Fixed bugs/issues:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/102" target="_top">GitHub
                  #102: <span class="emphasis"><em>"flat_map::insert ambiguous with initializer
                  list &amp; pairs that need to convert"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/139" target="_top">GitHub
                  #139: <span class="emphasis"><em>"flat_map merge and iterators"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/141" target="_top">GitHub
                  #141: <span class="emphasis"><em>"small_vector does not propagate no throw
                  properties of move operation of contained type"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/164" target="_top">GitHub
                  #164: <span class="emphasis"><em>"Compile error when using <code class="computeroutput"><span class="identifier">pmr</span><span class="special">::</span><span class="identifier">map</span></code>
                  with a <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span></code>; works when using a <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">tuple</span></code>"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/171" target="_top">GitHub
                  #171: <span class="emphasis"><em>"deque::clear() uses undefined behaviour"</em></span></a>.
                </li>
</ul></div>
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_75_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_75_00" title="Boost 1.75 Release">Boost
      1.75 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            New <code class="computeroutput"><a class="link" href="../boost/container/devector.html" title="Class template devector">devector</a></code>
            container.
          </li>
<li class="listitem">
            Fixed bugs/issues:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/152" target="_top">GitHub
                  #152: <span class="emphasis"><em>"Tree-based containers have troubles with
                  move-only types"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/156" target="_top">GitHub
                  #156: <span class="emphasis"><em>"Compile error with vector"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/157" target="_top">GitHub
                  #157: <span class="emphasis"><em>"Add missing include"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/159" target="_top">GitHub
                  #159: <span class="emphasis"><em>"pmr::monotonic_buffer_resource crashes on
                  large single allocations"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/160" target="_top">GitHub
                  #160: <span class="emphasis"><em>"Usage of uses_allocator needs a remove_cvref_t"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/162" target="_top">GitHub
                  #162: <span class="emphasis"><em>"small_vector on MSVC x86 call-by-value crash"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/161" target="_top">GitHub
                  #161: <span class="emphasis"><em>"polymorphic_allocator(memory_resource*) non-standard
                  extension causes headache"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/163" target="_top">GitHub
                  #163: <span class="emphasis"><em>"container_rebind for small_vector with options"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/165" target="_top">GitHub
                  #165: <span class="emphasis"><em>"Link error with shared library and memory_resource
                  inline members"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/166" target="_top">GitHub
                  #166: <span class="emphasis"><em>"Fix encoding error in copyright headers"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/167" target="_top">GitHub
                  #167: <span class="emphasis"><em>"error: the address of 'msg' will always evaluate
                  as 'true' warning with GCC 4.4"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/169" target="_top">GitHub
                  #169: <span class="emphasis"><em>"flood of warnings building dlmalloc_ext_2_8_6.c
                  on clang11"</em></span></a>.
                </li>
</ul></div>
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_74_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_74_00" title="Boost 1.74 Release">Boost
      1.74 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Fixed bugs/issues:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/125" target="_top">GitHub
                  #125: <span class="emphasis"><em>"flat_map doc misleading complexity"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/126" target="_top">GitHub
                  #126: <span class="emphasis"><em>"flat_set.hpp and set.hpp in pmr have the
                  same header guard"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/128" target="_top">GitHub
                  #128: <span class="emphasis"><em>"moved from small_vector and static_vector
                  calls destructor on elements in static part"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/129" target="_top">GitHub
                  #129: <span class="emphasis"><em>"Alias templates for small_flat_[multi</em></span>{set|map}
                  using small_vector as container"</a>].
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/135" target="_top">GitHub
                  #135: <span class="emphasis"><em>"Missing BOOST_NORETURN for user defined functions"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/137" target="_top">GitHub
                  #137: <span class="emphasis"><em>"RandomAccessIterator + 0"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/138" target="_top">GitHub
                  #138: <span class="emphasis"><em>"Remove Classes from Global Namespace"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/142" target="_top">GitHub
                  #142: <span class="emphasis"><em>"memset called with null pointer"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/144" target="_top">GitHub
                  #144: <span class="emphasis"><em>"GCC suggest-override warnings"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/145" target="_top">GitHub
                  #145: <span class="emphasis"><em>"Allocations not handled correctly in some
                  cases of vector move with unequal allocators"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/146" target="_top">GitHub
                  #146: <span class="emphasis"><em>"Changes for Embarcadero C++ clang-based compilers,
                  targeting Boost 1.74. Addition needed for Embarcardero clang-based
                  compilers"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/148" target="_top">GitHub
                  #148: <span class="emphasis"><em>"Fix static initialization issues in pmr global
                  resources"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/149" target="_top">GitHub
                  #149: <span class="emphasis"><em>"InitializeCriticalSectionEx returns "BOOL"
                  (int)"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/151" target="_top">GitHub
                  #151: <span class="emphasis"><em>"Buffer overflow in monotonic_buffer_resource::do_allocate"</em></span></a>.
                </li>
</ul></div>
          </li></ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_72_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_72_00" title="Boost 1.72 Release">Boost
      1.72 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/127" target="_top">GitHub
                  #127: <span class="emphasis"><em>"Fix docs for static_vector::max_size() and
                  capacity()"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/132" target="_top">GitHub
                  #132: <span class="emphasis"><em>"flat_map::lower_bound and upper_bound have
                  wrong/misleading docs"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/133" target="_top">GitHub
                  #133: <span class="emphasis"><em>"basic_string move constructor with allocator
                  argument has incorrect allocator check"</em></span></a>.
                </li>
</ul></div>
          </li></ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_71_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_71_00" title="Boost 1.71 Release">Boost
      1.71 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/47" target="_top">GitHub
                  #47: <span class="emphasis"><em>"added alignment specification for small_vector"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/88" target="_top">GitHub
                  #88: <span class="emphasis"><em>"Implement C++17 MoveAssignable requirements
                  for self-move assignments"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/107" target="_top">GitHub
                  #107: <span class="emphasis"><em>"Alignment ignored in resource_adaptor"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/109" target="_top">GitHub
                  #109: <span class="emphasis"><em>"Get rid of integer overflow in copy_move_algo.hpp
                  (-fsanitize=integer)"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/110" target="_top">GitHub
                  #110: <span class="emphasis"><em>"Avoid gcc 9 deprecated copy warnings in new_allocator.hpp"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/112" target="_top">GitHub
                  #112: <span class="emphasis"><em>"vector::resize() compilation error with msvc-10..12:
                  data is not a member of boost::detail::aligned_storage"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/114" target="_top">GitHub
                  #114: <span class="emphasis"><em>"Fix small_vector noexcept specification"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/116" target="_top">GitHub
                  #116: <span class="emphasis"><em>"MSVC + boost 1.70 compilation error when
                  windows.h is already included (detail/thread_mutex.hpp)"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/117" target="_top">GitHub
                  #117: <span class="emphasis"><em>"flat_map/map::insert_or_assign with hint
                  has wrong return types"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/118" target="_top">GitHub
                  #118: <span class="emphasis"><em>"Non-unique inplace_set_difference used in
                  in flat_tree_merge_unique and iterator invalidation in insert_unique"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/122" target="_top">GitHub
                  #122: <span class="emphasis"><em>"Fix has_trivial_destructor_after_move"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/123" target="_top">GitHub
                  #123: <span class="emphasis"><em>"With heterogeneous lookup, <code class="computeroutput"><span class="identifier">equal_range</span></code> can result in a range
                  with length greater than 1"</em></span></a>.
                </li>
</ul></div>
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/container/deque.html" title="Class template deque">deque</a></code> can now have
            options, using <code class="computeroutput"><a class="link" href="../boost/container/deque_options.html" title="Struct template deque_options">deque_options</a></code>.
            The block size/bytes can be be specified.
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/container/static_vector.html" title="Class template static_vector">static_vector</a></code>
            can now have options, using <code class="computeroutput"><a class="link" href="../boost/container/static_vector_options.html" title="Struct template static_vector_options">static_vector_options</a></code>.
            Alignment and throwing behaviour can be be specified.
          </li>
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/container/small_vector.html" title="Class template small_vector">small_vector</a></code>
            can now have options, using <code class="computeroutput"><a class="link" href="../boost/container/small_vector_options.html" title="Struct template small_vector_options">small_vector_options</a></code>.
            Alignment and growth factor can be be specified.
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_70_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_70_00" title="Boost 1.70 Release">Boost
      1.70 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Removed support for already deprecated GCC &lt; 4.3 and MSVC &lt; 9.0
            (Visual 2008) compilers.
          </li>
<li class="listitem">
            Default allocator parameter changed form <code class="computeroutput"><span class="identifier">new_allocator</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code> to <code class="computeroutput"><span class="keyword">void</span></code>
            to reduce symbol lenghts.
          </li>
<li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/96" target="_top">GitHub
                  #96: <span class="emphasis"><em>"Workaround: Intel compilers do not offer CTAD
                  yet"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/97" target="_top">GitHub
                  #97: <span class="emphasis"><em>"buffer overflow in boost::container::flat_map
                  on FreeBSD"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/98" target="_top">GitHub
                  #98: <span class="emphasis"><em>"flat_map: insert_or_assign does not work with
                  hint"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/100" target="_top">GitHub
                  #100: <span class="emphasis"><em>"Compile error on Green Hills: container_detail::flat_tree
                  has no member insert"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/103" target="_top">GitHub
                  #103: <span class="emphasis"><em>"Fix deallocating never-allocated storage
                  in vector.merge()"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/104" target="_top">GitHub
                  #104: <span class="emphasis"><em>"Fix -Wmissing-noreturn clang warnings"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/105" target="_top">GitHub
                  #105: <span class="emphasis"><em>"Fix gcc -Wdeprecated-copy"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/111" target="_top">GitHub
                  #111: <span class="emphasis"><em>"container::vector of interprocess::offset_ptrs
                  to variants holding incomplete type"</em></span></a>.
                </li>
</ul></div>
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_69_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_69_00" title="Boost 1.69 Release">Boost
      1.69 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Deprecated GCC &lt; 4.3 and MSVC &lt; 9.0 (Visual 2008) compilers.
          </li>
<li class="listitem">
            Implemented C++20 <code class="computeroutput"><span class="identifier">contains</span><span class="special">()</span></code> for associative containers as specified
            in <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2018/p0458r2.html" target="_top">P0458R2:
            Checking for Existence of an Element in Associative Containers</a>.
          </li>
<li class="listitem">
            Fixed serious bug in heterogeneous lookup functions (is_transparent was
            broken).
          </li>
<li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/77" target="_top">GitHub
                  #77: <span class="emphasis"><em>"warning: 'sbrk' is deprecated"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/79" target="_top">GitHub
                  #79: <span class="emphasis"><em>"Mark small_vector move operations noexcept"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/80" target="_top">GitHub
                  #80: <span class="emphasis"><em>"flat_map deduction guides are ambiguous"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/81" target="_top">GitHub
                  #81: <span class="emphasis"><em>"Vector with custom allocator does not support
                  value types with operator&amp;"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/82" target="_top">GitHub
                  #82: <span class="emphasis"><em>"Function definition in header file"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/83" target="_top">GitHub
                  #83: <span class="emphasis"><em>"Iterator zero incrementing leads to assert
                  on empty vector"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/84" target="_top">GitHub
                  #84: <span class="emphasis"><em>"Allow vector to be assigned to itself"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/85" target="_top">GitHub
                  #85: <span class="emphasis"><em>"container: misc-typos"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/86" target="_top">GitHub
                  #86: <span class="emphasis"><em>"Add missing warning re-enabling include"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/89" target="_top">GitHub
                  #89: <span class="emphasis"><em>"UBSAN failures detected in preflight CI PR"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/90" target="_top">GitHub
                  #90: <span class="emphasis"><em>"Build fails on clang-5 with libstdc++7-dev
                  (C++17 issue)"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/93" target="_top">GitHub
                  #93: <span class="emphasis"><em>"vector::erase memory leak"</em></span></a>.
                </li>
</ul></div>
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_68_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_68_00" title="Boost 1.68 Release">Boost
      1.68 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Improved correctness of <code class="computeroutput"><a class="link" href="../boost/container/adaptive_pool.html" title="Class template adaptive_pool">adaptive_pool</a></code>
            and many parameters are now compile-time constants instead of runtime
            constants.
          </li>
<li class="listitem">
            Implemented C++14's heterogeneous lookup functions for <code class="computeroutput"><span class="special">[</span><span class="identifier">multi</span><span class="special">]</span><span class="identifier">map</span><span class="special">/[</span><span class="identifier">multi</span><span class="special">]</span><span class="identifier">set</span><span class="special">/</span><span class="identifier">flat_</span><span class="special">[</span><span class="identifier">multi</span><span class="special">]</span><span class="identifier">map</span><span class="special">/</span><span class="identifier">flat_</span><span class="special">[</span><span class="identifier">multi</span><span class="special">]</span><span class="identifier">set</span></code>.
          </li>
<li class="listitem">
            Added <a href="https://github.com/boostorg/container/pull/71" target="_top">GitHub
            #71: <span class="emphasis"><em>"Constructor Template Auto Deduction guides "</em></span></a>.
          </li>
<li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/13533" target="_top">Trac
                  #13533: <span class="emphasis"><em>"Boost vector resize causes assert(false)"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/73" target="_top">GitHub
                  #73: <span class="emphasis"><em>"triviality of pair"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/74" target="_top">GitHub
                  #74: <span class="emphasis"><em>"vector assignment not using memcpy"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/75" target="_top">GitHub
                  #75: <span class="emphasis"><em>"flat_set: Heap overflow"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/76" target="_top">GitHub
                  #76: <span class="emphasis"><em>"flat_set: undefined behaviour on empty range"</em></span></a>.
                </li>
<li class="listitem">
                  Fixed race condition bug in <code class="computeroutput"><a class="link" href="../boost/container/pmr/unsynchronized_po_idm19691.html" title="Class unsynchronized_pool_resource">unsynchronized_pool_resource</a></code>
                  found by Arthur O'Dowyer in his blog post <a href="https://quuxplusone.github.io/blog/2018/06/05/libcpp-memory-resource/" target="_top">&lt;memory_resource&gt;
                  for libc++</a>
                </li>
</ul></div>
          </li>
<li class="listitem">
            Implemented proposed resolution for <a href="https://cplusplus.github.io/LWG/issue3120" target="_top"><span class="emphasis"><em>"LWG
            3120 Unclear behavior of monotonic_buffer_resource::release()"</em></span></a>.
            After <code class="computeroutput"><span class="identifier">release</span><span class="special">()</span></code>
            the original buffer is recovered for the next allocation.
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_67_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_67_00" title="Boost 1.67 Release">Boost
      1.67 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <span class="emphasis"><em>vector</em></span> can now have options, using <code class="computeroutput"><a class="link" href="../boost/container/vector_options.html" title="Struct template vector_options">vector_options</a></code>.
            The growth factor and the stored size type can be specified.
          </li>
<li class="listitem">
            Improved range insertion in <span class="emphasis"><em>flat_[multi</em></span>map/set]
            containers overall complexity is reduced to O(NlogN).
          </li>
<li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/61" target="_top">GitHub
                  #61: <span class="emphasis"><em>"Compile problems on Android ndk r16 beta 1"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/64" target="_top">GitHub
                  #64: <span class="emphasis"><em>"Fix splice for slist"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/58" target="_top">GitHub
                  #65: <span class="emphasis"><em>"<code class="computeroutput"><span class="identifier">pmr</span><span class="special">::</span><span class="identifier">monotonic_buffer_resource</span><span class="special">::</span><span class="identifier">allocate</span><span class="special">()</span></code> can return a pointer to freed
                  memory after <code class="computeroutput"><span class="identifier">release</span><span class="special">()</span></code> is called"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/13500" target="_top">Trac
                  #13500: <span class="emphasis"><em>"Memory leak when using erase on string
                  vectors"</em></span></a>.
                </li>
</ul></div>
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_66_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_66_00" title="Boost 1.66 Release">Boost
      1.66 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <span class="emphasis"><em>flat_[multi</em></span>map/set] can now work as container adaptors,
            as proposed in <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2017/p0429r1.pdf" target="_top">P0429R1</a>.
            The allocator argument is checked for <span class="emphasis"><em>size()</em></span> and
            <span class="emphasis"><em>empty()</em></span> members. If so, the argument is interpreted
            as the required underlying container. This means that <span class="emphasis"><em>static_vector</em></span>,
            <span class="emphasis"><em>stable_vector</em></span> and <span class="emphasis"><em>small_vector</em></span>
            can be used now with flat associative containers.
          </li>
<li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/54" target="_top">GitHub
                  #54: <span class="emphasis"><em>"no sbrk() in VxWorks, configure dlmalloc to
                  use only mmap"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/58" target="_top">GitHub
                  #58: <span class="emphasis"><em>"Comparing strings does not compile in gcc
                  7+ in C++17 mode"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/issues/59" target="_top">GitHub
                  #59: <span class="emphasis"><em>"basic_string::npos is missing its definition"</em></span></a>.
                </li>
</ul></div>
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_65_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_65_00" title="Boost 1.65 Release">Boost
      1.65 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Implemented <code class="computeroutput"><span class="identifier">extract_sequence</span></code>,
            <code class="computeroutput"><span class="identifier">adopt_sequence</span></code> functions
            for flat_[multi]map/set associative containers.
          </li>
<li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/48" target="_top">GitHub
                  #48: <span class="emphasis"><em>"Replace deprecated/removed C++98 binders"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/49" target="_top">GitHub
                  #49: <span class="emphasis"><em>"Remove useless allocator copy in map"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/50" target="_top">GitHub
                  #50: <span class="emphasis"><em>"Fixed bug Trac #13038 (base64 iterators can't
                  be used with iterator_advance)"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/51" target="_top">GitHub
                  #51: <span class="emphasis"><em>"Fix integer rollover that triggers clang ubsan
                  when U is unsigned"</em></span></a>.
                </li>
</ul></div>
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_64_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_64_00" title="Boost 1.64 Release">Boost
      1.64 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11333" target="_top">Trac
                  #11333: <span class="emphasis"><em>"boost::basic_string_ref should interop
                  with boost::container::basic_string"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/12749" target="_top">Trac
                  #12749: <span class="emphasis"><em>"container::pmr::polymorphic_allocator compilation
                  error"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/12915" target="_top">Trac
                  #12915: <span class="emphasis"><em>"Buffer overflow in boost::container::vector
                  (affects flat_set)"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/45" target="_top">GitHub
                  #45: <span class="emphasis"><em>"emplace_back must return reference to back(),
                  not to *end()"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/46" target="_top">GitHub
                  #46: <span class="emphasis"><em>"Fix use of propagate_on_container_swap"</em></span></a>.
                </li>
</ul></div>
          </li></ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_63_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_63_00" title="Boost 1.63 Release">Boost
      1.63 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/12534" target="_top">Trac
                  #12534: <span class="emphasis"><em>"flat_map fails to compile if included after
                  type_traits is instantiated under gcc"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/12577" target="_top">Trac
                  #12577: <span class="emphasis"><em>"Null reference in pair.hpp triggers runtime
                  warning with -fsanitize=undefined"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/41" target="_top">GitHub
                  #40: <span class="emphasis"><em>Fix parameter types in copy_move_algo.hpp: iterator_traits::difference_type
                  -&gt; allocator_traits::size_type</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/41" target="_top">GitHub
                  #41: <span class="emphasis"><em>Avoid -Wunreachable-code in do_allocate()</em></span></a>.
                </li>
</ul></div>
          </li></ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_62_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_62_00" title="Boost 1.62 Release">Boost
      1.62 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/9481" target="_top">Trac
                  #9481: <span class="emphasis"><em>"Minor comment typo in Boost.Container"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/9689" target="_top">Trac
                  #9689: <span class="emphasis"><em>"Add piecewise_construct to boost::container"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11170" target="_top">Trac
                  #11170: <span class="emphasis"><em>"Doc slip for index_of"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11802" target="_top">Trac
                  #11802: <span class="emphasis"><em>"Incorrect ordering after using insert()
                  with ordered_range_t on a flat_multiset with a non-default sort
                  order"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/12117" target="_top">Trac
                  #12117: <span class="emphasis"><em>"flat_set constructor with ordered_unique_range"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/12177" target="_top">Trac
                  #12177: <span class="emphasis"><em>"vector::priv_merge uses unqualified uintptr_t"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/12183" target="_top">Trac
                  #12183: <span class="emphasis"><em>"GCC 6.1 thinks boost::container::string
                  violates strict aliasing"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/12256" target="_top">Trac
                  #12256: <span class="emphasis"><em>"set&lt;std::pair&lt;int,int&gt;&gt;::insert
                  cause compilation error in debug configuration in Visual Studio
                  2012"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/12273" target="_top">Trac
                  #12273: <span class="emphasis"><em>"static_vector max_size() and capacity()
                  should be constant expressions"</em></span></a>. Added
                  constant <code class="computeroutput"><span class="identifier">static_vector</span><span class="special">&lt;&gt;::</span><span class="identifier">static_capacity</span></code>
                  to use the configured capacity in constant expressions.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/12286" target="_top">Trac
                  #12286: <span class="emphasis"><em>"PMR flat_map from Boost Container does
                  not compile"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/12296" target="_top">Trac
                  #12296: <span class="emphasis"><em>"{deque,string} combine for a memory leak"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/12319" target="_top">Trac
                  #12319: <span class="emphasis"><em>"flat_set` should be nothrow move constructible"</em></span></a>.
                </li>
</ul></div>
          </li>
<li class="listitem">
            Revised noexcept expressions of default and move constructors in all
            containers.
          </li>
<li class="listitem">
            Implemented C++17's <code class="computeroutput"><span class="identifier">insert_or_assign</span></code>/<code class="computeroutput"><span class="identifier">try_emplace</span></code> for <code class="computeroutput"><a class="link" href="../boost/container/map.html" title="Class template map">map</a></code>
            and <code class="computeroutput"><a class="link" href="../boost/container/flat_map.html" title="Class template flat_map">flat_map</a></code>.
          </li>
<li class="listitem">
            Implemented C++17's <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2016/p0083r3.pdf" target="_top"><span class="emphasis"><em>Splicing
            Maps and Sets (Revision 5)</em></span></a> for <code class="computeroutput"><a class="link" href="../boost/container/map.html" title="Class template map">map</a></code>,
            <code class="computeroutput"><a class="link" href="../boost/container/multimap.html" title="Class template multimap">multimap</a></code>, <code class="computeroutput"><a class="link" href="../boost/container/set.html" title="Class template set">set</a></code>, <code class="computeroutput"><a class="link" href="../boost/container/multiset.html" title="Class template multiset">multiset</a></code>.
          </li>
<li class="listitem">
            Implemented C++17's <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2016/p0084r2.pdf" target="_top"><span class="emphasis"><em>P0084R2
            Emplace Return Type</em></span></a> in <code class="computeroutput"><span class="identifier">deque</span></code>,
            <code class="computeroutput"><span class="identifier">vector</span></code>, <code class="computeroutput"><span class="identifier">stable_vector</span></code>, <code class="computeroutput"><span class="identifier">small_vector</span></code>,
            <code class="computeroutput"><span class="identifier">static_vector</span></code>, <code class="computeroutput"><span class="identifier">list</span></code> and <code class="computeroutput"><span class="identifier">slist</span></code>.
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_61_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_61_00" title="Boost 1.61 Release">Boost
      1.61 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <code class="computeroutput"><a class="link" href="../boost/container/small_vector.html" title="Class template small_vector">boost::container::small_vector</a></code>
            supports more constructors and assignments.
          </li>
<li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11820" target="_top">Trac
                  #11820: <span class="emphasis"><em>"compiler error when using operator[</em></span>
                  of map"</a>].
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11856" target="_top">Trac
                  #11856: <span class="emphasis"><em>"pool_resource.cpp error: declaration changes
                  meaning"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11866" target="_top">Trac
                  #11866: <span class="emphasis"><em>"small_vector does not have range constructor"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11867" target="_top">Trac
                  #11867: <span class="emphasis"><em>"small_vector should have constructor and
                  assignment operator taking other small_vector"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11912" target="_top">Trac
                  #11912: <span class="emphasis"><em>"flat_map use of vector::priv_forward_range_insert_expand_backwards
                  may cause move with same source"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11957" target="_top">Trac
                  #11957: <span class="emphasis"><em>"static_vector::max_size() is higher than
                  the capacity"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/12014" target="_top">Trac
                  #12014: <span class="emphasis"><em>"boost::container::set can not insert const
                  (ref) range"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/33" target="_top">GitHub
                  #33: <span class="emphasis"><em>Make sure std::string constructor is available</em></span></a>.
                </li>
</ul></div>
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_60_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_60_00" title="Boost 1.60 Release">Boost
      1.60 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Implemented <a class="link" href="cpp_conformance.html#container.cpp_conformance.polymorphic_memory_resources" title="Polymorphic Memory Resources">Polymorphic
            Memory Resources</a>.
          </li>
<li class="listitem">
            Add more BOOST_ASSERT checks to test preconditions in some operations
            (like <code class="computeroutput"><span class="identifier">pop_back</span></code>, <code class="computeroutput"><span class="identifier">pop_front</span></code>, <code class="computeroutput"><span class="identifier">back</span></code>,
            <code class="computeroutput"><span class="identifier">front</span></code>, etc.)
          </li>
<li class="listitem">
            Added C++11 <code class="computeroutput"><span class="identifier">back</span></code>/<code class="computeroutput"><span class="identifier">front</span></code> operations to <code class="computeroutput"><a class="link" href="../boost/container/basic_string.html" title="Class template basic_string">basic_string</a></code>.
          </li>
<li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11627" target="_top">Trac
                  #11627: <span class="emphasis"><em>"small_vector&lt;T,n&gt;::swap() appears
                  to be broken"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11628" target="_top">Trac
                  #11628: <span class="emphasis"><em>"small_vector&lt;int,n&gt; iterates over
                  elements in destructor"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11697" target="_top">Trac
                  #11697: <span class="emphasis"><em>"Wrong initialization order in tuple copy-constructor"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11698" target="_top">Trac
                  #11698: <span class="emphasis"><em>"Missing return statement in static_storage_allocator"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/29" target="_top">GitHub
                  #29: <span class="emphasis"><em>Doc fixes for flap_map complexity requirements</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/31" target="_top">GitHub
                  #31: <span class="emphasis"><em>DL_SIZE_IMPL also dereference addr</em></span></a>.
                </li>
</ul></div>
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_59_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_59_00" title="Boost 1.59 Release">Boost
      1.59 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            <a href="https://github.com/boostorg/container/pull/26" target="_top">GitHub #26:
            <span class="emphasis"><em>Fix bug in stable_vector::capacity()</em></span></a>. Thanks
            to timsong-cpp/Arindam Mukerjee.
          </li>
<li class="listitem">
            <a href="https://github.com/boostorg/container/pull/27" target="_top">GitHub #27:
            <span class="emphasis"><em>fix stable_vector's index_of's doxygen comment</em></span></a>.
            Thanks to kariya-mitsuru.
          </li>
<li class="listitem">
            <a href="https://svn.boost.org/trac/boost/ticket/11380" target="_top">Trac #11380:
            <span class="emphasis"><em>"Container library std forward declarations incorrect
            in std_fwd.hpp on libc++ with gcc"</em></span></a>.
          </li>
<li class="listitem">
            <a href="https://svn.boost.org/trac/boost/ticket/11388" target="_top">Trac #11388:
            <span class="emphasis"><em>"boost::container::list::emplace_back broken on Visual
            Studio 2010"</em></span></a>.
          </li>
<li class="listitem">
            <a href="https://svn.boost.org/trac/boost/ticket/11339" target="_top">Trac #11339:
            <span class="emphasis"><em>"VC12 LNK2005 error with boost::container::adaptive_pool"</em></span></a>.
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_58_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_58_00" title="Boost 1.58 Release">Boost
      1.58 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Experimental <code class="computeroutput"><a class="link" href="../boost/container/small_vector.html" title="Class template small_vector">small_vector</a></code>
            container.
          </li>
<li class="listitem">
            Massive dependency reorganization. Now <span class="bold"><strong>Boost.Container</strong></span>
            depends on very basic utilities like Boost.Core and <span class="bold"><strong>Boost.Intrusive</strong></span>.
            Preprocessed code size have decreased considerably and compilation times
            have improved.
          </li>
<li class="listitem">
            Added <code class="computeroutput"><span class="identifier">nth</span></code> and <code class="computeroutput"><span class="identifier">index_of</span></code> functions to containers with
            random-access iterators (except <code class="computeroutput"><span class="identifier">basic_string</span></code>).
          </li>
<li class="listitem">
            Added C++17's <code class="computeroutput"><span class="identifier">allocator_traits</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;::</span><span class="identifier">is_always_equal</span></code>.
          </li>
<li class="listitem">
            Updated containers to implement new constructors as specified in <a href="http://www.open-std.org/jtc1/sc22/wg21/docs/lwg-defects.html#2210" target="_top">2210.
            Missing allocator-extended constructor for allocator-aware containers</a>.
          </li>
<li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/9931" target="_top">Trac
                  #9931: <span class="emphasis"><em>"flat_map::insert(ordered_unique_range_t...)
                  fails with move_iterators"</em></span></a> (reopened).
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11076" target="_top">Trac
                  #11076: <span class="emphasis"><em>"Unqualified calls to memmove/memcpy in
                  container/detail/copy_move_algo.hpp"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/10790" target="_top">Trac
                  #10790 (<span class="emphasis"><em>"long long errors from container"</em></span>)</a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/10808" target="_top">Trac
                  #10808 (<span class="emphasis"><em>"compare equal operator of vector is broken"</em></span>)</a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/10930" target="_top">Trac
                  #10930 (<span class="emphasis"><em>"container std_fwd.hpp neglects custom std
                  namespaces"</em></span>)</a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/11139" target="_top">Trac
                  #11139 (<span class="emphasis"><em>"boost::container::vector&lt;std::shared_ptr&lt;const
                  T&gt;...&gt;::const_iterator allows changing dereferenced elements"</em></span>)</a>.
                </li>
</ul></div>
          </li>
<li class="listitem">
            <span class="bold"><strong>Source Breaking</strong></span>: <code class="computeroutput"><a class="link" href="../boost/container/scoped_allocator_adaptor.html" title="Class template scoped_allocator_adaptor">scoped_allocator_adaptor</a></code>'s
            <code class="computeroutput"><span class="identifier">propagate_on_container_copy_assignment</span></code>,
            <code class="computeroutput"><span class="identifier">propagate_on_container_move_assignment</span></code>
            and <code class="computeroutput"><span class="identifier">propagate_on_container_swap</span></code>
            are no longer <code class="computeroutput"><span class="special">::</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">integral_constant</span><span class="special">&lt;</span><span class="keyword">bool</span><span class="special">,</span> <span class="keyword">true</span><span class="special">/</span><span class="keyword">false</span><span class="special">&gt;</span></code> types. The dependency reorganization
            needed to break with those classes to avoid MPL dependencies, and interoperability
            with <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">integral_constant</span></code> was not guaranteed.
            Code assumming <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">true_type</span><span class="special">/</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">false_type</span></code>
            on this will not compile. As a workaround, use the guaranteed internal
            <code class="computeroutput"><span class="special">::</span><span class="identifier">value</span></code>
            constant: <code class="computeroutput"><span class="special">::</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">integral_constant</span><span class="special">&lt;</span><span class="keyword">bool</span><span class="special">,</span> <span class="identifier">scoped_allocator_adaptor</span><span class="special">&lt;</span><span class="identifier">Allocator</span><span class="special">&gt;::</span><span class="identifier">propagate_on_container_move_assignment</span><span class="special">::</span><span class="identifier">value</span><span class="special">&gt;</span></code>.
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_57_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_57_00" title="Boost 1.57 Release">Boost
      1.57 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Added support for <code class="computeroutput"><span class="identifier">initializer_list</span></code>.
            Contributed by Robert Matusewicz.
          </li>
<li class="listitem">
            Fixed double destruction bugs in vector and backward expansion capable
            allocators.
          </li>
<li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/10263" target="_top">Trac
                  #10263 (<span class="emphasis"><em>"AIX 6.1 bug with sched_yield() function
                  out of scope"</em></span>)</a>.
                </li>
<li class="listitem">
                  <a href="https://github.com/boostorg/container/pull/16" target="_top">GitHub
                  #16: <span class="emphasis"><em>Fix iterators of incomplete type containers</em></span></a>.
                  Thanks to Mikael Persson.
                </li>
</ul></div>
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_56_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_56_00" title="Boost 1.56 Release">Boost
      1.56 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Added DlMalloc-based <a class="link" href="extended_allocators.html" title="Extended functionality: Extended allocators">Extended
            Allocators</a>.
          </li>
<li class="listitem">
            <a class="link" href="configurable_containers.html#container.configurable_containers.configurable_tree_based_associative_containers" title="Configurable tree-based associative ordered containers">Improved
            configurability</a> of tree-based ordered associative containers.
            AVL, Scapegoat and Splay trees are now available to implement <code class="computeroutput"><a class="link" href="../boost/container/set.html" title="Class template set">set</a></code>, <code class="computeroutput"><a class="link" href="../boost/container/multiset.html" title="Class template multiset">multiset</a></code>,
            <code class="computeroutput"><a class="link" href="../boost/container/map.html" title="Class template map">map</a></code> and <code class="computeroutput"><a class="link" href="../boost/container/multimap.html" title="Class template multimap">multimap</a></code>.
          </li>
<li class="listitem">
            Fixed bugs:
            <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; ">
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/9338" target="_top">#9338:
                  <span class="emphasis"><em>"VS2005 compiler errors in swap() definition after
                  including container/memory_util.hpp"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/9637" target="_top">#9637:
                  <span class="emphasis"><em>"Boost.Container vector::resize() performance issue"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/9648" target="_top">#9648:
                  <span class="emphasis"><em>"string construction optimization - char_traits::copy
                  could be used ..."</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/9801" target="_top">#9801:
                  <span class="emphasis"><em>"I can no longer create and iterator_range from
                  a stable_vector"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/9915" target="_top">#9915:
                  <span class="emphasis"><em>"Documentation issues regarding vector constructors
                  and resize methods - value/default initialization"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/9916" target="_top">#9916:
                  <span class="emphasis"><em>"Allocator propagation incorrect in the assignment
                  operator of most"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/9931" target="_top">#9931:
                  <span class="emphasis"><em>"flat_map::insert(ordered_unique_range_t...) fails
                  with move_iterators"</em></span></a>.
                </li>
<li class="listitem">
                  <a href="https://svn.boost.org/trac/boost/ticket/9955" target="_top">#9955:
                  <span class="emphasis"><em>"Using memcpy with overlapped buffers in vector"</em></span></a>.
                </li>
</ul></div>
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_55_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_55_00" title="Boost 1.55 Release">Boost
      1.55 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Implemented <a class="link" href="main_features.html#container.main_features.scary_iterators" title="SCARY iterators">SCARY
            iterators</a>.
          </li>
<li class="listitem">
            Fixed bugs <a href="https://svn.boost.org/trac/boost/ticket/8269" target="_top">#8269</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/8473" target="_top">#8473</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/8892" target="_top">#8892</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/9009" target="_top">#9009</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/9064" target="_top">#9064</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/9092" target="_top">#9092</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/9108" target="_top">#9108</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/9166" target="_top">#9166</a>.
          </li>
<li class="listitem">
            Added <code class="computeroutput"><span class="keyword">default</span> <span class="identifier">initialization</span></code>
            insertion functions to vector-like containers with new overloads taking
            <code class="computeroutput"><span class="identifier">default_init_t</span></code> as an
            argument instead of <code class="computeroutput"><span class="keyword">const</span> <span class="identifier">value_type</span> <span class="special">&amp;</span></code>.
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_54_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_54_00" title="Boost 1.54 Release">Boost
      1.54 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Added experimental <code class="computeroutput"><span class="identifier">static_vector</span></code>
            class, based on Andrew Hundt's and Adam Wulkiewicz's high performance
            <code class="computeroutput"><span class="identifier">varray</span></code> class.
          </li>
<li class="listitem">
            Speed improvements in <code class="computeroutput"><span class="identifier">vector</span></code>
            constructors/copy/move/swap, dispatching to memcpy when possible.
          </li>
<li class="listitem">
            Support for <code class="computeroutput"><span class="identifier">BOOST_NO_EXCEPTIONS</span></code>
            <a href="https://svn.boost.org/trac/boost/ticket/7227" target="_top">#7227</a>.
          </li>
<li class="listitem">
            Fixed bugs <a href="https://svn.boost.org/trac/boost/ticket/7921" target="_top">#7921</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/7969" target="_top">#7969</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/8118" target="_top">#8118</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/8294" target="_top">#8294</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/8553" target="_top">#8553</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/8724" target="_top">#8724</a>.
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_53_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_53_00" title="Boost 1.53 Release">Boost
      1.53 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Fixed bug <a href="https://svn.boost.org/trac/boost/ticket/7650" target="_top">#7650</a>.
          </li>
<li class="listitem">
            Improved <code class="computeroutput"><span class="identifier">vector</span></code>'s insertion
            performance.
          </li>
<li class="listitem">
            Changed again experimental multiallocation interface for better performance
            (still experimental).
          </li>
<li class="listitem">
            Added no exception support for those willing to disable exceptions in
            their compilers.
          </li>
<li class="listitem">
            Fixed GCC -Wshadow warnings.
          </li>
<li class="listitem">
            Replaced deprecated BOOST_NO_XXXX with newer BOOST_NO_CXX11_XXX macros.
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_52_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_52_00" title="Boost 1.52 Release">Boost
      1.52 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Improved <code class="computeroutput"><span class="identifier">stable_vector</span></code>'s
            template code bloat and type safety.
          </li>
<li class="listitem">
            Changed typedefs and reordered functions of sequence containers to improve
            doxygen documentation.
          </li>
<li class="listitem">
            Fixed bugs <a href="https://svn.boost.org/trac/boost/ticket/6615" target="_top">#6615</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/7139" target="_top">#7139</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/7215" target="_top">#7215</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/7232" target="_top">#7232</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/7269" target="_top">#7269</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/7439" target="_top">#7439</a>.
          </li>
<li class="listitem">
            Implemented LWG Issue #149 (range insertion now returns an iterator)
            &amp; cleaned up insertion code in most containers
          </li>
<li class="listitem">
            Corrected aliasing errors.
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_51_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_51_00" title="Boost 1.51 Release">Boost
      1.51 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            Fixed bugs <a href="https://svn.boost.org/trac/boost/ticket/6763" target="_top">#6763</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/6803" target="_top">#6803</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/7114" target="_top">#7114</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/7103" target="_top">#7103</a>.
            <a href="https://svn.boost.org/trac/boost/ticket/7123" target="_top">#7123</a>,
          </li></ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_50_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_50_00" title="Boost 1.50 Release">Boost
      1.50 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Added Scoped Allocator Model support.
          </li>
<li class="listitem">
            Fixed bugs <a href="https://svn.boost.org/trac/boost/ticket/6606" target="_top">#6606</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/6533" target="_top">#6533</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/6536" target="_top">#6536</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/6566" target="_top">#6566</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/6575" target="_top">#6575</a>,
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_49_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_49_00" title="Boost 1.49 Release">Boost
      1.49 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
            Fixed bugs <a href="https://svn.boost.org/trac/boost/ticket/6540" target="_top">#6540</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/6499" target="_top">#6499</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/6336" target="_top">#6336</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/6335" target="_top">#6335</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/6287" target="_top">#6287</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/6205" target="_top">#6205</a>,
            <a href="https://svn.boost.org/trac/boost/ticket/4383" target="_top">#4383</a>.
          </li>
<li class="listitem">
            Added <code class="computeroutput"><span class="identifier">allocator_traits</span></code>
            support for both C++11 and C++03 compilers through an internal <code class="computeroutput"><span class="identifier">allocator_traits</span></code> clone.
          </li>
</ul></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="container.release_notes.release_notes_boost_1_48_00"></a><a class="link" href="release_notes.html#container.release_notes.release_notes_boost_1_48_00" title="Boost 1.48 Release">Boost
      1.48 Release</a>
</h3></div></div></div>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            First release. Container code from <span class="bold"><strong>Boost.Interprocess</strong></span>
            was deleted and redirected to <span class="bold"><strong>Boost.Container
            </strong></span> via using directives.
          </li></ul></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2009-2018 Ion Gaztanaga<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="acknowledgements_notes.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../container.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../conversion.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
