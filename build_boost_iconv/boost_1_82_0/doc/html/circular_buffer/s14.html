<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Index</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../circular_buffer.html" title="Chapter 7. Boost.Circular Buffer">
<link rel="prev" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">
<link rel="next" href="../container.html" title="Chapter 8. Boost.Container">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../boost/circular_buffer_sp_idm5277.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../circular_buffer.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../container.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="id-1.3.8.17"></a>Index</h2></div></div></div>
<p><a class="link" href="s14.html#idx_id_0">A</a> <a class="link" href="s14.html#idx_id_1">B</a> <a class="link" href="s14.html#idx_id_2">C</a> <a class="link" href="s14.html#idx_id_3">D</a> <a class="link" href="s14.html#idx_id_4">E</a> <a class="link" href="s14.html#idx_id_5">F</a> <a class="link" href="s14.html#idx_id_6">H</a> <a class="link" href="s14.html#idx_id_7">I</a> <a class="link" href="s14.html#idx_id_8">L</a> <a class="link" href="s14.html#idx_id_9">M</a> <a class="link" href="s14.html#idx_id_10">P</a> <a class="link" href="s14.html#idx_id_11">R</a> <a class="link" href="s14.html#idx_id_12">S</a> <a class="link" href="s14.html#idx_id_13">T</a> <a class="link" href="s14.html#idx_id_14">V</a> <a class="link" href="s14.html#idx_id_15">W</a></p>
<div class="variablelist"><dl class="variablelist">
<dt>
<a name="idx_id_0"></a><span class="term">A</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">allocator_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">array</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">array_range</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">assign</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_1"></a><span class="term">B</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">begin</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">BOOST_CB_ENABLE_DEBUG</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost_circular_buffer_c___reference.html#header.boost.circular_buffer_hpp" title="Header &lt;boost/circular_buffer.hpp&gt;"><span class="index-entry-level-1">Header &lt; boost/circular_buffer.hpp &gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">Implementation </span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_CB_ENABLE_DEBUG.html" title="Macro BOOST_CB_ENABLE_DEBUG"><span class="index-entry-level-1">Macro BOOST_CB_ENABLE_DEBUG</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">bounded_buffer</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">More Examples</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">buffer</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">Implementation </span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../circular_buffer.html#circular_buffer.intro" title="Introduction"><span class="index-entry-level-1">Introduction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="rationale.html" title="Rationale"><span class="index-entry-level-1">Rationale</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_2"></a><span class="term">C</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">capacity</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">capacity_control</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">capacity_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">cb</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="example.html" title="Circular_buffer example"><span class="index-entry-level-1">Circular_buffer example</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">Implementation </span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">More Examples</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">circular_buffer</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="release.html" title="Release Notes"><span class="index-entry-level-1">Release Notes</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Circular_buffer example</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="example.html" title="Circular_buffer example"><span class="index-entry-level-1">cb</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">circular_buffer_space_optimized</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Class template circular_buffer</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">allocator_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">array</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">array_range</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">assign</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">begin</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">capacity</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">capacity_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Constant</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">const_array_range</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">const_iterator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">const_pointer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">const_reference</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">const_reverse_iterator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">destructruction</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">difference_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">element</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">end</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">erase_begin</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">erase_end</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">exhausted</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">insert</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">invalid</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">it</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">iterator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Linear</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">linearize</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">param_value_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">point</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">pointer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">pop_back</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">postcondition</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">push_front</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">range</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">reference</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">resize</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">reverse_iterator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">rinsert</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">rotate</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">rvalue_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">size_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">this_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">value_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">write</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Class template circular_buffer_space_optimized</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">allocator_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">array_range</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">assign</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">begin</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">capacity</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">capacity_control</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">capacity_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">circular_buffer_space_optimized</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Constant</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">const_array_range</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">const_iterator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">const_pointer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">const_reference</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">const_reverse_iterator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">difference_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">end</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">exhausted</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">insert</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">iterator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Linear</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">param_value_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">pointer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">pop_back</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">push_front</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">reference</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">resize</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">reverse_iterator</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">rinsert</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">rvalue_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">size_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">value_type</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Constant</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/swap_idm5227.html" title="Function template swap"><span class="index-entry-level-1">Function template swap</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">const_array_range</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">const_iterator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">const_pointer</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">const_reference</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">const_reverse_iterator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">consumer</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">Implementation </span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">container_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">More Examples</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_3"></a><span class="term">D</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">destructruction</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">difference_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_4"></a><span class="term">E</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">element</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">Implementation </span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">end</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">erase_begin</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="release.html" title="Release Notes"><span class="index-entry-level-1">Release Notes</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">erase_end</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="release.html" title="Release Notes"><span class="index-entry-level-1">Release Notes</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">exhausted</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_5"></a><span class="term">F</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Function template operator!=</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/operator___idm5059.html" title="Function template operator!="><span class="index-entry-level-1">Linear</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Function template operator&gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/operator_idm5101.html" title="Function template operator&gt;"><span class="index-entry-level-1">Linear</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Function template operator&gt;=</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/operator___idm5185.html" title="Function template operator&gt;="><span class="index-entry-level-1">Linear</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Function template operator&lt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/operator_idm5020.html" title="Function template operator&lt;"><span class="index-entry-level-1">Linear</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Function template operator&lt;=</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/operator___idm5143.html" title="Function template operator&lt;="><span class="index-entry-level-1">Linear</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Function template operator==</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/operator___idm4981.html" title="Function template operator=="><span class="index-entry-level-1">Linear</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Function template swap</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/swap_idm5227.html" title="Function template swap"><span class="index-entry-level-1">Constant</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_6"></a><span class="term">H</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Header &lt; boost/circular_buffer.hpp &gt;</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost_circular_buffer_c___reference.html#header.boost.circular_buffer_hpp" title="Header &lt;boost/circular_buffer.hpp&gt;"><span class="index-entry-level-1">BOOST_CB_ENABLE_DEBUG</span></a></p></li></ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_7"></a><span class="term">I</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Implementation </span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">BOOST_CB_ENABLE_DEBUG</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">cb</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">consumer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">element</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">main</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">pop_back</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">insert</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Introduction</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../circular_buffer.html#circular_buffer.intro" title="Introduction"><span class="index-entry-level-1">buffer</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">invalid</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">it</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">iterator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_8"></a><span class="term">L</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Linear</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/operator___idm5059.html" title="Function template operator!="><span class="index-entry-level-1">Function template operator!=</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/operator_idm5101.html" title="Function template operator&gt;"><span class="index-entry-level-1">Function template operator&gt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/operator___idm5185.html" title="Function template operator&gt;="><span class="index-entry-level-1">Function template operator&gt;=</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/operator_idm5020.html" title="Function template operator&lt;"><span class="index-entry-level-1">Function template operator&lt;</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/operator___idm5143.html" title="Function template operator&lt;="><span class="index-entry-level-1">Function template operator&lt;=</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/operator___idm4981.html" title="Function template operator=="><span class="index-entry-level-1">Function template operator==</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">linearize</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="tickets.html" title="Trac Tickets"><span class="index-entry-level-1">Trac Tickets</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">lock</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">More Examples</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_9"></a><span class="term">M</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Macro BOOST_CB_ENABLE_DEBUG</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../BOOST_CB_ENABLE_DEBUG.html" title="Macro BOOST_CB_ENABLE_DEBUG"><span class="index-entry-level-1">BOOST_CB_ENABLE_DEBUG</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">main</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">Implementation </span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">More Examples</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">More Examples</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">bounded_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">cb</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">container_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">lock</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">main</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">param_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">pop_back</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">push_front</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">size_type</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">value_type</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_10"></a><span class="term">P</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">param_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">More Examples</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">param_value_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">point</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">pointer</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">pop_back</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="implementation.html" title="Implementation"><span class="index-entry-level-1">Implementation </span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">More Examples</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">postcondition</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">push_front</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">More Examples</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_11"></a><span class="term">R</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">range</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Rationale</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="rationale.html" title="Rationale"><span class="index-entry-level-1">buffer</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">reference</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Release Notes</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="release.html" title="Release Notes"><span class="index-entry-level-1">circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="release.html" title="Release Notes"><span class="index-entry-level-1">erase_begin</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="release.html" title="Release Notes"><span class="index-entry-level-1">erase_end</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="release.html" title="Release Notes"><span class="index-entry-level-1">rotate</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">resize</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">reverse_iterator</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">rinsert</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">rotate</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="release.html" title="Release Notes"><span class="index-entry-level-1">Release Notes</span></a></p></li>
</ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">rvalue_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
</ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_12"></a><span class="term">S</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">size_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">More Examples</span></a></p></li>
</ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_13"></a><span class="term">T</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">this_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li></ul></div>
</li>
<li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">Trac Tickets</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="tickets.html" title="Trac Tickets"><span class="index-entry-level-1">linearize</span></a></p></li></ul></div>
</li>
</ul></div></dd>
<dt>
<a name="idx_id_14"></a><span class="term">V</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">value_type</span></p>
<div class="index"><ul class="index" style="list-style-type: none; ">
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized"><span class="index-entry-level-1">Class template circular_buffer_space_optimized</span></a></p></li>
<li class="listitem" style="list-style-type: none"><p><a class="link" href="examples.html" title="More Examples"><span class="index-entry-level-1">More Examples</span></a></p></li>
</ul></div>
</li></ul></div></dd>
<dt>
<a name="idx_id_15"></a><span class="term">W</span>
</dt>
<dd><div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none">
<p><span class="index-entry-level-0">write</span></p>
<div class="index"><ul class="index" style="list-style-type: none; "><li class="listitem" style="list-style-type: none"><p><a class="link" href="../boost/circular_buffer.html" title="Class template circular_buffer"><span class="index-entry-level-1">Class template circular_buffer</span></a></p></li></ul></div>
</li></ul></div></dd>
</dl></div>
</div>
<div class="copyright-footer">Copyright © 2003-2013 Jan Gaspar<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../boost/circular_buffer_sp_idm5277.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../circular_buffer.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../container.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
