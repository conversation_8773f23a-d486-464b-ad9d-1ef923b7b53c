<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Synchronized Data Structures</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../thread.html" title="Chapter 38. Thread 4.8.0">
<link rel="prev" href="thread_local_storage.html" title="Thread Local Storage">
<link rel="next" href="parallel.html" title="Parallel - Fork-Join -- EXPERIMENTAL">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="thread_local_storage.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../thread.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="parallel.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="thread.sds"></a><a class="link" href="sds.html" title="Synchronized Data Structures">Synchronized Data Structures</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_valuesxxx">Synchronized Values
      - EXPERIMENTAL</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_queues">Synchronized Queues --
      EXPERIMENTAL</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="thread.sds.synchronized_valuesxxx"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx" title="Synchronized Values - EXPERIMENTAL">Synchronized Values
      - EXPERIMENTAL</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_valuesxxx.tutorial">Tutorial</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref">Reference
        </a></span></dt>
</dl></div>
<div class="warning"><table border="0" summary="Warning">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Warning]" src="../../../doc/src/images/warning.png"></td>
<th align="left">Warning</th>
</tr>
<tr><td align="left" valign="top"><p>
          These features are experimental and subject to change in future versions.
          There are not too much tests yet, so it is possible that you can find out
          some trivial bugs :(
        </p></td></tr>
</table></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="thread.sds.synchronized_valuesxxx.tutorial"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.tutorial" title="Tutorial">Tutorial</a>
</h4></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_valuesxxx.tutorial.the_problem_with_mutexes">The
          Problem with Mutexes</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_valuesxxx.tutorial.beyond_simple_accesses">Beyond
          Simple Accesses</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_valuesxxx.tutorial.operations_across_multiple_objects">Operations
          Across Multiple Objects</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_valuesxxx.tutorial.value_semantics">Value
          semantics</a></span></dt>
</dl></div>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
            This tutorial is an adaptation of the paper of Anthony Williams "Enforcing
            Correct Mutex Usage with Synchronized Values" to the Boost library.
          </p></td></tr>
</table></div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="thread.sds.synchronized_valuesxxx.tutorial.the_problem_with_mutexes"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.tutorial.the_problem_with_mutexes" title="The Problem with Mutexes">The
          Problem with Mutexes</a>
</h5></div></div></div>
<p>
            The key problem with protecting shared data with a mutex is that there
            is no easy way to associate the mutex with the data. It is thus relatively
            easy to accidentally write code that fails to lock the right mutex -
            or even locks the wrong mutex - and the compiler will not help you.
          </p>
<pre class="programlisting"><span class="identifier">std</span><span class="special">::</span><span class="identifier">mutex</span> <span class="identifier">m1</span><span class="special">;</span>
<span class="keyword">int</span> <span class="identifier">value1</span><span class="special">;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">mutex</span> <span class="identifier">m2</span><span class="special">;</span>
<span class="keyword">int</span> <span class="identifier">value2</span><span class="special">;</span>

<span class="keyword">int</span> <span class="identifier">readValue1</span><span class="special">()</span>
<span class="special">{</span>
  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">lock_guard</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">mutex</span><span class="special">&gt;</span> <span class="identifier">lk</span><span class="special">(</span><span class="identifier">m1</span><span class="special">);</span>
  <span class="keyword">return</span> <span class="identifier">value1</span><span class="special">;</span>
<span class="special">}</span>
<span class="keyword">int</span> <span class="identifier">readValue2</span><span class="special">()</span>
<span class="special">{</span>
  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">lock_guard</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">mutex</span><span class="special">&gt;</span> <span class="identifier">lk</span><span class="special">(</span><span class="identifier">m1</span><span class="special">);</span> <span class="comment">// oops: wrong mutex</span>
  <span class="keyword">return</span> <span class="identifier">value2</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
            Moreover, managing the mutex lock also clutters the source code, making
            it harder to see what is really going on.
          </p>
<p>
            The use of synchronized_value solves both these problems - the mutex
            is intimately tied to the value, so you cannot access it without a lock,
            and yet access semantics are still straightforward. For simple accesses,
            synchronized_value behaves like a pointer-to-T; for example:
          </p>
<pre class="programlisting"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span><span class="special">&gt;</span> <span class="identifier">value3</span><span class="special">;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="identifier">readValue3</span><span class="special">()</span>
<span class="special">{</span>
  <span class="keyword">return</span> <span class="special">*</span><span class="identifier">value3</span><span class="special">;</span>
<span class="special">}</span>
<span class="keyword">void</span> <span class="identifier">setValue3</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">newVal</span><span class="special">)</span>
<span class="special">{</span>
  <span class="special">*</span><span class="identifier">value3</span><span class="special">=</span><span class="identifier">newVal</span><span class="special">;</span>
<span class="special">}</span>
<span class="keyword">void</span> <span class="identifier">appendToValue3</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">extra</span><span class="special">)</span>
<span class="special">{</span>
  <span class="identifier">value3</span><span class="special">-&gt;</span><span class="identifier">append</span><span class="special">(</span><span class="identifier">extra</span><span class="special">);</span>
<span class="special">}</span>
</pre>
<p>
            Both forms of pointer dereference return a proxy object rather than a
            real reference, to ensure that the lock on the mutex is held across the
            assignment or method call, but this is transparent to the user.
          </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="thread.sds.synchronized_valuesxxx.tutorial.beyond_simple_accesses"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.tutorial.beyond_simple_accesses" title="Beyond Simple Accesses">Beyond
          Simple Accesses</a>
</h5></div></div></div>
<p>
            The pointer-like semantics work very well for simple accesses such as
            assignment and calls to member functions. However, sometimes you need
            to perform an operation that requires multiple accesses under protection
            of the same lock, and that's what the synchronize() method provides.
          </p>
<p>
            By calling synchronize() you obtain a strict_lock_ptr object that holds
            a lock on the mutex protecting the data, and which can be used to access
            the protected data. The lock is held until the strict_lock_ptr object
            is destroyed, so you can safely perform multi-part operations. The strict_lock_ptr
            object also acts as a pointer-to-T, just like synchronized_value does,
            but this time the lock is already held. For example, the following function
            adds a trailing slash to a path held in a synchronized_value. The use
            of the strict_lock_ptr object ensures that the string hasn't changed
            in between the query and the update.
          </p>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">addTrailingSlashIfMissing</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span><span class="special">&gt;</span> <span class="special">&amp;</span> <span class="identifier">path</span><span class="special">)</span>
<span class="special">{</span>
  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">strict_lock_ptr</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span><span class="special">&gt;</span> <span class="identifier">u</span><span class="special">=</span><span class="identifier">path</span><span class="special">.</span><span class="identifier">synchronize</span><span class="special">();</span>

  <span class="keyword">if</span><span class="special">(</span><span class="identifier">u</span><span class="special">-&gt;</span><span class="identifier">empty</span><span class="special">()</span> <span class="special">||</span> <span class="special">(*</span><span class="identifier">u</span><span class="special">-&gt;</span><span class="identifier">rbegin</span><span class="special">()!=</span><span class="char">'/'</span><span class="special">))</span>
  <span class="special">{</span>
    <span class="special">*</span><span class="identifier">u</span><span class="special">+=</span><span class="char">'/'</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="thread.sds.synchronized_valuesxxx.tutorial.operations_across_multiple_objects"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.tutorial.operations_across_multiple_objects" title="Operations Across Multiple Objects">Operations
          Across Multiple Objects</a>
</h5></div></div></div>
<p>
            Though synchronized_value works very well for protecting a single object
            of type T, nothing that we've seen so far solves the problem of operations
            that require atomic access to multiple objects unless those objects can
            be combined within a single structure protected by a single mutex.
          </p>
<p>
            One way to protect access to two synchronized_value objects is to construct
            a strict_lock_ptr for each object and use those to access the respective
            protected values; for instance:
          </p>
<pre class="programlisting"><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">queue</span><span class="special">&lt;</span><span class="identifier">MessageType</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">q1</span><span class="special">,</span><span class="identifier">q2</span><span class="special">;</span>
<span class="keyword">void</span> <span class="identifier">transferMessage</span><span class="special">()</span>
<span class="special">{</span>
  <span class="identifier">strict_lock_ptr</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">queue</span><span class="special">&lt;</span><span class="identifier">MessageType</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">u1</span> <span class="special">=</span> <span class="identifier">q1</span><span class="special">.</span><span class="identifier">synchronize</span><span class="special">();</span>
  <span class="identifier">strict_lock_ptr</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">queue</span><span class="special">&lt;</span><span class="identifier">MessageType</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">u2</span> <span class="special">=</span> <span class="identifier">q2</span><span class="special">.</span><span class="identifier">synchronize</span><span class="special">();</span>

  <span class="keyword">if</span><span class="special">(!</span><span class="identifier">u1</span><span class="special">-&gt;</span><span class="identifier">empty</span><span class="special">())</span>
  <span class="special">{</span>
    <span class="identifier">u2</span><span class="special">-&gt;</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">u1</span><span class="special">-&gt;</span><span class="identifier">front</span><span class="special">());</span>
    <span class="identifier">u1</span><span class="special">-&gt;</span><span class="identifier">pop_front</span><span class="special">();</span>
  <span class="special">}</span>
<span class="special">}</span>
</pre>
<p>
            This works well in some scenarios, but not all -- if the same two objects
            are updated together in different sections of code then you need to take
            care to ensure that the strict_lock_ptr objects are constructed in the
            same sequence in all cases, otherwise you have the potential for deadlock.
            This is just the same as when acquiring any two mutexes.
          </p>
<p>
            In order to be able to use the dead-lock free lock algorithms we need
            to use instead unique_lock_ptr, which is Lockable.
          </p>
<pre class="programlisting"><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">queue</span><span class="special">&lt;</span><span class="identifier">MessageType</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">q1</span><span class="special">,</span><span class="identifier">q2</span><span class="special">;</span>
<span class="keyword">void</span> <span class="identifier">transferMessage</span><span class="special">()</span>
<span class="special">{</span>
  <span class="identifier">unique_lock_ptr</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">queue</span><span class="special">&lt;</span><span class="identifier">MessageType</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">u1</span> <span class="special">=</span> <span class="identifier">q1</span><span class="special">.</span><span class="identifier">unique_synchronize</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">defer_lock</span><span class="special">);</span>
  <span class="identifier">unique_lock_ptr</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">queue</span><span class="special">&lt;</span><span class="identifier">MessageType</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">u2</span> <span class="special">=</span> <span class="identifier">q2</span><span class="special">.</span><span class="identifier">unique_synchronize</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">defer_lock</span><span class="special">);</span>
  <span class="identifier">boost</span><span class="special">::</span><span class="identifier">lock</span><span class="special">(</span><span class="identifier">u1</span><span class="special">,</span><span class="identifier">u2</span><span class="special">);</span> <span class="comment">// dead-lock free algorithm</span>

  <span class="keyword">if</span><span class="special">(!</span><span class="identifier">u1</span><span class="special">-&gt;</span><span class="identifier">empty</span><span class="special">())</span>
  <span class="special">{</span>
    <span class="identifier">u2</span><span class="special">-&gt;</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">u1</span><span class="special">-&gt;</span><span class="identifier">front</span><span class="special">());</span>
    <span class="identifier">u1</span><span class="special">-&gt;</span><span class="identifier">pop_front</span><span class="special">();</span>
  <span class="special">}</span>
<span class="special">}</span>
</pre>
<p>
            While the preceding takes care of dead-lock, the access to the synchronized_value
            via unique_lock_ptr requires a lock that is not forced by the interface.
            An alternative on compilers providing a standard library that supports
            movable std::tuple is to use the free synchronize function, which will
            lock all the mutexes associated to the synchronized values and return
            a tuple os strict_lock_ptr.
          </p>
<pre class="programlisting"><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">queue</span><span class="special">&lt;</span><span class="identifier">MessageType</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">q1</span><span class="special">,</span><span class="identifier">q2</span><span class="special">;</span>
<span class="keyword">void</span> <span class="identifier">transferMessage</span><span class="special">()</span>
<span class="special">{</span>
  <span class="keyword">auto</span> <span class="identifier">lks</span> <span class="special">=</span> <span class="identifier">synchronize</span><span class="special">(</span><span class="identifier">u1</span><span class="special">,</span><span class="identifier">u2</span><span class="special">);</span> <span class="comment">// dead-lock free algorithm</span>

  <span class="keyword">if</span><span class="special">(!</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">get</span><span class="special">&lt;</span><span class="number">1</span><span class="special">&gt;(</span><span class="identifier">lks</span><span class="special">)-&gt;</span><span class="identifier">empty</span><span class="special">())</span>
  <span class="special">{</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">get</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;(</span><span class="identifier">lks</span><span class="special">)-&gt;</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">u1</span><span class="special">-&gt;</span><span class="identifier">front</span><span class="special">());</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">get</span><span class="special">&lt;</span><span class="number">1</span><span class="special">&gt;(</span><span class="identifier">lks</span><span class="special">)-&gt;</span><span class="identifier">pop_front</span><span class="special">();</span>
  <span class="special">}</span>
<span class="special">}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="thread.sds.synchronized_valuesxxx.tutorial.value_semantics"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.tutorial.value_semantics" title="Value semantics">Value
          semantics</a>
</h5></div></div></div>
<p>
            synchronized_value has value semantics even if the syntax lets is close
            to a pointer (this is just because we are unable to define smart references).
          </p>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref" title="Reference">Reference
        </a>
</h4></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value">Class
          <code class="computeroutput"><span class="identifier">synchronized_value</span></code></a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronize">Non-Member
          Function <code class="computeroutput"><span class="identifier">synchronize</span></code></a></span></dt>
</dl></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">synchronized_value</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">Lockable</span> <span class="special">=</span> <span class="identifier">mutex</span><span class="special">&gt;</span>
  <span class="keyword">class</span> <span class="identifier">synchronized_value</span><span class="special">;</span>

  <span class="comment">// Specialized swap algorithm</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">T</span> <span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span><span class="identifier">T</span> <span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>

  <span class="comment">// Hash support</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">struct</span> <span class="identifier">hash</span><span class="special">&lt;</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="special">&gt;;</span>

  <span class="comment">// Comparison</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">)</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">)</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">)</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">)</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">)</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">)</span>

  <span class="comment">// Comparison with T</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span><span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">rhs</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span><span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">rhs</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;(</span><span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">rhs</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span><span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">rhs</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;(</span><span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">rhs</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span><span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">rhs</span><span class="special">);</span>

  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">==(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">!=(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&lt;=(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">L</span><span class="special">&gt;</span>
  <span class="keyword">bool</span> <span class="keyword">operator</span><span class="special">&gt;=(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">L</span><span class="special">&gt;</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">lhs</span><span class="special">,</span> <span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>

<span class="preprocessor">#if</span> <span class="special">!</span> <span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_THREAD_NO_SYNCHRONIZE</span><span class="special">)</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="special">...</span><span class="identifier">SV</span><span class="special">&gt;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">tuple</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">synchronized_value_strict_lock_ptr</span><span class="special">&lt;</span><span class="identifier">SV</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="special">...&gt;</span> <span class="identifier">synchronize</span><span class="special">(</span><span class="identifier">SV</span><span class="special">&amp;</span> <span class="special">...</span><span class="identifier">sv</span><span class="special">);</span>
<span class="preprocessor">#endif</span>
<span class="special">}</span>
</pre>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value" title="Class synchronized_value">Class
          <code class="computeroutput"><span class="identifier">synchronized_value</span></code></a>
</h5></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">synchronized_value</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>

  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">Lockable</span> <span class="special">=</span> <span class="identifier">mutex</span><span class="special">&gt;</span>
  <span class="keyword">class</span> <span class="identifier">synchronized_value</span>
  <span class="special">{</span>
  <span class="keyword">public</span><span class="special">:</span>
    <span class="keyword">typedef</span> <span class="identifier">T</span> <span class="identifier">value_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">Lockable</span> <span class="identifier">mutex_type</span><span class="special">;</span>

    <span class="identifier">synchronized_value</span><span class="special">()</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">is_nothrow_default_constructible</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">value</span><span class="special">);</span>
    <span class="identifier">synchronized_value</span><span class="special">(</span><span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">is_nothrow_copy_constructible</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">value</span><span class="special">);</span>
    <span class="identifier">synchronized_value</span><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">is_nothrow_move_constructible</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">value</span><span class="special">);</span>
    <span class="identifier">synchronized_value</span><span class="special">(</span><span class="identifier">synchronized_value</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
    <span class="identifier">synchronized_value</span><span class="special">(</span><span class="identifier">synchronized_value</span><span class="special">&amp;&amp;</span> <span class="identifier">other</span><span class="special">);</span>

    <span class="comment">// mutation</span>
    <span class="identifier">synchronized_value</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="identifier">synchronized_value</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
    <span class="identifier">synchronized_value</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="identifier">value_type</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">);</span>
    <span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span><span class="identifier">synchronized_value</span> <span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
    <span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span><span class="identifier">value_type</span> <span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>

    <span class="comment">//observers</span>
    <span class="identifier">T</span> <span class="identifier">get</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="preprocessor">#if</span> <span class="special">!</span> <span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_NO_CXX11_EXPLICIT_CONVERSION_OPERATORS</span><span class="special">)</span>
    <span class="keyword">explicit</span> <span class="keyword">operator</span> <span class="identifier">T</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="preprocessor">#endif</span>

    <span class="identifier">strict_lock_ptr</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">Lockable</span><span class="special">&gt;</span> <span class="keyword">operator</span><span class="special">-&gt;();</span>
    <span class="identifier">const_strict_lock_ptr</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">Lockable</span><span class="special">&gt;</span> <span class="keyword">operator</span><span class="special">-&gt;()</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="identifier">strict_lock_ptr</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">Lockable</span><span class="special">&gt;</span> <span class="identifier">synchronize</span><span class="special">();</span>
    <span class="identifier">const_strict_lock_ptr</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">Lockable</span><span class="special">&gt;</span> <span class="identifier">synchronize</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="identifier">deref_value</span> <span class="keyword">operator</span><span class="special">*();;</span>
    <span class="identifier">const_deref_value</span> <span class="keyword">operator</span><span class="special">*()</span> <span class="keyword">const</span><span class="special">;</span>

  <span class="keyword">private</span><span class="special">:</span>
    <span class="identifier">T</span> <span class="identifier">value_</span><span class="special">;</span> <span class="comment">// for exposition only</span>
    <span class="keyword">mutable</span> <span class="identifier">mutex_type</span> <span class="identifier">mtx_</span><span class="special">;</span>  <span class="comment">// for exposition only</span>
  <span class="special">};</span>
<span class="special">}</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires:</span></dt>
<dd><p>
                  <code class="computeroutput"><span class="identifier">Lockable</span></code> is <code class="computeroutput"><span class="identifier">Lockable</span></code>.
                </p></dd>
</dl>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.constructor"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.constructor" title="synchronized_value()"><code class="computeroutput"><span class="identifier">synchronized_value</span><span class="special">()</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">synchronized_value</span><span class="special">()</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">is_nothrow_default_constructible</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">value</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">T</span></code> is <code class="computeroutput"><span class="identifier">DefaultConstructible</span></code>.
                  </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
                    Default constructs the cloaked value_type
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Any exception thrown by <code class="computeroutput"><span class="identifier">value_type</span><span class="special">()</span></code>.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.constructor_vt"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.constructor_vt" title="synchronized_value(T const&amp;)"><code class="computeroutput"><span class="identifier">synchronized_value</span><span class="special">(</span><span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;)</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">synchronized_value</span><span class="special">(</span><span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">is_nothrow_copy_constructible</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">value</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">T</span></code> is <code class="computeroutput"><span class="identifier">CopyConstructible</span></code>.
                  </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
                    Copy constructs the cloaked value_type using the parameter <code class="computeroutput"><span class="identifier">other</span></code>
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Any exception thrown by <code class="computeroutput"><span class="identifier">value_type</span><span class="special">(</span><span class="identifier">other</span><span class="special">)</span></code>.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.copy_cons"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.copy_cons" title="synchronized_value(synchronized_value const&amp;)"><code class="computeroutput"><span class="identifier">synchronized_value</span><span class="special">(</span><span class="identifier">synchronized_value</span> <span class="keyword">const</span><span class="special">&amp;)</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">synchronized_value</span><span class="special">(</span><span class="identifier">synchronized_value</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">T</span></code> is <code class="computeroutput"><span class="identifier">DefaultConstructible</span></code> and <code class="computeroutput"><span class="identifier">Assignable</span></code>.
                  </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
                    Assigns the value on a scope protected by the mutex of the rhs.
                    The mutex is not copied.
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Any exception thrown by <code class="computeroutput"><span class="identifier">value_type</span><span class="special">()</span></code> or <code class="computeroutput"><span class="identifier">value_type</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="identifier">value_type</span><span class="special">&amp;)</span></code> or <code class="computeroutput"><span class="identifier">mtx_</span><span class="special">.</span><span class="identifier">lock</span><span class="special">()</span></code>.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.move_vt"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.move_vt" title="synchronized_value(T&amp;&amp;)"><code class="computeroutput"><span class="identifier">synchronized_value</span><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;&amp;)</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">synchronized_value</span><span class="special">(</span><span class="identifier">T</span><span class="special">&amp;&amp;</span> <span class="identifier">other</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">is_nothrow_move_constructible</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;::</span><span class="identifier">value</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">T</span></code> is <code class="computeroutput"><span class="identifier">MoveConstructible</span> </code>.
                  </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
                    Move constructs the cloaked value_type
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Any exception thrown by <code class="computeroutput"><span class="identifier">value_type</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;&amp;)</span></code>.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.move"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.move" title="synchronized_value(synchronized_value&amp;&amp;)"><code class="computeroutput"><span class="identifier">synchronized_value</span><span class="special">(</span><span class="identifier">synchronized_value</span><span class="special">&amp;&amp;)</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">synchronized_value</span><span class="special">(</span><span class="identifier">synchronized_value</span><span class="special">&amp;&amp;</span> <span class="identifier">other</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">T</span></code> is <code class="computeroutput"><span class="identifier">MoveConstructible</span> </code>.
                  </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
                    Move constructs the cloaked value_type
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Any exception thrown by <code class="computeroutput"><span class="identifier">value_type</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;&amp;)</span></code> or <code class="computeroutput"><span class="identifier">mtx_</span><span class="special">.</span><span class="identifier">lock</span><span class="special">()</span></code>.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.assign"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.assign" title="operator=(synchronized_value const&amp;)"><code class="computeroutput"><span class="keyword">operator</span><span class="special">=(</span><span class="identifier">synchronized_value</span> <span class="keyword">const</span><span class="special">&amp;)</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">synchronized_value</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="identifier">synchronized_value</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">T</span></code> is <code class="computeroutput"><span class="identifier">Assignable</span></code>.
                  </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
                    Copies the underlying value on a scope protected by the two mutexes.
                    The mutex is not copied. The locks are acquired avoiding deadlock.
                    For example, there is no problem if one thread assigns <code class="computeroutput"><span class="identifier">a</span> <span class="special">=</span>
                    <span class="identifier">b</span></code> and the other assigns
                    <code class="computeroutput"><span class="identifier">b</span> <span class="special">=</span>
                    <span class="identifier">a</span></code>.
                  </p></dd>
<dt><span class="term">Return:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Any exception thrown by <code class="computeroutput"><span class="identifier">value_type</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">(</span><span class="identifier">value_type</span>
                    <span class="keyword">const</span><span class="special">&amp;)</span></code>
                    or <code class="computeroutput"><span class="identifier">mtx_</span><span class="special">.</span><span class="identifier">lock</span><span class="special">()</span></code>.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.assign_vt"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.assign_vt" title="operator=(T const&amp;)"><code class="computeroutput"><span class="keyword">operator</span><span class="special">=(</span><span class="identifier">T</span> <span class="keyword">const</span><span class="special">&amp;)</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">synchronized_value</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="identifier">value_type</span> <span class="keyword">const</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">T</span></code> is <code class="computeroutput"><span class="identifier">Assignable</span></code>.
                  </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
                    Copies the value on a scope protected by the mutex.
                  </p></dd>
<dt><span class="term">Return:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="special">*</span><span class="keyword">this</span></code>
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Any exception thrown by <code class="computeroutput"><span class="identifier">value_type</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">(</span><span class="identifier">value_type</span>
                    <span class="keyword">const</span><span class="special">&amp;)</span></code>
                    or <code class="computeroutput"><span class="identifier">mtx_</span><span class="special">.</span><span class="identifier">lock</span><span class="special">()</span></code>.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.get"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.get" title="get() const"><code class="computeroutput"><span class="identifier">get</span><span class="special">()</span> <span class="keyword">const</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">T</span> <span class="identifier">get</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">T</span></code> is <code class="computeroutput"><span class="identifier">CopyConstructible</span></code>.
                  </p></dd>
<dt><span class="term">Return:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">A</span> <span class="identifier">copy</span>
                    <span class="identifier">of</span> <span class="identifier">the</span>
                    <span class="keyword">protected</span> <span class="identifier">value</span>
                    <span class="identifier">obtained</span> <span class="identifier">on</span>
                    <span class="identifier">a</span> <span class="identifier">scope</span>
                    <span class="keyword">protected</span> <span class="identifier">by</span>
                    <span class="identifier">the</span> <span class="identifier">mutex</span><span class="special">.</span></code>
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Any exception thrown by <code class="computeroutput"><span class="identifier">value_type</span><span class="special">(</span><span class="identifier">value_type</span>
                    <span class="keyword">const</span><span class="special">&amp;)</span></code>
                    or <code class="computeroutput"><span class="identifier">mtx_</span><span class="special">.</span><span class="identifier">lock</span><span class="special">()</span></code>.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.T"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.T" title="operator T() const"><code class="computeroutput"><span class="keyword">operator</span> <span class="identifier">T</span><span class="special">()</span> <span class="keyword">const</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="preprocessor">#if</span> <span class="special">!</span> <span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_NO_CXX11_EXPLICIT_CONVERSION_OPERATORS</span><span class="special">)</span>
  <span class="keyword">explicit</span> <span class="keyword">operator</span> <span class="identifier">T</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
<span class="preprocessor">#endif</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">T</span></code> is <code class="computeroutput"><span class="identifier">CopyConstructible</span></code>.
                  </p></dd>
<dt><span class="term">Return:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">A</span> <span class="identifier">copy</span>
                    <span class="identifier">of</span> <span class="identifier">the</span>
                    <span class="keyword">protected</span> <span class="identifier">value</span>
                    <span class="identifier">obtained</span> <span class="identifier">on</span>
                    <span class="identifier">a</span> <span class="identifier">scope</span>
                    <span class="keyword">protected</span> <span class="identifier">by</span>
                    <span class="identifier">the</span> <span class="identifier">mutex</span><span class="special">.</span></code>
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Any exception thrown by <code class="computeroutput"><span class="identifier">value_type</span><span class="special">(</span><span class="identifier">value_type</span>
                    <span class="keyword">const</span><span class="special">&amp;)</span></code>
                    or <code class="computeroutput"><span class="identifier">mtx_</span><span class="special">.</span><span class="identifier">lock</span><span class="special">()</span></code>.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.swap"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.swap" title="swap(synchronized_value&amp;)"><code class="computeroutput"><span class="identifier">swap</span><span class="special">(</span><span class="identifier">synchronized_value</span><span class="special">&amp;)</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span><span class="identifier">synchronized_value</span> <span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">T</span></code> is <code class="computeroutput"><span class="identifier">Assignable</span></code>.
                  </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
                    Swaps the data on a scope protected by both mutex. Both mutex
                    are acquired to avoid dead-lock. The mutexes are not swapped.
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Any exception thrown by <code class="computeroutput"><span class="identifier">swap</span><span class="special">(</span><span class="identifier">value_</span><span class="special">,</span> <span class="identifier">rhs</span><span class="special">.</span><span class="identifier">value</span><span class="special">)</span></code> or <code class="computeroutput"><span class="identifier">mtx_</span><span class="special">.</span><span class="identifier">lock</span><span class="special">()</span></code> or <code class="computeroutput"><span class="identifier">rhs_</span><span class="special">.</span><span class="identifier">mtx_</span><span class="special">.</span><span class="identifier">lock</span><span class="special">()</span></code>.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.swap_vt"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.swap_vt" title="swap(synchronized_value&amp;)"><code class="computeroutput"><span class="identifier">swap</span><span class="special">(</span><span class="identifier">synchronized_value</span><span class="special">&amp;)</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">swap</span><span class="special">(</span><span class="identifier">value_type</span> <span class="special">&amp;</span> <span class="identifier">rhs</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">T</span></code> is <code class="computeroutput"><span class="identifier">Swapable</span></code>.
                  </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
                    Swaps the data on a scope protected by both mutex. Both mutex
                    are acquired to avoid dead-lock. The mutexes are not swapped.
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Any exception thrown by <code class="computeroutput"><span class="identifier">swap</span><span class="special">(</span><span class="identifier">value_</span><span class="special">,</span> <span class="identifier">rhs</span><span class="special">)</span></code> or <code class="computeroutput"><span class="identifier">mtx_</span><span class="special">.</span><span class="identifier">lock</span><span class="special">()</span></code>.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.indir"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.indir" title="operator-&gt;()"><code class="computeroutput"><span class="keyword">operator</span><span class="special">-&gt;()</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">strict_lock_ptr</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">Lockable</span><span class="special">&gt;</span> <span class="keyword">operator</span><span class="special">-&gt;();</span>
</pre>
<p>
              Essentially calling a method <code class="computeroutput"><span class="identifier">obj</span><span class="special">-&gt;</span><span class="identifier">foo</span><span class="special">(</span><span class="identifier">x</span><span class="special">,</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">z</span><span class="special">)</span></code> calls the method <code class="computeroutput"><span class="identifier">foo</span><span class="special">(</span><span class="identifier">x</span><span class="special">,</span> <span class="identifier">y</span><span class="special">,</span> <span class="identifier">z</span><span class="special">)</span></code> inside a critical section as long-lived
              as the call itself.
            </p>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Return:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">A</span> <span class="identifier">strict_lock_ptr</span><span class="special">&lt;&gt;.</span></code>
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Nothing.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.indir_const"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.indir_const" title="operator-&gt;() const"><code class="computeroutput"><span class="keyword">operator</span><span class="special">-&gt;()</span>
            <span class="keyword">const</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">const_strict_lock_ptr</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">Lockable</span><span class="special">&gt;</span> <span class="keyword">operator</span><span class="special">-&gt;()</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<p>
              If the <code class="computeroutput"><span class="identifier">synchronized_value</span></code>
              object involved is const-qualified, then you'll only be able to call
              const methods through <code class="computeroutput"><span class="keyword">operator</span><span class="special">-&gt;</span></code>. So, for example, <code class="computeroutput"><span class="identifier">vec</span><span class="special">-&gt;</span><span class="identifier">push_back</span><span class="special">(</span><span class="string">"xyz"</span><span class="special">)</span></code>
              won't work if <code class="computeroutput"><span class="identifier">vec</span></code> were
              const-qualified. The locking mechanism capitalizes on the assumption
              that const methods don't modify their underlying data.
            </p>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Return:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">A</span> <span class="identifier">const_strict_lock_ptr</span>
                    <span class="special">&lt;&gt;.</span></code>
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Nothing.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.synchronize"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.synchronize" title="synchronize()"><code class="computeroutput"><span class="identifier">synchronize</span><span class="special">()</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">strict_lock_ptr</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">Lockable</span><span class="special">&gt;</span> <span class="identifier">synchronize</span><span class="special">();</span>
</pre>
<p>
              The synchronize() factory make easier to lock on a scope. As discussed,
              <code class="computeroutput"><span class="keyword">operator</span><span class="special">-&gt;</span></code>
              can only lock over the duration of a call, so it is insufficient for
              complex operations. With <code class="computeroutput"><span class="identifier">synchronize</span><span class="special">()</span></code> you get to lock the object in a scoped
              and to directly access the object inside that scope.
            </p>
<p>
              <span class="bold"><strong>Example:</strong></span>
            </p>
<pre class="programlisting"><span class="keyword">void</span> <span class="identifier">fun</span><span class="special">(</span><span class="identifier">synchronized_value</span><span class="special">&lt;</span><span class="identifier">vector</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;&gt;</span> <span class="special">&amp;</span> <span class="identifier">vec</span><span class="special">)</span> <span class="special">{</span>
  <span class="keyword">auto</span> <span class="identifier">vec2</span><span class="special">=</span><span class="identifier">vec</span><span class="special">.</span><span class="identifier">synchronize</span><span class="special">();</span>
  <span class="identifier">vec2</span><span class="special">.</span><span class="identifier">push_back</span><span class="special">(</span><span class="number">42</span><span class="special">);</span>
  <span class="identifier">assert</span><span class="special">(</span><span class="identifier">vec2</span><span class="special">.</span><span class="identifier">back</span><span class="special">()</span> <span class="special">==</span> <span class="number">42</span><span class="special">);</span>
<span class="special">}</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Return:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">A</span> <span class="identifier">strict_lock_ptr</span>
                    <span class="special">&lt;&gt;.</span></code>
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Nothing.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.synchronize_const"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.synchronize_const" title="synchronize() const"><code class="computeroutput"><span class="identifier">synchronize</span><span class="special">()</span>
            <span class="keyword">const</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">const_strict_lock_ptr</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span><span class="identifier">Lockable</span><span class="special">&gt;</span> <span class="identifier">synchronize</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Return:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">A</span> <span class="identifier">const_strict_lock_ptr</span>
                    <span class="special">&lt;&gt;.</span></code>
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Nothing.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.deref"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.deref" title="operator*()"><code class="computeroutput"><span class="keyword">operator</span><span class="special">*()</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">deref_value</span> <span class="keyword">operator</span><span class="special">*();;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Return:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">A</span> <span class="identifier">an</span>
                    <span class="identifier">instance</span> <span class="identifier">of</span>
                    <span class="identifier">a</span> <span class="keyword">class</span>
                    <span class="identifier">that</span> <span class="identifier">locks</span>
                    <span class="identifier">the</span> <span class="identifier">mutex</span>
                    <span class="identifier">on</span> <span class="identifier">construction</span>
                    <span class="keyword">and</span> <span class="identifier">unlocks</span>
                    <span class="identifier">it</span> <span class="identifier">on</span>
                    <span class="identifier">destruction</span> <span class="keyword">and</span>
                    <span class="identifier">provides</span> <span class="identifier">implicit</span>
                    <span class="identifier">conversion</span> <span class="identifier">to</span>
                    <span class="identifier">a</span> <span class="identifier">reference</span>
                    <span class="identifier">to</span> <span class="identifier">the</span>
                    <span class="keyword">protected</span> <span class="identifier">value</span><span class="special">.</span></code>
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Nothing.
                  </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.deref_const"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronized_value.deref_const" title="operator*() const"><code class="computeroutput"><span class="keyword">operator</span><span class="special">*()</span>
            <span class="keyword">const</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">const_deref_value</span> <span class="keyword">operator</span><span class="special">*()</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Return:</span></dt>
<dd><p>
                    <code class="computeroutput"><span class="identifier">A</span> <span class="identifier">an</span>
                    <span class="identifier">instance</span> <span class="identifier">of</span>
                    <span class="identifier">a</span> <span class="keyword">class</span>
                    <span class="identifier">that</span> <span class="identifier">locks</span>
                    <span class="identifier">the</span> <span class="identifier">mutex</span>
                    <span class="identifier">on</span> <span class="identifier">construction</span>
                    <span class="keyword">and</span> <span class="identifier">unlocks</span>
                    <span class="identifier">it</span> <span class="identifier">on</span>
                    <span class="identifier">destruction</span> <span class="keyword">and</span>
                    <span class="identifier">provides</span> <span class="identifier">implicit</span>
                    <span class="identifier">conversion</span> <span class="identifier">to</span>
                    <span class="identifier">a</span> <span class="identifier">constant</span>
                    <span class="identifier">reference</span> <span class="identifier">to</span>
                    <span class="identifier">the</span> <span class="keyword">protected</span>
                    <span class="identifier">value</span><span class="special">.</span></code>
                  </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                    Nothing.
                  </p></dd>
</dl>
</div>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronize"></a><a class="link" href="sds.html#thread.sds.synchronized_valuesxxx.synchronized_value_ref.synchronize" title="Non-Member Function synchronize">Non-Member
          Function <code class="computeroutput"><span class="identifier">synchronize</span></code></a>
</h5></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">synchronized_value</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
<span class="preprocessor">#if</span> <span class="special">!</span> <span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_THREAD_NO_SYNCHRONIZE</span><span class="special">)</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="special">...</span><span class="identifier">SV</span><span class="special">&gt;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">tuple</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">synchronized_value_strict_lock_ptr</span><span class="special">&lt;</span><span class="identifier">SV</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="special">...&gt;</span> <span class="identifier">synchronize</span><span class="special">(</span><span class="identifier">SV</span><span class="special">&amp;</span> <span class="special">...</span><span class="identifier">sv</span><span class="special">);</span>
<span class="preprocessor">#endif</span>
<span class="special">}</span>
</pre>
</div>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="thread.sds.synchronized_queues"></a><a class="link" href="sds.html#thread.sds.synchronized_queues" title="Synchronized Queues -- EXPERIMENTAL">Synchronized Queues --
      EXPERIMENTAL</a>
</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_queues.introduction">Introduction</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_queues.tutorial">Tutorial</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_queues.examples">Examples</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_queues.ref">Reference</a></span></dt>
</dl></div>
<div class="warning"><table border="0" summary="Warning">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Warning]" src="../../../doc/src/images/warning.png"></td>
<th align="left">Warning</th>
</tr>
<tr><td align="left" valign="top"><p>
          These features are experimental and subject to change in future versions.
          There are not too much tests yet, so it is possible that you can find out
          some trivial bugs :(
        </p></td></tr>
</table></div>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>
          These features are based on the <a href="http://www.open-std.org/JTC1/SC22/WG21/docs/papers/2013/n3533.html" target="_top"><span class="bold"><strong>N3533 - C++ Concurrent Queues</strong></span></a> C++1y proposal
          from Lawrence Crowl and Chris Mysen and <a href="http://www.manning.com/williams/" target="_top"><span class="bold"><strong>C++ Concurrency in Action</strong></span></a> from Anthony Williams.
        </p></td></tr>
</table></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="thread.sds.synchronized_queues.introduction"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.introduction" title="Introduction">Introduction</a>
</h4></div></div></div>
<p>
          Queues provide a mechanism for communicating data between components of
          a system.
        </p>
<p>
          The existing deque in the standard library is an inherently sequential
          data structure. Its reference-returning element access operations cannot
          synchronize access to those elements with other queue operations. So, concurrent
          pushes and pops on queues require a different interface to the queue structure.
        </p>
<p>
          Moreover, concurrency adds a new dimension for performance and semantics.
          Different queue implementation must trade off uncontended operation cost,
          contended operation cost, and element order guarantees. Some of these trade-offs
          will necessarily result in semantics weaker than a serial queue.
        </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="thread.sds.synchronized_queues.tutorial"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.tutorial" title="Tutorial">Tutorial</a>
</h4></div></div></div>
<p>
          Concurrent queues are a well know mechanism for communicating data between
          different threads.
        </p>
<p>
          Concurrent queues have inherently copy/move semantics for the data handling
          operation. Reference-returning interfaces are forbidden as multiple access
          to these references can not be thread-safe.
        </p>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="thread.sds.synchronized_queues.examples"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.examples" title="Examples">Examples</a>
</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="thread.sds.synchronized_queues.ref"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref" title="Reference">Reference</a>
</h4></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req">Synchronized
          Queue Model</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_queues.ref.queue_op_status">Queue
          Operation Status</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_queues.ref.queue_base">Queue
          Base</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_queues.ref.queue_adaptor">Queue
          Adaptor</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_queues.ref.queue_views">Queue
          Views</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_queues.ref.sync_bounded_queue_ref">Synchronized
          Bounded Queue</a></span></dt>
<dt><span class="section"><a href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_ref">Synchronized
          Unbounded Queue</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req" title="Synchronized Queue Model">Synchronized
          Queue Model</a>
</h5></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.bounded_unbounded"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.bounded_unbounded" title="Bounded-Unbounded Queues">Bounded-Unbounded
            Queues</a>
</h6></div></div></div>
<p>
              One of the major features of a concurrent queue is whether it has a
              bounded-unbounded capacity.
            </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.locking"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.locking" title="Locking/Lock-free Queues">Locking/Lock-free
            Queues</a>
</h6></div></div></div>
<p>
              Locking queues can by nature block waiting for the queue to be non-empty
              or non-full.
            </p>
<p>
              Lock-free queues will have some trouble waiting for the queue to be
              non-empty or non-full queues. These queues can not define operations
              such as push (and pull for bounded queues). That is, it could have
              blocking operations (presumably emulated with busy wait) but not waiting
              operations.
            </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.closed"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.closed" title="Closed Queue">Closed
            Queue</a>
</h6></div></div></div>
<p>
              Threads using a queue for communication need some mechanism to signal
              when the queue is no longer needed. The usual approach is add an additional
              out-of-band signal. However, this approach suffers from the flaw that
              threads waiting on either full or empty queues need to be woken up
              when the queue is no longer needed. Rather than require an out-of-band
              signal, we chose to directly support such a signal in the queue itself,
              which considerably simplifies coding.
            </p>
<p>
              To achieve this signal, a thread may close a queue. Once closed, no
              new elements may be pushed onto the queue. Push operations on a closed
              queue will either return queue_op_status::closed (when they have a
              queue_op_status return type), set the closed parameter if it has one
              or throw sync_queue::closed (when they do not). Elements already on
              the queue may be pulled off. When a queue is empty and closed, pull
              operations will either return queue_op_status::closed (when they have
              a status return), set the closed parameter if it has one or throw sync_queue::closed
              (when they do not).
            </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.exception"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.exception" title="Concurrent Queues Throw specification">Concurrent
            Queues Throw specification</a>
</h6></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.exception.locking"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.exception.locking" title="Locking">Locking</a>
</h6></div></div></div>
<p>
                All the functions are defined as if we had in addition to its specific
                Throw specification the following:
              </p>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      Any exception thrown by the internal locking.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.exception.bad_alloc"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.exception.bad_alloc" title="Allocation">Allocation</a>
</h6></div></div></div>
<p>
                All the functions that allocate a resource are defined as if we had
                in addition to its specific Throw specification the following:
              </p>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      Any exception due to allocation errors.
                    </p></dd>
</dl>
</div>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.BasicConcurrentQueue"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.BasicConcurrentQueue" title="Basic Concurrent Queue Operations">Basic
            Concurrent Queue Operations</a>
</h6></div></div></div>
<p>
              The essential solution to the problem of concurrent queuing is to shift
              to value-based operations, rather than reference-based operations.
            </p>
<p>
              The BasicConcurrentQueue concept models the basic operations of a concurrent
              queue.
            </p>
<p>
              A type <code class="computeroutput"><span class="identifier">Q</span></code> meets the
              BasicConcurrentQueue requirements if the following expressions are
              well-formed and have the specified semantics
            </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                  Q::value_type
                </li>
<li class="listitem">
                  Q::size_type
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">q</span><span class="special">.</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">e</span><span class="special">);</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">q</span><span class="special">.</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">rve</span><span class="special">);</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">q</span><span class="special">.</span><span class="identifier">pull_front</span><span class="special">(</span><span class="identifier">lre</span><span class="special">);</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">lre</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">pull_front</span><span class="special">();</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">b</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">empty</span><span class="special">();</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">u</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">size</span><span class="special">();</span></code>
                </li>
</ul></div>
<p>
              where
            </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">q</span></code> denotes a value
                  of type <code class="computeroutput"><span class="identifier">Q</span></code>,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">e</span></code> denotes a value
                  of type Q::value_type,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">u</span></code> denotes a value
                  of type Q::size_type,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">lve</span></code> denotes an
                  lvalue reference of type Q::value_type,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">rve</span></code> denotes an
                  rvalue reference of type Q::value_type:
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">qs</span></code> denotes a variable
                  of of type <code class="computeroutput"><span class="identifier">queue_op_status</span></code>,
                </li>
</ul></div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.BasicConcurrentQueue.push_back"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.BasicConcurrentQueue.push_back" title="q.push_back(e);"><code class="computeroutput"><span class="identifier">q</span><span class="special">.</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">e</span><span class="special">);</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      Waits until the queue is not full (for bounded queues) and
                      then push back <code class="computeroutput"><span class="identifier">e</span></code>
                      to the queue copying it (this could need an allocation for
                      unbounded queues).
                    </p></dd>
<dt><span class="term">Synchronization:</span></dt>
<dd><p>
                      Prior pull-like operations on the same object synchronizes
                      with this operation.
                    </p></dd>
<dt><span class="term">Postcondition:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="special">!</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">empty</span><span class="special">()</span></code>.
                    </p></dd>
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="keyword">void</span></code>.
                    </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      If the queue was closed, throws sync_queue_is_closed. Any exception
                      thrown by the copy of <code class="computeroutput"><span class="identifier">e</span></code>.
                    </p></dd>
<dt><span class="term">Exception safety:</span></dt>
<dd><p>
                      If an exception is thrown then the queue state is unmodified.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.BasicConcurrentQueue.push_back_m"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.BasicConcurrentQueue.push_back_m" title="q.push_back(rve);"><code class="computeroutput"><span class="identifier">q</span><span class="special">.</span><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">rve</span><span class="special">);</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      Waits until the queue is not full (for bounded queues) and
                      then push <code class="computeroutput"><span class="identifier">e</span></code>
                      to the queue moving it back in the queue (this could need an
                      allocation for unbounded queues).
                    </p></dd>
<dt><span class="term">Synchronization:</span></dt>
<dd><p>
                      Prior pull-like operations on the same object synchronizes
                      with this operation.
                    </p></dd>
<dt><span class="term">Postcondition:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="special">!</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">empty</span><span class="special">()</span></code>.
                    </p></dd>
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="keyword">void</span></code>.
                    </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      If the queue is closed, throws sync_queue_is_closed. Any exception
                      thrown by the copy of <code class="computeroutput"><span class="identifier">e</span></code>.
                    </p></dd>
<dt><span class="term">Exception safety:</span></dt>
<dd><p>
                      If an exception is thrown then the queue state is unmodified.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.BasicConcurrentQueue.pull_front_lv"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.BasicConcurrentQueue.pull_front_lv" title="q.pull_front(lve)"><code class="computeroutput"><span class="identifier">q</span><span class="special">.</span><span class="identifier">pull_front</span><span class="special">(</span><span class="identifier">lve</span><span class="special">)</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      Waits until the queue is not empty and then pull_front the
                      element from the queue <code class="computeroutput"><span class="identifier">q</span></code>
                      and moves the pulled element into <code class="computeroutput"><span class="identifier">lve</span></code>
                      (this could need an allocation for unbounded queues).
                    </p></dd>
<dt><span class="term">Synchronization:</span></dt>
<dd><p>
                      Prior pull-like operations on the same object synchronizes
                      with this operation.
                    </p></dd>
<dt><span class="term">Postcondition:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="special">!</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">full</span><span class="special">()</span></code>.
                    </p></dd>
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="keyword">void</span></code>.
                    </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      Any exception thrown by the move of <code class="computeroutput"><span class="identifier">e</span></code>.
                    </p></dd>
<dt><span class="term">Exception safety:</span></dt>
<dd><p>
                      If an exception is thrown then the queue state is unmodified.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.BasicConcurrentQueue.pull_front"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.BasicConcurrentQueue.pull_front" title="e = q.pull_front()"><code class="computeroutput"><span class="identifier">e</span> <span class="special">=</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">pull_front</span><span class="special">()</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Requires:</span></dt>
<dd><p>
                      Q::value_type is no throw move constructible. This is needed
                      to ensure the exception safety.
                    </p></dd>
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      Waits until the queue is not empty and not closed. If the queue
                      is empty and closed throws sync_queue_is_closed. Otherwise
                      pull the element from the queue <code class="computeroutput"><span class="identifier">q</span></code>
                      and moves the pulled element.
                    </p></dd>
<dt><span class="term">Synchronization:</span></dt>
<dd><p>
                      Prior pull-like operations on the same object synchronizes
                      with this operation.
                    </p></dd>
<dt><span class="term">Postcondition:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="special">!</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">full</span><span class="special">()</span></code>.
                    </p></dd>
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="identifier">Q</span><span class="special">::</span><span class="identifier">value_type</span></code>.
                    </p></dd>
<dt><span class="term">Return:</span></dt>
<dd><p>
                      The pulled element.
                    </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      Any exception thrown by the copy of <code class="computeroutput"><span class="identifier">e</span></code>.
                    </p></dd>
<dt><span class="term">Exception safety:</span></dt>
<dd><p>
                      If an exception is thrown then the queue state is unmodified.
                    </p></dd>
</dl>
</div>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.non_waiting"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.non_waiting" title="Non-waiting Concurrent Queue Operations">Non-waiting
            Concurrent Queue Operations</a>
</h6></div></div></div>
<p>
              The ConcurrentQueue concept models a queue with Non-waiting operations.
            </p>
<p>
              A type <code class="computeroutput"><span class="identifier">Q</span></code> meets the
              ConcurrentQueue requirements if is a model of a BasicConcurrentQueue
              and the following expressions are well-formed and have the specified
              semantics
            </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">try_push_back</span><span class="special">(</span><span class="identifier">e</span><span class="special">);</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">try_push_back</span><span class="special">(</span><span class="identifier">rve</span><span class="special">);</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">try_pull_front</span><span class="special">(</span><span class="identifier">lre</span><span class="special">);</span></code>
                </li>
</ul></div>
<p>
              where
            </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">q</span></code> denotes a value
                  of type <code class="computeroutput"><span class="identifier">Q</span></code>,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">e</span></code> denotes a value
                  of type <code class="computeroutput"><span class="identifier">Q</span><span class="special">::</span><span class="identifier">value_type</span></code>,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">s</span></code> denotes a value
                  of type <code class="computeroutput"><span class="identifier">queue_status</span></code>,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">u</span></code> denotes a value
                  of type <code class="computeroutput"><span class="identifier">Q</span><span class="special">::</span><span class="identifier">size_type</span></code>,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">lve</span></code> denotes an
                  lvalue reference of type Q::value_type,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">rve</span></code> denotes an
                  rvalue reference of type Q::value_type:
                </li>
</ul></div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.non_waiting.try_push_back"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.non_waiting.try_push_back" title="s = q.try_push_back(e);"><code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">try_push_back</span><span class="special">(</span><span class="identifier">e</span><span class="special">);</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      If the queue <code class="computeroutput"><span class="identifier">q</span></code>
                      is not full and not closed, push back the <code class="computeroutput"><span class="identifier">e</span></code>
                      to the queue copying it.
                    </p></dd>
<dt><span class="term">Synchronization:</span></dt>
<dd><p>
                      Prior pull-like operations on the same object synchronizes
                      with this operation when the operation succeeds.
                    </p></dd>
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="identifier">queue_op_status</span></code>.
                    </p></dd>
<dt><span class="term">Return:</span></dt>
<dd>
<p>
                      - If the queue is closed, returns <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">closed</span></code>,
                    </p>
<p>
                      - otherwise if the queue <code class="computeroutput"><span class="identifier">q</span></code>
                      is full return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">full</span></code>,
                    </p>
<p>
                      - otherwise return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">success</span></code>;
                    </p>
</dd>
<dt><span class="term">Postcondition:</span></dt>
<dd><p>
                      If the call returns <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">success</span></code>,
                      <code class="computeroutput"><span class="special">!</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">empty</span><span class="special">()</span></code>.
                    </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      If the queue is closed, throws sync_queue_is_closed. Any exception
                      thrown by the copy of <code class="computeroutput"><span class="identifier">e</span></code>.
                    </p></dd>
<dt><span class="term">Exception safety:</span></dt>
<dd><p>
                      If an exception is thrown then the queue state is unmodified.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.non_waiting.try_push_back_m"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.non_waiting.try_push_back_m" title="s = q.try_push_back(rve);"><code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">try_push_back</span><span class="special">(</span><span class="identifier">rve</span><span class="special">);</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      If the queue <code class="computeroutput"><span class="identifier">q</span></code>
                      is not full and not closed, push back the <code class="computeroutput"><span class="identifier">e</span></code>
                      onto the queue moving it.
                    </p></dd>
<dt><span class="term">Synchronization:</span></dt>
<dd><p>
                      Prior pull-like operations on the same object synchronizes
                      with this operation.
                    </p></dd>
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="identifier">queue_op_status</span></code>.
                    </p></dd>
<dt><span class="term">Return:</span></dt>
<dd>
<p>
                      - If the queue is closed, returns <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">closed</span></code>,
                    </p>
<p>
                      - otherwise if the queue <code class="computeroutput"><span class="identifier">q</span></code>
                      is full return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">full</span></code>,
                    </p>
<p>
                      - otherwise return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">success</span></code>;
                    </p>
</dd>
<dt><span class="term">Postcondition:</span></dt>
<dd><p>
                      If the call returns <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">success</span></code>,
                      <code class="computeroutput"><span class="special">!</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">empty</span><span class="special">()</span></code>.
                    </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      Any exception thrown by the copy of <code class="computeroutput"><span class="identifier">e</span></code>.
                    </p></dd>
<dt><span class="term">Exception safety:</span></dt>
<dd><p>
                      If an exception is thrown then the queue state is unmodified.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.non_waiting.try_pull_front_lv"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.non_waiting.try_pull_front_lv" title="s = q.try_pull_front(lve)"><code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">try_pull_front</span><span class="special">(</span><span class="identifier">lve</span><span class="special">)</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      If the queue is not empty pulls the element from the queue
                      <code class="computeroutput"><span class="identifier">q</span></code> and moves
                      the pulled element into <code class="computeroutput"><span class="identifier">lve</span></code>
                      (this could need an allocation for unbounded queues).
                    </p></dd>
<dt><span class="term">Synchronization:</span></dt>
<dd><p>
                      Prior pull-like operations on the same object synchronizes
                      with this operation.
                    </p></dd>
<dt><span class="term">Postcondition:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="special">!</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">full</span><span class="special">()</span></code>.
                    </p></dd>
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="keyword">bool</span></code>.
                    </p></dd>
<dt><span class="term">Return:</span></dt>
<dd>
<p>
                      - If the queue <code class="computeroutput"><span class="identifier">q</span></code>
                      is empty return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">empty</span></code>,
                    </p>
<p>
                      - otherwise return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">success</span></code>;
                    </p>
</dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      Any exception thrown by the move of <code class="computeroutput"><span class="identifier">e</span></code>.
                    </p></dd>
<dt><span class="term">Exception safety:</span></dt>
<dd><p>
                      If an exception is thrown then the queue state is unmodified.
                    </p></dd>
</dl>
</div>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.non_blocking"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.non_blocking" title="Non-blocking Concurrent Queue Operations">Non-blocking
            Concurrent Queue Operations</a>
</h6></div></div></div>
<p>
              For cases when blocking for mutual exclusion is undesirable, we have
              non-blocking operations. The interface is the same as the try operations
              but is allowed to also return queue_op_status::busy in case the operation
              is unable to complete without blocking.
            </p>
<p>
              Non-blocking operations are provided only for lock based queues
            </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">nonblocking_push_back</span><span class="special">(</span><span class="identifier">nb</span><span class="special">,</span>
                  <span class="identifier">e</span><span class="special">);</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">nonblocking_push_back</span><span class="special">(</span><span class="identifier">nb</span><span class="special">,</span>
                  <span class="identifier">rve</span><span class="special">);</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">nonblocking_pull_front</span><span class="special">(</span><span class="identifier">nb</span><span class="special">,</span>
                  <span class="identifier">lre</span><span class="special">);</span></code>
                </li>
</ul></div>
<p>
              where
            </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">q</span></code> denotes a value
                  of type <code class="computeroutput"><span class="identifier">Q</span></code>,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">e</span></code> denotes a value
                  of type Q::value_type,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">s</span></code> denotes a value
                  of type <code class="computeroutput"><span class="identifier">queue_status</span></code>,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">lve</span></code> denotes an
                  lvalue reference of type Q::value_type,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">rve</span></code> denotes an
                  rvalue reference of type Q::value_type:
                </li>
</ul></div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.non_blocking.nonblocking_push_back"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.non_blocking.nonblocking_push_back" title="s = q.nonblocking_push_back(e);"><code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">nonblocking_push_back</span><span class="special">(</span><span class="identifier">e</span><span class="special">);</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      If the queue <code class="computeroutput"><span class="identifier">q</span></code>
                      is not full and not closed, push back the <code class="computeroutput"><span class="identifier">e</span></code>
                      to the queue copying it.
                    </p></dd>
<dt><span class="term">Synchronization:</span></dt>
<dd><p>
                      Prior pull-like operations on the same object synchronizes
                      with this operation when the operation succeeds.
                    </p></dd>
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="identifier">queue_op_status</span></code>.
                    </p></dd>
<dt><span class="term">Return:</span></dt>
<dd>
<p>
                      - If the operation would block, return queue_op_status::busy,
                    </p>
<p>
                      - otherwise, if the queue is closed, return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">closed</span></code>,
                    </p>
<p>
                      - otherwise, if the queue <code class="computeroutput"><span class="identifier">q</span></code>
                      is full return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">full</span></code>,
                    </p>
<p>
                      - otherwise return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">success</span></code>;
                    </p>
</dd>
<dt><span class="term">Postcondition:</span></dt>
<dd><p>
                      If the call returns <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">success</span></code>,
                      <code class="computeroutput"><span class="special">!</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">empty</span><span class="special">()</span></code>.
                    </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      If the queue is closed, throws sync_queue_is_closed. Any exception
                      thrown by the copy of <code class="computeroutput"><span class="identifier">e</span></code>.
                    </p></dd>
<dt><span class="term">Exception safety:</span></dt>
<dd><p>
                      If an exception is thrown then the queue state is unmodified.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.non_blocking.nonblocking_push_back_m"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.non_blocking.nonblocking_push_back_m" title="s = q.nonblocking_push_back(rve);"><code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">nonblocking_push_back</span><span class="special">(</span><span class="identifier">rve</span><span class="special">);</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      If the queue <code class="computeroutput"><span class="identifier">q</span></code>
                      is not full and not closed, push back the <code class="computeroutput"><span class="identifier">e</span></code>
                      onto the queue moving it.
                    </p></dd>
<dt><span class="term">Synchronization:</span></dt>
<dd><p>
                      Prior pull-like operations on the same object synchronizes
                      with this operation.
                    </p></dd>
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="identifier">queue_op_status</span></code>.
                    </p></dd>
<dt><span class="term">Return:</span></dt>
<dd>
<p>
                      - If the operation would block, return queue_op_status::busy,
                    </p>
<p>
                      - otherwise if the queue is closed, returns <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">closed</span></code>,
                    </p>
<p>
                      - otherwise if the queue <code class="computeroutput"><span class="identifier">q</span></code>
                      is full return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">full</span></code>,
                    </p>
<p>
                      - otherwise return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">success</span></code>;
                    </p>
</dd>
<dt><span class="term">Postcondition:</span></dt>
<dd><p>
                      If the call returns <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">success</span></code>,
                      <code class="computeroutput"><span class="special">!</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">empty</span><span class="special">()</span></code>.
                    </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      Any exception thrown by the copy of <code class="computeroutput"><span class="identifier">e</span></code>.
                    </p></dd>
<dt><span class="term">Exception safety:</span></dt>
<dd><p>
                      If an exception is thrown then the queue state is unmodified.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.non_blocking.nonblocking_pull_front_lv"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.non_blocking.nonblocking_pull_front_lv" title="s = q.nonblocking_pull_front(lve)"><code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">nonblocking_pull_front</span><span class="special">(</span><span class="identifier">lve</span><span class="special">)</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      If the queue is not empty pulls the element from the queue
                      <code class="computeroutput"><span class="identifier">q</span></code> and moves
                      the pulled element into <code class="computeroutput"><span class="identifier">lve</span></code>
                      (this could need an allocation for unbounded queues).
                    </p></dd>
<dt><span class="term">Synchronization:</span></dt>
<dd><p>
                      Prior pull-like operations on the same object synchronizes
                      with this operation.
                    </p></dd>
<dt><span class="term">Postcondition:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="special">!</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">full</span><span class="special">()</span></code>.
                    </p></dd>
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="keyword">bool</span></code>.
                    </p></dd>
<dt><span class="term">Return:</span></dt>
<dd>
<p>
                      - If the operation would block, return queue_op_status::busy,
                    </p>
<p>
                      - otherwise if the queue <code class="computeroutput"><span class="identifier">q</span></code>
                      is empty return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">empty</span></code>,
                    </p>
<p>
                      - otherwise return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">success</span></code>;
                    </p>
</dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      Any exception thrown by the move of <code class="computeroutput"><span class="identifier">e</span></code>.
                    </p></dd>
<dt><span class="term">Exception safety:</span></dt>
<dd><p>
                      If an exception is thrown then the queue state is unmodified.
                    </p></dd>
</dl>
</div>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.bounded"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.bounded" title="Bounded Concurrent Queue Operations">Bounded
            Concurrent Queue Operations</a>
</h6></div></div></div>
<p>
              Bounded queues add the following valid expressions
            </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">Q</span> <span class="identifier">q</span><span class="special">(</span><span class="identifier">u</span><span class="special">);</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">b</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">full</span><span class="special">();</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">u</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">capacity</span><span class="special">();</span></code>
                </li>
</ul></div>
<p>
              where
            </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">q</span></code> denotes a value
                  of type <code class="computeroutput"><span class="identifier">Q</span></code>,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">b</span></code> denotes a value
                  of type <code class="computeroutput"><span class="keyword">bool</span></code>,
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">u</span></code> denotes a value
                  of type <code class="computeroutput"><span class="identifier">Q</span><span class="special">::</span><span class="identifier">size_type</span></code>,
                </li>
</ul></div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.bounded.full"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.bounded.full" title="b = q.full();"><code class="computeroutput"><span class="identifier">b</span> <span class="special">=</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">full</span><span class="special">();</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="keyword">bool</span></code>.
                    </p></dd>
<dt><span class="term">Return:</span></dt>
<dd><p>
                      Return <code class="computeroutput"><span class="keyword">true</span></code> iff
                      the queue is full.
                    </p></dd>
<dt><span class="term">Remark:</span></dt>
<dd><p>
                      Not all queues will have a full state, and these would always
                      return false if the function is provided.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.bounded.capacity"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.bounded.capacity" title="b = q.capacity();"><code class="computeroutput"><span class="identifier">b</span> <span class="special">=</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">capacity</span><span class="special">();</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="identifier">Q</span><span class="special">::</span><span class="identifier">size_type</span></code>.
                    </p></dd>
<dt><span class="term">Return:</span></dt>
<dd><p>
                      Return the capacity of queue.
                    </p></dd>
</dl>
</div>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.closed_op"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.closed_op" title="Closed Concurrent Queue Operations">Closed
            Concurrent Queue Operations</a>
</h6></div></div></div>
<p>
              Closed queues add the following valid expressions
            </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">q</span><span class="special">.</span><span class="identifier">close</span><span class="special">();</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">b</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">closed</span><span class="special">();</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">wait_push_back</span><span class="special">(</span><span class="identifier">e</span><span class="special">);</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">wait_push_back</span><span class="special">(</span><span class="identifier">rve</span><span class="special">);</span></code>
                </li>
<li class="listitem">
                  <code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span>
                  <span class="identifier">q</span><span class="special">.</span><span class="identifier">wait_pull_front</span><span class="special">(</span><span class="identifier">lre</span><span class="special">);</span></code>
                </li>
</ul></div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.closed_op.wait_push_back"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.closed_op.wait_push_back" title="q.close();"><code class="computeroutput"><span class="identifier">q</span><span class="special">.</span><span class="identifier">close</span><span class="special">();</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      Close the queue.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.closed_op.wait_push_back0"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.closed_op.wait_push_back0" title="b = q.closed();"><code class="computeroutput"><span class="identifier">b</span> <span class="special">=</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">closed</span><span class="special">();</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="keyword">bool</span></code>.
                    </p></dd>
<dt><span class="term">Return:</span></dt>
<dd><p>
                      Return <code class="computeroutput"><span class="keyword">true</span></code> iff
                      the queue is closed.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.closed_op.wait_push_back1"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.closed_op.wait_push_back1" title="s = q.wait_push_back(e);"><code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">wait_push_back</span><span class="special">(</span><span class="identifier">e</span><span class="special">);</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      Waits until the queue is not full (for bounded queues) and
                      then push back <code class="computeroutput"><span class="identifier">e</span></code>
                      to the queue copying it (this could need an allocation for
                      unbounded queues).
                    </p></dd>
<dt><span class="term">Synchronization:</span></dt>
<dd><p>
                      Prior pull-like operations on the same object synchronizes
                      with this operation.
                    </p></dd>
<dt><span class="term">Postcondition:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="special">!</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">empty</span><span class="special">()</span></code>.
                    </p></dd>
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="identifier">queue_op_status</span></code>.
                    </p></dd>
<dt><span class="term">Return:</span></dt>
<dd>
<p>
                      - If the queue is closed return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">closed</span></code>,
                    </p>
<p>
                      - otherwise, return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">success</span></code>
                      if no exception is thrown.
                    </p>
</dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      Any exception thrown by the copy of <code class="computeroutput"><span class="identifier">e</span></code>.
                    </p></dd>
<dt><span class="term">Exception safety:</span></dt>
<dd><p>
                      If an exception is thrown then the queue state is unmodified.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.closed_op.wait_push_back_m"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.closed_op.wait_push_back_m" title="s = q.wait_push_back(rve);"><code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">wait_push_back</span><span class="special">(</span><span class="identifier">rve</span><span class="special">);</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      Waits until the queue is not full (for bounded queues) and
                      then push <code class="computeroutput"><span class="identifier">e</span></code>
                      to the queue moving it back in the queue (this could need an
                      allocation for unbounded queues).
                    </p></dd>
<dt><span class="term">Synchronization:</span></dt>
<dd><p>
                      Prior pull-like operations on the same object synchronizes
                      with this operation.
                    </p></dd>
<dt><span class="term">Postcondition:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="special">!</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">empty</span><span class="special">()</span></code>.
                    </p></dd>
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="identifier">queue_op_status</span></code>.
                    </p></dd>
<dt><span class="term">Return:</span></dt>
<dd>
<p>
                      - If the queue is closed return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">closed</span></code>,
                    </p>
<p>
                      - otherwise, return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">success</span></code>
                      if no exception is thrown.
                    </p>
<p>
                      .
                    </p>
</dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      Any exception thrown by the copy of <code class="computeroutput"><span class="identifier">e</span></code>.
                    </p></dd>
<dt><span class="term">Exception safety:</span></dt>
<dd><p>
                      If an exception is thrown then the queue state is unmodified.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_req.closed_op.wait_pull_front_lv"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_req.closed_op.wait_pull_front_lv" title="s = q.wait_pull_front(lve)"><code class="computeroutput"><span class="identifier">s</span> <span class="special">=</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">wait_pull_front</span><span class="special">(</span><span class="identifier">lve</span><span class="special">)</span></code></a>
</h6></div></div></div>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      if the queue is not empty and not closed, waits until the queue
                      is not empty and then pull_front the element from the queue
                      <code class="computeroutput"><span class="identifier">q</span></code> and moves
                      the pulled element into <code class="computeroutput"><span class="identifier">lve</span></code>.
                    </p></dd>
<dt><span class="term">Synchronization:</span></dt>
<dd><p>
                      Prior pull-like operations on the same object synchronizes
                      with this operation.
                    </p></dd>
<dt><span class="term">Postcondition:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="special">!</span> <span class="identifier">q</span><span class="special">.</span><span class="identifier">full</span><span class="special">()</span></code>.
                    </p></dd>
<dt><span class="term">Return type:</span></dt>
<dd><p>
                      <code class="computeroutput"><span class="identifier">queue_op_status</span></code>.
                    </p></dd>
<dt><span class="term">Return:</span></dt>
<dd>
<p>
                      - If the queue is empty and closed, return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">closed</span></code>,
                    </p>
<p>
                      - otherwise, return <code class="computeroutput"><span class="identifier">queue_op_status</span><span class="special">::</span><span class="identifier">success</span></code>
                      if no exception is thrown.
                    </p>
</dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      Any exception thrown by the move of <code class="computeroutput"><span class="identifier">e</span></code>.
                    </p></dd>
<dt><span class="term">Exception safety:</span></dt>
<dd><p>
                      If an exception is thrown then the queue state is unmodified.
                    </p></dd>
</dl>
</div>
</div>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="thread.sds.synchronized_queues.ref.queue_op_status"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.queue_op_status" title="Queue Operation Status">Queue
          Operation Status</a>
</h5></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">concurrent_queues</span><span class="special">/</span><span class="identifier">queue_op_status</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
   <span class="keyword">enum</span> <span class="keyword">class</span> <span class="identifier">queue_op_status</span> <span class="special">{</span> <span class="identifier">success</span> <span class="special">=</span> <span class="number">0</span><span class="special">,</span> <span class="identifier">empty</span><span class="special">,</span> <span class="identifier">full</span><span class="special">,</span> <span class="identifier">closed</span><span class="special">,</span> <span class="identifier">busy</span> <span class="special">}</span>
<span class="special">}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="thread.sds.synchronized_queues.ref.queue_base"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.queue_base" title="Queue Base">Queue
          Base</a>
</h5></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">concurrent_queues</span><span class="special">/</span><span class="identifier">queue_base</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">SizeType</span><span class="special">=</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span><span class="special">&gt;</span>
  <span class="keyword">class</span> <span class="identifier">queue_base</span>
  <span class="special">{</span>
  <span class="keyword">public</span><span class="special">:</span>
    <span class="keyword">typedef</span> <span class="identifier">ValueType</span> <span class="identifier">value_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">SizeType</span> <span class="identifier">size_type</span><span class="special">;</span>

    <span class="comment">// Constructors/Assignment/Destructors</span>
    <span class="keyword">virtual</span> <span class="special">~</span><span class="identifier">queue_base</span><span class="special">()</span> <span class="special">{};</span>

    <span class="comment">// Observers</span>
    <span class="keyword">virtual</span> <span class="keyword">bool</span> <span class="identifier">empty</span><span class="special">()</span> <span class="keyword">const</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>
    <span class="keyword">virtual</span> <span class="keyword">bool</span> <span class="identifier">full</span><span class="special">()</span> <span class="keyword">const</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>
    <span class="keyword">virtual</span> <span class="identifier">size_type</span> <span class="identifier">size</span><span class="special">()</span> <span class="keyword">const</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>
    <span class="keyword">virtual</span> <span class="keyword">bool</span> <span class="identifier">closed</span><span class="special">()</span> <span class="keyword">const</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>

    <span class="comment">// Modifiers</span>
    <span class="keyword">virtual</span> <span class="keyword">void</span> <span class="identifier">close</span><span class="special">()</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>

    <span class="keyword">virtual</span> <span class="keyword">void</span> <span class="identifier">push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>
    <span class="keyword">virtual</span> <span class="keyword">void</span> <span class="identifier">push_back</span><span class="special">(</span><span class="identifier">BOOST_THREAD_RV_REF</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">)</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>

    <span class="keyword">virtual</span> <span class="keyword">void</span> <span class="identifier">pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;)</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>
    <span class="keyword">virtual</span> <span class="identifier">value_type</span> <span class="identifier">pull_front</span><span class="special">()</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>

    <span class="keyword">virtual</span> <span class="identifier">queue_op_status</span> <span class="identifier">try_push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>
    <span class="keyword">virtual</span> <span class="identifier">queue_op_status</span> <span class="identifier">try_push_back</span><span class="special">(</span><span class="identifier">BOOST_THREAD_RV_REF</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">)</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>
    <span class="keyword">virtual</span> <span class="identifier">queue_op_status</span> <span class="identifier">try_pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;)</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>

    <span class="keyword">virtual</span> <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>
    <span class="keyword">virtual</span> <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_push_back</span><span class="special">(</span><span class="identifier">BOOST_THREAD_RV_REF</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">)</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>
    <span class="keyword">virtual</span> <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;)</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>

    <span class="keyword">virtual</span> <span class="identifier">queue_op_status</span> <span class="identifier">wait_push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>
    <span class="keyword">virtual</span> <span class="identifier">queue_op_status</span> <span class="identifier">wait_push_back</span><span class="special">(</span><span class="identifier">BOOST_THREAD_RV_REF</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">)</span> <span class="identifier">x</span><span class="special">)</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>
    <span class="keyword">virtual</span> <span class="identifier">queue_op_status</span> <span class="identifier">wait_pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">elem</span><span class="special">)</span> <span class="special">=</span> <span class="number">0</span><span class="special">;</span>

  <span class="special">};</span>
<span class="special">}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="thread.sds.synchronized_queues.ref.queue_adaptor"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.queue_adaptor" title="Queue Adaptor">Queue
          Adaptor</a>
</h5></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">concurrent_queues</span><span class="special">/</span><span class="identifier">queue_adaptor</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Queue</span><span class="special">&gt;</span>
  <span class="keyword">class</span> <span class="identifier">queue_adaptor</span> <span class="special">:</span> <span class="keyword">public</span> <span class="identifier">queue_base</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Queue</span><span class="special">::</span><span class="identifier">value_type</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">Queue</span><span class="special">::</span><span class="identifier">size_type</span><span class="special">&gt;</span>
  <span class="special">{</span>
  <span class="keyword">public</span><span class="special">:</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">Queue</span><span class="special">::</span><span class="identifier">value_type</span> <span class="identifier">value_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">Queue</span><span class="special">::</span><span class="identifier">size_type</span> <span class="identifier">size_type</span><span class="special">;</span>

    <span class="comment">// Constructors/Assignment/Destructors</span>

    <span class="identifier">queue_adaptor</span><span class="special">();</span>

    <span class="comment">// Observers</span>
    <span class="keyword">bool</span> <span class="identifier">empty</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="keyword">bool</span> <span class="identifier">full</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="identifier">size_type</span> <span class="identifier">size</span><span class="special">()</span> <span class="keyword">const</span> <span class="special">{</span> <span class="keyword">return</span> <span class="identifier">queue</span><span class="special">.</span><span class="identifier">size</span><span class="special">();</span> <span class="special">}</span>
    <span class="keyword">bool</span> <span class="identifier">closed</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="comment">// Modifiers</span>
    <span class="keyword">void</span> <span class="identifier">close</span><span class="special">();</span>

    <span class="keyword">void</span> <span class="identifier">push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="keyword">void</span> <span class="identifier">push_back</span><span class="special">(</span><span class="identifier">BOOST_THREAD_RV_REF</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">)</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="keyword">void</span> <span class="identifier">pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">value_type</span> <span class="identifier">pull_front</span><span class="special">();</span>

    <span class="identifier">queue_op_status</span> <span class="identifier">try_push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">queue_op_status</span> <span class="identifier">try_push_back</span><span class="special">(</span><span class="identifier">BOOST_THREAD_RV_REF</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">)</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">queue_op_status</span> <span class="identifier">try_pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_push_back</span><span class="special">(</span><span class="identifier">BOOST_THREAD_RV_REF</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">)</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="identifier">queue_op_status</span> <span class="identifier">wait_push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">queue_op_status</span> <span class="identifier">wait_push_back</span><span class="special">(</span><span class="identifier">BOOST_THREAD_RV_REF</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">)</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">queue_op_status</span> <span class="identifier">wait_pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

  <span class="special">};</span>
<span class="special">}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="thread.sds.synchronized_queues.ref.queue_views"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.queue_views" title="Queue Views">Queue
          Views</a>
</h5></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">concurrent_queues</span><span class="special">/</span><span class="identifier">queue_views</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Queue</span><span class="special">&gt;</span>
  <span class="keyword">class</span> <span class="identifier">queue_back_view</span><span class="special">;</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Queue</span><span class="special">&gt;</span>
  <span class="keyword">class</span> <span class="identifier">queue_front_view</span>

  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
  <span class="keyword">using</span> <span class="identifier">queue_back</span> <span class="special">=</span> <span class="identifier">queue_back_view</span><span class="special">&lt;</span><span class="identifier">queue_base</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;&gt;;</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
  <span class="keyword">using</span> <span class="identifier">queue_front</span> <span class="special">=</span> <span class="identifier">queue_front_view</span><span class="special">&lt;</span><span class="identifier">queue_base</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;&gt;;</span>
<span class="special">}</span>
</pre>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.queue_views.queue_back_view"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.queue_views.queue_back_view" title="Class template queue_back_view&lt;&gt;">Class
            template <code class="computeroutput"><span class="identifier">queue_back_view</span><span class="special">&lt;&gt;</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Queue</span><span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">queue_back_view</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">Queue</span><span class="special">::</span><span class="identifier">value_type</span> <span class="identifier">value_type</span><span class="special">;</span>
  <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">Queue</span><span class="special">::</span><span class="identifier">size_type</span> <span class="identifier">size_type</span><span class="special">;</span>

  <span class="comment">// Constructors/Assignment/Destructors</span>
  <span class="identifier">queue_back_view</span><span class="special">(</span><span class="identifier">Queue</span><span class="special">&amp;</span> <span class="identifier">q</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="comment">// Observers</span>
  <span class="keyword">bool</span> <span class="identifier">empty</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">bool</span> <span class="identifier">full</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <span class="identifier">size</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">bool</span> <span class="identifier">closed</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

  <span class="comment">// Modifiers</span>
  <span class="keyword">void</span> <span class="identifier">close</span><span class="special">();</span>

  <span class="keyword">void</span> <span class="identifier">push</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
  <span class="keyword">void</span> <span class="identifier">push</span><span class="special">(</span><span class="identifier">BOOST_THREAD_RV_REF</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">)</span> <span class="identifier">x</span><span class="special">);</span>

  <span class="identifier">queue_op_status</span> <span class="identifier">try_push</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
  <span class="identifier">queue_op_status</span> <span class="identifier">try_push</span><span class="special">(</span><span class="identifier">BOOST_THREAD_RV_REF</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">)</span> <span class="identifier">x</span><span class="special">);</span>

  <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_push</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
  <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_push</span><span class="special">(</span><span class="identifier">BOOST_THREAD_RV_REF</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">)</span> <span class="identifier">x</span><span class="special">);</span>

  <span class="identifier">queue_op_status</span> <span class="identifier">wait_push</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
  <span class="identifier">queue_op_status</span> <span class="identifier">wait_push</span><span class="special">(</span><span class="identifier">BOOST_THREAD_RV_REF</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">)</span> <span class="identifier">x</span><span class="special">);</span>

<span class="special">};</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.queue_views.queue_front_view"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.queue_views.queue_front_view" title="Class template queue_front_view&lt;&gt;">Class
            template <code class="computeroutput"><span class="identifier">queue_front_view</span><span class="special">&lt;&gt;</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Queue</span><span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">queue_front_view</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">Queue</span><span class="special">::</span><span class="identifier">value_type</span> <span class="identifier">value_type</span><span class="special">;</span>
  <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">Queue</span><span class="special">::</span><span class="identifier">size_type</span> <span class="identifier">size_type</span><span class="special">;</span>

  <span class="comment">// Constructors/Assignment/Destructors</span>
  <span class="identifier">queue_front_view</span><span class="special">(</span><span class="identifier">Queue</span><span class="special">&amp;</span> <span class="identifier">q</span><span class="special">)</span> <span class="identifier">BOOST_NOEXCEPT</span><span class="special">;</span>

  <span class="comment">// Observers</span>
  <span class="keyword">bool</span> <span class="identifier">empty</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">bool</span> <span class="identifier">full</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <span class="identifier">size</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">bool</span> <span class="identifier">closed</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

  <span class="comment">// Modifiers</span>
  <span class="keyword">void</span> <span class="identifier">close</span><span class="special">();</span>

  <span class="keyword">void</span> <span class="identifier">pull</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
  <span class="identifier">value_type</span> <span class="identifier">pull</span><span class="special">();</span>

  <span class="identifier">queue_op_status</span> <span class="identifier">try_pull</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

  <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_pull</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

  <span class="identifier">queue_op_status</span> <span class="identifier">wait_pull</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>

<span class="special">};</span>
</pre>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_bounded_queue_ref"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_bounded_queue_ref" title="Synchronized Bounded Queue">Synchronized
          Bounded Queue</a>
</h5></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">sync_bounded_queue</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
  <span class="keyword">struct</span> <span class="identifier">sync_queue_is_closed</span> <span class="special">:</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">exception</span> <span class="special">{};</span>

  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="keyword">class</span> <span class="identifier">sync_bounded_queue</span><span class="special">;</span>

  <span class="comment">// Stream-like operators</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="identifier">sync_bounded_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="keyword">operator</span><span class="special">&lt;&lt;(</span><span class="identifier">sync_bounded_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="identifier">sbq</span><span class="special">,</span> <span class="identifier">ValueType</span><span class="special">&amp;&amp;</span> <span class="identifier">elem</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="identifier">sync_bounded_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="keyword">operator</span><span class="special">&lt;&lt;(</span><span class="identifier">sync_bounded_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="identifier">sbq</span><span class="special">,</span> <span class="identifier">ValueType</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">elem</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="identifier">sync_bounded_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="keyword">operator</span><span class="special">&gt;&gt;(</span><span class="identifier">sync_bounded_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="identifier">sbq</span><span class="special">,</span> <span class="identifier">ValueType</span> <span class="special">&amp;</span><span class="identifier">elem</span><span class="special">);</span>
<span class="special">}</span>
</pre>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_bounded_queue_ref.sync_queue_is_closed"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_bounded_queue_ref.sync_queue_is_closed" title="Class sync_queue_is_closed">Class
            <code class="computeroutput"><span class="identifier">sync_queue_is_closed</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">sync_bounded_queue</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
  <span class="keyword">struct</span> <span class="identifier">sync_queue_is_closed</span> <span class="special">:</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">exception</span> <span class="special">{};</span>
<span class="special">}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_bounded_queue_ref.sync_bounded_queue"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_bounded_queue_ref.sync_bounded_queue" title="Class template sync_bounded_queue&lt;&gt;">Class
            template <code class="computeroutput"><span class="identifier">sync_bounded_queue</span><span class="special">&lt;&gt;</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">sync_bounded_queue</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="keyword">class</span> <span class="identifier">sync_bounded_queue</span>
  <span class="special">{</span>
  <span class="keyword">public</span><span class="special">:</span>
    <span class="keyword">typedef</span> <span class="identifier">ValueType</span> <span class="identifier">value_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">size_t</span> <span class="identifier">size_type</span><span class="special">;</span>

    <span class="identifier">sync_bounded_queue</span><span class="special">(</span><span class="identifier">sync_bounded_queue</span> <span class="keyword">const</span><span class="special">&amp;)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>
    <span class="identifier">sync_bounded_queue</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="identifier">sync_bounded_queue</span> <span class="keyword">const</span><span class="special">&amp;)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>
    <span class="keyword">explicit</span> <span class="identifier">sync_bounded_queue</span><span class="special">(</span><span class="identifier">size_type</span> <span class="identifier">max_elems</span><span class="special">);</span>
    <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Range</span><span class="special">&gt;</span>
    <span class="identifier">sync_bounded_queue</span><span class="special">(</span><span class="identifier">size_type</span> <span class="identifier">max_elems</span><span class="special">,</span> <span class="identifier">Range</span> <span class="identifier">range</span><span class="special">);</span>
    <span class="special">~</span><span class="identifier">sync_bounded_queue</span><span class="special">();</span>

    <span class="comment">// Observers</span>
    <span class="keyword">bool</span> <span class="identifier">empty</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="keyword">bool</span> <span class="identifier">full</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="identifier">size_type</span> <span class="identifier">capacity</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="identifier">size_type</span> <span class="identifier">size</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="keyword">bool</span> <span class="identifier">closed</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="comment">// Modifiers</span>
    <span class="keyword">void</span> <span class="identifier">push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="keyword">void</span> <span class="identifier">push_back</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="identifier">queue_op_status</span> <span class="identifier">try_push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">queue_op_status</span> <span class="identifier">try_push_back</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;&amp;)</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_push_back</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="keyword">void</span> <span class="identifier">pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;);</span>
    <span class="identifier">value_type</span> <span class="identifier">pull_front</span><span class="special">();</span>

    <span class="identifier">queue_op_status</span> <span class="identifier">try_pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;);</span>
    <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;);</span>


    <span class="keyword">void</span> <span class="identifier">close</span><span class="special">();</span>
  <span class="special">};</span>
<span class="special">}</span>
</pre>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_bounded_queue_ref.sync_bounded_queue.constructor"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_bounded_queue_ref.sync_bounded_queue.constructor" title="Constructor sync_bounded_queue(size_type)">Constructor
              <code class="computeroutput"><span class="identifier">sync_bounded_queue</span><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="keyword">explicit</span> <span class="identifier">sync_bounded_queue</span><span class="special">(</span><span class="identifier">size_type</span> <span class="identifier">max_elems</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      Constructs a sync_bounded_queue with a maximum number of elements
                      given by <code class="computeroutput"><span class="identifier">max_elems</span></code>.
                    </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      any exception that can be throw because of resources unavailable.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_bounded_queue_ref.sync_bounded_queue.constructort"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_bounded_queue_ref.sync_bounded_queue.constructort" title="Template Constructor sync_bounded_queue(size_type, Range)">Template
              Constructor <code class="computeroutput"><span class="identifier">sync_bounded_queue</span><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">Range</span><span class="special">)</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Range</span><span class="special">&gt;</span>
<span class="identifier">sync_bounded_queue</span><span class="special">(</span><span class="identifier">size_type</span> <span class="identifier">max_elems</span><span class="special">,</span> <span class="identifier">Range</span> <span class="identifier">range</span><span class="special">);</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      Constructs a sync_bounded_queue with a maximum number of elements
                      given by <code class="computeroutput"><span class="identifier">max_elems</span></code>
                      and push back the elements of the range.
                    </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      any exception that can be throw because of resources unavailable.
                    </p></dd>
</dl>
</div>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_bounded_queue_ref.stream_out_operators"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_bounded_queue_ref.stream_out_operators" title="Non-Member Function operator&lt;&lt;()">Non-Member
            Function <code class="computeroutput"><span class="keyword">operator</span><span class="special">&lt;&lt;()</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">sync_bounded_queue</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="identifier">sync_bounded_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="keyword">operator</span><span class="special">&lt;&lt;(</span><span class="identifier">sync_bounded_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="identifier">sbq</span><span class="special">,</span> <span class="identifier">ValueType</span><span class="special">&amp;&amp;</span> <span class="identifier">elem</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="identifier">sync_bounded_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="keyword">operator</span><span class="special">&lt;&lt;(</span><span class="identifier">sync_bounded_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="identifier">sbq</span><span class="special">,</span> <span class="identifier">ValueType</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">elem</span><span class="special">);</span>
<span class="special">}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_bounded_queue_ref.stream_in_operators"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_bounded_queue_ref.stream_in_operators" title="Non-Member Function operator&gt;&gt;()">Non-Member
            Function <code class="computeroutput"><span class="keyword">operator</span><span class="special">&gt;&gt;()</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">sync_bounded_queue</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="identifier">sync_bounded_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="keyword">operator</span><span class="special">&gt;&gt;(</span><span class="identifier">sync_bounded_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="identifier">sbq</span><span class="special">,</span> <span class="identifier">ValueType</span> <span class="special">&amp;</span><span class="identifier">elem</span><span class="special">);</span>
<span class="special">}</span>
</pre>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_ref"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_ref" title="Synchronized Unbounded Queue">Synchronized
          Unbounded Queue</a>
</h5></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">sync_queue</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="keyword">class</span> <span class="identifier">sync_queue</span><span class="special">;</span>

  <span class="comment">// Stream-like operators</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="identifier">sync_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="keyword">operator</span><span class="special">&lt;&lt;(</span><span class="identifier">sync_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="identifier">sbq</span><span class="special">,</span> <span class="identifier">ValueType</span><span class="special">&amp;&amp;</span> <span class="identifier">elem</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="identifier">sync_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="keyword">operator</span><span class="special">&lt;&lt;(</span><span class="identifier">sync_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="identifier">sbq</span><span class="special">,</span> <span class="identifier">ValueType</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">elem</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="identifier">sync_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="keyword">operator</span><span class="special">&gt;&gt;(</span><span class="identifier">sync_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="identifier">sbq</span><span class="special">,</span> <span class="identifier">ValueType</span> <span class="special">&amp;</span><span class="identifier">elem</span><span class="special">);</span>
<span class="special">}</span>
</pre>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_ref.sync_queue"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_ref.sync_queue" title="Class template sync_queue&lt;&gt;">Class
            template <code class="computeroutput"><span class="identifier">sync_queue</span><span class="special">&lt;&gt;</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">sync_queue</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">,</span> <span class="keyword">class</span> <span class="identifier">Container</span> <span class="special">=</span> <span class="identifier">csbl</span><span class="special">::</span><span class="identifier">devector</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&gt;</span>
  <span class="keyword">class</span> <span class="identifier">sync_queue</span>
  <span class="special">{</span>
  <span class="keyword">public</span><span class="special">:</span>
    <span class="keyword">typedef</span> <span class="identifier">ValueType</span> <span class="identifier">value_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">Container</span> <span class="identifier">underlying_queue_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">Container</span><span class="special">::</span><span class="identifier">size_type</span> <span class="identifier">size_type</span><span class="special">;</span>

    <span class="identifier">sync_queue</span><span class="special">(</span><span class="identifier">sync_queue</span> <span class="keyword">const</span><span class="special">&amp;)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>
    <span class="identifier">sync_queue</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="identifier">sync_queue</span> <span class="keyword">const</span><span class="special">&amp;)</span> <span class="special">=</span> <span class="keyword">delete</span><span class="special">;</span>
    <span class="identifier">sync_queue</span><span class="special">();</span>
    <span class="keyword">explicit</span> <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">Range</span><span class="special">&gt;</span>
    <span class="identifier">sync_queue</span><span class="special">(</span><span class="identifier">Range</span> <span class="identifier">range</span><span class="special">);</span> <span class="comment">// Not yet implemented</span>
    <span class="special">~</span><span class="identifier">sync_queue</span><span class="special">();</span>

    <span class="comment">// Observers</span>
    <span class="keyword">bool</span> <span class="identifier">empty</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="keyword">bool</span> <span class="identifier">full</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="identifier">size_type</span> <span class="identifier">size</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
    <span class="keyword">bool</span> <span class="identifier">closed</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>

    <span class="comment">// Modifiers</span>
    <span class="keyword">void</span> <span class="identifier">push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="keyword">void</span> <span class="identifier">push_back</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="identifier">queue_op_status</span> <span class="identifier">try_push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">queue_op_status</span> <span class="identifier">try_push_back</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;&amp;)</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span><span class="special">&amp;</span> <span class="identifier">x</span><span class="special">);</span>
    <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_push_back</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;&amp;</span> <span class="identifier">x</span><span class="special">);</span>

    <span class="keyword">void</span> <span class="identifier">pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;);</span>
    <span class="identifier">value_type</span> <span class="identifier">pull_front</span><span class="special">();</span>

    <span class="identifier">queue_op_status</span> <span class="identifier">try_pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;);</span>
    <span class="identifier">queue_op_status</span> <span class="identifier">nonblocking_pull_front</span><span class="special">(</span><span class="identifier">value_type</span><span class="special">&amp;);</span>

    <span class="identifier">underlying_queue_type</span> <span class="identifier">underlying_queue</span><span class="special">()</span> <span class="keyword">noexcept</span><span class="special">;</span>

    <span class="keyword">void</span> <span class="identifier">close</span><span class="special">();</span>
  <span class="special">};</span>
<span class="special">}</span>
</pre>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_ref.sync_queue.constructor"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_ref.sync_queue.constructor" title="Constructor sync_queue(size_type)">Constructor
              <code class="computeroutput"><span class="identifier">sync_queue</span><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="keyword">explicit</span> <span class="identifier">sync_queue</span><span class="special">();</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Effects:</span></dt>
<dd><p>
                      Constructs an empty sync_queue.
                    </p></dd>
<dt><span class="term">Throws:</span></dt>
<dd><p>
                      any exception that can be throw because of resources unavailable.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_ref.sync_queue.full"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_ref.sync_queue.full" title="Member Function full()">Member
              Function <code class="computeroutput"><span class="identifier">full</span><span class="special">()</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="keyword">bool</span> <span class="identifier">full</span><span class="special">()</span> <span class="keyword">const</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Returns:</span></dt>
<dd><p>
                      false.
                    </p></dd>
</dl>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_ref.sync_queue.constructor0"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_ref.sync_queue.constructor0" title="Member Function underlying_queue()">Member
              Function <code class="computeroutput"><span class="identifier">underlying_queue</span><span class="special">()</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="identifier">underlying_queue_type</span> <span class="identifier">underlying_queue</span><span class="special">()</span> <span class="keyword">noexcept</span><span class="special">;</span>
</pre>
<div class="variablelist">
<p class="title"><b></b></p>
<dl class="variablelist">
<dt><span class="term">Returns:</span></dt>
<dd><p>
                      Moves internal queue.
                    </p></dd>
</dl>
</div>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_ref.stream_out_operators"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_ref.stream_out_operators" title="Non-Member Function operator&lt;&lt;()">Non-Member
            Function <code class="computeroutput"><span class="keyword">operator</span><span class="special">&lt;&lt;()</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">sync_queue</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="identifier">sync_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="keyword">operator</span><span class="special">&lt;&lt;(</span><span class="identifier">sync_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="identifier">sbq</span><span class="special">,</span> <span class="identifier">ValueType</span><span class="special">&amp;&amp;</span> <span class="identifier">elem</span><span class="special">);</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="identifier">sync_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="keyword">operator</span><span class="special">&lt;&lt;(</span><span class="identifier">sync_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="identifier">sbq</span><span class="special">,</span> <span class="identifier">ValueType</span> <span class="keyword">const</span><span class="special">&amp;</span><span class="identifier">elem</span><span class="special">);</span>
<span class="special">}</span>
</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h6 class="title">
<a name="thread.sds.synchronized_queues.ref.sync_queue_ref.stream_in_operators"></a><a class="link" href="sds.html#thread.sds.synchronized_queues.ref.sync_queue_ref.stream_in_operators" title="Non-Member Function operator&gt;&gt;()">Non-Member
            Function <code class="computeroutput"><span class="keyword">operator</span><span class="special">&gt;&gt;()</span></code></a>
</h6></div></div></div>
<pre class="programlisting"><span class="preprocessor">#include</span> <span class="special">&lt;</span><span class="identifier">boost</span><span class="special">/</span><span class="identifier">thread</span><span class="special">/</span><span class="identifier">sync_queue</span><span class="special">.</span><span class="identifier">hpp</span><span class="special">&gt;</span>
<span class="keyword">namespace</span> <span class="identifier">boost</span>
<span class="special">{</span>
  <span class="keyword">template</span> <span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">ValueType</span><span class="special">&gt;</span>
  <span class="identifier">sync_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="keyword">operator</span><span class="special">&gt;&gt;(</span><span class="identifier">sync_queue</span><span class="special">&lt;</span><span class="identifier">ValueType</span><span class="special">&gt;&amp;</span> <span class="identifier">sbq</span><span class="special">,</span> <span class="identifier">ValueType</span> <span class="special">&amp;</span><span class="identifier">elem</span><span class="special">);</span>
<span class="special">}</span>
</pre>
</div>
</div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2007 -11 Anthony Williams<br>Copyright © 2011 -17 Vicente J. Botet Escriba<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="thread_local_storage.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../thread.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="parallel.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
