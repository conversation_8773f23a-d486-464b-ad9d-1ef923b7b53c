<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Performance</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../boost_lexical_cast.html" title="Chapter 19. Boost.Lexical_Cast 1.0">
<link rel="prev" href="changes.html" title="Changes">
<link rel="next" href="../lockfree.html" title="Chapter 20. Boost.Lockfree">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="changes.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost_lexical_cast.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../lockfree.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_lexical_cast.performance"></a><a class="link" href="performance.html" title="Performance">Performance</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="performance.html#boost_lexical_cast.performance.tests_description">Tests
      description</a></span></dt>
<dt><span class="section"><a href="performance.html#boost_lexical_cast.performance.gnu_c___version_6_1_1_20160511">GNU
      C++ version 6.1.1 20160511</a></span></dt>
<dt><span class="section"><a href="performance.html#boost_lexical_cast.performance.gnu_c___version_4_8_5">GNU
      C++ version 4.8.5</a></span></dt>
<dt><span class="section"><a href="performance.html#boost_lexical_cast.performance.clang_version_3_6_0__tags_release_360_final_">Clang
      version 3.6.0 (tags/RELEASE_360/final)</a></span></dt>
</dl></div>
<p>
      In most cases <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">lexical_cast</span></code> is faster than <code class="computeroutput"><span class="identifier">scanf</span></code>, <code class="computeroutput"><span class="identifier">printf</span></code>,
      <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">stringstream</span></code>. For more detailed info you
      can look at the tables below.
    </p>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_lexical_cast.performance.tests_description"></a><a class="link" href="performance.html#boost_lexical_cast.performance.tests_description" title="Tests description">Tests
      description</a>
</h3></div></div></div>
<p>
        All the tests measure execution speed in milliseconds for 10000 iterations
        of the following code blocks:
      </p>
<div class="table">
<a name="boost_lexical_cast.performance.tests_description.legend"></a><p class="title"><b>Table 19.1. Tests source code</b></p>
<div class="table-contents"><table class="table" summary="Tests source code">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  Test name
                </p>
              </th>
<th>
                <p>
                  Code
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  lexical_cast
                </p>
              </td>
<td>
                <p>
</p>
<pre class="programlisting"><span class="identifier">_out</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">lexical_cast</span><span class="special">&lt;</span><span class="identifier">OUTTYPE</span><span class="special">&gt;(</span><span class="identifier">_in</span><span class="special">);</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  std::stringstream with construction
                </p>
              </td>
<td>
                <p>
</p>
<pre class="programlisting"><span class="identifier">std</span><span class="special">::</span><span class="identifier">stringstream</span> <span class="identifier">ss</span><span class="special">;</span>
<span class="identifier">ss</span> <span class="special">&lt;&lt;</span> <span class="identifier">_in</span><span class="special">;</span>
<span class="keyword">if</span> <span class="special">(</span><span class="identifier">ss</span><span class="special">.</span><span class="identifier">fail</span><span class="special">())</span> <span class="keyword">throw</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">logic_error</span><span class="special">(</span><span class="identifier">descr</span><span class="special">);</span>
<span class="identifier">ss</span> <span class="special">&gt;&gt;</span> <span class="identifier">_out</span><span class="special">;</span>
<span class="keyword">if</span> <span class="special">(</span><span class="identifier">ss</span><span class="special">.</span><span class="identifier">fail</span><span class="special">())</span> <span class="keyword">throw</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">logic_error</span><span class="special">(</span><span class="identifier">descr</span><span class="special">);</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  std::stringstream without construction
                </p>
              </td>
<td>
                <p>
</p>
<pre class="programlisting"><span class="identifier">ss</span> <span class="special">&lt;&lt;</span> <span class="identifier">_in</span><span class="special">;</span> <span class="comment">// ss is an instance of std::stringstream</span>
<span class="keyword">if</span> <span class="special">(</span><span class="identifier">ss</span><span class="special">.</span><span class="identifier">fail</span><span class="special">())</span> <span class="keyword">throw</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">logic_error</span><span class="special">(</span><span class="identifier">descr</span><span class="special">);</span>
<span class="identifier">ss</span> <span class="special">&gt;&gt;</span> <span class="identifier">_out</span><span class="special">;</span>
<span class="keyword">if</span> <span class="special">(</span><span class="identifier">ss</span><span class="special">.</span><span class="identifier">fail</span><span class="special">())</span> <span class="keyword">throw</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">logic_error</span><span class="special">(</span><span class="identifier">descr</span><span class="special">);</span>
<span class="comment">/* reseting std::stringstream to use it again */</span>
<span class="identifier">ss</span><span class="special">.</span><span class="identifier">str</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span><span class="special">());</span>
<span class="identifier">ss</span><span class="special">.</span><span class="identifier">clear</span><span class="special">();</span>
</pre>
<p>
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  scanf/printf
                </p>
              </td>
<td>
                <p>
</p>
<pre class="programlisting"><span class="keyword">typename</span> <span class="identifier">OUTTYPE</span><span class="special">::</span><span class="identifier">value_type</span> <span class="identifier">buffer</span><span class="special">[</span><span class="number">500</span><span class="special">];</span>
<span class="identifier">sprintf</span><span class="special">(</span> <span class="special">(</span><span class="keyword">char</span><span class="special">*)</span><span class="identifier">buffer</span><span class="special">,</span> <span class="identifier">conv</span><span class="special">,</span> <span class="identifier">_in</span><span class="special">);</span>
<span class="identifier">_out</span> <span class="special">=</span> <span class="identifier">buffer</span><span class="special">;</span>
</pre>
<p>
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break"><p>
        Fastest results are highlitened with "!!! <span class="bold"><strong>x</strong></span>
        !!!". Do not use this results to compare compilers, because tests were
        taken on different hardware.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_lexical_cast.performance.gnu_c___version_6_1_1_20160511"></a><a class="link" href="performance.html#boost_lexical_cast.performance.gnu_c___version_6_1_1_20160511" title="GNU C++ version 6.1.1 20160511">GNU
      C++ version 6.1.1 20160511</a>
</h3></div></div></div>
<div class="table">
<a name="boost_lexical_cast.performance.gnu_c___version_6_1_1_20160511.id"></a><p class="title"><b>Table 19.2. Performance Table ( GNU C++ version 6.1.1 20160511)</b></p>
<div class="table-contents"><table class="table" summary="Performance Table ( GNU C++ version 6.1.1 20160511)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  From-&gt;To
                </p>
              </th>
<th>
                <p>
                  lexical_cast
                </p>
              </th>
<th>
                <p>
                  std::stringstream with construction
                </p>
              </th>
<th>
                <p>
                  std::stringstream without construction
                </p>
              </th>
<th>
                <p>
                  scanf/printf
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  string-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  52
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  54
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  61
                </p>
              </td>
<td>
                <p>
                  19
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  61
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  70
                </p>
              </td>
<td>
                <p>
                  21
                </p>
              </td>
<td>
                <p>
                  21
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;float
                </p>
              </td>
<td>
                <p>
                  91
                </p>
              </td>
<td>
                <p>
                  152
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>40</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;double
                </p>
              </td>
<td>
                <p>
                  86
                </p>
              </td>
<td>
                <p>
                  140
                </p>
              </td>
<td>
                <p>
                  58
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>28</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;long double
                </p>
              </td>
<td>
                <p>
                  48
                </p>
              </td>
<td>
                <p>
                  90
                </p>
              </td>
<td>
                <p>
                  34
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>2</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  129
                </p>
              </td>
<td>
                <p>
                  29
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  70
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  99
                </p>
              </td>
<td>
                <p>
                  27
                </p>
              </td>
<td>
                <p>
                  20
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>9</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  101
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  86
                </p>
              </td>
<td>
                <p>
                  27
                </p>
              </td>
<td>
                <p>
                  27
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  int-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>9</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  89
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
<td>
                <p>
                  14
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  short-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  71
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
<td>
                <p>
                  15
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  long int-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  71
                </p>
              </td>
<td>
                <p>
                  18
                </p>
              </td>
<td>
                <p>
                  19
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  long long-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>13</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  127
                </p>
              </td>
<td>
                <p>
                  34
                </p>
              </td>
<td>
                <p>
                  25
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned int-&gt;string
                </p>
              </td>
<td>
                <p>
                  16
                </p>
              </td>
<td>
                <p>
                  117
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>12</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned short-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>8</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  71
                </p>
              </td>
<td>
                <p>
                  16
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned long int-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>12</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  100
                </p>
              </td>
<td>
                <p>
                  36
                </p>
              </td>
<td>
                <p>
                  26
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned long long-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>14</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  97
                </p>
              </td>
<td>
                <p>
                  21
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  float-&gt;string
                </p>
              </td>
<td>
                <p>
                  70
                </p>
              </td>
<td>
                <p>
                  97
                </p>
              </td>
<td>
                <p>
                  43
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>25</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  double-&gt;string
                </p>
              </td>
<td>
                <p>
                  130
                </p>
              </td>
<td>
                <p>
                  155
                </p>
              </td>
<td>
                <p>
                  51
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>25</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  long double-&gt;string
                </p>
              </td>
<td>
                <p>
                  104
                </p>
              </td>
<td>
                <p>
                  160
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>47</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  57
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  95
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  52
                </p>
              </td>
<td>
                <p>
                  7
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  106
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  118
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  21
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  104
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  19
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>8</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  112
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  14
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  90
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  103
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  82
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  58
                </p>
              </td>
<td>
                <p>
                  20
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;float
                </p>
              </td>
<td>
                <p>
                  58
                </p>
              </td>
<td>
                <p>
                  103
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>32</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  37
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;double
                </p>
              </td>
<td>
                <p>
                  52
                </p>
              </td>
<td>
                <p>
                  155
                </p>
              </td>
<td>
                <p>
                  32
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>27</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;long double
                </p>
              </td>
<td>
                <p>
                  72
                </p>
              </td>
<td>
                <p>
                  135
                </p>
              </td>
<td>
                <p>
                  51
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>30</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  80
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>10</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  150
                </p>
              </td>
<td>
                <p>
                  18
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  64
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  52
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  54
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  54
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  66
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  19
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  79
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  123
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>8</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  121
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>8</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  121
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;float
                </p>
              </td>
<td>
                <p>
                  97
                </p>
              </td>
<td>
                <p>
                  167
                </p>
              </td>
<td>
                <p>
                  67
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>47</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;double
                </p>
              </td>
<td>
                <p>
                  96
                </p>
              </td>
<td>
                <p>
                  164
                </p>
              </td>
<td>
                <p>
                  67
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>47</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;long double
                </p>
              </td>
<td>
                <p>
                  97
                </p>
              </td>
<td>
                <p>
                  165
                </p>
              </td>
<td>
                <p>
                  66
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>47</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>11</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  139
                </p>
              </td>
<td>
                <p>
                  34
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  121
                </p>
              </td>
<td>
                <p>
                  25
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  106
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  81
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  109
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  122
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  21
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  64
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  120
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>8</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  121
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>8</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  122
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;float
                </p>
              </td>
<td>
                <p>
                  95
                </p>
              </td>
<td>
                <p>
                  165
                </p>
              </td>
<td>
                <p>
                  68
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>46</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;double
                </p>
              </td>
<td>
                <p>
                  95
                </p>
              </td>
<td>
                <p>
                  161
                </p>
              </td>
<td>
                <p>
                  66
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>47</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;long double
                </p>
              </td>
<td>
                <p>
                  96
                </p>
              </td>
<td>
                <p>
                  161
                </p>
              </td>
<td>
                <p>
                  66
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>46</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  117
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>10</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  84
                </p>
              </td>
<td>
                <p>
                  15
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  25
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  111
                </p>
              </td>
<td>
                <p>
                  16
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  110
                </p>
              </td>
<td>
                <p>
                  16
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  111
                </p>
              </td>
<td>
                <p>
                  15
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  25
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  25
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  120
                </p>
              </td>
<td>
                <p>
                  25
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>8</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  117
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  120
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>8</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  118
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;float
                </p>
              </td>
<td>
                <p>
                  96
                </p>
              </td>
<td>
                <p>
                  155
                </p>
              </td>
<td>
                <p>
                  48
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>47</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;double
                </p>
              </td>
<td>
                <p>
                  96
                </p>
              </td>
<td>
                <p>
                  141
                </p>
              </td>
<td>
                <p>
                  47
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>47</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;long double
                </p>
              </td>
<td>
                <p>
                  96
                </p>
              </td>
<td>
                <p>
                  140
                </p>
              </td>
<td>
                <p>
                  46
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>46</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  118
                </p>
              </td>
<td>
                <p>
                  25
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>10</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  136
                </p>
              </td>
<td>
                <p>
                  35
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  26
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  108
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  106
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  107
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  121
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  123
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  121
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  25
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  120
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;float
                </p>
              </td>
<td>
                <p>
                  47
                </p>
              </td>
<td>
                <p>
                  80
                </p>
              </td>
<td>
                <p>
                  32
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;double
                </p>
              </td>
<td>
                <p>
                  46
                </p>
              </td>
<td>
                <p>
                  82
                </p>
              </td>
<td>
                <p>
                  31
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;long double
                </p>
              </td>
<td>
                <p>
                  49
                </p>
              </td>
<td>
                <p>
                  82
                </p>
              </td>
<td>
                <p>
                  31
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  7
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  70
                </p>
              </td>
<td>
                <p>
                  15
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  int-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  61
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  float-&gt;double
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  111
                </p>
              </td>
<td>
                <p>
                  54
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  51
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_lexical_cast.performance.gnu_c___version_4_8_5"></a><a class="link" href="performance.html#boost_lexical_cast.performance.gnu_c___version_4_8_5" title="GNU C++ version 4.8.5">GNU
      C++ version 4.8.5</a>
</h3></div></div></div>
<div class="table">
<a name="boost_lexical_cast.performance.gnu_c___version_4_8_5.id"></a><p class="title"><b>Table 19.3. Performance Table ( GNU C++ version 4.8.5)</b></p>
<div class="table-contents"><table class="table" summary="Performance Table ( GNU C++ version 4.8.5)">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  From-&gt;To
                </p>
              </th>
<th>
                <p>
                  lexical_cast
                </p>
              </th>
<th>
                <p>
                  std::stringstream with construction
                </p>
              </th>
<th>
                <p>
                  std::stringstream without construction
                </p>
              </th>
<th>
                <p>
                  scanf/printf
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  string-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  100
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  97
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  103
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  122
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  116
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  97
                </p>
              </td>
<td>
                <p>
                  21
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  118
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  116
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  106
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  58
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;float
                </p>
              </td>
<td>
                <p>
                  88
                </p>
              </td>
<td>
                <p>
                  166
                </p>
              </td>
<td>
                <p>
                  70
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>47</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;double
                </p>
              </td>
<td>
                <p>
                  102
                </p>
              </td>
<td>
                <p>
                  162
                </p>
              </td>
<td>
                <p>
                  65
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>51</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;long double
                </p>
              </td>
<td>
                <p>
                  96
                </p>
              </td>
<td>
                <p>
                  163
                </p>
              </td>
<td>
                <p>
                  71
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>46</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  112
                </p>
              </td>
<td>
                <p>
                  21
                </p>
              </td>
<td>
                <p>
                  18
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>2</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  139
                </p>
              </td>
<td>
                <p>
                  37
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  121
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>9</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  121
                </p>
              </td>
<td>
                <p>
                  31
                </p>
              </td>
<td>
                <p>
                  21
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>9</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  121
                </p>
              </td>
<td>
                <p>
                  31
                </p>
              </td>
<td>
                <p>
                  34
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>9</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  120
                </p>
              </td>
<td>
                <p>
                  31
                </p>
              </td>
<td>
                <p>
                  30
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  int-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>17</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  141
                </p>
              </td>
<td>
                <p>
                  39
                </p>
              </td>
<td>
                <p>
                  30
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  short-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>18</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  142
                </p>
              </td>
<td>
                <p>
                  39
                </p>
              </td>
<td>
                <p>
                  30
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  long int-&gt;string
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
<td>
                <p>
                  136
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>12</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  long long-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  69
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned int-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>8</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  70
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned short-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  69
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned long int-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  71
                </p>
              </td>
<td>
                <p>
                  16
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned long long-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  71
                </p>
              </td>
<td>
                <p>
                  16
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  float-&gt;string
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  95
                </p>
              </td>
<td>
                <p>
                  49
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>24</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  double-&gt;string
                </p>
              </td>
<td>
                <p>
                  68
                </p>
              </td>
<td>
                <p>
                  97
                </p>
              </td>
<td>
                <p>
                  45
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>26</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  long double-&gt;string
                </p>
              </td>
<td>
                <p>
                  72
                </p>
              </td>
<td>
                <p>
                  108
                </p>
              </td>
<td>
                <p>
                  45
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>28</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  52
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  52
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  52
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  61
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  61
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  103
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  97
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  123
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  72
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;float
                </p>
              </td>
<td>
                <p>
                  85
                </p>
              </td>
<td>
                <p>
                  160
                </p>
              </td>
<td>
                <p>
                  66
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>47</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;double
                </p>
              </td>
<td>
                <p>
                  94
                </p>
              </td>
<td>
                <p>
                  161
                </p>
              </td>
<td>
                <p>
                  65
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>46</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;long double
                </p>
              </td>
<td>
                <p>
                  94
                </p>
              </td>
<td>
                <p>
                  172
                </p>
              </td>
<td>
                <p>
                  64
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>47</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>2</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  113
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  16
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>10</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  145
                </p>
              </td>
<td>
                <p>
                  34
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  120
                </p>
              </td>
<td>
                <p>
                  25
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  102
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  100
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  102
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  120
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  82
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  58
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;float
                </p>
              </td>
<td>
                <p>
                  47
                </p>
              </td>
<td>
                <p>
                  80
                </p>
              </td>
<td>
                <p>
                  32
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;double
                </p>
              </td>
<td>
                <p>
                  47
                </p>
              </td>
<td>
                <p>
                  79
                </p>
              </td>
<td>
                <p>
                  31
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>23</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;long double
                </p>
              </td>
<td>
                <p>
                  47
                </p>
              </td>
<td>
                <p>
                  80
                </p>
              </td>
<td>
                <p>
                  31
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  58
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  7
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  68
                </p>
              </td>
<td>
                <p>
                  15
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  52
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  54
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  52
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  6
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  58
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  58
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;float
                </p>
              </td>
<td>
                <p>
                  47
                </p>
              </td>
<td>
                <p>
                  81
                </p>
              </td>
<td>
                <p>
                  32
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>25</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;double
                </p>
              </td>
<td>
                <p>
                  46
                </p>
              </td>
<td>
                <p>
                  79
                </p>
              </td>
<td>
                <p>
                  31
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;long double
                </p>
              </td>
<td>
                <p>
                  48
                </p>
              </td>
<td>
                <p>
                  80
                </p>
              </td>
<td>
                <p>
                  32
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  63
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  7
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  68
                </p>
              </td>
<td>
                <p>
                  15
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  58
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  54
                </p>
              </td>
<td>
                <p>
                  6
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  57
                </p>
              </td>
<td>
                <p>
                  6
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  54
                </p>
              </td>
<td>
                <p>
                  6
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  61
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  57
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  58
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  58
                </p>
              </td>
<td>
                <p>
                  15
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;float
                </p>
              </td>
<td>
                <p>
                  46
                </p>
              </td>
<td>
                <p>
                  78
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;double
                </p>
              </td>
<td>
                <p>
                  94
                </p>
              </td>
<td>
                <p>
                  85
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>21</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;long double
                </p>
              </td>
<td>
                <p>
                  47
                </p>
              </td>
<td>
                <p>
                  79
                </p>
              </td>
<td>
                <p>
                  33
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  102
                </p>
              </td>
<td>
                <p>
                  25
                </p>
              </td>
<td>
                <p>
                  16
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>10</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  96
                </p>
              </td>
<td>
                <p>
                  16
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  64
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  75
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  54
                </p>
              </td>
<td>
                <p>
                  6
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  103
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  121
                </p>
              </td>
<td>
                <p>
                  25
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  122
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>7</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  120
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  121
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  121
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  118
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  20
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  109
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  21
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;float
                </p>
              </td>
<td>
                <p>
                  93
                </p>
              </td>
<td>
                <p>
                  150
                </p>
              </td>
<td>
                <p>
                  61
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>43</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;double
                </p>
              </td>
<td>
                <p>
                  89
                </p>
              </td>
<td>
                <p>
                  147
                </p>
              </td>
<td>
                <p>
                  61
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>43</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;long double
                </p>
              </td>
<td>
                <p>
                  91
                </p>
              </td>
<td>
                <p>
                  148
                </p>
              </td>
<td>
                <p>
                  61
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>42</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>2</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  106
                </p>
              </td>
<td>
                <p>
                  21
                </p>
              </td>
<td>
                <p>
                  15
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>10</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  124
                </p>
              </td>
<td>
                <p>
                  32
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  109
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  int-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  114
                </p>
              </td>
<td>
                <p>
                  26
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  float-&gt;double
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  207
                </p>
              </td>
<td>
                <p>
                  105
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  97
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_lexical_cast.performance.clang_version_3_6_0__tags_release_360_final_"></a><a class="link" href="performance.html#boost_lexical_cast.performance.clang_version_3_6_0__tags_release_360_final_" title="Clang version 3.6.0 (tags/RELEASE_360/final)">Clang
      version 3.6.0 (tags/RELEASE_360/final)</a>
</h3></div></div></div>
<div class="table">
<a name="boost_lexical_cast.performance.clang_version_3_6_0__tags_release_360_final_.id"></a><p class="title"><b>Table 19.4. Performance Table ( Clang version 3.6.0 (tags/RELEASE_360/final))</b></p>
<div class="table-contents"><table class="table" summary="Performance Table ( Clang version 3.6.0 (tags/RELEASE_360/final))">
<colgroup>
<col>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                <p>
                  From-&gt;To
                </p>
              </th>
<th>
                <p>
                  lexical_cast
                </p>
              </th>
<th>
                <p>
                  std::stringstream with construction
                </p>
              </th>
<th>
                <p>
                  std::stringstream without construction
                </p>
              </th>
<th>
                <p>
                  scanf/printf
                </p>
              </th>
</tr></thead>
<tbody>
<tr>
<td>
                <p>
                  string-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  79
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  51
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  51
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  80
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  108
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  66
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  101
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  20
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  77
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  61
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  87
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  89
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;float
                </p>
              </td>
<td>
                <p>
                  52
                </p>
              </td>
<td>
                <p>
                  114
                </p>
              </td>
<td>
                <p>
                  38
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;double
                </p>
              </td>
<td>
                <p>
                  49
                </p>
              </td>
<td>
                <p>
                  79
                </p>
              </td>
<td>
                <p>
                  32
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;long double
                </p>
              </td>
<td>
                <p>
                  83
                </p>
              </td>
<td>
                <p>
                  160
                </p>
              </td>
<td>
                <p>
                  65
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>47</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  114
                </p>
              </td>
<td>
                <p>
                  21
                </p>
              </td>
<td>
                <p>
                  16
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>2</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  78
                </p>
              </td>
<td>
                <p>
                  34
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  100
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  16
                </p>
              </td>
<td>
                <p>
                  7
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  70
                </p>
              </td>
<td>
                <p>
                  30
                </p>
              </td>
<td>
                <p>
                  30
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  string-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>10</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  31
                </p>
              </td>
<td>
                <p>
                  30
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  int-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>17</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  140
                </p>
              </td>
<td>
                <p>
                  38
                </p>
              </td>
<td>
                <p>
                  28
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  short-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>17</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  139
                </p>
              </td>
<td>
                <p>
                  38
                </p>
              </td>
<td>
                <p>
                  29
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  long int-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>17</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  139
                </p>
              </td>
<td>
                <p>
                  37
                </p>
              </td>
<td>
                <p>
                  29
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  long long-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>18</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  138
                </p>
              </td>
<td>
                <p>
                  37
                </p>
              </td>
<td>
                <p>
                  30
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned int-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>17</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  138
                </p>
              </td>
<td>
                <p>
                  37
                </p>
              </td>
<td>
                <p>
                  29
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned short-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>17</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  139
                </p>
              </td>
<td>
                <p>
                  38
                </p>
              </td>
<td>
                <p>
                  29
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned long int-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>17</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  142
                </p>
              </td>
<td>
                <p>
                  37
                </p>
              </td>
<td>
                <p>
                  29
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned long long-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>8</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  71
                </p>
              </td>
<td>
                <p>
                  16
                </p>
              </td>
<td>
                <p>
                  28
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  float-&gt;string
                </p>
              </td>
<td>
                <p>
                  68
                </p>
              </td>
<td>
                <p>
                  97
                </p>
              </td>
<td>
                <p>
                  42
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>38</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  double-&gt;string
                </p>
              </td>
<td>
                <p>
                  68
                </p>
              </td>
<td>
                <p>
                  134
                </p>
              </td>
<td>
                <p>
                  43
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>25</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  long double-&gt;string
                </p>
              </td>
<td>
                <p>
                  72
                </p>
              </td>
<td>
                <p>
                  164
                </p>
              </td>
<td>
                <p>
                  91
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>55</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  76
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  54
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  55
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  61
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  61
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;float
                </p>
              </td>
<td>
                <p>
                  48
                </p>
              </td>
<td>
                <p>
                  80
                </p>
              </td>
<td>
                <p>
                  32
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>25</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;double
                </p>
              </td>
<td>
                <p>
                  48
                </p>
              </td>
<td>
                <p>
                  81
                </p>
              </td>
<td>
                <p>
                  32
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;long double
                </p>
              </td>
<td>
                <p>
                  48
                </p>
              </td>
<td>
                <p>
                  90
                </p>
              </td>
<td>
                <p>
                  31
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  59
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  7
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  77
                </p>
              </td>
<td>
                <p>
                  15
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char*-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  62
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  54
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  53
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  57
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  63
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  65
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  67
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  116
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  114
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  118
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  116
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;float
                </p>
              </td>
<td>
                <p>
                  93
                </p>
              </td>
<td>
                <p>
                  160
                </p>
              </td>
<td>
                <p>
                  66
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>47</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;double
                </p>
              </td>
<td>
                <p>
                  93
                </p>
              </td>
<td>
                <p>
                  158
                </p>
              </td>
<td>
                <p>
                  64
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>46</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;long double
                </p>
              </td>
<td>
                <p>
                  93
                </p>
              </td>
<td>
                <p>
                  158
                </p>
              </td>
<td>
                <p>
                  64
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>46</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  112
                </p>
              </td>
<td>
                <p>
                  21
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>10</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  136
                </p>
              </td>
<td>
                <p>
                  33
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  unsigned char*-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  117
                </p>
              </td>
<td>
                <p>
                  26
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  102
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  102
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  102
                </p>
              </td>
<td>
                <p>
                  11
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  119
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  116
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  116
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  115
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  116
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  114
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  92
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  60
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;float
                </p>
              </td>
<td>
                <p>
                  94
                </p>
              </td>
<td>
                <p>
                  134
                </p>
              </td>
<td>
                <p>
                  51
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>28</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;double
                </p>
              </td>
<td>
                <p>
                  47
                </p>
              </td>
<td>
                <p>
                  80
                </p>
              </td>
<td>
                <p>
                  31
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>22</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;long double
                </p>
              </td>
<td>
                <p>
                  90
                </p>
              </td>
<td>
                <p>
                  115
                </p>
              </td>
<td>
                <p>
                  64
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>25</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  97
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  17
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>11</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  139
                </p>
              </td>
<td>
                <p>
                  34
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  signed char*-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  118
                </p>
              </td>
<td>
                <p>
                  26
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  106
                </p>
              </td>
<td>
                <p>
                  15
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  107
                </p>
              </td>
<td>
                <p>
                  15
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  107
                </p>
              </td>
<td>
                <p>
                  15
                </p>
              </td>
<td>
                <p>
                  12
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  117
                </p>
              </td>
<td>
                <p>
                  25
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  116
                </p>
              </td>
<td>
                <p>
                  25
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>6</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  114
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  20
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  106
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  104
                </p>
              </td>
<td>
                <p>
                  21
                </p>
              </td>
<td>
                <p>
                  20
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  105
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  20
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  106
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  20
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>5</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  105
                </p>
              </td>
<td>
                <p>
                  23
                </p>
              </td>
<td>
                <p>
                  20
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;float
                </p>
              </td>
<td>
                <p>
                  89
                </p>
              </td>
<td>
                <p>
                  140
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>42</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  43
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;double
                </p>
              </td>
<td>
                <p>
                  88
                </p>
              </td>
<td>
                <p>
                  127
                </p>
              </td>
<td>
                <p>
                  43
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>43</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;long double
                </p>
              </td>
<td>
                <p>
                  88
                </p>
              </td>
<td>
                <p>
                  127
                </p>
              </td>
<td>
                <p>
                  43
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>43</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  104
                </p>
              </td>
<td>
                <p>
                  22
                </p>
              </td>
<td>
                <p>
                  15
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>9</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  122
                </p>
              </td>
<td>
                <p>
                  32
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  iterator_range&lt;char*&gt;-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  105
                </p>
              </td>
<td>
                <p>
                  24
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  68
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  47
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  48
                </p>
              </td>
<td>
                <p>
                  4
                </p>
              </td>
<td>
                <p>
                  5
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  53
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  54
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  54
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
<td>
                <p>
                  7
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  53
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  52
                </p>
              </td>
<td>
                <p>
                  7
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned short
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  53
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
<td>
                <p>
                  7
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned long int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  53
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;unsigned long long
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>3</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  53
                </p>
              </td>
<td>
                <p>
                  9
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;float
                </p>
              </td>
<td>
                <p>
                  43
                </p>
              </td>
<td>
                <p>
                  72
                </p>
              </td>
<td>
                <p>
                  29
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>20</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;double
                </p>
              </td>
<td>
                <p>
                  42
                </p>
              </td>
<td>
                <p>
                  72
                </p>
              </td>
<td>
                <p>
                  28
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>20</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;long double
                </p>
              </td>
<td>
                <p>
                  43
                </p>
              </td>
<td>
                <p>
                  72
                </p>
              </td>
<td>
                <p>
                  28
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>20</strong></span> !!!
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;array&lt;char, 50&gt;
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  53
                </p>
              </td>
<td>
                <p>
                  8
                </p>
              </td>
<td>
                <p>
                  6
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>4</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  62
                </p>
              </td>
<td>
                <p>
                  13
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  array&lt;char, 50&gt;-&gt;container::string
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  54
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  int-&gt;int
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  57
                </p>
              </td>
<td>
                <p>
                  10
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  float-&gt;double
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  102
                </p>
              </td>
<td>
                <p>
                  49
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
<tr>
<td>
                <p>
                  char-&gt;signed char
                </p>
              </td>
<td>
                <p>
                  !!! <span class="bold"><strong>&lt;1</strong></span> !!!
                </p>
              </td>
<td>
                <p>
                  49
                </p>
              </td>
<td>
                <p>
                  3
                </p>
              </td>
<td>
                <p>
                  ---
                </p>
              </td>
</tr>
</tbody>
</table></div>
</div>
<br class="table-break">
</div>
</div>
<div class="copyright-footer">Copyright © 2000-2005 Kevlin Henney<br>Copyright © 2006-2010 Alexander Nasonov<br>Copyright © 2011-2023 Antony Polukhin<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="changes.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost_lexical_cast.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../lockfree.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
