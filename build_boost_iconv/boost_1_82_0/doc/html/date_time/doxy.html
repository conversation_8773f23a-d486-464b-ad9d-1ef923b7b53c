<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Library Reference</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../date_time.html" title="Chapter 11. Boost.Date_Time">
<link rel="prev" href="examples.html" title="Examples">
<link rel="next" href="../boost/date_time/day_functor.html" title="Class template day_functor">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="examples.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../date_time.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost/date_time/day_functor.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="date_time.doxy"></a>Library Reference</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="doxy.html#date_time_reference">Date Time Reference</a></span></dt>
<dt><span class="section"><a href="doxy.html#gregorian_reference">Gregorian Reference</a></span></dt>
<dt><span class="section"><a href="doxy.html#posix_time_reference">Posix Time Reference</a></span></dt>
<dt><span class="section"><a href="doxy.html#local_time_reference">Local Time Reference</a></span></dt>
</dl></div>
<p>
    The following is a detailed reference of the date_time library. A click on any of the reference links will take you to a list of the header files found in that section. Following one of those links will take you to a list of the items declared in that header file. Further sublinks take you to detailed descriptions of each individual item.
  </p>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="date_time_reference"></a>Date Time Reference</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="doxy.html#header.boost.date_time.adjust_functors_hpp">Header &lt;boost/date_time/adjust_functors.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.c_local_time_adjustor_hpp">Header &lt;boost/date_time/c_local_time_adjustor.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.c_time_hpp">Header &lt;boost/date_time/c_time.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.compiler_config_hpp">Header &lt;boost/date_time/compiler_config.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.constrained_value_hpp">Header &lt;boost/date_time/constrained_value.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_hpp">Header &lt;boost/date_time/date.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_clock_device_hpp">Header &lt;boost/date_time/date_clock_device.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_defs_hpp">Header &lt;boost/date_time/date_defs.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_duration_hpp">Header &lt;boost/date_time/date_duration.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_duration_types_hpp">Header &lt;boost/date_time/date_duration_types.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_facet_hpp">Header &lt;boost/date_time/date_facet.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_format_simple_hpp">Header &lt;boost/date_time/date_format_simple.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_formatting_hpp">Header &lt;boost/date_time/date_formatting.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_formatting_limited_hpp">Header &lt;boost/date_time/date_formatting_limited.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_formatting_locales_hpp">Header &lt;boost/date_time/date_formatting_locales.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_generator_formatter_hpp">Header &lt;boost/date_time/date_generator_formatter.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_generator_parser_hpp">Header &lt;boost/date_time/date_generator_parser.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_generators_hpp">Header &lt;boost/date_time/date_generators.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_iterator_hpp">Header &lt;boost/date_time/date_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_names_put_hpp">Header &lt;boost/date_time/date_names_put.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.date_parsing_hpp">Header &lt;boost/date_time/date_parsing.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.dst_rules_hpp">Header &lt;boost/date_time/dst_rules.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.dst_transition_generators_hpp">Header &lt;boost/date_time/dst_transition_generators.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.filetime_functions_hpp">Header &lt;boost/date_time/filetime_functions.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.find_match_hpp">Header &lt;boost/date_time/find_match.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.format_date_parser_hpp">Header &lt;boost/date_time/format_date_parser.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian_calendar_hpp">Header &lt;boost/date_time/gregorian_calendar.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.int_adapter_hpp">Header &lt;boost/date_time/int_adapter.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.iso_format_hpp">Header &lt;boost/date_time/iso_format.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.local_time_adjustor_hpp">Header &lt;boost/date_time/local_time_adjustor.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.local_timezone_defs_hpp">Header &lt;boost/date_time/local_timezone_defs.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.microsec_time_clock_hpp">Header &lt;boost/date_time/microsec_time_clock.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.parse_format_base_hpp">Header &lt;boost/date_time/parse_format_base.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.period_hpp">Header &lt;boost/date_time/period.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.period_formatter_hpp">Header &lt;boost/date_time/period_formatter.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.period_parser_hpp">Header &lt;boost/date_time/period_parser.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.special_defs_hpp">Header &lt;boost/date_time/special_defs.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.special_values_formatter_hpp">Header &lt;boost/date_time/special_values_formatter.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.special_values_parser_hpp">Header &lt;boost/date_time/special_values_parser.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.string_convert_hpp">Header &lt;boost/date_time/string_convert.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.string_parse_tree_hpp">Header &lt;boost/date_time/string_parse_tree.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.strings_from_facet_hpp">Header &lt;boost/date_time/strings_from_facet.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.time_hpp">Header &lt;boost/date_time/time.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.time_clock_hpp">Header &lt;boost/date_time/time_clock.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.time_defs_hpp">Header &lt;boost/date_time/time_defs.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.time_duration_hpp">Header &lt;boost/date_time/time_duration.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.time_facet_hpp">Header &lt;boost/date_time/time_facet.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.time_formatting_streams_hpp">Header &lt;boost/date_time/time_formatting_streams.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.time_iterator_hpp">Header &lt;boost/date_time/time_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.time_parsing_hpp">Header &lt;boost/date_time/time_parsing.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.time_resolution_traits_hpp">Header &lt;boost/date_time/time_resolution_traits.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.time_system_counted_hpp">Header &lt;boost/date_time/time_system_counted.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.time_system_split_hpp">Header &lt;boost/date_time/time_system_split.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.time_zone_base_hpp">Header &lt;boost/date_time/time_zone_base.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.time_zone_names_hpp">Header &lt;boost/date_time/time_zone_names.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.tz_db_base_hpp">Header &lt;boost/date_time/tz_db_base.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.wrapping_int_hpp">Header &lt;boost/date_time/wrapping_int.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.year_month_day_hpp">Header &lt;boost/date_time/year_month_day.hpp&gt;</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.adjust_functors_hpp"></a>Header &lt;<a href="../../../boost/date_time/adjust_functors.hpp" target="_top">boost/date_time/adjust_functors.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/day_functor.html" title="Class template day_functor">day_functor</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/month_functor.html" title="Class template month_functor">month_functor</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/week_functor.html" title="Class template week_functor">week_functor</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/year_functor.html" title="Class template year_functor">year_functor</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.c_local_time_adjustor_hpp"></a>Header &lt;<a href="../../../boost/date_time/c_local_time_adjustor.hpp" target="_top">boost/date_time/c_local_time_adjustor.hpp</a>&gt;</h4></div></div></div>
<p>Time adjustment calculations based on machine </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/c_local_adjustor.html" title="Class template c_local_adjustor">c_local_adjustor</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.c_time_hpp"></a>Header &lt;<a href="../../../boost/date_time/c_time.hpp" target="_top">boost/date_time/c_time.hpp</a>&gt;</h4></div></div></div>
<p>Provide workarounds related to the ctime header </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">std</span> <span class="special">{</span>
<span class="special">}</span><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/date_time/c_time.html" title="Struct c_time">c_time</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.compiler_config_hpp"></a>Header &lt;<a href="../../../boost/date_time/compiler_config.hpp" target="_top">boost/date_time/compiler_config.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.constrained_value_hpp"></a>Header &lt;<a href="../../../boost/date_time/constrained_value.hpp" target="_top">boost/date_time/constrained_value.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">CV</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> value_policies<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/CV/constrained_value.html" title="Class template constrained_value">constrained_value</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> rep_type<span class="special">,</span> <span class="identifier">rep_type</span> min_value<span class="special">,</span> <span class="identifier">rep_type</span> max_value<span class="special">,</span> 
             <span class="keyword">typename</span> exception_type<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/CV/simple_exception_policy.html" title="Class template simple_exception_policy">simple_exception_policy</a><span class="special">;</span>

    <span class="comment">// Represent a min or max violation type. </span>
    <span class="keyword">enum</span> <a name="boost.CV.violation_enum"></a>violation_enum <span class="special">{</span> min_violation, max_violation <span class="special">}</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_hpp"></a>Header &lt;<a href="../../../boost/date_time/date.hpp" target="_top">boost/date_time/date.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> calendar<span class="special">,</span> <span class="keyword">typename</span> duration_type_<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/date.html" title="Class template date">date</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_clock_device_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_clock_device.hpp" target="_top">boost/date_time/date_clock_device.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/day_clock.html" title="Class template day_clock">day_clock</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_defs_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_defs.hpp" target="_top">boost/date_time/date_defs.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>

    <span class="comment">// An enumeration of weekday names. </span>
    <span class="keyword">enum</span> <a name="boost.date_time.weekdays"></a>weekdays <span class="special">{</span> Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, 
                    Saturday <span class="special">}</span><span class="special">;</span>

    <span class="comment">// Simple enum to allow for nice programming with Jan, Feb, etc. </span>
    <span class="keyword">enum</span> <a name="boost.date_time.months_of_year"></a>months_of_year <span class="special">{</span> Jan = =1, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, 
                          Oct, Nov, Dec, NotAMonth, NumMonths <span class="special">}</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_duration_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_duration.hpp" target="_top">boost/date_time/date_duration.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> duration_rep_traits<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/date_duration.html" title="Class template date_duration">date_duration</a><span class="special">;</span>

    <span class="keyword">struct</span> <a class="link" href="../boost/date_time/duration_traits_adapted.html" title="Struct duration_traits_adapted">duration_traits_adapted</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/date_time/duration_traits_long.html" title="Struct duration_traits_long">duration_traits_long</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_duration_types_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_duration_types.hpp" target="_top">boost/date_time/date_duration_types.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> base_config<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/months_duration.html" title="Class template months_duration">months_duration</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> duration_config<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/weeks_duration.html" title="Class template weeks_duration">weeks_duration</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> base_config<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/years_duration.html" title="Class template years_duration">years_duration</a><span class="special">;</span>

    <span class="keyword">class</span> <span class="identifier">BOOST_SYMBOL_VISIBLE</span> <a class="link" href="../boost/date_time/years_duration.html" title="Class template years_duration">years_duration</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_facet_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_facet.hpp" target="_top">boost/date_time/date_facet.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> CharT<span class="special">,</span> 
             <span class="keyword">typename</span> OutItrT <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostreambuf_iterator</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/date_facet.html" title="Class template date_facet">date_facet</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> CharT<span class="special">,</span> 
             <span class="keyword">typename</span> InItrT <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">istreambuf_iterator</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/date_input_facet.html" title="Class template date_input_facet">date_input_facet</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_format_simple_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_format_simple.hpp" target="_top">boost/date_time/date_format_simple.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/simple_format.html" title="Class template simple_format">simple_format</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/simpl_1_3_12_15_3_13_1_1_2.html" title="Class simple_format&lt;wchar_t&gt;">simple_format</a><span class="special">&lt;</span><span class="keyword">wchar_t</span><span class="special">&gt;</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_formatting_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_formatting.hpp" target="_top">boost/date_time/date_formatting.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_formatting_limited_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_formatting_limited.hpp" target="_top">boost/date_time/date_formatting_limited.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> format_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/date_formatter.html" title="Class template date_formatter">date_formatter</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> month_type<span class="special">,</span> <span class="keyword">typename</span> format_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/month_formatter.html" title="Class template month_formatter">month_formatter</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ymd_type<span class="special">,</span> <span class="keyword">typename</span> format_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/ymd_formatter.html" title="Class template ymd_formatter">ymd_formatter</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_formatting_locales_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_formatting_locales.hpp" target="_top">boost/date_time/date_formatting_locales.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> facet_type<span class="special">,</span> <span class="keyword">typename</span> charT <span class="special">=</span> <span class="keyword">char</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/ostream_date_formatter.html" title="Class template ostream_date_formatter">ostream_date_formatter</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> facet_type<span class="special">,</span> <span class="keyword">typename</span> charT <span class="special">=</span> <span class="keyword">char</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/ostream_month_formatter.html" title="Class template ostream_month_formatter">ostream_month_formatter</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> weekday_type<span class="special">,</span> <span class="keyword">typename</span> facet_type<span class="special">,</span> 
             <span class="keyword">typename</span> charT <span class="special">=</span> <span class="keyword">char</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/ostream_weekday_formatter.html" title="Class template ostream_weekday_formatter">ostream_weekday_formatter</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ymd_type<span class="special">,</span> <span class="keyword">typename</span> facet_type<span class="special">,</span> <span class="keyword">typename</span> charT <span class="special">=</span> <span class="keyword">char</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/ostream_ymd_formatter.html" title="Class template ostream_ymd_formatter">ostream_ymd_formatter</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_generator_formatter_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_generator_formatter.hpp" target="_top">boost/date_time/date_generator_formatter.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> CharT<span class="special">,</span> 
             <span class="keyword">typename</span> OutItrT <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostreambuf_iterator</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/date_generator_formatter.html" title="Class template date_generator_formatter">date_generator_formatter</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_generator_parser_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_generator_parser.hpp" target="_top">boost/date_time/date_generator_parser.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> charT<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/date_generator_parser.html" title="Class template date_generator_parser">date_generator_parser</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_generators_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_generators.hpp" target="_top">boost/date_time/date_generators.hpp</a>&gt;</h4></div></div></div>
<p>Definition and implementation of date algorithm templates </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/first_kday_after.html" title="Class template first_kday_after">first_kday_after</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/first_kday_before.html" title="Class template first_kday_before">first_kday_before</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/first_kday_of_month.html" title="Class template first_kday_of_month">first_kday_of_month</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/last_kday_of_month.html" title="Class template last_kday_of_month">last_kday_of_month</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/nth_kday_of_month.html" title="Class template nth_kday_of_month">nth_kday_of_month</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/partial_date.html" title="Class template partial_date">partial_date</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/year_based_generator.html" title="Class template year_based_generator">year_based_generator</a><span class="special">;</span>

    <span class="comment">// Returns nth arg as string. 1 -&gt; "first", 2 -&gt; "second", max is 5. </span>
    <span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span> <a name="boost.date_time.nth_as_str"></a><span class="identifier">nth_as_str</span><span class="special">(</span><span class="keyword">int</span> ele<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> weekday_type<span class="special">&gt;</span> 
      <span class="identifier">date_type</span><span class="special">::</span><span class="identifier">duration_type</span> 
      <a class="link" href="../boost/date_time/days_until_weekday.html" title="Function template days_until_weekday"><span class="identifier">days_until_weekday</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">date_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">weekday_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> weekday_type<span class="special">&gt;</span> 
      <span class="identifier">date_type</span><span class="special">::</span><span class="identifier">duration_type</span> 
      <a class="link" href="../boost/date_time/days_before_weekday.html" title="Function template days_before_weekday"><span class="identifier">days_before_weekday</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">date_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">weekday_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> weekday_type<span class="special">&gt;</span> 
      <span class="identifier">date_type</span> <a class="link" href="../boost/date_time/next_weekday.html" title="Function template next_weekday"><span class="identifier">next_weekday</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">date_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">weekday_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> weekday_type<span class="special">&gt;</span> 
      <span class="identifier">date_type</span> <a class="link" href="../boost/date_time/previous_weekday.html" title="Function template previous_weekday"><span class="identifier">previous_weekday</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">date_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">weekday_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_iterator_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_iterator.hpp" target="_top">boost/date_time/date_iterator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> offset_functor<span class="special">,</span> <span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/date_itr.html" title="Class template date_itr">date_itr</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/date_itr_base.html" title="Class template date_itr_base">date_itr_base</a><span class="special">;</span>

    <span class="comment">// An iterator over dates with varying resolution (day, week, month, year, etc) </span>
    <span class="keyword">enum</span> <a name="boost.date_time.date_resolutions"></a>date_resolutions <span class="special">{</span> day, week, months, year, decade, century, 
                            NumDateResolutions <span class="special">}</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_names_put_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_names_put.hpp" target="_top">boost/date_time/date_names_put.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Config<span class="special">,</span> <span class="keyword">typename</span> charT <span class="special">=</span> <span class="keyword">char</span><span class="special">,</span> 
             <span class="keyword">typename</span> <a class="link" href="../OutputIterator.html" title="Concept OutputIterator">OutputIterator</a> <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostreambuf_iterator</span><span class="special">&lt;</span><span class="identifier">charT</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/all_date_names_put.html" title="Class template all_date_names_put">all_date_names_put</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Config<span class="special">,</span> <span class="keyword">typename</span> charT <span class="special">=</span> <span class="keyword">char</span><span class="special">,</span> 
             <span class="keyword">typename</span> <a class="link" href="../OutputIterator.html" title="Concept OutputIterator">OutputIterator</a> <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostreambuf_iterator</span><span class="special">&lt;</span><span class="identifier">charT</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/date_names_put.html" title="Class template date_names_put">date_names_put</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.date_parsing_hpp"></a>Header &lt;<a href="../../../boost/date_time/date_parsing.hpp" target="_top">boost/date_time/date_parsing.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a class="link" href="../boost/date_time/convert_to_lower.html" title="Function convert_to_lower"><span class="identifier">convert_to_lower</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Helper function for parse_date. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> month_type<span class="special">&gt;</span> 
      <span class="keyword">unsigned</span> <span class="keyword">short</span> <a name="boost.date_time.month_str_to_ushort"></a><span class="identifier">month_str_to_ushort</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="keyword">const</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> 
      <span class="identifier">date_type</span> <a class="link" href="../boost/date_time/parse_date.html" title="Function template parse_date"><span class="identifier">parse_date</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">int</span> <span class="special">=</span> <span class="identifier">ymd_order_iso</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Generic function to parse undelimited date (eg: 20020201) </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> 
      <span class="identifier">date_type</span> <a name="boost.date_time.parse_undelimited_date"></a><span class="identifier">parse_undelimited_date</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> iterator_type<span class="special">&gt;</span> 
      <span class="identifier">date_type</span> <a class="link" href="../boost/date_time/from__1_3_12_15_3_22_1_1_5.html" title="Function template from_stream_type"><span class="identifier">from_stream_type</span></a><span class="special">(</span><span class="identifier">iterator_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator_type</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">char</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> iterator_type<span class="special">&gt;</span> 
      <span class="identifier">date_type</span> <a class="link" href="../boost/date_time/from__1_3_12_15_3_22_1_1_6.html" title="Function template from_stream_type"><span class="identifier">from_stream_type</span></a><span class="special">(</span><span class="identifier">iterator_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator_type</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                                 <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> iterator_type<span class="special">&gt;</span> 
      <span class="identifier">date_type</span> <a class="link" href="../boost/date_time/from__1_3_12_15_3_22_1_1_7.html" title="Function template from_stream_type"><span class="identifier">from_stream_type</span></a><span class="special">(</span><span class="identifier">iterator_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator_type</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                                 <span class="keyword">wchar_t</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> iterator_type<span class="special">&gt;</span> 
      <span class="identifier">date_type</span> <a class="link" href="../boost/date_time/from__1_3_12_15_3_22_1_1_8.html" title="Function template from_stream_type"><span class="identifier">from_stream_type</span></a><span class="special">(</span><span class="identifier">iterator_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">iterator_type</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> 
                                 <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// function called by wrapper functions: date_period_from_(w)string() </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <a class="link" href="../boost/date_time/period.html" title="Class template period">period</a><span class="special">&lt;</span> <span class="identifier">date_type</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">date_type</span><span class="special">::</span><span class="identifier">duration_type</span> <span class="special">&gt;</span> 
      <a name="boost.date_time.from_simple_string_type"></a><span class="identifier">from_simple_string_type</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.dst_rules_hpp"></a>Header &lt;<a href="../../../boost/date_time/dst_rules.hpp" target="_top">boost/date_time/dst_rules.hpp</a>&gt;</h4></div></div></div>
<p>Contains template class to provide static dst rule calculations </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> time_duration_type<span class="special">,</span> 
             <span class="keyword">typename</span> dst_traits<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/dst_calc_engine.html" title="Class template dst_calc_engine">dst_calc_engine</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type_<span class="special">,</span> <span class="keyword">typename</span> time_duration_type_<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/dst_calculator.html" title="Class template dst_calculator">dst_calculator</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type_<span class="special">,</span> <span class="keyword">typename</span> time_duration_type_<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/null_dst_rules.html" title="Class template null_dst_rules">null_dst_rules</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type_<span class="special">,</span> <span class="keyword">typename</span> time_duration_type_<span class="special">,</span> 
             <span class="keyword">unsigned</span> <span class="keyword">int</span> dst_start_offset_minutes <span class="special">=</span> <span class="number">120</span><span class="special">,</span> 
             <span class="keyword">short</span> dst_length_minutes <span class="special">=</span> <span class="number">60</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/us_dst_rules.html" title="Class template us_dst_rules">us_dst_rules</a><span class="special">;</span>

    <span class="keyword">enum</span> <a name="boost.date_time.time_is_dst_result"></a>time_is_dst_result <span class="special">{</span> is_not_in_dst, is_in_dst, ambiguous, 
                              invalid_time_label <span class="special">}</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.dst_transition_generators_hpp"></a>Header &lt;<a href="../../../boost/date_time/dst_transition_generators.hpp" target="_top">boost/date_time/dst_transition_generators.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> spec<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/day_calc_dst_rule.html" title="Class template day_calc_dst_rule">day_calc_dst_rule</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/dst_day_calc_rule.html" title="Class template dst_day_calc_rule">dst_day_calc_rule</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.filetime_functions_hpp"></a>Header &lt;<a href="../../../boost/date_time/filetime_functions.hpp" target="_top">boost/date_time/filetime_functions.hpp</a>&gt;</h4></div></div></div>
<p>Function(s) for converting between a FILETIME structure and a time object. This file is only available on systems that have BOOST_HAS_FTIME defined. </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> TimeT<span class="special">,</span> <span class="keyword">typename</span> FileTimeT<span class="special">&gt;</span> 
      <span class="identifier">TimeT</span> <a class="link" href="../boost/date_time/time_from_ftime.html" title="Function template time_from_ftime"><span class="identifier">time_from_ftime</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">FileTimeT</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.find_match_hpp"></a>Header &lt;<a href="../../../boost/date_time/find_match.hpp" target="_top">boost/date_time/find_match.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="keyword">short</span> <a class="link" href="../boost/date_time/find_match.html" title="Function template find_match"><span class="identifier">find_match</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">charT</span> <span class="special">*</span><span class="keyword">const</span> <span class="special">*</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">charT</span> <span class="special">*</span><span class="keyword">const</span> <span class="special">*</span><span class="special">,</span> <span class="keyword">short</span><span class="special">,</span> 
                       <span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.format_date_parser_hpp"></a>Header &lt;<a href="../../../boost/date_time/format_date_parser.hpp" target="_top">boost/date_time/format_date_parser.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">std</span> <span class="special">{</span>
<span class="special">}</span><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> charT<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/format_date_parser.html" title="Class template format_date_parser">format_date_parser</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> int_type<span class="special">,</span> <span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">int_type</span> <a class="link" href="../boost/date_time/fixed_1_3_12_15_3_27_2_1_2.html" title="Function template fixed_string_to_int"><span class="identifier">fixed_string_to_int</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">istreambuf_iterator</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
                                   <span class="identifier">std</span><span class="special">::</span><span class="identifier">istreambuf_iterator</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
                                   <a class="link" href="../boost/date_time/parse_match_result.html" title="Struct template parse_match_result">parse_match_result</a><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
                                   <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">charT</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> int_type<span class="special">,</span> <span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">int_type</span> <a class="link" href="../boost/date_time/fixed_1_3_12_15_3_27_2_1_3.html" title="Function template fixed_string_to_int"><span class="identifier">fixed_string_to_int</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">istreambuf_iterator</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
                                   <span class="identifier">std</span><span class="special">::</span><span class="identifier">istreambuf_iterator</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
                                   <a class="link" href="../boost/date_time/parse_match_result.html" title="Struct template parse_match_result">parse_match_result</a><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
                                   <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> int_type<span class="special">,</span> <span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">int_type</span> <a class="link" href="../boost/date_time/var_string_to_int.html" title="Function template var_string_to_int"><span class="identifier">var_string_to_int</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">istreambuf_iterator</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
                                 <span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">istreambuf_iterator</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
                                 <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian_calendar_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian_calendar.hpp" target="_top">boost/date_time/gregorian_calendar.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ymd_type_<span class="special">,</span> <span class="keyword">typename</span> date_int_type_<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/gregorian_calendar_base.html" title="Class template gregorian_calendar_base">gregorian_calendar_base</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.int_adapter_hpp"></a>Header &lt;<a href="../../../boost/date_time/int_adapter.hpp" target="_top">boost/date_time/int_adapter.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> int_type_<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/int_adapter.html" title="Class template int_adapter">int_adapter</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">,</span> <span class="keyword">typename</span> int_type<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a class="link" href="../boost/date_time/opera_1_3_12_15_3_29_1_1_2.html" title="Function template operator&lt;&lt;"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> 
                 <span class="keyword">const</span> <a class="link" href="../boost/date_time/int_adapter.html" title="Class template int_adapter">int_adapter</a><span class="special">&lt;</span> <span class="identifier">int_type</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.iso_format_hpp"></a>Header &lt;<a href="../../../boost/date_time/iso_format.hpp" target="_top">boost/date_time/iso_format.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/iso_extended_format.html" title="Class template iso_extended_format">iso_extended_format</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/iso_format.html" title="Class template iso_format">iso_format</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/iso_format_base.html" title="Class template iso_format_base">iso_format_base</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/iso_f_1_3_12_15_3_30_1_1_4.html" title="Class iso_format_base&lt;wchar_t&gt;">iso_format_base</a><span class="special">&lt;</span><span class="keyword">wchar_t</span><span class="special">&gt;</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.local_time_adjustor_hpp"></a>Header &lt;<a href="../../../boost/date_time/local_time_adjustor.hpp" target="_top">boost/date_time/local_time_adjustor.hpp</a>&gt;</h4></div></div></div>
<p>Time adjustment calculations for local times </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_type<span class="special">,</span> <span class="keyword">typename</span> dst_rules<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/dynam_1_3_12_15_3_31_2_1_1.html" title="Class template dynamic_local_time_adjustor">dynamic_local_time_adjustor</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_type<span class="special">,</span> <span class="keyword">short</span> utc_offset<span class="special">,</span> <span class="keyword">typename</span> dst_rule<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/local_adjustor.html" title="Class template local_adjustor">local_adjustor</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_type<span class="special">,</span> <span class="keyword">typename</span> dst_rules<span class="special">,</span> 
             <span class="keyword">typename</span> utc_offset_rules<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/static_local_time_adjustor.html" title="Class template static_local_time_adjustor">static_local_time_adjustor</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_duration_type<span class="special">,</span> <span class="keyword">short</span> hours<span class="special">,</span> 
             <span class="keyword">unsigned</span> <span class="keyword">short</span> minutes <span class="special">=</span> <span class="number">0</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/utc_adjustment.html" title="Class template utc_adjustment">utc_adjustment</a><span class="special">;</span>
    <span class="keyword">void</span> <a name="boost.date_time.dummy_to_prevent_msvc6_ice"></a><span class="identifier">dummy_to_prevent_msvc6_ice</span><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.local_timezone_defs_hpp"></a>Header &lt;<a href="../../../boost/date_time/local_timezone_defs.hpp" target="_top">boost/date_time/local_timezone_defs.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/date_time/acst_dst_trait.html" title="Struct template acst_dst_trait">acst_dst_trait</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/date_time/eu_dst_trait.html" title="Struct template eu_dst_trait">eu_dst_trait</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/date_time/uk_dst_trait.html" title="Struct template uk_dst_trait">uk_dst_trait</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/date_time/us_dst_trait.html" title="Struct template us_dst_trait">us_dst_trait</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.microsec_time_clock_hpp"></a>Header &lt;<a href="../../../boost/date_time/microsec_time_clock.hpp" target="_top">boost/date_time/microsec_time_clock.hpp</a>&gt;</h4></div></div></div>
<p>This file contains a high resolution time clock implementation. </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/microsec_clock.html" title="Class template microsec_clock">microsec_clock</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.parse_format_base_hpp"></a>Header &lt;<a href="../../../boost/date_time/parse_format_base.hpp" target="_top">boost/date_time/parse_format_base.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>

    <span class="comment">// Enum for distinguishing parsing and formatting options. </span>
    <span class="keyword">enum</span> <a name="boost.date_time.month_format_spec"></a>month_format_spec <span class="special">{</span> month_as_integer, month_as_short_string, 
                             month_as_long_string <span class="special">}</span><span class="special">;</span>
    <span class="keyword">enum</span> <a class="link" href="../boost/date_time/ymd_order_spec.html" title="Type ymd_order_spec">ymd_order_spec</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.period_hpp"></a>Header &lt;<a href="../../../boost/date_time/period.hpp" target="_top">boost/date_time/period.hpp</a>&gt;</h4></div></div></div>
<p>This file contain the implementation of the period abstraction. This is basically the same idea as a range. Although this class is intended for use in the time library, it is pretty close to general enough for other numeric uses. </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> point_rep<span class="special">,</span> <span class="keyword">typename</span> duration_rep<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/period.html" title="Class template period">period</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.period_formatter_hpp"></a>Header &lt;<a href="../../../boost/date_time/period_formatter.hpp" target="_top">boost/date_time/period_formatter.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> 
             <span class="keyword">typename</span> OutItrT <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostreambuf_iterator</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/period_formatter.html" title="Class template period_formatter">period_formatter</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.period_parser_hpp"></a>Header &lt;<a href="../../../boost/date_time/period_parser.hpp" target="_top">boost/date_time/period_parser.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> CharT<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/period_parser.html" title="Class template period_parser">period_parser</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.special_defs_hpp"></a>Header &lt;<a href="../../../boost/date_time/special_defs.hpp" target="_top">boost/date_time/special_defs.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>

    <span class="keyword">enum</span> <a name="boost.date_time.special_values"></a>special_values <span class="special">{</span> not_a_date_time, neg_infin, pos_infin, 
                          min_date_time, max_date_time, not_special, 
                          NumSpecialValues <span class="special">}</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.special_values_formatter_hpp"></a>Header &lt;<a href="../../../boost/date_time/special_values_formatter.hpp" target="_top">boost/date_time/special_values_formatter.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> 
             <span class="keyword">typename</span> OutItrT <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostreambuf_iterator</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/special_values_formatter.html" title="Class template special_values_formatter">special_values_formatter</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.special_values_parser_hpp"></a>Header &lt;<a href="../../../boost/date_time/special_values_parser.hpp" target="_top">boost/date_time/special_values_parser.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> date_type<span class="special">,</span> <span class="keyword">typename</span> charT<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/special_values_parser.html" title="Class template special_values_parser">special_values_parser</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.string_convert_hpp"></a>Header &lt;<a href="../../../boost/date_time/string_convert.hpp" target="_top">boost/date_time/string_convert.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputT<span class="special">,</span> <span class="keyword">typename</span> OutputT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">OutputT</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/date_time/convert_string_type.html" title="Function template convert_string_type"><span class="identifier">convert_string_type</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">InputT</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.string_parse_tree_hpp"></a>Header &lt;<a href="../../../boost/date_time/string_parse_tree.hpp" target="_top">boost/date_time/string_parse_tree.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/date_time/parse_match_result.html" title="Struct template parse_match_result">parse_match_result</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/date_time/string_parse_tree.html" title="Struct template string_parse_tree">string_parse_tree</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.date_time.opera_1_3_12_15_3_43_1_1_3"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <a class="link" href="../boost/date_time/parse_match_result.html" title="Struct template parse_match_result">parse_match_result</a><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> mr<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.strings_from_facet_hpp"></a>Header &lt;<a href="../../../boost/date_time/strings_from_facet.hpp" target="_top">boost/date_time/strings_from_facet.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/date_time/gather_month_strings.html" title="Function template gather_month_strings"><span class="identifier">gather_month_strings</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">=</span> <span class="keyword">true</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&gt;</span> 
      <a class="link" href="../boost/date_time/gather_weekday_strings.html" title="Function template gather_weekday_strings"><span class="identifier">gather_weekday_strings</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">=</span> <span class="keyword">true</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.time_hpp"></a>Header &lt;<a href="../../../boost/date_time/time.hpp" target="_top">boost/date_time/time.hpp</a>&gt;</h4></div></div></div>
<p>This file contains the interface for the time associated classes. </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> time_system<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/base_time.html" title="Class template base_time">base_time</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.time_clock_hpp"></a>Header &lt;<a href="../../../boost/date_time/time_clock.hpp" target="_top">boost/date_time/time_clock.hpp</a>&gt;</h4></div></div></div>
<p>This file contains the interface for clock devices. </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/second_clock.html" title="Class template second_clock">second_clock</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.time_defs_hpp"></a>Header &lt;<a href="../../../boost/date_time/time_defs.hpp" target="_top">boost/date_time/time_defs.hpp</a>&gt;</h4></div></div></div>
<p>This file contains nice definitions for handling the resoluion of various time reprsentations. </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>

    <span class="comment">// Defines some nice types for handling time level resolutions. </span>
    <span class="keyword">enum</span> <a name="boost.date_time.time_resolutions"></a>time_resolutions <span class="special">{</span> sec, tenth, hundreth, hundredth = = hundreth, 
                            milli, ten_thousandth, micro, nano, 
                            NumResolutions <span class="special">}</span><span class="special">;</span>

    <span class="comment">// Flags for daylight savings or summer time. </span>
    <span class="keyword">enum</span> <a name="boost.date_time.dst_flags"></a>dst_flags <span class="special">{</span> not_dst, is_dst, calculate <span class="special">}</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.time_duration_hpp"></a>Header &lt;<a href="../../../boost/date_time/time_duration.hpp" target="_top">boost/date_time/time_duration.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> base_duration<span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">int64_t</span> frac_of_second<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/subsecond_duration.html" title="Class template subsecond_duration">subsecond_duration</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> rep_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/time_duration.html" title="Class template time_duration">time_duration</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.time_facet_hpp"></a>Header &lt;<a href="../../../boost/date_time/time_facet.hpp" target="_top">boost/date_time/time_facet.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_type<span class="special">,</span> <span class="keyword">typename</span> CharT<span class="special">,</span> 
             <span class="keyword">typename</span> OutItrT <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">ostreambuf_iterator</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/time_facet.html" title="Class template time_facet">time_facet</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/date_time/time_formats.html" title="Struct template time_formats">time_formats</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_type<span class="special">,</span> <span class="keyword">typename</span> CharT<span class="special">,</span> 
             <span class="keyword">typename</span> InItrT <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">istreambuf_iterator</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/time_input_facet.html" title="Class template time_input_facet">time_input_facet</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.time_formatting_streams_hpp"></a>Header &lt;<a href="../../../boost/date_time/time_formatting_streams.hpp" target="_top">boost/date_time/time_formatting_streams.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_duration_type<span class="special">,</span> <span class="keyword">typename</span> charT <span class="special">=</span> <span class="keyword">char</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/ostre_1_3_12_15_3_50_1_1_1.html" title="Class template ostream_time_duration_formatter">ostream_time_duration_formatter</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_type<span class="special">,</span> <span class="keyword">typename</span> charT <span class="special">=</span> <span class="keyword">char</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/ostream_time_formatter.html" title="Class template ostream_time_formatter">ostream_time_formatter</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_period_type<span class="special">,</span> <span class="keyword">typename</span> charT <span class="special">=</span> <span class="keyword">char</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/ostre_1_3_12_15_3_50_1_1_3.html" title="Class template ostream_time_period_formatter">ostream_time_period_formatter</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.time_iterator_hpp"></a>Header &lt;<a href="../../../boost/date_time/time_iterator.hpp" target="_top">boost/date_time/time_iterator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/time_itr.html" title="Class template time_itr">time_itr</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.time_parsing_hpp"></a>Header &lt;<a href="../../../boost/date_time/time_parsing.hpp" target="_top">boost/date_time/time_parsing.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>

    <span class="comment">// computes exponential math like 2^8 =&gt; 256, only works with positive integers </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> int_type<span class="special">&gt;</span> 
      <span class="identifier">int_type</span> <a name="boost.date_time.power"></a><span class="identifier">power</span><span class="special">(</span><span class="identifier">int_type</span> base<span class="special">,</span> <span class="identifier">int_type</span> exponent<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_duration<span class="special">,</span> <span class="keyword">typename</span> char_type<span class="special">&gt;</span> 
      <a class="link" href="../boost/date_time/time_duration.html" title="Class template time_duration">time_duration</a> 
      <a class="link" href="../boost/date_time/str_f_1_3_12_15_3_52_1_1_2.html" title="Function template str_from_delimited_time_duration"><span class="identifier">str_from_delimited_time_duration</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">char_type</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_duration<span class="special">&gt;</span> 
      <a class="link" href="../boost/date_time/time_duration.html" title="Class template time_duration">time_duration</a> <a class="link" href="../boost/date_time/parse_1_3_12_15_3_52_1_1_3.html" title="Function template parse_delimited_time_duration"><span class="identifier">parse_delimited_time_duration</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Utility function to split appart string. </span>
    <span class="keyword">bool</span> <a name="boost.date_time.split"></a><span class="identifier">split</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">,</span> <span class="keyword">char</span> sep<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> first<span class="special">,</span> 
               <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> second<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_type<span class="special">&gt;</span> 
      <span class="identifier">time_type</span> <a name="boost.date_time.parse_delimited_time"></a><span class="identifier">parse_delimited_time</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">,</span> <span class="keyword">char</span> sep<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Parse time duration part of an ISO 8601 time of form: [-]hhmmss<a href="eg:%20120259.123%20is%2012%20hours,%202%20min,%2059%20seconds,%20123000%20microseconds" target="_top">.fff...</a></span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_duration<span class="special">&gt;</span> 
      <a class="link" href="../boost/date_time/time_duration.html" title="Class template time_duration">time_duration</a> <a name="boost.date_time.parse_1_3_12_15_3_52_1_1_6"></a><span class="identifier">parse_undelimited_time_duration</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Parse time string of form YYYYMMDDThhmmss where T is delimeter between date and time. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_type<span class="special">&gt;</span> 
      <span class="identifier">time_type</span> <a name="boost.date_time.parse_iso_time"></a><span class="identifier">parse_iso_time</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">,</span> <span class="keyword">char</span> sep<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.time_resolution_traits_hpp"></a>Header &lt;<a href="../../../boost/date_time/time_resolution_traits.hpp" target="_top">boost/date_time/time_resolution_traits.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> frac_sec_type<span class="special">,</span> <span class="identifier">time_resolutions</span> res<span class="special">,</span> 
             <span class="preprocessor">#if</span><span class="special">(</span><span class="identifier">defined</span><span class="special">(</span><span class="identifier">BOOST_MSVC</span><span class="special">)</span> <span class="special">&amp;&amp;</span><span class="special">(</span><span class="identifier">_MSC_VER</span><span class="special">&lt;</span> <span class="number">1300</span><span class="special">)</span><span class="special">)</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">int64_t</span> resolution_adjust<span class="special">,</span> 
             <span class="preprocessor">#else</span> <span class="keyword">typename</span> <span class="identifier">frac_sec_type</span><span class="special">::</span><span class="identifier">int_type</span> resolution_adjust<span class="special">,</span> 
             <span class="preprocessor">#endif</span> <span class="keyword">unsigned</span> <span class="keyword">short</span> frac_digits<span class="special">,</span> 
             <span class="keyword">typename</span> var_type <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">int64_t</span><span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/time_resolution_traits.html" title="Class template time_resolution_traits">time_resolution_traits</a><span class="special">;</span>

    <span class="keyword">struct</span> <a class="link" href="../boost/date_time/time__1_3_12_15_3_53_1_1_2.html" title="Struct time_resolution_traits_adapted32_impl">time_resolution_traits_adapted32_impl</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/date_time/time__1_3_12_15_3_53_1_1_3.html" title="Struct time_resolution_traits_adapted64_impl">time_resolution_traits_adapted64_impl</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/date_time/time__1_3_12_15_3_53_1_1_4.html" title="Struct time_resolution_traits_bi32_impl">time_resolution_traits_bi32_impl</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/date_time/time__1_3_12_15_3_53_1_1_5.html" title="Struct time_resolution_traits_bi64_impl">time_resolution_traits_bi64_impl</a><span class="special">;</span>

    <span class="keyword">typedef</span> <a class="link" href="../boost/date_time/time_resolution_traits.html" title="Class template time_resolution_traits">time_resolution_traits</a><span class="special">&lt;</span> <a class="link" href="../boost/date_time/time__1_3_12_15_3_53_1_1_2.html" title="Struct time_resolution_traits_adapted32_impl">time_resolution_traits_adapted32_impl</a><span class="special">,</span> <span class="identifier">milli</span><span class="special">,</span> <span class="number">1000</span><span class="special">,</span> <span class="number">3</span> <span class="special">&gt;</span> <a name="boost.date_time.milli_res"></a><span class="identifier">milli_res</span><span class="special">;</span>
    <span class="keyword">typedef</span> <a class="link" href="../boost/date_time/time_resolution_traits.html" title="Class template time_resolution_traits">time_resolution_traits</a><span class="special">&lt;</span> <a class="link" href="../boost/date_time/time__1_3_12_15_3_53_1_1_3.html" title="Struct time_resolution_traits_adapted64_impl">time_resolution_traits_adapted64_impl</a><span class="special">,</span> <span class="identifier">micro</span><span class="special">,</span> <span class="number">1000000</span><span class="special">,</span> <span class="number">6</span> <span class="special">&gt;</span> <a name="boost.date_time.micro_res"></a><span class="identifier">micro_res</span><span class="special">;</span>
    <span class="keyword">typedef</span> <a class="link" href="../boost/date_time/time_resolution_traits.html" title="Class template time_resolution_traits">time_resolution_traits</a><span class="special">&lt;</span> <a class="link" href="../boost/date_time/time__1_3_12_15_3_53_1_1_3.html" title="Struct time_resolution_traits_adapted64_impl">time_resolution_traits_adapted64_impl</a><span class="special">,</span> <span class="identifier">nano</span><span class="special">,</span> <span class="number">1000000000</span><span class="special">,</span> <span class="number">9</span> <span class="special">&gt;</span> <a name="boost.date_time.nano_res"></a><span class="identifier">nano_res</span><span class="special">;</span>

    <span class="comment">// Simple function to calculate absolute value of a numeric type. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <span class="identifier">T</span> <a name="boost.date_time.absolute_value"></a><span class="identifier">absolute_value</span><span class="special">(</span><span class="identifier">T</span> x<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.time_system_counted_hpp"></a>Header &lt;<a href="../../../boost/date_time/time_system_counted.hpp" target="_top">boost/date_time/time_system_counted.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> config<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/date_time/counted_time_rep.html" title="Struct template counted_time_rep">counted_time_rep</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_rep<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/counted_time_system.html" title="Class template counted_time_system">counted_time_system</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.time_system_split_hpp"></a>Header &lt;<a href="../../../boost/date_time/time_system_split.hpp" target="_top">boost/date_time/time_system_split.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> config<span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">int32_t</span> ticks_per_second<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/split_timedate_system.html" title="Class template split_timedate_system">split_timedate_system</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.time_zone_base_hpp"></a>Header &lt;<a href="../../../boost/date_time/time_zone_base.hpp" target="_top">boost/date_time/time_zone_base.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_duration_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/dst_adjustment_offsets.html" title="Class template dst_adjustment_offsets">dst_adjustment_offsets</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_type<span class="special">,</span> <span class="keyword">typename</span> CharT<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/time_zone_base.html" title="Class template time_zone_base">time_zone_base</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.time_zone_names_hpp"></a>Header &lt;<a href="../../../boost/date_time/time_zone_names.hpp" target="_top">boost/date_time/time_zone_names.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/date_time/default_zone_names.html" title="Struct template default_zone_names">default_zone_names</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/time_zone_names_base.html" title="Class template time_zone_names_base">time_zone_names_base</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.tz_db_base_hpp"></a>Header &lt;<a href="../../../boost/date_time/tz_db_base.hpp" target="_top">boost/date_time/tz_db_base.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/date_time/bad_field_count.html" title="Class bad_field_count">bad_field_count</a><span class="special">;</span>
    <span class="keyword">class</span> <a class="link" href="../boost/date_time/data_not_accessible.html" title="Class data_not_accessible">data_not_accessible</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> time_zone_type<span class="special">,</span> <span class="keyword">typename</span> rule_type<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/tz_db_base.html" title="Class template tz_db_base">tz_db_base</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.wrapping_int_hpp"></a>Header &lt;<a href="../../../boost/date_time/wrapping_int.hpp" target="_top">boost/date_time/wrapping_int.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> int_type_<span class="special">,</span> <span class="identifier">int_type_</span> wrap_val<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/date_time/wrapping_int.html" title="Class template wrapping_int">wrapping_int</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> int_type_<span class="special">,</span> <span class="identifier">int_type_</span> wrap_min<span class="special">,</span> <span class="identifier">int_type_</span> wrap_max<span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/date_time/wrapping_int2.html" title="Class template wrapping_int2">wrapping_int2</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.year_month_day_hpp"></a>Header &lt;<a href="../../../boost/date_time/year_month_day.hpp" target="_top">boost/date_time/year_month_day.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">date_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> YearType<span class="special">,</span> <span class="keyword">typename</span> MonthType<span class="special">,</span> <span class="keyword">typename</span> DayType<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/date_time/year_month_day_base.html" title="Struct template year_month_day_base">year_month_day_base</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="gregorian_reference"></a>Gregorian Reference</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.conversion_hpp">Header &lt;boost/date_time/gregorian/conversion.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.formatters_hpp">Header &lt;boost/date_time/gregorian/formatters.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.formatters_limited_hpp">Header &lt;boost/date_time/gregorian/formatters_limited.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.greg_calendar_hpp">Header &lt;boost/date_time/gregorian/greg_calendar.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.greg_date_hpp">Header &lt;boost/date_time/gregorian/greg_date.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.greg_day_hpp">Header &lt;boost/date_time/gregorian/greg_day.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.greg_day_of_year_hpp">Header &lt;boost/date_time/gregorian/greg_day_of_year.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.greg_duration_hpp">Header &lt;boost/date_time/gregorian/greg_duration.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.greg_duration_types_hpp">Header &lt;boost/date_time/gregorian/greg_duration_types.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.greg_facet_hpp">Header &lt;boost/date_time/gregorian/greg_facet.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.greg_month_hpp">Header &lt;boost/date_time/gregorian/greg_month.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.greg_serialize_hpp">Header &lt;boost/date_time/gregorian/greg_serialize.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.greg_weekday_hpp">Header &lt;boost/date_time/gregorian/greg_weekday.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.greg_year_hpp">Header &lt;boost/date_time/gregorian/greg_year.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.greg_ymd_hpp">Header &lt;boost/date_time/gregorian/greg_ymd.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.gregorian_hpp">Header &lt;boost/date_time/gregorian/gregorian.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.gregorian_io_hpp">Header &lt;boost/date_time/gregorian/gregorian_io.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.gregorian_types_hpp">Header &lt;boost/date_time/gregorian/gregorian_types.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.gregorian.parsers_hpp">Header &lt;boost/date_time/gregorian/parsers.hpp&gt;</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.conversion_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/conversion.hpp" target="_top">boost/date_time/gregorian/conversion.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>

    <span class="comment">// Converts a date to a tm struct. Throws out_of_range exception if date is a special value. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">tm</span> <a name="boost.gregorian.to_tm"></a><span class="identifier">to_tm</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Converts a tm structure into a date dropping the any time values. </span>
    <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <a name="boost.gregorian.date_from_tm"></a><span class="identifier">date_from_tm</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">tm</span> <span class="special">&amp;</span> datetm<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.formatters_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/formatters.hpp" target="_top">boost/date_time/gregorian/formatters.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <a name="boost.gregorian.to_sim_1_3_12_15_4_3_1_1_1"></a><span class="identifier">to_simple_string_type</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <a name="boost.gregorian.to_sim_1_3_12_15_4_3_1_1_2"></a><span class="identifier">to_simple_string_type</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">date_period</span> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <a name="boost.gregorian.to_iso_1_3_12_15_4_3_1_1_3"></a><span class="identifier">to_iso_string_type</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">date_period</span> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <a name="boost.gregorian.to_iso_1_3_12_15_4_3_1_1_4"></a><span class="identifier">to_iso_extended_string_type</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <a name="boost.gregorian.to_iso_1_3_12_15_4_3_1_1_5"></a><span class="identifier">to_iso_string_type</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <a name="boost.gregorian.to_sql_string_type"></a><span class="identifier">to_sql_string_type</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert date period to simple string. Example: [2002-Jan-01/2002-Jan-02]. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <a name="boost.gregorian.to_sim_1_3_12_15_4_3_1_1_7"></a><span class="identifier">to_simple_wstring</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">date_period</span> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>

    <span class="comment">// To YYYY-mmm-DD string where mmm 3 char month name. Example: 2002-Jan-01. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <a name="boost.gregorian.to_sim_1_3_12_15_4_3_1_1_8"></a><span class="identifier">to_simple_wstring</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Date period to iso standard format CCYYMMDD/CCYYMMDD. Example: 20021225/20021231. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <a name="boost.gregorian.to_iso_1_3_12_15_4_3_1_1_9"></a><span class="identifier">to_iso_wstring</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">date_period</span> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert to iso extended format string CCYY-MM-DD. Example 2002-12-31. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <a name="boost.gregorian.to_iso_extended_wstring"></a><span class="identifier">to_iso_extended_wstring</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert to iso standard string YYYYMMDD. Example: 20021231. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <a name="boost.gregorian.to_is_1_3_12_15_4_3_1_1_11"></a><span class="identifier">to_iso_wstring</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <a name="boost.gregorian.to_sql_wstring"></a><span class="identifier">to_sql_wstring</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.formatters_limited_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/formatters_limited.hpp" target="_top">boost/date_time/gregorian/formatters_limited.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>

    <span class="comment">// To YYYY-mmm-DD string where mmm 3 char month name. Example: 2002-Jan-01. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a name="boost.gregorian.to_sim_1_3_12_15_4_4_1_1_1"></a><span class="identifier">to_simple_string</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert date period to simple string. Example: [2002-Jan-01/2002-Jan-02]. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a name="boost.gregorian.to_sim_1_3_12_15_4_4_1_1_2"></a><span class="identifier">to_simple_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">date_period</span> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Date period to ISO 8601 standard format CCYYMMDD/CCYYMMDD. Example: 20021225/20021231. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a name="boost.gregorian.to_iso_1_3_12_15_4_4_1_1_3"></a><span class="identifier">to_iso_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">date_period</span> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert to ISO 8601 extended format string CCYY-MM-DD. Example 2002-12-31. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a name="boost.gregorian.to_iso_extended_string"></a><span class="identifier">to_iso_extended_string</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a name="boost.gregorian.to_sql_string"></a><span class="identifier">to_sql_string</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.greg_calendar_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/greg_calendar.hpp" target="_top">boost/date_time/gregorian/greg_calendar.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/gregorian/gregorian_calendar.html" title="Class gregorian_calendar">gregorian_calendar</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">int_adapter</span><span class="special">&lt;</span> <span class="identifier">uint32_t</span> <span class="special">&gt;</span> <a name="boost.gregorian.fancy_date_rep"></a><span class="identifier">fancy_date_rep</span><span class="special">;</span>  <span class="comment">// An internal date representation that includes infinities, not a date. </span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.greg_date_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/greg_date.hpp" target="_top">boost/date_time/gregorian/greg_date.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a><span class="special">;</span>
    <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <span class="keyword">bool</span> <a name="boost.gregorian.operator=="></a><span class="keyword">operator</span><span class="special">==</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> lhs<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> rhs<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.greg_day_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/greg_day.hpp" target="_top">boost/date_time/gregorian/greg_day.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/gregorian/bad_day_of_month.html" title="Struct bad_day_of_month">bad_day_of_month</a><span class="special">;</span>

    <span class="keyword">class</span> <a class="link" href="../boost/gregorian/greg_day.html" title="Class greg_day">greg_day</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">CV</span><span class="special">::</span><span class="identifier">simple_exception_policy</span><span class="special">&lt;</span> <span class="keyword">unsigned</span> <span class="keyword">short</span><span class="special">,</span> <span class="number">1</span><span class="special">,</span> <span class="number">31</span><span class="special">,</span> <a class="link" href="../boost/gregorian/bad_day_of_month.html" title="Struct bad_day_of_month">bad_day_of_month</a> <span class="special">&gt;</span> <a name="boost.gregorian.greg_day_policies"></a><span class="identifier">greg_day_policies</span><span class="special">;</span>  <span class="comment">// Policy class that declares error handling and day of month ranges. </span>
    <span class="keyword">typedef</span> <span class="identifier">CV</span><span class="special">::</span><span class="identifier">constrained_value</span><span class="special">&lt;</span> <span class="identifier">greg_day_policies</span> <span class="special">&gt;</span> <a name="boost.gregorian.greg_day_rep"></a><span class="identifier">greg_day_rep</span><span class="special">;</span>  <span class="comment">// Generated represetation for gregorian day of month. </span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.greg_day_of_year_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/greg_day_of_year.hpp" target="_top">boost/date_time/gregorian/greg_day_of_year.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/gregorian/bad_day_of_year.html" title="Struct bad_day_of_year">bad_day_of_year</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">CV</span><span class="special">::</span><span class="identifier">simple_exception_policy</span><span class="special">&lt;</span> <span class="keyword">unsigned</span> <span class="keyword">short</span><span class="special">,</span> <span class="number">1</span><span class="special">,</span> <span class="number">366</span><span class="special">,</span> <a class="link" href="../boost/gregorian/bad_day_of_year.html" title="Struct bad_day_of_year">bad_day_of_year</a> <span class="special">&gt;</span> <a name="boost.gregorian.greg_day_of_year_policies"></a><span class="identifier">greg_day_of_year_policies</span><span class="special">;</span>  <span class="comment">// A day of the year range (1..366) </span>
    <span class="keyword">typedef</span> <span class="identifier">CV</span><span class="special">::</span><span class="identifier">constrained_value</span><span class="special">&lt;</span> <span class="identifier">greg_day_of_year_policies</span> <span class="special">&gt;</span> <a name="boost.gregorian.greg_day_of_year_rep"></a><span class="identifier">greg_day_of_year_rep</span><span class="special">;</span>  <span class="comment">// Define a range representation type for the day of the year 1..366. </span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.greg_duration_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/greg_duration.hpp" target="_top">boost/date_time/gregorian/greg_duration.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">duration_traits_adapted</span> <a name="boost.gregorian.date_duration_rep"></a><span class="identifier">date_duration_rep</span><span class="special">;</span>  <span class="comment">// An internal date representation that includes infinities, not a date. </span>
    <span class="keyword">typedef</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a> <a name="boost.gregorian.days"></a><span class="identifier">days</span><span class="special">;</span>  <span class="comment">// Shorthand for <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a>. </span>
    <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a> 
    <a name="boost.gregorian.operator-"></a><span class="keyword">operator</span><span class="special">-</span><span class="special">(</span><a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a> rhs<span class="special">,</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a> <span class="keyword">const</span> <span class="special">&amp;</span> lhs<span class="special">)</span><span class="special">;</span>
    <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a> 
    <a name="boost.gregorian.operator+"></a><span class="keyword">operator</span><span class="special">+</span><span class="special">(</span><a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a> rhs<span class="special">,</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a> <span class="keyword">const</span> <span class="special">&amp;</span> lhs<span class="special">)</span><span class="special">;</span>
    <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a> <a name="boost.gregorian.operator/"></a><span class="keyword">operator</span><span class="special">/</span><span class="special">(</span><a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a> rhs<span class="special">,</span> <span class="keyword">int</span> lhs<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.greg_duration_types_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/greg_duration_types.hpp" target="_top">boost/date_time/gregorian/greg_duration_types.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/gregorian/greg_durations_config.html" title="Struct greg_durations_config">greg_durations_config</a><span class="special">;</span>

    <span class="keyword">class</span> <a class="link" href="../boost/gregorian/weeks_duration.html" title="Class weeks_duration">weeks_duration</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">months_duration</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/greg_durations_config.html" title="Struct greg_durations_config">greg_durations_config</a> <span class="special">&gt;</span> <a name="boost.gregorian.months"></a><span class="identifier">months</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">years_duration</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/greg_durations_config.html" title="Struct greg_durations_config">greg_durations_config</a> <span class="special">&gt;</span> <a name="boost.gregorian.years"></a><span class="identifier">years</span><span class="special">;</span>
    <span class="keyword">typedef</span> <a class="link" href="../boost/gregorian/weeks_duration.html" title="Class weeks_duration">weeks_duration</a> <a name="boost.gregorian.weeks"></a><span class="identifier">weeks</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.greg_facet_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/greg_facet.hpp" target="_top">boost/date_time/gregorian/greg_facet.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/gregorian/greg_facet_config.html" title="Struct greg_facet_config">greg_facet_config</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">date_names_put</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/greg_facet_config.html" title="Struct greg_facet_config">greg_facet_config</a> <span class="special">&gt;</span> <a name="boost.gregorian.greg_base_facet"></a><span class="identifier">greg_base_facet</span><span class="special">;</span>  <span class="comment">// Create the base facet type for <a class="link" href="../boost/gregorian/date.html" title="Class date">gregorian::date</a>. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a class="link" href="../boost/gregorian/opera_1_3_12_15_4_11_1_1_3.html" title="Function template operator&lt;&lt;"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a class="link" href="../boost/gregorian/opera_1_3_12_15_4_11_1_1_4.html" title="Function template operator&lt;&lt;"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/gregorian/greg_month.html" title="Class greg_month">greg_month</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a class="link" href="../boost/gregorian/opera_1_3_12_15_4_11_1_1_5.html" title="Function template operator&lt;&lt;"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/gregorian/greg_weekday.html" title="Class greg_weekday">greg_weekday</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a class="link" href="../boost/gregorian/opera_1_3_12_15_4_11_1_1_6.html" title="Function template operator&lt;&lt;"><span class="keyword">operator</span><span class="special">&lt;&lt;</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">date_period</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.opera_1_3_12_15_4_11_1_1_7"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a> <span class="special">&amp;</span> dd<span class="special">)</span><span class="special">;</span>

    <span class="comment">// operator&lt;&lt; for gregorian::partial_date. Output: "Jan 1" </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.opera_1_3_12_15_4_11_1_1_8"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">partial_date</span> <span class="special">&amp;</span> pd<span class="special">)</span><span class="special">;</span>

    <span class="comment">// operator&lt;&lt; for gregorian::nth_kday_of_month. Output: "first Mon of Jun" </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.opera_1_3_12_15_4_11_1_1_9"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">nth_kday_of_month</span> <span class="special">&amp;</span> nkd<span class="special">)</span><span class="special">;</span>

    <span class="comment">// operator&lt;&lt; for gregorian::first_kday_of_month. Output: "first Mon of Jun" </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_11_1_1_10"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">first_kday_of_month</span> <span class="special">&amp;</span> fkd<span class="special">)</span><span class="special">;</span>

    <span class="comment">// operator&lt;&lt; for gregorian::last_kday_of_month. Output: "last Mon of Jun" </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_11_1_1_11"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">last_kday_of_month</span> <span class="special">&amp;</span> lkd<span class="special">)</span><span class="special">;</span>

    <span class="comment">// operator&lt;&lt; for gregorian::first_kday_after. Output: "first Mon after" </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_11_1_1_12"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">first_kday_after</span> <span class="special">&amp;</span> fka<span class="special">)</span><span class="special">;</span>

    <span class="comment">// operator&lt;&lt; for gregorian::first_kday_before. Output: "first Mon before" </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_11_1_1_13"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">first_kday_before</span> <span class="special">&amp;</span> fkb<span class="special">)</span><span class="special">;</span>

    <span class="comment">// operator&gt;&gt; for <a class="link" href="../boost/gregorian/date.html" title="Class date">gregorian::date</a></span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_11_1_1_14"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>

    <span class="comment">// operator&gt;&gt; for <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">gregorian::date_duration</a></span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_11_1_1_15"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a> <span class="special">&amp;</span> dd<span class="special">)</span><span class="special">;</span>

    <span class="comment">// operator&gt;&gt; for gregorian::date_period </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_11_1_1_16"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <span class="identifier">date_period</span> <span class="special">&amp;</span> dp<span class="special">)</span><span class="special">;</span>

    <span class="comment">// generates a locale with the set of gregorian name-strings of type char* </span>
    <span class="identifier">BOOST_DATE_TIME_DECL</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span> 
    <a name="boost.gregorian.gene_1_3_12_15_4_11_1_1_17"></a><span class="identifier">generate_locale</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span> <span class="special">&amp;</span> loc<span class="special">,</span> <span class="keyword">char</span> type<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Returns a pointer to a facet with a default set of names (English) </span>
    <span class="identifier">BOOST_DATE_TIME_DECL</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">all_date_names_put</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/greg_facet_config.html" title="Struct greg_facet_config">greg_facet_config</a><span class="special">,</span> <span class="keyword">char</span> <span class="special">&gt;</span> <span class="special">*</span> 
    <a name="boost.gregorian.crea_1_3_12_15_4_11_1_1_18"></a><span class="identifier">create_facet_def</span><span class="special">(</span><span class="keyword">char</span> type<span class="special">)</span><span class="special">;</span>

    <span class="comment">// generates a locale with the set of gregorian name-strings of type wchar_t* </span>
    <span class="identifier">BOOST_DATE_TIME_DECL</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span> 
    <a name="boost.gregorian.gene_1_3_12_15_4_11_1_1_19"></a><span class="identifier">generate_locale</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">locale</span> <span class="special">&amp;</span> loc<span class="special">,</span> <span class="keyword">wchar_t</span> type<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Returns a pointer to a facet with a default set of names (English) </span>
    <span class="identifier">BOOST_DATE_TIME_DECL</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">all_date_names_put</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/greg_facet_config.html" title="Struct greg_facet_config">greg_facet_config</a><span class="special">,</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <span class="special">*</span> 
    <a name="boost.gregorian.crea_1_3_12_15_4_11_1_1_20"></a><span class="identifier">create_facet_def</span><span class="special">(</span><span class="keyword">wchar_t</span> type<span class="special">)</span><span class="special">;</span>

    <span class="comment">// operator&gt;&gt; for <a class="link" href="../boost/gregorian/greg_month.html" title="Class greg_month">gregorian::greg_month</a> - throws exception if invalid month given </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_11_1_1_21"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/gregorian/greg_month.html" title="Class greg_month">greg_month</a> <span class="special">&amp;</span> m<span class="special">)</span><span class="special">;</span>

    <span class="comment">// operator&gt;&gt; for <a class="link" href="../boost/gregorian/greg_weekday.html" title="Class greg_weekday">gregorian::greg_weekday</a> - throws exception if invalid weekday given </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_11_1_1_22"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/gregorian/greg_weekday.html" title="Class greg_weekday">greg_weekday</a> <span class="special">&amp;</span> wd<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.greg_month_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/greg_month.hpp" target="_top">boost/date_time/gregorian/greg_month.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/gregorian/bad_month.html" title="Struct bad_month">bad_month</a><span class="special">;</span>

    <span class="keyword">class</span> <a class="link" href="../boost/gregorian/greg_month.html" title="Class greg_month">greg_month</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">months_of_year</span> <a name="boost.gregorian.months_of_year"></a><span class="identifier">months_of_year</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">CV</span><span class="special">::</span><span class="identifier">simple_exception_policy</span><span class="special">&lt;</span> <span class="keyword">unsigned</span> <span class="keyword">short</span><span class="special">,</span> <span class="number">1</span><span class="special">,</span> <span class="number">12</span><span class="special">,</span> <a class="link" href="../boost/gregorian/bad_month.html" title="Struct bad_month">bad_month</a> <span class="special">&gt;</span> <a name="boost.gregorian.greg_month_policies"></a><span class="identifier">greg_month_policies</span><span class="special">;</span>  <span class="comment">// Build a policy class for the greg_month_rep. </span>
    <span class="keyword">typedef</span> <span class="identifier">CV</span><span class="special">::</span><span class="identifier">constrained_value</span><span class="special">&lt;</span> <span class="identifier">greg_month_policies</span> <span class="special">&gt;</span> <a name="boost.gregorian.greg_month_rep"></a><span class="identifier">greg_month_rep</span><span class="special">;</span>  <span class="comment">// A constrained range that implements the gregorian_month rules. </span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.greg_serialize_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/greg_serialize.hpp" target="_top">boost/date_time/gregorian/greg_serialize.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>

    <span class="comment">// Convert to ISO 8601 standard string YYYYMMDD. Example: 20021231. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a name="boost.gregorian.to_is_1_3_12_15_4_13_1_1_1"></a><span class="identifier">to_iso_string</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
  <span class="keyword">namespace</span> <span class="identifier">serialization</span> <span class="special">{</span>
     <a class="link" href="../boost/serialization/BOOST_1_3_12_15_4_13_1_2_1.html" title="Function BOOST_DATE_TIME_SPLIT_FREE"><span class="identifier">BOOST_DATE_TIME_SPLIT_FREE</span></a><span class="special">(</span><span class="special">::</span><a class="link" href="../boost/gregorian/date.html" title="Class date">boost::gregorian::date</a><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/load_1_3_12_15_4_13_1_2_2.html" title="Function template load"><span class="identifier">load</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="special">::</span><a class="link" href="../boost/gregorian/date.html" title="Class date">boost::gregorian::date</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load__1_3_12_15_4_13_1_2_3"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="special">::</span><a class="link" href="../boost/gregorian/date.html" title="Class date">boost::gregorian::date</a> <span class="special">*</span> dp<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to save <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">gregorian::date_duration</a> objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.save_1_3_12_15_4_13_1_2_4"></a><span class="identifier">save</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">gregorian::date_duration</a> <span class="special">&amp;</span> dd<span class="special">,</span> 
                <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to load <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">gregorian::date_duration</a> objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_5"></a><span class="identifier">load</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">gregorian::date_duration</a> <span class="special">&amp;</span> dd<span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load__1_3_12_15_4_13_1_2_6"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">gregorian::date_duration</a> <span class="special">*</span> dd<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// helper unction to save date_duration objects using serialization lib </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.save_1_3_12_15_4_13_1_2_7"></a><span class="identifier">save</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> 
                <span class="keyword">const</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">date_duration</span><span class="special">::</span><span class="identifier">duration_rep</span> <span class="special">&amp;</span> dr<span class="special">,</span> 
                <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// helper function to load date_duration objects using serialization lib </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_8"></a><span class="identifier">load</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">date_duration</span><span class="special">::</span><span class="identifier">duration_rep</span> <span class="special">&amp;</span> dr<span class="special">,</span> 
                <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load__1_3_12_15_4_13_1_2_9"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> 
                               <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">date_duration</span><span class="special">::</span><span class="identifier">duration_rep</span> <span class="special">*</span> dr<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/save_1_3_12_15_4_13_1_2_10.html" title="Function template save"><span class="identifier">save</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">date_period</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/load_1_3_12_15_4_13_1_2_11.html" title="Function template load"><span class="identifier">load</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">date_period</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_12"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">date_period</span> <span class="special">*</span> dp<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to save <a class="link" href="../boost/gregorian/greg_year.html" title="Class greg_year">gregorian::greg_year</a> objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.save_1_3_12_15_4_13_1_2_13"></a><span class="identifier">save</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/gregorian/greg_year.html" title="Class greg_year">gregorian::greg_year</a> <span class="special">&amp;</span> gy<span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to load <a class="link" href="../boost/gregorian/greg_year.html" title="Class greg_year">gregorian::greg_year</a> objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_14"></a><span class="identifier">load</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <a class="link" href="../boost/gregorian/greg_year.html" title="Class greg_year">gregorian::greg_year</a> <span class="special">&amp;</span> gy<span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_15"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <a class="link" href="../boost/gregorian/greg_year.html" title="Class greg_year">gregorian::greg_year</a> <span class="special">*</span> gy<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to save <a class="link" href="../boost/gregorian/greg_month.html" title="Class greg_month">gregorian::greg_month</a> objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.save_1_3_12_15_4_13_1_2_16"></a><span class="identifier">save</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/gregorian/greg_month.html" title="Class greg_month">gregorian::greg_month</a> <span class="special">&amp;</span> gm<span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to load <a class="link" href="../boost/gregorian/greg_month.html" title="Class greg_month">gregorian::greg_month</a> objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_17"></a><span class="identifier">load</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <a class="link" href="../boost/gregorian/greg_month.html" title="Class greg_month">gregorian::greg_month</a> <span class="special">&amp;</span> gm<span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_18"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <a class="link" href="../boost/gregorian/greg_month.html" title="Class greg_month">gregorian::greg_month</a> <span class="special">*</span> gm<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to save <a class="link" href="../boost/gregorian/greg_day.html" title="Class greg_day">gregorian::greg_day</a> objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.save_1_3_12_15_4_13_1_2_19"></a><span class="identifier">save</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/gregorian/greg_day.html" title="Class greg_day">gregorian::greg_day</a> <span class="special">&amp;</span> gd<span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to load <a class="link" href="../boost/gregorian/greg_day.html" title="Class greg_day">gregorian::greg_day</a> objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_20"></a><span class="identifier">load</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <a class="link" href="../boost/gregorian/greg_day.html" title="Class greg_day">gregorian::greg_day</a> <span class="special">&amp;</span> gd<span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_21"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <a class="link" href="../boost/gregorian/greg_day.html" title="Class greg_day">gregorian::greg_day</a> <span class="special">*</span> gd<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to save <a class="link" href="../boost/gregorian/greg_weekday.html" title="Class greg_weekday">gregorian::greg_weekday</a> objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.save_1_3_12_15_4_13_1_2_22"></a><span class="identifier">save</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/gregorian/greg_weekday.html" title="Class greg_weekday">gregorian::greg_weekday</a> <span class="special">&amp;</span> gd<span class="special">,</span> 
                <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to load <a class="link" href="../boost/gregorian/greg_weekday.html" title="Class greg_weekday">gregorian::greg_weekday</a> objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_23"></a><span class="identifier">load</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <a class="link" href="../boost/gregorian/greg_weekday.html" title="Class greg_weekday">gregorian::greg_weekday</a> <span class="special">&amp;</span> gd<span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_24"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <a class="link" href="../boost/gregorian/greg_weekday.html" title="Class greg_weekday">gregorian::greg_weekday</a> <span class="special">*</span> gd<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/save_1_3_12_15_4_13_1_2_25.html" title="Function template save"><span class="identifier">save</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">partial_date</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/load_1_3_12_15_4_13_1_2_26.html" title="Function template load"><span class="identifier">load</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">partial_date</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_27"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">partial_date</span> <span class="special">*</span> pd<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/save_1_3_12_15_4_13_1_2_28.html" title="Function template save"><span class="identifier">save</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">nth_kday_of_month</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/load_1_3_12_15_4_13_1_2_29.html" title="Function template load"><span class="identifier">load</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">nth_kday_of_month</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_30"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">nth_kday_of_month</span> <span class="special">*</span> nkd<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/save_1_3_12_15_4_13_1_2_31.html" title="Function template save"><span class="identifier">save</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">first_kday_of_month</span> <span class="special">&amp;</span><span class="special">,</span> 
                <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/load_1_3_12_15_4_13_1_2_32.html" title="Function template load"><span class="identifier">load</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">first_kday_of_month</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_33"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> 
                               <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">first_kday_of_month</span> <span class="special">*</span> fkd<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/save_1_3_12_15_4_13_1_2_34.html" title="Function template save"><span class="identifier">save</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">last_kday_of_month</span> <span class="special">&amp;</span><span class="special">,</span> 
                <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/load_1_3_12_15_4_13_1_2_35.html" title="Function template load"><span class="identifier">load</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">last_kday_of_month</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_36"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">last_kday_of_month</span> <span class="special">*</span> lkd<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to save first_day_of_the_week_before objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.save_1_3_12_15_4_13_1_2_37"></a><span class="identifier">save</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">first_kday_before</span> <span class="special">&amp;</span> fkdb<span class="special">,</span> 
                <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to load first_day_of_the_week_before objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_38"></a><span class="identifier">load</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">first_kday_before</span> <span class="special">&amp;</span> fkdb<span class="special">,</span> 
                <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_39"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">first_kday_before</span> <span class="special">*</span> fkdb<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to save first_day_of_the_week_after objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.save_1_3_12_15_4_13_1_2_40"></a><span class="identifier">save</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">first_kday_after</span> <span class="special">&amp;</span> fkda<span class="special">,</span> 
                <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to load first_day_of_the_week_after objects using serialization lib. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_41"></a><span class="identifier">load</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">first_kday_after</span> <span class="special">&amp;</span> fkda<span class="special">,</span> 
                <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_4_13_1_2_42"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">first_kday_after</span> <span class="special">*</span> fkda<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.greg_weekday_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/greg_weekday.hpp" target="_top">boost/date_time/gregorian/greg_weekday.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/gregorian/bad_weekday.html" title="Struct bad_weekday">bad_weekday</a><span class="special">;</span>

    <span class="keyword">class</span> <a class="link" href="../boost/gregorian/greg_weekday.html" title="Class greg_weekday">greg_weekday</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">CV</span><span class="special">::</span><span class="identifier">simple_exception_policy</span><span class="special">&lt;</span> <span class="keyword">unsigned</span> <span class="keyword">short</span><span class="special">,</span> <span class="number">0</span><span class="special">,</span> <span class="number">6</span><span class="special">,</span> <a class="link" href="../boost/gregorian/bad_weekday.html" title="Struct bad_weekday">bad_weekday</a> <span class="special">&gt;</span> <a name="boost.gregorian.greg_weekday_policies"></a><span class="identifier">greg_weekday_policies</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">CV</span><span class="special">::</span><span class="identifier">constrained_value</span><span class="special">&lt;</span> <span class="identifier">greg_weekday_policies</span> <span class="special">&gt;</span> <a name="boost.gregorian.greg_weekday_rep"></a><span class="identifier">greg_weekday_rep</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.greg_year_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/greg_year.hpp" target="_top">boost/date_time/gregorian/greg_year.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/gregorian/bad_year.html" title="Struct bad_year">bad_year</a><span class="special">;</span>

    <span class="keyword">class</span> <a class="link" href="../boost/gregorian/greg_year.html" title="Class greg_year">greg_year</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">CV</span><span class="special">::</span><span class="identifier">simple_exception_policy</span><span class="special">&lt;</span> <span class="keyword">unsigned</span> <span class="keyword">short</span><span class="special">,</span> <span class="number">1400</span><span class="special">,</span> <span class="number">9999</span><span class="special">,</span> <a class="link" href="../boost/gregorian/bad_year.html" title="Struct bad_year">bad_year</a> <span class="special">&gt;</span> <a name="boost.gregorian.greg_year_policies"></a><span class="identifier">greg_year_policies</span><span class="special">;</span>  <span class="comment">// Policy class that declares error handling gregorian year type. </span>
    <span class="keyword">typedef</span> <span class="identifier">CV</span><span class="special">::</span><span class="identifier">constrained_value</span><span class="special">&lt;</span> <span class="identifier">greg_year_policies</span> <span class="special">&gt;</span> <a name="boost.gregorian.greg_year_rep"></a><span class="identifier">greg_year_rep</span><span class="special">;</span>  <span class="comment">// Generated representation for gregorian year. </span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.greg_ymd_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/greg_ymd.hpp" target="_top">boost/date_time/gregorian/greg_ymd.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">year_month_day_base</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/greg_year.html" title="Class greg_year">greg_year</a><span class="special">,</span> <a class="link" href="../boost/gregorian/greg_month.html" title="Class greg_month">greg_month</a><span class="special">,</span> <a class="link" href="../boost/gregorian/greg_day.html" title="Class greg_day">greg_day</a> <span class="special">&gt;</span> <a name="boost.gregorian.greg_year_month_day"></a><span class="identifier">greg_year_month_day</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.gregorian_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/gregorian.hpp" target="_top">boost/date_time/gregorian/gregorian.hpp</a>&gt;</h4></div></div></div>
<p>Single file header that provides overall include for all elements of the gregorian date-time system. This includes the various types defined, but also other functions for formatting and parsing. </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.gregorian_io_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/gregorian_io.hpp" target="_top">boost/date_time/gregorian/gregorian_io.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">period_formatter</span><span class="special">&lt;</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <a name="boost.gregorian.wperiod_formatter"></a><span class="identifier">wperiod_formatter</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">period_formatter</span><span class="special">&lt;</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.gregorian.period_formatter"></a><span class="identifier">period_formatter</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">date_facet</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a><span class="special">,</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <a name="boost.gregorian.wdate_facet"></a><span class="identifier">wdate_facet</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">date_facet</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a><span class="special">,</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.gregorian.date_facet"></a><span class="identifier">date_facet</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">period_parser</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a><span class="special">,</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.gregorian.period_parser"></a><span class="identifier">period_parser</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">period_parser</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a><span class="special">,</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <a name="boost.gregorian.wperiod_parser"></a><span class="identifier">wperiod_parser</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">special_values_formatter</span><span class="special">&lt;</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.gregorian.special_values_formatter"></a><span class="identifier">special_values_formatter</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">special_values_formatter</span><span class="special">&lt;</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <a name="boost.gregorian.wspecial_values_formatter"></a><span class="identifier">wspecial_values_formatter</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">special_values_parser</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a><span class="special">,</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.gregorian.special_values_parser"></a><span class="identifier">special_values_parser</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">special_values_parser</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a><span class="special">,</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <a name="boost.gregorian.wspecial_values_parser"></a><span class="identifier">wspecial_values_parser</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">date_input_facet</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a><span class="special">,</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.gregorian.date_input_facet"></a><span class="identifier">date_input_facet</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">date_input_facet</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a><span class="special">,</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <a name="boost.gregorian.wdate_input_facet"></a><span class="identifier">wdate_input_facet</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_13"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">boost::gregorian::date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for date </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_14"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&amp;</span> d<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_15"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">boost::gregorian::date_duration</a> <span class="special">&amp;</span> dd<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a></span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_16"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a> <span class="special">&amp;</span> dd<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_17"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">date_period</span> <span class="special">&amp;</span> dp<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for date_period </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_18"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <span class="identifier">date_period</span> <span class="special">&amp;</span> dp<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_19"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <a class="link" href="../boost/gregorian/greg_month.html" title="Class greg_month">boost::gregorian::greg_month</a> <span class="special">&amp;</span> gm<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for <a class="link" href="../boost/gregorian/greg_month.html" title="Class greg_month">greg_month</a></span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_20"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/gregorian/greg_month.html" title="Class greg_month">greg_month</a> <span class="special">&amp;</span> m<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_21"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <a class="link" href="../boost/gregorian/greg_weekday.html" title="Class greg_weekday">boost::gregorian::greg_weekday</a> <span class="special">&amp;</span> gw<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for <a class="link" href="../boost/gregorian/greg_weekday.html" title="Class greg_weekday">greg_weekday</a></span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_22"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/gregorian/greg_weekday.html" title="Class greg_weekday">greg_weekday</a> <span class="special">&amp;</span> wd<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for <a class="link" href="../boost/gregorian/greg_day.html" title="Class greg_day">greg_day</a></span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_23"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/gregorian/greg_day.html" title="Class greg_day">greg_day</a> <span class="special">&amp;</span> gd<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for <a class="link" href="../boost/gregorian/greg_year.html" title="Class greg_year">greg_year</a></span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_24"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/gregorian/greg_year.html" title="Class greg_year">greg_year</a> <span class="special">&amp;</span> gy<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_25"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">partial_date</span> <span class="special">&amp;</span> pd<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for partial_date </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_26"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <span class="identifier">partial_date</span> <span class="special">&amp;</span> pd<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_27"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">nth_day_of_the_week_in_month</span> <span class="special">&amp;</span> nkd<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for nth_day_of_the_week_in_month </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_28"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> 
                 <span class="identifier">nth_day_of_the_week_in_month</span> <span class="special">&amp;</span> nday<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_29"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">first_day_of_the_week_in_month</span> <span class="special">&amp;</span> fkd<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for first_day_of_the_week_in_month </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_30"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> 
                 <span class="identifier">first_day_of_the_week_in_month</span> <span class="special">&amp;</span> fkd<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_31"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">last_day_of_the_week_in_month</span> <span class="special">&amp;</span> lkd<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for last_day_of_the_week_in_month </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_32"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> 
                 <span class="identifier">last_day_of_the_week_in_month</span> <span class="special">&amp;</span> lkd<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_33"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">first_day_of_the_week_after</span> <span class="special">&amp;</span> fda<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for first_day_of_the_week_after </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_34"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> 
                 <span class="identifier">first_day_of_the_week_after</span> <span class="special">&amp;</span> fka<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_35"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">first_day_of_the_week_before</span> <span class="special">&amp;</span> fdb<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for first_day_of_the_week_before </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.gregorian.oper_1_3_12_15_4_18_1_1_36"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> 
                 <span class="identifier">first_day_of_the_week_before</span> <span class="special">&amp;</span> fkb<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.gregorian_types_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/gregorian_types.hpp" target="_top">boost/date_time/gregorian/gregorian_types.hpp</a>&gt;</h4></div></div></div>
<p>Single file header that defines most of the types for the gregorian date-time system. </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">period</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a><span class="special">,</span> <a class="link" href="../boost/gregorian/date_duration.html" title="Class date_duration">date_duration</a> <span class="special">&gt;</span> <a name="boost.gregorian.date_period"></a><span class="identifier">date_period</span><span class="special">;</span>  <span class="comment">// Date periods for the gregorian system. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">year_based_generator</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span> <a class="link" href="../boost/gregorian/year_based_generator.html" title="Type definition year_based_generator"><span class="identifier">year_based_generator</span></a><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">partial_date</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span> <a name="boost.gregorian.partial_date"></a><span class="identifier">partial_date</span><span class="special">;</span>  <span class="comment">// A date generation object type. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">nth_kday_of_month</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span> <a name="boost.gregorian.nth_kday_of_month"></a><span class="identifier">nth_kday_of_month</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">nth_kday_of_month</span> <a name="boost.gregorian.nth_d_1_3_12_15_4_19_2_1_5"></a><span class="identifier">nth_day_of_the_week_in_month</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">first_kday_of_month</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span> <a name="boost.gregorian.first_kday_of_month"></a><span class="identifier">first_kday_of_month</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">first_kday_of_month</span> <a name="boost.gregorian.first_1_3_12_15_4_19_2_1_7"></a><span class="identifier">first_day_of_the_week_in_month</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">last_kday_of_month</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span> <a name="boost.gregorian.last_kday_of_month"></a><span class="identifier">last_kday_of_month</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">last_kday_of_month</span> <a name="boost.gregorian.last__1_3_12_15_4_19_2_1_9"></a><span class="identifier">last_day_of_the_week_in_month</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">first_kday_after</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span> <a name="boost.gregorian.first_kday_after"></a><span class="identifier">first_kday_after</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">first_kday_after</span> <a name="boost.gregorian.firs_1_3_12_15_4_19_2_1_11"></a><span class="identifier">first_day_of_the_week_after</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">first_kday_before</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span> <a name="boost.gregorian.first_kday_before"></a><span class="identifier">first_kday_before</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">first_kday_before</span> <a name="boost.gregorian.firs_1_3_12_15_4_19_2_1_13"></a><span class="identifier">first_day_of_the_week_before</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">day_clock</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span> <a name="boost.gregorian.day_clock"></a><span class="identifier">day_clock</span><span class="special">;</span>  <span class="comment">// A clock to get the current day from the local computer. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">date_itr_base</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span> <a name="boost.gregorian.date_iterator"></a><span class="identifier">date_iterator</span><span class="special">;</span>  <span class="comment">// Base date_iterator type for gregorian types. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">date_itr</span><span class="special">&lt;</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">day_functor</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span><span class="special">,</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span> <a name="boost.gregorian.day_iterator"></a><span class="identifier">day_iterator</span><span class="special">;</span>  <span class="comment">// A day level iterator. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">date_itr</span><span class="special">&lt;</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">week_functor</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span><span class="special">,</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span> <a name="boost.gregorian.week_iterator"></a><span class="identifier">week_iterator</span><span class="special">;</span>  <span class="comment">// A week level iterator. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">date_itr</span><span class="special">&lt;</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">month_functor</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span><span class="special">,</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span> <a name="boost.gregorian.month_iterator"></a><span class="identifier">month_iterator</span><span class="special">;</span>  <span class="comment">// A month level iterator. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">date_itr</span><span class="special">&lt;</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">year_functor</span><span class="special">&lt;</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span><span class="special">,</span> <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <span class="special">&gt;</span> <a name="boost.gregorian.year_iterator"></a><span class="identifier">year_iterator</span><span class="special">;</span>  <span class="comment">// A year level iterator. </span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.gregorian.parsers_hpp"></a>Header &lt;<a href="../../../boost/date_time/gregorian/parsers.hpp" target="_top">boost/date_time/gregorian/parsers.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">gregorian</span> <span class="special">{</span>
    <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">special_values</span> <a class="link" href="../boost/gregorian/special_value_from_string.html" title="Function special_value_from_string"><span class="identifier">special_value_from_string</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// Deprecated: Use from_simple_string. </span>
    <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <a name="boost.gregorian.from_string"></a><span class="identifier">from_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>

    <span class="comment">// From delimited date string where with order year-month-day eg: 2002-1-25 or 2003-Jan-25 (full month name is also accepted) </span>
    <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <a name="boost.gregorian.from_simple_string"></a><span class="identifier">from_simple_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>

    <span class="comment">// From delimited date string where with order year-month-day eg: 1-25-2003 or Jan-25-2003 (full month name is also accepted) </span>
    <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <a name="boost.gregorian.from_us_string"></a><span class="identifier">from_us_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>

    <span class="comment">// From delimited date string where with order day-month-year eg: 25-1-2002 or 25-Jan-2003 (full month name is also accepted) </span>
    <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <a name="boost.gregorian.from_uk_string"></a><span class="identifier">from_uk_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>

    <span class="comment">// From ISO 8601 type date string where with order year-month-day eg: 20020125. </span>
    <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <a name="boost.gregorian.from_undelimited_string"></a><span class="identifier">from_undelimited_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>

    <span class="comment">// From ISO 8601 type date string where with order year-month-day eg: 20020125. </span>
    <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <a name="boost.gregorian.date_from_iso_string"></a><span class="identifier">date_from_iso_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Stream should hold a date in the form of: 2002-1-25. Month number, abbrev, or name are accepted. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> iterator_type<span class="special">&gt;</span> 
      <a class="link" href="../boost/gregorian/date.html" title="Class date">date</a> <a name="boost.gregorian.from_stream"></a><span class="identifier">from_stream</span><span class="special">(</span><span class="identifier">iterator_type</span> beg<span class="special">,</span> <span class="identifier">iterator_type</span> end<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to parse a date_period from a string (eg: [2003-Oct-31/2003-Dec-25]) </span>
    <span class="identifier">date_period</span> <a name="boost.gregorian.date_period_from_string"></a><span class="identifier">date_period_from_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function to parse a date_period from a wstring (eg: [2003-Oct-31/2003-Dec-25]) </span>
    <span class="identifier">date_period</span> <a name="boost.gregorian.date_period_from_wstring"></a><span class="identifier">date_period_from_wstring</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="posix_time_reference"></a>Posix Time Reference</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.conversion_hpp">Header &lt;boost/date_time/posix_time/conversion.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.date_duration_operators_hpp">Header &lt;boost/date_time/posix_time/date_duration_operators.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.posix_time_hpp">Header &lt;boost/date_time/posix_time/posix_time.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.posix_time_config_hpp">Header &lt;boost/date_time/posix_time/posix_time_config.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.posix_time_duration_hpp">Header &lt;boost/date_time/posix_time/posix_time_duration.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.posix_time_io_hpp">Header &lt;boost/date_time/posix_time/posix_time_io.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.posix_time_legacy_io_hpp">Header &lt;boost/date_time/posix_time/posix_time_legacy_io.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.posix_time_system_hpp">Header &lt;boost/date_time/posix_time/posix_time_system.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.posix_time_types_hpp">Header &lt;boost/date_time/posix_time/posix_time_types.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.ptime_hpp">Header &lt;boost/date_time/posix_time/ptime.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.time_formatters_hpp">Header &lt;boost/date_time/posix_time/time_formatters.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.time_formatters_limited_hpp">Header &lt;boost/date_time/posix_time/time_formatters_limited.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.time_parsers_hpp">Header &lt;boost/date_time/posix_time/time_parsers.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.time_period_hpp">Header &lt;boost/date_time/posix_time/time_period.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.posix_time.time_serialize_hpp">Header &lt;boost/date_time/posix_time/time_serialize.hpp&gt;</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.conversion_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/conversion.hpp" target="_top">boost/date_time/posix_time/conversion.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">posix_time</span> <span class="special">{</span>

    <span class="comment">// Function that converts a time_t into a ptime. </span>
    <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <a name="boost.posix_time.from_time_t"></a><span class="identifier">from_time_t</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">time_t</span> t<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Function that converts a ptime into a time_t. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">time_t</span> <a name="boost.posix_time.to_time_t"></a><span class="identifier">to_time_t</span><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> pt<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert a time to a tm structure truncating any fractional seconds. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">tm</span> <a name="boost.posix_time.to_tm_1_3_12_15_5_2_1_1_3"></a><span class="identifier">to_tm</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">boost::posix_time::ptime</a> <span class="special">&amp;</span> t<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert a <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> to a tm structure truncating any fractional seconds and zeroing fields for date components. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">tm</span> <a name="boost.posix_time.to_tm_1_3_12_15_5_2_1_1_4"></a><span class="identifier">to_tm</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">boost::posix_time::time_duration</a> <span class="special">&amp;</span> td<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert a tm struct to a ptime ignoring is_dst flag. </span>
    <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <a name="boost.posix_time.ptime_from_tm"></a><span class="identifier">ptime_from_tm</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">tm</span> <span class="special">&amp;</span> timetm<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> TimeT<span class="special">,</span> <span class="keyword">typename</span> FileTimeT<span class="special">&gt;</span> 
      <span class="identifier">TimeT</span> <a class="link" href="../boost/posix_time/from_ftime.html" title="Function template from_ftime"><span class="identifier">from_ftime</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">FileTimeT</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.date_duration_operators_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/date_duration_operators.hpp" target="_top">boost/date_time/posix_time/date_duration_operators.hpp</a>&gt;</h4></div></div></div>
<p>Operators for ptime and optional gregorian types. Operators use snap-to-end-of-month behavior. Further details on this behavior can be found in reference for date_time/date_duration_types.hpp and documentation for month and year iterators. </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">posix_time</span> <span class="special">{</span>
    <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> 
    <a class="link" href="../boost/posix_time/operat_1_3_12_15_5_3_2_1_1.html" title="Function operator+"><span class="keyword">operator</span><span class="special">+</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">months</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> 
    <a class="link" href="../boost/posix_time/operat_1_3_12_15_5_3_2_1_2.html" title="Function operator+="><span class="keyword">operator</span><span class="special">+=</span></a><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">months</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> 
    <a class="link" href="../boost/posix_time/operat_1_3_12_15_5_3_2_1_3.html" title="Function operator-"><span class="keyword">operator</span><span class="special">-</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">months</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> 
    <a class="link" href="../boost/posix_time/operat_1_3_12_15_5_3_2_1_4.html" title="Function operator-="><span class="keyword">operator</span><span class="special">-=</span></a><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">months</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> 
    <a class="link" href="../boost/posix_time/operat_1_3_12_15_5_3_2_1_5.html" title="Function operator+"><span class="keyword">operator</span><span class="special">+</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">years</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> 
    <a class="link" href="../boost/posix_time/operat_1_3_12_15_5_3_2_1_6.html" title="Function operator+="><span class="keyword">operator</span><span class="special">+=</span></a><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">years</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> 
    <a class="link" href="../boost/posix_time/operat_1_3_12_15_5_3_2_1_7.html" title="Function operator-"><span class="keyword">operator</span><span class="special">-</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">years</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> 
    <a class="link" href="../boost/posix_time/operat_1_3_12_15_5_3_2_1_8.html" title="Function operator-="><span class="keyword">operator</span><span class="special">-=</span></a><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">years</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.posix_time_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/posix_time.hpp" target="_top">boost/date_time/posix_time/posix_time.hpp</a>&gt;</h4></div></div></div>
<p>Global header file to get all of posix time types </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.posix_time_config_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/posix_time_config.hpp" target="_top">boost/date_time/posix_time/posix_time_config.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">posix_time</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/posix_time/millis_1_3_12_15_5_5_1_1_1.html" title="Class millisec_posix_time_system_config">millisec_posix_time_system_config</a><span class="special">;</span>
    <span class="keyword">class</span> <a class="link" href="../boost/posix_time/posix_time_system_config.html" title="Class posix_time_system_config">posix_time_system_config</a><span class="special">;</span>

    <span class="keyword">struct</span> <a class="link" href="../boost/posix_time/simple_time_rep.html" title="Struct simple_time_rep">simple_time_rep</a><span class="special">;</span>

    <span class="keyword">class</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_resolution_traits</span><span class="special">&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_resolution_traits_adapted64_impl</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">nano</span><span class="special">,</span> <span class="number">1000000000</span><span class="special">,</span> <span class="number">9</span> <span class="special">&gt;</span> <a name="boost.posix_time.time_res_traits"></a><span class="identifier">time_res_traits</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.posix_time_duration_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/posix_time_duration.hpp" target="_top">boost/date_time/posix_time/posix_time_duration.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">posix_time</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/posix_time/hours.html" title="Class hours">hours</a><span class="special">;</span>
    <span class="keyword">class</span> <a class="link" href="../boost/posix_time/minutes.html" title="Class minutes">minutes</a><span class="special">;</span>
    <span class="keyword">class</span> <a class="link" href="../boost/posix_time/seconds.html" title="Class seconds">seconds</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">subsecond_duration</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a><span class="special">,</span> <span class="number">1000</span> <span class="special">&gt;</span> <a name="boost.posix_time.millisec"></a><span class="identifier">millisec</span><span class="special">;</span>  <span class="comment">// Allows expression of durations as milli seconds. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">subsecond_duration</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a><span class="special">,</span> <span class="number">1000</span> <span class="special">&gt;</span> <a name="boost.posix_time.milliseconds"></a><span class="identifier">milliseconds</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">subsecond_duration</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a><span class="special">,</span> <span class="number">1000000</span> <span class="special">&gt;</span> <a name="boost.posix_time.microsec"></a><span class="identifier">microsec</span><span class="special">;</span>  <span class="comment">// Allows expression of durations as micro seconds. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">subsecond_duration</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a><span class="special">,</span> <span class="number">1000000</span> <span class="special">&gt;</span> <a name="boost.posix_time.microseconds"></a><span class="identifier">microseconds</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">subsecond_duration</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a><span class="special">,</span> <span class="number">1000000000</span> <span class="special">&gt;</span> <a name="boost.posix_time.nanosec"></a><span class="identifier">nanosec</span><span class="special">;</span>  <span class="comment">// Allows expression of durations as nano seconds. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">subsecond_duration</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a><span class="special">,</span> <span class="number">1000000000</span> <span class="special">&gt;</span> <a name="boost.posix_time.nanoseconds"></a><span class="identifier">nanoseconds</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.posix_time_io_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/posix_time_io.hpp" target="_top">boost/date_time/posix_time/posix_time_io.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">posix_time</span> <span class="special">{</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_facet</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a><span class="special">,</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <a class="link" href="../boost/posix_time/wtime_facet.html" title="Type definition wtime_facet"><span class="identifier">wtime_facet</span></a><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_facet</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a><span class="special">,</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.posix_time.time_facet"></a><span class="identifier">time_facet</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_input_facet</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a><span class="special">,</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <a name="boost.posix_time.wtime_input_facet"></a><span class="identifier">wtime_input_facet</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_input_facet</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a><span class="special">,</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.posix_time.time_input_facet"></a><span class="identifier">time_input_facet</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.posix_time.operat_1_3_12_15_5_7_1_1_5"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span> p<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for ptime </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.posix_time.operat_1_3_12_15_5_7_1_1_6"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span> pt<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.posix_time.operat_1_3_12_15_5_7_1_1_7"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">posix_time</span><span class="special">::</span><span class="identifier">time_period</span> <span class="special">&amp;</span> p<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for time_period </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.posix_time.operat_1_3_12_15_5_7_1_1_8"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <span class="identifier">time_period</span> <span class="special">&amp;</span> tp<span class="special">)</span><span class="special">;</span>

    <span class="comment">// ostream operator for <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">posix_time::time_duration</a></span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.posix_time.operat_1_3_12_15_5_7_1_1_9"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> <span class="special">&amp;</span> td<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a></span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.posix_time.opera_1_3_12_15_5_7_1_1_10"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> <span class="special">&amp;</span> td<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.posix_time_legacy_io_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/posix_time_legacy_io.hpp" target="_top">boost/date_time/posix_time/posix_time_legacy_io.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">posix_time</span> <span class="special">{</span>

    <span class="comment">// ostream operator for <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">posix_time::time_duration</a></span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.posix_time.operat_1_3_12_15_5_8_1_1_1"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> <span class="special">&amp;</span> td<span class="special">)</span><span class="special">;</span>

    <span class="comment">// ostream operator for <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">posix_time::ptime</a></span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.posix_time.operat_1_3_12_15_5_8_1_1_2"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span> t<span class="special">)</span><span class="special">;</span>

    <span class="comment">// ostream operator for posix_time::time_period </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">,</span> <span class="keyword">typename</span> traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.posix_time.operat_1_3_12_15_5_8_1_1_3"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">charT</span><span class="special">,</span> <span class="identifier">traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">time_period</span> <span class="special">&amp;</span> tp<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.posix_time.operat_1_3_12_15_5_8_1_1_4"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> <span class="special">&amp;</span> td<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.posix_time.operat_1_3_12_15_5_8_1_1_5"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span> pt<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a class="link" href="../boost/posix_time/operat_1_3_12_15_5_8_1_1_6.html" title="Function template operator&gt;&gt;"><span class="keyword">operator</span><span class="special">&gt;&gt;</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">time_period</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.posix_time_system_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/posix_time_system.hpp" target="_top">boost/date_time/posix_time/posix_time_system.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">posix_time</span> <span class="special">{</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">split_timedate_system</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/posix_time_system_config.html" title="Class posix_time_system_config">posix_time_system_config</a><span class="special">,</span> <span class="number">1000000000</span> <span class="special">&gt;</span> <a name="boost.posix_time.posix_time_system"></a><span class="identifier">posix_time_system</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">counted_time_rep</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/millis_1_3_12_15_5_5_1_1_1.html" title="Class millisec_posix_time_system_config">millisec_posix_time_system_config</a> <span class="special">&gt;</span> <a name="boost.posix_time.int64_time_rep"></a><span class="identifier">int64_time_rep</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.posix_time_types_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/posix_time_types.hpp" target="_top">boost/date_time/posix_time/posix_time_types.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">posix_time</span> <span class="special">{</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_itr</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&gt;</span> <a name="boost.posix_time.time_iterator"></a><span class="identifier">time_iterator</span><span class="special">;</span>  <span class="comment">// Iterator over a defined time duration. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">second_clock</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&gt;</span> <a name="boost.posix_time.second_clock"></a><span class="identifier">second_clock</span><span class="special">;</span>  <span class="comment">// A time clock that has a resolution of one second. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">microsec_clock</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&gt;</span> <a name="boost.posix_time.microsec_clock"></a><span class="identifier">microsec_clock</span><span class="special">;</span>  <span class="comment">// A time clock that has a resolution of one microsecond. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">null_dst_rules</span><span class="special">&lt;</span> <span class="identifier">ptime</span><span class="special">::</span><span class="identifier">date_type</span><span class="special">,</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> <span class="special">&gt;</span> <a name="boost.posix_time.no_dst"></a><span class="identifier">no_dst</span><span class="special">;</span>  <span class="comment">// Define a dst null dst rule for the posix_time system. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">us_dst_rules</span><span class="special">&lt;</span> <span class="identifier">ptime</span><span class="special">::</span><span class="identifier">date_type</span><span class="special">,</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> <span class="special">&gt;</span> <a name="boost.posix_time.us_dst"></a><span class="identifier">us_dst</span><span class="special">;</span>  <span class="comment">// Define US dst rule calculator for the posix_time system. </span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.ptime_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/ptime.hpp" target="_top">boost/date_time/posix_time/ptime.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">posix_time</span> <span class="special">{</span>
    <span class="keyword">class</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a><span class="special">;</span>
    <span class="identifier">BOOST_CXX14_CONSTEXPR</span> <span class="keyword">bool</span> 
    <a name="boost.posix_time.operator=="></a><span class="keyword">operator</span><span class="special">==</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span> lhs<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <span class="special">&amp;</span> rhs<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.time_formatters_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/time_formatters.hpp" target="_top">boost/date_time/posix_time/time_formatters.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">posix_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <a name="boost.posix_time.to_si_1_3_12_15_5_12_1_1_1"></a><span class="identifier">to_simple_string_type</span><span class="special">(</span><a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> td<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <a name="boost.posix_time.to_is_1_3_12_15_5_12_1_1_2"></a><span class="identifier">to_iso_string_type</span><span class="special">(</span><a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> td<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Time to simple format CCYY-mmm-dd hh:mm:ss.fffffff. </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <a name="boost.posix_time.to_si_1_3_12_15_5_12_1_1_3"></a><span class="identifier">to_simple_string_type</span><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> t<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <a name="boost.posix_time.to_si_1_3_12_15_5_12_1_1_4"></a><span class="identifier">to_simple_string_type</span><span class="special">(</span><span class="identifier">time_period</span> tp<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <a name="boost.posix_time.to_is_1_3_12_15_5_12_1_1_5"></a><span class="identifier">to_iso_string_type</span><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> t<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> charT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_string</span><span class="special">&lt;</span> <span class="identifier">charT</span> <span class="special">&gt;</span> <a name="boost.posix_time.to_is_1_3_12_15_5_12_1_1_6"></a><span class="identifier">to_iso_extended_string_type</span><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> t<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Time duration to wstring -hh::mm::ss.fffffff. Example: 10:09:03.0123456. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <a name="boost.posix_time.to_si_1_3_12_15_5_12_1_1_7"></a><span class="identifier">to_simple_wstring</span><span class="special">(</span><a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> td<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Time duration in ISO 8601 format -hhmmss.fffffff. Example: 10:09:03.0123456. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <a name="boost.posix_time.to_is_1_3_12_15_5_12_1_1_8"></a><span class="identifier">to_iso_wstring</span><span class="special">(</span><a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> td<span class="special">)</span><span class="special">;</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <a name="boost.posix_time.to_si_1_3_12_15_5_12_1_1_9"></a><span class="identifier">to_simple_wstring</span><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> t<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert to wstring of form [YYYY-mmm-DD HH:MM::SS.ffffff/YYYY-mmm-DD HH:MM::SS.fffffff]. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <a name="boost.posix_time.to_s_1_3_12_15_5_12_1_1_10"></a><span class="identifier">to_simple_wstring</span><span class="special">(</span><span class="identifier">time_period</span> tp<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert ISO 8601 short form YYYYMMDDTHHMMSS where T is the date-time separator. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <a name="boost.posix_time.to_i_1_3_12_15_5_12_1_1_11"></a><span class="identifier">to_iso_wstring</span><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> t<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert to form YYYY-MM-DDTHH:MM:SS where T is the date-time separator. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">wstring</span> <a name="boost.posix_time.to_iso_extended_wstring"></a><span class="identifier">to_iso_extended_wstring</span><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> t<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.time_formatters_limited_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/time_formatters_limited.hpp" target="_top">boost/date_time/posix_time/time_formatters_limited.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">posix_time</span> <span class="special">{</span>

    <span class="comment">// Time duration to string -hh::mm::ss.fffffff. Example: 10:09:03.0123456. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a name="boost.posix_time.to_si_1_3_12_15_5_13_1_1_1"></a><span class="identifier">to_simple_string</span><span class="special">(</span><a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> td<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Time duration in ISO 8601 format -hhmmss.fffffff. Example: 10:09:03.0123456. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a name="boost.posix_time.to_is_1_3_12_15_5_13_1_1_2"></a><span class="identifier">to_iso_string</span><span class="special">(</span><a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> td<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Time to simple format CCYY-mmm-dd hh:mm:ss.fffffff. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a name="boost.posix_time.to_si_1_3_12_15_5_13_1_1_3"></a><span class="identifier">to_simple_string</span><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> t<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert to string of form [YYYY-mmm-DD HH:MM::SS.ffffff/YYYY-mmm-DD HH:MM::SS.fffffff]. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a name="boost.posix_time.to_si_1_3_12_15_5_13_1_1_4"></a><span class="identifier">to_simple_string</span><span class="special">(</span><span class="identifier">time_period</span> tp<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert ISO 8601 short form YYYYMMDDTHHMMSS where T is the date-time separator. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a name="boost.posix_time.to_is_1_3_12_15_5_13_1_1_5"></a><span class="identifier">to_iso_string</span><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> t<span class="special">)</span><span class="special">;</span>

    <span class="comment">// Convert to form YYYY-MM-DDTHH:MM:SS where T is the date-time separator. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <a name="boost.posix_time.to_iso_extended_string"></a><span class="identifier">to_iso_extended_string</span><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> t<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.time_parsers_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/time_parsers.hpp" target="_top">boost/date_time/posix_time/time_parsers.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">posix_time</span> <span class="special">{</span>
    <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> <a class="link" href="../boost/posix_time/duration_from_string.html" title="Function duration_from_string"><span class="identifier">duration_from_string</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <a name="boost.posix_time.time_from_string"></a><span class="identifier">time_from_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>
    <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <a name="boost.posix_time.from_iso_string"></a><span class="identifier">from_iso_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>
    <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a> <a name="boost.posix_time.from_iso_extended_string"></a><span class="identifier">from_iso_extended_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.time_period_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/time_period.hpp" target="_top">boost/date_time/posix_time/time_period.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">posix_time</span> <span class="special">{</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">period</span><span class="special">&lt;</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">ptime</a><span class="special">,</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">time_duration</a> <span class="special">&gt;</span> <a name="boost.posix_time.time_period"></a><span class="identifier">time_period</span><span class="special">;</span>  <span class="comment">// Time period type. </span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.posix_time.time_serialize_hpp"></a>Header &lt;<a href="../../../boost/date_time/posix_time/time_serialize.hpp" target="_top">boost/date_time/posix_time/time_serialize.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">serialization</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/serialization/version.html" title="Struct template version">version</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/serialization/versi_1_3_12_15_5_16_1_1_2.html" title="Struct version&lt;boost::posix_time::time_duration&gt;">version</a><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">posix_time</span><span class="special">::</span><span class="identifier">time_duration</span><span class="special">&gt;</span><span class="special">;</span>
     <a class="link" href="../boost/serialization/BOOST_1_3_12_15_5_16_1_1_3.html" title="Function BOOST_DATE_TIME_SPLIT_FREE"><span class="identifier">BOOST_DATE_TIME_SPLIT_FREE</span></a><span class="special">(</span><a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">boost::posix_time::ptime</a><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.save_1_3_12_15_5_16_1_1_4"></a><span class="identifier">save</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">posix_time::time_duration</a> <span class="special">&amp;</span> td<span class="special">,</span> 
                <span class="keyword">unsigned</span> <span class="keyword">int</span> version<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> TimeResTraitsSize<span class="special">,</span> <span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/load_td.html" title="Function template load_td"><span class="identifier">load_td</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">posix_time::time_duration</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_5_16_1_1_6"></a><span class="identifier">load</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <a class="link" href="../boost/posix_time/time_duration.html" title="Class time_duration">posix_time::time_duration</a> <span class="special">&amp;</span> td<span class="special">,</span> 
                <span class="keyword">unsigned</span> <span class="keyword">int</span> version<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/save_1_3_12_15_5_16_1_1_7.html" title="Function template save"><span class="identifier">save</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">posix_time::ptime</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/load_1_3_12_15_5_16_1_1_8.html" title="Function template load"><span class="identifier">load</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">posix_time::ptime</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load__1_3_12_15_5_16_1_1_9"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <a class="link" href="../boost/posix_time/ptime.html" title="Class ptime">posix_time::ptime</a> <span class="special">*</span> pt<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/save_1_3_12_15_5_16_1_1_10.html" title="Function template save"><span class="identifier">save</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">posix_time</span><span class="special">::</span><span class="identifier">time_period</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a class="link" href="../boost/serialization/load_1_3_12_15_5_16_1_1_11.html" title="Function template load"><span class="identifier">load</span></a><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">posix_time</span><span class="special">::</span><span class="identifier">time_period</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>

    <span class="comment">// override needed b/c no default constructor </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_12_15_5_16_1_1_12"></a><span class="identifier">load_construct_data</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">posix_time</span><span class="special">::</span><span class="identifier">time_period</span> <span class="special">*</span> tp<span class="special">,</span> 
                               <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="local_time_reference"></a>Local Time Reference</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="doxy.html#header.boost.date_time.local_time.conversion_hpp">Header &lt;boost/date_time/local_time/conversion.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.local_time.custom_time_zone_hpp">Header &lt;boost/date_time/local_time/custom_time_zone.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.local_time.date_duration_operators_hpp">Header &lt;boost/date_time/local_time/date_duration_operators.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.local_time.dst_transition_day_rules_hpp">Header &lt;boost/date_time/local_time/dst_transition_day_rules.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.local_time.local_date_time_hpp">Header &lt;boost/date_time/local_time/local_date_time.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.local_time.local_time_io_hpp">Header &lt;boost/date_time/local_time/local_time_io.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.local_time.local_time_types_hpp">Header &lt;boost/date_time/local_time/local_time_types.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.local_time.posix_time_zone_hpp">Header &lt;boost/date_time/local_time/posix_time_zone.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="doxy.html#header.boost.date_time.local_time.tz_database_hpp">Header &lt;boost/date_time/local_time/tz_database.hpp&gt;</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.local_time.conversion_hpp"></a>Header &lt;<a href="../../../boost/date_time/local_time/conversion.hpp" target="_top">boost/date_time/local_time/conversion.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">local_time</span> <span class="special">{</span>

    <span class="comment">// Function that creates a tm struct from a local_date_time. </span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">tm</span> <a name="boost.local_time.to_tm"></a><span class="identifier">to_tm</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&amp;</span> lt<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.local_time.custom_time_zone_hpp"></a>Header &lt;<a href="../../../boost/date_time/local_time/custom_time_zone.hpp" target="_top">boost/date_time/local_time/custom_time_zone.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">local_time</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/local_time/custom_time_zone_base.html" title="Class template custom_time_zone_base">custom_time_zone_base</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">shared_ptr</span><span class="special">&lt;</span> <span class="identifier">dst_calc_rule</span> <span class="special">&gt;</span> <a name="boost.local_time.dst_calc_rule_ptr"></a><span class="identifier">dst_calc_rule_ptr</span><span class="special">;</span>
    <span class="keyword">typedef</span> <a class="link" href="../boost/local_time/custom_time_zone_base.html" title="Class template custom_time_zone_base">custom_time_zone_base</a><span class="special">&lt;</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.local_time.custom_time_zone"></a><span class="identifier">custom_time_zone</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.local_time.date_duration_operators_hpp"></a>Header &lt;<a href="../../../boost/date_time/local_time/date_duration_operators.hpp" target="_top">boost/date_time/local_time/date_duration_operators.hpp</a>&gt;</h4></div></div></div>
<p>Operators for local_date_time and optional gregorian types. Operators use snap-to-end-of-month behavior. Further details on this behavior can be found in reference for date_time/date_duration_types.hpp and documentation for month and year iterators. </p>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">local_time</span> <span class="special">{</span>
    <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> 
    <a class="link" href="../boost/local_time/operat_1_3_12_15_6_4_2_1_1.html" title="Function operator+"><span class="keyword">operator</span><span class="special">+</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">months</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> 
    <a class="link" href="../boost/local_time/operat_1_3_12_15_6_4_2_1_2.html" title="Function operator+="><span class="keyword">operator</span><span class="special">+=</span></a><span class="special">(</span><a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">months</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> 
    <a class="link" href="../boost/local_time/operat_1_3_12_15_6_4_2_1_3.html" title="Function operator-"><span class="keyword">operator</span><span class="special">-</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">months</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> 
    <a class="link" href="../boost/local_time/operat_1_3_12_15_6_4_2_1_4.html" title="Function operator-="><span class="keyword">operator</span><span class="special">-=</span></a><span class="special">(</span><a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">months</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> 
    <a class="link" href="../boost/local_time/operat_1_3_12_15_6_4_2_1_5.html" title="Function operator+"><span class="keyword">operator</span><span class="special">+</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">years</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> 
    <a class="link" href="../boost/local_time/operat_1_3_12_15_6_4_2_1_6.html" title="Function operator+="><span class="keyword">operator</span><span class="special">+=</span></a><span class="special">(</span><a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">years</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> 
    <a class="link" href="../boost/local_time/operat_1_3_12_15_6_4_2_1_7.html" title="Function operator-"><span class="keyword">operator</span><span class="special">-</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">years</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
    <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> 
    <a class="link" href="../boost/local_time/operat_1_3_12_15_6_4_2_1_8.html" title="Function operator-="><span class="keyword">operator</span><span class="special">-=</span></a><span class="special">(</span><a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">years</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.local_time.dst_transition_day_rules_hpp"></a>Header &lt;<a href="../../../boost/date_time/local_time/dst_transition_day_rules.hpp" target="_top">boost/date_time/local_time/dst_transition_day_rules.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">local_time</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/local_time/first_last_rule_spec.html" title="Struct first_last_rule_spec">first_last_rule_spec</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/local_time/last_last_rule_spec.html" title="Struct last_last_rule_spec">last_last_rule_spec</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/local_time/nth_kday_rule_spec.html" title="Struct nth_kday_rule_spec">nth_kday_rule_spec</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/local_time/nth_last_rule_spec.html" title="Struct nth_last_rule_spec">nth_last_rule_spec</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/local_time/partial_date_rule_spec.html" title="Struct partial_date_rule_spec">partial_date_rule_spec</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">dst_day_calc_rule</span><span class="special">&lt;</span> <span class="identifier">gregorian</span><span class="special">::</span><span class="identifier">date</span> <span class="special">&gt;</span> <a name="boost.local_time.dst_calc_rule"></a><span class="identifier">dst_calc_rule</span><span class="special">;</span>  <span class="comment">// Provides rule of the form starting Apr 30 ending Oct 21. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">day_calc_dst_rule</span><span class="special">&lt;</span> <a class="link" href="../boost/local_time/partial_date_rule_spec.html" title="Struct partial_date_rule_spec">partial_date_rule_spec</a> <span class="special">&gt;</span> <a name="boost.local_time.partial_date_dst_rule"></a><span class="identifier">partial_date_dst_rule</span><span class="special">;</span>  <span class="comment">// Provides rule of the form first Sunday in April, last Saturday in Oct. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">day_calc_dst_rule</span><span class="special">&lt;</span> <a class="link" href="../boost/local_time/first_last_rule_spec.html" title="Struct first_last_rule_spec">first_last_rule_spec</a> <span class="special">&gt;</span> <a name="boost.local_time.first_last_dst_rule"></a><span class="identifier">first_last_dst_rule</span><span class="special">;</span>  <span class="comment">// Provides rule of the form first Sunday in April, last Saturday in Oct. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">day_calc_dst_rule</span><span class="special">&lt;</span> <a class="link" href="../boost/local_time/last_last_rule_spec.html" title="Struct last_last_rule_spec">last_last_rule_spec</a> <span class="special">&gt;</span> <a name="boost.local_time.last_last_dst_rule"></a><span class="identifier">last_last_dst_rule</span><span class="special">;</span>  <span class="comment">// Provides rule of the form last Sunday in April, last Saturday in Oct. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">day_calc_dst_rule</span><span class="special">&lt;</span> <a class="link" href="../boost/local_time/nth_last_rule_spec.html" title="Struct nth_last_rule_spec">nth_last_rule_spec</a> <span class="special">&gt;</span> <a name="boost.local_time.nth_last_dst_rule"></a><span class="identifier">nth_last_dst_rule</span><span class="special">;</span>  <span class="comment">// Provides rule in form of [1st|2nd|3rd|4th] Sunday in April, last Sunday in Oct. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">day_calc_dst_rule</span><span class="special">&lt;</span> <a class="link" href="../boost/local_time/nth_kday_rule_spec.html" title="Struct nth_kday_rule_spec">nth_kday_rule_spec</a> <span class="special">&gt;</span> <a name="boost.local_time.nth_kday_dst_rule"></a><span class="identifier">nth_kday_dst_rule</span><span class="special">;</span>  <span class="comment">// Provides rule in form of [1st|2nd|3rd|4th] Sunday in April/October. </span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">day_calc_dst_rule</span><span class="special">&lt;</span> <a class="link" href="../boost/local_time/nth_kday_rule_spec.html" title="Struct nth_kday_rule_spec">nth_kday_rule_spec</a> <span class="special">&gt;</span> <a name="boost.local_time.nth_d_1_3_12_15_6_5_1_1_12"></a><span class="identifier">nth_day_of_the_week_in_month_dst_rule</span><span class="special">;</span>  <span class="comment">// Provides rule in form of [1st|2nd|3rd|4th] Sunday in April/October. </span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.local_time.local_date_time_hpp"></a>Header &lt;<a href="../../../boost/date_time/local_time/local_date_time.hpp" target="_top">boost/date_time/local_time/local_date_time.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">local_time</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/local_time/ambiguous_result.html" title="Struct ambiguous_result">ambiguous_result</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/local_time/dst_not_valid.html" title="Struct dst_not_valid">dst_not_valid</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> utc_time_ <span class="special">=</span> <span class="identifier">posix_time</span><span class="special">::</span><span class="identifier">ptime</span><span class="special">,</span> 
             <span class="keyword">typename</span> tz_type <span class="special">=</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_zone_base</span><span class="special">&lt;</span><span class="identifier">utc_time_</span><span class="special">,</span><span class="keyword">char</span><span class="special">&gt;</span> <span class="special">&gt;</span> 
      <span class="keyword">class</span> <a class="link" href="../boost/local_time/local_date_time_base.html" title="Class template local_date_time_base">local_date_time_base</a><span class="special">;</span>

    <span class="keyword">struct</span> <a class="link" href="../boost/local_time/time_label_invalid.html" title="Struct time_label_invalid">time_label_invalid</a><span class="special">;</span>

    <span class="keyword">typedef</span> <a class="link" href="../boost/local_time/local_date_time_base.html" title="Class template local_date_time_base">local_date_time_base</a> <a name="boost.local_time.local_date_time"></a><span class="identifier">local_date_time</span><span class="special">;</span>  <span class="comment">// Use the default parameters to define local_date_time. </span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.local_time.local_time_io_hpp"></a>Header &lt;<a href="../../../boost/date_time/local_time/local_time_io.hpp" target="_top">boost/date_time/local_time/local_time_io.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">local_time</span> <span class="special">{</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_facet</span><span class="special">&lt;</span> <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a><span class="special">,</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <a name="boost.local_time.wlocal_time_facet"></a><span class="identifier">wlocal_time_facet</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_facet</span><span class="special">&lt;</span> <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a><span class="special">,</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.local_time.local_time_facet"></a><span class="identifier">local_time_facet</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_input_facet</span><span class="special">&lt;</span> <span class="identifier">local_date_time</span><span class="special">::</span><span class="identifier">utc_time_type</span><span class="special">,</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <a name="boost.local_time.wlocal_time_input_facet"></a><span class="identifier">wlocal_time_input_facet</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_input_facet</span><span class="special">&lt;</span> <span class="identifier">local_date_time</span><span class="special">::</span><span class="identifier">utc_time_type</span><span class="special">,</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.local_time.local_time_input_facet"></a><span class="identifier">local_time_input_facet</span><span class="special">;</span>

    <span class="comment">// operator&lt;&lt; for local_date_time - see local_time docs for formatting details </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.local_time.operat_1_3_12_15_6_8_1_1_5"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&amp;</span> ldt<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for local_date_time </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.local_time.operat_1_3_12_15_6_8_1_1_6"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> 
                 <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&amp;</span> ldt<span class="special">)</span><span class="special">;</span>

    <span class="comment">// output operator for local_time_period </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> TraitsT<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.local_time.operat_1_3_12_15_6_8_1_1_7"></a><span class="keyword">operator</span><span class="special">&lt;&lt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_ostream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">TraitsT</span> <span class="special">&gt;</span> <span class="special">&amp;</span> os<span class="special">,</span> 
                 <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">local_time</span><span class="special">::</span><span class="identifier">local_time_period</span> <span class="special">&amp;</span> p<span class="special">)</span><span class="special">;</span>

    <span class="comment">// input operator for local_time_period </span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits<span class="special">&gt;</span> 
      <span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
      <a name="boost.local_time.operat_1_3_12_15_6_8_1_1_8"></a><span class="keyword">operator</span><span class="special">&gt;&gt;</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">basic_istream</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> <span class="special">&amp;</span> is<span class="special">,</span> 
                 <span class="identifier">boost</span><span class="special">::</span><span class="identifier">local_time</span><span class="special">::</span><span class="identifier">local_time_period</span> <span class="special">&amp;</span> tp<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.local_time.local_time_types_hpp"></a>Header &lt;<a href="../../../boost/date_time/local_time/local_time_types.hpp" target="_top">boost/date_time/local_time/local_time_types.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">local_time</span> <span class="special">{</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">period</span><span class="special">&lt;</span> <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a><span class="special">,</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">posix_time</span><span class="special">::</span><span class="identifier">time_duration</span> <span class="special">&gt;</span> <a name="boost.local_time.local_time_period"></a><span class="identifier">local_time_period</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_itr</span><span class="special">&lt;</span> <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&gt;</span> <a name="boost.local_time.local_time_iterator"></a><span class="identifier">local_time_iterator</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">second_clock</span><span class="special">&lt;</span> <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&gt;</span> <a name="boost.local_time.local_sec_clock"></a><span class="identifier">local_sec_clock</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">microsec_clock</span><span class="special">&lt;</span> <a class="link" href="doxy.html#boost.local_time.local_date_time">local_date_time</a> <span class="special">&gt;</span> <a name="boost.local_time.local_microsec_clock"></a><span class="identifier">local_microsec_clock</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_zone_base</span><span class="special">&lt;</span> <span class="identifier">posix_time</span><span class="special">::</span><span class="identifier">ptime</span><span class="special">,</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.local_time.time_zone"></a><span class="identifier">time_zone</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_zone_base</span><span class="special">&lt;</span> <span class="identifier">posix_time</span><span class="special">::</span><span class="identifier">ptime</span><span class="special">,</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <a name="boost.local_time.wtime_zone"></a><span class="identifier">wtime_zone</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">shared_ptr</span><span class="special">&lt;</span> <span class="identifier">time_zone</span> <span class="special">&gt;</span> <a name="boost.local_time.time_zone_ptr"></a><span class="identifier">time_zone_ptr</span><span class="special">;</span>  <span class="comment">// Shared Pointer for custom_time_zone and posix_time_zone objects. </span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">shared_ptr</span><span class="special">&lt;</span> <span class="identifier">wtime_zone</span> <span class="special">&gt;</span> <a name="boost.local_time.wtime_zone_ptr"></a><span class="identifier">wtime_zone_ptr</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_zone_names_base</span><span class="special">&lt;</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.local_time.time_zone_names"></a><span class="identifier">time_zone_names</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">time_zone_names_base</span><span class="special">&lt;</span> <span class="keyword">wchar_t</span> <span class="special">&gt;</span> <a name="boost.local_time.wtime_zone_names"></a><span class="identifier">wtime_zone_names</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.local_time.posix_time_zone_hpp"></a>Header &lt;<a href="../../../boost/date_time/local_time/posix_time_zone.hpp" target="_top">boost/date_time/local_time/posix_time_zone.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">local_time</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/local_time/bad_adjustment.html" title="Struct bad_adjustment">bad_adjustment</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/local_time/bad_offset.html" title="Struct bad_offset">bad_offset</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">&gt;</span> <span class="keyword">class</span> <a class="link" href="../boost/local_time/posix_time_zone_base.html" title="Class template posix_time_zone_base">posix_time_zone_base</a><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">date_time</span><span class="special">::</span><span class="identifier">dst_adjustment_offsets</span><span class="special">&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">posix_time</span><span class="special">::</span><span class="identifier">time_duration</span> <span class="special">&gt;</span> <a name="boost.local_time.dst_adjustment_offsets"></a><span class="identifier">dst_adjustment_offsets</span><span class="special">;</span>
    <span class="keyword">typedef</span> <a class="link" href="../boost/local_time/posix_time_zone_base.html" title="Class template posix_time_zone_base">posix_time_zone_base</a><span class="special">&lt;</span> <span class="keyword">char</span> <span class="special">&gt;</span> <a name="boost.local_time.posix_time_zone"></a><span class="identifier">posix_time_zone</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.date_time.local_time.tz_database_hpp"></a>Header &lt;<a href="../../../boost/date_time/local_time/tz_database.hpp" target="_top">boost/date_time/local_time/tz_database.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">local_time</span> <span class="special">{</span>
    <span class="keyword">typedef</span> <span class="identifier">date_time</span><span class="special">::</span><span class="identifier">tz_db_base</span><span class="special">&lt;</span> <a class="link" href="doxy.html#boost.local_time.custom_time_zone">custom_time_zone</a><span class="special">,</span> <span class="identifier">nth_kday_dst_rule</span> <span class="special">&gt;</span> <a class="link" href="../boost/local_time/tz_database.html" title="Type definition tz_database"><span class="identifier">tz_database</span></a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2001-2005 CrystalClear Software, Inc<p>Subject to the Boost Software License, Version 1.0. (See accompanying file
    <code class="filename">LICENSE_1_0.txt</code> or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)</p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="examples.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../date_time.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost/date_time/day_functor.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
